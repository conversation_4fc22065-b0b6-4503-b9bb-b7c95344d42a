#!/usr/bin/env python3
# coding=utf-8

def get_item_attr_list():
    return [
        "photo_id",
        "author_id",
        "duration_ms",
        "photo_type",
        "fans_count",
        "audit_hot_high_tag_level",
        "upload_stamp",
        "upload_diff_day",
        "is_official_account",
        "magic_face_id",
        "music_id",
        "photo_click_upload_rate",
        "author_click_upload_rate",
        "photo_cover_cluser_id",
        "photo_level_kmeans_id",
        "photo_dnn_cluster_id",
        "is_hot_forward_photo",
        "is_hot_download_photo",
        "is_author_hetu_tag",
        "leaf_name_id",
        "category_name1_id",
        "category_name2_id",
        "category_name3_id",
        "category_name4_id",
        "sexy_score",
        "author_low_score",
        "author_high_score",
        "friend_score",
        "friend_score_position",
#         "hot_show_count",
#         "hot_click_count",
#         "hot_like_count",
#         "hot_comment_count",
#         "follow_page_show_count",
#         "follow_page_click_count",
#         "follow_page_like_count",
#         "follow_page_comment_count",
#         "hot_in_profile",
#         "hot_realshow",
#         "hot_follow",
#         "hot_list_comment",
#         "hot_forward",
#         "hot_longview",
#         "hot_shortview",
#         "hot_viewtime",
        "hot_unfollow",
#         "hot_unlike",
        "hot_neg",
#         "follow_in_profile",
#         "follow_realshow",
#         "follow_list_comment",
#         "follow_forward",
#         "follow_longview",
#         "follow_shortview",
#         "follow_viewtime",
        "follow_unfollow",
#         "follow_unlike",
        "follow_neg",
#         "sl_hot_in_profile",
#         "sl_hot_realshow",
#         "sl_hot_follow",
#         "sl_hot_list_comment",
#         "sl_hot_forward",
#         "sl_hot_longview",
#         "sl_hot_shortview",
#         "sl_hot_viewtime",
#         "sl_hot_unfollow",
#         "sl_hot_unlike",
#         "sl_hot_neg",
#         "sl_follow_in_profile",
#         "sl_follow_realshow",
#         "sl_follow_list_comment",
#         "sl_follow_forward",
#         "sl_follow_longview",
#         "sl_follow_shortview",
#         "sl_follow_viewtime",
#         "sl_follow_unfollow",
#         "sl_follow_unlike",
#         "sl_follow_neg",
#         "bl_hot_in_profile",
#         "bl_hot_realshow",
#         "bl_hot_follow",
#         "bl_hot_list_comment",
#         "bl_hot_forward",
#         "bl_hot_longview",
#         "bl_hot_shortview",
#         "bl_hot_viewtime",
#         "bl_hot_unfollow",
#         "bl_hot_unlike",
#         "bl_hot_neg",
#         "bl_follow_in_profile",
#         "bl_follow_realshow",
#         "bl_follow_list_comment",
#         "bl_follow_forward",
#         "bl_follow_longview",
#         "bl_follow_shortview",
#         "bl_follow_viewtime",
#         "bl_follow_unfollow",
#         "bl_follow_unlike",
#         "bl_follow_neg",
#         "follow_like_rate",
#         "follow_click_rate",
#         "follow_like_click_rate",
#         "hot_like_rate",
#         "hot_like_click_rate",
#         "sl_hot_show_count",
#         "sl_hot_click_count",
#         "sl_hot_like_count",
#         "sl_hot_comment_count",
#         "sl_follow_page_show_count",
#         "sl_follow_page_click_count",
#         "sl_follow_page_like_count",
#         "sl_follow_page_comment_count",
#         "sl_follow_like_rate",
#         "sl_follow_click_rate",
#         "sl_hot_like_rate",
#         "sl_hot_click_rate",
#         "bl_hot_show_count",
#         "bl_hot_click_count",
#         "bl_hot_like_count",
#         "bl_hot_comment_count",
#         "bl_follow_page_show_count",
#         "bl_follow_page_click_count",
#         "bl_follow_page_like_count",
#         "bl_follow_page_comment_count",
#         "bl_follow_like_rate",
#         "bl_follow_click_rate",
#         "bl_hot_like_rate",
#         "bl_hot_click_rate",
        "true_face",
        "clear_face",
        "face_area_ratio",
        "common_poor_quality",
        "meaningless_quality",
        "content_pure_color",
        "photo_cover_pure_color",
        "item_ctr",
        "item_ltr",
        "item_wtr",
        "item_cmtr",
        "item_svr",
        "item_lvr",
        "item_htr",
        "item_ftr",
        "item_pptr",
        "item_unwtr",
        "item_unltr",
        "item_lcmtr",
        'a_emp_pctr',
        'a_emp_pltr',
        'a_emp_pcmtr',
        'a_emp_pwtr',
        'a_emp_pptr',
        'a_emp_psvr',
        'a_emp_phtr',
        'a_upload_7d',
        'a_active',
        'a_up',
        'a_lfs',
        'a_dy',
        'a_cs',
        'a_crs',
        'a_ws',
        'a_qs',
        'a_nfs',
        'a_nfos',
        'a_ncs',
        'a_ncrs',
        'a_nws',
        'a_nqs',
        "ua_intimate_msg",
        "ua_intimate_like",
        "ua_intimate_cmt",
        "ua_intimate_cmt_like",
        "ua_intimate_pub_at",
        "ua_intimate_cmt_at",
        "ua_intimate_play",
        "ua_intimate_itr_play",
        "ua_intimate_val_play",
        "ua_intimate_val_itr_play",
        "ua_intimate_enter_prf",
        "ua_intimate_dur_p",
        "ua_intimate_dur_l",
        "ua_intimate_poke",
        "ua_intimate_amt_l",
        "ua_intimate_favor",
        "ua_intimate_search",
        "ua_intimate_intimate",
        "ua_intimate_alias",
        "ua_intimate_privacy",
        "ua_intimate_score",
        "ua_intimate_percentile_score",
        "ua_intimate_itr_rate",
        "ua_intimate_val_rate",
        "photo_status",
        "friend_list_type",
        "photo_partial_friend_visible_uid_list",
        "is_valid_photo",
        "interact_friend_list",
        "interact_friend_cnt",
        "friends_count",
        "city_id",
        "punished",
        "punish_ids",
        "punish_group_ids",
        "duplicate_punish_type",
        "author_sample_friend_list",
        "author_life_cycle",
        "author_age",
        "author_gender",
        "author_device_id",
        "author_follow_count",
        "p_hetu_l1",
        "p_hetu_l2",
        "p_hetu_l3",
        "p_hetu_l4",
        "p_hetu_l5",
        "a_hetu_l1",
        "a_hetu_l2",
        "a_hetu_l3",
        "a_hetu_l4",
        "a_hetu_l5",
        "picture_type",
        "mmu_quality_score",
        "follow_page_show_count",
        "profile_show_count",
        "profile_comment_count",
        "profile_unfollow",
        "profile_neg",
        "fs_show_cnt",
        "fs_realshow_cnt",
        "fs_click_cnt",
        "fs_like_cnt",
        "fs_dislike_cnt",
        "fs_forward_cnt",
        "fs_cmt_cnt",
        "photo_like_cnt",
        "photo_neg_cnt",
        "plc_business_type",
        "plc_category_type",
        "p_quality_score",
        "relation_author_stats",
        "p_hetu_tag",
        "a_hetu_tag",
        "csm_to_crt_new_upload_photo_cnt_v2",
        "csm_to_crt_play_cnt_one_day",
        "photo_cold_start_garantee",
        "video_cold_start_author_tails_json",
        "author_tail_int_index_keys",
        "author_tail_int_index_values",
        "adjust_quality_score",
        "is_uploaded_by_keyperson",
        "is_uploaded_by_produce_core_author",
        "is_coin_photo",
        "adjust_create_score",
        "upload_by_causal",
        "coldstart_guarantee_value",
        "author_op_session_class",
        "is_ugc"
    ]

def get_relation_base_attr_list():
    return [
        "user_id",
        "u_age",
        "u_gender",
        "u_province",
        "u_fans_count",
        "u_follow_count",
        "u_friend_count",
        "u_activeNum7Days",
        "u_uploadNum7Days",
        "u_activeNum30Days",
        "u_uploadNum30Days",
        "a_age",
        "a_gender",
        "a_province",
        "a_fans_count",
        "a_follow_count",
        "a_friend_count",
        "a_activeNum7Days",
        "a_uploadNum7Days",
        "a_activeNum30Days",
        "a_uploadNum30Days",
        "a_same_gender",
        "a_same_province",
        "a_age_gap",
        "a_same_fans_level"
    ]

def get_relation_counter_attr_list():
    return [
        "a_realshowedByFriendCount7D",
        "a_clickedByFriendCount7D",
        "a_likedByFriendCount7D",
        "a_likePhotoRealshowedByUserCount7D",
        "a_likePhotoClickedByUserCount7D",
        "a_likedByUserCount7D"
    ]

def get_relation_info_attr_list():
    return [
        "a_commonFriendCount",
        "a_commonFollowCount",
        "a_commonContactCount",
        "a_commonContactReverseCount",
        "a_commonWechatCount",
        "a_commonQqCount",
        "a_commonCurWifiUserCount",
        "a_commonFreqWifiUserCount",
        "a_commonCurLocationNearbyUserCount",
        "a_commonFreqLocationNearbyUserCount",
        "a_isFriendRelation",
        "a_isFollowRelation",
        "a_isContactRelation",
        "a_isContactReverseRelation",
        "a_isWechatRelation",
        "a_isQqRelation",
        "a_isWechatFriendRelation",
        "a_isContactBidirectionRelation",
        "a_isFamilyRelation"
    ]

def get_relation_u2a_attr_list():
    return [
        "u2a_realshow",
        "u2a_click",
        "u2a_like",
        "u2a_follow",
        "u2a_comment",
        "u2a_shortview",
        "u2a_profile",
        "u2a_hate",
        "u2a_playtime"
    ]

def get_relation_ua_rel_attr_list():
    return [
        "ua_comm_rel_from_redis_valid",
        "a_commonFriendCount_v2",
        "a_commonFollowCount_v2",
        "a_commonContactCount_v2",
        "a_commonContactReverseCount_v2",
        "a_commonWechatCount_v2",
        "a_commonQqCount_v2"
    ]

def get_relation_ua_cross_common_attr_list():
    return [
        'hour_ctr_1d', 'hour_ltr_1d', 'hour_cmtr_1d', 'hour_pptr_1d',
        'hour_ctr_7d', 'hour_ltr_7d', 'hour_cmtr_7d', 'hour_pptr_7d',
        'hour_ctr_30d', 'hour_ltr_30d', 'hour_cmtr_30d', 'hour_pptr_30d'
    ]

def get_relation_ua_cross_item_attr_list():
    return [
        'ptype_ctr_1d', 'ptype_ltr_1d', 'ptype_cmtr_1d', 'ptype_pptr_1d',
        'a_gender_age_ctr_1d', 'a_gender_age_ltr_1d', 'a_gender_age_cmtr_1d', 'a_gender_age_pptr_1d',
        'follow_gap_ctr_1d', 'follow_gap_ltr_1d', 'follow_gap_cmtr_1d', 'follow_gap_pptr_1d',
        'fans_cnt_ctr_1d', 'fans_cnt_ltr_1d', 'fans_cnt_cmtr_1d', 'fans_cnt_pptr_1d',

        'ptype_ctr_7d', 'ptype_ltr_7d', 'ptype_cmtr_7d', 'ptype_pptr_7d',
        'a_gender_age_ctr_7d', 'a_gender_age_ltr_7d', 'a_gender_age_cmtr_7d', 'a_gender_age_pptr_7d',
        'follow_gap_ctr_7d', 'follow_gap_ltr_7d', 'follow_gap_cmtr_7d', 'follow_gap_pptr_7d',
        'fans_cnt_ctr_7d', 'fans_cnt_ltr_7d', 'fans_cnt_cmtr_7d', 'fans_cnt_pptr_7d',

        'ptype_ctr_30d', 'ptype_ltr_30d', 'ptype_cmtr_30d', 'ptype_pptr_30d',
        'a_gender_age_ctr_30d', 'a_gender_age_ltr_30d', 'a_gender_age_cmtr_30d', 'a_gender_age_pptr_30d',
        'follow_gap_ctr_30d', 'follow_gap_ltr_30d', 'follow_gap_cmtr_30d', 'follow_gap_pptr_30d',
        'fans_cnt_ctr_30d', 'fans_cnt_ltr_30d', 'fans_cnt_cmtr_30d', 'fans_cnt_pptr_30d'
    ]

def get_relation_info_v2_attr_list():
    return [
        "a_commonFriendCount_v2",
        "a_commonFollowCount_v2",
        "a_commonContactCount_v2",
        "a_commonContactReverseCount_v2",
        "a_commonWechatCount_v2",
        "a_commonQqCount_v2"
    ]
