#!/usr/bin/env python3
# coding=utf-8
"""
filename: common_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, kuiba api mixin
author: <EMAIL>
date: 2020-01-19 16:45:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .mio_enricher import *
from .mio_observer import *
from .mio_retriever import *

class MioApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 mio 相关的 Processor 接口，主要用于 mio/KAI 系列框架的训练和预估流程。
  涉及到的功能包括抽取特征 sign、拉 Embedding、执行预估等等。
  训练和预估使用同样的抽 sign 逻辑，可以尽可能地保证特征的在离线一致性。
  """

  def fetch_mio_embedding(self, **kwargs):
    """
    MioEmbeddingAttrEnricher
    ------
    通过 gRPC 访问 Mio 的 Parameter Server 拿到 sign 对应的 embedding 参数

    参数配置
    ------
    `kess_service`: [string] kess 服务名

    `kess_cluster`: [string] kess 集群名，默认 PRODUCTION

    `thread_num`: [int] gRPC 线程数，默认 1

    `shards`: [int] shard 数量

    `slots_inputs`: [list] 从哪些 item attr 读取 slots 信息，当且仅当 slot_as_attr_name 为 False 生效。

    `parameters_inputs`: [list] 从哪些 item attr 读取 parameters 信息，当且仅当 slot_as_attr_name 为 False 生效。

    `common_slots_inputs`: [list] 从哪些 common attr 读取 slots 信息，当且仅当 slot_as_attr_name 为 False 生效。

    `common_parameters_inputs`: [list] 从哪些 common attr 读取 parameters 信息，当且仅当 slot_as_attr_name 为 False 生效。

    `timeout_ms`: [int] gRPC 超时，单位毫秒，默认 10

    `max_signs_per_request`: [int] 每个 RPC 请求包含的最大 sign 个数，默认为 0，即不限制

    `slots_config`: [list] embedding 获取的结果存储配置，格式同之前 mio predict server 的 embedding_slots 或 embedding.slots_config

    `model_version_attr`: [string] 从哪个 common attr 中获取模型版本号，默认值是空字符串。模型的版本号应是一个 int64 的值，默认为 0

    `protocol`: [int] 访问 embedding 服务所使用的协议，默认为 0，协议的支持情况见 协议支持 部分。

    `save_result_as_tensor_output`: [bool] 把这个 req 中所有 sample 的 embedding 存到 PtrCommonAttr 中 batching 起来, 方便预估使用

    `direct_write`: [bool] protocol == 0 且 save_result_as_tensor_output == true 时, 打开该参数能够提升写 context 的吞吐

    `reco_embedding_feature`: [string] protocol == 4 时，用于指定查询的 embedding 在 colossus 中的列名，需要指定特征的 name_space 和 table_name ，“name_space.table_name.feature_name”

    `slot_names`: [list] 支持给每个 attr 设置显示的名字，需要包含 slot 和 name 两个字段，仅用于 Perfutil 上报。

    `slot_as_attr_name`: [bool] 是否使用 slot 作为 key 读取 sign，默认为 False。

    `slot_as_attr_name_prefix`: [string] 读取 signs 时的前缀，默认为空，当且仅当 slot_as_attr_name 为 True 时生效。

    `use_new_fill_impl`: [bool] 是否使用新的 tensor 填充实现（作用与过去的一样，只是兼容更多的数据格式）

    `emb_dtype`: [int] 存储的embeding数据类型，0: int16, 1:float, other:undefined

    `save_final_status_to_attr`: [string] 当本字段不为空时，会将拉取 embedding 最终状态保存在以 `save_final_status_to_attr` 作为名称的 int common attr 中；当拉取 embedding 的全部请求都成功时，最终状态为 0；当拉取 embedding 的全部请求部分成功时，最终状态为 1; 当拉取 embedding 的全部请求都失败时，最终状态为 2

    协议支持
    ------

    当前共实现了5种协议，对应的服务器端实现情况分别为：

    | protocol \ server                  | kuiba_shm_predict_server | shm_dnn_predict_server | bt_embedding_server | kvs_embedding_server | colossus_server |
    |:-----------------------------------|:-------------------------|:-----------------------|:--------------------|:---------------------|:----------------|
    | 0 (GetKuibaEmbedding)              | YES                      | NO                     | YES                 | NO                   | NO              |
    | 1 (FeatureEmbedding with Count)    | NO                       | YES                    | NO                  | NO                   | NO              |
    | 2 (FeatureEmbedding without Count) | NO                       | YES                    | YES                 | NO                   | NO              |
    | 3 (FPGAKVSEmbedding)               | NO                       | NO                     | NO                  | YES                  | NO              |
    | 4 (RecoEmbedding)                  | NO                       | NO                     | NO                  | NO                   | YES             |

    调用示例
    ------
    ``` python
    import yaml
    .fetch_mio_embedding(
        kess_service="grpc_kuibaShmEmbeddingServerNearbyLiveFastNerual",
        shards=1,
        max_signs_per_request=500,
        slots_inputs=["slots"],
        parameters_inputs=["parameters"],
        common_slots_inputs=["common_slots"],
        common_parameters_inputs=["common_parameters"],
        slot_names=[
          dict(slot=128, name="author_id"),
          dict(slot=26, name="photo_id"),
        ],
        slots_config=yaml.load(open("dnn_model.yaml"))["embedding"]["slots_config"])
    ```
    """
    self._add_processor(MioEmbeddingAttrEnricher(kwargs))
    return self

  def fetch_mio_embedding_lite(self, **kwargs):
    """
    MioEmbeddingAttrLiteEnricher
    ------
    通过 RPC 访问 Mio 的 Parameter Server 拿到 sign 对应的 embedding 参数

    从 MioEmbeddingAttrEnricher 精简而来，去掉了不常用的协议，支持了统一存储 (colossusdb embedding server) 的访问

    所有协议都支持 cache 和 direct_write 等 infer 端优化功能，后续的开发者要扩展协议的话也要保证这一点

    参数配置
    ------

    `protocol`: [int] 访问 embedding 服务所使用的协议，默认为 0，协议的支持情况见 协议支持 部分。

    `slots_inputs`: [list] 从哪些 item attr 读取 slots 信息，当且仅当 slot_as_attr_name 为 False 生效。

    `parameters_inputs`: [list] 从哪些 item attr 读取 parameters 信息，当且仅当 slot_as_attr_name 为 False 生效。

    `common_slots_inputs`: [list] 从哪些 common attr 读取 slots 信息，当且仅当 slot_as_attr_name 为 False 生效。

    `common_parameters_inputs`: [list] 从哪些 common attr 读取 parameters 信息，当且仅当 slot_as_attr_name 为 False 生效。

    `timeout_ms`: [int] RPC 超时，单位毫秒，默认 10

    `max_signs_per_request`: [int] 每个 RPC 请求包含的最大 sign 个数，默认为 0，即不限制

    `slots_config`: [list] embedding 获取的结果存储配置，格式同之前 mio predict server 的 embedding_slots 或 embedding.slots_config

    `save_result_as_tensor_output`: [bool] 把这个 req 中所有 sample 的 embedding 存到 PtrCommonAttr 中 batching 起来, 方便预估使用

    `direct_write`: [bool] protocol == 0 且 save_result_as_tensor_output == true 时, 打开该参数能够提升写 context 的吞吐

    `slot_names`: [list] 支持给每个 attr 设置显示的名字，需要包含 slot 和 name 两个字段，仅用于 Perfutil 上报。

    `slot_as_attr_name`: [bool] 是否使用 slot 作为 key 读取 sign，默认为 False。

    `slot_as_attr_name_prefix`: [string] 读取 signs 时的前缀，默认为空，当且仅当 slot_as_attr_name 为 True 时生效。

    `use_new_fill_impl`: [bool] 是否使用新的 tensor 填充实现（作用与过去的一样，只是兼容更多的数据格式）

    `emb_dtype`: [int] 存储的 embeding 数据类型，0: int16, 1:float, other:undefined

    以下为 protocol == 0 时的对应配置

    `kess_service`: [string] kess 服务名

    `kess_cluster`: [string] kess 集群名，默认 PRODUCTION

    `thread_num`: [int] gRPC 线程数，默认 1

    `shards`: [int] shard 数量

    `model_version_attr`: [string] 从哪个 common attr 中获取模型版本号，默认值是空字符串。模型的版本号应是一个 int64 的值，默认为 0

    `save_final_status_to_attr`: [string] 当本字段不为空时，会将拉取 embedding 最终状态保存在以 `save_final_status_to_attr` 作为名称的 int common attr 中；当拉取 embedding 的全部请求都成功时，最终状态为 0；当拉取 embedding 的全部请求部分成功时，最终状态为 1; 当拉取 embedding 的全部请求都失败时，最终状态为 2

    以下为 protocol == 1 时的对应配置，下游服务信息会从自定义的路由表中获取

    `colossusdb_embd_service_name`: [string] 下游 colossusdb embedding server 的服务名，一般是: {模型名称}.
      获取服务名的方法详见文档[获取Embedding](https://docs.corp.kuaishou.com/k/home/<USER>/fcAB1k2CwkZRcxQ09lTBYMRkH)

    `colossusdb_embd_table_name`: [string] 下游 colossusdb embedding server 的表名，如 test-table

    `colossusdb_use_kconf_client`: [bool] 是否用 kconf_client 包装在原生 client 外面，默认为 true.
      如果设为 false, 则一般情况下应将上述的 colossusdb_embd_service_name 修改为 grpc_clsdb_ps-{模型名称}, colossusdb_embd_table_name 不变。
      详见文档[统一存储流量治理功能说明](https://docs.corp.kuaishou.com/d/home/<USER>

    协议支持
    ------

    当前共实现了2种协议，对应的服务器端实现情况分别为：

    | protocol \ server       | bt_embedding_server | colossusdb_embedding_server |
    |:------------------------|:--------------------|:----------------------------|
    | 0 (GetKuibaEmbedding)   | YES                 | NO                          |
    | 1 (统一存储)             | NO                  | YES                         |

    调用示例
    ------
    ``` python
    import yaml
    .fetch_mio_embedding_lite(
        kess_service="grpc_kuibaShmEmbeddingServerNearbyLiveFastNerual",
        shards=1,
        max_signs_per_request=500,
        slots_inputs=["slots"],
        parameters_inputs=["parameters"],
        common_slots_inputs=["common_slots"],
        common_parameters_inputs=["common_parameters"],
        slot_names=[
          dict(slot=128, name="author_id"),
          dict(slot=26, name="photo_id"),
        ],
        slots_config=yaml.load(open("dnn_model.yaml"))["embedding"]["slots_config"])

    .fetch_mio_embedding_lite(
        protocol=1,
        colossusdb_embd_service_name="grpc_clsdb_ps-reco-arch-test",
        colossusdb_embd_table_name="test_table",
        max_signs_per_request=500,
        timeout_ms=50,
        slots_inputs=["slots"],
        parameters_inputs=["parameters"],
        common_slots_inputs=["common_slots"],
        common_parameters_inputs=["common_parameters"],
        slot_names=[
          dict(slot=128, name="author_id"),
          dict(slot=26, name="photo_id"),
        ],
        slots_config=yaml.load(open("dnn_model.yaml"))["embedding"]["slots_config"])
    ```
    """
    self._add_processor(MioEmbeddingAttrLiteEnricher(kwargs))
    return self

  def fetch_local_mio_embedding(self, **kwargs):
    """
    MioLocalEmbeddingAttrEnricher
    ------
    查询本地 EmbeddingTable 拿到 sign 对应的 embedding 参数 , 除了查询 embeding ，其他逻辑继承 MioEmbeddingAttrEnricher

    参数配置
    ------
    基本与 MioEmbeddingAttrEnricher 相同, 不同点:
      1. 不需要配置 embeding server 的相关配置: kess name / shard / timeout
      2. protocol 必须为 5

    调用示例
    ------
    ``` python
    import yaml
    .fetch_local_mio_embedding(
        slots_inputs=["slots"],
        parameters_inputs=["parameters"],
        common_slots_inputs=["common_slots"],
        common_parameters_inputs=["common_parameters"],
        slot_names=[
          dict(slot=128, name="author_id"),
          dict(slot=26, name="photo_id"),
        ],
        slots_config=yaml.load(open("dnn_model.yaml"))["embedding"]["slots_config"])
    ```
    """
    self._add_processor(MioLocalEmbeddingAttrEnricher(kwargs))
    return self


  def fetch_mio_external_embedding(self, **kwargs):
    """
    MioExternalEmbeddingAttrEnricher
    ------
    访问存储在 redis 中的 embedding 参数

    参数配置
    ------
    `redis_cluster`: [string] redis 集群名

    `timeout_ms`: [int] redis 请求超时时间，单位毫秒，默认 100

    `redis_client`: [string] redis 客户端类型，默认为 kcc_zk，还可以填 kcc_kconf，通常不用改。

    `use_sync_client`: [bool] 是否使用 sync 版的 redis 客户端，默认为 false，使用之前咨询 kcc oncall。

    `slots_config`: [list] embedding 获取的结果存储配置，格式同之前 mio predict server 的
    embedding_slots 或 embedding.slots_config

    `slots_inputs`: [list] 从哪些 item attr 读取 slots 信息

    `parameters_inputs`: [list] 从哪些 item attr 读取 parameters 信息

    `common_slots_inputs`: [list] 从哪些 common attr 读取 slots 信息

    `common_parameters_inputs`: [list] 从哪些 common attr 读取 parameters 信息

    调用示例
    ------
    ``` python
    import yaml
    .fetch_mio_external_embedding(
        redis_cluster="no_such_cluster",
        slots_inputs=["slots"],
        parameters_inputs=["parameters"],
        common_slots_inputs=["common_slots"],
        common_parameters_inputs=["common_parameters"],
        slots_config=yaml.load(open("dnn_model.yaml"))["embedding"]["slots_config"])
    ```
    """
    self._add_processor(MioExternalEmbeddingAttrEnricher(kwargs))
    return self

  def mio_predict(self, **kwargs):
    """
    MioPredictItemAttrEnricher
    ------
    通过 Mio 模型进行预估（只支持通过 tensorflow 进行预估）

    参数配置
    ------
    `inputs`: [dict] 模型输入配置，包括 `attr_name` [string]，`tensor_name` [string], `common` [bool] (optional 默认为 False), `dim` [int]。将根据 `common` 从 common attr 或 item attr 的 `attr_name` 取出参数，构造成宽度为 `dim` 的矩阵放到计算图的 `tensor_name` 中执行模型。

    `outputs`: [dict] 模型输出配置，包括 `attr_name` [string]，`tensor_name` [string]，将计算图中的 `tensor_name` 放到 item attr 的 `attr_name` 中。

    `key`: [string] 模型唯一标识符，如果有多个模型需要指定不同的 key。

    `graph`: [string] 计算图 uri，支持 base64:// 和 file:// 两种 scheme，不指定 scheme 视为路径。

    `queue_prefix`: [string] 接收模型的 BTQueue topic 前缀。

    `receive_dnn_model_as_macro_block`: [bool] 按 MacroBlock 从 BTQueue 接收模型数据，缺省为 false，注意当 gflag 为 true 时，这个选项将失效，永远为 true。

    `flatten_outputs`: [bool] 是否将长度为 1 的 float list attr 输出转换为 float attr (optional，默认为 False)

    `output_common_attr` : [bool] 默认false，是否将预估结果写入 context 的 CommonAttr ，例如将 user embedding predict结果写入context

    `rowmajor`: [bool] BTQueue 传输过来的参数是否是 Row-Major 的，默认为 False，即 Column-Major。（当前只有 simple-mio 支持 Column-Major 的参数）

    调用示例
    ------
    ``` python
    import mio_tensorflow.patch as mio_tensorflow_patch
    mio_tensorflow_patch()

    import base64
    import tensorflow as tf

    ...

    .mio_predict(
        graph=base64.b64encode(tf.get_default_graph().as_graph_def().SerializeToString()),
        queue_prefix='nearby_photo_v6_test',
        output_common_attr = False,
        inputs=[{
          "attr_name": "user_input",
          "tensor_name": "user_input",
          "common": True,
          "dim": 64,
        }, {
          "attr_name": "photo_input",
          "tensor_name": "photo_input",
          "dim": 128,
        }],
        outputs=[{
          "attr_name": "ctr",
          "tensor_name": "mul:0",
        }, {
          "attr_name": "ltr",
          "tensor_name": "mul_1:0",
        }],
    ```
    """
    self._add_processor(MioPredictItemAttrEnricher(kwargs))
    return self

  def tensorrt_predict(self, **kwargs):
    """
    TensorrtPredictItemAttrEnricher
    ------
    通过 TensorRT 模型进行预估（为了提高吞吐量, 会自动 batching 前后的请求）

    参数配置
    ------
    同上 mio_predict, 增加以下配置:

    `tensorrt_config`: [dict] TensorRT 的 config, 目前只需要传入一个空 dict {} 即可使用默认配置
    `log_input_tensor_attr_suffix`: [string] 调试时可将输入 tensor 写回到某个 attr，此配置为 attr name 的后缀


    调用示例
    ------
    ``` python
    import mio_tensorflow.patch as mio_tensorflow_patch
    mio_tensorflow_patch()

    import base64
    import tensorflow as tf
    from tensorrt_optimizer import Optimize

    # ... some preceding processors

    .tensorrt_predict(
      graph=Optimize(base64.b64encode(tf.get_default_graph().as_graph_def() \
        .SerializeToString().decode('ascii')), output_nodes),
      queue_prefix='nearby_photo_v6_test',
      use_tensorrt=True,
      tensorrt_config={},
      output_common_attr = False,
      inputs=[{
        "attr_name": "user_input",
        "tensor_name": "user_input",
        "common": True,
        "dim": 64,
      }, {
        "attr_name": "photo_input",
        "tensor_name": "photo_input",
        "dim": 128,
      }],
      outputs=[{
        "attr_name": "ctr",
        "tensor_name": "mul:0",
      }, {
        "attr_name": "ltr",
        "tensor_name": "mul_1:0",
      }],
      # ... some other attrs
    )
    ```
    """
    self._add_processor(TensorrtPredictItemAttrEnricher(kwargs))
    return self

  def kai_predict(self, **kwargs):
    """
    KaiPredictItemAttrEnricher
    ------
    通过新框架 python 中控训练的模型进行预估，需要使用此 processor

    参数配置
    ------
    同上 mio_predict, 去掉 graph 的配置; 新框架 python 中控进行模型训练会将模型的网络部分 freeze 并通过 tf saved mode 格式打到 btq 中，
    线上预估直接去 btq 中读取模型的网络部分即可(计算图和参数在一起); 另外默认不要设 receive_dnn_model_as_macro_block，或者请设置为 false

    调用示例
    ------
    ``` python
    import mio_tensorflow.patch as mio_tensorflow_patch
    mio_tensorflow_patch()

    import tensorflow as tf

    # ... some preceding processors

    .kai_predict(
      queue_prefix='nearby_photo_v6_test',
      output_common_attr = False,
      inputs=[{
        "attr_name": "user_input",
        "tensor_name": "user_input",
        "common": True,
        "dim": 64,
      }, {
        "attr_name": "photo_input",
        "tensor_name": "photo_input",
        "dim": 128,
      }],
      outputs=[{
        "attr_name": "ctr",
        "tensor_name": "mul:0",
      }, {
        "attr_name": "ltr",
        "tensor_name": "mul_1:0",
      }],
      # ... some other attrs
    )
    ```
    """
    self._add_processor(KaiPredictItemAttrEnricher(kwargs))
    return self

  def extract_with_ks_sign_feature(self, **kwargs):
    """
    MioKsSignFeatureEnricher
    ------
    使用 KsSignFeature 进行特征抽取

    参数配置
    ------
    `feature_list`: [list] 特征列表。

    `extractor_kconf_path`: [string] 从 kconf 读取多服务的特征列表，与 feature_list 同时存在时，优先使用 kconf.

    `caller_model`: [string] 动态参数，当前调用的模型，与 kconf 中的服务名对应。

    `common_slots_output`: [string] User 侧 slots 输出到给定 common attr，默认不抽取 User 侧特征。

    `common_parameters_output`: [string] User 侧 parameters 输出到给定 common attr，默认不抽取 User 侧特征。

    `item_slots_output`: [string] Item 侧 slots 输出到给定 item attr，默认不抽取 User 侧特征。

    `item_parameters_output`: [string] Item 侧 parameters 输出到给定 item attr，默认不抽取 User 侧特征。

    `slot_as_attr_name`: [bool] 是否将 slot 作为 attr name，sign 作为 value，如果为 True 则 common_slots_output，common_parameters_output，item_slots_output，item_parameters_output 都需要为空。

    `slot_as_attr_name_prefix`: [string] 当 slot_as_attr_name 为 True 时生效，用于向 key 添加固定前缀，以在使用多种格式数据时(例: mio, kuiba, klearn) 防止 key 名冲突，勿用数字，默认留空。

    `user_info_attr`: [string] 从给定 common attr 获取 UserInfo，默认留空。

    `photo_id_attr`: [string] 从给定 item attr 获取 photo id, 无需依赖 PhotoInfo 即可抽取 pid 相关 feature sign，默认留空。

    `photo_info_attr`: [string] 从给定 item attr 获取 PhotoInfo，默认留空。

    `context_info_attr`: [string] 从给定 item attr 获取 ContextInfo，默认留空。

    `retrieve_info_attr`: [string] 从给定 item attr 获取 RetrieveInfo，默认留空。

    `click_pids_attr`: [string] 从给定 common attr 获取 click list，需要是 Int List，默认留空。

    `like_pids_attr`: [string] 从给定 common attr 获取 like list，需要是 Int List，默认留空。

    `follow_ids_attr`: [string] 从给定 common attr 获取 follow list，需要是 Int List，默认留空。

    `tab_id_attr`: [string] 从给定 common attr 获取 tab_id，需要是 Int，默认或取不到则不赋值。

    `page_attr`: [string] 从给定 item attr 获取 page，需要是 Int，默认或取不到则不赋值（为1）。

    `rank_attr`: [string] 从给定 item attr 获取 rank，需要是 Int，默认或取不到则不赋值（为0）。

    `is_living_attr`: [string] 从给定 item attr 获取 is_living，需要是 Int，默认或取不到则不赋值。

    `pctr_attr`: [string] 从给定 item attr 获取 pctr，需要是 Double，默认或取不到则不赋值。

    `pltr_attr`: [string] 从给定 item attr 获取 pltr，需要是 Double，默认或取不到则不赋值。

    `reason_attr`: [string] 从给定 item attr 获取 reason，需要是 String，默认或取不到则不赋值。

    `cascade_pctr_attr`: [string] 从给定 item attr 获取 cascade_pctr，需要是 Double，默认或取不到则不赋值（注意这个值会覆盖 ContextInfo 里的 cascade_pctr 字段）。

    `cascade_plvtr_attr`: [string] 从给定 item attr 获取 cascade_plvtr，需要是 Double，默认或取不到则不赋值（注意这个值会覆盖 ContextInfo 里的 cascade_plvtr 字段）。

    `cascade_psvr_attr`: [string] 从给定 item attr 获取 cascade_psvr，需要是 Double，默认或取不到则不赋值（注意这个值会覆盖 ContextInfo 里的 cascade_psvr 字段）。

    `cascade_pltr_attr`: [string] 从给定 item attr 获取 cascade_pltr，需要是 Double，默认或取不到则不赋值（注意这个值会覆盖 ContextInfo 里的 cascade_pltr 字段）。

    `cascade_pwtr_attr`: [string] 从给定 item attr 获取 cascade_pwtr，需要是 Double，默认或取不到则不赋值（注意这个值会覆盖 ContextInfo 里的 cascade_pwtr 字段）。

    `cascade_pftr_attr`: [string] 从给定 item attr 获取 cascade_pftr，需要是 Double，默认或取不到则不赋值（注意这个值会覆盖 ContextInfo 里的 cascade_pftr 字段）。

    `source_photo_info_attr`: [string] 从给定 common attr 获取 source_photo_info，需要是 String，默认或取不到则不赋值。

    `cas_xtr_pos_ptr_attr`: [string] 从给定 item attr 获取 cas_xtr_pos_ptr，需要是 Double，默认或取不到则不赋值（注意这个值会覆盖 ContextInfo 里的 cas_xtr_pos_ptr 字段）。

    `cas_final_pos_ptr_attr`: [string] 从给定 item attr 获取 cas_final_pos_ptr，需要是 Double，默认或取不到则不赋值（注意这个值会覆盖 ContextInfo 里的 cas_final_pos_ptr 字段）。
    调用示例
    ------
    ``` python


    .extract_with_ks_sign_feature(
        ks_sign_feature_filename="ks_sign_feature.txt",
        common_slots_output='common_slots',
        common_parameters_output='common_parameters',
        item_slots_output='item_slots',
        item_parameters_output='item_parameters',
        user_info_attr='user_info',
        photo_id_attr='pId',
        photo_info_attr='photo_info',
        context_info_attr='context_info',
        retrieve_info_attr='retrieve_info',
        click_pids_attr='click_pids',
        like_pids_attr='like_pids',
        follow_ids_attr='follow_ids',
        page_attr='page',
        rank_attr='rank',
        tab_id_attr='tab_id',
        is_living_attr='is_living',
        pctr_attr='pctr',
        pltr_attr='pltr',
        reason_attr='reason')
    ```
    """
    self._add_processor(MioKsSignFeatureEnricher(kwargs))
    return self

  def extract_with_oversea_sign_feature(self, **kwargs):
    """
    MioOverseaSignFeatureEnricher
    ------
    使用 KsSignFeature 进行特征抽取

    参数配置
    ------
    `feature_list`: [list] 特征列表。

    `common_slot_with_non_user_photo`: [bool] User侧是否抽取non_photo_non_user特征，请谨慎设置！！！默认false。

    `common_slots_output`: [string] User 侧 slots 输出到给定 common attr，默认不抽取 User 侧特征。

    `common_parameters_output`: [string] User 侧 parameters 输出到给定 common attr，默认不抽取 User 侧特征。

    `item_slots_output`: [string] Item 侧 slots 输出到给定 item attr，默认不抽取 User 侧特征。

    `item_parameters_output`: [string] Item 侧 parameters 输出到给定 item attr，默认不抽取 User 侧特征。

    `reader_info_attr`: [string] 从给定 common attr 获取 ReaderInfo，默认留空。

    `photo_info_attr`: [string] 从给定 item attr 获取 PhotoInfo，默认留空。

    `context_info_attr`: [string] 从给定 item attr 获取 ContextInfo，默认留空。

    `common_sample_attrs`: [list] common sample attr 列表，抽取用户特征时的额外输入，默认留空。

    `item_sample_attrs`: [list] item sample attr 列表，抽取 item 特征时的额外输入，默认留空。

    调用示例
    ------
    ``` python
    .extract_with_oversea_sign_feature(
        feature_list=[
            "ExtractOverseaSignUserId",
            "ExtractOverseaSignAfterClickUserId",
            "ExtractOverseaSignAfterClickUserIdV2",
            "ExtractOverseaSignUserDeviceId",
        ],
        common_slot_with_non_user_photo=False,
        common_slots_output='common_slots',
        common_parameters_output='common_parameters',
        item_slots_output='item_slots',
        item_parameters_output='item_parameters',
        reader_info_attr='reader_info',
        photo_info_attr='photo_info',
        context_info_attr='context_info')
    ```
    """
    self._add_processor(MioOverseaSignFeatureEnricher(kwargs))
    return self

  def extract_with_dense_feature(self, **kwargs):
    """
    MioDenseFeatureEnricher
    ------
    使用 MioDenseFeature 进行 dense 特征抽取

    参数配置
    ------
    `config`: [list] dense 特征抽取配置。配置格式: slot_id, 特征名, 抽取类名, is_common(0-非用户特征/1-用户特征), 其他输入参数

    调用示例
    ------
    ``` python
    .extract_with_dense_feature(
        config=[
            "100000001,eltr,ExtractDenseFeatureXTR,0,real_show_count;like_count;4.0;100.0",
        ])
    ```
    """
    self._add_processor(MioDenseFeatureEnricher(kwargs))
    return self

  def extract_with_ksib_sign_feature(self, **kwargs):
    """
    MioKsibSignFeatureEnricher
    ------
    使用 KsSignFeature 进行特征抽取

    参数配置
    ------
    `feature_list`: [list] 特征列表。

    `common_slot_with_non_user_photo`: [bool] User侧是否抽取non_photo_non_user特征，请谨慎设置！！！默认false。

    `common_slots_output`: [string] User 侧 slots 输出到给定 common attr，默认不抽取 User 侧特征。

    `common_parameters_output`: [string] User 侧 parameters 输出到给定 common attr，默认不抽取 User 侧特征。

    `item_slots_output`: [string] Item 侧 slots 输出到给定 item attr，默认不抽取 User 侧特征。

    `item_parameters_output`: [string] Item 侧 parameters 输出到给定 item attr，默认不抽取 User 侧特征。

    `slot_as_attr_name`: [bool] 是否将 slot 作为 attr name，sign 作为 value，如果为 True 则 common_slots_output，common_parameters_output，item_slots_output，item_parameters_output 都需要为空。

    `slot_as_attr_name_prefix`: [string] 当 slot_as_attr_name 为 True 时生效，用于向 key 添加固定前缀，以在使用多种格式数据时(例: mio, kuiba, klearn) 防止 key 名冲突，勿用数字，默认留空。

    `reader_info_attr`: [string] 从给定 common attr 获取 ReaderInfo，默认留空。

    `photo_info_attr`: [string] 从给定 item attr 获取 PhotoInfo，默认留空。

    `context_info_attr`: [string] 从给定 item attr 获取 ContextInfo，默认留空。

    `request_info_attr`: [string] 从给定 common attr 获取 RequestInfo，默认留空。

    `colossus_sim_attr`: [string] 从 colossus 取回的 sim 特征原始内容，默认不抽取 sim 特征。

    `common_sample_attrs`: [list] common sample attr 列表，从 Leaf Context 取 SampleAttr 字段写入 SampleInfo，默认留空。

    `item_sample_attrs`: [list] item sample attr 列表，从 Leaf Context 取 SampleAttr 字段写入 SampleInfo，默认留空。

    `user_attrs`: [list] 从 Leaf Context 取 common_attrs 字段写入 SampleInfo 的 user_attrs 中，默认留空。

    `photo_attrs`: [list] 从 Leaf Context 取 item_attrs 字段写入 SampleInfo 的 photo_attrs 中，默认留空

    `context_attrs`: [list] 从 Leaf Context 取 item_attrs 字段写入 SampleInfo 的 context_attrs 中，默认留空

    `save_all_proto_attr`: [bool] 是否从 reader_info photo_info context_info 取所有 attr 到 SampleInfo 中，默认为 True。

    调用示例
    ------
    ``` python
    .extract_with_ksib_sign_feature(
        feature_list=[
            "ExtractKsibSignUserId",
            "ExtractKsibSignAfterClickUserId",
            "ExtractKsibSignAfterClickUserIdV2",
            "ExtractKsibSignUserDeviceId",
        ],
        common_slot_with_non_user_photo=False,
        common_slots_output='common_slots',
        common_parameters_output='common_parameters',
        item_slots_output='item_slots',
        item_parameters_output='item_parameters',
        reader_info_attr='reader_info',
        photo_info_attr='photo_info',
        context_info_attr='context_info',
        colossus_sim_attr='snack_long_term_res')
    ```
    """
    self._add_processor(MioKsibSignFeatureEnricher(kwargs))
    return self

  def extract_with_ksib_redis_feature(self, **kwargs):
    """
    MioKsibRedisFeatureEnricher
    ------
    使用 MioKsibRedisFeatureEnricher 进行特征抽取

    参数配置
    ------
    `common_redis_feature_output`: [string] common redis feature 特征输出到给定 common attr ，默认不抽取 redis 特征。

    `item_redis_feature_output`: [string] item redis feature 特征输出到给定 item attr ，默认不抽取 redis 特征。

    `feature_config_path`: [string] 特征配置文件。

    `common_feature_prefix`: [string] 抽取 common redis 特征的 key 前缀。

    `item_feature_prefix`: [string] 抽取 item redis 特征的 key 前缀。

    `redis_zpath`: [string] 抽取物品 redis 特征的 redis zpath。

    `redis_zk_addr`: [string] 抽取物品 redis 特征的 redis zk_addr。

    `redis_cache_time`: [string] 抽取物品 redis 特征的 cache_time，默认7200。

    `redis_cache_size`: [string] 抽取物品 redis 特征的 cache_size，默认10000000。

    `reader_info_attr`: [string] 从给定 common attr 获取 ReaderInfo，默认留空。

    `photo_info_attr`: [string] 从给定 item attr 获取 PhotoInfo，默认留空。

    `context_info_attr`: [string] 从给定 item attr 获取 ContextInfo，默认留空。

    调用示例
    ------
    ``` python
    .extract_with_ksib_redis_feature(
        common_redis_feature_output='common_redis_feature_output',
        item_redis_feature_output='item_redis_feature_output',
        feature_config_path='./redis_feature_config.yaml',
        common_feature_prefix='user_v4_',
        item_feature_prefix='item_v4_',
        redis_zpath='/ks2/redis/kwaiRecoFeature/_YZ',
        redis_zk_addr='config.zk.cluster.yz:2181',
        redis_cache_time='7200',
        redis_cache_size='100000',
        reader_info_attr='reader_info',
        photo_info_attr='photo_info',
        context_info_attr='context_info')
    ```
    """
    with open(kwargs['feature_config_path'], 'r') as f:
      kwargs['feature_config_content'] = f.read()
    kwargs['feature_config_path'] = kwargs['feature_config_path'].split('/')[-1]
    self._add_processor(MioKsibRedisFeatureEnricher(kwargs))
    return self


  def ksib_rewrite_slot_in_sign(self, **kwargs):
    """
    MioKsibRewriteSlotIdEnricher
    ------
    用于 in-place 改写 sign 里面的 slot 实现可配置式 share embedding。
    - slot_as_attr_name = False: 通常接在特征抽取 processor 后，比如：extract_with_ksib_sign_feature。
    - slot_as_attr_name = True: 通常接在特征读取 processor 后，比如：read_cofea_sample。

    注意事项
    ------
    - 该 processor 默认遵循 mio 的 sign 格式，高四位不为空则 slot = sign >> 60，否则 slot = (sign >> 48) + 16。
    - mio 保留高四位用于长 id 拼 sign，用户需确认长 id 在改写时不会因 slot 由占高四位变为占高十六位而被覆盖。
    - mio 保留高四位用于长 id 拼 sign 的代价是 2**16 大小的 slot 空间被缩减成 2**4 + 2**12，请确保 slot 值不超过 2**12 + 15。

    参数配置
    ------
    `common_slots_attr`: [string] User 侧 slots 输出时给定的 common attr 名。
    `common_signs_attr`: [string] User 侧 parameters(signs) 输出时给定的 common attr 名。
    `item_slots_attr`: [string] Item 侧 slots 输出时给定的 item attr 名。
    `item_signs_attr`: [string] Item 侧 parameters(signs) 输出时给定的 item attr 名。
    `slot_as_attr_name`: [bool] 忽略上述四个字段，直接使用下方 map 信息中的 slot 为 key 取 attr，默认为 False。
    `slot_mapping`: [dict of int strings] 重写 sign 中 slot 时使用的新老 slot 映射关系，参数配置: "原始 slot_id": "rewrite slot_id:是否拷贝[0|1]"，默认不拷贝直接改写。
    `sign_format`: [string] sign格式，默认为mio
    调用示例
    ------
    ```python
    .ksib_rewrite_slot_in_sign(
      common_slots_attr='common_slots',
      common_signs_attr='common_signs',
      item_slots_attr='item_slots',
      item_signs_attr='item_signs',
      slot_as_attr_name=False,
      slot_mapping={
        "666": "886",
        "777": "887,888:1"
      }
    ```
    """
    self._add_processor(MioKsibRewriteSlotIdEnricher(kwargs))
    return self


  def send_to_mio_learner(self, **kwargs):
    """
    MioRecordChannelObserver
    ------
    构造 mio_learner::Record 并通过 mio::Channel 进行 IPC 通信发给 mio-learner。

    注意事项
    ------
    - 该 processor 仅允许使用在 mio-learner 的离线训练配置里，禁止用到线上，会导致阻塞引发严重的线上故障。
    - 该 processor 需要与 teams/reco-model/train/mio-learner-tf/ 配合使用，无法与其他版本的 mio-learner 一起使用。

    参数配置
    ------
    `attrs`: [list] 从给定的 common attr 或 item attr 获取 double (list) 或 int64 （list) 填到 Record 的 vec_feas

    `slots_attrs`: [list] 从给定 common attr 或 item attr 获取 slot，填到 Record 的 vec。

    `signs_attrs`: [list] 从给定 common attr 或 item attr 获取 sign，填到 Record 的 vec。

    `eval_attrs`: [list] 从给定 common attr 或 item attr 获取 int attr，当值大于 0 时该样本参与对应细分指标的评估，注意 mio-learner.yaml 里需要对应配置细分评估的名字，否则不生效。

    `lineid_attr`: [string] 从给定的 common attr 或 item attr 获取 int64，填到 Record 的 lineid，默认为空，
    使用 user_id。

    `user_hash_attr`: [string] 从给定的 common attr 或 item attr 获取 int64 或 string（计算 CityHash64），
    填到 Record 的 user_hash，默认为空，使用 device_id。

    `time_ms_attr`: [string] 从给定的 common attr 或 item attr 获取 int64，填到 Record 的 user_hash，默认为空，
    使用 request_time。

    `pid_attr`: [string] 从给定的 item attr 获取 int64，填到 Record 的 pid，默认为空，使用 item_key。

    `label_attr`: [string] 从给定的 item attr 获取 label，填到 Record 的 clk，如果给定的 int item attr 存在且值大于 0，
    则 clk 为 1，否则为 0。

    `slots`: [list] 用于过滤 slot，默认为读取所有 slot。

    `slot_as_attr_name`: [bool] 是否采用 slot 为 key 的格式读取 sign feature，当这个值为 True 时，slots_attrs 和 signs_attrs 必须为空。

    调用示例
    ------
    ``` python


    .send_to_mio_learner(
        attrs = ["user_id", "device_id", "photo_id"],
        slots_attrs = ["item_slots", "common_slots"],
        signs_attrs = ["item_signs", "common_signs"],
        user_hash_attr = "user_hash",
        label_attr = "click")
    ```
    """
    self._add_processor(MioRecordChannelObserver(kwargs))
    return self

  def generate_update_message(self, **kwargs):
    """
    MioUpdateMessageEnricher
    ------
    按 shard 生成 ks::model::ModelUpdateMessage

    参数配置
    ------
    `inputs`: [list] 配置输入项，成员为一个 dict，包含一个 sign_source_type字段，用于指定 sign 的来源，支持 user_id，device_id，user_id_or_device_id，common_attr，item_attr，默认为 item_attr。当 sign_source_type 为 item_attr/common_attr 时，需要给出 embedding_attr 和 sign_attr，否则仅需要给出 embedding_attr。除此之外，还可以指定 slot 或者 slot_attr，当 slot_attr 为空时使用 slot，slot 通常仅用于 Embedding Server 打点，当 id_converter 不为 plainIdConverter 时也用于对 sign 做处理，注意默认的 slot 为 -1，是一个非法值，当指定了 id_converter 时必须通过 slot 或 slot_attr 来指定 slot 的值。

    `shards`: [int] shard 数量。

    `max_items_per_update_message`: [int] 单个 ModelUpdateMessage 中最大的 item 数量。

    `min_items_per_update_message_batch_buffer`: [int] 单个 ModelUpdateMessage 中最最小的 item 数量。 当 [min_items_per_update_message_batch_buffer, max_items_per_update_message] 区间合法时，开启buffer模式 ， 一个message 至少攒 min_items_per_update_message_batch_buffer 个item才发送

    `save_result_to_common_attr`: [string] 将生成的 ModelUpdateMessage 存到指定的 Common Attr，实际存入时加上 shard 作为后缀。结果会以 String List 的格式存储，每个 String 是一个序列化的 ModelUpdateMessage。

    `expire_seconds`: [int] embedding 存储逐出时间，以秒为单位，默认为 30 小时。

    `id_converter`: [object] 需要包含一个 type_name 字段，支持 plainIdConverter, kuibaEmbeddingIdConverter, mioEmbeddingIdConverter，默认为 plainIdConverter。

    `use_raw_embedding`: [bool] 是否用 embedding 原样输出至 btq , 默认 false 、此时会进行 WeightToShort 转换

    `is_raw_embedding_list`: [bool] 当 use_raw_embedding 时用于指定输入 embedding 是 list 还是单值，不支持 string。

    `raw_embedding_type`: [string] 当 use_raw_embedding 时用于指定 attr 的类型。支持 float32, uint16, uint32, uint64, string。默认为 float32。

    `compress_type`: [string] 当 use_raw_embedding 为 false 用于指定 embedding 压缩的方式，支持 mio_int16, scale_int8, scale_int16，默认为 mio_int16。

    调用示例
    ------
    ``` python

    .generate_update_message(
        inputs = [dict(
            embedding_attr="embedding",
            sign_attr="sign",
            slot_attr="slot",
        )],
        save_result_to_common_attr = "update_message")
    ```
    """
    self._add_processor(MioUpdateMessageEnricher(kwargs))
    return self

  def mio_remote_predict(self, **kwargs):
    """
    MioRemotePredictItemAttrEnricher
    ------
    请求 mio 的老版本 predict server，结果写回 context。

    参数配置
    ------
    `kess_service`: [string] 请求的 predict server 的 kess name。

    `kess_cluster`: [string] 请求的 predict server 的 cluster，默认为 PRODUCTION。

    `shard_num`: [int] predict server 的 shard num，默认为 1，一次请求会分别请求所有 shard，每个 shard 返回部分结果。

    `timeout_ms`: [int] 超时时间，单位为毫秒，默认为 300。

    `user_info_attr`: [string] 从给定的 common attr 读取 UserInfo，支持 string 或者 UserInfo。

    `reco_photo_attr`: [string] 从给定的 item attr 读取 RecoPhotoInfo，支持 string 或者 RecoPhotoInfo。

    `pctr_attr`: [string] 将结果中的 pctr 写到给定 item attr，默认不写。

    `pltr_attr`: [string] 将结果中的 pltr 写到给定 item attr，默认不写。

    `pwtr_attr`: [string] 将结果中的 pwtr 写到给定 item attr，默认不写。

    `pftr_attr`: [string] 将结果中的 pftr 写到给定 item attr，默认不写。

    `phtr_attr`: [string] 将结果中的 phtr 写到给定 item attr，默认不写。

    `pvtr_attr`: [string] 将结果中的 pvtr 写到给定 item attr，默认不写。

    `extend_rate_attrs`: [list] 将结果中的 extend_rate 写到给定 item attr，默认不写。

    `preds_attrs`: [list] 将结果中的 preds 写到给定 item attr，支持 string 或 object，如果是 object 需要
    有 pred_name 字段，可以有一个 as 字段指定 attr name，默认不写。

    `debug`: [bool] 开启 debug 功能，慎用，会对被调方造成比较大的压力。

    `dnn_input_attr`: [string] 将返回的 debug_info 中的 dnn_input，存到给定的 item attr，仅 debug 启用时有效，
    默认不存。

    `feature_info_attr`: [string] 将返回的 debug_info 中的 feature_info，存到给定的 item attr，仅 debug 启用时有效，
    默认不存。

    调用示例
    ------
    ``` python

    .generate_update_message(
        inputs = [dict(
            embedding_attr="embedding",
            sign_attr="sign",
            slot_attr="slot",
        )],
        save_result_to_common_attr = "update_message")
    ```
    """
    self._add_processor(MioRemotePredictItemAttrEnricher(kwargs))
    return self

  def retrieve_from_uic_server(self, **kwargs):
    """
    MioUicServerRetriever
    ------
    访问 UicServer

    参数配置
    ------
    `kess_service`: [string] 请求的 UIC Server 的 kess name，默认为 grpc_UicListServerExp1

    `kess_cluster`: [string] 请求的 UIC server 的 cluster，默认为 PRODUCTION。

    `timeout_ms`: [int] 超时时间，单位为毫秒，默认为 100。

    `shard_num`: [int] UIC 的 shard 数量，默认为 16，根据 shard 选择正确的集群请求。

    `item_type`: [int] 生成的 item 的类型，默认为 0。

    `reason`: [int] 触发的 reason，默认为 0。

    `aid_attr`: [string] 存储 aid 的 item attr，默认不存。

    `play_attr`: [string] 存储 play 的 item attr，默认不存。

    `time_attr`: [string] 存储 time 的 item attr，默认不存。

    `pid_attr`: [string] 存储 pid 的 item attr，默认不存。

    `tab_attr`: [string] 存储 tab 的 item attr，默认不存。

    `slot_attr`: [string] 存储 slot 的 item attr，默认不存。

    `click_attr`: [string] 存储 click 的 item attr，默认不存。

    `like_attr`: [string] 存储 like 的 item attr，默认不存。

    `follow_attr`: [string] 存储 follow 的 item attr，默认不存。

    `forward_attr`: [string] 存储 forward 的 item attr，默认不存。

    `cmef_attr`: [string] 存储 cmef 的 item attr，默认不存。

    `download_attr`: [string] 存储 download 的 item attr，默认不存。

    `long_view_attr`: [string] 存储 long_view 的 item attr，默认不存。

    调用示例
    ------
    ``` python

    .retrieve_from_uic_server(
        pid_attr="pid",
        aid_attr="aid",
        play_attr="play",
        time_attr="time",
        tab_attr="tab",
        slot_attr="slot",
        click_attr="click",
        like_attr="like",
        follow_attr="follow",
        forward_attr="forward",
        cmef_attr="cmef",
        download_attr="download",
        long_view_attr="long_view")
    ```
    """
    self._add_processor(MioUicServerRetriever(kwargs))
    return self

  def mio_enrich_from_gsu(self, **kwargs):
    """
    MioGeneralSearchUnitEnricher
    ------
    调用基于 CommonLeaf 的 General Search Unit，返回用户的长期行为列表。

    参数配置
    ------
    `kess_service`: [string] 调用的 General Search Unit 的 kess service，默认为 grpc_recoGeneralSearchService

    `kess_cluster`: [string] 选填项，调用 CommonLeaf 的 kess cluster，默认为 PRODUCTION

    `thread_num`: [int] 选填项，gRPC 线程数，默认为 1

    `shard_num`: [int] 选填项，shard数，默认为 1

    `timeout_ms`: [int] 选填项，gRPC 超时时间，默认为 300ms。

    `send_item_attrs`: [list] 选填项，发送的 item attr 列表，默认不发送 item attr。

    `send_common_attrs`: [list] 选填项，发送的 common attr 列表，默认不发送 common attr。

    `recv_item_attrs`：[list] 选填项，接收的 item attr 列表，默认不接收 item attr。

    `recv_common_attrs`：[list] 选填项，接收的 common attr 列表，默认不接收 common attr，注意结果会存到 item attr 里。

    `save_item_list_to_item_attr`: [string] 存储收到的 item 列表到指定的 item attr，默认不存。

    `request_num`：[int] 选填项，请求的 request num，默认为 50。

    `request_type`：[string] 选填项，请求的 request type，默认为本 leaf 当前的 request type。

    调用示例
    ------
    ``` python
    .mio_enrich_from_gsu(
      send_common_attrs = [
        {"name": "_REQ_TIME_", "as": "target_time"},
      ],
      send_item_attrs = [
        {"name": "aid", "as": "target_aid"},
        {"name": "pid", "as": "target_pid"},
        {"name": "slot", "as": "target_slot"},
      ],
      save_item_list_to_item_attr = "long_term_list",
      recv_item_attrs = [],
      request_type = "default",
    )
    ```
    """
    self._add_processor(MioGeneralSearchUnitEnricher(kwargs))
    return self

  def send_to_simple_mio(self, **kwargs):
    """
    MioStringChannelObserver
    ------
    将 common string attr 通过 mio::Channel 发送给 simple-mio。

    通常来说这个 string attr 应该是 BatchedSamples(ks/cofea/proto/samples.fbs)

    注意事项
    ------
    - 该 processor 仅允许使用在 simple-mio 的离线训练配置里，禁止用到线上，会导致阻塞引发严重的线上故障。
    - 该 processor 需要与 teams/reco-model/train/simple-mio/ 配合使用。

    参数配置
    ------
    `input_attr`: [string] 输入的 common string attr 名。

    调用示例
    ------
    ``` python

    .send_to_simple_mio(input_attr="batched_samples")
    ```
    """
    self._add_processor(MioStringChannelObserver(kwargs))
    return self

  def mio_enrich_feature_embedding_tensor(self, **kwargs):
    """
    MioFeatureEmbeddingTensorAttrEnricher
    ------
    将特征存储格式从 DoubleList CommonAttr 转化成 TensorOutput CommonAttr

    参数配置
    ------
    `tensors` : [list[json]] 需要进行转换的特征配置, 每个 list 元素包含以下变量:
                attr_name : [string] 待转换的 DoubleList 格式特征的 common attr name
                tensor_name : [string] 转换后的 TensorOutput 格式的 common attr name
                common : [bool] 是否为 user 侧特征
                dim : 特征维度

    调用示例
    ------
    .mio_enrich_feature_embedding_tensor(tensors=[
      {attr_name:"xxx", tensor_name:"yyy", common=true, dim=128},
      {attr_name:"aaa", tensor_name:"bbb", common=true, dim=1024},
    ])
    """
    self._add_processor(MioFeatureEmbeddingTensorAttrEnricher(kwargs))
    return self

  def gpu_live_retrieval(self, **kwargs):
    """
    MioGpuLiveRetriever
    ------
    直播 embedding 填充模式:
    从多张 GPU 卡获取已缓存全量 live embedding 的首地址、数据长度、shape 信息，并构造 TensorBuffer -> Tensor -> TensorOutput CommonAttr

    直播召回部分item模式:
    从 common attr 获取 item no list，查询 live embedding 缓存获取相应的 embedding key 作为 item key 进行召回

    直播召回全部item模式:
    将 GPU 中缓存的 embedding 对应的 embedding key 作为 item key 全部召回

    参数配置
    ------
    `run_type` : [int] 0: embedding 填充模式 ，
                       1: 召回指定的 item ,
                       2: 召回全部 item ,
                       other ：未定义, 无默认值

    `gpu_live_embed` : [dict] gpu live embedding 双 buffer 更新的相关配置

                       - `embedding_queue` : [list[string]] live embedding 更新流 btqueue

                       - `remove_queue` : [list[string]] 关播信号所在的直播索引 btqueue

                       - `embedding_queue_thread_num` : [int] live embedding 更新线程数, 默认 4

                       - `remove_queue_thread_num` : [int] 删除关播 item 的线程数, 默认 4

                       - `memkv_shm_path` : [string] live embedding 存储的共享内存路径，默认 /dev/shm/live_embed

                       - `memkv_part_num` : [int] live embedding 内部空间分 part ，默认 16

                       - `memkv_expire_sec` : [int] live embedding 不更新时的强制过期时间，默认 3600 秒

                       - `memkv_capacity` : [int] live embedding 最大 kv 数量，默认 200000

                       - `memkv_mem_limit` : [int] live embedding 最大内存占用，默认 2G

                       - `update_interval_sec` : [int] live embedding 往 gpu copy 的间隔时间, 默认 60 秒

                       - `emb_dim` : [int] live embedding 单目标维数

                       - `emb_target_num` : [int] live embedding 多目标数量

                       - `min_emb_num` : [int] live embedding 最少积攒多少有效 item 才认为加载成功

                       - `rpc_service` : [string] 请求 service 获取当前所有在播 live id，用于过滤

                       - `rpc_cluster` : [string] rpc_service 对应的 cluster，默认 PRODUCTION

                       - `rpc_timeout_ms` : [int] rpc_service 对应的超时阈值

                       - `redis_cluster` : [string] 请求的 redis 集群名, 获取当前 live id 白名单，用于过滤，不在内的抛弃之，不设置时过滤功能关闭

                       - `redis_keys` : [list[string]] 请求 redis 的 key list， 不设置时过滤功能关闭

                       - `redis_value_get_type` : [int] 指定请求 redis 的方式, 目前只支持 = 0 : Get(string key, string &value)

                       - `redis_value_parse_type` : [int] 指定解析 value 的方式，目前只能支持 = 0 : 解析 num1,num2,...,numN 的数字字符序列序列

                       - `redis_biz` : [string] 访问 redis 集群所属业务，可以不填写, 默认为空

                       - `redis_io_threads` : [int] 访问 redis 单个 client 的 io 线程数，一般无需修改，默认为 2

                       - `redis_replica` : [bool] redis 集群是否开启双集群同步，一般无需修改，默认为 true

    embedding填充模式:

    `item_embedding_table_attr` : [string] live embedding 缓存 table 存入 common attr 的字段名

    `item_embedding_attr` : [string] 构造的 TensorOutput 在 common context 中的 attr name

    直播召回模式

    `item_embedding_table_attr` : [string] 获取 common attr 中 live embedding 缓存 table 指针

    `item_no_attr` : [string] (部分召回时) 获取 common attr 中 item no list

    `reason` : [int] 召回 reason , 默认 0

    `cluster_tensor_attr` : [string] 不为空时，填充 item 的 cluster tensor 到该字段指定的 common attr

    `default_cluster_id` : [int] item cluster id 缺失时，补充默认的 cluster id

    调用示例
    ------
    .gpu_live_retrieval(
      run_type=0,
      gpu_live_embed=dict(
        embedding_queue=["xxxx_live_emb"],
        remove_queue=["reco_index_builder_kuiba_live"],
        embedding_queue_thread_num=4,
        remove_queue_thread_num=2,
        update_interval_sec=120,
        emb_dim=128,
        emb_target_num=4,
        min_emb_num=80000,
        rpc_service="grpc_liveExposeAllLivingRpcService",
        rpc_timeout_ms=500,
        redis_cluster="recoExploreLiveDebugInfo",
        redis_keys=["LIVE_LEAF_ANN_LIVEID_WHITE_V4"],
        redis_value_get_type=0,
        redis_value_parse_type=0,
        redis_timeout_ms=1000,
        ),
      item_embedding_attr="all_live_embed",
      item_embedding_table_attr="live_embed_table",
    )\
    .blabla(....)\
    .gpu_live_retrieval(
      run_type=1,
      gpu_live_embed=dict(
        embedding_queue=["xxxx_live_emb"],
        remove_queue=["reco_index_builder_kuiba_live"],
        embedding_queue_thread_num=4,
        remove_queue_thread_num=2,
        update_interval_sec=120,
        emb_dim=128,
        emb_target_num=4,
        min_emb_num=80000,
        ),
      item_no_attr="item_nos",
      item_embedding_table_attr="live_embed_table",
    )\
    """
    self._add_processor(MioGpuLiveRetriever(kwargs))
    return self

  def load_emb_from_file(self, **kwargs):
    """
    MioLoadEmbFromFileEnricher
    ------
    从本地或 HDFS 上的一个 csv 文件读取 embedding 并存入全局 EmbeddingTable 中
    后续可以通过 get_local_embedding 来取出数据

    该 processor 只在初始化时执行相关逻辑，后续的调用不执行任何操作

    csv 文件的格式需要为：`id_field,weight1,weight2,...,weightk`
    其中 id_field 为原始特征名，需要配合特定的 SignFeature 抽出 sign，取出数据时使用同样的逻辑来抽 sign

    目前支持的抽 sign 方法有：
     - ExtractSignUserApplistRecent （id_field 为 App名称）

    参数配置
    ------
    `local_file_path` : [string] 本地 csv 文件的路径，优先级比 HDFS 文件更高

    `hdfs_file_path` : [string] hdfs 上 csv 文件的路径

    `feature_name` : [string] 抽 Sign 的类名

    `expire_timet` : [int] embedding 过期时间，单位为秒，默认为 365 天

    `queue_prefix`: [string] 模型用的 queue_prefix，可以为空字符串，代表不从 btq 消费数据

    `queue_shard_num`: [int] 模型用的 queue shard 数量

    `shard_offset`: [int] 模型 queue shard 的起始 id，默认为 0

    `sign_format`: [string] 模型 sign 的格式，默认为 kuiba，也可以选择 mio。

    `thread_num`: [int] 线程数，默认为 1（不从 btq 更新）

    `read_slots`: [string] 逗号分隔的 slot 列表，白名单过滤，默认为空，不过滤。

    调用示例
    ------
    .load_emb_from_hdfs(
      hdfs_file_path="xxx",
      feature_name="ExtractSignUserApplistRecent",
      queue_prefix="",
      queue_shard_num=0,
      shard_offset=0,
      sign_format="mio",
      thread_num=1,
    )
    """
    self._add_processor(MioLoadEmbFromFileEnricher(kwargs))
    return self

  def remote_model_enrich(self, **kwargs):
    """
    RemoteModelAttrEnricher
    ------
    通过 gRPC 访问 Model Predict Server 获取预估结果

    参数配置
    ------
    `kess_service`: [string] kess 服务名

    `kess_cluster`: [string] kess 集群名，默认 PRODUCTION

    `slots_inputs`: [list] 从哪些 item attr 读取 slots 信息

    `parameters_inputs`: [list] 从哪些 item attr 读取 parameters 信息

    `common_slots_inputs`: [list] 从哪些 common attr 读取 slots 信息

    `common_parameters_inputs`: [list] 从哪些 common attr 读取 parameters 信息

    `timeout_ms`: [int] gRPC 超时，单位毫秒，默认 10

    `return attrs`: [list] remote predict server 返回的 attr。

    ------
    ``` python
    import yaml
    .remote_model_enrich(
        kess_service="grpc_RemoteModelEnrichServer",
        slots_inputs=["slots"],
        parameters_inputs=["parameters"],
        common_slots_inputs=["common_slots"],
        common_parameters_inputs=["common_parameters"],
        return_attrs=[
          'evtr',
          'ctr'
        ])
    ```
    """
    self._add_processor(RemoteModelAttrEnricher(kwargs))
    return self

  def next_interest_predict_samples_generate_retriever(self, **kwargs):
    """
    NextInterestSampleRetriever
    ------

    参数配置
    ------
    `sampling_num`: [int] 循环采样次数

    `mask_rate`: [double] mask 兴趣的百分比

    `avg_photo_embedding_attr`: [string] 每个类的 avg_photo_embedding 的 common attr

    `avg_watch_time_attr`: [string] 每个类的 watch_time 的 common attr

    `cluster_ids_attr`: [string] 存储当前用户当前的cluster id list的common attr

    `input_cluster_id_attr`: [string] 被mask之后的cluster id，item attr

    `input_watch_time_attr`: [string] 被mask之后的观看时长 item attr

    `input_photo_embedding_attr`: [string] 输入photo embedding 的 item attr

    `label_weight_attr`: [string] loss的weight

    `label_watch_time_attr`: [string] watch time label d item attr

    `mask_cluster_id_attr`: [string] mask的cluster id，item attr

    调用示例
    ------
    ``` python
    .next_interest_predict_samples_generate_retriever(
      sampling_num=10,
      mask_rate=0.1,
      avg_photo_embedding_attr="avg_mmu_embedding",
      avg_watch_time_attr="avg_watch_time",
      cluster_ids_attr="cluster_ids_attr",
      input_cluster_id_attr="input_cluster_id",
      input_photo_embedding_attr="input_photo_embedding",
      input_watch_time_attr="input_watch_time",
      label_weight_attr="label_weight",
      label_watch_time_attr="label_watch_time",
      mask_cluster_id_attr="mask_cluster_id_list"
      )
    ```
    """
    self._add_processor(NextInterestSampleRetriever(kwargs))
    return self

  def dump_mio_tensor(self, **kwargs):
    """
    MioDumpTensorAttrEnricher
    ------
    将 mio TensorOutput 转换成行文本，以便脚本进行文本处理

    参数配置
    ------
    `tensors`: [list] 需要转换的 tensor attr 列表，是一个 dict list，每个 dict 包含如下配置:
              "tensor_attr" : xxx, 需要被转换的 tensor common attr,
              "dump_attr" : yyy, tensor 转成字符串后保存的 common attr

    调用示例
    ------
    ``` python
    .dump_mio_tensor(
      "tensors" = [
        dict(
          tensor_attr = "one_photo_input",
          dump_attr = one_photo_input_str"
        ),
        dict(
          tensor_attr = "one_photo_input2",
          dump_attr = one_photo_input2_str"
        ),
      ]
    )
    ```
    """
    self._add_processor(MioDumpTensorAttrEnricher(kwargs))
    return self

  def hot_fix_user_info(self, **kwargs):
    """
    MioUserInfoHotFixEnricher
    ------
    通过 hot-fix 快速解决 JointRecoLog 在离线不一致的问题

    参数配置
    ------
    `user_info_attr`: [string] 需要修复的 UserInfo

    调用示例
    ------
    ``` python
    .hot_fix_user_info(user_info_attr="user_info")
    ```
    """
    self._add_processor(MioUserInfoHotFixEnricher(kwargs))
    return self

  def mio_predict_opt(self, **kwargs):
    """
    MioPredictItemAttrOptEnricher
    ------
    通过 Mio 模型进行预估（只支持通过 tensorflow 进行预估）

    参数配置
    ------
    `inputs`: [dict] 模型输入配置，包括 `attr_name` [string]，`tensor_name` [string], `common` [bool] (optional 默认为 False), `dim` [int]。将根据 `common` 从 common attr 或 item attr 的 `attr_name` 取出参数，构造成宽度为 `dim` 的矩阵放到计算图的 `tensor_name` 中执行模型。

    `outputs`: [dict] 模型输出配置，包括 `attr_name` [string]，`tensor_name` [string]，将计算图中的 `tensor_name` 放到 item attr 的 `attr_name` 中。

    `key`: [string] 模型唯一标识符，如果有多个模型需要指定不同的 key。

    `graph`: [string] 计算图 uri，支持 base64:// 和 file:// 两种 scheme，不指定 scheme 视为路径。

    `queue_prefix`: [string] 接收模型的 BTQueue topic 前缀。

    `receive_dnn_model_as_macro_block`: [bool] 按 MacroBlock 从 BTQueue 接收模型数据，缺省为 false，注意当 gflag 为 true 时，这个选项将失效，永远为 true。

    `flatten_outputs`: [bool] 是否将长度为 1 的 float list attr 输出转换为 float attr (optional，默认为 False)

    `output_common_attr` : [bool] 默认false，是否将预估结果写入 context 的 CommonAttr ，例如将 user embedding predict结果写入context

    `rowmajor`: [bool] BTQueue 传输过来的参数是否是 Row-Major 的，默认为 False，即 Column-Major。（当前只有 simple-mio 支持 Column-Major 的参数）

    调用示例
    ------
    ``` python
    import mio_tensorflow.patch as mio_tensorflow_patch
    mio_tensorflow_patch()

    import base64
    import tensorflow as tf

    ...

    .mio_predict_opt(
        graph=base64.b64encode(tf.get_default_graph().as_graph_def().SerializeToString()),
        queue_prefix='nearby_photo_v6_test',
        output_common_attr = False,
        inputs=[{
          "attr_name": "user_input",
          "tensor_name": "user_input",
          "common": True,
          "dim": 64,
        }, {
          "attr_name": "photo_input",
          "tensor_name": "photo_input",
          "dim": 128,
        }],
        outputs=[{
          "attr_name": "ctr",
          "tensor_name": "mul:0",
        }, {
          "attr_name": "ltr",
          "tensor_name": "mul_1:0",
        }],
    ```
    """
    self._add_processor(MioPredictItemAttrOptEnricher(kwargs))
    return self

  def fetch_mio_embedding_opt(self, **kwargs):
    """
    MioEmbeddingAttrOptEnricher
    ------
    通过 gRPC 访问 Mio 的 Parameter Server 拿到 sign 对应的 embedding 参数。
    和 fetch_mio_embedding 功能相同，区别是 fetch_mio_embedding_opt 会在内部线程池完成所有操作以达到充分并行节省耗时的目的。
    为了尽可能的优化性能，一些参数和功能在 fetch_mio_embedding_opt 中无法使用。详情见参数配置。


    参数配置
    ------
    `kess_service`: [string] kess 服务名

    `kess_cluster`: [string] kess 集群名，默认 PRODUCTION

    `thread_num`: [int] gRPC 线程数，默认 1

    `shards`: [int] shard 数量

    `slots_inputs`: [list] 从哪些 item attr 读取 slots 信息，当且仅当 slot_as_attr_name 为 False 生效。

    `parameters_inputs`: [list] 从哪些 item attr 读取 parameters 信息，当且仅当 slot_as_attr_name 为 False 生效。

    `common_slots_inputs`: [list] 从哪些 common attr 读取 slots 信息，当且仅当 slot_as_attr_name 为 False 生效。

    `common_parameters_inputs`: [list] 从哪些 common attr 读取 parameters 信息，当且仅当 slot_as_attr_name 为 False 生效。

    `timeout_ms`: [int] gRPC 超时，单位毫秒，默认 10

    `slots_config`: [list] embedding 获取的结果存储配置，格式同之前 mio predict server 的 embedding_slots 或 embedding.slots_config

    `model_version_attr`: [string] 从哪个 common attr 中获取模型版本号，默认值是空字符串。模型的版本号应是一个 int64 的值，默认为 0

    `protocol`: [int] 访问 embedding 服务所使用的协议，默认为 0，协议的支持情况见 协议支持 部分。

    `save_result_as_tensor_output`: [bool] 把这个 req 中所有 sample 的 embedding 存到 PtrCommonAttr 中 batching 起来, 方便预估使用

    `direct_write`: [bool] protocol == 0 且 save_result_as_tensor_output == true 时, 打开该参数能够提升写 context 的吞吐

    `reco_embedding_feature`: [string] protocol == 4 时，用于指定查询的 embedding 在 colossus 中的列名，需要指定特征的 name_space 和 table_name ，“name_space.table_name.feature_name”

    `slot_names`: [list] 支持给每个 attr 设置显示的名字，需要包含 slot 和 name 两个字段，仅用于 Perfutil 上报。

    `slot_as_attr_name`: [bool] 暂不支持，只能为 False。

    `slot_as_attr_name_prefix`: [string] 暂不支持，只能为 False。

    `emb_dtype`: [int] 存储的embeding数据类型，0: int16, 1:float, other:undefined

    `use_new_fill_impl`: [bool] 是否使用新的 tensor 填充实现（作用与过去的一样，只是兼容更多的数据格式）

    `emb_dtype`: [int] 存储的embeding数据类型，0: int16, 1:float, other:undefined

    `save_final_status_to_attr`: [string] 当本字段不为空时，会将拉取 embedding 最终状态保存在以 `save_final_status_to_attr` 作为名称的 int common attr 中；当拉取 embedding 的全部请求都成功时，最终状态为 0；当拉取 embedding 的全部请求部分成功时，最终状态为 1; 当拉取 embedding 的全部请求都失败时，最终状态为 2

    协议支持
    ------

    当前共实现了2种协议（0和5），对应的服务器端实现情况分别为：

    | protocol \ server                  | kuiba_shm_predict_server | shm_dnn_predict_server | bt_embedding_server | kvs_embedding_server | colossus_server |
    |:-----------------------------------|:-------------------------|:-----------------------|:--------------------|:---------------------|:----------------|
    | 0 (GetKuibaEmbedding)              | YES                      | NO                     | YES                 | NO                   | NO              |

    注意，以下为必须配置：
    direct_write=True,
    save_result_as_tensor_output=True

    调用示例
    ------
    ``` python
    import yaml
    .fetch_mio_embedding_opt(
        kess_service="grpc_kuibaShmEmbeddingServerNearbyLiveFastNerual",
        shards=1,
        slots_inputs=["slots"],
        parameters_inputs=["parameters"],
        common_slots_inputs=["common_slots"],
        common_parameters_inputs=["common_parameters"],
        slot_names=[
          dict(slot=128, name="author_id"),
          dict(slot=26, name="photo_id"),
        ],
        slots_config=yaml.load(open("dnn_model.yaml"))["embedding"]["slots_config"])
    ```
    """
    protocol=kwargs.get("protocol", False)
    assert (protocol==0 or protocol==5), 'only support protocol=0 or protocol=5'
    assert kwargs.get("direct_write", False), 'only support direct_write=True'
    assert kwargs.get("save_result_as_tensor_output", False), 'only support save_result_as_tensor_output=True'
    assert kwargs.get("slot_as_attr_name", False) == False, 'only support slot_as_attr_name=False'
    self._add_processor(MioEmbeddingAttrOptEnricher(kwargs))
    return self

  def fetch_local_mio_embedding_opt(self, **kwargs):
    """
    MioLocalEmbeddingAttrOptEnricher
    ------
    查询本地 EmbeddingTable 拿到 sign 对应的 embedding 参数 , 除了查询 embeding ，其他逻辑继承 MioEmbeddingAttrEnricher

    参数配置
    ------
    基本与 MioEmbeddingAttrOptEnricher 相同, 不同点:
      1. 不需要配置 embeding server 的相关配置: kess name / shard / timeout
      2. protocol 必须为 5

    调用示例
    ------
    ``` python
    import yaml
    .fetch_local_mio_embedding_opt(
        slots_inputs=["slots"],
        parameters_inputs=["parameters"],
        common_slots_inputs=["common_slots"],
        common_parameters_inputs=["common_parameters"],
        slot_names=[
          dict(slot=128, name="author_id"),
          dict(slot=26, name="photo_id"),
        ],
        slots_config=yaml.load(open("dnn_model.yaml"))["embedding"]["slots_config"])
    ```
    """
    self._add_processor(MioLocalEmbeddingAttrOptEnricher(kwargs))
    return self

  def generate_item_sign_enrich(self, **kwargs):
    """
    GenerateItemSignEnricher
    ------
    替换lua脚本,构造以pid为list的signs,为后续请求下游embedding server构造输入

    参数配置
    ------
    `output_slot_attrs`: [list] output_slot_attrs

    `output_slots`: [list] output_slots

    `output_sign_attr`: [string] output_sign_attr

    `return attrs`: [list] remote predict server 返回的 attr。

    ------
    ``` python
    import yaml
    .generate_item_sign_enrich(
      output_slot_attrs = ["photo_query_slots", "item_mmu_slots"],
      output_slots = [1002, 1007],
      output_sign_attr = "itop_signs")
    ```
    """
    self._add_processor(GenerateItemSignEnricher(kwargs))
    return self

  def extract_long_term_sign_feature(self, **kwargs):
    """
    MioLongTermSignFeatureEnricher
    ------
    根据 colossus response 提取其中的 long term item list，并对 long term item 抽取 sign

    参数
    ------
    `long_term_items_attr`: [string] 所有 item 匹配到的 long term item 去重后的列表, 写入 common attr

    `output_sign_attr`: [string] sign 输出 attr name @${long_term_items_attr}

    `output_slot_attr`: [string] slot 输出 attr name @${long_term_items_attr}

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    示例
    ------
    ``` python
    .extract_long_term_sign_feature(colossus_resp_attr='colossus_resp',
                      long_term_items_attr='long_term_items',
                      output_sign_attr='sign',
                      output_slot_attr='slot',
                      )
    ```
    """
    self._add_processor(MioLongTermSignFeatureEnricher(kwargs))
    return self

  def mio_tensor_op(self, **kwargs):
    """
    MioTensorOpEnricher
    ------
    将 common attr 中的 TensorOutput 序列化写入 common attr, 或者将 common attr 中的 string 反序列化为 TensorOutput 写入 common attr

    参数配置
    ------
    `op_config`: list[dict] mio tesnor op config, dict 元素如下:
                 `mode` : [int] 0, tensor -> string ; 1, string -> tensor
                 `input_attr`: [string] 输入的 attr 名称，意义随着 mode 不同而不同
                 `output_attr`: [string] 输出的 attr 名称，意义随着 mode 不同而不同

    调用示例
    ------
    ``` python
    .mio_tensor_op(
      op_config = [dict(
        mode=1,
        input_attr="tensor_data",
        output_attr="tensor_obj",
        ),
        dict(
          mode=0,
          input_attr="tensor_obj2",
          output_attr="tensor_data2",
        ),
      ],
      )
    ```
    """
    self._add_processor(MioTensorOpEnricher(kwargs))
    return self

  def mio_ksib_live_deep_hash_encode(self, **kwargs):
    '''
    MioKsibLiveDeepHashEncodeEnricher
    ------
    将 List 类型的 common attr 中各元素（k个元素）与 item attr 组合后，生成 embedding_size 个 hash code,
    并归一划到 （0，1）区间，最后 k 个 embedding 进行 sum pooling。

    参数配置
    ------
    `input_common_list_attr_name`: list[int] 通常为 play list 之类特征

    `input_item_attr_name`: int 通常为 item_id/aid/live_id/pid 等特征

    `output_item_attr_name`: list[double] 输出到指定 item attr

    `embedding_size`: int 默认值 512

    `prime_number`: long 哈希时使用的质数，默认值 1000000000000037

    调用示例
    ------
    ``` python
    .mio_ksib_live_deep_hash_encode(
      input_common_list_attr_name='play_list',
      input_item_attr_name='live_id',
      output_item_attr_name='deephash',
      embedding_size=128,
      prime_number=1000000007
    )
    ```
    '''
    self._add_processor(MioKsibLiveDeepHashEncodeEnricher(kwargs))
    return self

  def decompress_embedding_from_string(self, **kwargs):
    '''
    DecompressEmbeddingFromString
    -----
    将存在 string attr 中压缩后的 embedding 解压成 common leaf context 内置的 double list 格式

    参数配置
    -----
    `decompress_configs`: list[dict] 配置列表, 每一个配置是一个dict， 包含如下配置:
      `input_attr`: string 输入 attr name
      `output_attr`: string 输出 attr name
      `is_common`: bool 是否是 common attr
      `expected_dim`: embedding 解压后的 dim size, 可以为空
      `compress_type`: string, one of ["fp32", "fp16", "mio_int16", "scale_int8", "scale_int16"], 默认为 mio_int16

    调用示例
    -----
    ```python
    .decompress_embedding_from_string(
      decompress_configs=[
        dict(
          input_attr="user_emb_str",
          output_attr="user_emb",
          is_common=True,
          compress_type="mio_int16",
        ),
        dict(
          input_attr="photo_emb_str",
          output_attr="photo_emb",
          is_common=False,
          compress_type="mio_int16",
        )
      ]
    )
    ```
    '''
    self._add_processor(DecompressEmbeddingFromStringEnricher(kwargs))
    return self

  def get_common_attr_from_redis_by_lrange(self, **kwargs):
    '''
    CommonAttrFromRedisByLrangeEnricher
    -----
    从redis中读取list型数据, 返回结果为string list格式

    参数配置
    -----
    `cluster_name`: [string] 输入redis集群名
    `redis_key`: [string] 动态参数, 输入redis key
    `output_common_attr`: [string] 输出结果attr name

    调用示例
    -----
    ```python
    .get_common_attr_from_redis_by_lrange(
      cluster_name="redisxxxx",
      redis_key="redis_key_xx",
      output_common_attr="redis_value_xxx"
    )
    ```
    '''
    self._add_processor(CommonAttrFromRedisByLrangeEnricher(kwargs))
    return self

  def mio_unique(self, **kwargs):
    '''
    MioUniqueAttrEnricher
    -----
    从 common attr 和 item attr 读入单值或 list, 输出去重之后的 list, 也支持输出
    原始输入对应的去重 list 的 index. 当前仅支持 int 类型.

    参数配置
    -----
    `output_attr`: [string] 输出去重的 common attr.
    `input_common_attrs`: [list] 输入的 common attr 列表.
    `output_common_index_attrs`: [list] 输出 common 对应 index 的 attr 列表, 允许不配置.
    `input_item_attrs`: [list] 输入的 item attr 列表.
    `output_item_index_attrs`: [list] 输出 item 对应 index 的 attr 列表, 允许不配置.

    调用示例
    -----
    ```python
    .mio_unique(
      output_attr="unique_int",
      input_common_attrs=["int_list1", "int_value1", "int_list2"],
      output_common_index_attrs=["int_list1_index", "int_value1_index", "int_list2_index"]
      input_item_attrs=["int_item_list1", "int_item_value1", "int_item_list2"],
      output_item_index_attrs=["int_item_list1_index", "int_item_value1_index", "int_item_list2_index"]
    )
    ```
    '''
    self._add_processor(MioUniqueAttrEnricher(kwargs))
    return self

  def filter_slot_sign_attr_by_slotid_list(self, **kwargs):
    """
    FilterSlotSignAttrBySlotIdListEnricher
    ------
    使用 slotid_reserved_list 过滤 common & item 的 slots、对应下标位置的 signs, 生成以 generated_attr_name_suffix 作为后缀的 slot/sign attr

    参数配置
    ------
    `common_slots_attr`: [string] User 侧 slots 输出时给定的 common attr 名
    `common_signs_attr`: [string] User 侧 parameters(signs) 输出时给定的 common attr 名
    `item_slots_attr`: [string] Item 侧 slots 输出时给定的 item attr 名
    `item_signs_attr`: [string] Item 侧 parameters(signs) 输出时给定的 item attr 名
    `export_attr_name_suffix`: [string] export attr 的后缀（在以上四个 attr name 派生新的 attr name）
    `slotid_reserved_list`: [int list] 需要保留的 slotid 列表 

    调用示例
    ------
    ```python
    .filter_slot_sign_attr_by_slotid_list(
      common_slots_attr='common_slots',
      common_signs_attr='common_signs',
      item_slots_attr='item_slots',
      item_signs_attr='item_signs',
      export_attr_name_suffix='_exported',
      slotid_reserved_list=[1,2,3]
    )
    ```
    """
    assert kwargs.get("export_attr_name_suffix", "") != "", 'export_attr_name_suffix 应配置非空字符串'
    slotid_reserved_list = kwargs.get("slotid_reserved_list", [])
    assert len(slotid_reserved_list) > 0 and all(isinstance(i, int) for i in slotid_reserved_list), \
      f"slotid_reserved_list 应配置非空 int-list: {slotid_reserved_list}"
    self._add_processor(FilterSlotSignAttrBySlotIdListEnricher(kwargs))
    return self

  def feature_slot_copy(self, **kwargs):
    """
    MioFeatureSlotCopyEnricher
    ------
    基于 slot_copy 配置, 拷贝 common & item 的 slots、对应下标位置的 signs, 生成以 output_attr_suffix 作为后缀的 slot/sign attr

    参数配置
    ------
    `slot_copy`: [dict] 特征拷贝信息, 格式为 '111': '222,333,444', 均以字符串方式设置

    `slot_as_attr_name`: [boolean] 是否 slot 的 signs 信息以 attr 方式存储，默认false
    - 当`slot_as_attr_name` = true 时，以下配置生效:
      * `src_slot_name_prefix`: [string] 原始 attr prefix 字符串,
      * `dst_slot_name_prefix`: [string] 目标 attr prefix 字符串, 要求和 `src_slot_name_prefix` 不相同
    - 当`slot_as_attr_name` = false 时，以下配置生效:
      * `common_slot_attrs`: [list(string)] User 侧 slots 输出时给定的 common attr 列表
      * `common_sign_attrs`: [list(string)] User 侧 parameters(signs) 输出时给定的 common attr 列表，需要和 slot 一一对应
      * `item_slot_attrs`: [list(string)] Item 侧 slots 输出时给定的 item attr 名
      * `item_sign_attrs`: [list(string)] Item 侧 parameters(signs) 输出时给定的 item attr 名
      * `output_attr_suffix`: [string] export attr 的后缀（在以上 attr name 派生新的 attr name, 不允许为空, 避免覆盖原始数据）
      * `reserve_valid_slots`: [boolean] 是否直接保存无冲突的 slotId 的特征, 不冲突定义为 slotId 不在`slot_copy.values`中, 默认false。
      * `reserve_input_slots`: [list(int)] 需要保留的 slotid 列表, 可以和`reserve_valid_signs`同时设置, 取并集, 不保证 slotId 不冲突。

    调用示例
    ------
    ```python
    .feature_slot_copy(
      common_slot_attrs=['common_slots'],
      common_sign_attrs=['common_signs'],
      item_slot_attrs=['item_slots'],
      item_sign_attrs=['item_signs'],
      output_attr_suffix='_copy',
      slot_copy={"111": "222,333,444", "112":"223,334,445"},
      reserve_valid_signs=true,
      reserve_input_slots=[1,2,3]
    )
    ```
    """
    as_attr = kwargs.get('slot_as_attr_name', False)
    src_prefix = kwargs.get('src_slot_name_prefix', '')
    dst_prefix = kwargs.get('dst_slot_name_prefix', '')
    if as_attr:
      assert src_prefix != dst_prefix, f'src_slot_name_prefix 和 dst_slot_name_prefix 不能相同, {src_slot_name_prefix} == {dst_slot_name_prefix}'
    else:
      common_slots_len = len(kwargs.get('common_slot_attrs', []))
      common_signs_len = len(kwargs.get('common_sign_attrs', []))
      item_slots_len = len(kwargs.get('item_slot_attrs', []))
      item_signs_len = len(kwargs.get('item_sign_attrs', []))
      assert common_slots_len == common_signs_len and item_slots_len == item_signs_len, 'slot / sign attr 需要一一对应, 长度不对齐'
      assert common_slots_len > 0 or item_slots_len > 0, 'common / item attr 不能全部为空'

      assert kwargs.get("output_attr_suffix", "") != "", 'output_attr_suffix 应配置非空字符串'
      slotid_reserved_list = kwargs.get("reserve_input_slots", [])
      assert len(slotid_reserved_list) == 0 or all(isinstance(i, int) for i in slotid_reserved_list), \
        f"reserve_input_slots 应配置为 int-list: {slotid_reserved_list}"

    slot_copy = kwargs.get('slot_copy', {})
    assert len(slot_copy) > 0, 'slot_copy没有设置, 请设置copy关系'

    self._add_processor(MioFeatureSlotCopyEnricher(kwargs))
    return self

  def feature_slot_share(self, **kwargs):
    """
    MioFeatureSlotShareEnricher
    ------
    基于 slot_share 配置, 改写 common & item 对应 slot 下的 sign 的高位, 在原有 attr 上做 in-place 更改

    参数配置
    ------
    `slot_share`: [dict] 特征共享信息, 格式为 '111': '222', 均以字符串方式设置, key 和 value 都是单一 slotId

    `slot_as_attr_name`: [boolean] 是否 slot 的 signs 信息以 attr 方式存储, 默认false
    - 当`slot_as_attr_name` = true 时，以下配置生效:
      * `slot_name_prefix`: [string] 原始 attr prefix 字符串

    - 当`slot_as_attr_name` = false 时，以下配置生效:
      * `common_slot_attrs`: [list(string)] User 侧 slots 输出时给定的 common attr 列表
      * `common_sign_attrs`: [list(string)] User 侧 parameters(signs) 输出时给定的 common attr 列表，需要和 slot 一一对应
      * `item_slot_attrs`: [list(string)] Item 侧 slots 输出时给定的 item attr 名
      * `item_sign_attrs`: [list(string)] Item 侧 parameters(signs) 输出时给定的 item attr 名

    调用示例
    ------
    ```python
    .feature_slot_share(
      common_slot_attrs=['common_slots'],
      common_sign_attrs=['common_signs'],
      item_slot_attrs=['item_slots'],
      item_sign_attrs=['item_signs'],
      slot_share={"111": "222", "112":"223"},
    )
    ```
    """
    as_attr = kwargs.get('slot_as_attr_name', False)
    if not as_attr:
      common_slots_len = len(kwargs.get('common_slot_attrs', []))
      common_signs_len = len(kwargs.get('common_sign_attrs', []))
      item_slots_len = len(kwargs.get('item_slot_attrs', []))
      item_signs_len = len(kwargs.get('item_sign_attrs', []))
      assert common_slots_len == common_signs_len and item_slots_len == item_signs_len, 'slot / sign attr 需要一一对应, 长度不对齐'
      assert common_slots_len > 0 or item_slots_len > 0, 'common / item attr 不能全部为空'

    slot_share = kwargs.get('slot_share', {})
    assert len(slot_share) > 0, 'slot_share 没有设置, 请设置特征共享关系'

    self._add_processor(MioFeatureSlotShareEnricher(kwargs))
    return self

