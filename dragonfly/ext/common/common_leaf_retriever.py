#!/usr/bin/env python3
# coding=utf-8
"""
filename: common_leaf_processor.py
description: common_leaf dynamic_json_config DSL intelligent builder, retriever module
author: <EMAIL>
date: 2020-01-09 10:45:00
"""
import os
from ...common_leaf_util import strict_types, check_arg, extract_common_attrs, \
  check_common_query_syntax, extract_attr_names
from ...common_leaf_processor import LeafR<PERSON>riever, try_add_table_name

class CommonRecoFakeRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fake_retrieve"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("reason", 0) >= 0, "reason 需为非负整数")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("num")))
    return attrs

class CommonRecoDelegateRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "delegate_retrieve"

  @strict_types
  def is_async(self) -> bool:
    return True


  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service","")))
    attrs.update(self.extract_dynamic_params(self._config.get("kess_group")))
    attrs.update(self.extract_dynamic_params(self._config.get("request_type", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("request_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("partition_service_kconf","")))
    attrs.add(self._config.get("sample_list_common_attr_key"))
    attrs.update(extract_attr_names(self._config.get("send_common_attrs", []), "name"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(extract_attr_names(self._config.get("recv_item_attrs", []), "as"))
    attrs.update(extract_attr_names(self._config.get("append_item_attrs", []), "as"))
    for config in self._config.get("recv_table_columns", []):
      table_name = config.get("table_name")
      new_table_name = config.get("as", table_name)
      attrs.update(try_add_table_name(new_table_name, extract_attr_names(config.get("columns", []), "as")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return extract_attr_names(self._config.get("recv_common_attrs", []), "as")

  @property
  @strict_types
  def output_item_tables(self) -> set:
    attrs = super().output_item_tables
    for config in self._config.get("recv_table_columns", []):
      attrs.add(config.get("as", config.get("table_name")))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    recv_attrs = set()
    append_attrs = set()
    check_arg("kess_service" in self._config or "partition_service_kconf" in self._config,"需要有 partition_service_kconf 或者 kess_service")
    recv_attrs.update(extract_attr_names(self._config.get("recv_item_attrs", []), "as"))
    append_attrs.update(extract_attr_names(self._config.get("append_item_attrs", []), "as"))
    check_arg(all(attr not in append_attrs for attr in recv_attrs), "recv_item_attrs 和 append_item_attrs 的属性名称不能重复")
    if "recv_item_attrs" in self._config or "append_item_attrs" in self._config:
      check_arg("recv_table_columns" not in self._config, "recv_table_columns 不能和 recv_item_attrs 或 append_item_attrs 同时存在")

  @property
  @strict_types
  def config_hash_fields(self) -> list:
    return ["kess_service"]


class CommonRecoPipelineRetriever(LeafRetriever):
  @strict_types
  def __init__(self, config: dict):
    check_arg("sub_flow" in config, "缺少 sub_flow 配置")
    self.__sub_flow = config.pop("sub_flow")
    from ...common_leaf_dsl import LeafFlow
    check_arg(isinstance(self.__sub_flow, LeafFlow), f"sub_flow 类型必须为 LeafFlow。但是取到的类型为: {type(self.__sub_flow)} Tips: 请检查下有没有继承 LeafFLow、链式调用的函数有没有返回 self")
    super().__init__(config)
    self._auto_detect_pass_common_attrs = "pass_common_attrs" not in self._config
    # NOTE: 仅在开启了 pass_all_items 的情况下自动推导 pass_item_attrs
    self._auto_detect_pass_item_attrs = "pass_item_attrs" not in self._config if self._config.get("pass_all_items", False) else False
    self._auto_detect_merge_common_attrs = "merge_common_attrs" not in self._config
    self._auto_detect_merge_item_attrs = "merge_item_attrs" not in self._config
    self._has_context_name = False
    if config.get("common_reco_context_attr_name", "").strip() != "":
      check_arg(os.environ.get("ENABLE_SUB_FLOW_SHARED_CONTEXT", "false") == "true", "使用 共享context 需要开启 ENABLE_SUB_FLOW_SHARED_CONTEXT")
      self._has_context_name = True

  @strict_types
  def _check_config(self) -> None:
    if self._config.get("deep_copy", True):
      check_arg("deep_copy_attrs" not in self._config, "deep_copy 为 true 时，不需要配置 deep_copy_attrs")
    else:
      check_arg("shadow_copy_attrs" not in self._config, "deep_copy 为 false 时，不需要配置 shadow_copy_attrs")

  @strict_types
  def get_type_alias(self) -> str:
    return "retrieve_by_sub_flow"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def depend_on_items(self) -> bool:
    return self._config.get("pass_all_items", False)

  @strict_types
  def get_sub_flow(self):
    return self.__sub_flow

  @strict_types
  def pass_common_attrs_in_request(self) -> bool:
    return self._config.get("pass_common_attrs_in_request", True)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("pass_common_attrs", []))
    attrs.update(self.extract_dynamic_params(self._config.get("retrieve_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("deduplicate_results")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.add(self._config.get("pass_common_attrs_in_list"))
    attrs.update(self.extract_dynamic_params(self._config.get("task_queue_id")))
    if self._has_context_name:
      attrs.add(self._config.get("common_reco_context_attr_name", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("pass_all_items", False):
      attrs.update(self._config.get("pass_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("merge_common_attrs", []), "as")
    if self._has_context_name:
      attrs.add(self._config.get("common_reco_context_attr_name", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return extract_attr_names(self._config.get("merge_item_attrs", []), "as")

  @strict_types
  def __hash__(self):
    return hash(self.config_hash)

class CommonRecoCommonAttrRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_common_attr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = { v["name"] for v in self._config["attrs"] }
    if "exclude_items_in_attr" in self._config:
      attrs.add(self._config["exclude_items_in_attr"])
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("attrs"), list), "attrs 需为 list 类型")
    check_arg(all(isinstance(v, dict) for v in self._config["attrs"]), "attrs 中的每项需为 dict 类型")
    check_arg(all("name" in v for v in self._config["attrs"]), "attrs 中的每项需包含 name 字段")
    check_arg(all(isinstance(v["name"], str) and v["name"] for v in self._config["attrs"]), "name 字段需为非空字符串")
    check_arg(all("reason" in v for v in self._config["attrs"]), "attrs 中的每项需包含 reason 字段")
    check_arg(all(isinstance(v["reason"], int) and v["reason"] > 0 for v in self._config["attrs"]), "reason 字段需为 > 0 的整数")

class CommonRecoCommonQueryRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_local_index"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("browsed_item_count")))
    attrs.update(self.extract_dynamic_params(self._config.get("common_query")))
    attrs.update(self.extract_dynamic_params(self._config.get("default_total_request_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("max_query_time_ms")))
    for cfg in self._config["querys"]:
      # NOTE(fangjianbing): query 中可能存在多个 common attr 动态参数，需要用 extract_common_attrs 抽取
      attrs.update(extract_common_attrs(cfg["query"]))
      for key in ["search_num", "random_search", "expire_second", "max_attr_num", "prefix"]:
        if key in cfg:
          attrs.update(self.extract_dynamic_params(cfg[key]))
      if "lat" in cfg:
        attrs.add(cfg["lat"])
      if "lon" in cfg:
        attrs.add(cfg["lon"])
      if "unit_size" in cfg and isinstance(cfg["unit_size"], str):
        attrs.add(cfg["unit_size"])
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("common_query", ""), str), "common_query 需为 string 类型")
    check_arg(isinstance(self._config.get("consider_browse_set", False), bool), "consider_browse_set 需为 bool 类型")
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("default_search_num", 0), int), "default_search_num 必须为整数且不支持动态参数，如需使用动态参数请改用 search_num 配置")
    check_arg(self._config.get("default_random_search", 1) in { 0, 1 }, "default_random_search 需为 0 或 1")
    check_arg(isinstance(self._config.get("querys"), list), "querys 需为 list 类型")
    check_arg(all(isinstance(v, dict) for v in self._config["querys"]), "querys 里的每项需为 dict 类型")
    check_arg( \
      all( \
        (isinstance(v.get("query"), str) and v.get("query")) \
        or ( \
          isinstance(v.get("lat"), str) and v.get("lat") and isinstance(v.get("lon"), str) and v.get("lon") and \
          ((isinstance(v.get("unit_size"), str) and v.get("unit_size")) or \
            isinstance(v.get("unit_size"), int) or \
            isinstance(v.get("unit_size"), float) or \
            (isinstance(v.get("unit_size"), list) and \
              all(isinstance(u, float) or isinstance(u, int) for u in v["unit_size"]) \
            ))) for v in self._config["querys"]), "querys 里每项的 query 需为非空字符串，或者指定 lat lon 以及 unit_size")
    # check_arg(all(v.get("search_num", self._config.get("default_search_num", 0)) > 0 for v in self._config["querys"]), "未指定 search_num")
    # check_arg(all(v.get("random_search", 0) in { 0, 1 } for v in self._config["querys"]), "querys 里每项的 random_search 需为 0 或 1")
    for query in self._config.get("querys"):
      if query.get("lat") and query.get("lon") and query.get("unit_size"):
        if query.get("query"):
          raise ArgumentError(f"❌ [query 格式错误：lat/lon/unit_size和query不能同时使用！]")
      else:
        query = query.get("query")
        check_common_query_syntax(query)

class CommonRecoCommonQueryRemoteRetriever(CommonRecoCommonQueryRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_remote_index"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg("client_kconf" not in self._config, "默认模式不可以包含 client_kconf")
    check_arg(self._config.get("timeout_ms"), "timeout_ms 必须配置")
    super()._check_config()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("browsed_item_count")))
    attrs.update(self.extract_dynamic_params(self._config.get("common_query")))
    attrs.add(self._config.get("exclude_items_in_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["save_score_to_attr", "save_query_index_to_attr"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs

class CommonRecoRemoteColossusdbIndexRetriever(CommonRecoCommonQueryRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_remote_colossusdb_index"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    check_arg("{{" not in self._config.get("common_query", ""), "common_query 中不能包含 {{}} 格式动态参数")
    check_arg(isinstance(self._config.get("client_kconf"), str) and self._config["client_kconf"], "client_kconf 需为非空字符串")
    super()._check_config()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("browsed_item_count")))
    attrs.add(self._config.get("exclude_items_in_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["save_score_to_attr", "save_query_index_to_attr"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs


class CommonRecoEmbeddingRetriever(LeafRetriever):
  @strict_types
  def __init__(self, config: dict):
    if "fetch_user_embedding" in config and \
        config["fetch_user_embedding"].get("include_sample_list_user_info", False):
      config["fetch_user_embedding"]["use_sample_list_attr_flag"] = True
      config["fetch_user_embedding"]["sample_list_common_attr_key"] = self._SAMPLE_LIST_COMMON_ATTR_KEY
      if "use_sample_list_attr_flatten" not in config["fetch_user_embedding"]:
        config["fetch_user_embedding"]["use_sample_list_attr_flatten"] = True
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_ann_embedding"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def depend_on_sample_list_user_info(self) -> bool:
    user_embedding_config = self._config.get("fetch_user_embedding", {})
    return user_embedding_config.get("use_sample_list_attr_flag", True) and bool(user_embedding_config.get("sample_list_common_attr_key"))

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("browsed_item_count")))
    attrs.update(self._config.get("items_from_attr", []))
    attrs.update(self._config.get("embeddings_from_attr", []))
    for key in ["kess_service", "shard_num", "space", "timeout_ms", 
                "src_bucket", "dest_bucket", "dest_bucket_item_type", "src_data_type"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key, ""), check_format=False))
    for key in ["top_k", "total_limit"]:
      attrs.update(self.extract_dynamic_params(self._config["bound_type"].get(key, ""), check_format=False))
    attrs.update(self.extract_dynamic_params(self._config["algo_type"].get("annoy", {}).get("search_k", ""), check_format=False))
    attrs.update(self.extract_dynamic_params(self._config["algo_type"].get("hnsw", {}).get("ef", ""), check_format=False))
    embedding_config = self._config.get("fetch_user_embedding", {})
    attrs.update(embedding_config.get("attr_name_transform_map", {}).keys())
    if embedding_config.get("sample_list_common_attr_key", ""):
      attrs.add(embedding_config.get("sample_list_common_attr_key", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["save_source_item_to_attr", "save_distance_to_attr", "save_seq_num_to_attr", "save_embs_to_attr"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), (int, str)), "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("bound_type"), dict), "bound_type 需为 dict 类型")
    check_arg(len(self._config["bound_type"]) == 1, "bound_type 只能包含一项配置")
    check_arg({ "top_k", "total_limit" } & self._config["bound_type"].keys(), "bound_type 只能包含 top_k 或 total_limit")
    check_arg(isinstance(self._config.get("algo_type"), dict), "algo_type 需为 dict 类型")
    check_arg(len(self._config["algo_type"]) == 1, "algo_type 只能包含一项配置")
    check_arg({ "annoy", "hnsw", "faiss", "scann" } & self._config["algo_type"].keys(), "algo_type 只能包含 annoy, hnsw, faiss 或 scann")
    check_arg(isinstance(self._config.get("src_bucket"), str) and self._config["src_bucket"], "src_bucket 需为非空字符串")
    check_arg(isinstance(self._config.get("dest_bucket"), str) and self._config["dest_bucket"], "dest_bucket 需为非空字符串")
    if "fetch_user_embedding" in self._config:
      check_arg(isinstance(self._config["fetch_user_embedding"], dict), "fetch_user_embedding 需为 dict 类型")
      check_arg(isinstance(self._config["fetch_user_embedding"].get("kess_service"), str) and self._config["kess_service"], "fetch_user_embedding 中的 kess_service 需为非空字符串")
      check_arg(isinstance(self._config["fetch_user_embedding"].get("timeout_ms"), int) and self._config["timeout_ms"] > 0, "fetch_user_embedding 中的 timeout_ms 需为大于 0 的整数")


class CommonRecoLocalAnnRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_local_ann"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    skip_check_attrs = ["top_k_attr", "nprobe_attr", "ef_attr", "search_k_attr", "dest_bucket_attr", 
    "leaves_to_search_attr", "pre_reorder_neighbors_num_attr", "src_data_type_attr"]
    for attr in skip_check_attrs:
      if self._config.get(attr):
        attrs.add(self._config.get(attr))
    attrs.add(self._config.get("src_items_attr", "src_items"))
    if "src_embedding_list_attr" in self._config:
      attrs.add(self._config.get("src_embedding_list_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("top_k")))
    attrs.update(self.extract_dynamic_params(self._config.get("multi_target_weight")))
    attrs.update(self.extract_dynamic_params(self._config.get("src_data_type"), check_format=False))
    attrs.update(self.extract_dynamic_params(self._config.get("dest_bucket"), check_format=False))
    attrs.update(self.extract_dynamic_params(self._config.get("dest_bucket_item_type"), check_format=False))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs_to_process = ["save_src_item_to_attr", "save_distance_to_attr", "save_seq_num_to_attr",
                        "save_src_data_type_to_attr", "save_dest_bucket_to_attr"]
    for attr in attrs_to_process:
      if attr in self._config:
        attrs.add(self._config[attr])
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0,
              "reason 需为大于 0 的整数")

    check_arg(
      isinstance(self._config.get("src_items_attr", "src_items"), str) and self._config.get("src_items_attr",
                                                                                            "src_items"),
      "src_items_attr 需为非空字符串")

    check_arg((isinstance(self._config.get("dest_bucket_attr"), str) and self._config.get(
      "dest_bucket_attr")) or (isinstance(self._config.get("dest_bucket"), str) and self._config.get(
      "dest_bucket")), "dest_bucket_attr 或 dest_bucket 需为非空字符串")
    
    check_arg((isinstance(self._config.get("src_data_type_attr"), str) and self._config.get(
      "dest_bucket_attr")) or (isinstance(self._config.get("src_data_type"), str) and self._config.get(
      "src_data_type")), "src_data_type_attr 或 src_data_type 需为非空字符串")
    
    check_arg((isinstance(self._config.get("top_k_attr"), str) and self._config.get(
      "top_k_attr")) or self._config.get("top_k"), "top_k_attr 或 top_k 需为非空字符串")


class CommonRecoModelRetriever(LeafRetriever):
  @strict_types
  def __init__(self, config: dict):
    if config.get("include_sample_list_user_info", False):
      config["sample_list_common_attr_key"] = self._SAMPLE_LIST_COMMON_ATTR_KEY
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_model"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def depend_on_sample_list_user_info(self) -> bool:
    return bool(self._config.get("sample_list_common_attr_key"))

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("extra_common_attrs", []))
    attrs.update(self.extract_dynamic_params(self._config.get("browsed_item_count")))
    if "sample_list_common_attr_key" in self._config:
      attrs.add(self._config["sample_list_common_attr_key"])
    for key in ["kess_service", "timeout_ms", "retrieval_item_type"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    for key in ["right_term_param", "term_list_param", "item_param", "final_param"]:
      for _, v in self._config[key].items():
        attrs.update(self.extract_dynamic_params(v))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    # NOTE(fangjianbing): 当 enable_model_retrieve_metrics 为 True 时, 其实该 processor 会产出
    # item_attr, 但只针对当前 processor 召回的部分 item, 目的也仅仅用于之后发送 leaf show, 所以这里
    # 故意忽略. leaf show 侧也会在 input_item_attrs 里忽略这些 item_attr
    attrs = set()
    attr = self._config.get("add_left_term_to_attr", "")
    if attr:
      attrs.add(attr)
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), (int, str)), "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("browsed_item_count", 0), int), "browsed_item_count 需为整数")

class CommonRecoExploreRpcRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_explore_rpc"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("count")))
    for key in ["imei_from_attr", "idfa_from_attr", "web_pass_info_from_attr"]:
      if self._config.get(key, ""):
        attrs.add(self._config.get(key, ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0, "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("count"), (int, str)), "count 需为整数或字符串类型")
    if isinstance(self._config["count"], int):
      check_arg(self._config["count"] > 0, "count 为整数时需大于 0")
    if isinstance(self._config["count"], str):
      check_arg(self._config["count"].startswith("{{") and self._config["count"].endswith("}}"), "count 为字符串时需满足动态参数 {{}} 格式")

class CommonRecoSlideLeafRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_slide_leaf"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["count"]))
    if self._config.get("user_info_attr"):
      attrs.add(self._config.get("user_info_attr"))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0, "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("reason", 0), int) and self._config["reason"] >= 0, "reason 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("count"), (int, str)), "count 需为整数或字符串类型")
    if isinstance(self._config["count"], int):
      check_arg(self._config["count"] > 0, "count 为整数时需大于 0")
    if isinstance(self._config["count"], str):
      check_arg(self._config["count"].startswith("{{") and self._config["count"].endswith("}}"), "count 为字符串时需满足动态参数 {{}} 格式")

class CommonRecoColossusRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_colossus_server"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      self._config[key] for key in [
        "save_photo_id_to_attr", "save_author_id_to_attr", "save_duration_to_attr", "save_play_time_to_attr",
        "save_tag_to_attr", "save_label_to_attr", "save_timestamp_to_attr",
      ] if key in self._config
    }

class CommonRecoResponseRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_common_reco_response"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["response_attr"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(extract_attr_names(self._config.get("recv_item_attrs", []), "as"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return extract_attr_names(self._config.get("recv_common_attrs", []), "as")

class CommonRecoParseContextRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "parse_context"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.update(self._config.get("extract_common_attrs", []))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.update(self._config.get("extract_item_attrs", []))
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["parse_from_attr"])
    return attrs

  @strict_types
  def like_an_enricher(self) -> bool:
    return bool(not self._config.get("extract_item_results", len(self._config.get("extract_item_attrs", [])) > 0))

class CommonRecoRedisRegexRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_redis"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for config in self._config.get("extra_item_attrs", []):
      attrs.add(config["name"])

    attrs.add(self._config.get("save_src_key_to_attr"))
    attrs.add(self._config.get("append_src_key_to_attr"))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("retrieve_num", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("key_prefix", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("retrieve_num_per_key", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("zrange_start", "")))
    for key in ["key_from_attr", "reason_from_attr"]:
      if self._config.get(key):
        attrs.add(self._config.get(key))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_err_code_to"))
    attrs.add(self._config.get("save_miss_key_to"))
    return attrs  

  @strict_types
  def _check_config(self) -> None:
    if "item_separator" in self._config:
      check_arg(isinstance(self._config.get("item_separator"), str)
                and self._config["item_separator"], "item_separator 需为 非空字符串")
    if "attr_separator" in self._config:
      check_arg(isinstance(self._config.get("attr_separator"), str)
                and self._config["attr_separator"], "attr_separator 需为 非空字符串")
    if "item_regex" in self._config:
      check_arg(isinstance(self._config.get("item_regex"), str) and self._config["item_regex"],
                "item_regex 需为 非空字符串")
    check_arg(not (("item_separator" in self._config or "attr_separator" in self._config) and "item_regex" in self._config),
              "item_regex 不能与 item_separator 或 attr_separator 同时使用")
    check_arg("item_separator" in self._config or "attr_separator" in self._config or "item_regex" in self._config,
              "item_regex item_separator attr_separator 不可同时为空")
    check_arg(isinstance(self._config.get("retrieve_num"), int) and self._config["retrieve_num"] > 0 or
              isinstance(self._config.get("retrieve_num"), str),
              "retrieve_num 需为大于 0 的整数")
    check_arg(any(self._config.get(v) for v in ["key", "key_from_attr"]), "缺少 key 或 key_from_attr 配置")
    if "cache_bits" in self._config:
      check_arg(isinstance(self._config.get("cache_bits"), int) and 0 <= self._config["cache_bits"] < 30,
                "cache_bits 需为大于等于 0 且小于 30 的整数")
    if "cache_delay_delete_ms" in self._config:
      check_arg(isinstance(self._config.get("cache_delay_delete_ms"), int) and self._config["cache_delay_delete_ms"] > 0,
                "cache_delay_delete_ms 需为大于 0 的整数")
    if "cache_expire_second" in self._config:
      check_arg(isinstance(self._config.get("cache_expire_second"), int) and self._config["cache_expire_second"] > 0,
                "cache_expire_second 需为大于 0 的整数")
    for config in self._config.get("extra_item_attrs", []):
      if config.get("as_score") is True:
        check_arg(config.get("type") in ("int", "double"), "as_score 为 True 时 type 必须为 int 或 double")

    extra_item_attr_as_score = self._config.get("extra_item_attr_as_score")
    if extra_item_attr_as_score:
      check_arg(isinstance(extra_item_attr_as_score, str), "extra_item_attr_as_score 应为非空字符串")
      score_attr = filter(lambda x: x["name"] == extra_item_attr_as_score and x["type"] in ("int", "double"), self._config.get("extra_item_attrs", []))
      check_arg(next(score_attr, None), "extra_item_attr_as_score 应为 extra_item_attrs 中一项，且类型为 int / double")

class CommonRecoRedisIdPoolRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_id_by_redis"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    retrieval_type = self._config.get("retrieval_type", -1)
    if retrieval_type == 0:
      ret.add("_USER_ID_")
    elif retrieval_type == 1:
      if "output_attr" in self._config:
        ret.add(self._config.get("output_attr"))
    return ret

class CommonRecoRpcIdPoolRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_id_by_rpc"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    retrieval_type = self._config.get("retrieval_type", -1)
    if retrieval_type == 0:
      ret.add("_USER_ID_")
    elif retrieval_type == 1:
      if "output_attr" in self._config:
        ret.add(self._config.get("output_attr"))
    return ret

class CommonRecoBtqIdPoolRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_id_by_btq"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    retrieval_type = self._config.get("retrieval_type", -1)
    if retrieval_type == 0:
      ret.add("_USER_ID_")
    elif retrieval_type == 1:
      if "output_attr" in self._config:
        ret.add(self._config.get("output_attr"))
    return ret
  
class CommonRecoElasticSearchRetriever(LeafRetriever):

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_new_elastic_search"
  
  @strict_types
  def _check_config(self) -> None:
    for config in self._config.get("headers", []):
      check_arg("name" in config, "headers 中必须配置 name")
      check_arg("value" in config, "headers 中必须配置 value")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("index", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("query", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("clusters", "")))
    for config in self._config.get("headers", []):
      attrs.update(self.extract_dynamic_params(config.get("value", "")))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return extract_attr_names(self._config.get("item_attrs", []), "name")
