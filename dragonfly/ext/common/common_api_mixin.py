#!/usr/bin/env python3
# coding=utf-8
"""
filename: common_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, common api mixin
author: <EMAIL>
date: 2020-01-19 16:45:00
"""

from typing import Optional, Union, List
from collections import OrderedDict
import json
import re
from ...common_leaf_util import ArgumentError, check_abnormal_if_expr, extract_attrs_from_expr, check_parenthesis_closed, check_arg
from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .common_leaf_retriever import *
from .common_leaf_enricher import *
from .common_leaf_arranger import *
from .common_leaf_observer import *
from .common_leaf_mixer import *

class CommonApiMixin(CommonLeafBaseMixin):
  """
  CommonLeaf 中台官方 Processor API 接口的 Mixin 实现
  """

  # 业务方调用接口
  def fake_retrieve(self, **kwargs):
    """
    CommonRecoFakeRetriever
    ------
    生成指定数目的伪造召回结果，item_key 从 0 开始顺序编号

    一般是为了在召回源准备好之前测试数据通路使用

    参数配置
    ------
    `item_keys`: [list] 选配项，手动指定要召回的 item_key 列表

    `num`: [int] [动态参数] 选配项，指定召回的 item 数目，item key 从 1 开始自动自增

    `reason`: [int] 选配项，指定召回原因，默认为 0

    调用示例
    ------
    ``` python
    .fake_retrieve(item_keys=[111, 112, 113], reason=888)
    .fake_retrieve(num=100, reason=999)
    ```
    """
    self._add_processor(CommonRecoFakeRetriever(kwargs))
    return self

  def enrich_by_generic_grpc(self, **kwargs):
    """
    CommonRecoGenericGrpcEnricher
    ------
    通用 grpc 调用接口。将 request_attr 作为 request 发送到相应的 grpc 服务，将 response 存入 response_attr

    method_name 支持任意 rpc 方法名。可以是没有编译成 protobuf 的方法，会动态注册。

    一般可在前序使用 build_protobuf() 构建 request_attr 内容，并在之后使用 enrich_with_protobuf() 解析 response_attr 数据。

    参数配置
    ------
    `kess_service`: [string] [动态参数] grpc 服务的 kess 服务名

    `service_group`: [string] grpc 服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] [动态参数] 请求 grpc 服务的超时时间，默认值为 200

    `method_name`: [string] rpc 服务的方法名, 格式 为 /{rpc.package}.{rpc.service}/{rpc.method}, 通过proto 动态注册方法支持任意 grpc 方法名。

    `request_attr`: [string] 存储将要发送给 grpc 服务的 request 的 common attr

    `response_attr`: [string] 存储 grpc 服务接收到的 request 的 common attr

    `response_class`: [string] grpc 服务接收的 response 的 类型，当前支持所有链接的 Message 类型。

    `use_dynamic_proto`: [bool] 选配项, response_class 是否使用 proto 动态注册, 默认为 False

    调用示例
    ------
    ``` python
    .enrich_by_generic_grpc(
      kess_service="grpc_SomeUserProfileService",
      timeout_ms=200,
      method_name="/kuaishou.reco.RecoUserProfile/Load",
      request_attr="user_info_request",
      response_attr="user_info_response",
      response_class="kuaishou.reco.RecoUserProfileResponse"
    )
    ```
    """

    kwargs["response_class"] = kwargs["response_class"].replace("::", ".")
    self._add_processor(CommonRecoGenericGrpcEnricher(kwargs))
    return self

  def fetch_sphinx_params(self, **kwargs):
    """
    CommonRecoSphinxParameterEnricher
    ------
    调用自动调参服务 parameter server 获取指定的 common attr，仅支持 double 类型

    用于自动调参过程中替换召回/排序阶段的超参数

    参数配置
    ------
    `kess_service`: [string] [动态参数] parameter server 的 kess name

    `timeout_ms`: [int] 超时时间

    `reco_biz`: [string] 业务名称

    `model_name`: [string] [动态参数] 拉取参数归属的模型名

    `params`: [list] 拉取的参数名列表，支持对参数重命名保存

    调用示例
    ------
    ``` python
    .fetch_params(
      kess_service="grpc_paramaterServer",
      model_name="RoughRank",
      params=["wctr", "wcvr"])

    # params 重命名
    .fetch_params(
      kess_service="grpc_paramaterServer",
      model_name="RoughRank",
      params=[{"name":"wctr", "as":"wctr_test"}, {"name":"wcvr", "as":"wcvr_test"}])
    ```
    """
    self._add_processor(CommonRecoSphinxParameterEnricher(kwargs))
    return self

  def attr_statistic(self, **kwargs):
    """
    CommonRecoAttrStatisticEnricher
    ------
    统计指定的 item attr 信息，并把统计信息写入 common attr，仅支持统计 int/string 类型的 item attr

    参数配置
    ------
    `statistic_config`: [list] 需要统计的 item attr 的配置
      - `attr_name`: [string] 统计的 item attr 名
      - `aggregator`: [string] 选配项，参数聚合的方式，支持 count(累加计数)，默认为 count
      - `save_values_to`: [string] 选配项，将 item attr 的值列表输出到的 common attr 名
      - `save_results_to`: [string] 选配项，将 item attr 的统计结果列表输出到的 common attr 名，与上述值列表按下标位置一一对应

    `check_point`: [string] 选配项，自定义打点位置标识，用于统计阶段的标记

    `save_as_json_to`: [string] 选配项，统计结果以 json 格式存入的 common attr

    调用示例
    ------
    ``` python
    .attr_statistic(
      statistic_config=[{
        "attr_name": "reason",
        "aggregator": "count",
        "save_values_to": "tag_list",
        "save_results_to": "tag_count_list",
      }],
    )

    # json 格式的统计结果：
    # {
    #   check_point1:[attr_name1:{attr_value: statistic_value}, attr_name2:{attr_value: statistic_value}],
    #   check_point2:[attr_name3:{attr_value: statistic_value}, attr_name4:{attr_value: statistic_value}],
    # }
    # 示例配置的示例统计信息：
    # {"retrieval_1":["reason":{"110112": 1666, "110115": 1334}]}
    .attr_statistic(
      statistic_config=[{
        "attr_name": "reason",
        "aggregator": "count"
      }],
      check_point="retrieval_1",
      save_as_json_to="statisticInfo",
    )
    ```
    """
    self._add_processor(CommonRecoAttrStatisticEnricher(kwargs))
    return self

  def retrieve_by_sub_flow(self, **kwargs):
    """
    CommonRecoPipelineRetriever
    ------
    异步并行一条独立的 common leaf pipeline (dragonfly LeafFlow)，并根据配置将其结果合并到当前主流程中。

    ?> 提示：推荐利用 `@async_retrieve` 装饰器来间接调用该接口，功能和配置一致，使用更便捷：[参考示例](http://ksurl.cn/T2R59HNU)

    ?> 提示：具有共同下游的多个 sub_flow 的结果默认将按调用的先后顺序合入主 flow，如果需要调整合入的先后顺序，可配置 `wait_priority` 整数值（默认值为 0，可为负），priority 较大的 sub_flow 将被优先等待结果并合入

    参数配置
    ------
    `sub_flow`: [LeafFlow] 异步并行的 LeafFlow 对象

    `timeout_ms`: [int] [动态参数] 选配项，sub_flow 执行超时时间毫秒数，若小于等于 0 则无超时，默认值为 0。

    `retrieve_num`: [int] [动态参数] sub_flow 召回结果的数量上限，默认不做限制.

    `deduplicate_results`: [bool] [动态参数] 召回的 item 是否和主流程中的当前结果集做去重，以保证 retrieve_num 个结果都是唯一的，默认为 false，执行优先于 retrieve_num。

    `pass_browse_set`: [bool] 把 request 请求携带的 browse set 填充到待执行的 sub_flow 中，默认为 true

    `pass_common_attrs_in_request`: [bool] 把 request 请求携带的 common attrs 填充到待执行的 sub_flow 中，默认为 true

    `pass_common_attrs`: [list] 选配项，执行 sub_flow 携带的初始 common attrs，若缺省将由框架自动推断

    `pass_common_attrs_in_list`: [string] **(不建议使用，未来可能废弃)** 变长填充列表，值类型为 list，拷贝 list 值以及对应的 common attr value 到待执行的 sub_flow 中，默认为空

    `pass_all_items`: [bool] 选配项，是否将主 flow 中的所有 item 传递到 sub_flow 中，默认为 false

    `pass_item_attrs`: [list] 选配项，仅在 `pass_all_items=True` 时有效，指定传递 item 给 sub_flow 时要同时携带的 item_attr。

    `deep_copy`: [bool] 选配项 (与原 no_copy 配置项功能相同，配置相反) 将 pass_common_attrs && pass_common_attrs_in_list 内的 common_attr 以深拷贝的方式传入 sub_flow。默认为 true

    `deep_copy_attrs`: [list] 选配项 deep_copy 为 false 时，特别指定需要 deep_copy 的 attr（pass_common_attrs 内命中的 attr 会强制深拷贝）

    `shadow_copy_attrs`: [list] 选配项 deep_copy 为 true 时，特别指定不进行 deep_copy 的 attr（pass_common_attrs 内命中的 attr 会强制浅拷贝）

    `merge_item_attrs`: [list] 选配项，合并 sub_flow 返回的 item attrs，支持对 attr 重命名保存，若缺省将由框架自动推断。注意: merge_and_overwrite=true 重复时会覆盖原 item attrs。支持两种格式，
      - string 格式：直接填写 attr 名
      - dict 格式：支持 name、as、merge_mode 字段，merge_mode 可选项 ["COPY", "MIN", "MAX", "SUM", "CONCAT"]，注意仅 int/float 类型支持 MAX/MIN/SUM，仅 int/float/string 支持 CONCAT，默认为 COPY。

    `merge_common_attrs`: [list] 选配项，合并 sub_flow 返回的 common attrs，支持对 attr 重命名保存，若缺省将由框架自动推断。注意: merge_and_overwrite=true 重复时会覆盖原 common attrs

    `merge_and_overwrite`:[bool] 选配项, 是否使用 sub_flow 返回的 attr 覆盖现有 attr。默认为 false 。

    `task_queue_id`: [int] [动态参数] 选配项，选择使用的任务队列。默认值为0。注意：需配置 gflag sub_flow_task_queue_num 使用，不能大于等于 sub_flow_task_queue_num。

    `common_reco_context_attr_name`: [str] 选配项，从对应名称的 common_attr 获取 CommonRecoContext 对象传递到 sub_flow 中，搭配 `init_common_reco_context` 使用，默认为 ""，即不传递 CommonRecoContext 对象到 sub_flow 中。

    调用示例
    ------
    ``` python
    '''
    Graph of M flow:
                       +---+
                  +--->| A |---+
                  |    +---+   |
    +--------+    |            |    +-------+
    | get_ab |--->|            |--->| dedup |
    +--------+    |            |    +-------+
                  |    +---+   |
                  +--->| B |---+
                       +---+
    '''
    A = LeafFlow(name="sub_a")
    B = LeafFlow(name="sub_b")
    M = LeafFlow(name="main") \\
        .get_abtest_params(name="get_ab") \\
        .retrieve_by_sub_flow(sub_flow = A, merge_item_attrs=["aid"]) \\
        .retrieve_by_sub_flow(sub_flow = B, merge_item_attrs=[{"name": "aid", "as": "aid_B"}]) \\
        .deduplicate(name="dedup")
    ```
    """
    self._add_processor(CommonRecoPipelineRetriever(kwargs))
    return self

  def arrange_by_sub_flow(self, **kwargs):
    """
    CommonRecoPipelineArranger
    ------
    按 item 维度分片交由 sub_flow 并行执行一个子流程逻辑。

    !> 注意：1.在调用该函数之前建议先用 deduplicate() 进行 item 去重，否则删除类操作无法对重复的 item 生效; 2.子流程中不允许包含 retriever。

    ?> 提示：也可以通过 `@parallel()` 装饰器来简化使用，相关配置项可作为装饰器的参数传入，具体用法参考调用示例，或此 [playground 链接](http://ksurl.cn/vlOIM1WQ)。

    注意：子流程中不能包含 retriever。

    参数配置
    ------
    `sub_flow`: [LeafFlow] 异步并行的 LeafFlow 对象。

    `expected_partition_size`: [int] [动态参数] sub_flow 并行处理的分片大小数，会以此为 base 粗略估算出切片数量，再按照切片数量让每片 item 数接近，举例如果该项配置成 60，上游传入 item 数量为200，那么会切成 4 片，每片为 50，而不是 60，60，60，20

    `group_by`: [string] sub_flow 按 item_attr 的值进行分组执行，每一组 item 的 attr 值相同。仅支持 item_attr 为 int 或 string 类型。没有 item attr 值的 item 会被分为一组。一个分组排序取 top1 的示例：[http://ksurl.cn/T49jFHvF](http://ksurl.cn/T49jFHvF)

    `pass_browse_set`: [bool] 把 request 请求携带的 browse set 填充到待执行的 sub_flow 中，默认为 true

    `pass_common_attrs_in_request`: [bool] 把 request 请求携带的 common attrs 填充到待执行的 sub_flow 中，默认为 false（注意: 2022-03-01 19:42 之前默认值为 true）

    `pass_common_attrs`: [list] 选配项，sub_flow 携带的初始 common attrs，若缺省将由框架自动推断。注意：这些属性会被标记为只读，直到 sub_flow 执行完成。

    `pass_item_attrs`: [list] 选配项，sub_flow 携带的初始 item attrs，若缺省将由框架自动推断。注意：这些属性会被标记为只读，直到 sub_flow 执行完成。

    `deep_copy_common_attrs`: [list] 选配项 对 pass_common_attrs 内命中的 attr 强制深拷贝，不参与框架关于 attr 的自动推断

    `deep_copy_item_attrs`: [list] 选配项 对 pass_item_attrs 内命中的 attr 强制深拷贝，不参与框架关于 attr 的自动推断

    `merge_common_attrs`: [list] 选配项，sub_flow 产出的 common attrs，产出的 common attrs 将直接写入主流程。多分片下，只取第一个有值的分片作为输出。若缺省将由框架自动推断。

    `merge_item_attrs`: [list] 选配项，sub_flow 产出的 item attrs，产出的 item attrs 将直接写入主流程。若缺省将由框架自动推断。注意：不受 merge_and_overwrite 影响。

    `initial_parallel_num`: [int] 选配项，初始切片数，一般仅在发生性能问题时才需要配置！根据自己业务情况估算，每个请求的期望 item 数 / expected_partition_size，用于初始化资源池，实际切片数根据上面的 expected_partition_size 规则决定，默认为 8，无法确定可以不配置。实际切片数超过资源池数量时，会自动扩容，有可能会引发当前请求超时

    `task_queue_id`: [int] [动态参数] 选配项，选择使用的任务队列。默认值为0。注意：需配置 gflag sub_flow_task_queue_num 使用，不能大于等于 sub_flow_task_queue_num。

    调用示例
    ------
    ``` python
    '''
    Graph of M flow:
                       +---+
                  +--->| A |---+
                  |    +---+   |
    +--------+    |            |    +-----+
    | get_ab |--->|            |--->| var |
    +--------+    |            |    +-----+
                  |    +---+   |
                  +--->| B |---+
                       +---+
    '''
    # 以下两种方式都是把所有 item 按 100 的分片大小分别并行计算得出 ctr、ltr 两个 item attr 值的加速方案
    # 1. 直接调用
    sub_flow = LeafFlow(name="sub_a")
    sub_flow.gen_random_item_attr(attr_name="ctr", attr_type="double")
    sub_flow.gen_random_item_attr(attr_name="ltr", attr_type="double")
    main_flow = LeafFlow(name="main")
    main_flow.arrange_by_sub_flow(sub_flow=sub_flow, expected_partition_size=100, merge_item_attrs=["aid"]) \\

    # 2. 装饰器调用
    from dragonfly.decorators import parallel
    @parallel(partition_size=100)
    def compute(flow):
      flow.gen_random_item_attr(attr_name="ctr", attr_type="double")
      flow.gen_random_item_attr(attr_name="ltr", attr_type="double")
    main_flow = MyFlow()
    compute(main_flow)
    ```
    """
    self._add_processor(CommonRecoPipelineArranger(kwargs))
    return self

  def enrich_by_sub_flow(self, **kwargs):
    """
    CommonRecoPipelineEnricher
    ------
    异步并行一条独立的 common leaf pipeline (dragonfly LeafFlow)，并根据配置将子流程中产生的 attr 合并到当前主流程中。该 processor 会拷贝主流程中全部 item 和指定的 attr 到子流程中。

    注意：子流程中不能包含 retriever。

    ?> 提示：具有共同下游的多个 sub_flow 的结果默认将按调用的先后顺序合入主 flow，如果需要调整合入的先后顺序，可配置 `wait_priority` 整数值（默认值为 0，可为负），priority 较大的 sub_flow 将被优先等待结果并合入

    参数配置
    ------
    `sub_flow`: [LeafFlow] 异步并行的 LeafFlow 对象

    `timeout_ms`: [int] [动态参数] 选配项，sub_flow 执行超时时间毫秒数，若小于等于 0 则无超时，默认值为 0。

    `pass_browse_set`: [bool] 把 request 请求携带的 browse set 填充到待执行的 sub_flow 中，默认为 true

    `pass_common_attrs_in_request`: [bool] 把 request 请求携带的 common attrs 填充到待执行的 sub_flow 中，默认为 false（注意: 2022-03-01 19:42 之前默认值为 true）

    `pass_common_attrs`: [list] 选配项，执行 sub_flow 携带的初始 common attrs，若缺省将由框架自动推断。注意：这些属性同时会被标记为只读，直到 sub_flow 执行完成。

    `pass_item_attrs`: [list] 选配项，执行 sub_flow 携带的初始 item attrs，若缺省将由框架自动推断。注意：这些属性同时会被标记为只读，直到 sub_flow 执行完成。

    `merge_item_attrs`: [list] 选配项，合并 sub_flow 返回的 item attrs，若缺省将由框架自动推断。支持对 attr 以 name/as 的配置格式重命名保存。注意: merge_and_overwrite=true 重复时会覆盖原 item attrs

    `merge_common_attrs`: [list] 选配项，合并 sub_flow 返回的 common attrs，若缺省将由框架自动推断。支持对 attr 以 name/as 的配置格式重命名保存。注意: merge_and_overwrite=true 重复时会覆盖原 common attrs

    `merge_and_overwrite`:[bool] 选配项, 是否使用 sub_flow 返回的 attr 覆盖现有 attr。默认为 false 。

    `save_results_to`: [string] 选配项，将 sub_flow 执行完毕后的 item 结果集（以 vector* 指针格式）存储到指定的 CommonAttr 下供后续使用（可配合 filter_by_item_results() 接口清理主 flow 中的结果）

    `deep_copy`: [bool] 选配项 将 pass_common_attrs && pass_item_attrs 内的 attr 以深拷贝的方式传入 sub_flow 并且不会改变 attr 的 read_only 属性。默认为 false

    `deep_copy_common_attrs`: [list] 选配项 deep_copy 为 false 时，特别指定需要 deep_copy 的 attr（pass_common_attrs 内命中的 attr 会强制深拷贝），不参与框架关于 attr 的自动推断

    `deep_copy_item_attrs`: [list] 选配项 deep_copy 为 false 时，特别指定需要 deep_copy 的 attr（pass_item_attrs 内命中的 attr 会强制深拷贝），不参与框架关于 attr 的自动推断

    `shadow_copy_common_attrs`: [list] 选配项 deep_copy 为 true 时，特别指定不进行 deep_copy 的 attr（pass_common_attrs 内命中的 attr 会强制浅拷贝），不参与框架关于 attr 的自动推断

    `shadow_copy_item_attrs`: [list] 选配项 deep_copy 为 true 时，特别指定不进行 deep_copy 的 attr（pass_item_attrs 内命中的 attr 会强制浅拷贝），不参与框架关于 attr 的自动推断

    `merge_item_attr_for_all_items`: [bool] 选配项，是否将 subflow 中所有 item 的指定 attr 都 merge 回主 flow，默认为 false（只 merge 主 flow 中存在的 item）

    `task_queue_id`: [int] [动态参数] 选配项，选择使用的任务队列，默认值为0。注意：需配置 gflag sub_flow_task_queue_num 使用，不能大于等于 sub_flow_task_queue_num。

    `common_reco_context_attr_name`: [str] 选配项，从对应名称的 common_attr 获取 CommonRecoContext 对象传递到 sub_flow 中，搭配 `init_common_reco_context` 使用，默认为 ""，即不传递 CommonRecoContext 对象到 sub_flow 中。

    调用示例
    ------
    ``` python
    '''
    Graph of M flow:
                       +---+
                  +--->| A |---+
                  |    +---+   |
    +--------+    |            |    +-----+
    | get_ab |--->|            |--->| var |
    +--------+    |            |    +-----+
                  |    +---+   |
                  +--->| B |---+
                       +---+
    '''
    A = LeafFlow(name="sub_a")
    B = LeafFlow(name="sub_b")
    M = LeafFlow(name="main") \\
        .fake_retrieve(num=100, reason=999) \\
        .enrich_by_sub_flow(sub_flow = A, merge_item_attrs=["aid"]) \\
        .enrich_by_sub_flow(sub_flow = B, merge_item_attrs=[{"name": "aid", "as": "aid_B"}]) \\
        .variant(name="var")
    ```
    """
    self._add_processor(CommonRecoPipelineEnricher(kwargs))
    return self

  def retrieve_by_common_attr(self, **kwargs):
    """
    CommonRecoCommonAttrRetriever
    ------
    从某个 int 或 int_list 类型的 CommonAttr 中获取 item_id 进行召回并填入结果集

    参数配置
    ------
    `attr`: [string] 待召回的 CommonAttr 名称

    `reason`: [int] 召回原因

    `num_limit`: [int] 选配项, 限制从该 CommonAttr 里召回的最大数目

    `random_pick`: [bool] 选配项，当配置了 `num_limit` 时可选择是否随机选取，随机选取的结果也将保持原 list 中的相对顺序，默认值为 False

    `retrieval_item_type`: [int] 选配项, 召回 item 的 item_type，和 item_id 一起产生 item_key，默认值为 0

    调用示例
    ------
    ``` python
    .retrieve_by_common_attr(attr="like_list", reason=999)
    ```
    """
    attr = {
      "name": kwargs.pop("attr"),
      "reason": kwargs.pop("reason"),
    }
    for key in ["num_limit", "retrieval_item_type"]:
      if key in kwargs:
        attr[key] = kwargs.pop(key)
    conf = { "attrs": [attr] }
    conf.update(kwargs)
    self._add_processor(CommonRecoCommonAttrRetriever(conf))
    return self

  def retrieve_by_common_attrs(self, **kwargs):
    """
    CommonRecoCommonAttrRetriever
    ------
    从多个 int_list 类型的 CommonAttr 中获取 item_key 进行召回并填入结果集（注意不是 item_id !!!）

    参数配置
    ------
    `attrs`: [dict]
      - `name`: [string] 待召回的 CommonAttr 名称
      - `reason`: [int] 召回原因
      - `num_limit`: [int] 选配项, 限制从该 CommonAttr 里召回的最大数目
      - `random_pick`: [bool] 选配项，当配置了 `num_limit` 时可选择是否随机选取，随机选取的结果也将保持原 list 中的相对顺序，默认值为 False
      - `retrieval_item_type`: [int] 选配项, 召回 item 的 item_type，和 item_id 一起产生 item_key，默认值为 0

    `total_limit`: [int] 选配项, 若不小于 0 则控制从所有 CommonAttr 中召回的所有数目不超过该值

    `exclude_items_in_attr`: [string] 选配项, 指定一个 int/int_list 类型的 CommonAttr 对待召回 item 进行提前过滤

    调用示例
    ------
    ``` python
    .retrieve_by_common_attrs(
      attrs=[{
        "name": "like_list",
        "reason": 991,
        "num_limit": 150,
      }, {
        "name": "click_list",
        "reason": 992,
        "num_limit": 150,
      }],
      total_limit=200
    )
    ```
    """
    self._add_processor(CommonRecoCommonAttrRetriever(kwargs))
    return self

  def retrieve_by_local_index(self, **kwargs):
    """
    CommonRecoCommonQueryRetriever
    ------
    从通用索引中通过 Query 语句进行 item 召回。

    [Query 检索语法介绍文档](https://docs.corp.kuaishou.com/k/home/<USER>/fcAC4SvGLozLHuj0wy-mSZ42Q)

    参数配置
    ------
    `reason`: [int] 召回原因

    `common_query`: [string] 公共查询条件，会分别跟 `querys` 中配置的各个 query 语句进行 AND 操作

    `querys`: [list] 查询条件列表，多个条件之间取并集进行结果返回
      - `query`: [string] 查询语句
      - `search_num`: [int] [动态参数] 每条查询语句的索引查找数目，可选项，缺省时将使用 default_search_num 值（备注：每条查询语句是指展开后的每条查询语句，也就是说对于 CommonAttr 里的每个值都会查找 `search_num` 个）
      - `random_search`: [int] [动态参数] 索引随机查找开关，0(关) or 1(开)，可选项，缺省时将使用 default_random_search 值
      - `reason`: [int] 选配项，针对单个 query 设置召回原因，默认为顶层配置的 `reason` 值
      - `max_attr_num`: [int] [动态参数] 当查询条件中包含 list 类型 attr 时，通过该配置限制取用的 list 大小，默认值 10000

    `default_search_num`: [int] 查询条件召回数目缺省值，默认值为 0

    `default_random_search`: [int] 随机召回开关缺省值，默认值为 1

    `default_total_request_num`: [int] [动态参数] 多 query 情况下的熔断数目，当前序 query 的总返回数超过该值时，后续 query 将不再执行，缺省则不做任何熔断处理

    `browsed_item_count`: [int] [动态参数] 请求携带的 browsed item 个数，用于在召回服务端进行前置过滤，正数为最近，0 为全部，负数为取最远的 abs(browsed_item_count) 个，缺省则不过滤！browsed item 来自请求中的 browse set

    调用示例
    ------
    ``` python
    .retrieve_by_local_index(
      reason = 1000,
      common_query = "resource_type:CONTENT_VIDEO",
      querys = [{
        "query": "show_count_log2:{{8~15}}",
      }, {
        # 单值情况不需要 {{}} 包裹
        "query": "show_count_log:7",
      }, {
        # 从 channel_list CommonAttr 中的 channel 进行召回
        "query": "sub_channel_id:{{channel_list}}",
        # 最多召回的 channel 数目
        "max_attr_num": 10,
      }],
      default_search_num = 2000,
      default_random_search = 1
    )
    ```
    """
    self._add_processor(CommonRecoCommonQueryRetriever(kwargs))
    return self

  def retrieve_by_remote_index(self, **kwargs):
    """
    CommonRecoCommonQueryRemoteRetriever
    ------
    从异地部署的通用索引中通过 Query 语句进行 item 召回（`.retrieve_by_local_index()` 的远程版本）

    [Query 检索语法介绍文档](https://docs.corp.kuaishou.com/k/home/<USER>/fcAC4SvGLozLHuj0wy-mSZ42Q)

    参数配置
    ------
    `kess_service`: [string] [动态参数] 远程索引服务的 kess 服务名

    `shard_num`: [int] 如果远程索引服务端是分片服务，通过该项指定分片数，默认为 1（未分片），如果设为 0 则自动从 kess 平台识别 shard 数目

    `timeout_ms`: [int] [动态参数] 请求远程索引服务的超时时间，默认值为 300

    `consistent_hash`: [bool] 是否按 user_id 或 device_id 对请求进行一致性 hash 的分发，以保证同一用户的请求始终落在同一索引机器上，默认 True

    `reason`: [int] 召回原因

    `common_query`: [string][动态参数] 公共查询条件，会分别跟 `querys` 中配置的各个 query 语句进行 AND 操作

    `querys`: [list] 查询条件列表，多个条件之间取并集进行结果返回
      - `query`: [string] 查询语句
      - `search_num`: [int] [动态参数] 每条查询语句的索引查找数目，可选项，缺省时将使用 default_search_num 值（备注：每条查询语句是指展开后的每条查询语句，也就是说对于 CommonAttr 里的每个值都会查找 `search_num` 个）
      - `random_search`: [int] [动态参数] 索引随机查找开关，0 or 1，可选项，缺省时将使用 default_random_search 值
      - `reason`: [int] 选配项，针对单个 query 设置召回原因，默认为顶层配置的 `reason` 值
      - `expire_second`: [int] [动态参数] 索引 server 端查询结果 cache 的过期时间（仅对 random search 有效），缺省时将使用 default_expire_second 的值
      - `max_attr_num`: [int] [动态参数] 当查询条件中包含 list 类型 attr 时，通过该配置限制取用的 list 大小，默认值 10000

    `save_score_to_attr`: [string] 如果不为空，且查询到的 score 不为空，则将各个召回 item 对应的 score 存入该 item_attr 中，可缺省

    `save_query_index_to_attr`: [string] 如果不为空，则将各个召回 item 对应的 query index 存入该 item_attr 中，可缺省 （备注：query index 指的是各个 query 全部展开后从 0 开始的 index）

    `default_search_num`: [int] 查询条件召回数目缺省值，默认值为 0

    `default_random_search`: [int] 随机召回开关缺省值，默认值为 1

    `default_total_request_num`: [int] [动态参数] 多 query 情况下的熔断数目，当前序 query 的总返回数超过该值时，后续 query 将不再执行，缺省则不做任何熔断处理

    `default_expire_second`: [int] 索引 server 端查询结果 cache 的过期时间（仅对 random search 有效），默认值为 0 关闭 cache 功能

    `browsed_item_count`: [int] [动态参数] 请求携带的 browsed item 个数，用于在召回服务端进行前置过滤，正数为最近，0 为全部，负数为取最远的 abs(browsed_item_count) 个，缺省则不过滤！browsed item 来自请求中的 browse set

    `exclude_items_in_attr`: [string] 选配项, 指定一个 int/int_list 类型的 CommonAttr 对待召回 item 进行提前过滤

    `ordered_index_send_by_shard`: [bool] 选配项，是否按查询的 query 语句进行 hash 分给不同的 shard 发送查询语句，有序倒排可用该开关减少对索引服务的 qps。默认为 False

    调用示例
    ------
    ``` python
    .retrieve_by_remote_index(
      kess_service = "grpc_XXX",
      timeout_ms = 300,
      reason = 1000,
      common_query = "resource_type:CONTENT_VIDEO",
      querys = [{
        "query": "show_count_log2:{{8~15}}",
      }, {
        # 单值情况不需要 {{}} 包裹
        "query": "show_count_log:7",
      }, {
        # 从 channel_list CommonAttr 中的 channel 进行召回
        "query": "sub_channel_id:{{channel_list}}",
        # 最多召回的 channel 数目
        "max_attr_num": 10,
      }],
      default_search_num = 2000,
      default_random_search = 1
    )
    ```
    """
    self._add_processor(CommonRecoCommonQueryRemoteRetriever(kwargs))
    return self

  def retrieve_by_ann_embedding(self, **kwargs):
    """
    CommonRecoEmbeddingRetriever
    ------
    从通用 Embedding 服务进行相似 item 召回

    参数配置
    ------
    `reason`: [int] 召回原因

    `kess_service`: [string] [动态参数] Embedding 服务的 kess 服务名

    `shard_num`: [int][动态参数] 如果 ann 服务端是分片服务，通过该项指定分片数，默认为 1（未分片）

    `space`: [string][动态参数] 如果 ann 服务端是分片服务，space 需要和 server 端做相同配置，默认为 cosine

    `service_group`: [string] Embedding 服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] [动态参数] 请求 embedding 服务的超时时间，默认值为 300

    `items_from_attr`: [list] 从哪些 common_attr 选取目标 item 发送给 embedding 服务，支持 int/int_list attr，
      NN retr 流程需要每个 src embedding 都有对应 id, items_from_attr 长度必须与 embeddings_from_attr 内 embedding 数量一致，为对应的 ID 列表

    `user_key_for_embedding`: [string] 选配项，使用 user key 作为 src_item_id 从 src_bucket 获取 embedding，用于 u2i 和 u2u 召回，可选值：
      1. "uid_only": 使用 uid 作为 user key；
      2. "did_only": 使用 did 作为 user key；
      3. "uid_or_did"：登录用户（uid > 0）使用 uid，否则使用 did。

    `embeddings_from_attr`: [list] 跟 items_from_attr 类似，从对应的 common_attr 中直接获取 embedding 数据（注意：common_attr 数目需与 items_from_attr 保持一致，且均为 double_list 类型，多个 item 的 embedding 按 items_from_attr 中的顺序拼接在一起）

    `attr_single_limit`: [int] 为 int_list 类型的 common_attr 限制最大选取数目，不限制可缺省该项

    `browsed_item_count`: [int] [动态参数] 请求携带的 browsed item 个数，用于在召回服务端进行前置过滤，正数为最近，0 为全部，负数为取最远的 abs(browsed_item_count) 个，缺省则不过滤！browsed item 来自请求中的 browse set

    `bound_type`: [dict] embedding 召回的边界类型，以下可选类型只可配置一项！
      - `top_k`: [int] [动态参数] 按距离最近的 K 个筛选
      - `total_limit`: [int] [动态参数] 根据 items_from_attr 配置中传入的 item 数量动态调整 top_k 的值，使召回的总数目控制在 total_limit 以下

    `algo_type`: [dict] 算法类型
      - `annoy`: [dict] annoy 算法参数配置
        - `search_k`: [int] [动态参数] 启发式的获取 search_k 个节点, 最后暴力找 top n 个结果
      - `hnsw`: [dict] hnsw 算法参数配置
        - `ef`: [int] [动态参数] 算法动态 list 大小，阈值越大耗时越长但结果更准确，同组数据调参时通常考虑和 m 成反比
      - `faiss`: [dict] faiss 算法参数配置
        - `nprobe`: [int] [动态参数] 从 nlist 个分区的 nprobe 中查找结果，越大越耗时，但结果越精确，可缺省不配置

    `src_data_type`: [string] [动态参数] 目标 item 的 embedding 数据桶名称，也可通过 "{{}}" 格式从 CommonAttr 中获取

    `src_bucket`: [string] [动态参数] [DEPRECATED] 目标 item 的 embedding 数据桶名称，也可通过 "{{}}" 格式从 CommonAttr 中获取

    `dest_bucket`: [string] [动态参数] 待召回候选集的 embedding 数据桶名称，也可通过 "{{}}" 格式从 CommonAttr 中获取

    `dest_bucket_item_type`: [int] [动态参数] dest_bucket 中各个 item 的 item_type（该值一般需要从 embedding 服务的数据提供方获取），默认值为 0

    `save_source_item_to_attr`: [string] 如果不为空，则将各个召回 item 对应的 source_item_key 存入该 item_attr 中（类型为 int_list, 对应多个 source item），可缺省

    `save_embs_to_attr`: [string] 如果不为空，则将各个召回 item 对应的 embeddings 存入该 item_attr 中（类型为 double_list, 对应多个 source item），可缺省

    `save_distance_to_attr`: [string] 如果不为空，则将各个召回 item 与其 source_item 的 distance 值存入该 item_attr 中（类型为 double_list, 对应多个 source item），可缺省

    `save_seq_num_to_attr`: [string] 如果不为空，则将各个召回 item 在其 source_item 所触发的结果队列顺序值（从 0 开始）存入该 item_attr 中（类型为 int_list, 对应多个 source item），可缺省

    `fetch_user_embedding`: [dict] 已废弃，请使用 [get_kuiba_user_embedding](https://dragonfly.corp.kuaishou.com/#/api/retrieval?id=get_kuiba_user_embedding) 获取 kuiba tower 顶层embedding

    `perturb`: [dict] 如果需要对 user embedding 做随机扰动，以避免对相同用户重复触发相同的 item
      - `perturb_norm_stddev`: [double] 随机扰动的 norm_stddev
      - `perturb_prob`: [double] user embedding 随机扰动的概率
      - `embedding_bit_perturb_prob`: [double] user embedding 每一位扰动的概率
    
    `consistent_hash`: [bool] 是否按 user_id 或 device_id 对请求进行一致性 hash 的分发，以保证同一用户的请求始终落在同一召回机器上，默认 False
    
    调用示例
    ------
    ``` python
    .retrieve_by_ann_embedding(
      kess_service = "grpc_acfunI2iEmbeddingRetrieveServer",
      timeout_ms = 300,
      reason = 1000,
      items_from_attr = ["play_pid_list"],
      attr_single_limit = 10,
      bound_type = {
        "top_k": 10,
        # "total_limit": 100,
      },
      algo_type = {
        "faiss": {},
      },
      # item 对应的数据桶，如 i2i 这里是 "item", u2i 这里是 "user"
      src_data_type = "ac_mmu",
      # 目标桶
      dest_bucket = "ac_mmu"
    )
    ```
    """

    self._add_processor(CommonRecoEmbeddingRetriever(kwargs))
    return self


  def retrieve_by_local_ann(self, **kwargs):
    """
    CommonRecoLocalAnnRetriever
    ------
    从本地 ann 进行相似 item 召回

    参数配置
    ------
    `reason`: [int] 召回原因

    `top_k`: [int][动态参数] 直接指定 top_k ， 默认值为 0 (配置 top_k_attr 已废弃，已上线服务保证兼容)

    `nprobe_attr`: [string] nprobe 配置的 common attr key， 默认值为 "nprobe"

    `multi_target_weight`: [float list][动态参数] GPU KNN 特有参数，多目标权配置， 默认值为空

    `leaves_to_search_attr`: [string] leaves_to_search 配置的 common attr key， 默认值为 "leaves_to_search"

    `pre_reorder_neighbors_numarch_attr`: [string] pre_reorder_neighbors_numarch 配置的 common attr key， 默认值为 "pre_reorder_neighbors_numarch"

    `src_data_type`: [string][动态参数] 直接指定 src_data_type 如: "user" 、 "src_photo"， 默认值为 ""，  (配置 src_data_type_attr 已废弃，已上线服务保证兼容)

    `src_items_attr`: [string] src_items 配置的 common attr key 默认值为 "src_items"，
      NN retr 流程需要每个 src embedding 都有对应 id, src_items 长度必须与 src_embedding_list_attr 内 embedding 数量一致，为对应的 ID 列表

    `src_embedding_list_attr`: [string] src_embedding_list 配置的 common attr key， 默认值为 "src_embedding_list"

    `dest_bucket`: [string][动态参数] 直接指定 dest_bucket 如: "photo" 、 "dest_photo"， 默认值为 ""， (配置 dest_bucket_attr 已废弃，已上线服务保证兼容)

    `save_src_item_to_attr`: [string] 如果不为空，则将各个召回 item 对应的 src_item_key 存入该 item_attr 中（类型为 int_list, 对应多个 src item），可缺省

    `save_distance_to_attr`: [string] 如果不为空，则将各个召回 item 与其 src_item 的 distance 值存入该 item_attr 中（类型为 double_list, 对应多个 source item），可缺省

    `save_seq_num_to_attr`: [string] 如果不为空，则将各个召回 item 在其 src_item 所触发的结果队列顺序值（从 0 开始）存入该 item_attr 中（类型为 int_list, 对应多个 source item），可缺省

    `save_src_data_type_to_attr`: [string] 如果不为空，则将各个召回 item 与其 src_data_type 值存入该 item_attr 中（类型为 string_list, 对应多个 src_data_type），可缺省

    `save_dest_bucket_to_attr`: [string] 如果不为空，则将各个召回 item 与其 dest_bucket 的 distance 值存入该 item_attr 中（类型为 string_list, 对应多个 dest_bucket item），可缺省

    `dest_bucket_item_type`: [int] dest_bucket 中各个 item 的 item_type（该值一般需要从 embedding 服务的数据提供方获取），默认值为 0

    `disabled`: [bool] 设置 True 则 retrieve 直接返回，不需等待 local ann ready 也不会召回任何 item, 默认值为 False

    `timeout_ms`: [int] [动态参数] 召回的超时时间，单位 ms, 如果值小于或者等于 0 则超时配置不生效, 默认值为 0

    调用示例
    ------
    ``` python
    .retrieve_by_local_ann(
      reason = 1000,
      src_embedding_list_attr = "src_item_embeddings",
    )
    ```
    """
    self._add_processor(CommonRecoLocalAnnRetriever(kwargs))
    return self

  def retrieve_by_model(self, **kwargs):
    """
    CommonRecoModelRetriever
    ------
    从 model retrieval 服务召回 item

    参数配置
    ------
    `reason`: [int] 召回原因

    `kess_service`: [string] [动态参数] Embedding 服务的 kess 服务名

    `service_group`: [string] Embedding 服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] [动态参数] 请求 embedding 服务的超时时间，默认值为 300

    `retrieval_item_type`: [int] [动态参数] 召回 item 的 item_type，和 item_id 一起产生 item_key，默认值为 0

    `browsed_item_count`: [int] [动态参数] 请求携带的 browsed item 个数，用于在召回服务端进行前置过滤，正数为最近，0 为全部，负数为取最远的 abs(browsed_item_count) 个，缺省则不过滤！browsed item 来自请求中的 browse set

    `include_sample_list_user_info`: [bool] 是否额外获取 SampleList 服务的 UserAttr 并发送, 默认值为 False

    `extra_common_attrs`: [list] 默认情况会把 request 中带来的 CommonAttr 全部作为 User 侧特征进行发送，可通过该项配置指定哪些中间生成的 CommonAttr 也进行发送，可缺省

    `exclude_common_attrs`: [list] 默认情况会把 request 中带来的 CommonAttr 全部作为 User 侧特征进行发送，可通过该项配置指定哪些 Attr 不进行发送，array 中的每项值为属性名，可缺省

    `add_left_term_to_attr`: [string] 选配项，将召回 item 的 left_term 值追加存入指定的 int_list 类型 item_attr

    `right_term_param`: [dict] left term -> right term 的参数
      - `iterate_count`: [int] [动态参数] 探查 right term 的数目， <=0 非法
      - `right_term_count`: [int] [动态参数] 最终取的 right term 数目，<=0 非法
      - `benefit_formula`: [string] [动态参数] 算 benefit 的公式, 对应服务配置中 formula 下面的 object key
      - `min_benefit`: [int] [动态参数] 最小 benefit 阈值，如果 <0 表示不限制
      - `param_by_attr`: [list] 支持不同的 left term 配置不同的遍历和截取数，可以不配
          - `left_attr_name`: [string] left term 的名字， 空字串非法
          - `iterate_count`: [int] 单独配置的探查 right term 的数目， <=0 非法
          - `right_term_count`: [int] 单独配置的最终取的 right term 数目 <=0 非法

    `term_list_param`: [dict] 所有 left term -> right term 后再对进行一次排序和截断的参数
      - `count`: [int] [动态参数] 最终只留多少个 right term，如果 <0 表示不限制

    `item_param`: [dict] right term -> item 的参数
      - `iterate_count`: [int] [动态参数] 倒排检索 item 的数目，<=0 非法
      - `item_count`: [int] [动态参数] 取的 item 数目，<=0 非法
      - `score_formula`: [string] [动态参数] 算 score 的公式

    `final_param`: [dict] 所有 item 最终返回之前的参数
      - `count`: [int] [动态参数] 最终返回的结果数目，如果 <0 表示不限制一般不这么用，除非 RetrieveItemParam.item_count 就够用了
      - `score_formula`: [string] [动态参数] 算 score 的公式

    `enable_model_retrieve_metrics`: [bool] 是否记录 model retrieve 的指标评估数据并在之后的 leaf show 中发送, 默认值为 True

    调用示例
    ------
    ``` python
    .retrieve_by_model(
      kess_service = "grpc_acfunCommonModelRetrieval",
      timeout_ms = 300,
      # 召回的 item type，和 item id 一起生产 item key 0 为不处理
      retrieval_item_type = 0,
      browsed_item_count = 0,
      # left term -> right term 的参数
      right_term_param = {
        "iterate_count": 100, # 探查 right term 的数目， <=0非法，
        "right_term_count": 10, # 最终取的 right term 数目，<=0非法，
        "benefit_formula": "", # string 算 benefit 的公式, 对应服务配置中 formula 下面的 object key
        "min_benefit": -1  # 最小 benefit 阈值，如果 <0 表示不限制
        "param_by_attr": { # 支持不同的 left term 配置不同的遍历和截取数，可以不配
          { "left_attr_name": "gender", # left term 的名字， 空字串非法
            "iterate_count": 200, # 单独配置的探查 right term 的数目， <=0 非法
            "right_term_count": 30, # 单独配置的最终取的 right term 数目 <=0 非法
          },
          { "left_attr_name": "click_item_list", # left term 的名字， 空字串非法
            "iterate_count": 50, # 单独配置的探查 right term 的数目， <=0 非法
            "right_term_count": 5, # 单独配置的最终取的 right term 数目 <=0 非法
          }
        }
      },
      # 所有 left term -> right term 后再对进行一次排序和截断的参数
      term_list_param = {
        "count": -1, # 最终只留多少个 right term，如果 <0 表示不限制
      },
      # right term -> item 的参数
      item_param = {
        # 倒排检索 item 的数目，<=0非法
        "iterate_count": 100,
        "item_count": 10, # 取的 item 数目，<=0非法
        "score_formula": ""  # string  算 score 的公式
      },
      # 所有 item 最终返回之前的参数
      final_param = {
        "count": 10, # 最终返回的结果数目，如果 <0 表示不限制
                     #（一般不这么用，除非 RetrieveItemParam.item_count 就够用了）
        "score_formula": ""  # string 算 score 的公式
      }
    )
    ```
    """
    self._add_processor(CommonRecoModelRetriever(kwargs))
    return self

  def retrieve_by_explore_rpc(self, **kwargs):
    """
    CommonRecoExploreRpcRetriever
    ------
    从主站的发现页 Leaf（实际经过发现页 RPC 服务中转）获取视频作为召回源

    参数配置
    ------
    `reason`: [int] 召回原因

    `kess_service`: [string] 发现页 RPC 服务的 kess 服务名，**注意：请先与相关服务 owner 确认应该调用的服务名（服务可能无法承受新增的 QPS）！！！**

    `service_group`: [string] 发现页 RPC 服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] 请求发现页 RPC 服务的超时时间，建议最少 500，默认值为 1000

    `count`: [int] [动态参数] 请求的 item 数目（注意：目前 RPC 服务端会限制最多返回 120 个结果，并且对小于 5 的请求会返回 0 个结果）

    `source`: [int] 请求发现页的类型，默认值为 0

    `browse_type`: [int] 主 APP 不同的 UI 版本。1：正常版，2：上下滑，3：滑滑设置版，0：其它。默认值为 0

    `locale`: [string] Embedding 服务的 kess 服务组，默认值为 "zh_CN"

    `user_id_from_attr`: [string] 若设置则从指定的 CommonAttr 中获取 user_id 作为 request 使用的 uid，可缺省

    `device_id_from_attr`: [string] 若设置则从指定的 CommonAttr 中获取 device_id 作为 request 使用的 did，可缺省

    `web_pass_info_from_attr`: [string] 若设置则从指定的 CommonAttr 中获取 web_pass_info 填充到 request 中，可缺省

    `imei_from_attr`: [string] 从指定的 CommonAttr 中获取 IMEI 数据（for Android device），CommonAttr 需为 string 或 string_list 类型，可缺省

    `idfa_from_attr`: [string] 从指定的 CommonAttr 中获取 IDFA 数据（for IOS device），CommonAttr 需为 string 类型，可缺省

    `retrieval_item_type`: [int] 召回 item 的 item_type，和 item_id 一起产生 item_key，默认值为 0

    调用示例
    ------
    ``` python
    .retrieve_by_explore_rpc(
      reason = 600,
      kess_service = "grpc_xxx",
      timeout_ms = 3000,
      browse_type = 1,
      count = 100,
      user_id_from_attr = "uId",
    )
    ```
    """
    self._add_processor(CommonRecoExploreRpcRetriever(kwargs))
    return self

  def retrieve_by_slide_leaf(self, **kwargs):
    """
    CommonRecoSlideLeafRetriever
    ------
    从主站的滑滑 Leaf 获取视频作为召回源

    参数配置
    ------
    `reason`: [int] 召回原因

    `kess_service`: [string] 滑滑 Leaf 服务的 kess 服务名

    `service_group`: [string] 滑滑 Leaf 服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] 请求滑滑 Leaf 服务的超时时间，建议最少 300，默认值为 1000

    `count`: [int] [动态参数] 请求的 item 数目

    `reason`: [bool] 召回原因，若小于等于 0 则延用 slide leaf 返回的 reason，默认值为 0

    `send_common_attr`: [bool] 是否发送 common leaf request 中携带的 common_attr 给滑滑 leaf 作为用户属性，默认为 True

    `user_info_attr`: [string] 指定从哪个 common_attr 中获取用户画像，支持 protobuf 对象或其序列化 string

    `product_app_id`: [int] 业务标识 ks.reco.ProductFullId.app_id，若小于等于 0 则默认使用 0 （UNKNOWN）

    调用示例
    ------
    ``` python
    .retrieve_by_slide_leaf(
      reason = 600,
      kess_service = "grpc_slideLeafRpc_slideLeafRpc",
      timeout_ms = 300,
      count = 1200,
      user_info_attr = "ksUserInfo",
      send_common_attr = False,
      product_app_id = 0,
    )
    ```
    """
    self._add_processor(CommonRecoSlideLeafRetriever(kwargs))
    return self

  def retrieve_by_colossus_server(self, **kwargs):
    """
    CommonRecoColossusRetriever
    ------
    访问 Colossus 服务，获取用户的长期行为列表

    参数配置
    ------
    `service_name`: [string] kess 服务名。

    `shard_number`: [int] kess 服务 shard 数。

    `reason`: [int] 召回原因，默认为 0。

    `key_type`: [string] 支持 uid_only，did_only 或 uid_or_did，默认为 uid_only。

    `save_photo_id_to_attr`: [string] 将返回的 photo_id 存到指定的 item attr，默认为空，不存储。

    `save_author_id_to_attr`: [string] 将返回的 author_id 存到指定的 item attr，默认为空，不存储。

    `save_duration_to_attr`: [string] 将返回的 duration 存到指定的 item attr，默认为空，不存储。

    `save_play_time_to_attr`,: [string] 将返回的 play_time 存到指定的 item attr，默认为空，不存储。

    `save_tag_to_attr`: [string] 将返回的 tag 存到指定的 item attr，默认为空，不存储。

    `save_label_to_attr`: [string] 将返回的 label 存到指定的 item attr，默认为空，不存储。

    `save_timestamp_to_attr`: [string] 将返回的 timestamp 存到指定的 item attr，默认为空，不存储。

    调用示例
    ------
    ``` python
    .retrieve_by_colossus_server(
      service_name = "grpcColossusTest",
      shard_number = 1,
      save_photo_id_to_attr = "pid")
    ```
    """
    self._add_processor(CommonRecoColossusRetriever(kwargs))
    return self

  def get_item_attr_by_local_index(self, **kwargs):
    """
    CommonRecoLocalIndexItemAttrEnricher
    ------
    提示: 若配置了 IndexSource.LOCAL 正排属性可被自动注入, 无需手动调用该接口!

    从本地索引（common_index）获取 ItemAttr 并写入 Context 供后续 Processor 使用

    参数配置
    ------
    `attrs`: [list] 属性名称列表

    `no_overwrite`: [bool] 是否不覆盖已存在的 item_attr, 默认 False

    `max_value_bytes`: [int] 正排属性值的大小限制，若超过限制则会丢弃该属性值，默认为 1024*1024 (1MB)

    `additional_item_source`: [dict] 选配项，从其他数据源里为额外的 item_key 获取 ItemAttr，可不配置。若 reco_results 配置为 false，忽略当前结果集里的 item
      - `reco_results`: [bool] 是否将当前结果集里的所有 item 作为 item_key 的来源之一，默认值为 true
      - `common_attr`: [list] 需要从哪些 common_attr 的值中获取 item_key，仅支持 int/int_list 类型的 common_attr
      - `item_attr`: [list] 需要从当前结果集的哪些 item_attr 的值中获取 item_key，仅支持 int/int_list 类型的 item_attr
      - `latest_browse_set_item`: [int] 需要从 BrowseSet 中获取 item 的数目（最近浏览的），值为 0 时全部抽取，值小于 0 时获取最远浏览的 item，不需要时需删除该项配置

    `item_key_attr`: [string] 选配项，若设置则使用该 ItemAttr 中的值作为查询正排的 item_key，而非 item 本身的 item_key 值.

    `item_miss_tag`: [string] 选配项，若不为空， 则设置该 ItemAttr 的值(int 类型),  1 表示索引中未命中， 0 表示索引中命中

    调用示例
    ------
    ``` python
    .get_item_attr_by_local_index(
      attrs = [
        "type_attr",
        "nick_name_attr",
        "head_image_attr",
        "photo_count_attr",
      ],
    )
    ```
    """
    if "additional_item_source" in kwargs:
      # NOTE(fangjianbing): 若配置了 additional_item_source 则强制忽略对当前结果集的处理
      kwargs["range_start"] = 1
      kwargs["range_end"] = 1
    self._add_processor(CommonRecoLocalIndexItemAttrEnricher(kwargs))
    return self

  def get_item_attr_by_local_attr_index(self, **kwargs):
    """
    CommonRecoLocalAttrIndexItemAttrEnricher
    ------
    从本地索引（attr_index）获取 ItemAttr 并写入 Context 供后续 Processor 使用

    配置请参考 get_item_attr_by_local_index
    """
    if "additional_item_source" in kwargs:
      # NOTE(fangjianbing): 若配置了 additional_item_source 则强制忽略对当前结果集的处理
      kwargs["range_start"] = 1
      kwargs["range_end"] = 1
    self._add_processor(CommonRecoLocalAttrIndexItemAttrEnricher(kwargs))
    return self

  def get_item_attr_by_remote_index(self, **kwargs):
    """
    CommonRecoRemoteIndexItemAttrEnricher
    ------
    提示: 若配置了 `IndexSource.REMOTE` 相关配置可被自动注入, 无需为该接口手动填写参数!

    从异地索引服务 krp_common_query_server 远程获取 ItemAttr 并写入 Context 供后续 Processor 使用

    CommonRecoLocalIndexItemAttrEnricher 的远程版本

    参数配置
    ------
    `kess_service`: [string] [动态参数] 远程索引服务的 kess 服务名

    `shard_num`: [int] 如果远程索引服务端是分片服务，通过该项指定分片数，默认为 1（未分片）

    `timeout_ms`: [int] 请求远程索引服务的超时时间，默认值为 300

    `attrs`: [list] 属性名称列表

    `no_overwrite`: [bool] 是否不覆盖已存在的 item_attr, 默认 False

    `consistent_hash`: [bool] 是否按 user_id 或 device_id 对请求进行一致性 hash 的分发，以保证同一用户的请求始终落在同一索引机器上，默认 True

    `max_value_bytes`: [int] 正排属性值的大小限制，若超过限制则会丢弃该属性值，默认为 1024*1024 (1MB)

    `additional_item_source`: [dict] 选配项，从其他数据源里为额外的 item_key 获取 ItemAttr，可不配置。若 reco_results 配置为 false，忽略当前结果集里的 item
      - `reco_results`: [bool] 是否将当前结果集里的所有 item 作为 item_key 的来源之一，默认值为 true
      - `common_attr`: [list] 需要从哪些 common_attr 的值中获取 item_key，仅支持 int/int_list 类型的 common_attr
      - `item_attr`: [list] 需要从当前结果集的哪些 item_attr 的值中获取 item_key，仅支持 int/int_list 类型的 item_attr
      - `latest_browse_set_item`: [int] 需要从 BrowseSet 中获取 item 的数目（最近浏览的），值为 0 时全部抽取，值小于 0 时获取最远浏览的 item，不需要时需删除该项配置

    `item_key_attr`: [string] 选配项，若设置则使用该 ItemAttr 中的值作为查询正排的 item_key，而非 item 本身的 item_key 值.

    调用示例
    ------
    ``` python
    .get_item_attr_by_remote_index(
      kess_service = "grpc_XXX",
      timeout_ms = 200,
      attrs = [
        "type_attr",
        "nick_name_attr",
        "head_image_attr",
        "photo_count_attr",
      ]
    )
    ```
    """
    self._add_processor(CommonRecoRemoteIndexItemAttrEnricher(kwargs))
    return self

  def get_item_attr_by_distributed_index(self, **kwargs):
    """
    CommonRecoDistributedIndexItemAttrEnricher
    ------
    **DEPRECATED!!!**

    **请改用 [get_item_attr_by_distributed_new_photo_info_index](#get_item_attr_by_distributed_new_photo_info_index)**

    从[分布式索引](https://docs.corp.kuaishou.com/d/home/<USER>

    注意：目前仅支持主站发现页短视频的 PhotoInfo

    参数配置
    ------
    `photo_store_kconf_key`: [string] 必填项，kconf_key， kconf 内容为本地缓存配置和被调服务信息。[kconf配置说明参考](https://docs.corp.kuaishou.com/d/home/<USER>
    
    `use_dynamic_photo_store`: [bool] 选配项，是否使用 dynamic_photo_store，默认 false。推荐设置成 true，性能更好，但是只能通过 photo_store_kconf_key 对应的 kconf 进行配置，不再使用本地的 flag。

    `attrs`: [list] 选配项，将 PhotoInfo 中的字段作为 ItemAttr 进行保存，改列表中的 value 支持两种格式的值：
      - string 格式：可直接把 proto 中第一层的字段填入同名 item_attr 中
      - dict 格式: 用于取嵌套的内层字段，包含 name 和 path 两个配置项，示例：{"name": "author_id", "path": "author.id"}，将 author 里的 id 字段填入名为 author_id 的 item_attr 中

    `item_id_attr`: [string] 选配项，若设置则使用该 ItemAttr 中的值作为发送请求的 item_id 参数，否则使用 item_key 解析出来的 item_id

    `save_item_info_to_attr`: [string] 选配项，可将分布式索引返回的 PhotoInfo 直接存到指定的 extra 类型 ItemAttr 中，默认不存

    `additional_item_source`: [dict] 选配项，从其他数据源里为额外的 item_key 获取 ItemAttr，可不配置
      - `common_attr`: [list] 需要从哪些 common_attr 的值中获取 item_key，仅支持 int/int_list 类型的 common_attr
      - `latest_browse_set_item`: [int] 需要从 BrowseSet 中获取 item 的数目（最近浏览的），值为 0 时全部抽取，值小于 0 时获取最远浏览的 item，不需要时需删除该项配置

    `no_overwrite`: [bool] 是否不覆盖已存在的 item_attr, 默认 False

    `photo_store_rpc_req_cache_rate`: [float] [动态参数] 选配项, 当 cache 命中率大于该值时不请求远端, 用于 cache 命中率比较高时减少对远端的 QPS, 默认值 100.0, 取值范围 [0.0, 100.0]

    `photo_store_request_data_set_tags_attr`: [string] 选配项, 默认是空字符串。该 common attr 表示进行索引实验时请求的数据集, attr 的取值样例 1,2,3, 使用时 attrs 列表需要包含 data_set_tags 字段 [参考链接](https://docs.corp.kuaishou.com/d/home/<USER>
    
    `data_set_tags_bit`: [list] [动态参数] 选配项，int 类型，指定索引实验的序号，仅透出实验中的 item，过滤其余 item。默认值为 [1]，仅出 base 组的索引。

    调用示例
    ------
    ``` python
    .get_item_attr_by_distributed_index(
      photo_store_kconf_key = "reco.distributedIndex.hotPhotoStoreConfig",
      use_dynamic_photo_store = true,
      attrs = [
        "photo_id",
        "caption",
        "subtitle",
        { "name": "author_id", "path": "author.id" }
        "upload_time",
        "show_count",
        "click_count",
        "like_count",
        "follow_count",
        "forward_count",
        "unlike_count",
        "comment_count",
      ]
    )
    ```
    """
    self._add_processor(CommonRecoDistributedIndexItemAttrEnricher(kwargs))
    return self

  def get_merchant_item_attr_by_distributed_index(self, **kwargs):
    """
    CommonRecoDistributedIndexMerchantItemAttrEnricher
    ------
    从[分布式索引](https://docs.corp.kuaishou.com/d/home/<USER>

    注意：目前仅支持主站发现页短视频的 MerchantPhotoInfo

    参数配置
    ------
    见 get_item_attr_by_distributed_index
    """
    self._add_processor(CommonRecoDistributedIndexMerchantItemAttrEnricher(kwargs))
    return self

  def get_merchant_living_item_attr_by_distributed_index(self, **kwargs):
    """
    CommonRecoDistributedIndexMerchantLivingItemAttrEnricher
    ------
    从[分布式索引](https://docs.corp.kuaishou.com/d/home/<USER>

    注意：目前仅支持主站发现页短视频的 MerchantLivingPhotoInfo

    参数配置
    ------
    见 get_item_attr_by_distributed_index
    """
    self._add_processor(CommonRecoDistributedIndexMerchantLivingItemAttrEnricher(kwargs))
    return self

  def get_item_attr_by_distributed_new_photo_info_index(self, **kwargs):
    """
    CommonRecoDistributedIndexNewPhotoInfoItemAttrEnricher
    ------
    从[分布式索引](https://docs.corp.kuaishou.com/d/home/<USER>
    和 CommonRecoDistributedIndexItemAttrEnricher 的差别是，实时队列支持删除操作

    注意：目前仅支持主站发现页短视频的 PhotoInfo，仅支持使用 dynamic_photo_store

    参数配置
    ------
    `photo_store_kconf_key`: [string] 必填项，kconf_key， kconf 内容为本地缓存配置和被调服务信息。[kconf配置说明参考](https://docs.corp.kuaishou.com/d/home/<USER>

    `use_dynamic_photo_store`: [bool] 选配项，是否使用 dynamic_photo_store，默认 false。推荐设置成 true，性能更好，但是只能通过 photo_store_kconf_key 对应的 kconf 进行配置，不再使用本地的 flag。

    `attrs`: [list] 选配项，将 PhotoInfo 中的字段作为 ItemAttr 进行保存，改列表中的 value 支持两种格式的值：
      - string 格式：可直接把 proto 中第一层的字段填入同名 item_attr 中
      - dict 格式: 用于取嵌套的内层字段，包含 name 和 path 两个配置项，示例：{"name": "author_id", "path": "author.id"}，将 author 里的 id 字段填入名为 author_id 的 item_attr 中

    `item_id_attr`: [string] 选配项，若设置则使用该 ItemAttr 中的值作为发送请求的 item_id 参数，否则使用 item_key 解析出来的 item_id

    `save_item_info_to_attr`: [string] 选配项，可将分布式索引返回的 PhotoInfo 直接存到指定的 extra 类型 ItemAttr 中，默认不存

    `additional_item_source`: [dict] 选配项，从其他数据源里为额外的 item_key 获取 ItemAttr，可不配置
      - `common_attr`: [list] 需要从哪些 common_attr 的值中获取 item_key，仅支持 int/int_list 类型的 common_attr
      - `latest_browse_set_item`: [int] 需要从 BrowseSet 中获取 item 的数目（最近浏览的），值为 0 时全部抽取，值小于 0 时获取最远浏览的 item，不需要时需删除该项配置

    `no_overwrite`: [bool] 是否不覆盖已存在的 item_attr, 默认 False

    `photo_store_rpc_req_cache_rate`: [float] [动态参数] 选配项, 当 cache 命中率大于该值时不请求远端, 用于 cache 命中率比较高时减少对远端的 QPS, 默认值 100.0, 取值范围 [0.0, 100.0]

    `photo_store_request_data_set_tags_attr`: [string] 选配项, 默认是空字符串。该 common attr 表示进行索引实验时请求的数据集, attr 的取值样例 1,2,3, 使用时 attrs 列表需要包含 data_set_tags 字段 [参考链接](https://docs.corp.kuaishou.com/d/home/<USER>
    
    `data_set_tags_bit`: [list] [动态参数] 选配项，int 类型，指定索引实验的序号，仅透出实验中的 item，过滤其余 item。默认值为 [1]，仅出 base 组的索引。

    调用示例
    ------
    ``` python
    .get_item_attr_by_distributed_new_photo_info_index(
      photo_store_kconf_key = "reco.distributedIndex.hotItemDocPhotoStoreConfig",
      use_dynamic_photo_store = true,
      attrs = [
        "photo_id",
        "caption",
        "subtitle",
        { "name": "author_id", "path": "author.id" }
        "upload_time",
        "show_count",
        "click_count",
        "like_count",
        "follow_count",
        "forward_count",
        "unlike_count",
        "comment_count",
      ]
    )
    ```
    """
    self._add_processor(CommonRecoDistributedIndexNewPhotoInfoItemAttrEnricher(kwargs))
    return self

  def get_item_attr_by_distributed_kuiba_predict_item_index(self, **kwargs):
    """
    CommonRecoDistributedIndexKuibaPredictItemAttrEnricher
    ------
    从[分布式索引](https://docs.corp.kuaishou.com/d/home/<USER>

    注意：目前仅支持 kuiba::PredictItem，仅支持使用 dynamic_photo_store

    参数配置
    ------
    `photo_store_kconf_key`: [string] 必填项，kconf_key， kconf 内容为本地缓存配置和被调服务信息。[kconf配置说明参考](https://docs.corp.kuaishou.com/d/home/<USER>

    `attrs`: [list] 选配项，将 kuiba::PredictItem 中的属性作为 ItemAttr 进行保存，改列表中的 value 支持一种格式的值：
      - string 格式：可直接把 proto 中同名的属性填入同名 item_attr 中

    `item_id_attr`: [string] 选配项，若设置则使用该 ItemAttr 中的值作为发送请求的 item_id 参数，否则使用 item_key 解析出来的 item_id

    `save_item_info_to_attr`: [string] 选配项，可将分布式索引返回的 kuiba::PredictItem 直接存到指定的 extra 类型 ItemAttr 中，默认不存

    `no_overwrite`: [bool] 是否不覆盖已存在的 item_attr, 默认 False

    `kuiba_predict_item_type`: [string] 选配项, kuiba predict 索引的类型, 默认值是 ITEM_TYPE_LIVESTREAM; 访问视频索引, 请用 ITEM_TYPE_PHOTO

    调用示例
    ------
    ``` python
    .get_item_attr_by_distributed_kuiba_predict_item_index(
      photo_store_kconf_key = "reco.distributedIndex.XXX",
      attrs = [
        "photo_id",
        "caption",
        "subtitle"
      ],
      save_item_info_to_attr = "extra"
    )
    ```
    """
    self._add_processor(CommonRecoDistributedIndexKuibaPredictItemAttrEnricher(kwargs))
    return self

  def get_item_attr_by_distributed_common_index(self, **kwargs):
    """
    CommonRecoDistributedIndexAttrKVItemAttrEnricher
    ------
    从[分布式索引](https://docs.corp.kuaishou.com/d/home/<USER>

    注意：仅支持中台兼容分布式索引

    参数配置
    ------
    `photo_store_kconf_key`: [string] 必填项，kconf_key， kconf 内容为本地缓存配置和被调服务信息。[kconf配置说明参考](https://docs.corp.kuaishou.com/d/home/<USER>

    `use_dynamic_photo_store`: [bool] 选配项，是否使用 dynamic_photo_store，默认 false。推荐设置成 true，性能更好，但是只能通过 photo_store_kconf_key 对应的 kconf 进行配置，不再使用本地的 flag。

    `attrs`: [list] 选配项，将 kuiba::PredictItem 中的属性作为 ItemAttr 进行保存，改列表中的 value 支持一种格式的值：
      - string 格式：可直接把 proto 中同名的属性填入同名 item_attr 中
      - json 格式：支持 { "name": "author_id", "type": "int", "class_name": "xxx", "as": "local_author_id"} 类型为 int, float, string, int_list, float_list, string_list 之一

    `attr_name_types`: [dict] 选配项，指定需要获取的正排属性名称 attr name 与类型 attr type 的键值对，类型为 int, float, string, int_list, float_list, string_list 之一

    `item_id_attr`: [string] 选配项，若设置则使用该 ItemAttr 中的值作为请求索引的 key，否则使用当前召回结果 CommonRecoResult 中 item_key 作为请求索引的 key。请求索引的 key 默认情况为 item_type 和 item_id 按如下 item_type << 56 | item_id 拼接后的值，如不清楚相应索引的 item_type 需找索引服务负责人确认；如果服务设置 --enable_item_type_and_keysign=false，此时 item_key 与 item_id 一致。

    `additional_item_source`: [dict] 选配项，从其他数据源里为额外的 item_key 获取 ItemAttr，可不配置
      - `common_attr`: [list] 需要从哪些 common_attr 的值中获取 item_key，仅支持 int/int_list 类型的 common_attr
      - `latest_browse_set_item`: [int] 需要从 BrowseSet 中获取 item 的数目（最近浏览的），值为 0 时全部抽取，值小于 0 时获取最远浏览的 item，不需要时需删除该项配置

    `no_overwrite`: [bool] 是否不覆盖已存在的 item_attr, 默认 False

    `photo_store_request_data_set_tags_attr`: [string] 选配项, 默认是空字符串。该 common attr 表示进行索引实验时请求的数据集, attr 的取值样例 1,2,3, 使用时 attrs 列表需要包含 data_set_tags 字段 [参考链接](https://docs.corp.kuaishou.com/d/home/<USER>
    
    `data_set_tags_bit`: [list] [动态参数] 选配项，int 类型，指定索引实验的序号，仅透出实验中的 item，过滤其余 item。默认值为 [1]，仅出 base 组的索引。

    `enable_kconf_key`: [string] 选配项，使用 kconf 配置的开关判断是否进行算子初始化；如果跳过算子初始化可以在不使用当前算子的场景下避免 photo store 的初始化与内存占用

    `enable_init_common_index`: [string] 选配项，默认是 enable_init_common_index_default, 该 common attr 标记是否启动算子初始化，取值为 enable_kconf_key 中对应的值

    注意： `attrs` 与 `attr_name_types` 只能选择其中一种方式，此外 `attrs` 方式较新，无需指定类型，与其它 enricher 的配置方式类似，需要分布式索引服务也支持此接口。

    调用示例
    ------
    ``` python
    # 新方式，推荐
    .get_item_attr_by_distributed_common_index(
      photo_store_kconf_key = "reco.distributedIndex.XXXXX",
      attrs = [
        "photo_id",
        "caption",
        # 可以强制指定类型，主要用于那些历史上同名 attr 存在多种类型的情况，此时索引服务返回的类型可能不准确
        { "name": "author_id", "type": "int" },
        "subtitle",
      ],
    )

    # 老方式
    .get_item_attr_by_distributed_common_index(
      photo_store_kconf_key = "reco.distributedIndex.XXXXX",
      attr_name_types = {
        "photo_id" : "int",
        "caption" : "float",
        "subtitle" : "string",
      }
    )
    ```
    """
    if not self.SWITCH_TO_FLAT_INDEX:
      self._add_processor(CommonRecoDistributedIndexAttrKVItemAttrEnricher(kwargs))
    else:
      kwargs["use_dynamic_photo_store"] = True
      self._add_processor(CommonRecoDistributedIndexFlatKvItemAttrEnricher(kwargs))
    return self

  def get_item_attr_by_distributed_flat_index(self, **kwargs):
    """
    CommonRecoDistributedIndexFlatKvItemAttrEnricher
    ------
    从[分布式索引](https://docs.corp.kuaishou.com/d/home/<USER>

    可以使用 FLAG 参数 --attr_perf_sample_rate=0.01 调整正排信息上报的采样率为 1/100。

    注意！！：1.获取的正排字段会被置为 read only
    注意！！：2.仅支持中台兼容分布式索引

    参数配置
    ------
    `photo_store_kconf_key`: [string] 必填项，kconf_key， kconf 内容为本地缓存配置和被调服务信息。[kconf配置说明参考](https://docs.corp.kuaishou.com/d/home/<USER>

    `attrs`: [list] 选配项，将 CommonIndexDoc 中的属性作为 ItemAttr 进行保存，该列表中的 value 支持一种格式的值：
      - string 格式：可直接把属性填入同名 item_attr 中
      - json 格式：支持 { "name": "author_id", "type": "int", "as": "local_author_id"} 类型为 int, float, string, int_list, float_list, string_list 之一

    `attr_name_types`: [dict] 选配项，指定需要获取的正排属性名称 attr name 与类型 attr type 的键值对，类型为 int, float, string, int_list, float_list, string_list 之一

    `item_id_attr`: [string] 选配项，若设置则使用该 ItemAttr 中的值作为请求索引的 key，否则使用当前召回结果 CommonRecoResult 中 item_key 作为请求索引的 key。请求索引的 key 默认情况为 item_type 和 item_id 按如下 item_type << 56 | item_id 拼接后的值，如不清楚相应索引的 item_type 需找索引服务负责人确认；如果服务设置 --enable_item_type_and_keysign=false，此时 item_key 与 item_id 一致。

    `additional_item_source`: [dict] 选配项，从其他数据源里为额外的 item_key 获取 ItemAttr，可不配置
      - `common_attr`: [list] 需要从哪些 common_attr 的值中获取 item_key，仅支持 int/int_list 类型的 common_attr
      - `latest_browse_set_item`: [int] 需要从 BrowseSet 中获取 item 的数目（最近浏览的），值为 0 时全部抽取，值小于 0 时获取最远浏览的 item，不需要时需删除该项配置

    `drop_item_attrs`: [list] 选配项，在配置中丢弃列表里的 item attrs。不会从索引服务查询这些 attrs，但是 attr 依赖关系会被保留。

    `packed_key`: [string] 选配项，是否将正排数据打包成一个序列化的 flat_kv string attr，需要在 kconf 中设置 flat_kv 的 schema 信息。

    `use_flat_kv`: [bool] 选配项，是否直接以 flat kv 格式读取数据，需要在 kconf 中设置 flat_kv 的 schema 信息，默认为 false。

    `photo_store_rpc_req_cache_rate`: [float] [动态参数] 选配项, 当 cache 命中率大于该值时不请求远端, 用于 cache 命中率比较高时减少对远端的 QPS, 默认值 100.0, 取值范围 [0.0, 100.0]

    `photo_store_request_data_set_tags_attr`: [string] 选配项, 默认是空字符串。该 common attr 表示进行索引实验时请求的数据集, attr 的取值样例 1,2,3, 使用时 attrs 列表需要包含 data_set_tags 字段 [参考链接](https://docs.corp.kuaishou.com/d/home/<USER>
    
    `data_set_tags_bit`: [list] [动态参数] 选配项，int 类型，指定索引实验的序号，仅透出实验中的 item，过滤其余 item。默认值为 [1]，仅出 base 组的索引。

    
    调用示例
    ------
    ``` python
    .get_item_attr_by_distributed_flat_index(
      photo_store_kconf_key = "reco.distributedIndex.XXXXX",
      attrs = [
        "photo_id",
        "caption",
        "subtitle",
      ],
    )
    ```
    """
    self._add_processor(CommonRecoDistributedIndexFlatKvItemAttrEnricher(kwargs))
    return self


  def gen_common_attr_by_packing_item_attr(self, **kwargs):
    """
    CommonRecoItemAttrPackEnricher
    ------
    与 `.pack_item_attr()` 接口功能一致，请参考 `.pack_item_attr()` 接口文档
    """
    self.pack_item_attr(**kwargs)
    return self

  def pack_item_attr(self, **kwargs):
    """
    CommonRecoItemAttrPackEnricher
    ------
    把指定的一组 item_keys 的 ItemAttr 根据指定的聚合方式打包成一个新的 attr 数据并填入指定的 CommonAttr 中；item_keys 的来源可以是 CommonAttr 或 BrowseSet。

    场景举例：为了过滤最近看过的 N 个 视频的作者，将这 N 个 item 的 author_id 属性抽取出来生成一个长度为 N 的 recent_browsed_author_id_list 并写入 CommonAttr，可供后续过滤使用

    参数配置
    ------
    `item_source`: [dict] item_key 的来源渠道, 以下均为选配项
      - `reco_results`: [bool] 是否将当前结果集里的所有 item 作为 item_key 的来源之一, 默认值为 False
      - `latest_browse_set_item`: [int] 将 BrowseSet 作为 item_key 的来源之一, 设置从中获取的数目（最近浏览的），值为 0 时全部抽取，值小于 0 时获取最远浏览的 item，不需要时可缺省该项
      - `common_attr`: [list] 将哪些 common_attr 作为 item_key 的来源之一，仅支持 int/int_list 类型的 common_attr
      - `single_limit`: [int] 对单个 common_attr 下的 item_key 最多选取多少个，缺省则不做限制
      - `total_limit`: [int] [动态参数] 对所有 item_source 选取的总 item 数目限制，缺省则不做限制

    `mappings`: [list] Attr 转换映射
      - `aggregator`: [string] 抽取的各个 ItemAttr 值后的 pack 方式，可选值：
        - "concat": 默认方式，把所有值拼接到 list 类型的 to_common_attr 中，支持全类型的 ItemAttr
        - "copy": 把第一个 item 的 ItemAttr 拷贝到 to_common_attr中，支持全类型的 ItemAttr
        - "avg": 把所有值的平均数放入 to_common_attr 中，仅支持 double / double_list 类型 ItemAttr (double_list 按向量 avg pooling 处理)
        - "sum": 把所有值的求和值放入 to_common_attr 中，仅支持 int / double / int_list / double_list 类型 ItemAttr (list 类型按向量 sum pooling 处理)
        - "min": 把所有值的最小值放入 to_common_attr 中，仅支持 int / double 类型 ItemAttr
        - "max": 把所有值的最大值放入 to_common_attr 中，仅支持 int / double 类型 ItemAttr
        - "dev": 把所有值的方差放入 to_common_attr 中，仅支持 int / double 类型 ItemAttr
      - `from_item_attr`: [string] 需要抽取的各个 ItemAttr 名称，支持全类型 attr，当 aggregator 为 'concat' 时缺省该项则将使用 item_key 用于 concat
      - `item_attr_limit`: [int] [动态参数] 对应抽取 ItemAttr 的数目限制，缺省则不做限制
      - `to_common_attr`: [string] 待写入的 CommonAttr 名称，被抽取的各个 ItemAttr 组装成对应类型的 ListAttr 写入该 CommonAttr 中
      - `default_val`: [int|double|string] 选配项, 若配置则将作为 concat/avg/sum/mix/max 单值 ItemAttr 时的缺省值参与 aggregate 操作, 如果配置的值类型与实际需要的类型不一致, 将会忽略配置并打印 WARNING 日志
      - `dedup_to_common_attr`: [bool] 对应 CommonAttr 的去重开关，缺省则不做去重
      - `reset_to_common_attr`: [bool] 执行 pack 操作前是否先清空对应的 CommonAttr，默认值为 True
      - `pack_if`: [string] 选配项，指定一个 item attr，只有当该 attr 为 int 类型且值大于 0 时才作处理，否则跳过，不填或为空时不做限制。

    调用示例
    ------
    ``` python
    .pack_item_attr(
      item_source = {
        "reco_results": False,
        "common_attr": ["negative_list"],
      },
      mappings = [{
        "from_item_attr": "mmu_cover_cluster_id_attr",
        "to_common_attr": "negative_mmu_cover_cluster_id_list",
      }]
    )
    ```
    """
    self._add_processor(CommonRecoItemAttrPackEnricher(kwargs))
    return self

  def pack_common_attr(self, **kwargs):
    """
    CommonRecoCommonAttrPackEnricher
    ------
    把指定的一组 CommonAttr 打包拼接成一组 list attr 填入指定的 CommonAttr 中

    参数配置
    ------
    `input_common_attrs`: [list] 要打包拼接的一组 CommonAttr 名称列表

    `output_common_attr`: [string] 将拼接后的结果填入的 CommonAttr 名称

    `limit_num`: [int][动态参数] 限制拼接结果的最大长度为，缺省为无限制

    `deduplicate`: [bool] 选配项，是否对拼接结果进行去重，只对 int/string 类型的值有效，默认为 False

    调用示例
    ------
    ``` python
    .pack_common_attr(
      input_common_attrs = ["uPhotoClickCardSellerIdList", "uPhotoClickCartSellerIdList"],
      output_common_attr = "uAuthorList",
      limit_num = 10,
    )
    ```
    """
    self._add_processor(CommonRecoCommonAttrPackEnricher(kwargs))
    return self

  def set_default_value(self, **kwargs):
    """
    DEPRECATED! 已更名为 set_attr_value()
    """
    return self.set_attr_value(**kwargs)

  def set_attr_value(self, **kwargs):
    """
    CommonRecoItemAttrDefaultValueEnricher
    ------
    给指定的 attr 赋予指定的值。

    **注意：若 attr 已存在值，不可新赋值为其他类型，否则会赋值失败。**

    Tips: 如果是想给 ItemAttr 设置默认值，可以使用 `set_attr_default_value()` 接口，性能更优！

    参数配置
    ------
    `no_overwrite`: [bool] 选配项，是否**不覆盖**已有的值，默认 False, 即会覆盖已存在的值。

    `common_attrs`: [list] 默认值配置
      - `name`: [string] 待设置的 CommonAttr 名称
      - `type`: [string] 待设置的 CommonAttr 值类型，可选值：int, double, string, int_list, double_list, string_list
      - `value`: [int|double|string|int_list|double_list|string_list] 待设置的 CommonAttr 值，需要和 type 保持类型一致

    `item_attrs`: [list] 默认值配置
      - `name`: [string] 待设置的 ItemAttr 名称
      - `type`: [string] 待设置的 ItemAttr 值类型，可选值：int, double, string, int_list, double_list, string_list
      - `value`: [int|double|string|int_list|double_list|string_list] 待设置的 ItemAttr 值，需要和 type 保持类型一致

    调用示例
    ------
    ``` python
    .set_attr_value(
      no_overwrite=True,
      item_attrs=[
        {
          "name": "type",
          "type": "int_list",
          "value": [3,4,5]
        }
      ]
    )
    ```
    """
    self._add_processor(CommonRecoItemAttrDefaultValueEnricher(kwargs))
    return self

  def aggregate_list_attr(self, **kwargs):
    """
    CommonRecoAggregateListAttrEnricher
    ------
    对 string / int_list / double_list / string_list 的 item_atrr 或者 common_attr 进行聚合操作，转换成同类型的单值 attr 
    主要是为了解决使用 lua 处理超长 list 类型数据时，导入属性性能开销过重的问题

    参数配置
    ------
    `for_common`: [bool] mappings 中描述的 from / to 属性名是否是 CommonAttr，默认是 false.

    `mappings`: [list] Attr 聚合映射
      - `from_attr`: [string] 需要抽取的各个 Attr 名称, 需要是 string / int_list / double_list / string_list 类型中一个
      - `to_attr`: [string] 待写入的 Attr 名称
      - `aggregator`: [string] 抽取的各个 ItemAttr 值后的 pack 方式，可选值：
        - "count": 支持 string / int_list / double_list / string_list 类型，会将 int 类型的 list 长度写入 to_attr
        - "min": 支持 int_list / double_list 类型，会将范围的最小值写入 to_attr，from_attr 不存在或长度为 0 时，不写入
        - "max": 支持 int_list / double_list 类型，会将范围的最大值写入 to_attr，from_attr 不存在或长度为 0 时，不写入
        - "sum": 支持 int_list / double_list 类型，会将范围的和写入 to_attr，from_attr 不存在或长度为 0 时，不写入
        - "avg": 支持 int_list / double_list 类型，会将范围的平均值写入 to_attr，from_attr 不存在或长度为 0 时，不写入(int_list 平均值也会是 int 类型)

    调用示例
    ------
    ``` python
    .aggregate_list_attr(
      mappings = [{
        "from_attr": "photo_embedding",
        "to_attr": "photo_embedding_size",
        "aggregator": "count"
      }, {
        "from_attr": "photo_embedding",
        "to_attr": "max_emb",
        "aggregator": "max"
      }]
    )
    ```
    """
    self._add_processor(CommonRecoAggregateListAttrEnricher(kwargs))
    return self

  def append_value(self, **kwargs):
    '''
    CommonRecoAppendValueEnricher
    ------
    将 [int|double|string|int_list|double_list|string_list] 类型的 Common/Item Attr追加写入 [int_list|double_list|string_list] 类型的 Common/Item Attr 中。

    注意：from/to 必须且只能配置一个, from 和 to 对应的类型必须保持一致。

    Tips：
    - from_common + append_to_common 相当于把 common_attr 中结果追加写入 common_attr 中。
    - from_common + append_to_item 相当于把 common_attr 中结果分别追加写入各个Item的结果中。
    - from_item + append_to_common 相当于把各个 item_attr 中结果全部追加写入 common_attr 中。
    - from_item + append_to_item 相当于把每个 item_attr 结果写入每个对应的 item_attr 中。 

    参数配置
    ------
    `attrs`: [list]
      - `from_common`: [string] 需要被拷贝的 common_attr 名称
      - `from_item`: [string] 需要被拷贝的 item_attr 名称
      - `append_to_common`: [string] 需要将拷贝值追加写入的 common_attr 名称
      - `append_to_item`: [string] 需要将拷贝值追加写入的 item_attr 名称

    调用示例
    ------
    ``` python
    .append_value(
      attrs=[{
        "from_common": "common_attr_name",
        "append_to_common": "append_common_attr_name"
      }]
    )
    ```
    '''
    self._add_processor(CommonRecoAppendValueEnricher(kwargs))
    return self

  def common_predict(self, **kwargs):
    """
    CommonRecoCommonPredictItemAttrEnricher
    ------
    从 CommonPredict server 获取预估值并作为 ItemAttr 写入 Context

    参数配置
    ------
    `kess_service`: [string] [动态参数] 预估服务的 kess 服务名

    `service_group`: [string] 预估服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] 请求预估服务的超时时间，默认值为 300

    `loss_function_name`: [list] 预估的 loss 数组，每项的值类型为 string

    `loss_default_value`: [double] 预估默认值

    `output_prefix`: [string] 写入 ItemAttr 的 attr_name 前缀，默认值为 ""

    `target_item_type`: [int] 只针对该 item_type 的 item 发送预估请求，缺省则对所有 item 进行发送(说明：该配置名原为 "item_type"，已做兼容处理，i.e. 写为 "item_type" 同样生效)

    `exclude_common_attrs`: [list] 默认情况会把 request 中带来的 CommonAttr 全部作为 User 侧特征进行发送，可通过该项配置指定哪些 Attr 不进行发送，array 中的每项值为属性名，可缺省

    `extra_common_attrs`: [list] 默认情况会把 request 中带来的 CommonAttr 全部作为 User 侧特征进行发送，可通过该项配置指定哪些中间生成的 CommonAttr 也进行发送，可缺省

    `attr_name_transform_map`: [dict] 某些情况下模型训练中设置的 user 侧特征名称和 leaf 里的 CommonAttr 名称不一样，可通过该项配置映射转换关系
      - common_attr_name_in_leaf: 映射关系的 key 值为 leaf 里的 CommonAttr 名称，value 值为模型中使用的 user 侧特征名称

    `include_sample_list_user_info`: [bool] 是否从 sample_list 服务获取 user info 并作为 common attr 发送，默认值 false

    `use_sample_list_attr_flatten`: [bool] 是否**不使用**压缩格式发送 sample_list 服务获取的 common attrs，默认为 false

    `filter_unlogin_user_flag`: [bool] 是对未登录用户取消预估请求，默认值为 false

    `item_attrs`: [list] 当 Predict Server 工作在无索引模式时，需要在 leaf 侧把所有的 item attr 也通过 request 携带过去，通过该设置指定发送哪些 item 属性给 Predict Server。若 Predict Server 本地包含索引则可不配置该项以减少请求发送数据量！持支持对 attr 重命名发送

    `item_attrs_in_name_list`: [string] 选填项，从指定的 string_list common attr 中获取需要发送的 item attr 的 name list

    `item_key_attr`: [string] 选配项，若设置则使用该 ItemAttr 中的值作为发送请求的 item_key 列表，而非 item 本身的 item_key 值，可以通过该项配置选用 item_id 发送

    调用示例
    ------
    ``` python
    .common_predict(
      kess_service = "grpc_xxxPredictServer",
      timeout_ms = 300,
      loss_function_name = ["ctr"],
      loss_default_value = -1.0,
      # 为了区别不同输出需求，定义的output前缀
      output_prefix = "p_",
      # 名称转换格式为 from: to， 例如 "play": "view_list"
      attr_name_transform_map = {
        "user_id": "uId",
        "device_id": "dId",
      }
    )
    ```
    """
    self._add_processor(CommonRecoCommonPredictItemAttrEnricher(kwargs))
    return self

  def get_item_attr_by_predict_fetcher(self, **kwargs):
    """
    CommonRecoPredictFetcherItemAttrEnricher
    ------
    通过 PredictKessFetcher 接口访问双塔模型的预估服务，并获取如下 pxtr 作为 item_attr:
      - pctr
      - pltr
      - pwtr
      - plvtr
      - psvtr
      - pftr
      - pshowtr
      - plvtr2
      - ptr
      - pwatch_time
      - pepstr
      - pcmtr
      - pecstr
      - plivingtr
      - pcestr
      - plttr
      - pwttr
      - pdtr
      - pelivingtr
      - pcotr
      - pfostr
      - pwtd
      - pclk_cmt
      - pswptr
      - pswptr_after

    参数配置
    ------
    `kess_service`: [string] [动态参数] 预估服务的 kess 服务名

    `service_group`: [string] 预估服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] 请求预估服务的超时时间，默认值为 80

    `user_info_attr`: [string] 从指定的 extra 类型 common_attr 中获取 `ks::reco::UserInfo` 对象，或是 string 类型 common_attr 中获取 `ks::reco::UserInfo` 对象的序列化字符串进行发序列化使用

    `pxtr`: [list] 可缺省，需要保存 item_attr 的 pxtr 列表，若缺省则对所有 pxtr 都进行保存，可选的 pxtr 名见上方的方法说明

    `output_prefix`: [string] 写入 ItemAttr 的 attr_name 前缀，默认值为 ""

    `item_id_attr`: [string] 可缺省，若设置则使用该 ItemAttr 中的值作为发送请求的 item_id 参数，否则使用 item_key 解析出来的 item_id

    调用示例
    ------
    ``` python
    .get_item_attr_by_predict_fetcher(
      kess_service = "grpc_XXX",
      timeout_ms = 80,
      user_info_attr = "user_info",
      pxtr = ["pctr", "pltr", "pwtr"],
      # 为了区别不同输出需求，定义的output前缀
      output_prefix = "tower_",
    )
    ```
    """
    self._add_processor(CommonRecoPredictFetcherItemAttrEnricher(kwargs))
    return self

  def get_item_attr_by_predict_fetcher_v2(self, **kwargs):
    """
    CommonRecoPredictFetcherV2ItemAttrEnricher
    ------
    通过 CommonRecoPredictClient 接口访问新架构双塔模型的预估服务，并获取如下 pxtr 作为 item_attr:
      - pctr
      - pltr
      - pwtr
      - plvtr
      - psvtr
      - pftr
      - pshowtr
      - plvtr2
      - ptr
      - pwatch_time
      - pepstr
      - pcmtr
      - pecstr
      - plivingtr
      - pcestr
      - plttr
      - pwttr
      - pdtr
      - pelivingtr
      - pcotr
      - pfostr
      - pwtd
      - pclk_cmt
      - pswptr
      - pswptr_after

    参数配置
    ------
    `kess_service`: [string] [动态参数] 预估服务的 kess 服务名

    `service_group`: [string] 预估服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] 请求预估服务的超时时间，默认值为 80

    `user_info_attr`: [string] 从指定的 string 类型 common_attr 中获取 `ks::reco::UserInfo` 的序列化字符串

    `try_parse_user_info`: [bool] 是否尝试从 user_info_attr 反序列化 UserInfo, 默认为 True, 建议设为 False

    `pxtr`: [list] 选配项，需要保存 item_attr 的 pxtr 列表，若缺省则对所有 pxtr 都进行保存，可选的 pxtr 名见上方的方法说明

    `output_prefix`: [string] 选配项，写入 ItemAttr 的 attr_name 前缀，默认值为 ""

    `item_id_attr`: [string] 选配项，若设置则使用该 ItemAttr 中的值作为发送请求的 item_id 参数，否则使用 item_key 解析出来的 item_id

    `tower_request_type`: [string] [动态参数] tower service request type

    `return_pxtr_label`: [string] 选配项，tower server return pxtr label field in common attr

    `return_pxtr_value`: [string] 选配项，tower server return pxtr value field in common attr

    调用示例
    ------
    ``` python
    .get_item_attr_by_predict_fetcher_v2(
      kess_service = "grpc_XXX",
      timeout_ms = 80,
      user_info_attr = "user_info_str",
      try_parse_user_info = False,
      pxtr = ["pctr", "pltr", "pwtr"],
      # 为了区别不同输出需求，定义的output前缀
      output_prefix = "tower_",
      tower_request_type="predict_by_mc_tower",
    )
    ```
    """
    self._add_processor(CommonRecoPredictFetcherV2ItemAttrEnricher(kwargs))
    return self

  def gen_random_item_attr(self, **kwargs):
    """
    CommonRecoRandomItemAttrEnricher

    给 item 设置一个随机的 IntAttr 或 DoubleAttr。int 随机值范围 [0, 100], double 随机值范围 [0, 1)

    参数配置
    ------
    `attr_name`: [string] 待设置的 ItemAttr 名称

    `attr_type`: [string] 待设置的 ItemAttr 值类型，可选值：int, float/double

    调用示例
    ------
    ``` python
    .gen_random_item_attr(
      attr_name = "random_int_attr",
      attr_type = "int"
    )
    ```
    """
    self._add_processor(CommonRecoRandomItemAttrEnricher(kwargs))
    return self

  def get_abtest_params(self, **kwargs):
    """
    CommonRecoAbtestCommonAttrEnricher
    ------
    从 abtest 系统中获得参数值并作为 CommonAttr 填入 Context 中（千分世界和百分世界均可）

    参数配置
    ------
    `biz_name`: [string] [动态参数] ab 参数所属业务，即 abtest 网站上全大写字母的“所属业务”（ab1.0）或“关联 App 组”（ab2.0）名称，若缺省将使用 gflag --abtest_biz_name 的值。

    `ab_params`: [list]
      - `param_name`: [string|dict] ab 参数的名称
      - `default_value`: [int|double|string|bool] ab 参数获取失败时取用的默认值
      - `param_type`: [string] 可缺省，ab 参数的类型，缺省将由 `default_value` 类型自动推断，可选值："int", "double", "string", "bool"（bool 将被转换为 0 / 1 int 值存储到 int CommonAttr 中）
      - `attr_name`: [string] 可缺省，要写入的 CommonAttr 名称，若缺省或为空将直接取 `param_name` 的值
      - `report_ab_hit`: [bool] [动态参数] 可缺省，是否将 ab 命中结果做上报，默认为 False 不上报
      - `save_exp_id_to`: [striung] 可缺省，获取参数取值的同时拿到实验命中（包含 combo 实验命中），获取的实验 exp 信息写入的 CommonAttr 的名称，仅 common level 生效
      - `save_group_id_to`: [string] 可缺省，获取参数取值的同时拿到实验命中（包含 combo 实验命中），获取的实验 group 信息写入的 CommonAttr 的名称，仅 common level 生效

    `param_prefix`: [string] [动态参数] 可缺省，为所有 `param_name` 添加一个前缀，例如 "slide_" 会取 `"slide_" + param_name` 的参数值

    `prioritized_suffix`: [list] [动态参数] 可缺省，为所有 `param_name` 定义一组优先尝试的参数名规则。
      - 若 `param_name` 配置为 string 类型将使用后缀拼接模式生成参数名，例如 ["_A", "_B"] 将优先使用 `param_name + "_A"` 参数值，次优使用 `param_name + "_B"` 参数值，最后兜底使用 `param_name` 参数值
      - 若 `param_name` 配置为 dict<str, str> 类型将使用 map 映射查询模式生成参数名，例如 ["C", "D"] 将优先使用 `param_name["C"]` 的参数名取值，次优使用 `param_name["D"]` 的参数名取值，若查询不到将直接取默认值

    `user_id`: [int] [动态参数] 选配项，指定获取 ab 参数时使用的 user_id ，缺省时，使用当前请求的 user_id

    `device_id`: [string] [动态参数] 选配项，指定获取 ab 参数时使用的 device_id ，缺省时，使用当前请求的 device_id

    `session_id`: [string] [动态参数] 选配项，ab 平台用于分流的 sid 值，原始含义是 session id，ab 平台的后续规划是用于自定义扩展分流 id，默认为 ""

    `product`: [string] [动态参数] 选配项，对于配置了 product 筛选的 ab 受众人群实验，指定获取 ab 参数时使用的 product ，例如 "KUAISHOU" 或 "ACFUN"

    `platform`: [string] [动态参数] 选配项，对于配置了 platform 筛选的 ab 受众人群实验，指定获取 ab 参数时使用的 platform ，例如 "ANDROID_PHONE" 或 "IPHONE"

    `app_version`: [string] [动态参数] 选配项，对于配置了 app_version 筛选的 ab 受众人群实验，指定获取 ab 参数时使用的 app_version

    `explore_locale`: [string] [动态参数] 选配项，对于配置了 explore_locale 筛选的 ab 受众人群实验，指定获取 ab 参数时使用的 explore_locale

    `country`: [string] [动态参数] 选配项，对于配置了 country 筛选的 ab 受众人群实验，指定获取 ab 参数时使用的 country

    `province`: [string] [动态参数] 选配项，对于配置了 province 筛选的 ab 受众人群实验，指定获取 ab 参数时使用的 province

    `city`: [string] [动态参数] 选配项，对于配置了 city 筛选的 ab 受众人群实验，指定获取 ab 参数时使用的 city

    `photo_page`: [string] [动态参数] 选配项，对于配置了 photo_page 筛选的 ab 受众人群实验，指定获取 ab 参数时使用的 photo_page

    `browse_type`: [string] [动态参数] 选配项，对于配置了 browse_type 筛选的 ab 受众人群实验，指定获取 ab 参数时使用的 browse_type

    `network_type`: [string] [动态参数] 选配项，对于配置了 network_type 筛选的 ab 受众人群实验，指定获取 ab 参数时使用的 network_type

    `phone_model`: [string] [动态参数] 选配项，对于配置了 phone_model 筛选的 ab 受众人群实验，指定获取 ab 参数时使用的 phone_model

    `language`: [string] [动态参数] 选配项，对于配置了 language 筛选的 ab 受众人群实验，指定获取 ab 参数时使用的 language

    `isp`: [string] [动态参数] 选配项，对于配置了 isp 筛选的 ab 受众人群实验，指定获取 ab 参数时使用的 isp
    
    `for_item_level`: [bool] [动态参数] 选配项，是否切换到 item 侧的 ab 参数获取，遍历每个 item 获取 ab 参数。默认值 false，仅获取 user 侧 ab 参数

    `item_level_user_id_attr`: [string] 选配项，指定从哪个 item attr 下获取 item 侧 ab 参数所使用的 user_id ，缺省时将使用 0 作为 user_id 默认值

    `item_level_device_id_attr`: [string] 选配项，指定从哪个 item attr 下获取 item 侧 ab 参数所使用的 device_id ，缺省时将使用 "" 作为 device_id 默认值

    `item_level_session_id_attr`: [string] 选配项，指定从哪个 item attr 下获取 item 侧 ab 参数所使用的 session_id ，缺省时将使用 "" 作为 session_id 默认值

    `deduplicate`: [bool] 选配项, 为 True 时会对 ab 参数去重(保留第一个)，缺省时默认为 False

    `parallel_get`: [int] 选配项, 大于1时将使用 parallel_get 线程数来多线程获取 common level ab 参数，建议配合 deduplicate 设置为 True 来使用， 缺省时默认为 1 即不开启多线程

    调用示例
    ------
    ``` python
    # 完整格式
    .get_abtest_params(
      biz_name = "YOUR_ABTEST_BIZ_NAME",
      ab_params = [{
        "param_name": "mmu_cover_erotic_prob_threshold",
        "param_type": "double",
        "default_value": 0.0
      }]
    )
    # 简写格式
    .get_abtest_params(
      biz_name = "YOUR_ABTEST_BIZ_NAME",
      # 简写格式下每个 ab_param 为二元组 (param_name, default_value) 或三元组 (param_name, default_value, attr_name)
      ab_params = [
        ("mmu_cover_erotic_prob_threshold", 0.0),
        ("use_new_predict", False),
      ]
    )
    ```
    """
    keys = ("param_name", "default_value", "attr_name", "param_type")
    func = lambda x: dict(zip(keys, x)) if isinstance(x, tuple) else x
    kwargs["ab_params"] = list(map(func, kwargs["ab_params"]))
    self._add_processor(CommonRecoAbtestCommonAttrEnricher(kwargs))
    return self

  def dispatch_common_attr(self, **kwargs):
    """
    CommonRecoCommonAttrDispatchEnricher
    ------
    把给定的 common_attr 中的值依次分发给各个 item 作为 item_attr 持有

    参数配置
    ------
    方式一：使用 `from_common_attr` 和 `to_item_attr` 配置一个分发策略

    `from_common_attr`: [string] 需要分发的 common_attr 名称，
      - 若 common_attr 为 int/double/string 单值类型，则对所有 item 都复制分发一份；
      - 若 common_attr 为 int_list/double_list/string_list 类型，则把 list 中的各个单值按位置顺序依次分发给各个 item（item 将持有一个单值类型的 item_attr），直至 list 中的值分发完毕或接收分发的 item 数目不足。

    `to_item_attr`: [string] 将分发的值写入到指定的 item_attr

    `by_list_size`: [int] 选配项，若设置大于 0，则不再分发单值而是分发定长 list，list 的大小由该配置项决定，默认值 0

    方式二：使用 dispatch_config 配置多个分发策略

    `dispatch_config`: [list] 方便配置多个分发策略，格式为一个 list ，每项包含
      - `from_common_attr`: [string] 含义同方式一中的 `from_common_attr`
      - `to_item_attr`: [string] 含义同方式一中的 `to_item_attr`
      - `by_list_size`: [int] 含义同方式一中的 `by_list_size`

    注意：不支持同时使用两种方式

    调用示例
    ------
    ``` python
    # 方式一
    .dispatch_common_attr(
      from_common_attr = "score_list",
      to_item_attr = "score",
    )

    # 方式二
    .dispatch_common_attr(
      dispatch_config = [
        {
          "from_common_attr" : "score_list",
          "to_item_attr" : "score"
        },{
          "from_common_attr" : "score_list2",
          "to_item_attr" : "score2"
        }
      ]
    )
    ```
    """
    self._add_processor(CommonRecoCommonAttrDispatchEnricher(kwargs))
    return self


  def dispatch_item_attrs(self, **kwargs):
    """
    CommonRecoItemAttrDispatchEnricher
    ------
    把部分 item 的 attr 按指定的新名称拷贝分发至其它 item 下

    参数配置
    ------
    `attrs`: [list] 需要分发的 item attr 名称列表

    `output_prefix`: [string] 分发拷贝之后的 item attr 名字的前缀, 如 top_, 新生成的 attr name 将是 top_0_pid, top_1_pid...

    方式一，按 item 范围分发，支持以下配置：

    `from_item_range`: [dict] 原始 item attr 范围
      - `start`: [int] 范围的起始位置（包含），可缺省（默认为 0 ），不可以是负数
      - `end`: [int] 范围的终止位置（不包含），不可以是负数

    `to_item_range`: [dict] 目标 item attr 范围，注意当 dispatch_to_common_attr 为 true 时该选项应该不设置
      - `start`: [int] 范围的起始位置（包含），不可以是负数
      - `end`: [int] 范围的终止位置（不包含），可缺省（默认到 result 数组结尾），不可以是负数

    `dispatch_to_common_attr`: [bool] 为 true 则将结果分发至 common attr，否则分发至 item attr，默认为 false

    方式二，按前 N 个 item 分发，支持以下配置：

    `prev_n`: [int] 分发前 N 个 item 的 attr 到当前 item 中

    `include_self`: [bool] 是否将当前 item 自己也加入分发的 item 列表中, 默认 true

    调用示例
    ------
    ``` python
    .dispatch_item_attrs(
      from_item_range = {
        "start": 0,
        "end": 3,
      },
      to_item_range = {
        "start": 3
      },
      attrs = ["pid", "aid"],
      output_prefix = "top_"
    )
    ```
    """
    self._add_processor(CommonRecoItemAttrDispatchEnricher(kwargs))
    return self

  def auto_adjust(self, **kwargs):
    """
    CommonRecoAutoAdjustEnricher
    ------
    对输入值，进行 自动调节，计算 adjust_output 值，使输入值达到 set_point 值。
    当处于稳态是 adjust 值为 0。如对用 adjust 值进行乘法运算，可以 + 1.0, 也可以取指数。
    pid计算公式如下：
    ```
    measures.size() == windows_num;
    output = -kp * (measures[n - 1] - set_point) - ki * sum(measures[i] - set_point) - kd * (measures[n - 1] - measures[n - 2]);
    ```
    不建议将 windows_num 设置特别大，由于积分项是后置反馈调节，导致到达稳态时间变长。

    参数配置
    ------
    `adjust_output`: [string] 调节的输出值，存到 common attr 中。

    `history_input_save_mod`: [string] 输入值存储模式，目前支持两种模式 1.local 本地模式 2.customize 自定义模式。

    `fractions_attr`: [string] 输入为 customize 模式下，存储 fractions 的 common attr name。（注意 fractions 为 double list 格式）

    `adjust_function`: [string] 调节的方式目前仅支持 pid 调节

    `set_point`: [double][动态参数] 输入为 local 模式下，调节的目标值。

    `group_name`: [string] [动态参数] 可缺省。将流量按照 group_name 进行分组，每个分组内进行 pid 控制，保证每个分组内的控制结果都达到 set_point。

    `kp`: [double] [动态参数] 可缺省。 pid 调节的比例项。

    `ki`: [double] [动态参数] 可缺省。 pid 调节的积分项。

    `kd`: [double] [动态参数] 可缺省。 pid 调节的微分项。

    `window_size`: [int] 输入为 local 模式下，窗口大小

    `windows_num`: [int] 输入为 local 模式下，窗口个数

    `numerator`: [string] 输入为 local 模式下，记录的分子, 来自 common attr

    `denominator`: [string] 可缺省，输入为 local 模式下，记录的分母, 来自 common attr, 默认为 1

    调用示例
    ------
    ``` python
    .auto_adjust(
      adjust_function="pid",
      history_input_save_mod="local",
      adjust_output="adjust_output",
      set_point=0.001,
      window_size=10,
      windows_num=10,
      numerator="item_show_count",
      denominator="total_result_count"
    )
    ```
    """
    self._add_processor(CommonRecoAutoAdjustEnricher(kwargs))
    return self

  def item_show_count(self, **kwargs):
    """
    CommonRecoItemShowCountEnricher
    ------
    计算 在 result 中 某些 item 的个数。通过 item attr 进行判断。目前仅支持 int item attr.

    参数配置
    ------
    `show_item_attr`: [string] 用来区分 item 的 判断条件。

    `common_attr`: [string] 计数结果 存到 int common attr 中。

    调用示例
    ------
    ``` python
    .item_show_count(show_item_attr="is_special_photo",
                     common_attr="agg_count")
    ```
    """
    self._add_processor(CommonRecoItemShowCountEnricher(kwargs))
    return self

  def get_kconf_params(self, **kwargs):
    """
    CommonRecoKconfCommonAttrEnricher
    ------
    从 kconf 系统中获取配置值并作为 CommonAttr 或 ItemAttr 填入 Context 中

    参数配置
    ------
    `kconf_configs`: [list]
      - `kconf_key`: [string] [动态参数] kconf 中的 key
      - `value_type`: [string] kconf 的值类型（若配置了单值的 `default_value` 或 `json_path` 可省略该项利用类型自动推断），可选值：int64(兼容int32)/double/string/bool/list_int64(兼容list_int32)/list_double/list_string/set_int64(兼容set_int32)/set_double/set_string/json
      - `default_value`: [int|double|string|bool|list] kconf 配置获取失败时取用的默认值, 该值的类型需与 kconf_key 实际配置的值类型一致！
      - `json_path`: [string] [动态参数] 若 kconf_key 对应的值为 json 类型，可通过该项配置获取指定 json_path 下的值（仅支持 int/double/string/int_list/double_list/string_list 6 种类型的值），json_path 使用句点 '.' 分隔："field1.filed2.filed3"。注意：配合 export_item_attr 使用时**不再是动态参数**，但借用动态参数 "{{}}" 的格式来指定某个 item_attr 做展开处理。
      - `export_common_attr`: [string] 将 kconf 值写入指定的 CommonAttr
      - `export_item_attr`: [string] 将 kconf 值写入指定的 ItemAttr（根据不同 ItemAttr 下指定的 json_path 值），要写入的 ItemAttr 名称，注意：不可与 `export_common_attr` 同时配置，且必须设置动态类型的 `json_path`!

    调用示例
    ------
    ``` python
    .get_kconf_params(
      kconf_configs = [{
        "kconf_key": "webserver.api.tecentApi",
        "export_common_attr": "tecentApi",
        "default_value": "http://www.qq.com/"
      }]
    )
    ```
    """
    self._add_processor(CommonRecoKconfCommonAttrEnricher(kwargs))
    return self

  def lookup_kconf(self, **kwargs):
    """
    CommonRecoKconfLookupEnricher
    ------
    用 kconf 上的 set/map/tailnumber 类型配置检查指定的 common_attr 或 item_attr

    参数配置
    ------
    `kconf_configs`: [list]
      - `kconf_key`: [string] [动态参数] 必配项，kconf 配置的 key
      - `value_type`: [string] 必配项，kconf 的值类型，可选值：set_int64/set_string/map_string_bool/map_string_int64/map_string_double/map_string_string/tail_number
      - `lookup_attr`: [string] 必配项，指定被检查的 attr 名称，`is_common_attr=False` 时仅支持 int 或 string 类型 attr; `is_common_attr=True` 时支持 int/int_list/string/string_list 类型 attr。
      - `output_attr`: [string] 必配项，指定将检查结果输出到哪个 attr，对于 set/tail_number 类型 kconf 数据将输出 0/1 int 值（0 表示未命中，1 表示命中），对于 map 类型 kconf 数据将输出在 map 中查找到的 value 实际值（lookup_attr 不是 list 类型且未查找到则不进行赋值，保持缺省状态; lookup_attr 是 list 类型且未查找到赋值为map的value类型的默认值，int 类型默认值为0，string 类型默认值为空字符串，double 类型默认值为0.0，bool 类型默认值为 false）。
      - `is_common_attr`: [bool] 选配项，`lookup_attr` 和 `output_attr` 是否均为 common attr，默认为 True，否则按 item attr 处理。

    调用示例
    ------
    ``` python
    .lookup_kconf(
      kconf_configs = [{
        "kconf_key": "xxx.yyy.bad_authors",
        "value_type": "set_int64",
        "lookup_attr": "aid",
        "output_attr": "is_bad_author",
        "is_common_attr": False,
      }]
    )
    ```
    """
    self._add_processor(CommonRecoKconfLookupEnricher(kwargs))
    return self

  def tf_serving_predict(self, **kwargs):
    """
    CommonRecoTfServingPredictEnricher
    ------
    从 krp_kuiba_tf_serving 服务获取 item 的 ctr 等预估分数，并写入 ItemAttr 支持两种请求构造方式：
    1. 默认情况会把 request 中带来的 CommonAttr 全部作为 User 侧特征进行发送；
    2. 使用 Common Attr 中已有的 ks::reco::feature_pipe::SequenceExample PB 构造 TF PredictRequest 进行发送

    参数配置
    ------

    `kess_service`: [string] [动态参数] 预估服务的 kess 服务名

    `service_group`: [string] 预估服务的 kess 服务组，默认值为 "PRODUCTION"

    `kess_division`: [string] 预估服务的 division, 默认值是 ""

    `kess_shard`: [string] kess 服务的 shard，默认值是 s0

    `timeout_ms`: [int] 请求预估服务的超时时间，默认值为 300

    `loss_function_name`: [list] 预估的 loss 数组，每项的值类型为 string

    `loss_default_value`: [array|double] 预估默认值，如果是 array，长度需要和 loss_function_name 相同

    `output_prefix`: [string] 写入 ItemAttr 的 attr_name 前缀，默认值为 ""

    `extra_common_attrs`: [list] 选配项，默认情况会把 request 中带来的 CommonAttr 全部作为 User 侧特征进行发送，可通过该项配置指定哪些中间生成的 CommonAttr 也进行发送

    `exclude_common_attrs`: [list] 默认情况会把 request 中带来的 CommonAttr 全部作为 User 侧特征进行发送，可通过该项配置指定哪些 Attr 不进行发送，array 中的每项值为属性名，可缺省

    `send_item_attrs`: [list] 选配项，指定发送哪些 ItemAttr 给 TF Servering Predict，仅在请求方式 1 中有效

    `tf_sequence_example_attr`: [string] common attr 中 ks::reco::feature_pipe::SequenceExample 指针的 attr name，默认为空，表示从其他 attr 中构造请求

    调用示例
    ------
    ``` python
    .tf_serving_predict(
      timeout_ms = 300,
      kess_shard = "s1",
      loss_function_name = ["ctr"],
      loss_default_value = [-1.0],
      # 为了区别不同输出需求，定义的 output 前缀
      output_prefix = "tfp_",
      send_item_attrs = ["pId", "aId"],
    )

    .tf_serving_predict(
      timeout_ms = 300,
      kess_shard = "s1",
      loss_function_name = ["ctr"],
      loss_default_value = [-1.0],
      # 为了区别不同输出需求，定义的 output 前缀
      output_prefix = "tfp_",
      tf_sequence_example_attr = "tf_sequence_example"
    )
    ```
    """
    self._add_processor(CommonRecoTfServingPredictEnricher(kwargs))
    return self

  def get_common_attr_by_sample_list(self, **kwargs):
    """
    CommonRecoSampleListCommonAttrEnricher
    ------
    从 SampleList 服务中获取用户特征（SampleAttr 格式）作为 CommonAttr 内容填入 Context 中

    参数配置
    ------
    `kess_service`: [string] [动态参数] SampleList 服务的 kess 服务名

    `service_group`: [string] 选配项，SampleList 服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] SampleList 服务的超时时间，必须大于 0，默认值 200

    `include_attrs`: [list] 选配项，显式指定需要的 attr，为空或者不设置则包含所有返回的 attr（不建议缺省留空，尽量显式指定需要的数据）。

    `user_id`: [int] [动态参数] 选配项，手动指定要获取特征的 user_id，缺省则使用当前请求的 user_id

    `device_id`: [string] [动态参数] 选配项，手动指定要获取特征的 device_id，缺省则使用当前请求的 device_id

    `attr_config`: [string] 业务token用来动态配置需要请求的attr

    `no_overwrite`: [bool] 选配项，仅当 attr 不存在时写入，默认为 false。

    `save_attr_names_to_attr`: [string] 选配项，(**更建议使用 include_attrs 配置项显式写明属性列表！**) 把从样本拼接服务中获取的 UserAttr 名称列表作为值，save_attr_names_to_attr 值作为 AttrName 存入 CommonAttr 中，以方便后续 Processor 可以知道 CommonAttr 中哪些 Attr 是从样本拼接服务获取的。为空或不设置将不会存储 UserAttr 的名称列表。

    调用示例
    ------
    ``` python
    .get_common_attr_by_sample_list(
      kess_service = "grpc_xxx",
      include_attrs = ["aaa", "bbb"]
      attr_config = "Live"
    )
    ```
    """
    self._add_processor(CommonRecoSampleListCommonAttrEnricher(kwargs))
    return self

  def transform_item_attr(self, **kwargs):
    """
    CommonRecoTransformItemAttrEnricher
    ------
    根据自定义规则来转换 ItemAttr。可设定一组规则来检查某个 ItemAttr ，判定其值是否属于某个集合或某个范围，若判定成功则为该 item 生成一个新的 ItemAttr 并赋予对应规则所指定的值。

    参数配置
    ------
    `mappings`: [list] 新 ItemAttr 的生成映射关系
      - `check_attr_name`: [string] 待检查的 ItemAttr 名称
      - `check_attr_type`: [string] 待检查的 ItemAttr 类型，仅支持 int/double/string （注意：double 类型只支持范围检查，string 类型只支持集合检查）
      - `output_attr_name`: [string] 待生成的 ItemAttr 名称
      - `output_attr_type`: [string] 待生成的 ItemAttr 类型，仅支持 int/double/string
      - `output_default_value`: [int|double|string] 如果待检查的 ItemAttr 未匹配上任何一个规则，则生成新 ItemAttr 并设为该默认值（需与 `output_attr_type` 指定的类型一致）；若不设置，将不会生成新 ItemAttr
      - `rules`: [list] 集合和对应值的配置，可配置多项
        - `check_values`: [list] 待判定的值集合，可包含整数、字符串或 int/string/int_list/string_list 类型的动态参数；该配置仅当 check_attr_type 为 int 或 string 时有效
        - `check_range`: [dict] 待判定的值范围，该配置仅当 check_attr_type 为 int 或 double 时有效
          - `lower_bound`: [double] [动态参数] 值范围的下界（包含），缺省则不做限制
          - `upper_bound`: [double] [动态参数] 值范围的上界（不包含），缺省则不做限制
        - `output_value`: [int|double|string] 若该条规则匹配成功，则为新 ItemAttr 设置该值，该值的类型需要与 output_attr_type 保持一致！
        - `single_limit`: [int] 当 check_values 中指定了 CommonAttr 时，CommonAttr 可能是一个长度很长的 list，为防止耗时严重可通过该配置设置单个 list 最多检查多少项，缺省则不做限制
        - `total_limit`: [int] 与 single_limit 类似，total_limit 表示集合里的所有值只检查不超过 total_limit 个，缺省则不做限制

    调用示例
    ------
    ``` python
    .transform_item_attr(
      mappings = [{
        "check_attr_name": "cluster_id",
        "check_attr_type": "int",
        "output_attr_name": "in_browsed_cluster",
        "output_attr_type": "int",
        # 当所有 rule 都无法匹配时将使用该默认值，可不设置
        "output_default_value": -1,
        # 检查规则
        "rules": [{
          # 当 cluster_id 在 recent_browsed_cluster 内或为 108 时，设置 in_browsed_cluster 属性值为 1
          "check_values": ["{{recent_browsed_cluster}}", 108],
          "output_value": 1,
        }, {
          # 当 cluster_id 在 [200, 500) 内时，设置 in_browsed_cluster 属性值为 1
          "check_range": {
            "lower_bound": 200, # 包含，可缺省
            "upper_bound": 500, # 不包含，可缺省
          },
          "output_value": 1,
        }]
      }]
    )
    ```
    """
    self._add_processor(CommonRecoTransformItemAttrEnricher(kwargs))
    return self

  def count_item_attr(self, **kwargs):
    """
    CommonRecoOccurrenceCountItemAttrEnricher
    ------
    与 transform_item_attr 类似，该 Enricher 的功能是统计某个 ItemAttr 的值在某个集合中出现的次数，该集合可以指定固定值或 CommonAttr

    参数配置
    ------
    `counters`: [list] 新 ItemAttr 的生成映射关系
      - `check_attr_name`: [string] 待检查的 ItemAttr 名，仅支持 int/string/int_list/string_list 类型的 ItemAttr，对 list 类型 attr 统计的是所有值出现的次数总和
      - `output_attr_name`: [string] 待生成的 int ItemAttr 名称，其值将为 `check_attr_name` 的值在 `check_values` 集合中出现的次数
      - `check_values`: [list] 待判定的值集合，可包含整数或字符串，字符串若为 "{{}}" 格式将作为 CommonAttr 展开处理
      - `single_limit`: [int] 当 `check_values` 中指定了 CommonAttr 时，CommonAttr 可能是一个长度很长的 list，为防止耗时严重可通过该配置设置单个 list 最多检查多少项，缺省则不做限制
      - `total_limit`: [int] 与 `single_limit` 类似，`total_limit` 表示集合里的所有值只检查不超过 `total_limit` 个，缺省则不做限制
      - `length_limit_for_check_attr`: [int] 当待检查的 ItemAttr 是个 list 时, 限制 list 中仅前 `length_limit_for_check_attr` 个参与检查, 若为 "{{}}" 格式将作为 CommonAttr 展开处理, 缺省则不做限制
      - `max_count`: [int] 最多计数多少次，缺省则不做限制
    调用示例
    ------
    ``` python
    .count_item_attr(
      counters = [{
        "check_attr_name": "author_id",
        "output_attr_name": "author_browsed_count",
        # 统计 author_id 在 recent_browsed_authors 中出现的次数
        "check_values": ["{{recent_browsed_authors}}", 566],
      }]
    )
    ```
    """
    self._add_processor(CommonRecoOccurrenceCountItemAttrEnricher(kwargs))
    return self

  def copy_user_meta_info(self, **kwargs):
    """
    CommonRecoUserMetaInfoEnricher
    ------
    将 request 结构中的 user_id, device_id, request_id, request_type 数据作为 common_attr 另存一份，以供其他 Processor 获取处理

    参数配置
    ------
    `save_user_id_to_attr`: [string] 将 user_id 另存到指定的 common_attr 中，缺省则不存

    `save_device_id_to_attr`: [string] 将 device_id 另存到指定的 common_attr 中，缺省则不存

    `save_request_id_to_attr`: [string] 将 request_id 另存到指定的 common_attr 中，缺省则不存

    `save_request_type_to_attr`: [string] 将 request_type 另存到指定的 common_attr 中，缺省则不存

    `save_browse_set_size_to_attr`: [string] 将 browse_set 的 size 另存到指定的 common_attr 中，缺省则不存

    `save_result_size_to_attr`: [string] 将当前结果集的 size 大小另存到指定的 common_attr 中，缺省则不存

    `save_request_time_to_attr`: [string] 将 request_time 另存到指定的 common_attr 中，缺省则不存

    `save_request_num_to_attr`: [string] 将 request_num 另存到指定的 common_attr 中，缺省则不存

    `save_current_time_ms_to_attr`: [string] 将当前时间戳（毫秒数）另存到指定的 common_attr 中，缺省则不存

    `save_host_name_to_attr`: [string] 将当前机器名另存到指定的 common_attr 中，缺省则不存

    `save_host_ip_to_attr`: [string] 将当前机器 ip 另存到指定的 common_attr 中，缺省则不存

    `save_elapsed_time_to_attr`: [string] 将当前请求在服务内部处理已耗费的微秒数存入指定的 common_attr 中，缺省则不存

    `save_shard_no_to_attr`: [string] 将当前服务所在的 shard 号另存到指定的 common_attr 中，缺省则不存

    `save_shard_num_to_attr`: [string] 将当前服务所在的 shard 总数另存到指定的 common_attr 中，缺省则不存

    `save_flow_cpu_cost_to_attr`: [string] 将当前 flow 开始到现在的 cpu 消耗（包括当前 flow 内已经合并回来的 subflow 的 cpu 消耗）存到指定的 common_attr 中，缺省则不存。需开启 gflag pipeline_cpu_cost_sample_rate，采样命中时将该指定的 common_attr 设为大于 0 的 int 值，采样未命中时设为 -1

    `save_need_traceback_to_attr`: [string] 将当前 request 是否被先知采样命中，另存到指定的 common_attr 中，缺省则不存

    调用示例
    ------
    ``` python
    .copy_user_meta_info(
      save_user_id_to_attr="uid",
      save_device_id_to_attr="did",
    )
    ```
    """
    self._add_processor(CommonRecoUserMetaInfoEnricher(kwargs))
    return self

  def copy_item_meta_info(self, **kwargs):
    """
    CommonRecoItemMetaInfoEnricher
    ------
    将 CommonRecoResult 结构中的 item_key、item_id、item_type、reason、score 数据作为 item_attr 另存一份，以供其他 Processor 获取处理

    注意：如果结果集中存在多个相同 item_key 的 item，那么排在靠后的 item 将覆盖之前的 item 数据进行 item_attr 存储

    参数配置
    ------
    `save_item_key_to_attr`: [string] 将 item_key 另存到指定的 item_attr 中，缺省则不存

    `save_item_id_to_attr`: [string] 将 item_id 另存到指定的 item_attr 中，缺省则不存

    `save_item_type_to_attr`: [string] 将 item_type 另存到指定的 item_attr 中，缺省则不存

    `save_reason_to_attr`: [string] 将 reason 另存到指定的 item_attr 中，缺省则不存

    `save_score_to_attr`: [string] 将 score 另存到指定的 item_attr 中，缺省则不存

    `save_in_browse_set_to_attr`: [string] 将该 item 是否存在于 browse_set 的信息存到指定的 item_attr 中（在 browse_set 中置 1，否则置 0），缺省则不存

    `save_item_seq_to_attr`: [string] 将当前 item 的位置序号（从 0 开始）另存到指定的 item_attr 中，缺省则不存

    调用示例
    ------
    ``` python
    .copy_item_meta_info(
      save_reason_to_attr="reason",
      save_item_type_to_attr="item_type",
    )
    ```
    """
    self._add_processor(CommonRecoItemMetaInfoEnricher(kwargs))
    return self

  def enrich_attr_by_lua(self, **kwargs):
    """
    CommonRecoLuaAttrEnricher
    ------
    ?> 对性能要求较高，或希望用 python 脚本实现的场景，可以用 [enrich_attr_by_py](#enrich_attr_by_py) 算子替代

    根据自定义的 Lua 脚本逻辑对 common_attr 和 item_attr 进行操作（读取和生成）

    可用于打分、attr 转换等复杂操作。`function_for_common` 和 `function_for_item` 不必同时配置，按需选用即可。

    安装 python3 lupa 模块后，dsl 会自动开启 lua 语法检测。
    - 开发云直接执行 pip3 install lupa，开发机需要安装 lua 后再执行 pip3 install lupa。

    可用扩展函数：
    - `util.GetTimestamp()`: 获取微秒级时间戳
    - `util.CityHash64(str)`: 对 string 参数值执行 CityHash64 生成 int64 哈希值
    - `util.XXHash32(data, seed)`: 对 data 参数值执行 XXHash32 生成 int32 哈希值，data 类型为 string 或 int(底层为int64，即 8 个字节参与计算)，seed 为 int 类型的可选参数，不设置则使用默认值 0，表示不使用 seed 引入随机性
    - `util.XXHash64(data, seed)`: 对 data 参数值执行 XXHash64 生成 int64 哈希值，data 类型为 string 或 int(底层为int64，即 8 个字节参与计算)，seed 为 int 类型的可选参数，不设置则使用默认值 0，表示不使用 seed 引入随机性
    - `util.GeoHashEncode(latitude, longitude, length)`: 对 latitude longitude 执行 GeoHashEncode 生成长度为 length 的哈希值，latitude 和 longitude 类型为 double，length 为 int 类型的可选参数，不设置则使用默认值 8
    - `util.GeoHashDecode(geo_hash_code)`: 对 geo_hash_code 执行 GeoHashDecode 解码成 latitude 和 longitude，geo_hash_code 类型为 string，返回 latitude 和 longitude 类型均为 double
    - `util.GenKeysign(type, id)`: 根据 type 和 id 值生成 item key
    - `util.GetType(key)`: 根据 item key 获取 item type
    - `util.GetId(key)`: 根据 item key 获取 item id
    - `util.Random()`: 易用的随机数引擎，会自动为每个线程设置不同 seed，根据[0, 1)之间的均匀分布产生随机数。
    - `util.Sleep(ms)`: 睡眠指定的毫秒数。
    - `util.GetDistance(lat_a, lon_a, lat_b, lon_b)`: 计算 (lat_a, lon_a), (lat_b, lon_b)  两个坐标之间的距离。
    - `util.TimeStringToTimestamp(str)`: 将类似 "2011-01-02 15:38:59"、"2011-01-02 15:38:59.300" 格式的时间字符串转换为 int64 类型毫秒级时间戳，[调用示例](http://ksurl.cn/wuZHh2XM)。
    - `util.discretize(data, boundaries)`: 计算数值 data 在 boundaries 切分的第几个区间内，譬如 util.discretize(15, [0, 10, 20]) 将返回 2，注意 boundaries 必须为一个升序数组。
    - `math.beta_dist(arg1, arg2)`: 获取满足 beta 分布的随机数, arg1 和 arg2 分别为 beta 分布的 alpha 和 beta 参数

    注意事项：
    - Lua 版本为 5.4.4
    - 只加载了 Lua 标准库，如有其它 Library 需求请与 @方剑冰 协商支持
    - [Lua 语法快速上手](https://learnxinyminutes.com/docs/zh-cn/lua-cn/)

    Tips:
    - 如果 import 的 attr_name 不符合 lua 的变量名规范，可通过 `_G` 全局变量获取: `local foo = _G["any.Special#attr-NAME"]`

    参数配置
    ------
    `import_common_attr`: [list] 选配项，需要导入到 lua 脚本全局变量的 common_attr 名称列表（支持所有 attr 类型）

    `import_item_attr`: [list] 选配项，需要导入到 lua 脚本全局变量的 item_attr 名称列表（支持所有 attr 类型）

    `export_common_attr`: [list] 选配项，将 lua 脚本的返回值依次导出到指定的 common_attr 中（支持所有 attr 类型）

    `export_item_attr`: [list] 选配项，将 lua 脚本的返回值依次导出到指定的 item_attr 中（支持所有 attr 类型）

    `function_for_common`: [string] [动态参数] 选配项，指定使用 lua 脚本中哪个 function 的返回结果导出到 `export_common_attr` 指定的 common_attr 中，仅在 `function_for_item` 之前执行一次.

    `function_for_item`: [string] [动态参数] 选配项，指定使用 lua 脚本中哪个 function 的返回结果导出到 `export_item_attr` 指定的 item_attr 中，为每个 item 执行一次，
                         同时该 function 接收 4 个输入参数: (seq, item_key, reason, score), 其中 seq 分别表示当前处理的是第几个 item（从 0 开始）

    `lua_script`: [string] 必配项，用于执行计算逻辑的 lua 脚本内容，被 `function_for_common` 或 `function_for_item` 指定的函数可 return 多个值，返回值类型支持 int(list) / bool(list) / double(list) / string(list)

    `lua_script_file`: [string] 选配项，指定一个外部文件的相对路径, 用其内容作为 `lua_script` 的值, 配置该项后可不配置 `lua_script`

    `clear_attr_if_nil`: [bool] 当函数返回 nil 时清空 context 中的值，默认为 false，即返回 nil 时保持 context 中的值不变。

    `check_lua`: [bool] 选配项，深度检查 lua 函数脚本合法性，默认为 false 。 开启需要安装 python3 lupa 模块。 安装请参考: https://halo.corp.kuaishou.com/api/cloud-storage/v1/public-objects/zhaoyang09_public/install_lupa.sh

    调用示例
    ------
    ``` python
    .enrich_attr_by_lua(
      import_common_attr = ["curTimeStamp", "ctr_weight", "ltr_weight", "lvtr_weight"],
      import_item_attr = ["pKsAvgEmpCtr", "pKsAvgEmpLtr", "pHotShowCnt", "pp_pctr", "pp_pltr", "pp_plvtr", "pUploadTime"],
      # function_for_item 的值也可用 "{{}}" 格式指定为某个 common_attr
      function_for_item = "calculate",
      # 将 calculate 函数的返回值依次存入 export_item_attr 指定的 3 个 item_attr 中
      export_item_attr = ["score", "weighted_emp_score", "weighted_score"],
      lua_script = \"\"\"
        function calculate(seq, item_key, reason, score)
          local click_score = pp_ctr * 1.0
          local like_score = pp_ctr * pp_ltr
          local lview_score = pp_ctr * pp_lvtr
          local day_gap = (curTimeStamp - pUploadTime) / 86400000
          local time_decay = day_gap < 0 and 1 or (0.99 ^ day_gap)
          local weighted_emp_score = (pKsAvgEmpCtr * ctr_weight + pKsAvgEmpLtr * ltr_weight) * time_decay
          local weighted_score = (click_score * ctr_weight + like_score * ltr_weight + lview_score * lvtr_weight) * time_decay
          local score = pp_ctr <= 0 and ((show_cnt > 50) and weighted_emp_score or 0) or weighted_score
          return score, weighted_emp_score, weighted_score
        end
      \"\"\"
    )
    ```
    """
    if "lua_script_file" in kwargs and "lua_script" not in kwargs:
      from pathlib import Path
      lua_script_file = kwargs.pop("lua_script_file")
      file_path = Path(lua_script_file)
      if not file_path.exists():
        import os, sys
        file_path = Path(os.path.dirname(sys.argv[0]), lua_script_file)
      if not file_path.is_file():
        raise ArgumentError(f"lua_script_file 文件不存在: {file_path.resolve()}")
      kwargs["lua_script"] = file_path.read_text()

    if "lua_script" not in kwargs:
      raise ArgumentError("缺少 lua_script 配置")
    kwargs["lua_script"] = kwargs["lua_script"].strip()

    self._add_processor(CommonRecoLuaAttrEnricher(kwargs))
    return self

  def enrich_attr_by_python(self, **kwargs):
    """
    CommonRecoPythonAttrEnricher
    ------
    根据自定义的 Python 脚本逻辑对 common_attr 和 item_attr 进行操作（读取和生成）

    注意：由于 GIL 的存在，Python 脚本是单线程执行的，如果要进行复杂运算请使用 Numba。

    可用于打分、attr 转换等复杂操作。

    注意事项：
    - Python 性能不算好，慎用，另外由于 GIL，这个 Processor 是单线程执行的
    - Python 版本为 3.6
    - 该 Processor 要求运行机器上安装了 Python3.6
    - 需要额外的 Python 库直接在运行机器上安装就行
    - [Numba](https://numba.pydata.org/)

    参数配置
    ------
    `import_common_attr`: [list] 选配项，需要导入到 python 脚本全局变量的 common_attr 名称列表（支持所有 attr 类型）

    `import_item_attr`: [list] 选配项，需要导入到 python 脚本全局变量的 item_attr 名称列表（支持所有 attr 类型）

    `export_common_attr`: [list] 选配项，将 python 脚本的返回值依次导出到指定的 common_attr 中（暂不支持 list）

    `export_item_attr`: [list] 选配项，将 python 脚本的返回值依次导出到指定的 item_attr 中（暂不支持 list）

    `python_script`: [string] 必配项，用于执行计算逻辑的 python 脚本内容

    调用示例
    ------
    ``` python
    .enrich_attr_by_python(
      import_common_attr = ["curTimeStamp", "ctr_weight", "ltr_weight", "lvtr_weight"],
      import_item_attr = ["pKsAvgEmpCtr", "pKsAvgEmpLtr", "pHotShowCnt", "pp_pctr", "pp_pltr", "pp_plvtr", "pUploadTime"],
      export_item_attr = ["score", "weighted_emp_score", "weighted_score"],
      python_script = \"\"\"
        click_score = pp_ctr * 1.0
        like_score = pp_ctr * pp_ltr
        lview_score = pp_ctr * pp_lvtr
        day_gap = (curTimeStamp - pUploadTime) / 86400000
        time_decay = 1 if day_gap < 0 else (0.99 ** day_gap)
        weighted_emp_score = (pKsAvgEmpCtr * ctr_weight + pKsAvgEmpLtr * ltr_weight) * time_decay
        weighted_score = (click_score * ctr_weight + like_score * ltr_weight + lview_score * lvtr_weight) * time_decay
        if pp_ctr <= 0:
          if show_cnt > 50:
            score = weighted_emp_score
          else:
            score = 0
        else:
          score = weighted_score
      \"\"\"
    )
    ```
    """
    self._add_processor(CommonRecoPythonAttrEnricher(kwargs))
    return self

  def gen_common_attr_by_lua(self, **kwargs):
    """
    CommonRecoLuaAttrEnricher 语法糖扩展
    ------
    根据已有的 common_attr 利用 lua 表达式生成新的 common_attr 用于后续使用，底层基于 CommonRecoLuaAttrEnricher 实现

    参数配置
    ------
    `attr_map`: [dict] 需要生成的新 common_attr 名称及计算规则，格式为一个 dict，每项包含
      - key: [string] 新 common_attr 的名称
      - value: [string] 新 common_attr 值的 lua 计算表达式

    调用示例
    ------
    ``` python
    .gen_common_attr_by_lua(
      attr_map={
        "one_hour_later": "current_time + 3600000",
        "click_list_size": "#(click_list or {})",
      }
    )
    ```
    """
    attr_map = kwargs.pop("attr_map", {})
    if not attr_map or not isinstance(attr_map, dict):
      raise ArgumentError("gen_common_attr_by_lua() 的 attr_map 参数需为非空 dict 类型")
    detected_import_attrs = set()
    for attr, expr in attr_map.items():
      if not expr:
        raise ArgumentError(f"gen_common_attr_by_lua() 中 {attr} 的表达式不可为空")
      pos = expr.find("..")
      if pos >= 0:
        if (pos-1 >= 0 and expr[pos-1] != ' ') or (pos+2 < len(expr) and expr[pos+2] != ' '):
          raise ArgumentError(f"gen_common_attr_by_lua() 中表达式 {attr}: {expr} 里的字符串拼接符 .. 前后必须加空格隔开")
      detected_import_attrs.update(extract_attrs_from_expr(expr))
    detected_import_attrs.update(kwargs.get("import_common_attr", []))
    func_name = "gen_common_attrs"
    kwargs["import_common_attr"] = list(sorted(detected_import_attrs))
    kwargs["export_common_attr"] = list(sorted(attr_map.keys()))
    kwargs["function_for_common"] = func_name
    returns_expr = ", ".join(f"({attr_map[key]})" for key in kwargs["export_common_attr"])
    kwargs["lua_script"] = f"function {func_name}() return {returns_expr} end"
    self._add_processor(CommonRecoLuaAttrEnricher(kwargs))
    return self

  def calc_weighted_sum(self, **kwargs):
    """
    CommonRecoWeightedSumEnricher
    ------
    对多个 item attr 进行加权计算。

    参数配置
    ------
    `formula_version`: [int] 选配项，使用哪个版本的单项分公式计算，默认为 0 (线性加权)
      - 0: `Si = channels[i].weight * ATTR_VALUE(channels[i].name` 线性加权公式
      - 1: `Si = channels[i].weight * log(ATTR_VALUE(channels[i].name)` 对数加权公式

    `channels`: [list] 需要用于加权计算的队列配置，每个队列包含以下两个个配置项：
      - `name`: [string] 队列名，将从同名 double 或 int 类型 item_attr 获取值进行加权计算
      - `weight`: [double] [动态参数] 该队列的权重

    `output_item_attr`: [string] 将最后计算出加权和分数存入指定的 double item_attr

    调用示例
    ------
    ``` python
    .calc_weighted_sum(
      channels = [
        { "name": "pctr", "weight": "{{w_pctr}}" },
        { "name": "pltr", "weight": "{{w_pltr}}" },
        { "name": "pftr", "weight": "{{w_pftr}}" },
      ],
      output_item_attr = "final_score",
    )
    ```
    """
    self._add_processor(CommonRecoWeightedSumEnricher(kwargs))
    return self

  def calc_ensemble_score(self, **kwargs):
    """
    CommonRecoEnsembleScoreEnricher
    ------
    对指定的 N 个队列计算用于 ensemble sort 的综合分，计算方式:

    每个 item 在各个队列 channels[i] 的单项分公式: `Si = func(formula_version)`

    每个 item 最后输出到 `output_attr` 的综合分数值为: `S1 + S2 +...+ Sn` (`n` 为 channels 个数)

    注意：该方法只生成 ensemble score, 如需根据该综合分进行排序, 请在之后自行调用 `.sort(score_from_attr="${output_attr}")`

    ?> 推荐改用 [calc_by_formula1](#calc_by_formula1) 算子实现，自定义公式结构及 AB 实验更加方便

    参数配置
    ------
    `formula_version`: [int] 选配项，使用哪个版本的单项分公式计算，默认为 0 (初版 ensemble 公式)
      - 0: `Si = channels[i].weight / (RANK(channels[i].name) ^ regulator + smooth)`
      - 1: `Si = channels[i].weight * (1 - min(1, RANK(channels[i].name) / item_num) ^ regulator)`
      - 2: `Si = channels[i].weight * (e ^ (channels[i].hyper_scala * (2.0 * (RANK(channels[i].name) / item_num) - 1.0)) - e ^ (-channels[i].hyper_scala * (2.0 * (RANK(channels[i].name) / item_num) - 1.0)))`
      - 3: `Si = channels[i].weight * (1 - (RANK(channels[i].name) / (item_num + smooth)) ^ regulator)`

    `channels`: [list] 需要用于 ensemble sort 的队列配置，每个队列包含以下三个配置项：
      - `enabled`: [bool] [动态参数] 该队列开关，默认值是 true
      - `name`: [string] 队列名，将从同名 double/int 类型 item_attr 获取值进行排序计算
      - `weight`: [double] [动态参数] 该队列的权重
      - `hyper_scala`: [double] [动态参数] 该队列的双曲调节因子
      - `as_seq_value`: [bool] 是否跳过对该队列的排序操作，直接使用该 attr 的值作为 seq 序号参与 score 计算，默认为 False
      - `save_score_to`: [string] 选配项，将该 channel 的 `Si = func(formula_version)` 分值存入指定的 item_attr

    `regulator`: [double] [动态参数] ensemble sort 各队列的调节因子

    `smooth`: [double] [动态参数] ensemble sort 各队列的平滑因子

    `output_attr`: [string] 将最后计算出的 ensemble 综合分存入指定的 item_attr

    `default_value`: [double] 选配项，若 item 不存在指定的 item_attr 则使用该默认值参与排序，默认值为 0

    `start_seq`: [int] 选配项，序号的起始值，默认为 0

    `allow_tied_seq`: [bool] 选配项，是否允许并列的序号值，例如可能按排名产出如下值：0, 1, 1, 3, 4, 4, 4, 7; 默认值 False

    `continuous_tied_seq`: [bool] 选配项，在允许并列序号的情况下，是否产出连续的序号，例如为 True 时将按排名产出如下值：0, 1, 1, 2, 3, 3, 3, 4; 默认值 False

    `cliff_ratio`: [double] [动态参数] 选配项，ensemble 队列排序序号开始降权 position 的比例点，即对排名 `队列长度 * cliff_ratio` 之后的 item 进行打压

    `cliff_height`: [int] [动态参数] 选配项，ensemble 队列降权系数，即对排名 `队列长度 * cliff_ratio` 之后的 item 序号额外加上该值作为惩罚

    `epsilon`: [double] 选配项，在允许并列序号的情况下，比较是否相等时的精度要求，默认为 1e-9

    `stable_sort`: [bool] 选配项，是否使用稳定排序来计算各 item 序号，默认值 False

    调用示例
    ------
    ``` python
    .calc_ensemble_score(
      channels = [
        { "name": "pctr", "weight": "{{w_pctr}}" },
        { "name": "pltr", "weight": "{{w_pltr}}" },
        { "name": "pftr", "weight": "{{w_pftr}}" },
      ],
      regulator = "{{ensemble_regulator}}",
      smooth = "{{ensemble_smooth}}",
      output_attr = "ensemble_score",
    )
    ```
    """
    self._add_processor(CommonRecoEnsembleScoreEnricher(kwargs))
    return self

  def gen_ensemble_seq_num(self, **kwargs):
    """
    CommonRecoEnsembleSeqNumEnricher
    ------
    对指定的一组 item_attr 指标进行 ensemble sort，并将各个 item 排序后所处的序号位置写入新的 item_attr 中供后续使用

    ?> 提示：如果是要用于 ensemble sort, 可改用封装程度更高的 `.calc_ensemble_score()` 接口

    参数配置
    ------
    `ensemble_attrs`: [list] 需要用于 ensemble sort 的 item_attr 列表

    `output_attr_postfix`: [string] 将 ensemble sort 后的排序序号写入对应指标 item_attr 名称加上该后缀的属性中

    `default_value`: [double] 若 item 不存在指定的 item_attr 则使用该默认值参与排序，默认值为 0

    `start_seq`: [int] 序号的起始值，默认为 0

    `allow_tied_seq`: [bool] 是否允许并列的序号值，例如可能按排名产出如下值：0, 1, 1, 3, 4, 4, 4, 7; 默认值 False

    `continuous_tied_seq`: [bool] [动态参数] 在允许并列序号的情况下，是否产出连续的序号，例如为 True 时将按排名产出如下值：0, 1, 1, 2, 3, 3, 3, 4; 默认值 False

    `epsilon`: [double] [动态参数] 在允许并列序号的情况下，比较是否相等时的精度要求，默认为 1e-9

    `stable_sort`: [bool] 排序时是否使用稳定排序，默认值 False

    `desc`: [bool] 是否降序排序，默认 True

    调用示例
    ------
    ``` python
    .gen_ensemble_seq_num(
      ensemble_attrs=["ctr", "ltr", "ftr"],
      output_attr_postfix="_seq",
    )
    ```
    """
    self._add_processor(CommonRecoEnsembleSeqNumEnricher(kwargs))
    return self

  def filter_by_rule(self, **kwargs):
    """
    CommonRecoRuleFilterArranger
    ------
    通过 and/or 的 结合方法，基于 item attr 值的组合规则过滤

    参数配置
    ------
    `rule`: [dict] 规则应用模式：对某个 ItemAttr 的值进行过滤
      - `enable`: [bool] [动态参数] 单个过滤模块的开关
      - `attr_name`: [string] 单个过滤模块的 Item attr 名称
      - `check_reason`: [bool] 是否使用 Item reason 代替 Item attr 的值进行检查判断，默认为 false
      - `remove_if`: [string] 单个过滤模块的比较运算符，可选值："<", "<=", ">", ">=", "==", "!=", "in", "not in", "contain", "not contain", "intersect", "not intersect", "is null", "not null"
      - `compare_to`: [int|double|string|int_list|string_list] [动态参数] 单个过滤模块的`remove_if` 被比较的值，默认值为 0，double 类型值对于 ==、in、contain、intersect 等精确比较场景不生效。字符串若为 "{{}}" 格式将作为 CommonAttr 展开处理。
      - `compare_to_item_attr`: [string] 被比较的 item attr name, 不能与 compare_to 同时配置
      - `remove_if_attr_missing`: [bool] 如果 item 没有该 attr 是否进行删除，默认值 false，同时可用于对任意类型 item_attr 的存在性进行判定删除

    `rule`: [dict] 规则组合模式，用 and/or 连接不同过滤 rule
      - `join`: [string] 复合过滤的子过滤模块结合方式，支持 or 和 and 两种方式，可缺省
      - `filters`: [list] 由一组 `rule` 组成（可嵌套）

    `pardon_num`: [int] [动态参数] 可通过该值保留一定数目满足删除条件的 item（即不完全删除，保留前 n 个），默认值为 0

    `cancel_num`: [int] [动态参数] 如果该值 >= 0，且执行 filter 后的 item 数目小于等于该值，将取消该次 filter 操作，默认值为 -1

    `ignore_invalid_rule`: [bool] 是否忽略无效的 rule，即 rule 中的 compare_to 值不存在的情况，默认值为 false

    调用示例
    ------
    ``` python
    # 单值过滤
    .filter_by_rule(
      rule = {
        "attr_name": "like_score",
        "remove_if": "<=",
        "compare_to": 0
      }
    )
    # join 过滤
    .filter_by_rule(
      rule = {
        "join": "and",
        "filters": [{
          "attr_name": "p_int",
          "remove_if": "<=",
          "compare_to": 8
        }, {
          "attr_name": "p_int",
          "remove_if": ">=",
          "compare_to": 3
        }]
      }
    )
    # 两层 join
    .filter_by_rule(
      rule = {
        "join": "or",
        "filters": [{
          "join": "and",
          "filters": [{
            "attr_name": "p_int",
            "compare_to": 3,
            "remove_if": ">"
          }, {
            "attr_name": "p_double",
            "compare_to": 5.0,
            "remove_if": "<"
          }]
        }, {
          "attr_name": "p_str",
          "compare_to": "str",
          "remove_if": "==",
          "remove_if_attr_missing": True
        }]
      }
    )
    ```
    """
    self._add_processor(CommonRecoRuleFilterArranger(kwargs))
    return self

  def filter_by_attr(self, **kwargs):
    """
    CommonRecoAttrFilterArranger
    ------
    ?> 推荐使用 [filter_by_rule](#filter_by_rule), 支持更多样的比较方式及逻辑组合能力!

    根据某个 int/double/string 类型 ItemAttr 的值进行过滤

    参数配置
    ------
    `attr_name`: [string] 用于过滤的 ItemAttr 名称

    `remove_if`: [string] 比较运算符，可选值："<", "<=", ">", ">=", "==", "!=", 默认值为 "<="

    `compare_to`: [int|double|string] [动态参数] `remove_if` 被比较的值，默认值为 0。注意：string 类型的值只支持 remove_if 为 "==" 或 "!="。

    `remove_if_attr_missing`: [bool] 如果 item 没有该 attr 是否进行删除，默认值 false，同时可用于对 list 和 extra 类型 item_attr 的存在性进行判定删除

    `pardon_num`: [int] [动态参数] 可通过该值保留一定数目满足删除条件的 item（即不完全删除，保留前 n 个），默认值为 0

    `cancel_num`: [int] [动态参数] 如果该值 >= 0，且执行 filter 后的 item 数目小于等于该值，将取消该次 filter 操作，默认值为 -1

    调用示例
    ------
    ``` python
    .filter_by_attr(
      attr_name="like_score",
      remove_if="<=",
      compare_to=0,
      remove_if_attr_missing=True,
    )
    ```
    """
    self._add_processor(CommonRecoAttrFilterArranger(kwargs))
    return self

  def ktrace_span(self, **kwargs):
    """
    CommonRecoKtraceSpanObserver
    ------
    会将与 ktrace_span start 和 end 之间的 processor 聚合在一起打一个 Ktrace，供业务方排查部分需求。不支持多个区间嵌套。

    目前禁止交叉，如果出现交叉的情况，我们这边会自动添加一个 type 为 end 的 ktrace_span

    end processor 无须配置除 type 以外其他参数。

    注：亦可使用封装好的 start_ktrace_span(tag) 和 end_ktrace_span() 函数来实现同样的功能

    参数配置
    ------
    `span_name`: [string] [动态参数] 将区间内的 processor 都打上该 span_name, 例如 sort

    `span_tags`: [dict] 组装的 tag map, 用来添加 span 自定义 tag 的配置
      - key: [string] 表示额外标注的 tag 名
      - value: [string] 表示额外标注的 tag value，不能为空

    `type`: [string] 用来标记是 ktrace 的开始还是结束，start 表示 ktrace 开始，end 表示 ktrace 结束仅能填写 start 或者 end。

    调用示例
    ------
    ``` python
    .ktrace_span(
      span_name="sort",
      span_tags={
        "location":"us",
        "timezone":"utc+8"
      },
      type="start"
    )
    .ktrace_span(
      type="end"
    )
    ```
    """
    self._add_processor(CommonRecoKtraceSpanObserver(kwargs))
    return self

  def start_ktrace_span(self, span_name: str, span_tags: dict):
    """
    CommonRecoKtraceSpanObserver
    ------
    封装好的 ktrace_span 接口，易于调用，表示 span 的开始。

    参数配置
    ------
    `span_name`: [string] [动态参数] 将区间内的 processor 都打上该 span_name, 例如 sort

    `span_tags`: [dict] 组装的 tag map, 用来添加 span 自定义 tag 的配置
      - key: [string] 表示额外标注的 tag 名
      - value: [string] 表示额外标注的 tag value，不能为空

    调用示例
    ------
    ``` python
    .start_ktrace_span(
      span_name="start",
      span_tags={
        "location":"us",
        "timezone":"utc+8"
      }
    )
    ```
    """
    return self.ktrace_span(span_name=span_name, type="start", span_tags=span_tags)

  def end_ktrace_span(self):
    """
    CommonRecoKtraceSpanObserver
    ------
    封装好的 ktrace_span 接口，易于调用，表示 span 的结束。

    调用示例
    ------
    ``` python
    .end_ktrace_span()
    ```
    """
    return self.ktrace_span(type="end")

  def filter_by_common_attr(self, **kwargs):
    """
    CommonRecoCommonAttrFilterArranger
    ------
    根据一组 int_list 类型的 CommonAttr 的值集合进行过滤

    参数配置
    ------
    `common_attr`: [list] 需要从哪些 common_attr 的值中获取 item_key，仅支持 int_list 类型的 common_attr

    `on_item_attr`: [string] 选配项, 若非空则将使用指定的 item_attr 值用于判断是否存在于过滤集中 (仅支持 int 类型 item_attr)

    `exclude`: [bool] 选配项, 为 true 时删除 common_attr 中存在的 item (互斥关系), 否则删除 common_attr 中不存在的 item (取交关系), 默认值为 true

    `pardon_num`: [int] [动态参数] 可通过该值保留一定数目满足删除条件的 item（即不完全删除，保留前 n 个），默认值为 0

    `cancel_num`: [int] [动态参数] 如果该值 >= 0，且执行 filter 后的 item 数目小于等于该值，将取消该次 filter 操作，默认值为 -1

    调用示例
    ------
    ``` python
    .filter_by_common_attr(common_attr=["unwanted_items"])
    ```
    """
    self._add_processor(CommonRecoCommonAttrFilterArranger(kwargs))
    return self

  def filter_by_browse_set(self, **kwargs):
    """
    CommonRecoBrowseSetFilterArranger
    ------
    根据 request 中传入的 BrowseSet 内容进行过滤

    参数配置
    ------
    `check_id_in_attr`: [string] 选配项，从指定的 ItemAttr 中读取 int/int_list 值作为当前 item 检查 browse set 过滤用的 item_id（而非该 item 自己的 item_id 值），若 item 无该 ItemAttr 将不会被过滤

    `item_type_of_checked_id`: [int] 选配项，指定 check_id_in_attr 配置中 item_id 对应的 item_type (需大于等于 0)，默认使用与当前 item 一样的 item_type

    `save_filtered_items_to_common_attr`: [string] 选配项，将被 BrowseSet 过滤的 item key 存入指定的 CommonAttr 中，供后续 Processor 取用

    `browse_set_attr`: [string] 选配项，若未配置该项则使用context中的BrowseSet进行过滤，若配置该项则使用该common_attr(ptr)存储的browseset对象代替context中的browseset进行过滤

    调用示例
    ------
    ``` python
    .filter_by_browse_set()
    # 或者
    .filter_by_browse_set(
      save_filtered_items_to_common_attr="browse_set_filtered_items",
    )
    ```
    """
    self._add_processor(CommonRecoBrowseSetFilterArranger(kwargs))
    return self

  def filter_by_item_results(self, **kwargs):
    """
    CommonRecoItemResultsFilterArranger
    ------
    根据需要的指向 result_list 的 extra_common_attrs ， 合并为一个 result_set 。 并用此 result_set 对现有 result_list 过滤掉不在 set 内的 result 。

    一般配合 enrich_by_sub_flow 使用 。

    参数配置
    ------
    `remove_if_not_in`: [list] 指定需要合并的 result_list 的 extra common attr 列表, 会将列表内指向的 result_lists 合并成一个 set, 用于过滤掉不在 set 内的 result 。

    调用示例
    ------
    ``` python
    # 示例场景如下：
    # sub_flow_a 选择处理 reason = 1 || 2 的 results ，并且在 pipeline 内删除了 reason = 1 的 results。
    # sub_flow_b 选择处理 reason = 2 || 3 的 results
    # 原本 enrich_by_sub_flow 内的删除并不会影响到主流程中的 results 的状态 ，只会影响到 merge_item_attr 的属性填充。
    # 现在希望能将 sub_flow_a 的删除 reason = 1 的 results 的操作作用到主流程 ， 可以使用如下示例。
    # 特别注意： 如果 sub_flow_a 删除的是 reason = 2 的 results, 但同时 sub_flow_b 内没有删除 reason = 2 ，则即使如示例操作 sub_flow_a 的删除操作也不会作用到主流程。
    .enrich_by_sub_flow(name = sub_flow_a ,sub_flow = A, merge_item_attrs=["aid"], save_results_to="sub_flow_a_result", target_reason=[1,2])\\
    .enrich_by_sub_flow(name = sub_flow_b ,sub_flow = B, merge_item_attrs=["aid"], save_results_to="sub_flow_b_result", target_reason=[2,4])\\
    .filter_by_item_results(
      remove_if_not_in=["sub_flow_a_result","sub_flow_b_result"]
    )\\
    ```
    """
    self._add_processor(CommonRecoItemResultsFilterArranger(kwargs))
    return self

  def deduplicate(self, **kwargs):
    """
    CommonRecoResultsDeduplicateArranger
    ------
    根据 item key 对结果集里的 item 进行去重（保留第一个 item）。一般在所有 Retriever 召回完毕之后进行。

    参数配置
    ------
    `on_item_attr`: [string] 选配项, 若非空则将使用指定的 item_attr 值用于判断重复 (仅支持 int 和 string 类型 item_attr)

    `reason_priority_list`: [list] 选配项，若非空则将按照指定优先级保留重复 item 的 reason。优先级：【reason_priority_list（内部高到低）中的 reason】 > 【未声明的 reason】

    `lower_reason_priority_list`: [list] 选配项，次要的 reason 优先级列表。优先级：【reason_priority_list（内部高到低）中的 reason】 > 【未声明的 reason】>【lower_reason_priority_list（内部高到低）中的 reason】。(适用场景: 原有召回 reason 很多，不方便全部写入 reason_priority_list ，当新加一路召回，并只希望只保留该召回中与原有召回的差集，可以使用该配置项。)

    `reason_priority_mode`: [string] 选配项，若非空则将按指定模式保留重复 item 的 reason，当和 reason_priority_list 的优先级冲突时优先满足 reason_priority_list，支持 first(前者优先)/greater(大值优先)/less(小值优先)，默认为 first

    `append_reason_to`: [string] 选配项, 若非空则将各个 item 的 reason 值追加保存到指定的 int_list 类型 item_attr 中

    `append_order_to`: [list] 选配项, 若非空则将各个 item 的位置追加保存到指定的 int_list 类型 item_attr 中，与 append_reason_to 一一对应，仅在 append_reason_to 存在时生效

    `append_reason_order_to`: [string] 选配项, 若非空则将各个 item 在每个 reason 下的位置(排序从1开始)保存在该 item_attr 中, 与 append_reason_to 中的 reason 一一对应，仅在 append_reason_to 存在时生效

    `save_dup_count_to`: [string] 选配项, 若非空则将各个 item 的重复次数记录累加到指定的 int 类型 item_attr 中

    `append_reason_for_top_distinct_items`: [int][动态参数] 选配项, 若正整数则只将截止到找到 `append_reason_for_top_distinct_items` 个非重复 item 时为止，各个 item 的 reason 值追加保存到 `append_reason_to` 中, 其余的不再追加保存。此配置不影响去重结果, 只影响生成的 item attr `append_reason_to`。默认值为-1, 该配置仅在非负值时生效。
      - 例子: 对于结果集 [a1, b1, a2, c1, d1, b2, a3, d2]
      - 当append_reason_for_top_distinct_items=3时, 遍历到c1时, 已找到a,b,c这3个不重复的item; 截止到此刻, a有[1,2]两个reason, 所以a的聚合结果为[1,2], 其余同理。 结果: a[1,2], b[1], c[1], d[]
      - 当append_reason_for_top_distinct_items为默认值时, 则对每个item聚合所有reason。 结果: a[1,2,3], b[1,2], c[1], d[1,2]

    调用示例
    ------
    ``` python
    .deduplicate()
    ```
    """
    self._add_processor(CommonRecoResultsDeduplicateArranger(kwargs))
    return self

  def ranking_by_formula(self, **kwargs):
    """
    CommonRecoScoreCalcArranger
    ------
    **该接口已进入 DEPRECATE 状态，不再进行功能更新，建议改用 ranking_by_lua 方法**

    根据自定义 parameter, dynamic_parameter 和 formula 对 item 进行打分，并排序

    参数配置
    ------
    具体格式参见 [中台 score rerank](https://docs.corp.kuaishou.com/k/home/<USER>/fcAAHE4XZzLlB9hvZcoX8KzTY)

    调用示例
    ------
    ``` python
    .ranking_by_formula(
      parameter = {
        "ctr_weight": 1,
        "ltr_weight": 1,
        "lvtr_weight": 1,
      },
      dynamic_parameter = {
        "aAcu": "item/aAcu,,1",
        "pOnlineCnt": "item/pOnlineCnt,,1",
        "pl_ctr": "item/pl_ctr,,-1",
        "pl_ltr": "item/pl_ltr,,-1",
        "pl_lvtr": "item/pl_lvtr,,-1"
      },
      formula = {
        "click_score": "pl_ctr * 1.0",
        "like_score": "pl_ctr * pl_ltr",
        "lview_score": "pl_ctr * pl_lvtr",
        "score": "pl_ctr < 0? pOnlineCnt : xtr_score",
        "xtr_score": "(click_score*ctr_weight + like_score*ltr_weight + lview_score*lvtr_weight) * aAcu"
      },
      score_key = "score"
    )
    ```
    """
    score_calc_config_keys = ("dynamic_parameter", "parameter", "formula", "save_for_debug_request_only", "save_score_to_attr", "save_formula_score", "save_formula_score_prefix")
    sort_conf = { k: v for k, v in kwargs.items() if k not in score_calc_config_keys }
    if "name" in kwargs:
      sort_conf["name"] = kwargs["name"] + "_sort"
    self._add_processor(CommonRecoScoreCalcArranger(kwargs))
    self.sort(**sort_conf)
    return self

  def ranking_by_lua(self, **kwargs):
    """
    CommonRecoLuaAttrEnricher + CommonRecoScoreSortArranger
    ------
    根据自定义的 Lua 脚本逻辑对 item 进行打分，并排序

    提示: 可在 Lua 脚本中自行 print 各种中间变量用于 debug, 打印的内容将出现在 server.out 文件中

    注意事项：
    - Lua 版本为 5.4.4
    - 只加载了 Lua 标准库，如有其它 Library 需求请与 @方剑冰 协商支持
    - [Lua 语法快速上手](https://learnxinyminutes.com/docs/zh-cn/lua-cn/)

    参数配置
    ------
    `import_common_attr`: [list] 选配项，需要导入到 lua 脚本全局变量的 common_attr 名称列表（支持所有 attr 类型）

    `import_item_attr`: [list] 选配项，需要导入到 lua 脚本全局变量的 item_attr 名称列表（支持所有 attr 类型）.

    `function_for_item`: [string] [动态参数] 选配项，指定使用 lua 脚本中哪个 function 的返回结果导出到 `export_item_attr` 指定的 item_attr 中，为每个 item 执行一次，
                         同时该 function 接收 4 个输入参数: (seq, item_key, reason, score), 其中 seq 分别表示当前处理的是第几个 item

    `export_item_attr`: [list] 必配项，将 lua 脚本的返回值依次导出到指定的 item_attr 中（支持所有 attr 类型）

    `lua_script`: [string] 必配项，用于执行计算逻辑的 lua 脚本内容，被 `function_for_common` 或 `function_for_item` 指定的函数可 return 多个值，返回值类型支持 int(list) / bool(list) / double(list) / string(list)

    `lua_script_file`: [string] 选配项，指定一个外部文件的相对路径, 用其内容作为 `lua_script` 的值, 配置该项后可不配置 `lua_script`

    `sort_by_attr`: [string] 选配项，将哪个 item_attr 的值作为 score 用于排序，默认使用 `export_item_attr` 的第一个值

    `function_for_common`: [string] [动态参数] 选配项，指定使用 lua 脚本中哪个 function 的返回结果导出到 `export_common_attr` 指定的 common_attr 中，仅在 `function_for_item` 之前执行一次

    `export_common_attr`: [list] 选配项，将 lua 脚本的返回值依次导出到指定的 common_attr 中（支持所有 attr 类型）

    `stable_sort`: [bool] 选配项，是否使用稳定排序，默认 False

    调用示例
    ------
    ``` python
    .ranking_by_lua(
      import_common_attr = ["curTimeStamp", "ctr_weight", "ltr_weight", "lvtr_weight"],
      import_item_attr = ["pKsAvgEmpCtr", "pKsAvgEmpLtr", "pHotShowCnt", "pp_pctr", "pp_pltr", "pp_plvtr", "pUploadTime"],
      # function_for_item 的值也可用 "{{}}" 格式指定为某个 common_attr
      function_for_item = "calculate",
      # 将 calculate 函数的返回值依次存入 export_item_attr 指定的 3 个 item_attr 中
      export_item_attr = ["score", "weighted_emp_score", "weighted_score"],
      lua_script = \"\"\"
        function calculate(seq, item_key, reason, score)
          local click_score = pp_ctr * 1.0
          local like_score = pp_ctr * pp_ltr
          local lview_score = pp_ctr * pp_lvtr
          local day_gap = (curTimeStamp - pUploadTime) / 86400000
          local time_decay = day_gap < 0 and 1 or (0.99 ^ day_gap)
          local weighted_emp_score = (pKsAvgEmpCtr * ctr_weight + pKsAvgEmpLtr * ltr_weight) * time_decay
          local weighted_score = (click_score * ctr_weight + like_score * ltr_weight + lview_score * lvtr_weight) * time_decay
          local score = pp_ctr <= 0 and ((show_cnt > 50) and weighted_emp_score or 0) or weighted_score
          return score, weighted_emp_score, weighted_score
        end
      \"\"\"
    )
    ```
    """
    export_item_attr = kwargs.get("export_item_attr", [])
    if not export_item_attr:
      raise ArgumentError(".ranking_by_lua() 中未指定 export_item_attr")
    lua_config_keys = ("import_common_attr", "import_item_attr", "export_common_attr", "export_item_attr", \
        "function_for_common", "function_for_item", "lua_script", "lua_script_file")
    sort_config_keys = ("sort_by_attr", "stable_sort")
    lua_conf = { k: v for k, v in kwargs.items() if k not in sort_config_keys }
    sort_conf = { k: v for k, v in kwargs.items() if k not in lua_config_keys }
    sort_by_attr = sort_conf.pop("sort_by_attr", "")
    sort_conf["score_from_attr"] = sort_by_attr if sort_by_attr else export_item_attr[0]
    if not sort_conf["score_from_attr"]:
      raise ArgumentError(".ranking_by_lua() 中未指定 sort_by_attr")
    if "name" in kwargs:
      sort_conf["name"] = kwargs["name"] + "_sort"

    self.enrich_attr_by_lua(**lua_conf)
    self.sort(**sort_conf)
    return self

  def delegate_enrich(self, **kwargs):
    """
    CommonRecoDelegateEnricher
    ------
    调用另一个基于 CommonLeaf 协议的远程服务进行计算，并填充返回的属性

    参数配置
    ------
    `kess_service`: [string] [动态参数] 调用的 CommonLeaf 的 kess service

    `kess_cluster`: [string] 选填项，调用 CommonLeaf 的 kess cluster，默认为 PRODUCTION

    `kess_group`: [string] [动态参数] 选填项，调用的 CommonLeaf 的 kess_group，默认为 ""

    `shard_num`: [int] 被调服务的 shard 数，该 processor 会并发请求多个 shard，结果合并，默认为 1。

    `shard_id_offset`: [int] 被调服务的 shard_id 起始偏移量，默认为 0，即 shard 号从 s0 开始

    `shard_by_item_attr`: [string] 按给定的 item attr 将 item list 拆分成不同的 shard 去请求下游服务，默认为空，此时对于各个 shard 的请求完全相同。

    `consistent_hash`: [bool] 是否对请求进行一致性 hash 的分发，以保证同一用户的请求始终落在同一索引机器上，默认 False

    `hash_id`: [string] [动态参数] 优先使用该 id 对请求进行一致性 hash 的分发，可缺省，缺省时使用 user_id 或 device_id 进行分发， consistent_hash 为 True 时生效

    `timeout_ms`: [int] [动态参数] 选填项，gRPC 超时时间，默认为 300ms。

    `request_type`: [string] [动态参数] 选填项，请求的 request type，默认为本 leaf 当前的 request type。

    `use_item_id_in_attr`: [string] 选填项，若设置则使用指定的 ItemAttr 下的 int 值替代实际 Item 的 item_key，填充进 request 用于发送给下游（注意是替换 item_key，不是 item_id，配置名中的 use_item_id 字眼有歧义）

    `send_item_attrs`: [list] 选填项，发送的 item attr 列表，默认不发送 item attr，支持对 attr 重命名发送。

    `send_browse_set`: [bool] 选填项，是否在请求中填充 browse_set，默认为 false

    `send_common_attrs`: [list] 选填项，发送的 common attr 列表，默认不发送 common attr，支持对 attr 重命名发送。

    `send_common_attrs_in_request`: [bool] 选填项, 是否将上游发送过来的 request 中的全部 common_attr 发送给下游, 默认为 false。

    `exclude_common_attrs`: [list] 选填项, 发送给下游时，需要过滤的 common_attr. 一般配合 send_common_attrs_in_request 使用

    `recv_item_attrs`: [list] 选填项，接收的 item attr 列表，默认不接收 item attr，支持对 attr 重命名保存。

    `recv_common_attrs`: [list] 选填项，接收的 common attr 列表，默认不接受 common attr，支持对 attr 重命名保存。

    `for_predict`: [bool] 选填项，标记是否是为预估服务请求，若为 true 则会对接收的 double 类型 item_attr 按 pxtr 进行监控上报，默认为 true

    `use_packed_item_attr`: [bool] [动态参数] 选填项，是否要求服务端用 packed_item_attr 格式返回 item_attr 以提高数据读写性能，缺省时会优先尝试使用 packed_item_attr 格式，若获取数据失败会取消 packed_item_attr 格式的使用

    `infer_output_type`: [int] 选填项，要求 tower infer server 用指定的 output_type（与服务端 [fetch_tower_remote_pxtr](https://dragonfly.corp.kuaishou.com/#/api/embed_calc?id=fetch_tower_remote_pxtr) 的 output_type 配置功能相同）返回 pxtr 数据，默认为 -1

    `use_sample_list_attr_flag`: [bool] 选填项，是否使用 sample_list 服务获取的 common attrs，默认为 false

    `sample_list_common_attr_key`: [string] 选填项，从指定的 string_list common attr 中获取 sample_list attr 所在的 attr name 列表

    `sample_list_ptr_attr`: [string] 选填项，从指定的 kuiba::PredictItem 类型的 ptr common attr 中获取 sample_list attt, 若与 sample_list_common_attr_key 同时配置，会取并集

    `flatten_sample_list_attr`: [bool] 选填项，是否使用压缩格式发送 sample_list 服务获取的 common attrs，默认为 false

    `flatten_sample_list_attr_to`: [string] 选填项，将 flatten 后的 sample_list attr 以指定的 attr name 发送，仅当 flatten_sample_list_attr=true 时有效，默认为 "kuiba_user_attrs"

    `ttl_seconds`: [int] 选配项，在创建 request 的时候，底层默认会复用 protobuf 的对象空间，如果发生像 UserInfo 一样长期复用导致内存无限增长的情况，可通过该项配置来定期清理内存空间，默认值为 3600

    `random_shift_window`: [int] 选配项，清理 request 会每隔 ttl_seconds 集中在 random_shift_window 时间段，该值过小会使部分服务稳定性抖动较大，默认值为 120

    调用示例
    ------
    ``` python
    .delegate_enrich(
      kess_service = "grpc_KrpCommonLeafTest",
      send_item_attrs = ["pId", "aId"],
      send_common_attrs = ["uId"],
      recv_item_attrs = ["ctr"],
      request_type = "default",
      use_packed_item_attr = True,
    )
    # 如果有 attr 需要改名，或设 readonly
    .delegate_enrich(
      kess_service = "grpc_KrpCommonLeafTest",
      send_item_attrs = ["pId", "aId"],
      send_common_attrs = [{"name": "user_id", "as": "uId", "readonly": True}],
      recv_item_attrs = [{"name": "ctr", "as": "pctr"}],
      request_type = "default",
      use_packed_item_attr = True,
    )
    # 访问 krp-infer-server 可能需要配置从 sample_list, request 获取发送的 common_attr
    .delegate_enrich(
      kess_service = "grpc_KrpCommonLeafTest",
      send_item_attrs = ["pId", "aId"],
      send_common_attrs = [{"name": "user_id", "as": "uId"}],
      send_common_attrs_in_request = True,
      exclude_common_attrs = ["userFollowList"],
      recv_item_attrs = ["ctr"],
      request_type = "default",
      use_packed_item_attr = True,
    )
    ```
    """
    self._add_processor(CommonRecoDelegateEnricher(kwargs))
    return self

  def delegate_with(self, **kwargs):
    """
    DEPRECATED! 已更名为 delegate_enrich()
    """
    return self.delegate_enrich(**kwargs)

  def delegate_retrieve(self, **kwargs):
    """
    CommonRecoDelegateRetriever
    ------
    调用另一个 CommonLeaf 进行召回，并填充结果。

    参数配置
    ------
    `kess_service`: [string] [动态参数] 调用的 CommonLeaf 的 kess service

    `kess_cluster`: [string] 选填项，调用 CommonLeaf 的 kess cluster，默认为 PRODUCTION

    `kess_group`: [string] [动态参数] 选填项，调用的 CommonLeaf 的 kess_group，默认为 ""

    `shard_num`: [int] 选填项，被调服务的 shard 数，该 processor 会并发请求多个 shard，结果合并，默认为 1。

    `consistent_hash`: [bool] 是否按 user_id 或 device_id 对请求进行一致性 hash 的分发，以保证同一用户的请求始终落在同一索引机器上，默认 False

    `timeout_ms`: [int] [动态参数] 选填项，gRPC 超时时间，默认为 300ms。
    
    `partition_service_kconf`: [string] [动态参数] 分片有状态服务的 kconf 配置，会替代上面所有 kess 相关的配置

    `reason`: [int] 选填项，召回原因，默认使用原 common leaf 返回的召回原因。

    `request_type`: [string] [动态参数] 选填项，请求的 request_type，默认为当前请求的 request_type。

    `request_num`: [int] [动态参数] 选填项，请求的 request_num，默认使用当前请求的 request_num。

    `use_item_id_in_attr`: [string] 选填项，返回的 item_attr 属性名，使用该属性值做为 item 的 key，值类型为int。

    `send_browse_set`: [bool] 选填项，是否在请求中填充 browse_set，默认为 true

    `send_common_attrs_in_request`: [bool] 选填项, 是否将上游发送过来的 request 中的全部 common_attr 发送给下游, 默认为 true

    `exclude_common_attrs`: [list] 选填项, 发送给下游时，需要过滤的 common_attr. 一般配合 send_common_attrs_in_request 使用

    `send_common_attrs`: [list] 选填项，额外发送的 common_attr 列表，默认仅发送 request 中携带的 common_attr，支持对 attr 重命名发送，支持 readonly 特性，不允许被调服务修改。

    `recv_common_attrs`: [list] 选填项，接收的 common attr 列表，默认不接受 common attr，支持对 attr 重命名保存。

    `recv_item_attrs`: [list] 选填项，接收的 item_attr 列表，默认不接收 item_attr，支持对 attr 重命名保存。

    `append_item_attrs`: [list] 选填项，接收的 item_attr 列表，接收的 item_attr 值会被追加到指定的 list 类型 item_attr 中，默认不接收 item_attr，支持对 attr 重命名追加

    `recv_table_columns`: [list] 选填项，接收的 table 和 item_attr 的配置，支持对 table 重命名保存, 在 recv_item_attrs 或 append_item_attrs 已有的情况下不可用。
      - `table_name`: [string] 接收 table 的名称。
      - `as`: [string] 接受 table 的重命名。
      - `columns`: [list]  该 table 接收的 item_attr 列表，格式同 recv_item_attrs 配置项，支持对 attr 重命名保存。

    `recv_return_item_attrs_in_request`: [bool] 选填项, 是否将上游发送过来的 request 中的全部 return_item_attrs 合并入 recv_item_attrs 发送给下游, 默认为 false

    `use_packed_item_attr`: [bool] 是否要求服务端用 packed_item_attr 格式返回 item_attr 以提高数据读写性能，默认为 false

    `sample_list_common_attr_key`: [string] 选填项，sample_list 的 common_attr key，是否发送压缩格式 sample list

    `serialize_sample_list_attrs_to`: [string] 选填项，和 sample_list_common_attr_key 配合使用，发送使用的参数名称，两个选项都不为空才会生效

    调用示例
    ------
    ``` python
    .delegate_retrieve(
      kess_service = "grpc_KrpCommonLeafTest",
      send_common_attrs = ["uId"],
      recv_item_attrs = ["ctr"],
      append_item_attrs = ["tag"],
      request_type = "default",
      use_packed_item_attr = True,
    )
    #有 attr 重命名的情况
    .delegate_retrieve(
      kess_service = "grpc_KrpCommonLeafTest",
      send_common_attrs = [{"name": "uId", "as": "user_id"}],
      recv_item_attrs = [{"name": "emp_ctr", "as": "ctr"}],
      append_item_attrs = [{"name": "game_tag", "as": "tag"}],
      request_type = "default",
      use_packed_item_attr = True,
    )
    #支持 readonly 特性
    .delegate_retrieve(
      kess_service = "grpc_KrpCommonLeafTest",
      send_common_attrs = [{"name": "uId", "readonly": True}],
      recv_item_attrs = [{"name": "emp_ctr", "as": "ctr"}],
      append_item_attrs = [{"name": "game_tag", "as": "tag"}],
      request_type = "default",
      use_packed_item_attr = True,
    )
    # 启用 sample list 压缩格式参数
    .delegate_retrieve(
      kess_service = "grpc_xxx",
      sample_list_common_attr_key = "user_attr_names",
      serialize_sample_list_attrs_to = "user_info_str",
    )
    ```
    """
    self._add_processor(CommonRecoDelegateRetriever(kwargs))
    return self

  def count_reco_result(self, **kwargs):
    """
    CommonRecoCountRecoResultEnricher
    ------
    统计结果集中 item 的数量，并将数目保存至指定的 common_attr 中

    参数配置
    ------
    `save_count_to`: [string] 将 item 数目值保存至指定的 common_attr 中

    调用示例
    ------
    ``` python
    .count_reco_result(save_count_to="item_num")
    ```
    """
    attr_name = kwargs.pop("save_count_to", "")
    if attr_name:
      kwargs["save_result_size_to_common_attr"] = attr_name
    if not kwargs.get("save_result_size_to_common_attr"):
      raise ArgumentError(".count_reco_result() 缺少 save_count_to 参数")
    self._add_processor(CommonRecoCountRecoResultEnricher(kwargs))
    return self

  def serialize_protobuf_message(self, **kwargs):
    """
    CommonRecoProtobufSerializeAttrEnricher
    ------
    从 attr 里读取一个 protobuf Message，将序列化 string 写回到 context。

    参数配置
    ------
    `from_common_attr`: [string] 选配项 从哪个 common attr 获取 Message，一般可指定为 `parse_protobuf_from_string` 接口的 `output_attr` 值

    `from_item_attr`: [string] 选配项 从哪个 item attr 获取 Message，一般可指定为 `parse_protobuf_from_string` 接口的 `output_attr` 值

    `serialize_to_common_attr`: [string] 选配项 将 `from_common_attr` 中的 pb message 序列化成 string 存入指定的 common attr 中。

    `serialize_to_item_attr`: [string] 选配项 将 `from_item_attr` 中的 pb message 序列化成 string 存入指定的 item attr 中。

    调用示例
    ------
    ``` python
    .serialize_protobuf_message(
      from_common_attr = "user_info",
      serialize_to_common_attr = "user_info_str"
    )
    ```
    """
    self._add_processor(CommonRecoProtobufSerializeAttrEnricher(kwargs))
    return self

  def inject_protobuf_bytes(self, **kwargs):
    """
    CommonRecoProtobufInjectBytesEnricher
    ------
    将 protobuf 子 message 的序列化字符串插入到父 message 的序列化字符串中。

    参数配置
    ------
    `from_common_attr`: [string] 选配项 需要写入 pb bytes 的 common attr。 类型为 string common attr

    `from_item_attr`: [string] 选配项 需要写入 pb bytes 的 item attr。 类型为 string item attr

    `append_msgs`: [list] 选配项 填入的 pb bytes 的 attr
      - `common_attr`: [string] 输入的 common attr 的 name
      - `item_attr`: [string] 输入的 item attr 的 name
      - `field`: [int] pb 的 field number
      - `field_type`: [string] pb 的 field type, 默认为 message; 可选值为 string, message, float, double, int, int64

    `output_common_attr`: [string] 选配项 输出的 pb bytes 的 common attr

    `output_item_attr`: [string] 选配项 输出的 pb bytes 的 item attr

    调用示例
    ------
    ``` python
    .inject_protobuf_bytes(
      from_common_attr = "input_user_info_str",
      append_msgs=[
        {
          "common_attr": "user_profile_v1_str",
          "field": 29
        }
      ]
      output_common_attr = "output_user_info_str"
    )
    ```
    """
    self._add_processor(CommonRecoProtobufInjectBytesEnricher(kwargs))
    return self

  def release_protobuf_message(self, **kwargs):
    """
    CommonRecoProtobufReleaseMessageEnricher
    ------
    从 attr 里读取一个 protobuf Message，释放指定的 sub message，配合 build_protobuf 中的 allocated 选项使用。

    注意：释放 sub message 不正确可能会导致 core 或者 内存泄漏。

    参数配置
    ------
    `from_common_attr`: [string] 选配项 从哪个 common attr 获取 Message，一般可指定为 `parse_protobuf_from_string` 接口的 `output_attr` 值

    `from_item_attr`: [string] 选配项 从哪个 item attr 获取 Message，一般可指定为 `parse_protobuf_from_string` 接口的 `output_attr` 值

    `paths`: [list] 需要释放的属性列表，每项值为字符串格式, 为 pb path。在线上使用，请务必只配置需要释放的 message！

    调用示例
    ------
    ``` python
    .release_protobuf_message(
      from_common_attr = "user_info",
      paths = [
        "user_profile_v1"
      ]
    )
    ```
    """
    self._add_processor(CommonRecoProtobufReleaseMessageEnricher(kwargs))
    return self

  def enrich_with_protobuf(self, **kwargs):
    """
    CommonRecoProtobufAttrEnricher
    ------
    从 extra attr 里读取一个 protobuf Message，遍历所有 field 或者指定 field，将结果写回到 context。

    注意：全量读取 protobuf 的 field 将会增加大量延迟，**对线上服务请务必只配置需要使用的 attr**。

    SampleAttr-like 类型
    -----
    该 Processor 给像 SampleAttr 这样的 message 提供了额外的支持，只要 message 符合如下定义，
    都称为 SampleAttr-like 的 message：

    ```protobuf
        // 下面所有等号后的 number 都可以任意指定，不限制。

        enum AttrType { // 任意 enum，名字不限
            // 枚举类型的名字，当该枚举存在时，SampleAttr 里必须有对应的 *_value 字段。
            // AttrType 里至少需要有一个枚举值的名字匹配下面的名字。
            BOOL_ATTR = 0;
            INT_ATTR = 1;
            FLOAT_ATTR = 2;
            STRING_ATTR = 3;
            INT_LIST_ATTR = 4;
            FLOAT_LIST_ATTR = 5;
            STRING_LIST_ATTR = 6;
            // 可以有其他枚举值，会忽略
        };

        // 自定义 kv 属性值
        message SampleAttrLike { // 任意 message，名字不限
            AttrType type = 1; // 必须有 type 字段，是一个枚举类型，约束见 AttrType 的注释

            // bytes/string 类型的 name，int32 类型的 id，二者至少定义一个，用于匹配。
            bytes name = 2;
            int32 id = 3;

            // 下面的是所有支持的类型，后续可以进一步添加更多支持，需要对应 AttrType 里对应的枚举类型存在。
            bool bool_value = 5;  // bool 会以 0/1 int 值存入 context
            int64 int_value = 6;
            float float_value = 7;
            bytes string_value = 8;
            repeated int64 int_list_value = 9;
            repeated float float_list_value = 10;
        }
    ```

    当前系统里满足 SampleAttr-like 的 message 类型包括并不限于：

    * kuiba::SampleAttr
    * kuaishou::log::LabelAttr
    * mix.kuaishou.ad::LabelAttr

    参数配置
    ------
    `from_extra_var`: [string] 从哪个 extra 类型（具体为 pb 指针类型）的 attr 中获取 Message，一般可指定为 `parse_protobuf_from_string` 接口的 `output_attr` 值

    `attrs`: [list] 需要抽取的属性列表，每项值为字符串或 {"name"="xxx", "path"="yyy"} 的 dict 格式。在线上使用，请务必只配置需要使用的 attr！
      - `name`: [string] attr 的名称。
      - `path`: [string] protobuf 结构内数据路径，类似 json path 的句点分隔格式。
      - `skip_unset_field`: [bool] 选配项，对 pb 中未设置过值的非 repeated 字段是否跳过取值，给对应的 attr 留空，默认为 False（将取对应类型的默认值）。
      - `repeat_limit`: [dict] 选配项，只针对 repeated 字段有效，用于限制 repeated 值的选取次数。
          配置格式为一个 dict，key 为 path 的某个子集，例如若 path = "a.b.c"，那么 key 的可选值有 "a", "a.b", "a.b.c"，其它均为非法值；
          value 为针对这个 repeated 字段进行截断的长度，不存在或值为负数，则不作任何操作，为 0 则截断为无数据。
      - `repeat_align`: [bool] 选配项，默认为 false 。 如果设置为 true，则 repeated 类型数据按照 repeat_limit 指定的 path 截断个数进行补填，补填值为 protobuf 缺省值。
      - `sample_attr_name`: [string] 如果 path 指向的 field 是一个 repeated SampleAttr-like 类型，则可通过配置该字段来将其视为一个 map<string, V> 的逻辑结构，抽取指定 name 的某个 SampleAttr-like 值。
      - `sample_attr_name_value`: [int] 如果 path 指向的 field 是一个 repeated SampleAttr-like 类型，则可通过配置该字段来将其视为一个 map<int32, V> 的逻辑结构，抽取指定 name_value 的某个 SampleAttr-like 值。

    `is_common_attr`: [bool] 选配项，是否为 common attr，为 True 时从 common attr 读取 Message，结果存到 common attr，否则从 item attr 读取 Message，结果存到 item attr，默认为 True。

    `pb_msg_max_depth`: [int] 选配项，限制检索深度，当且仅当 attrs 为空时生效，默认不限制，线上服务慎用！

    `serialize_to_attr`: [string] 选配项，若配置则会将 `from_extra_var` 中的 pb message 序列化成 string 存入指定的 common/item attr 中。

    `save_all_fields`: [bool] 选配项，是否保存所有 pb 字段（会同时忽略 `attrs` 配置），注意该功能性能消耗较大，**线上服务慎用**！默认为 False。

    `save_attr_names_to_attr`: [string] 选配项，仅当 save_all_fields=True 时生效，存储所有 attr name 到指定 common attr，默认不存。

    `output_attr_prefix`: [string] 选配项，仅当 save_all_fields=True 时生效，用于给保存的 attr 名增加一个前缀，默认无前缀。

    调用示例
    ------
    ``` python
    .enrich_with_protobuf(
      from_extra_var = "user_info",
      attrs = [
        "gender",
        dict(name="lang", path="language"),
        dict(name="city", path="location.city"),
        dict(name="like_list",
             path="user_profile_v1.like_list.photo_id",
             repeat_limit={"user_profile_v1.like_list": 10}),
      ]
    )
    ```
    """
    self._add_processor(CommonRecoProtobufAttrEnricher(kwargs))
    return self


  def select_list_values(self, **kwargs):
    """
    CommonRecoGatherAttrEnricher

    ------
    根据指定的 index list，从 list attr 中选择部分成员返回。

    参数配置
    ------
    `index_attr`: [string] 从给定的 attr 里读取 int list 或 int 作为 index。当类型为 int list 时，产出的 attr 为 list 类型。当类型为 int 时，产出的 attr 为单值类型

    `list_values`: [list] 待处理的 attr 列表，需要包含 from 和 to 两个字段，从 from 读 list attr，处理之后写回到 to。

    `is_common_attr`: [bool] 选配项，是否为 common attr，为 True 时从 common attr 读取 index_attr 和输入 list，结果存到 common attr，否则从 item attr 读取  index_attr 和输入 list，结果存到 item attr，默认为 True。

    `is_range`: [bool] 选配项，默认为 False, 目前只有 is_common_attr = True 时生效。is_range = True 表示 index_attr 配置是一个范围，尝试从 int list 类型的 common attr 取 [begin, end)

    调用示例
    ------
    ``` python
    .select_list_values(
      index_attr = "index",
      list_values = [
        {"from": "int_list", "to": "sub_int_list"},
        {"from": "float_list", "to": "sub_float_list"},
        {"from": "str_list", "to": "sub_str_list"},
      ],
      is_common_attr=True)
    ```
    """
    self._add_processor(CommonRecoGatherAttrEnricher(kwargs))
    return self


  def enrich_with_json(self, **kwargs):
    """
    CommonRecoJsonAttrEnricher

    ------
    读取一个字符串，作为一个 json 解析并且访问需要的字段。

    参数配置
    ------
    `import_attr`: [string] 从给定字段读取字符串，支持 string 和 string list。

    `attrs`: [list] 需要抽取的属性列表，每项值为 {"name": "xxx", "path": "yyy", "output_type": "some_type"} 的 dict 格式。
      - `name`: [string] 输出 attr 的名称。
      - `path`: [string] json 路径，用点分割。
      - `output_type`: [string] 输出的类型，支持 json_string, json_string_list, int64, double, string, int64_list, double_list, string_list。

    `is_common_attr`: [bool] 选配项，是否为 common attr，为 True 时从 common attr 读取 json 字符串，结果存到 common attr，否则从 item attr 读取 json 字符串，结果存到 item attr，默认为 True。

    调用示例
    ------
    ``` python
    .enrich_with_json(
      import_attr = "json_str",
      attrs = [
        dict(name="some_number", path="path.to.number", output_type="double_list"),
      ],
      is_common_attr=True)
    ```
    """
    self._add_processor(CommonRecoJsonAttrEnricher(kwargs))
    return self


  def enrich_attr_by_tf_func(self, **kwargs):
    """
    CommonRecoTensorFlowAttrEnricher
    ------
    根据自定义的 Python 函数(基于 TensorFlow)进行矩阵运算，输入 item attr 或 common attr

    提示: 可在 func 中调用 tf.print 来 debug

    注意：
    - TensorFlow 版本为 1.12.3
    - 支持 AutoGraph

    参数配置
    ------
    `func`: [function] 定义需要执行的计算图，参数列表代表输入的 attr 名。

    `output_as`: [list] 输出的 attr name，当只有一个输出时可以写成一个 string。

    `compress_common_input`: [bool] 是否 common attr 不做展开，缺省为 False。

    `output_common_attr`: [bool] 是否输出 common attr，缺省为 False。

    `inter_op_parallelism_threads`: [int] 多 Op 并行度，默认为 CPU processor 数量。

    `intra_op_parallelism_threads`: [int] 单 Op 并行度，默认为 CPU processor 数量。

    `use_per_session_threads`: [bool] 是否禁用公共线程池，默认 False。

    关于函数参数名
    ------
    - 参数名按 `__` 分割，最多包含四个部分：
      * 数据来源，支持 user 与 item，不指定时会先尝试在 item attr 里取，取不到则从 common attr 取。
      * 数据类型，支持所有常见数据类型，不指定默认为 float。
      * Attr 名，从给定的 attr 读取输入，必须指定。
      * 长度，当输入为一个 list 的时候需要指定长度，不指定默认为 scalar。

    关于函数默认值
    ------
    函数参数列表中的默认值将作为 attr 不存在或长度不足时的缺省值。

    关于输入的 shape
    ------
    - 对于 item attr，如果是 scalar 输入，item 共有 n 个，则输入 shape 是 (n, )
    - 对于 item attr，如果是长度为 m 的 list 输入，item 共有 n 个，则输入 shape 是 (n, m)
    - 对于 common attr，如果是 scalar 输入，compress_common_input 为 True，则输入 shape 是 (, )
    - 对于 common attr，如果是 scalar 输入，compress_common_input 为 False，item 共有 n 个，则输入 shape 是 (n, )
    - 对于 common attr，如果是长度为 m 的 list 输入，compress_common_input 为 True，则输入 shape 是 (m, )
    - 对于 common attr，如果是长度为 m 的 list 输入，compress_common_input 为 False，item 共有 n 个，则输入 shape 是 (n, m)

    关于输出的 shape
    ------
    - 如果 output_common_attr 为 False，假如 item 共有 n 个，输出 shape 为 (n, ) 时，则结果作为单值写入到 item attr。
    - 如果 output_common_attr 为 False，假如 item 共有 n 个，输出 shape 为 (n, m) 时，则结果作为长度为 m 的 list 写入到 item attr。
    - 如果 output_common_attr 为 True，输出 shape 为 (, ) 时，则结果作为单值写入到 common attr。
    - 如果 output_common_attr 为 True，输出 shape 为 (m, ) 时，则结果作为长度为 m 的 list 写入到 common attr。

    调用示例
    ------
    ``` python
    import tensorflow as tf

    def score_function(item__pKsAvgEmpCtr=0, item__pKsAvgEmpLtr=0, item__pHotShowCnt=0, item__pp_pctr=-1, item__pp_pltr=-1, item__pp_plvtr=-1,
                       item__pUploadTime=0, user__curTimeStamp=0, user__ctr_weight=1, user__ltr_weight=1, user__lvtr_weight=1):
      click_score = item__pp_pctr * 1.0
      like_score = item__pp_pctr * item__pp_pltr
      lview_score = item__pp_pctr * item__pp_plvtr
      day_gap = (user__curTimeStamp - item__pUploadTime) / 86400000
      time_decay = tf.where(day_gap < 0, tf.ones_like(day_gap), 0.99 ** day_gap)
      weighted_emp_score = (item__pKsAvgEmpCtr * user__ctr_weight + item__pKsAvgEmpLtr * user__ltr_weight) * time_decay
      weighted_score = (click_score * user__ctr_weight + like_score * user__ltr_weight + lview_score * user__lvtr_weight) * time_decay
      score = tf.where(item__pp_pctr <= 0,
                       tf.where(item__pHotShowCnt > 50, weighted_emp_score, tf.zeros_like(weighted_emp_score)),
                       weighted_score)
      return score

    .enrich_attr_by_tf_func(func=score_function, output_as="score")
    ```
    """
    func = kwargs.pop("func")
    output_as = kwargs.pop("output_as")

    self._add_processor(CommonRecoTensorFlowAttrEnricher.from_function(func, output_as, **kwargs))
    return self

  def enrich_attr_by_tf_local_predict(self, **kwargs):
    """
    CommonRecoTfLocalPredictEnricher
    ------
    根据 tf kuiba 导出的 saved model 进行计算，从 common attr 中获取值作为输入，模型计算完后输出结果到 item attr 或 common attr 中

    注意事项：
    - 目前模型支持两种方式来指定：1. model_dir 参数：直接指定模型所在目录 2. model_tgz 参数：将模型打包成 tgz 格式后再编码为 base64 写入配置文件中
    - 可以使用 common attr 中 SequenceExample 的 pb 作为输入，该 pb 可以通过 convert_to_tf_sequence_example 来构造
    - 可以使用 common attr 中 kuiba::RawSamplePackage 的 pb 作为输入，该 pb 可以通过 build_raw_sample_package 来构造
    - 如果 common attr 中有 tf_kuiba_update_model_key 的 int 属性且值不为0，则会从 online ps 拉取最新网络参数进行更新，冷启动时必须！！！

    参数配置
    ------
    `model_dir`: [str] 模型所在的目录

    `model_tgz`: [str] 指定模型压缩包，目前只支持 base64:// 的 schema，使用时，需要将直接包含 saved_model.pb、variables 的目录压缩打包成 tgz，然后编码成 base64 放到配置中

    `inputs`: [dict] 输入信息，映射格式为 tensor 别名 -> {"attr_type": "xx", "attr_name": "yy"}，目前只支持用 common attr 中的 pb 作为输入

    `outputs`: [list] 输出的 tensor 别名（非 tensor name），也会作为输出的 item attr 名称

    `use_per_session_threads`: [bool] 是否每个 session 使用独享的计算线程，默认值为 false，即所有 session 共享计算线程池

    `inter_op_parallelism_threads`: [int] 不同 op 间的计算并行度，默认值为 32

    `intra_op_parallelism_threads`: [int] 同一 op 内的计算并行度，默认值为 32

    `tf_kuiba_update_model_key`: [str] 用于控制拉取最新网络参数的 key，默认值为 PULL_MODEL_VARIABLE

    `save_to_common_attr`: [bool] 是否保存输出结果到 common attr 中，默认为 false。大多数情况下此 processor 用于预估 item 侧的值，少部分情况会用于预估 user 侧的值（如双塔模型）

    调用示例
    ------
    ``` python

    inputs = {
      "examples": {
        "attr_type": "pb",
        "attr_name": "tf_sequence_example"
      }
    }
    outputs = [
      "click_probabilities",
      "download_probabilities",
      "effectview_probabilities",
      "enter_profile_probabilities",
      "finish_probabilities",
      "follow_probabilities",
      "forward_probabilities",
      "like_probabilities",
      "shortview_probabilities",
      "watch_time_probabilities"
    ]

    .enrich_attr_by_tf_local_predict(model_dir="./saved_model/111", inputs=inputs, outputs=outputs)
    ```
    """
    self._add_processor(CommonRecoTfLocalPredictEnricher(kwargs))
    return self

  def sort(self, **kwargs):
    """
    CommonRecoScoreSortArranger
    ------
    对结果集按当前 score 或将某个 item_attr 作为 score 进行排序（从高到低）

    参数配置
    ------
    `score_from_attr`: [str | list[str]] 将哪些 item_attr 的值作为 score 用于排序，默认为空（直接用当前的 score 值），为多值时，优先级按照列表从左至右递减。

    `stable_sort`: [bool] 是否使用稳定排序，默认 False

    `partial_sort`: [bool] 是否使用 std::partial_sort ，默认 False

    `partial_num`: [int] partial_sort 时的 middle num

    `desc`: [bool] 是否降序排序，默认 True

    `update_score`: [bool] 可选项 是否更新 result 的 score 字段，默认 True。若使用多个 attr 进行排序则使用第一个 attr 的值更新 score 字段

    调用示例
    ------
    ``` python
    .sort()
    # 或指定用 pctr 的属性值作为分数排序
    .sort(score_from_attr="pctr")
    # 或指定优先用 like_count 值，次优用 pctr 值作为分数排序
    .sort(score_from_attr=["like_cnt", "pctr"])
    ```
    """
    self._add_processor(CommonRecoScoreSortArranger(kwargs))
    return self

  def sort_by(self, attr: Union[str, List[str]], **kwargs):
    """
    CommonRecoScoreSortArranger
    ------
    对结果集按当前 score 或将某个 item_attr 作为 score 进行排序（从高到低）

    参数配置
    ------
    `attr`: [str | list[str]] 将哪个 item_attr 的值作为 score 用于排序，并将其置为 item 的 score 字段值

    `stable_sort`: [bool] 是否使用稳定排序，默认 False

    `partial_sort`: [bool] 是否使用 std::partial_sort ，默认 False

    `partial_num`: [int] partial_sort 时的 middle num

    `desc`: [bool] 是否降序排序，默认 True

    `update_score`: [bool] 可选项 是否更新 result 的 score 字段，默认 True

    调用示例
    ------
    ``` python
    # 指定用 pctr 的属性值作为分数排序
    .sort_by("pctr")
    # 或指定优先用 like_count 值，次优用 pctr 值作为分数排序
    .sort_by(["like_cnt", "pctr"])
    ```
    """
    check_arg(attr, "sort_by() 必须指定一个非空的 attr 名用于排序")
    kwargs["score_from_attr"] = attr
    return self.sort(**kwargs)

  def truncate(self, **kwargs):
    """
    CommonRecoTruncateArranger
    ------
    对结果集进行截断

    参数配置
    ------
    `size_limit`: [int] [动态参数] 截断当前结果集，最多保留前多少个 item，需大于或等于 0

    `backfill_to`: [dict] 选配项，根据配置对 item 进行保量追回，key 为一个 int 类型的 item_attr，value 为截断后该 item_attr 值存在且非 0 的保量数目，注意：保量后可能造成剩余 item 数超过 `size_limit` 大小
    - key: [string] 必须指定为 int 类型的 item_attr 名称用于保量标记，不存在或值为 0 将认为是不需要被保的 item
    - value: [int] [动态参数] 被保 item 的最少数量，如果截断的前 `size_limit` 个中不足要求数量，将从 `size_limit` 之后寻找满足条件的 item 进行补齐

    调用示例
    ------
    ``` python
    .truncate(size_limit=200)
    # 或指定为 common_attr 的动态值
    .truncate(size_limit="{{truncate_size}}")
    # 或指定对 partner_flag != 0 的 item 保量至少 200 个
    .truncate(size_limit=500, backfill_to={"partner_flag": 200})
    ```
    """
    self._add_processor(CommonRecoTruncateArranger(kwargs))
    return self

  def truncate_by_reason(self, **kwargs):
    """
    CommonRecoReasonTruncateArranger
    ------
    **DEPRECATED!!!**

    **请改用 [limit_by_reason](#limit_by_reason)，其截断逻辑更加合理，使用方式更加灵活**

    对结果集大小按各个 reason 进行等比例缩减，且支持保量

    支持两种配置模式：
      1. 配置 `reason_config`：各 reason 最终的保留数目 = `max(全局缩减比例得到的数目, min_survival)`，每个 reason 的 `min_survival` 会覆盖全局默认的 `min_survival`
      2. 配置 `queues`：各 reason 最终的保留数目 = `min(item_size * ratio, limit)`，此时直接覆盖掉全局缩放比例得到的数目

    参数配置
    ------
    `size_limit`: [int] [动态参数] 截断当前结果集最多保留前多少个 item ，需大于或等于 0
      - 如果 `size_limit` 大于 0，那么每个 `reason` 的数目会进行等比例缩减，比例数值 = `size_limit / distance(begin, end)`
      - 如果 `size_limit` 等于 0，那么每个 `reason` 的数目不会进行等比例缩减，这时候会以各个 `reason` 配置的 `min_survival` 或者 `limit` 配置为准

    `min_survival`: [int] [动态参数] 所有 reason 默认保底留下的数目（只对 `reason_config` 配置有效），比如根据全局比例算出来该 reason 保留 1 个，此参数会覆盖成 3 个保底数目

    `reason_config`: [list] 每个 reason 自己的覆盖配置
      - `reason`: [int] reason 值
      - `min_survival`: [int] [动态参数] 每个 reason 自己的保底数目，会覆盖全局默认的 min_survival 保底数目

    `queues`: [list] 每个 reason 可以覆盖配置全局的截断逻辑
      - `reason`: [int] reason 值
      - `ratio`: [double] 该 reason 保留的比例，如果为 1.0 代表获取全部 items
      - `limit`: [int] 该 reason 保留的数目，限制的数目

    调用示例
    ------
    ``` python
    # 模式 1： min survival 方式配置
    .truncate_by_reason(
      size_limit = 500,
      min_survival = 10
    )
    # 模式 2： queue 方式配置
    .truncate_by_reason(
      size_limit = 100,
      queues = [
        {
          "reason": 4,
          "ratio": 1.0,
          "limit": 3,
        },
        {
          "reason": 5,
          "ratio": 1.0,
          "limit": 50,
        }
      ]
    )
    ```
    """
    self._add_processor(CommonRecoReasonTruncateArranger(kwargs))
    return self

  def limit_by_reason(self, **kwargs):
    """
    CommonRecoReasonLimitArranger
    ------
    对结果集大小按 reason 进行等比例缩减或指定数目的保留，且支持保量

    参数配置
    ------
    `size_limit`: [int] [动态参数] 选配项，限制所有 item 的总数目，默认值为 -1
      - 如果 `size_limit` 大于或等于 0，那么每个 `reason` 的数目会进行等比例缩减，比例数值 = `size_limit / item 数目`
      - 如果 `size_limit` 小于 0，那么每个 `reason` 的数目不会进行等比例缩减，将以各个 `reason_config` 中的配置为准

    `reason_limit`: [int] [动态参数] 选配项，限制每个 reason 的 item 数目（只在 size_limit 未配置时生效），默认值为 -1
      - 如果 `reason_limit` 大于或等于 0 ，那么将限制所有 `reason_config` 中未配置的 `reason` 的数目为 `reason_limit` 个
      - 如果 `reason_limit` 小于 0，那么每个 `reason` 的数目不变，以各个 `reason_config` 中的配置为准

    `min_survival`: [int] [动态参数] 选配项，所有 reason 默认保底留下的数目（只对 `reason_config` 配置有效），默认值为 1

    `reason_config`: [list|str] 选配项，每个 reason 的单独配置（优先级更高，忽略缩减比例），`limit` 和 `ratio` 若同时配置，将以较小值为准（即尝试保留更少的 item）。
                                该配置同时支持设置为一个 kconf key，将从 kconf 上读取相应的配置内容（json 类型），格式下述配置一致，但不支持动态参数。
      - `reason`: [int|int_list] [动态参数] 必配项，reason 值，必须大于 0。动态参数的配置生效优先级高于静态参数配置。
      - `limit`: [int] [动态参数] 选配项，该 reason 保留的数目，仅对非负数有效，默认值为 -1
      - `ratio`: [double] [动态参数] 选配项，该 reason 保留的比例，若大于或等于 1.0 则保留该 reason 的全部 item，仅对非负数有效，默认值为 -1.0
      - `min_survival`: [int] [动态参数] 选配项，保证该 reason 截断后的最少数目，会覆盖全局默认的 min_survival 保底数目（注意：该配置可能让最终结果数超出 `size_limit` 的总限制）

    `default_reason_config`: [dict] 为 `reason_config` 中未定义的 reason 配置默认截断参数
      - `limit`: [int] [动态参数] 选配项，该 reason 保留的数目，仅对非负数有效，默认值为 -1
      - `ratio`: [double] [动态参数] 选配项，该 reason 保留的比例，若大于或等于 1.0 则保留该 reason 的全部 item，仅对非负数有效，默认值为 -1.0
      - `min_survival`: [int] [动态参数] 选配项，保证该 reason 截断后的最少数目，会覆盖全局默认的 min_survival 保底数目（注意：该配置可能让最终结果数超出 `size_limit` 的总限制）

    调用示例
    ------
    ``` python
    # 统一配置 reason 截断数目
    .limit_by_reason(
      size_limit = 500,
      min_survival = 10
    )
    # 统一配置 reason 截断数目
    .limit_by_reason(
      reason_limit = 500,
    )
    # 单独配置 reason 截断数目（size_limit 总限制也可以不设）
    .limit_by_reason(
      size_limit = 100,
      reason_config = [
        {
          "reason": 4,
          "ratio": 1.0,
          "limit": 3,
        },
        {
          "reason": 5,
          "ratio": 1.0,
          "limit": 50,
        }
      ]
    )
    # 通过 kconf 单独配置 reason 截断数目
    .limit_by_reason(
      size_limit = 100,
      reason_config = "xxx.yyy.zzz", # xxx.yyy.zzz 为 kconf key, 值为 json 类型
    )
    ```
    """
    self._add_processor(CommonRecoReasonLimitArranger(kwargs))
    return self

  def limit(self, size: Union[int, str], **kwargs):
    """
    CommonRecoTruncateArranger
    ------
    `truncate()` 接口的简化封装版本, 对结果集进行截断

    参数配置
    ------
    `size`: [int] [动态参数] 截断当前结果集，最多保留前多少个 item，需大于或等于 0

    `backfill_to`: [dict] 选配项，根据配置对 item 进行保量追回，key 为一个 int 类型的 item_attr，value 为截断后该 item_attr 值存在且非 0 的保量数目，注意：保量后可能造成剩余 item 数超过 `size_limit` 大小
    - key: [string] 必须指定为 int 类型的 item_attr 名称用于保量标记，不存在或值为 0 将认为是不需要被保的 item
    - value: [int] [动态参数] 被保 item 的最少数量，如果截断的前 `size_limit` 个中不足要求数量，将从 `size_limit` 之后寻找满足条件的 item 进行补齐

    调用示例
    ------
    ``` python
    .limit(200)
    # 或指定为 common_attr 的动态值
    .limit("{{truncate_size}}")
    # 或指定对 partner_flag != 0 的 item 保量至少 200 个
    .limit(500, backfill_to={"partner_flag": 200})
    ```
    """
    kwargs["size_limit"] = size
    self.truncate(**kwargs)
    return self

  def variant(self, **kwargs):
    """
    CommonRecoVariantArranger
    ------
    对结果集按某些属性进行打散。

    ℹ️ 该打散效果可解释性较差，在对性能要求不高的情况下建议优先使用 `diversify_by_rules()`。

    ‼️ **注意:** 若 item 的 decay_score 被降权到 0 或负值，该 item 将被删除！

    执行原理：在指定的滑动窗口内为每个 item 计算打散分，初始情况下所有 item 的打散分为 1.0，每遇到一次重复，则对该 item 的打散分进行降权(decay)，降权的计算即为当前打散分乘以所配置的 decay_rate，最后对所有 item 会参考打散分进行重排序（并不是只按打散分高低排！）

    参数配置
    ------
    `variant_config`: [dict] 具体的打散逻辑
    - `default_decay_window_size`: [int] 打散考虑的窗口大小
    - `default_decay_occurrent_times`: [int] 设置第几次重复出现时开始降权
    - `default_decay_rate`: [double] 降权系数，每次降权时会用当前降权分乘以该系数进行更新，需设置为 (0, 1) 之间的小数，注意不能为 0 ！
    - variant_attr_name: [dict] 必配项，可以针对 variant_attrs 中的各个 attr 制定单独的打散逻辑（不配置则应用上述的 default 配置），key 指定为待打散的 item_attr 名称（支持 int/string/int_list/string_list 类型），value 为与 default_xxx 类似的三项对应设置：
      - `enabled`: [bool] [动态参数] 选配项，是否启用该打散属性，不配置意思是启用该属性，如果为 false 表示不使用该打散属性
      - `decay_window_size`: [int] [动态参数]
      - `decay_occurrent_times`: [int] [动态参数]
      - `decay_rate`: [double] [动态参数] 当 item 无法满足当前打散要求时的降权系数，注意不能为 0 ！
      - `any_of`: [bool] 选配项，当待打散 attr 为 list 类型时，指定 list 中是否任意一个值相同就认为是重复，默认为 False。

    `save_decay_score_to_attr`: [string] 选配项，将打散使用的降权分写入指定的 ItemAttr，为空或不设置时不写入

    `prev_items_from_attr`: [string] 选配项，从指定的 int_list CommonAttr 中读取上一刷的 item_key 用于跨屏打散

    `default_init_decay_score`: [double] 选配项，每个 item 打散前的初始降权分（用于后续乘以 decay_rate），默认为 1.0

    `init_decay_score_from_attr`: [string] 选配项，若非空则优先从指定的 ItemAttr (double 或 int 类型) 中获取值作为各个 item 初始的 decay score，缺省则将使用 1.0 作为初始值

    `allow_item_deletion`: [bool] [动态参数] 选配项，是否允许 item 因打散而被删除，默认为 True (即当 decay_score 被降权到小于或等于 0 时 item 会被自动删除)，如需保证 variant 前后的 item 数量一致请设置该项为 False

    `max_undecay_select`: [int] [动态参数] 选配项，打散分未被降权的 item 最多选取多少个保留进结果集，默认为当前结果集大小（该配置可能会导致结果集输出大小 < 输入大小，且需同时设置 `allow_item_deletion=True` 才会生效）

    `min_select`: [int] [动态参数] 选配项，不管打散分是否被降权，最少选取多少个 item 保留进结果集，默认为当前结果集大小（该配置可能会导致结果集输出大小 < 输入大小，且需同时设置 `allow_item_deletion=True` 才会生效）

    调用示例
    ------
    ``` python
    .variant(
      variant_config = {
        # 按 author_id 打散
        "author_id_attr": {
          # 打散窗口一般设置为一屏或一刷的数目
          "decay_window_size": 7,
          # 设置为 2 表示不允许重复，只能出现 1 次
          "decay_occurrent_times": 2,
          # 打散衰减系数
          "decay_rate": 0.1,
        }
      }
    )
    ```
    """
    self._add_processor(CommonRecoVariantArranger(kwargs))
    return self

  def diversify_by_rules(self, **kwargs):
    """
    CommonRecoDiversityRulesArranger
    ------
    规则引擎，根据打散和强插规则，对结果集的顺序进行调整，保证输出的前 max_satisfied_pick 个结果是当前规则配置下的最优解。

    - 支持 slide 窗口的打散（强插）逻辑: 每 window_size 个结果中最多（最少）出现 max_num (min_num) 个某属性的结果。
    - 支持 top 窗口的打散 (强插) 逻辑: 结果集的前 window_size 个结果中最多 (最少) 出现 max_num (min_num) 个某属性的结果。

    执行原理: 将打散和强插规则区分为不同优先级，针对每个待选位置，检测候选项的规则命中情况，使用贪心算法优先保证高优先级规则被满足。
    - 高优先级与低优先级规则冲突时，高优先级规则生效。
    - 同优先级的打散和强插规则冲突时，配置靠前的规则生效。
    - 同优先级的同类规则(强插规则之间 或 打散规则之间)冲突时，按照输入规则引擎时的原顺序选取候选项。

    算法的时间复杂度：O(kn+kmn+nlogn)，k 是规则的数目，m 是需要生成打散的结果的数目，n是候选集数目

    参数配置
    ------
    `top_priority`: [int] 选配项，设定规则体系的最高优先级，需为 >=0 的整数，默认值为 9。限定 top_priority 后，各规则 priority 属性的可取值范围为 [0, top_priority]。

    `max_satisfied_pick`: [int] [动态参数] 必配项，需为正整数，输出结果集的前 max_satisfied_pick 个结果是根据规则选取出的结果，max_satisfied_pick 之后的结果按照原顺序排序。 不考虑降级情况时，max_satisfied_pick 可设置为单次请求下发的 page_size。

    `perflog_enabled`: [bool] [动态参数] 选配项，是否开启打散计数监控，打散生效计数监控位置在 其他 -> 打散命中规则计数，默认值为 false。

    `prev_items_from_attr`: [string] 选配项，从指定的 int_list CommonAttr 中读取上一刷的 item_key 用于跨屏打散

    `rules`: [list] 必配项，具体的规则配置
    - `enabled`: [bool] [动态参数] 选配项，控制规则是否生效，默认值为 true
    - `attr_name`: [string] 必配项，该条规则依赖的 item_attr (支持 int/str/int_list/str_list 类型)，对于 list 类 attr 使用 any of 的逻辑，不同规则的 attr_name 可重复
    - `window_type`: [string] 选配项，控制该条规则是 slide 窗口还是 top 窗口，可选配置为 "slide" 和 "top", 默认值为 "slide"
    - `window_size`: [int] [动态参数] 必配项，该条规则的窗口大小
    - `max_num`: [int] [动态参数] 选配项，适用于打散规则，控制窗口内最多出现多少个某属性的结果，默认值为 INT_MAX
    - `min_num`: [int] [动态参数] 选配项，适用于强插规则，控制窗口内最少出现多少个某属性的结果，默认值为 0
    - `priority`: [int] [动态参数] 必配项，该条规则的优先级，参数取值范围 [0, top_priority]，取值越大优先级越高，强烈不建议将大部分规则集中于同一优先级
    - `consider_prev_items`: [bool] [动态参数] 选配项，控制规则是否跨屏生效，默认值为 false

    注意事项
    ------
    针对某个 item_attr 配置打散/强插规则时，其所有的取值都会被执行打散/强插。如果只希望对其中的部分取值执行打散/强插，请将不参与打散/强插的取值清空。

    例如，当某个 item_attr 有 0 和 1 两种取值时，如果只需要对取值为 1 的 item 执行打散， 则需要在调用本方法前将 0 值清空。

    调用示例
    ------
    ``` python
    .diversify_by_rules(
      max_satisfied_pick=10,
      top_priority=8,
      rules = [
        # 对作者 id 做滑动窗口 6 出 1 打散
        dict(priority=6, window_size=6, max_num=1, attr_name="author_id_attr"),
        # 在结果集的前 10 个结果中强插 2 个直播短视频， 注意该情况下非直播短视频的 "is_living" 为缺省状态
        dict(priority=2, window_type="top", window_size=10, min_num=2, attr_name="is_living"),
        # 以上规则配置中，第一条规则的优先级高于第二条规则 (6 > 2), 规则引擎会优先保证第一条规则被满足
      ]
    )
    ```
    """
    self._add_processor(CommonRecoDiversityRulesArranger(kwargs))
    return self

  def rotate(self, **kwargs):
    """
    CommonRecoRotateArranger
    ------
    对当前结果集中的 item 进行旋转位置变换，例如：`[1,2,3,4,5]` → `[2,3,4,5,1]`

    该 processor 可直接将尾部 item 提升（或强插）至头部位置。

    配合 Arranger 的公有配置项 `range_start` / `range_end` 可完成将任意位置的连续 N 个 items 强插至第 M 个位置的功能

    参数配置
    ------
    `head`: [int] 类似 C++ 中 `std::rotate` 方法的 `middle` 参数功能，表示将第几个 item 旋转至头部，下标计数从 0 开始；为正数时表示从头正数的第几个，为负数时表示倒数第几个

    调用示例
    ------
    ``` python
    # 将最后一个 item 强插到第一位
    .rotate(head=-1)
    ```
    """
    self._add_processor(CommonRecoRotateArranger(kwargs))
    return self

  def shuffle_list_attr(self, **kwargs):
    """
    CommonRecoShuffleListAttrEnricher
    ------
    对 list 类型的 CommonAttr/ ItemAttr 进行 shuffle，以均匀随机的方式打乱顺序

    参数配置
    ------
    `common_attr`: [string] 指定被 shuffle 的 CommonAttr

    `item_attr`: [string] 指定被 shuffle 的 ItemAttr

    注意：`common_attr` 和 `item_attr` 至少有一项或两项。

    调用示例
    ------
    ``` python
    .shuffle_list_attr(common_attr="follow_list")
    ```
    """
    self._add_processor(CommonRecoShuffleListAttrEnricher(kwargs))
    return self
  
  def get_author_exp_params(self, **kwargs):
    """
    CommonRecoAuthorExpParamEnricher
    ------
    获取作者侧参数系统中的 item 参数

    参数配置
    ------
    `author_tail`: [string] 存储视频尾号的 item_attr

    `src_attr`: [string] 需要从作者侧参数系统读取的参数名称
    
    `dst_attr`: [string] 存储作者侧参数的 item_attr 名称
    
    `attr_type`: [string] 作者侧参数类型，范围是 ['int', 'double', 'string', 'bool'] 

    调用示例
    ------
    ``` python
    .get_author_exp_params(
      author_tail = "author_tail",
      author_exp_params = [
        {
          param_name = "young_low_active_expand_boost_cascade_coeff_author_exp",
          attr_name = "young_low_active_expand_boost_cascade",
          attr_type = 'double'
        },
        {
          param_name = "young_low_active_expand_boost_prerank_coeff",
          attr_name = "young_low_active_expand_boost_prerank",
          attr_type = 'double'
        }
      ]
    )
    ```
    """
    self._add_processor(CommonRecoAuthorExpParamEnricher(kwargs))
    return self

  def shuffle(self, **kwargs):
    """
    CommonRecoShuffleArranger
    ------
    对当前结果集的 item 进行 shuffle，默认以均匀随机的方式打乱顺序

    ?> 提示：可通过公共配置 `range_start`、`range_end` 或 `target_item` 来对某个子集进行 shuffle

    参数配置
    ------
    `weight_attr`: [string] 选配项，根据 int/double 类型 ItemAttr 的权重来 shuffle；若不配置该参数，则采用均匀随机的方式；若指定了该参数，但 item 无该 attr，则不进行 shuffle

    调用示例
    ------
    ``` python
    # 以等概率的方式，均匀的 shuffle
    .shuffle()
    # 以 item 的 sc 属性为概率进行 shuffle，即 sc 值越大，排在头部的概率越高
    .shuffle(weight_attr="sc")
    ```
    """
    self._add_processor(CommonRecoShuffleArranger(kwargs))
    return self

  def partition(self, **kwargs):
    """
    CommonRecoPartitionArranger
    ------
    对当前结果集的 item 按指定的条件进行 partition，将符合目标条件的 item 全部排在结果集头部

    参数配置
    ------
    `target`: [dict] 与通用配置项 target_item 的格式相同，存在多个条件时需要全部满足
      - key: 需要比对的 item_attr 名称（仅支持 int 和 string 类型 item_attr）
      - value: 需要比对的 item_attr 值，支持 list 类型（匹配 list 中任意一个值即可）

    调用示例
    ------
    ``` python
    .partition(
      target={
        "category": "food",
        "tag": [1, 2, 3]
      }
    )
    ```
    """
    self._add_processor(CommonRecoPartitionArranger(kwargs))
    return self

  def force_insert(self, **kwargs):
    """
    CommonRecoStickyArranger
    ------
    根据配置的规则，对 item 进行指定位置的强插或置顶

    参数配置
    ------
    方式一：按 reason 显式指定要强插的位置和数目限制

    `reason`: [int|int_list] [动态参数] 按 reason 强插时指定的 reason 值，可指定单个值，也可指定一个 int_list 对多 reason 同时进行强插，需大于等于 0。

    `position`: [int] [动态参数] 当 reason 被指定时，必须同时设置该值以指定对应的插入位置，下标从 0 开始计数

    方式二：或根据各个 item 的属性值来动态指定强插的位置

    `position_from_attr`: [string] 设置从哪个 int 类型 item_attr 中获取该 item 需要被强插入的位置，下标从 0 开始计数，可缺省


    以上两种方式可同时使用，方式一的优先级高于方式二，且强插总数受下面的 `limit` 参数限制：

    `limit`: [int] [动态参数] 限制强插的 item 数目（包括所有方式的强插）

    `reset_reason`: [int] 将被执行强插的 item 重置为指定的 reason，缺省则不进行重置

    调用示例
    ------
    ``` python
    .force_insert(
      # 在 int item_attr "promote_to_position" 中指定要强插的位置
      position_from_attr = "promote_to_position",
      # 将被强插的 item 的 reason 重置为 666
      reset_reason = 666
    )
    ```
    """
    self._add_processor(CommonRecoStickyArranger(kwargs))
    return self

  def intermix(self, **kwargs):
    """
    CommonRecoIntermixArranger
    ------
    根据自定义规则将不同的 item 按 item_type 或 item_attr 进行交错编排，例如用于视频和直播的混排策略

    参数配置
    ------
    `mix_on_attr`: [string] 用哪个 item_attr (仅支持 int 类型) 的值进行 pattern 匹配，缺省且不用 reason 做匹配的情况下，将使用 item_type 的值

    `mix_pattern`: [list] [动态参数] 指定的 item_type 或 item_attr 值（必须是 int 类型）交错规则，数组中的各项为具体的 item_type 值。示例：`[1, 1, 0]` 表示让结果集中的 item_type 顺序循环满足 `1, 1, 0, 1, 1, 0, ...` 的排列，规则为尽力满足

    `num_limit`: [int] [动态参数] 仅对前 `num_limit` 个 item 按规则进行重排，可缺省则对所有结果集进行处理

    `mix_on_reason`: [bool] 在 mix_on_attr 为空的情况下，是否用 reason 做 pattern 匹配。

    调用示例
    ------
    ``` python
    .intermix(
      mix_on_attr = "category",
      # 1: 视频，0: 直播，该 pattern 表示每 2 个视频跟 1 个直播，无限往复下去
      mix_pattern = [1, 1, 0],
    )
    ```
    """
    self._add_processor(CommonRecoIntermixArranger(kwargs))
    return self

  def leaf_show(self, **kwargs):
    """
    CommonRecoLeafShowObserver
    ------
    根据当前结果集给样本拼接服务发送样本特征和 leaf_show action log。

    启用该 Processor 需设置参数 `enable_leaf_show = True`。

    Request 中携带的 CommonAttr 将被自动发送，无需指定。

    参数配置（方式一：通过 reco_biz 读取 kconf 配置，支持走 kafka 发送，支持发送 action log）
    ------
    `reco_biz`: [string] Kconf 上指定的 recoBiz 名，需联系架构同学创建 kconf key 为 recoBiz.{reco_biz}.krpConfig 的配置项，[kconf 配置示例](https://kconf.corp.kuaishou.com/#/recoBiz/acfunLite/krpConfig)

    `kafka_topic`: [string] 发送 leaf show 至 kafka 的 topic, producer_type 为 kafka 时必填

    `kafka_user_params`: [string] 发送 leaf show 至 kafka 的 user_parmas, 默认值为空

    `action_type`: [string] 选配项，指定发送 action log 的 action type 值，默认为 "leaf_show"

    `action_value`: [string] 选配项，指定 action log 的 value 所在的 ItemAttr 名称（仅支持 int 类型）进行 value 填充

    `action_log_common_attrs`: [list] 选配项，可通过该项配置指定把哪些 CommonAttr 发送给 actionLogConsumer 服务

    `action_log_item_type`: [int] [动态参数] 选配项，可以设置 action_log 中 item_type 值, 默认值为 0, 具体值意义请参照: ks.platform.ItemTypeEnum.ItemType

    参数配置（方式二：通过 biz_name 读取本地配置，不支持发送 action log）
    ------
    `biz_name`: [string] 需要与样本拼接服务中设置的 biz_name 对应。biz_name 将与 shard_no 拼接在一起作为打入的 bt_queue name

    `group`: [string] 需要打入的样本拼接服务的输入 bt_queue group

    `use_new_btq_client`: [bool] 是否使用新 btq_client 进行发送

    `bt_queue_name`: [string] 强制发送到指定的 btq 队列名

    `shard_num`: [int] 样本拼接服务的 shard_num，默认值为 1，小业务一般不用设置

    `use_request_id`: [bool] 是否只使用 request_id 作为拼接的 common key 进行发送，默认为 FLAG leaf_show_use_request_id 的值（默认 false）

    `use_item_id`: [bool] 是否使用 item_id 作为拼接的 item key 进行发送，默认为 FLAG leaf_show_use_id 的值（默认 false）

    参数配置（各方式共有）
    ------
    `enable_leaf_show`: [bool] 是否启用发送 leaf show 的功能，对于线上环境需要显式开启，默认值为 flag common_reco_enable_leaf_show 的值（默认 false）

    `respect_request_num`: [bool] 是否尊重 request 中的 request_num 值，默认值为 true；打 leaf show 的目标是实际输出的 items，所以默认情况下最多只会从 range_start 位置开始选取至多 request_num 个发送 leaf show，如果有特殊需求可将该值设为 false 并通过 range_start 和  range_end 来人工设置需要发送 leaf show 的 item 范围

    `producer_type`: [string] 发送 leaf show 使用的 producer 类型， 使用 kafka 时填写 `kafka`，使用 btq 时，可不填写，默认值为 btq

    `relative_type`: [int] relative_id 的类型值，不需要可不设置

    `relative_id`: [string] relative_id 所在的 ItemAttr 名称，一般用于设置 item 的 owner_id/author_id，不需要可不设置

    `attrs`: [list] 需要发送的 ItemAttr 名称列表，注意：此列表只用包含 item 侧特征，从 request 携带来的 CommonAttr 中的 user 侧特征会自动发送，无需配置！

    `as_extra_attrs`: [bool] 是否将 item 的所有 `attrs` 特征值作为样本拼接的 extra_attrs 发送, 默认 false

    `send_one_request`: [bool] 是否将 item 的所有 `attrs` 特征值与 common_attr 放到一个 sample_request 里发送, 使用 bigriver 拼接时需要开启, 其他拼接引擎不用开启, 默认 false

    `exclude_common_attrs`: [list] 默认情况会把 request 中带来的 CommonAttr 全部作为 User 侧特征进行发送，可通过该项配置指定哪些 Attr 不进行发送，array 中的每项值为属性名，可缺省

    `extra_common_attrs`: [list] 默认情况会把 request 中带来的 CommonAttr 全部作为 User 侧特征进行发送，可通过该项配置指定哪些中间生成的 CommonAttr 也进行发送，可缺省

    `use_item_key_from_attr`: [string] 选配项，若配置了则使用 item_attr 作为拼接发送的 item key, 若配置了该项的同时配置了 use_item_id 则实际拼接的是 item attr 的低 56 位

    `use_device_id`: [bool] 是否只使用 device_id 作为拼接的 common key 进行发送，优先级低于 `use_request_id` 配置项，默认为 false

    `send_item_base_info`: [bool] 是否将 item_id、item_type、item_key 三个 item 基础数据作为同名的 item 特征发送给样本拼接（默认 true）

    `send_item_reason`: [bool] 当 send_item_base_info 配置为 True 时，是否额外发送 reason 基础数据作为同名的 item 特征发送给样本拼接（默认 false）

    `attr_name_transform_map`: [dict] 某些情况下样本拼接中设置的 user 或 item 特征名称和 leaf 里的 CommonAttr / ItemAttr 名称不一样，可通过该项配置对属性名进行重命名转换
      - item_attr_name_in_leaf: 映射关系的 key 值为 leaf 里的 attr 名称，value 值为样本拼接服务中使用的特征名称

    `attr_name_duplicate_map`: [dict] 与 attr_name_transform_map 配置类似，这里不是转换某个 ItemAttr 的名称，而是复制一个 ItemAttr 并以新名称命名，新老两个 ItemAttr 都会进行发送
      - item_attr_name_in_leaf: key 值为需要复制的 ItemAttr 名称，value 为新复制的 ItemAttr 名称

    `include_common_attrs_from_request`: [bool] 选配项, 发送样本时包含 request 中的所有 common attr, 默认为 true, 此时 exclude_common_attrs 生效; 当配置为 false 时则 request 中所有的 common attr 都不会被发送

    `include_sample_list_user_info`: [bool] 是否额外获取 SampleList 服务的 UserAttr 并发送, 默认值为 False

    `send_other_user_info_if_sample_list_missing`: [bool] 选配项，根据 include_sample_list_user_info 获取 SampleList 服务的 UserAttr 失败后是否继续加入 request 中的 common attr 作为 UserAttr，默认值为 False

    `forward_predict_item_from_attr`: [string] 选配项，从指定的 ItemAttr 中获取 kuiba::PredictItem 对象指针进行序列化发送拼接服务，用于透传 item 特征（注意：配置该项的情况下不会发送 CommonAttr 等 user 侧特征）

    `sample_rate`: [double] 选配项，对结果集范围内的 item 按指定概率进行采样，决定是否发送样本特征和 action log，需要是在 [0, 1] 范围的小数，代表采样比率，默认值为 1，代表不采样

    `btq_push_timeout_ms`: [int] 选配项，样本发送到 btq 的超时时间，单位 ms，小于等于 0 采用的是 btq 默认的超时时间，默认值为 -1，走 BTQ 的默认延迟

    调用示例
    ------
    ``` python
    .leaf_show(
      enable_leaf_show = True,
      biz_name = "YOUR_BIZ_NAME",
      bt_queue_name = "YOUR_BT_QUEUE_NAME",
      attrs = [
        "photoLevel",
        "photoShowCnt",
        "photoClickCnt",
        "photoPlayCnt",
        "photoLikeCnt",
        "tower_pctr",
        "tower_pltr",
        "tower_pwtr",
      ],
      attr_name_transform_map = {
        "user_id": "uId",
        "device_id": "dId",
      }
    )
    ```
    """
    self._add_processor(CommonRecoLeafShowObserver(kwargs))
    return self

  def write_to_stdout(self, **kwargs):
    """
    CommonRecoStdoutObserver
    ------
    将指定的 CommonAttr 写入到 stdout。（仅支持 String/String List CommonAttr 和 PB Message 指针类型的 Extra CommonAttr）

    参数配置
    ------
    `common_attr`: [string] 需要写到 stdout 的 attr name。

    `output_file`: [string] 需要写入的文件路径，不为空时不会写 stdout, 默认为空

    `base64_encode`: [bool] 对于 string 类型是否做 base64 编码，Protobuf Message 强制 base64 编码，默认 false。

    调用示例
    ------
    ``` python
    .write_to_stdout(common_attr = 'user_info')
    ```
    """
    self._add_processor(CommonRecoStdoutObserver(kwargs))
    return self

  def send_with_btq(self, **kwargs):
    """
    CommonRecoBTQueueObserver
    ------
    将指定的 Common/Item Attr 通过 BTQueue 发送出去。（仅支持 String Attr, String List Attr 和 PB Message 指针类型的 Extra Attr）

    参数配置
    ------
    `common_attr`: [string] 需要通过 BTQueue 发送的 common attr name。

    `item_attr`: [string] 需要通过 BTQueue 发送的 item attr name, 与 common_attr 只能选填一个。

    `queue_name`: [string] BTQueue topic name 或 topic 前缀。

    `with_shard`: [bool] 是否分 shard 发送，如果分 shard，shard 将作为后缀加到 queue name 后面，默认为 False。

    `shard_num`: [int] 当且仅当 with_shard 为 True 时生效，分 shard 的数量。

    `hash_attr`: [string] 当且仅当 with_shard 为 True 时生效。 当发送 common_attr 时，应填写 common_attr , 当发送 item_attr 时，应填写 item_attr。 根据指定 attr 来分 shard，仅支持 string 和 int 类型，string 时计算 city hash 对 `shard_num` 取模得到 shard，int 时直接对 `shard_num` 取模得到 shard。当这个配置为空（默认为空）或指定 attr 不存在时则通过伪随机数分 shard。

    `shard_offset`: [int] 当且仅当 with_shard 为 True 时生效，最小的 shard 的 id，默认为 0。

    `use_raw_btq_client`: [bool] 是否使用原始的btq_client，BufferedBtqProducer在某种情况下python调用退出时会导致core，默认为 false。

    `btq_timeout_ms`: [int] 使用原始的btq_client时，btq send的超时设置，默认50ms。

    调用示例
    ------
    ``` python
    .send_with_btq(
      common_attr = 'user_info',
      queue_name = 'some_topic_you_owned')
    ```
    """
    self._add_processor(CommonRecoBTQueueObserver(kwargs))
    return self

  def send_with_kafka(self, **kwargs):
    """
    CommonRecoKafkaObserver
    ------
    将指定的 CommonAttr 通过 Kafka 发送出去。（仅支持 String CommonAttr 和 PB Message 指针类型的 Extra CommonAttr）

    参数配置
    ------
    `common_attr`: [string] 需要通过 Kafka 发送的 attr name。

    `topic_name`: [string] Kafka topic name

    `user_params`: [string] custom kafka user_params

    `kafka_tag`: [string] [动态参数] 启用kafka Tag过滤功能，指定 attr 携带的 kafka Tag，每个 attr 只能携带一个 tag，每个物理 topic 最多支持 31 种 tag。

    调用示例
    ------
    ``` python
    .send_with_kafka(
      common_attr = 'user_info',
      topic_name = 'some_topic_you_owned')
    ```
    """
    self._add_processor(CommonRecoKafkaObserver(kwargs))
    return self

  def export_attr_to_kafka(self, **kwargs):
    """
    CommonRecoAttrExportObserver
    ------
    将指定的 CommonAttr 或 ItemAttr 以 plain json string 的格式发送至 Kafka。

    可配置每个 item 一条 json 数据，且各自持有一份重复的 CommonAttr 数据，可直接导入 hive 或 clickhouse 用于后续数据分析。

    也可配置所有 item 拼接成一条 json 数据，且共享一份同样的 CommonAttr 数据，可直接导入 hive 或 clickhouse 用于后续数据分析。

    参数配置
    ------
    `kafka_topic`: [string] Kafka topic name

    `user_params`: [string] 选配项，kafka producer 的 user_params 参数

    `common_attrs`: [list] 选配项，需要发送的 CommonAttr 列表

    `item_attrs`: [list] 选配项，需要发送的 ItemAttr 列表，注意：若缺省则不会发送 item 的任何数据

    `abtest_worlds`: [list] 选配项，需要发送有关 abtest 实验组命中相关信息的世界名列表

    `check_point`: [string] 选配项，额外发送一个名为 __check_point 的自定义字段值

    `single_json`: [bool] 选配项，是否将所有 item 拼接成一条 json 数据进行发送，默认值 False，对 item 量较大的场景建议设置为 True

    `wrap_common_attrs`: [bool] 选配项，是否将所有 common_attr 集中放置在 "__common_attrs" 字段下，默认为 False（即平铺在 json 顶层）

    调用示例
    ------
    ``` python
    .export_attr_to_kafka(
      kafka_topic="your_kafka_topic",
      common_attrs=["city", "like_list"],
      item_attrs=["pctr", "pltr"],
    )
    ```
    """
    self._add_processor(CommonRecoAttrExportObserver(kwargs))
    return self

  def log_debug_info(self, **kwargs):
    """
    CommonRecoDebugInfoObserver
    ------
    打印指定的 CommonAttr / ItemAttr / Abtest 分组等信息到本地的 INFO log 中

    注意：为保证线上服务的性能及日志量大小，若 `for_debug_request_only=True` 或缺省，那么该 Processor 只会对 debug request 进行日志打印

    提示：若打印出的 attr 名称末尾带 "*" 标记表示这是个只读的 attr。若打印出的 item attr 名称末尾带 "?" 标记表示其实无值，生效的是预先设置的默认值。

    提示：对于本地临时性的 debug info 输出可以用配置更简便的封装版本 [debug_log()](#debug_log)，默认关闭了防御性配置

    参数配置
    ------
    `log_tag`: [string] 自定义标记内容，会一同被打印到日志中，方便定位查找，以及区分不同地方的 debug log

    `common_attrs`: [list] 需要打印的 CommonAttr 名称列表，可缺省

    `item_attrs`: [list] 需要打印的 ItemAttr 名称列表，可缺省

    `item_num_limit`: [int] 最多打印多少个 item 的 ItemAttr，默认值为 10

    `to`: [string] 输出的位置，支持 glog、stdout、stderr、file，默认值为 glog。注意：glog 对单次日志的长度有限制，若因内容太多被截断展示不全，可改成输出到 stdout/stderr/file 来避免。

    `to_file_folder`: [string] 当 `to="file"` 时，需要提供该参数，用于指定 debug info 输出文件所在的目录

    `to_file_name`: [string] [动态参数] 当 `to="file"` 时，需要提供该参数，用于指定 debug info 输出文件名，默认为 debug_info.log

    `append_to_file`: [bool] 当 `to="file"` 时，该参数有效，用于指定 debug info 写入日志文件的方式，默认为 False，表示覆盖写入文件；如果配置成 True，表示追加写入文件

    `for_debug_request_only`: [bool] 是否只在 request 的 debug 字段为 true 时才执行该 Processor 逻辑，默认值为 true，如需为非 debug request 打印 debug 信息可设置该值为 false（如果你设置的 attrs 数目特别多，将会刷屏线上日志，请谨慎设置）

    `respect_sample_logging`: [bool] [动态参数] 是否遵守全局按用户维度的打印采样，若为 False 则每条 debug info 必打，默认值为 True

    `shadow_mode`: [bool] 是否开启影子模式，以该模式加入 flow 中执行将不会对流程的 DAG 产生任何影响（不会参与 attr 依赖分析），开启后需要自己保证观察点所处位置的异步数据存在性，默认为 False

    `abtest_worlds`: [list] 需要打印哪些世界的 abtest 分组信息，array 的每项值为世界的名称，可缺省

    `trace_user_ids`: [list] [动态参数] 需要追踪的 user_id 列表，如果该列表不为空则只会针对列表中的 user_id 执行该 Processor

    `trace_device_ids`: [list] [动态参数] 需要追踪的 device_id 列表，如果该列表不为空则只会针对列表中的 device_id 执行该 Processor

    `trace_item_keys`: [list] 需要追踪的 item_key 列表，如果该列表不为空则只会针对 item_key 出现在列表中的 item 进行 debug 信息打印。注：若该配置不为空 item_num_limit 将不生效

    `print_all_item_keys`: [bool] 是否打印出当前 range_start 和 range_end 中的所有 item_key，默认值为 false

    `print_all_common_attrs`: [bool] 是否打印出当前存在所有的 CommonAttr 数据（但不影响数据依赖分析），默认值为 false。**注意：功能可能会显著影响性能，请勿在线上服务使用！**

    `print_all_item_attrs`: [bool] 是否打印出当前存在所有的 ItemAttr 数据（但不影响数据依赖分析），默认值为 false。**注意：功能可能会显著影响性能，请勿在线上服务使用！**

    `print_browse_set`: [bool] 是否打印 browse set 中的所有 item，默认值为 false

    调用示例
    ------
    ``` python
    .log_debug_info(
      common_attrs = [
        "user_id",
        "gender",
      ],
      item_attrs = [
        "photo_real_show_attr",
        "photo_click_attr",
        "photo_like_attr",
        "photo_comment_attr",
        "photo_forward_attr",
      ]
    )
    ```
    """
    self._add_processor(CommonRecoDebugInfoObserver(kwargs))
    return self

  def debug_log(self, **kwargs):
    """
    CommonRecoDebugInfoObserver
    ------
    便捷性优先而非安全性优先的 [log_debug_info 接口](#log_debug_info) 封装版本，不需要设置 for_debug_request_only=False 即可生效打印，方便在 playground 等测试场景使用

    参数配置
    ------
    在 [log_debug_info()](#log_debug_info) 的配置基础上额外扩充了一些易用的配置项。

    `log_tag`: [string] 自定义标记内容，会一同被打印到日志中，方便定位查找，以及区分不同地方的 debug log

    `common_attrs`: [list] 需要打印的 CommonAttr 名称列表，可缺省

    `item_attrs`: [list] 需要打印的 ItemAttr 名称列表，可缺省

    `item_num_limit`: [int] 最多打印多少个 item 的 ItemAttr，默认值为 10

    `print_all`: [bool] 是否打印存在的全部 common attr 和 item attr，默认 False

    调用示例
    ------
    ``` python
    .debug_log(
      common_attrs = [
        "user_id",
        "gender",
      ],
      item_attrs = [
        "photo_real_show_attr",
        "photo_click_attr",
        "photo_like_attr",
        "photo_comment_attr",
        "photo_forward_attr",
      ]
    )
    ```
    """
    kwargs["for_debug_request_only"] = False
    if "item_attrs" not in kwargs:
      kwargs["item_attrs"] = [""]
    if kwargs.get("print_all", False):
      kwargs["print_all_common_attrs"] = True
      kwargs["print_all_item_attrs"] = True
    self.log_debug_info(**kwargs)
    return self

  def perflog_reason_count(self, **kwargs):
    """
    CommonRecoReasonCountPerflogObserver
    ------
    统计当前各个 item reason 元数据的数目，上报 perflog 并可在 [grafana 监控](https://grafana.corp.kuaishou.com/d/S10M9QdZk/commonleafjian-kong?orgId=3&panelId=275&fullscreen)中查看统计结果

    注意：如果要监测多个点，请用 `check_point` 区分，否则会合并统计数据

    参数配置
    ------
    `check_point`: [string] [动态参数] 自定义打点位置标识，将用于 perflog 聚合和 grafana 展示

    `log_info_from_attr`: [string] 选配项，需要额外上报的 common attr 信息，仅支持 int/string 类型。

    调用示例
    ------
    ``` python
    # 统计 default 流程中的召回后 reason 分布
    .perflog_reason_count(check_point="default.retrieval")
    ```
    """
    self._add_processor(CommonRecoReasonCountPerflogObserver(kwargs))
    return self

  def perflog_attr_value(self, **kwargs):
    """
    CommonRecoAttrValuePerflogObserver
    ------
    对 attr 的统计值上报 perflog

    注意：如果要监测多个点，请用 `check_point` 区分，否则会合并统计数据

    参数配置
    ------
    `check_point`: [string] [动态参数] 自定义打点位置标识，将用于 perflog 聚合和 grafana 展示

    `item_attrs`: [list] 需要统计的 item_attr 名称列表

    `common_attrs`: [list] 需要统计的 common_attr 名称列表

    `aggregator`: [string] 聚合方式，可选值 avg/sum/min/max/count，默认为 "avg"。
      - avg: 支持 int/double/int_list/double_list 类型的 attr（list 数据先对内求均值得到单值后再参与整体统计），上报均值，监控图位置：【Attr 相关】->【[Attr 均值统计](https://grafana.corp.kuaishou.com/d/S10M9QdZk/commonleafjian-kong?orgId=3&fullscreen&panelId=1690)】
      - sum: 支持 int/double/int_list/double_list 类型的 attr（list 数据先对内求均值得到单值后再参与整体统计），上报求和值，监控图位置：【Attr 相关】->【[Attr SUM 值统计](https://grafana.corp.kuaishou.com/d/S10M9QdZk/commonleafjian-kong?orgId=3&fullscreen&panelId=4606)】
      - min: 支持 int/double/int_list/double_list 类型的 attr（list 数据先对内求最小值得到单值后再参与整体统计），上报最小值，监控图位置：【Attr 相关】->【[Attr 最小值统计](https://grafana.corp.kuaishou.com/d/S10M9QdZk/commonleafjian-kong?orgId=3&fullscreen&panelId=4608)】
      - max: 支持 int/double/int_list/double_list 类型的 attr（list 数据先对内求最大值得到单值后再参与整体统计），上报最大值，监控图位置：【Attr 相关】->【[Attr 最大值统计](https://grafana.corp.kuaishou.com/d/S10M9QdZk/commonleafjian-kong?orgId=3&fullscreen&panelId=4607)】
      - count: 支持 int/string 类型的 attr 不同值数量统计，监控图位置：【Attr 相关】->【[Attr 数量 Count 统计](https://grafana.corp.kuaishou.com/d/S10M9QdZk/commonleafjian-kong?orgId=3&fullscreen&panelId=3915)】
      - length: 支持 int/double/string/int_list/double_list/string_list 类型的 attr，上报 attr 的长度（int/double 为 1，string/int_list/double_list/string_list 为 C++ size() 函数）

    `local`: [bool] 选配项，统计结果直接写到本地 perflog.log 文件而不是上传到 clickhouse，默认为 False。

    `perf_base`: [int] 打点时的放大倍数，用于保留小数点，默认为 1000000L，注意修改这个值会导致 Grafana 上打点显示异常，使用时需要注意。

    `namespace`: [string] 默认为 "common.leaf"。

    调用示例
    ------
    ``` python
    # 统计 default 流程中排序阶段的各个 pxtr 均值
    .perflog_attr_value(
      check_point="default.ranking",
      item_attrs=["pctr", "pltr", "pftr"],
      common_attrs=["tab_id"],
    )
    ```
    """
    self._add_processor(CommonRecoAttrValuePerflogObserver(kwargs))
    return self

  def perflog_processor_metrics(self, **kwargs):
    """
    CommonRecoAttrValuePerflogObserver
    ------
    此 perflog 信息可以同时在可视化网站上查看。在当前 processor 上展示。 配置与 perflog_attr_value 完全一致。

    调用示例
    ------
    ``` python
    # 统计 default 流程中排序阶段的各个 pxtr 均值
    .perflog_processor_metrics(
      check_point="default.ranking",
      item_attrs=["pctr", "pltr", "pftr"],
      common_attrs=["tab_id"],
    )
    ```
    """
    kwargs["metric_type"] = "processor"
    self._add_processor(CommonRecoAttrValuePerflogObserver(kwargs))
    return self

  def perflog_module_metrics(self, **kwargs):
    """
    CommonRecoAttrValuePerflogObserver
    ------
    此 perflog 信息可以同时在可视化网站上查看。在当前 processor 所属模块上展示。 配置与 perflog_attr_value 完全一致。

    调用示例
    ------
    ``` python
    # 统计 default 流程中排序阶段的各个 pxtr 均值
    .perflog_module_metrics(
      check_point="default.ranking",
      item_attrs=["pctr", "pltr", "pftr"],
      common_attrs=["tab_id"],
    )
    ```
    """
    kwargs["metric_type"] = "module"
    self._add_processor(CommonRecoAttrValuePerflogObserver(kwargs))
    return self

  def parse_protobuf_from_string(self, **kwargs):
    """
    CommonRecoProtobufParseAttrEnricher
    ------
    从当前的 context 里取出 String，转成 Protobuf 存回 context 中。

    参数配置
    ------
    `is_common_attr`: [bool] 是否为 common attr，默认为 True。

    `input_attr`: [string] 输入的 string 类型的 attr 的 name。

    `output_attr`: [string] 输出的 protobuf 类型的 attr 的 name。

    `class_name`: [string] 转换的 protobuf 的类型，当前支持所有链接的 Message 类型。

    `ttl_seconds`: [int] 选配项，默认底层会复用 protobuf 的对象空间，如果发生像 UserInfo 一样长期复用导致内存无限增长的情况，可通过该项配置来定期清理内存空间。默认值为 -1，表示 pb 内存将持续复用。 注意：若值为 0 则表示每次都清理。

    `use_dynamic_proto`: [bool] 选配项，是否使用 proto 动态注册，默认为 False

    调用示例
    ------
    ``` python
    .parse_protobuf_from_string(
      input_attr="user_info_str",
      output_attr="user_info",
      class_name="ks.reco.UserInfo",
    )
    ```
    """
    kwargs["class_name"] = kwargs["class_name"].replace("::", ".")
    self._add_processor(CommonRecoProtobufParseAttrEnricher(kwargs))
    return self

  def concat_attr(self, **kwargs):
    """
    CommonRecoConcatAttrEnricher
    ------
    用户侧: input_common_attrs 列表中的 attr 内容合并，产生的结果存放到新的 attr(output_common_attr)

    item 侧: input_item_attrs 列表中的 attr 内容合并，产生的结果存放到新的 attr(output_item_attr)

    也可以存储原始 attr 的名称，用户侧/ item 侧，分别存储到 output_common_attr_name / output_item_attr_name

    原始 attr 的名称默认存储为 vector<string> ,也可通过设置 output_attr_name_save_as_int , 存储成 vector<int>

    注意: 拼接类型暂只支持[ string,double,int,string_list,double_list,int_list ], 且不支持用户/ item 混合拼接

    目前不支持 用户侧 和 item 侧 attr 的混合

    使用场景：在特征抽取完之后，想要查看模型底层的 concat embedding; slot=>feature 拼接

    参数配置
    ------
    `input_common_attrs`: [list] 选配项，用户侧待拼接 attr 列表

    `input_item_attrs`: [list] 选配项，item 侧待拼接 attr 列表【如 input_common_attrs 未配置，该参数必须配置】

    `output_common_attr`: [string] 选配项，输出的用户侧 attr name。 需要 input_common_attrs 同时设置。为空不输出

    `output_item_attr`: [string] 选配项，输出的 item 侧 attr name。 需要 input_item_attrs 同时设置。为空不输出

    `output_common_attr_name`: [string] 选配项，用户侧，在拼接过程中，原始 attr 名称对应关系存储的 attr 。 为空不输出

    `output_item_attr_name`: [string] 选配项，item 侧， 在拼接过程中 原始 attr 的名称对应关系存储的 attr 。 为空不输出

    `output_attr_name_save_as_int`: [bool] 选配项，是否把原始 attr 的名称转换为 int 输出。 true 则 output_common_attr_name / output_item_attr_name 输出的 attr 类型为 vector<string> , 如果 false , 则转换成 vector<int> 进行存储。 默认 false

    注意事项
    ------
    各个参数都可以选配，但是如果设置了 input_common_attrs 那么 output_common_attr / output_common_attr_name 至少需配置一个

    同理，如果设置了 input_item_attrs 那么 output_item_attr / output_item_attr_name 至少配置一个

    调用示例
    ------
    ``` python
    .concat_attr(
      input_common_attrs = [
        "13", "48"
      ],
      output_common_attr = "concat_common",
      output_common_attr_name = "conat_common_name",
      output_attr_name_save_as_int=True
    )

    .concat_attr(
      input_item_attrs = [
        "131", "148"
      ],
      output_item_attr = "concat_item",
      output_item_attr_name = "conat_item_name",
      output_attr_name_save_as_int=True
    )
    ```
    """
    self._add_processor(CommonRecoConcatAttrEnricher(kwargs))
    return self

  def select_sign(self, **kwargs):
    """
    CommonRecoSelectSignEnricher
    ------
    从 sign 列表中，通过 slot 过滤出部分 sign 存放起来

    参数配置
    ------
    `input_slot_attr`: [string] 输入 slot attr 【用于过滤 sign 】。

    `input_sign_attr`: [string] 输入 sign attr 。

    `select_slots`: [list] 过滤的 slot 。

    `output_sign_attrs`: [list] 对应 select_slots , 输出存储过滤后结果。

    `reserve_size`: [int] 选配项，输出 sign 大约个数（为预留空间使用），默认为 0 。

    `is_common_attr`: [bool] 选配项，是否为 common attr ， input_XXX & output_sign_attrs 相关。 默认 false 。

    调用示例
    ------
    ``` python
    .select_sign(
      input_slot_attr = "item_slots",
      input_sign_attr = "item_signs",
      select_slots = [100, 101],
      output_sign_attrs = ["item_slot_100_bak", "item_slot_101_bak"]
    )
    ```
    """
    self._add_processor(CommonRecoSelectSignEnricher(kwargs))
    return self

  def sleep(self, sleep_ms=None, **kwargs):
    """
    CommonRecoSleepObserver
    ------
    sleep 若干毫秒数

    参数配置
    ------
    `sleep_ms`: [int] [动态参数] sleep 的毫秒数

    调用示例
    ------
    ``` python
    # sleep 100 毫秒
    .sleep(100)
    ```
    """
    if sleep_ms:
      kwargs["sleep_ms"] = sleep_ms
    self._add_processor(CommonRecoSleepObserver(kwargs))
    return self

  def build_from_protobuf(self, **kwargs):
    """
    CommonRecoProtobufCopyAttrEnricher
    ------
    用 context 中 attr 的值组装 Protobuf Message 并以 PB 指针或序列化 string 的格式存回 attr 中。

    参数配置
    ------
    `is_common_attr`: [bool] 是否为 common attr，默认为 True，值为 True, 需要填充 common_attr 的名字到 output_attr 中，值为 False, 需要填充 item_attr 的名字到 output_attr 中

    `class_name`: [string] 转换的 protobuf 的类型，当前支持所有链接的 Message 类型

    `inputs`: [list] 从源字段赋值到目标字段的配置
      - `field_type`: [string] 支持类型 trivial_field/pb_field。trivial_field: 表示从 attr 中进行数据赋值，pb_field: 表示从 protobuf 中进行数据赋值
      - `class_name`: [string] 当 field_type 为 pb_field 时，表示源数据的 protobuf 类型
      - `build_list`: [list] 需要进行构建的源字段和目标字段
        - `from_attr`: [string] 源字段在 attr 中的名字
        - `from_path`: [string] 当 field_type 为 pb_field 时才填写，表示源字段的pb路径
        - `to_path`: [string] 表示要赋值的目标 pb 字段的路径
        - `append`: [bool] 表示目标pb的字段如果是repeated类型，是否追加

    `as_string` : [bool] 是否将 pb 序列化存储，默认为 False（即以 protobuf 的 message 指针类型存储）

    `output_attr`: [string] 输出的 common_attr 或者 item_attr 的 name

    `use_dynamic_proto`: [bool] 是否使用 proto 动态注册，默认为 False

    调用示例
    ------
    ``` python
    1、源字段在 common_attr 中例子:
    .build_from_protobuf(
      is_common_attr: True,
      class_name: "acddd::adddd",
      inputs=[
        {
            "field_type": "trivial_field",
            "build_list": [
              { "from_attr": "aa", "to_path":"a.a", "append": True },
              { "from_attr": "bb", "to_path":"b.b", "append": True }
            ]
        },
        {
            "field_type": "pb_field",
            "class_name": "a::b::c::d",
            "build_list": [
              { "from_attr": "aa", "from_path":"a.a.a", "to_path":"a.a", "append": True },
              { "from_attr": "aa", "from_path":"a.a.a", "to_path":"a.a", "append": True }
            ]
        }
      ],
      as_string: False,
      output_attr: "",
      use_dynamic_proto=False
    }
    2、源字段在 item_attr 中例子:
    .build_from_protobuf(
      is_common_attr: False,
      class_name: "acddd::adddd",
      inputs=[
        {
            "field_type": "trivial_field",
            "build_list": [
              { "from_attr": "aa", "to_path":"a.a", "append": True },
              { "from_attr": "bb", "to_path":"b.b", "append": True }
            ]
        },
        {
            "field_type": "pb_field",
            "class_name": "a::b::c::d",
            "build_list": [
              { "from_attr": "aa", "from_path":"a.a.a", "to_path":"a.a", "append": True },
              { "from_attr": "aa", "from_path":"a.a.a", "to_path":"a.a", "append": True }
            ]
        }
      ],
      as_string: False,
      output_attr: "",
      use_dynamic_proto=False
    }
    ```
    """
    kwargs["class_name"] = kwargs["class_name"].replace("::", ".")
    for item in kwargs["inputs"]:
      if "class_name" in item:
        item["class_name"] = item["class_name"].replace("::", ".")
    self._add_processor(CommonRecoProtobufCopyAttrEnricher(kwargs))
    return self

  def build_protobuf(self, **kwargs):
    """
    CommonRecoProtobufBuildAttrEnricher
    ------
    用 context 中 attr 的值组装 Protobuf Message 并以 PB 指针或序列化 string 的格式存回 context 中。

    注意：请务必谨慎使用 Allocated 接口，如果配置错误，有 core 或者内存泄漏的风险。

    参数配置
    ------
    `class_name`: [string] 转换的 protobuf 的类型，当前支持所有链接的 Message 类型

    `inputs`: [list] 填充到 protobuf 的 attr
      - `common_attr`: [string] 输入的 common attr 的 name
      - `item_attr`: [string] 输入的 item attr 的 name
      - `path`: [string] 存储到 pb 的 field path
      - `append`: [bool] 选配项 是否追加到 repeated field, 默认为 False
      - `allocated`: [bool] 选配项 是否使用 Allocated 接口, 默认为 False，如果设置为 True, 请务必配合 release_protobuf_message 使用！
      - `attr_name`: [string] [DEPRECATED] 输入的 attr 的 name, 是否为 common attr 由 is_common_attr 决定

    `as_string` : [bool] 是否将 pb 序列化存储，默认为 False（即以 protobuf 的 message 指针类型存储）

    `output_common_attr`: [string] 输出的 common attr 的 name

    `output_item_attr`: [string] 输出的 item attr 的 name

    `is_common_attr`: [bool] [DEPRECATED] 是否为 common attr，默认为 True

    `output_attr`: [string] [DEPRECATED] 输出的 attr 的 name, 是否为 common attr 由 is_common_attr 决定

    `use_dynamic_proto`: [bool] 是否使用 proto 动态注册，默认为 False

    调用示例
    ------
    ``` python
    .build_protobuf(
      class_name="ks::reco::RecoPhotoInfo",
      inputs=[
        { "common_attr": "cascade_pctr", "path": "context_info.cascade_pctr" },
        { "common_attr": "cascade_pltr", "path": "context_info.cascade_pltr" },
        { "common_attr": "cascade_pwtr", "path": "context_info.cascade_pwtr" },
        { "common_attr": "cascade_plvtr", "path": "context_info.cascade_plvtr" },
      ],
      output_common_attr="reco_photo_info",
    )
    ```
    """
    kwargs["class_name"] = kwargs["class_name"].replace("::", ".")
    self._add_processor(CommonRecoProtobufBuildAttrEnricher(kwargs))
    return self

  def build_sample_attr(self, **kwargs):
    """
    CommonRecoBuildSampleAttrEnricher
    ------
    用 context 中 attr 的值组装 SampleAttr 并以 PB 指针的格式存回 context 中。

    参数配置
    ------
    `is_common_attr`: [bool] 是否为 common attr，默认为 True。

    `mappings`: [list] 填充到 SampleAttr 的 attr。
      - `from_attr`: [string] 输入的 attr 的 name, 是否为 common attr 由 is_common_attr 决定
      - `to_attr`: [string] 选配项 输出的 attr 的 name, 是否为 common attr 由 is_common_attr 决定, 默认为 from_attr
      - `rename`: [string] 选配项 指定 SampleAttr name 字段的值, 默认为 from_attr

    调用示例
    ------
    ``` python
    .build_sample_attr(
      is_common_attr=False,
      mappings=[
        {
          "from_attr": "score",
          "rename": "FinalScore",
          "to_attr": "score_sample_attr"
        }
      ]
    )
    ```
    """
    self._add_processor(CommonRecoBuildSampleAttrEnricher(kwargs))
    return self

  def common_cluster(self, **kwargs):
    """
    CommonRecoCommonClusterEnricher
    ------
    !> 该算子对接的通用聚类服务 krp_common_cluster_server 已废弃，请勿使用！

    请求通用聚类服务（Common Cluster）为当前结果集内的 item 进行聚类分析，并为各个 item 设置所属的 cluster id，可供后续打散使用

    参数配置
    ------
    `kess_service`: [string] [动态参数] 通用聚类服务的 kess 服务名

    `service_group`: [string] 通用聚类服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] 请求通用聚类服务的超时时间，默认值为 200

    `bucket`: [string] [动态参数] 通用聚类服务中目标 item 所在的桶名称

    `output_attr`: [string] 将通用聚类服务的聚类结果（cluster id）存入指定的 item_attr 中

    `add_item_embeddings_from_attr`: [string] 聚类使用请求传入的 embedding 的 item attr 名称。
                      备注： 一般不需要填该字段, 默认 cluster server 会根据传入的 item_id
                      先进行 embedding 检索，再获取聚类结果；
                      但在有些场景（ 比如冷启动 ）下，可能传入的 item_id 对应的 embedding
                      在当前的 cluster server 中不存在，所以需要通过请求传入.

    调用示例
    ------
    ``` python
    .common_cluster(
      kess_service = "grpc_xxxClusterServer",
      timeout_ms = 200,
      bucket = "item",
      output_attr = "cluster_id",
    )
    ```
    """
    self._add_processor(CommonRecoCommonClusterEnricher(kwargs))
    return self

  def get_local_embedding(self, **kwargs):
    """
    CommonRecoEmbeddingAttrEnricher
    ------
    访问本地的 Embedding 存储，存到 context，返回值类型为 double list，多个 embedding 会按照 pooling_type 进行 pooling。

    参数配置
    ------
    `queue_prefix`: [string] 模型用的 queue_prefix。

    `queue_shard_num`: [int] 模型用的 queue shard 数量。

    `shard_offset`: [int] 模型 queue shard 的起始 id，默认为 0。

    `sign_format`: [string] 模型 sign 的格式，默认为 kuiba，也可以选择 mio。

    `thread_num`: [int] 线程数，默认为 10。

    `read_slots`: [string] 逗号分隔的 slot 列表，白名单过滤，默认为空，不过滤。

    `parameters_inputs`: [list] 输入的 sign 的 item attr。

    `output_attr`: [string] 输出的 embedding 存到给定的 item attr。

    `is_common_attr`: [bool] 是否为 common attr，默认为 False。

    `pooling_mode`: [string] 有多个 embedding 时的 pooling 方式，目前支持 "sum_pooling" 和 "none"，默认为 "sum_pooling"。

    `is_raw_data`: [bool] 启动 raw data 数据类型，即非 Embedding 的数据。默认为 False。

    `raw_data_type`: [string] raw data 的数据类型，支持 uint16，uint32，uint64，float32。

    `is_raw_data_list`: [bool] raw data 是否按 list 存储，如果 list size 为 1，可以设为 False，存储为单值。默认为 True。

    调用示例
    ------
    ``` python
    .get_local_embedding(
      queue_prefix = "embedding_dnn_model_with_context_retr",
      queue_shard_num = 1,
      shard_offset = 1024,
      sign_format = "mio",
      thread_num = 65,
      parameter_inputs = ["_ITEM_ID_"],
      output_attr = "embedding",
    )
    ```
    """
    self._add_processor(CommonRecoEmbeddingAttrEnricher(kwargs))
    return self

  def fetch_user_info(self, **kwargs):
    """
    CommonRecoUserInfoCommonAttrEnricher
    ------
    访问 UserProfile 服务，获取 user_info，注意返回的类型为 string，如果要访问需要调用 parse_protobuf_from_string 转化为 protobuf 格式。

    参数配置
    ------
    `kess_service`: [string] UserProfile 服务的 kess 服务名

    `service_group`: [string] UserProfile 服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] 请求 UserProfile 服务的超时时间毫秒数，默认值为 200

    `new_user`: [bool] [动态参数] 是否为新用户, true 是新用户, false 为老用户, 默认为 false

    `biz_name`: [string] 不同业务有不同的 biz_name, 如果不知道可以联系 UserProfile 服务的 Oncall 同学

    `product`: [int] 用于区分不同的 app，默认值为 0，表示快手国内主 app，如果不知道可以联系 UserProfile 服务的 Oncall 同学

    `save_to_common_attr`: [string] 将返回的 string 存到指定的 common attr

    `user_id`: [int] [动态参数] 选配项，用户自定义 uid 默认值为请求自带 user_id

    `device_id`: [string] [动态参数] 选配项，用户自定义 did 默认值为请求自带 device_id

    调用示例
    ------
    ``` python
    .fetch_user_info(
      kess_service = "grpc_xxx",
      timeout_ms = 100,
      new_user = False,
      biz_name = "yyy",
      save_to_common_attr = "user_info_str",
    )
    ```
    """
    self._add_processor(CommonRecoUserInfoCommonAttrEnricher(kwargs))
    return self

  def write_to_csv(self, **kwargs):
    """
    CommonRecoCsvObserver
    ------
    将 Context 写入 csv 文件。

    参数配置
    ------
    `attr_seperator`: [string] attr 之间的分隔符，默认为 ','

    `list_seperator`: [string] 列表成员之间的分隔符，默认为 ' ' (空格)

    `has_header`: [bool] 文件第一行是否插入 header 列出所有 attr

    `to_stdout`: [bool] 默认 False. 设置 True 输出到 stdout, `path_prefix` 会不起作用, 只允许单线程

    `path_prefix`: [string] csv 文件的前缀

    `attrs`: [list] 存到 csv 的 attr 列表，支持 item attr 与 common attr

    调用示例
    ------
    ``` python
    .write_to_csv(
      attrs=["user_id", "device_id", "item_id", "author_id"],
      has_header=True,
      path_prefix="/data/log/common_leaf_log_",
    )
    ```
    """
    self._add_processor(CommonRecoCsvObserver(kwargs))
    return self

  def check_attr_diff(self, **kwargs):
    """
    CommonRecoAttrDiffObserver
    ------
    比较一组 attr pair 的值，将 diff 结果打印到屏幕，并将一致或不一致的数目上报 perflog, 可在 [grafana 监控](https://grafana.corp.kuaishou.com/d/S10M9QdZk/commonleafjian-kong?orgId=3&fullscreen&panelId=4321)查看

    参数配置
    ------
    `attr_pairs`: [dict] 需要比较的 attr pair 列表，用 "attr_a" 和 "attr_b" 指定, 同时也可通过 "error_tolerance" 指定该 attr pair 允许的误差

    `default_error_tolerance`: [double] attr 允许的误差范围，默认为 1e-4

    调用示例
    ------
    ``` python
    .check_attr_diff(
      attr_pairs = [
        (ctr, trt_ctr),
        (ptr, trt_ptr, 1e-5)
      ],
      default_error_tolerance = 1e-4
    )
    ```
    """
    conf = {}
    if "attr_pairs" in kwargs:
      conf["attr_pairs"] = []
      attr_pair_lists = kwargs.pop("attr_pairs")
      for item in attr_pair_lists:
        assert len(item) >= 2, "expect tuple like (attr_a, attr_b) or (attr_a, attr_b, error_tolerance), but get " + str(item)
        attr_pair = {"attr_a" : item[0], "attr_b" : item[1]}
        if len(item) > 2:
          attr_pair["error_tolerance"] = item[2]
        conf["attr_pairs"].append(attr_pair)
    conf.update(kwargs)
    self._add_processor(CommonRecoAttrDiffObserver(conf))
    return self

  def send_abtest_metrics(self, **kwargs):
    """
    CommonRecoAbtestMetricsObserver
    ------
    将指定的 common_attr 值作为 abtest 实时指标发送给 abtest 系统

    参数配置
    ------
    `metrics`: [list] 需要作为 abtest metric 发送的属性列表（仅支持发送 int/double 类型的 common_attr），列表的每项可为 string 类型（即 common_attr 的名称），或 dict 类型包含 "name" 和 "as" 字段（分别为 common_attr 名称和发送的 metric 名称）。

    `metric_name_prefix`: [string] [动态参数] 选配项，给所有 metric 名称加上指定的前缀，默认为服务名（无 `grpc_` 前缀）加下划线 `_`。

    调用示例
    ------
    ``` python
    .send_abtest_metrics(
      metrics=[
        "retrival_time_cost",
        "ranking_time_cost",
        { "name": "total_time_cost", "as": "leaf_process_time" },
      ],
    )
    ```
    """
    self._add_processor(CommonRecoAbtestMetricsObserver(kwargs))
    return self

  def map_item_list_attr(self, **kwargs):
    """
    CommonRecoItemListAttrEnricher
    ------
    对某个存储在 int_list 或 int 类型 item_attr 的 item 列表进行相关 attr 抽取，并将抽取的 attr 写入指定的 item_attr 中。

    注意：
    - 对 int / double / string 单值类型的 item_attr map 映射 **会保证** 新生成的 attr list 长度与原 item list 长度一致（如遇缺失将用默认值补齐）。
    - 对 int_list / double_list / string_list 类型的 item_attr map 映射 **不保证** 新生成的 attr list 长度与原 item list 长度一致（如遇缺失将忽略，不补齐）！
    - 若 `items_in_item_attr` 为单值 int 类型（即只有一个 item 需要被映射），且待抽取的 from attr 也为单值类型，那么映射后的 to attr 也将继续保持为单值类型。

    参数配置
    ------
    `items_in_item_attr`: [string] 从哪个 item_attr 中抽取 item list 作为 item 列表

    `item_attr_mapping`: [dict] 抽取 item_attr 的映射关系
      - key: mapping 配置的 key 为待抽取的 item_attr 来源（from attr）, 譬如 "pctr"
      - value: mapping 配置的 value 为待抽取的 item_attr 的存储目标位置（to attr）, 譬如 "pctr_list"

    `default_int_value`: [int] 选配项，配置单值属性映射情况下缺失 item_attr 时使用的默认值, 以保证映射后的 attr list 长度与原 item list 长度一致, 默认值为 0

    `default_double_value`: [double] 选配项，配置单值属性映射情况下缺失 item_attr 时使用的默认值, 以保证映射后的 attr list 长度与原 item list 长度一致, 默认值为 0.0

    `default_string_value`: [string] 选配项，配置单值属性映射情况下缺失 item_attr 时使用的默认值, 以保证映射后的 attr list 长度与原 item list 长度一致, 默认值为空字符串

    `use_item_type`: [int] 选配项，如果 item list 中存储的是 item_id 而非 item_key, 可通过该项配置设置其 item_type 以生成正确的 item_key, 默认值为 0

    调用示例
    ------
    ``` python
    .map_item_list_attr(
      items_in_item_attr = "similar_photos",
      item_attr_mapping = {
        "category": "similar_photo_categories",
        "pctr": "similar_photo_pctrs"
      }
    )
    ```
    """
    self._add_processor(CommonRecoItemListAttrEnricher(kwargs))
    return self


  def get_common_attr_from_redis(self, **kwargs):
    """
    CommonRecoRedisCommonAttrEnricher
    ------
    读取 redis value 写入指定的 common_attr 中。

    参数配置
    ------
    `cluster_name`: [string] redis 的 cluster name

    `timeout_ms`: [int] 获取 redis client 的超时时间，默认为 10

    `redis_params`: [list] redis key/value 的相关配置
      - `redis_key`: [string | string_list] [动态参数] redis key 的字面值，也可以动态参数的方式从 string/string_list/int/int_list 类型的 common_attr 获取 key。如果 key 是 string_list（或动态参数的值是一个 string_list）则必须手动指定 output_attr_type 为对应的 list 类型，会逐个使用 list 中的 key 获取 value 并存入到对应的 list 类型 common_attr 中
      - `redis_value_type`: [string] 读取的 redis value 的类型，仅支持 string，默认为 string 类型
      - `output_attr_name`: [string] value 写入的 commonAttr 名
      - `output_attr_type`: [string] value 写入的 commonAttr 类型，支持 int/double/string/int_list/double_list/string_list，默认为 string 类型
      - `key_prefix`: [string] [动态参数] 选配项，为 redis_key 添加统一的前缀，默认为空

    `skip_empty`: [bool] 是否需要跳过为空的情况，仅对 int/int_list/double/double_list 生效，默认 False

    `skip_parse_fail`: [bool] 是否需要跳过解析错误的情况，仅对 int/int_list/double/double_list 生效，注意空字符串一定会 parse fail, 默认 False

    `cache_bits`: [int] 选配项，cache 大小，即最多存 2^cache_bits 个 kv 值（LRU 删除），默认为 0（无 cache）

    `cache_expire_second`: [int] 选配项，cache 内的数据过期的时间，默认为 3600 秒

    `cache_delay_delete_ms`: [int] 选配项，cache 内的数据延迟删除的时间，一般使用默认值即可，默认为 10 秒

    `cache_name`: [string] 选配项，用于在 [grafana 监控](https://grafana.corp.kuaishou.com/d/0jCdxsQMk/kv_cache_client?orgId=3) 中区分不同 cache 的命中率等信息，默认跟 cluster_name 相同

    `save_err_code_to`: [string] 选配项，将 redis 读取操作的错误码 RedisErrorCode（数字值） 存到指定的 common attr

    `is_async`: [bool] 选配项，是否异步获取redis，默认为 False。

    调用示例
    ------
    ``` python
    .get_common_attr_from_redis(
      cluster_name = "redis_cluster_name",
      redis_params = [
        {
          "redis_key": "key1",
          "output_attr_name": "output1"
        }, {
          "redis_key": "{{key2}}",
          "redis_value_type": "string",
          "output_attr_name": "output2",
          "output_attr_type": "int"
        }
      ]
    )
    ```
    """
    self._add_processor(CommonRecoRedisCommonAttrEnricher(kwargs))
    return self

  def write_to_redis(self, **kwargs):
    """
    CommonRecoWriteToRedisObserver
    ------
    通用写 redis 插件, 能够将 CommonAttr 或者 ItemAttr 写入到指定的 redis 之中。

    如果填写的是 CommonAttr，那么存储格式就是会存储一个 StringValue 进去。

    如果填写的是 ItemAttr，那么对于每个 Item 都将会填写一个 StringValue 进去。

    其 redis 读取 processor 为 CommonRecoRedisCommonAttrEnricher

    参数配置
    ------
    `kcc_cluster`: [string] 必填，redis 的 kcc cluster name

    `timeout_ms`: [int] 选配项，redis client 的超时时间，单位 ms，默认为 10 ms

    `key_prefix`: [string] [动态参数] 选配项，每个 redis key 的值添加一个前缀，默认为空

    `expire_second`: [int] [动态参数]，单位秒, 负数或者 0 表示不设置过期时间，默认 -1

    `list_separator`: [string] 如果存储的 value 是 list 类型，那么我们会用所给的分隔符来拼接成一个 string 存入到 redis 中，默认为 ','

    以下两种 key 二选一：

    `key`: [string] [动态参数] 指定写入 redis 的 key，如果配置了动态参数且该 common_attr 不存在将忽略写入，动态参数支持 int/string/int_list/string_list 类型，int 类型会先转换为 uint64 再转换成 string

    `key_from_item_attr`: [string] 从指定的 item_attr 中获取动态的 redis key，如果该 item_attr 不存在将被跳过。支持 int/string/int_list/string_list 类型，int 类型会先转换为 uint64 再转换成 string

    对于 value 有两种方式，二选一:

    `value`: [string] [动态参数] 指定写入 redis 的 value。

    `value_from_item_attr`: [string] 仅针对 `key_from_item_attr`，会根据其 item_attr 一一对应的去写。

    调用示例
    ------
    ``` python
    .write_to_redis(
      kcc_cluster="test_cluster",
      timeout_ms=10,
      key_prefix="test_",
      key="test_key",
      value="test_value",
      expire_second=600
    )
    ```
    """
    self._add_processor(CommonRecoWriteToRedisObserver(kwargs))
    return self

  def dump_context(self, **kwargs):
    """
    CommonRecoDumpContextEnricher
    ------
    将当前 context 中的结果集和 attr 数据快照保存到指定的 string 类型 common_attr 中，后续可发送给 redis 等外部存储供其它使用。

    注：通过 `dump_context()` dump 后的 context 数据，可以通过 `parse_context()` 反解并 merge 至当前 context 中继续使用。

    参数配置
    ------
    `include_item_results`: [bool] 是否需要记录 item，如果 item_attrs 非空的话 include_item_results 默认为 True，否则为 False

    `common_attrs`: [list] 用以填写存储的 common_attr 的 list

    `dump_common_attrs_from_request`: [bool] 是否保存请求中携带的 common_attr

    `item_attrs`: [list | string] include_item_results 为 True 的情况下，填写存储的 item_attr 的 list。支持设置为 "*"，存储所有 item_attr

    `dump_as_table`: [bool] 是否将 item_attrs 以 ks.platform.DataTable 格式并序列化

    `dump_to_attr`: [string] 必配项，将 attr 和 item 数据 dump 后的 string 内容存储到指定 common_attr 下

    调用示例
    ------
    ``` python
    .dump_context(
      common_attrs=["test1", "test2"],
      include_item_results=True,
      item_attrs=["test_item1", "test_item2"],
      dump_to_attr="context"
    )
    ```
    """
    self._add_processor(CommonRecoDumpContextEnricher(kwargs))
    return self

  def parse_context(self, **kwargs):
    """
    CommonRecoParseContextRetriever
    ------
    将通过 `dump_context()` dump 后的 context，反解并 merge 至当前 context 中继续使用。

    其 item 侧数据将会补充在后面，common 数据如果重复将会直接覆盖掉。

    参数配置
    ------
    `parse_from_attr`: [string] 必配项，context 来自哪个 string 字段

    `data_format`: [string] 选配项，string 数据的格式，可选值 data_table 或 step_info，默认值为 step_info

    `extract_common_attrs`: [list] 用以填写需要解析的 common_attr 的名称列表，如果名称列表为空，默认不写。

    `extract_item_results`: [bool] 是否把 item 追加至当前结果集的最后，如果 extract_item_attrs 不为空，该项默认为 True，否则为 False。

    `extract_item_attrs`: [list] 解析对应的 item_attr 的 名称列表，如果名称列表为空，默认不写。

    调用示例
    ------
    ``` python
    .parse_context(
      parse_from_attr="context",
      extract_common_attrs=["test1", "test2"],
      extract_item_results=True,
      extract_item_attrs=["test_item1", "test_item2"]
    )
    ```
    """
    self._add_processor(CommonRecoParseContextRetriever(kwargs))
    return self

  def retrieve_by_common_reco_response(self, **kwargs):
    """
    CommonRecoResponseRetriever
    ------
    从 string common attr 中读取 CommonRecoResponse 对象，并填充结果。

    参数配置
    ------
    `response_attr`: [string] 必配项，CommonRecoResponse 来源，支持 String 和 PB Message 类型

    `recv_item_attrs`: [list] 选填项，接收的 item_attr 列表，默认不接收 item_attr，支持对 attr 重命名保存。

    `recv_common_attrs`: [list] 选填项，接收的 common attr 列表，默认不接受 common attr，支持对 attr 重命名保存。

    `recv_return_item_attrs_in_request`: [bool] 选填项, 是否将上游发送过来的 request 中的全部 return_item_attrs 合并入 recv_item_attrs 发送给下游, 默认为 false

    调用示例
    ------
    ``` python
    .retrieve_by_common_reco_response(
      response_attr="response_str",
      recv_item_attrs=["attr1", "attr2"],
      recv_common_attrs=["did"],
      recv_return_item_attrs_in_request=True,
    )
    ```
    """
    self._add_processor(CommonRecoResponseRetriever(kwargs))
    return self

  def get_item_attr_from_redis(self, **kwargs):
    """
    CommonRecoRedisItemAttrEnricher
    ------
    用 string item attr 为 key 读取 redis value 写入指定的 item_attr 中。

    参数配置
    ------
    `cluster_name`: [string] redis 的 cluster name

    `timeout_ms`: [int] 获取 redis client 的超时时间，默认为 10

    参数配置（方式一：单批获取）
    ------
    `redis_key_from`: [string] 从 string 类型的 item_attr 获取 key

    `save_value_to`: [string] 将 redis 获取到的值存入指定的 string 类型的 item_attr 中

    `key_prefix`: [string][动态参数] 选配项，每个 redis key 的值添加一个前缀，默认为空

    参数配置（方式二：多批获取）
    ------
    `attrs_config`: [list] redis key 和 value 的 attr 配置
      - `key_attr`: [string] 从 string 类型的 item_attr 获取 key
      - `value_attr`: [string] 将 redis 获取到的值存入指定的 string 类型的 item_attr 中
      - `key_prefix`: [string][动态参数] 选配项，每个 redis key 的值添加一个前缀，默认为空

    参数配置（各方式共有）
    ------
    `cache_bits`: [int] 选配项，cache 大小，即最多存 2^cache_bits 个 kv 值（LRU 删除），默认为 0（无 cache）

    `cache_expire_second`: [int] 选配项，cache 内的数据过期的时间，默认为 3600 秒

    `cache_delay_delete_ms`: [int] 选配项，cache 内的数据延迟删除的时间，一般使用默认值即可，默认为 10 秒

    `cache_name`: [string] 选配项，用于在 [grafana 监控](https://grafana.corp.kuaishou.com/d/0jCdxsQMk/kv_cache_client?orgId=3) 中区分不同 cache 的命中率等信息，默认跟 cluster_name 相同

    `is_async`: [bool] 选配项，是否异步获取redis，默认为 False。

    调用示例
    ------
    ``` python
    .get_item_attr_from_redis(
      cluster_name = "redis_cluster_name",
      redis_key_from="redis_key",
      save_value_to="redis_value",
    )
    ```
    """
    if "attrs_config" in kwargs:
      self._add_processor(CommonRecoRedisItemAttrBatchEnricher(kwargs))
    else:
      self._add_processor(CommonRecoRedisItemAttrEnricher(kwargs))
    return self

  def do_nothing(self, **kwargs):
    """
    CommonRecoDummyObserver
    ------
    一个空逻辑的 Processor，主要为测试场景使用。

    参数配置
    ------
    无

    调用示例
    ------
    ``` python
    .do_nothing()
    ```
    """
    self._add_processor(CommonRecoDummyObserver(kwargs))
    return self

  def do(self, *arg):
    """
    一个 dsl 空跑的方法，主要为了 dsl 缩进统一。
    ------
    调用实例
    ------
    ```python
    def process_something(flow):
      flow.do_nothing()

    def process_something_else(flow):
      flow.do_nothing()

    # 引入 do 之前格式
    flow.if_("enable_do_something == 1")
    process_something(flow)
    flow.else_()
    process_something_else(flow)
    flow.end_if_()

    # 引入 do 之后格式，可以按照 if_ else_ 进行缩进。
    flow \\
      .if_("enable_do_something == 1") \\
        .do(process_something(flow)) \\
      .else_() \\
        .do(process_something_else(flow)) \\
      .end_if_()
    ```
    """
    return self


  def return_(self, code: int = 0, msg: str = "", **kwargs):
    """
    CommonRecoExecutionStatusEnricher
    ------
    提前结束并跳出当前 flow 层级的处理流程，若在主流程中执行将直接结束当前请求返回 Response，若在 subflow 中执行将返回到主流程。

    参数配置
    ------
    `code`: [int] 任务处理的状态码（仅在主流程中有效），可选值：
      - 0: 正常结束，grpc 服务将以“成功”状态返回，可用性不受影响，该值为默认值
      - 1: 错误结束，grpc 服务将以“失败”状态返回 (grpc::StatusCode::ABORTED)，Response 为空，可用性会因此下降
      - 2: 正常终止，grpc 服务将以“成功”状态返回，Response 为空，可用性不受影响，与正常结束的区别在于表达流程并未走完，因某些原因提前结束
      - 4: 权限不足，grpc 服务将以“PERMISSION_DENIED”状态返回，Response 为空，可用性不受影响。

    `msg`: [string] 选配项，该信息将作为日志内容的一部分出现在 INFO log 中

    调用示例
    ------
    ``` python
    # 正常结束
    .return_(0)
    # 错误结束
    .return_(1, "something went wrong")
    # 正常终止
    .return_(2, "terminate the processing")
    ```
    """
    if code >= 0:
      kwargs["status_code"] = code
    if msg:
      kwargs["message"] = msg
    self._add_processor(CommonRecoExecutionStatusEnricher(kwargs))
    return self

  def if_(self, expr: str, name: Optional[str] = None, **kwargs):
    """
    用于开启 LeafFlow 的 if 分支逻辑，需与 `end_()` 方法配对使用

    参数配置
    ------
    `expr`: [string] 一个合法的 Lua `bool` 类型表达式，表达式中可直接引用 common_attr。表达式的判定结果统计可在 [Grafana 监控](https://grafana.corp.kuaishou.com/d/S10M9QdZk/commonleafjian-kong?orgId=3&fullscreen&panelId=6241)中查看。

    调用示例
    ------
    ``` python
    # xxx() 和 yyy() 将在 "is_new_user == 1" 时执行
    .if_("is_new_user == 1")  \\
      .xxx()                  \\
      .yyy()                  \\
    .end_()
    ```
    """
    if not expr:
      raise ArgumentError("if_() 缺少 expr 参数")
    check_parenthesis_closed(expr, "Lua 表达式")
    if kwargs.get("check_expr", True):
      check_abnormal_if_expr(expr)
    self._start_if_branch(expr, name, **kwargs)
    return self

  def else_if_(self, expr: str, name: Optional[str] = None, **kwargs):
    """
    用于开启 LeafFlow 的 if 的 else-if 分支逻辑，需与 `if_()` 方法配合使用

    参数配置
    ------
    expr: [string] 一个合法的 Lua bool 类型表达式，表达式中可直接引用 common_attr。表达式的判定结果统计可在 Grafana 监控中查看。

    调用示例
    ------
    ``` python
    # xxx() 将在 "is_new_user == 1" 时执行
    # yyy() 将在 "is_new_user != 1 and is_nebula == 1" 时执行（可以有多个 else-if）
    # zzz() 将在 "is_new_user != 1 and is_nebula != 1" 时执行（也可以没有 else）
    .if_("is_new_user == 1")     \\
      .xxx()                     \\
    .else_if_("is_nebula == 1")  \\
      .yyy()                     \\
    .else_()                     \\
      .zzz()                     \\
    .end_()
    ```
    """
    if kwargs.get("check_expr", True):
      check_abnormal_if_expr(expr)
    self._start_else_if_branch(expr, name, **kwargs)
    return self

  def else_(self, name: Optional[str] = None, **kwargs):
    """
    用于开启 LeafFlow 的 if 的 else 分支逻辑，需与 `if_()` 方法配对使用

    参数配置
    ------
    无

    调用示例
    ------
    ``` python
    # xxx() 将在 "is_new_user == 1" 时执行
    # yyy() 将在 "is_new_user != 1" 时执行
    .if_("is_new_user == 1")  \\
      .xxx()                  \\
    .else_()                  \\
      .yyy()                  \\
    .end_()
    ```
    """
    self._start_else_branch(name, **kwargs)
    return self

  def end_if_(self):
    """
    用于结束 LeafFlow 的 if 分支逻辑，在 `if_()` 之后的分支结束处必须手动调用一次

    ?> 提示：可用 `end_()` 代替！

    参数配置
    ------
    无

    调用示例
    ------
    ``` python
    # zzz() 将不受 is_new_user 控制
    .if_("is_new_user == 1")  \\
      .xxx()                  \\
      .yyy()                  \\
    .end_if_()                \\
    .zzz()
    ```
    """
    self._end_if_branch()
    return self

  def switch_(self, expr: str):
    """
    用于开启 LeafFlow 的 switch 分支逻辑，需与 `end_()` 方法配对使用

    参数配置
    ------
    `expr`: [string] 单个 common_attr 名称，或一个合法的 Lua 表达式，表达式中可直接引用 common_attr

    调用示例
    ------
    ``` python
    .switch_("user_type")   \\
      .case_(1, 2)          \\
        .xxx()              \\
      .case_(3)             \\
        .yyy()              \\
      .default_()           \\
        .zzz()              \\
    .end_()
    ```
    """
    if not expr:
      raise ArgumentError("switch_() 缺少 expr 参数")
    check_parenthesis_closed(expr, "Lua 表达式")
    self._start_switch_branch(expr)
    return self

  def case_(self, *values, **kwargs):
    """
    用于匹配 `switch_()` 分支逻辑判定表达式的值，需在 `switch_()` 和 `end_()` 之间使用

    参数配置
    ------
    `*values`: 用于匹配 `switch_()` 中表达式的值，可填入多个以匹配多值，值类型必须为 int 或 string

    调用示例
    ------
    ``` python
    .switch_("user_type")   \\
      .case_(1, 2)          \\
        .xxx()              \\
      .case_(3)             \\
        .yyy()              \\
      .default_()           \\
        .zzz()              \\
    .end_()
    ```
    """
    if not values:
      raise ArgumentError("未指定 case_() 的匹配值")
    if not all(isinstance(val, (int, str, bool)) for val in values):
      raise ArgumentError("case_() 的参数值只支持 int / string / bool 类型")
    self._start_case_branch(*values, **kwargs)
    return self

  # 给 default_ 接口增加一个无用的 placeholder 参数，用于规避 $code_info 对无参函数调用代码行判断不准的问题
  def default_(self, placeholder: Optional[str] = None, **kwargs):
    """
    用于匹配 `switch_()` 分支之前所有 case 之外的值，需在 `switch_()` 和 `end_()` 之间使用。

    参数配置
    ------
    无

    调用示例
    ------
    ``` python
    .switch_("user_type")   \\
      .case_(1, 2)          \\
        .xxx()              \\
      .case_(3)             \\
        .yyy()              \\
      .default_()           \\
        .zzz()              \\
    .end_()
    ```
    """
    self._start_default_branch(**kwargs)
    return self

  def end_switch_(self):
    """
    用于结束 LeafFlow 的 switch 分支逻辑，需与 `switch_()` 方法成对使用

    ?> 提示：可用 `end_()` 代替！

    参数配置
    ------
    无

    调用示例
    ------
    ``` python
    .switch_("user_type")   \\
      .case_(1, 2)          \\
        .xxx()              \\
      .case_(3)             \\
        .yyy()              \\
      .default_()           \\
        .zzz()              \\
    .end_switch_()
    ```
    """
    self._end_switch_branch()
    return self

  def end_(self):
    """
    用于结束 LeafFlow 的分支逻辑，需与 `switch_()` 或 `if_()` 成对使用

    参数配置
    ------
    无

    调用示例
    ------
    ``` python
    # 与 if_ 配合使用
    .if_("is_new_user == 1")  \\
      .xxx()                  \\
      .yyy()                  \\
    .end_()

    # 与 switch_ 配合使用
    .switch_("user_type")     \\
      .case_(1, 2)            \\
        .xxx()                \\
      .case_(3)               \\
        .yyy()                \\
      .default_()             \\
        .zzz()                \\
    .end_()
    ```
    """
    self._auto_end_branch()
    return self

  def get_remote_embedding(self, **kwargs):
    """
    CommonRecoRemoteEmbeddingAttrEnricher
    ------
    **DEPRECATED!!! 本 processor 不再维护，功能已收敛到 fetch_remote_embedding**
    请求远程 embedding server 获取 user 或 item 的 embeddings。

    参数配置
    ------
    `kess_service`: [string][动态参数] embedding server 服务的 kess 服务名 [必填]

    `kess_cluster`: [string] embedding server 服务的 kess 服务组，默认值为 "PRODUCTION"

    `colossusdb_table_name`: [string] 对于传统 embedding server 不要配置，默认为空，对于统一存储 embedding server 需要配置

    `shard_num`: [int] embedding server 的 shard num, 默认值为 1。如果设为 0 则自动从 kess 平台识别 shard 数目 [慎重使用]，当下游某个 shard 全部挂掉时，shard 数目会识别错误，这会导致请求错乱最终拿不到正确数据。对于统一存储模式不要设置，使用默认值。

    `max_signs_per_request`: [int] 每个 RPC 请求包含的最大 sign 个数，默认为 0，即不限制

    `timeout_ms`: [int] [动态参数] 请求 embedding server 服务的超时时间，默认值为 10

    `id_converter`: [dict] sign -> id 生成器的相关配置 [必填]
      - `type_name`: [string][动态参数] 根据 sign_generator 格式可选值："mioEmbeddingIdConverter" 或 "kuibaEmbeddingIdConverter" 或 "plainIdConverter"

    `slot`: [int] 填写需要与 item_key 拼接的 slot 值, 默认值为 0

    `save_to_common_attr`: [bool] True 需要填写 `output_item_list_attr` 和 `output_embedding_list_attr`，False 需要填写 `output_attr_name`

    `output_attr_name`: [string] 写入的 ItemAttr name

    `output_item_list_attr`: [string] 填充 id 结果的 common attr 名字，为空时不填充。

    `output_embedding_list_attr`: [string] 填充 embedding 结果的 common attr 名字

    `query_source_type`: [string] 请求 key 来源，支持 item_key，item_id，item_attr, user_id，device_id，user_id_and_device_id，其中只有 item_key, item_id
    和 item_attr 支持 save_to_common_attr 为 False。默认为 item_key。

    `query_source_item_attr`: [string] 当 query_source_type 设为 item_attr 时，必须提供 query_source_item_attr 用于获取 source attr。

    `is_raw_data`: [bool] 启动 raw data 数据类型，即非 Embedding 的数据。

    `raw_data_type`: [string] raw data 的数据类型，支持 uint8，uint16，uint32，uint64，int8，int16，int32，int64，float32，string, scale_int8。

    `is_raw_data_list`: [bool] raw data 是否按 list 存储，如果 list size 为 1，可以存储为单值。

    `client_side_shard`: [bool] 是否根据 shard 发送请求，默认为 False，当 embedding 按 shard 存储时建议打开。

    调用示例
    ------
    ``` python
    .get_remote_embedding(
      kess_service = "grpc_xxx",
      id_converter = {"type_name": "mioEmbeddingIdConverter"},
      slot = 38,
      save_to_common_attr = True,
      output_item_list_attr = "attr_id1",
      output_embedding_list_attr = "attr_embedding1",
    )
    ```
    """
    self._add_processor(CommonRecoRemoteEmbeddingAttrEnricher(kwargs))
    return self

  def get_remote_embedding_lite(self, **kwargs):
    """
    CommonRecoRemoteEmbeddingAttrLiteEnricher
    ------
    **DEPRECATED!!! 本 processor 不再维护，功能已收敛到 fetch_remote_embedding**
    get_remote_embedding 的精简版，请求远程 embedding 服务获取 embedding。

    参数配置
    ------
    `protocol`: [int] 访问 embedding 服务所使用的协议，默认为 0，协议的支持情况见 协议支持 部分。

    `id_converter`: [dict] sign -> id 生成器的相关配置 [必填]
      - `type_name`: [string] 根据 sign_generator 格式可选值："mioEmbeddingIdConverter" 或 "kuibaEmbeddingIdConverter" 或 "plainIdConverter"

    `slot`: [int] 填写需要与 item_key 拼接的 slot 值, 默认值为 0

    `input_attr_name`: [string] 输入的 ItemAttr name，当 query_source_type 为 item_attr 或 common_attr 时生效。

    `output_attr_name`: [string] 输出的 ItemAttr name

    `query_source_type`: [string] 请求 key 来源，支持 item_key，item_id，user_id，device_id，item_attr 和 common_attr。默认为 item_key。

    `size`: [int] embedding 维度，需要指定，当结果的 size 不符合时会被过滤掉。

    `max_signs_per_request`: [int] 每个 RPC 请求包含的最大 sign 个数，默认为 0，即不限制

    `timeout_ms`: [int] 请求 embedding server 服务的超时时间，默认值为 10

    `is_raw_data`: [bool] 启动 raw data 数据类型，即非 Embedding 的数据。默认为 false, 即 mio_int16 格式

    `raw_data_type`: [string] raw data 的数据类型，支持 int8，int16，int32，int64，uint8，uint16，uint32，uint64，float32, 默认值为 uint16。
      注意本 processor 总是会将结果写入 double attr 中，即使原始值是 int 也会先转换成 double。

    以下为 protocol == 0 时的对应配置

    `kess_service`: [string][动态参数] embedding server 服务的 kess 服务名 [必填]

    `kess_cluster`: [string] embedding server 服务的 kess 服务组，默认值为 "PRODUCTION"

    `shard_num`: [int] embedding server 的 shard num, 默认值为 1

    `client_side_shard`: [bool] 是否根据 shard 发送请求，默认为 False，当 embedding 按 shard 存储时建议打开。

    `hash_input`: [string][动态参数] 根据指定值计算 hash 决定请求发往哪个副本，某个确定的值总是会请求到固定的某一个副本。
      默认为空，此时仍然是随机选择副本发送请求。

    以下为 protocol == 1 时的对应配置，下游服务信息会从自定义的路由表中获取

    `colossusdb_embd_service_name`: [string] 下游 colossusdb embedding server 的服务名，一般是: {模型名称}.
      获取服务名的方法详见文档[获取Embedding](https://docs.corp.kuaishou.com/k/home/<USER>/fcAB1k2CwkZRcxQ09lTBYMRkH)

    `colossusdb_embd_table_name`: [string] 下游 colossusdb embedding server 的表名，如 test-table

    `colossusdb_use_kconf_client`: [bool] 是否用 kconf_client 包装在原生 client 外面，默认为 true.
      如果设为 false, 则一般情况下应将上述的 colossusdb_embd_service_name 修改为 grpc_clsdb_ps-{模型名称}, colossusdb_embd_table_name 不变。
      详见文档[统一存储流量治理功能说明](https://docs.corp.kuaishou.com/d/home/<USER>

    调用示例
    ------
    ``` python
    .get_remote_embedding_lite(
      kess_service = "grpc_xxx",
      id_converter = {"type_name": "mioEmbeddingIdConverter"},
      slot = 38,
      input_attr_name = "signs",
      output_attr_name = "embeddings",
      hash_input = "{{string_attr_name}}",
    )

    .get_remote_embedding_lite(
      protocol=1,
      colossusdb_embd_service_name="merchant-query-mm-emb-srv",
      colossusdb_embd_table_name="test-table",
      id_converter = {"type_name": "mioEmbeddingIdConverter"},
      slot = 38,
      input_attr_name = "signs",
      output_attr_name = "embeddings",
    )
    ```
    """
    self._add_processor(CommonRecoRemoteEmbeddingAttrLiteEnricher(kwargs))
    return self

  def get_remote_embedding_lite_v2(self, **kwargs):
    """
    CommonRecoRemoteEmbeddingAttrRawLiteEnricher
    ------
    **DEPRECATED!!! 本 processor 不再维护，功能已收敛到 fetch_remote_embedding**
    支持 raw data 的 get_remote_embedding_lite 版本，请求远程 embedding 服务获取 embedding。

    参数配置
    ------
    `protocol`: [int] 访问 embedding 服务所使用的协议，默认为 0，协议的支持情况见 协议支持 部分。

    `max_signs_per_request`: [int] 每个 RPC 请求包含的最大 sign 个数，默认为 0，即不限制

    `timeout_ms`: [int] 请求 embedding server 服务的超时时间，默认值为 10

    `id_converter`: [dict] sign -> id 生成器的相关配置 [必填]
      - `type_name`: [string] 根据 sign_generator 格式可选值："mioEmbeddingIdConverter" 或 "kuibaEmbeddingIdConverter" 或 "plainIdConverter"

    `slot`: [int] 填写需要与 item_key 拼接的 slot 值, 默认值为 0

    `input_attr_name`: [string] 输入的 ItemAttr name，当 query_source_type 为 item_attr 或 common_attr 时生效。

    `output_attr_name`: [string] 输出的 ItemAttr name

    `query_source_type`: [string] 请求 key 来源，支持 item_key，item_id，user_id，device_id，item_attr 和 common_attr。默认为 item_key。

    `size`: [int] embedding 维度，需要指定，当结果的 size 不符合时会被过滤掉。

    `is_raw_data`: [bool] 启动 raw data 数据类型，即非 Embedding 的数据。

    `raw_data_type`: [string] raw data 的数据类型，支持 int8，int16，int32，int64，uint8，uint16，uint32，uint64，float32，默认值为 uint16。

    以下为 protocol == 0 时的对应配置

    `kess_service`: [string][动态参数] embedding server 服务的 kess 服务名 [必填]

    `kess_cluster`: [string] embedding server 服务的 kess 服务组，默认值为 "PRODUCTION"

    `shard_num`: [int] embedding server 的 shard num, 默认值为 1

    `client_side_shard`: [bool] 是否根据 shard 发送请求，默认为 False，当 embedding 按 shard 存储时建议打开。

    以下为 protocol == 1 时的对应配置，下游服务信息会从自定义的路由表中获取

    `colossusdb_embd_service_name`: [string] 下游 colossusdb embedding server 的服务名，一般是: {模型名称}.
      获取服务名的方法详见文档[获取Embedding](https://docs.corp.kuaishou.com/k/home/<USER>/fcAB1k2CwkZRcxQ09lTBYMRkH)

    `colossusdb_embd_table_name`: [string] 下游 colossusdb embedding server 的表名，如 test-table

    `colossusdb_use_kconf_client`: [bool] 是否用 kconf_client 包装在原生 client 外面，默认为 true.
      如果设为 false, 则一般情况下应将上述的 colossusdb_embd_service_name 修改为 grpc_clsdb_ps-{模型名称}, colossusdb_embd_table_name 不变。
      详见文档[统一存储流量治理功能说明](https://docs.corp.kuaishou.com/d/home/<USER>

    调用示例
    ------
    ``` python
    .get_remote_embedding_lite(
      kess_service = "grpc_xxx",
      id_converter = {"type_name": "mioEmbeddingIdConverter"},
      slot = 38,
      input_attr_name = "signs",
      output_attr_name = "embeddings",
    )
    ```
    """
    self._add_processor(CommonRecoRemoteEmbeddingAttrRawLiteEnricher(kwargs))
    return self

  def get_local_ann_embedding(self, **kwargs):
    """
    CommonRecoLocalAnnEmbeddingAttrEnricher
    ------
    请求本地 local ann 获取 item 的 embeddings 填充 item attr 或者用结果非空的 item 的 id 和 embedding 填充 common attrs 供其他 processor 使用

    参数配置
    ------
    `src_data_type`: [string] [动态参数] 必填项，填写需要查询的 item 的 src_data_type, 对应 ann KVRequest 中的 src_data_type 或旧版的 bucket 的值

    `item_type`: [int] 用于拼接 item_nid 生成 item_key, 默认值为 0

    `dim`: [int] 必填项，获取的 embedding 维数，必须大于 0

    `save_to_common_attr`: [bool] True 需要填写 `item_list_output_attr` `embedding_list_output_attr` `data_type_list_output_attr`，False 需要填写 `data_type_item_attr` 和 `embedding_item_attr`, 默认值为 False

    `data_type_item_attr`: [string] 用于写入 src_data_type 的 ItemAttr name, 可缺省

    `embedding_item_attr`: [string] 用于写入 embedding 的 ItemAttr name

    `item_list_output_attr`: [string] 填充 id 结果的 common attr name

    `embedding_list_output_attr`: [string] 填充 embedding 结果的 common attr name

    `data_type_list_output_attr`: [string] 填充 data_type 的 common attr name, 可缺省

    `save_as_ptr`: [bool] 默认值 false, true 表示将获取的 embedding 数据指针存入 attr

    调用示例
    ------
    ``` python
    .get_local_ann_embedding(
      src_data_type = "subdivision_user_bucket",
      dim = 64,
      embedding_item_attr = "element",
    )
    ```
    """
    self._add_processor(CommonRecoLocalAnnEmbeddingAttrEnricher(kwargs))
    return self

  def split_string(self, **kwargs):
    """
    CommonRecoStringSplitEnricher
    ------
    将 string attr 切割成 string list attr，不支持同时处理 input_common_attr 与 input_item_attr，必须配置其中一个

    参数配置
    ------
    `input_common_attr`: [string] 选配项，输入的 string common attr，需与 output_common_attr 配合使用

    `output_common_attr`: [string] 选配项，输出的 string list common attr，需与 input_common_attr 配合使用

    `input_item_attr`: [string] 选配项，输入的 string item attr，需与 output_item_attr 配合使用

    `output_item_attr`: [string] 选配项，输出的 string list item attr，需与 input_item_attr 配合使用

    `delimiters`: [string] 分割字符集合。当 delimiters 为空字符串时会按 UTF8 单字符的粒度分割并且忽略其他分割相关配置。

    `skip_empty_tokens`: [bool] 是否忽略分割出的空字符串，默认为 False。

    `trim_spaces`: [bool] 仅当 `skip_empty_tokens=True` 时有效，控制是否将仅包含空白字符的字符串也剔除掉，默认为 False。

    `max_splits`: [int] 最大分割次数，当小于 0 时不生效，默认不生效。

    `strip_whitespaces`: [bool] 是否将分割后的每个字符串去除首尾空白字符，默认为 False。

    `parse_to_int`: [bool] 是否将分割后的每个字符串转义为 int 值按 int_list 类型存储，默认为 False。

    `parse_to_double`: [bool] 是否将分割后的每个字符串转义为 double 值按 double_list 类型存储，默认为 False。

    调用示例
    ------
    ``` python
    .split_string(
      input_common_attr = "input_string",
      output_common_attr = "output_string_list",
      delimiters=",",
    )
    ```
    """
    self._add_processor(CommonRecoStringSplitEnricher(kwargs))
    return self

  def retrieve_by_redis(self, **kwargs):
    """
    CommonRecoRedisRegexRetriever
    ------
    从 redis 读取 string value，按照正则格式解析为 item 和 item_attr 并召回
    - 当 redis_cmd 为 get 时，支持多 key、cache
    - 当 redis_cmd 为 zrange 时，仅持单 key ， 不支持 cache

    参数配置
    ------
    `reason`: [int] 召回原因

    `retrieve_num`: [int] [动态参数] 必配项，召回结果的数量上限

    `cluster_name`: [string] redis 的 kcc cluster name

    `timeout_ms`: [int] 选配项，获取 redis client 的超时时间，默认为 10

    `redis_cmd`: [str] redis 命令， 支持 get/zrange，默认为 get，仅当 redis_cmd 为 get 时，支持多 key

    `zrange_start`: [int] [动态参数] 当 redis_cmd 为 zrange 时有效，起始 index ，默认为0

    `key`: [string] 指定一个固定的 redis key (注意：`key` 与 `key_from_attr` 需至少配置一项)

    `key_from_attr`: [string] 从指定的 common_attr 中获取动态的 redis key，支持 int/string/int_list/string_list 类型，int 类型会先转换为 uint64 再转换成 string (注意：`key` 与 `key_from_attr` 需至少配置一项)

    `key_prefix`: [string] [动态参数] 选配项，为 `key_from_attr` 中每个 redis key 的值添加一个前缀，默认为空

    `retrieve_num_per_key`: [int] [动态参数] 选配项，单个 key 召回的数量上限，默认为 10000

    `save_src_key_to_attr`: [string] 选配项，将 key 填入到召回 item 的指定 item attr 中 (不包含 key_prefix)

    `append_src_key_to_attr`: [string] 选配项，将 key 追加到召回 item 的指定 item attr 中 (不包含 key_prefix)，召回结果会有相同 key 想要全的 src_key list 的情况使用

    `reason_from_attr`: [int] 选配项，当配置了 `key_from_attr` 的情况下，可通过该项配置从某个 int_list 类型的 CommonAttr 中获取 reason 列表，来给 `key_from_attr` 中的每个 key 分配不同的 reason 值。两个 list 长度未对齐的部分将回退使用 `reason` 配置的值。

    `item_regex`: [string] 选配项，(**该方式性能较差，优先使用 item_separator 和 attr_separator 配置**) 单个 item 信息的正则格式，会按照捕获组 '()' 捕获值并填入对应 item_attr，第一个元素应为 item_id

    `item_separator` : [string] 选配项，item 信息之间的分隔符。无设置则不分割。不可与 item_regex 同时使用。

    `attr_separator`: [string] 选配项，item 信息的分隔符，无配置认为只有 key 。不可与 item_regex 同时使用。

    `extra_item_attrs`: [list] 选配项，每个 item 需要额外填充的 item_attrs 列表，个数与 item_pattern 中除 item_id 外的 '()' 个数对应
      - `name`: [string] 作为 item_attr 的名称
      - `type`: [string] 按什么值类型对该 attr 进行抽取，仅支持: int/double/string, 默认为 string
      - `as_score`: [bool] 选配项, 是否将该 item_attr 抽取后的值作为 item 的初始 score, 默认为 False, 注意：若设置为 True 则 `type` 必须为 double 或 int。

    `retrieval_item_type`: [int] 召回 item 的 item_type，和 item_id 一起产生 item_key，默认值为 0

    `cache_bits`: [int] 选配项，cache 大小，即最多存 2^cache_bits 个 kv 值（LRU 删除），默认为 0（无 cache）

    `cache_expire_second`: [int] 选配项，cache 内的数据过期的时间，默认为 3600 秒

    `cache_delay_delete_ms`: [int] 选配项，cache 内的数据延迟删除的时间，一般使用默认值即可，默认为 10 秒

    `cache_name`: [string] 选配项，用于在 [grafana 监控](https://grafana.corp.kuaishou.com/d/0jCdxsQMk/kv_cache_client?orgId=3) 中区分不同 cache 的命中率等信息，默认跟 cluster_name 相同

    `save_err_code_to`: [string] 选配项，将 redis 读取操作的错误码 RedisErrorCode（数字值） 存到指定的 common attr

    `save_miss_key_to`: [string] 选配项，将请求 redis 未命中的 key 存到指定的 string list 类型的 common attr 中

    调用示例
    ------
    ``` python
    # 对应 redis 格式示例
    # redis_value = "38680800976_0.99_cat,38670790701_0.38_dog,38650272587_0.33_pig"
    # [item1] item_id: 38680800976, item_attrs: {emp_ctr=0.99, tag="cat"}
    # [item2] item_id: 38670790701, item_attrs: {emp_ctr=0.38, tag="dog"}
    # [item3] item_id: 38650272587, item_attrs: {emp_ctr=0.33, tag="pig"}

    # 使用正则获取 item 信息
    .retrieve_by_redis(
      reason = 100,
      retrieve_num = 1000,
      cluster_name = "my_kcc_cluster",
      key_from_attr = "source_pid_list",
      item_regex = r"(\\d+)_([0-9]{1,}[.][0-9]*)_([a-z]*)",
      extra_item_attrs = [
        {"name": "emp_ctr", "type": "double"},
        {"name": "tag", "type": "string"}
      ]
    )

    # 使用 separator 获取 item 信息
    # item_separator = ","
    # attr_separator = "_"
    # redis_value = "38680800976_0.99_cat,38670790701_0.38_dog,38650272587_0.33_pig"
    # [item1] item_id: 38680800976, item_attrs: {emp_ctr=0.99, tag="cat"}
    # [item2] item_id: 38670790701, item_attrs: {emp_ctr=0.38, tag="dog"}
    # [item3] item_id: 38650272587, item_attrs: {emp_ctr=0.33, tag="pig"}

    # item_separator = ","
    # extra_item_attrs = []
    # redis_value = "38680800976,38670790701,38650272587"
    # [item1] item_id: 38680800976, item_attrs: {}
    # [item2] item_id: 38670790701, item_attrs: {}
    # [item3] item_id: 38650272587, item_attrs: {}

    # attr_separator = "_"
    # redis_value = "38680800976_0.99_cat"
    # [item1] item_id: 38680800976, item_attrs: {emp_ctr=0.99, tag="cat"}

    .retrieve_by_redis(
      reason = 100,
      retrieve_num = 1000,
      cluster_name = "my_kcc_cluster",
      key_from_attr = "source_pid_list",
      item_separator = ",",
      attr_separator = "_",
      extra_item_attrs = [
        {"name": "emp_ctr", "type": "double"},
        {"name": "tag", "type": "string"}
      ]
    )

    # zrange 支持
    # 对应 redis 格式示例
    # redis_value = {"38680800976_0.99_cat" ,"38670790701_0.38_dog","38650272587_0.33_pig"}
    # [item1] item_id: 38680800976, item_attrs: {emp_ctr=0.99, tag="cat"}
    # [item2] item_id: 38670790701, item_attrs: {emp_ctr=0.38, tag="dog"}
    # [item3] item_id: 38650272587, item_attrs: {emp_ctr=0.33, tag="pig"}
    .retrieve_by_redis(
      reason = 100,
      redis_cmd = "zrange",
      retrieve_num = 1000,
      cluster_name = "my_kcc_cluster",
      key_from_attr = "source_pid_list",
      attr_separator = "_",
      extra_item_attrs = [
        {"name": "emp_ctr", "type": "double"},
        {"name": "tag", "type": "string"}
      ]
    )

    # 上面示例，使用 cache，cache 目前是 lru cache
    .retrieve_by_redis(
      reason = 100,
      retrieve_num = 1000,
      cluster_name = "my_kcc_cluster",
      cache_name = "my_cache_name",
      cache_expire_second = 300,
      cache_bits = 10,
      key_from_attr = "source_pid_list",
      item_regex = r"(\\d+)_([0-9]{1,}[.][0-9]*)_([a-z]*)",
      extra_item_attrs = [
        {"name": "emp_ctr", "type": "double"},
        {"name": "tag", "type": "string"}
      ]
    )
    ```
    """
    self._add_processor(CommonRecoRedisRegexRetriever(kwargs))
    return self

  def retrieve_id_by_rpc(self, **kwargs):
    """
    CommonRecoRpcIdPoolRetriever
    ------
    从 rpc 获取一个 id list， 多线程合作遍历 (retrieve) 全部 id ，遍历完成后再请求 rpc 获取新的 id list
    每个线程持有一个 RpcIdPoolProxy 对象、利用 GetNextId 和 GetNextIdList 完成遍历

    参数配置
    ------
    `retrieval_type` : [int] 遍历方式，0 : 获取一个 id 并设置为 context 的 user id (实际是 enricher)
                                       1 : 获取一个 id 并设置为IntCommonAttr, 字段名为 output_attr 指定 (实际是 enricher)
                                       默认 : 获取多个 id 多为 photo retrieval 的结果列表 (retriever)

    `retrieval_num` : [int] 默认的 retrieval_type 时，该值指定要获取的 id 数量

    `retrieval_reason` : [int] 默认的 retrieval_type 时，该值指定 item reason

    `output_attr` : [string] retrieval_type=1 时，该值指定 id 存 common attr 的字段名

    `rpc_service` : [int] 指定 rpc kess name

    `rpc_cluster` : [int] 指定 rpc kess 集群名, 默认 PRODUCTION

    `rpc_timeout_ms` : [int] 请求rpc是的超时阈值，默认 100 ms

    调用示例
    ------
    ``` python
    .retrieve_id_by_rpc(
      retrieve_num=500,
      retrieve_reason=0,
      rpc_service="grpc_xxxx",
      rpc_timeout_ms=1000)
      ```
      """
    self._add_processor(CommonRecoRpcIdPoolRetriever(kwargs))
    return self

  def retrieve_id_by_redis(self, **kwargs):
    """
    CommonRecoRedisIdPoolRetriever
    ------
    用于多线程合作遍历 redis 的多 key id 集合, 每次遍历可以从 redis id 池中获取 1 个或多个 id
    譬如多线程并行循环遍历 redis 里 user0 ~ user49 为 key 对应的 user id 列表，每个 key 有 10w id 需要遍历
    每个线程持有一个 RedisIdPoolProxy 对象、利用 GetNextId 和 GetNextIdList 完成遍历
    读取 redis 更新数据时性能较差，不适合高性能场景

    参数配置
    ------
    `retrieval_type` : [int] 遍历方式，0 : 获取一个 id 并设置为 context 的 user id (实际是 enricher)
                                       1 : 获取一个 id 并设置为IntCommonAttr, 字段名为 output_attr 指定 (实际是 enricher)
                                       默认 : 获取多个 id 多为 photo retrieval 的结果列表 (retriever)

    `retrieval_num` : [int] 默认的 retrieval_type 时，该值指定要获取的 id 数量

    `retrieval_reason` : [int] 默认的 retrieval_type 时，该值指定 item reason

    `output_attr` : [string] retrieval_type=1 时，该值指定 id 存 common attr 的字段名

    `redis_key_prefix` : [string] 需要遍历的 redis key 前缀

    `redis_key_num` : [int] 需要遍历的 redis key 数量

    `redis_value_get_type` : [int] 指定请求 redis 的方式, 目前只支持 = 0 : Get(string key, string &value)

    `redis_value_parse_type` : [int] 指定解析 value 的方式，目前只能支持 = 0 : 解析 num1,num2,...,numN 的数字字符序列序列

    `redis_cluster` : [int] 指定 redis 集群名

    `redis_timeout_ms` : [int] 访问 redis 的超时阈值 ，默认 200 ms

    `redis_io_threads` : [int] 访问 redis 单个 client 的 io 线程数，一般无需修改，默认为 2

    `redis_biz_name` : [int] 访问 redis 集群所属业务，可以不填写, 默认为空

    `redis_replica` : [int] redis 集群是否开启双集群同步，一般无需修改，默认为 true

    调用示例
    ------
    ``` python
    .retrieve_id_by_redis(
      retrieval_type = 0,
      redis_key_prefix="user_ids",
      redis_key_num=50,
      redis_value_get_type=0,
      redis_value_parse_type=0,
      redis_cluster="recoNewUserOffline",
      redis_timeout_ms=1000)
      ```
      """
    self._add_processor(CommonRecoRedisIdPoolRetriever(kwargs))
    return self

  def base64(self, **kwargs):
    """
    CommonRecoBase64Enricher
    ------
    base64 encode 或 decode

    `mode` 为 "encode" ，将 String/String List/PB Message 进行 encoding 写入指定的 attr

    `mode` 为 "decode" ，将 String/String List 进行 decoding 写入指定的 attr

    参数配置
    ------
    `mode`: [string] 必配项, 只能是 {"encode", "decode"} 中的的一个

    `is_common_attr`: [bool] 选配项，是否为 common attr，为 True 时从 common attr 读取 attr，结果存到 common attr，否则从 item attr 读取 attr，结果存到 item attr，默认为 True。

    `input_attr`: [string] 必配项，待编码的 attr

    `output_attr`: [string] 必配项，编码后的 attr

    调用示例
    ------
    ``` python
    .base64(
      mode="encode",
      is_common_attr=True,
      input_attr="batched_samples",
      output_attr="batched_samples_encoded",
    )
    ```
    """
    self._add_processor(CommonRecoBase64Enricher(kwargs))
    return self

  def encrypted_id(self, **kwargs):
    """
    CommonRecoEncryptedIdEnricher
    ------
    使用快手的 encrypted_id 库对客户端上报的 photo id 或者 live id 进行加密解密，当输入为 list 时输出也为 list。当加解密失败时，如果是输出是 int 则填 0，如果输出是 string 则填空字符串。

    `mode` 为 "encrypt_photo_id" ，将 int 作为 photo_id 加密为 int

    `mode` 为 "decrypt_photo_id" ，将 string 作为 photo_id 解密为 int

    `mode` 为 "encrypt_live_id" ，将 int 作为 live_id 加密为 string

    `mode` 为 "decrypt_live_id" ，将 string 作为 live_id 解密为 int

    `mode` 为 "decrypt_id" ，将 string 作为 photo_id 或 live_id 解密为 int

    参数配置
    ------
    `mode`: [string] 必配项, 只能是 {"encrypt_photo_id", "decrypt_photo_id", "encrypt_live_id", "decrypt_live_id", "decrypt_id"} 中的的一个

    `input_attr`: [string] 必配项，输入的 attr

    `output_attr`: [string] 必配项，输出的 attr

    `is_common_attr`: [bool] 输入输出是 common_attr 还是 item_attr，默认为 False，即输入输出都是 item attr

    `reset_errno`: [bool] [动态参数] 修复由于 errno 导致的 encrypted_id 解密失败问题

    调用示例
    ------
    ``` python
    .encrypted_id(
      mode="encrypt_photo_id",
      input_attr="photo_id",
      output_attr="encrypted_photo_id",
    )
    ```
    """
    self._add_processor(CommonRecoEncryptedIdEnricher(kwargs))
    return self

  def zstd(self, **kwargs):
    """
    CommonRecoZstdEnricher
    ------
    zstd 压缩或解压缩

    不支持同时处理 input_common_attr 与 input_item_attr，必须配置其中一个

    `mode` 为 "compress" ，将 String 进行 compressing 写入指定的 common_attr

    `mode` 为 "decompress" ，将 String 进行 decompressing 写入指定的 common_attr

    参数配置
    ------
    `mode`: [string] 必配项, 只能是 {"compress", "decompress"} 中的一个

    `input_common_attr`: [string] 选配项，待压缩或解压缩的 string 类型的 common_attr，与 output_common_attr 配合使用

    `output_common_attr`: [string] 选配项，压缩或解压缩后的 string 类型的 common_attr，与 input_common_attr 配合使用

    `input_item_attr`: [string] 选配项，待压缩或解压缩的 string 类型的 item_attr， 与 output_item_attr 配合使用

    `output_item_attr`: [string] 选配项，压缩或解压缩后的 string 类型的 item_attr，与 input_item_attr 配合使用

    `compression_level`: [int][动态参数] mode 为 "compress" 时选配，目前支持压缩级别 [-5 ~ 22]，默认值为 3，其中 1 ~ 22 为常规级别当级别 >= 20 时标记为 `--ultra`，需要更多的内存需谨慎使用。此外，还支持负的压缩级别，级别越低压缩速度越快，压缩量越小

    调用示例
    ------
    ``` python
    .zstd(
      mode="decompress",
      input_common_attr="compressed_str",
      output_common_attr="original_str",
    )
    ```
    """
    self._add_processor(CommonRecoZstdEnricher(kwargs))
    return self

  def string_format(self, **kwargs):
    """
    CommonRecoStringFormatEnricher
    ------
    **该接口已进入 DEPRECATE 状态，不再进行功能更新，建议改用 [str_format](#str_format) 方法**

    将 common / item 侧的 `input_attrs` 按照 printf 的格式 format 成 String ，保存到 `output_attr` 中

    `input_attrs` 列表中的每一项限制如下:
      - 只支持如下类型: Int64(%ld/%lu) / Double (%f)/ String (%s)
      - 不支持列表类型
      - 对于缺失的 int/double 值将使用 0 值代替，对于缺失的 string 值将使用字符串 "(null)" 代替

    该接口与 [str_format](#str_format) 的区别：
    - format 占位符格式支持类型有限。
    - 对于不存在 input attr 的情况：若 format 格式为 `%s` 将被处理为字符串 "(null)"；若为 `%d` 等数字类型格式将用 0 值代替。

    参数配置
    ------
    `is_common_attr`: [bool] 选配项，是否对 common 侧做 format 处理，默认为 True

    `format_string`: [string] format 字符串, 语法同 c 语言的 printf ，只支持子集

    `input_attrs`: [List[string]] 必配项，用于 format 的 attr 列表，列表长度和 attr 的类型必须和 `format_string` 一致

    `output_attr`: [string] 必配项， format 后的 attr

    调用示例
    ------
    ``` python
    .string_format(
      is_common_attr=True,
      format_string="%lu %s",
      input_attrs=["primary_key", "batched_samples_encoded"],
      output_attr="batched_samples_kv",
    )
    ```
    """
    self._add_processor(CommonRecoStringFormatEnricher(kwargs))
    return self

  def str_format(self, **kwargs):
    """
    CommonRecoStrFormatEnricher
    ------
    将 common / item 侧的 `input_attrs` 按照 printf 的格式 format 成 string 保存到 `output_attr` 中。

    该接口与 [string_format](#string_format) 的区别：
    - 支持所有 format 占位符格式，详见[链接](https://www.cplusplus.com/reference/cstdio/printf/)
    - 可配置对于不存在 input attr 的处理方式，用默认值 0 代替，或是直接 format 失败不生成 output attr。对于 string 类型，默认值为空字符串，而非 "(null)"。

    参数配置
    ------
    `is_common_attr`: [bool] 选配项，是否对 common 侧做 format 处理，默认为 True

    `format_string`: [string] 必配项，format 字符串，语法同 C 语言的 [printf 格式](https://www.cplusplus.com/reference/cstdio/printf/)

    `input_attrs`: [list] 必配项，用于 format 的 attr 列表，attr 的数目及类型必须和 `format_string` 中的占位符数目及类型一致，否则会 format 失败

    `output_attr`: [string] 必配项，将 format 后的 string 值写入指定 attr

    `fill_default_val`: [string] 选配项，默认为 True，如果部分 item 缺失 input attr，是否采用 0 作为默认值填充 format string；若为 False 则不填充，output attr 将不被生成

    调用示例
    ------
    ``` python
    .str_format(
      format_string="%lu %s",
      input_attrs=["primary_key", "batched_samples_encoded"],
      output_attr="batched_samples_kv",
    )
    ```
    """
    self._add_processor(CommonRecoStrFormatEnricher(kwargs))
    return self

  def mark_stage_end(self, **kwargs):
    """
    CommonRecoMarkStageObserver
    ------
    用来标记一个 Stage (流程) 阶段的结束。

    该 Processor 表示一个 Stage 的结束，会默认从请求开始或者上一个 Stage 的结束开始计算。

    将用于：
    - Grafana 里面会有 [Stage 粒度的监控](https://grafana.corp.kuaishou.com/d/S10M9QdZk/commonleafjian-kong?orgId=3&fullscreen&panelId=4974)；
    - 先知等周边工具也会根据 Stage 来进行优化展示等。

    参数配置
    ------
    `stage_name`: [string] 该 Stage 的名字。

    调用示例
    ------
    ``` python
    .mark_stage_end(
      stage_name="ranking"
    )
    ```
    """
    self._add_processor(CommonRecoMarkStageObserver(kwargs))
    return self

  def perflog(self, **kwargs):
    """
    CommonRecoPerflogObserver
    ------
    自由的 perflog 上报，每一个字段都可以自定义😊

    数据上报到 perflog-clickhouse

    namespace 和 talbe 表的关系参考 https://docs.corp.kuaishou.com/k/home/<USER>/fcADbZWCVFUyTmuAUzcFpbKcO

    参数配置
    ------
    `mode`: [string] 设置 perf 上报的类型，支持 count 和 interval 模式。

    `value`: [int/double] [动态参数] 选配项 设置 perf 上报的值。
      - interval 模式：支持 int 或 double 类型值的上报。默认值为 0。
      - count 模式：支持 int 类型值的上报。默认值为 1。

    `boost_factor`: [int] 上报模式为 interval 时，对上报 value 的放大系数（上报值为 value * boost_factor）。默认值为 1。

    `namespace`: [string] [动态参数] 设置 perf 上报的 namespace。

    `subtag`: [string] [动态参数] 设置 perf 上报的 sub_tag。

    `extra1`: [string] [动态参数] 选配项 设置 perf 上报的 ext1，默认为当前服务名。

    `extra2`: [string] [动态参数] 选配项 设置 perf 上报的 ext2，默认为当前请求的 request_type 值。

    `extra3`: [string] [动态参数] 选配项 设置 perf 上报的 ext3，默认为空字符串。

    `extra4`: [string] [动态参数] 选配项 设置 perf 上报的 ext4，默认为空字符串。

    `extra5`: [string] [动态参数] 选配项 设置 perf 上报的 ext5，默认为空字符串。

    `extra6`: [string] [动态参数] 选配项 设置 perf 上报的 ext6，默认为空字符串。

    调用示例
    ------
    ``` python
    .perflog(mode = 'interval', namespace = 'merchant', subtag = 'retrieveTime')
    .perflog(mode = 'count', namespace = 'merchant', subtag = 'retrieveCount')
    ```
    """
    self._add_processor(CommonRecoPerflogObserver(kwargs))
    return self

  def truncate_by_attr(self, **kwargs):
    """
    CommonRecoAttrTruncateArranger
    ------
    对结果集大小按指定 item_attr 进行等比例缩减，支持保量和打压

    参数配置
    ------
    `attr_name`: [string] 用于分组截断的 item_attr 名称

    `size_limit`: [int] [动态参数] 截断当前结果集最多保留前多少个 item
      - 如果 `size_limit` 小于 0，该 processor 不会进行截断
      - 如果 `size_limit` 大于等于 0，那么每个 attr 的数目会进行等比例缩减，最终保留数量 = `size_limit / distance(begin, end)`

    `queues`: [list] 每个 attr_value 可以覆盖配置全局的截断逻辑（注意：每个 queue 里可单独配置 limit 或 ratio）
      - `attr_value`: [int | string] attr 值
      - `ratio`: [double] [动态参数] 该 attr 保留的比例，如果为 1.0 代表获取全部 items
      - `limit`: [int] [动态参数] 该 attr 保留的数目，限制的数目，`ratio` 与 `limit` 同时存在时取两者的最小值（保留更少的 item）
      - `mode`: [string] 当全局配置与该 attr_value 配置不一致时，如何选择最终结果，可选值："max"、"min"、"default"，默认 "default" ：单独配置优先

    `allow_overflow`: [boolean] 是否允许最终结果集数量超出 `size_limit` 限制, 默认 True
      - 只对 `queues` 中没有单独配置的 attr_value 有限制作用
      - 如果 `queues` 中配置的 attr_value 截断数量已经超过了 `size_limit` , 最终结果数量仍会超出

    调用示例
    ------
    ``` python
    .truncate_by_attr(
        attr_name="bucket_name",
        size_limit="{{limit}}",
        allow_overflow=True,
        queues=[
            {
              "attr_value": "country",
              "ratio": 1.0,
              "limit": 5,
              "mode": "min"
            },
            {
              "attr_value": "city",
              "ratio": "{{bucket_ratio}}",
              "limit": "{{bucket_limit}}",
            }
        ],
    )
    ```
    """
    self._add_processor(CommonRecoAttrTruncateArranger(kwargs))
    return self

  def retrieve_id_by_btq(self, **kwargs):
    """
    CommonRecoBtqIdPoolRetriever
    ------
    用于多线程合作消费 btq id , 有一个 buffer set 从 btq 接受 id 并去重，然后多线程依次从 buffer set 里消费并删除 id
    消费的 id 所谓 retrieval 结果

    参数配置
    ------
    `retrieval_type`: [int] 遍历方式，0 : 获取一个 id 并设置为 context 的 user id (实际是 enricher)
                                       1 : 获取一个 id 并设置为IntCommonAttr, 字段名为 output_attr 指定 (实际是 enricher)
                                       默认 : 获取多个 id 多为 photo retrieval 的结果列表 (retriever)

    `retrieval_num`: [int] 默认的 retrieval_type 时，该值指定要获取的 id 数量

    `retrieval_reason`: [int] 默认的 retrieval_type 时，该值指定 item reason

    `output_attr`: [string] retrieval_type=1 时，该值指定 id 存 common attr 的字段名

    `queues`: [list[string]] 要读取的 btq queue list

    `group_name`: [string] 消费者 group name

    `batch_size`: [int] 消费 btq 时 message batch size, 默认 64

    `buffer_size`: [int] id buffer set 存储的最大 id 量，防止 oom, 默认 400000000

    `thread_num`: [int] 后台消费 btq 的线程数，默认为 1

    调用示例
    ------
    ``` python
    .retrieve_id_by_btq(
      retrieval_type = 0,
      queues=["user_ids"],
      batch_size=64,
      buffer_size=400000000,
      thread_num=4)
      ```
      """
    self._add_processor(CommonRecoBtqIdPoolRetriever(kwargs))
    return self

  def aggregate_mapping_list(self, **kwargs):
    """
    CommonRecoMappingAggregateEnricher
    ------
    item 匹配 user 侧数据，并把聚合结果保存至 item_attr

    user 侧数据由 n 个等长的 int_list_attr 组成（n >= 1）。其中至少包含一个 key_list_attr，作为匹配的 user 侧键值。其余 value_list_attr 作为聚合统计的原始值。
    （即"key_list_attr" 和 "value_list_attr" 必须使用 int_list_attr 类型的 common attr，并且 list 长度需要一致，不一致时会取最短的长度。）
    item 侧使用 item_key 作为匹配的 item 侧键值（可以指定 match_key_in_item_attr 代替 item_key）。

    遍历候选集中的每个 item， 以 item_key 作为 key，与 key_list_attr 中的每个值匹配，匹配到的列进一步进行 aggregate_config 中的聚合计算。
    将聚合的结果保存至某个 item_attr 中。匹配到的个数也可以保存至某个 item_attr 中。

    参数配置
    ------
    `key_list_attr`: [string] 作为 user 侧的特征匹配的 key 列表

    `match_key_in_item_attr`: [string] [选配项] 使用某个 int_item_attr 的值代替 item_key 作为匹配的键值

    `save_for_match_key`: [boolean] [选配项] 产出的 item_attr 是否保存在 match_key_in_item_attr 下。默认为 false，保存至原 item_key 下

    `aggregate_config`: [list] common_attr 计算配置
      - `value_list_attr`: [string] 统计的 common_attr_name，
      - `aggregator`: [string] 计算函数，支持 sum/min/max
      - `save_result_to`: [string] 结果保存至某个 item_attr

    `save_count_to`: [string] [选配项] 匹配的个数保存到某个 item_attr

    调用示例
    ------
    ``` python
    .aggregate_mapping_list(
      key_list_attr = "seller_id_list",
      match_key_in_item_attr = "seller_id",
      aggregate_config = [
        { "value_list_attr" : "buy_item_price_list", "aggregator" : "sum", "save_result_to" : "buy_item_price_sum"},
        { "value_list_attr" : "pay_time_list", "aggregator" : "max", "save_result_to" : "seller_max_buy_time"}],
      save_count_to = "match_count")
      ```
      """
    self._add_processor(CommonRecoMappingAggregateEnricher(kwargs))
    return self

  def enrich_norm_and_discrete(self, **kwargs):
    '''
    CommonRecoNormAndDiscreteEnricher
    ------
    把某个特征根据给定的分位点列表进行离散化和归一化操作

    参数配置
    ------
    `is_common_attr`: [bool] 必配项 处理的特征是否是 common attr

    `input_attr`: [string] 必配项 待处理的特征名称, 支持四种数据类型： int double int_list double_list

    `output_norm_attr`: [string] 选配项 归一化后的特征名称，shape 与 input_attr 相同，数据类型 double，数据范围 [0,1]

    `output_discrete_attr`:[string] 选配项 离散化后的特征名称，shape 与 input_attr 相同，数据类型 int，数据范围 [0, len(quantile_list) - 1)

    `quantile_list`: [double_list] [动态参数] 必配项 分位点数据，包含最小值与最大值。比如二分位点：[ 最小值，1 分位点，2 分位点，最大值 ]

    调用示例
    ------
    ``` python
    .enrich_norm_and_discrete(
      is_common_attr=False,
      input_attr='fr_pctr',
      output_norm_attr='fr_pctr_norm',
      output_discrete_attr="fr_pctr_percentile",
      quantile_list=[0,0.1,0.2,0.3, 1.0]
    )
    ```
    '''
    self._add_processor(CommonRecoNormAndDiscreteEnricher(kwargs))
    return self

  def get_abtest_exp_info(self, **kwargs):
    '''
    CommonRecoAbtestExpInfoEnricher
    ------
    从 abtest 系统中根据世界名或者参数名获得实验信息存入 CommonAttr 中

    参数配置
    ------
    `user_id`: [int] [动态参数] 选配项，指定获取 ab 参数时使用的 user_id ，缺省时，使用当前请求的 user_id

    `device_id`: [string] [动态参数] 选配项，指定获取 ab 参数时使用的 device_id ，缺省时，使用当前请求的 device_id

    `biz_name`: [string] 选配项，ab 参数所属业务，即 abtest 网站上全大写字母的“关联 App 组”（ab2.0）名称。使用 ab_key 获取实验信息时需要填写此项。

    `exp_info`: [list] world 和 param 二者选填一个。如果配置了 param，会先根据参数名获取所在 world，再获取命中的实验信息。
      - `world`: [string] [动态参数] 世界的名称
      - `ab_key`: [string] 参数的名称。使用 ab_key 需要同时配置 biz_name。注意此方式仅支持ab2.0的参数。
      - `save_exp_id_to`: [string] 获取的实验名称写入的 CommonAttr 的名称，可缺省
      - `save_group_id_to`: [string] 获取的实验名称写入的 CommonAttr 的名称，可缺省

    调用示例
    ------
    ``` python
    .get_abtest_exp_info(
      exp_info=[{
        "world": "world_name",
        "save_exp_id_to": "experiment_name",
        "save_group_id_to": "group_name"
      }]
    )
    ```
    '''
    self._add_processor(CommonRecoAbtestExpInfoEnricher(kwargs))
    return self

  def copy_attr(self, **kwargs):
    '''
    CommonRecoCopyAttrEnricher
    ------
    将 Common/Item Attr 拷贝到新的 Common/Item Attr 中。

    注意：from/to 必须且只能配置一个。

    Tips：
    - from_common + to_item 相当于把 common attr 给所有 item 拷贝一份
    - from_item + to_common 就只选第一个有值的 item attr 复制到 common

    参数配置
    ------
    `attrs`: [list]
      - `from_common`: [string] 需要被拷贝的 common_attr 名称
      - `from_item`: [string] 需要被拷贝的 item_attr 名称
      - `to_common`: [string] 需要将拷贝值存入的 common_attr 名称
      - `to_item`: [string] 需要将拷贝值存入的 item_attr 名称
      - `overwrite`: [bool] 选配项，是否覆盖原有的值，默认为 True

    调用示例
    ------
    ``` python
    .copy_attr(
      attrs=[{
        "from_common": "common_attr_name",
        "to_common": "copy_common_attr_name"
      }]
    )
    ```
    '''
    self._add_processor(CommonRecoCopyAttrEnricher(kwargs))
    return self

  def set_attr_default_value(self, **kwargs):
    '''
    CommonRecoAttrDefaultValueEnricher
    ------
    给 ItemAttr 设置一个默认值，如果和原有的 Attr 类型不一致，则不设置默认值

    是给 ItemAttr 独立的 default value 字段设置指定的默认值（n 个 item 只发生 1 次赋值操作）。某个 item 缺失的 ItemAttr 依然为空，只是在 get attr value 时会返回该 ItemAttr default value 字段的值
    相比 `set_default_value()` 不会给该 ItemAttr 下每一个 Item 实际去设置默认值，性能更高

    参数配置
    ------
    `item_attrs`: [list] 默认值配置
      - `name`: [string] 待设置的 ItemAttr 名称
      - `type`: [string] 待设置的 ItemAttr 值类型，可选值：int, double, string, int_list, double_list, string_list
      - `value`: [int|double|string|int_list|double_list|string_list] [动态参数] 待设置的 ItemAttr 值，需要和 type 保持类型一致
      - `clear_existing_value`: [bool] 是否清除待设置的 ItemAttr 的原始值，默认为 False

    调用示例
    ------
    ``` python
    .set_attr_default_value(
      item_attrs=[
        {
          "name": "type",
          "type": "int_list",
          "value": [3,4,5]
        }
      ]
    )
    ```
    '''
    self._add_processor(CommonRecoAttrDefaultValueEnricher(kwargs))
    return self

  def enrich_attr_by_light_function(self, **kwargs):
    '''
    CommonRecoLightFunctionEnricher
    ------
    对于一些相对简单且特殊的业务逻辑，可以封装成 c++ 轻函数，不用新加一个 processor，且不需要承受 lua 的性能损失

    参数配置
    ------
    `import_common_attr`: [list] 选配项，需要导入到 lua 脚本全局变量的 common_attr 名称列表（支持所有 attr 类型），支持重命名

    `import_item_attr`: [list] 选配项，需要导入到 lua 脚本全局变量的 item_attr 名称列表（支持所有 attr 类型）

    `export_common_attr`: [list] 选配项，将 lua 脚本的返回值依次导出到指定的 common_attr 中（支持所有 attr 类型），支持重命名

    `export_item_attr`: [list] 选配项，将 lua 脚本的返回值依次导出到指定的 item_attr 中（支持所有 attr 类型）

    `function_name`: [string]  必配项，指定使用的函数

    `class_name`: [string]  必配项，指定函数集合所在的 C++ 类（通常该类放在各自业务目录下）

    调用示例
    ------
    ``` python
    .enrich_attr_by_light_function(
      import_item_attr = [
        "cascade_fc_pctr",
      ],
      export_item_attr = [
        "cascade_pctr",
      ],
      function_name = "ReplaceMcPctr",
      class_name = "ExploreLightFunctionSet",
    )
    ```
    '''
    self._add_processor(CommonRecoLightFunctionEnricher(kwargs))
    return self

  def normalize_attr(self, **kwargs):
    """
    CommonRecoNormalizationEnricher
    ------
    对 double 类型 item attr 数据归一化&标准化

    参数配置
    ------

    `input_attr`: [string] 待计算的 xtr/score, double 类型

    `output_attr`: [string] 归一化/标准化 后生成的 xtr/score

    `mode`: [string] min_max_scale, mean_based_scale, min_based_scale, stdev_based_scale, max_based_scale, 默认为 min_max_scale

    `eps`: [double] 浮点数精度, 默认值 0.0000001

    `default_val`: [double] 默认值 0.0

    `alpha`: [double] 用于非零值优化,防止 norm 后值为 0, 默认值 0.0

    调用示例
    ------
    ``` python
    .normalize_attr(
      input_attr="fr_evr",
      output_attr="fr_evr_scaled",
      mode="min_max_scale",
      default_val=0.0
    )
    ```
    """
    self._add_processor(CommonRecoNormalizationEnricher(kwargs))
    return self

  def get_common_attr_from_memcached(self, **kwargs):
    """
    CommonRecoMemcachedCommonAttrEnricher
    ------
    读取 memcached value 写入指定的 common_attr 中。

    参数配置
    ------
    `cluster_name`: [string|string_list] memcached 的 kcc cluster name，可以配置单集群或双集群

    `timeout_ms`: [int] 读取 memcached value 的超时时间，默认为 10 毫秒

    `query_key`: [string] [动态参数] 作为 memcached key 的 commonAttr 名

    `key_prefix`: [string] memcached key 的前缀，默认为 ""

    `output_attr_name`: [string] value 写入的 commonAttr 名

    `conn_pool_init_size`: [int] memcached 初始连接池大小，默认值 32

    `conn_pool_max_size`: [int] memcached 最大连接池大小，默认值 64

    调用示例
    ------
    ``` python
    .get_common_attr_from_memcached(
      cluster_name = ["memcached_cluster_name1", "memcached_cluster_name2"],
      query_key = "{{did}}",
      key_prefix = "did_",
      output_attr_name = "did_memcached_value"
    )
    ```
    """
    self._add_processor(CommonRecoMemcachedCommonAttrEnricher(kwargs))
    return self

  def pack_attr_to_bytes(self, **kwargs):
    """
    CommonRecoPackBytesEnricher
    ------
    将 common attr 或 item attr 按给定的格式转成 bytes 存到 common attr 或 item attr。
    可以使用 unpack_bytes_to_attr 进行反解。
    
    参数配置
    ------
    `is_common`: [bool] 如果为 True 则输入输出都是 common attr，否则输入输出都是 item attr。

    `schema`: [list[dict]] 输入字段，每个 object 对应一个 attr，需要包含两个字段：
      - `attr_name`: [string] 输入的 attr 名
      - `dtype`: [string] 为输出中的数据类型，当前支持 int8/int16/int32/int64/fp16/fp32/fp64/bytes

    `output_attr_name` : [string] 输出的 bytes 存到哪个 attr。

    调用示例
    ------
    ``` python
    .pack_attr_to_bytes(
      is_common=True,
      schema=[
        dict(attr_name="common_int1", dtype="int8"),
        dict(attr_name="common_int2", dtype="int16"),
        dict(attr_name="common_int3", dtype="int32"),
        dict(attr_name="common_int4", dtype="int64"),
        dict(attr_name="common_float0", dtype="fp16"),
        dict(attr_name="common_float1", dtype="fp32"),
        dict(attr_name="common_float2", dtype="fp64"),
        dict(attr_name="common_byte1", dtype="bytes"),
        dict(attr_name="common_int_list1", dtype="int8"),
        dict(attr_name="common_int_list2", dtype="int16"),
        dict(attr_name="common_int_list3", dtype="int32"),
        dict(attr_name="common_int_list4", dtype="int64"),
        dict(attr_name="common_float_list1", dtype="fp32"),
        dict(attr_name="common_float_list2", dtype="fp64"),
        dict(attr_name="common_byte_list1", dtype="bytes"),
      ],
      output_attr_name="output_common_bytes")

    .pack_attr_to_bytes(
      is_common=False,
      schema=[
        dict(attr_name="item_int1", dtype="int8"),
        dict(attr_name="item_int2", dtype="int16"),
        dict(attr_name="item_int3", dtype="int32"),
        dict(attr_name="item_int4", dtype="int64"),
        dict(attr_name="item_float0", dtype="fp16"),
        dict(attr_name="item_float1", dtype="fp32"),
        dict(attr_name="item_float2", dtype="fp64"),
        dict(attr_name="item_byte1", dtype="bytes"),
        dict(attr_name="item_int_list1", dtype="int8"),
        dict(attr_name="item_int_list2", dtype="int16"),
        dict(attr_name="item_int_list3", dtype="int32"),
        dict(attr_name="item_int_list4", dtype="int64"),
        dict(attr_name="item_float_list1", dtype="fp32"),
        dict(attr_name="item_float_list2", dtype="fp64"),
        dict(attr_name="item_byte_list1", dtype="bytes"),
      ],
      output_attr_name="output_item_bytes")
    )
    ```
    """
    self._add_processor(CommonRecoPackBytesEnricher(kwargs))
    return self

  def unpack_bytes_to_attr(self, **kwargs):
    """
    CommonRecoUnpackBytesEnricher
    ------
    对pack_attr_to_bytes 打包的bytes 进行反解：
    将 common bytes 或 item bytes 按给定的格式反解回 common attr 或 item attr。

    参数配置
    ------
    `is_common`: [bool] 如果为 True 则输入输出都是 common attr，否则输入输出都是 item attr。

    `source_attr_name` : [string] 输入待解析的 bytes attr。

    `is_array`: [bool] 如果为 True 则将source_attr 作为schema 的元组列表处理，输出字段是解析后字段的列表。

    `schema`: [list[dict]] 解析格式及输出字段，每个 object 对应一个 attr，需要包含两个字段:
        - `attr_name`: [string] 输入的 attr 名
        - `dtype`: [string] 为输出的数据类型，当前支持 int8/int16/int32/int64/fp16/fp32/fp64

    调用示例
    ------
    ``` python
    .unpack_bytes_to_attr(
      is_common=True,
      is_array=False,
      source_attr_name="input_common_bytes"
      schema=[
        dict(attr_name="common_int1", dtype="int8"),
        dict(attr_name="common_int2", dtype="int16"),
        dict(attr_name="common_int3", dtype="int32"),
        dict(attr_name="common_int4", dtype="int64"),
        dict(attr_name="common_float1", dtype="fp16"),
        dict(attr_name="common_float2", dtype="fp32"),
        dict(attr_name="common_float3", dtype="fp64"),
      ])

    .unpack_bytes_to_attr(
      is_common=True,
      is_array=True,
      source_attr_name="input_common_bytes_list"
      schema=[
        dict(attr_name="common_int1_list", dtype="int8"),
        dict(attr_name="common_int2_list", dtype="int16"),
        dict(attr_name="common_int3_list", dtype="int32"),
        dict(attr_name="common_int4_list", dtype="int64"),
        dict(attr_name="common_float1_list", dtype="fp16"),
        dict(attr_name="common_float2_list", dtype="fp32"),
        dict(attr_name="common_float3_list", dtype="fp64"),
      ])

    .unpack_bytes_to_attr(
      is_common=False,
      is_array=False,
      source_attr_name="input_item_bytes"
      schema=[
        dict(attr_name="item_int1", dtype="int8"),
        dict(attr_name="item_int2", dtype="int16"),
        dict(attr_name="item_int3", dtype="int32"),
        dict(attr_name="item_int4", dtype="int64"),
        dict(attr_name="item_float1", dtype="fp16"),
        dict(attr_name="item_float2", dtype="fp32"),
        dict(attr_name="item_float3", dtype="fp64"),
      ]
    )
    ```
    """
    self._add_processor(CommonRecoUnpackBytesEnricher(kwargs))
    return self

  def pack_multi_item_attrs(self, **kwargs):
    """
    CommonRecoItemMultiAttrPackEnricher
    ------
    将 item 的多个 attr 合并成一个新的 common attr, 支持 int/double/string attr 合并，支持多种合并方式

    参数配置
    ------
    `from_attrs`: [list of string] 需要合并的 item attr 列表，需要自行保证 attr 是相同数据类型

    `to_attr`: [string] 合并后写入的 common attr name

    `pack_mode`: [string] 合并类型，目前支持:
                 "concat_rowmajor": 将所有 item 的多个 item attr 以 rowmajor 的方式合并成一个新的 common attr,
                 "concat_colmajor": 将所有 item 的多个 item attr 以 colmajor 的方式合并成一个新的 common attr

    `default_value` : [int/float/string] 如果 item attr 缺失时，以此值填充，与 from_attrs 保持类型相同

    调用示例
    ------
    ``` python
    .pack_multi_item_attrs(
      pack_mode = "concat_rowmajor",
      from_attrs = ["attr1", "attr2", "attr3"],
      to_attr = "concat_attr",
      default_value = 0.0,
    )
    ```
    """
    self._add_processor(CommonRecoItemMultiAttrPackEnricher(kwargs))
    return self

  def enrich_attr_by_json(self, **kwargs):
    """
    CommonRecoJsonStringAttrEnricher
    ------
    从 plain json string 中获取配置值并作为 CommonAttr/ItemAttr 填入 Context 中

    参数配置
    ------
    `json_attr`: [string] 待解析的 plain json string 的 Attr 名称

    `json_from_item_attr`: [bool] `json_attr` 是否来自 ItemAttr，默认为 False (即来自 CommonAttr)

    `json_configs`: [list]
      - `json_path`: [string] 指定用于寻值的 json_path，支持 int 或 string 类型的 attr 作为动态值(字符串若为 "{{}}" 格式将作为 CommonAttr 展开处理)，string 类型的 path 可用句点 '.' 分隔多层级："field1.filed2[].filed3"，"[]"表示该字段是列表类型（列表类型的字段只支持解析整个列表，不支持按索引取值）
      - `export_common_attr`: [string] 将 json_path 对应的值写入指定的 CommonAttr（仅支持 int/double/string/int_list/double_list/string_list 6 种类型的值）; `json_from_item_attr` 为 `True` 时只支持 list 类型(int_list/double_list/string_list)
      - `export_item_attr`: [string] 将 json_path 对应的值写入指定的 ItemAttr（仅支持 int/double/string/int_list/double_list/string_list 6 种类型的值）; `json_from_item_attr` 为 `True` 时只支持单值类型(int/double/string)
      - `default_value`: [int|double|string|bool] 选配项，在 json_path 下取值失败时取用的默认值；`json_from_item_attr` 为 `True` 时会在某个 item 值不存在时设于置默认值
      - `to_json_str`: [bool] 是否将 json_path 下的 json 子串以 string 格式写入指定的 CommonAttr/ItemAttr，默认值为 `False`。该模式将忽略 default_value 配置。`json_from_item_attr` 为 `True` 时暂不支持此选项。

    注意：
      - 同一个 json_config 中，`export_common_attr` 和 `export_item_attr` 必须且只能配置一个
      - `json_attr` 来自 CommonAttr 时: 若只有 `export_common_attr`，`json_path` 会用动态参数的方式获取；若只有 `export_item_attr`，`json_path` 会用动态参数的格式解析获得 item_attr name，对于结果集中每个 item 这个名字的 item_attr 对应一个 json_path 存到 item_attr 中。
      - `json_attr` 来自 ItemAttr 时:`json_path`: 用动态参数的方式获取, 与来 `json_attr` 自 CommonAttr 的不同之处在于 `export_item_attr` 不会先解析 item_attr name 再对应到 json_path。

    Tips: 如果是想给 ItemAttr 设置默认值，可以使用 `set_attr_default_value()` 接口，性能更优！

    调用示例
    ------
    ``` python
    # 从固定 path 获取 common attr 值
    .enrich_attr_by_json(
      json_attr = "json_user_data",
      json_configs = [
        {
          "json_path": "user_info.device_id",
          "export_common_attr": "device_id",
          "default_value": ""
        },
        {
          "json_path": "user_info.click_list[].pid",
          "export_common_attr": "click_pid_list"
        }
      ]
    )
    # 从动态 path 获取 item attr 值
    .enrich_attr_by_json(
      json_attr = "json_id_mapping",
      json_configs = [{
        "json_path": "{{pid}}",
        "export_item_attr": "related_pids",
        "default_value": []
      }]
    )
    ```
    """
    self._add_processor(CommonRecoJsonStringAttrEnricher(kwargs))
    return self

  def parse_protobuf_list_from_common_attr(self, **kwargs):
    """
    CommonProtobufListParseEnricher
    ------
    解析 string_list common_attr，转换成 pb list 并抽取 field 字段存储到 common_attr 中。
    不支持 repeated 类型的 field

    参数配置
    ------
    `class_name`: [string] 转换的 protobuf 的类型，当前支持所有链接的 Message 类型。

    `input_common_attr`: [string] 输入的 string_list 类型的 attr 的 name。

    `attrs`: [list] 需要抽取的属性列表
      - `name`: [string] attr 的名称。
      - `path`: [string] protobuf 结构内数据路径。
    
    `use_dynamic_proto`: [bool] 选配项，是否使用 proto 动态注册，默认为 False

    调用示例
    ------
    ``` python
    .parse_protobuf_list_from_common_attr(
      input_common_attr="buy_action_list",
      class_name="ks.reco.BuyerStat",
      attrs=[
        {"name":"pay_time_list", "path":"pay_time"},
        {"name":"buy_item_price_list", "path":"item_price"},
        {"name":"seller_id_list", "path":"real_seller_id"}]
    )
    ```
    """
    kwargs["class_name"] = kwargs["class_name"].replace("::", ".")
    self._add_processor(CommonProtobufListParseEnricher(kwargs))
    return self

  def check_tail_number(self, **kwargs):
    """
    CommonRecoKconfTailNumberEnricher
    ------

    检测一个 common attr 是否命中了 kconf 配置的 [tail number](https://docs.corp.kuaishou.com/k/home/<USER>/fcADfCErt2fPh6fqCJ0yaAXLT#section=h.kcayc1et6tir), 结果存放到指定的 common attr 中, 1 表示命中，0 表示没有命中。

    参数配置
    ------
    `kconf_key` : [string] 必配项，配置了命中规则的 tail number kconf, 如果 kconf 不存在, 结果为没有命中

    `test_value` : [int] [动态参数] 必配项，用于检测该值的 tail number 是否命中 kconf 的配置, 如果是动态参数且不存在的情况下不会对 output_to 进行赋值

    `output_to` : [string] 输出的 common attr 名字, 默认为 tail_number_output，1为命中，0为没有命中

    调用示例
    ------
    ``` python
    .check_tail_number(
        kconf_key = 'reco.debug.tailNumberForDebug',
        test_value = '{{user_id}}'
        output_to = 'is_sampled_user',
    )
    ```
    """
    self._add_processor(CommonRecoKconfTailNumberEnricher(kwargs))
    return self

  def mix_by_sub_flow(self, **kwargs):
    """
    CommonRecoPipelineMixer
    ------
    异步并行一条独立的 common leaf pipeline (dragonfly LeafFlow)，并根据配置将子流程中产生的 table 合并到当前主流程中。该 processor 会拷贝主流程中指定 table 的全部 item 和指定的 attr 到子流程中。

    参数配置
    ------
    `sub_flow`: [LeafFlow] 异步并行的 LeafFlow 对象

    `timeout_ms`: [int] [动态参数] 选配项，sub_flow 执行超时时间毫秒数，若小于等于 0 则无超时，默认值为 0。

    `pass_browse_set`: [bool] 把 request 请求携带的 browse set 填充到待执行的 sub_flow 中，默认为 true

    `pass_common_attrs_in_request`: [bool] 把 request 请求携带的 common attrs 填充到待执行的 sub_flow 中，默认为 true

    `pass_common_attrs`: [list] 选配项，执行 sub_flow 携带的初始 common attrs。注意：deep_copy = false 的情况下，这些属性同时会被标记为只读，直到 sub_flow 执行完成。

    `merge_common_attrs`: [list] 选配项，合并 sub_flow 返回的 common attrs。支持对 attr 以 name/as 的配置格式重命名保存。注意: merge_and_overwrite=true 重复时会覆盖原 common attrs

    `merge_and_overwrite`:[bool] 选配项, 是否使用 sub_flow 返回的 attr 覆盖现有 attr。默认为 false 。

    `input_tables` : [list] 选配项，拷贝进 sub_flow 的 table 配置
      - `table_name`: [string] 必配项，拷贝的 table 名
      - `attrs`: [list] 选配项，拷贝的 attr 列表

    `enrich_tables` : [list] 选配项，合并回主 flow 的 table 配置（仅拷贝 attr）
      - `table_name`: [string] 必配项，拷贝的 table 名
      - `attrs`: [list] 选配项，拷贝的 attr 列表

    `retrieve_tables` : [list] 选配项，合并回主 flow 的 table 配置（召回 item 且拷贝 attr）
      - `table_name`: [string] 必配项，拷贝的 table 名
      - `attrs`: [list] 选配项，拷贝的 attr 列表

    `deep_copy` : [bool] 是否将 input_tables 和 common_attrs 的数据以深拷贝的方式传入 sub_flow，默认为 false

    调用示例
    ------
    ``` python
    .mix_by_sub_flow(
        sub_flow = merchant_flow,
        input_tables=[{"table_name": "merchant", "attrs": ["id", "tag"]}],
        enrich_tables=[{"table_name": "merchant", "attrs": ["goods"]}],
        retrieve_tables=[{"table_name": "merchant_live", "attrs": ["aId"]}],
    )
    ```
    """
    self._add_processor(CommonRecoPipelineMixer(kwargs))
    return self

  def pack_item_attr_to_item_attr(self, **kwargs):
    """
    CommonRecoPackItemAttrEnricher
    ------
    把指定的一组 item_keys 的 ItemAttr 拼接为新的 attr 数据并填入指定的 ItemAttr 中

    参数配置
    ------
    `from_item_attrs`: [list] 必填项，需要抽取的各个 ItemAttr 名称，支持 int|double|string|int_list|double_list|string_list 类型 attr。注意：`from_item_attrs` 的类型由 `default_val` 隐式指定

    `to_item_attr`: [string] 必填项，待写入的 ItemAttr 名称，被抽取的各个 ItemAttr 组装成对应类型的 ListAttr 写入该 ItemAttr 中。`default_val` 为 int|int_list 时，`to_item_attr` 类型为 int_list；`default_val` 为 double|double_list 时，`to_item_attr` 类型为 double_list；`default_val` 为 string|string_list 时，`to_item_attr` 类型为 string_list；

    `aggregator`: [string] 选配项，抽取的各个 ItemAttr 值后的 pack 方式，可选值：
      - "concat": 默认方式，把所有值拼接到 list 类型的 to_item_attr 中，支持 int|double|string|int_list|double_list|string_list 类型 ItemAttr
      - "copy": 把 from_item_attrs 中第一个 ItemAttr 拷贝到 to_item_attr 中，支持 int|double|string|int_list|double_list|string_list 类型 ItemAttr

    `default_val`: [int|double|string] 必填项，拼接的默认值，如果 ItemAttr 不存在或 ItemAttr 与默认值的类型不同，则将 default_val 赋值给该 ItemAttr

    `fill_default_val`: [bool] 选配项，对应类型的 ItemAttr 不存在时，是否填充默认值，默认为 True

    `limit_num`: [int][动态参数] 选配项，限制拼接结果的长度，仅当 aggregator 为 concat 模式时生效

    `dedup_to_item_attr`: [bool] 选配项，对 to_item_attr 中的元素去重，支持的元素类型为 int|string，默认为 False

    调用示例
    ------
    🎯 [Try in Playground](http://ksurl.cn/eGHSf5P6)
    ``` python
    .pack_item_attr_to_item_attr(
      "from_item_attrs": ["pctr", "pltr", "pftr"],
      "to_item_attr": "pxtr_list",
      "default_val": 0.0
    )
    ```
    """
    self._add_processor(CommonRecoPackItemAttrEnricher(kwargs))
    return self

  def cast_attr_type(self, **kwargs):
    """
    CommonRecoAttrTypeCastEnricher
    ------
    attr类型转换。支持 int/double/string/float16/float32 之间的转换，支持 int_list/double_list/string_list/float16_list/float32_list 之间的转换

    这个 api 不支持 单值和 list 类型之间的转换！！！

    eg. 支持以下类型转换：
    - int <=> double
    - int <=> string
    - double <=> string
    - int_list <=> double_list
    - int_list <=> string_list
    - double_list <=> string_list

    请注意，如果是 list 类型之间转换，to_type 也写成 int/double/string

    参数配置
    ------

    `attr_type_cast_configs`: [list]
      - `to_type`: [string] 目标类型，可选值：int/double/string。如果是来源 attr 是 list 类型，则目标类型为对应的 list 类型
      - `from_item_attr`: [string] 待转换的 item attr 名称
      - `to_item_attr`: [string] 转换后的 item attr 名称，不能与 from_item_attr 同名
      - `from_common_attr`: [string] 待转换的 common attr 名称
      - `to_common_attr`: [string] 转换后的 common attr 名称

    调用示例
    ------
    ``` python
    .cast_attr_type(
      attr_type_cast_configs=[
        {
          "to_type": "int",
          "from_item_attr": "item_str_attr",
          "to_item_attr": "item_int_attr"
        },
        {
          "to_type": "string",
          "from_common_attr": "common_int_list_attr",
          "to_common_attr": "common_str_list_attr"
        }
      ]
    )
    ```
    """
    self._add_processor(CommonRecoAttrTypeCastEnricher(kwargs))
    return self
  
  def enrich_by_wasm(self, **kwargs):
    """
    CommonRecoWasmEnricher
    ------
    通过 wasm 程序丰富 attr 内容，内部使用了 [wasmtime](https://github.com/bytecodealliance/wasmtime) 引擎编译运行 WAT 代码。目前使用的 wasmtime 版本为 7.0.0 。

    配置比较复杂，请参考文章 https://kstack.corp.kuaishou.com/article/7544

    一个 processor 对应一个 wasmtime 的 instance ，在初始化阶段生成 instance 后，会 **重复** 使用。每次 processor 执行过程如下：
    - 先调用 `init` （如有）
    - 然后往 wasm 环境中输入 `input_attrs` 配置的相关 attr 内容
    - 初始化 `output_attrs` 配置的内存空间
    - 按 `calls` 配置依次调用函数
    - 从 wasm 环境中读取 `output_attrs` 配置相关的 attr 内容并保存

    如果使用内存分配功能，那在 wasm 中必须 export `alloc` 与 `init` 方法。示例中给出了 `alloc` 与 `init` 的默认实现，请参考示例。
    - `alloc` 方法用于内存分配，返回值是指针，如果返回 0 (NULL) 说明内存不够，申请失败。
    - `init` 方法重置已分配的内存

    有下面几种配置方式：
    - 方式1: 仅配置 `wat`
    - 方式2: 仅配置 `wasm_file` ，用户提供对应 wasm 文件，上线时带上 wasm 文件
    - 方式3: 配置 `wasm_file` + `c` ，在 dragonfly python 解析成 json 时编译成 wasm ，上线时需带上 wasm 文件，解析环境需要 clang
    - 方式4: 配置 `c` + `compile_to_wat` 为 True ，在 dragonfly python 解析成 json 时编译出 wasm 并转换成 wat ，并把 wat 内容填入到 `wat` 参数中，此方式不需要带上 `wasm` 文件，解析环境需要 clang 与 wasm-tools

    参数配置
    ------

    `wat`: [string] 选配项， WAT(WebAssembly Text format) 是基于 s-exp 的类似 lisp 的语言

    `wasm_file`: [string] 选配项，指定一个外部 `.wasm` 文件的相对路径，上线时需要带上此文件

    `template_c`: [string] 选配项，覆盖默认的 C 语言代码前半部分模版，默认模板请看源码

    `clang_args`: [list] 选配项，编译 C 代码时给 clang 的编译参数，一般不需要改

    `debug_show_code`: [boolean] 选配项（调试用），显示要编译的 C 代码，默认是 False

    `debug_show_wat`: [boolean] 选配项（调试用），编译 C 代码到 wasm 文件时，是否输出对应 WAT 内容，需要有 wasm-tools 工具，默认是 False

    `compile_to_wat`: [boolean] 选配项，若为 True 必须配置 `c` ，会覆盖 `wat` 内容

    `c`: [string] 通过编写 C 语言代码实现，编译成 wasm 文件，需要有 clang ，前面会拼上模板代码后进行编译

    c 语言中能使用的一些定义与函数：
    - `i64`: int64_t
    - `f64`: double
    - `string_t`: 4字节长度 + char数组
    - `i64_list_t`: 4字节长度 + i64数组
    - `f64_list_t`: 4字节长度 + f64数组
    - `alloc(int)`: 申请内存空间
    - `alloc_string()`, `alloc_i64_list()`, `alloc_f64_list()`: 申请对应类型的对象空间，参数是 list 长度
    - `copy_string()`, `copy_i64_list()`, `copy_f64_list()`: 复制已有 attr 内容，申请一个新空间写入

    上面几个类型在 C 语言中的定义是这样的
    ```
    typedef int64_t i64;
    typedef int32_t i32;
    typedef double f64;
    typedef float f32;
    typedef struct { int size; char data[0]; } string_t;
    typedef struct __attribute__((packed)) { int size; i64 data[0]; } i64_list_t;
    typedef struct __attribute__((packed)) { int size; f64 data[0]; } f64_list_t;
    ```

    原理是会拼上固定的头部代码（参考[common_leaf_enricher.py](https://git.corp.kuaishou.com/reco-cpp/dragon/-/blob/master/dragonfly/ext/common/common_leaf_enricher.py)中的 `CommonRecoWasmEnricher` 类 `compile_c_to_wasm()` 的代码）
    加上输入输出的 attr 定义，再加上这个配置里的代码形成一个 `c` 文件并编译成 wasm 文件
    （固定头部代码可以通过 `template_c` 覆盖）

    `input_attrs`: [list] 选配项，指输入的 attrs ，需要在 wasm 中 export 对应的全局变量，此 processor 开始时，调用 `calls` 函数列表前，会先注入配置的 attr 值

    - 配置中每个元素是 dict 类型，对每个元素需要：
      - `name`: [string] 必配项， dragonfly 中的 attr 名称
      - `type`: [string] 选配项，支持 "common_attr", "item_attr" ，默认是 "common_attr"
      - `wasm_var`: [string] 选配项，指 wasm 中 export 的名称，默认与 name 相同，一般 attr 名称特殊时使用
      - `value_type`: "i64", "f64", "string", "i64_list", "f64_list" 之一
    - 如果是 common attr 类型
      - 如果获取 common attr 失败， int float 会使用默认值 0 填充，其它类型会用长度为 0 的默认值填充
      - 对应 c 语言中的定义类型是
        - int --> `i64`
        - float --> `f64`
        - string --> `string_t*`
        - int list --> `i64_list_t*`
        - float list --> `f64_list_t*`
    - 如果是 item attr 类型
      - 在 wasm 中必须 export `item_num` 表示 item 数量
      - 如果获取 item attr 失败， int float 会使用默认值 0 填充，其它类型会用长度为 0 的默认值填充
      - 在 wasm 中是数组形式，共有 `item_num` 个元素
      - 对应 c 语言中的定义类型是
        - int --> `i64*`
        - float --> `f64*`
        - string --> `string_t**`
        - int list --> `i64_list_t**`
        - float list --> `f64_list_t**`
    
    `output_attrs`: [list] 选配项，指输出的 attrs ，需要在 wasm 中 export 对应的全局变量。

    - 配置中每个元素可以是 dict 类型，需要配置
      - `name`: [string] 必配项， dragonfly 中的 attr 名称
      - `type`: [string] 选配项，支持 "common_attr", "item_attr" ，默认是 "common_attr"
      - `wasm_var`: [string] 选配项，指 wasm 中 export 的名称，默认与 name 相同，一般 attr 名称特殊时使用
      - `value_type`: "i64", "f64", "string", "i64_list", "f64_list" 之一
    - 如果是 common attr 类型
      - 如果值类型是 i64 f64 ，已申请好内存空间，默认值填入了 0 ，其它类型需要用 alloc 函数申请内存空间，请参考示例
      - 对应 c 语言中的定义类型是
        - int --> `i64` 已申请好，默认是 0
        - float --> `f64` 已申请好，默认是 0
        - string --> `string_t*` 默认是 null 需要 alloc
        - int list --> `i64_list_t*` 默认是 null 需要 alloc
        - float list --> `f64_list_t*` 默认是 null 需要 alloc
    - 如果是 item attr 类型
      - 第一层是 item_num 个指针，已申请好内存空间
      - 第二层
        - 如果值类型是 i64 f64 ，已申请好内存空间，默认值填入了 0
        - 其它类型需要用 alloc 函数申请内存空间，写入具体内容后，赋值指针，默认指针是 null
      - 对应 c 语言中的定义类型是
        - int --> `i64*`
        - float --> `f64*`
        - string --> `string_t**`
        - int list --> `i64_list_t**`
        - float list --> `f64_list_t**`
    
    `clear_attr_if_null`: [boolean] 选配项，默认 False ，如果为 True 时，输出配置 `output_attrs` 的 string/int list/float list 类型是 null 时，会清空对应的 attr ，否则不做任何操作

    `set_empty_attr_if_null`: [boolean] 选配项，默认 False ，如果为 True 时，输出配置 `output_attrs` 的 string/int list/float list 类型是 null 时，会赋值对应类型 0 长度的内容 ，否则不做任何操作

    注意：上述两个配置对 int/float 无效，因为默认会初始化成 0 ，如果在 wasm 内不改动，输出也会赋值 0

    `calls`: [list] 必配项，具体要调用的 wasm 函数列表，每个 item 是 dict 类型，会按顺序依次调用
    - `name`: 要调用的函数名称，且必须是 export 的函数
    - `params`: 调用参数 common attr ，数量要与实际函数参数数量一致，目前只支持 int 与 double 类型的 common attr
    - `results`: 调用结果输出到 common attr ，数量要与实际函数 result 数量一致，目前只支持 int 与 double 类型的 common attr

    调用示例：使用 `c` 配置辅助编译成 wasm 文件，请注意， C++ 侧不直接读取这个 `c` 参数，而是依赖 wasm_file 文件，所以部署时需要自行带上 wasm_file 对应的文件
    ------
    ``` python
    .enrich_by_wasm(
        wasm_file="my.wasm",
        c=\"\"\"
          void common_attrs_example() {
            ca_out1 = ca_in1;
            ca_out2 = ca_in2;

            ca_out3 = alloc_string(ca_in3->size);
            for (int i = 0; i < ca_in3->size; i++) {
              ca_out3->data[i] = ca_in3->data[i];
            }

            ca_out4 = alloc_i64_list(ca_in4->size);
            for (int i = 0; i < ca_in4->size; i++) {
              ca_out4->data[i] = ca_in4->data[i];
            }

            ca_out5 = alloc_f64_list(ca_in5->size);
            for (int i = 0; i < ca_in5->size; i++) {
              ca_out5->data[i] = ca_in5->data[i];
            }
          }

          void item_attrs_example() {
            for (int i = 0; i < item_num; i++) {
              ia_out1[i] = ia_in1[i];
            }
            for (int i = 0; i < item_num; i++) {
              ia_out2[i] = ia_in2[i];
            }
            for (int i = 0; i < item_num; i++) {
              int size = ia_in3[i]->size;
              ia_out3[i] = copy_string(ia_in3[i]->data, ia_in3[i]->size);
            }
            for (int i = 0; i < item_num; i++) {
              ia_out4[i] = copy_i64_list(ia_in4[i]->data, ia_in4[i]->size);
            }
            for (int i = 0; i < item_num; i++) {
              ia_out5[i] = copy_f64_list(ia_in5[i]->data, ia_in5[i]->size);
            }
          }
        \"\"\",
        input_attrs=[
            {"name": "ca_in1", "value_type": "i64"},
            {"name": "ca_in2", "value_type": "f64"},
            {"name": "ca_in3", "value_type": "string"},
            {"name": "ca_in4", "value_type": "i64_list"},
            {"name": "ca_in5", "value_type": "f64_list"},
            {"name": "ia_in1", "type": "item_attr", "value_type": "i64"},
            {"name": "ia_in2", "type": "item_attr", "value_type": "f64"},
            {"name": "ia_in3", "type": "item_attr", "value_type": "string"},
            {"name": "ia_in4", "type": "item_attr", "value_type": "i64_list"},
            {"name": "ia_in5", "type": "item_attr", "value_type": "f64_list"},
        ],
        output_attrs=[
            {"name": "ca_out1", "value_type": "i64"},
            {"name": "ca_out2", "value_type": "f64"},
            {"name": "ca_out3", "value_type": "string"},
            {"name": "ca_out4", "value_type": "i64_list"},
            {"name": "ca_out5", "value_type": "f64_list"},
            {"name": "ia_out1", "type": "item_attr", "value_type": "i64"},
            {"name": "ia_out2", "type": "item_attr", "value_type": "f64"},
            {"name": "ia_out3", "type": "item_attr", "value_type": "string"},
            {"name": "ia_out4", "type": "item_attr", "value_type": "i64_list"},
            {"name": "ia_out5", "type": "item_attr", "value_type": "f64_list"},
        ],
        calls=[
            {"name": "common_attrs_example"},
            {"name": "item_attrs_example"},
        ],
    )
    ```
    
    自行编译成 wasm 文件
    ------

    ``` c
    #include <stdint.h>

    typedef __SIZE_TYPE__ size_t;

    #define NULL ((void *) 0)

    typedef int64_t i64;
    typedef int32_t i32;
    typedef double f64;
    typedef float f32;
    typedef struct { int size; char data[0]; } string_t;
    typedef struct __attribute__((packed)) { int size; i64 data[0]; } i64_list_t;
    typedef struct __attribute__((packed)) { int size; f64 data[0]; } f64_list_t;

    extern void __heap_base;
    static size_t heap_used = 0;

    #define PAGE_SIZE 65536
    #define PAGE_SIZE_LOG_2 16
    #define DEFAULT_ALIGN 4

    static inline size_t align(size_t val, size_t alignment) {
      return (val + alignment - 1) & ~(alignment - 1);
    }

    // a simple allocator
    void *alloc(size_t size) {
      size_t memory_size = __builtin_wasm_memory_size(0) * PAGE_SIZE;
      size_t used = (size_t)(&__heap_base) + heap_used;
      size_t need_size = align(size, DEFAULT_ALIGN);
      if (need_size > SIZE_MAX - used) {
        // overflow
        return NULL;
      }
      if (need_size + used > memory_size) {
        // allocate pages
        size_t pages = align(need_size + used - memory_size, PAGE_SIZE) >> PAGE_SIZE_LOG_2;
        size_t ret = __builtin_wasm_memory_grow(0, pages);
        if (ret == -1) {
          // not enough memory
          return NULL;
        }
      }
      // save used heap size
      heap_used += need_size;
      return (void *)used;
    }

    void init() {
      // makes the heap reusable
      heap_used = 0;
    }

    string_t *alloc_string(int len) {
      string_t *r = (string_t *)alloc(sizeof(int) + sizeof(char) * len);
      r->size = len;
      return r;
    }

    i64_list_t *alloc_i64_list(int len) {
      i64_list_t *r = (i64_list_t *)alloc(sizeof(int) + sizeof(i64) * len);
      r->size = len;
      return r;
    }

    f64_list_t *alloc_f64_list(int len) {
      f64_list_t *r = (f64_list_t *)alloc(sizeof(int) + sizeof(f64) * len);
      r->size = len;
      return r;
    }

    string_t *copy_string(char *data, int size) {
      string_t *r = alloc_string(size);
      for (int i = 0; i < size; i++) {
        r->data[i] = data[i];
      }
      return r;
    }

    i64_list_t *copy_i64_list(i64 *data, int size) {
      i64_list_t *r = alloc_i64_list(size);
      for (int i = 0; i < size; i++) {
        r->data[i] = data[i];
      }
      return r;
    }

    f64_list_t *copy_f64_list(f64 *data, int size) {
      f64_list_t *r = alloc_f64_list(size);
      for (int i = 0; i < size; i++) {
        r->data[i] = data[i];
      }
      return r;
    }

    // input common attrs
    i64 ca_in1;
    f64 ca_in2;
    string_t *ca_in3;
    i64_list_t *ca_in4;
    f64_list_t *ca_in5;

    // output common attrs
    i64 ca_out1;
    f64 ca_out2;
    string_t *ca_out3;
    i64_list_t *ca_out4;
    f64_list_t *ca_out5;

    void common_attrs_example() {
      ca_out1 = ca_in1;
      ca_out2 = ca_in2;

      ca_out3 = alloc_string(ca_in3->size);
      for (int i = 0; i < ca_in3->size; i++) {
        ca_out3->data[i] = ca_in3->data[i];
      }

      ca_out4 = alloc_i64_list(ca_in4->size);
      for (int i = 0; i < ca_in4->size; i++) {
        ca_out4->data[i] = ca_in4->data[i];
      }

      ca_out5 = alloc_f64_list(ca_in5->size);
      for (int i = 0; i < ca_in5->size; i++) {
        ca_out5->data[i] = ca_in5->data[i];
      }
    }

    // input item attrs
    int item_num;

    i64 *ia_in1;
    f64 *ia_in2;
    string_t **ia_in3;
    i64_list_t **ia_in4;
    f64_list_t **ia_in5;

    // output item attrs
    i64 *ia_out1;
    f64 *ia_out2;
    string_t **ia_out3;
    i64_list_t **ia_out4;
    f64_list_t **ia_out5;

    void item_attrs_example() {
      for (int i = 0; i < item_num; i++) {
        ia_out1[i] = ia_in1[i];
      }
      for (int i = 0; i < item_num; i++) {
        ia_out2[i] = ia_in2[i];
      }
      for (int i = 0; i < item_num; i++) {
        int size = ia_in3[i]->size;
        ia_out3[i] = copy_string(ia_in3[i]->data, ia_in3[i]->size);
      }
      for (int i = 0; i < item_num; i++) {
        ia_out4[i] = copy_i64_list(ia_in4[i]->data, ia_in4[i]->size);
      }
      for (int i = 0; i < item_num; i++) {
        ia_out5[i] = copy_f64_list(ia_in5[i]->data, ia_in5[i]->size);
      }
    }
    ```

    若文件名为 my.c 编译命令如下

    ```
    clang --target=wasm32-unknown-unknown --optimize=3 -nostdlib -Wl,--export-all -Wl,--no-entry -Wl,--allow-undefined -mbulk-memory -mreference-types -mmultivalue -mmutable-globals -mnontrapping-fptoint -msign-ext -isystem Wasm my.c --output my.wasm
    ```

    > 编写 c 语言有诸多限制，因没有 stdlib 所以不能用大部分的基础库

    对应 processor 配置

    ```
    .enrich_by_wasm(
      wasm_file = "my.wasm",
      input_attrs = [
        { "name": "ca_in1", "value_type": "i64" },
        { "name": "ca_in2", "value_type": "f64" },
        { "name": "ca_in3", "value_type": "string" },
        { "name": "ca_in4", "value_type": "i64_list" },
        { "name": "ca_in5", "value_type": "f64_list" },
        { "name": "ia_in1", "type": "item_attr", "value_type": "i64" },
        { "name": "ia_in2", "type": "item_attr", "value_type": "f64" },
        { "name": "ia_in3", "type": "item_attr", "value_type": "string" },
        { "name": "ia_in4", "type": "item_attr", "value_type": "i64_list" },
        { "name": "ia_in5", "type": "item_attr", "value_type": "f64_list" },
      ],
      output_attrs = [
        { "name": "ca_out1", "value_type": "i64" },
        { "name": "ca_out2", "value_type": "f64" },
        { "name": "ca_out3", "value_type": "string" },
        { "name": "ca_out4", "value_type": "i64_list" },
        { "name": "ca_out5", "value_type": "f64_list" },
        { "name": "ia_out1", "type": "item_attr", "value_type": "i64" },
        { "name": "ia_out2", "type": "item_attr", "value_type": "f64" },
        { "name": "ia_out3", "type": "item_attr", "value_type": "string" },
        { "name": "ia_out4", "type": "item_attr", "value_type": "i64_list" },
        { "name": "ia_out5", "type": "item_attr", "value_type": "f64_list" },
      ],
      calls = [
        { "name": "common_attrs_example" },
        { "name": "item_attrs_example" },
      ],
    )
    ```

    直接编写 WAT
    ------

    1. 生成一个 int 类型常数 common attr: `out`

    ``` python
    .enrich_by_wasm(
      wat = \"\"\"
      (module
        (func (export "f1") (result i64)
          i64.const 42))
      \"\"\",
      calls = [
        { "name": "f1", "results": [ "out" ], },
      ],
    )
    ```

    2. 简单的 a + b （对 param 起名）

    ``` python
    .enrich_by_wasm(
      wat = \"\"\"
      (module
        (func (export "f1") (param $p1 i64) (param $p2 i64) (result i64)
          local.get $p1
          local.get $p2
          i64.add))
      \"\"\",
      calls = [
        {
          "name": "f1",
          "params": [ "a", "b" ],
          "results": [ "add" ],
        },
      ],
    )
    ```

    3. fibonacci 递归实现

    ``` python
    .enrich_by_wasm(
      wat = \"\"\"
      (module
        (func $fib (export "f1") (param i64) (result i64)
          (if
            (i64.lt_s (local.get 0) (i64.const 2))
            (return (local.get 0)))
          (i64.add
            (call $fib (i64.sub (local.get 0) (i64.const 1)))
            (call $fib (i64.sub (local.get 0) (i64.const 2))))))
      \"\"\",
      calls = [
        {
          "name": "f1",
          "params": [ "n" ],
          "results": [ "r" ],
        },
      ],
    )
    ```

    """
    self._add_processor(CommonRecoWasmEnricher(kwargs))
    return self

  def calc_by_formula1(self, **kwargs):
    """
    CommonRecoFormulaOneEnricher
    ------
    基于 [Formula One 公式引擎](https://docs.corp.kuaishou.com/d/home/<USER>//docs.corp.kuaishou.com/d/home/<USER>

    ?> 提示：formula1 支持的语法规则详见 [F1-Exprtk 中文文档](https://docs.corp.kuaishou.com/d/home/<USER>//www.partow.net/programming/exprtk/code/readme.txt) 

    ?> 提示：`import_common_attr` 和 `import_item_attr` 也可以在 kconf 上配置，格式相同，将与算子里的配置合并生效。但**不建议**随意把 import 数据配置在 kconf 上，因为未在算子配置里设置的 import 数据如果来自于前序异步算子的输出，那么该数据将不可见，需要使用者自行保证数据的存在性。

    参数配置
    ------
    `kconf_key`: [string] 选配项，指定本地基线配置的 kconf_key。Kconf 必须以 Json 形式配置。与 base_json 需且仅需配置一个

    `base_json`: [dict] 选配项，指定本地基线配置的 Json。与 kconf_key 需且仅需配置一个。base_json 中需配置 scenario 用于打点，但不会与 AB 平台交互，无法通过 ab 参数修改 json 中的公式配置。

    `abtest_biz_name`: [str] 配了 kconf_key 时必配项，获取 abtest 参数时使用的 abtest biz_name

    `import_common_attr`: [list] 选配项，需要导入到 formula1 的 common_attr 名称列表（只支持 int/double/string 类型的 attr，int 将被转为 double 值导入），支持 name/as 重命名保存。可用 default_val 重设默认值，未指定则为 0.0

    `import_item_attr`: [list] 必配项，需要导入到 formula1 的 item_attr 名称列表（只支持 int/double/string 类型的 attr，int 将被转为 double 值导入），支持 name/as 重命名保存。可用 default_val 重设默认值，未指定则为 0.0

    `export_formula_value`: [list] 必配项，将 formula1 中的公式计算结果导出到同名的 item_attr 中（将以 double/string/int 类型保存），支持 name/as 重命名保存。配置 to_common 可以将第一个 item 的公式分输出到 CommonAttr 中，适用于输出 SUM,MEAN 等对于全体 item 取值相同的 F1 公式。

    `user_id`: [int] [动态参数] 选配项，用指定的 user_id 值获取 abtest 参数，默认为当前请求所用的 user_id

    `device_id`: [string] [动态参数] 选配项，用指定的 device_id 值获取 abtest 参数，默认为当前请求所用的 device_id

    `session_id`: [string] [动态参数] 选配项，用指定的 session_id 值获取 abtest 参数，默认为 ""

    `prioritized_suffix`: [list] [动态参数] 可缺省，为 formula1 中的公式和参数添加可选的后缀拼接功能。例如 ["_A", "_B"] 将优先使用 `param_name + "_A"` 参数值，次优使用 `param_name + "_B"` 参数值，最后兜底使用 `param_name` 参数值。注意：F1 的后缀匹配逻辑和平常使用 AB 参数有所不同，请参考[开发手册](https://docs.corp.kuaishou.com/d/home/<USER>

    `perf_tag`: [string][动态参数] 选配项， formula1 一次公式计算上报时按该 perf_tag 进行区分展示

    `enable_pruning_opt`: [bool] [动态参数] 选配项, formula1 支持计算剪枝优化, 默认为 False

    调用示例
    ------
    ``` python
    .calc_by_formula1(
      kconf_key = "reco.live2.formula1Test",
      import_common_attr=[
        {"name": "user_type", "as": "uType", "default_val": "U0"},
        {"name": "new_user", "as": "is_new_user"},
        "u_province"
      ],
      import_item_attr = [
        "ctr",
        {"name": "click_alpha", "as": "alpha", "default_val": 0.5}
      ],
      export_formula_value = [
        "rank_ctr",
        {"name": "score", "as": "f1_score", "to_int": True},
        {"name": "mean_score", "to_common": True}
      ],
      abtest_biz_name = "THANOS_RECO"
    )
    ```
    """
    if "base_json" in kwargs and isinstance(kwargs["base_json"], dict):
      kwargs["base_json_str"] = json.dumps(OrderedDict(kwargs["base_json"]))
    self._add_processor(CommonRecoFormulaOneEnricher(kwargs))
    return self

  def union_table(self, **kwargs):
    """
    CommonRecoTableUnionUpdateMixer
    ------
    将 from_table 的数据合并到 to_table 中，默认合并所有 item，并拷贝 copy_attrs 字段。重复的 item 和 attr 的拷贝行为可以配置，但是 union_table 并不会对结果集自动去重。

    参数配置
    ------
    `from_table`: [string] 必配项，合并数据的来源 table

    `to_table`: [string] 必配项，合并数据的目标 table，注意合并数据会在 to_table 增量更新

    `copy_attrs`: [list] 选配项，合并时拷贝的 item_attr，支持两种格式
      - string 格式：直接填写 attr 名
      - dict 格式：支持 `name`、`as`、`copy_mode` 字段，copy_mode 可选项 ["OVERWRITE", "MAX", "CONCAT"]，注意仅 int/double 类型支持 MAX，仅 int/double/string/int_list/double_list/string_list 支持 CONCAT，默认为 OVERWRITE。

    `if_attr`: [string] 选配项，from_table 中的 int 类型 item_attr。配置后仅合并 from_table 中 if_attr 值非 0 的 item。


    调用示例
    ------
    ``` python
    .union_table(
      from_table="sub_photo",
      to_table="photo",
      copy_attrs=["author_id",
                  {"name": "pctr", "copy_mode": "MAX"},
                  {"name": "tag", "copy_mode": "CONCAT"}]
    )
    ```

    数据示例
    ------
    photo 表（union 前）：

    | item_key | author_id | pctr  | tag  |
    |----------|-----------|------:|------|
    | 1        | 1001      |   5.7 | 1    |
    | 2        | 1002      | 100.1 | 1    |
    | 3        | 1003      |   0.5 | 1    |

    sub_photo 表（union 前）：

    | item_key | author_id | pctr  | tag  |
    |----------|-----------|------:|------|
    | 3        | 1001      |  90.1 | 2    |
    | 4        | 1002      |   2.5 | 2    |
    | 5        | 1003      |   3.5 | 2    |

    photo 表（union 后）：

    | item_key | author_id | pctr  | tag   |
    |----------|-----------|------:|-------|
    | 1        | 1001      |   5.7 | [1]   |
    | 2        | 1002      | 100.1 | [1]   |
    | 3        | 1001      |  90.1 | [1,2] |
    | 3        | 1001      |  90.1 | [1,2] |
    | 4        | 1002      |   2.5 | [2]   |
    | 5        | 1003      |   3.5 | [2]   |

    sub_photo 表 union 后不变。
    """

    self._add_processor(CommonRecoTableUnionUpdateMixer(kwargs))
    return self

  def expand_by(self, **kwargs):
    """
    CommonRecoTableExpandByMixer
    ------
    将 from_table 的数据展开到 to_table 中，并拷贝 copy_attrs 字段，注意：to_table 中的主键为从0开始的自增ID。

    参数配置
    ------
    `from_table`: [string] 必配项，expand_by 的来源 table

    `to_table`: [string] 必配项，expand_by 的目标 table

    `reason`: [int] 选配项，指定召回原因，默认为 0

    `by_attr`: [string] 必配项，按照该列进行展开，展开后会作为 to_table 的 同名列。

    `expand_source_table_key_as`: [string] 选配项，展开后是否将 from_table 的 item_key 放到一个新列中。

    `copy_attrs`: [list] 选配项，拷贝的 item_attr，支持两种格式
      - string 格式：直接填写 attr 名
      - dict 格式：支持 name、as、copy_mode 字段，copy_mode 目前仅支持 ["OVERWRITE"]。

    调用示例
    ------
    ``` python
    .expand_by(
      from_table="hetu_table_before_expand",
      to_table="hetu_table_after_expand",
      by_attr="photo_id_list",
      by_alias_attr="photo_id",
      expand_source_table_key_as="hetu_tag_id",
      copy_attrs=[{"name": "author_list", "copy_mode": "OVERWRITE", "as": "author_list"},
                  {"name": "concat_caption_segment", "copy_mode": "OVERWRITE", "as": "concat_caption_segment"}]
    )
    ```

    数据示例
    ------
    hetu_table 表（expand_by前）：
    from_table: hetu_table_before_expand
    | item_key | photo_id_list | author_list      | concat_caption_segment |
    |----------|---------------|------------------|------------------------|
    | 14942050 | [1,3,4]       | [1001,1003,1003] | ["a", "b", "c", "d"]   |
    | 14941137 | [2,5]         | [1002,1003]      | ["e"]                  |
    | 14943000 | [6]           | [1004]           | ["f", "g"]             |

    hetu_table 表（expand_by 后）：
    to_table: hetu_table_after_expand
    | item_key | photo_id | hetu_tag_id |  author_list     | concat_caption_segment |
    |----------|----------|-------------|------------------|------------------------|
    | 0        | 1        | 14942050    | [1001,1003,1003] | ["a", "b", "c", "d"]   |
    | 1        | 3        | 14942050    | [1001,1003,1003] | ["a", "b", "c", "d"]   |
    | 2        | 4        | 14942050    | [1001,1003,1003] | ["a", "b", "c", "d"]   |
    | 3        | 2        | 14941137    | [1002,1003]      | ["e"]                  |
    | 4        | 5        | 14941137    | [1002,1003]      | ["e"]                  |
    | 5        | 6        | 14943000    | [1004]           | ["f", "g"]             |
    """
    self._add_processor(CommonRecoTableExpandByMixer(kwargs))
    return self

  def group_by(self, **kwargs):
    """
    CommonRecoTableGroupByMixer
    ------
    将 from_table 的数据聚合到 to_table 中，并拷贝 copy_attrs 字段

    参数配置
    ------
    `from_table`: [string] 必配项，group_by 的来源 table

    `to_table`: [string] 必配项，group_by 的目标 table

    `reason`: [int] 选配项，指定召回原因，默认为 0

    `by_attr`: [string] 必配项，聚合的主键，聚合后会作为 to_table 的 item_key。必须是 from_table 中的 int 类型 item_attr。

    `concat_source_table_key_as`: [string] 选配项，聚合后是否将 from_table 的 item_key concat 起来并作为 to_table 的一个 int_list attr。

    `copy_attrs`: [list] 选配项，拷贝的 item_attr，支持两种格式
      - string 格式：直接填写 attr 名
      - dict 格式：支持 name、as、copy_mode 字段，copy_mode 可选项 ["OVERWRITE", "MAX", "CONCAT"]，注意仅 int/double 类型支持 MAX，仅 int/double/string/int_list/double_list/string_list 支持 CONCAT，默认为 OVERWRITE。

    调用示例
    ------
    ``` python
    .group_by(
      from_table="photo",
      to_table="hetu_table",
      by_attr="hetu_tag_id",
      concat_source_table_key_as="photo_id_list",
      copy_attrs=[{"name": "author", "copy_mode": "CONCAT", "as": "author_list"},
                  {"name": "reason", "copy_mode": "CONCAT", "as": "reason_list"}]
    )
    ```

    数据示例
    ------
    photo 表（group_by 前）：

    | item_key | hetu_tag_id | author | tag | caption_segment |
    |----------|-------------|--------|-----|-----------------|
    | 1        | 14942050    | 1001   | 1   | ["a", "b"]      |
    | 2        | 14941137    | 1002   | 1   | []              |
    | 3        | 14942050    | 1003   | 1   | ["c", "d"]      |
    | 4        | 14942050    | 1003   | 2   | []              |
    | 5        | 14941137    | 1003   | 2   | ["e"]           |
    | 6        | 14943000    | 1004   | 2   | ["f", "g"]      |

    photo 表 group_by 后不变。

    hetu_table 表（group_by 后）：

    | item_key | photo_id_list | author_list      | tag_list | concat_caption_segment |
    |----------|---------------|------------------|----------|------------------------|
    | 14942050 | [1,3,4]       | [1001,1003,1003] | [1,1,2]  | ["a", "b", "c", "d"]   |
    | 14941137 | [2,5]         | [1002,1003]      | [1,2]    | ["e"]                  |
    | 14943000 | [6]           | [1004]           | [2]      | ["f", "g"]             |
    """
    self._add_processor(CommonRecoTableGroupByMixer(kwargs))
    return self

  def left_join_table(self, **kwargs):
    """
    CommonRecoTableJoinMixer
    ------
    根据连接条件组合 left_table 和 right_table 的列数据，并填入到 left_table 中。attr 的拷贝行为可以配置。

    连接条件的情况：
      1. 当 join_attr_left 和 join_attr_right 均为单值的情况，对 join_attr_left 和 join_attr_right 相等的行进行组合。
      2. 当 join_attr_left 和 join_attr_right 其中一个为单值，另一个为 list 类型时，对 A in B 的行进行组合。

    其余情况均不能 join。

    注意：不会在 left_table 新增行，只会多次更新已有行的列。

    参数配置
    ------
    `left_table`: [string] 必配项，合并数据的目标 table

    `right_table`: [string] 必配项，合并数据的来源 table

    `copy_attrs`: [list] 选配项，合并时从 right_table 拷贝的 item_attr，支持两种格式
      - string 格式：直接填写 attr 名
      - dict 格式：支持 name、as、copy_mode 字段，copy_mode 可选项 ["OVERWRITE", "MAX", "CONCAT"]，注意仅 int/double 类型支持 MAX，仅 int/double/string/int_list/double_list/string_list 支持 CONCAT，默认为 OVERWRITE。

    `join_attr_left`: [string] 选配项，连接条件使用的 attr，默认使用 item_key。

    `join_attr_right`: [string] 选配项，连接条件使用的 attr，默认使用 item_key。

    调用示例
    ------
    ``` python
    .left_join_table(
      left_table="photo",
      right_table="author",
      join_attr_left="author_id",
      copy_attrs=["author_fans_count", "author_score"]
    )
    ```

    数据示例
    ------
    photo 表（join 前）：

    | item_key | author_id |
    |----------|-----------|
    | 1        | 1001      |
    | 2        | 1002      |
    | 3        | 1001      |

    author 表（join 前）：

    | item_key | author_fans_count | author_score |
    |----------|------------------:|-------------:|
    | 1001     | 10                | 12.5         |
    | 1002     | 1000              | 50.3         |
    | 1003     | 100000            | 99.9         |

    photo 表（join 后）：

    | item_key | author_id | author_fans_count | author_score |
    |----------|-----------|------------------:|-------------:|
    | 1        | 1001      | 10                | 12.5         |
    | 2        | 1002      | 1000              | 50.3         |
    | 3        | 1001      | 10                | 12.5         |
    """
    self._add_processor(CommonRecoTableJoinMixer(kwargs))
    return self

  def create_logic_table(self, **kwargs):
    """
    CommonRecoCreateLogicTableMixer
    ------
    创建一个当前 table 的引用。筛选并复制符合条件的结果集到新 table。
    两表的结果集是独立的，共享引用的 ItemAttr。

    参数配置
    ------
    `logic_table`: [string] 必配项，创建出的逻辑表名。

    `select_attr`: [list] 必配项，引用的 item_attrs 列表。

    `select_item`: [dict] 选配项，仅对满足条件的 item 进行复制，详细规则可以参考通用配置 [select_item](https://dragonfly.corp.kuaishou.com/#/wiki/processor_config?id=select_item)。

    `remove_selected_items`: [bool] 选配项，是否在当前 table 中移除筛选出的 item。默认值为 false

    `ignore_input_attr_source`: [bool] 选配项，是否忽略当前 table 的 select_attr 来源，当逻辑表创建时机在实际字段填充前时，可以开启。默认值为 false

    调用示例
    ------
    ``` python
    # 创建一个引用自 'photo' 表的逻辑表 'temp_photo'：
    # 筛选 photo 表中满足 author_fans_count <= 10 的 item 填入 temp_photo 表；
    # 在 temp_photo 表中创建 "author_id", "author_fans_count", "author_score" 三个字段的引用。
    .create_logic_table(
      item_table="photo",  # 如果是直接基于当前主表创建可以省略该项配置
      logic_table="temp_photo",
      select_attr=["author_id", "author_fans_count", "author_score"],
      select_item={
        "attr_name": "author_fans_count",
        "select_if": "<=",
        "compare_to": 10
      }
    )
    ```
    """
    self._add_processor(CommonRecoCreateLogicTableMixer(kwargs))
    return self

  def build_custom_kv_user_info(self, **kwargs):
    """
    CommonRecoBuildCustomKVUserInfoEnricher
    ------
    使用 [FDK](https://git.corp.kuaishou.com/reco-arch/fdk) 序列化指定的 User KV 数据

    参数配置
    ------
    `collection_name`: [string] 必配项，特征管理需要的 collection ，可理解为一个业务下某个场景需要的字段集合，统一由业务 kconf 管理

    `kv_user_info_attr`: [string] 必配项，存储 KV User Info 类型指针的 attr

    `custom_user_info_keys`: [string_list] [动态参数] 必配项，指定需要序列化的 KV 字段的 Key

    `save_result_to_attr`: [string] 必配项，指定序列化结果保存的 attr

    调用示例
    ------
    ``` python
    .build_custom_kv_user_info(
      collection_name = "reco.explore.kv_user_info",
      kv_user_info_attr = "kv_user_info",
      custom_user_info_keys = [
        "uId",
        "uGender",
        "uYear",
      ],
      save_result_to_attr = "custom_kv_user_info_str",
    )
    ```
    """

    self._add_processor(CommonRecoBuildCustomKVUserInfoEnricher(kwargs))
    return self

  def parse_from_custom_kv_user_info(self, **kwargs):
    """
    CommonRecoParseFromCustomKVUserInfoEnricher
    ------
    使用 [FDK](https://git.corp.kuaishou.com/reco-arch/fdk) 反序列化并抽取指定的 User KV 数据

    参数配置
    ------
    `collection_name`: [string] 必配项，特征管理需要的 collection ，可理解为一个业务下某个场景需要的字段集合，统一由业务 kconf 管理

    `custom_user_info_from`: [string] 必配项，存储 Custom User Info 来自哪个 attr, 数据类型为 string

    `fields_to_read`: [string_list] 必配项，指定需要读取字段名称集合

    调用示例
    ------
    ``` python
    .parse_from_custom_kv_user_info(
      collection_name = "reco.explore.kv_user_info",
      custom_user_info_from = "custom_kv_user_info_str",
      fields_to_read = [
        "uId",
        "uGender",
        "uYear",
      ],
    )
    ```
    """

    self._add_processor(CommonRecoParseFromCustomKVUserInfoEnricher(kwargs))
    return self


  def observe_by_sub_flow(self, **kwargs):
    """
    CommonRecoPipelineObserver
    ------
    异步并行一条独立的 common leaf pipeline (dragonfly LeafFlow)，该 pipeline 执行与主流程独立，且不会 merge 任何数据回主流程。该 processor 会拷贝主流程中全部 item 和指定的 attr 到子流程中。

    提示：只支持显式的深拷贝 attr 进子流程 ， 如包含指针类型 attr 慎用。该 pipeline 执行周期与请求周期完全独立，不会受到请求超时影响。

    提示：如果 observe_by_sub_flow 预期耗时较长，请将 sub_flow 线程池大小设置为大于 grpc worker 线程数 (设置参数： sub_flow_thread_num_per_worker || sub_flow_thread_num)。

    注意：子流程不会输出任何信息至主流程中。且该算子异步且无下游算子。子流程将独立于主流程在后台执行，不会阻塞主流程结束。

    参数配置
    ------
    `sub_flow`: [LeafFlow] 异步并行的 LeafFlow 对象

    `pass_common_attrs`: [list] 选配项，需要拷贝传递进 sub_flow 的 common attr 数据，若缺省将由框架自动推断。注意：传递为拷贝传递。

    `pass_item_attrs`: [list] 选配项，需要拷贝传递进 sub_flow 的 item attr 数据，若缺省将由框架自动推断。注意：传递为拷贝传递。

    `task_queue_id`: [int] [动态参数] 选配项，选择使用的任务队列，默认值为0。注意：需配置 gflag sub_flow_task_queue_num 使用，不能大于等于 sub_flow_task_queue_num。
      - 一般用用于期望该子流程执行的线程池独立于其他 sub_flow 的场景，例如：该子流程耗时巨大，数量较多，容易耗尽线程池线程，需要与其他 sub_flow 线程池独立，避免相互影响。

    `common_reco_context_attr_name`: [str] 选配项，从对应名称的 common_attr 获取 CommonRecoContext 对象传递到 sub_flow 中，搭配 `init_common_reco_context` 使用，默认为 ""，即不传递 CommonRecoContext 对象到 sub_flow 中。

    调用示例
    ------
    ``` python
    '''
    Graph of M flow:
                                
                         
    +---------------+    +---+         +-----+  
    | fake_retrieve |--->| A |----+--->| END |
    +---------------+    +---+    |    +-----+   
                                  |            +---+
                                  +----------->| B |
                                               +---+
    ''' 
    A = LeafFlow(name="sub_a")
    B = LeafFlow(name="sub_b")
    M = LeafFlow(name="main") \\
        .fake_retrieve(num=100, reason=999) \\
        .enrich_by_sub_flow(sub_flow = A, merge_item_attrs=["aid"]) \\
        .observe_by_sub_flow(sub_flow = B) \\ 
    ```
    """
    self._add_processor(CommonRecoPipelineObserver(kwargs))
    return self


  def mark_attr_readonly(self, **kwargs):
    """
    CommonRecoReadonlyAttrEnricher
    ------
    将指定的 attr 标记为只读，在后续流程中禁止修改（仅限于当前服务内部，对下游服务无效）

    注意：readonly 状态下对无值的 attr 也会禁止赋值。

    参数配置
    ------
    `common_attrs`: [list] 选配项，需要标记为只读的 common attr 列表

    `item_attrs`: [list] 选配项，需要标记为只读的 item attr 列表

    调用示例
    ------
    ``` python
    .mark_attr_readonly(
      common_attrs=["aaa", "bbb"],
      item_attrs=["ccc"]
    )
    ```
    """
    self._add_processor(CommonRecoReadonlyAttrEnricher(kwargs))
    return self

  def build_common_reco_response(self, **kwargs):
    """
    CommonRecoBuildResponseEnricher
    ------
    ?> build pb 操作较重，请确定有业务需求再使用，一般情况推荐使用 [dump_context](#dump_context)
    
    将当前 context 中的结果集和指定的 attr 数据快照保存到指定的 string 类型 common_attr 中，后续可发送给 redis 等外部存储供其它使用。

    参数配置
    ------
    `common_attrs`: [list] 需要保存的 common attr 列表

    `item_attrs`: [list] 需要保存的 item attr 列表
    
    `item_num`: [int] [动态参数] 需要保存的 item 数量，默认保存整个结果集
    
    `to_attr`: [string] 必配项, 将 attr 和 item 数据 dump 后的 string 内容存储到指定 common_attr 下
    
    `include_return_attrs_in_request`: [bool] 是否包含请求中指定的 return attrs ，默认为 false
    
    `as_string`: [bool] 是否将 pb 序列化为 string 进行存储，默认为 False（即以 protobuf 的 message 指针类型存储）

    调用示例
    ------
    ``` python
    .build_common_reco_response(
      common_attrs=["common_attr_1", "common_attr_2"],
      item_attrs=["item_attr_1"],
      item_num=10,
      to_attr="response_str",
      include_return_attrs_in_request=True
    )
    ```
    """
    self._add_processor(CommonRecoBuildResponseEnricher(kwargs))
    return self
  
  def reverse_item(self, **kwargs):
    """
    CommonRecoItemReverseArranger
    ------
    对当前结果集中 item 的顺序进行反转

    调用示例
    ------
    ``` python
    .reverse_item()
    ```
    """
    self._add_processor(CommonRecoItemReverseArranger(kwargs))
    return self

  def report_metric_to_kafka(self, **kwargs):
      """
      CommonRecoMetricReportObserver
      -------
      发送白盒化指标到 kafka， 目前使用 [MetricLog](https://git.corp.kuaishou.com/reco/kuaishou-reco-biz-split-proto/blob/d9657a12610a1fd26caf943346ca760bb43abbaa/kuaishou-reco-biz-split-log-base-proto/src/main/proto/kuaishou/reco/log/metric/reco_metric_log.proto#L26) 格式发送，预计在 2023 年 12 月 15日左右切到 [CommonRecoTraceback](https://git.corp.kuaishou.com/reco/kuaishou-reco-platform-proto/-/blob/master/src/main/proto/kuaishou/reco/platform/leaf/common_reco.proto#L320)格式，使用前请先联系作者。
      
      参数配置 
      ------
      `item_type`: [string] 每个 item 的类型，例如: VIDEO(视频), LIVE, GOOD;[代码位置](https://git.corp.kuaishou.com/reco/kuaishou-reco-biz-split-proto/blob/d9657a12610a1fd26caf943346ca760bb43abbaa/kuaishou-reco-biz-split-log-base-proto/src/main/proto/kuaishou/reco/log/metric/reco_metric_log.proto#L14)

      `metric_kconf_path`: [string] 业务对应的 kconf 配置，不要直接拷贝其他业务的配置

      `owner_id_attr_name`: [string] 类似 authord Id 的 item attr name
      
      `common_attr_names`: [list] 需要的 commonAttr 的指标名, 暂不支持 list 类型的特征, 需要在白名单中配置才能生效。
      
      `long_metric_attr_names`: [list] long/int 类型的指标名
      
      `double_metric_attr_names`: [list] double/float 类型的指标名
      
      `service_stage`: [string] 服务阶段，默认值是 LEAF [代码位置](https://git.corp.kuaishou.com/reco/kuaishou-reco-biz-split-proto/blob/d9657a12610a1fd26caf943346ca760bb43abbaa/kuaishou-reco-biz-split-log-base-proto/src/main/proto/kuaishou/reco/log/metric/reco_metric_log.proto#L26)
      
      `sub_stage`: [string] 服务子阶段，必须填写，例如 RETRIEVE,PRE_RANK,MC_RANK,FR_RANK,MIX_RANK; [代码位置](https://git.corp.kuaishou.com/reco/kuaishou-reco-biz-split-proto/blob/d9657a12610a1fd26caf943346ca760bb43abbaa/kuaishou-reco-biz-split-log-base-proto/src/main/proto/kuaishou/reco/log/metric/reco_metric_log.proto#L34)
      
      `product_common_attr_name`: [string] request 请求中的 product_name 字段名

      调用示例
      ------
      ``` python
      .report_metric_to_kafka(
          item_type="LIVE",
          metric_kconf_path="recoStream.merchantDataFlow.realTimeSdkTest",
          owner_id_attr_name="author_id",
          common_attr_names=["common_attr1", "common_attr2"]
          long_metric_attr_names=[
            "long_metric_name1",
            "long_metric_name2"
          ],
          double_metric_attr_names=[
            "double_metric_name1",
            "double_metric_name2"
          ],
          service_stage="LEAF",
          sub_stage="RETRIEVE",
          product_common_attr_name="app_source_type"
      )
      ```
      """
      self._add_processor(CommonRecoMetricReportObserver(kwargs))
      return self

  def supplement_abtest_mapping_id(self, **kwargs):
    """
    CommonRecoSupplementIdMappingEnricher
    ------
    补充当前请求缺失的 ab_test_mapping_id

    参数配置
    ------

    `cluster`: [string] 必配项 需要请求的 idmapping 服务集群，允许可填范围:[ ONLINE, OFFLINE, SEARCH, PUSH ]。
    
    调用示例
    ------
    ``` python
    .supplement_abtest_mapping_id(
      cluster="ONLINE",
    )
    ```
    """
    self._add_processor(CommonRecoSupplementIdMappingEnricher(kwargs))
    return self
  
  def retrieve_by_remote_colossusdb_index(self, **kwargs):
    """
    CommonRecoRemoteColossusdbIndexRetriever
    ------
    从异地部署的统一存储（ colossusdb ）版本倒排索引通过 Query 语句进行 item 召回（与 `.retrieve_by_remote_index()` 区别在于服务类型不同）

    [Query 检索语法介绍文档](https://docs.corp.kuaishou.com/k/home/<USER>/fcAC4SvGLozLHuj0wy-mSZ42Q)

    参数配置
    ------    
    `client_kconf`: [string] 统一存储版本倒排服务的请求配置
    
    `reason`: [int] 召回原因

    `common_query`: [string] 公共查询条件，会分别跟 `querys` 中配置的各个 query 语句进行 AND 操作

    `querys`: [list] 查询条件列表，多个条件之间取并集进行结果返回
      - `query`: [string] 查询语句
      - `search_num`: [int] [动态参数] 每条查询语句的索引查找数目，可选项，缺省时将使用 default_search_num 值（备注：每条查询语句是指展开后的每条查询语句，也就是说对于 CommonAttr 里的每个值都会查找 `search_num` 个）
      - `random_search`: [int] [动态参数] 索引随机查找开关，0 or 1，可选项，缺省时将使用 default_random_search 值
      - `reason`: [int] 选配项，针对单个 query 设置召回原因，默认为顶层配置的 `reason` 值
      - `expire_second`: [int] [动态参数] 索引 server 端查询结果 cache 的过期时间（仅对 random search 有效），缺省时将使用 default_expire_second 的值
      - `max_attr_num`: [int] [动态参数] 当查询条件中包含 list 类型 attr 时，通过该配置限制取用的 list 大小，默认值 10000

    `save_score_to_attr`: [string] 如果不为空，且查询到的 score 不为空，则将各个召回 item 对应的 score 存入该 item_attr 中，可缺省

    `save_query_index_to_attr`: [string] 如果不为空，则将各个召回 item 对应的 query index 存入该 item_attr 中，可缺省 （备注：query index 指的是各个 query 全部展开后从 0 开始的 index）

    `default_search_num`: [int] 查询条件召回数目缺省值，默认值为 0

    `default_random_search`: [int] 随机召回开关缺省值，默认值为 1

    `default_total_request_num`: [int] [动态参数] 多 query 情况下的熔断数目，当前序 query 的总返回数超过该值时，后续 query 将不再执行，缺省则不做任何熔断处理

    `browsed_item_count`: [int] [动态参数] 请求携带的 browsed item 个数，用于在召回服务端进行前置过滤，正数为最近，0 为全部，负数为取最远的 abs(browsed_item_count) 个，缺省则不过滤！browsed item 来自请求中的 browse set

    `exclude_items_in_attr`: [string] 选配项, 指定一个 int/int_list 类型的 CommonAttr 对待召回 item 进行提前过滤

    `field_replace_kconf`: [string] 选配项，特征替换 ab 功能的 kconf 路径

    `field_replace_kconf_watch`: [bool] 选配项，默认不监控 kconf 动态变更，仅重启生效（默认为 False 的原因是，获取正排 processor 是重启生效的，是为了对齐正排）。如果配置成 True 表示随 kconf 更新动态变更。

    调用示例
    ------
    ``` python
    .retrieve_by_remote_colossusdb_index(
      client_kconf = "aaa.bbb.ccc",
      reason = 1000,
      common_query = "resource_type:CONTENT_VIDEO",
      querys = [{
        "query": "show_count_log2:{{8~15}}",
      }, {
        # 单值情况不需要 {{}} 包裹
        "query": "show_count_log:7",
      }, {
        # 从 channel_list CommonAttr 中的 channel 进行召回
        "query": "sub_channel_id:{{channel_list}}",
        # 最多召回的 channel 数目
        "max_attr_num": 10,
      }],
      default_search_num = 2000,
      default_random_search = 1
    )
    ```
    """
    self._add_processor(CommonRecoRemoteColossusdbIndexRetriever(kwargs))
    return self


  def get_rodis_counter(self, **kwargs):
    """
    CommonRecoRodisCounterEnricher
    ------
    根据结果集从 RodisCounter 填充计数
    
    参数配置
    ------

    `action_list`: [list] 必配项 计数配置列表，每个配置包含以下参数：
      - `action`: [string] 必配项 ActionEnum::ActionType 单值， 单值为枚举值的字符串，例如：SERVER_SHOW。
      - `channel_list`: [list] 必配项 ActionEnum::ChannelType 集合，单值为枚举值的字符串，例如：CHANNEL_BL_HOT。
      - `save_count_to`: [string] 必配项 存储返回计数的 attr 名称。注意：该计数 channel_list 全部数值的 sum。

    `group_by`: [string] 选配项 包含分组列表的 item attr。 注：该参数需要为 string list 类型的 item attr。

    `rodis_kess_name`: [string] 必配项 PhotoCountService 服务 kess_name。

    `rodis_domain`: [string] 必配项 PhotoCountService 服务 domain

    `timeout_ms`: [int64] 选配项 请求超时时间，默认值为 10。
    
    调用示例
    ------
    ``` python
    .get_rodis_counter(
      action_list=[{
        "action": "SERVER_SHOW",
        "channel_list": ["CHANNEL_BL_HOT", "CHANNEL_SL_RANDOM"],
        "save_count_to": "photo_count"
      }],
      rodis_kess_name = "grpc_rodisxxxxx",
      rodis_domain = "SOMEDOMAIN",
      timeout_ms=10
    )
    ```
    """
    self._add_processor(CommonRecoRodisCounterEnricher(kwargs))
    return self
  
  def reverse_list_attr(self, **kwargs):
    """
    CommonRecoReverseListAttrEnricher
    ------
    对 list 类型的 CommonAttr/ ItemAttr 进行 reverse

    参数配置
    ------
    `common_attr`: [string] 指定被 reverse 的 CommonAttr

    `item_attr`: [string] 指定被 reverse 的 ItemAttr

    `common_attrs`: [list] 指定被 reverse 的 CommonAttr List, 自动去重 [recommended]

    `item_attrs`: [list] 指定被 reverse 的 ItemAttr List, 自动去重 [recommended]

    注意：`common_attr` `item_attr` `common_attrs` `item_attrs`至少有一项或两项
          common_attrs/common_attr、item_attrs/item_attr同时出现时取并集(去重)

    调用示例
    ------
    ``` python
    .reverse_list_attr(common_attrs=["click_item_id_list", "click_item_category_list"])
    ```
    """
    self._add_processor(CommonRecoReverseListAttrEnricher(kwargs))
    return self

  def enrich_attr_by_py(self, **kwargs):
    """
    CommonRecoPyAttrEnricher
    ------
    根据自定义的 Python 脚本逻辑对 context 中的变量（common 和 item）进行操作（读取和生成）
    [用户手册](https://docs.corp.kuaishou.com/d/home/<USER>

    注意事项：
    - py_function 直接 copy 下面调用示例改函数名即可，由于涉及到依赖导入，函数第二个参数必须为 ctx ！
    - 变量首次定义时声明类型可以提高执行速度，容器类型建议声明为 FT 系列容器，如 FTList[int], FTSet[int], FTDict[str, int]，需要其中 kv 存储同一类型，声明 case： a: FTList[int] = list()
    - 不支持 lambda、** 运算符
    - 不支持 sum 函数
    - 定义 double 类型请加入小数点，如：a = 0.0

    参数配置
    ------
    `function_set`: [class] 选配项，指定使用哪个 function set（组织管理 python function 的类），存在该配置时，该配置为编译所生成文件名的前缀。

    `py_function`: [function] 选配项，指定使用哪个 python function，如果配置 function_set，则必须是 function_set 中的方法；也可以只使用 py_function，此时该配置为编译所生成文件名的前缀。

    `import_common_attr`: [list] 选配项，手动指定 common attr 输入，支持 name as 重命名，未在该项中声明的 attr 会由依赖推导自动添加，在该项中声明的 attr 若未使用会报错。（建议仅用该项添加需要 name as 的 attr）

    `import_item_attr`: [list] 选配项，手动指定 item attr 输入，支持 name as 重命名，未在该项中声明的 attr 会由依赖推导自动添加，在该项中声明的 attr 若未使用会报错。（建议仅用该项添加需要 name as 的 attr）

    `export_common_attr`: [list] 选配项，手动指定 common attr 输出，支持 name as 重命名，未在该项中声明的 attr 会由依赖推导自动添加，在该项中声明的 attr 若未使用会报错。（建议仅用该项添加需要 name as 的 attr）

    `export_item_attr`: [list] 选配项，手动指定 item attr 输出，支持 name as 重命名，未在该项中声明的 attr 会由依赖推导自动添加，在该项中声明的 attr 若未使用会报错。（建议仅用该项添加需要 name as 的 attr）

    `function_code`: [string] 选配项，字符串格式的 python 函数代码，与 py_function 不能共同配置。

    `imports`: [list] 选配项，import 语句列表，当 function_code 需要 import 外部库或变量时需要添加，如 ['import math']。

    `auto_add_dependency`: [bool] 选配项，是否自动根据 python 函数自动推倒依赖，默认为 True。如果函数需要从一个变量表示的 attr_name 获取 value，可以关闭该选项后手动添加所有可能获取的 import/export attr。

    调用示例
    ------
    ``` python
    # function set 写法
    from dragonfly.matx.dragonfly_context import *

    class FunctionSet:
      def __init__(self) -> None:
        pass
  
      def mark_tandian_photo(self, ctx: DragonflyContext) -> None:
        # Common Attr 处理逻辑 
        s_level_good_tandian_id = ctx.GetInt(b"s_level_good_tandian_id")
        a_level_good_tandian_id = ctx.GetInt(b"a_level_good_tandian_id")

        # Item Attr Getter
        hetu_tag_getter = ctx.ItemAttrGetter(b"hetu_tag_level_info__hetu_tag")
        
        # Item Attr Setter
        is_s_level_tandian_photo_setter = ctx.ItemAttrSetter(b"is_s_level_tandian_photo_py2")
        is_a_level_tandian_photo_setter = ctx.ItemAttrSetter(b"is_a_level_tandian_photo_py2")
        
        # Item For 循环
        result_size = ctx.GetItemNum()
        for i in range(result_size):
          is_s_level_tandian_photo = 0
          is_a_level_tandian_photo = 0
          hetu_tag_list = hetu_tag_getter.GetIntList(i)
          for tag in hetu_tag_list:
            if tag == s_level_good_tandian_id:
              is_s_level_tandian_photo = 1
            elif tag == a_level_good_tandian_id:
              is_a_level_tandian_photo = 1
          is_s_level_tandian_photo_setter.SetInt(i, is_s_level_tandian_photo)
          is_a_level_tandian_photo_setter.SetInt(i, is_a_level_tandian_photo)

    # dsl 写法
    .enrich_attr_by_py(
      function_set = FunctionSet,
      py_function = FunctionSet.mark_tandian_photo
    )

    # code 用法
    attr_list = ['test_out1', 'test_out2']
    body = "\\n".join(f"    ctx.SetInt(b'{attr}', 123)" for attr in attr_list)
    function_code = f"def calc(ctx: DragonflyContext) -> None:\\n{body}"
    self.enrich_attr_by_py(
      imports = ['import math'],  # 无额外 import 时不写
      function_code = function_code
    )
    ```
    """

    self._add_processor(CommonRecoPyAttrEnricher(kwargs))
    return self

  def random_stable_shuffle(self, **kwargs):
    """
    CommonRecoRandomStableShuffleArranger
    ------
    根据某个属性进行随机稳定打散
    该算子以连续的相同属性为标记切块，块内原始顺序不变，块之间随机打散。
    如果需要保证相同属性的顺序，请开启 stable_gather=True 对属性进行稳定排序，将不连续相同属性 item 聚合在一起。
    相关性能简约说明:
    | 10000 个 item       | 耗时(约)  |
    | stable_gather=True  | 1.2ms    |
    | stable_gather=False | 0.5ms    |
    
    参数配置
    ------

    `shuffle_by_reason`: [bool] 必配项 按照 reason 进行随机稳定打散 shuffle_by_reason 与 shuffle_by_channel 必须有且只能有一个为 True。

    `shuffle_by_channel`: [bool] 必配项 按照 channel 进行随机稳定打散 shuffle_by_reason 与 shuffle_by_channel 必须有且只能有一个为 True。

    `stable_gather`: [bool] 选配项 是否在打散前先根据打散属性进行一次聚合，保证相同属性连续且原始顺序不变。默认为 False
    
    调用示例
    ------
    ``` python
    .random_stable_shuffle(
      shuffle_by_reason = True
    )
    ```
    """
    self._add_processor(CommonRecoRandomStableShuffleArranger(kwargs))
    return self

  def get_rodis_value_no_proxy(self, **kwargs):
    """
    CommonRecoRodisNoProxyAttrEnricher
    ------
    注意：
    用于特定需求的临时算子，使用前请联系liuyuanjun03 !!!!!
    该算子仅用于短视频精排项目临时推全使用，为了方便组内同学迭代，不是通用算子，后续会下掉，不了解情况的不要用这个算子
    该算子的功能，后续会合入到get_rodis_value

    参数配置
    ------
    `kess_name`: [string] Rodis 集群的 kess name

    `doamin`: [string] Rodis 数据的 domain

    `payload_id`: [int] Rodis 数据的 payload id

    `timeout_ms`: [int] 超时时间, 默认 10ms

    `key_attr`: [string] 从哪个 attr 读 key.

    `value_attr`: [string] 结果填充到哪个 attr.
    """
    self._add_processor(CommonRecoRodisNoProxyAttrEnricher(kwargs))
    return self

  def get_rodis_value(self, **kwargs):
    """
    CommonRecoRodisAttrEnricher
    ------
    从 rodis 中读取 string 类型的 attr 存到 context.
    当前仅支持通过 TimeList 类型存储, 比较适合带版本号的大数据

    参数配置
    ------
    `kess_name`: [string] Rodis 集群的 kess name

    `doamin`: [string] Rodis 数据的 domain

    `payload_id`: [int] Rodis 数据的 payload id

    `timeout_ms`: [int] 超时时间, 默认 10ms

    `key_attr`: [string] 从哪个 attr 读 key.

    `value_attr`: [string] 结果填充到哪个 attr.

    `valid_duration_ms`: [int] 数据过期时间, 默认为 -1, 即不过期.

    调用示例
    ------
    ``` python
    .get_rodis_value(
      kess_name="grpc_rodisCamelCase",
      domain="TEST_DOMAIN",
      payload_id=1,
      key_attr="key",
      value_attr="value",
    )
    ```
    """
    self._add_processor(CommonRecoRodisAttrEnricher(kwargs))
    return self

  def write_to_rodis(self, **kwargs):
    """
    CommonRecoRodisAttrObserver
    ------
    将 common attr 里的值以 kv 对的形式写到 rodis, 可以配合 get_rodis_value 读回来
    当前仅支持通过 TimeList 类型存储, 比较适合带版本号的大数据

    参数配置
    ------
    `kess_name`: [string] Rodis 集群的 kess name

    `doamin`: [string] Rodis 数据的 domain

    `payload_id`: [int] Rodis 数据的 payload id

    `timeout_ms`: [int] 超时时间, 默认 10ms

    `key_attr`: [string] 从哪个 attr 读 key.

    `value_attr`: [string] 结果填充到哪个 attr.

    调用示例
    ------
    ``` python
    .write_to_rodis(
      kess_name="grpc_rodisCamelCase",
      domain="TEST_DOMAIN",
      payload_id=1,
      key_attr="key",
      value_attr="value",
    )
    ```
    """
    self._add_processor(CommonRecoRodisAttrObserver(kwargs))
    return self

  def streaming_enrich(self, **kwargs):
    """
    CommonRecoStreamingEnricher
    ------
    调用另一个基于 CommonLeaf streaming rpc 模式的远程服务进行计算，并填充返回的属性

    参数配置
    ------
    `service`: [string] 调用的 CommonLeaf 的 kess service

    `timeout_ms`: [int] [动态参数] 选填项，gRPC 超时时间，默认为 300ms。

    `request_type`: [string] [动态参数] 选填项，请求的 request type，默认为本 leaf 当前的 request type。

    `send_common_attrs`: [list] 选填项，发送的 common attr 列表，默认不发送 common attr，支持对 attr 重命名发送。

    `recv_common_attrs`: [list] 选填项，接收的 common attr 列表，默认不接受 common attr，支持对 attr 重命名保存。

    调用示例
    ------
    ``` python
    .streaming_enrich(
      service = "grpc_KrpCommonLeafTest",
      send_common_attrs = ["query"],
      recv_common_attrs = ["sub_answer"],
    )
    ```
    """
    self._add_processor(CommonRecoStreamingEnricher(kwargs))
    return self

  def streaming_status(self, code: int = 0, save: str = "", **kwargs):
    """
    CommonRecoStreamingStatusEnricher
    ------
    设置 streaming 的状态

    参数配置
    ------
    `status`: [bool] [动态参数] 选填项，设置 streaming 的状态，默认为 0。 0: 正常 1: 异常 2: 结束。status != 0 时终止 streaming 的处理流程

    `save_streaming_status_to`: [string] 选填项，将当前 streaming status 保存到指定 common attr
    
    调用示例
    ------
    ``` python
    .streaming_status(1)
    ```
    """
    if code >= 0:
      kwargs["status"] = code
    if save:
      kwargs["save_streaming_status_to"] = save
    self._add_processor(CommonRecoStreamingStatusEnricher(kwargs))
    return self

  def send_streaming_response(self, **kwargs):
    """
    CommonRecoPipelineObserver
    ------
    循环执行 streaming 相关逻辑。每次循环结束时，会把 return_common_attrs 指定的 common attr 打包成 CommonRecoResponse 返回给上游, 并将本 subflow 中的 common attr 清空。

    参数配置
    ------
    `enable`: [bool] 选填项，是否开启 streaming 模式, 默认为 True

    `return_common_attrs`: [bool] 选填项，通过 streaming 返回的 common attr 列表

    `save_streaming_loop_index_to`: [string] 选填项，把循环次数保存到指定的 common attr，从 0 开始

    其他参数同 observe_by_sub_flow

    调用示例
    ------
    ``` python
    .send_streaming_response(
      return_common_attrs = ["answer"],
      sub_flow = streaming_flow
    )
    ```
    """
    streaming_config = {"enable": True}
    for key in ["enable", "return_common_attrs", "save_streaming_loop_index_to"]:
      if key in kwargs:
        streaming_config[key] = kwargs[key]
        kwargs.pop(key)
    kwargs["streaming_config"] = streaming_config
    self._add_processor(CommonRecoPipelineObserver(kwargs))
    return self

  def item_attr_operation(self, **kwargs):
    """
    CommonRecoItemAttrOperationEnricher
    ------
    item attr 基础运算： output_attr = item_attr_a operator (item_attr_b or common_attr_b)

    参数配置
    ------
    `item_attr_a`: [string] item attr 的变量名，支持 int 和 double 类型

    `common_attr_b`: [int/double] [动态参数] 选配项，用于运算的常量。和 item_attr_b 至少配置一个。同时配置时，按 common_attr_b 计算
    
    `item_attr_b`: [string] 选配项，item attr 的变量名，支持 int 和 double 类型。和 common_attr_b 至少配置一个

    `operator`: [string] 运算符，支持四则运算（"+", "-", "*", "/"）, 位运算（"&", "|", "^", "<<", ">>"），指数运算（"pow"）

    `output_attr`: [string] 输出的 item_attr 的变量名

    调用示例
    ------
    ``` python
    .item_attr_operation(
      item_attr_a="item_attr_a",
      item_attr_b="item_attr_b",
      operator="+",
      output_attr="item_attr_c"
    )
    ```
    """
    self._add_processor(CommonRecoItemAttrOperationEnricher(kwargs))
    return self

  def item_attr_missing_rate(self, **kwargs):
    """
    CommonRecoItemAttrMissingRateEnricher
    ------
    统计 item_attr 空值率，没有这个 item_attr 的 item 数量 / item 总数量，并将结果存入 common_attr, 不存在 item 时不会设 common_attr

    参数配置
    ------
    `check`: [list] 需要统计的 item_attr 和 存结果的 common_attr 列表
      - `item_attr`: [string] 需要统计空值率的 item_attr 变量名
      - `save_rate_to`: [string] 结果存入的 common_attr 变量名

    调用示例
    ------
    ``` python
    .item_attr_missing_rate(
      check = [
        {
            "item_attr": "item_attr_a",
            "save_rate_to": "common_attr_b"
        },
      ]
    )
    ```
    """
    self._add_processor(CommonRecoItemAttrMissingRateEnricher(kwargs))
    return self

  def get_clotho_value(self, **kwargs):
    """
    CommonRecoClothoAttrEnricher
    ------
    从 clotho 中读取 kv 类型的数据 存到 context.

    参数配置
    ------
    `kess_name`: [string] Clotho 的 kess name

    `table_name`: [string] Clotho 数据的 table

    `kconf_name`: [int] Clotho 数据的 kconf

    `token_name`: [int] Clotho 请求token

    `timeout_ms`: [int] 超时时间, 默认 200ms

    `key_attr`: [string] 从哪个 attr 读 key.

    `value_attr`: [column1, column2, {"from": column3, "to": attr3} ] 结果填充到哪个 attr.

    `read_all_column`: [bool] 是否读取所有列数据，默认false.

    调用示例
    ------
    ``` python
    .get_clotho_value(
      kess_name="reco-rodis-gateway-test-clotho",
      table_name="clotho_rodis_test",
      kconf_key = "reco.rodisV1.clotho_rodis_test",
      key_attr="key",
      value_attr=["value"],
    )
    ```
    """
    self._add_processor(CommonRecoClothoAttrEnricher(kwargs))
    return self

  def write_to_clotho(self, **kwargs):
    """
    CommonRecoClothoAttrObserver
    ------
    将 common attr 里的值以 kv 对的形式写到 clotho, 可以配合 get_clotho_value 读回来

    参数配置
    ------
    `kess_name`: [string] Clotho 的 kess name

    `table_name`: [string] Clotho 数据的 table

    `token_name`: [string] Clotho 的 token

    `kconf_key`: [int] Clotho 数据的 元配置kconf路径, 定义了列名以及数据格式。

    `timeout_ms`: [int] 超时时间, 默认200ms

    `key_attr`: [string] 从哪个 attr 读 key.

    `value_attr`: [attr1, attr2, {"from": attr3, "to": column3} ] 将哪个 attr写到clotho.

    调用示例
    ------
    ``` python
    .write_to_clotho(
      kess_name="reco-rodis-gateway-test-clotho",
      table_name="clotho_rodis_test",
      token_name = "clotho_test",
      timeout_ms = 200,
      kconf_key = "reco.rodisV1.clotho_rodis_test",
      key_attr="key",
      value_attr=["value"],
    )
    ```
    """
    self._add_processor(CommonRecoClothoAttrObserver(kwargs))
    return self

  def find_value(self, **kwargs):
    """
    CommonRecoFindValueEnricher
    ------
    在 common list attr 里查找某个 value 是否存在

    参数配置
    ------
    `input`: [string] [动态参数] 查找 value 的 common list attr, 支持的类型包括 int_list/double_list/string_list

    `value`: [string] [动态参数] 查找的 value，input 为 int_list，value 需要为 int，input 为 double_list value 需要为 double, input 为 string_list value 需要为 string

    `count`: [bool] 是否统计出现次数，默认为 False

    `result`: [int] count 为 False 时，表示 value 是否在 input 中存在，1: 存在 0: 不存在。count 为 True 时，表示 value 在 input 中出现的次数。value 与 input 类型不匹配时，result 为 0。

    `output_index`: [string] 选填，输出 value 在 input 中的下标。count 为 False 时，输出首次出现的位置下标，输出的 attr 类型为 int。count 为 True 时，输出所有出现位置的下标，如果出现次数大于 1 次，输出的 attr 类型为 int_list，如果只出现 1 次，输出的 attr 类型为 int。

    调用示例
    ------
    ``` python
    .find_value(
      input="{{user_click_photo_list}}",
      value="{{pid}}",
      result="is_clicked"
    )
    ```
    """
    self._add_processor(CommonRecoFindValueEnricher(kwargs))
    return self

  def get_geoinfo(self, **kwargs):
    """
    CommonRecoGeoInfoEnricher
    ------
    根据经纬度获取地理相关信息, 包括 adcode bdcode, 底层使用 infra 提供的 geoinfo lib: https://halo.corp.kuaishou.com/help/docs/4712e2cb3ad9c52a20dbcf9521e3c425

    注意：转换 bdcode 需要额外设置 gflag: `--infra_geoinfo_is_search_bd=true`

    参数配置
    ------
    `is_common_attr`: [bool] 是否为 common attr，默认为 True。

    `lat_attr`: [double] 纬度

    `lon_attr`: [double] 经度

    `save_bdcode_to_attr`: [string] 将转换后的 bdcode (商圈代码) 保存到指定 attr

    `save_adcode_to_attr`: [string] 将转换后的 adcode (区县代码) 保存到指定 attr

    `save_nation_to_attr`: [string] 将转换后的 nation (国家) 保存到指定 attr

    `save_province_to_attr`: [string] 将转换后的 province (省份) 保存到指定 attr

    `save_city_to_attr`: [string] 将转换后的 city (城市) 保存到指定 attr
    
    `save_county_to_attr`: [string] 将转换后的 county (区/县) 保存到指定 attr

    `save_bdname_to_attr`: [string] 将转换后的 bdname (商圈名) 保存到指定 attr

     调用示例
    ------
    ``` python
    .get_geoinfo(
        lat_attr = "lat",
        lon_attr = "lon",
        save_adcode_to_attr = "adcode",
        save_bdcode_to_attr = "bdcode"
    )
    ```
    """
    self._add_processor(CommonRecoGeoInfoEnricher(kwargs))
    return self

  def rodis_unique_list_observer(self, **kwargs):
    """
    CommonRecoRodisUniqueListObserver
    ------
    将 common attr 中所需 item list 信息以 UniqueList 格式存入rodis
    配合 rodis_unique_list_enricher 算子读取

    参数配置
    -----
    `kess_name`: [string] rodis 集群的 kess name

    `domain`: [string] rodis 集群对应数据的 domain

    `payload_id`: [int] rodis 集群对应数据的 payload id

    `timeout_ms`: [int] 请求 rodis 的超时时间，默认10ms

    `key_attr`: [string] 以指定 common attr 的值作为 UniqueList 的 key

    `unique_key_attr`: [string] 以指定 list 类型 common attr 的值作为 UniqueListItem 的 unique_key

    `sort_key_attr`: [string] 以指定 list 类型 common attr 的值作为 UniqueListItem 的 sort_key

    `data_attr_`: [string] 以指定 list 类型 common attr 的值作为 UniqueListItem 的 data

    调用示例
    ------
    ``` python
    .rodis_unique_list_observer(
        kess_name = "grpc_testRodis",
        domain = "TEST_DOMAIN",
        payload_id = 1,
        timeout_ms = 20,
        key_attr = "key",
        unique_key_attr = "unique_key",
        sort_key_attr = "sort_key",
        data_attr = "data"
    )
    ```
    """
    self._add_processor(CommonRecoRodisUniqueListObserver(kwargs))
    return self

  def rodis_unique_list_enricher(self, **kwargs):
    """
    CommonRecoRodisUniqueListEnricher
    ------
    读取 rodis 中对应 key 的 UniqueList 格式数据，并将其中 UniqueListItem 信息分别存入 context 中
    配合 rodis_unique_list_observer 算子写入

    参数配置
    -----
    `kess_name`: [string] rodis 集群的 kess name

    `domain`: [string] rodis 集群对应数据的 domain

    `payload_id`: [int] rodis 集群对应数据的 payload id

    `timeout_ms`: [int] 请求 rodis 的超时时间，默认10ms

    `key_attr`: [string] 以指定 common attr 的值作为 UniqueList 的 key

    `unique_key_attr`: [string] 解析返回的 UniqueListItem 中 unique_key 存放到指定 list 类型的 common attr

    `sort_key_attr`: [string] 解析返回的 UniqueListItem 中 sort_key 存放到指定 list 类型的 common attr

    `data_attr_`: [string] 解析返回的 UniqueListItem 中 data 存放到指定 list 类型的 common attr

    调用示例
    ------
    ``` python
    .rodis_unique_list_enricher(
        kess_name = "grpc_testRodis",
        domain = "TEST_DOMAIN",
        payload_id = 1,
        timeout_ms = 20,
        key_attr = "key",
        unique_key_attr = "unique_key",
        sort_key_attr = "sort_key",
        data_attr = "data"
    )
    ```
    """
    self._add_processor(CommonRecoRodisUniqueListEnricher(kwargs))
    return self

  def memory_data_enrich(self, **kwargs):
    """
    CommonMemoryDataEnricher
    ------
    从内存中获取指定类型的数据指针保存到 common attr 中

    参数配置
    -----
    `data_key`: [string] [动态参数] 数据的 key

    `data_type`: [string] 数据类型

    `save_data_ptr_to_attr`: [string] 保存数据指针的 common attr

    调用示例
    ------
    ``` python
    .memory_data_enrich(
      data_key = "explore_mc_dynamic_xtr_debias_map",
      data_type = "string_double_vector_map",
      save_data_ptr_to_attr = "explore_mc_hourly_xtr_debias_map_ptr",
    )
    ```
    """
    self._add_processor(CommonMemoryDataEnricher(kwargs))
    return self

  def enrich_common_attr_by_http(self, **kwargs):
    """
    CommonRecoHttpCommonAttrEnricher
    ------
    通过 HTTP 请求获取 common attr 的值，目前不支持 ssl，通常搭配 enrich_attr_by_json 解析返回的 json 数据

    参数配置
    ------
    `url`: [string] 必填，请求的 url 地址

    `path`: [string] 必填，请求的 path 路径

    `method`: [string] 必填，请求的方法，支持 "GET" 和 "POST"

    `headers`: [list] 选填，请求的 headers，默认包含 "Content-Type": "application/json"
      - `name`: [string] header 的 key
      - `value`: [string]【动态参数】header 的 value

    `body`: [json] 选填，请求的 body，key 为 string，value 为 string【动态参数】，支持 list 和 dict 嵌套，默认不传 body

    `input_common_attrs`: [list] 选填，指明 `body` 的动态参数所需的 common_attr，影响 attr 依赖检测

    `output_common_attr`: [string] 必填，返回结果 body 存入的 common attr

    `timeout_ms`: [int]【动态参数】 选填，超时时间，默认 100 ms

    `keep_alive`: [bool] 选填，是否保持长连接，默认为 True

    `forbib_reuse`: [bool] 选填，是否禁止重用连接，默认为 False

    调用示例
    ------
    ``` python
    .enrich_common_attr_by_http(
      url = "http://postman-echo.com",
      path = "/post",
      method = "POST",
      headers = [
        {"name": "User-Agent", "value": "dragonfly"},
      ],
      body = {"test": "test"},
      output_common_attr = "http_result",
      timeout_ms = 1000
    )
    ```
    """
    self._add_processor(CommonRecoHttpCommonAttrEnricher(kwargs))
    return self

  def get_rodis_timelist(self, **kwargs):
    """
    CommonRecoRodisTlAttrEnricher
    ------
    从 rodis 中读取 string 类型的 attr 存到 context.
    当前仅支持通过 TimeList 类型存储, 多条件筛选

    参数配置
    ------
    `kess_name`: [string] Rodis 集群的 kess name

    `doamin`: [string] Rodis 数据的 domain

    `payload_id`: [int] Rodis 数据的 payload id

    `timeout_ms`: [int] 超时时间, 默认 10ms

    `key_attr`: [string] 从哪个 attr 读 key.

    `value_attr`: [string] 结果填充到哪个 attr.

    `valid_duration_ms`: [int] 数据过期时间, 默认为 -1, 即不过期.

    `len_limit`: [int] 设置 action list 的 长度，最大为 200.

    `after_ts`: [int] action list 时间范围的上限.

    `before_ts`: [int] action list 时间范围的下限.

    `min_len`: [int] action list 至少返回多少个.

    调用示例
    ------
    ``` python
    .get_rodis_timelist(
      kess_name="grpc_rodisCamelCase",
      domain="TEST_DOMAIN",
      payload_id=1,
      key_attr="key",
      value_attr="value",
      len_limit=6000,
      after_ts=0,
      before_ts=0,
      min_len=0,
    )
    ```
    """

    self._add_processor(CommonRecoRodisTlAttrEnricher(kwargs))
    return self

  def dot_product(self, **kwargs):
    """
    CommonRecoDotProductEnricher
    ------
    向量进行点积运算 x · y ，支持 common 与 common 进行点积、common 与 item 直接进行点积，结果保存到指定的 attr 中

    参数配置
    ------
    `x_attr`: [string] 进行点积运算的 common attr 名称

    `y_attr`: [string] 进行点积运算的 attr 名称

    `y_is_common_attr`: [bool] y_attr 是否为 common attr，默认为 false

    `save_result_to`: [string] 保存点积结果。y_is_common_attr 为 false 时，结果保持到指定的 item attr 中，y_is_common_attr 为 true 时，结果保存到 common attr 中

    调用示例
    ------
    ``` python
    .dot_product(
      x_attr="x",
      y_attr="y",
      save_result_to="result"
    )
    ```
    """

    self._add_processor(CommonRecoDotProductEnricher(kwargs))
    return self
  
  def retrieve_by_new_elastic_search(self, **kwargs):
    """
    CommonRecoElasticSearchRetriever
    ------
    从 elastic search 中通过查询语句获取结果，并将返回结果加入结果集，支持解析返回的 item_attr，解析结构如下：
    ``` json
    {
      "hits": {
        "hits": [
          {
            "_id": "111",
            "_score": 0.99,
            "_source": {
              "item_attr1": xxx,
              "item_attr2": xxx,
            }
          },
          {
            ...
          }
        ]
      }
    }
    ```

    参数配置
    ------
    `clusters`: [string][动态参数] 必填，es 集群，ip:port 形式，多个以英文逗号分隔

    `index`: [string][动态参数] 必填，表示 es 的索引，详细 es 的说明可以见官方文档

    `headers`: [list] 选填，请求的 headers，"Content-Type" 不支持更改，默认为 "application/json"
      - `name`: [string] header 的 key
      - `value`: [string][动态参数] header 的 value

    `query`: [string][动态参数] 必填，表示 es 的查询语句，详细 es 的说明可以见官方文档

    `timeout_ms`: [int] 选填，超时时间，单位 ms，默认值 200
    
    `reason`: [int] 选填，指定召回原因，默认为 0

    `item_attrs`: [list] 选填，指定需要从 es 返回值中解析的 item_attr 列表，默认不解析，支持下面两种格式的值：
      - string 格式：直接填写 item attr 的名称，类型默认为 string 类型
      - dict 格式：填写 {"name": "item_attr1", "type": "int"}，name 字段为 item attr 的名称，type 字段为 item attr 的类型，支持 int/float/string/float_list 类型，默认为 string 类型。

    调用示例
    ------ 
    ``` python
    .retrieve_by_new_elastic_search(
      clusters="127.0.0.1:9200,xx.xx.xx.xx:9200",
      index="index_name",
      headers = [
        {"name": "Authorization", "value": "xxx"},
      ],
      query="query_string",
      reason=100,
      timeout_ms=200,
      item_attrs=[
        "item_attr1",
        {"name": "item_attr2", "type": "int"},
        {"name": "item_attr3", "type": "string"},
      ]
    )
    ```
    """
    self._add_processor(CommonRecoElasticSearchRetriever(kwargs))
    return self

  def enrich_by_elastic_search(self, **kwargs):
    """
    CommonRecoElasticSearchEnricher
    ------
    从 elastic search 中通过查询语句获取结果，解析返回的 item_attr 并填充到 context 中，解析结构如下：
    ``` json
    {
      "hits": {
        "hits": [
          {
            "_id": "111",
            "_score": 0.99,
            "_source": {
              "item_attr1": xxx,
              "item_attr2": xxx,
              ...
            }
          }, ...]}
    }
    ```

    参数配置
    ------
    `clusters`: [string][动态参数] 必填，es 集群，ip:port 形式，多个以英文逗号分隔

    `index`: [string][动态参数] 必填，表示 es 的索引，详细 es 的说明可以见官方文档

    `headers`: [list] 选填，请求的 headers，"Content-Type" 不支持更改，默认为 "application/json
      - `name`: [string] header 的 key
      - `value`: [string][动态参数] header 的 value

    `query`: [string][动态参数] 必填，表示 es 的查询语句，详细 es 的说明可以见官方文档

    `timeout_ms`: [int] 选填，超时时间，单位 ms，默认值 200

    `save_score_to`: [string] 选填，表示将查询结果的 _score 字段值存入指定的 item_attr 中，默认不存储

    `default_score`: [double] 选填，表示当查询结果为空时，默认的 _score 值，默认值 0.0

    `save_response_to`: [string] 选填，表示将查询结果整体存入指定的 common_attr 中，默认不存储

    `item_attrs`: [list] 选填，指定需要从 es 返回值中解析的 item_attr 列表，需完整配置 name，type，value 字段
      - `name`: [string] item attr 的名称
      - `type`: [string] item attr 的类型，支持 int/float/string/float_list 类型
      - `value`: [int/float/string/float_list] 默认值，当 item_attr 不存在时使用默认值

    调用示例
    ------
    ``` python
    .enrich_by_elastic_search(
      clusters="127.0.0.1:9200,xx.xx.xx.xx:9200",
      index="index_name",
      headers = [
        {"name": "Authorization", "value": "xxx"},
      ],
      query="query_string",
      timeout_ms=200,
      save_score_to="score",
      item_attrs=[
        {"name": "item_attr1", "type": "int", "value": 0},
        {"name": "item_attr2", "type": "string", "value": "xxx"},
        {"name": "item_attr3", "type": "float_list", "value": [0.1, 0.2]},
        ...
      ]
    )
    ```
    """
    self._add_processor(CommonRecoElasticSearchEnricher(kwargs))
    return self

  def init_common_reco_context(self, **kwargs):
    """
    CommonRecoContextEnricher
    ------
    新建 CommonRecoContextEnricher 对象并存储到指定的 common_attr 中

    参数配置
    ------
    `output_common_attr`: [string] 必填，指定存储 CommonRecoContextEnricher 对象的 common_attr 名称

    `pass_common_attrs_in_request`: [bool] 选填，是否将当前请求中的 common attrs 传递到 CommonRecoContextEnricher 对象中，默认为 False

    `pass_browse_set`: [bool] 选填，是否将当前请求中的 browse set 传递到 CommonRecoContextEnricher 对象中，默认为 True

    调用示例
    ------
    ``` python
    .init_common_reco_context(
      output_common_attr = "context_attr",
      pass_common_attrs_in_request = False,
      pass_browse_set = True
    )
    ```
    """

    self._add_processor(CommonRecoContextEnricher(kwargs))
    return self

  def fetch_browse_set(self, **kwargs):
    """
    CommonRecoFetchBrowseSetEnricher
    ------
    读取kconf配置的BrowseSetService，将结果存入当前context的BrowseSet中

    参数配置
    ------
    `biz_name`: [string] 必填，读取的配置名,browse_set_config_kconf_key中对应的BIZ_NAME

    `save_to_common_attr`: [string] 选填，若配置则将读取到的browseset对象放入该common_attr(ptr)中，若未配置则将读取到的browseset设置到context中

    `browse_set_config_kconf_key`: [string] 选填，读取配置的kconf路径，默认值为reco.browseset.browseSetAccessConfig2

    `browse_set_definition_kconf_key`: [string] 选填，读取服务配置的Kkconf路径，默认值为reco.browseset.browseSetDefinitionConfig

    fetch_browse_set(biz_name = "TEST",save_to_common_attr="attr1",browse_set_config_kconf_key = "reco.browseset.browseSetAccessConfig2",browse_set_definition_kconf_key="reco.browseset.browseSetDefinitionConfig")
    """
    self._add_processor(CommonRecoFetchBrowseSetEnricher(kwargs))
    return self
  
  def write_browse_set(self, **kwargs):
    """
    CommonRecoWriteBrowseSetObserver
    ------
    写入kconf配置的BrowseSetService，将当前结果集的item_key写入配置的BrowseSet中

    参数配置
    ------
    `biz_name`: [string] 必填，读取的配置名,browse_set_config_kconf_key中对应的BIZ_NAME

    `browse_set_config_kconf_key`: [string] 选填，读取配置的kconf路径，默认值为reco.browseset.browseSetAccessConfig2

    `browse_set_definition_kconf_key`: [string] 选填，读取服务配置的Kkconf路径，默认值为reco.browseset.browseSetDefinitionConfig


    write_browse_set(biz_name = "TEST",browse_set_config_kconf_key = "reco.browseset.browseSetAccessConfig2",browse_set_definition_kconf_key="reco.browseset.browseSetDefinitionConfig")
    """
    self._add_processor(CommonRecoWriteBrowseSetObserver(kwargs))
    return self

  def calc_by_simple_formula(self, **kwargs):
    """
    CommonRecoCalcBySimpleFormulaEnricher
    ------
    简易公式计算, 仅适用于固定公式、表达式简单的计算逻辑

    表达式中 [[ ]] 代表 item attr, <span v-pre>`{{ }}`</span> 代表 common attr(注意与动态参数不同，不能填 lua 表达式)

    基于exprtk, 使用手册详见 [英文原版](https://www.partow.net/programming/exprtk/code/readme.txt) 

    参数配置
    ------
    `formulas`: [list]
      - `expr`: [string] 需要计算的表达式, 其中 [[ ]] 代表 item attr, <span v-pre>`{{ }}`</span> 代表 common attr, 仅支持 int/double 类型, attr 默认值为0.0
      - `output_attr`: [string] 输出的 attr 名称，当 expr 中有 item attr 时输出为 item attr, 否则为 common attr
      - `to_int`: [bool] 是否将输出转为 int, 默认 False, 即输出为 double 类型

    调用示例
    ------
    ``` python
    .calc_by_simple_formula(
        formulas = [
            dict(expr='[[explore_click_cnt]]+[[fountain_realshow_cnt]]+[[thanos_realshow_cnt]]+[[nebula_realshow_cnt]]',
                  output_attr='item_total_play_cnt', to_int=True),
            dict(expr='max({{pctr_alpha}} * [[pctr]] + {{pctr_bias}}, 0.0) ^ {{pctr_beta}}',
                  output_attr='pctr_score'),
            dict(expr='[[zero_based_item_seq]] + 1', output_attr='one_based_item_seq', to_int=True),
        ]
    )
    ```
    """
    formulas = kwargs.get("formulas", [])
    if not formulas or not isinstance(formulas, list):
      raise ArgumentError("calc_by_simple_formula() 的 formulas 参数需为非空 list 类型")
    
    for formula in formulas:
      expr = formula.get('expr', '')
      if not expr:
        raise ArgumentError("expr 参数不能为空")

      item_attrs = re.findall(r'\[\[(.*?)\]\]', expr)
      common_attrs = re.findall(r'\{\{(.*?)\}\}', expr)
      
      # 去掉表达式的中括号大括号
      cleaned_expr = re.sub(r'\[\[(.*?)\]\]', r'\1', expr)
      cleaned_expr = re.sub(r'\{\{(.*?)\}\}', r'\1', cleaned_expr)
      formula['expr'] = cleaned_expr
      seen = set()
      formula['common_attrs'] = [x for x in common_attrs if not (x in seen or seen.add(x))]
      seen = set()
      formula['item_attrs'] = [x for x in item_attrs if not (x in seen or seen.add(x))]
      formula['is_common'] = (len(item_attrs) == 0)

    self._add_processor(CommonRecoCalcBySimpleFormulaEnricher(kwargs))
    return self

  def calc_city_hash64(self, **kwargs):
    """
      GeneralCityHashEnricher
      ------
      将给定的 attr 用分隔符拼接成字符串之后计算 CityHash64
      ------
      `is_common`: [bool] [可选项] 输出是否为 common attr ，默认值为 True

      `overflow_protect`: [bool] [可选项] 计算结果和 9223372036854775807 (INT64_MAX) 按位与，保证结果在 INT64 范围内，默认为 True
        - c++ 计算结果为 UINT64 类型，主要在一些和 java 服务/离线流程交互的场景下（只有有符号类型）需要正整数结果时配合使用

      `output_attr_name`: [string] [必填项] 输出计算结果的 attr 名称

      `delimiter`: [string] [可选项] 拼接字符串的分隔符，默认为空串

      `input_attrs`: [list] [必填项] 计算哈希值用到的 attr 列表，每一项为 dict
        - `name`: [string] attr 名称，类型支持 int/string
        - `is_common`: [bool] 是否为 common attr ， 注意当 `is_common=True` 时输入的所有 attr 都必须是 common 维度

      调用示例
      ------
      ``` python
      .calc_city_hash64(
        is_common=False,
        overflow_protect=True,
        output_attr_name="creative_hash_key",
        delimiter="_",
        input_attrs=[
          dict(name="media", is_common=True),
          dict(name="creative_id", is_common=False)
        ],
      )
      ```
    """
    self._add_processor(GeneralCityHashEnricher(kwargs))
    return self
