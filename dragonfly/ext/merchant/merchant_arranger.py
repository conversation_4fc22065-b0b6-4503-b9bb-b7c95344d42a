#!/usr/bin/env python3
# coding=utf-8
"""
filename: merchant_arranger.py
description: common_leaf merchant arranger module
author: <EMAIL>
date: 2021-11-16 13:10:00
"""
from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafArranger

class MerchantRecoBucketTruncateArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "truncate_by_bucket_flag"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("size_limit")))
    attrs.update(self.extract_dynamic_params(self._config.get("reserved_quota")))
    for cfg in self._config.get("buckets", []):
      attrs.update(self.extract_dynamic_params(cfg.get("ratio")))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("natural_results_compete_success_flag_to", ""))
    return attrs

class MerchantRecoColdStartBucketTruncateArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "cold_start_truncate_by_bucket"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("bucket_params")))
    attrs.update(self.extract_dynamic_params(self._config.get("sort_params")))
    attrs.update(self.extract_dynamic_params(self._config.get("size_limit")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_bucket_mutual_exclusion")))
    return attrs

class MerchantRecoScoreSortArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_sort"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attr = self._config.get("score_from_attr", "")
    return { attr } if attr else set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { "_SCORE_" }

class MerchantRerankResetReasonArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_rerank_reset_reason"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attr = self._config.get("target_reason_attr", "")
    attr = self._config.get("target_aid_attr", "")
    attr = self._config.get("aId_attr", "")
    return { attr } if attr else set()

class MerchantConsistentRankingArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) ->str:
    return "merchant_consistent_ranking_ranger"

class MerchantChannelSortArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_channel_sort"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("input_count_threshold")))
    attrs.update(self.extract_dynamic_params(self._config.get("output_count")))
    attrs.update(self.extract_dynamic_params(self._config.get("channel_queue_names")))
    attrs.update(self.extract_dynamic_params(self._config.get("force_last_queue_name")))
    if 'queue_weight_attrs' in self._config.keys():
      attrs.update(self._config['queue_weight_attrs'])
    if 'sub_queue_config' in self._config.keys():
      sub_queue_configs = self._config.get("sub_queue_config")
      if isinstance(sub_queue_configs, dict):
        for value in sub_queue_configs.values():
          if isinstance(value, dict):
            if 'sub_quota' in value.keys():
              attrs.update(self.extract_dynamic_params(value.get("sub_quota")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if 'queue_score_attrs' in self._config.keys():
      attrs.update(self._config['queue_score_attrs'])
    if 'queue_flag_attrs' in self._config.keys():
      attrs.update(self._config['queue_flag_attrs'])
    if 'sub_queue_config' in self._config.keys():
      sub_queue_configs = self._config.get("sub_queue_config")
      if isinstance(sub_queue_configs, dict):
        for value in sub_queue_configs.values():
          if isinstance(value, dict):
            if 'sub_queue_score_attrs' in value.keys():
              attrs.update(value.get("sub_queue_score_attrs"))
            if 'sub_queue_flag_attrs' in value.keys():
              attrs.update(value.get("sub_queue_flag_attrs"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set(["mc_channel_origin_rank", "mc_channel_origin_name", "sub_mc_channel_origin_name", "sub_mc_channel_origin_rank"])
    return attrs

class MerchantItemBarrierArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_barrier_arranger"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("size_limit")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update([attr for attr, val in self._config.get("pass_flags", dict()).items()])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

class MerchantUnifyTruncateArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_unify_truncate"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    assert "limit" in self._config
    attrs.update(self.extract_dynamic_params(self._config.get("limit")))
    for channel in self._config.get("channels", list()):
      assert "type" in channel
      if channel.get("type") == "condition":
        assert "compare_to" in channel
        attrs.update(self.extract_dynamic_params(channel.get("compare_to")))
      if "enable" in channel:
        attrs.update(self.extract_dynamic_params(channel.get("enable")))
      if "reason" in channel:
        attrs.update(self.extract_dynamic_params(channel.get("reason")))
      if "replice_cmp_version" in channel:
        attrs.update(self.extract_dynamic_params(channel.get("replice_cmp_version")))
    for channel in self._config.get("pass_channels", list()):
      assert "compare_to" in channel
      if "compare_to" in channel:
        attrs.update(self.extract_dynamic_params(channel.get("compare_to")))
      if "enable" in channel:
        attrs.update(self.extract_dynamic_params(channel.get("enable")))
      if "reason" in channel:
        attrs.update(self.extract_dynamic_params(channel.get("reason")))
      if "replice_cmp_version" in channel:
        attrs.update(self.extract_dynamic_params(channel.get("replice_cmp_version")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    assert "primary_score" in self._config
    attrs.add(self._config.get("primary_score"))
    for channel in self._config.get("channels", list()):
      assert "type" in channel
      assert "name" in channel
      assert "attr_name" in channel
      attrs.add(channel.get("attr_name"))
      if channel.get("type") == "condition":
        assert "select_if" in channel
        assert "compare_to" in channel
      if "target_item" in channel:
        attrs.add(channel.get("target_item"))
      if "replice_target_item" in channel:
        attrs.add(channel.get("replice_target_item"))
      if "replice_cmp_attr" in channel:
        attrs.add(channel.get("replice_cmp_attr"))
    for channel in self._config.get("pass_channels", list()):
      channel["type"] = "pass_channels"
      assert "name" in channel
      assert "attr_name" in channel
      assert "insert_to" in channel
      assert "limit" in channel
      attrs.add(channel.get("attr_name"))
      if channel.get("type") == "condition":
        assert "select_if" in channel
        assert "compare_to" in channel
        attrs.add(channel.get("attr_name"))
      if "target_item" in channel:
        attrs.add(channel.get("target_item"))
      if "replice_target_item" in channel:
        attrs.add(channel.get("replice_target_item"))
      if "replice_cmp_attr" in channel:
        attrs.add(channel.get("replice_cmp_attr"))
      if "sort_score" in channel:
        attrs.add(channel.get("sort_score"))

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for channel in self._config.get("channels", list()):
      for set_attr_value in channel.get("set_attr_value", list()):
        assert "attr_name" in set_attr_value
        assert "op" in set_attr_value
        assert "value" in set_attr_value
        assert "default_val" in set_attr_value
        attrs.add(set_attr_value.get("attr_name"))
    for channel in self._config.get("pass_channels", list()):
      for set_attr_value in channel.get("set_attr_value", list()):
        assert "attr_name" in set_attr_value
        assert "op" in set_attr_value
        assert "value" in set_attr_value
        assert "default_val" in set_attr_value
        attrs.add(set_attr_value.get("attr_name"))
    return attrs

class MerchantReasonMapArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_reason_map"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    return ret

class MerchantFilterWithEarlystopArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "filter_with_earlystop"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("count")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

class MerchantMergeWithPriorityArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merge_tunnel_with_priority"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("count")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_str_attr_name", "item_id_str"))
    item_author_id_attr_name = self._config.get("item_author_id_attr_name", "")
    if item_author_id_attr_name:
      attrs.add(item_author_id_attr_name)
    multi_sources = self._config.get("item_multi_sources_attr_name", "")
    if multi_sources:
      attrs.add(self._config.get("item_source_attr_name", "item_source"))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("item_multi_sources_attr_name", "")
    if attr:
      attrs.add(attr)
    return attrs

class MerchantUniRecallRetrieveDiff(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_uni_recall_retrieve_diff"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

class MerchantFullRecallConsumePartitionArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_full_recall_consume_partition"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    return ret
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class MerchantResetReasonArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_reset_reason_arranger"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["default_reason"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["reason_attr"]:
      attrs.add(self._config.get(key, ""))
    return attrs

