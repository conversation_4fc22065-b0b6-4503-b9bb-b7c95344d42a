#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg, gen_attr_name_with_common_attr_channel, extract_attr_names
from ...common_leaf_processor import LeafEnricher
import itertools
class LiveStreamCommonCalcScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_common_calc_score"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_userinfo_calc_decay")))
    attrs.update(self.extract_dynamic_params(self._config.get("liveroom_action_list_truncate_day_gap")))
    attrs.update(self.extract_dynamic_params(self._config.get("thanos_live_trigger_weights")))

    attrs.update(self.extract_dynamic_params(self._config.get("feed_livestream_realshow_live_size_min_cnt")))
    attrs.update(self.extract_dynamic_params(self._config.get("feed_livestream_preview_playing_time_threshold")))
    attrs.update(self.extract_dynamic_params(self._config.get("author_time_bound_hour")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_slide_live_room_browse_use_realshow")))
    attrs.update(self.extract_dynamic_params(self._config.get("merchant_page_bound")))
    attrs.update(self.extract_dynamic_params(self._config.get("merchant_count_bound")))
    attrs.update(self.extract_dynamic_params(self._config.get("live_profile_effective_watch_time_thres")))
    attrs.update(self.extract_dynamic_params(self._config.get("live_profile_short_watch_time_thres")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_hot_live_hate_set_filtration")))
    attrs.update(self.extract_dynamic_params(self._config.get("user_hated_author_list_attr")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add("user_is_live_hate_user")
    attrs.add("enable_merchant_live")
    attrs.add("user_low_dwell_aid_list")
    attrs.add("user_live_browse_aid_list")
    attrs.add("enable_live_follow")
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("livestream_click_score")
    attrs.add("livestream_like_score")
    attrs.add("livestream_forward_score")
    attrs.add("livestream_click_live_score")
    attrs.add("livestream_profile_enter_score")
    attrs.add("livestream_comment_score")
    attrs.add("livestream_follow_score")
    attrs.add("livestream_unfollow_score")
    attrs.add(self._config.get("livestream_score_save_to"))
    attrs.add("livestream_long_watch_hit")
    attrs.add("livestream_interest_user_type")

    attrs.add("live_profile_click_hit")
    attrs.add("live_profile_like_hit")
    attrs.add("live_profile_follow_hit")
    attrs.add("live_profile_forward_hit")
    attrs.add("live_profile_gift_hit")
    attrs.add("live_profile_comment_hit")
    attrs.add("live_profile_avg_view_time")
    attrs.add("live_profile_view_hit")
    attrs.add("live_profile_effeview_hit")
    attrs.add("live_profile_shortview_hit")
    attrs.add("punish_effect_filter_tag")
    attrs.add("is_short_play")
    attrs.add("live_live_follow_author_gap_hit_feature")
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("livestream_score_save_to"), str) and self._config["livestream_score_save_to"], \
              "需要使用 livestream_score_save_to 指定存储 score 的 item_attr")
    check_arg(isinstance(self._config.get("user_info_attr"), str) and self._config["user_info_attr"], \
              "需要使用 user_info_attr 指定存储序列化的 user_info 的 common_attr 名称")
    check_arg((isinstance(self._config.get("enable_userinfo_calc_decay"), bool) or isinstance(self._config.get("enable_userinfo_calc_decay"), str)) \
              and self._config["enable_userinfo_calc_decay"], \
              "需要使用 enable_userinfo_calc_decay 指定是否按时间对用户行为权重做衰减处理")
    check_arg((isinstance(self._config.get("liveroom_action_list_truncate_day_gap"), int) and self._config["liveroom_action_list_truncate_day_gap"] > 0) \
              or isinstance(self._config.get("liveroom_action_list_truncate_day_gap"), str), \
              "liveroom_action_list_truncate_day_gap 需为大于 0 的整数, 如果不需截断可将该值设定为 365")
    check_arg(isinstance(self._config.get("thanos_live_trigger_weights"), str) and self._config["thanos_live_trigger_weights"], \
              "需要使用 thanos_live_trigger_weights 指定用户行为和各路召回占得分的权重，或者使用 {{common_attr}} 指定从哪个common_attr获取该权重")

class LiveStreamRetrievalOffFiltersEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_off_filters_enrich"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for rule in self._config.get("reason_off_filter_map"):
      attrs.update(self.extract_dynamic_params(rule.get("off_filters")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("reason_list")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("off_filter_list_name"))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("reason_off_filter_map"), list), \
              "reason_off_filter_map 需要为 list")
    for rule in self._config.get("reason_off_filter_map"):
      check_arg(isinstance(rule, dict), "reason_off_filter_map 的各项需要为 dict")
      check_arg(("reason" in rule) and isinstance(rule.get("reason"), int), "必须配置 int 类型的 reason")
      check_arg(("off_filters" in rule) and isinstance(rule.get("off_filters"), str), "必须配置 str 类型的 off_filters")

class LiveStreamRedisLrangeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_redis_lrange_enrich"
  
  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("cluster_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("use_two_key")))
    attrs.update(self.extract_dynamic_params(self._config.get("time_stamps")))
    attrs.update(self.extract_dynamic_params(self._config.get("first_value_limit")))
    attrs.update(self.extract_dynamic_params(self._config.get("second_value_limit")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_get_common_attr")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("from_author_id"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_item_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_attr_name"))

    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("cluster_name"), str) and self._config["cluster_name"], "cluster_name 需为非空字符串")

class LiveStreamRedisRangePKHistoryEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_redis_range_pk_history_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("main_author_id")))
    attrs.update(self.extract_dynamic_params(self._config.get("pk_history_last_n_days")))
    attrs.update(self.extract_dynamic_params(self._config.get("pk_history_limit")))
    attrs.update(self.extract_dynamic_params(self._config.get("cluster_name")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_pk_author_list"))
    attrs.add(self._config.get("output_pk_duration_list"))

    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config["main_author_id"], "main_author_id 为是必选项")

class LiveStreamRetrievalFilterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_enrich_retrieval_filter_flag"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("on_filters")))
    attrs.update(self.extract_dynamic_params(self._config.get("off_filters")))

    attrs.add("common_filter_list")
    attrs.add("acu_7d_filter_thred")
    # attrs.add("online_count_range_str")
    # attrs.add("online_count_thred_str")
    attrs.add("online_count_range_list")
    attrs.add("online_count_thred_list")
    attrs.add("kconf_live_black_author_list")
    attrs.add("punish_live_scene_id")
    attrs.add("punish_live_enable_scene_effect")
    attrs.add("kconf_live_explore_province_set")
    attrs.add("kconf_live_gr_level")
    attrs.add("white_list_author_version")
    attrs.add("pk_user_online_cnt")
    attrs.update(self.extract_dynamic_params(self._config.get("unified_post_filter_mode")))

    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("off_filter_list_name"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("filter_flag_save_to"))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    pass
 

class LiveStreamCommonKVUserEmbeddingEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_common_kv_user_embedding"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_id_attr"))
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.update(self.extract_dynamic_params(self._config["timeout_ms"]))
    attrs.update(self.extract_dynamic_params(self._config["bucket"]))

    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_tensor_attr"))

    return attrs


class LiveStreamAuthorGnnEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_author_gnn_enrich"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))

    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_tensor_attr"))

    return attrs

class SlideLiveStreamPreProcessEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_live_pre_process_enrich"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

class LiveStreamUserLiveExitTimeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_user_live_exit_time_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("user_live_last_exit_gap_ts")
    return attrs

class RetrCenterControlEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retr_center_control_enrich"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_hash_attr"))
    attrs.add(self._config.get("circuit_ratio_list_name"))
    attrs.add(self._config.get("limit_list_name"))
    attrs.add(self._config.get("retrieval_list_name"))
    attrs.add("req_type")
    if ("module_replace_flow_attr_name" in self._config.keys()):
      attrs.add(self._config.get("module_replace_flow_attr_name"))
    if ("module_replace_flow_list_attr_name" in self._config.keys()):
      attrs.add(self._config.get("module_replace_flow_list_attr_name"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

class RetrCenterParamsEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retr_center_params_enrich"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add("total_retr_timeout_ms")
    attrs.add("lr_model_timeout_ms")
    attrs.add("ann_emb_timeout_ms")
    attrs.add("retr_redis_limit")
    attrs.add("default_retr_reason_limits")
    attrs.add("items_limit_list")
    attrs.add("items_limit_list")
    attrs.update(self.extract_dynamic_params(self._config.get("disable_game_search_info_from_redis")))
    attrs.update(self.extract_dynamic_params(self._config.get("a2a_mmu_actions_days_gap")))
    attrs.update(self.extract_dynamic_params(self._config.get("a2a_mmu_embedding_play_time_thres")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_game_interest_retr")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_game_casual_and_qipai_retr")))
    attrs.add(self._config.get("user_info_name", "user_info_str"))
    attrs.add("user_info_attr")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set(["user_province"])
    attrs.add(self._config.get("user_follow_list_name"))
    attrs.add(self._config.get("game_user_gender"))
    attrs.add(self._config.get("game_user_age"))
    attrs.add(self._config.get("game_user_city"))
    attrs.add(self._config.get("game_user_province"))
    attrs.add(self._config.get("game_user_real_city"))
    attrs.add(self._config.get("game_revenue_user_type"))
    attrs.add(self._config.get("is_live_revenue_user"))
    attrs.add(self._config.get("game_user_client_id"))
    attrs.add(self._config.get("game_user_mod_price"))
    attrs.add(self._config.get("output_photo_category_list_attr"))
    attrs.add(self._config.get("output_photo_category_list_v2_attr"))
    attrs.add(self._config.get("output_photo_category_list_v3_attr"))
    attrs.add(self._config.get("output_game_mmu_cluster_attr"))
    attrs.add(self._config.get("output_game_interest_attr"))
    attrs.add(self._config.get("output_new_game_qipai_attr"))
    attrs.add(self._config.get("output_new_game_casual_tags_attr"))
    attrs.add(self._config.get("output_new_game_casual_aids_attr"))
    # attrs.add("recruit_realtime_action_type")
    # attrs.add("recruit_job_category_list")
    # attrs.add("recruit_job_province_list")
    # attrs.add("recruit_realtime_negative_count")
    # attrs.add("recruit_tag_expose_count")
    attrs.add("game_user_gender")
    attrs.add("game_user_age")
    attrs.add("game_user_city")
    attrs.add("game_user_province")
    attrs.add("game_user_real_city")
    attrs.add("game_revenue_user_type")
    attrs.add("is_live_revenue_user")
    attrs.add("game_user_client_id")
    attrs.add("game_user_mod_price")
    attrs.add("output_photo_category_list_attr")
    attrs.add("output_photo_category_list_v2_attr")
    attrs.add("output_photo_category_list_v3_attr")
    attrs.add("output_game_mmu_cluster_attr")
    attrs.add("output_game_interest_attr")
    attrs.add("output_new_game_qipai_attr")
    attrs.add("output_new_game_casual_tags_attr")
    attrs.add("output_new_game_casual_aids_attr")
    attrs.add("user_all_app_list")
    attrs.add("lat_attr")
    attrs.add("lon_attr")
    attrs.add("user_province")
    # attrs.add("user_city_level")
    attrs.add(self._config.get("user_relation_list_name", "user_relation_list"))
    attrs.add(self._config.get("user_follow_list_name", "user_follow_list"))
    attrs.add(self._config.get("play_list_name", "play_action_list"))
    attrs.add(self._config.get("comment_list_name", "comment_action_list"))
    attrs.add(self._config.get("follow_like_list_name", "follow_like_action_list"))
    attrs.add(self._config.get("gift_list_name", "gift_action_list"))
    # attrs.add(self._config.get("hate_list_name", "hate_action_list"))
    attrs.add(self._config.get("prefer_tag_list_name", "user_prefer_live_tag_list"))
    attrs.add(self._config.get("user_relation_list_v2_name", "user_relation_list_v2"))
    attrs.add(self._config.get("search_list_name", "search_action_list"))
    attrs.add("lr_output_game_mmu_cluster_attr")
    attrs.add("lr_output_game_interest_attr")
    attrs.add("output_game_casual_aids_attr")
    attrs.add("live_common_a2a_follow_trigger")
    return attrs

class RecruitRealtimeParamsEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "recruit_realtime_params_enrich"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add("user_info_attr")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add("recruit_realtime_action_type")
    attrs.add("recruit_job_category_list")
    attrs.add("recruit_job_province_list")
    attrs.add("recruit_realtime_negative_count")
    attrs.add("recruit_tag_expose_count")
    attrs.add("offline_recruit_negative_count")
    attrs.add("recruit_interact_live_aid_list")
    attrs.add("recruit_interact_live_province_list")
    attrs.add("recruit_interact_live_job_id_list")
    attrs.add("recruit_interact_live_job_category_list")
    attrs.add("recruit_interact_live_job_subcategory_list")
    attrs.add("recruit_interact_live_job_province_list")
    attrs.add("recruit_interact_live_job_city_list")
    attrs.add("recruit_interact_photo_aid_list")
    attrs.add("recruit_interact_photo_province_list")
    attrs.add("recruit_interact_photo_job_id_list")
    attrs.add("recruit_interact_photo_job_category_list")
    attrs.add("recruit_interact_photo_job_subcategory_list")
    attrs.add("recruit_interact_photo_job_province_list")
    attrs.add("recruit_interact_photo_job_city_list")
    return attrs

class RetrClientControlEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retr_client_control_enrich"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "input_switch_attrs" in self._config:
      attrs.update(self._config.get("input_switch_attrs"))
    if "reason_list" in self._config:
      attrs.update(self.extract_dynamic_params(self._config["reason_list"]))
    if "filter_list" in self._config:
      attrs.update(self.extract_dynamic_params(self._config["filter_list"]))
    attrs.add(self._config.get("retrieval_list_map"))
    attrs.add(self._config.get("retrieval_reason_list"))
    attrs.add(self._config.get("filter_list_map"))
    attrs.add(self._config.get("profix_convert_map"))
    attrs.add(self._config.get("req_type_name", "req_type"))
    attrs.add(self._config.get("user_type_name", "u_user_type"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("retrieval_list_name", "common_retrieval_list"))
    attrs.add(self._config.get("filter_list_name", "common_filter_list"))
    # attrs.add(self._config.get("retr_switch_name", "retr_center_switch"))
    return attrs

class LiveRecommendCommonLeafParamsEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "liverecommend_retr_params_enrich"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("request_type_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(["user_province"])

    return attrs

class LiveStreamMcPredictXtrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_mc_predict_xtr_enrich"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("pxtr_label"))
    attrs.add(self._config.get("pxtr_value"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()

    attrs.add("mc_pctr")
    attrs.add("mc_pltr")
    attrs.add("mc_pwtr")
    attrs.add("mc_plvtr")
    attrs.add("mc_plvtr2")
    attrs.add("mc_psvtr")
    attrs.add("mc_pgtr")
    attrs.add("mc_pwatchtime")
    attrs.add("mc_pinwatchtime")
    attrs.add("mc_gift_value_1")
    attrs.add("mc_gift_value_2")
    attrs.add("mc_pievr")
    attrs.add("mc_pnctr")
    attrs.add("mc_preptr")

    return attrs

class LiveStreamMcBypassPredictXtrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_mc_bypass_predict_xtr_enrich"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("pxtr_label"))
    attrs.add(self._config.get("pxtr_value"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()

    attrs.add("bypass_mc_pctr")
    attrs.add("bypass_mc_pltr")
    attrs.add("bypass_mc_pwtr")
    attrs.add("bypass_mc_plvtr")
    attrs.add("bypass_mc_plvtr2")
    attrs.add("bypass_mc_psvtr")
    attrs.add("bypass_mc_pgtr")
    attrs.add("bypass_mc_pwatchtime")
    attrs.add("bypass_mc_pinwatchtime")
    attrs.add("bypass_mc_gift_value_1")
    attrs.add("bypass_mc_gift_value_2")
    attrs.add("bypass_mc_pievr")
    attrs.add("bypass_mc_pnctr")

    return attrs

class LiveStreamGetRandomIndexEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_get_random_index_enrich"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("candidate"))
    attrs.update(self.extract_dynamic_params(self._config.get("sample_num")))
    attrs.add(self._config.get("left_percent"))
    attrs.add(self._config.get("right_percent"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_common_attr"))
    return attrs

class LiveStreamLiveRecommendInitContextEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_live_recommend_init_context_enrich"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("from_user_info"))
    attrs.add("live_low_active_day_thred")
    attrs.add("live_high_active_day_thred")
    attrs.add("live_low_active_effect_play_thred")
    attrs.add("stage_gift_user_type_stra")
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("item_attrs", []))

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add("is_low_active_user")
    attrs.add("is_high_active_user")
    attrs.add("is_pref_new_author")
    attrs.add("is_gift_user")
    attrs.add("is_high_gift_user")
    attrs.add("user_info_follow_author_b_count")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("is_follow_author")
    attrs.add("aLiveTagLv0")
    attrs.add("aLiveTagLv1")
    attrs.add("aLiveTagLv2")
    attrs.add("aLiveTagLv3")
    attrs.add("live_author_full_tag_path_list")
    attrs.add("channel_type")
    return attrs

class LiveStreamBeforeTideEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "before_tide"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["default_value", "group_name", "save_name"]:
      if key in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return { self._config["save_name"] }

class LiveStreamAfterTideEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "after_tide"

  @strict_types
  def depend_on_items(self) -> bool:
    return False
    
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("from_common_attr"))
    for key in ["max_value", "min_value", "default_value", "pid_kp", "pid_ki", "pid_kd", "group_name", "step", "window_size", "target", "from_common_attr"]:
      if key in self._config:
        attrs.update(self.extract_dynamic_params(self._config[key]))
    return attrs

class LiveStreamLiveRecommendFrPredictEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_fr_predict_enrich"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.add(self._config.get("user_info_attr"))
    attrs.add(self._config.get("kuiba_user_attrs"))
    attrs.add(self._config.get("sample_list_common_attr_key"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("cross_attrs", []), "name")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return extract_attr_names(self._config.get("receive_item_attrs", []), "as")

class LiveStreamExploreFrRankEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_explore_fr_rank_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("fr_ensemble_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("ensemble_formula_version")))
    attrs.update(self.extract_dynamic_params(self._config.get("is_low_active_user")))
    attrs.update(self.extract_dynamic_params(self._config.get("is_gift_user")))
    attrs.update(self.extract_dynamic_params(self._config.get("is_pref_new_author")))
    attrs.update(self.extract_dynamic_params(self._config.get("author_protect_pref_new_author_extra_boost_coeff")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_online_count_boost")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_cover_merchant_punish")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_author_category_boost")))
    attrs.update(self.extract_dynamic_params(self._config.get("author_category_boost_coeff")))
    attrs.update(self.extract_dynamic_params(self._config.get("author_category_boost_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_author_category_punish")))
    attrs.update(self.extract_dynamic_params(self._config.get("author_category_punish_coeff")))
    attrs.update(self.extract_dynamic_params(self._config.get("author_category_punish_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_gift_author_category_boost")))
    attrs.update(self.extract_dynamic_params(self._config.get("gift_author_category_boost_coeff")))
    attrs.update(self.extract_dynamic_params(self._config.get("gift_author_category_boost_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_nearby_retr_boost")))
    attrs.update(self.extract_dynamic_params(self._config.get("nearby_retr_boost_coeff")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_high_gift_author_boost")))
    attrs.update(self.extract_dynamic_params(self._config.get("high_gift_author_boost_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_gift_low_follow_bplus_boost")))
    attrs.update(self.extract_dynamic_params(self._config.get("user_info_follow_author_b_count")))
    attrs.update(self.extract_dynamic_params(self._config.get("low_follow_author_b_boost_coeff")))
    attrs.update(self.extract_dynamic_params(self._config.get("fr_engage_score_type")))
    attrs.update(self.extract_dynamic_params(self._config.get("fr_engage_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("fr_engage_cost_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_fr_ensemble")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_fr_engagement")))
    attrs.update(self.extract_dynamic_params(self._config.get("stage_gift_user_egm_diff_on")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_mmu_glad_author_fullrank_boost")))
    attrs.update(self.extract_dynamic_params(self._config.get("mmu_glad_author_fullrank_boost_coeff")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_same_city_recruit_live_boost")))
    attrs.update(self.extract_dynamic_params(self._config.get("same_city_recruit_live_boost_coeff")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_channel_recruit_live_boost")))
    attrs.update(self.extract_dynamic_params(self._config.get("same_city_recruit_level_boost_coeff")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(["fr_pctr", "fr_pwtr", "fr_pltr", "fr_psvtr", "fr_plvtr", "fr_pgtr",
                  "fr_pwatchtime", "fr_pbenefit", "fr_gvr", "fr_ptr",
                  "count.live_recommend_click", "ip_region"])
    attrs.add(self._config.get("reason_list_attr"))

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("fr_ensemble_score")
    attrs.add("fr_score")

    return attrs

class LiveStreamNegSimilarityEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_neg_similarity_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("live_dislike_aid_list_attr"))
    attrs.add(self._config.get("live_report_aid_list_attr"))
    attrs.add(self._config.get("black_author_list_attr"))
    attrs.add(self._config.get("dislike_embedding_attr"))
    attrs.add(self._config.get("report_embedding_attr"))
    attrs.add(self._config.get("black_embedding_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_embedding_attr"))

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_attr"))

    return attrs

class LiveStreamCalcRetrPrecisionRecallEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_calc_retr_precision_recall"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("candidate_set_name"))
    attrs.add(self._config.get("gt_set_name"))
    
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_precision_name"))
    attrs.add(self._config.get("export_recall_name"))

    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("author_id")

    return attrs

class LiveStreamGroupByItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_group_by_item_attr"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return { v["item_attr_name"] for v in self._config["group_by_rules"] }

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

class LiveStreamGradeDistributeFilterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "grade_distribute_filter_enrich"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    attrs.add("grade_distribute_rule_version")
    attrs.add("grade_distribute_item_label_version")
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("grade_label")
    return attrs;

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("filter_flag_save_to"))
    return attrs

class LiveStreamXtrCalibrationEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "xtr_calibration_enrich"
  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {xtr for xtr in self._config.get("xtrs")}
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {'calibrated_{}'.format(xtr) for xtr in self._config.get("xtrs")}

class LiveStreamNegativeFeedbackEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_negative_feedback_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("time_range_sec")))
    attrs.update(self.extract_dynamic_params(self._config.get("filter_thred")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_mmu_neg_name"))
    return attrs

class LiveStreamSampleEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_sample_gen"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("leaf_show_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("build_extra_info_num")))
    attrs.add(self._config.get("feature_attrs_from_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_pos_samp_list_to_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("basic_attrs", []), "name")
    attrs.update(extract_attr_names(self._config.get("predict_attrs", []), "name"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(gen_attr_name_with_common_attr_channel("extra_sample_attrs", self._config.get("save_pos_samp_list_to_attr")))
    return attrs

class LiveStreamGradeIndexTermEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "grade_index_term_enrich"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("label_list_str")))
    attrs.update(self.extract_dynamic_params(self._config.get("rule_version")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_term_to_attr"))
    return attrs

class LiveStreamFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_feature_gen"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    attrs.add(self._config.get("redis_show_attr"))
    attrs.add(self._config.get("redis_click_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set(["author_id"])
    return attrs

  @property
  @strict_types
  def output_output_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_feature_names_to_attr"))
    return attrs

class LiveRecommendUserAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "liverecommend_user_attr"
  @strict_types
  def depend_on_items(self) -> bool:
    return False
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(["mc_mode_str", "fr_mode_str"])
    attrs.add(self._config.get("user_info_attr"))
    attrs.add(self._config.get("sample_attr_name_list_attr"))
    return attrs

class LiveStreamChannelSortQueueParamEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_stream_channel_sort_queue_param_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("channel_sort_queue_param")))
    if 'queue_weight_attrs' in self._config.keys():
      attrs.update(self._config['queue_weight_attrs'])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add("channel_sort_queue_names")
    if 'queue_weight_attrs' in self._config.keys():
      attrs.update(self._config['queue_weight_attrs'])
    return attrs

class LiveStreamKvParamEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_stream_kv_param_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("origin_param")))
    if "param_separator" in self._config.keys():
      attrs.add("param_separator")
    if "kv_separator" in self._config.keys():
      attrs.add("kv_separator")
    attrs.add("param_attr_prefix")
    if 'import_common_attr' in self._config.keys():
      attrs.update(self._config['import_common_attr'])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("param_name_list_attr"))
    if 'export_common_attr' in self._config.keys():
      attrs.update(self._config['export_common_attr'])
    return attrs

class LiveStreamRedisRecordAndGetAvgEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "redis_record_and_get_avg"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for string_config in self._config.get("params", []):
      attrs.update(self.extract_dynamic_params(string_config.get("extra_info", "")))
    if 'enable_random_get' in self._config.keys():
      attrs.update(self.extract_dynamic_params(self._config.get('enable_random_get')))
    if 'random_get_key_copies' in self._config.keys():
      attrs.update(self.extract_dynamic_params(self._config.get('random_get_key_copies')))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for string_config in self._config.get("params", []):
      attrs.add(string_config["output_attr"])
    return attrs

class RetrCenterFilterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retr_center_filter_enrich"

class LiveStreamRevenueModelScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_revenue_model_score_enrich"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    use_item_id_in_attr = self._config.get("use_item_id_in_attr")
    if use_item_id_in_attr:
      attrs.add(use_item_id_in_attr)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr_name"])
    return attrs 

class LiveStreamTransformScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_transform_score"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["transform_param"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config['input_attr'])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    return attrs

class LiveStreamEnsembleScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_calc_ensemble_score"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["channels", "formula_version", "aggregator"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get('xtrs', []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    return attrs

class LiveStreamMcEnsembleScoreEnricher(LiveStreamEnsembleScoreEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_mc_ensemble_score"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = super().input_common_attrs
    for key in ["regulator", "smooth"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

class LiveStreamFollowEnsembleScoreEnricher(LiveStreamEnsembleScoreEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_follow_ensemble_score"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = super().input_common_attrs
    for key in ["regulator", "smooth", "protect"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

class LiveStreamFollowRewardEnsembleScoreEnricher(LiveStreamEnsembleScoreEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_follow_reward_ensemble_score"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = super().input_common_attrs
    for key in ["regulator", "smooth", "protect"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("livestream_follow_score")
    return attrs

class LiveStreamFollowMerchantEnsembleScoreEnricher(LiveStreamEnsembleScoreEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_follow_merchant_ensemble_score"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = super().input_common_attrs
    for key in ["regulator", "smooth", "protect"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("livestream_follow_score")
    return attrs

class LiveStreamNormScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_norm_score"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["top_ratio", "norm_type"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["input_attr"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    return attrs

class LiveStreamEngagementScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_calc_engagement_score"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("engage_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("engage_cost_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("engage_score_type")))
    attrs.update(self.extract_dynamic_params(self._config.get("is_gift_user")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(["mc_pctr", "mc_plvtr", "mc_plvtr2", "mc_psvtr", "mc_pltr", "mc_pwtr", "mc_pgtr",
      "mc_pwatchtime", "mc_pinwatchtime", "mc_gift_value_1", "mc_gift_value_2", "mc_pievr",
      "mc_pnctr", "mc_pevtr", "mc_pctr_enter", "mc_pwt1", "mc_preal_wt1", "mc_pweight", "mc_pgpr",
      "author_info.category_type"])
    if self._config.get("engage_score_type", 6) == 8:
      attrs.add(self._config.get("external_score_attr", "mc_score"))

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_attr"))
    return attrs

class LiveStreamRevenueEnsembleScoreEnricher(LiveStreamEnsembleScoreEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_revenue_ensemble_score"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = super().input_common_attrs
    for key in ["regulator", "smooth", "protect", "trans_alpha", "trans_min", "trans_max", "condition_xtrs"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

class LiveStreamTimeEnsembleScoreEnricher(LiveStreamEnsembleScoreEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_time_ensemble_score"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = super().input_common_attrs
    for key in ["regulator", "smooth", "protect", "formula_version", "aggregator", "formula_params"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

class LiveStreamRecruitRegressionScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_recruit_regression_score"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("model_weight")))
    if "user_recruit_job_province" in self._config:
      attrs.add(self._config.get("user_recruit_job_province"))
    if "user_recruit_job_category" in self._config:
      attrs.add(self._config.get("user_recruit_job_category"))
    if "model_type" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("model_type")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("author_id")
    attrs.add("ip_region")
    attrs.add("fans_count")
    attrs.add("author_info.acu_7d")
    attrs.add("live_recruit_job_category")
    attrs.add("live_recruit_job_salary")
    attrs.add("live_recruit_job_address")
    attrs.add("lLiveRecruitExplainJobCategoryString")
    attrs.add("lLiveRecruitExplainJobSalaryString")
    attrs.add("lLiveRecruitExplainJobAddressString")
    attrs.add("lLiveRecruitExplainJobProvinceString")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    return attrs

class LiveStreamRecruitRedisScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_recruit_redis_score"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("redis_key_type")))
    attrs.update(self.extract_dynamic_params(self._config.get("user_age")))
    attrs.update(self.extract_dynamic_params(self._config.get("user_gender")))
    attrs.update(self.extract_dynamic_params(self._config.get("user_province")))
    attrs.update(self.extract_dynamic_params(self._config.get("feedback_weight_list")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("author_id")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    return attrs

class LiveStreamRecruitFMScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_recruit_fm_score"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    if "user_recruit_job_province" in self._config:
      attrs.add(self._config.get("user_recruit_job_province"))
    if "user_recruit_job_category" in self._config:
      attrs.add(self._config.get("user_recruit_job_category"))
    if "model_type" in self._config:
      attrs.update(self.extract_dynamic_params(self._config["model_type"]))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("ip_region")
    attrs.add("fans_count")
    attrs.add("author_info.acu_7d")
    attrs.add("live_recruit_job_category")
    attrs.add("live_recruit_job_salary")
    attrs.add("live_recruit_job_address")
    attrs.add("lLiveRecruitExplainJobCategoryString")
    attrs.add("lLiveRecruitExplainJobSalaryString")
    attrs.add("lLiveRecruitExplainJobAddressString")
    attrs.add("lLiveRecruitExplainJobProvinceString")
    attrs.add("author_id")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    return attrs

class LiveStreamSampleListCommonAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_common_attr_by_sample_list"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("sample_list_ptr_attr", "")
    if attr:
      attrs.add(attr)
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("save_attr_names_to_attr", "")
    if attr:
      attrs.add(attr)
    return attrs

class LiveStreamFractileScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_calc_fractile_score"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["fractile"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config['input_attr'])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    return attrs

class LiveStreamRedisShowClickEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_redis_show_click"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["user_id"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["redis_show_attr"])
    attrs.add(self._config["redis_click_attr"])
    return attrs

class LiveStreamUserAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_enrich_user_attr"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["redis_show_attr"])
    attrs.add(self._config["redis_click_attr"])
    for key in ["show_count_threshold", "show_time_threshold"]:
      if key in self._config.keys():
        attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["is_hate_live_attr"])
    return attrs

class LiveStreamUserInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_extract_from_user_info"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["user_info_attr"])
    for key in ["merchant_browse_bound"]:
      if key in self._config.keys():
        attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for key in ["merchant_browse_count_attr"]:
      if key in self._config.keys():
        attrs.add(self._config["merchant_browse_count_attr"])
    return attrs

class LiveStreamRetrievalNegativeSystemFilter(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_retrival_follow_negative_system_filter"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["valid_tag_id", "enable_filter_flag"]:
      if key in self._config.keys():
        attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add('punish_sirius')
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("punish_effect_filter_tag")
    return attrs

class LiveStreamItemAttrMappingEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_enrich_item_attr_by_mapping"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["map_keys_list", "map_values_list"]:
      if key in self._config.keys():
        attrs.update(self._config.get(key))
        attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get('output_item_attr'))
    return attrs

class LiveStreamEmbeddingSimilarityEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_embedding_similarity_enricher"
  
  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("seed_embedding_attr"))
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_embedding_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("similarity_attr"))
    attrs.add(self._config.get("norm_similarity_attr"))
    return attrs

class LiveStreamCountByItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_count_by_item_attr_enricher"
  
  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("group_output_attr"))
    attrs.add(self._config.get("remind_output_attr"))
    return attrs

class LiveStreamFollowEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_enrich_follow"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("author_id_attr"))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("follow_attr"))
    attrs.add(self._config.get("unfollow_attr"))
    return attrs

class LiveStreamLtvByColossusEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_ltv_by_colossus"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["author_id_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        "live_id_list", "timestamp_list", "play_time_list",
        "auto_play_time_list", "label_list", "reward_list", "reward_count_list"
    }

class LiveStreamKconfAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_common_attr_by_kconf"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("kconf_key"), "`kconf_key` 是必选项")

  @strict_types
  def depend_on_items(self) -> bool:
    return not self._is_common_attr
  
  @property
  @strict_types
  def _is_common_attr(self) -> bool:
    return True

class LivingPhotoUAPidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_revenue_ua_living_photo_enricher"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("living_photo_aid_attr"), "`living_photo_aid_attr` 是必选项")
    check_arg(self._config.get("pid_list_attr"), "`pid_list_attr` 是必选项")
    check_arg(self._config.get("living_photo_reason_attr"), "`living_photo_reason_attr` 是必选项")
    check_arg(self._config.get("revenue_ua_reason_attr"), "`revenue_ua_reason_attr` 是必选项")

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("living_photo_aid_attr"))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("pid_list_attr"))
    return attrs

class UALivePhotoAidFreqEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_revenue_live_photo_aid_browse_enricher"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("freq_control_threshold"), "`freq_control_threshold` 是必选项")
    check_arg(self._config.get("freq_control_attr"), "`freq_control_attr` 是必选项")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add("browse_author_ts_list")
    attrs.add("browse_author_aid_list")
    attrs.update(self.extract_dynamic_params(self._config.get("freq_control_threshold")))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("freq_control_attr"))
    return attrs

class LiveStreamRetrievalDistributionCalcEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_retrieval_distribution_calc"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["top_k"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_retrieval_list_name"))
    attrs.add(self._config.get("export_count_list_name"))

    return attrs

class LiveStreamElementWiseDivisionEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_elementwise_division"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("den_tag_list_name"))
    attrs.add(self._config.get("den_element_list_name"))
    attrs.add(self._config.get("num_tag_list_name"))
    attrs.add(self._config.get("num_element_list_name"))
    
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_quotient_list_name"))
    attrs.add(self._config.get("export_perf_quotient_list_name"))

    return attrs
class LiveStreamTopKItemEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_pack_topk_item"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("topk_num")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    out_attr_suffix = self._config.get("out_attr_suffix")
    for item_attr in self._config.get("item_attrs", []):
      attrs.add(item_attr + out_attr_suffix)
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get('item_attrs', []))
    return attrs  

class LiveStreamIntersectionCountEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_intersection_count"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("topk_num")))
    source_attrs = self._config.get("source_attrs", [])
    target_attrs = self._config.get("target_attrs", [])
    in_source_suffix = self._config.get("in_source_suffix")
    in_target_suffix = self._config.get("in_target_suffix")
    for in_attr in source_attrs:
      if in_source_suffix:
        in_attr = in_attr + in_source_suffix
      attrs.add(in_attr)
    for in_attr in target_attrs:
      if in_target_suffix:
        in_attr = in_attr + in_target_suffix
      attrs.add(in_attr)
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    out_attr_suffix = self._config.get("out_attr_suffix")
    source_attrs = self._config.get("source_attrs", [])
    target_attrs = self._config.get("target_attrs", [])
    sep = self._config.get("sep", ":")
    for source_attr, target_attr in itertools.product(source_attrs, target_attrs):
      out_attr = source_attr + sep + target_attr
      if out_attr_suffix:
        out_attr = out_attr + sep + out_attr_suffix
      attrs.add(out_attr)
    return attrs

class LiveStreamModuleDurationEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_module_duration"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("long_tail_request_thres_ms")))
    # NOTE(zhaoyang09): duration_names_attr 使用声明依旧有问题，暂时先屏蔽。
    # if(self._config.get("is_request_end")):
      # attrs.add(self._config.get("duration_names_attr", "duration_names_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    # NOTE(zhaoyang09): duration_names_attr 使用声明依旧有问题，暂时先屏蔽。
    """
    if(not self._config.get("is_module_start", False)):
      attrs.add(self._config.get("duration_names_attr", "duration_names_attr"))
    for sub_str in ["wall_time_cost_us", "cpu_time_cost_ns", "item_size_change"]:
      for suffix in ["start", "end", "duration"]:
        attrs.add("{}.{}.{}".format(self._config.get("module_name"), sub_str, suffix))
    """

    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("module_name"), "`module_name` 是必填项")

class LiveStreamLtvByRodisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_ltv_by_rodis"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["rodis_resp_attr"])
    ret.add(self._config["author_id_attr"])

    return ret

class LivestreamDcafControlEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_dcaf_control_enrich"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("json_path")))
    ret.add(self._config.get("user_value_attr"))
    ret.add(self._config.get("lambda_value_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_attr", "dcaf_output_attr"))
    return ret

class LiveStreamFNVHashEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_fnv_hash_enricher"
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("input_item_attrs", []):
      attrs.add(attr)
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("output_item_attrs", []):
      attrs.add(attr)
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("input_common_attrs", []):
      attrs.add(attr)
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("output_common_attrs", []):
      attrs.add(attr)
    return attrs

class LiveStreamLtv3ByRodisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_ltv3_by_rodis"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["rodis_resp_attr"])
    ret.add(self._config["author_id_attr"])

    return ret

class LiveStreamLtvnByRodisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_ltvn_by_rodis"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["rodis_resp_attr"])
    ret.add(self._config["count_days"])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["author_id_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    count_days = str(self._config["count_days"]) 
    ret.add("reward_amt_"+count_days)
    ret.add("reward_times_"+count_days)
    ret.add("like_amt_" + count_days)
    ret.add("comment_amt_" + count_days)
    ret.add("watch_live_time_" + count_days)
    ret.add("simple_watch_live_time_" + count_days)
    ret.add("view_amt_" + count_days)
    ret.add("real_show_amt_" + count_days)
    ret.add("action_list_amt_" + count_days)
    ret.add("view_live_room_amt_" + count_days)
    ret.add("like_live_room_amt_" + count_days)
    ret.add("comment_live_room_amt_" + count_days)
    ret.add("reward_live_room_amt_" + count_days)
    ret.add("following_time")
    ret.add("public_reward_amt_" + count_days)
    ret.add("public_reward_times_" + count_days)
    ret.add("public_like_amt_" + count_days)
    ret.add("public_comment_amt_" + count_days)
    ret.add("public_watch_live_time_" + count_days)
    ret.add("public_simple_watch_live_time_" + count_days)
    ret.add("public_view_amt_" + count_days)
    ret.add("public_real_show_amt_" + count_days)
    ret.add("public_action_list_amt_" + count_days)
    ret.add("private_reward_amt_" + count_days)
    ret.add("private_reward_times_" + count_days)
    ret.add("private_like_amt_" + count_days)
    ret.add("private_comment_amt_" + count_days)
    ret.add("private_watch_live_time_" + count_days)
    ret.add("private_simple_watch_live_time_" + count_days)
    ret.add("private_view_amt_" + count_days)
    ret.add("private_real_show_amt_" + count_days)
    ret.add("private_action_list_amt_" + count_days)
    ret.add("private_total_active_play_days_" + count_days)
    ret.add("private_total_active_play_ex_days_" + count_days)
    ret.add("private_total_active_play_rooms_" + count_days)
    ret.add("private_active_play_cnt_" + count_days)
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    count_days = str(self._config["count_days"]) 
    ret.add("total_reward_amt"+count_days)
    ret.add("total_reward_times"+count_days)
    ret.add("following_total_reward_amt" + count_days)
    ret.add("following_total_reward_times" + count_days)
    ret.add("total_like_amt" + count_days)
    ret.add("total_comment_amt" + count_days)
    ret.add("total_watch_live_time" + count_days)
    ret.add("total_simple_watch_live_time" + count_days)
    ret.add("total_view_amt" + count_days)
    ret.add("total_real_show_amt" + count_days)
    ret.add("total_action_list_amt" + count_days)
    ret.add("following_total_like_amt" + count_days)
    ret.add("following_total_comment_amt" + count_days)
    ret.add("following_total_watch_live_time" + count_days)
    ret.add("following_total_simple_watch_live_time" + count_days)
    ret.add("following_total_view_amt" + count_days)
    ret.add("following_total_real_show_amt" + count_days)
    ret.add("following_total_action_list_amt" + count_days)
    ret.add("total_new_following_amt" + count_days)
    ret.add("total_view_live_room_amt" + count_days)
    ret.add("total_like_live_room_amt" + count_days)
    ret.add("total_comment_live_room_amt" + count_days)
    ret.add("total_reward_live_room_amt" + count_days)
    ret.add("public_total_reward_amt" + count_days)
    ret.add("public_total_reward_times" + count_days)
    ret.add("public_total_like_amt" + count_days)
    ret.add("public_total_comment_amt" + count_days)
    ret.add("public_total_watch_live_time" + count_days)
    ret.add("public_total_simple_watch_live_time" + count_days)
    ret.add("public_total_view_amt" + count_days)
    ret.add("public_total_real_show_amt" + count_days)
    ret.add("public_total_action_list_amt" + count_days)
    ret.add("private_total_reward_amt" + count_days)
    ret.add("private_total_reward_times" + count_days)
    ret.add("private_total_like_amt" + count_days)
    ret.add("private_total_comment_amt" + count_days)
    ret.add("private_total_watch_live_time" + count_days)
    ret.add("private_total_simple_watch_live_time" + count_days)
    ret.add("private_total_view_amt" + count_days)
    ret.add("private_total_real_show_amt" + count_days)
    ret.add("private_total_action_list_amt" + count_days)
    return ret


class LiveStreamMinLtvByRodisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_minute_ltv_by_rodis"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["rodis_resp_attr"])
    ret.add(self._config["count_minutes"])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["author_id_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    count_minutes = str(self._config["count_minutes"]) 
    ret.add("reward_amt_"+count_minutes)
    ret.add("reward_times_"+count_minutes)
    ret.add("like_amt_" + count_minutes)
    ret.add("comment_amt_" + count_minutes)
    ret.add("watch_live_time_" + count_minutes)
    ret.add("simple_watch_live_time_" + count_minutes)
    ret.add("view_amt_" + count_minutes)
    ret.add("real_show_amt_" + count_minutes)
    ret.add("action_list_amt_" + count_minutes)
    ret.add("view_live_room_amt_" + count_minutes)
    ret.add("like_live_room_amt_" + count_minutes)
    ret.add("comment_live_room_amt_" + count_minutes)
    ret.add("reward_live_room_amt_" + count_minutes)
    ret.add("following_time_")
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    count_minutes = str(self._config["count_minutes"]) 
    ret.add("total_reward_amt_"+count_minutes)
    ret.add("total_reward_times_"+count_minutes)
    ret.add("following_total_reward_amt_" + count_minutes)
    ret.add("following_total_reward_times_" + count_minutes)
    ret.add("total_like_amt_" + count_minutes)
    ret.add("total_comment_amt_" + count_minutes)
    ret.add("total_watch_live_time_" + count_minutes)
    ret.add("total_simple_watch_live_time_" + count_minutes)
    ret.add("total_view_amt_" + count_minutes)
    ret.add("total_real_show_amt_" + count_minutes)
    ret.add("total_action_list_amt_" + count_minutes)
    ret.add("following_total_like_amt_" + count_minutes)
    ret.add("following_total_comment_amt_" + count_minutes)
    ret.add("following_total_watch_live_time_" + count_minutes)
    ret.add("following_total_simple_watch_live_time_" + count_minutes)
    ret.add("following_total_view_amt_" + count_minutes)
    ret.add("following_total_real_show_amt_" + count_minutes)
    ret.add("following_total_action_list_amt_" + count_minutes)
    ret.add("total_new_following_amt_" + count_minutes)
    ret.add("total_view_live_room_amt_" + count_minutes)
    ret.add("total_like_live_room_amt_" + count_minutes)
    ret.add("total_comment_live_room_amt_" + count_minutes)
    ret.add("total_reward_live_room_amt_" + count_minutes)
    return ret



class LiveStreamGiftUserTypeParserEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_gift_user_type_parser"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("user_type_list_str"))
    return ret
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["save_user_type_to"])
    ret.add(self._config["save_user_property_to"])
    return ret

class LiveStreamAbtestCommonAttrEnricher(LeafEnricher):
  @strict_types
  def __init__(self, config: dict):
    TYPE_NAME_MAP = {
      "int": "int",
      "float": "double",
      "str": "string",
      "bool": "bool",
    }
    prioritized_suffix = config.get("prioritized_suffix")
    skip_special_param = config.get("skip_special_param_during_suffix_check")
    if (skip_special_param != None):
      check_arg(isinstance(skip_special_param, dict), "skip_special_param_during_suffix_check 应为 dict 类型")
    for param in config["ab_params"]:
      param_name = param.get("param_name")
      check_arg(isinstance(param_name, (str, dict)), "ab_params 里的 param_name 值必须为 string 或 dict 类型")
      check_arg(param_name, "ab_params 里的 param_name 不可为空")
      if prioritized_suffix or isinstance(param_name, dict):
        check_arg(param.get("attr_name", ""), f"配置了 prioritized_suffix 或 param_name 为 dict 类型的情况下必须指定 attr_name")
      if "param_type" not in param:
        type_name = type(param["default_value"]).__name__
        check_arg(type_name in TYPE_NAME_MAP, f"非法的 abtest param default_value: {param['default_value']}")
        param["param_type"] = TYPE_NAME_MAP[type_name]
    check_arg(isinstance(config.get("deduplicate", False), bool), "deduplicate 必须为 bool 类型")
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_live_abtest_params"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  def __get_attr_names(self) -> set:
    return { ab.get("attr_name") or ab["param_name"] for ab in self._config["ab_params"] }

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("biz_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("prioritized_suffix")))
    attrs.update(self.extract_dynamic_params(self._config.get("user_id")))
    attrs.update(self.extract_dynamic_params(self._config.get("device_id")))
    attrs.update(self.extract_dynamic_params(self._config.get("product")))
    attrs.update(self.extract_dynamic_params(self._config.get("platform")))
    attrs.update(self.extract_dynamic_params(self._config.get("app_version")))
    # 下面两个静态 Common Attrs 一般不应该使用的，就不出现 API 文档里面了
    attrs.add("_ABTEST_USER_TAG_NAMES_")
    attrs.add("_ABTEST_USER_TAG_VALUES_")
    for ab in self._config["ab_params"]:
      attrs.update(self.extract_dynamic_params(ab.get("report_ab_hit")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set() if self._config.get("for_item_level", False) else self.__get_attr_names()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("for_item_level", False):
      attrs.add(self._config.get("item_level_user_id_attr", ""))
      attrs.add(self._config.get("item_level_device_id_attr", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return self.__get_attr_names() if self._config.get("for_item_level", False) else set()

class LiveLabelEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_live_label_proto"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("live_label_attr"), "`live_label_attr` 是必选项")
    check_arg(self._config.get("labels"), "`labels` 是必选项")
    check_arg(self._config.get("live_label_kconf_path"), "`live_label_kconf_path` 是必选项")

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    output_attr = set()
    for _ in self._config["labels"]:
      if isinstance(_, str):
        output_attr.add(_)
      else:
        output_attr.add(_["attr"])
    return output_attr
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set([self._config["live_label_attr"]])
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set()

class RawSamplePackageAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_raw_sample_package"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("from_extra_var"), "`from_extra_var` 是必选项")
    check_arg(self._config.get("item_id_attr_name"), "`item_id_attr_name` 是必选项")

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if ("save_item_attr_names_to" in self._config):
      attrs.add(self._config["save_item_attr_names_to"])
    return attrs
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(self._config["from_extra_var"])
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if ("save_common_attr_names_to" in self._config):
      attrs.add(self._config["save_common_attr_names_to"])
    return attrs

class LivestreamDumpContextEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_dump_context"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("common_attrs", [])))
    attrs.update(self.extract_dynamic_params(self._config.get("item_attrs", [])))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("dump_to_attr", "")
    attrs.add(attr)
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg("dump_to_attr" in self._config, "必须配置 dump_to_attr")
    if "item_attrs" in self._config:
      check_arg(isinstance(self._config.get("item_attrs"), str), 'item_attrs 必须是 str')
    if "common_attrs" in self._config:
      check_arg(isinstance(self._config.get("common_attrs"), str), 'common_attrs 必须是 str')

class LivestreamAthenaResultsRequestEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_athena_results_request"

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("athena_explore_request_bytes_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add('athena_explore_response_ptr')
    attrs.add('athena_explore_response_bytes')
    attrs.add('athena_explore_response_waiter')
    return attrs

class LivestreamMerchantShowCaseResultsRequestEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_merchant_show_case_results_request"

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add('user_info_attr')
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.update(self.extract_dynamic_params(self._config.get("kess_group")))
    attrs.update(self.extract_dynamic_params(self._config.get("request_type", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("request_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(extract_attr_names(self._config.get("send_common_attrs", []), "name"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add('merchant_show_case_response_ptr')
    attrs.add('merchant_show_case_response_bytes')
    attrs.add('merchant_show_case_response_waiter')
    return attrs

class LivestreamRocketLeafRequestEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_rocket_leaf_request"

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add('user_info_attr')
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.update(self.extract_dynamic_params(self._config.get("kess_group")))
    attrs.update(self.extract_dynamic_params(self._config.get("request_type", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("request_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(extract_attr_names(self._config.get("send_common_attrs", []), "name"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add('rocket_leaf_response_ptr')
    attrs.add('rocket_leaf_response_bytes')
    attrs.add('rocket_leaf_response_waiter')
    return attrs
