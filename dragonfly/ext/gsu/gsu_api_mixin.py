#!/usr/bin/env python3
# coding=utf-8

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .gsu_enricher import *
from .gsu_retriever import *


class GsuApiMixin(CommonLeafBaseMixin):
  """
  Gsu Processor API 接口的 Mixin 实现
  """

  def gsu_retriever_with_colossus_resp(self, **kwargs):
    """
    CommonRecoColossusRespEnricher
    ------

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    The following are required attrs from the resp_attr, could be empty
    ------
    `save_photo_id_to_attr`: [string]

    `save_author_id_to_attr`: [string]

    `save_duration_to_attr`: [string]

    `save_play_time_to_attr`: [string]

    `save_tag_to_attr`: [string]

    `save_label_to_attr`: [string]

    `save_timestamp_to_attr`: [string]

    `filter_future_attr` : [boolean] 是否只取时间在request time之前的colossus pid, 默认为false

    `colossus_result_as_list`: [boolean], 是否把 photo_id, author_id, duration, play_time, tag, label, timestamp 作为一个 list 一起写到 item attr 中;
                               colossus_result_as_list 为 true 的时候，必须提供 `colossus_result_list_attr`，这时 save_photo_id_to_attr，save_author_id_to_attr
                               save_duration_to_attr，save_play_time_to_attr，save_tag_to_attr，save_label_to_attr，save_timestamp_to_attr 不再生效; 这样做是起到
                               一些性能优化的作用。

    `colossus_result_list_attr`: [string], 把 photo_id, author_id, duration, play_time, tag, label, timestamp 作为一个 list 一起写到 item 侧的
                               colossus_result_list_attr 属性中。

    示例
    ------
    ``` python
    .gsu_retriever_with_colossus_resp(colossus_resp_attr="colossus_output",
                            save_photo_id_to_attr="pid",
                            save_author_id_to_attr="aid",
                            save_duration_to_attr="duration",
                            save_play_time_to_attr="play",
                            save_tag_to_attr="tag",
                            save_label_to_attr="label",
                            save_timestamp_to_attr="time",)

    # colossusv2
    .gsu_common_colossusv2_enricher(kconf='colossus.kconf_client.video_item',
                                    item_fields=dict(photo_id="photo_id_list",
                                                      author_id="author_id_list",
                                                      duration="duration_list",
                                                      play_time="play_time_list",
                                                      tag="tag_list",
                                                      label="label_list",
                                                      timestamp="timestamp_list"))
    .gsu_retriever_with_colossus_resp(from_colossus_sim_v2=True,
                    photo_id_from="photo_id_list",
                    author_id_from="author_id_list",
                    duration_from="duration_list",
                    play_time_from="play_time_list",
                    tag_from="tag_list",
                    label_from="label_list",
                    timestamp_from="timestamp_list",
                    save_photo_id_to_attr="pid",
                    save_author_id_to_attr="aid",
                    save_duration_to_attr="duration",
                    save_play_time_to_attr="play",
                    save_tag_to_attr="tag",
                    save_channel_to_attr="channel",
                    save_label_to_attr="label",
                    save_timestamp_to_attr="time")
    ```

    ``` python
    .gsu_retriever_with_colossus_resp(colossus_resp_attr="colossus_output",
                            colossus_result_as_list=True,
                            colossus_result_list_attr="colossus_result_list")
    ```
    """
    self._add_processor(CommonRecoColossusRespRetriever(kwargs))
    return self

  def gsu_retriever_with_colossus_resp_v2(self, **kwargs):
    """
    CommonRecoColossusRespRetrieverV2
    ------

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    The following are required attrs from the resp_attr, could be empty
    ------
    `save_photo_id_to_attr`: [string]

    `save_author_id_to_attr`: [string]

    `save_duration_to_attr`: [string]

    `save_play_time_to_attr`: [string]

    `save_tag_to_attr`: [string]

    `save_channel_to_attr`: [string]

    `save_label_to_attr`: [string]

    `save_timestamp_to_attr`: [string]

    `save_photo_lat_attr`: [string]

    `save_photo_lon_attr`: [string]

    `save_user_lat_attr`: [string]

    `save_user_lon_attr`: [string]

    `filter_future_attr` : [boolean] 是否只取时间在request time之前的colossus pid, 默认为false

    `parse_from_pb`: [boolean] 是否从protobuf message中取colossus的返回结果，默认为true

    示例
    ------
    ``` python
    .gsu_retriever_with_colossus_resp_v2(colossus_resp_attr="colossus_output",
                            save_photo_id_to_attr="pid",
                            save_author_id_to_attr="aid",
                            save_duration_to_attr="duration",
                            save_play_time_to_attr="play",
                            save_tag_to_attr="tag",
                            save_channel_to_attr="channel",
                            save_label_to_attr="label",
                            save_timestamp_to_attr="time",
                            save_photo_lat_attr="photo_lat_list",
                            save_photo_lon_attr="photo_lon_list",
                            save_user_lat_attr="user_lat",
                            save_user_lon_attr="user_lon")

    # 配合 colossus-sim v2 使用
    .gsu_common_colossusv2_enricher(kconf="colossus.kconf_client.video_item',
                    item_fields=dict(photo_id="",
                                      author_id_v2="",
                                      duration="",
                                      play_time="",
                                      tag="",
                                      channel="",
                                      label="",
                                      timestamp=""),
                    reflection_output_attr="video_item_reflection",
                    item_datas_output_attr="video_item_colossus_data") \
    .gsu_retriever_with_colossus_resp_v2(
                    colossusv2_reflection_input_attr="video_item_reflection",
                    colossusv2_item_datas_input_attr="video_item_colossus_data",
                    save_photo_id_to_attr="pid",
                    save_author_id_to_attr="aid",
                    save_duration_to_attr="duration",
                    save_play_time_to_attr="play",
                    save_tag_to_attr="tag",
                    save_channel_to_attr="channel",
                    save_label_to_attr="label",
                    save_timestamp_to_attr="time")
    ```
    """
    self._add_processor(CommonRecoColossusRespRetrieverV2(kwargs))
    return self
  
  def gsu_retriever_with_colossus_resp_v5(self, **kwargs):
    """
    CommonRecoColossusRespV5Retriever
    ------

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    The following are required attrs from the resp_attr, could be empty
    ------
    `save_pid_attr_to`: [string]

    `save_aid_attr_to`: [string]

    `save_play_attr_to`: [string]

    `save_duration_attr_to`: [string]

    `save_tag_attr_to`: [string]

    `save_time_attr_to`: [string]

    `save_label_attr_to`: [string]

    `save_channel_attr_to`: [string]

    `save_diff_attr_to`: [string]

    `save_play_duration_attr_to`: [string]

    `day_end`: [boolean] 是否使用当天23:59:59作为request_time

    `filter_time`: [int] 
    
    `limit_num` [int]

    `filter_future_attr` : [boolean] 是否只取时间在request time之前的colossus pid, 默认为false

    `parse_from_pb`: [boolean] 是否从protobuf message中取colossus的返回结果，默认为true

    示例
    ------
    ``` python
    .gsu_retriever_with_colossus_resp_v5(
            colossus_resp_attr="colossus_output",
            save_pid_attr_to="colossus_pids",
            save_aid_attr_to="colossus_aids",
            save_play_attr_to="colossus_plays",
            save_duration_attr_to="colossus_durations",
            save_tag_attr_to="colossus_tags",
            save_time_attr_to="colossus_times",
            save_label_attr_to="colossus_labels",
            save_channel_attr_to="colossus_channels",
            save_diff_attr_to="colossus_diff",
            save_play_duration_attr_to="colossus_play_duration",
            day_end=True,
            filter_time=60,
            limit_num=4500,
            parse_from_pb=False,
            filter_future_attr=True,
    )
    ```
    """
    self._add_processor(CommonRecoColossusRespV5Retriever(kwargs))
    return self
  
  def gsu_retriever_with_eshop_cut(self, **kwargs):
    """
    CommonEshopColossusCutRetriever
    ------

    参数
    ------
    name: [string] 检索器实例名称，用于日志和监控标识
    
    query_key: [string] 查询键名，用于关联用户请求（如"user_id"）

    输入属性（需从上游处理器获取）
    ------
    eshop_video_timestamp_attr: [string] 视频行为时间戳列表属性名
    eshop_video_photo_id_attr: [string] 视频图片ID列表属性名  
    eshop_video_author_id_attr: [string] 视频作者ID列表属性名
    eshop_video_play_time_attr: [string] 视频播放时间列表属性名
    eshop_video_channel_attr: [string] 视频频道分类列表属性名
    eshop_video_label_attr: [string] 视频标签列表属性名
    eshop_video_duration_attr: [string] 视频时长列表属性名
    eshop_video_category_attr: [string] 视频类目列表属性名  
    eshop_video_spu_id_attr: [string] 视频SPU ID列表属性名

    输出属性（存储处理结果）
    ------
    eshop_video_timestamp_cut_prefix: [string] 处理后时间戳存储属性名
    eshop_video_photo_id_cut_prefix: [string] 处理后图片ID存储属性名
    eshop_video_author_id_cut_prefix: [string] 处理后作者ID存储属性名
    eshop_video_play_time_cut_prefix: [string] 处理后播放时间存储属性名
    eshop_video_channel_cut_prefix: [string] 处理后频道分类存储属性名
    eshop_video_label_cut_prefix: [string] 处理后标签存储属性名
    eshop_video_duration_cut_prefix: [string] 处理后视频时长存储属性名
    eshop_video_category_cut_prefix: [string] 处理后类目存储属性名
    eshop_video_spu_id_cut_prefix: [string] 处理后SPU ID存储属性名
    eshop_video_save_diff_cut_prefix: [string] 时间差异值存储属性名（相对当前请求时间）
    eshop_video_save_diff_hour_cut_prefix: [string] 时间差异值存储属性名（相对当前请求时间，单位为小时）

    示例
    ------
    ``` python
     .gsu_retriever_with_eshop_cut(
        name='colossusv2_merchant_good_show_item',
        query_key="user_id",
        eshop_video_timestamp_attr = "eshop_video_timestamp_list",
        eshop_video_photo_id_attr = "eshop_video_photo_id_list",
        eshop_video_author_id_attr = "eshop_video_author_id_list",
        eshop_video_play_time_attr = "eshop_video_play_time_list",
        eshop_video_channel_attr = "eshop_video_channel_list",
        eshop_video_label_attr = "eshop_video_label_list",
        eshop_video_duration_attr = "eshop_video_duration_list",
        eshop_video_category_attr = "eshop_video_category_list",
        eshop_video_spu_id_attr = "eshop_video_spu_id_list",
   
        eshop_video_timestamp_cut_prefix = "eshop_video_timestamp_cut",
        eshop_video_photo_id_cut_prefix = "eshop_video_photo_id_cut",
        eshop_video_author_id_cut_prefix = "eshop_video_author_id_cut",
        eshop_video_play_time_cut_prefix = "eshop_video_play_time_cut",
        eshop_video_channel_cut_prefix = "eshop_video_channel_cut",
        eshop_video_label_cut_prefix = "eshop_video_label_cut",
        eshop_video_duration_cut_prefix = "eshop_video_duration_cut",
        eshop_video_category_cut_prefix = "eshop_video_category_cut",
        eshop_video_spu_id_cut_prefix = "eshop_video_spu_id_cut",
        eshop_video_save_diff_cut_prefix = "eshop_video_save_diff_cut",
        eshop_video_save_diff_hour_cut_prefix = "eshop_video_save_diff_hour_cut",

        kconf="colossus.kconf_client.merchant_good_show_item",
        filter_future_items=True,
    )\
    ```
    """
    self._add_processor(CommonEshopColossusCutRetriever(kwargs))
    return self

  def gsu_retriever_with_eshop_resp(self, **kwargs):
    """
    CommonEshopColossusRespRetriever
    ------

    参数
    ------
    name: [string] 检索器实例名称，用于日志和监控标识
    
    query_key: [string] 查询键名，用于关联用户请求（如"user_id"）

    输入属性（需从上游处理器获取）
    ------
    eshop_video_timestamp_attr: [string] 视频行为时间戳列表属性名
    eshop_video_photo_id_attr: [string] 视频图片ID列表属性名  
    eshop_video_author_id_attr: [string] 视频作者ID列表属性名
    eshop_video_play_time_attr: [string] 视频播放时间列表属性名
    eshop_video_channel_attr: [string] 视频频道分类列表属性名
    eshop_video_label_attr: [string] 视频标签列表属性名
    eshop_video_duration_attr: [string] 视频时长列表属性名
    eshop_video_category_attr: [string] 视频类目列表属性名  
    eshop_video_spu_id_attr: [string] 视频SPU ID列表属性名

    输出属性（存储处理结果）
    ------
    save_eshop_video_timestamp_attr_to: [string] 处理后时间戳存储属性名
    save_eshop_video_photo_id_attr_to: [string] 处理后图片ID存储属性名
    save_eshop_video_author_id_attr_to: [string] 处理后作者ID存储属性名
    save_eshop_video_play_time_attr_to: [string] 处理后播放时间存储属性名
    save_eshop_video_channel_attr_to: [string] 处理后频道分类存储属性名
    save_eshop_video_label_attr_to: [string] 处理后标签存储属性名
    save_eshop_video_duration_attr_to: [string] 处理后视频时长存储属性名
    save_eshop_video_category_1_attr_to: [string] 处理后类目1存储属性名
    save_eshop_video_category_2_attr_to: [string] 处理后类目2存储属性名
    save_eshop_video_category_3_attr_to: [string] 处理后类目3存储属性名
    save_eshop_video_spu_id_attr_to: [string] 处理后SPU ID存储属性名
    save_diff_attr_to: [string] 时间差异值存储属性名（相对当前请求时间）
    save_diff_hour_attr_to: [string] 时间差异值存储属性名（相对当前请求时间）
    save_hour_of_day_attr_to: [string] 时间差异值存储属性名（相对当前请求时间）
    save_if_positive_seq_attr_to:[string] 存储当前是否为正样本，并进行分类
    save_play_duration_attr_to: [string] 播放时长编码值存储属性名（高8位播放次数，低24位时长）

    示例
    ------
    ``` python
     .gsu_retriever_with_eshop_resp(
        name='colossusv2_merchant_good_show_item',
        query_key="user_id",
        eshop_video_timestamp_attr = "eshop_video_timestamp_list",
        eshop_video_photo_id_attr = "eshop_video_photo_id_list",
        eshop_video_author_id_attr = "eshop_video_author_id_list",
        eshop_video_play_time_attr = "eshop_video_play_time_list",
        eshop_video_channel_attr = "eshop_video_channel_list",
        eshop_video_label_attr = "eshop_video_label_list",
        eshop_video_duration_attr = "eshop_video_duration_list",
        eshop_video_category_attr = "eshop_video_category_list",
        eshop_video_spu_id_attr = "eshop_video_spu_id_list",
   
        save_eshop_video_timestamp_attr_to = "save_eshop_video_timestamp_list",
        save_eshop_video_photo_id_attr_to = "save_eshop_video_photo_id_list",
        save_eshop_video_author_id_attr_to = "save_eshop_video_author_id_list",
        save_eshop_video_play_time_attr_to = "save_eshop_video_play_time_list",
        save_eshop_video_channel_attr_to = "save_eshop_video_channel_list",
        save_eshop_video_label_attr_to = "save_eshop_video_label_list",
        save_eshop_video_duration_attr_to = "save_eshop_video_duration_list",
        save_eshop_video_category_1_attr_to = "save_eshop_video_category_1_list",
        save_eshop_video_category_2_attr_to = "save_eshop_video_category_2_list",
        save_eshop_video_category_3_attr_to = "save_eshop_video_category_3_list",
        save_eshop_video_spu_id_attr_to = "save_eshop_video_spu_id_list",
        save_diff_attr_to = "save_diff",
        save_play_duration_attr_to = "save_play_duration",
        save_if_positive_seq_attr_to = "save_if_positive_seq_list"

        kconf="colossus.kconf_client.merchant_good_show_item",
        filter_future_items=True,
        limit=10000,
    )\
    ```
    """
    self._add_processor(CommonEshopColossusRespRetriever(kwargs))
    return self

  def random_sleep(self, sleep_ms=None, **kwargs):
    """
    CommonEshopRandomSleepRetriever
    ------
    random_sleep 若干毫秒数

    参数配置
    ------
    `sleep_ms`: [int] [动态参数] random_sleep 的毫秒数

    调用示例
    ------
    ``` python
    # sleep 100 毫秒
    .random_sleep(100)
    ```
    """
    if sleep_ms:
      kwargs["sleep_ms"] = sleep_ms
    self._add_processor(CommonEshopRandomSleepRetriever(kwargs))
    return self

  def gsu_retriever_with_colossus_resp_batch_v2(self, **kwargs):
    """
    GsuColossusRespBatchRetrieverV2
    ------

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    `uid_list_attr`: [string] colossus返回的batch结果中对应的 uid list

    `from_colossus_sim_v2`: [string] 数据是否来自 colossusv2 ，即 colossus.kconf_client.video_item （里面没有经纬度数据）

    `input_reflection_attr`: [string] 如果数据来自 colossusv2，则这个 reflection 必须配

    The following are required attrs from the resp_attr, could be empty
    ------
    `save_photo_id_to_attr`: [string]

    `save_author_id_to_attr`: [string]

    `save_user_id_to_attr`: [string]

    `save_duration_to_attr`: [string]

    `save_play_time_to_attr`: [string]

    `save_tag_to_attr`: [string]

    `save_channel_to_attr`: [string]

    `save_label_to_attr`: [string]

    `save_timestamp_to_attr`: [string]

    `save_photo_lat_attr`: [string]

    `save_photo_lon_attr`: [string]

    `save_user_lat_attr`: [string]

    `save_user_lon_attr`: [string]

    `filter_future_attr` : [boolean] 是否只取时间在request time之前的colossus pid, 默认为false

    `filter_past_days` : [int][动态参数] 过滤在request time减去此天数阈值之前的colossus pid，默认为-1，即不过滤

    `save_item_attrs_in_list` : [boolean] 是否使用 list 存储 item attrs

    示例
    ------
    ``` python
    .gsu_retriever_with_colossus_resp_batch_v2(colossus_resp_attr="colossus_output",
                            uid_list_attr="uid_bi_follow_list",
                            save_photo_id_to_attr="pid",
                            save_author_id_to_attr="aid",
                            save_user_id_to_attr="uid",
                            save_duration_to_attr="duration",
                            save_play_time_to_attr="play",
                            save_tag_to_attr="tag",
                            save_channel_to_attr="channel",
                            save_label_to_attr="label",
                            save_timestamp_to_attr="time",
                            save_photo_lat_attr="photo_lat_list",
                            save_photo_lon_attr="photo_lon_list",
                            save_user_lat_attr="user_lat",
                            save_user_lon_attr="user_lon")
    ```
    """
    self._add_processor(GsuColossusRespBatchRetrieverV2(kwargs))
    return self

  def gsu_common_colossus_resp_retriever(self, **kwargs):
    """
    CommonColossusRespRetriever
    ------

    参数（详细说明参考文档：https://docs.corp.kuaishou.com/d/home/<USER>
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    `colossus_service_name`: [string]

    `item_key_field`: [string]

    `item_time_field`: [string]

    `item_fields`: [dict]

    `filter_future_items` : [boolean] 是否只取时间在 request time 之前的 item 数据, 默认为 false

    `filter_future_seconds`: [int][动态参数] filter_future_items 为 true 时过滤 N 秒内的行为，默认为 60

    `max_item_num`: [int][动态参数] 设置只返回最新的N个item，默认是0，不限制返回的item数量

    `to_common_attr` : [boolean] 是否把数据解析后填充到 common attr, 默认为 false

    示例
    ------
    ``` python
    .gsu_common_colossus_resp_retriever(colossus_resp_attr="colossus_output",
                            colossus_service_name="grpc_colossusSimV2",
                            item_key_field="photo_id",
                            item_time_field="timestamp",
                            item_fields=dict(photo_id="photo_id",
                                             author_id="author_id",
                                             duration="duration",
                                             play_time="play_time",
                                             tag="tag",
                                             channel="channel",
                                             label="label",
                                             timestamp="timestamp",
                                             user_latitude="user_latitude",
                                             user_longitude="user_longitude",
                                             photo_latitude="photo_latitude",
                                             photo_longitude="photo_longitude"))

    # 配合 colossus-sim v2 使用
    .gsu_common_colossusv2_enricher(kconf='colossus.kconf_client.video_item',
                                    partial_schema_output_attr="video_item_schema",
                                    item_fields=dict(photo_id="photo_id_list",
                                                    author_id="author_id_list",
                                                    duration="duration_list",
                                                    play_time="play_time_list",
                                                    tag="tag_list",
                                                    channel="channel_list",
                                                    label="label_list",
                                                    timestamp="timestamp_list")) \
    .gsu_common_colossus_resp_retriever(
      from_colossus_sim_v2=True,
      print_item_fields=True,
      partial_schema_input_attr="video_item_schema",
      item_key_field="photo_id",
      item_time_field="timestamp",
      input_item_fields=dict(photo_id="photo_id_list",
                    author_id="author_id_list",
                    duration="duration_list",
                    play_time="play_time_list",
                    tag="tag_list",
                    channel="channel_list",
                    label="label_list",
                    timestamp="timestamp_list"),
      item_fields=dict(photo_id="pids",
                    author_id="aids",
                    duration="drs",
                    play_time="pls",
                    tag="tags",
                    channel="cns",
                    label="lbs",
                    timestamp="tss"))
    ```
    """
    self._add_processor(CommonColossusRespRetriever(kwargs))
    return self

  def live_gsu_retriever_with_colossus_resp(self, **kwargs):
    """
    CommonLiveColossusRespRetriever
    ------

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    The following are required attrs from the resp_attr, could be empty
    ------
    `save_photo_id_to_attr`: [string]

    `save_author_id_to_attr`: [string]

    `save_page_type_to_attr`: [string]

    `save_play_time_to_attr`: [string]

    `save_tag_to_attr`: [string]

    `save_optag_to_attr`: [string]

    `save_timestamp_to_attr`: [string]

    `filter_future_attr` : [boolean] 是否只取时间在request time之前的colossus pid, 默认为false

    示例
    ------
    ``` python
    .live_gsu_retriever_with_colossus_resp(
      colossus_resp_attr="live_colossus_output",
      save_author_id_to_attr="live_colossus_aid",
      save_page_type_to_attr="live_colossus_page_type",
      save_play_time_to_attr="live_colossus_play_time",
      save_tag_to_attr="live_colossus_tag",
      save_optag_to_attr="live_colossus_optag",
      save_timestamp_to_attr="live_colossus_timestamp",
      save_result_to_common_attr="live_colossus_pid",
      filter_future_attr=True)
    ```
    """
    self._add_processor(CommonLiveColossusRespRetriever(kwargs))
    return self

  def live_gsu_retriever_with_colossus_resp_v4(self, **kwargs):
    """
    CommonLiveColossusRespRetrieverV4
    ------

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    The following are required attrs from the resp_attr, could be empty
    ------
    `save_photo_id_to_attr`: [string]

    `save_author_id_to_attr`: [string]

    `save_page_type_to_attr`: [string]

    `save_play_time_to_attr`: [string]

    `save_tag_to_attr`: [string]

    `save_optag_to_attr`: [string]

    `save_reward_to_attr`: [string]

    `save_timestamp_to_attr`: [string]

    `filter_future_attr` : [boolean] 是否只取时间在request time之前的colossus pid, 默认为false

    示例
    ------
    ``` python
    .live_gsu_retriever_with_colossus_resp_v4(
      colossus_resp_attr="live_colossus_output",
      save_author_id_to_attr="live_colossus_aid",
      save_page_type_to_attr="live_colossus_page_type",
      save_play_time_to_attr="live_colossus_play_time",
      save_tag_to_attr="live_colossus_tag",
      save_optag_to_attr="live_colossus_optag",
      save_reward_to_attr="live_colossus_reward",
      save_timestamp_to_attr="live_colossus_timestamp",
      save_result_to_common_attr="live_colossus_pid",
      filter_future_attr=True)
    ```
    """
    self._add_processor(CommonLiveColossusRespRetrieverV4(kwargs))
    return self

  def live_gsu_retriever_with_colossus_resp_v2(self, **kwargs):
    """
    CommonLiveColossusRespRetrieverV2
    ------

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    The following are required attrs from the resp_attr, could be empty
    ------
    `save_photo_id_to_attr`: [string]

    `save_author_id_to_attr`: [string]

    `save_page_type_to_attr`: [string]

    `save_play_time_to_attr`: [string]

    `save_tag_to_attr`: [string]

    `save_optag_to_attr`: [string]

    `save_reward_to_attr`: [string]

    `save_timestamp_to_attr`: [string]

    `filter_future_attr` : [boolean] 是否只取时间在request time之前的colossus pid, 默认为false

    示例
    ------
    ``` python
    .live_gsu_retriever_with_colossus_resp_v2(
      colossus_resp_attr="live_colossus_output",
      save_author_id_to_attr="live_colossus_aid",
      save_page_type_to_attr="live_colossus_page_type",
      save_play_time_to_attr="live_colossus_play_time",
      save_tag_to_attr="live_colossus_tag",
      save_optag_to_attr="live_colossus_optag",
      save_reward_to_attr="live_colossus_reward",
      save_timestamp_to_attr="live_colossus_timestamp",
      save_result_to_common_attr="live_colossus_pid",
      filter_future_attr=True)
    ```
    """
    self._add_processor(CommonLiveColossusRespRetrieverV2(kwargs))
    return self

  def common_reco_retriever_author_with_colossus_resp(self, **kwargs):
    """
    CommonRecoColossusRespAuthorRetriever
    ------

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    `save_author_id_to_attr`: [string]

    `filter_future_attr` : [boolean] 是否只取时间在 request time 之前出现过的 colossus aid, 默认为false

    `parse_from_pb`: [boolean] 是否从 protobuf message 中取 colossus 的返回结果，默认为 true

    示例
    ------
    ``` python
    .common_reco_retriever_author_with_colossus_resp(
      colossus_resp_attr="colossus_output",
      save_author_id_to_attr="colossus_aid",
      )
    ```
    """
    self._add_processor(CommonRecoColossusRespAuthorRetriever(kwargs))
    return self

  def calc_poi_distance(self, **kwargs):
    """
    CalcLocationDistanceEnricher
    ------
    计算 user 经纬度 与 item_list 经纬度的距离，单位km.

    参数
    ------
    `common_user_lat_attr` : [double] 用户看视频的纬度

    `common_user_lon_attr` : [double] 用户看视频的经度

    `item_lat_attr` : [double] history action list 对应的纬度

    `item_lon_attr` : [double] history action list 对应的经度

    `output_poi_distance_attr` : [double] user 与 item poi 距离(km)

    示例
    ------
    ``` python
    .calc_poi_distance(common_user_lat_attr="user_lat",
                       common_user_lon_attr="user_lon",
                       item_lat_attr="photo_lat_list",
                       item_lon_attr="photo_lon_list",
                       output_poi_distance_attr="poi_distance")
    ```
    """
    self._add_processor(CalcLocationDistanceEnricher(kwargs))
    return self

  def kwaipro_gsu_retriever_with_colossus_resp(self, **kwargs):
    """
    KwaiProColossusRespRetriever
    ------

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    The following are required attrs from the resp_attr, could be empty
    ------
    `save_photo_id_to_attr`: [string]

    `save_author_id_to_attr`: [string]

    `save_duration_to_attr`: [string]

    `save_play_time_to_attr`: [string]

    `save_tag_to_attr`: [string]

    `save_label_to_attr`: [string]

    `save_timestamp_to_attr`: [string]

    `save_item_attr_as_list`: [boolean] 默认 false, 对于 play_time/label/timestamp, 如果用户曾多次播放该视频，可能会出现多值。该参数为 true 时，会把这几个 item attr 存成 list. 该参数为 false 时，只会保留最后一次出现的 item 的相应 attr. 注意：其余 attr, 即 photo_id/author_id/duration/tag 不受该参数影响。

    `filter_future_attr` : [boolean] 是否只取时间在request time之前的colossus pid, 默认为false

    `filter_play_time_threshold`: [int] 过滤掉 play_time 小于该值的 action item，默认为 0，表示不过滤

    示例
    ------
    ``` python
    .kwaipro_gsu_retriever_with_colossus_resp(
      colossus_resp_attr="colossus_output",
      save_photo_id_to_attr="pid",
      save_author_id_to_attr="aid",
      save_duration_to_attr="duration",
      save_play_time_to_attr="play",
      save_tag_to_attr="tag",
      save_label_to_attr="label",
      save_timestamp_to_attr="time",)
    ```
    """
    self._add_processor(KwaiProColossusRespRetriever(kwargs))
    return self

  def ksib_gsu_retriever_with_colossus_resp(self, **kwargs):
    """
    KsiboColossusRespRetriever
    ------

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    The following are required attrs from the resp_attr, could be empty
    ------
    `save_photo_id_to_attr`: [string]

    `save_author_id_to_attr`: [string]

    `save_duration_to_attr`: [string]

    `save_play_time_to_attr`: [string]

    `save_tag_to_attr`: [string]

    `save_label_to_attr`: [string]

    `save_timestamp_to_attr`: [string]

    `save_item_attr_as_list`: [boolean] 默认 false, 对于 play_time/label/timestamp, 如果用户曾多次播放该视频，可能会出现多值。该参数为 true 时，会把这几个 item attr 存成 list. 该参数为 false 时，只会保留最后一次出现的 item 的相应 attr. 注意：其余 attr, 即 photo_id/author_id/duration/tag 不受该参数影响。

    `filter_future_attr` : [boolean] 是否只取时间在request time之前的colossus pid, 默认为false

    `filter_play_time_threshold`: [int] 过滤掉 play_time 小于该值的 action item，默认为 0，表示不过滤

    `colossus_output_type`: [string] colossus 返回数据类型, 可选项: "sim_item"、"commmon_item"、"ksib_colossusv2" , 默认为 "sim_item"
    
    `colossusv2_reflection_input_attr`: [string] 当 colossus_output_type==colossusv2 或者 ksib_colossusv2时，必须配置此项，指定colossus response的读取方式

    `colossusv2_item_datas_input_attr`: [string] 当 colossus_output_type==colossusv2 或者 ksib_colossusv2时，必须配置此项，指定colossus response的数据存储

    示例
    ------
    ``` python
    .ksib_gsu_retriever_with_colossus_resp(
      colossus_resp_attr="colossus_output",
      save_photo_id_to_attr="pid",
      save_author_id_to_attr="aid",
      save_duration_to_attr="duration",
      save_play_time_to_attr="play",
      save_tag_to_attr="tag",
      save_label_to_attr="label",
      save_timestamp_to_attr="time",)
    ```
    """
    self._add_processor(KsibColossusRespRetriever(kwargs))
    return self

  def snack_gsu_retriever_with_colossus_resp(self, **kwargs):
    """
    SnackColossusRespRetriever
    ------

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    The following are required attrs from the resp_attr, could be empty
    ------
    `save_photo_id_to_attr`: [string]

    `save_author_id_to_attr`: [string]

    `save_duration_to_attr`: [string]

    `save_play_time_to_attr`: [string]

    `save_tag_to_attr`: [string]

    `save_label_to_attr`: [string]

    `save_timestamp_to_attr`: [string]

    `filter_future_attr` : [boolean] 是否只取时间在request time之前的colossus pid, 默认为false

    示例
    ------
    ``` python
    .snack_gsu_retriever_with_colossus_resp(
      colossus_resp_attr="colossus_output",
      save_photo_id_to_attr="pid",
      save_author_id_to_attr="aid",
      save_duration_to_attr="duration",
      save_play_time_to_attr="play",
      save_tag_to_attr="tag",
      save_label_to_attr="label",
      save_timestamp_to_attr="time",)
    ```
    """
    self._add_processor(SnackColossusRespRetriever(kwargs))
    return self

  def kwaime_gsu_user_cluster_retriever(self, **kwargs):
    """
    KwaiMeGsuUserClusterRetriever
    ------
    根据 user colossus 行为列表召回 top k 出现的 cluster， 并填充对应的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 item attr name

    `output_slot_attr`: [string] slot 输出 item attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 common attr

    `limit_num`: [int][动态参数] 返回数目 common attr, 默认值 50

    `bucket`: [int][动态参数] 海外桶枚举值, 默认值 29

    `max_user_cluster`: [int][动态参数] 召回数量, 默认值 50

    `item_type`: [int] item type, 默认值 0

    `reason`: [int] 召回原因, 默认值 1

    `hetu_cluster_config_kconf_key`: [string] 选配项 聚类 cluster 距离最近的搜索聚类 Kconf 配置, 默认值 overseaReco.offline.kwaiMeMMUClusterIndex

    `mio_slots_id`: [int_list] 选配项 区分 embedding 特征的 slot 列表, 默认值 [346, 347, 349, 348, 350]

    `slots_id`: [int_list] 选配项 sign 对应的 slot 列表, 默认值 [26, 128, 349, 348, 350]

    `kess_service`: [string] embedding_server 的 kess 服务名

    `kess_cluster`: [string] 选配项 embedding_server 的 kess 集群名，默认为 PRODUCTION

    `thread_num`: [int] 选配项 kess client eventloop 线程数, 默认值 1

    `shard_num`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `timeout_ms`: [int] 选配项 查询的超时时间，默认为 10 ms

    `max_pids_per_request`: [int] 选配项 单次 rpc 请求中的最大 pid 个数，为 0 代表不限制, 默认值 0

    示例
    ------
    ``` python
    .kwaime_gsu_user_cluster_retriever(
      colossus_resp_attr="colossus_output",
      limit_num=100,
      kess_service='grpc_KwaiMeMMUCluster',
      timeous_ms=100,
      shard_num=2,
      slots_id=[123, 122, 692, 693, 694, 695],
      mio_slots_id=[690, 691, 692, 693, 694, 695],
      output_sign_attr="gsu_signs",
      output_slot_attr="gsu_slots",
      )
    ```
    """
    self._add_processor(KwaiMeGsuUserClusterRetriever(kwargs))
    return self

  def gsu_twinsNet_feature_enricher(self, **kwargs):
    """
    CommonRecoGsuTwinsNetFeaEnricher
    ------
    U2U 双塔孪生网络的召回特征抽取
    根据 user colossus 行为, 随机从历史行为中筛选出一些 item list 作为用户的表征

    参数
    ------
    ``` python
    gsu_twinsNet_feature_enricher(
         colossus_resp_attr='colossus_output',
         sample_items_num = 50,
         view_thresh = 10,
         parse_simItem_v2 = True,
         common_slots_name = "user_slots",
         common_signs_name = "user_signs",
         item_slots_name = "sample_slots",
         item_signs_name = "sample_signs",
       ) \
    ```
    """
    self._add_processor(CommonRecoGsuTwinsNetFeaEnricher(kwargs))
    return self

  def gsu_deep_match_with_cluster(self, **kwargs):
    """
    示例
    ------
    ``` python
    .gsu_deep_match_with_cluster(
      colossus_resp_attr="colossus_output",
      limit_num=50,
      timeous_ms=100,
      cluster_id_service_type='embedding_server',
      kess_service='grpc_GSUQueryServerLjw',
      shard_num=4,
      output_sign_attr="gsu_signs",
      output_slot_attr="gsu_slots",
      output_clutser_attr = "clutser_id",
      )
    ```
    """
    self._add_processor(CommonRecoGsuDeepMatchEnricher(kwargs))
    return self

  def gsu_with_tag(self, **kwargs):
    """
    CommonRecoGsuWithTagEnricher
    ------

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [string] 返回数目 attr

    `target_tag_attr`: [string] photo tag attr

    `shuffle`: [bool] gsu 是否随机打乱 action list, 默认为 False

    示例
    ------
    ``` python
    .gsu_with_tag(colossus_resp_attr='colossus_resp',
                  output_sign_attr='sign',
                  output_slot_attr='slot',
                  limit_num_attr='limit_num',
                  target_tag_attr='target_tag')
    ```
    """
    self._add_processor(CommonRecoGsuWithTagEnricher(kwargs))
    return self

  def gsu_with_multi_tag(self, **kwargs):
    """
    GsuWithMultiTagEnricher
    ------

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num`: [string] 返回数目

    `target_tag_attr`: [string] photo tag attr

    `additional_target_tag_attr` : [string] additional tags

    `sign_ids_list` : [int list] sign id for features

    `slot_ids_list` : [int list] slot id for features

    `shuffle`: [bool] gsu 是否随机打乱 action list, 默认为 False

    `keep_effective` : [bool] only keep effective view in colossus list

    示例
    ------
    ``` python
    .gsu_with_tag(colossus_resp_attr='colossus_resp',
                  output_sign_attr='tag_gsu_sign',
                  output_slot_attr='tag_gsu_slot',
                  limit_num_attr=50,
                  sign_ids_list = [1366, 1367, 1368, 1369, 1370],
                  slot_ids_list = [1366, 1367, 1368, 1369, 1370],
                  target_tag_attr='target_tag',
                  additional_target_tag_attr = "hetu_add_values",
                  keep_effective = True,
                  shuffle = True)
    ```
    """
    self._add_processor(GsuWithMultiTagEnricher(kwargs))
    return self

  def gsu_with_multi_tag_v2(self, **kwargs):
    """
    GsuWithMultiTagEnricher
    ------

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num`: [string] 返回数目

    `target_tag_attr`: [string] photo tag attr

    `additional_target_tag_attr` : [string] additional tags

    `sign_ids_list` : [int list] sign id for features

    `slot_ids_list` : [int list] slot id for features

    `shuffle`: [bool] gsu 是否随机打乱 action list, 默认为 False

    `keep_effective` : [bool] only keep effective view in colossus list

    示例
    ------
    ``` python
    .gsu_with_tag(colossus_resp_attr='colossus_resp',
                  output_sign_attr='tag_gsu_sign',
                  output_slot_attr='tag_gsu_slot',
                  limit_num_attr=50,
                  sign_ids_list = [1366, 1367, 1368, 1369, 1370],
                  slot_ids_list = [1366, 1367, 1368, 1369, 1370],
                  target_tag_attr='target_tag',
                  additional_target_tag_attr = "hetu_add_values",
                  keep_effective = True,
                  shuffle = True)
    ```
    """
    self._add_processor(GsuWithMultiTagV2Enricher(kwargs))
    return self

  def gsu_with_explore(self, **kwargs):
    """
    GsuWithExploreEnricher
    ------

    参数
    ------
    `colossus_aid_attr`: [string]

    `colossus_duration_attr`: [string]

    `colossus_play_attr`: [string]

    `colossus_time_attr`: [string]

    `colossus_label_attr`: [string]

    `colossus_tag_attr`: [string]

    `colossus_channel_attr`: [string]

    `limit_item_num`: [int] 返回数目, 默认 100
    """
    self._add_processor(GsuWithExploreEnricher(kwargs))
    return self

  def commertial_gsu_with_cluster(self, **kwargs):
    """
    CommonCommertialGsuWithClusterEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_aids_per_request`: [int] 单次 rpc 请求中的最大 aid 个数，为 0 代表不限制

    `use_append_cluster`  : [bool] 是否使用扩增类别, 默认为 False

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster string 值

    示例
    ------
    ``` python
    .commertial_gsu_with_aid_cluster(colossus_resp_attr='colossus_resp',
                           output_sign_attr='sign',
                           output_slot_attr='slot',
                           limit_num_attr='limit_num',
                           target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                           cluster_id_service_type='embedding_server',
                           kess_service='mmu_ad_aid_cluster_embedding_query_server',
                           shards=1,
                           use_append_cluster = false,
                           hetu_1k_cluster_json_value="")
    ```
    """
    self._add_processor(CommonCommertialGsuWithClusterEnricher(kwargs))
    return self

  def commertial_gsu_with_cluster_v2(self, **kwargs):
    """
    CommonCommertialGsuWithClusterV2Enricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_aids_per_request`: [int] 单次 rpc 请求中的最大 aid 个数，为 0 代表不限制

    `use_append_cluster`  : [bool] 是否使用扩增类别, 默认为 False

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster string 值

    示例
    ------
    ``` python
    .commertial_gsu_with_cluster_v2(colossus_resp_attr='colossus_resp',
                           output_sign_attr='sign',
                           output_slot_attr='slot',
                           limit_num_attr='limit_num',
                           target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                           cluster_id_service_type='embedding_server',
                           kess_service='mmu_ad_aid_cluster_embedding_query_server',
                           shards=1,
                           use_append_cluster = false,
                           hetu_1k_cluster_json_value="")
    ```
    """
    self._add_processor(CommonCommertialGsuWithClusterV2Enricher(kwargs))
    return self

  def commertial_gsu_with_aid_cluster_v2(self, **kwargs):
    """
    CommonCommertialGsuWithAidClusterEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_aids_per_request`: [int] 单次 rpc 请求中的最大 aid 个数，为 0 代表不限制

    `use_append_cluster`  : [bool] 是否使用扩增类别, 默认为 False

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster string 值

    示例
      ------
      ``` python
      .commertial_gsu_with_aid_cluster_v2(colossus_resp_attr='colossus_resp',
                          output_sign_attr='sign',
                          output_slot_attr='slot',
                          limit_num_attr='limit_num',
                          cluster_id_service_type='embedding_server',
                          kess_service='mmu_ad_aid_cluster_embedding_query_server',
                          shards=1,
                          use_append_cluster = false,
                          hetu_1k_cluster_json_value="")
      ```
      """
    self._add_processor(CommonCommertialGsuWithAidClusterEnricherV2(kwargs))
    return self

  def commertial_gsu_with_aid_cluster(self, **kwargs):
    """
    CommonCommertialGsuWithAidClusterEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_aids_per_request`: [int] 单次 rpc 请求中的最大 aid 个数，为 0 代表不限制

    `use_append_cluster`  : [bool] 是否使用扩增类别, 默认为 False

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster string 值

    示例
      ------
      ``` python
      .commertial_gsu_with_aid_cluster(colossus_resp_attr='colossus_resp',
                          output_sign_attr='sign',
                          output_slot_attr='slot',
                          limit_num_attr='limit_num',
                          target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                          cluster_id_service_type='embedding_server',
                          kess_service='mmu_ad_aid_cluster_embedding_query_server',
                          shards=1,
                          use_append_cluster = false,
                          hetu_1k_cluster_json_value="")
      ```
      """
    self._add_processor(CommonCommertialGsuWithAidClusterEnricher(kwargs))
    return self

  def live_gsu_with_aid_cluster(self, **kwargs):
    """
    LiveGsuWithAidClusterEnricher
    ------
    根据 aid 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `target_cluster_attr`: [string] item侧author的 cluster 属性

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_aids_per_request`: [int] 单次 rpc 请求中的最大 aid 个数，为 0 代表不限制

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster json object

    示例
      ------
      ``` python
      .live_gsu_with_aid_cluster(colossus_resp_attr='colossus_resp',
                          output_sign_attr='sign',
                          output_slot_attr='slot',
                          limit_num_attr='limit_num',
                          target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                          cluster_id_service_type='embedding_server',
                          kess_service='mmu_ad_aid_cluster_embedding_query_server',
                          shards=1,
                          hetu_1k_cluster_json_value="")
      ```
      """
    self._add_processor(LiveGsuWithAidClusterEnricher(kwargs))
    return self

  def gsu_with_cluster(self, **kwargs):
    """
    CommonRecoGsuWithClusterEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    之前的实现有移位 bug，如需修复，请增加 gflag: --gsu_with_cluster_duration_playtime_feature_bug_fix=true

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `cluster_id_service_type`: [string] 用于查询 photo 所属 cluster 的服务类型，目前支持 redis 和 embedding_server 两种，默认为 embedding_server

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    `slot_as_attr_name`: [bool] 用 slot 作为 key，signs 作为 int list 存储。默认为 False。

    `slot_as_attr_name_prefix`: [string] 用 slot 作为 key 时的前缀，默认无前缀。

    `mio_slots_id`: [string] 指定特征 slots。

    `slots_id`: [string] 指定特征 slots 或 shared slot。

    以下为 cluster_id_service_type == "redis" 时的配置

    `redis_cluster_name`: [string] 用于查询 photo 所属 cluster 的 redis 集群名

    以下为 cluster_id_service_type == "embedding_server" 时的配置

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_pids_per_request`: [int] 单次 rpc 请求中的最大 pid 个数，为 0 代表不限制

    示例
    ------
    ``` python
    .gsu_with_cluster(colossus_resp_attr='colossus_resp',
                      output_sign_attr='sign',
                      output_slot_attr='slot',
                      limit_num_attr='limit_num',
                      cluster_id_service_type='redis',
                      redis_cluster_name='mmuHeTuCluster')

    .gsu_with_cluster(colossus_resp_attr='colossus_resp',
                      output_sign_attr='sign',
                      output_slot_attr='slot',
                      limit_num_attr='limit_num',
                      cluster_id_service_type='embedding_server',
                      kess_service='mmu_hetu_cluster_id_query_server',
                      shards=4)
    ```
    """
    self._add_processor(CommonRecoGsuWithClusterEnricher(kwargs))
    return self

  def gsu_with_cluster_colossusv3(self, **kwargs):
    """
    CommonRecoGsuWithClusterEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `cluster_id_service_type`: [string] 用于查询 photo 所属 cluster 的服务类型，目前支持 redis 和 embedding_server 两种，默认为 embedding_server

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    `slot_as_attr_name`: [bool] 用 slot 作为 key，signs 作为 int list 存储。默认为 False。

    `slot_as_attr_name_prefix`: [string] 用 slot 作为 key 时的前缀，默认无前缀。

    `mio_slots_id`: [string] 指定特征 slots。

    `slots_id`: [string] 指定特征 slots 或 shared slot。

    以下为 cluster_id_service_type == "redis" 时的配置

    `redis_cluster_name`: [string] 用于查询 photo 所属 cluster 的 redis 集群名

    以下为 cluster_id_service_type == "embedding_server" 时的配置

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_pids_per_request`: [int] 单次 rpc 请求中的最大 pid 个数，为 0 代表不限制

    示例
    ------
    ``` python
    .gsu_with_cluster_colossusv3(colossus_resp_attr='colossus_resp',
                      output_sign_attr='sign',
                      output_slot_attr='slot',
                      limit_num_attr='limit_num',
                      cluster_id_service_type='redis',
                      redis_cluster_name='mmuHeTuCluster')

    .gsu_with_cluster_colossusv3(colossus_resp_attr='colossus_resp',
                      output_sign_attr='sign',
                      output_slot_attr='slot',
                      limit_num_attr='limit_num',
                      cluster_id_service_type='embedding_server',
                      kess_service='mmu_hetu_cluster_id_query_server',
                      shards=4)
    ```
    """
    self._add_processor(CommonRecoGsuWithClusterColossusV3Enricher(kwargs))
    return self

  def gsu_with_cluster_short_term_interests_enricher(self, **kwargs):
    """
    CommonRecoGsuWithClusterShortTermInterestsEnricher
    ------
    利用用户短期兴趣所属的 cluster 作为trigger进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `cluster_id_service_type`: [string] 用于查询 photo 所属 cluster 的服务类型，目前支持 redis 和 embedding_server 两种，默认为 embedding_server

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    `slot_as_attr_name`: [bool] 用 slot 作为 key，signs 作为 int list 存储。默认为 False。

    `slot_as_attr_name_prefix`: [string] 用 slot 作为 key 时的前缀，默认无前缀。

    `mio_slots_id`: [string] 指定特征 slots。

    `slots_id`: [string] 指定特征 slots 或 shared slot。

    以下为 cluster_id_service_type == "redis" 时的配置

    `redis_cluster_name`: [string] 用于查询 photo 所属 cluster 的 redis 集群名

    以下为 cluster_id_service_type == "embedding_server" 时的配置

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_pids_per_request`: [int] 单次 rpc 请求中的最大 pid 个数，为 0 代表不限制

    `view_threshold`: [int] 观看时长小于该阈值的近期行为不作为 trigger 召回长期行为

    `short_term_action_num`: [int] 近期行为 trigger 的数目

    `limit_num_from_single_trigger`: [int] 单个 trigger 召回的长期行为上限

    """
    self._add_processor(
        CommonRecoGsuWithClusterShortTermInterestsEnricher(kwargs))
    return self

  def gsu_tower_cluster(self, **kwargs):
    """
    CommonRecoGsuTowerClusterEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `cluster_id_service_type`: [string] 用于查询 photo 所属 cluster 的服务类型，目前支持 redis 和 embedding_server 两种，默认为 embedding_server

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    以下为 cluster_id_service_type == "redis" 时的配置

    `redis_cluster_name`: [string] 用于查询 photo 所属 cluster 的 redis 集群名

    以下为 cluster_id_service_type == "embedding_server" 时的配置

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_pids_per_request`: [int] 单次 rpc 请求中的最大 pid 个数，为 0 代表不限制

    示例
    ------
    ``` python
    .gsu_tower_cluster(colossus_resp_attr='colossus_resp',
                      output_sign_attr='sign',
                      output_slot_attr='slot',
                      limit_num_attr='limit_num',
                      cluster_id_service_type='redis',
                      redis_cluster_name='mmuHeTuCluster')

    .gsu_tower_cluster(colossus_resp_attr='colossus_resp',
                      output_sign_attr='sign',
                      output_slot_attr='slot',
                      limit_num_attr='limit_num',
                      cluster_id_service_type='embedding_server',
                      kess_service='mmu_hetu_cluster_id_query_server',
    ```
    """
    self._add_processor(CommonRecoGsuTowerClusterEnricher(kwargs))
    return self

  def gsu_with_entity_tag(self, **kwargs):
    """
    CommonRecoGsuWithEntityTagEnricher
    ------
    根据 photo 所属的 tag  进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `tag_id_service_type`: [string] 用于查询 photo 所属 tag 的服务类型，目前仅支持 embedding_server

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    以下为 tag_id_service_type == "embedding_server" 时的配置

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_tag`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_pids_per_request`: [int] 单次 rpc 请求中的最大 pid 个数，为 0 代表不限制

    `slot_as_attr_name`: [bool] 用 slot 作为 key，signs 作为 int list 存储。默认为 False。

    `slot_as_attr_name_prefix`: [string] 用 slot 作为 key 时的前缀，默认无前缀。

    `mio_slots_id`: [string] 指定特征 slots。

    `slots_id`: [string] 指定特征 slots 或 shared slot。

    示例
    ------
    ``` python
    .gsu_with_entity_tag(colossus_resp_attr='colossus_resp',
                      output_sign_attr='sign',
                      output_slot_attr='slot',
                      limit_num_attr='limit_num',
                      tag_id_service_type='embedding_server',
                      kess_service='mmu_entity_tag_id_query_server',
                      shards=4)
    ```
    """
    self._add_processor(CommonRecoGsuWithEntityTagEnricher(kwargs))
    return self

  def static_gsu_with_cluster(self, **kwargs):
    self._add_processor(CommonRecoStaticGsuWithClusterEnricher(kwargs))
    return self

  def gsu_with_cluster_follow(self, **kwargs):
    """
    CommonRecoGsuWithClusterEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征, 关注页专属。

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `cluster_id_service_type`: [string] 用于查询 photo 所属 cluster 的服务类型，目前支持 redis 和 embedding_server 两种，默认为 embedding_server

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    以下为 cluster_id_service_type == "redis" 时的配置

    `redis_cluster_name`: [string] 用于查询 photo 所属 cluster 的 redis 集群名

    以下为 cluster_id_service_type == "embedding_server" 时的配置

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_pids_per_request`: [int] 单次 rpc 请求中的最大 pid 个数，为 0 代表不限制

    示例
    ------
    ``` python
    .gsu_with_cluster(colossus_resp_attr='colossus_resp',
                      output_sign_attr='sign',
                      output_slot_attr='slot',
                      limit_num_attr='limit_num',
                      cluster_id_service_type='redis',
                      redis_cluster_name='mmuHeTuCluster')

    .gsu_with_cluster(colossus_resp_attr='colossus_resp',
                      output_sign_attr='sign',
                      output_slot_attr='slot',
                      limit_num_attr='limit_num',
                      cluster_id_service_type='embedding_server',
                      kess_service='mmu_hetu_cluster_id_query_server',
                      shards=4)
    ```
    """
    self._add_processor(CommonRecoGsuWithClusterFollowEnricher(kwargs))
    return self

  def follow_live_gsu_with_cluster(self, **kwargs):
    """
    CommonLiveGsuWithClusterFollowEnricher
    ------
    根据 live 所属的 cluster 进行 gsu 搜索并填充相似直播的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `target_cluster_attr`: [string] photo cluster attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    示例
    ------
    ``` python
    .follow_live_gsu_with_cluster(colossus_resp_attr='colossus_resp',
                                  output_sign_attr='sign',
                                  output_slot_attr='slot',
                                  limit_num_attr='limit_num',
                                  target_cluster_attr='lHetuCoverEmbeddingCluster')
    ```
    """
    self._add_processor(CommonLiveGsuWithClusterFollowEnricher(kwargs))
    return self

  def gsu_with_1w_cluster(self, **kwargs):
    """
    CommonRecoGsuWith1wClusterEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_pids_per_request`: [int] 单次 rpc 请求中的最大 pid 个数，为 0 代表不限制

    `hetu_1w_cluster_json_value`: [string] hetu 1w cluster string 值

    `reverse_distance_index`: [bool] hetu cluster json 文件需不需要 reverse，false 表示不需要，true 表示需要，默认是 false，表示从后往前检索。

    `slot_as_attr_name`: [bool] 用 slot 作为 key，signs 作为 int list 存储。默认为 False。

    `slot_as_attr_name_prefix`: [string] 用 slot 作为 key 时的前缀，默认无前缀。

    `mio_slots_id`: [string] 指定特征 slots。

    `slots_id`: [string] 指定特征 slots 或 shared slot。

    示例
    ------
    ``` python
    .gsu_with_1w_cluster(colossus_resp_attr='colossus_resp',
                         output_sign_attr='sign',
                         output_slot_attr='slot',
                         limit_num_attr='limit_num',
                         cluster_id_service_type='embedding_server',
                         kess_service='mmu_hetu_cluster_id_query_server',
                         shards=4,
                         hetu_1w_cluster_json_value="")
    ```
    """
    self._add_processor(CommonRecoGsuWith1wClusterEnricher(kwargs))
    return self

  def gsu_with_1w_cluster_merchant(self, **kwargs):
    """
    CommonRecoGsuWith1wClusterEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_pids_per_request`: [int] 单次 rpc 请求中的最大 pid 个数，为 0 代表不限制

    `use_append_cluster`  : [bool] 是否使用扩增类别, 默认为 False

    `hetu_1w_cluster_json_value`: [string] hetu 1w cluster string 值

    示例
    ------
    ``` python
    .CommonRecoGsuWith1wClusterMerchantEnricher(colossus_resp_attr='colossus_resp',
                        output_sign_attr='sign',
                        output_slot_attr='slot',
                        limit_num_attr='limit_num',
                        cluster_id_service_type='embedding_server',
                        kess_service='mmu_hetu_cluster_id_query_server',
                        shards=4,
                        use_append_cluster = false,
                        hetu_1w_cluster_json_value="")
    ```
    """
    self._add_processor(CommonRecoGsuWith1wClusterMerchantEnricher(kwargs))
    return self

  def merchant_gsu_with_commodity_cluster(self, **kwargs):
    """
    CommonMerchantGsuWithCommondityClusterEnricher
    ------
    根据 商品 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `target_good_list_attr`: [string] item侧使用的 item commodity list 属性

    `target_good_cluster_list_attr`: [string] item侧使用的 item commodity cluster list 属性

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `spu_kess_service`: [string] spu 服务的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `spu_shards`: [int] spu 服务的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_pids_per_request`: [int] 单次 rpc 请求中的最大 pid 个数，为 0 代表不限制

    `top_n_commondity_num`  : [int] 用直播商品列表的前n个来检索, 默认为 1

    `hetu_1w_cluster_json_value`: [string] hetu 1w cluster string 值

    示例
    ------
    ``` python
    .CommonMerchantGsuWithCommodityClusterEnricher(colossus_resp_attr='colossus_resp',
                        output_sign_attr='sign',
                        output_slot_attr='slot',
                        limit_num_attr='limit_num',
                        top_n_commondity_num = 'cartItemList',
                        cluster_id_service_type='embedding_server',
                        kess_service='mmu_hetu_cluster_id_query_server',
                        shards=1,
                        top_n_commondity_num = 1,
                        price_discret = [10, 20, 30],
                        hetu_1w_cluster_json_value="")
    ```
    """
    self._add_processor(
        CommonMerchantGsuWithCommodityClusterEnricher(kwargs))
    return self

  def merchant_gsu_with_photo_cluster(self, **kwargs):
    """
    CommonMerchantGsuWithPhotoClusterEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_pids_per_request`: [int] 单次 rpc 请求中的最大 pid 个数，为 0 代表不限制

    `use_append_cluster`  : [bool] 是否使用扩增类别, 默认为 False

    `hetu_1w_cluster_json_value`: [string] hetu 1w cluster string 值

    示例
    ------
    ``` python
    .CommonMerchantGsuWithPhotoClusterEnricher(colossus_resp_attr='colossus_resp',
                        output_sign_attr='sign',
                        output_slot_attr='slot',
                        limit_num_attr='limit_num',
                        target_cluster_attr = 'sEshopLive2mSliceUniSpaceClusterId',
                        cluster_id_service_type='embedding_server',
                        kess_service='mmu_hetu_cluster_id_query_server',
                        shards=1,
                        use_append_cluster = false,
                        hetu_1w_cluster_json_value="")
    ```
    """
    self._add_processor(CommonMerchantGsuWithPhotoClusterEnricher(kwargs))
    return self

  def merchant_gsu_with_photo_cluster_v2(self, **kwargs):
    """
    CommonMerchantGsuWithPhotoClusterV2Enricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_pids_per_request`: [int] 单次 rpc 请求中的最大 pid 个数，为 0 代表不限制

    `use_append_cluster`  : [bool] 是否使用扩增类别, 默认为 False

    `hetu_1w_cluster_json_value`: [string] hetu 1w cluster string 值

    示例
    ------
    ``` python
    .CommonMerchantGsuWithPhotoClusterV2Enricher(colossus_resp_attr='colossus_resp',
                        output_sign_attr='sign',
                        output_slot_attr='slot',
                        limit_num_attr='limit_num',
                        target_cluster_attr = 'sEshopLive2mSliceUniSpaceClusterId',
                        cluster_id_service_type='embedding_server',
                        kess_service='mmu_hetu_cluster_id_query_server',
                        shards=1,
                        use_append_cluster = false,
                        hetu_1w_cluster_json_value="")
    ```
    """
    self._add_processor(
        CommonMerchantGsuWithPhotoClusterV2Enricher(kwargs))
    return self

  def merchant_gsu_with_photo_cluster_extra(self, **kwargs):
    """
    CommonMerchantGsuWithPhotoClusterExtraEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_pids_per_request`: [int] 单次 rpc 请求中的最大 pid 个数，为 0 代表不限制

    `use_append_cluster`  : [bool] 是否使用扩增类别, 默认为 False

    `hetu_1w_cluster_json_value`: [string] hetu 1w cluster string 值

    示例
    ------
    ``` python
    .CommonMerchantGsuWithPhotoClusterExtraEnricher(colossus_resp_attr='colossus_resp',
                        output_sign_attr='sign',
                        output_slot_attr='slot',
                        limit_num_attr='limit_num',
                        target_cluster_attr = 'sEshopLive2mSliceUniSpaceClusterId',
                        cluster_id_service_type='embedding_server',
                        kess_service='mmu_hetu_cluster_id_query_server',
                        shards=1,
                        use_append_cluster = false,
                        hetu_1w_cluster_json_value="")
    ```
    """
    self._add_processor(
        CommonMerchantGsuWithPhotoClusterExtraEnricher(kwargs))
    return self

  def extract_user_bias_by_duration(self, **kwargs):
    """
    CommonRecoUserBiasByDurationEnricher
    ------
    根据 photo 所属的 duration 统计用户历史行为序列特征,并填充 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name, 仅当 slot_as_attr_name 为 False 时生效.

    `output_slot_attr`: [string] slot 输出 attr name, 仅当 slot_as_attr_name 为 False 时生效.

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `slot_as_attr_name`: [bool] 用 slot 作为 key，signs 作为 int list 存储。默认为 False。

    `slot_as_attr_name_prefix`: [string] 用 slot 作为 key 时的前缀，默认无前缀。

    `mio_slots_id`: [list] 指定特征slots。

    `slots_id`: [list] 指定特征slots或shared slot。

    示例
    ------
    ``` python
    .extract_user_bias_by_duration(colossus_resp_attr='colossus_resp',
                                   slot_as_attr_name=True)
    ```
    """
    self._add_processor(CommonRecoUserBiasByDurationEnricher(kwargs))
    return self

  def extract_user_bias_by_cluster(self, **kwargs):
    """
    CommonRecoUserBiasByClusterEnricher
    ------
    根据 photo 所属的 cluster 统计用户历史行为序列特征,并填充 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] userbias cluster sign 输出 attr name, 仅当 slot_as_attr_name 为 False 时生效

    `output_slot_attr`: [string] userbias cluster slot 输出 attr name, 仅当 slot_as_attr_name 为 False 时生效

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_pids_per_request`: [int] 单次 rpc 请求中的最大 pid 个数，为 0 代表不限制

    `hetu_1w_cluster_json_value`: [string] hetu 1w cluster string 值

    `slot_as_attr_name`: [bool] 用 slot 作为 key，signs 作为 int list 存储。默认为 False。

    `slot_as_attr_name_prefix`: [string] 用 slot 作为 key 时的前缀，默认无前缀。

    `mio_slots_id`: [string] 指定特征slots。

    `slots_id`: [string] 指定特征slots或shared slot。

   示例
    ------
    ``` python
    .extract_user_bias_by_cluster(
                         colossus_resp_attr='colossus_output',
                         output_userbias_cluster_sign_attr="userbias_cluster_signs",
                         output_userbias_cluster_slot_attr="userbias_cluster_slots",
                         timeout_ms=3600 * 1000, # never timeout
                         cluster_id_service_type='embedding_server',
                         kess_service='mmu_hetu_cluster_id2_query_server',
                         shards=4,
                         hetu_1w_cluster_json_value=load_cluster_index_1w("")
    ```
    """
    self._add_processor(CommonRecoUserBiasByClusterEnricher(kwargs))
    return self

  def live_gsu_with_cluster(self, **kwargs):
    """
    CommonLiveGsuWithClusterEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .live_gsu_with_cluster(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                      )
    ```
    """
    self._add_processor(CommonLiveGsuWithClusterEnricher(kwargs))
    return self

  def live_gsu_with_aid(self, **kwargs):
    """
    CommonLiveGsuWithClusterAid
    ------
    根据 photo 所属的 author_id 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    `use_LiveItemV2`: [bool] 是否使用LiveItemV2 (适配 grpc_colossusLiveItemV3)

    `use_gift_info`: [bool] 是否使用营收字段 (reward / reward_count)

    示例
    ------
    ``` python
    .live_gsu_with_aid(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'aId',
                      )
    ```
    """
    self._add_processor(CommonLiveGsuWithClusterEnricherAid(kwargs))
    return self
  
  def live_gsu_with_aid_v2(self, **kwargs):
    """
    CommonLiveGsuWithClusterAidV2
    ------
    根据 photo 所属的 author_id 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .live_gsu_with_aid_v2(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'aId',
                      )
    ```
    """
    self._add_processor(CommonLiveGsuWithClusterEnricherAidV2(kwargs))
    return self


  def live_gsu_with_kgnn_aids(self, **kwargs):
    """
    CommonLiveGsuWithAidEnricher
    ------
    根据 photo author 扩展的 author_ids 进行 gsu 搜索并填充相同作者直播的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `target_aids_attr`: [int] target aid 扩展的 aids

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .live_gsu_with_kgnn_aids(colossus_resp_attr='live_colossus_resp',
                             output_sign_attr='live_gsu_sign',
                             output_slot_attr='live_gsu_slot',
                             limit_num_attr='live_limit_num',
                             target_aids_attr = 'kgnn_aids',
                            )
    ```
    """
    self._add_processor(CommonLiveGsuWithAidEnricher(kwargs))
    return self

  def live_photo_gsu_with_aid(self, **kwargs):
    """
    CommonLiveGsuWithAidPhotoColossusEnricher
    ------
    根据 photo 所属的 author_id 进行 gsu 搜索并填充结果视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_aid_attr`: [string] item侧使用的 item aid  属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .live_photo_gsu_with_aid(colossus_resp_attr='colossus_resp',
                      output_sign_attr='gsu_sign',
                      output_slot_attr='gsu_slot',
                      limit_num_attr='limit_num',
                      target_aid_attr='aId',
                      )
    ```
    """
    self._add_processor(CommonLiveGsuWithAidPhotoColossusEnricher(kwargs))
    return self

  def live_gsu_with_cluster_v4(self, **kwargs):
    """
    CommonLiveGsuWithClusterEnricherV4
    ------
    上游 colossus 必须配置 parse_pb_=False, 根据 photo 所属的 cluster_id 进行 gsu 搜索并填充相似直播的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    `reward_item_only`: [bool] 是否只保留打赏的item，默认false
    
    `filter_time_flag`: [bool] 是否根据时间戳过滤，默认true

    `filter_channels`: [bool] 根据channel进行过滤，以英文逗号分隔，比如: 1,2,3，默认为空不过滤

    示例
    ------
    ``` python
    .live_gsu_with_cluster_v4(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                      )
    ```
    """
    self._add_processor(CommonLiveGsuWithClusterEnricherV4(kwargs))
    return self


  def gsu_with_time_clock(self, **kwargs):
    """
    GsuWithTimeClockEnricher
    ------
    上游使用新版 colossus , 根据 photo 所属的 time_range 进行 gsu 搜索的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `limit_num`: [int] 返回数目 attr

    `limit_min_play_time`: [int] 限制序列内的最小播放时长

    `limit_max_play_time`: [int] 限制序列内的最大播放时长

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .gsu_with_time_clock(
        limit_num=50,
        limit_min_play_time=10,
        limit_max_play_time=59,
        output_sign_attr="living_gsu_signs_ev",
        output_slot_attr="living_gsu_slots_ev",
        slots_id=[21000, 21001, 21002, 21003, 21004, 21005],
        mio_slots_id=[21000, 21001, 21002, 21003, 21004, 21005],
    )
    ```
    """
    self._add_processor(GsuWithTimeClockEnricher(kwargs))
    return self


  def gsu_with_living(self, **kwargs):
    """
    GsuWithLivingEnricher
    ------
    上游 colossus 必须配置 parse_pb_=False, 根据 photo 所属的 cluster_id 进行 gsu 搜索并填充相似直播的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `living_pid_list_attr`: [string] living 曝光 pid 序列输入的 attr

    `living_ts_list_attr`: [string] living 曝光时间序列输入的 attr

    `limit_num`: [int] 返回数目 attr

    `limit_min_play_time`: [int] 限制序列内的最小播放时长

    `limit_max_play_time`: [int] 限制序列内的最大播放时长

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_aids_per_request`: [int] 单次 rpc 请求中的最大 aid 个数，为 0 代表不限制

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    示例
    ------
    ``` python
    .gsu_with_living(
        colossus_resp_attr='photo_colossus_output',
        living_pid_list_attr='uStandardLivingPhotoShowIDSeqPhotoIDList',
        living_ts_list_attr='uStandardLivingPhotoShowIDSeqTimeList',
        limit_num=50,
        limit_min_play_time=10,
        limit_max_play_time=59,
        output_sign_attr="living_gsu_signs_ev",
        output_slot_attr="living_gsu_slots_ev",
        cluster_id_service_type="embedding_server",
        #kess_service='kws-kuaishou-full-rank-embedding-mmu-hetu-cluster-id-long',
        kess_service='mmu_hetu_cluster_id_query_server_nearby_follow_online',
        shards=4,
        timeout_ms=10 * 5000,
        slots_id=[21000, 21001, 21002, 21003, 21004, 21005],
        mio_slots_id=[21000, 21001, 21002, 21003, 21004, 21005],
    )
    ```
    """
    self._add_processor(GsuWithLivingEnricher(kwargs))
    return self


  def gsu_with_living_v2(self, **kwargs):
    """
    GsuWithLivingV2Enricher, 根据 living 曝光序列，返回短播序列(<=3s)和有效播放序列(>=10s)
    ------
    上游 colossus 必须配置 parse_pb_=False, 根据 photo 所属的 cluster_id 进行 gsu 搜索并填充相似直播的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `living_pid_list_attr`: [string] living 曝光 pid 序列输入的 attr

    `living_ts_list_attr`: [string] living 曝光时间序列输入的 attr

    `limit_num`: [int] 返回数目 attr

    `limit_min_play_time`: [int] 限制序列内的最小播放时长

    `limit_max_play_time`: [int] 限制序列内的最大播放时长

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_aids_per_request`: [int] 单次 rpc 请求中的最大 aid 个数，为 0 代表不限制

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    示例
    ------
    ``` python
    .gsu_with_living_v2(
        colossus_resp_attr='photo_colossus_output',
        living_pid_list_attr='uStandardLivingPhotoShowIDSeqPhotoIDList',
        living_ts_list_attr='uStandardLivingPhotoShowIDSeqTimeList',
        limit_num=50,
        limit_min_play_time=10,
        limit_max_play_time=59,
        output_sign_attr="living_gsu_signs_ev",
        output_slot_attr="living_gsu_slots_ev",
        cluster_id_service_type="embedding_server",
        #kess_service='kws-kuaishou-full-rank-embedding-mmu-hetu-cluster-id-long',
        kess_service='mmu_hetu_cluster_id_query_server_nearby_follow_online',
        shards=4,
        timeout_ms=10 * 5000,
        slots_id=[21000, 21001, 21002, 21003, 21004, 21005, 31000, 31001, 31002, 31003, 31004, 31005],
        mio_slots_id=[21000, 21001, 21002, 21003, 21004, 21005, 31000, 31001, 31002, 31003, 31004, 31005],
    )
    ```
    """
    self._add_processor(GsuWithLivingV2Enricher(kwargs))
    return self

  def live_gsu_longterm_item_derive(self, **kwargs):
    """
    CommonLiveGsuLongtermItemDeriveEnricher
    ------
    根据 live 所属的 cluster_id/tag 进行 gsu 搜索并填充top/latest的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 长度限定 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `cal_scope`: [int] cal attr_id by get_top-0 get_latest-1

    `cal_basis`: [int] cal attr type, cluster_id-0 hetu_tag-1

    `duration_limit`: [int] 播放时长限定 默认0

    `k_num`: [int] top/latest k attr_id 默认10

    示例
    ------
    ``` python
    .live_gsu_with_cluster_v4(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      )
    ```
    """
    self._add_processor(CommonLiveGsuLongtermItemDeriveEnricher(kwargs))
    return self

  def live_gsu_with_reward(self, **kwargs):
    """
    CommonLiveGsuWithClusterEnricherReward
    ------
    根据 photo 所属的 author_id 进行 gsu 搜索并填充相似视频的 sign/slot 特征，加入营收相关特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .live_gsu_with_reward(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'aId',
                      )
    ```
    """
    self._add_processor(CommonLiveGsuWithClusterEnricherReward(kwargs))
    return self

  def live_gsu_with_cluster_v2(self, **kwargs):
    """
    CommonLiveGsuWithClusterV2Enricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, colossus_resp的proto是LiveItemV2

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    `gift_item_only`: [bool] 是否只输出打赏过的 item，default:false

    `parse_to_pb`: [bool] colossue response 是否解析为 protobuf, default:true

    示例
    ------
    ``` python
    .live_gsu_with_cluster_v2(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                      )
    ```
    """
    self._add_processor(CommonLiveGsuWithClusterV2Enricher(kwargs))
    return self

  def live_gsu_with_cluster_v3(self, **kwargs):
    """
    CommonLiveGsuWithClusterV3Enricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, colossus_resp的proto是LiveItemV2

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    `gift_item_only`: [bool] 是否只输出打赏过的 item，default:false

    `limit_play_time`: [int] 最少播放时间，低于该值将被过滤

    示例
    ------
    ``` python
    .live_gsu_with_cluster_v3(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                      limit_play_time = 10,
                      )
    ```
    """
    self._add_processor(CommonLiveGsuWithClusterV3Enricher(kwargs))
    return self

  def live_prerank_gsu_with_cluster_v3(self, **kwargs):
    """
    CommonLivePrerankGsuWithClusterV3Enricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, colossus_resp的proto是LiveItemV2

    `limit_num_attr`: [int] 返回数目 attr

    `slot_as_attr_name`: [bool] 用 slot 作为 key, signs 作为 int list 存储。默认为 False。

    `slot_as_attr_name_prefix`: [string] 用 slot 作为 key 时的前缀，默认无前缀。

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    `gift_item_only`: [bool] 是否只输出打赏过的 item, default:false

    `limit_play_time`: [int] 最少播放时间，低于该值将被过滤

    示例
    ------
    ``` python
    .live_prerank_gsu_with_cluster_v3(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      slot_as_attr_name=false,
                      slot_as_attr_name_prefix='',
                      target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                      limit_play_time = 10,
                      )
    ```
    """
    self._add_processor(CommonLivePrerankGsuWithClusterV3Enricher(kwargs))
    return self

  def live_gsu_aid2aid(self, **kwargs):
    """
    MerchantGsuAid2AidEnricher
    ------
    根据 photo 所属的 aid 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, colossus_resp的proto是LiveItemV2

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_attr`: [string] item侧使用的 aid 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    `parse_to_pb`: [bool] colossue response 是否解析为 protobuf, default:true

    示例
    ------
    ``` python
    .live_gsu_aid2aid(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_attr = 'aId',
                      slots_id=[1000, 1001, 1002, 1003, 1004],
                      mio_slots_id=[1000, 1001, 1002, 1003, 1004]
                      )
    ```
    """
    self._add_processor(MerchantGsuAid2AidEnricher(kwargs))
    return self

  def live_gsu_1pp(self, **kwargs):
    """
    MerchantGsu1ppEnricher
    ------
    根据 photo 所属的 aid 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, colossus_resp的proto是LiveItemV2

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_attr`: [string] item侧使用的 aid 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .live_gsu_1pp(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_attr = 'aId',
                      slots_id=[1000, 1001, 1002, 1003, 1004],
                      mio_slots_id=[1000, 1001, 1002, 1003, 1004]
                      )
    ```
    """
    self._add_processor(MerchantGsu1ppEnricher(kwargs))
    return self

  def live_gsu_tower_sort_post(self, **kwargs):
    """
    LiveGsuTowerSortPostEnricher
    ------
    将 common_attr中的 `*std::vector<double>` 转化为各个item对应的 `vector<double>`，排序
    并返回最大的n个，和对应的 output_sign 和 output_slots

    参数
    ------
    `common_distance_ptr_attr` : [string] 待转换的 common attr

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `tag_attr` : [string] pid对应的 tag attr name

    `optag_attr` : [string] pid对应的 optag attr name

    `play_time_attr` : [string] pid对应的 play time attr name

    `page_type_attr` : [string] pid对应的 page_type attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    示例
    ------
    ``` python
    .gsu_tower_sort_post(common_distance_ptr_attr="live_distance",
                         target_item={"is_target_photo_embedding_hit": 1},
                         colossus_pid_attr="live_colossus_pid",
                         author_id_attr="live_colossus_aid",
                         tag_attr="live_colossus_tag",
                         optag_attr="live_colossus_optag",
                         play_time_attr="live_colossus_play_time",
                         page_type_attr="live_colossus_page_type",
                         timestamp_attr="live_colossus_timestamp",
                         output_sign_attr="live_gsu_signs",
                         output_slot_attr="live_gsu_slots",
                         slots_id=[26, 128, 375, 373 , 374, 376, 377],
                         mio_slots_id=[371, 372, 375, 373, 374, 376, 377],
                         top_n=50)
    ```
    """
    self._add_processor(LiveGsuTowerSortPostEnricher(kwargs))
    return self

  def live_gsu_tower_multi_sort_post(self, **kwargs):
    """
    LiveGsuTowerMultiSortPostEnricher
    ------
    对 common_attr 中的多个 `std::vector<float>*` 进行多队列排序，每个队列返回若干 item，最后返回抽取的
    output_sign 和 output_slots

    参数
    ------
    `common_distance_ptr_attrs` : [list] 输入的 common_attr 列表。

    `common_distance_filter_attrs` : [list] 与 common_distance_ptr_attrs 对应的 item attr，用于标记请求 DPCalc Server 时该 item 的 embedding 是否缺失。

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `tag_attr` : [string] pid对应的tag attr name

    `play_time_attr` : [string] pid对应的play time attr name

    `duration_attr` : [string] pid对应的duration attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `output_sign_attr` : [string] 输出的 sign 写入到的 attr name

    `output_slot_attr` : [string] 输出的 slot 写入到的 attr name

    `channel_sizes`: [list][动态参数] 每个队列输出的 item 数量。

    `channel_weights`: [list][动态参数] 每个队列对应的权重，size 为 common_distance_ptr_attrs 的 size 乘以 channel_sizes 的 size。

    `output_item_colossus_pid_attr`: [string] 输出的 pid list 存入的 item attr name

    示例
    ------
    ``` python
    .gsu_tower_sort_post(common_distance_ptr_attrs=["aid_distance", "pid_distance", "mmu_distance"],
                         common_distance_filter_attrs=["aid_embedding", "pid_embedding", "mmu_embedding"]
                         colossus_pid_attr="colossus_pid",
                         author_id_attr="aid_attr",
                         tag_attr="tag_attr",
                         play_time_attr="play_time_attr",
                         duration_attr="duration_attr",
                         timestamp_attr="ts_attr",
                         output_sign_attr="output_sign",
                         output_slot_attr="output_slot",
                         channel_sizes=[50, 50],
                         channel_weights=[1, 1, 0, 0, 0, 1])
    ```
    """
    self._add_processor(LiveGsuTowerMultiSortPostEnricher(kwargs))
    return self

  def photo_gsu_aid2aid(self, **kwargs):
    """
    MerchantGsuAid2AidPhotoEnricher
    ------
    根据 photo 所属的 aid 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, colossus_resp的proto是LiveItemV2

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_attr`: [string] item侧使用的 aid 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .photo_gsu_aid2aid(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_attr = 'aId',
                      slots_id=[1000, 1001, 1002, 1003, 1004],
                      mio_slots_id=[1000, 1001, 1002, 1003, 1004]
                      )
    ```
    """
    self._add_processor(MerchantGsuAid2AidPhotoEnricher(kwargs))
    return self

  def live_long_short_gsu_with_cluster(self, **kwargs):
    """
    CommonLiveLongShortGsuWithClusterEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `mio_slots_id`: [int_list] 选配项 区分 embedding 特征的 slot 列表, 默认值 [26, 128, 373, 374, 375, 26, 128, 373, 374, 375]

    `slots_id`: [int_list] 选配项 sign 对应的 slot 列表, 默认值 [371, 372, 373, 374, 375, 361, 362, 363, 364, 365]

    示例
    ------
    ``` python
    .live_long_short_gsu_with_cluster(colossus_resp_attr='colossus_resp',
                      output_sign_attr='gsu_sign',
                      output_slot_attr='gsu_slot',
                      output_last_sign_attr='last_sign',
                      output_last_slot_attr='last_slot',
                      limit_num_attr='limit_num',
                      target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                      mio_slots_id = [],
                      slots_id = []
                      )
    ```
    """
    self._add_processor(CommonLiveLongShortGsuWithClusterEnricher(kwargs))
    return self

  def photo_long_short_gsu_with_cluster(self, **kwargs):
    """
    CommonRecoLongShortGsuWithClusterEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `mio_slots_id`: [int_list] 选配项 区分 embedding 特征的 slot 列表, 默认值 [32, 171, 803, 804, 805, 32, 171, 803, 804, 805]

    `slots_id`: [int_list] 选配项 sign 对应的 slot 列表, 默认值 [801, 802, 803, 804, 805, 806, 807, 808, 809, 810]

    示例
    ------
    ``` python
    .photo_long_short_gsu_with_cluster(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      output_last_sign_attr='live_last_sign',
                      output_last_slot_attr='live_last_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                      mio_slots_id = [],
                      slots_id = []
                      )
    ```
    """
    self._add_processor(CommonRecoLongShortGsuWithClusterEnricher(kwargs))
    return self

  def gsu_with_tag_and_cluster(self, **kwargs):
    """
    CommonRecoGsuWithTagAndClusterEnricher
    ------
    根据 photo 所属的 tag 和 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `max_pids_per_request`: [int] 单次 rpc 请求中的最大 pid 个数，为 0 代表不限制

    `tag_kess_service`: [string] tag embedding_server 的 kess 服务名

    `tag_shards`: [int] tag embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `tag_kess_cluster`: [string] tag embedding_server 的 kess 集群名，默认为 PRODUCTION

    `cluster_kess_service`: [string] cluster embedding_server 的 kess 服务名

    `cluster_shards`: [int] cluster embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `cluster_kess_cluster`: [string] cluster embedding_server 的 kess 集群名，默认为 PRODUCTION

    示例
    ------
    ``` python
    .gsu_with_tag_and_cluster(colossus_resp_attr='colossus_resp',
                      output_sign_attr='sign',
                      output_slot_attr='slot',
                      limit_num_attr='limit_num',
                      tag_kess_service='brpc_shmMmuHetuTagIds',
                      tag_shards=4,
                      cluster_kess_service='mmu_hetu_cluster_id_query_server',
                      cluster_shards=4)
    ```
    """
    self._add_processor(CommonRecoGsuWithTagAndClusterEnricher(kwargs))
    return self

  def kwaipro_gsu_with_tag(self, **kwargs):
    """
    KwaiproGsuWithTagEnricher
    ------
    调用 colossus 存储服务，抽取相关 photo 作为特征输出到 common attr

    参数配置
    ------
    `service_name` : [string] kess name

    `shard_number` : [int] shard_number

    `output_sign_attr` : [string] sign 输出 attr name

    `output_slot_attr` : [string] slot 输出 attr name

    `limit_num_attr`   : [string] 返回数目 attr

    `target_tag_attr`  : [string] photo tag attr

    `bucket_attr` : [string] bucket attr

    `shuffle`  : [bool] gsu 是否随机打乱 action list, 默认为 False

    `use_cluster_id` : [bool] 是否使用 cluster id, 默认为 False; 初始化时读取该参数, 为 True 时会使 use_mmu_tag 参数失效

    `use_mmu_tag` : [bool] [动态参数] 是否使用 mmu tag，反之会使用人工 tag，默认为 True

    调用示例
    ------
    ``` python
    .kwaipro_gsu_with_tag(
      service_name='grpc_colossusKwaiPro',
      shard_number=33,
      output_sign_attr="gsuSign",
      output_slot_attr="gsuSlot",
      limit_num_attr="limit",
      target_tag_attr="tag",
      bucket_attr="bucket")
    ```
    """
    self._add_processor(KwaiproGsuWithTagEnricher(kwargs))
    return self

  def kwaipro_gsu_get_rough_tag(self, **kwargs):
    """
    KwaiproGsuRoughTagEnricher
    ------
    粗排调用 colossus 存储服务，抽取相关 photo 作为特征输出到 common attr

    参数配置
    ------
    `service_name`: [string] kess name

    `shard_number`: [int] shard_number

    `output_sign_common_attr`: [string] sign 输出 attr name

    `output_slot_common_attr`: [string] slot 输出 attr name

    `limit_num_attr`: [string] 返回数目 attr

    `target_tag_common_attrs`: [list] photo tag attr

    `bucket_common_attr`: [string] bucket attr

    `shuffle`: [bool] gsu 是否随机打乱 action list, 默认为 False

    `use_mmu_tag`: [bool] [动态参数] 是否使用 mmu tag，反之会使用人工 tag，默认为 True

    调用示例
    ------
    ``` python
    .kwaipro_gsu_get_rough_tag(
      service_name='grpc_colossusKwaiPro',
      shard_number=33,
      output_sign_common_attr="gsuSign",
      output_slot_common_attr="gsuSlot",
      limit_num_attr="limit",
      target_tag_common_attrs=["tag"],
      bucket_common_attr="bucket")
    ```
    """
    self._add_processor(KwaiproGsuRoughTagEnricher(kwargs))
    return self

  def gsu_tower_tag(self, **kwargs):
    """
    CommonRecoGsuTowerTagEnricher
    ------

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [colossus_resp] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `target_tag_attr`: [int] photo tag attr

    示例
    ------
    ``` python
    .gsu_tower_tag(colossus_resp='colossus_resp')
    ```
    """
    self._add_processor(CommonRecoGsuTowerTagEnricher(kwargs))
    return self

  def gsu_tower_recent_photos(self, **kwargs):
    """
    CommonRecoGsuRecentActionsEnricher
    ------

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [colossus_resp] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `play_time_threshold_attr`: [int] 播放时长阈值 attr

    `action_list_attr`: [string] action_filter_str


    示例
    ------
    ``` python
    .gsu_tower_recent_photos(colossus_resp='colossus_resp',
    limit_num_attr='limit_num',
    play_time_threshold_attr='play_time_threshold',
    output_sign_attr="gsu_signs",
    output_slot_attr="gsu_slots",
    )
    ```
    """
    self._add_processor(CommonRecoGsuRecentActionsEnricher(kwargs))
    return self

  def gsu_tower_recent_photos_col2(self, **kwargs):
    """
    CommonRecoGsuRecentActionsCol2Enricher
    ------

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [colossus_resp] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `play_time_threshold_attr`: [int] 播放时长阈值 attr


    示例
    ------
    ``` python
    .gsu_tower_recent_photos_col2(colossus_resp='colossus_resp',
    limit_num_attr='limit_num',
    play_time_threshold_attr='play_time_threshold',
    action_list_attr='action_list',
    output_sign_attr="gsu_signs",
    output_slot_attr="gsu_slots",
    )
    ```
    """
    self._add_processor(CommonRecoGsuRecentActionsCol2Enricher(kwargs))
    return self

  def merchant_live_gsu_with_tag(self, **kwargs):
    """
    MerchantLiveGsuWithTagEnricher
    ------

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [colossus_resp] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `kess_service`: [string] 电商 tag embedding server 服务名字

    `shards`: [int] 电商 tag embedding server 分片数目

    ``` python
    .colossus(
      service_name='grpc_colossusLiveItem',
      output_attr='colossus_resp',
      client_type='common_item_client') \
    .enrich_attr_by_lua(
      export_common_attr=["limit_num"],
      function_for_common="calculate",
      lua_script=f'''
        function calculate()
          return 50
        end
      ''') \
    .merchant_live_gsu_with_tag(
      colossus_resp_attr='colossus_resp',
      output_sign_attr='sign',
      output_slot_attr='slot',
      limit_num_attr='limit_num',
      kess_service='MerchantLiveModelFeatureV1',
      shards=2)
    ```
    """
    self._add_processor(MerchantLiveGsuWithTagEnricher(kwargs))
    return self

  def merchant_gsu_with_live_clip_cluster(self, **kwargs):
    """
    MerchantGsuWithLiveClipClusterEnricher
    ------

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `live_colossus_resp_attr`: [colossus_resp] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `kess_service`: [string] 电商直播切片cluster embedding server 服务名字

    `shards`: [int] 电商 tag embedding server 分片数目

    `merchant_cluster_json_value`: [string]

    ``` python
    .colossus(
      service_name='grpc_colossusLiveItemV3',
      output_attr='colossus_resp',
      client_type='common_item_client') \
    .enrich_attr_by_lua(
      export_common_attr=["limit_num"],
      function_for_common="calculate",
      lua_script=f'''
        function calculate()
          return 50
        end
      ''') \
    .merchant_gsu_with_live_clip_cluster(
      live_colossus_resp_attr='colossus_resp',
      output_sign_attr='sign',
      output_slot_attr='slot',
      limit_num_attr='limit_num',
      merchant_cluster_json_value='',
      kess_service='grpc_getMerchantLiveClipTags',
      shards=1)
    ```
    """
    self._add_processor(MerchantGsuWithLiveClipClusterEnricher(kwargs))
    return self

  def colossus(self, **kwargs):
    """
    CommonRecoColossusEnricher
    ------
    从 colossus 查询用户观看视频/直播视频历史数据

    参数
    ------
    `service_name`: [string] 必填参数，服务在 kess 上注册的名字

    `client_type`: [int] 必填参数，可选值：sim_client, snack_client, common_item_client

    `output_attr`: [string] colossus_resp 输出 attr name

    `is_batch_query`: [bool] 是否要批量查询多个 user_id，不填默认是 False

    `input_attr`: [string] 如果是批量查询，该参数必填，colossus_batch_uids 输入 attr name; 非批量查询时，该参数可选，如果传入，则查询使用的 id 从该参数指定的属性中取，否则 uid 不为 0 的情况下使用 uid，uid 为 0 的情况下使用 device_id 的 CityHash 值

    `parse_to_pb`: [bool] 是否把 common_item_client 的查询结果解析到 protobuf 中，默认是 True

    `max_resp_item_num`: [int] 设置 sim_client 或者 common_item_client 只返回最新的 N 个 item，默认是 0，不限制返回的 item 数量

    `input_colossus_resp_attr`: [string] 可以指定一个 common attr，里面存储预先请求好的 CommonItemResponse，当给定 attr 非空时跳过请求 colossus，直接解析该 attr 的结果，当前仅支持 common_item_client 以及 is_batch_query 为 False。

    `print_items`: [bool] debug 专用，是否把 common_item_client 查询到的 item 都打印出来（parse_to_pb=False 的情况下也支持），默认是 False

    `debug_uids`: [string] debug 专用，指定只使用设置的 uid （多个 uid 使用英文逗号分隔，中间不要有空格）来查询 colossus，默认为空串

    示例
    ------
    ``` python
    .colossus(service_name='grpc_colossusSimV2',
      client_type='common_item_client',
      output_attr='colossus_resp')
    .colossus(service_name='grpc_colossusLiveItemV4',
      client_type='common_item_client',
      is_batch_query=True,
      input_attr='colossus_batch_uids',
      output_attr='colossus_batch_resps')
    ```
    """
    self._add_processor(CommonRecoColossusEnricher(kwargs))
    return self

  def gsu_fetch_embedding_server_enricher(self, **kwargs):
    """
    FetchEmbeddingServerEnricher
    ------
    从 embedding server 里面查询 bytes 格式的特征

    参数
    ------
    `service_name`: [string] 必填参数，embedding server 服务在 kess 上注册的名字

    `shard_num`: [int] 必填参数，embedding server 分的 shard 数量

    `timeout_ms`: [int] 可选参数：访问 embedding server 超时时间，默认 50ms

    `local_cache_size`: [int] 可选参数：本地 LruMap 最多缓存的 key 数量，默认是 0，表示不开启缓存

    `input_attr`: [string] 必填参数：输入 key 列表的 attr name

    `output_attr`: [string] 必填参数：输出 attr name

    `debug_print_fea_num`: [bool] debug 专用，表示把返回的前 N 个 key 对应的 feature 打印出来

    `debug_keys`: [string] debug 专用，配置后，只用这些 key 去做查询，多个 key 之间用英文逗号分隔，中间不要有空格

    示例
    ------
    ``` python
    .gsu_fetch_embedding_server_enricher(service_name='grpc_xxx_embeding_server',
      shard_num=8,
      input_attr='author_id_list',
      output_attr='xxx_features')
    ```
    """
    self._add_processor(FetchEmbeddingServerEnricher(kwargs))
    return self

  def batch_colossus(self, **kwargs):
    """
    CommonRecoBatchColossusEnricher
    ------
    从 colossus 查询用户观看视频/直播视频历史数据

    参数
    ------
    `service_name`: [string] 必填参数，服务在 kess 上注册的名字

    `shard_number`: [int] shard_number (新版已废弃)

    `client_type`: [int] 必填参数，可选值：sim_client, snack_client, common_item_client

    `output_attr`: [string] colossus_resp 输出 attr name
    """
    self._add_processor(CommonRecoBatchColossusEnricher(kwargs))
    return self

  def batch_colossus_v2(self, **kwargs):
    """
    CommonRecoBatchColossusEnricherV2
    ------
    从 colossus 查询用户观看视频/直播视频历史数据

    参数
    ------
    `service_name`: [string] 必填参数，服务在 kess 上注册的名字

    `shard_number`: [int] shard_number (新版已废弃)

    `client_type`: [int] 必填参数，可选值：sim_client, snack_client, common_item_client

    `output_attr`: [string] colossus_resp 输出 attr name
    """
    self._add_processor(CommonRecoBatchColossusEnricherV2(kwargs))
    return self

  def dump_long_term_response(self, **kwargs):
    """
    LongTermResponseEnricher
    ------
    根据指定的若干 list common_attr 构造 LongTermResponses 的 flatbuffers 序列化字符串，并存入 string common_attr

    参数
    ------
    `dump_to_attr`: [string] 将构造的 flatbuffers 序列化字符串存入指定的 common_attr

    `photo_id_attr`: [string] 指定从哪个 common_attr list 中获取 SingleTerm.photo_id 的值

    `author_id_attr`: [string] 指定从哪个 common_attr list 中获取 SingleTerm.author_id 的值

    `duration_attr`: [string] 指定从哪个 common_attr list 中获取 SingleTerm.duration 的值

    `play_time_attr`: [string] 指定从哪个 common_attr list 中获取 SingleTerm.play_time 的值

    `tag_attr`: [string] 指定从哪个 common_attr list 中获取 SingleTerm.tag 的值

    `label_attr`: [string] 指定从哪个 common_attr list 中获取 SingleTerm.label 的值

    `timestamp_attr`: [string] 指定从哪个 common_attr list 中获取 SingleTerm.timestamp 的值

    示例
    ------
    ``` python
    .dump_long_term_response(
      dump_to_attr="long_term_resp",
      photo_id_attr="photo_id_list",
      author_id_attr="author_id_list",
      duration_attr="duration_list",
      play_time_attr="play_time_list",
      tag_attr="tag_list",
      label_attr="label_list",
      timestamp_attr="timestamp_list",
    )
    ```
    """
    self._add_processor(LongTermResponseEnricher(kwargs))
    return self

  def get_query_hash(self, **kwargs):
    """
    GsuUserQueryHashEnricher
    ------
    根据user的search_query_list或者item的search query str返回对应的query_hash_list。如果输入是user的search_query_list，存入int_list common_attr。如果输入是item的search query str，存入int_list item_attr。

    参数
    ------
    `user_query_list_attr`: [string] 指定从哪个common_attr获取用户搜索列表，user_query_list_attr和item_query_str_attr中必须只有一个非空

    `item_query_str_attr`: [string] 指定从哪个item_attr获取和图片相关的搜索词

    `save_hash_to_attr`: [string] 如果user_query_list_attr非空，指定将query hash列表存入哪个common_attr；如果user_query_list_attr为空，item_query_str_attr非空，指定将query hash列表存入哪个item_attr

    实例
    ------
    ``` python
    .get_query_hash(user_query_list_attr='user_query_list_attr', save_hash_to_attr=‘query_hash’)
    ```
    ``` python
    .get_query_hash(item_query_str_attr='item_query_str_attr', save_hash_to_attr=‘query_hash’)
    ```
    """
    self._add_processor(GsuUserQueryHashEnricher(kwargs))
    return self

  def gsu_tower_sort_post(self, **kwargs):
    """
    GsuTowerSortPostEnricher
    ------
    将 common_attr中的 `*std::vector<double>` 转化为各个item对应的 `vector<double>`，排序
    并返回最大的n个，和对应的 output_sign 和 output_slots

    参数
    ------
    `common_distance_ptr_attr` : [string] 待转换的 common attr

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `tag_attr` : [string] pid对应的tag attr name

    `play_time_attr` : [string] pid对应的play time attr name

    `duration_attr` : [string] pid对应的duration attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `label_attr` : [string] pid对应的label attr name

    `channel_attr` : [string] pid对应的channel attr name

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `output_item_distance_attr` : [string] 转换并排序后的top n结果存入的item attr，为空时不返回

    `output_item_colossus_pid_attr` : [string] 与上一个对应的colossus_pid列表中的内容，为空时不返回

    `output_item_raw_distance_attr` ： [string] 未经排序的原始distance序列存入的item_attr，为空时不返回

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    `slot_as_attr_name`: [bool] 用 slot 作为 key，signs 作为 int list 存储。默认为 False。

    `slot_as_attr_name_prefix`: [string] 用 slot 作为 key 时的前缀，默认无前缀。

    `mio_slots_id`: [string] 指定特征 slots。

    `slots_id`: [string] 指定特征 slots 或 shared slot。


    示例
    ------
    ``` python
    .gsu_tower_sort_post(common_distance_ptr_attr="distance",
                        matrixcolossus_pid_attr="colossus_pid",
                        author_id_attr="aid_attr",
                        tag_attr="tag_attr",
                        play_time_attr="play_time_attr",
                        duration_attr="duration_attr",
                        timestamp_attr="ts_attr",
                        output_sign_attr="output_sign",
                        output_slot_attr="output_slot",
                        output_item_distance_attr="item_colossus_distance",
                        output_item_colossus_pid_attr="item_colossus_pid",
                        top_n=50)
    ```
    """
    self._add_processor(GsuTowerSortPostEnricher(kwargs))
    return self

  def gsu_tower_multi_sort_post(self, **kwargs):
    """
    GsuTowerMultiSortPostEnricher
    ------
    对 common_attr 中的多个 `std::vector<float>*` 进行多队列排序，每个队列返回若干 item，最后返回抽取的
    output_sign 和 output_slots

    参数
    ------
    `user_distance_ptr_attrs` : [list] 存储在 common_attr 里的 user 与 colossus pid 距离的列表。

    `common_distance_ptr_attrs` : [list] 存储在 common_attr 里的 item 与 colossus pid 距离的列表。

    `common_distance_filter_attrs` : [list] 与 common_distance_ptr_attrs 对应的 item attr，用于标记请求 DPCalc Server 时该 item 的 embedding 是否缺失。

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `tag_attr` : [string] pid对应的tag attr name

    `label_attr` : [string] pid对应的label attr name

    `channel_attr` : [string] pid对应的channel attr name

    `play_time_attr` : [string] pid对应的play time attr name

    `duration_attr` : [string] pid对应的duration attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `output_sign_attr` : [string] 输出的 sign 写入到的 attr name

    `output_slot_attr` : [string] 输出的 slot 写入到的 attr name

    `channel_sizes`: [list][动态参数] 每个队列输出的 item 数量。

    `channel_weights`: [list][动态参数] 每个队列对应的权重，size 为 common_distance_ptr_attrs 的 size 乘以 channel_sizes 的 size。

    `user_channel_weights`: [list][动态参数] 每个 user 侧队列对应的权重，size 为 user_distance_ptr_attrs 的 size 乘以 channel_sizes 的 size。

    `output_item_colossus_pid_attr`: [string] 输出的 pid list 存入的 item attr name，缺省则不填充。

    `output_item_colossus_pid_attr`: [string] 输出的 pid list 对应的距离存入的 item attr name，缺省则不填充。

    示例
    ------
    ``` python
    .gsu_tower_multi_sort_post(common_distance_ptr_attrs=["aid_distance", "pid_distance", "mmu_distance"],
                               common_distance_filter_attrs=["aid_embedding", "pid_embedding", "mmu_embedding"]
                               colossus_pid_attr="colossus_pid",
                               author_id_attr="aid_attr",
                               tag_attr="tag_attr",
                               label_attr="label_attr",
                               channel_attr="channel_attr",
                               play_time_attr="play_time_attr",
                               duration_attr="duration_attr",
                               timestamp_attr="ts_attr",
                               output_sign_attr="output_sign",
                               output_slot_attr="output_slot",
                               channel_sizes=[50, 50],
                               channel_weights=[1, 1, 0, 0, 0, 1])
    ```
    """
    self._add_processor(GsuTowerMultiSortPostEnricher(kwargs))
    return self

  def gsu_tower_sort_post_nearby(self, **kwargs):
    """
    GsuTowerSortPostNearbyEnricher
    ------
    将 common_attr中的 `*std::vector<double>` 转化为各个item对应的 `vector<double>`，排序
    并返回最大的n个，和对应的 output_sign 和 output_slots

    参数
    ------
    `common_distance_ptr_attr` : [string] 待转换的 common attr

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `tag_attr` : [string] pid对应的tag attr name

    `play_time_attr` : [string] pid对应的play time attr name

    `duration_attr` : [string] pid对应的duration attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `item_lat_attr` : [string] pid对应的latitude attr name

    `item_lon_attr` : [string] pid对应的longtitude attr name

    `user_lat_attr` : [string] user对应的latitude attr name

    `user_lon_attr` : [string] user对应的longtitude attr name

    `target_item_lat_attr` : [string] target item对应的latitude attr name

    `target_item_lon_attr` : [string] target item对应的longtitude attr name

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `output_item_distance_attr` : [string] 转换并排序后的top n结果存入的item attr，为空时不返回

    `output_item_colossus_pid_attr` : [string] 与上一个对应的colossus_pid列表中的内容，为空时不返回

    `output_item_raw_distance_attr` ： [string] 未经排序的原始distance序列存入的item_attr，为空时不返回

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    `slot_as_attr_name`: [bool] 用 slot 作为 key，signs 作为 int list 存储。默认为 False。

    `slot_as_attr_name_prefix`: [string] 用 slot 作为 key 时的前缀，默认无前缀。

    `mio_slots_id`: [string] 指定特征 slots。

    `slots_id`: [string] 指定特征 slots 或 shared slot。

    `distance_limit`: [float] 过滤 distance < distance_limit 的视频

    `use_distance_limit`: [bool] 启用过滤

    `use_nth_element`: [bool] 启用 nth_element


    示例
    ------
    ``` python
    .gsu_tower_sort_post_nearby(common_distance_ptr_attr="distance",
                        colossus_pid_attr="colossus_pid",
                        author_id_attr="aid_attr",
                        tag_attr="tag_attr",
                        play_time_attr="play_time_attr",
                        duration_attr="duration_attr",
                        timestamp_attr="ts_attr",
                        item_lat_attr='lat',
                        item_lon_attr='lon',
                        user_lat_attr='user_lat',
                        user_lon_attr='user_lon',
                        target_item_lat_attr='item_lat',
                        target_item_lon_attr='item_lon',
                        output_sign_attr="output_sign",
                        output_slot_attr="output_slot",
                        output_item_distance_attr="item_colossus_distance",
                        output_item_colossus_pid_attr="item_colossus_pid",
                        top_n=50)
    ```
    """
    self._add_processor(GsuTowerSortPostNearbyEnricher(kwargs))
    return self


  def gsu_with_time_profile(self, **kwargs):
    """
    GsuWithTimeProfileCommonEnricher
    ------
    从 `colossus_resp_attr` 中选择对应的 history item，获取用户前一天相同时段观看的作品
    以及进入 Profile 页的所有 作者 和观看时长最长的 topn 个作者的作品

    参数
    ------
    `colossus_resp_attr` : [string] colossus_resp 输入的 common attr

    `filter_future_attr` : [bool] filter colossus_resp 穿越的 item

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `cycle_hour_gap` : [int] 用户前一天相同时段观看作品和当前时间 gap hour

    `cycle_photo_limit` : [int] 用户前一天相同时段观看作品返回数

    `total_profile_photo_limit` : [int] 用户 Profile 页观看作品最多返回个数

    `total_profile_author_limit` : [int] 用户 Profile 页观看时间最长的作者个数

    `effective_view_photo_limit`: [int] effective view 数目

    `mio_slots_id`: [int_list] 选配项 区分 embedding 特征的 slot 列表, 默认值 [346, 347, 349, 348, 350]

    `slots_id`: [int_list] 选配项 sign 对应的 slot 列表, 默认值 [26, 128, 349, 348, 350]

    示例
    ------
    ``` python
    .gsu_with_time_profile(colossus_resp_attr="colossus_output",
                    filter_future_attr=True,
                    cycle_hour_gap=1,
                    cycle_photo_limit=50,
                    total_profile_photo_limit=50,
                    total_profile_author_limit=50,
                    effective_view_photo_limit=250,
                    slots_id=[123, 122, 692, 693, 694],
                    mio_slots_id=[690, 691, 692, 693, 694],
                    output_sign_attr="output_sign",
                    output_slot_attr="output_slot")
    ```
    """
    self._add_processor(GsuWithTimeProfileCommonEnricher(kwargs))
    return self

  def pxtrs_colossus(self, **kwargs):
    """
    PxtrsColossusRespRetriever
    ------
    从 gsu_common_colossusv2_enricher 的输出中选择 list item pxtrs

    参数
    ------
    `photo_id_from` : [string] 必填，photo_id 从那个 CommonAttr 获取

    `author_id_from` : [string] author_id 从那个 CommonAttr 获取

    `photo_pxtr_from` : [string] photo_pxtr 从那个 CommonAttr 获取

    `timestamp_from` : [string] 必填，timestamp 从那个 CommonAttr 获取

    `play_time_from` : [string] play_time 从那个 CommonAttr 获取

    `duration_from` : [string] duration 从那个 CommonAttr 获取

    `hetu_tag_from` : [string] hetu_tag 从那个 CommonAttr 获取

    `label_from` : [string] label 从那个 CommonAttr 获取

    `filter_future_attr` : [bool] filter colossus_resp 穿越的 item

    `save_photo_id_to_attr` : [string] photo_id 输出的 item attr

    `save_author_id_to_attr` : [string] author_id 输出的 item attr

    `save_timestamp_to_attr` : [string] timestamp 输出的 item attr

    `save_playtime_to_attr` : [string] playtime 输出的 item attr

    `save_duration_to_attr` : [string] duration 输出的 item attr

    `save_label_to_attr` : [string] label 输出的 item attr

    `save_pctr_to_attr` : [string] pctr 输出的 item attr

    `save_pvtr_to_attr` : [string] pvtr 输出的 item attr

    `save_pltr_to_attr` : [string] pltr 输出的 item attr

    `save_pwtr_to_attr` : [string] pwtr 输出的 item attr

    `save_pftr_to_attr` : [string] pftr 输出的 item attr

    `save_plvtr_to_attr` : [string] plvtr 输出的 item attr

    示例
    ------
    ``` python
    # 数据来自 v2 colossus
    .gsu_common_colossusv2_enricher(kconf="colossus.kconf_client.hot_video_pxtr_item",
                                item_fields=dict(photo_id="photo_id_list",
                                                 timestamp="timestamp_list",
                                                 author_id="author_id_list",
                                                 photo_pxtr="photo_pxtr_list",
                                                 hetu_tag="hetu_tag_list",
                                                 duration="duration_list",
                                                 play_time="play_time_list")) \
    .pxtrs_colossus(from_v2_service=True,
                    photo_id_from="photo_id_list",
                    author_id_from="author_id_list",
                    photo_pxtr_from="photo_pxtr_list",
                    timestamp_from="timestamp_list",
                    play_time_from="play_time_list",
                    duration_from="duration_list",
                    hetu_tag_from="hetu_tag_list",
                    filter_future_attr=True,
                    save_photo_id_to_attr="pxtrs_colossus_pid",
                    save_author_id_to_attr="pxtrs_colossus_aid",
                    save_timestamp_to_attr="pxtrs_colossus_timestamp",
                    save_pctr_to_attr="pxtrs_colossus_pctr",
                    save_pvtr_to_attr="pxtrs_colossus_pvtr",
                    save_pltr_to_attr="pxtrs_colossus_pltr",
                    save_pwtr_to_attr="pxtrs_colossus_pwtr",
                    save_pftr_to_attr="pxtrs_colossus_pftr",
                    save_plvtr_to_attr="pxtrs_colossus_plvtr")
    ```
    """
    self._add_processor(PxtrsColossusRespRetriever(kwargs))
    return self

  def gsu_with_time(self, **kwargs):
    """
    GsuWithTimeCommonEnricher
    ------
    从 `colossus_resp_attr` 中选择对应的 history item，获取用户相同时段观看的作品

    参数
    ------
    `colossus_resp_attr` : [string] colossus_resp 输入的 common attr

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `time_window` : [int] 用户相同时段观看作品时间窗口 分钟

    `photo_limit` : [int] 用户相同时段观看作品返回数

    `photo_limit_day` : [int] 同一天最多返回数

    `mio_slots_id`: [int_list] 选配项 区分 embedding 特征的 slot 列表, 默认值 [1721, 1722, 1723, 1724, 1725]

    `slots_id`: [int_list] 选配项 sign 对应的 slot 列表, 默认值 [26, 128, 1723, 1724, 1725]

    示例
    ------
    ``` python
    .gsu_with_time_profile(colossus_resp_attr="colossus_output",
                    time_window=120,
                    photo_limit=50,
                    photo_limit_day=10,
                    slots_id=[26, 128, 1723, 1724, 1725],
                    mio_slots_id=[1721, 1722, 1723, 1724, 1725],
                    output_sign_attr="gsu_time_output_sign",
                    output_slot_attr="gsu_time_output_slot")
    ```
    """
    self._add_processor(GsuWithTimeCommonEnricher(kwargs))
    return self

  def gsu_with_remote_dot_product(self, **kwargs):
    """
    GsuWithRemoteDotProductEnricher
    ------
    这是一个 `fuse` 的 processor, 目的是降低抽取 GSU 特征的开销。
    该 Processor 集成了 gsu_retriever_with_colossus_resp, fetch_tower_topn_dot_product,
    以及 gsu_with_index 的功能。直接从 colossus 返回的数据中提取 history item_key, 请求 DPCalc Server,
    并从结果中选出 top_n, 根据 top_n 抽取 GSU 的特征。
    同 FetchTowerTopnDotProductEnricher, 该 Processor 目前只支持单目标，且 output_type 必须设置为 4.

    参数
    ------
    `kess_service`: [string] 预估服务kess 服务名

    `kess_cluster`: [string] 预估服务kess 集群名，默认 PRODUCTION

    `shards`: [int] 预估服务shard 数量

    `timeout_ms`: [int] 预估服务 rpc 超时，单位毫秒，默认 50

    `user_embedding_attr`: [string] user embedding 在 context CommonAttr 中的字段名

    `use_item_key_as_embed_key`: [bool] true: item_key 当做 embedding key,  fasle: 从 CommonAttr 获取 embedding key

    `item_embedding_key_attr`: [string] use_item_key_as_embed_key 为 false 时，从 CommonAttr 获取 embedding key 列表的key

    `predict_labels`: [string list] 与预估的label名称列表

    `server_request_type`: [string] 预估服务也是dragonfly 实现，需要request_type参数

    `req_common_embedding_attr`: [string] 从 CommonAttr 中指示 common embedding 存放的字段,默认值req_common_embedding

    `req_tower_caller_attr`: [string] 在 request common attr 中标明自己的调用身份，默认值tower_caller

    `return_pxtr_value_attr`: [string] 在 request common attr 中指定返回 pxtr 结果时存放的 response common attr字段，被调必须遵守，默认值return_pxtr_value
                              同时，如果上游没有指定 pxtr 值返回的字段(item_pxtr_value_attr)，那么该字段也是返回给上游的 attr 字段名

    `return_sorted_item_ids_attr`: [string] 在 request common attr 中指定返回排序后 topn index 时存放的 response common attr字段，被调必须遵守.

    `output_type`: [int]  0: 以 `std::vector<float>*` 输出至 common attr （默认）
                          1: 以 Doublelist 输出至 item attr
                          2: 以 Double 输出至对应 pxtr 的 item attr
                          4: 输出 TopN 的 pxtr 及对应的 index

    `sub_req_num_in_shard`: [int] 将 shard 请求拆分成若干并行请求的自请求，默认为 1

    `kconf_timeout_ms_attr`: [int] kconf 上配置的 pxtr 服务访问超时阈值

    `item_pxtr_label_attr`: [string] 输出 pxtr label 的 attr 名，默认为 "return_pxtr_value"

    `item_pxtr_value_attr`: [string] 输出 pxtr value 的 attr 名，默认同 `return_pxtr_value_attr`

    `colossus_resp_attr`: [string] 保存 colossus 返回结果的 attr

    `target_embedding_attr`: [string] 保存 target mmu embedding 的 attr

    `output_sign_attr`: [string] 保存抽取的 GSU 特征 sign 的 attr

    `output_slot_attr`: [string] 保存抽取的 GSU 特征 slot 的 attr

    `filter_by_label`: [int] 默认为 -1, 设置为大于等于零的值后会根据 label 过滤，选择 label 与该值相同的 history item

    `colossus_type`: [string] 支持 `sim_item` 或者 `common_item` 或者 `nearby_item`，分别对应不同 colossus 返回数据格式

    `parse_from_pb`: [bool] 暂时必须设置为 false

    `need_remap`: [bool] 是否需要 remap, 默认为 false. 商业化业务需要设置

    `remap_config`: [list] 当 need_remap 为 true 时需要，list of [slot, map_slot, map_prefix, size],
                    list 的长度和顺序必须与 mio_slots_ids 保持一致。mio_slots_ids 的默认值为
                    [346, 347, 349, 348, 350]

    `sign_prefix_bit_num_input`: [int] 需要映射的 sign 的前缀位数，有效范围 (0, 16], 当 need_remap 为 true 时需要设置

    `sign_prefix_bit_num_output`: [int] 输出 sign 的前缀位数，有效范围 (0, 16], 当 need_remap 为 true 时需要设置

    `common_user_lat_attr`: [string] 保存 user lat 的 attr。决定是否引入 ui 特征 (colossus_type = nearby)

    `common_user_lon_attr`: [string] 保存 user lng 的 attr。决定是否引入 ui 特征 (colossus_type = nearby)

    `target_item_lat_attr`: [string] 保存 reco item lat 的 attr。决定是否引入 ii 特征 (colossus_type = nearby)

    `target_item_lon_attr`: [string] 保存 reco item lng 的 attr。决定是否引入 ii 特征 (colossus_type = nearby)

    `enable_filter_by_label_for_nearby_items`: [bool] 默认为 false [正常情况无需打开] 开启后允许 colossus_type = nearby 时进行 label filter

    `enable_filter_far_dis_for_nearby_items`: [bool] 默认为 false [正常情况无需打开] 开启后允许 colossus_type = nearby 时进行 distance filter 

    `target_nearby_item_label`: [int] 配合 enable_filter_by_label_for_nearby_items 使用 对 colossus_type = nearby 时进行 label filter 的目标 label

    `enable_embedding_similarity_rfm_for_nearby_items`: [bool] 默认为 false [非特征产出不要打开] 对 colossus_type = nearby 时进行 video rfm 特征的计算
                                                        必须同时传入以下参数 否则开关会被重置为 false
                                                        output_embedding_similarity_rfm_show_attr_list [string] 输出 item attr的名字
                                                        output_embedding_similarity_rfm_effective_view_attr_list [string] 输出 item attr的名字
                                                        output_embedding_similarity_rfm_watch_time_attr_list [string] 输出 item attr的名字
                                                        output_embedding_similarity_rfm_glabel_attr_list [string] 输出 item attr的名字
                                                        embedding_similarity_rfm_bucket_interval [int] 分桶的 interval 需要为 (0, 100] 且为 5 的倍数

    示例
    ------
    ``` python
    .gsu_with_remote_dot_product(user_embedding_attr = "user_output_embedding",
                                  use_item_key_as_item_embed_key = True,
                                  predict_labels = [ctr, ltr, svtr],
                                  kess_service = 'grpc_TowerPhotoEmbeddingWithPxtrCalc',
                                  shards = 8,
                                  timeout_ms = 20,
                                  output_type = 4,
                                  server_request_type = 'rt_tower_predict_pxtr',
                                  req_common_embedding_attr='req_common_embedding',
                                  req_tower_caller_attr='tower_caller',
                                  return_pxtr_value_attr='return_pxtr_value_attr',
                                  return_sorted_item_ids_attr='sorted_item_ids_vec",
                                  colossus_resp_attr='colossus_output',
                                  target_embedding_attr='target_photo_mmu_embedding',
                                  output_sign_attr='gsu_signs',
                                  output_slot_attr='gsu_slots'
                                  )
    ```
    """
    self._add_processor(GsuWithRemoteDotProductEnricher(kwargs))
    return self

  def gsu_with_index(self, **kwargs):
    """
    GsuWithIndexEnricher
    ------
    根据 common_attr中的 item index, 从 `colossus_pid_attr` 中选择对应的 history item，和
    target item 一起计算对应的 sign, 返回 output_sign 和 output_slots

    参数
    ------
    `sorted_item_idx_attr` : [string] 排序后 top_n item index 对应的 attr

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `tag_attr` : [string] pid对应的tag attr name

    `play_time_attr` : [string] pid对应的play time attr name

    `duration_attr` : [string] pid对应的duration attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    `output_item_colossus_pid_attr` : [string] 与上一个对应的colossus_pid列表中的内容，为空时不返回

    `colossus_result_as_list`: [boolean], 假如上游 CommonRecoColossusRespRetriever 已经把 photo_id, author_id, duration, play_time, tag, label, timestamp
                               作为一个 list 一起写到 item attr 中, 这里可以开启这个开关，将其一次做为一个 list 读出来; colossus_result_as_list 为 true 的时候
                               必须提供 `colossus_result_list_attr`，这时 author_id_attr, duration_attr，play_time_attr，tag_attr，timestamp_attr 不再生效;
                               这样做是起到一些性能优化的作用

    `colossus_result_list_attr`: [string], 将 author_id_attr，duration_attr，play_time_attr，tag_attr，timestamp_attr 做为一个 list 从 item 侧属性读出来

    `item_attrs_as_list`: [boolean] 传入的视频 item attrs 是否使用 list 存储

    示例
    ------
    ``` python
    .gsu_with_index(sorted_item_idx_attr="distance",
                    matrixcolossus_pid_attr="colossus_pid",
                    author_id_attr="aid_attr",
                    tag_attr="tag_attr",
                    play_time_attr="play_time_attr",
                    duration_attr="duration_attr",
                    timestamp_attr="ts_attr",
                    output_sign_attr="output_sign",
                    output_slot_attr="output_slot",
                    top_n=50)
    ```
    """
    self._add_processor(GsuWithIndexEnricher(kwargs))
    return self
  
  def gsu_with_index_v3(self, **kwargs):
    """
    GsuWithIndexV3Enricher
    ------
    根据 common_attr中的 item index, 从 `colossus_pid_attr` 中选择对应的 history item，和
    target item 一起计算对应的 sign, 返回 output_sign 和 output_slots

    参数
    ------
    `sorted_item_idx_attr` : [string] 排序后 top_n item index 对应的 attr

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `tag_attr` : [string] pid对应的tag attr name

    `play_time_attr` : [string] pid对应的play time attr name

    `duration_attr` : [string] pid对应的duration attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    `output_item_colossus_pid_attr` : [string] 与上一个对应的colossus_pid列表中的内容，为空时不返回

    `colossus_result_as_list`: [boolean], 假如上游 CommonRecoColossusRespRetriever 已经把 photo_id, author_id, duration, play_time, tag, label, timestamp
                               作为一个 list 一起写到 item attr 中, 这里可以开启这个开关，将其一次做为一个 list 读出来; colossus_result_as_list 为 true 的时候
                               必须提供 `colossus_result_list_attr`，这时 author_id_attr, duration_attr，play_time_attr，tag_attr，timestamp_attr 不再生效;
                               这样做是起到一些性能优化的作用

    `colossus_result_list_attr`: [string], 将 author_id_attr，duration_attr，play_time_attr，tag_attr，timestamp_attr 做为一个 list 从 item 侧属性读出来

    `item_attrs_as_list`: [boolean] 传入的视频 item attrs 是否使用 list 存储

    示例
    ------
    ``` python
    .gsu_with_index_v3(sorted_item_idx_attr="distance",
                    matrixcolossus_pid_attr="colossus_pid",
                    author_id_attr="aid_attr",
                    tag_attr="tag_attr",
                    play_time_attr="play_time_attr",
                    duration_attr="duration_attr",
                    timestamp_attr="ts_attr",
                    output_sign_attr="output_sign",
                    output_slot_attr="output_slot",
                    top_n=50)
    ```
    """
    self._add_processor(GsuWithIndexV3Enricher(kwargs))
    return self

  def gsu_with_index_v3_sub_seq(self, **kwargs):
    """
    GsuWithIndexV3SubSeqEnricher
    ------
    根据 common_attr中的 item index, 从 `colossus_pid_attr` 中选择对应的 history item，和
    target item 一起计算对应的 sign, 返回 output_sign 和 output_slots

    参数
    ------
    `sorted_item_idx_attr` : [string] 排序后 top_n item index 对应的 attr

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `tag_attr` : [string] pid对应的tag attr name

    `play_time_attr` : [string] pid对应的play time attr name

    `duration_attr` : [string] pid对应的duration attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    `limit` : [int] 返回的阶段数量

    `output_item_colossus_pid_attr` : [string] 与上一个对应的colossus_pid列表中的内容，为空时不返回

    `colossus_result_as_list`: [boolean], 假如上游 CommonRecoColossusRespRetriever 已经把 photo_id, author_id, duration, play_time, tag, label, timestamp
                               作为一个 list 一起写到 item attr 中, 这里可以开启这个开关，将其一次做为一个 list 读出来; colossus_result_as_list 为 true 的时候
                               必须提供 `colossus_result_list_attr`，这时 author_id_attr, duration_attr，play_time_attr，tag_attr，timestamp_attr 不再生效;
                               这样做是起到一些性能优化的作用

    `colossus_result_list_attr`: [string], 将 author_id_attr，duration_attr，play_time_attr，tag_attr，timestamp_attr 做为一个 list 从 item 侧属性读出来

    `item_attrs_as_list`: [boolean] 传入的视频 item attrs 是否使用 list 存储

    示例
    ------
    ``` python
    .gsu_with_index_v3_sub_seq(sorted_item_idx_attr="distance",
                    matrixcolossus_pid_attr="colossus_pid",
                    author_id_attr="aid_attr",
                    tag_attr="tag_attr",
                    play_time_attr="play_time_attr",
                    duration_attr="duration_attr",
                    timestamp_attr="ts_attr",
                    output_sign_attr="output_sign",
                    output_slot_attr="output_slot",
                    top_n=50)
    ```
    """
    self._add_processor(GsuWithIndexV3SubSeqEnricher(kwargs))
    return self

  def gsu_with_live_index(self, **kwargs):
    """
    GsuWithIndexLiveEnricher
    ------
    根据 common_attr中的 item index, 从 `colossus_pid_attr` 中选择对应的 history item，和
    target item 一起计算对应的 sign, 返回 output_sign 和 output_slots

    参数
    ------
    `sorted_item_idx_attr` : [string] 排序后 top_n item index 对应的 attr

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `tag_attr` : [string] pid对应的tag attr name

    `play_time_attr` : [string] pid对应的play time attr name

    `duration_attr` : [string] pid对应的duration attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    `output_item_colossus_pid_attr` : [string] 与上一个对应的colossus_pid列表中的内容，为空时不返回

    `is_unit_live_` : [boolean], 适配 unit colossus 服务

    `colossus_result_as_list`: [boolean], 假如上游 CommonRecoColossusRespRetriever 已经把 photo_id, author_id, duration, play_time, tag, label, timestamp
                               作为一个 list 一起写到 item attr 中, 这里可以开启这个开关，将其一次做为一个 list 读出来; colossus_result_as_list 为 true 的时候
                               必须提供 `colossus_result_list_attr`，这时 author_id_attr, duration_attr，play_time_attr，tag_attr，timestamp_attr 不再生效;
                               这样做是起到一些性能优化的作用

    `colossus_result_list_attr`: [string], 将 author_id_attr，duration_attr，play_time_attr，tag_attr，timestamp_attr 做为一个 list 从 item 侧属性读出来

    `item_attrs_as_list`: [boolean] 传入的视频 item attrs 是否使用 list 存储

    示例
    ------
    ``` python
    .gsu_with_live_index(sorted_item_idx_attr="distance",
                    matrixcolossus_pid_attr="colossus_pid",
                    author_id_attr="aid_attr",
                    tag_attr="tag_attr",
                    play_time_attr="play_time_attr",
                    duration_attr="duration_attr",
                    timestamp_attr="ts_attr",
                    output_sign_attr="output_sign",
                    output_slot_attr="output_slot",
                    top_n=50)
    ```
    """
    self._add_processor(GsuWithIndexLiveEnricher(kwargs))
    return self

  def gsu_with_index_v2(self, **kwargs):
    """
    GsuWithIndexV2Enricher
    ------
    根据 common_attr中的 item index, 从 `colossus_pid_attr` 中选择对应的 history item，和
    target item 一起计算对应的 sign, 返回 output_sign 和 output_slots

    参数
    ------
    `sorted_item_idx_attr` : [string] 排序后 top_n item index 对应的 attr

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `item_fields_slots` : [list of object] [{"slots_id": xxx, "mio_slots_id": xxx, "item_fields": xxx}]

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    `output_item_colossus_pid_attr` : [string] 与上一个对应的colossus_pid列表中的内容，为空时不返回


    示例
    ------
    ``` python
    .gsu_with_index(sorted_item_idx_attr="distance",
                    matrixcolossus_pid_attr="colossus_pid",
                    item_fields_slots = [{"slots_id": 1, "mio_slots_id": 1000, "item_fields": "ad_author_id"}]
                    output_sign_attr="output_sign",
                    output_slot_attr="output_slot",
                    top_n=50)
    ```
    """
    self._add_processor(GsuWithIndexV2Enricher(kwargs))
    return self

  def gsu_with_index_nearby(self, **kwargs):
    """
    GsuWithIndexEnricherNearby
    ------
    根据 common_attr中的 item index, 从 `colossus_pid_attr` 中选择对应的 history item，和
    target item 一起计算对应的 sign, 返回 output_sign 和 output_slots

    参数
    ------
    `sorted_item_idx_attr` : [string] 排序后 top_n item index 对应的 attr

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `tag_attr` : [string] pid对应的tag attr name

    `play_time_attr` : [string] pid对应的play time attr name

    `duration_attr` : [string] pid对应的duration attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `common_user_lat_attr` : [string] user对应的latitude attr name

    `common_user_lon_attr` : [string] user对应的longtitude attr name

    `target_item_lat_attr` : [string] target item对应的latitude attr name

    `target_item_lon_attr` : [string] target item对应的longtitude attr name

    `item_lat_attr` : [string] pid对应的latitude attr name

    `item_lon_attr` : [string] pid对应的longtitude attr name

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    `output_item_colossus_pid_attr` : [string] 与上一个对应的colossus_pid列表中的内容，为空时不返回

    `colossus_result_as_list`: [boolean], 假如上游 CommonRecoColossusRespRetriever 已经把 photo_id, author_id, duration, play_time, tag, label, timestamp
                               作为一个 list 一起写到 item attr 中, 这里可以开启这个开关，将其一次做为一个 list 读出来; colossus_result_as_list 为 true 的时候
                               必须提供 `colossus_result_list_attr`，这时 author_id_attr, duration_attr，play_time_attr，tag_attr，timestamp_attr 不再生效;
                               这样做是起到一些性能优化的作用

    `colossus_result_list_attr`: [string], 将 author_id_attr，duration_attr，play_time_attr，tag_attr，timestamp_attr 做为一个 list 从 item 侧属性读出来

    示例
    ------
    ``` python
    .gsu_with_index_nearby(sorted_item_idx_attr="distance",
                    matrixcolossus_pid_attr="colossus_pid",
                    author_id_attr="aid_attr",
                    tag_attr="tag_attr",
                    play_time_attr="play_time_attr",
                    duration_attr="duration_attr",
                    timestamp_attr="ts_attr",
                    output_sign_attr="output_sign",
                    output_slot_attr="output_slot",
                    top_n=50)
    ```
    """
    self._add_processor(GsuWithIndexEnricherNearby(kwargs))
    return self

  def gsu_split_cls_seq(self, **kwargs):
    """
    GsuSplitClsSeqEnricher
    ------
    对输入的序列按照duration做等频分桶，然后从每个桶中均匀的去除play_time靠前的子序列，最大序列长度为sub_seq_max_len
    其中photo_id、author_id、play_time、duration是必须的字段，其它字段可以为空，会返回一个跟子序列等长的-1值序列

    参数
    ------
    `colossus_photo_id` : [string] 需要拆分的原始序列的photo_id

    `colossus_author_id` : [string] 需要拆分的原始序列的author_id

    `colossus_play_time` : [string] 需要拆分的原始序列的play_time

    `colossus_duration` : [string] 需要拆分的原始序列的duration

    `colossus_timestamp` : [string] 需要拆分的原始序列的timestamp

    `colossus_label` : [string] 需要拆分的原始序列的label

    `colossus_real_show_index` : [string] 需要拆分的原始序列的real_show_index

    `colossus_tag` : [string] 需要拆分的原始序列的tag

    `colossus_channel` : [string] 需要拆分的原始序列的channel

    `duration_bucket_num`: [int] 对序列的duration等频分桶的数量

    `sub_seq_max_len`: [int] 拆分出来的子序列长度

    `output_photo_id`: [string] 拆分子序列输出的common attr

    `output_author_id`: [string] 拆分子序列输出的common attr

    `output_play_time`: [string] 拆分子序列输出的common attr

    `output_duration`: [string] 拆分子序列输出的common attr

    `output_timestamp`: [string] 拆分子序列输出的common attr

    `output_label`: [string] 拆分子序列输出的common attr

    `output_real_show_index`: [string] 拆分子序列输出的common attr

    `output_tag`: [string] 拆分子序列输出的common attr

    `output_channel`: [string] 拆分子序列输出的common attr

    示例
    ------
    ``` python
    .gsu_split_cls_seq(
      colossus_photo_id = "colossus_photo_id",
      colossus_author_id = "colossus_author_id_v2",
      colossus_play_time = "colossus_play_time",
      colossus_duration = "colossus_duration",
      colossus_timestamp = "colossus_timestamp",
      colossus_label = "colossus_label",
      colossus_real_show_index = "colossus_real_show_index",
      colossus_tag = "colossus_tag",
      colossus_channel = "colossus_channel",
      duration_bucket_num = 100,
      sub_seq_max_len = 1000,
      output_photo_id = "output_photo_id",
      output_author_id = "output_author_id",
      output_play_time = "output_play_time",
      output_duration = "output_duration",
      output_timestamp = "output_timestamp",
      output_label = "output_label",
      output_real_show_index = "output_real_show_index",
      output_tag = "output_tag",
      output_channel = "output_channel",
    )
    ```
    """
    self._add_processor(GsuSplitClsSeqEnricher(kwargs))
    return self

  def gsu_with_multi_head_index(self, **kwargs):
    """
    GsuWithMultiHeadIndexEnricher
    ------
    根据 item attr 中的 topn_index, 从 `colossus_output_attr` 中选择对应的 item，和
    target item 一起计算对应的 sign, 返回 output_sign 和 output_slots

    参数
    ------
    `colossus_output_attr` : [string] 保存 colossus 返回结果的 attr

    `colossus_output_type`: [string] colossus 返回数据类型, 可选项: "sim_item" 或 "commmon_item", 默认为 "sim_item"

    `topn_index_attr` : [string] 保存 topn index 的 attr name

    `topn_value_attr` : [string] 保存 topn value 的 attr name

    `head_num`: [int] head 数

    `top_n`: [int] top_n 数量

    `output_sign_attr` : [string] 保存输出 sign 的 attr name

    `output_slot_attr` : [string] 保存输出 slot 的 attr name

    `output_item_colossus_pid_attr`: [string] 保存最终选择的 PID 的 attr, 默认为空，表示不用输出这个 attr

    `output_item_colossus_ts_attr`: [string] 保存最终选择的 PID 的 timestamp attr, 默认为空，表示不用输出这个 attr

    `colossus_service_name`: [int] colossus服务的kess_name，默认为 grpc_colossusSimV2

    `colossus_photo_id_field_name`: [int] colossus服务中photo_id的域名, 默认为 photo_id

    `colossus_author_id_field_name`: [int] colossus服务中author_id的域名，默认为 author_id

    `colossus_play_time_field_name`: [int] colossus服务中play_time的域名，默认为 play_time

    `colossus_duration_field_name`: [int] colossus服务中duration的域名，默认为 duration

    `colossus_timestamp_field_name`: [int] colossus服务中timestamp的域名，默认为 timestamp

    `colossus_channel_field_name`: [int] colossus服务中channel的域名，默认为 channel

    `colossus_label_field_name`: [int] colossus 服务中 label 的域名，默认为 label

    `colossus_profile_stay_time_field_name`: [int] colossus服务中colossus_profile_stay_time的域名，默认为空

    `colossus_comment_stay_time_field_name`: [int] colossus服务中comment_stay_time的域名，默认为空

    `extract_label_fea_switch`: [int] 是否抽取label特征，默认为false

    `skip_latest_items_seconds`: [int][动态参数] 跳过最近 T seconds 内的 item，默认 60。

    示例
    ------
    ``` python
    .gsu_with_multi_head_index(colossus_output_attr="colossus_output",
                               topn_index_attr="topn_index",
                               topn_value_attr="topn_value",
                               head_num=4,
                               top_n=100,
                               output_sign_attr="gsu_signs",
                               output_slot_attr="gsu_slots")
    ```
    """
    self._add_processor(GsuWithMultiHeadIndexEnricher(kwargs))
    return self

  def ksib_gsu_with_multi_head_index(self, **kwargs):
    """
    KsibGsuWithMultiHeadIndexEnricher
    ------
    根据 item attr 中的 topn_index, 从 `colossus_output_attr` 中选择对应的 item，和
    target item 一起计算对应的 sign, 返回 output_sign 和 output_slots

    参数
    ------
    `colossus_output_attr` : [string] 保存 colossus 返回结果的 attr

    `colossus_output_type`: [string] colossus 返回数据类型, 可选项: "sim_item"、"commmon_item"、"ksib_colossusv2" , 默认为 "sim_item"
    
    `colossusv2_reflection_input_attr`: [string] 当 colossus_output_type==colossusv2 或者 ksib_colossusv2时，必须配置此项，指定colossus response的读取方式

    `colossusv2_item_datas_input_attr`: [string] 当 colossus_output_type==colossusv2 或者 ksib_colossusv2时，必须配置此项，指定colossus response的数据存储

    `topn_index_attr` : [string] 保存 topn index 的 attr name

    `topn_value_attr` : [string] 保存 topn value 的 attr name

    `head_num`: [int] head 数

    `top_n`: [int] top_n 数量

    `output_sign_attr` : [string] 保存输出 sign 的 attr name

    `output_slot_attr` : [string] 保存输出 slot 的 attr name

    `output_item_colossus_pid_attr`: [string] 保存最终选择的 PID 的 attr, 默认为空，表示不用输出这个 attr

    `existed_item_attr`: [string] 如果设置了那么就会按照这个去重, item_attr
    示例
    ------
    ``` python
    .ksib_gsu_with_multi_head_index(colossus_output_attr="colossus_output",
                                    topn_index_attr="topn_index",
                                    topn_value_attr="topn_value",
                                    head_num=4,
                                    top_n=100,
                                    output_sign_attr="gsu_signs",
                                    output_slot_attr="gsu_slots")
    ```
    """
    self._add_processor(KsibGsuWithMultiHeadIndexEnricher(kwargs))
    return self

  def gsu_with_duration(self, **kwargs):
    """
    GsuWithDurationEnricher
    ------
    根据 common_attr中的 item index, 从 `colossus_pid_attr` 中选择对应的 history item，和
    target item 一起计算对应的 sign, 返回 output_sign 和 output_slots

    参数
    ------
    `sorted_item_idx_attr` : [string] 排序后 top_n item index 对应的 attr

    `target_duration_attr` : [string] Reco result duration 对应 attr

    `duration_bound_according_ab` : [Boolean] 是否根据 AB 划分标准划分 Duration 区间段

    `duration_bound_range` : [int] 不根据 AB 划分作为依据时，Duration bound 区间段范围，单位 ms

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `tag_attr` : [string] pid对应的tag attr name

    `play_time_attr` : [string] pid对应的play time attr name

    `duration_attr` : [string] pid对应的duration attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    `output_item_colossus_pid_attr` : [string] 与上一个对应的colossus_pid列表中的内容，为空时不返回

    `colossus_result_as_list`: [boolean], 假如上游 CommonRecoColossusRespRetriever 已经把 photo_id, author_id, duration, play_time, tag, label, timestamp
                               作为一个 list 一起写到 item attr 中, 这里可以开启这个开关，将其一次做为一个 list 读出来; colossus_result_as_list 为 true 的时候
                               必须提供 `colossus_result_list_attr`，这时 author_id_attr, duration_attr，play_time_attr，tag_attr，timestamp_attr 不再生效;
                               这样做是起到一些性能优化的作用

    `colossus_result_list_attr`: [string], 将 author_id_attr，duration_attr，play_time_attr，tag_attr，timestamp_attr 做为一个 list 从 item 侧属性读出来

    示例
    ------
    ``` python
    .gsu_with_duration(sorted_item_idx_attr="distance",
                    target_duration_attr="target_duration_attr",
                    duration_bound_according_ab=true,
                    duration_bound_range=5000,
                    matrixcolossus_pid_attr="colossus_pid",
                    author_id_attr="aid_attr",
                    tag_attr="tag_attr",
                    play_time_attr="play_time_attr",
                    duration_attr="duration_attr",
                    timestamp_attr="ts_attr",
                    output_sign_attr="output_sign",
                    output_slot_attr="output_slot",
                    top_n=50)
    ```
    """
    self._add_processor(GsuWithDurationEnricher(kwargs))
    return self

  def kwaipro_gsu_with_index(self, **kwargs):
    """
    KwaiProGsuWithIndexEnricher
    ------
    根据 common_attr中的 item index, 从 `colossus_pid_attr` 中选择对应的 history item，和
    target item 一起计算对应的 sign, 返回 output_sign 和 output_slots
    kwaipro 自定义：pid & aid 拼 bucket

    参数
    ------
    `sorted_item_idx_attr` : [string] 排序后 top_n item index 对应的 attr

    `sorted_item_idx_output_type`: [int]  默认 4
                                          4: 输出 TopN 的 pxtr 及对应的 index。
                                          5: 同 4，但是使用 dragon 内置的 int/double list list 而不是 int/double vector 指针，开销会稍微高一些，但是交互性更好。

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `tag_attr` : [string] pid对应的tag attr name

    `play_time_attr` : [string] pid对应的play time attr name

    `duration_attr` : [string] pid对应的duration attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    `bucket_attr` : [string] bucket attr

    `output_item_colossus_pid_attr` : [string] 与上一个对应的colossus_pid列表中的内容，为空时不返回

    示例
    ------
    ``` python
    .kwaipro_gsu_with_index(sorted_item_idx_attr="distance",
                    sorted_item_idx_output_type=4,
                    matrixcolossus_pid_attr="colossus_pid",
                    author_id_attr="aid_attr",
                    tag_attr="tag_attr",
                    play_time_attr="play_time_attr",
                    duration_attr="duration_attr",
                    timestamp_attr="ts_attr",
                    output_sign_attr="output_sign",
                    output_slot_attr="output_slot",
                    bucket_attr="photo_bucket",
                    top_n=50)
    ```
    """
    self._add_processor(KwaiProGsuWithIndexEnricher(kwargs))
    return self

  def ksib_gsu_with_aid(self, **kwargs):
    """
    KsibGsuAidEnricher
    ------
    根据 target_attr 中的 aid, 从 `colossus_resp_attr` 中选择相同aid对应的 history item，
    返回 output_sign 和 output_slots
    pid & aid 拼 bucket

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    `target_attr` : [string] item侧使用的 aid 属性

    `limit_num` : [int] 返回每个item对应的最大n个item, 为空时返回top50

    `n_minute_ago` : [int] 只取n_minute_ago的item, 默认为1

    `filter_play_time_threshold` : [int] 过滤掉 play_time 小于该值的 action item，默认 0，表示不过滤

    `bucket_attr` : [string] item对应的bucket attr name

    `slots_id` : [int] 抽sign 使用的slot

    `mio_slots_id` : [int] mio 前向使用的slot

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    示例
    ------
    ``` python
    .ksib_gsu_with_aid(colossus_resp_attr="colossus_resp",
                    target_attr="aid",
                    author_id_attr="aid_attr",
                    limit_num=50,
                    n_minute_ago=1,
                    filter_play_time_threshold=5000,
                    output_sign_attr="output_sign",
                    output_slot_attr="output_slot",
                    bucket_attr="photo_bucket",
                    slots_id=[1000, 1001, 1002, 1003, 1004, 1005, 1006],
                    mio_slots_id=[1000, 1001, 1002, 1003, 1004, 1005, 1006])
    ```
    """
    self._add_processor(KsibGsuAidEnricher(kwargs))
    return self

  def gsu_with_index_multi_merge(self, **kwargs):
    """
    GsuWithIndexMultiMergeEnricher
    ------
    根据 common_attr中的 item index, 从 `colossus_pid_attr` 中选择对应的 history item，和
    target item 一起计算对应的 sign, 返回 output_sign 和 output_slots

    参数
    ------
    `sorted_item_idx_attr` : [list] 排序后的 top_n item index 对应的 attr

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `tag_attr` : [string] pid对应的tag attr name

    `play_time_attr` : [string] pid对应的play time attr name

    `duration_attr` : [string] pid对应的duration attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    `output_item_colossus_pid_attr` : [string] 与上一个对应的colossus_pid列表中的内容，为空时不返回

    示例
    ------
    ``` python
    .gsu_with_index_multi_merge(sorted_item_idx_attr=["distance"],
                                matrixcolossus_pid_attr="colossus_pid",
                                author_id_attr="aid_attr",
                                tag_attr="tag_attr",
                                play_time_attr="play_time_attr",
                                duration_attr="duration_attr",
                                timestamp_attr="ts_attr",
                                output_sign_attr="output_sign",
                                output_slot_attr="output_slot",
                                top_n=50)
    ```
    """
    self._add_processor(GsuWithIndexMultiMergeEnricher(kwargs))
    return self

  def kwaipro_gsu_with_index_multi_merge(self, **kwargs):
    """
    KwaiProGsuWithIndexMultiMergeEnricher
    ------
    根据 common_attr中的 item index, 从 `colossus_pid_attr` 中选择对应的 history item，和
    target item 一起计算对应的 sign, 返回 output_sign 和 output_slots
    kwaipro 自定义：pid & aid 拼 bucket

    参数
    ------
    `sorted_item_idx_attr` : [string] 排序后 top_n item index 对应的 attr

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `tag_attr` : [string] pid对应的tag attr name

    `play_time_attr` : [string] pid对应的play time attr name

    `duration_attr` : [string] pid对应的duration attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    `bucket_attr` : [string] bucket attr

    `output_item_colossus_pid_attr` : [string] 与上一个对应的colossus_pid列表中的内容，为空时不返回

    示例
    ------
    ``` python
    .kwaipro_gsu_with_index_multi_merge(sorted_item_idx_attr="distance",
                    matrixcolossus_pid_attr="colossus_pid",
                    author_id_attr="aid_attr",
                    tag_attr="tag_attr",
                    play_time_attr="play_time_attr",
                    duration_attr="duration_attr",
                    timestamp_attr="ts_attr",
                    output_sign_attr="output_sign",
                    output_slot_attr="output_slot",
                    bucket_attr="photo_bucket",
                    top_n=50)
    ```
    """
    self._add_processor(KwaiProGsuWithIndexMultiMergeEnricher(kwargs))
    return self

  def kwaime_gsu_with_cluster(self, **kwargs):
    """
    KwaiMeGsuWithClusterEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 item attr name

    `output_slot_attr`: [string] slot 输出 item attr name

    `output_cluster_attr`: [string] 选配项 cluster 输出 item attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 common attr

    `limit_num`: [int][动态参数] 返回数目 common attr

    `cluster_id_service_type`: [string] 选配项 用于查询 photo 所属 cluster 的服务类型，目前支持 redis 和 embedding_server 两种，默认值 embedding_server

    `timeout_ms`: [int] 选配项 查询的超时时间，默认为 10 ms

    `hetu_cluster_config_kconf_key`: [string] 选配项 聚类 cluster 距离最近的搜索聚类 Kconf 配置, 默认值 reco.model.hetuEmbeddingIndex

    `target_cluster_attr`: [string] 选配项 引用外部 cluster item attr name

    `bucket_attr`: [string] 海外桶枚举 item attr

    `mio_slots_id`: [int_list] 选配项 区分 embedding 特征的 slot 列表, 默认值 [346, 347, 349, 348, 350]

    `slots_id`: [int_list] 选配项 sign 对应的 slot 列表, 默认值 [26, 128, 349, 348, 350]

    以下为 cluster_id_service_type == "redis" 时的配置

    `redis_cluster`: [string] 用于查询 photo 所属 cluster 的 redis 集群名

    以下为 cluster_id_service_type == "embedding_server" 时的配置

    `kess_service`: [string] embedding_server 的 kess 服务名

    `thread_num`: [int] 选配项 kess client eventloop 线程数, 默认值 1

    `shard_num`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] 选配项 embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_pids_per_request`: [int] 选配项 单次 rpc 请求中的最大 pid 个数，为 0 代表不限制, 默认值 0

    示例
    ------
    ``` python
    .kwaime_gsu_with_cluster(
      hetu_cluster_config_kconf_key="overseaReco.offline.kwaiMeMMUClusterIndex",
      colossus_resp_attr="colossus_output",
      limit_num="{{limit_num}}",
      cluster_id_service_type='embedding_server',
      kess_service='grpc_KwaiMeMMUCluster',
      timeous_ms=100,
      shard_num=2,
      slots_id=[123, 122, 692, 693, 694],
      mio_slots_id=[690, 691, 692, 693, 694],
      output_sign_attr="gsu_signs",
      output_slot_attr="gsu_slots",
      bucket_attr="photo_bucket")
    ```
    """
    self._add_processor(KwaiMeGsuWithClusterEnricher(kwargs))
    return self

  def kwaipro_gsu_tower_sort_post(self, **kwargs):
    """
    KwaiProGsuTowerSortPostEnricher
    ------
    将 common_attr中的 `*std::vector<double>` 转化为各个item对应的 `vector<double>`，排序
    并返回最大的n个，和对应的 output_sign 和 output_slots

    参数
    ------
    `common_distance_ptr_attr` : [string] 待转换的 common attr

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `tag_attr` : [string] pid对应的tag attr name

    `play_time_attr` : [string] pid对应的play time attr name

    `duration_attr` : [string] pid对应的duration attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `output_item_distance_attr` : [string] 转换并排序后的top n结果存入的item attr，为空时不返回

    `output_item_colossus_pid_attr` : [string] 与上一个对应的colossus_pid列表中的内容，为空时不返回

    `output_item_raw_distance_attr` ： [string] 未经排序的原始distance序列存入的item_attr，为空时不返回

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    `bucket_attr` : [string] bucket attr

    `shuffle`  : [bool] gsu 是否随机打乱 action list, 默认为 False

    `use_mmu_tag` : [bool] [动态参数] 是否使用 mmu tag，反之会使用人工 tag，默认为 True

    示例
    ------
    ``` python
    .kwaipro_gsu_tower_sort_post(
      common_distance_ptr_attr="distance",
      matrixcolossus_pid_attr="colossus_pid",
      author_id_attr="aid_attr",
      tag_attr="tag_attr",
      play_time_attr="play_time_attr",
      duration_attr="duration_attr",
      timestamp_attr="ts_attr",
      output_sign_attr="output_sign",
      output_slot_attr="output_slot",
      output_item_distance_attr="item_colossus_distance",
      output_item_colossus_pid_attr="item_colossus_pid",
      top_n=50,
      bucket_attr="photo_bucket",
      shuffle=False,
      use_mmu_tag=True)
    ```
    """
    self._add_processor(KwaiProGsuTowerSortPostEnricher(kwargs))
    return self

  def snack_gsu_tower_sort_post(self, **kwargs):
    """
    SnackGsuTowerSortPostEnricher
    ------
    将 common_attr中的 `*std::vector<double>` 转化为各个item对应的 `vector<double>`，排序
    并返回最大的n个，和对应的 output_sign 和 output_slots

    参数
    ------
    `common_distance_ptr_attr` : [string] 待转换的 common attr

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `tag_attr` : [string] pid对应的tag attr name

    `play_time_attr` : [string] pid对应的play time attr name

    `duration_attr` : [string] pid对应的duration attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `output_item_distance_attr` : [string] 转换并排序后的top n结果存入的item attr，为空时不返回

    `output_item_colossus_pid_attr` : [string] 与上一个对应的colossus_pid列表中的内容，为空时不返回

    `output_item_raw_distance_attr` ： [string] 未经排序的原始distance序列存入的item_attr，为空时不返回

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    `bucket_attr` : [string] bucket attr

    `shuffle`  : [bool] gsu 是否随机打乱 action list, 默认为 False

    `use_mmu_tag` : [bool] [动态参数] 是否使用 mmu tag，反之会使用人工 tag，默认为 True

    示例
    ------
    ``` python
    .snack_gsu_tower_sort_post(
      common_distance_ptr_attr="distance",
      matrixcolossus_pid_attr="colossus_pid",
      author_id_attr="aid_attr",
      tag_attr="tag_attr",
      play_time_attr="play_time_attr",
      duration_attr="duration_attr",
      timestamp_attr="ts_attr",
      output_sign_attr="output_sign",
      output_slot_attr="output_slot",
      output_item_distance_attr="item_colossus_distance",
      output_item_colossus_pid_attr="item_colossus_pid",
      top_n=50,
      bucket_attr="photo_bucket",
      shuffle=False,
      use_mmu_tag=True)
    ```
    """
    self._add_processor(SnackGsuTowerSortPostEnricher(kwargs))
    return self

  def gsu_cityhash(self, **kwargs):
    """
    GsuCityhashEnricher
    ------
    对 item attr 进行 cityhash，当前用于 dualsim 的特征抽取

    参数
    ------
    `input_attrs` : [list] 输入的 item attr 列表，包含 attr_name 和 is_common 两个字段，is_common 默认为 False，即为 item attr。

    `output_item_attr` : [string] 输出的 item attr

    示例
    ------
    ``` python
    .gsu_cityhash(
      input_attrs=[
        dict(attr_name="user_id", is_common=True),
        dict(attr_name="photo_id", is_common=False),
      ],
      output_item_attr="hash_list")
    ```
    """
    self._add_processor(GsuCityhashEnricher(kwargs))
    return self


  def gsu_bert_tokenization(self, **kwargs):
    """
    GsuBertTokenizationEnricher
    ------
    对输入的字符串列表里的字符串一一分词
    在gflag bert_vocab_file中指定词表路径

    参数
    ------
    `sentence_list_attr` : [string] 待分词的字符串列表

    `output_id_attr` : [string] 分词结果在词表中的对应id

    `output_token_attr` : [string] 输出的分词结果，为空时不返回

    `output_mask_attr` : [string] 按限长补齐后对应的mask

    `sentence_len_limit` : [int] 每个字符串对应分词的最大长度，不够时对应的id补0，mask填入-99999, 默认10

    `list_len_limit` : [int] 字符串列表限长，超过的部分不处理，默认50

    `is_common_attr` : [bool] 为true时从common_attr获取输入，结果存储在common_attr. 反之输入和输出都为item_attr

    示例
    ------
    ``` python
    .gsu_bert_tokenization(sentence_list_attr="input_sentences",
                        output_id_attr="output_token_ids")
    ```
    """
    self._add_processor(GsuBertTokenizationEnricher(kwargs))
    return self

  def gsu_with_cluster_tower_match(self, **kwargs):
    """
    CommonRecoGsuWithClusterTowerMatchEnricher
    ------
    根据 user colossus 行为列表召回 top k 出现的 cluster， 并填充对应的 sign/slot 特征

    参数
    ------
    `match_cluster_list_attr`: [string] 待匹配 cluster list 输入的 common attr

    `output_offset_attr`: [string] offset 输出 item attr name

    `target_cluster_attr`: [string] 选配项 photo cluster attr

    `match_nearby`: [boolean] 是否匹配近邻, 默认值 false

    `default_offset`: [int][动态参数] 缺省 offset, 默认值 0

    `hetu_cluster_config_kconf_key`: [string] 选配项 聚类 cluster 距离最近的搜索聚类 Kconf 配置, 默认值 overseaReco.offline.kwaiMeMMUClusterIndex

    `kess_service`: [string] embedding_server 的 kess 服务名

    `kess_cluster`: [string] 选配项 embedding_server 的 kess 集群名，默认为 PRODUCTION

    `thread_num`: [int] 选配项 kess client eventloop 线程数, 默认值 1

    `shard_num`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `timeout_ms`: [int] 选配项 查询的超时时间，默认为 10 ms

    `max_pids_per_request`: [int] 选配项 单次 rpc 请求中的最大 pid 个数，为 0 代表不限制, 默认值 0

    示例
    ------
    ``` python
    .gsu_with_cluster_tower_match(
      hetu_cluster_config_kconf_key="overseaReco.offline.kwaiMeMMUClusterIndex",
      match_cluster_list_attr="user_tower_cluster_list",
      output_offset_attr="cluster_offset",
      match_nearby=True,
      kess_service='grpc_KwaiMeMMUCluster',
      timeous_ms=100,
      shard_num=2,
      )
    ```
    """
    self._add_processor(CommonRecoGsuWithClusterTowerMatchEnricher(kwargs))
    return self

  def gsu_user_cluster_retrieve(self, **kwargs):
    """
    CommonRecoGsuUserClusterRetriever
    ------
    从user的colossus response中抽取 N 个cluster id —— 需要查询 embedding_server
    根据 cluster id 进行 gsu 搜索并填充 sign/slot 特征

    参数
    ------
    `colossus_resp_attr`: [string] colossus_resp 输入的 common attr

    `output_slot_attr`: [string] cluster 对应特征 slot 输出 attr name

    `output_sign_attr`: [string] cluster 对应特征 sign 输出 attr name

    `limit_num_attr`: [string] 抽取的 cluster 个数 N 配置字段

    `kess_service`: [string] 查询 embedding_server 的 kess name

    `kess_cluster`: [string] 查询 embedding_server 的 cluster name , 默认 PRODUCTION

    `shards`: [int] 查询 embedding_server 的 shard 数

    `timeout_ms`: [int] 查询 embedding_server 的超时阈值

    `max_pids_per_request`: [int] 选配项 单次 rpc 请求中的最大 pid 个数，为 0 代表不限制, 默认值 0

    `reason`: [int] retrieve type ， 默认 1

    `padding_default_cluster`: [int] 默认-1 ，但配置值>=0 时，会将改值作为一个item key强制召回

    示例
    ------
    ``` python
    .gsu_user_cluster_retrieve(
      colossus_resp_attr="colossus_output",
      output_slot_attr="gsu_slots",
      output_sign_attr="gsu_signs",
      limit_num_attr="sim_limit",
      kess_service="blabla",
      shards=4,
      timeout_ms=50,
    )
    ```
    """
    self._add_processor(CommonRecoGsuUserClusterRetriever(kwargs))
    return self

  def photo_tag_gsu_for_retr(self, **kwargs):
    """
    PhotoTagGsuForRetrEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `mio_slots_id`: [int_list] 选配项 区分 embedding 特征的 slot 列表, 默认值 [105-109]

    `slots_id`: [int_list] 选配项 sign 对应的 slot 列表, 默认值 [600-780]

    示例
    ------
    ``` python
    .photo_tag_gsu_for_retr(colossus_resp_attr='colossus_resp',
                      output_sign_attr='gsu_sign',
                      output_slot_attr='gsu_slot'
                      )
    ```
    """
    self._add_processor(PhotoTagGsuForRetrEnricher(kwargs))
    return self

  def live_tag_gsu_for_retr(self, **kwargs):
    """
    LiveTagGsuForRetrEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `mio_slots_id`: [int_list] 选配项 区分 embedding 特征的 slot 列表, 默认值 [100-104]

    `slots_id`: [int_list] 选配项 sign 对应的 slot 列表, 默认值 [800-860]

    示例
    ------
    ``` python
    .live_tag_gsu_for_retr(colossus_resp_attr='colossus_resp',
                      output_sign_attr='gsu_sign',
                      output_slot_attr='gsu_slot'
                      )
    ```
    """
    self._add_processor(LiveTagGsuForRetrEnricher(kwargs))
    return self

  def live_author_list_from_colossus(self, **kwargs):
    """
    LiveAuthorListFromColossus
    -----
    将 colossus 里的直播间粒度的序列聚合为 Author 粒度的序列,
    目前有如下特征 ["author_id", "play_time_sum", "reward_sum", "reward_count", "unseen_days"]
    使用 LiveItemV4 多出 ["comment_count", "like_count", "follow_count"]

    参数
    -----
    `colossus_resp_attr`: [string] colossus response attr name
    `output_prefix`: [string] 输入 attr prefix, 默认值为 "colossus_author_"
    `live_item_version`: [int] LiveItem proto 版本, 支持 [2, 4], 默认值 2
    `limit_author_num`: 输出 Author 序列的长度限制, 默认值50

    示例
    -----
    ```python
    .live_author_list_from_colossus(colossus_resp_attr='colossus_resp',
                              output_prefix='colossus_author_',
                              live_item_version=4,
                              limit_author_num=50)
    ```
    """
    self._add_processor(LiveAuthorListFromColossusEnricher(kwargs))
    return self

  def video_author_list_from_colossus(self, **kwargs):
    """
    VideoAuthorListFromColossus
    -----
    将 colossus 里短视频粒度序列聚合为 Author 粒度的序列,
    目前有如下特征 ["author_id", "duration_sum", "play_time_sum", "unseen_days",
    "like_count", "follow_count", "foward_count", "comment_count", "record_count"]

    参数
    -----
    `colossus_resp_attr`: [string] colossus response attr name
    `output_prefix`: [string] 输入 attr prefix, 默认值为 "video_author_"
    `sim_item_version`: [int] SimItem proto 版本, 支持 [2], 默认值 2
    `limit_author_num`: 输出 Author 序列的长度限制, 默认值50

    示例
    -----
    ```python
    .video_author_list_from_colossus(colossus_resp_attr='colossus_resp',
                              output_prefix='video_author_',
                              sim_item_version=4,
                              limit_author_num=50)
    ```
    """
    self._add_processor(VideoAuthorListFromColossusEnricher(kwargs))
    return self

  def live_tag_gsu_for_retr_v2(self, **kwargs):
    """
    LiveTagGsuForRetrV2Enricher
    ------
    根据 live 所属的 hetutag 进行 gsu 搜索并填充相似直播的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `mio_slots_id`: [int_list] 选配项 区分 embedding 特征的 slot 列表, 默认值 [800-804]

    `slots_id`: [int_list] 选配项 sign 对应的 slot 列表, 默认值 [26,128,802,803,804]

    `is_train_mode`: [int] 选配项 训练模式 gsu 采用 item 粒度抽取 否则预估模式采用 common 模式 默认 true

    `target_cluster_attr`: [string] 选配项 训练模式需要填 当前 item 根据哪个特征进行 gsu 检索

    示例
    ------
    ``` python
    .live_tag_gsu_for_retr_v2(colossus_resp_attr='colossus_resp',
                      output_sign_attr='gsu_sign',
                      output_slot_attr='gsu_slot'
                      )
    ```
    """
    self._add_processor(LiveTagGsuForRetrV2Enricher(kwargs))
    return self

  def sim_match_user_top_cluster(self, **kwargs):
    '''
    fast-tower sim match infer processor, generate user top-clusters and conresponding sign/slot info
    write to common-attr
    colossus_resp_attr: user long-term action list colossus response attr name
    kess_service:  the kess service where to get the cluster of long-term
    top_cluster_num: how many top clusters to get
    output_cluster_attr: top clusters list output attr name
    limit_num: how many items in every top cluster
    output_sign_prefix: sign attr name prefix
    output_slot_prefix: slot attr name prefix

    示例
    ------
       sim_match_user_top_cluster(
       colossus_resp_attr="colossus_output",
       kess_service="grpc_GSUQueryServerLjw",
       shards=4,
       default_cluster_id=10000,
       timeout_ms=3600*10,
       enable_near_cluser_search=True,
       max_pids_per_request=1024,
       top_cluster_num=16,
       output_cluster_attr="common_cluster_ids",
       limit_num=50,
       output_sign_prefix="sign_name_prefix",
       output_slot_prefix="slot_name_prefix")
    '''
    self._add_processor(CommonRecoSimMatchUserTopClusterEnricher(kwargs))
    return self

  def sim_match_cluster_side_attr(self, **kwargs):
    '''
    接 fast_tower_gsu_cluster 的输出， 把 top clusters 对对应的 attr 写入 item-side
    item_list_from_attr: fast_tower_gsu_cluster 的 output_cluster_attr 字段
    input_sign_attr_prefix: fast_tower_gsu_cluster 的 output_sign_prefix
    input_slot_attr_prefix: fast_tower_gsu_cluster 的 output_slot_prefix
    output_sign_attr: name of item side sign attr
    output_slot_attr: name of item side slot attr

    示例
    ------
     sim_match_cluster_side_attr(
     item_list_from_attr="common_cluster_ids",
     input_sign_attr_prefix="sign_name_prefix",
     input_slot_attr_prefix="slot_name_prefix",
     output_sign_attr="gsu_signs",
     output_slot_attr="gsu_slots",
     )
    '''
    self._add_processor(CommonRecoSimMatchClusterSideAttrEnricher(kwargs))
    return self

  def live_gsu_with_cluster_for_retr(self, **kwargs):
    """
    CommonLiveGsuWithClusterForRetrEnricher
    ------
    根据 user 的 直播浏览历史 填充所有 cluster 的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name. common 类型

    `output_slot_attr`: [string] slot 输出 attr name. common 类型

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `max_cluster_id`: [int] 选配. 最多有多少个 cluster. 默认 1000

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    示例
    ------
    ``` python
    .live_gsu_with_cluster_for_retr(
        colossus_resp_attr='live_colossus_resp',
        output_sign_attr='live_gsu_sign',
        output_slot_attr='live_gsu_slot',
        limit_num_attr='live_limit_num',
        )
    ```
    """
    self._add_processor(CommonLiveGsuWithClusterForRetrEnricher(kwargs))
    return self

  def gsu_with_cluster_for_retr(self, **kwargs):
    """
    CommonRecoGsuWithClusterForRetrEnricher
    ------
    根据 user 的 短视频浏览历史 填充所有 cluster 的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name. common 类型

    `output_slot_attr`: [string] slot 输出 attr name. common 类型

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `max_cluster_id`: [int] 选配. 最多有多少个 cluster. 默认 1000

    `cluster_id_service_type`: [string] 用于查询 photo 所属 cluster 的服务类型，目前支持 redis 和 embedding_server 两种，默认为 embedding_server

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    以下为 cluster_id_service_type == "redis" 时的配置

    `redis_cluster_name`: [string] 用于查询 photo 所属 cluster 的 redis 集群名

    以下为 cluster_id_service_type == "embedding_server" 时的配置

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_pids_per_request`: [int] 单次 rpc 请求中的最大 pid 个数，为 0 代表不限制

    示例
    ------
    ``` python
    .gsu_with_cluster_for_retr(colossus_resp_attr='colossus_resp',
                      output_sign_attr='sign',
                      output_slot_attr='slot',
                      limit_num_attr='limit_num',
                      cluster_id_service_type='redis',
                      redis_cluster_name='mmuHeTuCluster')

    .gsu_with_cluster_for_retr(colossus_resp_attr='colossus_resp',
                      output_sign_attr='sign',
                      output_slot_attr='slot',
                      limit_num_attr='limit_num',
                      cluster_id_service_type='embedding_server',
                      kess_service='mmu_hetu_cluster_id_query_server',
                      shards=4)
    ```
    """
    self._add_processor(CommonRecoGsuWithClusterForRetrEnricher(kwargs))
    return self

  def gsu_retrieve_from_colossus_sim_checkpoint(self, **kwargs):
    """
    BatchProcessCheckpointRetriever
    ------
    解析 colossus 的 checkpoint，按顺序召回用户的 UserId 以及该用户历史交互的 Photo 信息

    参数
    ------
    `version`: [int] version 为1时读取 grpc_colossusSim 的 checkpoint，version 为2时读取 grpc_colossusSimV2 的 checkpoint，version 为3时读取 grpc_colossusLongSimItem 的 checkpoint，默认为1

    `save_photo_id_to_attr`: [string] 填充 photo_id 的 item attr

    `save_author_id_to_attr`: [string] 填充 author_id 的 item attr

    `save_duration_to_attr`: [string] 填充 duration 的 item attr

    `save_play_time_to_attr`: [string] 填充 play time  的 item attr

    `save_tag_to_attr`: [string] 填充 tag 的 item attr

    `save_label_to_attr`: [string] 填充 label 的 item attr

    `save_timestamp_to_attr`: [string] 填充 timestamp 的 item attr

    `read_thread_num`: [int] 读取 checkpoint 线程数

    `buffer_size`: [int] 存储解析后数据的 buffer 大小
  
    `kess_name`: [string] 自定义kess_name，数据类型需要和type匹配

    `save_channel_to_attr`: version 为 2 时，填充 channel 的 item attr

    `save_cluster_id_to_attr`: version 为 3 时，填充 cluster_id 的 item attr
    
    `loop_interval`: 若loop_read_always为false，获取 checkpoint 新路径的间隔时间，默认为0，单位s
    
    `read_first_time`: 若loop_read_always为false，是否解析服务启动后第一次获取到的checkpoint，默认为true
    
    `loop_read_always`: 默认为true，会一直循环读取最新checkpoint，若false，则会判断最近checkpoint是否读取过，若读取过则会等待

    示例
    ------
    ``` python
    .gsu_retrieve_from_colossus_sim_checkpoint(
                            save_photo_id_to_attr="pid",
                            save_author_id_to_attr="aid",
                            save_duration_to_attr="duration",
                            save_play_time_to_attr="play",
                            save_tag_to_attr="tag",
                            save_label_to_attr="label",
                            save_timestamp_to_attr="time",
                            read_thread_num=8,
                            buffer_size=10000,
                            version=2,
                            save_channel_to_attr="channel",
                            loop_interval=365*24*60*60*1000)
    ```
    """
    self._add_processor(BatchProcessCheckpointRetriever(kwargs))
    return self

  def gsu_retrieve_from_colossus_v2_checkpoint(self, **kwargs):
    """
    ColossusV2CheckpointRetriever
    ------
    解析 colossus 的 checkpoint，按顺序召回用户的 UserId 以及该用户历史交互的 Photo 信息

    参数
    ------
    `version`: [int] version 为1时读取 grpc_colossusSim 的 checkpoint，version 为2时读取 grpc_colossusSimV2 的 checkpoint，version 为3时读取 grpc_colossusLongSimItem 的 checkpoint，默认为1

    `save_photo_id_to_attr`: [string] 填充 photo_id 的 item attr

    `save_author_id_to_attr`: [string] 填充 author_id 的 item attr

    `save_duration_to_attr`: [string] 填充 duration 的 item attr

    `save_play_time_to_attr`: [string] 填充 play time  的 item attr

    `save_tag_to_attr`: [string] 填充 tag 的 item attr

    `save_label_to_attr`: [string] 填充 label 的 item attr

    `save_timestamp_to_attr`: [string] 填充 timestamp 的 item attr

    `read_thread_num`: [int] 读取 checkpoint 线程数

    `buffer_size`: [int] 存储解析后数据的 buffer 大小

    `save_channel_to_attr`: version 为 2 时，填充 channel 的 item attr

    `save_cluster_id_to_attr`: version 为 3 时，填充 cluster_id 的 item attr
    
    `loop_interval`: 若loop_read_always为false，获取 checkpoint 新路径的间隔时间，默认为0，单位s
    
    `read_first_time`: 若loop_read_always为false，是否解析服务启动后第一次获取到的checkpoint，默认为true
    
    `loop_read_always`: 默认为true，会一直循环读取最新checkpoint，若false，则会判断最近checkpoint是否读取过，若读取过则会等待

    示例
    ------
    ``` python
    .gsu_retrieve_from_colossus_v2_checkpoint(
                            save_photo_id_to_attr="pid",
                            save_author_id_to_attr="aid",
                            save_duration_to_attr="duration",
                            save_play_time_to_attr="play",
                            save_tag_to_attr="tag",
                            save_label_to_attr="label",
                            save_timestamp_to_attr="time",
                            read_thread_num=8,
                            buffer_size=10000,
                            version=2,
                            save_channel_to_attr="channel",
                            loop_interval=365*24*60*60*1000)
    ```
    """
    self._add_processor(ColossusV2CheckpointRetriever(kwargs))
    return self

  def gsu_retrieve_from_ksib_colossus_sim_checkpoint(self, **kwargs):
    """
    BatchProcessKsibCheckpointRetriever
    ------
    解析 colossus 的 checkpoint，按顺序召回用户的 UserId 以及该用户历史交互的 Photo 信息

    参数
    ------
    `save_photo_id_to_attr`: [string] 填充 photo_id 的 item attr

    `save_author_id_to_attr`: [string] 填充 author_id 的 item attr

    `save_duration_to_attr`: [string] 填充 duration 的 item attr

    `save_play_time_to_attr`: [string] 填充 play time  的 item attr

    `save_tag_to_attr`: [string] 填充 tag 的 item attr

    `save_label_to_attr`: [string] 填充 label 的 item attr

    `save_timestamp_to_attr`: [string] 填充 timestamp 的 item attr

    `service_name`: [string] 读取 checkpoint 对应服务的 kess name

    `read_thread_num`: [int] 读取 checkpoint 线程数

    `buffer_size`: [int] 存储解析后数据的 buffer 大小

    示例
    ------
    ``` python
    .gsu_retrieve_from_ksib_colossus_sim_checkpoint(
                            save_photo_id_to_attr="pid",
                            save_author_id_to_attr="aid",
                            save_duration_to_attr="duration",
                            save_play_time_to_attr="play",
                            save_tag_to_attr="tag",
                            save_label_to_attr="label",
                            save_timestamp_to_attr="time",
                            service_name="grpc_colossusKwaiProV3",
                            read_thread_num=8,
                            buffer_size=10000)
    ```
    """
    self._add_processor(BatchProcessKsibCheckpointRetriever(kwargs))
    return self

  def kwaipro_gsu_for_retr(self, **kwargs):
    """ KwaiproGsuForRetrEnricher
    ------
    从 colossus 的返回结果中，按条件抽取符合条件的 photos

    参数
    ------
    `colossus_resp_attr`: [string] colossus 服务返回结果保存的 attr name. common 类型

    `bucket_attr`: [string] 用户 bucekt 的 attr name. common 类型

    `filter_future`: [bool] 是否过滤 context 时间戳之后的 photos. 默认为 False

    `retr_config`: [list] 抽取条件相关配置
      - `sort_type`: [string] 排序类型，可选值: playtime, tag
      - `count`: [int] [动态参数] 抽取的 photo 总个数
      - `tag_count`: [int] [动态参数] 如果 sort_type 是 tag, 则必填，表示 tag 个数
      - `save_to_attr`: [string] 结果写入的 commonAttr 名

    示例
    ------
    ``` python
    .kwaipro_gsu_for_retr(colossus_resp_attr="colossus_resp",
                          bucket_attr="bucket",
                          filter_future=False,
                          retr_config=[
                            {"sort_type": "playtime", "count": 100, "save_to_attr": "left_term"}
                          ])
    ```
    """
    self._add_processor(KwaiproGsuForRetrEnricher(kwargs))
    return self

  def kwaime_gsu_retriever_with_colossus_resp(self, **kwargs):
    """
    KwaiMeColossusRespRetriever
    ------

    参数
    ------
    `user_bucket_attr`: [string] 选配项 user_bucket

    `colossus_resp_attr`: [string] outupt from the colossus processor

    The following are required attrs from the resp_attr, could be empty
    ------
    `save_photo_id_to_attr`: [string] 选配项

    `save_photo_sign_to_attr`: [string] 选配项

    `save_author_id_to_attr`: [string] 选配项

    `save_author_sign_to_attr`: [string] 选配项

    `save_duration_to_attr`: [string] 选配项

    `save_play_time_to_attr`: [string] 选配项

    `save_tag_to_attr`: [string] 选配项

    `save_label_to_attr`: [string] 选配项

    `save_timestamp_to_attr`: [string] 选配项

    `save_item_attr_as_list`: [boolean] 默认 false, 对于 play_time/label/timestamp, 如果用户曾多次播放该视频，可能会出现多值。该参数为 true 时，会把这几个 item attr 存成 list. 该参数为 false 时，只会保留最后一次出现的 item 的相应 attr. 注意：其余 attr, 即 photo_id/author_id/duration/tag 不受该参数影响。

    `filter_future_attr` : [boolean] 是否只取时间在 request time 之前的 colossus pid, 默认 false

    `item_key_with_bucket` : [boolean] 默认 false

    `deduplicate` : [boolean] 默认 true

    `sign_with_bucket` : [boolean] photo author sign 是否带 bucket, 默认 true

    `photo_slot_id`: [int] 默认 0

    `author_slot_id`: [int] 默认 0

    `filter_play_time_threshold`: [int] 过滤掉 play_time 小于该值的 action item，默认 0，表示不过滤

    示例
    ------
    ``` python
    .kwaime_gsu_retriever_with_colossus_resp(
      colossus_resp_attr="colossus_output",
      filter_future_attr=True,
      filter_play_time_threshold=3000,
      save_photo_id_to_attr="pid",
      save_author_id_to_attr="aid",
      save_duration_to_attr="duration",
      save_play_time_to_attr="play",
      save_tag_to_attr="tag",
      save_label_to_attr="label",
      save_timestamp_to_attr="time"
      save_result_to_common_attr="colossus_photos")
    ```
    """
    self._add_processor(KwaiMeColossusRespRetriever(kwargs))
    return self

  def kwaime_gsu_with_cluster_lite(self, **kwargs):
    """
    KwaiMeGsuWithClusterEnricherLite
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频列表

    参数
    ------
    `colossus_photos_attr`: [string] colossus 视频列表

    `target_cluster_attr`: [string] 目标视频 cluster item attr

    `colossus_cluster_attr`: [string] 选配项 colossus 视频 cluster item attr, 默认 target_cluster_attr

    `output_searched_photos_attr`: [string] 选配项 输出视频 item attr

    `hetu_cluster_config_kconf_key`: [string] 选配项 聚类 cluster 距离最近的搜索聚类 Kconf 配置, 默认值 overseaReco.offline.kwaiMeMMUClusterIndex

    `max_search_clusters`: [int] 最大 cluster 搜索个数，默认 50

    `top_n`: [int] 输出视频最大长度，默认 -1，不限制

    `sort_by_cluster_distance` : [boolean] 是否按距离距离排序, 默认 false

    示例
    ------
    ``` python
    .kwaime_gsu_with_cluster_lite(
      hetu_cluster_config_kconf_key="overseaReco.offline.kwaiMeMMUClusterIndex",
      target_cluster_attr="mmu_cluster",
      colossus_photos_attr="colossus_photos",
      output_searched_photos_attr="colossus_searched_photos",
      sort_by_cluster_distance=True)
    ```
    """
    self._add_processor(KwaiMeGsuWithClusterEnricherLite(kwargs))
    return self

  def kwaime_gsu_tower_sort_post(self, **kwargs):
    """
    KwaiMeGsuTowerSortPostEnricher
    ------
    将 common_attr 中的 `*std::vector<double>` 转化为各个item对应的 `vector<double>`，排序
    并返回最大的 n 个视频

    参数
    ------
    `common_distance_ptr_attr` : [string] 距离向量 *std::vector<double>

    `common_colossus_photos_attr` : [string] colossus 视频列表

    `item_colossus_photos_attr` : [string] 选配项 处理过的 colossus 视频列表, 为空时使用 common_colossus_photos_attr

    `output_searched_photos_attr`: [string] 选配项 输出视频 item attr

    `output_item_distance_attr` : [string] 选配项 转换并排序后的 top n 结果存入的 item attr

    `output_item_raw_distance_attr` ： [string] 选配项 未经排序的原始 distance 序列存入的 item_attr

    `top_n`: [int] 输出视频最大长度，默认 100，-1 不限制

    示例
    ------
    ``` python
    .kwaime_gsu_tower_sort_post(
      common_distance_ptr_attr="distance",
      common_colossus_photos_attr="colossus_photos",
      item_colossus_photos_attr="colossus_searched_photos",
      output_searched_photos_attr="colossus_searched_photos")
    ```
    """
    self._add_processor(KwaiMeGsuTowerSortPostEnricher(kwargs))
    return self

  def kwaime_gsu_sign_feature(self, **kwargs):
    """
    KwaiMeGsuSignFeatureEnricher
    ------
    colossus search 输出的视频列表转成 sign feature

    参数
    ------
    `searched_colossus_photos_attr` : [string] 结果视频列表 item attr

    `photo_id_attr` : [string] pid 对应的 pid item attr

    `author_id_attr` : [string] pid 对应的 aid item attr

    `target tag_attr` : [string] target pid 对应的 tag item attr

    `tag_attr` : [string] pid 对应的 tag item attr

    `play_time_attr` : [string] pid 对应的 play time item attr

    `duration_attr` : [string] pid 对应的 duration item attr

    `timestamp_attr` : [string] pid 对应的 timestamp item attr

    `output_sign_attr`: [string] sign 输出 item attr

    `output_slot_attr`: [string] slot 输出 item attr

    `bucket_attr` : [string] bucket item attr

    `sign_with_bucket` : [boolean] 是否使用 bucket 填充 pid aid sign, 默认 true

    `slot_as_attr_name` : [boolean] 是否输出 slot attr (unifea 方式), 默认 false

    `top_n`: [int] 输出 sign 最大长度，默认 100

    `mio_slots_id`: [int_list] 选配项 区分 embedding 特征的 slot 列表, 默认值 [690, 691, 692, 694, 693]

    `slots_id`: [int_list] 选配项 sign 对应的 slot 列表, 默认值 [123, 122, 692, 694, 693]

    示例
    ------
    ``` python
    .kwaime_gsu_sign_feature(
      searched_colossus_photos_attr="colossus_searched_photos",
      photo_id_attr="pid",
      author_id_attr="aid",
      tag_attr="tag",
      play_time_attr="play",
      duration_attr="duration",
      timestamp_attr="time",
      output_sign_attr="gsu_signs",
      output_slot_attr="gsu_slots",
      bucket_attr="photo_bucket")
    ```
    """
    self._add_processor(KwaiMeGsuSignFeatureEnricher(kwargs))
    return self

  def picasso_ad_sim_enrich(self, **kwargs):
    """
    PicassoAdSimEnricher
    ------
    从 picasso 获取广告用户行为数据

    参数
    ------
    `self_service_name`: [string] 自身服务名

    `target_service_name`: [string] picasso 服务名, 可选填 grpc_adPicassoGatewayService / grpc_adPicassoOfflineGatewayService

    `output_ad_user_action_from_picasso`: [string] 从 picasso 获取的广告用户行为


    示例
    ------
    ``` python
    .picasso_ad_sim_enrich(
      self_service_name="dragon_service",
      target_service_name="grpc_adPicassoGatewayService",
      output_ad_user_action_from_picasso="ad_user_action")
    ```
    """
    self._add_processor(PicassoAdSimEnricher(kwargs))
    return self

  def ksib_live_gsu_retriever_with_colossus_resp(self, **kwargs):
    """
    KsibLiveColossusRespRetriever
    ------

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    The following are required attrs from the resp_attr, could be empty
    ------
    `save_live_id_to_attr`: [string] 选配项

    `save_author_id_to_attr`: [string] 选配项

    `save_duration_to_attr`: [string] 选配项，进入直播间时主播已开播时长

    `save_play_time_to_attr`: [string] 选配项

    `save_tag_to_attr`: [string] 选配项，参考 https://docs.corp.kuaishou.com/d/home/<USER>

    `save_label_to_attr`: [string] 选配项，参考 https://docs.corp.kuaishou.com/d/home/<USER>

    `save_timestamp_to_attr`: [string] 选配项

    `save_auto_play_time_to_attr`: [string] 选配项

    `save_item_attr_as_list`: [boolean] 默认 false, 对于 play_time/label/timestamp, 如果用户曾多次播放该视频，可能会出现多值。该参数为 true 时，会把这几个 item attr 存成 list. 该参数为 false 时，只会保留最后一次出现的 item 的相应 attr. 注意：其余 attr, 即 photo_id/author_id/duration/tag 不受该参数影响。

    `filter_future_attr` : [boolean] 是否只取时间在 request time 之前的 colossus pid, 默认 false

    `deduplicate` : [boolean] 默认 true

    `photo_slot_id`: [int] 默认 0

    `author_slot_id`: [int] 默认 0

    `filter_play_time_threshold`: [int] 过滤掉 play_time 小于该值的 action item，默认 0，表示不过滤

    示例
    ------
    ``` python
    .ksib_live_gsu_retriever_with_colossus_resp(
      colossus_resp_attr="colossus_output",
      filter_future_attr=True,
      filter_play_time_threshold=3000,
      save_live_id_to_attr="liveid",
      save_author_id_to_attr="aid",
      save_duration_to_attr="duration",
      save_play_time_to_attr="play",
      save_tag_to_attr="tag",
      save_label_to_attr="label",
      save_timestamp_to_attr="time"
      save_result_to_common_attr="colossus_photos")
    ```
    """
    self._add_processor(KsibLiveColossusRespRetriever(kwargs))
    return self

  def slide_gsu_with_cluster(self, **kwargs):
    """
    CommonSlideGsuWithClusterPidEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 pid

    参数
    ------
    `colossus_resp_attr`: [string] CommonAttr 名, 用于输入 colossus 的 response, 类型为 String

    `limit_num_attr`: [string] CommonAttr 名, 用于限定每个 photo 返回 pid 数目的上限, 类型为 Int

    `colossus_cut_size`: [int] 选配项，读取的 colossus 截断长度，默认值 -1, 不截断

    `timeout_ms`: [int] 选配项，查询的超时时间，默认为 10 ms

    `kess_service`: [string] 查询 embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数, pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] 选配项, embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_pids_per_request`: [int] 选配项, 单次 rpc 请求中的最大 pid 个数，为 0 代表不限制

    `output_pid_attr`: [string] ItemAttr 名, 用于输出相似视频 pid 列表, 类型为 IntList

    `output_cluster_attr`: [string] 选配项, ItemAttr 名, 用于输出视频所属 cluster, 类型为 Int

    `output_aid_attr`: [string] 选配项, ItemAttr 名, 用于输出相似视频 aid 列表, 类型为 IntList

    `output_diffs_attr`: [string] 选配项, ItemAttr 名, 用于输出相似视频曝光时间天数差列表, 类型为 IntList

    `output_label_attr`: [string] 选配项, ItemAttr 名, 用于输出相似视频 label 列表, 类型为 IntList

    `output_duration_attr`: [string] 选配项, ItemAttr 名, 用于输出相似视频 duration(s) 列表, 类型为 IntList

    `output_playtime_attr`: [string] 选配项, ItemAttr 名, 用于输出相似视频 playtime(s) 列表, 类型为 IntList

    示例
    ------
    ``` python
    .slide_gsu_with_cluster(
        colossus_resp_attr="colossus_output",
        limit_num_attr="limit_num",
        kess_service='kws-kuaishou-nebula-embedding-mmu-hetu-id-online-for-ltr',
        shards=4,
        output_cluster_attr="cluster_id",
        output_pid_attr="searched_pid_list"
      )
    ```
    """
    self._add_processor(CommonSlideGsuWithClusterPidEnricher(kwargs))
    return self

  def extract_feature_from_colossus_response(self, **kwargs):
    """ ExtractSignsFromColossusEnricher
    ------
    从 colossus 的返回结果中，抽取 PID, timediff 和 play & duration 作为计算 SIM 特征的

    参数
    ------
    `colossus_output`: [string] colossus 服务返回结果保存的 attr name. common 类型

    `colossus_output_type`: [string] colossus 返回数据类型，可选项: "sim_item" 或 "commmon_item", 默认为 "sim_item"

    `output_signs_attr`: [string] 保存抽取到的 sign 的 attr name

    `output_slots_attr`: [string] 保存 sign 对应 slot 的 attr

    `output_mask_bias_attr`: [string] 保存 mask bias 的 attr name

    `mask_bias_value`: [float] padding 或者 filter 掉的 item 对应的 bias 值，默认为 -1000.0

    `colossus_item_num_limit`: [int] mask_bias 需要 padding 到的 colossu_item_num

    `timediff_bias_slot_id`: [int] timediff 对应的 slot id

    `item_slot_id`: [int] PID 对应的 slot id

    `play_bias_slot_id`: [int] play bias 对应的 slot id

    `label_bias_slot_id`: [int] label bias 对应的 slot id

    `channel_bias_slot_id`: [int] channel bias 对应的 slot id

    `colossus_item_num_attr`: [int] 保存 colossus 结果数的 common attr_name

    `profile_stay_time_bias_slot_id`: [int] profile_stay_time bias 对应的 slot id

    `comment_stay_time_bias_slot_id`: [int] comment_stay_time bias 对应的 slot id

    `colossus_service_name`: [int] colossus服务的kess_name，默认为 grpc_colossusSimV2

    `colossus_photo_id_field_name`: [int] colossus服务中photo_id的域名, 默认为 photo_id

    `colossus_author_id_field_name`: [int] colossus服务中author_id的域名，默认为 author_id

    `colossus_play_time_field_name`: [int] colossus服务中play_time的域名，默认为 play_time

    `colossus_duration_field_name`: [int] colossus服务中duration的域名，默认为 duration

    `colossus_timestamp_field_name`: [int] colossus服务中timestamp的域名，默认为 timestamp

    `colossus_channel_field_name`: [int] colossus服务中channel的域名，默认为 channel

    `colossus_label_field_name`: [int] colossus 服务中 label 的域名，默认为 label

    `colossus_profile_stay_time_field_name`: [int] colossus服务中colossus_profile_stay_time的域名，默认为空

    `colossus_comment_stay_time_field_name`: [int] colossus服务中comment_stay_time的域名，默认为空

    `extract_label_fea_switch`: [int] 是否抽取label特征，默认为false

    `skip_latest_items_seconds`: [int][动态参数] 跳过最近 T seconds 内的 item，默认 60。

    示例
    ------
    ``` python
    .extract_feature_from_colossus_response(colossus_output="colossus_output",
                          output_signs_attr="colossus_signs",
                          output_slots_attr="colossus_slots",
                          timediff_bias_slot_id=1006,
                          pid_slot_id=1007,
                          play_bias_slot_id=1008,
                          label_bias_slot_id=1009,
                          channel_bias_slot_id=1010,
                          colossus_item_num_attr="item_num",
                          profile_stay_time_bias_slot_id=1011,
                          comment_stay_time_bias_slot_id=1012
                          )
    ```
    """
    self._add_processor(ExtractFeatureFromColossusResponseEnricher(kwargs))
    return self

  def extract_interval_feature_from_colossus_response(self, **kwargs):
    """ ExtractIntervalFeatureFromColossusResponseEnricher 
     ------
    从 colossus 中按一定时间间隔抽取视频特征，抽取 pid aid timediff 和 play & duration 等

    参数
    ------
    `colossus_output`: [string] colossus 服务返回结果保存的 attr name. common 类型

    `colossus_output_type`: [string] colossus 返回数据类型，可选项: "sim_item" 或 "commmon_item", 默认为 "sim_item"

    `output_signs_attr`: [string] 保存抽取到的属性特征的 sign 的 attr name

    `output_slots_attr`: [string] 保存抽取到的属性特征的 slot 的 attr name

    `output_pid_signs_attr_`: [string] 保存pid signs 的 attr name

    `output_pid_slots_attr_`: [string] 保存pid slots 的 attr name

    `output_aid_signs_attr_`: [string] 保存aid signs 的 attr name

    `output_aid_slots_attr_`: [string] 保存aid slots 的 attr name

    `item_slot_id`: [int] PID 对应的 slot id

    `author_slot_id`: [int] author id 对应的 slot id

    `tag_slot_id`: [int] Sim tag 对应的 slot id

    `timediff_slot_id`: [int] timediff 对应的 slot id

    `play_slot_id`: [int] play bias 对应的 slot id

    `label_slot_id`: [int] label bias 对应的 slot id

    `channel_slot_id`: [int] channel bias 对应的 slot id

    `dist_slot_id`: [int] dist(hop & mod) 对应的 slot id

    `profile_stay_time_slot_id`: [int] profile_stay_time bias 对应的 slot id

    `comment_stay_time_slot_id`: [int] comment_stay_time bias 对应的 slot id

    `mio_slot_type`: [int] mio slot id 的状态(0:和上面slot id一致(默认), 1:和SIM的底层325 324系列id一致)

    `mio_item_slot_id`: [int] PID 对应的 mio slot id(配置此处会覆盖上面的mio_slot_type逻辑,下同)

    `mio_author_slot_id`: [int] author id 对应的 mio slot id

    `mio_tag_slot_id`: [int] Sim tag 对应的 mio slot id

    `mio_timediff_slot_id`: [int] timediff 对应的 mio slot id

    `mio_play_slot_id`: [int] play bias 对应的 mio slot id

    `mio_label_slot_id`: [int] label bias 对应的 mio slot id

    `mio_channel_slot_id`: [int] channel bias 对应的 mio slot id

    `mio_profile_stay_time_slot_id`: [int] profile_stay_time bias 对应的 mio slot id

    `mio_comment_stay_time_slot_id`: [int] comment_stay_time bias 对应的 mio slot id

    `mio_dist_slot_id`: [int] dist(hop & mod) 对应的 mio slot id(新增特征，与timediff类似但不同)

    `colossus_service_name`: [int] colossus服务的kess_name，默认为 grpc_colossusSimV2

    `colossus_photo_id_field_name`: [int] colossus服务中photo_id的域名, 默认为 photo_id

    `colossus_author_id_field_name`: [int] colossus服务中author_id的域名，默认为 author_id

    `colossus_play_time_field_name`: [int] colossus服务中play_time的域名，默认为 play_time

    `colossus_duration_field_name`: [int] colossus服务中duration的域名，默认为 duration

    `colossus_timestamp_field_name`: [int] colossus服务中timestamp的域名，默认为 timestamp

    `colossus_channel_field_name`: [int] colossus服务中channel的域名，默认为 channel

    `colossus_label_field_name`: [int] colossus 服务中 label 的域名，默认为 label

    `colossus_profile_stay_time_field_name`: [int] colossus服务中colossus_profile_stay_time的域名，默认为空

    `colossus_comment_stay_time_field_name`: [int] colossus服务中comment_stay_time的域名，默认为空

    `extract_label_fea_switch`: [int] 是否抽取label特征，默认为false

    `interval_point_offset_s`: [int] 采样基准时间偏移量(参照当前请求时间)，单位：秒，下同

    `interval_every_k_s`: [int] 采样时间间隔，默认按天采样(24 * 3600)

    `interval_lower_bound_s`: [int] 单个采样点向更早多少时间范围内样本入选(采样点为五点，采样下界四点半，则设置1800，默认3600)

    `interval_upper_bound_s`: [int] 同上类似，指上界

    `interval_latest_offset_s`: [int] 采样总样本区间的最近时间(为偏移量，例：最早当前请求一小时前的样本，则设置3600，默认1800)

    `interval_oldest_offset_s`: [int] 采样总样本区间的最早时间(为偏移量，例：15天内的样本，则设置3600*24*15，默认值30天)

    `interval_bound_max_nums`: [int] 单个采样点的最大采样个数，默认：-1(不限制)，如果不限制则会尽可能用时间距离近的样本填充

    `interval_total_max_nums`: [int] 总最大采样样本个数，默认值:50

    `skip_latest_items_seconds`: [int][动态参数] 跳过最近 T seconds 内的 item，默认 60。

    示例
    ------
    ``` python
    .colossus(
      service_name='grpc_colossusSimV2',
      client_type='common_item_client',
      parse_to_pb=False,
      print_items=True,
      output_attr='colossus_resp') \
    .extract_interval_feature_from_colossus_response(
      colossus_output="colossus_resp",
      colossus_output_type="common_item",
      output_signs_attr="colossus_day_interval_signs",
      output_slots_attr="colossus_day_interval_slots",
      output_pid_signs_attr="colossus_day_interval_pid_signs",
      output_pid_slots_attr="colossus_day_interval_pid_slots",
      output_aid_signs_attr="colossus_day_interval_aid_signs",
      output_aid_slots_attr="colossus_day_interval_aid_slots",
      item_slot_id=1900,
      author_slot_id=1901,
      timediff_slot_id=1902,
      play_slot_id=1903,
      label_slot_id=1904,
      channel_slot_id=1905,
      profile_stay_time_slot_id=1906,
      comment_stay_time_slot_id=1907,
      profile_feed_mode_stay_time_slot_id_=1908,
      real_show_index_slot_id=1909,
      tag_slot_id=1911,
      colossus_service_name="grpc_colossusSimV2",
      extract_label_fea_switch=True,
      interval_point_offset_s=600,
      interval_every_k_s=3600*24,
      interval_lower_bound_s=1800,
      interval_upper_bound_s=1800,
      interval_latest_offset_s=3600*18,
      interval_oldest_offset_s=3600*24*30,
      interval_bound_max_nums=30,
      interval_total_max_nums=60
    ) \
    ```
    """
    self._add_processor(ExtractIntervalFeatureFromColossusResponseEnricher(kwargs))
    return self

  def extract_user_stat_feature_from_colossus_response(self, **kwargs):
    """ ExtractUserStatFeatureFromColossusResponseEnricher 
     ------
    从 colossus 中按duration分桶统计用户的有效播放百分位值

    参数
    ------
    `colossus_output`: [string] colossus 服务返回结果保存的 attr name. common 类型

    `colossus_output_type`: [string] colossus 返回数据类型，可选项: "sim_item" 或 "commmon_item", 默认为 "sim_item"

    `output_values_attr`: [string] 带统计平滑的duration分桶内的实际个数，大部分情况为lower_bound_num

    `output_dura1_attr`: [string] 分桶的界，在当前duration的前后多少时间选了值，取钱后时间的最大值

    `output_dura2_attr`: [string] 分桶的实际数量，用户行为中对应duration的实际样本数

    `output_dura3_attr`: [string] p75 / duration 的排序位置,

    `output_perc_attr`: [string] 哪一个百分位，每个duration的n个百分位值

    `rng_bar_ratio`: [string] 当前duration的前后多少百分比范围样本进行填充

    `lower_bound_num`: [string] 每个桶最少多少个行为

    `colossus_service_name`: [int] colossus服务的kess_name，默认为 grpc_colossusSimV2

    示例
    ------
    ``` python
    .colossus(
      service_name='grpc_colossusSimV2',
      client_type='common_item_client',
      parse_to_pb=False,
      print_items=True,
      output_attr='colossus_resp') \
    .extract_user_stat_feature_from_colossus_response(
      colossus_output="colossus_resp",
      colossus_output_type="common_item",
      output_values_attr="stat_values",
      output_dura1_attr="stat_dura1",
      output_dura2_attr="stat_dura2",
      output_dura3_attr="stat_p75_index",
      output_perc_attr="stat_percentile"
    ) \
    ```
    """
    self._add_processor(ExtractUserStatFeatureFromColossusResponseEnricher(kwargs))
    return self

  def general_extract_feature_from_colossus_response(self, **kwargs):
    """ GeneralExtractFeatureFromColossusResponseEnricher
    ------
    从 colossus 的返回结果中，抽取 extract_config 指定的特征 作为计算 SIM 特征的

    参数
    ------
    `bucket_attr`: [string] photo bucket值的 common attr, 默认为空, 给海外用的

    `colossus_output`: [string] colossus 服务返回结果保存的 attr name. common 类型

    `colossus_output_type`: [string] colossus 返回数据类型，可选项: "sim_item"、"common_item"、"common_item_index", "colossus_item"、"ksib_colossus_batch_item", "colossusv2", "ksib_colossusv2", 默认为 "sim_item"

    `colossus_item_index_attr `: [string] 当 colossus_output_type==common_item_index 时，必须配置此项，指定 colossus response 中需要抽取特征的 item index 序列

    `colossus_reflect_schema_type`: [string] 当 colossus_output_type==common_item_index 时，必须配置此项，指定 colossus response field 的读取方式

    `colossusv2_reflection_input_attr`: [string] 当 colossus_output_type==colossusv2 或者 ksib_colossusv2时，必须配置此项，指定colossus response的读取方式

    `colossusv2_item_datas_input_attr`: [string] 当 colossus_output_type==colossusv2 或者 ksib_colossusv2时，必须配置此项，指定colossus response的数据存储

    `output_signs_attr`: [string] 保存抽取到的 sign 的 attr name

    `output_slots_attr`: [string] 保存 sign 对应 slot 的 attr

    `output_mask_bias_attr`: [string] 保存 mask bias 的 attr name

    `output_colossus_item_key_attr`: [string] 保存 colossus items id 的 common attr name

    `mask_bias_value`: [float] padding 或者 filter 掉的 item 对应的 bias 值，默认为 -1000.0

    `colossus_item_num_limit`: [int] mask_bias 需要 padding 到的 colossu_item_num

    `slot_high_bits`: [int] 生成的特征 sign 时 slot 占多少高位, mio 为16, kuiba 为10，默认为16

    `join_pid_attr`: [string] 可选,用于join的pid list

    `pid_bits`: [int] join的pid bit

    `keep_filtered_item`: [bool] 抽特征时是否用 0 对被过滤的 colossus item 进行占位处理, 默认为 false，主要用于需要抽取的 feature 和输入的 colossus response items 保持位置映射的场景

    `filter_type`: [string] 用户历史行为 item 过滤方式:
                  FILTER_DEFAULT（主站精排）、
                  FILTER_IGNORE（主站粗排）、
                  FILTER_PLAYTIME（海外粗排）、
                  FILTER_CHANNEL（内流、精选）、
                  FILTER_PLAYTIME_AND_CHANNEL

    `filter_param` : [dict] 一些 filter 需要用到的参数

    `filter_param::playtime_lowerbound` : [int] item.play_time 小于该配置时过滤，FILTER_PLAYTIME/FILTER_PLAYTIME_AND_CHANNEL 需要用到，默认为 0

    `filter_param::channel_whitelist` : [set of int] item.channel 不在该配置里的将被过滤，FILTER_CHANNEL/FILTER_PLAYTIME_AND_CHANNEL 需要用到，默认为空

    `extract_config`: [list] of [dict], sim 特征抽取配置

    `extract_config::name`: [string] 特征抽取方法名，目前支持 photo_id/author_id/tag/play_time/time_diff/label
                            /channel/ksib_pid/ksib_aid/ksib_tag

    `extract_config::slot_id_in_sign`: [int] 特征 sign 的 slot id

    `extract_config::slot_id_in_model`: [int] 特征在模型输入中的 slot id, 不配置则取 slot_id_in_sign 的值

    示例
    ------
    ``` python
    .general_extract_feature_from_colossus_response(
                          bucket_attr="photo_bucket",
                          colossus_output="colossus_output",
                          colossus_output_type="ksib_colossus_batch_item",
                          output_signs_attr="colossus_signs",
                          output_slots_attr="colossus_slots",
                          output_colossus_item_key_attr="gsu_items",
                          output_mask_bias_attr="gsu_item_mask",
                          filter_type="FILTER_PLAYTIME",
                          extract_config = [
                            dict(name="ksib_pid", slot_id_in_sign=123, slot_id_in_model=690),
                            dict(name="ksib_aid", slot_id_in_sign=122, slot_id_in_model=691),
                            dict(name="ksib_tag", slot_id_in_sign=692),
                            dict(name="play_time", slot_id_in_sign=694),
                            dict(name="time_diff", slot_id_in_sign=693),
                          ],
                          )

    # use response of colossusv2
    .gsu_common_colossusv2_enricher(kconf="colossus.kconf_client.video_item",
        limit=10000,
        item_fields=dict(photo_id="",
                          timestamp="",
                          # 这里必须是 author_id_v2
                          author_id_v2="",
                          play_time="",
                          duration="",
                          tag="",
                          channel="",
                          label="",
                          profile_stay_time="",
                          comment_stay_time="",
                          profile_feed_mode_stay_time="",
                          real_show_index=""),
        reflection_output_attr="video_item_reflection",
        item_datas_output_attr="video_item_colossus_data")
    .general_extract_feature_from_colossus_response(
                          colossusv2_reflection_input_attr="video_item_reflection",
                          colossusv2_item_datas_input_attr="video_item_colossus_data",
                          colossus_output_type="colossusv2",
                          # 这里必须是 author_id_v2
                          colossus_author_id_field_name="author_id_v2",
                          bucket_attr="photo_bucket",
                          output_signs_attr="colossus_signs",
                          output_slots_attr="colossus_slots",
                          output_colossus_item_key_attr="gsu_items",
                          output_mask_bias_attr="gsu_item_mask",
                          filter_type="FILTER_PLAYTIME",
                          extract_config = [
                            dict(name="ksib_pid", slot_id_in_sign=123, slot_id_in_model=690),
                            dict(name="ksib_aid", slot_id_in_sign=122, slot_id_in_model=691),
                            dict(name="ksib_tag", slot_id_in_sign=692),
                            dict(name="play_time", slot_id_in_sign=694),
                            dict(name="time_diff", slot_id_in_sign=693),
                          ],
                          )
    ```
    """
    self._add_processor(GeneralExtractFeatureFromColossusResponseEnricher(kwargs))
    return self

  def general_extract_feature_by_colossus_reflect(self, **kwargs):
    """ GeneralExtractFeatureByColossusReflectEnricher
    ------
    从 colossus 的返回结果中，抽取 extract_config 指定的特征 作为计算 SIM 特征的,
    相较于 GeneralExtractFeatureFromColossusResponseEnricher, 本方法有更好的泛用性

    参数
    ------
    `bucket_attr`: [string] photo bucket值的 common attr, 默认为空, 给海外用的

    `colossus_output`: [string] colossus 服务返回结果保存的 attr name. common 类型

    `colossus_output_type`: [string] colossus 返回数据类型，可选项: "common_item"、"common_item_index"

    `colossus_item_index_attr `: [string] 当 colossus_output_type==common_item_index 时，必须配置此项，指定 colossus response 中需要抽取特征的 item index 序列

    `colossus_reflect_schema_type`: [string] 当 colossus_output_type==common_item_index 时，必须配置此项，指定 colossus response field 的读取方式

    `output_signs_attr`: [string] 保存抽取到的 sign 的 attr name

    `output_slots_attr`: [string] 保存 sign 对应 slot 的 attr

    `output_mask_bias_attr`: [string] 保存 mask bias 的 attr name

    `output_colossus_item_key_attr`: [string] 保存 colossus items id 的 common attr name

    `mask_bias_value`: [float] padding 或者 filter 掉的 item 对应的 bias 值，默认为 -1000.0

    `colossus_item_num_limit`: [int] mask_bias 需要 padding 到的 colossu_item_num

    `slot_high_bits`: [int] 生成的特征 sign 时 slot 占多少高位, mio 为16, kuiba 为10，默认为16

    `keep_filtered_item`: [bool] 抽特征时是否用 0 对被过滤的 colossus item 进行占位处理, 默认为 false，主要用于需要抽取的 feature 和输入的 colossus response items 保持位置映射的场景

    `read_fields`: [list] of [dict], 期望抽特征的 colossus response fields + 过滤所需 fields 的配置

    `read_fields::field`: [string], 期望抽特征的 colossus response field name

    `read_fields::is_item_id`: [bool], 标注该字段是否为 colossus item id 字段,
                               默认 false，只能有一个配置为 true, 否则出错

    `read_fields::default_int`: [int] int 类型 field 缺失时的默认填充值，与 default_double 二选一

    `read_fields::default_double`: [double] double 类型 field 缺失时的默认填充值，与 default_int 二选一

    `filter_config`: [list] of [dict],  根据 colossus response field 对 colossus item 进行过滤,
                     可配置多个 filter，过滤关系为“或” —— 即任意 filter 生效即过滤 item

    `filter_config::name`: [string] filter 类型名字, 一个类型的 fitler 可同时配置与多个 field,
                           目前支持 item_id/timestamp/play_time/channel 过滤

    `filter_config::field`: [string] filter 过滤执行的 colossus field name, filter 将针对该 colossus field 的 值进行判定,

    `filter_config::playtime_lowerbound`: [int] 可选配置，play_time filter 所需，指定 playtime 下界

    `filter_config::channel_whitelist`: [list] of [int], channel fitler 所需，指定过滤白名单,
                                        item channel 不在此 list 将被过滤

    `extract_config`: [list] of [dict], sim 特征抽取配置

    `extract_config::name`: [string] 特征抽取方法类别名，目前支持 plain/ksib_plain/play_time/time_diff/ksib_tag,
                            一类方法可以同时用于多个 field 抽取,
                            plain: 即常规的 slot 占据搞 16 bits、value 占据低 48 bits，
                            ksib_plain: ksib bucket 占据高 8 bits, slot 占据 中间的 16 bits, value 占据低 40 bits

    `extract_config::field`: [string]  待抽取特征的 colossus filed name

    `extract_config::slot_id_in_sign`: [int] 特征 sign 的 slot id

    `extract_config::slot_id_in_model`: [int] 特征在模型输入中的 slot id, 不配置则取 slot_id_in_sign 的值

    `extract_config::duration_field`: [string] play_time extractor 特需的配置，指定配合抽取所需的 colossus duration field

    示例
    ------
    ``` python
    .general_extract_feature_by_colossus_reflect(
                          colossus_output="colossus_output",
                          colossus_output_type="common_item",
                          colossus_reflect_schema_type="grpc_colossusSimV2",
                          output_signs_attr="colossus_signs",
                          output_slots_attr="colossus_slots",
                          output_colossus_item_key_attr="gsu_items",
                          output_mask_bias_attr="gsu_item_mask",
                          read_fields = [
                            dict(field="photo_id", default_int=0, is_item_id=True),
                            dict(field="author_id", default_int=0),
                            dict(field="timestamp", default_int=0),
                            dict(field="tag", default_int=-1),
                            dict(field="play_time", default_int=0),
                            dict(field="time_diff", default_int=0),
                            dict(field="duration", default_int=0),
                            dict(field="example_blabla", default_double=0.234234),
                          ],
                          filter_config= [
                            dict(name="item_id", field="live_id"),
                            dict(name="timestamp", field="timestamp"),
                            dict(name="play_time", field="play_time", playtime_lowerbound=100),
                            dict(name="channel", field="channel", channel_whitelist=[1,2,5,100]),
                          ],
                          extract_config = [
                            dict(name="plain", field="photo_id", slot_id_in_sign=255, slot_id_in_model=1255),
                            dict(name="plain", field="author_id", slot_id_in_sign=256, slot_id_in_model=1256),
                            dict(name="ksib_plain", field="ksib_pid", slot_id_in_sign=123, slot_id_in_model=690),
                            dict(name="ksib_plain", field="ksib_aid", slot_id_in_sign=122, slot_id_in_model=691),
                            dict(name="play_time", field="play_time", slot_id_in_sign=694, duration_field="duration"),
                            dict(name="time_diff", field="timestamp", slot_id_in_sign=693),
                            dict(name="ksib_tag", field="ksib_tag", slot_id_in_sign=692),
                          ],
                          )
    ```
    """
    self._add_processor(GeneralExtractFeatureByColossusReflectEnricher(kwargs))
    return self

  def generate_slot_sign_for_ids(self, **kwargs):
    """ GenerateSlotSignAttrEnricher
    ------
    从 colossus 的返回结果中，抽取 extract_config 指定的特征 作为计算 SIM 特征的

    参数
    ------
    `bucket_attr`: [string] bucket值的 common attr, 特定的 item_sign_type 才需要

    `from_common_attr`: [string] 如果配置，则从 common attr 获取 key 进行 sign 变换, 优先级高于 from_item_attr

    `from_item_attr`: [string] 如果配置，则从 item attr 获取 key 进行 sign 变换, 否则用 item_key 进行变换

    `output_signs_attr`: [string] 抽取的 sign 存放的 attr，from_common_attr 不为空则存 common attr， 否则存 item attr

    `output_slots_attr`: [list] of [dict]

    `output_slots_attr::attr_name`: [string] 抽取的 slot 存放的 attr，from_common_attr 不为空则存 common attr， 否则存 item attr

    `output_slots_attr::slot_id`: [int] 抽取的 slot 存放的 attr，from_common_attr 不为空则存 common attr， 否则存 item attr

    `item_sign_type`: [string] key -> sign 的转换方式, 支持
                      PID_48BIT_DEFAULT | PID_48BIT_WITH_BUCKET | PID_56BIT_WITH_BUCKET |
                      KSIB_PID_56BIT_WITH_BUCKET | RAW_ID | SLOT_SIGN

    `slot_id_in_sign`: [int] SLOT_SIGN 专属配置项，sign 高位要存放的 slot id

    `slot_high_bits`: [int] SLOT_SIGN 专属配置项，slot 占据 sign 高位的位数

    示例
    ------
    ``` python
    .generate_slot_sign_for_ids(
                          bucket_attr="bucket",
                          from_common_attr="gsu_items",
                          item_sign_type="KSIB_PID_56BIT_WITH_BUCKET",
                          output_signs_attr="gsu_item_signs",
                          output_slots_attr= [
                            dict(attr_name="fake_slot1", slot_id=123),
                            dict(attr_name="fake_slot2", slot_id=343),
                          ],
                          )
    ```
    """
    self._add_processor(GenerateSlotSignAttrEnricher(kwargs))
    return self

  def ksib_extract_feature_from_colossus_response(self, **kwargs):
    """ ExtractSignsFromColossusEnricher
    ------
    从 colossus 的返回结果中，抽取 PID, timediff 和 play & duration 作为计算 SIM 特征的

    参数
    ------
    `colossus_output`: [string] colossus 服务返回结果保存的 attr name. common 类型

    `colossus_output_type`: [string] colossus 返回数据类型，可选项: "sim_item"、"commmon_item"、"ksib_colossusv2", 默认为 "sim_item"

    `colossusv2_reflection_input_attr`: [string] 当 colossus_output_type==colossusv2 或者 ksib_colossusv2时，必须配置此项，指定colossus response的读取方式

    `colossusv2_item_datas_input_attr`: [string] 当 colossus_output_type==colossusv2 或者 ksib_colossusv2时，必须配置此项，指定colossus response的数据存储

    `output_signs_attr`: [string] 保存抽取到的 sign 的 attr name

    `output_slots_attr`: [string] 保存 sign 对应 slot 的 attr

    `output_mask_bias_attr`: [string] 保存 mask bias 的 attr name

    `mask_bias_value`: [float] padding 或者 filter 掉的 item 对应的 bias 值，默认为 -1000.0

    `colossus_item_num_limit`: [int] mask_bias 需要 padding 到的 colossu_item_num

    `timediff_bias_slot_id`: [int] timediff 对应的 slot id

    `item_slot_id`: [int] PID 对应的 slot id

    `play_bias_slot_id`: [int] play bias 对应的 slot id

    `label_bias_slot_id`: [int] label bias 对应的 slot id

    `channel_bias_slot_id`: [int] channel bias 对应的 slot id

    `colossus_item_num_attr`: [int] 保存 colossus 结果数的 common attr_name

    示例
    ------
    ``` python
    .ksib_extract_feature_from_colossus_response(colossus_output="colossus_output",
                          output_signs_attr="colossus_signs",
                          output_slots_attr="colossus_slots",
                          timediff_bias_slot_id=1006,
                          pid_slot_id=1007,
                          play_bias_slot_id=1008,
                          label_bias_slot_id=1009,
                          channel_bias_slot_id=1010,
                          colossus_item_num_attr="item_num"
                          )
    ```
    """
    self._add_processor(KsibExtractFeatureFromColossusResponseEnricher(kwargs))
    return self

  def fill_colossus_items(self, **kwargs):
    """ FillColossusItemsEnricher
    ------
    将 colossus 的返回结果保存到 ColossusItem 的数组种

    参数
    ------
    `colossus_output`: [string] colossus 服务返回结果保存的 attr name. common 类型

    `colossus_output_type`: [string] colossus 返回数据类型，可选项: "sim_item" 或 "commmon_item", 默认为 "sim_item"

    `output_colossus_item_attr`: [int] 保存结果的 common attr_name

    示例
    ------
    ``` python
    .fill_colossus_item(colossus_output="colossus_output",
                        colossus_output_type="sim_item",
                        output_colossus_item_attr="colossus_items")
    ```
    """
    self._add_processor(FillColossusItemsEnricher(kwargs))
    return self

  def ad_live_gsu_with_cluster(self, **kwargs):
    """
    CommonAdLiveGsuWithClusterEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    `cluster_id_type`: [string] 使用哪种域cluster id进行相似度计算，包括all_domain_cluster_id, merchant_domain_cluster_id  两种类型

    `filter_live_data`: [bool] 是否只保留直播数据 根据数据中的 label 第一位 photoItemimp过滤得到 默认为 false

    示例
    ------
    ``` python
    .ad_live_gsu_with_cluster(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='ad_live_gsu_sign',
                      output_slot_attr='ad_live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                      cluster_id_type = "all_domain_cluster_id",
                      filter_live_data = False
                      )
    ```
    """
    self._add_processor(CommonAdLiveGsuWithClusterEnricher(kwargs))
    return self

  def gsu_retriever_with_colossus_resp_ext(self, **kwargs):
    """
    CommonColossusRespRetrieverExtend
    ------

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    The following are required attrs from the resp_attr, could be empty
    ------
    `save_photo_id_to_attr`: [string]

    `save_author_id_to_attr`: [string]

    `save_duration_to_attr`: [string]

    `save_play_time_to_attr`: [string]

    `save_tag_to_attr`: [string]

    `save_channel_to_attr`: [string]

    `save_label_to_attr`: [string]

    `save_timestamp_to_attr`: [string]

    `save_photo_lat_attr`: [string]

    `save_photo_lon_attr`: [string]

    `save_user_lat_attr`: [string]

    `save_user_lon_attr`: [string]

    `filter_future_attr` : [boolean] 是否只取时间在request time之前的colossus pid, 默认为false

    `parse_from_pb`: [boolean] 是否从protobuf message中取colossus的返回结果，默认为true

    `keep_past_range_second`: [int] 只取时间在request time之前keep_past_range_second秒的colossus pid, 默认为0不限制

    `keep_future_range_second`: [int] 只取时间在request time之后keep_future_range_second秒的colossus pid, 默认为0不限制

    `channel_whitelist`: [string] 只取channel_whitelist内的colossus pid, 默认不过滤

    示例
    ------
    ``` python
    .gsu_retriever_with_colossus_resp_ext(colossus_resp_attr="colossus_output",
                            save_photo_id_to_attr="pid",
                            save_author_id_to_attr="aid",
                            save_duration_to_attr="duration",
                            save_play_time_to_attr="play",
                            save_tag_to_attr="tag",
                            save_channel_to_attr="channel",
                            save_label_to_attr="label",
                            save_timestamp_to_attr="time",
                            save_photo_lat_attr="photo_lat_list",
                            save_photo_lon_attr="photo_lon_list",
                            save_user_lat_attr="user_lat",
                            save_user_lon_attr="user_lon")
    ```
    """
    self._add_processor(CommonColossusRespRetrieverExtend(kwargs))
    return self

  def ksib_gsu_readjust_sign(self, **kwargs):
    """
    KsibGsuReadjustSignEnricher
    ------
    海外gsu特征sign值重调整

    参数配置
    ------
    `bucket_attr`: [string] bucket值的 common attr

    `common` : [bool] true，调整common attr，false, 调整 item attr

    `gsu_slots_attr` : [string] gsu slots list attr

    `gsu_signs_attr` : [string] gsu signs list attr

    调用示例
    ------
    ``` python
    .ksib_gsu_readjust_sign(bucket_attr='bucket',
                      gsu_slots_attr='ksib_gsu_slots',
                      gsu_signs_attr='ksib_gsu_signs',
                      )
    ```
    """
    self._add_processor(KsibGsuReadjustSignEnricher(kwargs))
    return self
  def ad_goods_gsu_id(self, **kwargs):
    """
    CommonGoodsGsuWithCateEnricher
    ---
    商业化内循环获取商品sim的商品id
    参数配置
    ---
    `limit_num_attr`: [string] id 列表长度的 common attr
    `goods_num_attr`: [string] target goods id 列表长度的 common attr
    `colossus_resp_attr`: [string] colossus 返回对应的 common attr
    `target_item_cate2_attr`: [string] target_item 的二级类目对应的 item attr
    `target_item_cate3_attr`: [string] target_item 的三级类目对应的 item attr
    `target_item_spu_attr`: [string] target_item 的 spu 对应的 item attr
    `output_id_attr`: [string] 返回的商品 id 列表对应的 item attr

    调用示例
    ---
    ``` python
    .ad_goods_gsu_id(
                    colossus_resp_attr="colossus_output",
                    limit_num_attr="limit_num",
                    goods_num_attr="goods_num",
                    target_item_spu_attr="target_live_spu_ids",
                    target_item_cate2_attr="target_live_cid2_ids",
                    target_item_cate3_attr="target_live_cid3_ids",
                    output_id_attr="output_id_list"
                   )

    """
    self._add_processor(CommonGoodsGsuWithCateEnricher(kwargs))
    return self
  def ad_goods_user_index(self, **kwargs):
    """
    CommonGoodsUserIndexEnricher
    ---
    商业化内循环获取对应索引的sign值
    参数配置
    ---
    `goods_id_list_attr`: [string] 商品 id 列表 对应的 item attr
    `user_id_list_attr`: [string] user id 列表 对应的 item attr
    `label_list_attr`: [string] label 列表对应的 item attr
    `sorted_index_list_attr`: [string] index 对应的 item attr
    `output_id_list_attr`: [string] 返回 user id 列表对应的 item attr

    调用示例
    ---
    ``` python
    .ad_goods_user_index(
                      sample_num=10,
                      label_size=3,
                      topn = 200,
                      goods_id_list_attr="co_goods_id_list",
                      user_id_list_attr="g2u_list",
                      label_list_attr="g2u_edge_int",
                      sorted_index_list_attr="sorted_item_idx_attr",
                      output_sign_attr="gsim_output_sign",
                      output_slot_attr="gsim_output_slot",
                      output_id_list_attr="one_order_user_id_list",
                      slots_id=[1, 772, 775, 773, 774],
                      mio_slots_id=[1, 372, 375, 373, 374]
) \
    """
    self._add_processor(CommonGoodsUserIndexEnricher(kwargs))
    return self

  def gsu_with_cluster_uniq(self, **kwargs):
    """ GeneralExtractFeatureFromColossusResponseEnricher
    ------
    从 colossus response 中抽取 target item (即 CommonRecoResult) 的 topk colossus item (基于 sim2.0 cluster 信息),
    抽取的结果会进行一定程度的去重, item attr 中将记录去重前后的位置映射关系，去重后的结果列表保存到 common attr

    参数
    ------
    `kess_service`: [string] cluster id 查询服务 kess name

    `kess_cluster`: [string] cluster id 查询服务 kess cluster, 默认 PRODUCTION

    `shards`: [int] cluster id 查询服务的 shard 数

    `timeout_ms`: [int] 请求 cluster id server 的超时阈值，默认 10ms

    `max_pids_per_request`: [int] 请求 cluster id server 时单次请求最多请求多少个 item , 默认 0 、即不限制

    `max_uniq_item_num`: [int][动态参数] topk item 按一定规则去重后允许保留的最大 item 数，默认 100000

    `max_uniq_cluster_num`: [int][动态参数] topk item 按一定规则去重后允许保留的最大 cluster 数，默认 1000

    `limit_num`: [int][动态参数] 指定 topk 的 k 值

    `skip_latest_items_num`: [int][动态参数] 抽 topk 时跳过最新的多少个 colossus item, 默认 0

    `skip_latest_items_seconds`: [int][动态参数] 抽 topk 时跳过最近的多少秒内更新的 colossus item, 默认 60s

    `colossus_resp_attr`: [string] 用于获取用户长期兴趣的 colossus response 信息

    `colossus_item_id_field_name`: [string] colossus response 中 item id 的字段名, 默认 photo_id

    `colossus_item_ts_field_name`: [string] colossus response 中 timestamp 的字段名, 默认 timestamp

    `colossus_item_cluster_id_field_name`: [string] colossus response 中 cluster id 的字段名, 默认空、 即不使用这个字段

    `target_item_cluster_id_attr`: [string] target item 的可读取 cluster id 的 item attr，默认为空、即没有可读取的 attr

    `target_topk_index_attr`: [string] target item 的 topk colossus item 去重前后 item 位置映射关系所保存的 item attr

    `uniq_topk_items_attr`: [string] 去重后 topk item list 在 common attr 里保存的字段名

    `uniq_topk_items_cluster_attr`: [string] 去重后 topk item list 伴随的 cluster id attr, 默认为空，即不设置该 attr

    `uniq_type`: [string] 去重模式，支持：
                "all_uniq": 所有 target topk colossus items 形成一个统一的去重集合,
                "cluster_uniq": 对 target item 对应的 cluster id 进行去重，然后对 cluster id 求 topk colossus items

    `colossus_reflect_schema_type`: [string] 读取 colossus response field 的 schema 类型

    `hetu_cluster_config_key_name`: [string] hetu cluster topk neighbors 配置, 默认 reco.model.hetuEmbeddingIndex

    调用示例
    ---
    ``` python
    .gsu_with_cluster_uniq(
      kess_service="xxxx",
      shards=4,
      timeout_ms=10,
      limit_num=50,
      max_uniq_item_num=20000,
      max_uniq_cluster_num=650,
      colossus_resp_attr="colossus_output",
      colossus_item_cluster_id_field_name="cluster_id",
      target_topk_index_attr="target_topk_item_index",
      uniq_topk_items_attr="uniq_colossus_topk_items",
      uniq_type="cluster_uniq",
      colossus_reflect_schema_type="grpc_colossusLongSimItem",
      )
    ```
    """
    self._add_processor(GsuWithClusterUniqueEnricher(kwargs))
    return self

  def kmeans_aggregate(self, **kwargs):
    """
    KmeansAggregateEnricher
    ------
    对聚类后的用户长期行为序列进行聚合操作

    参数配置
    ------
    `cluster_action_index_common_attr_name`: [string] 存放聚类后的 photo index 的 common attr name

    `cluster_action_num_common_attr_name`: [string] 存放每个类的行为数量的 common attr name

    `action_list_id_common_attr_name`: [string] 参与聚类的用户长期行为序列的 common attr name

    `output_common_attr_name`: [string] 存放聚合之后的结果的 common attr name

    `photo_id_field_name`: [string] 存放 photo_id 的item attr name

    `author_id_field_name`: [string] 存放 author_id 的item attr name

    `duration_field_name`: [string] 存放 duration 的item attr name

    `play_time_field_name`: [string] 存放 play_time 的item attr name

    `timestamp_field_name`: [string] 存放 timestamp 的item attr name

    `cluster_id_field_name`: [string] 存放 cluster_id 的item attr name

    调用示例
    ------
    ``` python
    .kmeans_aggregate(
                      cluster_action_index_common_attr_name="cluster_action_index",
                      cluster_action_num_common_attr_name="cluster_action_num",
                      action_list_id_common_attr_name = "action_list_id",
                      output_common_attr_name = "kmeans_agg_output",
                      photo_id_field_name = "photo_id",
                      author_id_field_name = "author_id",
                      duration_field_name = "duration",
                      play_time_field_name = "play_time",
                      timestamp_field_name = "timestamp",
                      cluster_id_field_name = "cluster_id"
    )
    ```
    """
    self._add_processor(KmeansAggregateEnricher(kwargs))
    return self

  def extract_feature_from_reco_long_term_action_list(self, **kwargs):
    """
    ExtractFeatureFromRecoLongTermEnricher
    ------
    对 grpc_colossusSimV2 的用户长期行为抽取 sign

    参数
    ------
    `colossus_resp_attr`: colossus 返回值（已经解析为 proto list）

    `output_slot_attr`: output slot attr

    `output_sign_attr`: output sign attr

    `save_pid_to_attr`: optional, 保存 pid list 到 common attr
    """
    self ._add_processor(ExtractFeatureFromRecoLongTermEnricher(kwargs))
    return self

  def gsu_with_cluster_general(self, **kwargs):
    """
    CommonRecoGsuWithClusterGeneralEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    该 processor 基于 colossus common item 实现，支持任意 common item 的 colossus

    参数
    ------
    `slot_as_attr_name`: [bool] 用 slot 作为 key，signs 作为 int list 存储。默认为 False。

    `slot_as_attr_name_prefix`: [string] 用 slot 作为 key 时的前缀，默认无前缀，当 slot_as_attr_name 为 True 时生效。

    `output_sign_attr`: [string] sign 输出 attr name，当 slot_as_attr_name 为 False 时生效。

    `output_slot_attr`: [string] slot 输出 attr name，当 slot_as_attr_name 为 False 时生效。

    `output_cluster_attr`: [string] cluster 输出 attr name，默认不输出。

    `target_cluster_attr`: [string] 输入 target cluster attr，默认为空，通过外部服务请求。

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr，要求上游 processor parse_to_pb=False。

    `limit_num`: [int][动态参数] 返回数目，默认 50。

    `skip_latest_items_num`: [int][动态参数] 跳过最新 item 数目，默认 0。

    `skip_latest_items_seconds`: [int][动态参数] 跳过最近 T seconds 内的 item，默认 60。

    `kess_service`: [string] embedding_server 的 kess 服务名

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `max_pids_per_request`: [int] 单次 rpc 请求中的最大 pid 个数，为 0 代表不限制

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms。

    `hetu_cluster_config_key_name`: [string] 存储 hetu cluster id 近邻关系的 kconf，默认为 `reco.model.hetuEmbeddingIndex`。

    `colossus_service_name`: [string] 请求的 colossus 服务的 kess name。

    `colossus_item_photo_id_name`: [string] colossus 中存储 photo id 的字段，默认为 `photo_id`。

    `colossus_item_ts_name`: [string] colossus 中存储时间戳的字段，默认为 `timestamp`。

    `colossus_item_duration_name`: [string] colossus 中存储视频时长的字段，默认为 `duration`。

    `colossus_item_play_time_name`: [string] colossus 中存储播放时长的字段，默认为 `play_time`。

    `colossus_item_cluster_id_name`: [string] colossus 中存储 cluster_id 的字段，默认为空，从 embedding 服务获取 cluster id。

    `past_days_slot`: [int] 抽取行为发生间隔天数的 slot，默认为 350。

    `past_days_mio_slot`: [int] 抽取行为发生间隔天数的 slot，默认为 350。

    `play_duration_slot`: [int] 抽取播放时长与视频时长交叉特征的 slot，默认为 348。

    `play_duration_mio_slot`: [int] 抽取播放时长与视频时长交叉特征的 slot，默认为 348。

    `extra_sign_feature_configs`: [list] 其他简单特征抽取逻辑，至少需要包含 name，slot 两个字段，也支持 mio_slot。

    示例
    ------
    ``` python
    .gsu_with_cluster_general(colossus_resp_attr='colossus_resp',
                              output_sign_attr='signs',
                              output_slot_attr='slots',
                              limit_num_attr='limit_num',
                              colossus_service_name="grpc_colossusLongSimItem",
                              kess_service='kws-kuaishou-full-rank-embedding-mmu-hetu-cluster-id-long',
                              shards=8)
    ```
    """
    self._add_processor(GsuWithClusterGeneralEnricher(kwargs))
    return self



  def live_gsu_with_aid_v4(self, **kwargs):
    """
    CommonLiveGsuWithClusterAidV4Enricher
    ------
    根据 photo 所属的 author_id 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .live_gsu_with_aid_v4(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'aId',
                      )
    ```
    """
    self._add_processor(CommonLiveGsuWithClusterAidV4Enricher(kwargs))
    return self

  def parse_bitwise_label(self, **kwargs):
    """
    CommonParseBitwiseLabelEnricher
    -----
    解析 bitwise label
    
    参数
    -----
    `input_attr`: [string] 输入的 attr name

    `is_common`: [bool] 是否是 common int list attr, false 代表是 item int attr

    `bit_configs`: [list] 每一位的配置
      `bit_index`: [int] 第几位
      `output_attr`: [string] 输出的 attr name
    
    示例
    -----
    ```python
    .parse_bitwise_label(
        input_attr='bitwise_label',
        bit_configs=[
          dict(bit_index=0, output_attr='like'),
          dict(bit_index=1, output_attr='follow'),
        ]
    )
    ```
    """
    self._add_processor(CommonParseBitwiseLabelEnricher(kwargs))
    return self

  def extract_feature_from_kmeans_colossus(self, **kwargs):
    """ ExtractFeatureFromKmeansColossusEnricher
    ------
    从 colossus 经过 kmeans 聚类后的返回结果中，抽取 gsu 计算所需要的 sign 和 attr

    参数
    ------
    `colossus_output`: [string] colossus 服务返回结果保存的 attr name. common 类型

    `colossus_output_type`: [string] colossus 返回数据类型，可选项: "sim_item" 或 "commmon_item", 默认为 "sim_item"

    `output_signs_attr`: [string] 保存抽取到的 sign 的 attr name

    `output_slots_attr`: [string] 保存 sign 对应 slot 的 attr

    `output_mask_bias_attr`: [string] 保存 mask bias 的 attr name

    `cluster_size_attr`: [string] 保存聚类中心大小的 attr name

    `mask_bias_value`: [float] padding 或者 filter 掉的 item 对应的 bias 值，默认为 -1000.0

    `colossus_item_num_limit`: [int] mask_bias 需要 padding 到的 colossu_item_num

    `timediff_bias_slot_id`: [int] timediff 对应的 slot id

    `item_slot_id`: [int] PID 对应的 slot id

    `play_bias_slot_id`: [int] play bias 对应的 slot id

    `colossus_item_num_attr`: [int] 保存 colossus 结果数的 common attr_name

    `colossus_service_name`: [int] colossus服务的kess_name，默认为 grpc_colossusSimV2

    `colossus_photo_id_field_name`: [int] colossus服务中photo_id的域名, 默认为 photo_id

    `colossus_author_id_field_name`: [int] colossus服务中author_id的域名，默认为 author_id

    `colossus_play_time_field_name`: [int] colossus服务中play_time的域名，默认为 play_time

    `colossus_duration_field_name`: [int] colossus服务中duration的域名，默认为 duration

    `colossus_timestamp_field_name`: [int] colossus服务中timestamp的域名，默认为 timestamp

    `colossus_channel_field_name`: [int] colossus服务中channel的域名，默认为 channel

    `colossus_label_field_name`: [int] colossus 服务中 label 的域名，默认为 label

    `colossus_profile_stay_time_field_name`: [int] colossus服务中colossus_profile_stay_time的域名，默认为空

    `colossus_comment_stay_time_field_name`: [int] colossus服务中comment_stay_time的域名，默认为空

    `skip_latest_items_seconds`: [int][动态参数] 跳过最近 T seconds 内的 item，默认 60。

    示例
    ------
    ``` python
    .extract_feature_from_kmeans_colossus(colossus_output="colossus_output",
                          output_signs_attr="colossus_signs",
                          output_slots_attr="colossus_slots",
                          cluster_size_attr="cluster_size_list",
                          timediff_bias_slot_id=1006,
                          pid_slot_id=1007,
                          play_bias_slot_id=1008,
                          label_bias_slot_id=1009,
                          channel_bias_slot_id=1010,
                          colossus_item_num_attr="item_num",
                          )
    ```
    """
    self._add_processor(ExtractFeatureFromKmeansColossusEnricher(kwargs))
    return self

  def ad_dsp_aid2aid(self, **kwargs):
    """
    AdDspAid2AidEnricher
    ------
    根据photo所属的aid对gsu结果检索相同aid的视频. 

    参数
    ------
    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `tag_attr` : [string] pid对应的tag attr name

    `play_time_attr` : [string] pid对应的play time attr name

    `duration_attr` : [string] pid对应的duration attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_attr`: [string] item侧使用的 aid 属性

    示例
    ------
    ``` python
    .ad_dsp_aid2aid(colossus_pid_attr="colossus_pid",
                    author_id_attr="aid_attr",
                    tag_attr="tag_attr",
                    play_time_attr="play_time_attr",
                    duration_attr="duration_attr",
                    timestamp_attr="ts_attr",
                    output_sign_attr="output_sign",
                    output_slot_attr="output_slot",
                    limit_num_attr=50,
                    slots_ids=[1000, 1001, 1002, 1003, 1004],
                    mio_slots_ids=[346, 347, 348, 349, 350])
    ```
    """
    self._add_processor(AdDspAid2AidEnricher(kwargs))
    return self

  def gsu_common_colossusv2_enricher(self, **kwargs):
    """
    GsuColossusV2Enricher
    ------
    从 colossusV2 查询用户观看视频/直播视频历史数据
    
    默认使用 context 的 UserId 作为查询的 key, 如果 UserId 为 0 且 DeviceId 不为空 ，则用 context 里面的 DeviceId 的 cityhash 值 作为查询的 key

    参数
    ------
    
    `kconf`: [必填] [string] 必配项，服务的 client_kconf。用来确定访问哪个服务, 怎么配置参考文档:
     [colossus V2 接入文档](https://docs.corp.kuaishou.com/d/home/<USER>
     打开文档搜索 client_kconf

    `item_fields`: [必填] [dict] 请求的特征和放入的 commonAttr (当设置了 v1_colossus_resp, 也就是要将返回数据转成 v1 格式时，该字段的 value 不再生效）
        
    `test_id`: [可选] [int] 覆盖请求的 id
  
    `query_key`: [可选] [string] 如果配置了，则从 context 里面拿 IntCommonAttr 作为 key 来查询数据（不再拿 context 里的 UserId 或 DeviceId, test_id 也不再生效）

    `v1_colossus_resp`: [可选] [string] 将结果转成 v1 格式，存入的 commonAttr, 类型是google::protobuf::Message *, 参考https://docs.corp.kuaishou.com/d/home/<USER>

    `v1_service_name`: [可选] [string] 要将结果转成 v1 格式，需指定一个服务，去请求获取它的 schema, v1_service_name 和 v1_colossus_resp 必须同时配置

    `concat_variable_item_fields`: [可选] [dict] 当要与可变部分的 colossus 结果拼接时，需要提供已有的特征及其在 commonAttr 中的 key

    `filter`: [可选] [string] [动态参数] 对 item list 执行的过滤条件, 可以是 attr 与常数的比较，支持 = > >= < <= != IN BETWEEN, 以及多个条件语句用 AND OR 连接起来, 也可以用小括号区分优先级

    `filter_future_items`: [可选] [bool] 是否过滤掉未来的 items, 会将时间戳 >= 当前时间戳的 items 过滤掉，当前时间戳会从 context 里拿

    `seconds_to_lookback`: [可选] [int] [动态参数] 过滤未来的 items 时, 将当前时间戳往前回溯的秒数，例如设置为 60 且打开 filter_future_items, 那么会过滤掉所有时间戳 >= 当前时间戳 - 60s 的 items, 默认为 0, 即不回溯。

    `order`: [可选] [list[string]] 对 item list 排序的依据，可以是多列

    `limit`: [可选] [int] [动态参数] 对 item list 截断，当 limit > 0 时例如 limit = 100 是指取最靠后的 100 个 item (如果没有 order 一般按照 timestamp 排序也就是取最近最新的 100 个item), 否则表示取最靠前的 100 个 item, 返回顺序仍按照默认顺序或者配置了 order 就按照 order 的顺序。当配置了拼接的时候，这部分的数量会减去可变部分的 item 数量

    示例
    ------
    ① 常规用法：该方法效率最高
    ```
    .gsu_common_colossusv2_enricher(kconf="colossus.kconf_client.test",
                            item_fields=dict(photo_id="my_photo_id",
                                             author_id="my_author_id",
                                             duration="my_duration",
                                             play_time="my_play_time",
                                             tag="my_tag",
                                             channel="my_channel",
                                             label="my_label",
                                             timestamp="my_timestamp"))
    ```
    会将获取的 item list 分字段存储在对应的 commonAttr 中，例如 item 有 1500 条，而 photo_id 是整数类型，那么会将这 1500 个 item 中的 photo_id 存在 my_photo_id 的c ommonAttr 里，类型是 int list

    ② 兼容用法：涉及数据格式转换, 性能会差一些, 主要为了服务从 v1 迁移到 v2, 不改动下游的 processor
    ```
    .gsu_common_colossusv2_enricher(kconf="colossus.kconf_client.live_item",
        # 必须填写老版和新版 colossus 共有的所有字段
        item_fields=dict(live_id="do_not_use_me",
                          timestamp="do_not_use_me",
                          author_id="do_not_use_me",
                          play_time="do_not_use_me",
                          auto_play_time="do_not_use_me",
                          hetu_tag_channel="do_not_use_me",
                          cluster_id="do_not_use_me",
                          label="do_not_use_me",
                          reward="do_not_use_me",
                          reward_count="do_not_use_me",
                          item_id="do_not_use_me",
                          audience_count="do_not_use_me",
                          user_latitude="do_not_use_me",
                          user_longitude="do_not_use_me",
                          order_price="do_not_use_me"),
        v1_service_name="grpc_colossusLiveItemV4",
        v1_colossus_resp="live_item_v4_colossus_resp")
    ```
    此时会将 v2 的返回结果转换成符合 v1_service_name 服务的 Schema 的数据中，并且存储到 v1_colossus_resp 的 commonAttr 中, 类型是google::protobuf::Message *的, 此时 item_fields 的 value 字段不生效, 但也必须填写, 更详细的说明参考 https://docs.corp.kuaishou.com/d/home/<USER>
    
    ③ 拼接用法：如果 commonAttr 中已经有一部分的最新数据, 可以使用该方法从服务中只拿取这段数据`以前`的数据并与这段数据完成拼接
    ```
    .gsu_common_colossusv2_enricher(kconf="colossus.kconf_client.client_kconf_for_sql_test",
                         debug_print_item_num=25,
                        item_fields=dict(photo_id="my_photo_id",
                                         duration="my_duration",
                                         timestamp="my_timestamp"),
                        concat_variable_item_fields=dict(
                        photo_id="from_photo_id", 
                        timestamp="from_timestamp",
                        duration="from_duration"))
    runner = service.executor()
    runner.user_id = 1238191291
    runner['from_timestamp'] = [1693310150, 1693310151, 1693310155]
    runner['from_duration'] = [200,30, 100]
    runner['from_photo_id'] = [10, 20, 30]
    runner.run("test")
    ```

    其中 concat_variable_item_field 配置了要拼接哪些字段（需与 item_fields 中的字段一一对应，以及必须有 timestamp 字段）以及从哪个 commonAttr 可以拿到它的最新数据
    以该例子为例, enricher 会拿到 from_timestamp 的第一个值也就是 1693310150, 然后再去请求 ColossusV2 服务，这时候服务只会返回 timestamp < 1693310150 的结果，并在拿到结果后将最新数据拼在返回结果最后
    注意：若配置了要拼接，但是拿不到 timestamp 的数据，例如 context 中的 from_timestamp 为空或者是一个空的 list, 那么服务会默认请求当前时间戳 - max_acceptable_update_delay_in_seconds 之前的所有数据，其中 max_acceptable_update_delay_in_seconds 在 Schema 的 kconf 中配置

    ④ 类SQL用法: 新版 ColossusV2 服务在服务端提供了计算逻辑, 支持过滤、排序和截断功能, 可以节省带宽
    ```
    .gsu_common_colossusv2_enricher(kconf="colossus.kconf_client.test",
                            item_fields=dict(photo_id="my_photo_id",
                                             author_id="my_author_id",
                                             duration="my_duration",
                                             play_time="my_play_time",
                                             timestamp="my_timestamp"),
                            filter=' duration < 20 and photo_id > 100000000000',
                            order=['timestamp', 'duration'],
                            limit=10)
    ```
    此时会将全量的 item list 先根据 filter 做过滤, 再将过滤后的结果按照 timestamp 列和 duration 列进行双关键字排序，然后选取最后的 100 个item返回,这样最终的 my_photo_id 最多有 100 个元素
    
    注意：这四种使用方式并不冲突,可以互相组合,例如 ① + ④ 就是 选列 + 计算, ① + ② + ③ 就是 选列 + 拼接 + 转V1, ① + ② + ③ + ④ 就是 选列 + 计算 + 拼接 + 转V1, 要注意的是计算只作用于请求服务时返回的 item list, 并不作用于 commonAttr 中已经存在的 item list
    """
    self._add_processor(GsuColossusV2Enricher(kwargs))
    return self

  def gsu_common_colossusv2_batch_enricher(self, **kwargs):
    """
    GsuColossusV2BatchEnricher
    ------
    从 colossusV2 批量查询序列数据，并按列存格式解析数据
    
    参数
    ------
    
    `client_kconf`: [必填] [string] 必配项，用来确定访问哪个服务，怎么配置参考文档：
     [colossus V2 接入文档](https://docs.corp.kuaishou.com/d/home/<USER>
     打开文档搜索 client_kconf

    `item_fields`: [必填] [array] 按需填写，需要哪些列的数据，就填写列对应的 field_name
        
    `input_keys_attr`: [必填] [string] 用这个 attr 从 context 里面拿 IntCommonAttr 配置的一批 key 来查询数据
  
    `output_int_attr`: [必填] [string] 指定整型 field 的数据会放到 context 的那个 PtrCommonAttr
  
    `output_str_attr`: [可选] [string] 指定 char 型 field 的数据会放到 context 的那个 PtrCommonAttr
  
    `output_double_attr`: [可选] [string] 指定 double/float 型 field 的数据会放到 context 的那个 PtrCommonAttr
  
    `output_repeated_int_attr`: [可选] [string] 如果 item_field 里面有整型 field 的 repeated_size>1 则必填
  
    `output_repeated_str_attr`: [可选] [string] 如果 item_field 里面有 char 型 field 的 repeated_size>1 则必填
  
    `output_repeated_double_attr`: [可选] [string] 如果 item_field 里面有 double/float 型 field 的 repeated_size>1 则必填
  
    `filter`: [可选] [string] [动态参数] 对 item list 执行的过滤条件, 可以是 attr 与常数的比较，支持 = > >= < <= != IN BETWEEN, 以及多个条件语句用 AND OR 连接起来, 也可以用小括号区分优先级

    `filter_future_items`: [可选] [bool] 是否过滤掉未来的 items, 会将时间戳 >= 当前时间戳的 items 过滤掉，当前时间戳会从 context 里拿

    `seconds_to_lookback`: [可选] [int] [动态参数] 过滤未来的 items 时, 将当前时间戳往前回溯的秒数，例如设置为 60 且打开 filter_future_items, 那么会过滤掉所有时间戳 >= 当前时间戳 - 60s 的 items, 默认为 0, 即不回溯。

    `order`: [可选] [list[string]] 对 item list 排序的依据，可以是多列

    `limit`: [可选] [int] [动态参数] 对 item list 截断，当 limit > 0 时例如 limit = 100 是指取最靠后的 100 个 item (如果没有 order 一般按照 timestamp 排序也就是取最近最新的 100 个item), 否则表示取最靠前的 100 个 item, 返回顺序仍按照默认顺序或者配置了 order 就按照 order 的顺序。当配置了拼接的时候，这部分的数量会减去可变部分的 item 数量
    
    `debug_keys`: [可选] [string] debug 专用，填写一批用英文逗号分隔的 key ，指定用这批 key 查询数据，配置后 input_keys_attr 不再生效

    `debug_print_item_num`: [可选] [int] debug 专用，大于零的情况下，会把返回的 item 打印到 glog 里面

    示例
    ------
    ① 常规用法：按所需的列请求数据
    ```
    .gsu_common_colossusv2_batch_enricher(client_kconf="colossus.kconf_client.video_item",
                            item_fields=['photo_id',
                                          'author_id',
                                          'duration',
                                          'play_time',
                                          'tag',
                                          'timestamp'],
                            output_int_attr="int_batch_output")
    ```

    ② 类SQL用法: 新版 ColossusV2 服务在服务端提供了计算逻辑, 支持过滤、排序和截断功能, 可以节省带宽
    ```
    .gsu_common_colossusv2_batch_enricher(client_kconf="colossus.kconf_client.video_item",
                            item_fields=['photo_id',
                                          'author_id',
                                          'duration',
                                          'play_time',
                                          'tag',
                                          'timestamp'],
                            filter=' duration < 20 and photo_id > 100000000000',
                            order=['timestamp', 'duration'],
                            limit=100,
                            output_int_attr="int_batch_output")
    ```
    此时会将全量的 item list 先根据 filter 做过滤, 再将过滤后的结果按照 timestamp 列和 duration 列进行双关键字排序，然后选取最后的 100 个 item 返回
    
    """
    self._add_processor(GsuColossusV2BatchEnricher(kwargs))
    return self
  
  def gsu_common_colossusv2_batch_parse_enricher(self, **kwargs):
    """
    ColossusV2BatchParseEnricher
    ------
    解析 GsuColossusV2BatchEnricher 批量查询的 int_list 数据
    
    参数
    ------

    `item_fields`: [必填] [array] 按需填写，需要哪些列的数据，就填写列对应的 field_name, 解析结果为 field_name 命名的 IntListCommonAttr, 每个 list 长度为 max_list_num * user_num
    
    `input_int_attr`: [必填] [string] 批量的 int 数据输入源
    
    `max_list_num`: [必填] [int] 解析list长度
    
    `enable_filter`: [必填] [bool] 是否对序列按照播放行为进行过滤, 默认false
                  
    """
    self._add_processor(ColossusV2BatchParseEnricher(kwargs))
    return self

  def gsu_common_colossusv2_debug_enricher(self, **kwargs):
    """
    GsuColossusV2DebugEnricher
    ------
    用于打印 gsu_common_colossusv2_batch_enricher 请求到的数据
    
    参数
    ------
    
    `input_int_attr`: [必填] [string] 批量的 int 数据输入源
  
    `input_str_attr`: [可选] [string] 批量的 char 数据输入源
  
    `input_double_attr`: [可选] [string] 批量的 double 数据输入源
  
    `input_repeated_int_attr`: [可选] [string] 批量的 repeated double 数据输入源
  
    `input_repeated_str_attr`: [可选] [string] 批量的 repeated char 数据输入源
  
    `input_repeated_double_attr`: [可选] [string] 批量的 repeated double 数据输入源
  
    `debug_print_key_num`: [可选] [int] debug 专用（默认是 0 ，会全部打印），大于零的情况下，会把前 N 个 key 的 item 打印到 glog 里面

    `debug_print_item_num`: [可选] [int] debug 专用（默认是 0 ，会全部打印），大于零的情况下，会把返回的每个 key 的前 N 个 item 打印到 glog 里面

    示例
    ------
     示例参考文档：[colossus V2 接入文档](https://docs.corp.kuaishou.com/d/home/<USER>

    """
    self._add_processor(GsuColossusV2DebugEnricher(kwargs))
    return self

  def gsu_common_colossusv2_batch_decode_enricher(self, **kwargs):
    """
    GsuColossusV2BatchDecodeEnricher
    ------
    用于打印 gsu_common_colossusv2_batch_decode_enricher 请求到的数据

    默认返回  output_int_attr_prefix + input_uid_list_attr + _missing: [array] 输出没有data的 uid list
    
    参数
    ------
    `item_fields`: [必填] [array] 按需填写，需要哪些列的数据，就填写列对应的 field_name

    `input_int_attr`: [必填] [string] 批量的 int 数据输入源

    `input_uid_list_attr`: [必填] [string] 批量的 int 数据uid_list输入源

    `input_uid_score_list_attr`: [可选] [string] 批量的 int 数据uid_score_list输入源
    
    `output_int_attr_prefix`: [必填] [string] 批量的 int 数据的输出源名字prefix

    `output_uid_num`: [可选] [string] 输出批量的 uid的个数, 默认输出全部

    `output_item_num`: [可选] [string] 输出批量的每个 uid下item的个数, 默认输出全部

    示例
    ------
    .gsu_common_colossusv2_batch_decode_enricher(
        item_fields=['photo_id',
                      'author_id',
                      'duration',
                      'play_time',
                      'tag',
                      'timestamp'],
        input_int_attr='int_batch_output',
        input_uid_list_attr='uid_list',
        input_uid_score_list_attr=‘sim_user_scores’,
        output_int_attr_prefix='batch_output_',
        output_uid_num=5,
        output_item_num=50,
      )

     示例参考文档：[colossus V2 接入文档](https://docs.corp.kuaishou.com/d/home/<USER>

    """
    self._add_processor(GsuColossusV2BatchDecodeEnricher(kwargs))
    return self

  def gsu_common_colossusv2_clotho_merge_enricher(self, **kwargs):
    """
    GsuColossusV2ClothoMergeEnricher
    ------
    把 colossusV2 返回的实时数据和 clotho 返回的历史数据合并成 colossusV2 格式的数据

    参数
    ------

    `client_kconf`: [必填] [string] 必配项，服务的 client_kconf。用来确定访问哪个服务, 怎么配置参考文档:
     [colossus V2 接入文档](https://docs.corp.kuaishou.com/d/home/<USER>
     打开文档搜索 client_kconf

    `colossus_reflection_from`: [必填] [string] 上游 colossus 输出的 reflection

    `colossus_data_from`: [必填] [string] 上游 colossus 输出的数据

    `clotho_data_from`: [必填] [string] 上游 clotho 输出的数据

    `output_data_to`: [选填] [string] 数据输出到哪个 common output attr

    `item_fields`: [选填] [string] 数据按列输出，item_fields 和 output_data_to 必须天填一个

    示例
    ------
    ```
    # 数据输出到 common 侧的 output_data_to attr ，下游使用 reflection 解析
    .gsu_common_colossusv2_clotho_merge_enricher(
        client_kconf="colossus.kconf_client.good_click_item",
        colossus_reflection_from="colossus_reflection_from",
        colossus_data_from="colossus_data_from",
        clotho_data_from="clotho_data_from",
        output_data_to="output_data_to")

    # 数据按列输出
    .gsu_common_colossusv2_enricher(
        debug_print_item_num=0,
        kconf="colossus.kconf_client.good_click_item",
        item_datas_output_attr="good_click_item_data",
        reflection_output_attr="good_click_item_reflection",
        do_not_parse_to_common_attr=True,
        item_fields=dict(
            item_id="",
            click_timestamp="",
            llsid="",
            real_price="",
            origin_price="")) \
    .gsu_common_colossusv2_clotho_merge_enricher(
        client_kconf="colossus.kconf_client.good_click_item",
        debug_print_item_num=0,
        colossus_reflection_from="good_click_item_reflection", # 最近 N 天的 colossus 数据
        colossus_data_from="good_click_item_data",
        clotho_data_from="clotho_value", # 天级更新的 clotho 数据
        item_fields=dict(item_id="item_id_list",
            click_timestamp="click_timestamp_list",
            llsid="llsid_list",
            real_price="real_price_list",
            origin_price="origin_price_list")) 
    ```

    """
    self._add_processor(GsuColossusV2ClothoMergeEnricher(kwargs))
    return self

  def gsu_common_colossusv2_checkpoint_enricher(self, **kwargs):
    """
    ColossusV2CheckpointEnricher
    ------
    从 colossusv2 格式的 checkpoint ，并把 key, item_datas, reflection 输出到 common attr
    
    参数
    ------
    
    `client_kconf`: [必填] [string] colossus 服务对应的 client_kconf
  
    `save_reflection_to_attr`: [必填] [string] 把读到的 item_data 对应的 reflection 指针保存到 CommonAttr
  
    `save_data_to_attr`: [必填] [string] 把读到的 item_data 保存到 CommonAttr

    `save_key_to_attr`: [可选] [string] 把读到的 key 保存到 CommonAttr

    示例
    ------
     示例参考文档：[colossus V2 接入文档](https://docs.corp.kuaishou.com/d/home/<USER>

    """
    self._add_processor(GsuColossusV2CheckpointEnricher(kwargs))
    return self

  def gsu_common_colossusv2_json_enricher(self, **kwargs):
    """
    GsuColossusV2JsonEnricher
    ------
    从 colossus 请求数据，解析成 json 格式的 string 放到 context 的 StringCommonAttr 里面
    
    参数
    ------
    
    `input_key_attr`: [必填] [string] 要查询的 key 从哪个 CommonIntAttr 里面获取
  
    `input_service_info_attr`: [必填] [string] 要查询的 colossus 服务信息从哪个 CommonAttr 里面获取
  
    `input_colossus_version_attr`: [必填] [string] 要查询的 colossus 版本从哪个 CommonAttr 里面获取（上游设置的时候只能设置 v1 或者 v2 这两个值）
  
    `output_result_attr`: [必填] [string] 输出的结果放到哪个 CommonAttr 里面

    `debug_max_item_num`: [可选] [int] debug 专用，设置以后只打印前面 N 个 item 

    示例
    ------
     示例参考文档：[colossus V2 接入文档](https://docs.corp.kuaishou.com/d/home/<USER>

    """
    self._add_processor(GsuColossusV2JsonEnricher(kwargs))
    return self

  def gsu_common_colossusv2_parse_update_enricher(self, **kwargs):
    """
    GsuColossusV2ParseUpdateEnricher
    ------
    解析 colossusv2 的实时更新数据，上游的数据源一般是 kafka

    参数
    ------

    `schema_kconf`: [必填] [string] 必配项，服务的 schema kconf 配置

    `input_data_from`: [必填] [string] 输入的数据从哪里获取

    `output_fields`: [必填] [dict] 需要的字段和想要放入的 CommonAttr

    `output_key_attr`: [选填] [dict] 解析到的 key 放到哪个 CommonAttr

    `debug_print_item_num`: [选填] [int] debug 专用，表示每一轮最多打印多少条 item 数据。默认是 0 不打印

    示例
    ------
    ```
    .gsu_common_colossusv2_parse_update_enricher(schema_kconf="colossus.tables.video_item",
                            input_data_from="input_kafka_data",
                            output_fields=dict(photo_id="my_photo_id",
                                             author_id="my_author_id",
                                             duration="my_duration",
                                             play_time="my_play_time",
                                             tag="my_tag",
                                             channel="my_channel",
                                             label="my_label",
                                             timestamp="my_timestamp"))

    # 下游通过 reflection 解析数据
    .gsu_common_colossusv2_parse_update_enricher(schema_kconf="colossus.tables.video_item",
                            input_data_from="input_kafka_data",
                            # debug 专用，正式服务不要配
                            debug_print_item_num=10,
                            # 下面三个配置必须配
                            do_not_parse_to_list=True,
                            output_reflection_attr="good_order_item_reflection",
                            output_item_data_attr="good_order_item_item_data")
    ```

    """
    self._add_processor(GsuColossusV2ParseUpdateEnricher(kwargs))
    return self

  def gsu_common_colossusv2_build_update_enricher(self, **kwargs):
    """
    GsuColossusV2BuildUpdateEnricher
    ------
    从 context 里面获取数据，构造一个 colossusv2 的 UpdateItems

    参数
    ------

    `client_kconf`: [必填] [string] 必配项，服务的 client kconf 配置

    `key_from`: [必填] [string] 输入的数据的 key 从哪里获取

    `input_fields`: [必填] [dict] 需要的字段和数据来源的 CommonAttr

    `topic_name`: [选填] [string] 构造的数据写到哪个 kafka topic 里面去

    `output_attr`: [选填] [string] 构造的数据放到哪个 CommonAttr 里面去，topic_name 和 output_attr 必须至少设置一个

    `debug_print_item_num`: [选填] [int] debug 专用，表示每一轮最多打印多少条 item 数据。默认是 0 不打印

    示例
    ------
    ```
    .gsu_common_colossusv2_build_update_enricher(client_kconf="colossus.kconf_client.video_item",
                            key_from="key_from_attr",
                            topic_name="xxx_topic",
                            input_fields=dict(photo_id="my_photo_id",
                                             author_id="my_author_id",
                                             duration="my_duration",
                                             play_time="my_play_time",
                                             tag="my_tag",
                                             channel="my_channel",
                                             label="my_label",
                                             timestamp="my_timestamp"))
    ```

    """
    self._add_processor(GsuColossusV2BuildUpdateEnricher(kwargs))
    return self

  def gsu_single_edge_feature_enricher(self, **kwargs):
    """
    SingleEdgeFeatureEnricher
    ------
    从 colossusV2 查询用户的单边特征，拼接好放到 common attr

    参数
    ------

    `kconf`: [必填] [string] 必配项，服务的 client_kconf。用来确定访问哪个服务, 怎么配置参考文档:
     [colossus V2 接入文档](https://docs.corp.kuaishou.com/d/home/<USER>
     打开文档搜索 client_kconf

    `input_keys_from`: [必填] [string] 需要获取单边特征的 key 的来源

    `item_fields`: [必填] [dict] 单边特征名，和对应的 output attr name

    `cache_configs`: [可选] [dict] local cache 的配置，不配性能会很差

    `testing_keys`: [可选] [string] debug 专用，英文逗号分隔的测试 key 列表

    `debug_print_item_num`: [可选] [int] debug 专用，要打印前几个 key 的单边特征

    示例
    ------
    ```
    .gsu_common_colossusv2_enricher(
        query_key="update_user_id",
        kconf="colossus.kconf_client.video_item",
        item_fields=dict(photo_id="photo_id_list",
                            author_id="author_id_list",
                            duration="duration_list",
                            timestamp="timestamp_list"))
    .gsu_single_edge_feature_enricher(
        cache_configs=dict(
            shard_num=29, # 这里必须填一个素数
            cache_size=40000000),
        kconf="colossus.kconf_client.photo_info_item",
        input_keys_from="photo_id_list",
        item_fields=dict(
            timestamp="fake_timestamp",
            collection_score="collection_score_list",
            collection_type="collection_type_list",
            hetu_cate_v3="hetu_cate_v3_list"))
    ```
    """
    self._add_processor(GsuSingleEdgeFeatureEnricher(kwargs))
    return self
 
  def gsu_concat_attr_enricher(self, **kwargs):
    """
    GsuConcatAttrEnricher

    `colossus_concat_attr`: [string list] common_attrs from colossus output

    `colossus_service_name`: [string]

    `output_attr_name`: [string]

    `colossus_is_string`: [bool]
    示例
    ------
    ``` python
    .gsu_concat_attr_enricher(colossus_concat_attr = ["colossus_output", "colossus_output_v2"],
                            colossus_service_name = "grpc_colossusRecoSimItemV3",
                            output_attr_name = "sim_colossus_v3_output",
                            max_resp_item_num = 10000,
                            colossus_is_string = False)
    ```

    """
    self._add_processor(GsuConcatAttrEnricher(kwargs))
    return self

  def gsu_concat_colossusv2_enricher(self, **kwargs):
    """
    GsuConcatColossusV2Enricher

    `colossus_concat_attr`: [string list] common_attrs from colossus output (from .gsu_common_colossusv2_enricher)
    
    `max_resp_item_num`: [int] max keep item num

    `truncate_from_start`: [bool] when item_num > max_resp_item_num,  truncate items from start, default is True

    `reflection_attr_name`: [string] reflection common attr (from .gsu_common_colossusv2_enricher)

    `output_attr_name`: [string]

    `print_concat_result`: [bool] for debug, print every item after concating, default is False

    示例
    ------
    ``` python

    .gsu_concat_colossusv2_enricher(colossus_concat_attr=['colossus_item_data'],
            reflection_attr_name='reflection_attr',
            output_attr_name='colossus_concat_data',
            max_resp_item_num=10000)

    .gsu_concat_colossusv2_enricher(colossus_concat_attr=['colossus_output_1','colossus_output_2','colossus_output_3'],
            reflection_attr_name='reflection_attr',
            output_attr_name='colossus_concat_data',
            max_resp_item_num=99)

    ```

    """
    self._add_processor(GsuConcatColossusV2Enricher(kwargs))
    return self

  def gsu_calculate_colossus_length_enricher(self, **kwargs):
    """
    GsuCalculateColossusLengthEnricher

    `colossus_resp_attr`: [string list] common_attrs from colossus output

    `colossus_service_name`: [string]

    `output_attr_name`: [string]

    `colossus_is_string`: [bool]
    示例
    ------
    ``` python
    .gsu_calculate_colossus_length_enricher(colossus_resp_attr = "colossus_output",
                            colossus_service_name = "grpc_colossusSimV2",
                            output_attr_name = "attr_name",
                            colossus_is_string = False)
    ```

    """
    self._add_processor(GsuCalculateColossusLengthEnricher(kwargs))
    return self

  def gsu_calculate_colossusv2_length_enricher(self, **kwargs):
    """
    GsuCalculateColossusV2LengthEnricher

    `colossus_resp_attr`: [string] common_attr from colossus output 

    `colossus_resp_type`: [string] colossus output type, default is 'colossusv2' (from .gsu_common_colossusv2_enricher), also can be 'concat_video_item' (from .gsu_concat_colossusv2_entricher)

    `reflection_attr_name`: [string][optional] reflection common attr (when colossus_resp_type = colossusv2) , from .gsu_common_colossusv2_enricher

    `output_attr_name`: [string] 

    示例
    ------
    ``` python

    .gsu_calculate_colossusv2_length_enricher(colossus_resp_attr="colossus_item_data",
            colossus_resp_type="colossusv2",
            reflection_attr_name="reflection_attr,
            output_attr_name="colossus_item_len")

    .gsu_calculate_colossusv2_length_enricher(colossus_resp_attr="concat_colossus_item_data",
            colossus_resp_type="concat_video_item",
            output_attr_name="colossus_item_len")
            
    ```

    """
    self._add_processor(GsuCalculateColossusV2LengthEnricher(kwargs))
    return self

  def colossus_concat_realtime_behavior(self, **kwargs):
    """ ColossusConcatRealtimeBehaviorEnricher
    ------
    colossus 结果拼接 user profile v1 中的实时行为 [预估时]，同时去掉超过 user profile v1 最大时间戳的行为 [训练时]

    参数
    ------
    `user_profile_attr`: [string] user profile v1 的属性名

    `colossus_input_attr`: [string] colossus 原始结果

    `colossus_output_attr`: [string] 重新裁剪拼接后的 colossus 结果

    `colossus_output_type`: [string] colossus 数据类型，默认为 "commmon_item"，目前只支持该类型

    `colossus_item_num_limit`: [int] 后续计算图最大可计算的行为数量

    `colossus_service_name`: [int] colossus服务的kess_name，默认为 grpc_colossusSimV3New

    `colossus_photo_id_field_name`: [int] colossus服务中photo_id的域名, 默认为 photo_id

    `colossus_author_id_field_name`: [int] colossus服务中author_id的域名，默认为 author_id

    `colossus_play_time_field_name`: [int] colossus服务中play_time的域名，默认为 play_time

    `colossus_duration_field_name`: [int] colossus服务中duration的域名，默认为 duration

    `colossus_timestamp_field_name`: [int] colossus服务中timestamp的域名，默认为 timestamp

    `colossus_channel_field_name`: [int] colossus服务中channel的域名，默认为 channel

    `colossus_label_field_name`: [int] colossus 服务中 label 的域名，默认为 label

    `colossus_profile_stay_time_field_name`: [int] colossus服务中colossus_profile_stay_time的域名，默认为profile_stay_time

    `colossus_comment_stay_time_field_name`: [int] colossus服务中comment_stay_time的域名，默认为comment_stay_time

    `realshow_index_field_name`: [int] colossus服务中realshow_index的域名，默认为realshow_index
    
    示例
    ------
    ``` python
    .colossus_concat_realtime_behavior(user_profile_attr="user_profile",
                          colossus_input_attr="colossus_output",
                          colossus_output_attr="colossus_output_new",
                          )
    ```
    """
    self._add_processor(ColossusConcatRealtimeBehaviorEnricher(kwargs))
    return self


  def sim_compute_offload(self, **kwargs):
    """
    SimComputeOffloadEnricher
    ------
    调用基于 CommonLeaf 协议的远程ColossusSim服务进行计算，并填充返回的属性

    参数配置
    ------
    `kconf`: [string] 必配项， kconf 配置路径 kconf 配置内容参考 https://docs.corp.kuaishou.com/d/home/<USER>

    `compute_offload_name`: [string] 选填项，计算下沉名称，使用计算下沉并返回结果后会将名为 name 的 int 型 common attr 置为1，否则为-1，默认名称为 use_compute_offload。

    `offload_per_million`: [string] [动态参数] 每一百万请求中计算下沉的数量，计算方法为userid取模, 非计算下沉的情况下，名为 name 的 common attr 置为 0。

    `is_sync`: [bool] 选填项，是否是同步调用，默认为 True。

    `timeout_ms`: [int] [动态参数] 选填项，gRPC 超时时间，默认为 300ms。

    `request_type`: [string] [动态参数] 选填项，请求的 request type，默认为本 leaf 当前的 request type。

    `use_item_id_in_attr`: [string] 选填项，若设置则使用指定的 ItemAttr 下的 int 值替代实际 Item 的 item_key，填充进 request 用于发送给下游（注意是替换 item_key，不是 item_id，配置名中的 use_item_id 字眼有歧义）

    `send_item_attrs`: [list] 选填项，发送的 item attr 列表，默认不发送 item attr，支持对 attr 重命名发送。

    `send_item_attrs_in_name_list`: [string] 选填项，从指定的 string_list common attr 中获取需要发送的 item attr 的 name list

    `send_common_attrs`: [list] 选填项，发送的 common attr 列表，默认不发送 common attr，支持对 attr 重命名发送。

    `send_common_attrs_in_request`: [bool] 选填项, 是否将上游发送过来的 request 中的全部 common_attr 发送给下游, 默认为 false。

    `exclude_common_attrs`: [list] 选填项, 发送给下游时，需要过滤的 common_attr. 一般配合 send_common_attrs_in_request 使用

    `recv_item_attrs`: [list] 选填项，接收的 item attr 列表，默认不接收 item attr，支持对 attr 重命名保存。

    `recv_common_attrs`: [list] 选填项，接收的 common attr 列表，默认不接受 common attr，支持对 attr 重命名保存。

    `use_packed_item_attr`: [bool] [动态参数] 选填项，是否要求服务端用 packed_item_attr 格式返回 item_attr 以提高数据读写性能，缺省时会优先尝试使用 packed_item_attr 格式，若获取数据失败会取消 packed_item_attr 格式的使用

    `infer_output_type`: [int] 选填项，要求 tower infer server 用指定的 output_type（与服务端 [fetch_tower_remote_pxtr](https://dragonfly.corp.kuaishou.com/#/api/embed_calc?id=fetch_tower_remote_pxtr) 的 output_type 配置功能相同）返回 pxtr 数据，默认为 -1

    `use_sample_list_attr_flag`: [bool] 选填项，是否使用 sample_list 服务获取的 common attrs，默认为 false

    `sample_list_common_attr_key`: [string] 选填项，从指定的 string_list common attr 中获取 sample_list attr 所在的 attr name 列表

    `flatten_sample_list_attr`: [bool] 选填项，是否使用压缩格式发送 sample_list 服务获取的 common attrs，默认为 false

    `flatten_sample_list_attr_to`: [string] 选填项，将 flatten 后的 sample_list attr 以指定的 attr name 发送，仅当 flatten_sample_list_attr=true 时有效，默认为 "kuiba_user_attrs"

    `ttl_seconds`: [int] 选配项，在创建 request 的时候，底层默认会复用 protobuf 的对象空间，如果发生像 UserInfo 一样长期复用导致内存无限增长的情况，可通过该项配置来定期清理内存空间，默认值为 3600

    调用示例
    ------
    ``` python
    .sim_compute_offload(
      kconf = "colossus.kconf_client.test",
      send_item_attrs = ["pId", "aId"],
      send_common_attrs = ["uId"],
      recv_item_attrs = ["ctr"],
      request_type = "default",
      use_packed_item_attr = True,
      offload_per_million = 1000000,
    )
    # 如果有 attr 需要改名
    .sim_compute_offload(
      kconf = "colossus.kconf_client.test",
      send_item_attrs = ["pId", "aId"],
      send_common_attrs = [{"name": "user_id", "as": "uId"}],
      recv_item_attrs = [{"name": "ctr", "as": "pctr"}],
      request_type = "default",
      use_packed_item_attr = True,
      offload_per_million = 1000000,
    )
    ```
    """
    self._add_processor(SimComputeOffloadEnricher(kwargs))
    return self
  
  def ksib_live_gsu_with_aids(self, **kwargs):
    """
    KsibLiveGsuAidEnricher
    ------
    根据 target author 扩展的 author_ids 进行 gsu 搜索并填充相同作者直播的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `target_aids_attr`: [int] target aid 扩展的 aids

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .ksib_live_gsu_with_aids(colossus_resp_attr='live_colossus_resp',
                             output_sign_attr='live_gsu_sign',
                             output_slot_attr='live_gsu_slot',
                             limit_num_attr='live_limit_num',
                             target_aids_attr = 'swing_aids',
                            )
    ```
    """
    self._add_processor(KsibLiveGsuAidEnricher(kwargs))
    return self

  def common_gsu_with_rq_vae_enricher(self, **kwargs):
    """
    CommonGsuWithRqVaeEnricher
    ------
    根据 target semantic id 对用户交互过的 photo id 做匹配，同时计算 match count

    参数
    ------
    `output_filter_first`: [string] 第一层 semantic id 匹配的 photo id

    `output_filter_second`: [string] 前两层 semantic id 匹配的 photo id

    `output_filter_third`: [string] 前三层 semantic id 匹配的 photo id

    `output_match_cnt`: [string] 三层 semantic id 匹配的数量

    `limit_num_attr`: [int] 返回数目 attr

    `target_semantic_id`: [int] target photo id 的 RQVAE 编码

    `photo_ids` : [int] 用户交互过的 photo id

    `semantic_ids` : [int] 用户交互过的视频 RQVAE 编码

    示例
    ------
    ``` python
    .common_gsu_with_rq_vae_enricher(photo_ids='photo_ids',
                             semantic_ids='semantic_ids',
                             target_semantic_id='target_semantic_id',
                             limit_num_attr='limit_num_attr',
                             output_filter_first='output_filter_first',
                             output_filter_second='output_filter_second',
                             output_filter_third='output_filter_third',
                             output_match_cnt = 'output_match_cnt',
                            )
    ```
    """
    self._add_processor(CommonGsuWithRqVaeEnricher(kwargs))
    return self


  def extract_attr_for_good_colossus_enricher(self, **kwargs):
    """
    商品序列特征解析
    ------
    参数
    ------
      # input common attr
    - `request_time_attr`: [必填] 请求时间戳
      # input item attr
    - `cate1_list_attr`: [必填] 一级类目
    - `cate2_list_attr`: [必填] 二级类目
    - `cate3_list_attr`: [必填] 三级类目
    - `category_list_attr`: [必填] 类目

    - `flow_type_list_attr`: [必填] 流量类型
    - `from_list_attr`: [必填] 来源
    - `index_list_attr`: [必填] 序列位置
    - `photo_id_list_attr`: [必填] 历史序列
    - `lag_list_attr`: [必填] 时间lag

    - `real_price_attr`: [必填] 价格
    - `real_seller_id_list_attr`: [必填] 真实seller_id
    - `seller_id_list_attr`: [必填] seller_id
    - `timestamp_list_attr`: [必填] 时间

      # output common attr
    -`resp_item_id_list_attr`: [必填] 
    -`resp_seller_id_list_attr`: [必填] 
    -`resp_real_seller_id_list_attr`: [必填] 
    -`resp_lag_list_attr`: [必填] 

    -`resp_cate1_list_attr`: [必填] 
    -`resp_cate2_list_attr`: [必填] 
    -`resp_cate3_list_attr`: [必填] 
    -`resp_category_list_attr`: [必填] 
    
    -`resp_carry_type_list_attr`: [必填]
    -`resp_click_type_list_attr`: [必填]
    -`resp_click_from_list_attr`: [必填] 
    -`resp_real_price_list_attr`: [必填] 
    -`resp_index_list_attr`: [必填] 
    -`resp_lag_hour_list_attr`: [必填] 
    -`resp_lag_min_list_attr`: [必填] 


    示例
    ------
    ``` python
    .extract_attr_for_good_colossus_enricher(request_time_attr="_REQ_TIME_",
        cate1_list_attr="good_click_cate2cate_cate1_list",
        cate2_list_attr="good_click_cate2cate_cate2_list",
        cate3_list_attr="good_click_cate2cate_cate3_list",
        category_list_attr="good_click_cate2cate_category_list",
        flow_type_list_attr="good_click_cate2cate_click_flow_type_list",
        from_list_attr="good_click_cate2cate_click_from_list",
        index_list_attr="good_click_cate2cate_click_index_list",
        photo_id_list_attr="good_click_cate2cate_item_id_list",
        lag_list_attr="good_click_cate2cate_lag_list",
        real_price_attr="good_click_cate2cate_real_price_list",
        real_seller_id_list_attr="good_click_cate2cate_real_seller_id_list",
        seller_id_list_attr="good_click_cate2cate_seller_id_list",
        timestamp_list_attr="good_click_cate2cate_timestamp_list",
        resp_item_id_list_attr="good_click_cate2cate_item_id_list_extend",
        resp_seller_id_list_attr="good_click_cate2cate_seller_id_list_extend",
        resp_real_seller_id_list_attr="good_click_cate2cate_real_seller_id_list_extend",
        resp_lag_list_attr="good_click_cate2cate_lag_list_extend",
        resp_cate1_list_attr="good_click_cate2cate_cate1_list_extend",
        resp_cate2_list_attr="good_click_cate2cate_cate2_list_extend",
        resp_cate3_list_attr="good_click_cate2cate_cate3_list_extend",
        resp_category_list_attr="good_click_cate2cate_category_list_extend",
        resp_carry_type_list_attr="good_click_cate2cate_carry_type_list_extend",
        resp_click_type_list_attr="good_click_cate2cate_click_type_list_extend",
        resp_click_from_list_attr="good_click_cate2cate_click_from_list_extend",
        resp_real_price_list_attr="good_click_cate2cate_real_price_list_extend",
        resp_index_list_attr="good_click_cate2cate_index_list_extend",
        resp_lag_hour_list_attr="good_click_cate2cate_lag_hour_list_extend",
        resp_lag_min_list_attr="good_click_cate2cate_lag_min_list_extend"
      )
    ```
    """
    self._add_processor(ExtractAttrForGoodColossusEnricher(kwargs))
    return self

  def extract_attr_for_good_colossus_v2_enricher(self, **kwargs):
    """
    商品序列特征解析
    ------
    参数
    ------
      # input common attr
    - `request_time_attr`: [必填] 请求时间戳
      # input item attr
    - `cate1_list_attr`: [选填] 一级类目
    - `cate2_list_attr`: [选填] 二级类目
    - `cate3_list_attr`: [选填] 三级类目
    - `category_list_attr`: [选填] 类目

    - `flow_type_list_attr`: [选填] 流量类型
    - `from_list_attr`: [选填] 来源
    - `index_list_attr`: [选填] 序列位置
    - `photo_id_list_attr`: [选填] 历史序列
    - `lag_list_attr`: [选填] 时间lag

    - `real_price_attr`: [选填] 价格
    - `real_seller_id_list_attr`: [选填] 真实seller_id
    - `seller_id_list_attr`: [选填] seller_id
    - `timestamp_list_attr`: [选填] 时间

      # output common attr
    -`resp_item_id_list_attr`: [选填] 
    -`resp_seller_id_list_attr`: [选填] 
    -`resp_real_seller_id_list_attr`: [选填] 
    -`resp_lag_list_attr`: [选填] 

    -`resp_cate1_list_attr`: [选填] 
    -`resp_cate2_list_attr`: [选填] 
    -`resp_cate3_list_attr`: [选填] 
    -`resp_category_list_attr`: [选填] 
    
    -`resp_carry_type_list_attr`: [选填]
    -`resp_click_type_list_attr`: [选填]
    -`resp_click_from_list_attr`: [选填] 
    -`resp_real_price_list_attr`: [选填] 
    -`resp_index_list_attr`: [选填] 
    -`resp_lag_hour_list_attr`: [选填] 
    -`resp_lag_min_list_attr`: [选填] 


    示例
    ------
    ``` python
    .extract_attr_for_good_colossus_v2_enricher(request_time_attr="_REQ_TIME_",
        cate1_list_attr="good_click_cate2cate_cate1_list",
        cate2_list_attr="good_click_cate2cate_cate2_list",
        cate3_list_attr="good_click_cate2cate_cate3_list",
        category_list_attr="good_click_cate2cate_category_list",
        flow_type_list_attr="good_click_cate2cate_click_flow_type_list",
        from_list_attr="good_click_cate2cate_click_from_list",
        index_list_attr="good_click_cate2cate_click_index_list",
        photo_id_list_attr="good_click_cate2cate_item_id_list",
        lag_list_attr="good_click_cate2cate_lag_list",
        real_price_attr="good_click_cate2cate_real_price_list",
        real_seller_id_list_attr="good_click_cate2cate_real_seller_id_list",
        seller_id_list_attr="good_click_cate2cate_seller_id_list",
        timestamp_list_attr="good_click_cate2cate_timestamp_list",
        resp_item_id_list_attr="good_click_cate2cate_item_id_list_extend",
        resp_seller_id_list_attr="good_click_cate2cate_seller_id_list_extend",
        resp_real_seller_id_list_attr="good_click_cate2cate_real_seller_id_list_extend",
        resp_lag_list_attr="good_click_cate2cate_lag_list_extend",
        resp_cate1_list_attr="good_click_cate2cate_cate1_list_extend",
        resp_cate2_list_attr="good_click_cate2cate_cate2_list_extend",
        resp_cate3_list_attr="good_click_cate2cate_cate3_list_extend",
        resp_category_list_attr="good_click_cate2cate_category_list_extend",
        resp_carry_type_list_attr="good_click_cate2cate_carry_type_list_extend",
        resp_click_type_list_attr="good_click_cate2cate_click_type_list_extend",
        resp_click_from_list_attr="good_click_cate2cate_click_from_list_extend",
        resp_real_price_list_attr="good_click_cate2cate_real_price_list_extend",
        resp_index_list_attr="good_click_cate2cate_index_list_extend",
        resp_lag_hour_list_attr="good_click_cate2cate_lag_hour_list_extend",
        resp_lag_min_list_attr="good_click_cate2cate_lag_min_list_extend"
      )
    ```
    """
    self._add_processor(ExtractAttrForGoodColossusV2Enricher(kwargs))
    return self
  def ksib_live_gsu_retriever_with_colossus_resp_v2(self, **kwargs):
    """
    KsibLiveColossusRespRetrieverV2
    ------

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    The following are required attrs from the resp_attr, could be empty
    ------
    `save_live_id_to_attr`: [string] 选配项

    `save_author_id_to_attr`: [string] 选配项

    `save_duration_to_attr`: [string] 选配项，进入直播间时主播已开播时长

    `save_play_time_to_attr`: [string] 选配项

    `save_tag_to_attr`: [string] 选配项，参考 https://docs.corp.kuaishou.com/d/home/<USER>

    `save_label_to_attr`: [string] 选配项，参考 https://docs.corp.kuaishou.com/d/home/<USER>

    `save_timestamp_to_attr`: [string] 选配项

    `save_auto_play_time_to_attr`: [string] 选配项

    `save_item_attr_as_list`: [boolean] 默认 false, 对于 play_time/label/timestamp, 如果用户曾多次播放该视频，可能会出现多值。该参数为 true 时，会把这几个 item attr 存成 list. 该参数为 false 时，只会保留最后一次出现的 item 的相应 attr. 注意：其余 attr, 即 photo_id/author_id/duration/tag 不受该参数影响。

    `filter_future_attr` : [boolean] 是否只取时间在 request time 之前的 colossus pid, 默认 false

    `deduplicate` : [boolean] 默认 true

    `photo_slot_id`: [int] 默认 0

    `author_slot_id`: [int] 默认 0

    `filter_play_time_threshold`: [int] 过滤掉 play_time 小于该值的 action item，默认 0，表示不过滤
    `filter_auto_play_time_threshold`: [int] 过滤掉 auto_play_time 小于该值的 action item，默认 0，表示不过滤

    示例
    ------
    ``` python
    .ksib_live_gsu_retriever_with_colossus_resp_v2(
      colossus_resp_attr="colossus_output",
      filter_future_attr=True,
      filter_play_time_threshold=3000,
      filter_auto_play_time_threshold=10000,
      save_live_id_to_attr="liveid",
      save_author_id_to_attr="aid",
      save_duration_to_attr="duration",
      save_play_time_to_attr="play",
      save_tag_to_attr="tag",
      save_label_to_attr="label",
      save_timestamp_to_attr="time"
      save_result_to_common_attr="colossus_photos")
    ```
    """
    self._add_processor(KsibLiveColossusRespRetrieverV2(kwargs))
    return self

  def extract_attr_for_eshop_video_colossus_enricher(self, **kwargs):
    """
    挂车短视频序列特征解析
    ------
    参数
    ------
      # input common attr
    - `request_time_attr`: [必填] 请求时间戳
    - `photo_id_list_attr`: [选填] pid
    - `author_id_list_attr`: [选填] aid
    - `duration_list_attr`: [选填] 视频时长
    - `play_time_list_attr`: [选填] 视频播放时长
    - `channel_list_attr`: [选填] 视频channel
    - `label_list_attr`: [选填] 用户行为
    - `timestamp_list_attr`: [选填] 时间
    - `spu_id_list_attr`: [选填] spu_id
    - `category_list_attr`: [选填] 类目
    - `resp_pid_list_attr`: [选填] pid
    - `resp_aid_list_attr`: [选填] aid
    - `resp_duration_list_attr`: [选填] 视频时长
    - `resp_play_time_list_attr`: [选填] 视频播放时长
    - `resp_channel_list_attr`: [选填] 视频channel
    - `resp_label_list_attr`: [选填] 用户行为
    - `resp_lag_min_list_attr`: [选填] 分钟lag
    - `resp_spu_id_list_attr`: [选填] spu_id
    - `resp_category_list_attr`: [选填] 类目
    - `filt_type`: [选填] filter type

    示例
    ------
    ``` python
    .extract_attr_for_eshop_video_colossus_enricher(
        request_time_attr="_REQ_TIME_",
        list_limit_size=1000,
        filt_type=0,
        photo_id_list_attr="photo_colossus_photo_id_list",
        author_id_list_attr="photo_colossus_author_id_list",
        duration_list_attr="photo_colossus_duration_list",
        play_time_list_attr="photo_colossus_play_time_list",
        channel_list_attr="photo_colossus_channel_list",
        label_list_attr="photo_colossus_label_list",
        timestamp_list_attr="photo_colossus_timestamp_list",
        spu_id_list_attr="photo_colossus_spu_id_list",
        category_list_attr="photo_colossus_category_list",
        resp_pid_list_attr="cart_photo_exposure_pid_list_expand_v2",
        resp_aid_list_attr="cart_photo_exposure_aid_list_expand_v2",
        resp_duration_list_attr="cart_photo_exposure_duration_list_expand_v2",
        resp_play_time_list_attr="cart_photo_exposure_play_time_list_expand_v2",
        resp_channel_list_attr="cart_photo_exposure_channel_list_expand_v2",
        resp_label_list_attr="cart_photo_exposure_label_list_expand_v2",
        resp_lag_min_list_attr="cart_photo_exposure_lag_min_list_expand_v2",
        resp_spu_id_list_attr="cart_photo_exposure_spu_id_list_expand_v2",
        resp_category_list_attr="cart_photo_exposure_category_list_expand_v2"
      )
    ```
    """
    self._add_processor(ExtractAttrForEshopVideoColossusEnricher(kwargs))
    return self

  def ksib_marm_gsu_with_index(self, **kwargs):
    """
    KsibMarmGsuWithIndexEnricher
    ------
    根据 item attr 中的 topk_index_attr, 从 `slot_input, sign_input` 中选择对应的index
    , 返回 output_sign 和 output_slots

    参数
    ------
    `slot_input` : [string] 输入的slot attrs, slot_as_attr_name 为false时可用

    `sign_input`: [string] 输入的sign attrs，slot_as_attr_name 为false时可用

    `topk_index_attr` : [string] 保存 topn index 的 attr names

    `topk` : [int] topk的大小

    `output_slot_attr`: [string] 返回的 slot attrs

    `output_sign_attr`: [string] 返回的 slot attrs

    `slot_as_attr_name`: [bool] 使用slot_id作为attr名称

    `slots`: [string] : 输入的slots, slot_as_attr_name 为true时可用
    `output_slots` [string] : 输出的slots, slot_as_attr_name 为true时可用

    示例
    ------
    ``` python
    .ksib_marm_gsu_with_index(slot_input="slot_input0",
                                    sign_input="sign_input0",
                                    topk_index_attr="topk0",
                                    topk=20,
                                    output_slot_attr=slot_output0",
                                    output_sign_attr="sign_output0")
    ```
    """
    self._add_processor(KsibMarmGsuWithIndexEnricher(kwargs))
    return self

  def ksib_slot_sign_trans(self, **kwargs):
    """
    KsibSlotSignTransEnricher
    ------
    参数
    ------

    `slot_input` : [string] 输入的slot attrs

    `sign_input`: [string] 输入的sign attrs

    `slot_as_attr_name`: [bool] 必填true

    `slots`: [string] : 输入的slots, slot_as_attr_name 为true时可用

    `is_common`: [bool] : 是否为common侧特征, 默认为true
    示例
    ------
    ``` python
    .ksib_slot_sign_trans(slot_input="slot_input0",
                                    sign_input="sign_input0",
                                    slots=["790", "791", "792", "793"])
    ```
    """
    self._add_processor(KsibSlotSignTransEnricher(kwargs))
    return self

  def marm_colossus_join(self, **kwargs):
    """
    MarmColossusJoinEnricher
    ------
    参数
    ------

    `pid_attr` : [string] pid attr

    `join_pid_attr`: [string] 用于join的 pid attr

    `pid_bits`: [int] pid位数

    `output_pid_attr` : [string] 输出 pid attr

    示例
    ------
    ``` python
    .marm_colossus_join( pid_attr = "pid_list",
                         join_pid_attr = "colossus_pids",
                         output_pid_attr = "output_pid_list",
                         pid_bits = 24
                        )
    ```
    """
    self._add_processor(MarmColossusJoinEnricher(kwargs))
    return self

  def gsu_clotho_get(self, **kwargs):
    """
    GsuClothoEnricher
    ------
    从 clotho 中读取 kv 类型的数据 存到 context.

    参数配置
    ------
    `kess_name`: [string] Clotho 的 kess name

    `table_name`: [string] Clotho 数据的 table

    `token_name`: [int] Clotho 请求token

    `timeout_ms`: [int] 超时时间, 默认 200ms

    `key_attr`: [string] 从哪个 attr 读 key.

    `output_attr`: 结果填充到哪个 attr.

    调用示例
    ------
    ``` python
    .gsu_clotho_get(
      kess_name="reco-clotho-gateway-test",
      table_name="clotho_test",
      key_attr="clotho_key",
      output_attr="clotho_value",
    )
    ```
    """
    self._add_processor(GsuClothoEnricher(kwargs))
    return self

  def inner_ad_action(self, **kwargs):
    """
    InnerAdActionEnricher
    ------
    
    根据行为标签位运算过滤用户行为序列，提取特定类型的广告行为序列
    
    参数
    ------
    `request_time_attr`: [string] 请求时间属性名
    
    `timestamp_list_attr`: [string] 输入时间戳序列属性名
    
    `photo_id_list_attr`: [string] 输入photo_id序列属性名
    
    `item_id_list_attr`: [string] 输入item_id序列属性名
    
    `channel_list_attr`: [string] 输入频道序列属性名
    
    `author_id_list_attr`: [string] 输入作者ID序列属性名
    
    `spu_id_list_attr`: [string] 输入SPU ID序列属性名
    
    `category_level1_list_attr`: [string] 输入一级类别序列属性名
    
    `category_level2_list_attr`: [string] 输入二级类别序列属性名
    
    `category_level3_list_attr`: [string] 输入三级类别序列属性名
    
    `category_level4_list_attr`: [string] 输入四级类别序列属性名
    
    `account_id_list_attr`: [string] 输入账户ID序列属性名
    
    `label_list_attr`: [string] 输入标签序列属性名（用于行为过滤）
    
    `resp_timestamp_list_attr`: [string] 输出时间戳序列属性名
    
    `resp_photo_id_list_attr`: [string] 输出photo_id序列属性名
    
    `resp_item_id_list_attr`: [string] 输出item_id序列属性名
    
    `resp_channel_list_attr`: [string] 输出频道序列属性名
    
    `resp_author_id_list_attr`: [string] 输出作者ID序列属性名
    
    `resp_spu_id_list_attr`: [string] 输出SPU ID序列属性名
    
    `resp_category_level1_list_attr`: [string] 输出一级类别序列属性名
    
    `resp_category_level2_list_attr`: [string] 输出二级类别序列属性名
    
    `resp_category_level3_list_attr`: [string] 输出三级类别序列属性名
    
    `resp_category_level4_list_attr`: [string] 输出四级类别序列属性名
    
    `resp_account_id_list_attr`: [string] 输出账户ID序列属性名
    
    `filt_type`: [int] 过滤类型，0-19对应不同的行为类型（曝光、3s播放、5s播放、播完、转化、下单、商品页浏览、点击等）
    
    `filt_mask`: [int] 过滤掩码，如果为0则使用 (1 << filt_type)，支持多位过滤
    
    `list_limit_size`: [int] 输出序列最大长度限制，默认1000
    
    `action_threshold`: [int] 行为阈值，默认0
    
    调用示例
    ------
    ``` python
    .inner_ad_action(
        name='inner_ad_action_impression_seq',
        timestamp_list_attr="inner_ad_timestamp_list",
        photo_id_list_attr="inner_ad_photo_id_list",
        item_id_list_attr="inner_ad_item_id_list",
        channel_list_attr="inner_ad_channel_list",
        author_id_list_attr="inner_ad_author_id_list",
        spu_id_list_attr="inner_ad_spu_id_list",
        category_level1_list_attr="inner_ad_category_level1_list",
        category_level2_list_attr="inner_ad_category_level2_list",
        category_level3_list_attr="inner_ad_category_level3_list",
        category_level4_list_attr="inner_ad_category_level4_list",
        account_id_list_attr="inner_ad_account_id_list",
        label_list_attr="inner_ad_label_list",
        resp_timestamp_list_attr="inner_ad_impression_timestamp_list",
        resp_photo_id_list_attr="inner_ad_impression_photo_id_list",
        resp_item_id_list_attr="inner_ad_impression_item_id_list",
        resp_channel_list_attr="inner_ad_impression_channel_list",
        resp_author_id_list_attr="inner_ad_impression_author_id_list",
        resp_spu_id_list_attr="inner_ad_impression_spu_id_list",
        resp_category_level1_list_attr="inner_ad_impression_category_level1_list",
        resp_category_level2_list_attr="inner_ad_impression_category_level2_list",
        resp_category_level3_list_attr="inner_ad_impression_category_level3_list",
        resp_category_level4_list_attr="inner_ad_impression_category_level4_list",
        resp_account_id_list_attr="inner_ad_impression_account_id_list",
        filt_type=0,
        list_limit_size=5000,
        request_time_attr="_REQ_TIME_"
    )
    ```
    """
    self._add_processor(InnerAdActionEnricher(kwargs))
    return self

