#!/usr/bin/env python3
# coding=utf-8
"""
filename: ug_rtb_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, ad api mixin
author: <EMAIL>
date: 2021-06-26 20:45:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .ug_rtb_enricher import *
from .ug_rtb_arranger import *

class UgRtbApiMixin(CommonLeafBaseMixin):
  """
  广告增长RTB服务Processor API 接口的 Mixin 实现。
  背景：广告通过RTB引擎对接多个流量媒体对素材、定向进行各种打分排序，实现个性化的广告投放，提升ROI。
  维护人/团队：增长广告投放(<EMAIL>、<EMAIL>、<EMAIL>)
  """

  def intersection(self, **kwargs):
    """
    ListIntersectionEnricher
    ------
    input_common_list和input_item_list取交集

    参数配置
    ------
    `input_common_list`: [string] 必配项，取交集的common attr name

    `input_item_list`: [string] 必配项，取交集的item attr name

    `output_attr_name`: [string] 必配项，交集的输出结果

    调用示例
    ------
    ``` python
    .intersection(
      input_common_list="common_list",
      input_item_list="list_attr",
      output_attr_name="output_item_attr"
    )
    ```
    """
    self._add_processor(ListIntersectionEnricher(kwargs))
    return self
  

  def common_json_kconf_to_item_attr(self, **kwargs):
    """
      CommonJsonKconfToItemAttrEnricher
      ------
      遍历list类型的common attr，每个common attr value作为key读取kconf配置得到map[key, kconf_value]。在item粒度按根据以指定的item attr为key，从map中读取kconf_value赋值到export_item_attr中。
      该算子目的是减少item粒度get_kconf_params的使用，优化执行时长。kconf读取逻辑继承自CommonRecoKconfCommonAttrEnricher

      参数配置
      ------
      `kconf_configs`: [list]
        - `kconf_key`: [string] [动态参数] kconf 中的 key

        - `default_value`: [int|double|string|bool|list] kconf 配置获取失败时取用的默认值, 该值的类型需与 kconf_key 实际配置的值类型一致！

        - `json_path`: [string] 通过该项配置获取指定 json_path 下的值（仅支持 int/double/string/int_list/double_list/string_list 6 种类型的值），json_path 使用句点 '.' 分隔："field1.filed2.filed3"，单个部分支持用花括号指定取某个common attr名称（借用格式获取attr名称，不是真实的动态参数）。

        - `export_item_attr`: [string] 写入指定的 ItemAttr（根据不同 ItemAttr 下指定的 json_path 值）

        - `list_common_attr_name`: [string] 动态参数取值list对应的CommonAttr名称，只支持list_int|list_string

        - `dynamic_attr_name`: [string] json_path中动态参数对应的attr名称

        - `target_item_attr_key`: [string] 从map取值填充export_item_attr时作key的字段，未配置时使用dynamic_attr_name

        - `dynamic_common_attrs`: [string_list] [可选项] json_path 中的动态参数 CommonAttr 名称列表（只支持指定 attr 名称，不是完整真实的动态参数）


      调用示例
      ------
      ``` python
      # 遍历product_list，以list中每个值作为 product，从kconf获取web.[product].url对应的配置值得到map[product, url]，然后对以item_product为key取map中的url赋值到item_url
      .common_json_kconf_to_item_attr(
        kconf_configs = [{
          "kconf_key": "webserver.api.tecentApi",
          "export_item_attr": "item_url",
          "default_value": "http://www.qq.com/",
          "list_common_attr_name": "product_list",
          "dynamic_attr_name": "product",
          "target_item_attr_key": "item_product",  # 缺省时用 dynamic_attr_name 的值代替
          "dynamic_common_attrs": ["media"],
          "json_path": "media.web.product.url",  # media 从 common attr 里解析， product 代表 product_list 中的每个取值
        }]
      )
      ```
      """
    self._add_processor(CommonJsonKconfToItemAttrEnricher(kwargs))
    return self


  def read_json_kconf_with_list_attr(self, **kwargs):
    """
      ReadJsonKconfWithListAttrEnricher
      ------
      （仅支持item粒度）遍历输入的list类型item attr，以每个取值作为动态参数，读取json类型的kconf，将value写入目标list item attr中（顺序和输入的item attr一致，即可用下标对应获取）。用于增长投放中“单创意、多adId出价”等场景。kconf读取逻辑继承自CommonRecoKconfCommonAttrEnricher

      参数配置
      ------
      `kconf_configs`: [list]
        - `kconf_key`: [string] [动态参数] [必须项] kconf 中的 key

        - `default_value`: [int|double|string|bool] [必须项] kconf 配置获取失败时取用的默认值, 不支持list类型！（考虑到读取结果用list处理，为了一一对应必须提供默认值）

        - `json_path`: [string] [必须项] 通过该项配置获取指定 json_path 下的值（仅支持 int/double/string/int_list/double_list/string_list 6 种类型的值），json_path 使用句点 '.' 分隔："field1.filed2.filed3"，句点分割的每个部分支持用双花括号指定取某个common/item attr名称（借用格式获取 attr 名称，不是真实的动态参数）。

        - `export_item_attr`: [string] [必须项] 写入指定的 ItemAttr（根据不同 ItemAttr 下指定的 json_path 值）

        - `list_item_attr_name`: [string] [必须项] 作为输入的 list 类型 ItemAttr 名称，只支持 list_int|list_string

        - `dynamic_item_attr`: [string] [必须项] json_path 中表示遍历 list_item_attr_name 取值来替换的部分

        - `dynamic_common_attrs`: [string_list] [可选项] json_path 中的动态参数 CommonAttr 名称列表（只支持指定 attr 名称，不是完整真实的动态参数）


      调用示例
      ------
      ``` python
      # 根据 adid_list读取多个出价系数，写入 auto_alpha_list 中，和 adid_list 的顺序一一对应
      .read_json_kconf_with_list_attr(
        kconf_configs = [{
          "list_item_attr_name": "adid_list",
          "dynamic_item_attr": "adId",
          "dynamic_common_attrs": ["media", "product"],
          "kconf_key": "userGrowth.rtb2.refluxConstraintParasDoubleBidUninstallV2",
          "export_item_attr": "auto_alpha_list",
          "default_value": 0.999,
          "json_path": "media.product.INSTALLED.adId.autoAlpha",  # 根据配置，media 和 product 是 CommonAttr 动态参数，adId 用 adid_list的每个取值来替换
        }]
      )
      ```
      """
    self._add_processor(ReadJsonKconfWithListAttrEnricher(kwargs))
    return self


  def ug_rtb_retrieve_strategy_kconf_parse(self, **kwargs):
    """
      UgRtbRetrieveStrategyEnricher
      ------
      增长 rtb 召回服务策略配置 kconf 解析，根据请求的 “媒体 x 产品 x 新回活状态” 动态获取要执行的召回策略的开关和分路召回数量，valid_strategy_list 内为有效策略，产出对应配置的 common attr ，其它策略赋予默认值（开关关闭、召回数为0等）
      未来可接入更多分策略配置的解析能力

      参数配置
      ------
      `kconf_key`: [string] [动态参数] [必须项] kconf 名称

      `output_strategies`: [string_list] [必须项] 需要产出配置的策略名称

      `default_value_map`: [dict] [可选项] 各策略参数的默认值，目前只可填策略召回数，当 `default_value_map["recall_num"]` 为设置时默认为10（经验值）

      `rtb_biz_list`: [string_list] [必须项] 需要产出策略参数的 biz_type 可填值有 `"base"` , `"content"` , `commodity`

      `media_attr_name`: [string] [可选项] “媒体”字段对应的 common attr 名称，默认为 `"media"`

      `product_attr_name`: [string] [可选项] “产品”字段对应的 common attr 名称，默认为 `"product"`

      `action_attr_name`: [string] [可选项] “新回活状态”字段对应的 common attr 名称，默认为 `"device_status"`

      
      调用示例
      ------
      ``` python
      # 产出 content_rtb_top_valid/content_ee_success_valid = 1 ， 以及 content_rtb_top_recall_num/content_ee_success_recall_num = 0
      # 其它 strategy 对应的 xxx_valid/xxx_recall_num 均为0
      .ug_rtb_retrieve_strategy_kconf_parse(
        kconf_key="userGrowth.rtbRetrieve.defaultRetrieveStrategy",
        output_strategies=["rta_top", "rtb_top", "ee_success"],
        default_value_map={"recall_num": 10},
        rtb_biz_list=["base", "content", "commodity"],
        media_attr_name="media",
        product_attr_name="product",
        action_attr_name="device_status"
      )
      ```
    """
    self._add_processor(UgRtbRetrieveStrategyEnricher(kwargs))
    return self


  def ug_rtb_retrieve_zigzag_select(self, **kwargs):
    """
      UgRtbRetrieveZigzagSelectArranger
      ------
      增长 rtb 多路召回结果按 zigzag 规则优选指定数量的 item ，根据配置的分路 quota ，每一路最多返回 quota 个 item ，不同分路召回了相同 item_key 时会去重。**选中的item不改变其在结果集中原有的顺序** 未来考虑引入兜底分路补齐、结果排序等

      参数配置
      ------
      `total_limit`: [int] [动态参数] [必须项] 返回的 item 数量上限

      `recall_reason_list`: [int_list] [动态参数] [必须项] 分路召回标识 reason 的列表

      `reason_limit_list`: [int_list] [动态参数] [必须项] 每个 reason 预期选出的 item 数量上限，**数组元素个数必须和 recall_reason_list 保持相同一一对应，不一致时代码层面兜底，所有 reason 都只取 
      `defualt_reason_limit` 个**

      `default_reason_limit`: [int] [可选项] 分路 quota 配置异常时所有分路使用的兜底 quota 不支持动态参数，未配置时默认值为 25

      `dedup_item_attr`: [string] [可选项] 选择过程中按指定的 item attr 去重， attr 的数据类型必须为 int

      调用示例
      ------
      ``` python
      # 根据配置的分路 quota 按 zigzag 规则交错选出 200 个 item
      .ug_rtb_retrieve_zigzag_select(
        total_limit=200,
        recall_reason_list=[1, 2, 3],  # 召回 reason 的枚举值
        reason_limit_list=[30, 40, 50],
        default_reason_limit=30,  # 在本示例中 recall_reason_list 和 reason_limit_list 的 size 相同，为合法配置，默认值不生效
        dedup_item_attr="creative_id",  # 按创意 id 去重
      )
      ```
    """
    self._add_processor(UgRtbRetrieveZigzagSelectArranger(kwargs))
    return self


  def ug_rtb_zigzag_select_by_attr(self, **kwargs):
    """
      UgRtbZigzagSelectByAttrArranger
      ------
      对召回结果集按指定的 item attr 进行聚合后做 zigzag 选取，每个 attr 取值的 quota 数量由 kconf 指定，kconf 遵循增长投放的媒体 x 产品 x 新回活维度配置，**选中的item不改变其在结果集中原有的顺序**
      
      参数配置
      ------
      `kconf_key`: [string] [动态参数] [必须项] kconf 名称

      `total_limit`: [int] [动态参数] [必须项] 返回的 item 数量上限

      `group_by_attr`: [string] [可选项] zigzag 聚合维度的 attr 名称，数据必须为 int 类型，默认为 `"biz_type"` ，按业务类型

      `group_attr_enum_name_map`: [dict] [必须项] 聚合维度 item attr 取值和对应枚举名称的映射关系，**枚举名称是 kconf 配置中 json key 的叶子节点**， 用于获取每个取值下选取的 item 数量上限，例如业务类型映射 <1, "content> <3, "commodity">

      `media_attr_name`: [string] [可选项] “媒体”字段对应的 common attr 名称，默认为 `"media"`

      `product_attr_name`: [string] [可选项] “产品”字段对应的 common attr 名称，默认为 `"product"`

      `action_attr_name`: [string] [可选项] “新回活状态”字段对应的 common attr 名称，默认为 `"device_status"`

      调用示例
      ------
      ``` python
      # 按 biz_type 粒度做 zigzag 选择，至多返回1000个
      .ug_rtb_zigzag_select_by_attr(
        kconf_key="zigZagByBiz",
        total_limit=1000,
        group_by_attr="biz_type",
        group_attr_enum_name_map=<1:"content", 3:"commodity", 4:"base">,
        media_attr_name="media",
        product_attr_name="product",
        action_attr_name="device_status"
      )
      ```
    """
    self._add_processor(UgRtbZigzagSelectByAttrArranger(kwargs))
    return self


  def ug_rtb_retrieve_strategy_parse_v2(self, **kwargs):
    """
      UgRtbRetrieveStrategyParseV2Enricher
      ------
      增长 rtb 召回服务策略配置 kconf 解析，根据请求的 “媒体 x 产品 x 新回活状态 x 安装态” 动态获取要执行的召回策略的开关、分路召回数量
      和 zigzag 选择截断的数量
      ------
      `kconf_key`: [string] [动态参数] [必须项] kconf 名称

      `output_strategies`: [int_list] [必须项] 需要产出召回配置的策略分路 reason 列表

      `reason_list_attr`: [string] [必须项] 输出的召回分路 reason 列表对应的 common attr 名称

      `select_num_attr`: [string] [必须项] 输出的分路对应的 zigzag 选择截断数量 list common attr 名称，配置的雷彪长度必须和

      `default_value_map`: [dict] [可选项] 各策略参数的默认值，支持倒排召回数 recall_num 和 选择截断数 select_num 默认值均为 10

      `media_attr_name`: [string] [可选项] “媒体”字段对应的 common attr 名称，默认为 `"media"`

      `product_attr_name`: [string] [可选项] “产品”字段对应的 common attr 名称，默认为 `"product"`

      `action_attr_name`: [string] [可选项] “新回活状态”字段对应的 common attr 名称，默认为 `"device_status"`

      `install_status_attr_name': [string] [可选项] “安装态”字段对应的 common attr 名称，默为 `"fusion_install_status"`

      
      调用示例
      ------
      ``` python
      # 产出 content_rtb_top_valid/content_ee_success_valid = 1 ， 以及 content_rtb_top_recall_num/content_ee_success_recall_num = 0
      # 其它 strategy 对应的 xxx_valid/xxx_recall_num 均为0
      .ug_rtb_retrieve_strategy_parse_v2(
        kconf_key="userGrowth.rtbRetrieve.defaultRetrieveStrategy",
        output_strategies=["rta_top", "rtb_top", "ee_success"],
        default_value_map={"recall_num": 10},
        rtb_biz_list=["base", "content", "commodity"],
        media_attr_name="media",
        product_attr_name="product",
        action_attr_name="device_status"
      )
      ```
    """
    self._add_processor(UgRtbRetrieveStrategyParseV2Enricher(kwargs))
    return self


  def ug_rtb_retrieve_term_combine_v1(self, **kwargs):
    """
      UgRtbRetrieveTermCombineV1Enricher
      ------
      增长 rtb 召回索引 query 组合倒排拼接，一次性产出所有倒排查询的 query 变量，倒排格式为 “媒体_模版_产品_新回活圈层_安装态_出价类型_索引实验标签_算法策略标签”。算子输出 `strategy_terms` 中所有 `combine_term` 为名称的 common attr
      ------
      `media_attr_name`: [string] [可选项] “媒体”字段对应的 common attr 名称，默认为 `"retrieve_media"`

      `template_attr_name`: [string] [可选项] “模版规格”字段对应的 common attr 名称，默认为 `"recall_template_id"`

      `product_attr_name`: [string] [可选项] “产品”字段对应的 common attr 名称，默认为 `"retrieve_product_name"`

      `action_attr_name`: [string] [可选项] “新回活状态”字段对应的 common attr 名称，默认为 `"device_status"`

      `install_status_attr_name': [string] [可选项] “安装态”字段对应的 common attr 名称，默为 `"fusion_install_status"`

      `bid_types_attr_name': [string] [可选项] “出价类型”字段对应的 common attr 名称，默为 `"support_bid_types"`

      `creative_package_attr_name': [string] [可选项] “索引创意实验”字段对应的 common attr 名称，默为 `"creative_package_list"`

      `gdt_site_attr_name`: [string] [可选项] “广点通站点大类”字段对应的 common attr 名称，默为 `"inner_gdt_site_id"`

      `use_gdt_site_term`: [bool] [可选项] 是否在组合倒排中引入广点通站点大类，即 `gdt_site_attr_name` 指定的 common attr

      `audit_status_attr_name`: [string] [可选项] “审核状态”字段对应的 common attr 名称，默为 `"retrieve_audit_status"`

      `use_audit_status_term`: [bool] [可选项] 是否在组合倒排中引入审核状态，即 `audit_status_attr_name` 指定的 common attr

      `strategy_terms`: [list] [必填项] 算法策略标签，每个对应一路召回策略，内容为 dict 格式
        - `combine_term`: [string] 组合倒排 term 对应的 common attr 名称
        - `single_term`: [string] [动态参数] 算法策略标签的取值（常量）或动态参数取值

      调用示例
      ------
      ``` python
      .ug_rtb_retrieve_term_combine_v1(
        # 其它参数均使用缺省默认值
        strategy_terms = [{
          "combine_term": media_tp_product_action_install_status_bid_types_creative_package_alliance_retarget_0,
          "single_term": "{{alliance_clk_item_categories}}"
        }]
      )
      ```
    """
    self._add_processor(UgRtbRetrieveTermCombineV1Enricher(kwargs))
    return self


  def ug_fill_flatten_item_attrs(self, **kwargs):
    """
      UgFillFlattenItemAttrEnricher
      ------
      对结果集按某一列 list item attr 展开新的 item 结果集进行 attr 拷贝填充，示例场景： RTB 0.5 把账户维度 item 的创意列表展开得到创意 item 之后，把账户 item 指定的 attr 拷贝给新的创意 item
      ------
      `original_item_key_list`: [string] [必填项] 所有原始 item key 的 common attr 名称（例如账户 id ）对应输入 attr 的账户维度 item 列表

      `flatten_item_id_list`: [string] [必填项] 展开后目标 item 的 id 列表的 common attr 名称，对应输出的 item 列表

      `aggregated_flatten_item_id_list`: [string] [必填项] 每个原始 item 用来展开新 item 的 attr 名称

      `flatten_item_id_attr': [string] [必填项] 展开后的目标 item 唯一 key 对应的 attr 名称

      `fill_item_attrs': [list] [必填项] 所有要填充的 item attr 名称列表

      调用示例
      ------
      ``` python
      # 将账户 id 上指定的 attr 拷贝给 按 aggregated_creative_id_list 展开得到的所有创意 item
      .ug_fill_flatten_item_attrs(
            original_item_key_list="ori_item_key_list",
            flatten_item_id_list="creative_id_collection",
            flatten_item_id_attr="real_creative_id",
            aggregated_flatten_item_id_list="aggregated_creative_id_list",
            fill_item_attrs=["image_width", "image_height", "creative_type"]
        )
      ```
    """
    self._add_processor(UgFillFlattenItemAttrEnricher(kwargs))
    return self


  def ug_modify_attr_mutability(self, **kwargs):
    """
      UgModifyAttrMutabilityEnricher
      ------
      设置指定 attr 的修改标识（只读或可修改），*慎重使用，确保只在主 flow 调用或没有写竞争，否则有线程安全问题！！！*
      ------
      `input_common_attrs`: [list] [可选项] 待标记的 common attr 列表

      `input_item_attrs`: [list] [可选项] 待标记的 item attr 列表

      `is_mutable`: [bool] [必填项] 是否标记为`可修改`，值为 True 时将 attr 设为`可修改`， False 时设为`只读`

      调用示例
      ------
      ``` python
      .ug_modify_attr_mutability(
        input_common_attrs=["request_id", "install_status"],
        input_item_attrs=["creative_id", "bid_price"],
        is_mutable=True,  # 以上attr设为可修改
      )
      ```
    """
    self._add_processor(UgModifyAttrMutabilityEnricher(kwargs))
    return self


  def enrich_attr_by_mapping(self, **kwargs):
    """
      AttrMappingEnricher
      ------
      根据传入的外部mappings, 对common/item attr进行key/value的映射
      ------
      `mappings`: [dict] [必填项] 映射规则, 仅支持<string, string> (<string, int>会转为<string, string>)

      `is_common`: [bool] [可选项] 处理的attr是否为 common attr ，默认值为 True

      `configs`: [list] [必填项] 待映射的配置, 可配置多项
        - `input`: [string] 输入的attr名称，类型支持 int/string (int会转为string完成查找)
        - `output`: [string] 输出的attr名称，类型支持 int/string
        - `mode`: [int] 转换模式，1=key->value，2=value->key，默认1
        - `to_int`: [bool] 是否将输出字段转为int类型，默认False

      调用示例
      ------
      ``` python
      .enrich_attr_by_mapping(
        mappings={"ACTIVE" : 1, "REFLUX" : 2},
        is_common=True,
        configs=[
          dict(input="test_input", ouput="test_output", mode=1, to_int=True),
          dict(input="test_input1", ouput="test_output2", mode=2)
        ]
      )
      ```
    """
    self._add_processor(AttrMappingEnricher(kwargs))
    return self


  def did_uid_resolver(self, **kwargs):
    """
      DidUidResolverEnricher
      ------
      返回 DID最近登录过的UID / UID最近登录过的DID, 支持获取末次活跃时间
      ------
      `behavior`: [int] [必填项] 转换行为类型
        - 1: DID_TO_UID - DID → UID
        - 2: DID_TO_UID_WITH_ACTIVITY - DID → UID + 活跃时间
        - 3: UID_TO_DID - UID → DID  
        - 4: UID_TO_DID_WITH_ACTIVITY - UID → DID + 活跃时间

      `input_id_attr_name`: [string] [必填项] 输入 ID 的 attr 名称
        - behavior=1/2 时，通过 DID (string 类型) 读取 UID 数据
        - behavior=3/4 时，读取 UID (string 类型)

      `product`: [int] [选填项] 产品标识
        - product=1, 快手
        - product=17, 快手极速版
        - 详情参考: reco_proto/proto/idmapping/product_platform.proto

      `kpn_product`: [string] [可选项] KPN 产品标识。
        - 作为 `product` 字段的补充。当 `product` 无法确定有效产品时，回退使用此字段。

      `request_src`: [string] [必填项] 请求来源标识，格式为 "业务+服务名"

      `cluster_name`: [string] [选填项], redis 集群名称
        - 默认值: behavior=1/2 时, cluster_name = userGrowthBaseDataDidUidMapping
        -        behavior=3/4 时, cluster_name = userGrowthBaseDataUidDidMapping

      `timeout_ms`:  [int] [选填项] 超时时间(ms), 默认10ms

      `output_id_attr_name`: [string] [必填项] 输出 ID 的 attr 名称
        - behavior=1/3 时，输出转换后的 ID
        - behavior=2/4 时，输出转换后的 ID

      `output_active_ts_attr`: [string] [可选项] 输出活跃时间戳的 attr 名称
        - behavior=2/4 时为必填项，输出上次活跃时间

      `resolve_status_attr`: [string] [必填项] ID 解析状态码输出 attr 名称
        - 用于标识解析是否成功及错误原因

      调用示例
      ------
      ``` python
      # DID 转 UID 并获取活跃时间
      .did_uid_resolver(
        behavior=2,  # DidUidResolverEnricher.IdMappingBehavior.DID_TO_UID_WITH_ACTIVITY
        input_id_attr_name="did",
        product=1,
        kpn_product="xxxx",
        request_src="ug_rtb_xxx_service",
        cluster_name="userGrowthBaseDataDidUidMapping",
        timeout_ms=10,
        output_id_attr_name="uid",
        output_active_ts_attr="last_active_time",
        resolve_status_attr="id_resolve_status"
      )

      # UID 转 DID
      .did_uid_resolver(
        behavior=3,  # DidUidResolverEnricher.IdMappingBehavior.UID_TO_DID
        input_id_attr_name="uid", 
        product=1,
        request_src="ug_rtb_xxx_service",
        output_id_attr_name="did",
        resolve_status_attr="id_resolve_status"
      )
      ```
    """
    self._add_processor(DidUidResolverEnricher(kwargs))
    return self