#!/usr/bin/env python3
"""
filename: ug_rtb_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for ad
author: k<PERSON><PERSON><PERSON>@kuaishou.com
date: 2021-09-19 13:21:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher


class ListIntersectionEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "intersection"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["input_common_list"])  # input_common_list实际上只是一个string，这里应该用add而非update
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {self._config.get("input_item_list")}

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {self._config.get("output_attr_name")}


class CommonJsonKconfToItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_json_kconf_to_item_attr"
  
  @strict_types
  def _check_config(self) -> None:
    for conf in self._config["kconf_configs"]:
      check_arg("export_item_attr" in conf, "必须配置 export_item_attr")
      check_arg("dynamic_attr_name" in conf and isinstance(conf.get("dynamic_attr_name"), str), "必须指定dynamic_attr_name，值为json_path中的动态参数部分")
      check_arg("list_common_attr_name" in conf, "必须配置list_common_attr_name")
      check_arg("kconf_key" in conf, "必须配置kconf_key")
      check_arg("json_path" in conf and isinstance(conf.get("json_path"), str), "必须配置 json_path")
      json_path = conf.get("json_path")
      dynamic_attr_name = conf.get("dynamic_attr_name")
      check_arg(f"{{{{{dynamic_attr_name}}}}}" in json_path, f"json_path中必须配置动态参数{dynamic_attr_name}")
      dynamic_common_attrs = conf.get("dynamic_common_attrs")
      if dynamic_common_attrs:
        check_arg(isinstance(dynamic_common_attrs, list), "dynamic_common_attrs 必须为 list 类型")
        for attr in dynamic_common_attrs:
          check_arg(isinstance(attr, str), "dynamic_common_attrs 内容必须为 str 类型")
      # 检查json_path中的动态参数项
      json_path_vec = json_path.split(".")
      for path in json_path_vec:
        if path.startswith("{{") and path.endswith("}}"):
          dynamic_param = path[2:-2]
          if dynamic_attr_name != dynamic_param and (dynamic_common_attrs is None or dynamic_param not in dynamic_common_attrs):
            check_arg(False, f"dynamic_attr({path}) in 'json_path' must be equal to 'dynamic_attr_name'({dynamic_attr_name}) or configured in 'dynamic_common_attrs")


  @strict_types
  def depend_on_items(self) -> bool:
    return any("export_item_attr" in v for v in self._config["kconf_configs"])

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for v in self._config["kconf_configs"]:
      attrs.update(self.extract_dynamic_params(v["kconf_key"]))
      if "list_common_attr_name" in v:
        attrs.add(v["list_common_attr_name"])
      if "dynamic_common_attrs" in v:
        for common_attr in v['dynamic_common_attrs']:
          attrs.add(common_attr)
      
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    # C++ 算子中判断不存在 target_item_attr_key 时用 dynamic_attr_name 为其赋值
    for v in self._config["kconf_configs"]:
      if "target_item_attr_key" in v and v["target_item_attr_key"] != '' and v["target_item_attr_key"] is not None:
        attrs.add(v["target_item_attr_key"])
      else:
        attrs.add(v["dynamic_attr_name"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { v["export_item_attr"] for v in self._config["kconf_configs"] if "export_item_attr" in v }

class ReadJsonKconfWithListAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "read_json_kconf_with_list_attr"
  
  @strict_types
  def _check_config(self) -> None:
    for conf in self._config["kconf_configs"]:
      check_arg("export_item_attr" in conf, "必须配置 export_item_attr")
      check_arg("dynamic_item_attr" in conf and isinstance(conf.get("dynamic_item_attr"), str), "必须指定 dynamic_item_attr")
      check_arg("list_item_attr_name" in conf, "必须配置 list_item_attr_name")
      check_arg("kconf_key" in conf, "必须配置 kconf_key")
      check_arg("json_path" in conf and isinstance(conf.get("json_path"), str), "必须配置 json_path")
      check_arg("default_value" in conf, "必须配置 default_value")
      json_path = conf.get("json_path")
      dynamic_item_attr = conf.get("dynamic_item_attr")
      check_arg(f"{{{{{dynamic_item_attr}}}}}" in json_path, f"json_path中必须配置动态参数{dynamic_item_attr}")
      dynamic_common_attrs = conf.get("dynamic_common_attrs")
      if dynamic_common_attrs:
        check_arg(isinstance(dynamic_common_attrs, list), "dynamic_common_attrs 必须为 list 类型")
        for attr in dynamic_common_attrs:
          check_arg(isinstance(attr, str), "dynamic_common_attrs 内容必须为 str 类型")
      # 检查json_path中的动态参数项
      json_path_vec = json_path.split(".")
      for path in json_path_vec:
        if path.startswith("{{") and path.endswith("}}"):
          dynamic_param = path[2:-2]
          if dynamic_item_attr != dynamic_param and (dynamic_common_attrs is not None and dynamic_param not in dynamic_common_attrs):
            check_arg(False, f"dynamic_attr({path}) in 'json_path' must be equal to 'dynamic_item_attr'({dynamic_item_attr}) or configured in 'dynamic_common_attrs")

  @strict_types
  def depend_on_items(self) -> bool:
    return any("export_item_attr" in v for v in self._config["kconf_configs"])

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for v in self._config["kconf_configs"]:
      attrs.update(self.extract_dynamic_params(v["kconf_key"]))
      if "dynamic_common_attrs" in v:
        for common_attr in v['dynamic_common_attrs']:
          attrs.add(common_attr)

    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for v in self._config["kconf_configs"]:
      if "list_item_attr_name" in v:
        attrs.add(v["list_item_attr_name"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { v["export_item_attr"] for v in self._config["kconf_configs"] if "export_item_attr" in v }


class UgRtbRetrieveStrategyEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ug_rtb_retrieve_strategy_kconf_parse"
  
  @strict_types
  def _check_config(self) -> None:
    conf = self._config
    valid_biz_names = ["base", "content", "commodity"]

    check_arg("kconf_key" in conf, "必须配置 kconf_key")
    check_arg("output_strategies" in conf, "必须配置 output_strategies")
    output_strategies = conf["output_strategies"]
    check_arg(isinstance(output_strategies, list) and len(output_strategies) > 0, "output_strategies 必须为 string_list 类型且非空")
    for name in output_strategies:
      check_arg(isinstance(name, str) and name != '', "output_strategies 每个取值必须为非空string")
    check_arg("rtb_biz_list" in conf, "必须配置 rtb_biz_list")
    rtb_biz_list = conf["rtb_biz_list"]
    check_arg(isinstance(rtb_biz_list, list) and len(rtb_biz_list) > 0, "rtb_biz_list 必须为非空 string_list 类型")
    for biz in rtb_biz_list:
      check_arg(biz in valid_biz_names, "rtb_biz_list 每个取值必须在 [%s] 中" % ", ".join(valid_biz_names))

  @strict_types
  def depend_on_items(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    conf = self._config
    attrs.update(self.extract_dynamic_params(conf["kconf_key"]))
    # 媒体
    media = conf.get("media_attr_name")
    if isinstance(media, str) and media != "":
      attrs.add(media)
    else:
      attrs.add("media")
    # 产品
    product = conf.get("product_attr_name")
    if isinstance(product, str) and product != "":
      attrs.add(product)
    else:
      attrs.add("product")
    # 新回活状态
    action = conf.get("action_attr_name")
    if isinstance(action, str) and action != "":
      attrs.add(action)
    else:
      attrs.add("device_status")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for biz_name in self._config["rtb_biz_list"]:
      for strategy_name in self._config["output_strategies"]:
        strategy_valid = "%s_%s_valid" % (biz_name, strategy_name)
        recall_num_conf = "%s_%s_recall_num" % (biz_name, strategy_name)
        attrs.add(strategy_valid)
        attrs.add(recall_num_conf)
    return attrs

class UgRtbRetrieveStrategyParseV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ug_rtb_retrieve_strategy_parse_v2"
  
  @strict_types
  def _check_config(self) -> None:
    conf = self._config

    check_arg("kconf_key" in conf, "必须配置 kconf_key")
    check_arg("output_strategies" in conf, "必须配置 output_strategies")
    output_strategies = conf["output_strategies"]
    check_arg(isinstance(output_strategies, list) and len(output_strategies) > 0, "output_strategies 必须为 int_list 类型且非空")
    for strategy_reason in output_strategies:
      check_arg(isinstance(strategy_reason, int) and strategy_reason > 0, "output_strategies 每个取值必须为正整数")
    check_arg("reason_list_attr" in conf and isinstance(conf["reason_list_attr"], str) and conf["reason_list_attr"] != "",
              "必须配置 reason_list_attr 取值为非空字符串")
    check_arg("select_num_attr" in conf and isinstance(conf["select_num_attr"], str) and conf["select_num_attr"] != "",
              "必须配置 select_num_attr 取值为非空字符串")

  @strict_types
  def depend_on_items(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    conf = self._config
    attrs.update(self.extract_dynamic_params(conf["kconf_key"]))
    # 媒体
    media = conf.get("media_attr_name")
    if isinstance(media, str) and media != "":
      attrs.add(media)
    else:
      attrs.add("media")
    # 产品
    product = conf.get("product_attr_name")
    if isinstance(product, str) and product != "":
      attrs.add(product)
    else:
      attrs.add("product")
    # 新回活状态
    action = conf.get("action_attr_name")
    if isinstance(action, str) and action != "":
      attrs.add(action)
    else:
      attrs.add("device_status")
    # 安装态
    install_status = conf.get("install_status_attr_name")
    if isinstance(install_status, str) and install_status != "":
      attrs.add(install_status)
    else:
      attrs.add("fusion_install_status")
    return attrs


  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for strategy_reason in self._config["output_strategies"]:
      strategy_valid = "reason_%d_recall_valid" % (strategy_reason)
      recall_num_conf = "reason_%d_recall_num" % (strategy_reason)
      attrs.add(strategy_valid)
      attrs.add(recall_num_conf)
    attrs.add(self._config["reason_list_attr"])
    attrs.add(self._config["select_num_attr"])
    return attrs


class UgRtbRetrieveTermCombineV1Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ug_rtb_retrieve_term_combine_v1"
  
  @strict_types
  def _check_config(self) -> None:
    conf = self._config

    check_arg("strategy_terms" in conf, "必须配置 strategy_terms")
    strategy_terms = conf["strategy_terms"]
    check_arg(isinstance(strategy_terms, list) and len(strategy_terms) > 0, "strategy_terms 必须为 list[dict] 类型且非空")
    for strategy in strategy_terms:
      check_arg(isinstance(strategy, dict), "strategy 每个取值必须为dict")
      check_arg("combine_term" in strategy, "strategy_terms 中每一项配置必须包含 combine_term")
      combine_term = strategy["combine_term"]
      check_arg(isinstance(combine_term, str) and combine_term != "", "combine_term 必须为非空字符串")
      check_arg("single_term" in strategy, "strategy_terms 中每一项配置必须包含 single_term")
      single_term = strategy["single_term"]
      check_arg(isinstance(single_term, str) and single_term != "", "single_term 必须为非空字符串")


  @strict_types
  def depend_on_items(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    conf = self._config
    for strategy in conf["strategy_terms"]:
      attrs.update(self.extract_dynamic_params(strategy["single_term"]))
    # 媒体
    media = conf.get("media_attr_name")
    if isinstance(media, str) and media != "":
      attrs.add(media)
    else:
      attrs.add("retrieve_media")
    # 规格模版
    templates = conf.get("template_attr_name")
    if isinstance(templates, str) and templates != "":
      attrs.add(templates)
    else:
      attrs.add("recall_template_id")
    # 产品
    product = conf.get("product_attr_name")
    if isinstance(product, str) and product != "":
      attrs.add(product)
    else:
      attrs.add("retrieve_product_name")
    # 新回活状态
    action = conf.get("action_attr_name")
    if isinstance(action, str) and action != "":
      attrs.add(action)
    else:
      attrs.add("device_status")
    # 安装态
    install_status = conf.get("install_status_attr_name")
    if isinstance(install_status, str) and install_status != "":
      attrs.add(install_status)
    else:
      attrs.add("fusion_install_status")
    # 出价类型
    bid_types = conf.get("bid_types_attr_name")
    if isinstance(bid_types, str) and bid_types != "":
      attrs.add(bid_types)
    else:
      attrs.add("support_bid_types")
    # 索引实验包
    creative_package = conf.get("creative_package_attr_name")
    if isinstance(creative_package, str) and creative_package != "":
      attrs.add(creative_package)
    else:
      attrs.add("creative_package_list")
    # 广点通站点大类
    gdt_site = conf.get("gdt_site_attr_name")
    if isinstance(gdt_site, str) and gdt_site != "":
      attrs.add(gdt_site)
    # 审核状态
    audit_status = conf.get("audit_status_attr_name")
    if isinstance(audit_status, str) and audit_status != "":
      attrs.add(audit_status)

    return attrs


  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for strategy in self._config["strategy_terms"]:
      attrs.add(strategy["combine_term"])
    return attrs

class UgFillFlattenItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ug_fill_flatten_item_attrs"
  
  @strict_types
  def _check_config(self) -> None:
    conf = self._config

    check_arg("original_item_key_list" in conf and isinstance(conf["original_item_key_list"], str), "必须配置 advertiser_id_list 类型为字符串")
    check_arg("flatten_item_id_list" in conf and isinstance(conf["flatten_item_id_list"], str), "必须配置 creative_id_list 类型为字符串")
    check_arg("flatten_item_id_attr" in conf and isinstance(conf["flatten_item_id_attr"], str), "必须配置 aggregated_creative_id_attr 类型为字符串")
    check_arg("aggregated_flatten_item_id_list" in conf and isinstance(conf["aggregated_flatten_item_id_list"], str), "必须配置 creative_id_attr 类型为字符串")

    check_arg("fill_item_attrs" in conf and isinstance(conf["fill_item_attrs"], list), "必须配置 fill_item_attrs 类型为 list")
    for attr in conf["fill_item_attrs"]:
      check_arg(isinstance(attr, str), "fill_item_attrs 内元素必须为字符串")


  @strict_types
  def depend_on_items(self) -> bool:
    return True


  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    conf = self._config

    original_item_key_list = conf.get("original_item_key_list")
    if isinstance(original_item_key_list, str) and original_item_key_list != "":
      attrs.add(original_item_key_list)

    flatten_item_id_list = conf.get("flatten_item_id_list")
    if isinstance(flatten_item_id_list, str) and flatten_item_id_list != "":
      attrs.add(flatten_item_id_list)

    return attrs


  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    conf = self._config

    flatten_item_id_attr = conf.get("flatten_item_id_attr")
    if isinstance(flatten_item_id_attr, str) and flatten_item_id_attr != "":
      attrs.add(flatten_item_id_attr)

    aggregated_flatten_item_id_list = conf.get("aggregated_flatten_item_id_list")
    if isinstance(aggregated_flatten_item_id_list, str) and aggregated_flatten_item_id_list != "":
      attrs.add(aggregated_flatten_item_id_list)
    
    fill_item_attrs = conf.get("fill_item_attrs")
    if isinstance(fill_item_attrs, list) and len(fill_item_attrs) > 0 and isinstance(fill_item_attrs[0], str):
      attrs.update(fill_item_attrs)

    return attrs


  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    conf = self._config

    fill_item_attrs = conf.get("fill_item_attrs")
    if isinstance(fill_item_attrs, list) and len(fill_item_attrs) > 0 and isinstance(fill_item_attrs[0], str):
      attrs.update(fill_item_attrs)
    return attrs


class UgModifyAttrMutabilityEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ug_modify_attr_mutability"
  
  @strict_types
  def _check_config(self) -> None:
    conf = self._config

    check_arg("input_common_attrs" in conf or "input_item_attrs" in conf,
              "input_common_attrs 和 input_item_attrs 至少配置一个")

    check_arg("is_mutable" in conf and isinstance(conf["is_mutable"], bool),
              "is_mutable 必须配置为布尔类型")

    if "input_common_attrs" in conf:
      input_common_attrs = conf["input_common_attrs"]
      check_arg(isinstance(input_common_attrs, list), "input_common_attrs 必须为 string list")
      for attr in input_common_attrs:
        check_arg(isinstance(attr, str) and attr != "", "input_common_attrs 内元素必须为非空字符串")

    if "input_item_attrs" in conf:
      input_item_attrs = conf["input_item_attrs"]
      check_arg(isinstance(input_item_attrs, list), "input_item_attrs 必须为 string list")
      for attr in input_item_attrs:
        check_arg(isinstance(attr, str) and attr != "", "input_item_attrs 内元素必须为非空字符串")


  @strict_types
  def depend_on_items(self) -> bool:
    return True


  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    conf = self._config
    
    input_common_attrs = conf.get("input_common_attrs")
    if isinstance(input_common_attrs, list) and len(input_common_attrs) > 0 and \
                  isinstance(input_common_attrs[0], str):
      attrs.update(input_common_attrs)

    return attrs


  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    conf = self._config
    
    input_item_attrs = conf.get("input_item_attrs")
    if isinstance(input_item_attrs, list) and len(input_item_attrs) > 0 and \
                  isinstance(input_item_attrs[0], str):
      attrs.update(input_item_attrs)

    return attrs


  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    conf = self._config
    
    input_item_attrs = conf.get("input_item_attrs")
    if isinstance(input_item_attrs, list) and len(input_item_attrs) > 0 and \
                  isinstance(input_item_attrs[0], str):
      attrs.update(input_item_attrs)

    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    conf = self._config
    
    input_common_attrs = conf.get("input_common_attrs")
    if isinstance(input_common_attrs, list) and len(input_common_attrs) > 0 and \
                  isinstance(input_common_attrs[0], str):
      attrs.update(input_common_attrs)

    return attrs


class AttrMappingEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_attr_by_mapping"
  
  @strict_types
  def _check_config(self) -> None:
    conf = self._config

    check_arg("mappings" in conf and isinstance(conf["mappings"], dict) \
              and conf["mappings"] != '', "mappings 必须为dict")

    check_arg("configs" in conf and isinstance(conf["configs"], list),
              "configs 必须为 list")
    configs = conf["configs"]

    if "is_common" in conf:
        check_arg(isinstance(conf["is_common"], bool), "is_common 必须为 bool 类型")

    for attr in configs:
      check_arg(isinstance(attr, dict), "configs 内元素必须为 dict")
      check_arg("input" in attr and isinstance(attr["input"], str) and attr["input"] != "",
                "configs[i]['input'] 必须为非空字符串")
      check_arg("output" in attr and isinstance(attr["output"], str) and attr["output"] != "",
                "configs[i]['output'] 必须为非空字符串")
      if "mode" in attr:
        check_arg(isinstance(attr["mode"], int),
                "configs[i]['mode'] 必须为int类型")
      if "to_int" in attr:
        check_arg(isinstance(attr["to_int"], bool),
                "configs[i]['to_int'] 必须为布尔类型")


  @strict_types
  def depend_on_items(self) -> bool:
    conf = self._config
    is_common = True
    if "is_common" in conf and isinstance(conf["is_common"], bool):
      is_common = conf["is_common"]
    
    return not is_common


  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    conf = self._config
    
    is_common = True
    if "is_common" in conf and isinstance(conf["is_common"], bool):
      is_common = conf["is_common"]

    if is_common:
      configs = conf["configs"]
      for attr in configs:
          attrs.add(attr["input"])

    return attrs


  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    conf = self._config
    
    is_common = True
    if "is_common" in conf and isinstance(conf["is_common"], bool):
      is_common = conf["is_common"]

    if not is_common:
      configs = conf["configs"]
      for attr in configs:
        attrs.add(attr["input"])

    return attrs


  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    conf = self._config
    
    is_common = True
    if "is_common" in conf and isinstance(conf["is_common"], bool):
      is_common = conf["is_common"]

    if not is_common:
      configs = conf["configs"]
      for attr in configs:
        attrs.add(attr["output"])

    return attrs


  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    conf = self._config
    
    is_common = True
    if "is_common" in conf and isinstance(conf["is_common"], bool):
      is_common = conf["is_common"]

    if is_common:
      configs = conf["configs"]
      for attr in configs:
        attrs.add(attr["output"])

    return attrs


class DidUidResolverEnricher(LeafEnricher):
  
  class IdMappingBehavior:
    """ID映射行为常量定义"""
    DID_TO_UID = 1                    # DID → UID
    DID_TO_UID_WITH_ACTIVITY = 2      # DID → UID + 活跃时间
    UID_TO_DID = 3                    # UID → DID      
    UID_TO_DID_WITH_ACTIVITY = 4      # UID → DID + 活跃时间
    
    @classmethod
    def is_valid(cls, behavior):
      """检查 behavior 是否有效"""
      return behavior in [cls.DID_TO_UID, cls.DID_TO_UID_WITH_ACTIVITY, 
                         cls.UID_TO_DID, cls.UID_TO_DID_WITH_ACTIVITY]
    
    @classmethod
    def requires_activity_output(cls, behavior):
      """检查是否需要输出活跃时间"""
      return behavior in [cls.DID_TO_UID_WITH_ACTIVITY, cls.UID_TO_DID_WITH_ACTIVITY]

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "did_uid_resolver"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("input_id_attr_name", ""))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_id_attr_name", ""))
    
    # 如果需要输出活跃时间
    behavior = self._config.get("behavior", 0)
    if self.IdMappingBehavior.requires_activity_output(behavior):
      # 用户指定了这个参数的值且不为空
      output_active_ts_attr = self._config.get("output_active_ts_attr", "")
      if output_active_ts_attr:
        attrs.add(output_active_ts_attr)
    
    # 解析状态码输出
    attrs.add(self._config.get("resolve_status_attr"))
    
    return attrs

  @strict_types
  def _check_config(self) -> None:
    # 检查必填参数
    behavior = self._config.get("behavior", 0)
    check_arg(self.IdMappingBehavior.is_valid(behavior), 
              f"behavior 必须是 {self.IdMappingBehavior.DID_TO_UID}(DID_TO_UID), "
              f"{self.IdMappingBehavior.DID_TO_UID_WITH_ACTIVITY}(DID_TO_UID_WITH_ACTIVITY), "
              f"{self.IdMappingBehavior.UID_TO_DID}(UID_TO_DID), "
              f"{self.IdMappingBehavior.UID_TO_DID_WITH_ACTIVITY}(UID_TO_DID_WITH_ACTIVITY)")
    
    check_arg(len(self._config.get("input_id_attr_name", "")) > 0, 
              "input_id_attr_name 为必配项")
    
    check_arg(len(self._config.get("request_src", "")) > 0, 
              "request_src 为必配项")
    
    check_arg(len(self._config.get("output_id_attr_name", "")) > 0, 
              "output_id_attr_name 为必配项")
    
    check_arg(len(self._config.get("resolve_status_attr", "")) > 0, 
              "resolve_status_attr 为必配项")

    # 检查参数类型
    check_arg(isinstance(self._config.get("input_id_attr_name", ""), str), 
              "input_id_attr_name 需为 string")
    
    check_arg(isinstance(self._config.get("request_src", ""), str), 
              "request_src 需为 string")
    
    check_arg(isinstance(self._config.get("output_id_attr_name", ""), str), 
              "output_id_attr_name 需为 string")
    
    check_arg(isinstance(self._config.get("resolve_status_attr", ""), str), 
              "resolve_status_attr 需为 string")

    # 检查可选参数类型
    if "product" in self._config:
      check_arg(isinstance(self._config.get("product", ""), int), 
                "product 需为 int")
      
    if "kpn_product" in self._config:
      check_arg(isinstance(self._config.get("kpn_product", ""), str), 
                "kpn_product 需为 string")

    if self.IdMappingBehavior.requires_activity_output(behavior):
      check_arg(
        len(self._config.get("output_active_ts_attr", "")) > 0,
        f"behavior={self.IdMappingBehavior.DID_TO_UID_WITH_ACTIVITY}/"
        f"{self.IdMappingBehavior.UID_TO_DID_WITH_ACTIVITY} 时 output_active_ts_attr 为必配项"
      )
      check_arg(
        isinstance(self._config.get("output_active_ts_attr", ""), str),
        "output_active_ts_attr 需为 string(属性名)，其值类型为 uint64"
      )

    if "timeout_ms" in self._config:
      check_arg(isinstance(self._config.get("timeout_ms", 10), int), 
                 "timeout_ms 需为 int")
    
    if "cluster_name" in self._config:
      check_arg(isinstance(self._config.get("cluster_name", ""), str), 
                 "cluster_name 需为 string")

  @classmethod
  @strict_types
  def is_async(cls) -> bool:
    return True
