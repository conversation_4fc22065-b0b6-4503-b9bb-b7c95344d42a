#!/usr/bin/env python3
# coding=utf-8
"""
filename: push_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder,push api mixin
author: <EMAIL>
date: 2020-06-11 13:54:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .push_enricher import *
from .push_arranger import *

class PushApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 push 相关的 Processor 接口
  - CommonRecoPushPredictEnricher
  """

  def get_push_xtr_by_predict(self, **kwargs):
    """
    CommonRecoPushPredictEnricher
    ------
    请求 ranking_predict_service_photo 或者 ranking_predict_service_live 来获取 xtr 填入到 item 中

    参数配置
    ------

    `output_prefix`: [string] 保存 xtr 的前缀

    调用事例
    ``` python
    .get_push_xtr_by_predict(
      output_prefix = "push_predict")
    ```
    """
    self._add_processor(CommonRecoPushPredictEnricher(kwargs))
    return self
  
  def fix_model_pctr_by_event_type(self, **kwargs):
    """
    RecoPushFixPctrEnricher
    ------
    各个event type 保序回归

    参数配置
    ------

    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表

    `input_item_attrs`: [list] 必配项，string list, 本 processor 输入的 item_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    调用示例
    --------
    ``` python
    .fix_model_pctr_by_event_type(
      input_common_attrs=[
        "hourOfDay",
      ],
      input_item_attrs=[
        "event_type",
        "mix_ctr",
      ],  
      output_item_attrs=[
        "fixed_pctr"
      ]
    )
    ```
    """ 
    self._add_processor(RecoPushFixPctrEnricher(kwargs))
    return self

  def fix_model_pctr_v2(self, **kwargs):
    """
    RecoPushFixPctrV2Enricher
    ------
    模型预估的 pctr 纠偏

    参数配置
    ------

    `hourOfDay`: [int] 必配项，发送时间，纠偏特征

    `event_type`: [string] 必配项，发送类型，纠偏特征

    `mix_ctr`: [double] 必配项，模型预估的 pctr， 纠偏值

    `redis_cluster_name`: [string] 必配项，存储纠偏值的 redis 集群

    `timeout_ms`: [int] 选配项，读 redis 的超时时间，默认 10 ms

    `redis_key_prefix`: [string] 选配项，读取 redis 的 key 前缀

    `output_item_attr`: [string] 必配项，新增一个item attr字段，用于保存修正后的pctr

    调用示例
    --------
    ``` python
    .fix_model_pctr_by_event_type(
      hourOfDay="hourOfDay",
      event_type="event_type",
      mix_ctr="mix_ctr",
      output_item_attr="fixed_pctr",
      redis_cluster_name="redis_cluster_name"
    )
    ```
    """
    self._add_processor(RecoPushFixPctrV2Enricher(kwargs))
    return self

  def fix_model_pctr_v5(self, **kwargs):
    """
    RecoPushFixPctrV5Enricher
    ------
    模型预估的 pctr 纠偏(5个特征)

    参数配置
    ------

    `hourOfDay`: [int] 必配项，发送时间，纠偏特征

    `uProvince`: [int] 必配项，省份，纠偏特征 

    `event_type`: [string] 必配项，发送类型，纠偏特征

    `app`: [string] 必配项，发送 app，纠偏特征 

    `infra_terminal`: [string] 必配项，发送机型，纠偏特征

    `mix_ctr`: [double] 必配项，模型预估的 pctr， 纠偏值

    `redis_cluster_name`: [string] 必配项，存储纠偏值的 redis 集群

    `timeout_ms`: [int] 选配项，读 redis 的超时时间，默认 10 ms

    `redis_key_prefix`: [string] 选配项，读取 redis 的 key 前缀

    `output_item_attr`: [string] 必配项，新增一个item attr字段，用于保存修正后的pctr

    调用示例
    --------
    ``` python
    .fix_model_pctr_by_event_type(
      hourOfDay="hourOfDay",
      uProvince="uProvince",
      app="app",
      infra_terminal="infra_terminal",
      event_type="event_type",
      mix_ctr="mix_ctr",
      output_item_attr="fixed_pctr",
      redis_cluster_name="redis_cluster_name"
    )
    ```
    """
    self._add_processor(RecoPushFixPctrV5Enricher(kwargs))
    return self

  def get_push_photo_info_attrs(self, **kwargs):
    """
    RecoPushPhotoInfoAttrEnricher
    ------
    获取push photo侧特征

    参数配置
    ------

    `photo_info_attrs`: [list] 必配项，指定需要的attr，attr 名称必须与 reco_push_photo.proto 中 field name 一致

    调用示例
    --------
    ``` python
    .get_push_photo_info_attrs(
      photo_info_attrs=["aaa", "bbb"]
    )
    ```
    """
    self._add_processor(RecoPushPhotoInfoAttrEnricher(kwargs))
    return self

  def select_key_and_value(self, **kwargs):
    """
    RecoPushSelectKeyAndValueAttrEnricher
    ------
    根据某种方法，选取 key 和 value，支持 max/min/random

    参数配置
    ------

    `key_list_attr`: [list] 必填项 key_list attr名。
    `value_list_attr`: [list] 必填项 value_list attr名。
    `output_key_attr`: [string] 必填项 输出key的attr名。
    `output_value_attr`: [string] 必填项 输出value的attr名。
    `method`: [string] 选填项 支持 max/min/random，默认为random。

    调用示例
    --------
    ``` python
    .select_key_and_value(
      key_list_attr = [1, 2, 3, 5],
      value_list_attr = [0.1, 0.2, 0.3, 0.5],
      output_key_attr = "relation",
      output_value_attr = "relation_score",
      method = "max"
    )
    ```
    """
    self._add_processor(RecoPushSelectKeyAndValueAttrEnricher(kwargs))
    return self

  def parse_json_str_by_list(self, **kwargs):
    """
    RecoPushParseJsonStrByListAttrEnricher
    ------
    解析 json 字符串，输出列表中每个 key 对应的 value

    参数配置
    ------

    `json_attr`: [string] 必填项 待解析 json 字符串。
    `key_list_attr`: [list] 必填项 限定输出 output_key_attr 值在 key_list_attr 中。
    `output_attr`: [string] 必填项 输出 attr 名。
    `default_value`: [double] 必填项。暂时只支持 double 类型。

    调用示例
    --------
    ``` python
    .parse_json_str_by_list(
      json_attr = "relation_score_json",
      key_list_attr = "relation_list",
      output_attr = "relation_score_list",
      default_value = 0.0
    )
    ```
    """
    self._add_processor(RecoPushParseJsonStrByListAttrEnricher(kwargs))
    return self

  def enrich_json_by_item_attr(self, **kwargs):
    """
    RecoPushEnrichJsonByItemAttrEnricher
    ------
    将指定的item_attr分别作为 key 和 value，构建 json string 格式的common attr

    参数配置
    ------

    `save_json_to`: [string] 必填项 以 json string 存入的 common attr。
    `key_attr`: [string] 必填项 作为 key 的 item attr。
    `value_attr`: [string] 必填项 作为 value 的 item attr。

    调用示例
    --------
    ``` python
    .enrich_json_by_item_attr(
      save_json_to = "relation_score_json",
      key_attr = "userItemrelation",
      value_attr = "user_item_relation_score"
    )
    ```
    """
    self._add_processor(RecoPushEnrichJsonByItemAttrEnricher(kwargs))
    return self

  def dense_match(self, **kwargs):
    """
    DenseMatchEnricher
    ------
    Dense特征匹配, 匹配到的 scores 会进行 floor 处理

    参数配置
    ------
    `to_match_attr`: [int] 用于匹配时key值比较的 item attr name
    `match_configs` : [list]
      - `keys` : [int_list] 待匹配的key值list item attr name
      - `scores` : [double_list] 待匹配的score值list item attr name
      - `output` : [double] 存入匹配结果 item attr name

    调用示例
    ------
    ``` python
    ```
    """
    self._add_processor(DenseMatchEnricher(kwargs))
    return self

  def dense_match_v2(self, **kwargs):
    """
    DenseMatchV2Enricher
    ------
    Dense特征匹配, 和 v1 的区别是 to_match_attr 使用 common_attr

    参数配置
    ------
    `to_match_attr`: [int] 用于匹配时key值比较的 common attr name
    `match_configs` : [list]
      - `keys` : [int_list] 待匹配的key值list common attr name
      - `scores` : [double_list] 待匹配的score值list common attr name
      - `output` : [double] 存入匹配结果 item attr name

    调用示例
    ------
    ``` python
    ```
    """
    self._add_processor(DenseMatchV2Enricher(kwargs))
    return self

  def dense_match_v3(self, **kwargs):
    """
    DenseMatchV3Enricher
    ------
    Dense特征匹配, 和 v1 的区别是匹配到的 scores 不会进行 floor 处理

    参数配置
    ------
    `to_match_attr`: [int] 用于匹配时key值比较的 item attr name
    `match_configs` : [list]
      - `keys` : [int_list] 待匹配的key值list item attr name
      - `scores` : [double_list] 待匹配的score值list item attr name
      - `output` : [double] 存入匹配结果 item attr name

    调用示例
    ------
    ``` python
    ```
    """
    self._add_processor(DenseMatchV3Enricher(kwargs))
    return self
  
  def keep_order_and_quantity(self, **kwargs):
    """
    RecoPushKeepOrderQuantityArranger
    ------
    从当前结果获取得到 total_num 个item，其中 total_num * keep_topk_ratio 个item顺序保持不变。

    参数配置
    ------
    ------
    `total_num`: [int] [动态参数] 总 item 数目
    `keep_topk_ratio`: [double] [动态参数] 保持顺序不变的 item 比例
    `attr_name`: [string] [动态参数] 用于分组截断的 item_attr 名称
    `queue`: [string] [动态参数] 每个 attr_value 的截断逻辑
    调用示例
    ------
    ``` python
    .keep_order_and_quantity(
      total_num=500,
      keep_topk_ratio=0.7,
      attr_name="event_type",
      queue="{\"EVENT_TOP_PHOTO\": 5, \"EVENT_MIX_HOT_NEWS_PUSH\": 3, \"EVENT_FOLLOW_UPLOAD_PHOTO\": 20}"
    )
    ```
    """
    self._add_processor(RecoPushKeepOrderQuantityArranger(kwargs))
    return self

  def modify_raw_sample_package_label(self, **kwargs):
    """
    RawSamplePackageLabelEnricher
    -----
    根据配置 修改 rsp 里面的 label, ⚠️注意是「原地修改」！

    参数配置
    ------
    `rsp_pointer_common_attr`: [string] [必填] rsp 在 common_attr 中的名字，存储类型 是 PB Message

    `need_clear_all_label_before_modify`: [bool] [选填] 是否在执行前 清空 rsp 里面的所有 label， 默认 true

    `pid_attr_name`: [string] [必填] rsp 里面 pid 的属性名字， 类型需要是 int 或 int_list[0]

    `label_config`: [list of dict] [必填] 需要修改 label的信息， 目前仅支持 ADD LABEL 操作

      - `label_name`: [string]  [必填] 添加 label 的名字

      - `modify_pid_list_common_attr`: [int_list]  [必填] common_attr 中读取 对哪些 pid 进行 label 修改
      
      - `label_value_list_common_attr`: [int_list]  [必填] common_attr 中读取 pid 对应的 label 值，需要与 `modify_pid_list_common_attr` 长度一致 

    调用示例
    ------
    ``` python
    .modify_raw_sample_package_label(
      need_clear_all_label_before_modify=True,
      rsp_pointer_common_attr="rsp",
      pid_attr_name="pid",
      label_config=[
        dict(
          label_name="labelA",
          modify_pid_list_common_attr="p1",
          label_value_list_common_attr="l1"
        )
      ]
    )
    ```
    """
    self._add_processor(RawSamplePackageLabelEnricher(kwargs))
    return self
  
  def push_truncate_by_attr(self, **kwargs):
    """
    RecoPushTruncateByAttrArranger
    ------
    对结果集大小按指定 item_attr 进行比例缩减，支持保量和打压

    参数配置
    ------
    ------
    `attr_name`: [int] 必填项 用于分组截断的 item_attr 名称

    `size_limit`: [int] [动态参数] 必填项 截断后的 item 总数，默认值为当前item总数
    
    `min_survival_ratio`: [double] [动态参数] 全部 attr_value 保存最少数量占比
    
    `attr_truncate_config`: [string] [动态参数] 每种 attr_value 的截断配置

    调用示例
    ------
    ``` python
    .push_truncate_by_attr(
      attr_name="event_type",
      size_limit=5000,
      min_survival_ratio=0.2,
      attr_truncate_config="{\"EVENT_TOP_PHOTO\": 0.2, \"EVENT_MIX_HOT_NEWS_PUSH\": 0.3, \"EVENT_FOLLOW_UPLOAD_PHOTO\": 0.5}"
    )
    ```
    """
    self._add_processor(RecoPushTruncateByAttrArranger(kwargs))
    return self

  def hash_get_all_from_redis(self, **kwargs):
    """
    RecoPushHashGetAllEnricher
    ------
    redis 的 hgetall 命令，将 fields 和 values 按顺序存到两个 common attr 中

    参数配置
    ------
    ------
    `cluster_name`: [string] 必填项 redis 集群名

    `timeout_ms`: [int] 超时时间（ms）默认10
    
    `key`: [string] [动态参数] redis key，必须是hash类型
    
    `field_attr`: [string] 存储 field list 的 common attr name
    
    `value_attr`: [string] 存储 value list 的 common attr name

    调用示例
    ------
    ``` python
    .hash_get_all_from_redis(
      cluster_name="push",
      key="redis_jkey"
      field_attr="field_list",
      value_attr="value_list"
    )
    ```
    """
    self._add_processor(RecoPushHashGetAllEnricher(kwargs))
    return self


  def hash_get_attr_from_redis(self, **kwargs):
    """
    RecoPushHashGetAttrFromRedisEnricher
    ------
    redis 的 hget 命令

    参数配置
    ------
    ------
    `cluster_name`: [string] 必填项 redis 集群名

    `timeout_ms`: [int] 超时时间（ms）默认10
    
    `key`: [string] [动态参数] redis key，必须是hash类型
    
    `field_attr`: [string] field name
    
    `value_attr`: [string] 存储 value 的 common attr

    调用示例
    ------
    ``` python
    .hash_get_attr_from_redis(
      cluster_name="push",
      key="redis_jkey"
      field_attr="field_name",
      value_attr="value"
    )
    ```
    """
    self._add_processor(RecoPushHashGetAttrEnricher(kwargs))
    return self



  def reco_push_null_item_enricher(self, **kwargs):
    """
    RecoPushNullItemEnricher
    ------
    判断item attr是否为空

    参数配置
    ------
    ------
    `mappings`: [list] 需要判断的 item attr 和输出结果的 item attr
      - `check_attr_name`: [string] 待检查的 ItemAttr 名称
      - `output_attr_name`: [string] 输出结果 ItemAttr 名称

    调用示例
    ------
    ``` python
    .reco_push_null_item_enricher(
      mappings = [{
        "check_attr_name": "device_id",
        "output_attr_name": "device_id_empty",
      }]
    )
    ```
    """
    self._add_processor(RecoPushNullItemEnricher(kwargs))
    return self


  def reco_push_parse_timing_seq_feature_enricher(self, **kwargs):
    """
    RecoPushParseTimingSeqFeatureEnricher
    ------
    处理强化学习模型item特征

    参数配置
    ------
    ------

    调用示例
    ------
    ``` python
    .reco_push_parse_timing_seq_feature_enricher()
    ```
    """
    self._add_processor(RecoPushParseTimingSeqFeatureEnricher(kwargs))
    return self

  def push_channel_sort(self, **kwargs):
    """
    PushChannelSortArranger
    多通道排序
    参数配置
    ------
      - `channel_queue_names`: [string list][动态参数] 实际队列的名字

      - `force_last_queue_name`: [string][动态参数] 默认队列的名字,会尽量补齐output_count
    
      - `input_count_threshold`: [int][动态参数] 进行多通道排序最小输入 Item 数量
    
      - `output_count`: [int][动态参数] 进行多通道排序输出 Item 数量
      
      - `queue_weight_attrs`: [string list] 表示各队列的权重的 Common Attr 名字
      
      - `queue_score_attrs`: [string list] 表示各队列的分数的 Item Attr 名字
      
      - `queue_flag_attrs`: [string list] 标志是否属于某队列的 Item Attr 名字
      
      - `enable_dryrun_mode`: [bool] 标志是否进行粗排精排重复率的衡量
      
      - `dryrun_mode_result_attr`: [string] dryrun 模式结果保存的 Item Attr 名字

    ------
    ``` python
    .push_channel_sort(
      channel_queue_names="{{mc_channel_queue_names}}",
      force_last_queue_name="main_queue",
      input_count_threshold=200,
      output_count=200,
      queue_weight_attrs=["mc_csqw_main_queue, "mc_csqw_cold_start"],
      queue_score_attrs=["mc_csqs_main_queue", "mc_csqs_cold_start"],
      queue_flag_attrs=["mc_csqf_main_queue", "mc_csqf_cold_start"],
    )
    ```
    """
    self._add_processor(PushChannelSortArranger(kwargs))
    return self

  def push_colossusv2_item_enricher(self, **kwargs):
    """
    PushColossusV2ItemEnricher
    item 维度读取 colossus
    参数配置
    ------
      - `client_kconf`: [string] 同 gsu_colossusv2_batch_enricher

      - `field_names`: [string list] 读取的特征列
    
      - `output_attrs`: [string list] 输出的item attr
    
      - `query`: [string] 同 gsu_colossusv2_batch_enricher
      
      - `filter`: [string] 同 gsu_colossusv2_batch_enricher
      
      - `filter_future_items`: [boolean] 同 gsu_colossusv2_batch_enricher
      
      - `item_key_attr`: [string] 存储 query key 的 item attr
      
      - `debug_print_item_num`: [int] 同 gsu_colossusv2_batch_enricher

    ------
    ``` python
    .push_channel_sort(
      channel_queue_names="{{mc_channel_queue_names}}",
      force_last_queue_name="main_queue",
      input_count_threshold=200,
      output_count=200,
      queue_weight_attrs=["mc_csqw_main_queue, "mc_csqw_cold_start"],
      queue_score_attrs=["mc_csqs_main_queue", "mc_csqs_cold_start"],
      queue_flag_attrs=["mc_csqf_main_queue", "mc_csqf_cold_start"],
    )
    ```
    """
    self._add_processor(PushColossusV2ItemEnricher(kwargs))
    return self

  def push_parse_push_type_enricher(self, **kwargs):
    """
    PushParsePushTypeEnricher
    用于做 event_type 到 push_type 的转换
    读取 `item_type` `type_attr` `recall_reason` `userItemRelation` `author_id_attr` `follow_friend_list` 生成 `push_type_attr`
    参数配置
    ------
      - `kconf_path`: [string] 读取的 kconf 链接，一般不用配置
    ------
    ``` python
    .push_parse_push_type_enricher()
    ```
    """
    self._add_processor(PushParsePushTypeEnricher(kwargs))
    return self
