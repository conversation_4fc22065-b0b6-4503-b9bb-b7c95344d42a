#!/usr/bin/env python3
# coding=utf-8
"""
filename: explore_api_mixin.py
description:
author: h<PERSON><PERSON><PERSON>@kuaishou.com
date: 2021-12-10 18:00:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .explore_arranger import *
from .explore_enricher import *
from .explore_retriever import *

class ExploreApiMixin(CommonLeafBaseMixin):
  """
  双列发现页内外流 Processor API 接口的 Mixin 实现
  """

  def explore_control_hetu_count_enricher(self, **kwargs):
    """
    ExploreControlHetuCountEnricher
    """
    self._add_processor(ExploreControlHetuCountEnricher(kwargs))
    return self

  def explore_photo_distribution_adjust_enricher(self, **kwargs):
    """
    ExplorePhotoDistributionAdjustEnricher
    """
    self._add_processor(ExplorePhotoDistributionAdjustEnricher(kwargs))
    return self

  def explore_photo_distribution_colossus_stat_enricher(self, **kwargs):
    """
    ExplorePhotoDistributionColossusStatEnricher
    """
    self._add_processor(ExplorePhotoDistributionColossusStatEnricher(kwargs))
    return self

  def explore_colossus_top_svtr_hetu_enricher(self, **kwargs):
    """
    ExploreColossusTopSvtrHetuEnricher
    """
    self._add_processor(ExploreColossusTopSvtrHetuEnricher(kwargs))
    return self

  def explore_mix_user_interest_stat_enricher(self, **kwargs):
    """
    ExploreMixUserInterestStatEnricher
    """
    self._add_processor(ExploreMixUserInterestStatEnricher(kwargs))
    return self

  def explore_xtr_debias_v3_enricher(self, **kwargs):
    """
    ExploreXtrDebiasV3Enricher
    """
    self._add_processor(ExploreXtrDebiasV3Enricher(kwargs))
    return self

  def explore_gen_user_top_wt_pids(self, **kwargs):
    """
    ExploreUserTopWtPidsEnricher
    """
    self._add_processor(ExploreUserTopWtPidsEnricher(kwargs))
    return self

  def calc_long_term_interest_ee_score(self, **kwargs):
    """
    CalcLongTermInterstEEScoreEnricher
    """
    self._add_processor(CalcLongTermInterstEEScoreEnricher(kwargs))
    return self

  def gen_realtime_browse_set(self, **kwargs):
    """
    GenRealtimeBrowseSetEnricher
    """
    self._add_processor(GenRealtimeBrowseSetEnricher(kwargs))
    return self

  def explore_uninterest_tag_exit(self, **kwargs):
    """
    ExploreUninterestTagExitEnricher
    """
    self._add_processor(ExploreUninterestTagExitEnricher(kwargs))
    return self

  def explore_realshow_emctr_unbias(self, **kwargs):
    """
    ExploreRealshowEmctrUnbiasEnricher
    """
    self._add_processor(ExploreRealshowEmctrUnbiasEnricher(kwargs))
    return self

  def explore_short_uninterest_tagger(self, **kwargs):
    """
    ExploreShortUninterestTaggerEnricher
    """
    self._add_processor(ExploreShortUninterestTaggerEnricher(kwargs))
    return self

  def explore_gamora_fountain_interest_tagger(self, **kwargs):
    """
    ExploreGamoraFountainInterestTaggerEnricher
    """
    self._add_processor(ExploreGamoraFountainInterestTaggerEnricher(kwargs))
    return self

  def explore_cluster_by_custom_rule(self, **kwargs):
    """
    ExploreCustomRuleClusterEnricher
    ------

    参数配置
    ------
    """
    self._add_processor(ExploreCustomRuleClusterEnricher(kwargs))
    return self

  def explore_cluster_variant_sort(self, **kwargs):
    """
    ExploreClusterVariantSortArranger
    ------

    参数配置
    ------
    """
    self._add_processor(ExploreClusterVariantSortArranger(kwargs))
    return self

  def explore_cluster_variant_sort_v2(self, **kwargs):
    """
    ExploreClusterVariantSortV2Arranger
    ------

    参数配置
    ------
    """
    self._add_processor(ExploreClusterVariantSortV2Arranger(kwargs))
    return self

  def explore_user_feedback_issue_enrich(self, **kwargs):
    """
    ExploreUserFeedbackIssueEnricher
    ------

    参数配置
    ------
    """
    self._add_processor(ExploreUserFeedbackIssueEnricher(kwargs))
    return self

  def explore_cluster_variant_sort_v2_enrich(self, **kwargs):
    """
    ExploreClusterVariantSortV2Enricher
    ------

    参数配置
    ------
    """
    self._add_processor(ExploreClusterVariantSortV2Enricher(kwargs))
    return self

  def explore_min_act_rank_score_enricher(self, **kwargs):
    """
    ExploreMinActRankScoreEnricher
    ------

    参数配置
    ------
    """
    self._add_processor(ExploreMinActRankScoreEnricher(kwargs))
    return self

  def explore_memory_data_enrich(self, **kwargs):
    self.memory_data_enrich(**kwargs)
    return self

  def explore_retrieve_by_knowledge_mid_photo(self, **kwargs):
    """
    ExploreKnowledgeMidPhotoRetriever
    ------
    定制召回

    参数配置
    ------
    """
    self._add_processor(ExploreKnowledgeMidPhotoRetriever(kwargs))
    return self

  def explore_mc_distill_sample_enrich(self, **kwargs):
    """
    ExploreMcDistillSampleEnricher
    ------
    级联模型样本采样

    参数配置
    ------
    `candidate_list_attr`: [string] 保存采样候选集 item_key 的 common attr

    `excludes_attr`: [string] [选配] 对该 attr 中的 item_key 不进行采样

    `sample_num`: [int] [动态参数] 采样个数

    `chunk_size`: [int] [动态参数] chunk采样模式间隔

    `sample_type`: [int] [动态参数] 采样模式

    `save_sample_result_to`: [string] 输出的 sample result attr 名称

    `enable_fill_if_result_not_enough`: [bool] 数量不足的情况下允许填充结果，默认为 false

    """
    self._add_processor(ExploreMcDistillSampleEnricher(kwargs))
    return self

  def explore_full_link_context_sample_reco_log_enricher(self, **kwargs):
    """
    ExploreFullLinkContextSampleRecoLogEnricher
    ------
    级联模型样本采样，填充 RecoLog 格式样本

    参数配置
    ------
    `user_info_attr`: [string] user_info 指针的 attr

    `save_result_to`： [string] 保存最终样本（recoLog 格式）指针的 common attr
    """
    self._add_processor(ExploreFullLinkContextSampleRecoLogEnricher(kwargs))
    return self

  def explore_full_link_distill_sample_reco_log_enricher(self, **kwargs):
    """
    ExploreMcDistillSampleRecoLogEnricher
    ------
    级联模型样本采样，填充 RecoLog 格式样本

    参数配置
    ------
    `sample_list_names`: [string_list] 保存采样 photo_id 的 common attr 列表

    `user_info_attr`: [string] user_info 指针的 attr

    `photo_info_attr`: [string] photo_info 指针的 attr

    `send_optional_user`: [int] [动态参数]

    `send_optional_photo`: [int] [动态参数]

    `save_result_to`： [string] 保存最终样本（recoLog 格式）指针的 common attr
    """
    self._add_processor(ExploreFullLinkDistillSampleRecoLogEnricher(kwargs))
    return self

  def explore_mc_distill_sample_reco_log_enrich(self, **kwargs):
    """
    ExploreMcDistillSampleRecoLogEnricher
    ------
    级联模型样本采样，填充 RecoLog 格式样本

    参数配置
    ------
    `sample_list_names`: [string_list] 保存采样 photo_id 的 common attr 列表

    `user_info_attr`: [string] user_info 指针的 attr

    `photo_info_attr`: [string] photo_info 指针的 attr

    `send_optional_user`: [int] [动态参数]

    `send_optional_photo`: [int] [动态参数]

    `save_result_to`： [string] 保存最终样本（recoLog 格式）指针的 common attr

    `attr_map`: [dict] [选配] recoLog 中字段对应的 item_attr name，如果不配置则使用默认值：
      {
        {"empirical_ctr", "empirical_ctr"},
        {"empirical_ltr", "empirical_ltr"},
        {"empirical_wtr", "empirical_wtr"},
        {"empirical_ftr", "empirical_ftr"},
        {"empirical_lvtr", "empirical_lvtr"},
        {"empirical_svtr", "empirical_svtr"},
        {"empirical_htr", "empirical_htr"},
        {"empirical_ptr", "empirical_ptr"},
        {"empirical_cmtr", "empirical_cmtr"},
        {"cascade_pctr", "cascade_pctr"},
        {"cascade_pltr", "cascade_pltr"},
        {"cascade_pwtr", "cascade_pwtr"},
        {"cascade_pftr", "cascade_pftr"},
        {"cascade_pptr", "cascade_ptr"},
        {"cascade_phtr", "cascade_phtr"},
        {"cascade_pwt", "cascade_pwatch_time"},
        {"pctr", "pctr"},
        {"pltr", "pltr"},
        {"pwtr", "pwtr"},
        {"pftr", "pftr"},
        {"phtr", "phtr"},
        {"plvtr", "plvtr"},
        {"psvr", "psvtr"},
        {"pptr", "ptr"},
        {"pcmtr", "pcmtr"},
        {"pevr", "pevr"},
        {"pdctr", "pdctr"},
        {"ptagctr", "ptagctr"},
        {"ppeef", "ppeef"},
        {"pfvtr", "pfvtr"},
        {"pfr_score1", "fr_score1"},
        {"pfr_score2", "fr_score2"},
        {"photo_id", "photo_id"},
        {"exptag", "reason"},
        {"label_type", "mc_distill_sample_label_type"},
        {"real_show_index", "rank_index"}
      }

    """
    self._add_processor(ExploreMcDistillSampleRecoLogEnricher(kwargs))
    return self

  def explore_cascade_debias_enricher(self, **kwargs):
    """
    ExploreCascadeDebiasEnricher
    """
    self._add_processor(ExploreCascadeDebiasEnricher(kwargs))
    return self

  def explore_information_related_score_enricher(self, **kwargs):
    """
    ExploreInformationRelatedScoreEnricher
    """
    self._add_processor(ExploreInformationRelatedScoreEnricher(kwargs))
    return self

  def explore_custom_score_enricher(self, **kwargs):
    """
    ExploreCustomScoreEnricher
    """
    self._add_processor(ExploreCustomScoreEnricher(kwargs))
    return self

  def explore_pdn_trigger_weight_enricher(self, **kwargs):
    """
    ExplorePdnTriggerWeightEnricher
    """
    self._add_processor(ExplorePdnTriggerWeightEnricher(kwargs))
    return self

  def explore_nn_user_embedding_enricher(self, **kwargs):
    """
    ExploreNnUserEmbeddingEnricher
    """
    self._add_processor(ExploreNnUserEmbeddingEnricher(kwargs))
    return self

  def explore_retrieve_by_nn_user_photo(self, **kwargs):
    """
    ExploreNnUserPhotoRetriever
    """
    self._add_processor(ExploreNnUserPhotoRetriever(kwargs))
    return self

  def explore_retrieve_by_embedding_icf(self, **kwargs):
    """
    ExploreEmbeddingIcfRetriever
    """
    self._add_processor(ExploreEmbeddingIcfRetriever(kwargs))
    return self

  def explore_browse_screen_enrich(self, **kwargs):
    """
    ExploreBrowseScreenEnricher
    ------
    获取 user info 中 BrowseScreen 字段相关信息
    """
    self._add_processor(ExploreBrowseScreenEnricher(kwargs))
    return self

  def explore_rerank_attr(self, **kwargs):
    """
    ExploreRerankAttrEnricher
    ------
    获取 rerank 模型所需要的common attr
    """
    self._add_processor(ExploreRerankAttrEnricher(kwargs))
    return self

  def explore_user_feature_common_attr_enrich(self, **kwargs):
    """
    ExploreUserFeatureCommonAttrEnricher
    """
    self._add_processor(ExploreUserFeatureCommonAttrEnricher(kwargs))
    return self

  def explore_copy_adjust_user_info_enrich(self, **kwargs):
    """
    ExploreRetrUserInfoAdjustEnricher
    """
    self._add_processor(ExploreRetrUserInfoAdjustEnricher(kwargs))
    return self

  def explore_environment_type_enrich(self, **kwargs):
    """
    ExploreEnvironmentTypeEnricher
    ------
    根据 service name ，host name ，ip 获取环境类型，用来判断一些操作是否需要执行
    """
    self._add_processor(ExploreEnvironmentTypeEnricher(kwargs))
    return self

  def explore_colossus_cluster_enricher(self, **kwargs):
    """
    ExploreColossusClusterEnricher
    """
    self._add_processor(ExploreColossusClusterEnricher(kwargs))
    return self

  def explore_listwise_seq_feature_enricher(self, **kwargs):
    """
    ExploreListwiseSeqFeatureEnricher
    """
    self._add_processor(ExploreListwiseSeqFeatureEnricher(kwargs))
    return self

  def explore_interest_migration_history_prepare_enricher(self, **kwargs):
    """
    ExploreInterestMigrationHistoryPrepareEnricher
    """
    self._add_processor(ExploreInterestMigrationHistoryPrepareEnricher(kwargs))
    return self

  def explore_interest_migration_coef_calculator_enricher(self, **kwargs):
    """
    ExploreInterestMigrationCoefCalculatorEnricher
    """
    self._add_processor(ExploreInterestMigrationCoefCalculatorEnricher(kwargs))
    return self

  def explore_user_interest_cocoon_enricher(self, **kwargs):
    """
    ExploreUserInterestCocoonEnricher
    """
    self._add_processor(ExploreUserInterestCocoonEnricher(kwargs))
    return self

  def explore_calc_user_xtr_enricher(self, **kwargs):
    """
    ExploreCalcUserXtrEnricher
    """
    self._add_processor(ExploreCalcUserXtrEnricher(kwargs))
    return self

  def explore_fill_avg_xtr_enricher(self, **kwargs):
    """
    ExploreFillAvgXtrEnricher
    """
    self._add_processor(ExploreFillAvgXtrEnricher(kwargs))
    return self

  def explore_list_item_predict(self, **kwargs):
    """
    ExploreListItemPredictEnricher
    """
    self._add_processor(ExploreListItemPredictEnricher(kwargs))
    return self

  def explore_listwise_attr(self, **kwargs):
    """
    ExploreListwiseAttrEnricher
    """
    self._add_processor(ExploreListwiseAttrEnricher(kwargs))
    return self

  def explore_listwise_score_enricher(self, **kwargs):
    """
    ExploreListwiseScoreEnricher
    """
    self._add_processor(ExploreListwiseScoreEnricher(kwargs))
    return self

  def explore_model_greedy_gen_list_enricher(self, **kwargs):
    """
    ExploreModelGreedyGenListEnricher
    """
    self._add_processor(ExploreModelGreedyGenListEnricher(kwargs))
    return self

  def explore_mdp_gen_list_enricher(self, **kwargs):
    """
    ExploreMdpGenListEnricher
    """
    self._add_processor(ExploreMdpGenListEnricher(kwargs))
    return self

  def list_wise_item_rerank(self, **kwargs):
    """
    ListwiseItemRerankEnricher
    """
    self._add_processor(ListwiseItemRerankEnricher(kwargs))
    return self

  def explore_common_user_feature_enricher(self, **kwargs):
    """
    ExploreCommonUserFeatureEnricher
    """
    self._add_processor(ExploreCommonUserFeatureEnricher(kwargs))
    return self

  def explore_common_item_feature_enricher(self, **kwargs):
    """
    ExploreCommonItemFeatureEnricher
    """
    self._add_processor(ExploreCommonItemFeatureEnricher(kwargs))
    return self

  def explore_embedding_candidates_attr_enricher(self, **kwargs):
    """
    ExploreEmbeddingCandidatesAttrEnricher
    """
    self._add_processor(ExploreEmbeddingCandidatesAttrEnricher(kwargs))
    return self

  def explore_custom_embedding_score_enricher(self, **kwargs):
    """
    ExploreCustomEmbeddingScoreEnricher
    """
    self._add_processor(ExploreCustomEmbeddingScoreEnricher(kwargs))
    return self

  def explore_prerank_trim_userinfo(self, **kwargs):
    """
    ExplorePrerankTrimUserInfoEnricher
    """
    self._add_processor(ExplorePrerankTrimUserInfoEnricher(kwargs))
    return self

  def explore_trimmed_photo_info_enrich(self, **kwargs):
    """
    ExploreTrimmedPhotoInfoEnricher
    ------
    截取 photo info 中需要的字段传回 rpc
    """
    self._add_processor(ExploreTrimmedPhotoInfoEnricher(kwargs))
    return self

  def explore_mc_distill_sample_kuiba_sample_enrich(self, **kwargs):
    """
    ExploreMcDistillSampleKuibaSampleEnricher
    """
    self._add_processor(ExploreMcDistillSampleKuibaSampleEnricher(kwargs))
    return self

  def explore_content_duplicate_enrich(self, **kwargs):
    """
    ExploreContentDuplicateEnricher
    ------
    判断内容重复
    """
    self._add_processor(ExploreContentDuplicateEnricher(kwargs))
    return self

  def explore_cluster_ensemble_filter_arranger(self, **kwargs):
    """
    ExploreClusterEnsembleFilterArranger
    ------
    判断内容重复
    """
    self._add_processor(ExploreClusterEnsembleFilterArranger(kwargs))
    return self

  def explore_ensemble_score_calc_enricher(self, **kwargs):
    """
    ExploreEnsembleScoreCalcEnricher
    ------
    队列weight调整ensemle sort
    """
    self._add_processor(ExploreEnsembleScoreCalcEnricher(kwargs))
    return self

  def explore_colossus_trigger_enrich(self, **kwargs):
    """
    ExploreColossusTriggerEnricher
    ------
    召回阶段请求 colossus 并预处理获得 colossus trigger
    """
    self._add_processor(ExploreColossusTriggerEnricher(kwargs))
    return self

  def explore_colossus_v2_trigger_enrich(self, **kwargs):
    """
    ExploreColossusV2TriggerEnricher
    ------
    召回阶段预处理获得 colossus trigger
    colossus 结果处理逻辑和 explore_colossus_trigger_enrich 保持一致
    去掉了请求 colossusV1 服务的逻辑，直接读 v2 服务请求得到的结果，不在单独请求 colossus 服务
    """
    self._add_processor(ExploreColossusV2TriggerEnricher(kwargs))
    return self

  def explore_colossus_v2_pic_trigger_enrich(self, **kwargs):
    """
    ExploreColossusV2PicTriggerEnricher
    ------
    召回阶段请求 colossusv2 并预处理获得 colossus pic trigger
    """
    self._add_processor(ExploreColossusV2PicTriggerEnricher(kwargs))
    return self

  def explore_colossus_v2_picture_trigger_enrich(self, **kwargs):
    """
    ExploreColossusV2AuthorTriggerEnricher
    ------
    召回阶段请求 colossusv2 并预处理获得 colossus author trigger

    参数配置
    ------
    `colossus_resp_attr`: [string] colossus_resp 指针的 attr

    `output_colossus_trigger_id_attr`: [string] 用于保存trgger id list 的 attr

    `output_colossus_trigger_weight_attr`: [string] 用于保存trgger weight list 的 attr

    `colossus_trigger_type`: [int] 用于聚合的trigger_type, 0为author_id, 1为tag, 其他为photo_id, 默认 0

    `is_select_picture`: [bool] [动态参数] 是否使用label中图文标记过滤视频, 默认false

    `trigger_num`: [int] [动态参数] 返回trigger的最大数目, 默认100

    `min_trigger_weight`: [int] [动态参数] 过滤掉一些不置信的trigger, 返回trigger的最小weight, 默认2

    `colossus_label_weight_attr`: [string] [动态参数] 统计reward使用的label weight, 格式为click:like:follow:forward:hate:comment,
                                  默认 1:2:5:2:-10:2

    `colossus_channel_select_attr`: [string] [动态参数] 统计reward只使用哪些channel,
                                  默认 1

    `colossus_time_s_min_max_attr`: [string] [动态参数] 统计reward使用的时间范围, 格式为为min:max,单位为s,默认0:604800,

    `colossus_duration_s_min_max_attr`: [string] [动态参数] 统计reward使用的duration范围, 格式为为min:max,单位为s,默认0:100000

    示例
    ------
    ``` python
    .explore_colossus_v2_picture_trigger_enrich(
                    colossus_resp_attr="colossus_resp_v2",
                    output_colossus_trigger_id_attr="colossus_author_trigger_list",
                    output_colossus_trigger_weight_attr="colossus_author_weight_list",
                    is_select_picture=False,
                    trigger_num=100,
                    min_trigger_weight=2,
                    colossus_label_weight_attr="1:2:5:2:-10:2",
                    colossus_time_s_min_max_attr="0:604800",
                    colossus_duration_s_min_max_attr="0:100000",
                    colossus_trigger_type=0
                    )
    ```

    """
    self._add_processor(ExploreColossusV2PictureTriggerEnricher(kwargs))
    return self

  def explore_cluster_by_custom_rule_v2(self, **kwargs):
    """
    ExploreClusterByCustomRuleV2Enricher
    ------

    参数配置
    ------
    """
    self._add_processor(ExploreCustomRuleClusterV2Enricher(kwargs))
    return self

  def explore_rule_cluster_enricher(self, **kwargs):
    """
    ExploreRuleClusterEnricher
    ------

    参数配置
    ------
    """
    self._add_processor(ExploreRuleClusterEnricher(kwargs))
    return self

  def explore_explore_similar_users_hetu_enricher(self, **kwargs):
    """
    ExploreExploreSimilarUsersHetuEnricher
    ------

    参数配置
    ------
    """
    self._add_processor(ExploreExploreSimilarUsersHetuEnricher(kwargs))
    return self


  def explore_related_score_enricher(self, **kwargs):
    """
    ExploreRelatedScoreEnricher
    """
    self._add_processor(ExploreRelatedScoreEnricher(kwargs))
    return self

  def explore_related_score_enricher_v2(self, **kwargs):
    """
    ExploreRelatedScoreEnricherV2
    """
    self._add_processor(ExploreRelatedScoreEnricherV2(kwargs))
    return self

  def explore_similar_photo_enricher(self, **kwargs):
    """
    ExploreSimilarPhotoEnricher
    """
    self._add_processor(ExploreSimilarPhotoEnricher(kwargs))
    return self

  def explore_complex_attr_filter_arranger(self, **kwargs):
    """
    ExploreComplexAttrFilterArranger
    """
    self._add_processor(ExploreComplexAttrFilterArranger(kwargs))
    return self

  def fountain_negative_feedback_discount_v2(self, **kwargs):
    """
    ExploreNegFeedbackDiscountV2Enricher
    ------
    计算 discount score

    参数配置
    ------
    `enable_fountain_user_profile`: [int] 是否使用内流 profile

    `enable_hot_user_profile`: [int] 是否使用外流 profile

    `enable_not_click_list`: [int] 是否使用曝光未点击

    `enable_play_stat_list`: [int] 是否使用 video 播放状态

    `enable_hate_list`: [int] 是否使用 hate list

    `discount_score`: [double] 打压系数

    `neg_feedback_threshold`: [double] 打压阈值

    `period_decay_factor`: [double] 周期衰减系数

    `no_click_factor`: [double] 曝光未点击权重

    `video_play_stat_factor`: [double] 播放状态权重

    `hate_list_factor`: [double] hate 权重

    `play_time_thresold_0`: [double] 播放时长阈值

    `play_time_thresold_1`: [double] 播放时长阈值

    `time_limit_second`: [int] 计算周期(s)

    """
    self._add_processor(ExploreNegFeedbackDiscountV2Enricher(kwargs))
    return self

  def explore_retrieval_filter(self, **kwargs):
    """
    ExploreRetrievalFilterArranger
    """
    self._add_processor(ExploreRetrievalFilterArranger(kwargs))
    return self

  def explore_personally_ensemble_weight(self, **kwargs):
    """
    ExplorePersonallyEnsembleWeightEnricher
    """
    self._add_processor(ExplorePersonallyEnsembleWeightEnricher(kwargs))
    return self

  def explore_personal_cem_weight(self, **kwargs):
    """
    ExplorePersonalCemWeightEnricher
    """
    self._add_processor(ExplorePersonalCemWeightEnricher(kwargs))
    return self

  def explore_pic_calc_cluster(self, **kwargs):
    """
    ExplorePicCalcClusterEnricher
    """
    self._add_processor(ExplorePicCalcClusterEnricher(kwargs))
    return self

  def explore_markov_gen_seq(self, **kwargs):
    """
    ExploreMarkovGenSeqEnricher
    """
    self._add_processor(ExploreMarkovGenSeqEnricher(kwargs))
    return self

  def explore_reset_reason(self, **kwargs):
    """
    ExploreResetReasonArranger
    """
    self._add_processor(ExploreResetReasonArranger(kwargs))
    return self

  def explore_retrieve_by_redis_list_range(self, **kwargs):
    """
    ExploreRedisListRangeRetriever
    """
    self._add_processor(ExploreRedisListRangeRetriever(kwargs))
    return self

  def explore_custom_trim_user_info(self, **kwargs):
    """
    ExploreUserInfoCostumTrimEnricher
    """
    self._add_processor(ExploreUserInfoCostumTrimEnricher(kwargs))
    return self

  def explore_ranking_gen_part_diversity_tag(self, **kwargs):
    """
    ExploreRankingPartDiversityTagEnricher
    ------
    生成部分 ranking 阶段比较复杂的打散 tag
    """
    self._add_processor(ExploreRankingPartDiversityTagEnricher(kwargs))
    return self

  def explore_parse_local_life_info(self, **kwargs):
    """
    ExploreParseLocalLifeInfoEnricher
    -----
    解析本地生活相关字段
    """
    self._add_processor(ExploreParseLocalLifeInfoEnricher(kwargs))
    return self

  def explore_replace_pirate_photo(self, **kwargs):
    """
    ExplorePiratePhotoReplacementArranger
    ------
    如果有盗版视频替换成原版
    """
    self._add_processor(ExplorePiratePhotoReplacementArranger(kwargs))
    return self

  def explore_sort_by_reason_priority(self, **kwargs):
    """
    ExploreReasonPrioritySortArranger
    ------
    按 reason 优先级调整结果集顺序
    """
    self._add_processor(ExploreReasonPrioritySortArranger(kwargs))
    return self

  def explore_add_inverted_index_weighted_score(self, **kwargs):
    """
    ExploreInvertedIndexWeightedScoreEnricher
    ------
    用于倒排后对结果集进行加权处理
    """
    self._add_processor(ExploreInvertedIndexWeightedScoreEnricher(kwargs))
    return self

  def explore_sphinx_param_enrich(self, **kwargs):
    """
    ExploreSphinxParamEnricher
    ------
    请求强化学习服务
    """
    self._add_processor(ExploreSphinxParamEnricher(kwargs))
    return self

  def explore_calc_ensemble_score(self, **kwargs):
    """
    ExploreEnsembleScoreCalcArranger
    """
    self._add_processor(ExploreEnsembleScoreCalcArranger(kwargs))
    return self

  def explore_list_ensemble_sort(self, **kwargs):
    """
    ExploreListEnsembleSortEnricher
    """
    self._add_processor(ExploreListEnsembleSortEnricher(kwargs))
    return self

  def explore_retr_search_trigger_enriche(self, **kwargs):
    """
    ExploreRetrSearchTriggerEnricher
    """
    self._add_processor(ExploreRetrSearchTriggerEnricher(kwargs))
    return self

  def explore_intrest_adjust_enricher(self, **kwargs):
    """
    ExploreIntrestAdjustEnricher
    """
    self._add_processor(ExploreIntrestAdjustEnricher(kwargs))
    return self

  def explore_cluster_variant_sort_v3(self, **kwargs):
    """
    ExploreClusterVariantSortV3Arranger

    """
    self._add_processor(ExploreClusterVariantSortV3Arranger(kwargs))
    return self

  def explore_hierachy_cluster_variant_sort(self, **kwargs):
    """
    ExploreHierachyClusterVariantSortArranger

    """
    self._add_processor(ExploreHierachyClusterVariantSortArranger(kwargs))
    return self

  def explore_transform_hetu_tag(self, **kwargs):
    """
    ExploreTransHetuTagEnricher
    ------
    把 item 级别的 hetu tag 转换成 tag，支持 int/int_list 类型的 attrs
    """
    self._add_processor(ExploreTransHetuTagEnricher(kwargs))
    return self

  def explore_trans_fintr_enricher(self, **kwargs):
    """
    ExploreTransFintrEnricher
    ------
    转化 fintr, 包括异常值处理 / 乘以 duration / 获得 duration 分桶内分位数
    """
    self._add_processor(ExploreTransFintrEnricher(kwargs))
    return self

  def retrieve_by_explore_reco_similar(self, **kwargs):
    """
    ExploreRecoSimilarKessI2iRetriever
    """
    self._add_processor(ExploreRecoSimilarKessI2iRetriever(kwargs))
    return self


  def retrieve_by_explore_fountain_itemcf(self, **kwargs):
    """
    ExploreFountainRecoItemCfRetriever
    """
    self._add_processor(ExploreFountainRecoItemCfRetriever(kwargs))
    return self

  def explore_pic_rerank(self, **kwargs):
    """
    ExplorePicRerankArranger
    """
    self._add_processor(ExplorePicRerankArranger(kwargs))
    return self

  def explore_get_embedding_map_enricher(self, **kwargs):
    """
    ExploreGetEmbeddingMapEnricher
    """
    self._add_processor(ExploreGetEmbeddingMapEnricher(kwargs))
    return self

  def explore_diversity_update_enricher(self, **kwargs):
    """
    ExploreDiversityUpdateEnricher
    """
    self._add_processor(ExploreDiversityUpdateEnricher(kwargs))
    return self

  def explore_picture_diversity_enricher(self, **kwargs):
    """
    ExplorePictureDiversityEnricher
    """
    self._add_processor(ExplorePictureDiversityEnricher(kwargs))
    return self

  def explore_pic_cluster_counter_enricher(self, **kwargs):
    """
    ExplorePicClusterCounterEnricher
    """
    self._add_processor(ExplorePicClusterCounterEnricher(kwargs))
    return self

  def explore_enrich_kv_param(self, **kwargs):
    """
    ExploreKvParamEnricher
    分割类似 sim_weight:2.0;sim_gift:1.8;sim_lvtr:1.2;sim_ctr:0.6 字符串参数到 Common Attrs
    参数配置
    ------
      - `origin_param`: [string][动态参数] 原始参数，一般来源于 AB
      - `param_attr_prefix`: [string] 参数解析后成 Common Attr 的前缀
      - `param_separator`: [string][可选] 默认值为英文分号
      - `kv_separator`: [string][可选] 默认值为英文冒号
      - `param_name_list_attr`: [string][可选] 参数解析后所有参数名的 Common Attr
    调用示例
    ------
    ``` python
    .explore_enrich_kv_param(origin_param="{{mc_queue_weight_param}}",
    param_attr_prefix="mc_cs_weight_",
    param_separator=";",
    kv_separator=":"
    param_name_list_attr="mc_weight_param_names"
    ```
    """
    self._add_processor(ExploreKvParamEnricher(kwargs))
    return self

  def explore_channel_sort(self, **kwargs):
    """
    ExploreChannelSortArranger
    多通道排序
    参数配置
    ------
      - `channel_queue_names`: [string list][动态参数] 实际队列的名字
      - `input_count_threshold`: [int][动态参数] 进行多通道排序最小输入 Item 数量
      - `output_count`: [int][动态参数] 进行多通道排序输出 Item 数量
      - `queue_weight_attrs`: [string list] 表示各队列的权重的 Common Attr 名字
      - `queue_score_attrs`: [string list] 表示各队列的分数的 Item Attr 名字
      - `queue_flag_attrs`: [string list] 标志是否属于某队列的 Item Attr 名字
      - `weight_type`: [string] 权重的类型, RELATIVE 说明队列权重表示各队列在结果集中的占比, ABSOLUTE 说明队列权重表示各自的队列保留下来的 item 的比例, 默认值是 RELATIVE
      - `stage`: [string] 用来拼 queue_score_attr, queue_flag_attr 的前缀, 例如当它的值为 prerank 时, 拼出来的 queue_score_attr 前缀是 mc_csqs_prerank_, 默认是空
      - `enable_double_lowest_score`: [bool][动态参数] 是否允许 double 最小值参与排序, double 的最小值一般是用于表示逻辑截断或者 score 为空
    ------
    ``` python
    .explore_channel_sort(
    channel_queue_names="{{mc_channel_queue_names}}"
    input_count_threshold=200,
    output_count=200,
    queue_weight_attrs=["mc_csqw_prerank_photo, "mc_csqw_prerank_picture"],
    queue_score_attrs=["mc_csqs_prerank_photo", "mc_csqs_prerank_picture"],
    queue_flag_attrs=["mc_csqf_prerank_photo", "mc_csqf_prerank_pciture"]),
    weight_type="RELATIVE",
    stage="prerank"
    ```
    """
    self._add_processor(ExploreChannelSortArranger(kwargs))

  def explore_user_colossus_x_item_enricher(self, **kwargs):
    """
    ExploreUserColossusXItemAttrEnricher
    """
    self._add_processor(ExploreUserColossusXItemAttrEnricher(kwargs))
    return self

  def explore_user_emp_xtr_enricher(self, **kwargs):
    """
    ExploreUserEmpXtrEnricher
    """
    self._add_processor(ExploreUserEmpXtrEnricher(kwargs))
    return self

  def explore_pic_colossus_stat(self, **kwargs):
    """
    ExplorePicColossusStatEnricher
    ------

    参数配置
    ------
    """
    self._add_processor(ExplorePicColossusStatEnricher(kwargs))
    return self

  def explore_user_debias_xtr_enricher(self, **kwargs):
    """
    ExploreUserDebiasXtrEnricher
    """
    self._add_processor(ExploreUserDebiasXtrEnricher(kwargs))
    return self

  def explore_user_debias_xtr_v2_enricher(self, **kwargs):
    """
    ExploreUserDebiasXtrV2Enricher
    """
    self._add_processor(ExploreUserDebiasXtrV2Enricher(kwargs))
    return self

  def explore_prerank_bucket_sort_enricher(self, **kwargs):
    """
    ExplorePrerankBucketSortEnricher
    """
    self._add_processor(ExplorePrerankBucketSortEnricher(kwargs))
    return self

  def explore_absolute_xtr_score_que_enricher(self, **kwargs):
    """
    ExploreAbsoluteXtrScoreQueEnricher
    """
    self._add_processor(ExploreAbsoluteXtrScoreQueEnricher(kwargs))
    return self

  def explore_trans_sim_cluster_id_enricher(self, **kwargs):
    """
    ExploreTransSimClusterIdEnricher
    """
    self._add_processor(ExploreTransSimClusterIdEnricher(kwargs))
    return self

  def explore_duration_xtr_debias_enricher(self, **kwargs):
    """
    ExploreDurationXtrDebiasEnricher
    """
    self._add_processor(ExploreDurationXtrDebiasEnricher(kwargs))
    return self

  def explore_support_author_tgi_score_enricher(self, **kwargs):
    """
    ExploreSupportAuthorTgiScoreEnricher
    参数配置：
    user_info_ptr_attr: user_info
    memory_data_ptr_attr: memory_data
    save_item_tgi_gender_score_attr: tgi_gender_score 存储attr name
    save_item_tgi_age_score_attr: tgi_age_score 存储attr name
    """
    self._add_processor(ExploreSupportAuthorTgiScoreEnricher(kwargs))
    return self

  def explore_user_colossus_cascade_cluster_enricher(self, **kwargs):
    """
    ExploreUserColossusCascadeClusterEnricher
    """
    self._add_processor(ExploreUserColossusCascadeClusterEnricher(kwargs))
    return self

  def explore_photo_info_pb2kv(self, **kwargs):
    """
    ExplorePhotoInfoPb2KvEnricher
    """
    self._add_processor(ExplorePhotoInfoPb2KvEnricher(kwargs))
    return self

  def explore_mix_rerank(self, **kwargs):
    """
    ExploreMixRerankArranger
    """
    self._add_processor(ExploreMixRerankArranger(kwargs))
    return self

  def explore_global_quantile_xtr_enricher(self, **kwargs):
    """
    ExploreGlobalQuantileXtrEnricher
    """
    self._add_processor(ExploreGlobalQuantileXtrEnricher(kwargs))
    return self

  def explore_select_negative_pids_enricher(self, **kwargs):
    """
    ExploreSelectNegativePidsEnricher
    """
    self._add_processor(ExploreSelectNegativePidsEnricher(kwargs))
    return self

  def explore_top_video_sim_score_enricher(self, **kwargs):
    """
    ExploreTopVideoSimScoreEnricher
    """
    self._add_processor(ExploreTopVideoSimScoreEnricher(kwargs))
    return self

  def explore_explore_hetu_tags_enricher(self, **kwargs):
    """
    ExploreExploreHetuTagsEnricher
    """
    self._add_processor(ExploreExploreHetuTagsEnricher(kwargs))
    return self

  def explore_interest_hetu_tags_enricher(self, **kwargs):
    """
    ExploreInterestHetuTagsEnricher
    """
    self._add_processor(ExploreInterestHetuTagsEnricher(kwargs))
    return self

  def explore_item_reason_score_enricher(self, **kwargs):
    """
    ExploreItemReasonScoreEnricher
    ------
    设置不同reason的rank得分

    参数配置
    ------
    `mappings`: [list] Attr 转换映射
      - `reason`: [int] 需要设置rank的召回reason
      - `to_item_attr`: [string] 待写入的 ItemAttr 名称
    `smoothing`: [int] 是否对不属于该reason的item进行平滑处理，默认不平滑
    调用示例
    ------
    ``` python
    .explore_item_reason_score_enricher(
      mappings = [{
        "reason": 3070,
        "to_common_attr": "pdn_rank_score",
      }],
      smoothing = 1
    )
    ```
    """
    self._add_processor(ExploreItemReasonScoreEnricher(kwargs))
    return self

  def explore_ensemble_score_calc_pure_value_enricher(self, **kwargs):
    """
    ExploreEnsembleScoreCalcPureValueEnricher
    """
    self._add_processor(ExploreEnsembleScoreCalcPureValueEnricher(kwargs))
    return self

  def explore_attr_quantile_enricher(self, **kwargs):
    """
    ExploreAttrQuantileEnricher
    """
    self._add_processor(ExploreAttrQuantileEnricher(kwargs))
    return self

  def explore_user_persona_sorter(self, **kwargs):
    """
    ExploreUserPersonaSortArranger
    ------
    根据获取到的 emb 计算 cos，并排序
    参数配置
    ------
    `user_emb_key`: [string] user 端的 emb 属性名，从 common attr 获取
    `item_emb_key`: [string] item 端的 emb 属性名，从 item attr 获取
    `mc_emb_cos_score_key`: [string] 计算的 cos 值，输出到 item attr
    """
    self._add_processor(ExploreUserPersonaSortArranger(kwargs))
    return self

  def explore_attrs_adjust_enricher(self, **kwargs):
    """
    ExploreAttrsAdjustEnricher
    """
    self._add_processor(ExploreAttrsAdjustEnricher(kwargs))
    return self

  def explore_merchant_global_data_enricher(self, **kwargs):
    """
    ExploreMerchantGlobalDataEnricher
    """
    self._add_processor(ExploreMerchantGlobalDataEnricher(kwargs))
    return self

  def explore_select_action_list_trigger_enricher(self, **kwargs):
    """
    ExploreSelectActionListTriggerEnricher
    """
    self._add_processor(ExploreSelectActionListTriggerEnricher(kwargs))
    return self

  def force_insert_position_enricher(self, **kwargs):
    """
    ForceInsertPositionEnricher
    """
    self._add_processor(ForceInsertPositionEnricher(kwargs))
    return self

  def explore_global_trigger_select_enricher(self, **kwargs):
    """
    ExploreTriggerSelectEnricher
    """
    self._add_processor(ExploreGlobalTriggerSelectEnricher(kwargs))
    return self

  def explore_global_trigger_select_v2_enricher(self, **kwargs):
    """
    ExploreTriggerSelectV2Enricher
    """
    self._add_processor(ExploreGlobalTriggerSelectV2Enricher(kwargs))
    return self

  def explore_cluster_truncate_arranger(self, **kwargs):
    """
    ExploreClusterTruncateArranger
    """
    self._add_processor(ExploreClusterTruncateArranger(kwargs))
    return self

  def explore_control_hetu_count_arranger(self, **kwargs):
    """
    ExploreControlHetuCountArranger
    """
    self._add_processor(ExploreControlHetuCountArranger(kwargs))
    return self

  def explore_control_similarity_score_arranger(self, **kwargs):
    """
    ExploreControlSimilarityScoreArranger
    """
    self._add_processor(ExploreControlSimilarityScoreArranger(kwargs))
    return self

  def explore_dpp_set_arranger(self, **kwargs):
    """
    ExploreDppSetArranger
    """
    self._add_processor(ExploreDppSetArranger(kwargs))
    return self

  def explore_retr_reason_limit(self, **kwargs):
    """
    ExploreRetrievalReasonLimitArranger
    """
    self._add_processor(ExploreRetrievalReasonLimitArranger(kwargs))
    return self

  def explore_ensemble_filter_score_enricher(self, **kwargs):
    """
    ExploreEnsembleFilterScoreEnricher
    """
    self._add_processor(ExploreEnsembleFilterScoreEnricher(kwargs))
    return self

  def explore_retrieve_by_cache_string(self, **kwargs):
    """
    ExploreCacheStringRetriever
    ------
    通过 cache 数据召回结果
    """
    self._add_processor(ExploreCacheStringRetriever(kwargs))
    return self

  def explore_build_cache_string(self, **kwargs):
    """
    ExploreCacheStringBuildEnricher
    ------
    将结果集结果构造成 cache 字符串
    """
    self._add_processor(ExploreCacheStringBuildEnricher(kwargs))
    return self

  def explore_retr_personal_quota_enrich(self, **kwargs):
    """
    ExploreRetrPersonalQuotaEnricher
    ------
    召回个性化系数, 当exptag realshow大于等于200时, ratio = user_exp_tag_reward / user_reward

    参数配置
    ------
    `register_exptag`: [string] 需要计算ratio的exptag,逗号分隔, 默认值""

    `cluster_name`: [string] redis集群名称

    `time_out`: [int]  超时时间, 单位ms

    `upper_limit`: [int]  ratio 上界, 默认值1.0

    `lower_limit`: [int]  ratio 下界, 默认值1.0

    `save_data_ptr_to_attr`: [string] 保存数据指针的 common attr

    `realshow_threshold`: [int] 计算ratio的最小曝光数

    `cal_ratio_mode`: [int] 计算ratio的模式,0表示线性, 1表示非线性,默认值为0

    `power`: [double] 非线性指数，默认值为1，形式为1±pow(diff, power), 默认值为1

    示例
    ------
    ``` python
    .explore_retr_personal_quota_enrich(
                    register_exptag="3099,3110",
                    cluster_name="xx",
                    time_out=50,
                    upper_limit=1.2,
                    lower_limit=0.8,
                    save_data_ptr_to_attr="retr_ratio"
                    )
    ```
    """
    self._add_processor(ExploreRetrPersonalQuotaEnricher(kwargs))
    return self

  def explore_trigger_selected_enricher(self, **kwargs):
    """
    ExploreTriggerSelectedEnricher
    生活tab构造trigger
    """
    self._add_processor(ExploreTriggerSelectedEnricher(kwargs))
    return self

  def explore_calc_value_and_rank_score(self, **kwargs):
    """
    ExploreValueAndRankScoreArranger
    """
    self._add_processor(ExploreValueAndRankScoreArranger(kwargs))
    return self

  def explore_sim_colossus_stat(self, **kwargs):
    """
    ExploreSimColossusStatEnricher
    ------

    参数配置
    ------
    """
    self._add_processor(ExploreSimColossusStatEnricher(kwargs))
    return self

  def explore_user_colossus_history(self, **kwargs):
    """
    ExploreUserColossusHistoryEnricher
    ------

    参数配置
    ------
    """
    self._add_processor(ExploreUserColossusHistoryEnricher(kwargs))
    return self

  def explore_consume_time_ltr_attr(self, **kwargs):
    """
    ExploreConsumeTimeLtrAttrEnricher
    """
    self._add_processor(ExploreConsumeTimeLtrAttrEnricher(kwargs))
    return self

  def explore_calc_fractile_score_by_multiple_bucket_enricher(self, **kwargs):
    """
    ExploreCalcFractileScoreByMultipleBucketEnricher
    """
    self._add_processor(ExploreCalcFractileScoreByMultipleBucketEnricher(kwargs))
    return self

  def explore_calc_pf2r_score_by_multiple_bucket_enricher(self, **kwargs):
    """
    ExploreCalcPf2rScoreByMultipleBucketEnricher
    """
    self._add_processor(ExploreCalcPf2rScoreByMultipleBucketEnricher(kwargs))
    return self

  def explore_calc_pairwise_rank_score_enricher(self, **kwargs):
    """
    ExploreCalcPairwiseRankScoreEnricher
    """
    self._add_processor(ExploreCalcPairwiseRankScoreEnricher(kwargs))
    return self

  def explore_calc_sharpe_ratio_score_enricher(self, **kwargs):
    """
    ExploreCalcSharpeRatioScoreEnricher
    """
    self._add_processor(ExploreCalcSharpeRatioScoreEnricher(kwargs))
    return self

  def explore_cluster_variant_sort_enricher(self, **kwargs):
    """
    ExploreClusterVariantSortEnricher
    """
    self._add_processor(ExploreClusterVariantSortEnricher(kwargs))
    return self

  def explore_cluster_priority_score(self, **kwargs):
    """
    ExploreClusterPriorityScoreEnricher
    分通蛇形排序
    参数配置
    ------
      - `cluster_attr_name`: [string][非动态参数] 聚类属性名
      - `weight_type`: [string] 权重的类型, RELATIVE 说明队列权重表示各队列在结果集中的占比, ABSOLUTE 说明队列权重表示各自的队列保留下来的 item 的比例, 默认值是 ABSOLUTE
      - `cluster_weight_map_str`: [string] cluster对应的权重大小，如果是ABSOLUTE是替换，如果是RELATIVE就是在原有的权重上乘以value,格式是k1:v1;k2:v2...
      - `max_item_num`: [int][动态参数] 对原队列的TOP多少的数据进行蛇形merge
    ------
    ``` python
    .explore_cluster_priority_score(
      cluster_attr_name="cluster_attr_name"
      cluster_weight_map_str="{{4:12}}",
      weight_type="ABSOLUTE"
    )
    ```
    """
    self._add_processor(ExploreClusterPriorityScoreEnricher(kwargs))
    return self

  def explore_memory_data_ptr_filter(self, **kwargs):
    """
    ExploreMemoryDataPtrFilterArranger
    用memoryData进行过滤
    ------
    参数配置
    ------
    `memory_data_ptr_attr`: [string] memory data ptr 字段（只能为 uint64 set 类型）
    """
    self._add_processor(ExploreMemoryDataPtrFilterArranger(kwargs))
    return self

  def explore_pic_diversity_control_enricher(self, **kwargs):
    """
    ExplorePicDiversityControlEnricher
    """
    self._add_processor(ExplorePicDiversityControlEnricher(kwargs))
    return self

  def user_history_cids_stat_enricher(self, **kwargs):
    """
    UserHistoryCidsStatEnricher
    """
    self._add_processor(UserHistoryCidsStatEnricher(kwargs))
    return self

  def explore_pic_recent_interest_adjust_enricher(self, **kwargs):
    """
    ExplorePicRecentInterestAdjustEnricher
    """
    self._add_processor(ExplorePicRecentInterestAdjustEnricher(kwargs))
    return self

  def explore_pic_interest_stat_enricher(self, **kwargs):
    """
    ExplorePicInterestStatEnricher
    """
    self._add_processor(ExplorePicInterestStatEnricher(kwargs))
    return self

  def explore_pic_interest_stat_enricher_v2(self, **kwargs):
    """
    ExplorePicInterestStatV2Enricher
    """
    self._add_processor(ExplorePicInterestStatV2Enricher(kwargs))
    return self
  
  def explore_rerank_calc_page1_trigger_score_enricher(self, **kwags):
    """
    ExploreRerankCalcPage1TriggerScoreEnricher
    """
    self._add_processor(ExploreRerankCalcPage1TriggerScoreEnricher(kwags))
    return self

  def explore_hierarchical_priority_insert_tag_enricher(self, **kwargs):
    """
    ExploreHierarchicalPriorityInsertTagEnricher
    分层强插计算逻辑
    参数配置
    ------
      - `queues`: [string][非动态参数] 强插队列，基于weight attr的大小可以做优先级分层
      - `name`: [string] queues里面的数组属性，需要计算的强插的属性名
      - `weight_attr`: [string] queues里面的数组子属性，基于name的设置分值权重大小，weight大小必须设置成足够分层情况
      - `photo_source_type`: [Int] 透传到RPC的photo_source_type
      - `enable_only_cold_start`: [bool][动态参数] 冷启开关
      - `enable_only_first_page_show`: [bool][动态参数] 首屏幕开关
      - `enable_only_is_zero_play`: [bool][动态参数] 零播人群开关
      - `is_cold_start`: [bool][非动态参数] 冷启请求
      - `is_first_page_show`: [bool][非动态参数] 首屏幕请求
      - `is_zero_play`: [bool][非动态参数] 零播人群请求
      - `seek_num`: [int][动态参数] 强插视频搜索范围
      - `save_tag_to_attr`: [string][非动态参数] 强插视频tag
    ------
    ``` python
    .explore_hierarchical_priority_insert_tag_enricher(
      queues=[
        {"name":"is_follow","weight_attr":"is_follow_weight_attr"},
        {"name":"is_author","weight_attr":"is_author_weight_attr"},
        {"name":"is_debug","weight_attr":"is_debug_weight_attr"},
      ],
      save_tag_to_attr="photo_source_type",
      enable_only_cold_start="{{enable_only_cold_start}}",
      enable_only_first_page_show="{{enable_only_first_page_show}}",
      is_cold_start_attr="is_cold_start",
      is_first_page_show="is_first_page",
      seek_num="{{seek_num}}"
    )
    ```
    """
    self._add_processor(ExploreHierarchicalPriorityInsertTagEnricher(kwargs))
    return self

  def explore_redis_recommend_by_friend_retriever(self, **kwargs):
    """
    ExploreRedisRecommendByFriendRetriever
    """
    self._add_processor(ExploreRedisRecommendByFriendRetriever(kwargs))
    return self

  def explore_cluster_priority_score_v2(self, **kwargs):
    """
    ExploreClusterPriorityScoreV2Enricher
    基于colossus的分通蛇形排序
    参数配置
    ------
      - `colossus_action_type`: [int][动态参数] 计算action类型 click:0 long_view_count:1 finish_count:2  read_count:3
      - `colossus_attr_name`: [string] colossus的字段
      - `cluster_attr_name`: [string][非动态参数] 聚类属性名
      - `exploitation_coef`: [double][动态参数] 利用系数
      - `exploration_coef`: [double][动态参数] 探索系数
      - `max_item_num`: [int][动态参数] 对原队列的TOP多少的数据进行蛇形merge
      - `save_score_to_attr`: [string] 将序值的score存入item_attr
      - `xtr_attr_name`: [int][非动态参数] 输出多少item
    ------
    ``` python
    .explore_cluster_priority_score_v2(
      cluster_attr_name="cluster_attr_name",
      colossus_attr_name="colossus_attr_name",
      colossus_action_type=0,
      exploitation_coef=1.0,
      exploration_coef=0.0,
      xtr_attr_name="pctr",
      save_score_to_attr = self._score_attr,
      max_item_num=0
    )
    ```
    """
    self._add_processor(ExploreClusterPriorityScoreV2Enricher(kwargs))
    return self

  def explore_retr_quota_limit(self, **kwargs):
    """
    ExploreRetrievalQuotaLimitArranger
    """
    self._add_processor(ExploreRetrievalQuotaLimitArranger(kwargs))
    return self

  def explore_rerank_collect_single_score_candidate(self, **kwargs):
    """
    ExploreRerankSingleScoreCandidateEnricher
    """
    self._add_processor(ExploreRerankSingleScoreCandidateEnricher(kwargs))
    return self

  def explore_rerank_gen_list_by_dpp(self, **kwargs):
    """
    ExploreRerankDPPListEnricher
    """
    self._add_processor(ExploreRerankDPPListEnricher(kwargs))
    return self

  def explore_rerank_list_retriever(self, **kwargs):
    """
    ExploreRerankListRetriever
    """
    self._add_processor(ExploreRerankListRetriever(kwargs))
    return self

  def explore_rerank_gen_list_feature(self, **kwargs):
    """
    ExploreRerankListFeatureEnricher
    """
    self._add_processor(ExploreRerankListFeatureEnricher(kwargs))
    return self

  def explore_rerank_dispatch_list_score(self, **kwargs):
    """
    ExploreRerankDispatchListScoreEnricher
    """
    self._add_processor(ExploreRerankDispatchListScoreEnricher(kwargs))
    return self

  def explore_rerank_gen_list_by_ssd(self, **kwargs):
    """
    ExploreRerankSSDListEnricher
    """
    self._add_processor(ExploreRerankSSDListEnricher(kwargs))
    return self

  def explore_rerank_gen_random_ensemble_candidate(self, **kwargs):
    """
    ExploreRerankRandomEnsembleCandidateEnricher
    """
    self._add_processor(ExploreRerankRandomEnsembleCandidateEnricher(kwargs))
    return self

  def explore_rerank_select_list(self, **kwargs):
    """
    ExploreRerankSelectListEnricher
    """
    self._add_processor(ExploreRerankSelectListEnricher(kwargs))
    return self

  def explore_rerank_gen_list_by_model_old(self, **kwargs):
    """
    ExploreRerankGenListByModelOldEnricher
    """
    self._add_processor(ExploreRerankGenListByModelOldEnricher(kwargs))
    return self

  def explore_snake_merge(self, **kwargs):
    """
    ExploreSnakeMergeArranger

    分桶蛇形排序
    参数配置
    ------
      - `cluster_attr_name`: [string][非动态参数] 聚类属性名
      - `max_item_num`: [int][动态参数] 返回数量
    ------
    ``` python
    .explore_snake_merge(
      cluster_attr_name="cluster_attr_name"
      max_item_num=1000
    )
    ```
    """
    self._add_processor(ExploreSnakeMergeArranger(kwargs))
    return self

  def explore_related_rank_score(self, **kwargs):
    """
    ExploreRelatedRankScoreEnricher
    """
    self._add_processor(ExploreRelatedRankScoreEnricher(kwargs))
    return self

  def explore_interest_hetu_retargeting_enricher(self, **kwargs):
    """
    ExploreInterestHetuRetargetingEnricher
    """
    self._add_processor(ExploreInterestHetuRetargetingEnricher(kwargs))
    return self

  def explore_short_item_adjust_enricher(self, **kwargs):
    """
    ExploreShortItemAdjustEnricher
    """
    self._add_processor(ExploreShortItemAdjustEnricher(kwargs))
    return self

  def explore_interest_chase_score(self, **kwargs):
    """
    ExploreInterestChaseScoreEnricher
    """
    self._add_processor(ExploreInterestChaseScoreEnricher(kwargs))
    return self

  def explore_colossus_nonclick_enricher(self, **kwargs):
    """
    ExploreColossusNonclickEnricher
    """
    self._add_processor(ExploreColossusNonclickEnricher(kwargs))
    return self

  def explore_interest_tagnex_chase_enricher(self, **kwargs):
    """
    ExploreInterestTagnexChaseEnricher
    """
    self._add_processor(ExploreInterestTagnexChaseEnricher(kwargs))
    return self
