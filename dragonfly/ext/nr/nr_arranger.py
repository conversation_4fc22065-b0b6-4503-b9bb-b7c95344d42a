#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafArranger

class NrLiteOnlineConfigurableFilterPhotoArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "nr_lite_online_configurable_filter_photo"

  @strict_types
  def __init__(self, config: dict):
    super().__init__(config)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("input_item_attrs"):
        attrs = {name for name in self._config["input_item_attrs"]}
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("input_common_attrs"):
        attrs = {name for name in self._config["input_common_attrs"]}
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return False


class NrRetrievalFilterArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "nr_retr_filter_arranger"

  @strict_types
  def __init__(self, config: dict):
    super().__init__(config)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("item_attr_map").values():
      attrs.add(attr)
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get('user_info_ptr_attr'))
    for filter in self._config.get("filters"):
       attrs.update(self.extract_dynamic_params(filter.get("enable")))
       if filter.get('input_common_attrs'):
        attrs.update(filter.get('input_common_attrs'))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return False

class NrSlidingWindowMmrDiversityArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_sliding_window_mmr_diversity"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["limit", "mmr_lambda", "allow_empty_embedding", "sliding_window_size"]:
      if key in self._config:
        ret.update(self.extract_dynamic_params(self._config[key]))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_embedding_attr", "ranking_score_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_item_mmr_score_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret


class NrSsdVariantArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_ssd_variant"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["limit", "theta", "allow_empty_embedding", "stable_ssd", "cross_screen_variant", "cross_screen_variant_v2", "embedding_dim", "l2_norm_ranking_score", "optimized_ssd", "enable_simscore_scale", "sim_scale_params_str", "enable_simscore_scale", "enable_rank_score_norm", "rank_score_norm_params_str"]:
      if key in self._config:
        ret.update(self.extract_dynamic_params(self._config[key]))
    if "cross_screen_items_from_attr" in self._config:
      ret.add(self._config["cross_screen_items_from_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_embedding_attr", "ranking_score_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    if ("cross_screen_variant" in self._config or "cross_screen_variant_v2" in self._config) \
        and "cross_screen_item_embedding_attr" in self._config \
        and "cross_screen_items_from_attr" in self._config:
      ret.add(gen_attr_name_with_common_attr_channel(self._config["cross_screen_item_embedding_attr"], self._config["cross_screen_items_from_attr"]))
    return ret


class SlideLeafDurationSegmentCutArranger(LeafArranger):
  def __init__(self, config: dict):
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_leaf_duration_segment_cut_filter"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    common_attr_set = set()
    common_attr_set.add(self._config.get("user_info_ptr_attr"))
    for attr in ["enable_duration_bucket_cut", "duration_bucket_cut_radio_str", "duration_bucket_seg_str", "skip_duration_fiter_exptag_set_str"]:
      common_attr_set.update(self.extract_dynamic_params(self._config.get(attr)))
    
    if "disable_filter_cutoff_attr_list" in self._config.keys() :
      for key, attr in self._config["disable_filter_cutoff_attr_list"].items():
        if attr not in common_attr_set:
          common_attr_set.add(attr)
    
    return common_attr_set

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    item_attr_set = set()
    item_attr_config = self._config.get("import_item_attr")
    for key, item_attr in item_attr_config.items():
      if item_attr not in item_attr_set:
        item_attr_set.add(item_attr)
    return item_attr_set

  @strict_types
  def _check_config(self) -> None:
    enable_duration_bucket_cut_attr = self._config.get("enable_duration_bucket_cut")
    check_arg(len(enable_duration_bucket_cut_attr) > 0, f"'enable_duration_bucket_cut' 不能配置为空")
    
    duration_bucket_cut_radio_str_attr = self._config.get("duration_bucket_cut_radio_str")
    check_arg(len(duration_bucket_cut_radio_str_attr) > 0, f"'duration_bucket_cut_radio_str' 不能配置为空")
    
    duration_bucket_seg_str_attr = self._config.get("duration_bucket_seg_str")
    check_arg(len(duration_bucket_seg_str_attr) > 0, f"'duration_bucket_seg_str' 不能配置为空")
