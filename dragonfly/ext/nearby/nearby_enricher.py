#!/usr/bin/env python3
# coding=utf-8

import operator
import itertools

from ...common_leaf_util import strict_types, check_arg, extract_common_attrs, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafEnricher
from ...common_leaf_processor import LeafArranger

class SMemberFromRedisAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "smember_common_attr_from_redis"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["redis_name", "key_value"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["output_value_list_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class NearbyEnsembleScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nearby_calc_ensemble_score"

  @strict_types
  def _check_config(self) -> None:
    formula_version = self._config.get("formula_version", 0)
    check_arg(formula_version in (0, 1, 2, 3, 4, 5), f"{self.get_type_alias()} 的 formula_version 配置只能为 0, 1, 2, 3, 4, 5")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    
    for key in ["smooth", "channel_sort_limit", "default_value", "score_molecule", "score_denominator"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    for channel in self._config["channels"]:
      for key in ["weight", "smooth_weight", "norm_type"]:
        attrs.update(self.extract_dynamic_params(channel.get(key)))
      if "norm_prefix" in channel.keys():
        attrs.add(channel.get("norm_prefix") + "_mean")
        attrs.add(channel.get("norm_prefix") + "_std")
        attrs.add(channel.get("norm_prefix") + "_dev")
        attrs.add(channel.get("norm_prefix") + "_min")
        attrs.add(channel.get("norm_prefix") + "_max")
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { x["name"] for x in self._config["channels"] }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    return attrs


class CalcLocationCodeAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_location_code_attr"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["common_lat_attr", "common_lon_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["output_common_adcode_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_lat_attr", "item_lon_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_item_adcode_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class NearbyRelationReasonArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "arranger_nearby_relation_reason"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()

    return attrs

class NearbyMapDistributeAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_map_distribute_attr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["lat_unit_count", "lon_unit_count"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["item_mask"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class CalcLocationAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_location_attr"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["common_lat_attr", "common_lon_attr", "common_ip_region_attr", "target_item_reason"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["output_common_region_id_attr", "output_common_province_id_attr", "output_common_city_id_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_lat_attr", "item_lon_attr", "item_ip_region_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_item_region_id_attr", "output_item_province_id_attr", "output_item_city_id_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret


class CalcItemCountAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_item_count_attr"

class NearbyGenUescoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nearby_gen_uescore"

class NearbyFilterAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_nearby_filter_attr"

class NearbyGenBoostCoeffEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_nearby_boost_coeff_attr"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["match_boost_coeff"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

class NearbySlideRerankScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_nearby_slide_rerank_score"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["enrich_live_realtime_perfer", "enrich_similar_top_score"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class NearbyWaterfallRerankScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_nearby_waterfall_rerank_score"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["enrich_live_realtime_perfer", "enrich_enrich_variant_attr", "enrich_enrich_auto_play_score", "enrich_fr_boost_coeff"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class NearbyMcPersonalVariantSortEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_nearby_mc_personal_variant_sort"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["category_sort_photo_num_bound", "category_sort_photo_num_bound"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class NearbyMcSortEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_nearby_mc_sort"
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["enable_hetu_prefer", "enable_photo_boost_coeff", "enable_live_boost_coeff", "enable_main_extra_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class NearbyPerfIndexCountEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nearby_perf_index_count_enricher"
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in []:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class NearbyFullRankEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_nearby_full_rank"
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in []:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class NearbyInitCommonAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_nearby_tag_attr"

class NearbyTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_nearby_trigger"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["similar_weight", "distance_weight", "time_weight", "infer_num", "enable_distance"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class NearbyTagAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_nearby_init_common_attr"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["use_hetu_tag_v1"]:
      ret.update(self.extract_dynamic_params(self._config.get(key)))
    return ret  
    
class NearbyLocalModelMixScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_nearby_mix_score_by_local_model"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["inference_thread_num", "mix_score_save_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    for key in ["model_path"]:
      ret.update(self.extract_dynamic_params(self._config.get(key)))
    return ret

class NearbySampleListCommonAttrEnricher(LeafEnricher):
  @strict_types
  def __init__(self, config: dict):
    if "save_attr_names_to_attr" not in config:
      config["save_attr_names_to_attr"] = self._SAMPLE_LIST_COMMON_ATTR_KEY
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_common_attr_by_sample_list"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set(self._config.get("include_attrs", []))
    attr = self._config.get("save_attr_names_to_attr", "")
    if attr:
      attrs.add(attr)
    return attrs

class GetNearbyFRXtrNormValueFromRedisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_nearby_fr_xtr_norm_value_from_redis"
  
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["output_redis_status"]:
      if key in self._config:
        ret.add(self._config[key])
    xtr_list = self._config.get("xtr_list_key",[])
    redis_prefix = self._config.get("redis_prefix_key",[])
    if len(redis_prefix) > 0 and len(xtr_list) > 0:
      for prefix in redis_prefix:
        for key in xtr_list:
          ret.add(prefix+'_'+key+'_mean')
          ret.add(prefix+'_'+key+'_std')
    return ret

class GetNearbyUserRetainInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_nearby_user_retain_info"
  
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["output_model_status"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class CalcItemXtrNormShiftEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_item_xtr_norm_shift"
    
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["product_prefix", "smooth_threshold", "status_str"]:
      ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["status_str"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    output_xtr_list = self._config.get("output_xtr_list",[])
    for key in output_xtr_list:
        attrs.add(key)
    return attrs 

class GetNearbyInnerBoostCoeffEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_get_nearby_inner_boost_coeff"

class NearbyItemLatLonNewEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nearby_item_lat_lon_new_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["item_lat_attr", "item_lon_attr", "item_lat_new_attr", "item_lon_new_attr"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["output_item_lat_attr", "output_item_lon_attr"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs
    
class NearbyDistanceFilterAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_nearby_distance_filter_attr"

class NearbyRecoStageSampleJoinEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nearby_fr_sample_join"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["kafka_topic"]:
      if key in self._config:
        ret.add(self._config[key])
    ret.update(self._config["slide_mc_clm_data"])
    return ret

class CalcHighPublishLabelEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_high_publish_label"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("high_publish_100_label")
    attrs.add("high_publish_50_label")
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("item_info.author_id")
    return attrs
