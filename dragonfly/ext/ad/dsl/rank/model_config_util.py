import os
import time
import json
import logging
import requests
from kconf.client import KConf
import copy
from os import path as osp

INVAILD_SET = {"template_pipeline_keys"}

INVAILD_ATTR = {
    "use_bs_fast_feature": False,
    "kai_model": False,
    "enableBlade": True,
    "enable_tvm": True,
}

# 支持服务相关配置
SUPPORT_SERVICE_CONFIG = {
    # 此处需考虑
    "INFER": {
        "CONFIG_SUFFIX": ".json",
        "DISCARD_KEY": {
            "btq_queue_name",
            "capacity_in_mem",
            "enable_idt_item",
            "enable_pb_cache",
            "do_not_store_btq_item",
            "enable_skip_parse_item",
            "feature_disable_nearby",
            "item_feature_cache_capacity_in_gb",
            "item_service_timeout",
            "only_read_incr_item",
            "emp_service",
            "dragon_feature_config",
        },
    },
    "FEATURE": {
        "CONFIG_SUFFIX": ".json",
        "DISCARD_KEY": {
            "emp_service",
            "btq_queue_name",
            "embedding_min_fill_ratio",
            "enable_explicit_mode",
            "enable_pb_cache",
            "kai_model",
            "output_op_names",
            "trt_max_workspace_size",
            "output_value_width",
            "use_double_buffer_loader",
            "enable_embedding_lookup_directly",
            "model_max_lag",
            "btq_read_shard",
            "predict_result_falcon",
            "enable_pb_cache",
            "max_batching_size",
            "compute_context_num",
            "batch_timeout_micros",
            "max_enqueued_batches",
            "infer_use_bs_version",
            "vector_op_names",
            "vector_value_nums",
            "use_trt_raw_topk",
            "reuse_trt_buffer_manager"
        },
    },
    "EMP": {
        "CONFIG_SUFFIX": "_emp.json",
        # 服务不需要的配置
        "DISCARD_KEY": {
            "pipeline_manager_config",
            "return_item_attrs",
            "request_type_config",
            "kess_config",
            "default_request_type",
            "transform_params",
            "grappler_transform_params",
            "ad_common_leaf_input",
            "_CONFIG_VERSION",
            "_DRAGONFLY_CREATE_TIME",
            "_DRAGONFLY_VERSION",
            "attrs_from_request",
            "apply_graph_transform",
            "use_bs_fast_feature",
            "ln_fusion",
        },
    },
}
# KCONF中忽略的配置, 将一些已经废弃的配置定义在这里
IGNORE_CONFIG_KEYS = {
    "feature_path", "model_path", "model_meta_path", "cmd_name", "feature_convert_file",
    "enable_infer_dragon_pipeline", "btq_incr_topic", "item_server_cmd", "ad_neixunhuan_filter",
    "apply_graph_transform", "attr_mapping", "emp_service", "component_item", "enable_explicit_mode",
    "enable_timing_cache", "enable_fixed_cache", "feature_process_config", "grappler_transform_params"
    "infer_disable_nearby", "item_shard_num", "ln_fusion", "need_parse_reco_user_info", "rollback_mode",
    "use_data_converter", "use_brpc", "use_gt9", "validator_check_by_minute", "validator_duration",
    "validator_max_roc", "use_component_item", "convert_in_local", "template_pipeline_keys",
    "stat_fea_value_mode", "transform_params", "grappler_transform_params", "output_value_width",
    "infer_disable_nearby", "item_feature_cache_capacity", "pb_cache_lru_capacity", "lru_capacity",
    "lru_capacity_gb", "disable_feature_consistency_config", "skip_embedding_vector_len_check",
    "log_to_kafka", "prune_bs_attr", "use_bs_reco_userinfo", "use_dist_item_service", "use_exist_fix_version",
    "use_opt_item_server", "use_simplified_raw_fea", "enable_dragon_pipeline", "enable_pb_cache",
    "enable_item_opt", "max_shared_mem_size_per_block", "max_shared_mem_size_per_block", "use_force_concat_opt",
    "feature_disable_nearby", "item_feature_cache_capacity_in_gb", "item_service_timeout",
    "enable_device_extending", "enable_pre_concat", "output_frozen_graph", "mio_embedding_update_qps"
}

PROJECT_SYNC_SERVER_ROOT = "/data/project/sync_server"


# 生成配置文件
def GenerateFile(output_file, service_type, config, kconf_path = ""):
    if service_type not in SUPPORT_SERVICE_CONFIG:
        EXIT_LOG(
            "不支持的服务类别: "
            + service_type
            + ", 目前支持的有: "
            + str(SUPPORT_SERVICE_CONFIG.keys())
        )
    # 集中式部署时
    enable_local_feature = service_type == "INFER" and config.get("enable_local_feature", False)
    if enable_local_feature:
        SUPPORT_SERVICE_CONFIG["INFER"]["DISCARD_KEY"] = SUPPORT_SERVICE_CONFIG["FEATURE"]["DISCARD_KEY"] \
                & SUPPORT_SERVICE_CONFIG["INFER"]["DISCARD_KEY"]
    new_config = config
    if "DISCARD_KEY" in SUPPORT_SERVICE_CONFIG[service_type]:
        new_config = {
            k: v
            for k, v in config.items()
            if k not in SUPPORT_SERVICE_CONFIG[service_type]["DISCARD_KEY"]
        }
    if kconf_path != "":
      CheckConfigWithKconf(service_type, config, kconf_path)
    with open(output_file, "w") as f:
        json.dump(new_config, f, indent=4)
    INFO_LOG("成功生成 " + service_type + " 配置: " + output_file)


def GenerateEMPFile(output_file, config, emp_model_key="model"):
    if "emp_service" not in config or config["emp_service"].get("emp_shard_count", 0) <= 0:
        return
    if emp_model_key not in config["deploy"]:
        EXIT_LOG("指定的 emp_model_key 不存在")
    emp_model_model_info = config["deploy"][emp_model_key]
    emp_model_model_info["ps_root"] = GetModelDirPath(emp_model_model_info["hdfs_root"])
    new_config = copy.deepcopy(config)
    new_config["deploy"] = emp_model_model_info
    new_config["model_meta_path"] = GetModelPathInfo(emp_model_model_info, "model_meta")
    # 顶层的 model_path 在回滚的时候需要
    new_config["model_path"] = GetModelPathInfo(emp_model_model_info, "model_path")
    GenerateFile(output_file, "EMP", new_config)


def GenerateDeployConfig(config, model_infos: dict):
    # 不存在 "hdfs_root" 时认为是多模型的两层级配置
    if "hdfs_root" in model_infos:
        model_infos = {"model": model_infos}
    required_keys = ["hdfs_root", "model_feature", "model_meta"]
    for model_key, model_info in model_infos.items():
        if not all(k in model_info for k in required_keys):
            EXIT_LOG("model_info 配置缺少必要字段: " + str(required_keys))
        # 补充其他部署所需字段
        model_infos[model_key]["model_dir_name"] = GetModelDirName(
            model_info["hdfs_root"]
        )
        model_infos[model_key]["model_path"] = "model_version"
    config["deploy"] = model_infos
    return model_infos


# 合并整体配置并生成配置文件
def MergeGenerateFile(
    btq_topic, output_file, service_type, tmp_file, config, model_infos, kconf_path
):
    dragon_json_config = {}
    with open(tmp_file, "r") as f:
        dragon_json_config = json.load(f)
    # 合并 DragonConfig 和模型 Config
    SetCustomConfig(dragon_json_config, config)
    # 检查处理 deploy 信息
    model_infos = GenerateDeployConfig(config, model_infos)
    GenerateFile(output_file, service_type, config, kconf_path)

    for model_key, value in model_infos.items():
        assert isinstance(value, dict), "model_infos item should be dict"
        emp_file = "_".join([osp.splitext(output_file)[0], model_key, "emp.json"])
        GenerateEMPFile(emp_file, config, model_key)

def _get_kconf_value(kconf_path):
    try:
        kc = KConf(kconf_path)
        val = kc.get_value()
        return val, kc
    except Exception as ex:
        EXIT_LOG(
            "Kconf获取错误, Path: "
            + kconf_path
            + ", 检查Kconf路径以及是否可以访问内部网络"
        )
        return None, None

# 废弃, 不再从 KCONF 中更新
def UpdateConfigWithKconf(config, kconf_path):
    if kconf_path == "":
        EXIT_LOG("需传入KCONF路径")
    kconf_val, _ = _get_kconf_value(kconf_path)
    if kconf_val is None:
        return config
    kconf_content = json.loads(kconf_val)
    for key, value in kconf_content.items():
        if key in IGNORE_CONFIG_KEYS:
            INFO_LOG(f"ignore key: {key} in kconf")
            continue
        if key not in config:
            WARNING_LOG(f"update key: {key} with kconf value: {value}")
            config[key] = value
            continue
        if value != config[key]:
            WARNING_LOG(f"update config with kconf, key: {key}, value: {value}")
            config[key] = value
    INFO_LOG(f"update config with kconf success.")
    return config

def CheckConfigWithKconf(service_type, config, kconf_path):
    if kconf_path == "":
        EXIT_LOG("需传入KCONF路径")
    kconf_val, _ = _get_kconf_value(kconf_path)
    if kconf_val is None:
        return False
    kconf_content = json.loads(kconf_val)
    enable_local_feature = service_type == "INFER" and config.get("enable_local_feature", False)
    for key, value in kconf_content.items():
        if key in IGNORE_CONFIG_KEYS:
            continue
        if enable_local_feature:
            # 本地 infer 模式 infer server 同样需要 feature 配置
            if key in SUPPORT_SERVICE_CONFIG["FEATURE"]["DISCARD_KEY"] and \
               key in SUPPORT_SERVICE_CONFIG["INFER"]["DISCARD_KEY"]:
               continue
        elif key in SUPPORT_SERVICE_CONFIG[service_type]["DISCARD_KEY"]:
            continue
        if key not in config:
            WARNING_LOG(f"KCONF DIFF 检查, JSON缺少配置:【{key}】, KCONF: {value}")
            continue
        if value != config[key]:
            WARNING_LOG(f"KCONF DIFF 检查, 配置校验不一致:【{key}】, JSON: {config[key]}, KCONF: {value}")
    return True


class Color:
    Red = "\033[31m"
    Green = "\033[32m"
    Yellow = "\033[93m"
    Reset = "\033[0m"


def INFO_LOG(log):
    print(f"{Color.Green}[info]{Color.Reset}: " + log)


def EXIT_LOG(log):
    print(
        f"{Color.Red}[exit]{Color.Red}: " + log + " 请修复问题, 有疑问联系老铁群Oncall"
    )
    exit("发生错误终止程序")


def WARNING_LOG(log):
    print(f"{Color.Yellow}[warn]{Color.Reset}: " + log)


def GetJsonConfig(config_file):
    if not os.path.exists(config_file):
        EXIT_LOG("配置文件不存在: " + config_file)
    with open(config_file) as fp:
        config = json.load(fp)
        return config

def GetFileFromHDFS(hdfs_root, hdfs_file_name):
    hdfs_url = "http://webhdfs-offline-lt.corp.kuaishou.com/webhdfs/v1"
    hdfs_path = osp.join(hdfs_root, hdfs_file_name)
    web_url = "%s%s?op=OPEN&user.name=ad" % (hdfs_url, hdfs_path)
    retry_times = 5
    content = None
    while retry_times > 0:
        res = requests.get(web_url, timeout=5)
        if res.status_code != requests.codes.ok:
            retry_times -= 1
            WARNING_LOG(f"下载 {hdfs_path} 失败, 剩余重试次数: {retry_times}")
            continue
        content = res.content
        break
    if content:
        INFO_LOG("下载 %s 成功" % hdfs_path)
    else:
        EXIT_LOG("下载 %s 失败, 请检查文件是否存在" % hdfs_path)
    return content

def GetDragonFeatureConfig(model_config, model_info):
    # dragon 特征只支持单模型
    if ("hdfs_root" not in model_info) or( "dragon_feature_config" not in model_info):
        return
    hdfs_root = model_info["hdfs_root"]
    dragon_feature_config_name = model_info["dragon_feature_config"]
    dragon_feature_config = None
    try:
        dragon_feature_config = GetFileFromHDFS(hdfs_root, dragon_feature_config_name)
    except Exception as ex:
        EXIT_LOG(f"获取 dragon_feature_config 失败, 错误: {ex}")
    if dragon_feature_config:
        model_config["dragon_feature_config"] = json.loads(dragon_feature_config)
        INFO_LOG(f"成功获取 dragon_feature_config: {dragon_feature_config_name}")


# 检查配置是否冲突或合规
def SetCustomConfig(dragon_json_config, model_config):
    for key in dragon_json_config.keys():
        if (key in INVAILD_SET) or (
            key in INVAILD_ATTR and dragon_json_config[key] == INVAILD_ATTR[key]
        ):
            EXIT_LOG(
                "不支持此定制配置, KEY [%s], 定制: %s\n"
                % (key, dragon_json_config[key])
            )
        if key in model_config.keys():
            if model_config[key] != dragon_json_config[key]:
                WARNING_LOG(
                    "定制配置和系统默认配置冲突, KEY [%s], 默认: %s, 定制: %s, 使用定制配置\n"
                    % (key, model_config[key], dragon_json_config[key])
                )
                model_config[key] = dragon_json_config[key]
        else:
            model_config[key] = dragon_json_config[key]
            #INFO_LOG("定制配置填充: %s, value: %s" % (key, dragon_json_config[key]))
    INFO_LOG("成功 Merge Dragon配置与模型配置")


def GetModelDirName(hdfs_root: str) -> str:
    """
    根据hdfs_root生成模型相关文件下载目录名

    hdfs_root: 模型文件hdfs目录 例: "/home/<USER>/big_model_rollback/dsp_conf_ctr"
    return: 下载目标目录名      例: "home_ad_big_model_rollback_dsp_conf_ctr"
    """
    target_dir_name = hdfs_root.rstrip("/").replace(osp.sep, "_")[1:]
    return target_dir_name


def GetModelDirPath(hdfs_root: str, file_name: str = "") -> str:
    """
    根据hdfs_root得到该模型相关文件下载目录全路径, 如果file_name不为空, 则返回路径加上文件名

    Args:
        hdfs_root (str): 模型文件hdfs目录 例: "/home/<USER>/big_model_rollback/dsp_conf_ctr"
        file_name (str, optional): 下载目标目录名      例: "/data/project/sync_server/home_ad_big_model_rollback_dsp_conf_ctr"

    Returns:
        str: _description_
    """
    model_dir_path = osp.join(PROJECT_SYNC_SERVER_ROOT, GetModelDirName(hdfs_root))
    if file_name:
        model_dir_path = osp.join(model_dir_path, file_name)
    return model_dir_path


def GetModelPathInfo(model_deploy_info: dict, filed: str) -> str:
    """
    从 model_deploy_info 中获取指定字段并生成真实路径
    """
    supported_fileds = ["model_feature", "model_meta", "model_path"]
    assert filed in supported_fileds, "filed must be in %s" % supported_fileds
    assert "hdfs_root" in model_deploy_info, "hdfs_root must be in model_deploy_info"
    if filed == "model_path":  # model_path="model_version" 都是同样的
        model_deploy_info["model_path"] = "model_version"
    assert filed in model_deploy_info, "filed must be in model_deploy_info"
    model_dir_name = GetModelDirName(model_deploy_info["hdfs_root"])
    return osp.join(PROJECT_SYNC_SERVER_ROOT, model_dir_name, model_deploy_info[filed])


# 生成 Feature 默认配置
def GetFeatureDefaultConfig(model_info):
    FEATURE_DEFAULT_CONFIG = {
        # "return_item_attrs": ["predict_value", "result_stat"],
        "model_type": "infer_service_tf",
        "remove_sign_prefix": True,
        "model_src": "btq",
        "use_idt": True,
        "use_bs_fast_feature": True,
        "enable_skip_parse_item": True,
        "do_not_store_btq_item": True,
        "only_read_incr_item": True,
        "capacity_in_mem": True,
        "enable_idt_item": True,
        "use_common_idt_service": True,
        # 以下是 Moss 没有自动生成的配置, 此处默认加上
        "add_default_only_miss": False,
        "remap_slot_sign_feature": {
            "ad_sign_prefix_bit_num": 12,
            "output_slot_bit_num": 12,
            "reco_gsu_sign_prefix_bit_num": 10,
            "reco_sign_prefix_bit_num": 16,
        },
        "zero_miss_sign": True,
    }
    # 获取 dragon feature 配置
    GetDragonFeatureConfig(FEATURE_DEFAULT_CONFIG, model_info)
    return FEATURE_DEFAULT_CONFIG


# 生成 Infer 默认配置
def GetInferDefaultConfig(model_info):
    INFER_DEFAULT_CONFIG = {
        "use_idt": True,
        "model_src": "btq",
        "btq_read_shard": True,
        "remap_slot_sign_feature": {
            "ad_sign_prefix_bit_num": 12,
            "output_slot_bit_num": 12,
            "reco_gsu_sign_prefix_bit_num": 10,
            "reco_sign_prefix_bit_num": 16,
        },
        "btq_model_read_qps": 1000,
        "btq_model_read_thread_num": 10,
        "add_default_only_miss": False,
        "use_bs_fast_feature": True,
        "enable_embedding_lookup_directly":True,
        "predict_result_falcon": {"enable": True, "replace_falcon": True},
        "enable_validator": True,
        "remove_sign_prefix": True,
        "use_common_idt_service": True,
        "zero_miss_sign": True,
        "ctx_per_gpu_device": 8,
        "model_type": "infer_service_tf",
        "zero_miss_sign": True,
    }
    # 获取 dragon feature 配置
    GetDragonFeatureConfig(INFER_DEFAULT_CONFIG, model_info)
    if "model_btq_topic" in model_info:
      GetEmpConfig(model_info["model_btq_topic"], INFER_DEFAULT_CONFIG)
    return INFER_DEFAULT_CONFIG


def SelectItemServerCMD(product_line, product_group=""):
    """
    根据产品线选择增量物料服务CMD

    Args:
        product_line (str): 'DSP' 或 'UNIVERSE' 或 'SEARCH'
        product_group (str, optional): 'OUT' 或 'INNER', product_line in ['UNIVERSE', 'SEARCH'] 时不需要
    Returns:
        str: ItemServer CMD
    """
    if product_line == "DSP" and product_group == "OUT":
        return "/ad/ps/item:extrinsic_cycle_item_batch_sample_4_shards"
    if product_line == "DSP" and product_group == "INNER":
        return "/ad/ps/item:ad_predict_batched_samples_v2_2_shards"
    if product_line == "UNIVERSE" and not product_group:
        return "/ad/ps/universe:union_item_creative_service_bs_4_shards"
    if product_line == "SEARCH" and not product_group:
        return "/ad/ps/item:ad_predict_search_creative_item_online_bs_4_shards"
    EXIT_LOG("请正确设置 product_line, product_group")


def SelectItemIncrBtq(product_line: str, product_group: str = ""):
    """
    根据产品线选择增量物料btq

    Args:
        product_line (str): 'DSP' 或 'UNIVERSE' 或 'SEARCH'
        product_group (str, optional): 'OUT' 或 'INNER', product_line in ['UNIVERSE', 'SEARCH'] 时不需要
    Returns:
        str: btq topic
    """
    if product_line == "DSP" and product_group == "OUT":
        return "ad_predict_extrinsic_cycle_creative_item_id_incr"
    if product_line == "DSP" and product_group == "INNER":
        return "ad_predict_item_info_v2_id_incr"
    if product_line == "UNIVERSE" and not product_group:
        return "ad_predict_union_item_creative_id_incr"
    if product_line == "SEARCH" and not product_group:
        return "ad_predict_search_creative_item_id_incr"
    EXIT_LOG("请正确设置 product_line, product_group")


# 获取EMP Shard 配置
def GetEmpConfig(btq_topic, model_config):
    emp_shard_json, _ = _get_kconf_value("ad.adPsModelEmpShards." + btq_topic)
    if emp_shard_json is None:
        EXIT_LOG(
            "无法从Kconf路径获取Embedding配置, 检查路径是否存在 & 环境与KCONF连通性: ad.adPsModelEmpShards."
            + btq_topic
        )
    model_config["emp_service"] = json.loads(emp_shard_json)
    return model_config
