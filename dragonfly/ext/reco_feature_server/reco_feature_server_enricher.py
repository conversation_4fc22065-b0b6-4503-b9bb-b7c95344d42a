#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher


class KuibaParameterDiscreteSimplified<PERSON><PERSON><PERSON>er(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_kuiba_discrete_simplified"

  @property
  @strict_types
  def need_preceding_output_info(self) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    check_arg(len(self._config.get("slots_output", "")) > 0, "slots_output 为必配项")
    check_arg(len(self._config.get("parameters_output", "")) > 0, "parameters_output 为必配项")
    check_arg(isinstance(self._config.get("config", None), dict), "config 需为 dict")
    config_dict = self._config.get("config", {})
    check_arg(len(config_dict) > 0, "config 未配置抽取特征")
    for unique_key, extract_conf in config_dict.items():
      check_arg(isinstance(extract_conf, dict), f"{unique_key} 格式应为：{{string: dict}}")
      check_arg("attrs" in extract_conf.keys(), f"{unique_key} 抽取配置缺少 `attrs`")
      attrs = extract_conf["attrs"]
      check_arg(isinstance(attrs, list), f"{unique_key}.attrs 应配置为 list")
      check_arg(len(attrs) == 1, f"{unique_key}.attrs 应仅配置一个有效抽取配置")
      attr_conf = attrs[0]
      check_arg(isinstance(attr_conf, dict), f"{unique_key}.attrs[0] 应配置为 dict")
      for field in ["attr", "attr_type", "key_type", "converter_args"]:
        check_arg(field in attr_conf.keys(), f"{unique_key}.attrs[0] 缺少配置: {field}")
      check_arg(isinstance(attr_conf["attr"], list) and len(attr_conf["attr"]) == 1 and \
                isinstance(attr_conf["attr"][0], str) and len(attr_conf["attr"][0]) > 0, \
                f"{unique_key}.attrs[0].attr 应配置仅含一个 string 的 list")
      check_arg(isinstance(attr_conf["attr_type"], list) and len(attr_conf["attr_type"]) == 1 and \
                isinstance(attr_conf["attr_type"][0], str) and attr_conf["attr_type"][0] in ["int64", "float64"], \
                f"{unique_key}.attrs[0].attr_type 应配置 ['int64'] 或者 ['float64]'")
      check_arg(isinstance(attr_conf["key_type"], int), f"{unique_key}.attrs[0].key_type 应配置正整数")
      check_arg(isinstance(attr_conf["converter_args"], str), f"{unique_key}.attrs[0].converter_args 应配置字符串")

  @strict_types
  def _get_output_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["slots_output"])
    attrs.add(self._config["parameters_output"])
    return attrs

  @strict_types
  def _get_input_attrs(self, common) -> set:
    ret = set()
    for conf in self._config["config"].values():
      attrs = list()
      for attr in conf["attrs"]:
        attrs += attr["attr"]

      if common is None:
        ret.update(attrs)
      else:
        for attr in attrs:
          if attr in self.preceding_output_item_attrs:
            if not common:
              ret.add(attr)
          elif attr in self.preceding_output_common_attrs:
            if common:
              ret.add(attr)
          else:
            ret.add(attr)
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if not self._config.get("is_common_attr", False):
      return self._get_output_attrs()
    else:
      return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config.get("is_common_attr", False):
      return self._get_output_attrs()
    else:
      return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if not self._config.get("is_common_attr", False):
      # item part
      return self._get_input_attrs(common=False)
    else:
      return set()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    if self._config.get("is_common_attr", False):
      # all input is common attr
      return self._get_input_attrs(common=None)
    else:
      # common part
      return self._get_input_attrs(common=True)


class UnpackColossusUserAuthorStatisticAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "unpack_colossus_user_author_statistic_attr"

  @strict_types
  def _check_config(self) -> None:
    check_arg(len(self._config.get("import_item_attr", "")) > 0, "import_item_attr 为必配项")
    check_arg(isinstance(self._config.get("export_attr_suffix_list", None), list), "export_attr_suffix_list 需为 list")
    export_attr_suffix_list = self._config.get("export_attr_suffix_list", [])
    check_arg(len(export_attr_suffix_list) > 0, "export_attr_suffix_list 未配置输出 attr 后缀")
    for suffix in export_attr_suffix_list:
      check_arg(isinstance(suffix, str), f"suffix 应配置 string")
      check_arg(len(suffix) > 0, f"suffix 应配置非空 string")

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attr_prefix = self._config["import_item_attr"]
    for suffix in self._config["export_attr_suffix_list"]:
      attr_name = f"{attr_prefix}_{suffix}"
      attrs.add(attr_name)
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["import_item_attr"])
    return attrs


class RoundingHighestDigitSearchFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "rounding_higest_digit_search_feature"

  def _check_str_list_conf(self, config_name) -> None:
    check_arg(isinstance(self._config.get(config_name, None), list), f"{config_name} 需为 list")
    item_list = self._config.get(config_name, [])
    check_arg(len(item_list) > 0, f"{config_name} 需配置非空列表")
    for item in item_list:
      check_arg(isinstance(item, str), f"{config_name} 成员应配置 string")
      check_arg(len(item) > 0, f"{config_name} 成员应配置非空 string")

  @strict_types
  def _check_config(self) -> None:
    check_arg(len(self._config.get("import_item_attr", "")) > 0, "import_item_attr 为必配项")
    self._check_str_list_conf("export_attr_prefix_list")
    self._check_str_list_conf("export_attr_suffix_list")

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attr_prefix_list = self._config["export_attr_prefix_list"]
    attr_suffix_list = self._config["export_attr_suffix_list"]
    for suffix in attr_suffix_list:
      for prefix in attr_prefix_list:
        attr_name = f"{prefix}_{suffix}"
        attrs.add(attr_name)
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["import_item_attr"])
    return attrs

class GenerateShuffledIndiceEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gen_shuffled_indice"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["attr_name"] }
