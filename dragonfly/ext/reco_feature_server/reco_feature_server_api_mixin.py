#!/usr/bin/env python3
# coding=utf-8

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .reco_feature_server_enricher import *
from .reco_feature_server_observer import *


class RecoFeatureServerMixin(CommonLeafBaseMixin):
  def extract_kuiba_discrete_simplified(self, **kwargs):
    """
    KuibaParameterDiscreteSimplifiedEnricher
    ------
    抽取离散化特征放到 attr 里（如果是 common 则放到 common attr, 否则放到 item attr）, 针对 kuiba discrete 算子简化实现提高抽取效率

    参数配置
    ------
    `slots_output`: [string] 输出 slots 的位置

    `parameters_output`: [string] 输出 parameters 的位置

    `config`: [dict] discrete 抽取配置，详见示例。kuiba discrete 算子，当前仅处理 attrs[0].attr[0] 数据，兼容原配置格式，保留数组配置形式
      - `slotid_999`: 自定义名称，用于去重判断的标识

    `is_common_attr`: [bool] 是否仅从 common attr 中抽取，默认为 false

    调用示例
    ------
    ``` python
    .extract_kuiba_discrete_simplified(
      slots_output="slots",
      parameters_output="parameters",
      is_common_attr=False,
      config={
        "slotid_999": {
          "attrs": [
            {
              "attr": ["pctr"],
              "attr_type": ["float64"],
              "key_type": 999,
              "converter_args": "0.25,0,4,10000,0",
            }
          ]
        }
      }
    )
    ```
    """
    self._add_processor(KuibaParameterDiscreteSimplifiedEnricher(kwargs))
    return self

  def perflog_slot_sign(self, **kwargs):
    """
    RecoFeatureSlotPerflogObserver
    ------
    将抽取到attr的特征上报到perlflog

    参数配置
    ------
    `common_slots`: [list] 选配项，指定要上报的 common_slots 列表，不填则不上报

    `common_signs`: [list] 选配项，指定要上报的 common_signs 列表，不填则不上报

    common_slots和common_signs 必须同时配置或不配置，且每一项都必须一一对应

    `item_slots`: [list] 选配项，指定要上报的 item_slots 列表，不填则不上报

    `item_signs`: [list] 选配项，指定要上报的 common_signs 列表，不填则不上报

    item_slots和item_signs 必须同时配置或不配置，且每一项都必须一一对应

    `prob`: [double] [动态参数] 选配项，对每个item进行抽样的概率，取值范围为(0.0, 1.0]，默认为0.1

    调用示例
    ------
    ``` python
    .perflog_slot_sign(
      common_slots = ["common_slots_1", "common_slots_2"],
      common_signs = ["common_param_1", "common_param_2"],
      item_slots = ["item_slots_1", "item_slots_2"],
      item_signs = ["item_param_1", "item_param_2"],
      prob = 0.3
    )
    ```
    """
    self._add_processor(RecoFeatureSlotPerflogObserver(kwargs))
    return self

  def unpack_colossus_user_author_statistic_attr(self, **kwargs):
    """
    UnpackColossusUserAuthorStatisticAttrEnricher
    ------
    基于 colossus_user_author_cross_static_resp 算子输出结果（比如：u_author_play_cnt_attr）, 按照 int_list 的下标位置顺序并经过计算后输出到新的 attr，通过 export_attr_suffix_list 指定输出的 int item_attr 后缀。注意：算子依赖输入的 search_feature 数据分布和个数，需要严格对齐

    举例：import_item_attr="author_play_cnt", export_attr_suffix_list=["1h", "6h", ...], 生成 int item_attr: author_play_cnt_1h, author_play_cnt_6h, ...

    说明：import_item_attr 不存在，或者对应 int_list 长度与 export_attr_suffix_list 不匹配，生成的 attr 默认值填充 0

    参数配置
    ------
    `import_item_attr`: [string] colossus_user_author_cross_static_resp 算子输出的 int_list item_attr_name

    `export_attr_suffix_list`: [string list] 输出的 item_attr 后缀列表

    调用示例
    ------
    ``` python
    .unpack_colossus_user_author_statistic_attr(
      import_item_attr="author_play_cnt",
      export_attr_suffix_list=["1h", "6h", "12h", "1d", "7d", "30d", "long"],
    )
    ```
    """
    self._add_processor(UnpackColossusUserAuthorStatisticAttrEnricher(kwargs))
    return self

  def rounding_higest_digit_search_feature(self, **kwargs):
    """
    RoundingHighestDigitSearchFeatureEnricher
    ------
    基于 searchPageFeatureServer 服务获取的 search_feature, 按照 int_list 的下标位置顺序并经过计算（十进制数最高位并舍弃尾数）后输出到新的 attr，通过 export_attr_prefix_list & export_attr_suffix_list 组合输出 int item_attr。注意：算子依赖输入的 search_feature 数据分布和个数，需要严格对齐

    举例：export_attr_prefix_list=["q_s_click", "q_s_r_click_PV"], export_attr_suffix_list=["1d", "7d"], 生成 int item_attr: q_s_click_1d, q_s_r_click_PV_1d, q_s_click_7d, q_s_r_click_PV_7d。注意遍历生成的顺序

    说明：import_item_attr 不存在，所有 export attr 默认填充 0；import_item_attr list 长度小于 export_attr 个数，超出部分的 attr 默认填充 0

    参数配置
    ------
    `import_item_attr`: [string] searchPageFeatureServer 服务获取的 search_feature attr

    `export_attr_prefix_list`: [string list] 输出的 item_attr 前缀列表

    `export_attr_suffix_list`: [string list] 输出的 item_attr 后缀列表

    调用示例
    ------
    ``` python
    .rounding_higest_digit_search_feature(
      import_item_attr="search_feature_result",
      export_attr_prefix_list=["q_s_click", "q_s_r_click_PV"],
      export_attr_suffix_list=["1d", "7d"],
    )
    ```
    """
    self._add_processor(RoundingHighestDigitSearchFeatureEnricher(kwargs))
    return self

  def gen_shuffled_indice(self, **kwargs):
    """
    GenerateShuffledIndiceEnricher

    不打乱候选集，给 item 设置一个乱序的 indice，取值范围：[0, 候选集个数), 配合 select_item 实现无放回随机挑选 N 个 item 的功能。举例：候选集一共 5 个 item，生成新的 indice item_attr，候选集对应取值可能为：[1,4,3,0,2]

    参数配置
    ------
    `attr_name`: [string] 待设置的 item_attr 名称

    调用示例
    ------
    ``` python
    .gen_shuffled_indice(
      attr_name = "shuffled_indice_attr",
    )
    ```
    """
    self._add_processor(GenerateShuffledIndiceEnricher(kwargs))
    return self
