#!/usr/bin/env python3
# coding=utf-8
from ...common_leaf_processor import LeafObserver
from ...common_leaf_util import strict_types, extract_attr_names


class FollowLeafTimeDistributionPerflogObserver(LeafObserver):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_time_distribution_perflog"

    @strict_types
    def depend_on_items(self) -> bool:
        return bool(self.input_item_attrs)

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("timestamp_attr_name"))
        attrs.add(self._config.get("item_type"))
        return attrs 

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("time_distribution_setting"))
        attrs.add(self._config.get("perf_sample"))
        return attrs
    
class FollowLeafItemTypePerflogObserver(LeafObserver):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_item_type_perflog"

    @strict_types
    def depend_on_items(self) -> bool:
        return bool(self.input_item_attrs)

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("item_type"))
        attrs.add(self._config.get("exp_tag"))
        return attrs 

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("perf_sample"))
        attrs.add(self._config.get("tag_name"))
        attrs.add("is_nebula_user")
        return attrs

class FollowMixBizTypePerflogObserver(LeafObserver):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_biz_type_perflog"

    @strict_types
    def depend_on_items(self) -> bool:
        return bool(self.input_item_attrs)

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("biz_type_list_name"))
        attrs.add(self._config.get("biz_type_name"))
        attrs.add("item_type")
        return attrs 

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("perf_sample"))
        attrs.add(self._config.get("perf_size"))
        attrs.add(self._config.get("tag_name"))
        attrs.add("mix_perf_type_str")
        return attrs
    
class FollowLeafItemReasonPerflogObserver(LeafObserver):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_item_reason_perflog"

    @strict_types
    def depend_on_items(self) -> bool:
        return bool(self.input_item_attrs)

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("item_type"))
        return attrs 

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("perf_sample"))
        attrs.update(self.extract_dynamic_params(self._config.get("exp_group_name")))
        return attrs


class FollowFeaturePerfObserver(LeafObserver):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_feature_perf_observer"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = extract_attr_names(self._config.get("item_attrs", []), "name")
        return attrs 

class FollowMixFinalPerflogObserver(LeafObserver):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_final_perflog"

    @strict_types
    def depend_on_items(self) -> bool:
        return bool(self.input_item_attrs)

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("biz_type")
        attrs.add("item_type")
        attrs.add("pVideoDurationMs")
        attrs.add("biz_type_list")
        attrs.add("ad_cpm")
        return attrs 

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("perf_sample"))
        attrs.add(self._config.get("tag_name"))
        attrs.add(self._config.get("product_type"))
        attrs.add("page_size")
        attrs.add("uIsTopFresh")
        attrs.add("is_nebula_user")
        return attrs

class FollowRankFinalPerflogObserver(LeafObserver):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_rank_final_perflog"

    @strict_types
    def depend_on_items(self) -> bool:
        return bool(self.input_item_attrs)

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("is_support_live")
        attrs.add("is_random_support")
        attrs.add("item_type")
        return attrs 

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("perf_sample"))
        attrs.add(self._config.get("tag_name"))
        attrs.add("is_nebula_user")
        return attrs

class FollowItemCountPerflogObserver(LeafObserver):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_item_count_perflog"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("aIsUnfollow")
        attrs.add("item_type")
        return attrs 

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("tag_name"))
        attrs.add("product_type")
        attrs.add("is_simple_live_entry")
        return attrs
    
class FollowRankHitratePerflogObserver(LeafObserver):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_rank_hitrate_perflog"
    
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("follow_fr_l2r_top_item_list")
        attrs.add("follow_fr_final_top_item_list")
        attrs.add("follow_fr_pctr_top_item_list")
        attrs.add("follow_fr_pltr_top_item_list")
        attrs.add("follow_fr_pwt_top_item_list")
        attrs.add("follow_fr_pfinish_top_item_list")
        attrs.add("follow_fr_plvtr_top_item_list")
        attrs.update(self.extract_dynamic_params(self._config.get("hitrate_exp_tag")))
        return attrs