#!/usr/bin/env python3
# coding=utf-8
from ...common_leaf_processor import LeafRetriever, try_add_table_name
from ...common_leaf_util import strict_types


class FollowLeafExtendRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_retrieve_by_extend"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("follow_list")
        attrs.add(self._config.get("user_info_path"))
        attrs.add(self._config.get("retrieval_reason"))
        attrs.add(self._config.get("enable_last_feed_retrieval"))
        attrs.add(self._config.get("enable_old_feed_list_retrieval"))
        attrs.add("FollowContextAttrKey")
        attrs.add("diagnosis")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("extend_photo_ids")
        attrs.add("score_index")
        attrs.add("query_index")
        return attrs

class FollowContextInitRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_context_init"

    @strict_types
    def is_async(self) -> bool:
        return False
    
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_path"))
        attrs.add("user_info_str")
        attrs.add("action_list_set")
        attrs.add("fine_browsed_set")
        attrs.add("global_action_list_set")
        attrs.add("fill_feasury_neg_author_action_strs_outside")
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add("FollowContextAttrKey")
        return attrs

class FollowContextResetRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_context_retriever"

    @strict_types
    def is_async(self) -> bool:
        return False

class FollowLeafLocalCacheRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_retrieve_by_local_cache"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("follow_list_attr"))
        attrs.add(self._config.get("retrieval_reason"))
        attrs.add(self._config.get("cluster_index_to_attr"))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_retrieval_cache")))
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("miss_cache_follow_list_attr"))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("cache_query_index_to_attr"))
        attrs.add(self._config.get("score_index_to_attr"))
        return attrs

class FollowLeafWireGuestRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_retrieve_by_wire_guest"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("retrieval_reason"))
        attrs.update(self.extract_dynamic_params(self._config.get("follow_list")))
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        if "wire_guest_list_attr" in self._config.keys():
            attrs.add(self._config.get("wire_guest_list_attr"))
        return attrs

class FollowQueryRemoteRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "follow_retrieve_by_remote_index"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("search_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("batch_limit")))
    attrs.update(self.extract_dynamic_params(self._config.get("query_total_limit")))
    attrs.add(self._config.get("query_list_attr_name"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["save_score_to_attr", "save_query_index_to_attr"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs


class FollowLeafAdAthenaRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "follow_retrieve_by_ad_athena"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = {"ad_athena_batch_waiter", "ad_response_ptr", "ad_request_ptr"}
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = {"ad_info_str"}
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["save_exp_tags_to_attr", "save_ad_dsp_to_attr"]:
      if key in self._config:
        attrs.add(self._config[key])
    attrs.add("ad_creative_id")
    return attrs

class FollowMixGenSeqListRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_gen_seq_list_retrieve"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.update(self.extract_dynamic_params(self._config.get("follow_mix_rank_seq_length")))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("item_list")
        return attrs

class FollowMixSelectFinalSeqRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_select_final_seq_retrieve"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("enable_follow_mix_model")
        attrs.add("mix_seq_model_final_key")
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        table_attrs = set()
        table_attrs.add("item_list")
        attrs = set()
        attrs.update(try_add_table_name(self._config.get("seq_results_table", ""), table_attrs))
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add("results_key_reason_map_ptr")
        return attrs

class FollowLeafRedisCacheRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_redis_cache_retrieve"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attr_names = self._config.get("attr_names")
        for attr_name in attr_names:
            attrs.add(attr_name)
        return attrs

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("follow_cache"))
        return attrs

class FollowPackResultsRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_pack_results_retrieve"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("results_key_reason_map_ptr")
        return attrs

