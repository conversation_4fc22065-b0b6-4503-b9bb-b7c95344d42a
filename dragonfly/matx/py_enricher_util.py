#!/usr/bin/env python3
# coding=utf-8
"""
- filename: py_enricher_util.py
- description: matx py enricher util
- author: <EMAIL>
- date: 2025-02-26 16:55:00
"""

import ast
from collections import defaultdict
import datetime
import hashlib
import inspect
import multiprocessing
import os
import pickle
import sys
import types
from .install_util import print_info, install_python_package

class PyUdfCompileUtil:
  # 编译开关
  ENABLE_PY_UDF = os.environ.get('DRAGON_MATX_ENABLE', 'true').lower() == "true"
  # 静态部署变量
  ENABLE_STATIC_COMPILE: bool = os.environ.get('DRAGON_MATX_DYNAMIC_DEPLOY', 'true').lower() == "false"
  # 多进程编译
  PARALLEL_COMPILE: bool = os.environ.get("DRAGON_MATX_PARALLEL_COMPILE", 'true').lower() == "true"
  # 远程 compile
  ENABLE_REMOTE_COMPILE: bool = os.environ.get("DRAGON_MATX_REMOTE_COMPILE", 'false').lower() == "true"
  # 远程 so 上传与下载
  USE_REMOTE_DSO: bool = os.environ.get('DRAGON_MATX_USE_REMOTE_DSO', 'false').lower() == 'true'
  # dso 目录
  DSO_PATH: str = os.environ.get('DRAGON_MATX_OUPUT_PATH', os.path.join(os.getcwd(), 'dso'))
  # 缓存文件
  CACHE_FILE_PATH = os.path.join(DSO_PATH, 'cache.pickle')
  # 编译选项
  COMPILE_OPTIONS: list = [
    "-std=c++14",
    "-O3",
    "-fdiagnostics-color=always",
    "-Werror=return-type",
    "-D_GLIBCXX_USE_CXX11_ABI=1",
    "-fvisibility=hidden",
    "-g",
    "-fdebug-prefix-map=$PWD=."
  ]
  # 不需要编译的类
  NO_NEED_COMPILE_SET: set = {'DragonflyContext', 'ItemAttrGetter', 'ItemAttrSetter', 'DragonflyUtil', 'PtrWrapper',
                              'Uint64FastSet', 'DoubleFastSet', 'StringFastSet',
                              "Uint64Uint64FastMap", "Uint64DoubleFastMap", "Uint64StringFastMap",
                              "StringUint64FastMap", "StringDoubleFastMap", "StringStringFastMap"}
  # 使用的编译工具，[gcc, clang, both] 之一
  PY_COMPILE_TOOL: list = []
  # 本地编译 g++ 路径
  PY_GCC_TOOL_PATH: str = ''
  # 本地编译 clang++ 路径
  PY_CLANG_TOOL_PATH: str = ''

  # 待编译的类与相关信息
  py_compile_class: dict = {}
  # 待编译的函数与相关信息
  py_compile_functions: dict = {}
  # 待编译的函数与相关信息
  py_import: set = set()

  # cache 内容
  _py_compile_cache: dict = {}
  # 远程 compile server url
  _py_compile_server_url: str = ''
  # 是否需要编译是否已经检查过
  _py_need_compile_checked = False
  

  @classmethod
  def check_and_add_compile_class(cls, compile_object_name: str, compile_object: type):
    if compile_object_name not in cls.py_compile_class:
      hash = hashlib.blake2b(inspect.getsource(compile_object).encode(), digest_size=16).hexdigest()
      cls.py_compile_class[compile_object_name] = {'compile_object': compile_object,
                                                   'hash': hash}
    elif cls.py_compile_class[compile_object_name]['compile_object'] != compile_object:
      hash = hashlib.blake2b(inspect.getsource(compile_object).encode(), digest_size=16).hexdigest()
      if cls.py_compile_class[compile_object_name]['hash'] != hash:
        old_module_name = inspect.getmodule(cls.py_compile_class[compile_object_name]['compile_object']).__name__
        new_module_name = inspect.getmodule(compile_object).__name__
        msg = f"存在同名但不同实现的 {compile_object_name}，两个主体所属的 module name 分别为 " + \
              f"{old_module_name} 与 {new_module_name}"
        raise ValueError(msg)

  @classmethod
  def if_need_compile_py(cls) -> bool:
    if cls._py_need_compile_checked:
      return False
    cls._py_need_compile_checked = True
    if (len(cls.py_compile_class) > 0 or len(cls.py_compile_functions) > 0) and cls.ENABLE_PY_UDF:
      return True
    return False

  @classmethod
  def init_py_env(cls, service) -> bool:
    import platform
    cls.ENABLE_REMOTE_COMPILE = service.PY_ENABLE_REMOTE_COMPILE
    cls.USE_REMOTE_DSO = service.PY_USE_REMOTE_DSO
    cls.PARALLEL_COMPILE = service.PY_PARALLEL_COMPILE
    if platform.system() != 'Linux':
      print_info("当前非 Linux 系统，将关闭多进程，使用远程 compile server 编译 Pycode")
      cls.ENABLE_REMOTE_COMPILE = True
      cls.PARALLEL_COMPILE = False
    # 没有本地 dso 目录则创建
    if not os.path.exists(cls.DSO_PATH):
      os.makedirs(cls.DSO_PATH)
    # 生成缓存文件 cache.pickle
    if not os.path.isfile(cls.CACHE_FILE_PATH):
      with open(cls.CACHE_FILE_PATH, 'wb') as f:
        pickle.dump(dict(), f)
    with open(cls.CACHE_FILE_PATH, "rb") as f:
      cls._py_compile_cache = pickle.load(f)
    # 版本检查
    if os.environ.get('DRAGON_MATX_CHECK_VERSION', 'true').lower() == 'true':
      from .install_util import check_matx_version
      updated, _ = check_matx_version()
      # 版本更新，需要把缓存清空
      if updated == True:
        cls._py_compile_cache = {}

    # 静态部署清缓存
    if cls.ENABLE_STATIC_COMPILE:
      cls._py_compile_cache = {}
    # 动态部署需要设置编译工具
    else:
      cls.set_compile_tool(service.PY_COMPILE_TOOL)
      # 设置远程 compile server url
      if cls.ENABLE_REMOTE_COMPILE:
        def get_url_by_ip():
          try:
            import socket
            ips = []
            hostname = socket.gethostname()
            for info in socket.getaddrinfo(hostname, None):
              if info[0] == socket.AF_INET:
                ips.append(info[4][0])
            ips = list(set(ips))  # 去重
            url = 'http://matx-compile-server.internal/compile'
            for ip in ips:
              if ip.startswith('172'):
                url = 'http://matx-compile-server.corp.kuaishou.com/compile'
                break
          except Exception as e:
            url = 'http://matx-compile-server.corp.kuaishou.com/compile'
          return url
        
        if os.getenv('DRAGON_MATX_DEVELOP_ENV'):
          env = os.environ.get("DRAGON_MATX_DEVELOP_ENV").lower()
          if env == 'hb1-internal':
            cls._py_compile_server_url = "http://matx-compile-server.internal/compile"
          elif env == 'hb1-corp':
            cls._py_compile_server_url = "http://matx-compile-server.corp.kuaishou.com/compile"
        else:
          cls._py_compile_server_url = get_url_by_ip()

  @classmethod
  def compile_py(cls):
    # 处理匿名函数
    cls.compile_anonymous_function()
    # 多进程编译列表
    process_list = []
    event = multiprocessing.Event()
    for compile_object_name, compile_object_info in cls.py_compile_class.items():
      compile_object = compile_object_info['compile_object']
      # 静态部署
      if cls.ENABLE_STATIC_COMPILE:
        cls.static_compile(compile_object, cls.DSO_PATH)
      else:
        compile_tool = cls.PY_COMPILE_TOOL
        lines = get_source_code(compile_object, cls.NO_NEED_COMPILE_SET)
        hash_source = f"{cls.ENABLE_REMOTE_COMPILE}\n{compile_tool}\n{cls.PY_GCC_TOOL_PATH}\n{cls.PY_CLANG_TOOL_PATH}\n{lines}"
        code_hash = hashlib.blake2b(hash_source.encode()).hexdigest()
        # 需要编译 so
        if cls.need_compile_so(cls.DSO_PATH, cls.PY_COMPILE_TOOL, compile_object_name, cls._py_compile_cache, code_hash):
          # 修改缓存
          cls._py_compile_cache[compile_object_name] = code_hash
          with open(os.path.join(cls.DSO_PATH, f"{compile_object_name}.py"), 'w') as f:
            f.write(lines)
          # 多进程编译
          if cls.PARALLEL_COMPILE:
            for tool in compile_tool:
              cc_file_name = f"{compile_object_name}_{tool}.cc"
              so_file_name = f"{compile_object_name}_{tool}.so"
              if cls.ENABLE_REMOTE_COMPILE:
                process_list.append(multiprocessing.Process(target=cls.remote_py_compile, args=(compile_object, lines, tool, cls.DSO_PATH, cc_file_name, so_file_name, cls.COMPILE_OPTIONS, event)))
              else:
                process_list.append(multiprocessing.Process(target=cls.local_py_compile, args=(compile_object, tool, getattr(cls, f'PY_{tool.upper()}_TOOL_PATH'), cls.DSO_PATH, cc_file_name, so_file_name, cls.COMPILE_OPTIONS, event)))
          else:
            for tool in compile_tool:
              cc_file_name = f"{compile_object_name}_{tool}.cc"
              so_file_name = f"{compile_object_name}_{tool}.so"
              if cls.ENABLE_REMOTE_COMPILE:
                cls.remote_py_compile(compile_object, lines, tool, cls.DSO_PATH, cc_file_name, so_file_name, cls.COMPILE_OPTIONS)
              else:
                cls.local_py_compile(compile_object, tool, getattr(cls, f'PY_{tool.upper()}_TOOL_PATH'), cls.DSO_PATH, cc_file_name, so_file_name, cls.COMPILE_OPTIONS)

    if len(process_list) > 0:
      print_info("开启多进程编译...")
      compile_start = datetime.datetime.now()
      for p in process_list:
        p.start()
      for p in process_list:
        p.join()
      if event.is_set():
        raise Exception(f"[Py UDF] 多进程编译 Py 异常，请检查是否为 multiprocessing 的异常。如果是，可以 export DRAGON_MATX_PARALLEL_COMPILE=false 后再次执行")
      print_info(f"多进程编译耗时: {datetime.datetime.now() - compile_start}")

    # 远程 so
    if not cls.ENABLE_STATIC_COMPILE and cls.USE_REMOTE_DSO:
      from .remote_dso_util import get_object_key, check_hash_and_upload
      for compile_object_name, compile_object_info in cls.py_compile_class.items():
        compile_object = compile_object_info['compile_object']
        for tool in compile_tool:
          so_file_name = f"{compile_object_name}_{tool}.so"
          local_so_file_path = os.path.join(cls.DSO_PATH, so_file_name)
          blob_object_key, md5_new = get_object_key(local_so_file_path)
          cls.py_compile_class[compile_object_name][f"{tool}_remote_so_name"] = blob_object_key
          check_hash_and_upload(so_file_name, local_so_file_path, blob_object_key, md5_new)

    # 保存缓存
    with open(cls.CACHE_FILE_PATH, "wb") as f:
      pickle.dump(cls._py_compile_cache, f)
  
  @classmethod
  def compile_anonymous_function(cls):
    if len(cls.py_compile_functions) == 0:
      return
    class_builder = ClassBuilder("AnonymousFunctionSet")
    class_builder._import_statements += sorted(list(cls.py_import))
    # 保序，防止缓存失效
    sorted_function_list = sorted(list(cls.py_compile_functions.keys()))
    for func_name in sorted_function_list:
      class_builder.add_method(cls.py_compile_functions[func_name]['code'], new_func_name=func_name)
    file_path = os.path.join(cls.DSO_PATH, 'anonymous_function_set.py')
    class_builder.save_to_file(file_path)
    function_set = class_builder.load_class_from_file(file_path, 'AnonymousFunctionSet')
    cls.check_and_add_compile_class('AnonymousFunctionSet', function_set)

  @classmethod
  def set_py_processor_config(cls, processor):
    compile_object_name = processor._config["compile_object_name"]
    processor._config["static_compile"] = cls.ENABLE_STATIC_COMPILE
    # 动态部署设置编译工具
    if not cls.ENABLE_STATIC_COMPILE:
      if len(cls.PY_COMPILE_TOOL) > 1:
        processor._config["compile_tool"] = 'both'
      else:
        processor._config["compile_tool"] = cls.PY_COMPILE_TOOL[0]
      for tool in cls.PY_COMPILE_TOOL:
        processor._config[f"{tool}_so_name"] = f"{compile_object_name}_{tool}.so"
      # 使用远程 so 添加远程 so path
      if cls.USE_REMOTE_DSO:
        for tool in cls.PY_COMPILE_TOOL:
          processor._config[f"{tool}_remote_so_name"] = cls.py_compile_class[compile_object_name][f"{tool}_remote_so_name"]

  @classmethod
  def set_compile_tool(cls, compile_tool_str):
    # 兼容之前设置了 MATX_SERVER_GCC_PATH 的，优先级最高
    compile_tool_path = os.environ.get('MATX_SERVER_GCC_PATH', '')
    if compile_tool_path != '':
      if "clang++" in compile_tool_path and os.path.isfile(compile_tool_path):
        print_info(f"MATX_SERVER_GCC_PATH has been setted, only use {compile_tool_path} compile py")
        cls.PY_COMPILE_TOOL = ['clang']
        cls.PY_CLANG_TOOL_PATH = compile_tool_path
        return
      elif "g++" in compile_tool_path and os.path.isfile(compile_tool_path):
        print_info(f"MATX_SERVER_GCC_PATH has been setted, only use {compile_tool_path} compile py")
        cls.PY_COMPILE_TOOL = ['gcc']
        cls.PY_GCC_TOOL_PATH = compile_tool_path
        return
      else:
        print_info("MATX_SERVER_GCC_PATH is not 'g++' or 'clang++', will use DRAGON_MATX_COMPILE_TOOL as tool")
    
    if compile_tool_str not in ['gcc', 'clang', 'both']:
      print_info("PY_COMPILE_TOOL not in ['gcc', 'clang', 'both'], will use both")
      cls.PY_COMPILE_TOOL = ['clang', 'gcc']
    elif compile_tool_str == 'both':
      cls.PY_COMPILE_TOOL = ['clang', 'gcc']
    else:
      cls.PY_COMPILE_TOOL = [compile_tool_str]
    print_info(f"Compile tool: {cls.PY_COMPILE_TOOL}")
    
    # 本地编译自动找工具
    if not cls.ENABLE_REMOTE_COMPILE:
      tools_download_path = os.environ.get('MATX_COMPILE_TOOLS_DOWNLOAD_PATH',  os.path.join(os.path.expanduser("~"), '.matx_compile_tools'))
      if 'clang' in cls.PY_COMPILE_TOOL:
        download_clang_path = os.path.join(tools_download_path, 'clang-11.1.0/bin/clang++')
        clang_tool_paths = ["/data/soft/distcc/clang-11.1.0/bin/clang++", "/media/ssd1/jenkins/cppTools/distcc/clang-11.1.0/bin/clang++", download_clang_path]
        tool_path_setted = False
        for path in clang_tool_paths:
          if os.path.isfile(path):
            cls.PY_CLANG_TOOL_PATH = path
            print_info(f"Local compile py, clang tool path: {path}")
            tool_path_setted = True
            break
        if not tool_path_setted:
          print_info('local compile py and cannot auto find clang tool path, will automatically download compile tool')
          from .get_compile_tool import get_compile_tool
          get_compile_tool('clang')
          cls.PY_CLANG_TOOL_PATH = download_clang_path
      if 'gcc' in cls.PY_COMPILE_TOOL:
        download_gcc_path = os.path.join(tools_download_path, 'gcc-10.3.0/bin/g++')
        gcc_tool_paths = ['/data/soft/distcc/gcc-10.3.0/bin/g++', '/media/ssd1/jenkins/cppTools/distcc/gcc-10.3.0/bin/g++', download_gcc_path]
        tool_path_setted = False
        for path in gcc_tool_paths:
          if os.path.isfile(path):
            cls.PY_GCC_TOOL_PATH = path
            print_info(f"Local compile py, gcc tool path: {path}")
            tool_path_setted = True
            break
        if not tool_path_setted:
          print_info('local compile py and cannot auto find gcc tool path, will automatically download compile tool')
          from .get_compile_tool import get_compile_tool
          get_compile_tool('gcc')
          cls.PY_GCC_TOOL_PATH = download_gcc_path

      download_libstdc_path = os.path.join(tools_download_path, 'gcc-10.3.0/lib64/libstdc++.so')
      libstdc_paths = ['/data/soft/distcc/gcc-10.3.0/lib64/libstdc++.so', '/media/ssd1/jenkins/cppTools/distcc/gcc-10.3.0/lib64/libstdc++.so', download_libstdc_path]
      libstdc_path_setted = False
      import ctypes
      for path in libstdc_paths:
        if os.path.isfile(path):
          ctypes.CDLL(path, ctypes.RTLD_GLOBAL)
          libstdc_path_setted = True
          break
      if not libstdc_path_setted:
        print_info('local compile py and cannot auto find libstdc++.so path, will automatically download')
        from .get_compile_tool import get_compile_tool
        get_compile_tool('std')
        ctypes.CDLL(download_libstdc_path, ctypes.RTLD_GLOBAL)

  @classmethod
  def static_compile(cls, compile_object, dso_path):
      from matx.script import context, _passes, _parser, _link_ir_module, _codegen_dragon
      sc_ctx = context.ScriptContext()
      sc_ctx.main_node.raw = compile_object
      _passes(sc_ctx)
      _parser(sc_ctx)
      _link_ir_module(sc_ctx)
      _codegen_dragon(sc_ctx)
      file_path = os.path.join(dso_path, compile_object.__name__ + ".cxx")
      with open(file_path, 'w') as f:
        f.write(sc_ctx.rt_module.get_source())
      print_info(f"Static compile, generated {file_path}")

  @classmethod
  def need_compile_so(cls, dso_path, compile_tools, compile_object_name, hash_map, hash) -> bool:
      for tool in compile_tools:
        so_file_name = f"{compile_object_name}_{tool}.so"
        local_so_file_path = os.path.join(dso_path, so_file_name)
        if not os.path.isfile(local_so_file_path):
          return True
      if compile_object_name not in hash_map:
        return True
      old_hash = hash_map.get(compile_object_name, None)
      if old_hash != hash:
        return True
      return False

  @classmethod
  def local_py_compile(cls, compile_object, compile_tool, compile_tool_path, dso_path, cc_file_name, so_file_name, compile_options, event = None):
    try:
      import subprocess
      from matx.script import context, from_source
      from matx._ffi.libinfo import find_include_path
      cc_file_path = os.path.join(dso_path, cc_file_name)
      so_file_path = os.path.join(dso_path, so_file_name)
      print_info(f"Compiling Pycode, use {compile_tool} compile object: {compile_object.__name__}, so file path: {so_file_path}")
      compile_start = datetime.datetime.now()
      compile_context: context.ScriptContext = from_source(compile_object)
      with open(cc_file_path, 'w') as f:
        f.write(compile_context.rt_module.get_source())
      if compile_tool == "gcc":
        static_cplus_option = ["-static-libstdc++"]
      elif compile_tool == "clang":
        static_cplus_option = ["--stdlib=libc++"]
      compile_cmd = [
        compile_tool_path,
        "-shared",
        "-fPIC",
        "-o",
        so_file_name,
        cc_file_name,
      ] + compile_options + static_cplus_option + ["-I" + path for path in find_include_path()]
      proc = subprocess.Popen(f"cd {dso_path} && {' '.join(compile_cmd)} && cd -", stdout=subprocess.PIPE, stderr=subprocess.STDOUT, shell=True)
      (out, _) = proc.communicate()
      if proc.returncode != 0:
        msg = "Compilation error:\n"
        msg += out.decode("utf-8")
        msg += "\nCommand line: " + " ".join(compile_cmd)
        raise Exception(msg)
      compile_end = datetime.datetime.now()
      print_info(f"Use {compile_tool} compile object: {compile_object.__name__}, time cost: {compile_end - compile_start}")
    except Exception as e:
      if event:
        event.set()
      raise Exception(f'local py compile error: {e}')

  @classmethod
  def remote_py_compile(cls, compile_object, source_code, compile_tool, dso_path, cc_file_name, so_file_name, compile_options, event = None):
    try:
      import requests
      so_file_path = os.path.join(dso_path, so_file_name)
      url = cls._py_compile_server_url
      # url = "http://localhost:5000/compile"
      if compile_tool == "gcc":
        static_cplus_option = ["-static-libstdc++"]
      elif compile_tool == "clang":
        static_cplus_option = ["--stdlib=libc++"]

      data = {'compile_tool': compile_tool,
              'compile_object_name': compile_object.__name__,
              'cc_file_name': cc_file_name,
              'so_file_name': so_file_name,
              'compile_options': " ".join(compile_options + static_cplus_option),
              'user_name': os.environ.get('USER', 'none'),
              'source_code': source_code}
    
      print_info(f"Remote compiling Pycode, use {compile_tool} compile object: {compile_object.__name__}, so file path: {so_file_path}")
      compile_start = datetime.datetime.now()
      response = requests.post(url, data=data)
      if response.status_code == 200:
        status = response.headers.get('X-Status', '')
        if status == "success":
          import io
          import zipfile
          zip_buffer = io.BytesIO(response.content)
          with zipfile.ZipFile(zip_buffer, 'r') as zip_ref:
            file_list = zip_ref.namelist()
            for file_name in file_list:
              with zip_ref.open(file_name) as file:
                file_content = file.read()
                target_file_path = os.path.join(dso_path, file_name)
                with open(target_file_path, 'wb') as target_file:
                  target_file.write(file_content)
        else:
          file_content = response.content
          raise Exception(f"remote compile py error: {file_content.decode()}")
      else:
        raise Exception("no response from remote py compile server")
      compile_end = datetime.datetime.now()
      print_info(f"Use {compile_tool} compile object: {compile_object.__name__}, time cost: {compile_end - compile_start}")
    except Exception as e:
      if event:
        event.set()
      raise Exception(f'remote py compile error: {e}')


class ImportCollector(ast.NodeVisitor):
  def __init__(self):
    self.imports = []
    self.symbol_map = defaultdict(dict)  # {module: {alias: real_name}}
    self.current_imports = {}

  def visit_Import(self, node):
    # 处理 import 语句
    for alias in node.names:
      module = alias.name
      self.imports.append(('module', module, alias.asname))
      if alias.asname:
        self.current_imports[alias.asname] = module
      else:
        self.current_imports[module.split('.')[0]] = module

  def visit_ImportFrom(self, node):
    # 处理 from...import 语句
    module = node.module
    for alias in node.names:
      self.imports.append(('symbol', module, alias.name, alias.asname))
      key = alias.asname or alias.name
      self.current_imports[key] = (module, alias.name)


class UsageCollector(ast.NodeVisitor):
  def __init__(self, node, import_collector: ImportCollector):
    self.context_node = node
    self.import_collector = import_collector
    self.used_symbols = set()
    self.used_var = list()

  def visit_Name(self, node):
    global_dep = self.context_node.module.globals.get(node.id, object())
    if isinstance(global_dep, (type(None), int, float, bool, str, bytes, bytearray)):
      self.used_var.append((node.id, global_dep))
      return

    # 跟踪符号使用
    if node.id in self.import_collector.current_imports:
      self.used_symbols.add(node.id)
      
  def visit_Attribute(self, node):
    # 跟踪属性访问
    if isinstance(node.value, ast.Name):
      base = node.value.id
      if base in self.import_collector.current_imports:
        module_info = self.import_collector.current_imports[base]
        if isinstance(module_info, tuple):  # from...import
          self.used_symbols.add(base)
        else:  # module import
          self.used_symbols.add(base)


def get_used_imports_and_var(node, internal_module) -> tuple:
  source_code = node.span.source_code
  compile_object = node.raw
  # 解析AST
  import_tree = ast.parse(source_code)
  # 分析导入和使用情况
  import_collector = ImportCollector()
  import_collector.visit(import_tree)

  compile_object_code = inspect.getsource(compile_object)
  compile_object_tree = ast.parse(compile_object_code)
  usage_collector = UsageCollector(node, import_collector)
  usage_collector.visit(compile_object_tree)

  required_imports = []
  # 处理普通import
  for imp in usage_collector.import_collector.imports:
    if imp[0] == 'module':
      _, module, alias = imp
      main_module = module.split('.')[0]
      if (alias or main_module in usage_collector.used_symbols) and (alias or main_module not in internal_module):
        required_imports.append(f"import {module}" + (f" as {alias}" if alias else ""))

    elif imp[0] == 'symbol':
      _, module, symbol, alias = imp
      if (alias in usage_collector.used_symbols or symbol in usage_collector.used_symbols) and (symbol not in internal_module):
        line = f"from {module} import {symbol}"
        if alias:
          line += f" as {alias}"
        required_imports.append(line)
  
  return required_imports, usage_collector.used_var


def get_source_code(compile_object, no_need_compile) -> str:
  from .analysis import DepsAnalysis, SourceAnalysis, ModuleAnalysis
  from .context import ScriptContext
  compile_context = ScriptContext()
  compile_context.main_node.raw = compile_object

  from .analysis import DepsAnalysis, SourceAnalysis, ModuleAnalysis
  dep_anls = DepsAnalysis()
  src_anls = SourceAnalysis()
  mdo_anls = ModuleAnalysis()
  # parse main ast and do module analysis
  src_anls.run(compile_context)
  mdo_anls.run(compile_context)
  # alternate execution: parse deps, source and module analysis.
  while dep_anls.run(compile_context):
    src_anls.run(compile_context)
    mdo_anls.run(compile_context)

  all_object = set()
  source_code = []
  used_import_list = []
  used_import_set = set()
  used_var_list = []
  used_var_set = set()

  def dfs(node):
    all_object.add(node.raw.__name__)
    source_code.append(inspect.getsource(node.raw))
    used_imports, used_vars = get_used_imports_and_var(node, all_object)
    for used_import in used_imports:
      if used_import not in used_import_set:
        used_import_set.add(used_import)
        used_import_list.append(used_import)
    for used_var in used_vars:
      if used_var[0] not in used_var_set:
        used_var_set.add(used_var[0])
        used_var_list.append(used_var)

  for dep_node in reversed(compile_context.deps_node):
    if dep_node.raw.__name__ not in no_need_compile:
      dfs(dep_node)
  dfs(compile_context.main_node)

  code = ""
  # 添加 import
  code += 'from dragonfly.matx.dragonfly_context import *\n'
  code += '\n'.join(used_import_list)
  code += '\n\n'
  # 添加变量
  for used_var in used_var_list:
    code += f"{used_var[0]} = {used_var[1]}\n"
  code += '\n\n'
  # 添加源代码
  code += '\n'.join(source_code)
  return code


def get_function_class(self, func):
  """判断函数归属并返回所属类对象"""
  # 处理实例方法和类方法
  if inspect.ismethod(func):
    return func.__self__ if inspect.isclass(func.__self__) else func.__self__.__class__
  
  # 处理静态方法和嵌套函数
  if inspect.isfunction(func):
    qualname_parts = func.__qualname__.split('.')
    if len(qualname_parts) >= 2:
      # 通过模块查找类对象
      cls = inspect.getmodule(func).__dict__.get(qualname_parts[0])
      if inspect.isclass(cls):
        return cls

  # 独立函数或未找到类
  return None


def validate_function_signature(func, in_function_set) -> None:
  # 获取函数签名
  try:
    sig = inspect.signature(func)
  except ValueError:
    raise TypeError("无效的函数对象") from None

  params = list(sig.parameters.values())
  required_count = 2 if in_function_set else 1
  func_name = func.__name__

  # 验证参数数量
  if len(params) != required_count:
    raise TypeError(
      f"函数 {func_name} 需要 {required_count} 个参数，但实际有 {len(params)} 个参数"
    )

  # 当需要self参数时验证第一个参数
  if in_function_set:
    first_param = params[0]
    if first_param.name != "self":
      raise TypeError(
        f"函数 {func_name} 第一个参数必须命名为 'self'，但实际为 '{first_param.name}'"
      )

  # 验证context参数类型
  context_param = params[-1]
  if context_param.annotation is inspect.Parameter.empty:
    raise TypeError(
      f"函数 {func_name} 参数 '{context_param.name}' 缺少类型注解"
    )

  if context_param.annotation.__name__ != "DragonflyContext":
    raise TypeError(
      f"函数 {func_name} 参数 '{context_param.name}' 必须声明为 DragonflyContext 类型"
    )
  
  if context_param.name != "ctx":
    raise TypeError(
      f"函数 {func_name} DragonflyContext 类型的参数名字需为 ctx'"
    )

class ClassBuilder:
  def __init__(self, class_name: str):
    init_method = ast.FunctionDef(
      name='__init__',
      args=ast.arguments(
        posonlyargs=[],
        args=[ast.arg(arg='self', annotation=None)],
        vararg=[],
        kwonlyargs=[],
        kw_defaults=[],
        kwarg=[],
        defaults=[]
      ),
      body=[ast.Pass()],
      decorator_list=[],
      returns=ast.Name(id='None', ctx=ast.Load()) if sys.version_info < (3, 9) 
        else ast.Constant(value=None)
    )

    self.class_ast = ast.ClassDef(
      name=class_name,
      bases=[],
      keywords=[],
      body=[init_method],
      decorator_list=[]
    )
    self._import_statements = [
      'from dragonfly.matx.dragonfly_context import *'
    ]

  def create_arg(self, name, annotation=None):
    """创建符合AST规范的参数节点"""
    # 确保annotation是合法的AST节点
    if annotation is None:
      anno_node = None
    elif isinstance(annotation, str):
      anno_node = ast.Name(id=annotation, ctx=ast.Load())
    else:
      anno_node = annotation
    
    # 兼容Python 3.5+的参数节点结构
    return ast.arg(
      arg=name,
      annotation=anno_node,
      # 处理3.8+的类型注释(type_comment)
      **({'type_comment': None} if hasattr(ast.arg, 'type_comment') else {})
    )

  def _process_args(self, args: ast.arguments) -> ast.arguments:
    """参数处理核心逻辑"""
    # 保留原始参数结构属性
    posonlyargs = getattr(args, 'posonlyargs', [])
    defaults = getattr(args, 'defaults', [])
    kwonlyargs = getattr(args, 'kwonlyargs', [])
    kw_defaults = getattr(args, 'kw_defaults', [])
    
    # 处理可变参数(*args, **kwargs)
    vararg = args.vararg if hasattr(args, 'vararg') else None
    kwarg = args.kwarg if hasattr(args, 'kwarg') else None

    if not posonlyargs and not args.args:
      new_args = [
        self.create_arg('self'),
        self.create_arg('ctx', 'DragonflyContext')
      ]
    elif args.args[0].arg != 'self':
      new_args = [self.create_arg('self')] + args.args.copy()
    else:
      return args

    return ast.arguments(
      posonlyargs=[],
      args=new_args,
      vararg=vararg,
      kwonlyargs=kwonlyargs,
      kw_defaults=kw_defaults,
      kwarg=kwarg,
      defaults=defaults
    )

  def add_method(self, func, new_func_name: str = None):
    from textwrap import dedent
    if isinstance(func, types.FunctionType):
      src = dedent(inspect.getsource(func))
    else:
      src = dedent(func)
    
    tree = ast.parse(src).body[0]
    if not isinstance(tree, ast.FunctionDef):
      raise ValueError("Input must be a function definition")

    # 参数处理
    tree.args = self._process_args(tree.args)

    if new_func_name:
      tree.name = new_func_name
    
    # 创建新函数节点
    new_func = ast.FunctionDef(
      name=tree.name,
      args=tree.args,
      body=tree.body,
      decorator_list=[],
      returns=tree.returns
    )
    self.class_ast.body.append(new_func)

  def _unparse_ast(self, node: ast.AST) -> str:
    """将AST节点转换为规范的Python代码"""
    from textwrap import indent
    try:
      import astunparse
    except Exception as e:
      install_python_package('astunparse')
      import astunparse
    code = astunparse.unparse(node).strip()
    # 格式化代码缩进
    return indent(code, ' ' * 4)

  def generate_source_code(self) -> str:
    """生成完整的类定义源码"""
    source = ['\n'.join(self._import_statements)]
    # 生成类内部代码
    class_body = []
    for node in self.class_ast.body:
      unparsed = self._unparse_ast(node)
      # 保留节点间的自然空行
      if class_body and not class_body[-1].endswith('\n\n'):
          class_body.append('')
      class_body.append(unparsed)
    
    if not class_body:
      class_body.append('  pass')

    source.append(f'class {self.class_ast.name}:\n' + '\n'.join(class_body))
    formatted = '\n\n'.join(source)
    return formatted

  def save_to_file(self, filepath: str):
    """将生成的源码保存到文件"""
    source = self.generate_source_code()
    with open(filepath, 'w', encoding='utf-8') as f:
      f.write(source)

  @staticmethod
  def load_class_from_file(filepath: str, class_name: str) -> type:
    """从源码文件加载类"""
    # 动态创建模块
    import importlib
    from pathlib import Path
    path = Path(filepath).resolve()  # 规范化路径
    module_name = path.stem.replace('.', '_')  # 处理类似 "module_v1.0.py" 的情况

    spec = importlib.util.spec_from_file_location(module_name, str(path))
    if spec is None:
      raise ImportError("Failed to create module spec")
    module = importlib.util.module_from_spec(spec)
    sys.modules[module_name] = module
    spec.loader.exec_module(module)
    
    # 获取目标类
    target_class = getattr(module, class_name, None)
    if not target_class:
      raise ImportError(f"Class {class_name} not found in {filepath}")
    if not isinstance(target_class, type):
      raise TypeError(f"{class_name} is not a class")

    return target_class


