#!/usr/bin/env python3
# coding=utf-8
"""
- filename: common_leaf_config.py
- description: common_leaf dynamic_json_config DSL intelligent builder
- author: <EMAIL>
- date: 2019-12-31 15:19:00
"""

import os
import sys
import datetime
import copy
from collections import OrderedDict
from typing import Optional, Union, Dict
from enum import IntEnum
import json

from .common_leaf_util import check_arg, strict_types, ArgumentError, LogicError, has_intersection, \
    iterate_all_processors, extract_attr_and_channel_name, is_browse_set_channel, is_item_attr_channel, \
    is_plain_attr, dump_to_json, find_processor, extract_attr_names, extract_table_and_attr_name, \
    extract_table_attrs_config, get_config_version, strip_channel_name, try_add_table_name, aggregate_table_attrs_config, \
    RaiseError, BLAME_ERROR_CODE, PRINT_ALL_ERROR, ERR_MSG_SET
from .common_leaf_processor import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Leaf<PERSON>bserve<PERSON>, <PERSON><PERSON><PERSON><PERSON>, LeafM<PERSON>er, get_all_input_common_attrs, \
    get_all_output_common_attrs, get_all_input_item_attrs, get_all_output_item_attrs
from .common_leaf_flow import LeafFlowCore
from .ext.common.common_leaf_retriever import CommonRecoPipelineRetriever
from .ext.common.common_leaf_mixer import CommonRecoPipelineMixer, CommonRecoCreateLogicTableMixer
from .ext.common.common_leaf_arranger import CommonRecoPipelineArranger
from .ext.common.common_leaf_enricher import CommonRecoContextEnricher, CommonRecoLocalIndexItemAttrEnricher, CommonRecoLuaAttrEnricher, CommonRecoPipelineEnricher, \
    CommonRecoRemoteIndexItemAttrEnricher, CommonRecoSampleListCommonAttrEnricher, CommonRecoAutoAdjustEnricher, \
    CommonRecoDistributedIndexAttrKVItemAttrEnricher, CommonRecoLocalAttrIndexItemAttrEnricher, \
    CommonRecoDistributedIndexFlatKvItemAttrEnricher, CommonRecoPyAttrEnricher
from .ext.common.common_leaf_observer import CommonRecoGlobalHolderObserver, CommonRecoPipelineObserver
from .ext.common.common_api_mixin import CommonApiMixin
from .auto_build.build_file_util import get_modules, get_all_cc_file_name, get_processor_name
from .auto_build.trie import Trie
from .matx.py_enricher_util import PyUdfCompileUtil



__version__ = "0.8.3"
__author__ = "fangjianbing"
__maintainer__ = "fangjianbing, xingjiayuan, qianlei, qingjun, zhaoyang09, sunmin, caohongjin, lixiaojia03"
__credits__ = ["huiyiqun"]
__email__ = "<EMAIL>"
__status__ = "Production"

# sub_flow共享context，全局map
class SharedContextConfig(object):
  def __init__(self,
                pass_common_attrs_in_request,  # bool
                pass_browse_set,               # bool
                level                          # int 
  ):                
    self.pass_common_attrs_in_request = pass_common_attrs_in_request
    self.pass_browse_set = pass_browse_set
    self.level = level
    self.existing_common_attrs = set()
    self.total_existing_item_attrs = set()
    self.processor_existing_item_attrs: Dict[
      Union[CommonRecoPipelineEnricher, CommonRecoPipelineRetriever, CommonRecoPipelineObserver],
      set()] = {}
    self.used_common_attrs = set()
    self.used_item_attrs = set()
    self.task_queue_id = None # None/int/str

  def __repr__(self):
    return (f"SharedContextConfig(pass_common_attrs_in_request={self.pass_common_attrs_in_request}, "
            f"pass_browse_set={self.pass_browse_set}, level={self.level}, "
            f"existing_common_attrs={self.existing_common_attrs}, existing_item_attrs={self.total_existing_item_attrs}, "
            f"used_common_attrs={self.used_common_attrs}, used_item_attrs={self.used_item_attrs}, "
            f"task_queue_id={self.task_queue_id})")

class IndexSource(IntEnum):
  """
  正排索引的类别 (用于获取 item_attr)

  可选值
  ------
  - `NONE`: 无本地索引
  - `LOCAL`: 本地 Common Index 索引
  - `REMOTE`: 远程 Common Index 索引
  - `LOCAL_ATTR_INDEX`: 本地 Attr Index 索引
  - `FLAT_INDEX`: 分布式索引（支持零拷贝）
  """
  NONE = 0
  LOCAL = 1
  REMOTE = 2
  LOCAL_ATTR_INDEX = 3
  FLAT_INDEX = 4


# NOTE(fangjianbing): LeafFlow 的 Mixin 模式利用多继承实现, LeafFlowCore 一定要放置在第一位!!!
# 其余 Mixin 若存在同名 API, 则顺序在前的生效(左覆盖右), 这里默认加载 CommonApiMixin, 其余自定义 Mixin
# 由用户在外部自行按需加载
class LeafFlow(LeafFlowCore, CommonApiMixin):
  """
  LeafFlow 用于描述推荐业务的逻辑流程, 每个 LeafFlow 将生成一个独立的 Pipeline, 多个 LeafFlow 相互组合给 LeafService 调用

  本 LeafFlow 由 LeafFlowCore 和 CommonApiMixin 混入而成, 包含了中台所有官方 Processor 的 API 接口

  初始化参数
  ------
  `name`: [str] LeafFlow 的名称, 将同时作为生成的 Pipeline 名称

  `abtest_biz_name`: [str] 选配项，对 Pipeline 进行 abtest 分流实验时使用的 abtest biz_name, 对应 abtest 网站上全英文大写的所属业务名称（例如 "RECO_PUSH"）

  `abtest_biz_seq_num`: (DEPRECATED! 请改用 `abtest_biz_name`) [int] 选配项，对 Pipeline 进行 abtest 分流实验时使用的 abtest biz_seq_num

  可用方法
  ------
  所有不以下划线 `_` 开头的方法名 (注意：以下划线开头的方法为框架内部接口, 业务方不要手动调用!)
  """
  # 是否自动转换分布式索引 processor 为 flattened 类型
  SWITCH_TO_FLAT_INDEX = False
  FLOW_STACK = [] 

  def __enter__(self):
    LeafFlow.FLOW_STACK.append(self)
    return self

  def __exit__(self, *args):
    LeafFlow.FLOW_STACK.pop()

def has_current_flow() -> bool:
  return len(LeafFlow.FLOW_STACK) > 0

def current_flow() -> LeafFlow:
  """
  获取当前 with 上下文环境所绑定的 LeafFlow 对象。使用示例：

  ```
  # 通过 with 语句创建一个 LeafFlow 的使用上下文
  with LeafFlow(name="xxx") as f:
    process()

  def process():
    # 在被调函数中可通过 current_flow() 直接获取当前 with 上下文中的 LeafFlow 对象，不用通过函数参数传递。
    # 这里拿到的 flow 对象即是前面 with LeafFlow(name="xxx") as f 中的 f
    flow = current_flow()
    flow.do_nothing()
  ```
  """
  assert len(LeafFlow.FLOW_STACK) > 0, "current_flow() 必须在 with 语境下使用"
  return LeafFlow.FLOW_STACK[-1]

class LeafService:
  """
  LeafService 对应一个独立的推荐服务, 可包含多个 LeafFlow, 一个业务实例化一个即可

  全局配置
  ------
  `AUTO_DETECT_DOWNSTREAM`: [bool] 是否为异步 processor 自动填充 downstream_processor, 默认开启

  `AUTO_INJECT_ITEM_ATTR`: [bool] 是否自动添加从正排索引捕获 item 属性的 processor, 默认开启

  `AUTO_RESET_EXISTING_ITEM_ATTR`: [bool] 当有新 item 加入时是否自动重置已存在的 item 属性, 以保证新 item 也能被自动获取缺失的正排属性, 默认开启

  `AUTO_INJECT_SAMPLE_LIST_USER_ATTR`: [bool] 是否自动添加从 SampleList 捕获 UserInfo 的 processor, 默认开启

  `ENABLE_ATTR_CHECK`: [bool] 是否开启所有属性相关的检查, 默认开启

  `CHECK_UNUSED_ATTR`: [bool] 是否开启对无用属性的检查, 默认开启

  `IGNORE_UNUSED_ATTR`: [list] 忽略对指定无用属性的检查, 默认为空

  `CHECK_NO_SOURCE_ATTR`: [bool] 是否开启对无源属性的检查, 默认开启

  `IGNORE_NO_SOURCE_ATTR`: [list] 忽略对指定无源属性的检查, 默认为空

  `FORBIDDEN_COMMON_ATTR`: [list] 禁止被使用的 common 属性列表, 默认为空

  `FORBIDDEN_ITEM_ATTR`: [list] 禁止被使用的 item 属性列表, 默认为空

  `CHECK_COMMON_LOGIC`: [bool] 是否检查一些不合常理的 processor 顺序逻辑, 默认开启

  `AUTO_INJECT_META_DATA`: [bool] 是否插入 meta data, 默认 True

  `DEFAULT_TRACEBACK_VALUE`: [bool] 是否默认记录 traceback_info, 默认 False

  `DISABLE_LUA_PROCESSOR`: [bool] 是否禁用 lua 相关的 processor, 默认 False

  `I_AM_MASTER_DRIVER`: [bool] 老司机手动挡模式, 是否关闭除 AUTO_DETECT_DOWNSTREAM 以外的所有自动及智能功能, 全手动操控, 默认 False
  
  `ENABLE_PROCESSOR_AUTO_NAMING`: [bool] 是否开启自动命名，如果开启后将会根据 config 配置 hash 产生一个名字，默认 True
  
  `ENABLE_PROCESSOR_AUTO_MERGE`: [bool] 是否开启算子自动合并，默认 False

  `ALLOW_ASYNC_FALLBACK_TO_SYNC`: [bool] 异步算子 downstream 自动推断找不到，且关闭了 attr 依赖检查的情况下，将其退化为同步行为，以保证异步数据立即可见，默认为 True

  `PRIOR_USE_SUB_FLOW_COMMON_ATTR`: [bool] 在 sub_flow 自动推断导出 attr 时，若 sub_flow 产出了和主 flow 已存在的同名 common attr，是否导出并覆盖主 flow 中的 common attr 给后续使用，默认 False

  `PY_ENABLE_REMOTE_COMPILE`: [bool] PyUdf 是否启用远程编译, 默认 False,  mac 默认 True, 适用于 uni-predict、kaiworks 等 bin 与 json 分开部署的服务

  `PY_USE_REMOTE_DSO`: [bool] PyUdf 是否使用远程 so, 默认 False, 适用于 uni-predict、kaiworks 等 bin 与 json 分开部署的服务

  `PY_PARALLEL_COMPILE`: [bool] PyUdf 是否开启多进程编译, 默认 True, mac 默认 False

  `PY_COMPILE_TOOL`: [str] PyUdf 使用的编译工具, 'gcc'、'clang'、'both', 默认为 both, gcc 和 clang 都编, 优先级低于 MATX_SERVER_GCC_PATH

  初始化参数
  ------
  `kess_name`: [str] 需为 leaf 服务注册的 kess service name

  `common_attrs_from_request`: [list] 选配项，指定从上游 request 中会携带进 leaf 的 common_attr 名称列表, 将用于 common_attr 依赖分析

  `item_attrs_from_request`: [list] 选配项，指定从上游 request 中会携带进 leaf 的 item_attr 名称列表, 将用于 item_attr 依赖分析

  `index_source`: [IndexSource] 选配项，不建议使用，可能引发不必要的性能消耗！leaf 服务正排索引的类型, 可选值: `IndexSource.LOCAL`, `IndexSource.REMOTE`, `IndexSource.LOCAL_ATTR_INDEX`, `IndexSource.FLAT_INDEX`

  配置示例
  ------
  ``` python
  service = LeafService(kess_name="grpc_Xxx")
  # 关闭自动填充 item_attr 的功能
  service.AUTO_INJECT_ITEM_ATTR = False
  ```
  """

  ### 用户配置项
  # 是否自动检测异步下游依赖
  AUTO_DETECT_DOWNSTREAM = True
  # 是否自动获取 item 正排属性
  AUTO_INJECT_ITEM_ATTR = True
  # 当有新 item 加入时是否自动重置已存在的 item 属性
  AUTO_RESET_EXISTING_ITEM_ATTR = True
  # 是否自动获取 sample list 用户特征数据
  AUTO_INJECT_SAMPLE_LIST_USER_ATTR = True
  # 是否开启所有属性相关的检查
  ENABLE_ATTR_CHECK = os.environ.get("DRAGONFLY_ENABLE_ATTR_CHECK", "true") == "true"
  # 是否检查无用的属性生成
  CHECK_UNUSED_ATTR = os.environ.get("DRAGONFLY_CHECK_UNUSED_ATTR", "true") == "true"
  # 是否检查只读属性的重复写入
  CHECK_READONLY_ATTR = os.environ.get("DRAGONFLY_CHECK_READONLY_ATTR", "true") == "true"
  # 忽略某些无用属性的检查
  IGNORE_UNUSED_ATTR = []
  # 是否检查无用的属性生成
  CHECK_NO_SOURCE_ATTR = os.environ.get("DRAGONFLY_CHECK_NO_SOURCE_ATTR", "true") == "true"
  # 忽略某些无源属性的检查
  IGNORE_NO_SOURCE_ATTR = []
  # 禁止被使用的 common 属性列表
  FORBIDDEN_COMMON_ATTR = []
  # 禁止被使用的 item 属性列表
  FORBIDDEN_ITEM_ATTR = []
  # 是否检查一般的逻辑合理性
  CHECK_COMMON_LOGIC = False
  # 是否默认记录 traceback_info
  DEFAULT_TRACEBACK_VALUE = None
  # 是否插入 meta data
  AUTO_INJECT_META_DATA = True
  # 是否禁用 lua 相关的 processor
  DISABLE_LUA_PROCESSOR = False
  # 是否开启老司机手动挡模式
  I_AM_MASTER_DRIVER = False
  # 是否开启自动命名
  ENABLE_PROCESSOR_AUTO_NAMING = True
  # 是否在 blame 分支语句的提交者
  ENABLE_GIT_BLAME = os.environ.get("ENABLE_GIT_BLAME", "false") == "true"
  # 是否稳定得生成 processor 的 name（可能会有名字冲突的需要手动命名）
  ENABLE_PROCESSOR_STABLE_NAME = False
  # 是否对动态参数进行格式检查，防止漏加 {{}} 的错误使用
  CHECK_DYNAMIC_PARAM_FORMAT = True
  # 是否开启算子合并去重
  ENABLE_PROCESSOR_AUTO_MERGE = False
  # sub_flow 自动推断导出 attr 时是否优先使用 sub_flow 中的 common attr
  PRIOR_USE_SUB_FLOW_COMMON_ATTR = False
  # 是否开启 sub_flow 多层线程池模式
  MULTI_SUB_FLOW_THREAD_POOL = False
  # 允许异步算子在无 downstream 情况下退化为同步行为
  ALLOW_ASYNC_FALLBACK_TO_SYNC = True
  # PyUdf 是否开启远程编译, 默认 False,  mac 默认 True, 适用于 uni-predict、kaiworks 等 bin 与 json 分开部署的服务
  PY_ENABLE_REMOTE_COMPILE = os.environ.get("DRAGON_MATX_REMOTE_COMPILE", 'false').lower() == "true"
  # PyUdf 是否使用远程 so, 默认 False, 适用于 uni-predict、kaiworks 等 bin 与 json 分开部署的服务
  PY_USE_REMOTE_DSO = os.environ.get('DRAGON_MATX_USE_REMOTE_DSO', 'false').lower() == 'true'
  # PyUdf 是否开启多进程编译, 默认 True, mac 默认 False
  PY_PARALLEL_COMPILE = os.environ.get("DRAGON_MATX_PARALLEL_COMPILE", 'true').lower() == "true"
  # PyUdf 使用的编译工具, 'gcc'、'clang'、'both', 默认为 both, gcc 和 clang 都编, 优先级低于 MATX_SERVER_GCC_PATH
  PY_COMPILE_TOOL = os.environ.get("DRAGON_MATX_COMPILE_TOOL", 'both').lower()

  @strict_types
  def __init__(self, kess_name: str, index_source: IndexSource = IndexSource.NONE,
               distributed_index_kconf_key: str = "",
               flat_index_config: Optional[dict] = None,
               common_attrs_from_request: Optional[list] = None,
               item_attrs_from_request: Optional[list] = None,
               ann_config: Optional[dict] = None,
               embedding_table: Optional[dict] = None):
    """ 初始化 LeafService """
    if not kess_name:
      raise ArgumentError("参数 kess_name 不可为空")
    self.__kess_service_name = kess_name
    self.__index_source = index_source
    self.__return_common_attrs = []
    self.__return_item_attrs = []
    self.__return_predict_xtr = False
    self.__common_attrs_from_request = common_attrs_from_request
    self.__item_attrs_from_request = item_attrs_from_request
    self.__request_type_config = {}
    self.__default_request_type = ""
    self.__leaf_flow_map = {}
    self.__abtest_biz_name_map = {}
    self.__abtest_biz_seq_num_map = {}
    self.__degrade_config = []
    self.__processor_map = {}
    self.__pipeline_map = {}
    self.__flow_unused_attrs = OrderedDict()
    self.__remote_index_kess_service = ""
    self.__remote_index_timeout_ms = 200
    self.__sample_list_kess_service = "grpc_sampleList"
    self.__sample_list_timeout_ms = 200
    self.__ann_config = ann_config
    self.__embedding_table = embedding_table
    self.__flat_index_item_attrs_with_type = {}
    self.__flat_index_item_attrs_with_more_config = {}
    self.__flat_index_config_type = {}
    # flat_index_config 中的每一项配置会合并到自动注入的 CommonRecoDistributedIndexFlatKvItemAttrEnricher 中
    self.__flat_index_config = flat_index_config
    self.__dynamic_protos = []
    if distributed_index_kconf_key != "":
      raise ArgumentError("distributed_index_kconf_key 已废弃！请改用 flat_index_config！有疑问联系@qianlei")
    self.__common_attr_types = []
    self.__item_attr_types = []
    self.__check_attr_type_for_tables = []
    self.__is_auto_delete = os.getenv("DRAGONFLY_AUTO_DELETE_UNUSED_BRANCH", 'false') == 'true'
    self.__auto_delete_ignore_unused_attr = set()
    self.__auto_delete_ignore_no_source_attr = set()
    self.__json_settings = dict()
    self.__settings_path = os.path.dirname(os.path.abspath(sys.argv[0])) + "/dragonfly.settings"
    if os.path.exists(self.__settings_path) and os.path.getsize(self.__settings_path) and not self.__is_auto_delete:
      with open(self.__settings_path) as f:
        self.__json_settings = json.load(f)
        if self.__kess_service_name in self.__json_settings and "IGNORE_UNUSED_ATTR" in self.__json_settings[self.__kess_service_name]:
          self.__auto_delete_ignore_unused_attr = set(self.__json_settings[self.__kess_service_name]["IGNORE_UNUSED_ATTR"])
        if self.__kess_service_name in self.__json_settings and "IGNORE_NO_SOURCE_ATTR" in self.__json_settings[self.__kess_service_name]:
          self.__auto_delete_ignore_no_source_attr = set(self.__json_settings[self.__kess_service_name]["IGNORE_NO_SOURCE_ATTR"])
    self.__processor_and_modules = {}
    self.__blame_attrs = set()
    self.__blame_attrs_to_processors = {}
    if os.environ.get("ENABLE_SUB_FLOW_SHARED_CONTEXT", "false") == "true":
      self.__shared_context_used_common_attr = set()
      self.__shared_context_used_item_attr = set()

  @strict_types
  def set_gflag(self, flag: str, value: str):
    """
    设置 gflag 配置。如果设置的 gflag 在 executor 初始化时生效，需要在获取 executor 前调用。返回值为设置的结果。

    参数说明
    ------
    `flag`: [str] 设置的 gflag 参数名。

    `value`: [str] 设置的 gflag 参数值。
    """
    from .common_reco_pipeline_executor_pywrap import set_gflag
    return set_gflag(flag, value)

  @strict_types
  def setup_remote_index(self, kess_service: str, timeout_ms: int):
    """
    设置远程索引服务的基本信息

    参数说明
    ------
    `kess_service`: [str] 远程索引服务的 kess 服务名

    `timeout_ms`: [int] 远程索引服务的请求 timeout 毫秒数
    """
    if not kess_service:
      raise ArgumentError("远程索引服务的 kess_service 不可为空")
    if timeout_ms <= 0:
      raise ArgumentError("远程索引服务的 timeout_ms 需大于 0")
    self.__remote_index_kess_service = kess_service
    self.__remote_index_timeout_ms = timeout_ms
    return self

  @strict_types
  def setup_sample_list(self, kess_service: str, timeout_ms: int):
    """
    设置 sample_list 服务的基本信息

    参数说明
    ------
    `kess_service`: [str] 远程索引服务的 kess 服务名

    `timeout_ms`: [int] 远程索引服务的请求 timeout 毫秒数
    """
    if not kess_service:
      raise ArgumentError("sample_list 服务的 kess_service 不可为空")
    if timeout_ms <= 0:
      raise ArgumentError("sample_list 服务的 timeout_ms 需大于 0")
    self.__sample_list_kess_service = kess_service
    self.__sample_list_timeout_ms = timeout_ms
    return self

  @property
  def common_attrs_from_request(self):
    """
    返回 leaf service 的 common_attrs_from_request
    """
    return self.__common_attrs_from_request

  @common_attrs_from_request.setter
  @strict_types
  def common_attrs_from_request(self, attrs: Optional[list]):
    """
    调整 leaf service 的 common_attrs_from_request
    """
    self.__common_attrs_from_request = attrs

  @property
  def item_attrs_from_request(self):
    """
    返回 leaf service 的 item_attrs_from_request
    """
    return self.__item_attrs_from_request

  @item_attrs_from_request.setter
  @strict_types
  def item_attrs_from_request(self, attrs: Optional[list]):
    """
    调整 leaf service 的 item_attrs_from_request
    """
    self.__item_attrs_from_request = attrs

  def get_return_common_attrs(self) -> set:
    """
    返回哪些 common_attr 给调用端
    """
    return extract_attr_names(self.__return_common_attrs, "name")

  @strict_types
  def return_common_attrs(self, attrs: list):
    """
    返回哪些 common_attr 给调用端，支持 name 和 as 格式的重命名

    参数说明
    ------
    `attrs`: [list] common_attr 名称列表
    """
    if self.__processor_map:
      raise LogicError("❌ return_common_attrs() 必须在 add_leaf_flows() 之前调用")
    
    for attr in attrs:
      if not isinstance(attr, str) and not isinstance(attr, dict):
        raise ArgumentError("参数 attrs 数组中只能包含字符串或 dict")
      if isinstance(attr, dict):
        if "name" not in attr or "as" not in attr:
          raise ArgumentError("参数 attrs 数组中的 dict 必须包含 name 和 as 属性")
        if not isinstance(attr["name"], str) or not isinstance(attr["as"], str):
          raise ArgumentError("参数 attrs 数组中的 dict 的 name 和 as 属性必须是字符串")

    return_common_attrs_dict = [v for v in attrs if isinstance(v, dict)]
    return_common_attrs_dict_set = extract_attr_names(return_common_attrs_dict, "name")
    if len(return_common_attrs_dict) != len(return_common_attrs_dict_set):
      raise ArgumentError("参数 attrs 数组中 dict 包含重复的 common attr")
    
    return_common_attrs_str_set = set(v for v in attrs if isinstance(v, str))
    self.__return_common_attrs = return_common_attrs_dict + list(return_common_attrs_str_set - return_common_attrs_dict_set)
    return self

  def get_return_item_attrs(self) -> set:
    """
    返回哪些 item_attr 给调用端
    """
    return extract_attr_names(self.__return_item_attrs, "name")


  @strict_types
  def return_item_attrs(self, attrs: list, include_predict_xtr: bool = False):
    """
    返回哪些 item_attr 给调用端，支持 name 和 as 格式的重命名

    参数说明
    ------
    `attrs`: [list] item_attr 名称列表

    `include_predict_xtr`: [bool] 已废弃！是否自动加入所有 predict 的返回结果(包括 common_predict 和 predict_fetcher 获取的各类 xtr), 默认 False
    """
    if self.__processor_map:
      raise LogicError("❌ return_item_attrs() 必须在 add_leaf_flows() 之前调用")
    for attr in attrs:
      if not isinstance(attr, str) and not isinstance(attr, dict):
        raise ArgumentError("参数 attrs 数组中只能包含字符串或 dict")
      if isinstance(attr, dict):
        if "name" not in attr or "as" not in attr:
          raise ArgumentError("参数 attrs 数组中的 dict 必须包含 name 和 as 属性")
        if not isinstance(attr["name"], str) or not isinstance(attr["as"], str):
          raise ArgumentError("参数 attrs 数组中的 dict 的 name 和 as 属性必须是字符串")

    return_item_attrs_dict = [v for v in attrs if isinstance(v, dict)]
    return_item_attrs_dict_set = extract_attr_names(return_item_attrs_dict, "name")
    if len(return_item_attrs_dict) != len(return_item_attrs_dict_set):
      raise ArgumentError("参数 attrs 数组中 dict 包含重复的 item attr")
    
    return_item_attrs_str_set = set(v for v in attrs if isinstance(v, str))
    self.__return_item_attrs = return_item_attrs_dict + list(return_item_attrs_str_set - return_item_attrs_dict_set)
    self.__return_predict_xtr = include_predict_xtr
    return self

  @strict_types
  def blame_attrs(self, attrs: list):
    """
    blame 这些 attr 在哪个 processor 被生成以及被使用到

    参数说明
    ------
    `attrs`: [list] attr 名称列表，common_attr 与 item_attr 都行
    """
    if self.__processor_map:
      raise LogicError("❌ blame_attrs() 必须在 add_leaf_flows() 之前调用")
    if not BLAME_ERROR_CODE:
      raise LogicError("❌ blame_attrs() 必须开启 BLAME_ERROR_CODE 环境变量")
    for attr in attrs:
      if not isinstance(attr, str):
        raise ArgumentError(" blame_attrs() 中，参数 attrs 数组中只能包含字符串")
      self.__blame_attrs.add(attr)

  def copy_return_common_attrs(self):
    return copy.deepcopy(self.get_return_common_attrs())

  def copy_return_item_attrs(self):
    return copy.deepcopy(self.get_return_item_attrs())

  @property
  def common_attr_types(self):
    return self.__common_attr_types 
  
  @common_attr_types.setter
  def common_attr_types(self, attr_configs: list):
    """
    设置 common_attr attr_type，用于 attr_type 运行时检查

    参数说明
    ------
    `attr_configs`: [list] common_attr 列表，list 内每一个元素需为一个 dict
      - `attr_name`: [string], common_attr 的名字 
      - `attr_type": [string], attr type 只支持 int/double/string/int_list/double_list/string_list/extra 7种类型
    """
    if self.__processor_map:
      raise LogicError("❌ common_attr_types() 必须在 add_leaf_flows() 之前调用")
    attr_names = set()
    for attr in attr_configs:
      if not isinstance(attr, dict):
        raise ArgumentError("参数 attr_configs 数组中只能包含 dict")
      if "attr_name" not in attr or "attr_type" not in attr:
        raise ArgumentError("参数 attr_configs 数组中的 dict 中需有 attr_name 和 attr_type")
      if attr["attr_type"] not in ["int", "double", "string", "int_list", "double_list", "string_list", "extra"]:
        raise ArgumentError("attr_type 不合法，只支持 int/double/string/int_list/double_list/string_list/extra 7种类型")
      if attr["attr_name"] in attr_names:
        raise ArgumentError("已经存在相同的 attr_name 设置过 attr_type 了")
      attr_names.add(attr["attr_name"])
    self.__common_attr_types = attr_configs
    return self
  
  @property
  def item_attr_types(self):
    return self.__item_attr_types 
  
  @item_attr_types.setter
  def item_attr_types(self, attr_configs: list):
    """
    设置 item_attr attr_type，用于 attr_type 运行时检查

    参数说明
    ------
    `attr_configs`: [list] item_attr 列表，list 内每一个元素需为一个 dict
      - `table_name`: [string], item_attr 所在的表名 
      - `attr_name`: [string], item_attr 的名字 
      - `attr_type": [string], attr type 只支持 int/double/string/int_list/double_list/string_list/extra 7种类型    
    """
    if self.__processor_map:
      raise LogicError("❌ item_attr_types() 必须在 add_leaf_flows() 之前调用")
    attr_names = set()
    for attr in attr_configs:
      if not isinstance(attr, dict):
        raise ArgumentError("参数 attr_configs 数组中只能包含 dict")
      if "table_name" not in attr or "attr_name" not in attr or "attr_type" not in attr:
        raise ArgumentError("参数 attr_configs 数组中的 dict 中需有 table_name 和 attr_name 和 attr_type")
      if not isinstance(attr["table_name"], str) or not isinstance(attr["attr_name"], str) or not isinstance(attr["attr_type"], str):
        raise ArgumentError("参数 attr_configs 数组中的 dict 中 table_name 和 attr_name 和 attr_type 必须都是 string")
      if attr["attr_type"] not in ["int", "double", "string", "int_list", "double_list", "string_list", "extra"]:
        raise ArgumentError("attr_type 不合法，只支持 int/double/string/int_list/double_list/string_list/extra 7种类型")
      attr_name = f'{attr["table_name"]}::{attr["attr_name"]}'
      if attr_name in attr_names:
        raise ArgumentError("已经存在相同的 table_name attr_name 设置过 attr_type 了")
      attr_names.add(attr_name)
    self.__item_attr_types = attr_configs
    return self

  @property
  def check_attr_type_for_tables(self):
    return self.__check_attr_type_for_tables

  @check_attr_type_for_tables.setter
  def check_attr_type_for_tables(self, check_attr_type_for_tables: list):
    """
    设置 attr_type check 作用于哪些 table，未在这里声明的 table 不进行检测。用于 attr_type 运行时检查

    参数说明
    ------
    `check_attr_type_for_tables`: [list] item_table 列表，list 内每一个元素需为一个 str
    """
    if self.__processor_map:
      raise LogicError("❌ check_attr_type_for_tables() 必须在 add_leaf_flows() 之前调用")
    tables = set()
    for table in check_attr_type_for_tables:
      if not isinstance(table, str):
        raise ArgumentError(f"参数 check_attr_type_for_tables 数组中只能包含 str, 其中 {table} 为 {type(table)} 类型")
      tables.add(table)
    self.__check_attr_type_for_tables = sorted(tables)
    return self

  @strict_types
  def register_proto(self, file_name: str, content: str, deps: list = []):
    """
    动态注册 proto 文件

    使用场景：

      1. 复杂 proto 只解析需要字段进行加速: https://kstack.corp.kuaishou.com/question/3198

      2. 动态新增 proto, 可以在 playground 进行快速调试。

      3. 复杂 proto log 太长，只解析部分字段查看信息。例如: UserInfo 太长了，后面的字段无法 log 出来，反解成一个小的 proto, 将后续字段打印。

    Tips: 目前动态 proto 与编译进二进制的 proto 相互隔离，不能互相引用(include)!

    参数说明
    ------
    `file_name`: [str] pb 文件名

    `content`: [str] pb 文件内容

    `deps` [list[str]] pb 依赖, 不能依赖 C++ 中原有 proto, 只能依赖已经动态注册的 proto 文件

    配置示例
    ------
    ``` python
    service \\
      .register_proto(
        "temp.proto",
        \"\"\"
syntax = "proto3";
package ks.reco;

message TempMessage {
  double score1 = 1;
  double score2 = 2;
}\"\"\") \\
      .register_proto(
        "temp2.proto",
        \"\"\"
syntax = "proto3";
package ks.reco;

message TempMessageSecond {
  int64 id = 1;
  TempMessage msg = 2;
}\"\"\",
        ["temp.proto"])
    ```
    """
    dynamic_proto = {}
    dynamic_proto["file_name"] = file_name
    dynamic_proto["deps"] = deps
    dynamic_proto["content"] = content

    self.__dynamic_protos.append(dynamic_proto)
    return self

  @strict_types
  def add_degrade_config(self, degrader_type: str, kconf_key: str, processor: list, request_type: Optional[list] = None):
    """
    增加降级配置项

    注意：该函数需在 `.add_leaf_flows()` 之后调用！

    参数说明
    ------
    `degrader_type`: [str] 降级类型, 可选值: circuit_breaker / bulk_head / adaptive_limiter

    `kconf_key`: [str] 降级策略参数所在的 kconf key

    `processor`: [list] 需要被降级的 Processor 名称列表

    `request_type`: [list] 只考虑 request_type 列表中的流量, 缺省则会作为所有 request_type 的总限流策略
    """
    if degrader_type not in ("circuit_breaker", "bulk_head", "adaptive_limiter"):
      raise ArgumentError(f"无效的 degrader_type 值: {degrader_type}")
    if not kconf_key or not isinstance(kconf_key, str):
      raise ArgumentError("add_degrade_config() 的 kconf_key 需为非空字符串")
    if not processor:
      raise ArgumentError("add_degrade_config() 的 processor 配置不可为空")
    for p in processor:
      if not p or not isinstance(p, str):
        raise ArgumentError("processor 列表不可为空且只能包含字符串")
      if p not in self.__processor_map:
        raise LogicError(f"存在未定义的 processor: {p}")
    if request_type:
      for t in request_type:
        if t not in self.__request_type_config:
          raise ArgumentError(f"存在未定义的 request_type: {t}")

    config = {
      "degrader_type": degrader_type,
      "kconf_key": kconf_key,
      "processor": processor,
    }
    if request_type:
      config["request_type"] = request_type
    self.__degrade_config.append(config)
    return self

  @strict_types
  def add_leaf_flows(self,
                     leaf_flows: Union[LeafFlow, list],
                     request_type: Optional[Union[str, list]] = None,
                     as_default: bool = False,
                     post_response_flows: Optional[Union[LeafFlow, list]] = None):
    """
    注册一组 LeafFlow 到服务中, 并可为该组 LeafFlow 绑定一个 request_type (如果非空)

    参数说明
    ------
    `leaf_flows`: [LeafFlow | list[LeafFlow]] 需注册的一个或一组 LeafFlow

    `request_type`: [str | list[str]] 需为该组 LeafFlow 绑定的 request_type 名称, 可留空用于注册仅被 abtest 调度的 LeafFlow

    `as_default`: [bool] 是否将该组 LeafFlow 所绑定的 request_type 作为默认调度的 request_type

    `post_response_flows`: [LeafFlow | list[LeafFlow]] 注册一个或一组在返回 response 之后运行的 LeafFlow
    """
    LeafFlowCore._ENABLE_PROCESSOR_STABLE_NAME = self.ENABLE_PROCESSOR_STABLE_NAME
    LeafProcessor._ENABLE_PROCESSOR_STABLE_NAME = self.ENABLE_PROCESSOR_STABLE_NAME
    LeafProcessor._CHECK_DYNAMIC_PARAM_FORMAT = self.CHECK_DYNAMIC_PARAM_FORMAT

    initial_item_attrs = set()
    if self.__item_attrs_from_request:
      initial_item_attrs.update(self.__item_attrs_from_request)
    if self.__item_attrs_from_request or self.__return_item_attrs:
      initial_item_attrs.update({"_SCORE_", "_REASON_"})

    if not isinstance(request_type, list):
      request_type = [request_type] if request_type else []
    for t in request_type:
      if not t:
        raise ArgumentError("request_type 值不可为空字符串")
      if t in self.__request_type_config:
        raise LogicError(f"❌ request_type {t} 被重复定义")
    if as_default:
      if not request_type:
        raise ArgumentError("参数 as_default 为 True 时必须指定非空 request_type 值")
      if self.__default_request_type:
        raise ArgumentError(f"已存在 default request_type: {self.__default_request_type}, 不可重复指定")
      self.__default_request_type = request_type[0]

    if not isinstance(post_response_flows, list):
      post_response_flows = [post_response_flows] if post_response_flows else []

    if post_response_flows:
      for flow in post_response_flows:
        flow.post_response = True
      leaf_flows.extend(post_response_flows)

    # 先按需收集 preceding output attrs
    self.__detect_preceding_attrs(list(iterate_all_processors(leaf_flows)),
                                  preceding_output_common_attrs=set(self.__common_attrs_from_request or []),
                                  preceding_output_item_attrs=initial_item_attrs)

    self.__add_leaf_flows(leaf_flows, level=0,
                          succeeding_absent_common_attrs=set(self.get_return_common_attrs()),
                          succeeding_absent_item_attrs=set(self.get_return_item_attrs()))

    self.__post_process(leaf_flows, request_type, existing_item_attrs=initial_item_attrs)

    if self.__common_attrs_from_request is not None:
      if not self.I_AM_MASTER_DRIVER and self.ENABLE_ATTR_CHECK and self.CHECK_NO_SOURCE_ATTR:
        self.__check_no_source_common_attr(leaf_flows, set(self.__common_attrs_from_request))

    self.__register_leaf_flows(leaf_flows, request_type)

    return self

  def __add_leaf_flows(self,
                       leaf_flows: list,
                       level: int = 0,
                       succeeding_absent_common_attrs: set = set(),
                       succeeding_absent_item_attrs: set = set()):
    """
    注册一组 LeafFlow 的实际逻辑实现
    """
    if not leaf_flows:
      return
    if not all(isinstance(flow, LeafFlow) for flow in leaf_flows):
      raise ArgumentError("参数 leaf_flows 中的元素必须为 LeafFlow 对象")
    if not all(flow._is_branch_state_clean() for flow in leaf_flows):
      raise LogicError(f"❌ 存在未关闭的 if/switch 分支")

    if self.ENABLE_PROCESSOR_AUTO_MERGE:
      # 相同 flow 下相同 scope 内的算子合并去重
      for flow in leaf_flows:
        self.__auto_merge_processors(flow)
      # 清理被标记为可删除的算子
      for flow in leaf_flows:
        self.__remove_merged_processors(flow)

    for flow in leaf_flows:
      flow.level = level

    all_processors = list(iterate_all_processors(leaf_flows))
    all_processors_reversed = list(reversed(all_processors))
    last_subflow_index = 0
    curr_absent_common_attrs = copy.copy(succeeding_absent_common_attrs)
    curr_absent_item_attrs = copy.copy(succeeding_absent_item_attrs)
    for i, processor in enumerate(all_processors_reversed):
      sub_flow = processor.get_sub_flow()
      if sub_flow:
        self.__update_post_needed_attrs(all_processors_reversed[last_subflow_index:i], \
                                        curr_absent_common_attrs, curr_absent_item_attrs)
        processor.succeeding_absent_item_attrs = copy.copy(curr_absent_item_attrs)
        processor.succeeding_absent_common_attrs = copy.copy(curr_absent_common_attrs)
        # common attr 优先复用 subflow 之前已存在的, item attr 优先用 subflow 里重新产生的
        if not self.PRIOR_USE_SUB_FLOW_COMMON_ATTR:
          processor.succeeding_absent_common_attrs -= processor.preceding_output_common_attrs

        last_subflow_index = i
        self.__add_leaf_flows([sub_flow], level=level+1,
                              succeeding_absent_common_attrs=processor.succeeding_absent_common_attrs,
                              succeeding_absent_item_attrs=processor.succeeding_absent_item_attrs)

        if os.environ.get("ENABLE_SUB_FLOW_SHARED_CONTEXT", "false") == "true":
          self.__adjust_post_needed_attrs(processor, all_processors_reversed[:i])

      if os.environ.get("ENABLE_EMBEDDED_FLOW", "false") == "true":
        processor.generate_embedded_flows(main_flow=leaf_flows[0])  # TODO(weiyilong): 应该用真正的上级 flow，但是目前无法获取
        embedded_flows = processor.get_embedded_flows()
        if embedded_flows:
          processor.succeeding_absent_item_attrs = copy.copy(curr_absent_item_attrs)
          processor.succeeding_absent_common_attrs = copy.copy(curr_absent_common_attrs)
          # common attr 优先复用 subflow 之前已存在的, item attr 优先用 subflow 里重新产生的
          if not self.PRIOR_USE_SUB_FLOW_COMMON_ATTR:
            processor.succeeding_absent_common_attrs -= processor.preceding_output_common_attrs

          self.__add_leaf_flows([embedded_flow for _, embedded_flow in embedded_flows],
                                level=level+1,
                                succeeding_absent_common_attrs=processor.succeeding_absent_common_attrs,
                                succeeding_absent_item_attrs=processor.succeeding_absent_item_attrs)

    for i, processor in enumerate(all_processors):
      if (isinstance(processor, CommonRecoContextEnricher) and
        os.environ.get("ENABLE_SUB_FLOW_SHARED_CONTEXT", "false") == "true"):
        self.__register_shared_context_init_config(processor, level)
      sub_flow = processor.get_sub_flow()
      if sub_flow:
        if os.environ.get("ENABLE_SUB_FLOW_SHARED_CONTEXT", "false") == "true":
          self.__validate_and_update_shared_context_state(processor, level)
        self.__autowire_sub_flow_attrs(processor)
      if os.environ.get("ENABLE_EMBEDDED_FLOW", "false") == "true":
        embedded_flows = processor.get_embedded_flows()
        if embedded_flows:
          self.__autowire_embedded_flow_attrs(processor)

    self.__fill_traceback_config(leaf_flows)


  def __post_process(self, leaf_flows, request_type, existing_item_attrs: set):
    for processor in iterate_all_processors(leaf_flows):
      sub_flow = processor.get_sub_flow()
      if sub_flow:
        curr_processor_existing_item_attrs = try_add_table_name(sub_flow.item_table, sub_flow._import_item_attrs)
        if os.environ.get("ENABLE_SUB_FLOW_SHARED_CONTEXT", "false") == "true" and getattr(processor, "_has_context_name", False) and hasattr(self, '_shared_context_config_map'): 
          curr_processor_existing_item_attrs |= self._shared_context_config_map[processor._config.get("common_reco_context_attr_name")]\
                                                .processor_existing_item_attrs[processor]
        self.__post_process([sub_flow], request_type, existing_item_attrs = curr_processor_existing_item_attrs)
        if self.MULTI_SUB_FLOW_THREAD_POOL and "task_queue_id" not in processor._config and sub_flow.level > 1:
          # NOTE(zhaoyang09): task_queue_id 从 0 开始计数，sub_flow 的 level 从 1 开始。
          processor._config["task_queue_id"] = sub_flow.level - 1

      if os.environ.get("ENABLE_EMBEDDED_FLOW", "false") == "true":
        embedded_flows = processor.get_embedded_flows()
        if embedded_flows:
          for _, embedded_flow in embedded_flows:
            self.__post_process([embedded_flow], request_type, existing_item_attrs)

    for processor in iterate_all_processors(leaf_flows):
      self.__processor_and_modules[processor.__class__.__name__] = processor.__module__

    if not self.ENABLE_PROCESSOR_AUTO_NAMING:
      self.__check_processor_name(leaf_flows)

    if self.DISABLE_LUA_PROCESSOR:
      for processor in iterate_all_processors(leaf_flows):
        if isinstance(processor, CommonRecoLuaAttrEnricher):
          raise LogicError(f"❌ 在开启 DISABLE_LUA_PROCESSOR 的情况下禁止使用 lua 相关功能: {processor.name or processor.get_type_alias()}")

    # py udf 编译
    if PyUdfCompileUtil.if_need_compile_py():
      PyUdfCompileUtil.init_py_env(self)
      PyUdfCompileUtil.compile_py()
    for processor in iterate_all_processors(leaf_flows):
      if isinstance(processor, CommonRecoPyAttrEnricher):
        PyUdfCompileUtil.set_py_processor_config(processor)

    if not self.I_AM_MASTER_DRIVER:
      self.__auto_inject_or_check_item_attr(leaf_flows, existing_item_attrs)

    if not self.I_AM_MASTER_DRIVER and self.AUTO_INJECT_SAMPLE_LIST_USER_ATTR:
      self.__auto_inject_sample_list_user_attr(leaf_flows)

    self.__auto_inject_global_holder(leaf_flows)

    if self.AUTO_INJECT_META_DATA:
      self.__fill_meta_data(leaf_flows)

    # 老司机一般也需要 AUTO_DETECT_DOWNSTREAM
    if self.AUTO_DETECT_DOWNSTREAM:
      for i, flow in enumerate(leaf_flows):
        is_dangling_downstream_allowed = i + 1 < len(leaf_flows) or flow.level > 0
        self.__autowire_downstream(flow, is_dangling_downstream_allowed)

    if not self.I_AM_MASTER_DRIVER and self.ENABLE_ATTR_CHECK:
      self.__check_forbidden_attr(leaf_flows)

    if not self.I_AM_MASTER_DRIVER and self.ENABLE_ATTR_CHECK and self.CHECK_READONLY_ATTR:
      self.__check_readonly_attr(leaf_flows)

    for flow in reversed(leaf_flows):
      flow._register_all_processors()

    self.__check_sub_flow_processors(leaf_flows)

    for processor in iterate_all_processors(leaf_flows):
      if isinstance(processor, (CommonRecoDistributedIndexFlatKvItemAttrEnricher, CommonRecoDistributedIndexAttrKVItemAttrEnricher)):
        self.__flat_index_item_attrs_with_type.setdefault(processor.photo_store_kconf_key, {})
        self.__flat_index_item_attrs_with_type[processor.photo_store_kconf_key].update(processor.flat_index_item_attrs_with_type)
        self.__flat_index_item_attrs_with_more_config.setdefault(processor.photo_store_kconf_key, [])
        self.__flat_index_item_attrs_with_more_config[processor.photo_store_kconf_key].extend(processor.flat_index_item_attrs_with_more_config)
        self.__flat_index_config_type.setdefault(processor.photo_store_kconf_key, "with_type")
        if processor.is_more_config:
          self.__flat_index_config_type[processor.photo_store_kconf_key] = "with_more_config"

    for processor in iterate_all_processors(leaf_flows):
      if isinstance(processor, CommonRecoDistributedIndexFlatKvItemAttrEnricher):
        for v in processor._config.get("drop_item_attrs", []):
          self.__flat_index_item_attrs_with_type[processor.photo_store_kconf_key].pop(v, {})
        drop_attrs = processor._config.get("drop_item_attrs", [])
        self.__flat_index_item_attrs_with_more_config[processor.photo_store_kconf_key] = list(
          filter(lambda x: x['name'] not in drop_attrs,
                 self.__flat_index_item_attrs_with_more_config[processor.photo_store_kconf_key]))

    # 对 list<dict> 格式的 flat_index 全局配置按 name 去重
    for key, config in self.__flat_index_item_attrs_with_more_config.items():
      if isinstance(config, list):
        dedup_config = []
        dedup_name_set = set()
        for attr_config in config:
          if isinstance(attr_config, dict) and attr_config["name"] not in dedup_name_set:
            dedup_name_set.add(attr_config["name"])
            dedup_config.append(attr_config)
        self.__flat_index_item_attrs_with_more_config[key] = dedup_config

    for processor in iterate_all_processors(leaf_flows):
      sub_flow = processor.get_sub_flow()
      if sub_flow and sub_flow.item_table != processor.item_table and not isinstance(processor, LeafMixer):
        raise LogicError(
          f"processor item_table 需要和 subflow 保持一致. processor: {processor.name}, processor item_table: {processor.item_table} sub_flow: {sub_flow.name} sub_flow item_table: {sub_flow.item_table}")

    created_tables = set()
    created_logic_tables = set()
    for processor in iterate_all_processors(leaf_flows):
      if (isinstance(processor, CommonRecoCreateLogicTableMixer)):
        table = processor._config.get("logic_table", "")
        if table in created_logic_tables:
          # 不限制创建多次相同逻辑表。多次调用的 select_attr 会合并。
          pass
        else:
          if table in created_tables:
            raise LogicError(f"逻辑表 {table} 创建前已有同名物理表！")
          created_logic_tables.add(table)
      created_tables.add(processor.item_table)
      created_tables.update(processor.input_item_tables)
      created_tables.update(processor.output_item_tables)

    self.__processor_map.update(LeafFlowCore._get_processor_definitions())

    if self.__blame_attrs:
      for processor in iterate_all_processors(leaf_flows):
        for attr in self.__blame_attrs:
          if attr not in self.__blame_attrs_to_processors:
            self.__blame_attrs_to_processors[attr] = {"input":set(), "output":set()}
          if attr in processor.input_item_attrs or attr in processor.input_common_attrs:
            self.__blame_attrs_to_processors[attr]["input"].add(f"{processor.name} in {processor.code_line}")
          if attr in processor.output_item_attrs or attr in processor.output_common_attrs:
            self.__blame_attrs_to_processors[attr]["output"].add(f"{processor.name} in {processor.code_line}")

  def __register_leaf_flows(self, leaf_flows: list, request_type: Optional[list] = None):
    flow_names = []
    for flow in leaf_flows:
      flow_names.append(flow.name)
      if flow.name not in self.__leaf_flow_map:
        self.__leaf_flow_map[flow.name] = flow
      if flow.name not in self.__pipeline_map:
        self.__pipeline_map[flow.name] = flow._get_pipeline_config()
      elif self.__pipeline_map[flow.name] != flow._get_pipeline_config():
        raise ArgumentError(f"❌ flow 名称冲突: {flow.name}")

      if flow.name not in self.__abtest_biz_name_map and flow.abtest_biz_name:
        self.__abtest_biz_name_map[flow.name] = flow.abtest_biz_name
      if flow.name not in self.__abtest_biz_seq_num_map and flow.abtest_biz_seq_num > 0:
        self.__abtest_biz_seq_num_map[flow.name] = flow.abtest_biz_seq_num
      for processor in flow._processors:
        if self.__return_predict_xtr and processor.is_for_predict():
          self.__return_item_attrs.extend(list(get_all_output_item_attrs(processor) - self.get_return_item_attrs()))
        sub_flow = processor.get_sub_flow()
        if sub_flow:
          self.__register_leaf_flows([sub_flow])
        if os.environ.get("ENABLE_EMBEDDED_FLOW", "false") == "true":
          embedded_flows = processor.get_embedded_flows()
          if embedded_flows:
            for _, embedded_flow in embedded_flows:
              self.__register_leaf_flows([embedded_flow])

    if request_type:
      for rt in request_type:
        self.__request_type_config[rt] = flow_names

  @property
  def _pipeline_manager_config(self):
    config = {
      "base_pipeline": {
        "type_name": "CommonRecoPipeline",
        "processor": self.__processor_map
      },
      "pipeline_map": self.__pipeline_map
    }
    if self.__flat_index_item_attrs_with_type or self.__flat_index_item_attrs_with_more_config:
      sub_config = {}
      for kconf_key, t in self.__flat_index_config_type.items():
        if t == "with_more_config":
          sub_config[kconf_key] = self.__flat_index_item_attrs_with_more_config[kconf_key]
        elif t == "with_type":
          sub_config[kconf_key] = self.__flat_index_item_attrs_with_type[kconf_key]
      config["flat_index_attrs_with_kconf_key"] = sub_config
    if self.__dynamic_protos:
      config["dynamic_proto"] = self.__dynamic_protos
    return config

  def executor(self):
    """
    获取一个 CommonLeaf pipeline 的 python3 版本 executor, 可用于在 Dragonfly 脚本中本地测试 pipeline 的运行

    注意：执行 executor 前需导入系统变量 `export LD_PRELOAD=./build_tools/gcc-8.3.0/lib64/libstdc++.so.6`,
         如果是 gcc10 编译则导入系统变量 `export LD_PRELOAD=./build_tools/gcc-10.3.0/lib64/libstdc++.so.6`
    """
    self.check_all_err_msg()
    from .common_reco_pipeline_executor_pywrap import CommonRecoLeafPipelineExecutor, set_service_name
    check_config = os.environ.get("DRAGONFLY_CONFIG_STRICT_CHECK", "true") == "true"
    json_str = dump_to_json(self._pipeline_manager_config)
    set_service_name(self.__kess_service_name)
    return CommonRecoLeafPipelineExecutor(json_str, check_config)

  def rpc_mocker(self):
    """
    获取一个 CommonLeaf pipeline 的 python3 版本 rpc_mocker, 可用于 mock Dragonfly 脚本中单元测试的 grpc response
    """
    from .common_reco_pipeline_executor_pywrap import CommonRecoLeafRpcMocker

    return CommonRecoLeafRpcMocker()

  def redis_mocker(self):
    """
    获取一个 CommonLeaf pipeline 的 python3 版本 redis_mocker, 可用于 mock Dragonfly 脚本中单元测试的 redis response
    """
    from .common_reco_pipeline_executor_pywrap import CommonRecoLeafRedisMocker

    return CommonRecoLeafRedisMocker()

  def kafka_mocker(self):
    """
    获取一个 CommonLeaf pipeline 的 python3 版本 kfk_mocker, 可用于 mock Dragonfly 脚本中单元测试的 kafka message
    """
    from .common_reco_pipeline_executor_pywrap import CommonRecoLeafKafkaMocker

    return CommonRecoLeafKafkaMocker()

  @strict_types
  def get_code_info_committer(self) -> None:
    try:
      import git
    except ImportError:
      raise ImportError("import git 失败，pip3 install GitPython 进行安装")
    gitcli = git.Git('.')
    try:
      gitcli.status()
    except git.exc.GitCommandError:
      raise git.exc.GitCommandError("git status 失败，非 git 仓库不能进行 blame, 请取消设置 ENABLE_GIT_BLAME")

    file_to_blame = {}
    branch_blame_map = {}
    for flow in self.__leaf_flow_map.values():
      branch_blame_map.update(flow._branch_blame_map)
    for fn, lineno in branch_blame_map.values():
      begin, end = lineno, lineno
      if fn in file_to_blame:
        begin, end = file_to_blame[fn]
      file_to_blame[fn] = [min(begin, lineno), max(end, lineno)]
    file_blame_result = {}
    for fn, [begin, end] in file_to_blame.items():
      blame_results = gitcli.blame('-L', f"{begin},{end}", "--line-porcelain", fn).split('\n')
      committer_list = [info[10:] for info in blame_results if info.startswith('committer ') ]
      file_blame_result[fn] = (begin, committer_list)

    for name ,config in self.__processor_map.items():
      if name in branch_blame_map:
        fn, lineno = branch_blame_map[name]
        begin, committer_list = file_blame_result[fn]
        config['$code_info'] += f' @{committer_list[lineno - begin]}'

  @strict_types
  def to_json(self, indent: Optional[int] = 2, sort_keys: bool = True, extra_fields: Optional[dict] = None) -> str:
    """
    返回构建后的 json config 内容

    参数说明
    ------
    `indent`: [int] json 的缩进空格数

    `sort_keys`: [bool] 是否对 json 里的 key 进行排序输出
    """

    if self.ENABLE_GIT_BLAME:
      self.get_code_info_committer()

    kess_name = self.__kess_service_name
    if "--online" in sys.argv:
      pass
    elif "--dryrun" in sys.argv:
      pos = sys.argv.index("--dryrun") + 1
      arg = sys.argv[pos] if pos < len(sys.argv) else ""
      dryrun_kess_name = "" if arg.startswith("--") else arg
      kess_name = dryrun_kess_name if dryrun_kess_name else f"{self.__kess_service_name}Dryrun"
      if "--enable_leaf_show" not in sys.argv:
        # 把 dryrun 配置里的 leaf show 功能全部关闭
        for _, config in self.__processor_map.items():
          if config["type_name"] == "CommonRecoLeafShowObserver":
            config["enable_leaf_show"] = False

    config = {}
    config["_DRAGONFLY_VERSION"] = __version__
    config["_DRAGONFLY_CREATE_TIME"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    config_version = get_config_version()
    if config_version:
      config["_CONFIG_VERSION"] = get_config_version()
    config["kess_config"] = { "service_name": kess_name }
    if not self.__request_type_config:
      raise LogicError("❌ 无 request_type 被 LeafFlow 绑定")
    config["request_type_config"] = self.__request_type_config
    config["default_request_type"] = self.__default_request_type if self.__default_request_type \
        else next(iter(self.__request_type_config.keys()))
    config["pipeline_manager_config"] = self._pipeline_manager_config
    if self.__return_common_attrs:
      config["return_common_attrs"] = sorted(self.__return_common_attrs, key = lambda attr: attr["name"] if isinstance(attr, dict) else attr)
    if self.__return_item_attrs:
      config["return_item_attrs"] = sorted(self.__return_item_attrs, key = lambda attr: attr["name"] if isinstance(attr, dict) else attr)
    if self.__common_attr_types:
      config["common_attr_types"] = sorted(self.__common_attr_types, key = lambda d: d["attr_name"])
    if self.__item_attr_types:
      config["item_attr_types"] = sorted(self.__item_attr_types, key = lambda d: (d["table_name"], d["attr_name"]))
    if self.__check_attr_type_for_tables:
      config["check_attr_type_for_tables"] = sorted(self.__check_attr_type_for_tables)
    if self.__common_attrs_from_request is not None or self.__item_attrs_from_request is not None:
      attrs_from_request = {}
      if self.__common_attrs_from_request is not None:
        attrs_from_request["common_attr"] = sorted(set(self.__common_attrs_from_request))
      if self.__item_attrs_from_request is not None:
        attrs_from_request["item_attr"] = sorted(set(self.__item_attrs_from_request))
      config["attrs_from_request"] = attrs_from_request
    if self.__abtest_biz_name_map:
      config["abtest_biz_name_map"] = self.__abtest_biz_name_map
    if self.__abtest_biz_seq_num_map:
      config["abtest_biz_seq_num_map"] = self.__abtest_biz_seq_num_map
    if self.__degrade_config:
      config["degrade_config"] = self.__degrade_config
    if self.__ann_config:
      config["ann_config"] = self.__ann_config
    if self.__embedding_table:
      config["embedding_table"] = self.__embedding_table

    if extra_fields:
      config.update(extra_fields)
    return dump_to_json(config, indent=indent, sort_keys=sort_keys)

  @strict_types
  def check_all_err_msg(self) -> None:
    if ERR_MSG_SET:
      err_msgs = "\n"
      for i, err_msg in enumerate(ERR_MSG_SET, start = 1):
        err_msgs += f"======= ❌ ERROR {i} =======\n{err_msg}\n\n"
      raise LogicError(err_msgs)
    
  @strict_types
  def build(self, output_file: str = "", json_indent: Optional[int] = 2, extra_fields: Optional[dict] = None) -> None:
    """
    构建 json config 内容并导出到指定文件, 若未指定文件路径则输出到 stdout

    参数说明
    ------
    `output_file`: [str] 输出文件的绝对路径

    `extra_fields`: [dict] 选填项，为生成的 json config 添加额外的自定义内容
    """
    self._check_unused_attr()

    self.check_all_err_msg()

    content = self.to_json(indent=json_indent, extra_fields=extra_fields)
    if output_file:
      with open(output_file, "w") as config_file:
        config_file.write(content)
      print(f"✅ build succeed to file: {output_file}", file=sys.stderr)
    else:
      print(content)
    if self.__is_auto_delete and (self.__auto_delete_ignore_unused_attr or self.__auto_delete_ignore_no_source_attr):
      if self.__kess_service_name not in self.__json_settings:
        self.__json_settings[self.__kess_service_name] = dict()
      self.__json_settings[self.__kess_service_name]["IGNORE_UNUSED_ATTR"] = sorted(self.__auto_delete_ignore_unused_attr)
      self.__json_settings[self.__kess_service_name]["IGNORE_NO_SOURCE_ATTR"] = sorted(self.__auto_delete_ignore_no_source_attr)
      with open(self.__settings_path, "w") as config_file:
        config_file.write(json.dumps(self.__json_settings))
    if os.getenv("DRAGONFLY_AUTO_BUILD", 'false') == 'true':
      self.infer_build_files()
    return self
  
  @strict_types
  def build_blame_attrs(self, output_file: str = "") -> None:
    blame_attrs_to_processors = {}
    for attr, attr_config in self.__blame_attrs_to_processors.items():
      blame_attrs_to_processors[attr] = {"input": list(attr_config["input"]), "output": list(attr_config["output"])}
    content = json.dumps(blame_attrs_to_processors, indent=2, sort_keys=True)
    if output_file:
      with open(output_file, "w") as attr_file:
        attr_file.write(content)
    else:
      print(content)

  @strict_types
  def draw(self, dag_folder: str = "", dag_format: str = "svg", request_type: Optional[str] = None, keep_gv_file: bool = False,
           to_dragonfly_viz = False, mode: str = "remote", draw_branch_controller_output: bool = True, hide_ab_param_node: bool = False, output_file: str = ""):
    """
    构建 json config 内容并导出到指定文件, 若未指定文件路径则输出到 stdout

    支持两种配置模式：
      1. 不带 to_dragonfly_viz 参数（默认为 False）, 使用 graphviz 生成 dag 图。
      2. 设置参数 to_dragonfly_viz=True 和 mode="remote", 使用 dragonfly-viz 网站生成 dag 图。

    参数说明
    ------
    `to_dragonfly_viz`: [bool] 使用 dragonfly-viz 可视化网站，默认为 false，用来模式区分

    `request_type`: [str|list] 需要绘制 DAG 图的 request_type 列表，默认全绘制（每个 request_type 生成一个 DAG 图）

    模式一：graphviz 方式

    `dag_folder`: [str] DAG 图输出文件夹的绝对路径，默认为当前执行路径

    `dag_format`: [str] DAG 图的文件格式类型，可选值: gv/pdf/svg/png/jpg/..., 默认为 svg, 更多的可选格式可参照[这里](https://graphviz.org/doc/info/output.html)

    `keep_gv_file`: [bool] 除了生成指定格式的图文件之外，是否同时生成一份 .gv 文件，默认为 False

    模式二：dragonfly-viz 方式

    `mode`: [string] 选配项 可配置为 local 或者 remote。默认为 remote
      - `mode` 为 "remote"，则会直接上传到 dragonfly viz 网站。一般用于 pipeline 中上传。
      - `mode` 为 "local" ，则会在本地生成 graph_def 文件，可以在 dragonfly viz 网站上传 graph_def 文件进行可视化。一般用于开发人员本地生成，避免覆盖线上的 graph_def 文件。

    `draw_branch_controller_output`: [bool] 选配项，画出 branch controller 下游的线。避免 branch controller 下游太多导致图层次变乱。默认为 true, 如果觉得图层次不清晰，可以设置为 false。

    `hide_ab_param_node`: [bool] 选配项，将获取 ab 参数的节点简化, 避免连线太多。在 processor 信息中添加 _input_ab_params_ 的信息，默认为 false。

    `output_file`: [str] 选配项，输出到指定文件的绝对路径。若未指定则输出到 stdout

    """
    if to_dragonfly_viz:
      url = self.__draw_dragonviz_dag(request_type, mode, draw_branch_controller_output, hide_ab_param_node)
      if output_file:
        with open(output_file, "w") as config_file:
          config_file.write(url)
        print(f"✅ viz build succeed to file: {output_file}, url: {url}", file=sys.stderr)
      return self

    if request_type:
      if isinstance(request_type, str):
        request_type = [request_type]
      if not isinstance(request_type, (list, tuple)):
        raise ArgumentError("request_type 参数需为字符串或字符串列表类型")
      for t in request_type:
        if t not in self.__request_type_config:
          raise ArgumentError(f"request_type {t} 不存在")
    else:
      request_type = self.__request_type_config.keys()

    for t in request_type:
      flows = [self.__leaf_flow_map[v] for v in self.__request_type_config[t]]
      self.__draw_dag(flows, t, dag_folder, dag_format, not keep_gv_file)
    return self


  @strict_types
  def __draw_dragonviz_dag(self, request_type: Optional[str] = None, mode: str = "remote", draw_branch_controller_output: bool = True, hide_ab_param_node: bool = False):
      try:
        import google.protobuf
      except ImportError:
        raise ImportError("import protobuf 失败, pip3 install protobuf 进行安装")
      try:
        import boto3
      except ImportError:
        raise ImportError("import boto3 失败, pip3 install boto3 进行安装")
      import urllib
      from .visualization.dragon_graph_pb2 import ServiceInfo
      from .visualization.dag import gen_graph_def

      if request_type:
        if isinstance(request_type, str):
          request_type = [request_type]
        if not isinstance(request_type, (list, tuple)):
          raise ArgumentError("request_type 参数需为字符串或字符串列表类型")
        for t in request_type:
          if t not in self.__request_type_config:
            raise ArgumentError(f"request_type {t} 不存在")
      else:
        request_type = self.__request_type_config.keys()

      kess_name = self.__kess_service_name
      if "--dryrun" in sys.argv:
        pos = sys.argv.index("--dryrun") + 1
        arg = sys.argv[pos] if pos < len(sys.argv) else ""
        dryrun_kess_name = "" if arg.startswith("--") else arg
        kess_name = dryrun_kess_name if dryrun_kess_name else f"{self.__kess_service_name}Dryrun"
      kwargs = {"mode": mode, "draw_branch_controller_output": draw_branch_controller_output, "hide_ab_param_node": hide_ab_param_node, "dragon_config_version": get_config_version()}

      all_request_types = set(self.__request_type_config.keys())
      dragonviz_url = "https://dragonfly-viz.corp.kuaishou.com/#graphs"
      for rt in request_type:
        service_info = ServiceInfo()
        service_info.service_name = kess_name
        service_info.current_request_types.append(rt)
        service_info.is_default = (self.__default_request_type == rt)
        service_info.other_request_types.extend(all_request_types - set([rt]))
        if self.ENABLE_PROCESSOR_STABLE_NAME:
          service_info.naming_mode = ServiceInfo.NamingMode.STABLE_NAMING
        else:
          service_info.naming_mode = ServiceInfo.NamingMode.NOT_STABLE_NAMING

        flow_names = self.__request_type_config[rt]
        flows = [self.__leaf_flow_map[flow_name] for flow_name in flow_names]
        kwargs["$service_info"] = service_info
        gen_graph_def(leaf_flows = flows, service_name = kess_name, request_type = rt, **kwargs)
        if mode == "remote":
          dragonviz_web_params = {"selectedRemoteFile": f"{kess_name}::{rt}", 'selectedRemoteVersion': get_config_version()}
          dragonviz_url = f"{dragonviz_url}&{urllib.parse.urlencode(dragonviz_web_params)}"
          print(f"✅ draw dag for {rt} success, open link: {dragonviz_url}", file=sys.stderr)
      if mode == "local":
        print(f"✅ gen graphdef success, please upload to dragonviz website: {dragonviz_url}", file=sys.stderr)
      return dragonviz_url

  @strict_types
  def __draw_dag(self, leaf_flows: list, request_type: str, output_folder: str, format: str, cleanup: bool) -> None:
    """
    构建一组 LeafFlow 的 DAG 流程图，输出到指定文件夹
    """
    try:
      from graphviz import Digraph
    except:
      raise Exception("缺少 graphviz 库，安装命令: pip install graphviz")
    dag_name = f"DAG-{self.__kess_service_name}-{request_type}"
    info = [
      f"DAG drawn by Dragonfly v{__version__}",
      f"Service: {self.__kess_service_name}",
      f"RequestType: {request_type}",
      f"Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
    ]
    comment = "\n// ".join(info)
    g = Digraph(name=dag_name, comment=comment, directory=output_folder)

    def draw_flow(index, c, flow, parent_processor = None):
      graph_label = f"{flow.name} ({parent_processor.name})" if parent_processor else flow.name
      c.attr(color='grey', label=graph_label, fontsize="20", labeljust="l")
      c.attr('node', shape='box')
      if parent_processor and parent_processor.is_async():
        c.attr(style='filled', fillcolor='#088cdb4d')

      flow_start = f"flow_start-{flow.name}_{index}"
      flow_end = f"flow_end-{flow.name}_{index}"
      virtual_node_attr = dict(shape="point", width="0.16", style="filled", color="grey")
      c.node(flow_start, **virtual_node_attr)
      c.node(flow_end, **virtual_node_attr)

      def get_node_id(i):
        sub_flow = flow._processors[i].get_sub_flow()
        return f"flow_start-{sub_flow.name}_{i}" if sub_flow else f"proc-{flow.name}_{index}-{i}"

      def gen_multi_output_edge(source_node, start) -> int:
        downstream_set = set()
        def link_edge_to(i) -> bool:
          if flow._processors[i].name in downstream_set:
            return False
          sub_flow = flow._processors[i].get_sub_flow()
          target = f"flow_start-{sub_flow.name}_{i}" if sub_flow else get_node_id(i)
          c.edge(source_node, target)
          return True

        output_edge_num = 0
        i = start + 1
        while i < len(flow._processors) and flow._processors[i].is_async():
          downstream_name = flow._processors[i]._config.get("downstream_processor")
          if not downstream_name:
            break
          downstream_set.add(downstream_name)
          if link_edge_to(i):
            output_edge_num += 1
          i += 1
        if i < len(flow._processors):
          if link_edge_to(i):
            output_edge_num += 1
        return output_edge_num

      gen_multi_output_edge(flow_start, -1)
      for i in range(len(flow._processors)):
        processor = flow._processors[i]
        sub_flow = processor.get_sub_flow()
        if sub_flow:
          # NOTE(fangjianbing): subgraph name 必须以 cluster_ 开头
          with c.subgraph(name=f"cluster_{flow.name}_{i}") as cc:
            _, processor_id = draw_flow(i, cc, sub_flow, processor)
        else:
          processor_id = get_node_id(i)
          attrs = {}
          attrs["style"] = "filled"
          attrs["fillcolor"] = "#088cdb4d" if processor.is_async() else "white"
          label = processor.name
          if processor.raw_name.startswith("_branch_controller"):
            attrs["shape"] = "ellipse"
            attrs["style"] = "filled"
            attrs["fillcolor"] = "lightgrey"
            lua_script = processor._config.get("lua_script", "")
            begin_pattern = "() if ("
            begin = lua_script.find(begin_pattern)
            if begin >= 0:
              end = lua_script.find(") then return false else return true end end")
              if end >= 0:
                label += f"\n({lua_script[begin + len(begin_pattern):end]})"
                # label = "\nand ".join(label.split(" and "))
          c.node(processor_id, label=label, **attrs)
        if processor.is_async() and processor._config.get("downstream_processor"):
          downstream = processor._config.get("downstream_processor")
          downstream_index = next((n for n, p in enumerate(flow._processors[i:]) if p.name == downstream), -1)
          node_id = get_node_id(i + downstream_index) if downstream_index >= 0 else flow_end
          c.edge(processor_id, node_id)
        elif gen_multi_output_edge(processor_id, i) == 0:
          c.edge(processor_id, flow_end)
      return flow_start, flow_end

    g.node("START", shape="Msquare", style="filled", fillcolor="lightgray" )
    g.node("END", shape="Msquare", style="filled", fillcolor="lightgray")
    prev_node_id = "START"
    for k, flow in enumerate(leaf_flows):
      # NOTE(fangjianbing): subgraph name 必须以 cluster_ 开头
      with g.subgraph(name=f"cluster_{k}") as c:
        head_id, tail_id = draw_flow(k, c, flow)
        g.edge(prev_node_id, head_id)
        prev_node_id = tail_id
    g.edge(prev_node_id, "END")

    g.attr(label="\n" + "\n".join(info) + "\n\n", fontsize="20")
    g.render(format=format, cleanup=cleanup)

  @strict_types
  def __auto_inject_global_holder(self, leaf_flows: list) -> None:
    """ 自动添加 CommonRecoAutoAdjustEnricher 对应的 CommonRecoGlobalHolderObserver """
    for flow in leaf_flows:
      if flow._has_auto_adjust_injected():
        break
      for processor in flow._processors:
        if isinstance(processor, CommonRecoAutoAdjustEnricher):
          if "history_input_save_mod" not in processor._config:
            processor._config["history_input_save_mod"] = "local"
          if "adjust_function" not in processor._config:
            processor._config["adjust_function"] = "pid"
          if "adjust_output" in processor._config and "counter_name" not in processor._config:
            processor._config["counter_name"] = processor._config["adjust_output"]
        if isinstance(processor, CommonRecoAutoAdjustEnricher) and processor._config["history_input_save_mod"] == "local":
          cfg = copy.deepcopy(processor._config)
          for key in ["window_size", "windows_num", "numerator", "denominator", "counter_name"]:
            if key in processor._config:
              processor._config.pop(key)
          for key in ["history_input_save_mod", "adjust_function", "kp", "ki", "kd", "adjust_output"]:
            if key in cfg:
              cfg.pop(key)
          leaf_flows[-1]._add_processor(CommonRecoGlobalHolderObserver(cfg))
      flow._finish_auto_adjust_inject()

  @strict_types
  def __auto_inject_sample_list_user_attr(self, leaf_flows: list) -> None:
    """ 自动添加获取 sample_list user 属性的 Processor """
    for flow in leaf_flows:
      if any(isinstance(x, CommonRecoSampleListCommonAttrEnricher) for x in flow._processors):
        # 已存在 sample_list enricher 则不再处理
        return

      _, processor = find_processor(flow._processors, lambda x: x.depend_on_sample_list_user_info())
      if processor:
        if not self.__sample_list_kess_service:
          raise ArgumentError("未设置 sample_list 的 kess name, 请先通过 setup_sample_list() 设置")
        config = {
          "kess_service": self.__sample_list_kess_service,
          "timeout_ms": self.__sample_list_timeout_ms,
        }
        sample_list_enricher = CommonRecoSampleListCommonAttrEnricher(config)
        sample_list_enricher.downstream = processor
        flow._add_processor(sample_list_enricher, position=0)
        return

  @strict_types
  def __gen_auto_inject_item_attrs_config(self, inject_attrs: set, item_table: str) -> dict:
    attrs = set()
    from_common_attrs = set()
    from_item_attrs = set()
    has_reco_results = False
    browse_set_count = None
    for attr in inject_attrs:
      name, channel = extract_attr_and_channel_name(attr)
      if name:
        check_arg(len(name) > 1, f"自动填充的正排属性存在可疑的 attr 名称: {name}")
      attrs.add(name)
      if not channel:
        has_reco_results = True
      elif is_browse_set_channel(channel):
        browse_set_count = int(channel[1:])
      elif is_item_attr_channel(channel):
        from_item_attrs.add(channel[1:])
      else:
        from_common_attrs.add(channel)
    if os.environ.get("CHECK_TALBE_DEPENDENCY", "false") == "true":
      check_arg(any(extract_table_and_attr_name(attr)[0] != item_table for attr in attrs), "自动填充的正排属性不能属于多个table！")
    cfg = { "attrs": sorted(extract_table_and_attr_name(attr)[1] for attr in attrs), "no_overwrite": True }
    item_source = {}
    if not has_reco_results:
      item_source["reco_results"] = False
    if browse_set_count is not None:
      item_source["latest_browse_set_item"] = browse_set_count
    if from_common_attrs:
      item_source["common_attr"] = sorted(from_common_attrs)
    if from_item_attrs:
      item_source["item_attr"] = sorted(from_item_attrs)
    if item_source:
      cfg["additional_item_source"] = item_source
    return cfg

  @strict_types
  def __auto_inject_or_check_item_attr(self, leaf_flows: list, input_existing_attrs: set) -> None:
    """ 自动添加获取正排属性的 Processor """
    if self.AUTO_INJECT_ITEM_ATTR and self.__index_source != IndexSource.NONE:
      for processor in iterate_all_processors(leaf_flows):
        if not processor.no_check() and processor.handle_subset_items() and get_all_output_item_attrs(processor):
          raise LogicError(f"❌ {processor.get_type_alias()} 在配置了 range_start / range_end 的情况下无法启用自动填充正排属性功能")

    def __update_existing_attrs(processor, existing_attrs: set, reserved_output_attrs: set) -> set:
      def __clear_attrs(existing_attrs: set, table: str) ->set:
        if os.environ.get("CHECK_TALBE_DEPENDENCY", "false") == "false":
          existing_attrs.clear()
        else:
          remove_attrs = [attr for attr in existing_attrs if extract_table_and_attr_name(attr)[0] == table]
          for attr in remove_attrs:
            existing_attrs.remove(attr)
        return existing_attrs

      new_output_item_attrs = get_all_output_item_attrs(processor)
      if self.AUTO_RESET_EXISTING_ITEM_ATTR and processor.output_item_tables:
        # 如果中途碰到了 Retriever 说明有新 item 加入, existing_attrs 需清理重来,
        # 但保留之前由 retriever 产生的 item_attr, 因为这些 attr 期望就是只有部分
        # item 才有的, 后续无需保证完整性
        reserved_output_attrs.update(new_output_item_attrs)
        for attr in existing_attrs - reserved_output_attrs:
          _, channel = extract_attr_and_channel_name(attr)
          # 如果是非 item level 的各个 channel 下的 output_item_attr, 后续也应保留其存在性
          if channel and not is_item_attr_channel(channel):
            reserved_output_attrs.add(attr)
        if processor.reset_existing_item_attrs():
          for table in processor.output_item_tables:
            __clear_attrs(existing_attrs, table)
        existing_attrs.update(reserved_output_attrs)
      else:
        existing_attrs.update(new_output_item_attrs)

      return new_output_item_attrs


    existing_attrs = set(input_existing_attrs)
    reserved_output_attrs = set(input_existing_attrs)

    if self.AUTO_INJECT_ITEM_ATTR and self.__index_source in (IndexSource.LOCAL, IndexSource.LOCAL_ATTR_INDEX, IndexSource.FLAT_INDEX):
      # 对于本地索引, 在每个 Processor 之前都按需获取 item_attr
      for flow in leaf_flows:
        if flow._has_local_item_attr_auto_injected():
          missing_attrs = flow._get_input_item_attrs_before_auto_inject() - existing_attrs
          if missing_attrs:
            raise LogicError(f"❌ LeafFlow {flow.name} 无法被共享使用，存在缺失的 item_attr 依赖: {missing_attrs}")
          else:
            existing_attrs.update(flow._get_output_item_attrs_after_auto_inject())
            continue
        flow_input_attrs = existing_attrs.copy()
        inject_pos_and_config_pairs = []
        for i, processor in enumerate(flow._processors):
          inject_attrs = get_all_input_item_attrs(processor) - existing_attrs
          if inject_attrs:
            cfg = self.__gen_auto_inject_item_attrs_config(inject_attrs, flow.item_table)
            if "skip" in processor._config:
              dependencies = filter(lambda x, inject_attrs=inject_attrs: has_intersection(get_all_input_item_attrs(x), inject_attrs), flow._processors[i+1:])
              if all(x._config.get("skip") == processor._config["skip"] for x in dependencies):
                cfg["skip"] = processor._config["skip"]
            pair = (i, cfg)
            inject_pos_and_config_pairs.append(pair)
            existing_attrs.update(inject_attrs)
          __update_existing_attrs(processor, existing_attrs, reserved_output_attrs)
        # 逆序后再依次插入
        for pos, cfg in reversed(inject_pos_and_config_pairs):
          if self.__index_source == IndexSource.LOCAL:
            flow._add_processor(CommonRecoLocalIndexItemAttrEnricher(cfg), position=pos)
          elif self.__index_source == IndexSource.LOCAL_ATTR_INDEX:
            flow._add_processor(CommonRecoLocalAttrIndexItemAttrEnricher(cfg), position=pos)
          elif self.__index_source == IndexSource.FLAT_INDEX:
            cfg.update(self.__flat_index_config)
            cfg["use_dynamic_photo_store"] = True
            flow._add_processor(CommonRecoDistributedIndexFlatKvItemAttrEnricher(cfg), position=pos)
          else:
            raise ArgumentError(f"未知的 IndexSource: {self.__index_source}")
        flow._finish_local_item_attr_auto_inject(flow_input_attrs, existing_attrs.copy())

    elif self.AUTO_INJECT_ITEM_ATTR and self.__index_source == IndexSource.REMOTE:
      # 对于远程索引, 默认在合适的位置一次性获取所有需要的 item_attr, 也支持在必要的位置手动插入一个空配置的
      # CommonRecoRemoteIndexItemAttrEnricher 并为其自动填充所需的 attr 列表
      if not self.__remote_index_kess_service:
        raise ArgumentError("未设置远程索引的 kess name, 请先通过 setup_remote_index() 设置")
      if len(leaf_flows) != 1:
        raise LogicError("❌ 远程索引模式 IndexSource.REMOTE 仅支持单 LeafFlow 执行")

      existing_attrs_copy = existing_attrs.copy()
      init_enricher_pos = -1
      for i, processor in enumerate(leaf_flows[0]._processors):
        if isinstance(processor, CommonRecoRemoteIndexItemAttrEnricher):
          break
        if get_all_input_item_attrs(processor) - existing_attrs_copy:
          init_enricher_pos = i
          break
        existing_attrs_copy.update(get_all_output_item_attrs(processor))
      if init_enricher_pos >= 0:
        # 填充一个必要的初始 Enricher
        leaf_flows[0]._add_processor(CommonRecoRemoteIndexItemAttrEnricher({}), position=init_enricher_pos)

      enricher = None
      inject_attrs = set()

      def auto_inject_last_enricher(proc, inject_attrs):
        if proc is not None and inject_attrs:
          # 对上一个 CommonRecoRemoteIndexItemAttrEnricher 的 attrs 配置进行赋值
          cfg = self.__gen_auto_inject_item_attrs_config(inject_attrs, proc.item_table)
          custom_attrs = proc._config.get("attrs", [])
          if custom_attrs:
            cfg["attrs"] = sorted(set(cfg["attrs"] + custom_attrs))
          proc._config.update(cfg)
          inject_attrs.clear()

      for processor in leaf_flows[0]._processors:
        if isinstance(processor, CommonRecoRemoteIndexItemAttrEnricher):
          # 对上一个 CommonRecoRemoteIndexItemAttrEnricher 的 attrs 配置进行清算赋值
          auto_inject_last_enricher(enricher, inject_attrs)
          config = {
            "kess_service": self.__remote_index_kess_service,
            "timeout_ms": self.__remote_index_timeout_ms,
          }
          processor._config = { **config, **processor._config }
          if "attrs" not in processor._config:
            processor._config["attrs"] = []
          enricher = processor
          continue

        inject_attrs.update(get_all_input_item_attrs(processor) - existing_attrs)
        __update_existing_attrs(processor, existing_attrs, reserved_output_attrs)

      # for 循环结束后对最后一个 enricher 进行 attrs 填充
      auto_inject_last_enricher(enricher, inject_attrs)

    else:
      # 对于无索引模式, 不自动填充 item_attr, 但对 item_attr 依赖进行缺失检查
      if self.ENABLE_ATTR_CHECK and self.CHECK_NO_SOURCE_ATTR:
        custom_ignore_attrs = set(self.IGNORE_NO_SOURCE_ATTR)
        custom_ignore_attrs.update(self.__auto_delete_ignore_no_source_attr)
        prev_output_info = []
        trace_log = []
        for flow in leaf_flows:
          for i, processor in enumerate(flow._processors):
            missing_attrs = get_all_input_item_attrs(processor) - existing_attrs - custom_ignore_attrs
            missing_attrs = set(filter(lambda x: extract_table_and_attr_name(x)[1] not in custom_ignore_attrs, missing_attrs))
            if (os.environ.get("ENABLE_SUB_FLOW_SHARED_CONTEXT", "false") == "true"
                and processor.get_sub_flow()
                and getattr(processor, "_has_context_name", False)):
              missing_attrs -= self._shared_context_config_map[processor._config.get("common_reco_context_attr_name")] \
                              .processor_existing_item_attrs[processor]
            if not processor.no_check() and missing_attrs:
              ellipsis_dots = 0
              for is_retrieve, log, output_attrs in prev_output_info:
                if output_attrs is None:
                  ellipsis_dots += 1
                  continue
                has_same_output = bool(output_attrs & missing_attrs)
                if not has_same_output and not is_retrieve:
                  ellipsis_dots += 1
                  continue
                if ellipsis_dots > 0:
                  trace_log.append("   " + "." * ellipsis_dots)
                  ellipsis_dots = 0
                if has_same_output:
                  log += (f" 新产出 item_attr: {sorted(output_attrs)}")
                trace_log.append(log)

              if ellipsis_dots > 0:
                trace_log.append("   " + "." * ellipsis_dots)
                ellipsis_dots = 0

              trace_log.append(f"🔴 [{self.__gen_position_info(flow, processor)}] 缺失 item_attr 输入: {sorted(missing_attrs)}")
              trace = '\n'.join(trace_log)
              existing_attrs_info = f"\n➡️ existing_attrs: {sorted(input_existing_attrs)}" if input_existing_attrs else ""
              if self.__is_auto_delete:
                self.__auto_delete_ignore_no_source_attr.update(missing_attrs)
              else:
                RaiseError(processor.name, processor.code_line, f"❌ 在该算子之前无法找到以下 item_attr 来源: {sorted(missing_attrs)}" if BLAME_ERROR_CODE
                           else f"\n>>>>>>{existing_attrs_info}\n{trace}\n<<<<<<\n❌ 在以上流程中无法找到以下 item_attr 来源: {sorted(missing_attrs)}")
            prev_existing_attr_num = len(existing_attrs)
            outputs = __update_existing_attrs(processor, existing_attrs, reserved_output_attrs)
            existing_attr_reduced = len(existing_attrs) < prev_existing_attr_num
            if existing_attr_reduced or outputs:
              if existing_attr_reduced:
                prev_output_info.append((True, f"🟡 [{self.__gen_position_info(flow, processor)}] 新召回 item（将导致前序 item_attr 产出缺失）", outputs.copy()))
              else:
                prev_output_info.append((False, f"🟢 [{self.__gen_position_info(flow, processor)}]", outputs.copy()))
            else:
              prev_output_info.append((False, None, None))

  @strict_types
  def __autowire_downstream(self, flow: LeafFlow, allow_dangling_downstream: bool) -> None:
    """ 自动添加 downstream_processor 配置, 若已存在 downstream_processor 配置则不会更改 """
    if flow._has_downstream_auto_detected():
      return
    for i, processor in enumerate(flow._processors):
      if not processor.is_async():
        continue
      assert isinstance(processor, (LeafRetriever, LeafEnricher, LeafObserver, LeafMixer)), "未知的异步 Processor 类型！"

      _, downstream_processor = flow._find_downstream(i)
      downstream_name = processor._config.get("downstream_processor")
      if downstream_name:
        # 如果手动指定了 downstream_processor 则进行存在性检查，并且覆盖自动匹配的 downstream_processor
        _, downstream_processor = find_processor(flow._processors[i+1:], lambda x: x.name == downstream_name)
        check_arg(downstream_processor, f"processor {processor.name or processor.get_type_alias()} 指定了不存在的 downstream_processor: {downstream_name}")

      if downstream_processor:
        processor.downstream = downstream_processor
        self.__add_downstream_info(processor)
      else:
        output_common_attrs = get_all_output_common_attrs(processor)
        output_item_attrs = get_all_output_item_attrs(processor)
        if isinstance(processor, (LeafRetriever, LeafObserver)) or allow_dangling_downstream or \
            has_intersection(self.get_return_item_attrs(), output_item_attrs) or \
            has_intersection(self.get_return_common_attrs(), output_common_attrs):
          if isinstance(processor, LeafObserver):
            # 如果是 LeafObserver 则指定 downstream_processor 为 "__none" 表示无下游,
            # 交给框架在请求返回后进行 wait
            processor._config["downstream_processor"] = "__none"
          else:
            # 如果在当前 flow 内未找到下游依赖则赋一个不太可能被用户使用的 Processor 名称 "__pipeline_end" 作为无效下游,
            # 交给框架在 pipeline 结束时兜底 wait 结果
            processor._config["downstream_processor"] = "__pipeline_end"
        elif not processor.no_check() and not self.I_AM_MASTER_DRIVER and self.ENABLE_ATTR_CHECK:
          ignore_attrs = set(self.IGNORE_UNUSED_ATTR)
          if output_common_attrs - ignore_attrs or output_item_attrs - ignore_attrs:
            attr_info = ""
            if output_common_attrs:
              attr_info += f"CommonAttr: {output_common_attrs}"
            if output_item_attrs:
              attr_info += f"ItemAttr: {output_item_attrs}"
            if not attr_info:
              attr_info += f"No output attr."
            err_msg = f"在 {flow.name} 流程中无法为 {processor.name or processor.get_type_alias()} 找到下游依赖，请检查该 processor 所产出的 attr 是否未被使用！"
            raise LogicError(f"❌ {err_msg} {attr_info}")
          else:
            # 被忽略掉的异步产出也交由 leaf 在 pipeline 结束时兜底 wait 结果
            processor._config["downstream_processor"] = "__pipeline_end"
        else:
          if self.ALLOW_ASYNC_FALLBACK_TO_SYNC:
            # 其实这里应该 raise Error，但为了兼容存量问题代码的既有行为，这里不得不先保障数据正确性，选择退化为同步行为
            processor._config["downstream_processor"] = ""
          else:
            processor._config["downstream_processor"] = "__none"

    flow._finish_downstream_auto_detect()

  @strict_types
  def __fill_meta_data(self, leaf_flows: list) -> None:
    """ 自动填入 meta data 参数 """
    for processor in iterate_all_processors(leaf_flows):
      if "$metadata" not in processor._config:
        processor._config["$metadata"] = {
          "$input_common_attrs": set(),
          "$input_item_attrs": set(),
          "$output_common_attrs": set(),
          "$output_item_attrs": set(),
          "$modify_item_tables": set(),
        }

      output_item_attrs = get_all_output_item_attrs(processor, with_channel_name=False, with_table_name=False) - {"_SCORE_", "_REASON_"}
      input_item_attrs = get_all_input_item_attrs(processor, with_channel_name=False, with_table_name=False)
      input_common_attrs = get_all_input_common_attrs(processor)
      output_common_attrs = get_all_output_common_attrs(processor)

      # TODO: 临时移除 metadata 中的table信息，c++ 代码同步改完后加回
      meta_data = processor._config["$metadata"]
      meta_data["$output_item_attrs"].update(extract_attr_and_channel_name(attr_name)[0] for attr_name in output_item_attrs)
      meta_data["$input_item_attrs"].update(extract_attr_and_channel_name(attr_name)[0] for attr_name in input_item_attrs)
      meta_data["$input_common_attrs"].update(input_common_attrs)
      meta_data["$output_common_attrs"].update(output_common_attrs)
      meta_data["$modify_item_tables"].update(processor.modify_item_tables)

      processor._config["$metadata"] = meta_data
      # XXX(qianlei): 调用一次 input_common_attrs，保证填充 meta_data $eval_common_attrs
      get_all_input_common_attrs(processor)

  @strict_types
  def __check_sub_flow_processors(self, leaf_flows: list) -> None:
    """ sub_flow 不支持同实例重入，所以在同一个 request_type 中不能使用多个相同的 sub_flow 实例 """
    processor_names = set()
    for processor in iterate_all_processors(leaf_flows):
      if processor.get_sub_flow():
        assert processor.name
        if processor.name in processor_names:
          raise LogicError(f"❌ 禁止重复使用 sub_flow: {processor._config['flow_name']}，processor_name: {processor.name}")
        processor_names.add(processor.name)

  def __add_downstream_info(self, processor: LeafProcessor):
    if not self.AUTO_INJECT_META_DATA:
      return
    assert(processor.downstream is not None)
    # 处理异步过程中处理过的 item_attrs
    pure_attrs = set(filter(is_plain_attr, get_all_output_item_attrs(processor)))
    # TODO: 临时移除 metadata 中的table信息，c++ 代码同步改完后加回
    if pure_attrs:
      processor.downstream._config["$metadata"].update({"$downstream_item_attrs": sorted(
        set(processor.downstream._config["$metadata"].get("$downstream_item_attrs", [])) | pure_attrs)})
    # 处理是否处理过 ARRANGER or RETRIEVER
    if not processor.downstream._config.get('$downstream_new_result', False) and isinstance(processor, (
            LeafRetriever, LeafArranger)):
      processor.downstream._config["$metadata"].update(
        {"$downstream_new_result": True})

  @strict_types
  def __fill_traceback_config(self, leaf_flows: list) -> None:
    """ 自动添加默认 traceback 参数 """
    for processor in iterate_all_processors(leaf_flows):
      if "traceback" not in processor._config and self.DEFAULT_TRACEBACK_VALUE is not None:
        processor._config["traceback"] = bool(self.DEFAULT_TRACEBACK_VALUE)

  def __detect_preceding_attrs(self, processors, preceding_output_common_attrs, preceding_output_item_attrs):
    preceding_output_info_indices = [i for i, p in enumerate(processors) if p.need_preceding_output_info]
    if preceding_output_info_indices:
      last_index = preceding_output_info_indices[-1] + 1
      curr_preceding_output_common_attrs = preceding_output_common_attrs.copy()
      curr_preceding_output_item_attrs = preceding_output_item_attrs.copy()
      for processor in processors[:last_index]:
        processor.preceding_output_common_attrs = curr_preceding_output_common_attrs
        processor.preceding_output_item_attrs = curr_preceding_output_item_attrs
        sub_flow = processor.get_sub_flow()
        if sub_flow:
          self.__detect_preceding_attrs(sub_flow._processors,
                                        curr_preceding_output_common_attrs,
                                        set() if isinstance(processor, LeafRetriever) else curr_preceding_output_item_attrs)
          if processor._auto_detect_merge_common_attrs:
            curr_preceding_output_common_attrs |= sub_flow._produced_output_common_attrs
          if processor._auto_detect_merge_item_attrs:
            curr_preceding_output_item_attrs |= sub_flow._produced_output_item_attrs
        curr_preceding_output_common_attrs |= get_all_output_common_attrs(processor)
        curr_preceding_output_item_attrs |= get_all_output_item_attrs(processor)
        if os.environ.get("ENABLE_EMBEDDED_FLOW", "false") == "true":
          # NOTE(weiyilong): 认为 embedded flow 可以使用任何 processor 自身产生的 attr
          embedded_flows = processor.get_embedded_flows()
          if embedded_flows:
            for _, embedded_flow in embedded_flows:
              self.__detect_preceding_attrs(embedded_flow._processors,
                                            curr_preceding_output_common_attrs,
                                            curr_preceding_output_item_attrs)

  def __update_post_needed_attrs(self, post_processors,
                                 needed_common_attrs: set,
                                 needed_item_attrs: set):
    for proc in post_processors:
      needed_common_attrs |= get_all_input_common_attrs(proc)
      needed_item_attrs |= get_all_input_item_attrs(proc, with_channel_name=False)
      if proc.get_sub_flow():
        needed_common_attrs |= proc.get_sub_flow()._absent_input_common_attrs
        needed_item_attrs |= strip_channel_name(proc.get_sub_flow()._absent_input_item_attrs)
  
  def __adjust_post_needed_attrs(self, curr_processor, post_processors):
    delete_common_attrs = set()
    delete_item_attrs = set()
    for proc in post_processors:
      if (proc.get_sub_flow() and
        getattr(curr_processor, "_has_context_name", False) and getattr(proc, "_has_context_name", False) and
        curr_processor._config.get("common_reco_context_attr_name", "") == proc._config.get("common_reco_context_attr_name", None)): 
        delete_common_attrs |= proc.get_sub_flow()._absent_input_common_attrs
        delete_item_attrs |= strip_channel_name(proc.get_sub_flow()._absent_input_item_attrs)
    for proc in post_processors:
      if not proc.get_sub_flow():
        delete_common_attrs -= get_all_input_common_attrs(proc)
        delete_item_attrs -= get_all_input_item_attrs(proc, with_channel_name=False)
    curr_processor.succeeding_absent_item_attrs -= delete_item_attrs
    curr_processor.succeeding_absent_common_attrs -= delete_common_attrs
    pass
  
  def __autowire_sub_flow_attrs(self, processor):
    sub_flow = processor.get_sub_flow()
    if processor._auto_detect_pass_common_attrs:
      prev_pass_common_attrs = set(processor._config.get("pass_common_attrs", []))
      pass_common_attrs = prev_pass_common_attrs | sub_flow._absent_input_common_attrs
      if pass_common_attrs:
        processor._config["pass_common_attrs"] = sorted(pass_common_attrs)

    if sub_flow.loop_on:
      origin_common_attrs = set(processor._config.get("pass_common_attrs", []))
      origin_common_attrs.add(sub_flow.loop_on)
      processor._config["pass_common_attrs"] = sorted(origin_common_attrs)
    
    if os.environ.get("ENABLE_SUB_FLOW_SHARED_CONTEXT", "false") == "true" and getattr(processor, "_has_context_name", False):
      origin_common_attrs = set(processor._config.get("pass_common_attrs", []))
      config = self._shared_context_config_map[processor._config.get("common_reco_context_attr_name")]
      config.used_common_attrs |= origin_common_attrs & config.existing_common_attrs
      origin_common_attrs.difference_update(config.existing_common_attrs)
      processor._config["pass_common_attrs"] = sorted(origin_common_attrs)
      config.existing_common_attrs |= origin_common_attrs

    sub_flow._import_common_attrs = set(processor._config.get("pass_common_attrs", []))

    if processor._auto_detect_pass_item_attrs:
      prev_pass_item_attrs = set(processor._config.get("pass_item_attrs", []))
      pass_item_attrs = prev_pass_item_attrs | strip_channel_name(sub_flow._absent_input_item_attrs)
      if pass_item_attrs:
        if (os.environ.get("ENABLE_SUB_FLOW_SHARED_CONTEXT", "false") == "true"
            and getattr(processor, "_has_context_name", False)):
          pass_item_attrs_with_table_name = set()
        if os.environ.get("CHECK_TALBE_DEPENDENCY", "false") == "true":
          attr_names = set()
          for attr in pass_item_attrs:
            table_name, attr_name = extract_table_and_attr_name(attr)
            if table_name == processor.item_table:
              attr_names.add(attr_name)
              if (os.environ.get("ENABLE_SUB_FLOW_SHARED_CONTEXT", "false") == "true" 
                  and getattr(processor, "_has_context_name", False)):
                pass_item_attrs_with_table_name.add(attr)
          pass_item_attrs = attr_names
        if (pass_item_attrs
            and os.environ.get("ENABLE_SUB_FLOW_SHARED_CONTEXT", "false") == "true"
            and getattr(processor, "_has_context_name", False)):
            config = self._shared_context_config_map[processor._config.get("common_reco_context_attr_name")]
            if os.environ.get("CHECK_TALBE_DEPENDENCY", "false") == "true":
              for attr in config.processor_existing_item_attrs[processor]:
                table_name, attr_name = extract_table_and_attr_name(attr)
                if attr_name in pass_item_attrs:
                  config.used_item_attrs.add(attr)
                  pass_item_attrs.remove(attr_name)
                  pass_item_attrs_with_table_name.remove(attr)
            else:
              config.used_item_attrs |= pass_item_attrs & config.processor_existing_item_attrs[processor]
              pass_item_attrs.difference_update(config.processor_existing_item_attrs[processor])
        processor._config["pass_item_attrs"] = sorted(pass_item_attrs)
      if (pass_item_attrs
        and os.environ.get("ENABLE_SUB_FLOW_SHARED_CONTEXT", "false") == "true"
        and getattr(processor, "_has_context_name", False)):
        config = self._shared_context_config_map[processor._config.get("common_reco_context_attr_name")]
        if os.environ.get("CHECK_TALBE_DEPENDENCY", "false") == "true":
          config.total_existing_item_attrs |= pass_item_attrs_with_table_name
        else:
          config.total_existing_item_attrs |= pass_item_attrs

    sub_flow._import_item_attrs = set(processor._config.get("pass_item_attrs", []))

    if processor._auto_detect_merge_common_attrs:
      merge_common_attrs = processor.succeeding_absent_common_attrs & sub_flow._produced_output_common_attrs
      prev_merge_common_attrs = set(processor._config.get("merge_common_attrs", []))
      merge_common_attrs |= prev_merge_common_attrs
      if merge_common_attrs:
        processor._config["merge_common_attrs"] = sorted(merge_common_attrs)
    sub_flow._export_common_attrs = extract_attr_names(processor._config.get("merge_common_attrs", []), "name")

    if processor._auto_detect_merge_item_attrs:
      merge_item_attrs = processor.succeeding_absent_item_attrs & strip_channel_name(sub_flow._produced_output_item_attrs)
      prev_merge_item_attrs = set(processor._config.get("merge_item_attrs", []))
      merge_item_attrs |= prev_merge_item_attrs
      if merge_item_attrs:
        if os.environ.get("CHECK_TALBE_DEPENDENCY", "false") == "true":
          attr_names = set()
          for attr in merge_item_attrs:
            table_name, attr_name = extract_table_and_attr_name(attr)
            if table_name == processor.item_table:
              attr_names.add(attr_name)
          merge_item_attrs = attr_names
        processor._config["merge_item_attrs"] = sorted(merge_item_attrs)
    sub_flow._export_item_attrs = extract_attr_names(processor._config.get("merge_item_attrs", []), "name")

    if isinstance(processor, CommonRecoPipelineMixer):
      if processor._auto_detect_input_item_tables:
        pass_item_attrs = strip_channel_name(sub_flow._absent_input_item_attrs)
        processor._config["input_tables"] = extract_table_attrs_config(pass_item_attrs)
      sub_flow._import_item_attrs = aggregate_table_attrs_config(processor._config["input_tables"])

      if processor._auto_detect_retrieve_item_tables or processor._auto_detect_enrich_item_tables:
        merge_item_attrs = processor.succeeding_absent_item_attrs & strip_channel_name(sub_flow._produced_output_item_attrs)
        merge_table_attrs = extract_table_attrs_config(merge_item_attrs)
        if processor._auto_detect_retrieve_item_tables:
          retrieve_tables = []
          for config in merge_table_attrs:
            if config["table_name"] in sub_flow._produced_output_item_tables:
              retrieve_tables.append(config)
          processor._config["retrieve_tables"] = retrieve_tables
        if processor._auto_detect_enrich_item_tables:
          input_tables = set(v["table_name"] for v in processor._config["input_tables"])
          enrich_tables = []
          for config in merge_table_attrs:
            if config["table_name"] in input_tables:
              enrich_tables.append(config)
          processor._config["enrich_tables"] = enrich_tables
      sub_flow._export_item_attrs = aggregate_table_attrs_config(processor._config["retrieve_tables"] + processor._config["enrich_tables"])

  def __validate_and_update_shared_context_state(self, processor, level: int):
    if not hasattr(processor, "_has_context_name") or not processor._has_context_name:
      return
    context_name = processor._config.get("common_reco_context_attr_name")
    if not hasattr(self, '_shared_context_config_map'):
      RaiseError(processor.name, processor.code_line, f"该processor指定的context: {context_name}, 没有init")
    if context_name not in self._shared_context_config_map:
      RaiseError(processor.name, processor.code_line, f"该processor指定的context: {context_name}, 没有init")
    config = self._shared_context_config_map[context_name]
    if level != config.level:
      RaiseError(processor.name, processor.code_line, f"该processor指定的context: {context_name}, 和之前processor不在一个level")
    if config.pass_common_attrs_in_request != processor.pass_common_attrs_in_request() or config.pass_browse_set != bool(processor._config.get("pass_browse_set", True)):
      RaiseError(processor.name, processor.code_line, f"该processor的pass_common_attrs_in_request属性或者pass_browse_set属性和context: {context_name}不一致")
    if "task_queue_id" in processor._config:
      if config.task_queue_id is None:
        config.task_queue_id = processor._config.get("task_queue_id")
      elif config.task_queue_id != processor._config.get("task_queue_id"):
        RaiseError(processor.name, processor.code_line, f"该processor的task_queue_id和之前共享context的processor不一致")
    sub_flow = processor.get_sub_flow()
    # output增加到existing
    if processor._auto_detect_merge_common_attrs:
      config.existing_common_attrs |= sub_flow._produced_output_common_attrs
    if processor._auto_detect_merge_item_attrs:
      config.total_existing_item_attrs |= sub_flow._produced_output_item_attrs
    config.existing_common_attrs |= get_all_output_common_attrs(processor)
    config.total_existing_item_attrs |= get_all_output_item_attrs(processor)
    config.processor_existing_item_attrs[processor] = config.total_existing_item_attrs.copy()

  def __register_shared_context_init_config(self, processor: CommonRecoContextEnricher, level: int):
    if not hasattr(self, '_shared_context_config_map'):
      self._shared_context_config_map: Dict[str, SharedContextConfig] = {}
    key = processor._config.get("output_common_attr")
    value = SharedContextConfig(
        bool(processor._config.get("pass_common_attrs_in_request", False)),
        bool(processor._config.get("pass_browse_set", True)),
        level
    )
    self._shared_context_config_map[key] = value

  def __autowire_embedded_flow_attrs(self, processor):
    embedded_flows = processor.get_embedded_flows()

    # NOTE(weiyilong): 实现和 LeafFlow._absent_input_common_attrs 基本是一致的
    absent_common_attrs = set()
    existing_common_attrs = get_all_output_common_attrs(processor)  # 认为 embedded flow 可以使用任何 processor 自身产生的 attr
    used_common_attrs = set()
    absent_item_attrs = set()
    existing_item_attrs = get_all_output_item_attrs(processor)  # 认为 embedded flow 可以使用任何 processor 自身产生的 attr
    used_item_attrs = set()
    for _, embedded_flow in embedded_flows:
      absent_common_attrs |= embedded_flow._absent_input_common_attrs - existing_common_attrs
      existing_common_attrs |= embedded_flow._produced_output_common_attrs
      used_common_attrs |= embedded_flow._used_common_attrs
      absent_item_attrs |= embedded_flow._absent_input_item_attrs - existing_item_attrs
      existing_item_attrs |= embedded_flow._produced_output_item_attrs
      used_item_attrs |= embedded_flow._used_item_attrs

    processor._config["embedded_flow"] = {
      'input_common_attrs': absent_common_attrs,
      'output_common_attrs': existing_common_attrs,
      'input_item_attrs': absent_item_attrs,
      'output_item_attrs': existing_item_attrs,
      'used_common_attrs': used_common_attrs,
      'used_item_attrs': used_item_attrs,
    }

  @strict_types
  def __check_no_source_common_attr(self, leaf_flows: list, existing_attrs: set) -> None:
    """ 检查是否存在缺失来源的 common attr """
    reserved_common_attrs = {"_USER_ID_", "_DEVICE_ID_", "_REQ_ID_", "_REQ_TYPE_", "_REQ_TIME_", "_REQ_NUM_", "_BROWSE_SET_SIZE_",
                             "_ABTEST_USER_TAG_NAMES_", "_ABTEST_USER_TAG_VALUES_"}
    existing_common_attrs = existing_attrs | reserved_common_attrs
    custom_ignore_attrs = set(self.IGNORE_NO_SOURCE_ATTR)
    trace_log = []
    continuous_no_output = 0
    for flow in leaf_flows:
      if flow.loop_index:
        existing_common_attrs.add(flow.loop_index)
      if flow.loop_value:
        existing_common_attrs.add(flow.loop_value)
      for processor in flow._processors:
        if processor.get_sub_flow():
          pass_common_attrs = set()
          pass_common_attrs.update(processor.get_sub_flow()._import_common_attrs)
          if processor.pass_common_attrs_in_request():
            pass_common_attrs.update(self.__common_attrs_from_request)
          if (os.environ.get("ENABLE_SUB_FLOW_SHARED_CONTEXT", "false") == "true" 
            and getattr(processor, "_has_context_name", False) 
            and hasattr(self, '_shared_context_config_map')):
            config = self._shared_context_config_map[processor._config.get("common_reco_context_attr_name")]
            pass_common_attrs.update(config.existing_common_attrs)
          self.__check_no_source_common_attr([processor.get_sub_flow()], pass_common_attrs)
        if os.environ.get("ENABLE_EMBEDDED_FLOW", "false") == "true":
          if processor.get_embedded_flows():
            pass_common_attrs = set()
            pass_common_attrs.update(existing_common_attrs)
            pass_common_attrs.update(get_all_output_common_attrs(processor))  # 认为 embedded flow 可以使用任何 processor 自身产生的 attr
            self.__check_no_source_common_attr([embedded_flow for _, embedded_flow in processor.get_embedded_flows()], pass_common_attrs)
        inputs = get_all_input_common_attrs(processor)
        missing_common_attrs = inputs - existing_common_attrs - custom_ignore_attrs
        if not processor.no_check() and missing_common_attrs:
          if continuous_no_output > 0:
            trace_log.append(f"   {'.' * continuous_no_output}")
            continuous_no_output = 0
          trace_log.append(f"🔴 [{self.__gen_position_info(flow, processor)}] 缺失 common_attr 输入: {sorted(missing_common_attrs)}")
          trace = '\n'.join(trace_log)
          if self.__is_auto_delete:
            self.__auto_delete_ignore_no_source_attr.update(missing_common_attrs)
          else:
            RaiseError(processor.name, processor.code_line, f"❌ 在该算子之前无法找到以下 common_attr 来源: {sorted(missing_common_attrs)}" if
                      BLAME_ERROR_CODE else f"\n>>>>>>\n{trace}\n<<<<<<\n❌ 在以上流程中无法找到以下 common_attr 来源: {sorted(missing_common_attrs)}")
        outputs = get_all_output_common_attrs(processor)
        existing_common_attrs.update(outputs)
        if outputs:
          if continuous_no_output > 0:
            trace_log.append(f"   {'.' * continuous_no_output}")
            continuous_no_output = 0
          trace_log.append(f"🟢 [{self.__gen_position_info(flow, processor)}] 新产出 common_attr: {sorted(outputs)}")
        else:
          continuous_no_output += 1

  def __gen_position_info(self, flow, processor) -> str:
    current_proc_name = processor.name or processor.get_type_alias()
    level_tag = "./" * flow.level
    return f"/{level_tag}{flow.name}/{current_proc_name}"

  def __detect_unused_attr(self, leaf_flows: list) -> list:
    """ 检查是否存在无用的 common 或 item attr """
    for i, flow in enumerate(leaf_flows):
      unused_common_attrs = flow._unused_common_attrs
      unused_item_attrs = flow._unused_item_attrs
      for f in leaf_flows[i+1:]:
        unused_common_attrs -= f._used_common_attrs
        unused_item_attrs -= f._used_item_attrs
      if flow.level == 0:
        unused_common_attrs -= self.get_return_common_attrs()
        unused_item_attrs -= self.get_return_item_attrs()
      unused_common_attrs.discard(LeafProcessor._SAMPLE_LIST_COMMON_ATTR_KEY)
      unused_common_attrs.difference_update(self.IGNORE_UNUSED_ATTR)
      unused_common_attrs.difference_update(self.__auto_delete_ignore_unused_attr)
      if os.environ.get("ENABLE_SUB_FLOW_SHARED_CONTEXT", "false") == "true":
        unused_common_attrs.difference_update(self.__shared_context_used_common_attr)
      unused_common_attrs = set(filter(lambda x: "_control_attr_" not in x, unused_common_attrs))
      ignore_item_attrs = set(self.IGNORE_UNUSED_ATTR) | {"_SCORE_", "_REASON_"} | self.__auto_delete_ignore_unused_attr
      if os.environ.get("ENABLE_SUB_FLOW_SHARED_CONTEXT", "false") == "true":
        ignore_item_attrs |= self.__shared_context_used_item_attr
      unused_item_attrs = set(filter(lambda x: x not in ignore_item_attrs \
                                      and extract_table_and_attr_name(extract_attr_and_channel_name(x)[0])[1] not in ignore_item_attrs,
                              unused_item_attrs))
      if flow.name not in self.__flow_unused_attrs:
        self.__flow_unused_attrs[flow.name] = [unused_common_attrs, unused_item_attrs]
      else:
        self.__flow_unused_attrs[flow.name][0] &= unused_common_attrs
        self.__flow_unused_attrs[flow.name][1] &= unused_item_attrs

    for processor in iterate_all_processors(leaf_flows):
      if processor.get_sub_flow():
        if os.environ.get("ENABLE_SUB_FLOW_SHARED_CONTEXT", "false") == "true" and getattr(processor, "_has_context_name", False):
          config = self._shared_context_config_map[processor._config.get("common_reco_context_attr_name")]
          self.__shared_context_used_common_attr = config.used_common_attrs
          self.__shared_context_used_item_attr = config.used_item_attrs
        self.__detect_unused_attr([processor.get_sub_flow()])


  def _check_unused_attr(self):
    if self.I_AM_MASTER_DRIVER or not self.ENABLE_ATTR_CHECK or not self.CHECK_UNUSED_ATTR:
      return

    all_flows = sorted(self.__request_type_config.values())
    for i, flow_names in enumerate(all_flows):
      if i > 0 and flow_names == all_flows[i-1]:
        continue
      leaf_flows = [self.__leaf_flow_map[name] for name in flow_names]
      self.__detect_unused_attr(leaf_flows)

    for name, (unused_common_attrs, unused_item_attrs) in self.__flow_unused_attrs.items():
      unused_common_attr_count = len(unused_common_attrs)
      unused_item_attr_count = len(unused_item_attrs)
      if unused_common_attr_count == 0 and unused_item_attr_count == 0:
        continue
      if self.__is_auto_delete:
        self.__auto_delete_ignore_unused_attr.update(unused_common_attrs | unused_item_attrs)
        continue
      flow = self.__leaf_flow_map[name]
      trace_log = []
      for processor in flow._processors:
        if processor.no_check():
          continue
        trace_info = ""
        if unused_common_attrs:
          problem_common_attrs = unused_common_attrs & get_all_output_common_attrs(processor)
          if problem_common_attrs:
            trace_info += f"\n  ↘️ common_attr: {sorted(problem_common_attrs)}"
            if BLAME_ERROR_CODE:
              RaiseError(processor.name, processor.code_line, f"在该 processor 中检测到无用 common_attr 产出：{sorted(problem_common_attrs)}")
        if unused_item_attrs:
          problem_item_attrs = unused_item_attrs & get_all_output_item_attrs(processor)
          if problem_item_attrs:
            trace_info += f"\n  ↘️ item_attr: {sorted(problem_item_attrs)}"
            if BLAME_ERROR_CODE:
              RaiseError(processor.name, processor.code_line, f"在该 processor 中检测到无用 item_attr 产出：{sorted(problem_item_attrs)}")
        if trace_info:
          trace_log.append(f"🔴 [{self.__gen_position_info(flow, processor)}]{trace_info}")
      if not PRINT_ALL_ERROR:
        trace = '\n'.join(trace_log)
        unused_count_info = ""
        if unused_common_attr_count > 0:
          unused_count_info += f"{unused_common_attr_count} 个无用 common attr"
        if unused_item_attr_count > 0:
          if unused_common_attr_count > 0:
            unused_count_info += " 和 "
          unused_count_info += f"{unused_item_attr_count} 个无用 item attr"
        err_msg = f"\n>>>>>>\n{trace}\n<<<<<<\n❌ 在 {'sub_flow' if flow.level > 0 else 'flow'} '{flow.name}' 中检测到以上 {unused_count_info} 产出，请检查相关配置是否不再使用或未同步更新。"
        err_msg += "\n更多详情介绍可查看：https://kstack.corp.kuaishou.com/question/3097"
        raise LogicError(err_msg)

  @strict_types
  def __check_readonly_attr(self, leaf_flows: list, init_readonly_common_attrs: set = set(), init_readonly_item_attrs: set = set(), init_flat_index_attrs: set = set()):
    """ 检查是否存在重复写入 readonly 的 common 或 item attr """
    readonly_common_attrs = set(init_readonly_common_attrs)
    readonly_item_attrs = set(init_readonly_item_attrs)
    flat_index_attrs = set(init_flat_index_attrs)
    ignore_rewrite_item_attrs = set()
    for processor in iterate_all_processors(leaf_flows):
      if isinstance(processor, CommonRecoDistributedIndexFlatKvItemAttrEnricher):
        attrs = get_all_output_item_attrs(processor, with_channel_name=False)
        readonly_item_attrs.update(attrs)
        flat_index_attrs.update(attrs)
        continue
      elif isinstance(processor, CommonRecoPipelineRetriever):
        ignore_rewrite_item_attrs = flat_index_attrs & extract_attr_names(processor._config.get("merge_item_attrs", []), "as")
        deep_copy = processor._config.get("deep_copy", not processor._config.get("no_copy", False))
        shadow_copy_attrs = set(processor._config.get("shadow_copy_attrs" if deep_copy else "pass_common_attrs", []))
        shadow_copy_attrs -= set(processor._config.get("deep_copy_attrs", []))
        if shadow_copy_attrs:
          self.__check_readonly_attr([processor.get_sub_flow()], init_readonly_common_attrs=shadow_copy_attrs,
                                     init_flat_index_attrs=flat_index_attrs)
      elif isinstance(processor, CommonRecoPipelineEnricher):
        ignore_rewrite_item_attrs = flat_index_attrs & extract_attr_names(processor._config.get("merge_item_attrs", []), "as")
        deep_copy = processor._config.get("deep_copy", False)
        shadow_copy_common_attrs = set(processor._config.get("shadow_copy_common_attrs" if deep_copy else "pass_common_attrs", []))
        shadow_copy_common_attrs -= set(processor._config.get("deep_copy_common_attrs", []))
        shadow_copy_item_attrs = set(processor._config.get("shadow_copy_item_attrs" if deep_copy else "pass_item_attrs", []))
        shadow_copy_item_attrs -= set(processor._config.get("deep_copy_item_attrs", []))
        self.__check_readonly_attr([processor.get_sub_flow()], init_readonly_common_attrs=shadow_copy_common_attrs,
            init_readonly_item_attrs=shadow_copy_item_attrs, init_flat_index_attrs=flat_index_attrs)
      elif isinstance(processor, CommonRecoPipelineArranger):
        ignore_rewrite_item_attrs = flat_index_attrs & set(processor._config.get("merge_item_attrs", []))
        shadow_copy_common_attrs = set(processor._config.get("pass_common_attrs", []))
        shadow_copy_common_attrs -= set(processor._config.get("deep_copy_common_attrs", []))
        shadow_copy_item_attrs = set(processor._config.get("pass_item_attrs", []))
        shadow_copy_item_attrs -= set(processor._config.get("deep_copy_item_attrs", []))
        self.__check_readonly_attr([processor.get_sub_flow()], init_readonly_common_attrs=shadow_copy_common_attrs,
            init_readonly_item_attrs=shadow_copy_item_attrs, init_flat_index_attrs=flat_index_attrs)

      if not processor.no_check():
        rewrite_common_attrs = readonly_common_attrs & get_all_output_common_attrs(processor)
        if rewrite_common_attrs:
          raise LogicError(f"🈲 {processor.name or processor.get_type_alias()} 对以下只读 common attr 进行了重复写入（被浅拷贝进 sub_flow 和从 flat index 中获取的 attr 都是只读的）: {sorted(rewrite_common_attrs)}")
        rewrite_item_attrs = readonly_item_attrs & get_all_output_item_attrs(processor) - ignore_rewrite_item_attrs
        if rewrite_item_attrs:
          raise LogicError(f"🈲 {processor.name or processor.get_type_alias()} 对以下只读 item attr 进行了重复写入（被浅拷贝进 sub_flow 和从 flat index 中获取的 attr 都是只读的）: {sorted(rewrite_item_attrs)}")

  @strict_types
  def __check_forbidden_attr(self, leaf_flows: list) -> None:
    if not self.FORBIDDEN_COMMON_ATTR and not self.FORBIDDEN_ITEM_ATTR:
      return
    forbidden_common_attrs = set(self.FORBIDDEN_COMMON_ATTR)
    forbidden_item_attrs = set(self.FORBIDDEN_ITEM_ATTR)
    for flow in leaf_flows:
      for _, processor in enumerate(flow._processors):
        if forbidden_common_attrs:
          used_forbidden_attrs = get_all_input_common_attrs(processor) & forbidden_common_attrs
          if used_forbidden_attrs:
            raise LogicError(f"🈲 在 flow {flow.name} 中检测到被禁止使用的 common_attr: {sorted(used_forbidden_attrs)}")
        if forbidden_item_attrs:
          used_forbidden_attrs = get_all_input_item_attrs(processor, with_table_name=False) & forbidden_item_attrs
          if used_forbidden_attrs:
            raise LogicError(f"🈲 在 flow {flow.name} 中检测到被禁止使用的 item_attr: {sorted(used_forbidden_attrs)}")

  @strict_types
  def __check_processor_name(self, leaf_flows: list) -> None:
    """ 检查是否每个 processor 都填写了 name """
    for processor in iterate_all_processors(leaf_flows):
      if not processor.raw_name:
        raise LogicError(f"❌ 必须为 {processor.get_type_alias()} 接口指定 name 配置，config: {processor._config}")

  @strict_types
  def __str__(self) -> str:
    return self.to_json()

  @strict_types
  def __auto_merge_processors(self, flow) -> None:
    for i, processor in enumerate(flow._processors):
      sub_flow = processor.get_sub_flow()
      if sub_flow:
        self.__auto_merge_processors(sub_flow)
      else:
        self.__merge_processor(processor, flow._processors[i+1:])
 
  @strict_types
  def __merge_processor(self, processor, other_processors) -> None:
    if not processor.enable_auto_merge:
      return
    if processor.is_merged:
      return 
    for other_processor in other_processors:
      if not type(processor) == type(other_processor):
        continue
      skip_config = processor._config.get("skip", None)
      other_skip_config = other_processor._config.get("skip", None)
      if not skip_config == other_skip_config:
        continue
      origin_config = processor._config.copy()
      origin_other_config = other_processor._config.copy()
      if processor.auto_merge_config(other_processor._config):
        other_processor.is_merged = True
      else:
        # 保持配置不变, 避免业务合并失败导致当前算子配置异常
        processor._config = origin_config
        other_processor._config = origin_other_config

  @strict_types
  def __remove_merged_processors(self, flow) -> None:
    for i in range(len(flow._processors) - 1, -1, -1):
      processor = flow._processors[i]
      sub_flow = processor.get_sub_flow()
      if sub_flow:
        self.__remove_merged_processors(sub_flow)
      else:
        if processor.is_merged:
          flow._processors.remove(flow._processors[i])

  def infer_build_files(self):
    module2processors = get_modules(self.__processor_and_modules)
    dragon_path = os.getenv("JOB_HOST_DIR", os.getenv("DRAGON_REPO_PATH", ""))
    if not dragon_path:
      return
    file_paths = [
      dragon_path + "/dragon/src/processor/common/",
      dragon_path + "/dragon/src/processor/ext/",
    ]
    process2file = {}
    for file_path in file_paths:
      file_names = get_all_cc_file_name(file_path)
      for f in file_names:
        p_s = get_processor_name(f)
        if p_s:
          for p in p_s:
            process2file[p] = f

    module2paths = {}
    for module, pro_set in module2processors.items():
      module2paths[module] = set()
      for p in pro_set:
        assert p in process2file and "processor/" in process2file[p], f"processor not find in dragon repo: {p}"
        idx = process2file[p].find("processor/")
        module2paths[module].add(process2file[p][idx:])

    module2files = {}
    for module, files in module2paths.items():
      t = Trie()
      t.build(list(files))
      module2files[module] = t.duplicate()

    with open(dragon_path + '/dragon/src/module_to_files.json', 'w') as file:
      json.dump(module2files, file, indent=2)
    return

class OfflineRunner:
  """
  OfflineRunner 对应一个离线 Pipeline Runner，内部可添加多个 LeafFlow 同时并行执行。
  """

  PASS_THROUGH_SETTINGS = [
    "AUTO_DETECT_DOWNSTREAM",
    "AUTO_INJECT_ITEM_ATTR",
    "AUTO_RESET_EXISTING_ITEM_ATTR",
    "AUTO_INJECT_SAMPLE_LIST_USER_ATTR",
    "ENABLE_ATTR_CHECK",
    "CHECK_UNUSED_ATTR",
    "IGNORE_UNUSED_ATTR",
    "CHECK_NO_SOURCE_ATTR",
    "IGNORE_NO_SOURCE_ATTR",
    "FORBIDDEN_COMMON_ATTR",
    "FORBIDDEN_ITEM_ATTR",
    "CHECK_COMMON_LOGIC",
    "AUTO_INJECT_META_DATA",
    "DEFAULT_TRACEBACK_VALUE",
    "DISABLE_LUA_PROCESSOR",
    "I_AM_MASTER_DRIVER",
    "ENABLE_PROCESSOR_AUTO_NAMING",
    "common_attr_types",
    "item_attr_types",
    "check_attr_type_for_tables",
    "MULTI_SUB_FLOW_THREAD_POOL",
    'PY_ENABLE_REMOTE_COMPILE',
    'PY_USE_REMOTE_DSO',
    'PY_PARALLEL_COMPILE',
    'PY_COMPILE_TOOL',
  ]

  @strict_types
  def __init__(self, service_identifier: str, embedding_table: Optional[dict] = None,
               index_source: IndexSource = IndexSource.NONE):
    # OfflineRunner 本来应该是一个独立于 LeafService 的概念，二者共享 PipelineManager 的抽象。
    # 但是由于 CommonLeaf 设计的时候并没有考虑到用于 OfflineRunner，因此当前没有把 PipelineManager 的
    # 配置与 LeafService 的在线服务的配置拆分开来，因此当前在 OfflineRunner 中内含一个 LeafService，
    # 实际上是一种临时的使用 LeafService 中 PipelineManager 代码的实现。
    # 后面如果要重构应该考虑把这两个概念拆分开。

    if not service_identifier:
      raise ArgumentError("service_identifier 必须非空")
    if service_identifier.startswith("grpc_"):
      raise ArgumentError("runner 的 service_identifier 不能以 grpc_ 开头")

    self.__leaf_service = LeafService(kess_name=service_identifier, index_source=index_source,
                                      common_attrs_from_request=[], item_attrs_from_request=[])

    # XXX(huiyiqun): 其实 OfflineRunner 也需要 Item Attr 缺失的检查，但是 CommonLeaf 中 hardcode 的
    # 索引在离线时都用不上，简单起见先禁用掉。
    self.__leaf_service.AUTO_INJECT_ITEM_ATTR = False

    # XXX(huiyiqun)：同上
    self.__leaf_service.AUTO_INJECT_SAMPLE_LIST_USER_ATTR = False

    # XXX(fangjianbing)：runner 的 processor 目前存在一些 common/item attr 同名混用的情况，
    # 会造成无源 item attr 的误检误报，暂时先对 runner 默认关闭该项检查
    self.__leaf_service.CHECK_NO_SOURCE_ATTR = False

    self.__pipeline_group = dict()

    self.__service_identifier = service_identifier

    self.__embedding_table = embedding_table


  @strict_types
  def add_leaf_flows(self, leaf_flows: Union[LeafFlow, list], name: str = "default_name", thread_num: int = -1, core_num_thread_ratio: float = 0.0):
    if name in self.__pipeline_group:
      raise LogicError(f"❌ 禁止重复添加同名 pipeline: {name}")
    if not isinstance(leaf_flows, list):
      leaf_flows = [leaf_flows]
    self.__leaf_service.add_leaf_flows(leaf_flows, request_type=name)
    pipeline = [flow.name for flow in leaf_flows]
    self.__pipeline_group[name] = {"pipeline": pipeline, "thread_num": thread_num, "core_num_thread_ratio" : core_num_thread_ratio}
    return self

  @strict_types
  def to_json(self, indent: Optional[int] = 2, sort_keys: bool = True, extra_fields: Optional[dict] = None) -> str:
    if not self.__pipeline_group:
      raise LogicError("❌ 尚未添加 LeafFlow")

    config = {}
    config["_DRAGONFLY_VERSION"] = __version__
    config["_DRAGONFLY_CREATE_TIME"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    config_version = get_config_version()
    if config_version:
      config["_CONFIG_VERSION"] = get_config_version()
    config["pipeline_manager_config"] = self._pipeline_manager_config
    config["runner_pipeline_group"] = self.__pipeline_group
    config["service_identifier"] = self.__service_identifier
    config["kess_config"] = dict()
    if self.__embedding_table:
      config["embedding_table"] = self.__embedding_table
    if extra_fields:
      config.update(extra_fields)
    if self.__leaf_service.common_attr_types:
      config["common_attr_types"] = sorted(self.__leaf_service.common_attr_types, key = lambda d: d["attr_name"])
    if self.__leaf_service.item_attr_types:
      config["item_attr_types"] = sorted(self.__leaf_service.item_attr_types, key = lambda d: (d["table_name"], d["attr_name"]))
    if self.__leaf_service.check_attr_type_for_tables:
      config["check_attr_type_for_tables"] = sorted(self.__leaf_service.check_attr_type_for_tables)
    return dump_to_json(config, indent=indent, sort_keys=sort_keys)

  @property
  def _pipeline_manager_config(self):
    return self.__leaf_service._pipeline_manager_config

  @strict_types
  def build(self, output_file: str = "", json_indent: Optional[int] = 2, extra_fields: Optional[dict] = None, sort_keys: bool = True) -> None:
    """
    构建 json config 内容并导出到指定文件, 若未指定文件路径则输出到 stdout

    参数说明
    ------
    `output_file`: [str] 输出文件的绝对路径

    `extra_fields`: [dict] 选填项，为生成的 json config 添加额外的自定义内容
    """
    self.__leaf_service._check_unused_attr()

    self.__leaf_service.check_all_err_msg()

    content = self.to_json(indent=json_indent, extra_fields=extra_fields, sort_keys=sort_keys)
    if output_file:
      with open(output_file, "w") as config_file:
        config_file.write(content)
      print(f"✅ build succeed to file: {output_file}", file=sys.stderr)
    else:
      print(content)
    return self


  def draw(self, dag_folder: str = "", dag_format: str = "svg", name: Optional[str] = None, keep_gv_file: bool = False,
           to_dragonfly_viz: bool = False, mode: str = "local", draw_branch_controller_output: bool = True, hide_ab_param_node: bool = False):
    self.__leaf_service.draw(dag_folder=dag_folder, dag_format=dag_format, request_type=name, keep_gv_file=keep_gv_file,
                             to_dragonfly_viz=to_dragonfly_viz, mode=mode, draw_branch_controller_output=draw_branch_controller_output, hide_ab_param_node=hide_ab_param_node)
    return self

  def executor(self):
    return self.__leaf_service.executor()

  def register_proto(self, file_name: str, content: str, deps: list = []):
    self.__leaf_service.register_proto(file_name, content, deps)
    return self

  def __getattr__(self, name):
    if name in self.PASS_THROUGH_SETTINGS:
      return getattr(self.__leaf_service, name)
    super().__getattr__(name)

  def __setattr__(self, name, value):
    if name in self.PASS_THROUGH_SETTINGS:
      return setattr(self.__leaf_service, name, value)
    super().__setattr__(name, value)
  
    
      

