#!/usr/bin/env python3
# coding=utf-8

# 注意: Python 单测需在 **云开发机** 上执行（因为 rpc mock 接口依赖 kess 环境）
# 配置的步骤说明（以下命令均在 dragon 的 kbuild workspace 根目录下执行）:
# 1. 二进制编译: ENABLE_COMMON_LEAF_PYTHON_WRAPPER=true COMMON_RECO_LEAF_EXTRA_PROCESSORS="gsu" kbuild build dragon/src dragon/server
# 2. 加载动态库: export LD_PRELOAD=./build_tools/gcc-8.3.0/lib64/libstdc++.so.6
# 3. 设置环境变量: export GRPC_CPU_CORES_USE_CONF=true
# 4. 设置环境变量: export PYTHONPATH=$PYTHONPATH:./dragon
# 5. 运行单测文件:  python3 dragon/dragonfly/unit_test/gsu_api_test.py
# 6. [可选] 单独跑某一个 testcase: python3 -m unittest dragon.dragonfly.unit_test.gsu_api_test.TestFlowFunc.xxxx

import os
import sys
import json
import struct
import base64
import unittest
import struct
import copy,math
import random
import time
import numpy as np
import itertools
# pip3 install -U infra-framework
from kconf.get_config import get_json_config
from dragonfly.common_leaf_dsl import LeafService, LeafFlow
from dragonfly.ext.gsu.gsu_api_mixin import GsuApiMixin
from dragonfly.unit_test.cluster_neighbors import cluster_neighbors

# LeafService.I_AM_MASTER_DRIVER = True

def encode_bytes(data: bytes) -> str:
  """
  PB bytes field need to be base64-encoded in json format.
  """
  return base64.b64encode(data).decode('utf-8')


def str2bytes(data: str) -> bytes:
  return bytes(data, 'utf-8')


def int2bytes(data: int) -> bytes:
  return struct.pack('<q', data)


def float2bytes(data: float) -> bytes:
  return struct.pack('<d', data)


def gen_key_sign(item_type: int, item_id: int) -> int:
  return item_type << 56 | item_id

def slot_high_val_low_sign(slot, value, high_bits=48):
  return (slot << high_bits) | (value & ((1 << high_bits) - 1))

class ColossusSimItemList:
  def __init__(self, user_id, schema):
    self.user_id = user_id
    self.schema = schema
    self.items = dict()  # dict * list 比 list * dict 性能应该会好一点
    self.item_num = 0
    for field_name, _, _ in schema: self.items[field_name] = list()

  def check_format(self):
    succ = True
    for key, vlist in self.items.items():
      if len(vlist) != self.item_num:
        succ = False
        print(f"field:{key} value list error, {len(vlist)} vs {self.item_num}")
    return succ

  def clear(self):
    self.item_num = 0
    self.items = dict()
    for field_name, _, _ in self.schema: self.items[field_name] = list()

  # 用于自定义初始化
  def add_item(self, item_fields):
    if (len(item_fields) != len(self.items)):
      print(f"input item_fields format error, {len(item_fields)} vs {len(self.items)}")
      return False
    for key in item_fields:
      if key not in self.items:
        print(f"unexpected input item field: {key}")
        return False
    self.item_num += 1
    for key, value in item_fields.items():
      self.items[key].append(value)

  # 懒得构造数据时，调用该函数, 提供了一种随机的初始化操作
  def random_initialize(self, item_num):
    self.item_num = item_num
    for field, size, format in self.schema:
      if format == "Q": self.items[field] = [x for x in np.random.randint(low=0, high=(1<<47)-1, size=item_num)]
      elif format == "I": self.items[field] = [x for x in np.random.randint(low=0, high=(1<<31)-1, size=item_num)]
      elif format == "H": self.items[field] = [x for x in np.random.randint(low=0, high=(1<<15)-1, size=item_num)]
      elif format == "q": self.items[field] = [x for x in np.random.randint(low=-((1<<47)-1), high=(1<<47)-1, size=item_num)]
      elif format == "i": self.items[field] = [x for x in np.random.randint(low=-(1<<31)-1, high=(1<<31)-1, size=item_num)]
      elif format == "h": self.items[field] = [x for x in np.random.randint(low=-((1<<15)-1), high=(1<<15)-1, size=item_num)]
      elif format == "e": self.items[field] = [x*10.0 for x in np.random.random(size=item_num)]
      elif format == "f": self.items[field] = [x*1000.0 for x in np.random.random(size=item_num)]
      elif format == "d": self.items[field] = [x*1000000.0 for x in np.random.random(size=item_num)]
      else:
        print(f"unsupport field's datatype: {format}")
        return False
    # for key, vlist in self.items.items(): print(f"{key} : ", vlist)
    return self.check_format()

  def serialize(self):
    # construct raw bytes
    item_size = 0
    for (field, size, format) in self.schema:
      item_size = item_size + size

    print(f"serialize item num: {self.item_num}, item_size: {item_size}")
    buf = bytearray(item_size * self.item_num)
    offset = 0
    for i in range(self.item_num):
      for (field, size, format) in self.schema:
        struct.pack_into(format, buf, offset, self.items[field][i])
        offset = offset + size
    # construct CommonItemResponse
    resp = dict(
      item_key = self.user_id,
      flatten_items = encode_bytes(buf)
    )
    return resp

class GsuFlow(LeafFlow, GsuApiMixin):
  pass

class TestFlowFunc(unittest.TestCase):
  __service = LeafService(kess_name="grpc_CommonLeafTest")

  def setUp(self) -> None:
    self.enable_attr_check_backup = LeafService.ENABLE_ATTR_CHECK
    LeafService.ENABLE_ATTR_CHECK = False

  def tearDown(self) -> None:
    LeafService.ENABLE_ATTR_CHECK = self.enable_attr_check_backup

  @classmethod
  def __init_service(cls, flow):
    cls.__temp_service = copy.deepcopy(cls.__service)
    cls.__temp_service.add_leaf_flows(leaf_flows=[flow])
    return cls.__temp_service.executor()

  def test_gsu_tower_multi_sort_post(self):
    flow = GsuFlow(name="test_gsu_tower_multi_sort_post") \
      .gsu_tower_multi_sort_post(common_distance_ptr_attrs=["aid_distance", "pid_distance", "mmu_distance"],
                                 common_distance_filter_attrs=["aid_embedding", "pid_embedding", "mmu_embedding"],
                                 colossus_pid_attr="colossus_pid",
                                 author_id_attr="aid_attr",
                                 tag_attr="tag_attr",
                                 label_attr="label_attr",
                                 channel_attr="channel_attr",
                                 play_time_attr="play_time_attr",
                                 duration_attr="duration_attr",
                                 timestamp_attr="ts_attr",
                                 output_sign_attr="output_signs",
                                 output_slot_attr="output_slots",
                                 output_item_colossus_pid_attr="output_pids",
                                 output_item_colossus_distance_attr="output_dists",
                                 channel_sizes=[5, 5],
                                 channel_weights=[1, 1, 0, 0, 0, 1])

    leaf = self.__init_service(flow)

    colossus_items = []
    colossus_pid_num = 10
    for i in range(colossus_pid_num):
      item = leaf.new_item(i)
      item["aid_attr"] = 1000 + i
      item["tag_attr"] = 2000 + i
      item["label_attr"] = 3000 + i
      item["channel_attr"] = 4000 + i
      item["play_time_attr"] = 5000 + i
      item["duration_attr"] = 6000 + i
      item["ts_attr"] = 7000 + i
      colossus_items.append(item)

    item_num = 10
    for i in range(item_num):
      item = leaf.add_item(100 + i)
      if i % 2 == 0:
        item["aid_embedding"] = True

      if i % 3 == 0:
        item["pid_embedding"] = True

      if i % 4 == 0:
        item["mmu_embedding"] = True


    leaf.request_time = 1000 * 1000
    leaf["colossus_pid"] = list(range(colossus_pid_num))
    aid_dis = leaf["aid_distance"] = [((133 * i) % 100) * 0.1 for i in range(5 * colossus_pid_num)]
    pid_dis = leaf["pid_distance"] = [((653 * i) % 100) * 0.1 for i in range(4 * colossus_pid_num)]
    mmu_dis = leaf["mmu_distance"] = [((187 * i) % 100) * 0.1 for i in range(3 * colossus_pid_num)]

    aid_dis_matrix = np.asarray(aid_dis).reshape((5, colossus_pid_num), order="F")
    pid_dis_matrix = np.asarray(pid_dis).reshape((4, colossus_pid_num), order="F")
    mmu_dis_matrix = np.asarray(mmu_dis).reshape((3, colossus_pid_num), order="F")

    leaf.run("test_gsu_tower_multi_sort_post")

    aid_offset = pid_offset = mmu_offset = 0
    for i, item in enumerate(leaf.items):
      distance1 = np.zeros(colossus_pid_num)
      distance2 = np.zeros(colossus_pid_num)

      if i % 2 == 0:
        distance1 += aid_dis_matrix[aid_offset, :]
        aid_offset += 1

      if i % 3 == 0:
        distance1 += pid_dis_matrix[pid_offset, :]
        pid_offset += 1

      if i % 4 == 0:
        distance2 += mmu_dis_matrix[mmu_offset, :]
        mmu_offset += 1

      sorted_idx1, sorted_dis1 = zip(*sorted(enumerate(distance1.tolist()), key=lambda x: x[1], reverse=True))
      sorted_idx2, sorted_dis2 = zip(*sorted(enumerate(distance2.tolist()), key=lambda x: x[1], reverse=True))

      expected_pids = [*sorted_idx1[:5], *sorted_idx2[:5]]
      expected_dists = [*sorted_dis1[:5], *sorted_dis2[:5]]
      output_pids = item["output_pids"]
      output_dists = item["output_dists"]

      for i, (dragon_output, numpy_output) in enumerate(zip(output_dists, expected_dists)):
        self.assertAlmostEqual(dragon_output, numpy_output, places=4, msg=f"the element of position {i} diff: {output_dists} vs {expected_dists}")
      self.assertEqual(output_pids, expected_pids)

  def test_gsu_tower_multi_sort_post_with_user(self):
    flow = GsuFlow(name="test_gsu_tower_multi_sort_post_with_user") \
      .gsu_tower_multi_sort_post(common_distance_ptr_attrs=["aid_distance", "pid_distance", "mmu_distance"],
                                 common_distance_filter_attrs=["aid_embedding", "pid_embedding", "mmu_embedding"],
                                 user_distance_ptr_attrs=["uid_distance", "did_distance"],
                                 colossus_pid_attr="colossus_pid",
                                 author_id_attr="aid_attr",
                                 tag_attr="tag_attr",
                                 label_attr="label_attr",
                                 channel_attr="channel_attr",
                                 play_time_attr="play_time_attr",
                                 duration_attr="duration_attr",
                                 timestamp_attr="ts_attr",
                                 output_sign_attr="output_signs",
                                 output_slot_attr="output_slots",
                                 output_item_colossus_pid_attr="output_pids",
                                 output_item_colossus_distance_attr="output_dists",
                                 channel_sizes=[5, 5],
                                 channel_weights=[1, 1, 0, 0, 0, 1],
                                 user_channel_weights=[1, 0.5, 0.5, 1])

    leaf = self.__init_service(flow)

    colossus_items = []
    colossus_pid_num = 10
    for i in range(colossus_pid_num):
      item = leaf.new_item(i)
      item["aid_attr"] = 1000 + i
      item["tag_attr"] = 2000 + i
      item["label_attr"] = 3000 + i
      item["channel_attr"] = 4000 + i
      item["play_time_attr"] = 5000 + i
      item["duration_attr"] = 6000 + i
      item["ts_attr"] = 7000 + i
      colossus_items.append(item)

    item_num = 10
    for i in range(item_num):
      item = leaf.add_item(100 + i)
      if i % 2 == 0:
        item["aid_embedding"] = True

      if i % 3 == 0:
        item["pid_embedding"] = True

      if i % 4 == 0:
        item["mmu_embedding"] = True


    leaf.request_time = 1000 * 1000
    leaf["colossus_pid"] = list(range(colossus_pid_num))
    aid_dis = leaf["aid_distance"] = [((133 * i) % 100) * 0.1 for i in range(5 * colossus_pid_num)]
    pid_dis = leaf["pid_distance"] = [((653 * i) % 100) * 0.1 for i in range(4 * colossus_pid_num)]
    mmu_dis = leaf["mmu_distance"] = [((187 * i) % 100) * 0.1 for i in range(3 * colossus_pid_num)]
    uid_dis = leaf["uid_distance"] = [((237 * i) % 100) * 0.1 for i in range(colossus_pid_num)]
    did_dis = leaf["did_distance"] = [((973 * i) % 100) * 0.1 for i in range(colossus_pid_num)]

    aid_dis_matrix = np.asarray(aid_dis).reshape((5, colossus_pid_num), order="F")
    pid_dis_matrix = np.asarray(pid_dis).reshape((4, colossus_pid_num), order="F")
    mmu_dis_matrix = np.asarray(mmu_dis).reshape((3, colossus_pid_num), order="F")
    uid_dis_matrix = np.asarray(uid_dis)
    did_dis_matrix = np.asarray(did_dis)

    leaf.run("test_gsu_tower_multi_sort_post_with_user")

    aid_offset = pid_offset = mmu_offset = 0
    for i, item in enumerate(leaf.items):
      distance1 = uid_dis_matrix + 0.5 * did_dis_matrix
      distance2 = 0.5 * uid_dis_matrix + did_dis_matrix

      if i % 2 == 0:
        distance1 += aid_dis_matrix[aid_offset, :]
        aid_offset += 1

      if i % 3 == 0:
        distance1 += pid_dis_matrix[pid_offset, :]
        pid_offset += 1

      if i % 4 == 0:
        distance2 += mmu_dis_matrix[mmu_offset, :]
        mmu_offset += 1

      sorted_idx1, sorted_dis1 = zip(*sorted(enumerate(distance1.tolist()), key=lambda x: x[1], reverse=True))
      sorted_idx2, sorted_dis2 = zip(*sorted(enumerate(distance2.tolist()), key=lambda x: x[1], reverse=True))

      expected_pids = [*sorted_idx1[:5], *sorted_idx2[:5]]
      expected_dists = [*sorted_dis1[:5], *sorted_dis2[:5]]
      output_pids = item["output_pids"]
      output_dists = item["output_dists"]

      for i, (dragon_output, numpy_output) in enumerate(zip(output_dists, expected_dists)):
        self.assertAlmostEqual(dragon_output, numpy_output, places=4, msg=f"the element of position {i} diff: {output_dists} vs {expected_dists}")
      self.assertEqual(output_pids, expected_pids)

  def test_kwaipro_gsu_with_index(self):
    user_id = self.mock_ksib_colossus()
    flow = GsuFlow(name="test_kwaipro_gsu_with_index")\
      .colossus(
        service_name='grpc_colossusLongKsibVideoItemB',
        shard_number=96,
        client_type='common_item_client',
        output_attr='colossus_output',
        debug_uids="666",
        parse_to_pb=False
      )\
      .kwaipro_gsu_with_index(
          target_item={"is_target_photo_embedding_hit": 1},
          colossus_pid_attr="colossus_pid",
          author_id_attr="aid",
          tag_attr="tag",
          play_time_attr="play",
          duration_attr="duration",
          timestamp_attr="time",
          slots_id = [123, 122, 692, 694, 693],
          mio_slots_id=[690, 691, 692, 694, 693],
          output_sign_attr="gsu_signs",
          output_slot_attr="gsu_slots",
          bucket_attr="photo_bucket",
          sorted_item_idx_attr="pack_tower_topk_indices",
          sorted_item_idx_output_type=5,
          top_n=100
      )

    leaf = self.__init_service(flow)

    for i in range(1):
      item = leaf.add_item(i)
      item["author_id"] = 150000148354736
      item["photo_bucket"] = 18
    leaf.user_id = user_id

    leaf.run("test_kwaipro_gsu_with_index")

  def test_gsu_with_live_index(self):
    user_id = self.mock_ksib_colossus()
    flow = GsuFlow(name="test_gsu_with_live_index")\
      .colossus(
          service_name="grpc_colossusSimV2",
          shard_number=16,
          client_type="common_item_client",
          output_attr="video_colossus_output") \
      .gsu_with_live_index(
        colossus_pid_attr="colossus_pid",
        author_id_attr="colossus_aid",
        tag_attr="colossus_tag",
        play_time_attr="colossus_play",
        duration_attr="colossus_duration",
        timestamp_attr="colossus_time",
        channel_attr="colossus_channel",
        label_attr="colossus_label",
        slots_id=[26, 128, 349, 348, 350, 345, 344],
        mio_slots_id=[346, 347, 349, 348, 350, 345, 344],
        output_sign_attr="gsu_signs",
        output_slot_attr="gsu_slots",
        sorted_item_idx_attr="sorted_item_index",
        output_item_colossus_pid_attr="item_colossus_pid",
        top_n=100) \

    leaf = self.__init_service(flow)
    leaf.user_id = user_id

    leaf.run("test_gsu_with_live_index")
    


  def test_gsu_retriever_with_colossus_resp_v5(self):
    user_id = 1550033135
    flow = GsuFlow(name="test_gsu_retriever_with_colossus_resp_v5")\
        .colossus(
            service_name="grpc_colossusSimAdStateItemNew",
            client_type="common_item_client",
            output_attr="colossus_output",
            parse_to_pb=False,
        )\
        .gsu_retriever_with_colossus_resp_v5(
            colossus_resp_attr="colossus_output",
            save_pid_attr_to="colossus_pids",
            save_aid_attr_to="colossus_aids",
            save_play_attr_to="colossus_plays",
            save_duration_attr_to="colossus_durations",
            save_tag_attr_to="colossus_tags",
            save_time_attr_to="colossus_times",
            save_label_attr_to="colossus_labels",
            save_channel_attr_to="colossus_channels",
            save_diff_attr_to="colossus_diff",
            save_play_duration_attr_to="colossus_play_duration",
            day_end=True,
            filter_time=60,
            limit_num=4500,
            parse_from_pb=False,
            filter_future_attr=True
        )

    leaf = self.__init_service(flow)
    leaf.user_id = user_id

    leaf.run("test_gsu_retriever_with_colossus_resp_v5")

  def test_gsu_retriever_with_eshop_resp(self):
    user_id = 1550033135
    flow = GsuFlow(name="test_gsu_retriever_with_eshop_resp")\
        .colossus(
            service_name="grpc_colossusSimAdStateItemNew",
            client_type="common_item_client",
            output_attr="colossus_output",
            parse_to_pb=False,
        )\
        .gsu_retriever_with_colossus_resp_v5(
            colossus_resp_attr="colossus_output",
            save_pid_attr_to="colossus_pids",
            save_aid_attr_to="colossus_aids",
            save_play_attr_to="colossus_plays",
            save_duration_attr_to="colossus_durations",
            save_tag_attr_to="colossus_tags",
            save_time_attr_to="colossus_times",
            save_label_attr_to="colossus_labels",
            save_channel_attr_to="colossus_channels",
            save_diff_attr_to="colossus_diff",
            save_play_duration_attr_to="colossus_play_duration",
            day_end=True,
            filter_time=60,
            limit_num=4500,
            parse_from_pb=False,
            filter_future_attr=True
        )

    leaf = self.__init_service(flow)
    leaf.user_id = user_id

    leaf.run("test_gsu_retriever_with_eshop_resp")

  def test_random_sleep(self, sleep_ms=None, **kwargs):
    user_id = 1550033135
    flow = GsuFlow(name="test_random_sleep")\
        .colossus(
            service_name="grpc_colossusSimAdStateItemNew",
            client_type="common_item_client",
            output_attr="colossus_output",
            parse_to_pb=False,
        )\
        .random_sleep(50)

    leaf = self.__init_service(flow)
    leaf.user_id = user_id

    leaf.run("test_random_sleep")

  def test_gsu_retriever_with_eshop_cut(self):
    user_id = 1550033135
    flow = GsuFlow(name="test_gsu_retriever_with_eshop_cut")\
        .colossus(
            service_name="grpc_colossusSimAdStateItemNew",
            client_type="common_item_client",
            output_attr="colossus_output",
            parse_to_pb=False,
        )\
        .gsu_retriever_with_colossus_resp_v5(
            colossus_resp_attr="colossus_output",
            save_pid_attr_to="colossus_pids",
            save_aid_attr_to="colossus_aids",
            save_play_attr_to="colossus_plays",
            save_duration_attr_to="colossus_durations",
            save_tag_attr_to="colossus_tags",
            save_time_attr_to="colossus_times",
            save_label_attr_to="colossus_labels",
            save_channel_attr_to="colossus_channels",
            save_diff_attr_to="colossus_diff",
            save_play_duration_attr_to="colossus_play_duration",
            day_end=True,
            filter_time=60,
            limit_num=4500,
            parse_from_pb=False,
            filter_future_attr=True
        )

    leaf = self.__init_service(flow)
    leaf.user_id = user_id
    leaf.run("test_gsu_retriever_with_eshop_cut")
    
  def test_gsu_with_index_v3(self):
    user_id = self.mock_ksib_colossus()
    flow = GsuFlow(name="test_gsu_with_index_v3")\
      .colossus(
        service_name='grpc_colossusSimAdStateItemNew',
        client_type='common_item_client',
        input_attr="effective_user_id",
        output_attr="colossus_output",
        parse_to_pb=False,
    ) \
    .gsu_with_index_v3(
        play_time_attr="play",
        author_id_attr="aid",
        channel_attr='channel',
        label_attr='label',
        colossus_pid_attr="colossus_pid_for_sim100",
        tag_attr="tag",
        duration_attr="duration",
        timestamp_attr="time",
        target_item={"is_target_photo_embedding_hit": 1},
        sorted_item_idx_attr="sim100_sorted_item_idx",
        top_n=100,
        # output_sign_attr="gsu_sim_100_signs",
        # output_slot_attr="gsu_sim_100_slots",
        only_like_and_play=True,
        export_like=True,
        play_list=[],
        like_prefix="gsu_like_",
    )

    leaf = self.__init_service(flow)
    leaf.user_id = user_id

    leaf.run("test_gsu_with_index_v3")

  def test_gsu_with_index_v3_sub_seq(self):
    user_id = self.mock_ksib_colossus()
    flow = GsuFlow(name="test_gsu_with_index_v3_sub_seq")\
      .colossus(
        service_name='grpc_colossusSimAdStateItemNew',
        client_type='common_item_client',
        input_attr="effective_user_id",
        output_attr="colossus_output",
        parse_to_pb=False,
    ) \
    .gsu_with_index_v3_sub_seq(
        play_time_attr="play",
        author_id_attr="aid",
        channel_attr='channel',
        label_attr='label',
        colossus_pid_attr="colossus_pid_for_sim100",
        tag_attr="tag",
        duration_attr="duration",
        timestamp_attr="time",
        target_item={"is_target_photo_embedding_hit": 1},
        sorted_item_idx_attr="sim100_sorted_item_idx",
        top_n=100,
        # output_sign_attr="gsu_sim_100_signs",
        # output_slot_attr="gsu_sim_100_slots",
        only_like_and_play=True,
        export_like=True,
        play_list=[],
        like_prefix="gsu_like_",
        limit = 50
    )

    leaf = self.__init_service(flow)
    leaf.user_id = user_id

    leaf.run("test_gsu_with_index_v3_sub_seq")

  def test_gsu_retrieve_from_colossus_sim_checkpoint(self):
    flow = GsuFlow(name="test_gsu_retrieve_from_colossus_sim_checkpoint")\
      .gsu_retrieve_from_colossus_sim_checkpoint(
        save_photo_id_to_attr="pid_million",
        save_author_id_to_attr="aid_million",
        save_duration_to_attr="duration_million",
        save_play_time_to_attr="play_million",
        save_tag_to_attr="tag_million",
        save_label_to_attr="label_million",
        save_timestamp_to_attr="time_million",
        save_channel_to_attr="channel_million",
        read_thread_num=32,
        buffer_size=10,
        version=2,
        kess_name='grpc_colossusSimAdStateItemNew',
        loop_read_always=False,
        loop_interval=24 * 60 * 60,
      )

    # 读取不到 HDFS ，就别跑了，免得影响 kdev 的代码提交
    # leaf = self.__init_service(flow)

  def test_ksib_gsu_with_aid(self):
    user_id = self.mock_ksib_colossus()
    flow = GsuFlow(name="test_ksib_gsu_with_aid")\
      .colossus(
        service_name='grpc_colossusLongKsibVideoItemB',
        shard_number=96,
        client_type='common_item_client',
        output_attr='colossus_output',
        debug_uids="666",
        parse_to_pb=False
      )\
      .ksib_gsu_with_aid(
              colossus_resp_attr="colossus_output",
              target_attr="author_id",
              filter_play_time_threshold=7,
              bucket_attr="photo_bucket",
              output_sign_attr="aid_gsu_signs",
              output_slot_attr="aid_gsu_slots",
              slots_id=[1140, 1141, 1142, 1143, 1144, 1145, 1146],
              mio_slots_id=[1140, 1141, 1142, 1143, 1144, 1145, 1146]
      )

    leaf = self.__init_service(flow)

    for i in range(1):
      item = leaf.add_item(i)
      item["author_id"] = 150000148354736
      item["photo_bucket"] = 18
    leaf.user_id = user_id

    leaf.run("test_ksib_gsu_with_aid")
    # for item in leaf.items:
    #   self.assertNotEqual(item['aid_gsu_signs'], None, 'test_ksib_gsu_with_aid not found common attr colossus_slots')
    #   self.assertNotEqual(item['aid_gsu_slots'], None, 'test_ksib_gsu_with_aid not found common attr colossus_signs')

  def test_get_item_query_hash(self):
    flow = GsuFlow(name="test_get_item_query_hash") \
      .get_query_hash(item_query_str_attr="item_query_str_attr", save_hash_to_attr="query_hash")

    leaf = self.__init_service(flow)

    query_list = ["test", "北京"]
    for i, query in enumerate(query_list):
      item = leaf.add_item(i)
      item["item_query_str_attr"] = query


    leaf.request_time = 1000 * 1000

    leaf.run("test_get_item_query_hash")
    expected_output_hash = [-742803963401426510, -8079092125935423775]

    for i, item in enumerate(leaf.items):
      query_hash = item["query_hash"]
      self.assertEqual(query_hash, expected_output_hash[i])

  def test_get_query_hash(self):
    flow = GsuFlow(name="test_get_user_app_hash") \
      .get_query_hash(user_query_list_attr="product_name", save_hash_to_attr="app_hash", app_query=True)
    
    leaf = self.__init_service(flow)
    leaf['product_name'] = ['Soul', '夸克首页', 'soul', '京东', 'JD', "123订票助手", "24小时天气", "24小时天气专业版软件"]
    leaf.request_time = 1000 * 1000

    leaf.run('test_get_user_app_hash')
    expected_output_hash = [3312470981, 2715235416, 1013387676, 2299685310, 1853396875, 3845819542, 526003884, 2664459180]

    app_hash = leaf['app_hash']
    self.assertEqual(app_hash, expected_output_hash)

    # test app_query: query for item
    flow_item_app_query = GsuFlow(name="test_item_app_query") \
      .get_query_hash(item_query_str_attr="product_name", save_hash_to_attr="item_app_hash", app_query=True)
    leaf = self.__init_service(flow_item_app_query)
    query_list = ['Soul', '夸克首页', 'soul', '京东', 'JD', "123订票助手", "24小时天气", "24小时天气专业版软件"]
    for i, query in enumerate(query_list):
      item = leaf.add_item(i)
      item["product_name"] = query
    leaf.request_time = 1000 * 1000

    leaf.run('test_item_app_query')
    expected_output_hash = [3312470981, 2715235416, 1013387676, 2299685310, 1853396875, 3845819542, 526003884, 2664459180]
    for i, item in enumerate(leaf.items):
      item_app_hash = item["item_app_hash"]
      self.assertEqual(item_app_hash, expected_output_hash[i])

    # test item_query
    flow_query_normal = GsuFlow(name="test_get_item_query_hash") \
      .get_query_hash(item_query_str_attr="item_query_str_attr", save_hash_to_attr="query_hash")

    leaf = self.__init_service(flow_query_normal)

    query_list = ["test", "北京"]
    for i, query in enumerate(query_list):
      item = leaf.add_item(i)
      item["item_query_str_attr"] = query


    leaf.request_time = 1000 * 1000

    leaf.run("test_get_item_query_hash")
    expected_output_hash = [-742803963401426510, -8079092125935423775]

    for i, item in enumerate(leaf.items):
      query_hash = item["query_hash"]
      self.assertEqual(query_hash, expected_output_hash[i])

    # test user_query
    flow_user_normal = GsuFlow(name="test_get_user_query_hash") \
      .get_query_hash(user_query_list_attr="user_query_list_attr", save_hash_to_attr="query_hash")

    leaf = self.__init_service(flow_user_normal)

    leaf["user_query_list_attr"] = ["test", "北京"]

    leaf.request_time = 1000 * 1000

    leaf.run("test_get_user_query_hash")
    expected_output_hash = [-742803963401426510, -8079092125935423775]

    query_hash = leaf["query_hash"]
    self.assertEqual(query_hash, expected_output_hash)

  def test_get_user_query_hash(self):
    flow = GsuFlow(name="test_get_user_query_hash") \
      .get_query_hash(user_query_list_attr="user_query_list_attr", save_hash_to_attr="query_hash")

    leaf = self.__init_service(flow)

    leaf["user_query_list_attr"] = ["test", "北京"]

    leaf.request_time = 1000 * 1000

    leaf.run("test_get_user_query_hash")
    expected_output_hash = [-742803963401426510, -8079092125935423775]

    query_hash = leaf["query_hash"]
    self.assertEqual(query_hash, expected_output_hash)
  
  def test_gsu_split_cls_seq(self):
    flow = GsuFlow(name="test_gsu_split_cls_seq") \
      .gsu_split_cls_seq(
        colossus_photo_id = "colossus_photo_id",
        colossus_author_id = "colossus_author_id_v2",
        colossus_play_time = "colossus_play_time",
        colossus_duration = "colossus_duration",
        colossus_timestamp = "colossus_timestamp",
        colossus_label = "colossus_label",
        colossus_real_show_index = "colossus_real_show_index",
        colossus_tag = "colossus_tag",
        colossus_channel = "colossus_channel",
        duration_bucket_num = 2,
        sub_seq_max_len = 2,
        output_photo_id = "output_photo_id",
        output_author_id = "output_author_id",
        output_play_time = "output_play_time",
        output_duration = "output_duration",
        output_timestamp = "output_timestamp",
        output_label = "output_label",
        output_real_show_index = "output_real_show_index",
        output_tag = "output_tag",
        output_channel = "output_channel",
      )

    leaf = self.__init_service(flow)

    leaf["colossus_photo_id"] = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    leaf["colossus_author_id_v2"] = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    leaf["colossus_play_time"] = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    leaf["colossus_duration"] = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]

    leaf.run("test_gsu_split_cls_seq")
    expected_output_photo_id = [4, 9]

    output_photo_id = leaf["output_photo_id"]
    self.assertEqual(output_photo_id, expected_output_photo_id)

  def test_gsu_cityhash(self):
    flow = GsuFlow(name="test_gsu_cityhash") \
      .gsu_cityhash(
        input_attrs=[
          dict(attr_name="user_id_list", is_common=False),
          dict(attr_name="photo_id", is_common=False),
        ],
        output_item_attr="hash_list")

    leaf = self.__init_service(flow)


    for i in range(3):
      item = leaf.add_item(i)
      item["photo_id"] = i
      item["user_id_list"] = [1024, 1025, 1026]

    leaf.run("test_gsu_cityhash")

    expected_output_hash = [
      [7200485126925882411, -6455607956975430469, -7264118000530783646],
      [-8868951707966388301, 1036435920961377785, 3655241897966620986],
      [1468161626409262761, -3991461048261677009, 7682070167647007625],
    ]
    for item, hash_list in zip(leaf.items, expected_output_hash):
      self.assertEqual(item["hash_list"], hash_list)


  def test_gsu_cityhash_cross(self):
    flow = GsuFlow(name="test_gsu_cityhash_cross") \
      .gsu_cityhash(
        input_attrs=[
          dict(attr_name="user_id", is_common=True),
          dict(attr_name="photo_id", is_common=False),
        ],
        output_item_attr="hash_list")

    leaf = self.__init_service(flow)

    leaf["user_id"] = 1024

    for i in range(3):
      item = leaf.add_item(i)
      item["photo_id"] = i

    leaf.run("test_gsu_cityhash_cross")

    expected_output_hash = [[7200485126925882411], [-8868951707966388301], [1468161626409262761]]
    for item, hash_list in zip(leaf.items, expected_output_hash):
      self.assertEqual(item["hash_list"], hash_list)


  def mock_colossus(self):
    mock_items = {}
    user_id = 478801757
    cluster_id = 563
    live_id_list = [8949585244, 8949585244, 8949585244, 8949585244, 8949585244]
    mock_items["live_id"] = live_id_list
    author_id_list = [177628711, 177628711, 177628711, 177628711, 177628711]
    mock_items["author_id"] = author_id_list
    play_time_list = [1, 226, 7, 7, 4]
    mock_items["play_time"] = play_time_list
    timestamp_list = [1640761588, 1640761699, 1640762051, 1640762051, 1640762952]
    mock_items["timestamp"] = timestamp_list
    hetu_tag_list = [115411404, 115411404, 115411404, 115411404, 115411404]
    mock_items["op_hetu_tag_id"] = hetu_tag_list
    cluster_id_list = [563, 3635, 3635, 3635, 3781]
    mock_items["page_frame_cluster_id"] = cluster_id_list
    item_list_len = 5

    live_item_field_and_size  = [
      ("live_id", 8, "Q"),
      ("author_id", 4, "I"),
      ("play_time", 4, "I"),
      ("timestamp", 4, "I"),
      ("op_hetu_tag_id", 4, "I"),
      ("page_frame_cluster_id", 4, "I")
    ]
    item_size = 0
    for (name, size, format_str) in live_item_field_and_size:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in live_item_field_and_size:
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size

    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_colossusLiveItem", response_json_str=json.dumps(resp))
    return user_id, cluster_id

  def mock_colossus_v3(self):
    mock_items = {}
    user_id = 478801757
    cluster_id = 563

    live_id_list = [8949585244, 8949585244, 8949585244, 8949585244, 8949585244]
    author_id_list = [177628711, 177628711, 177628711, 177628711, 177628711]
    timestamp_list = [1640761588, 1640761699, 1640762051, 1640762051, 1640762952]
    hetu_tag_list = [115411404, 115411404, 115411404, 115411404, 115411404]
    cluster_id_list = [563, 3635, 3635, 3635, 3781]
    play_time_list = [1, 226, 7, 7, 4]
    reward_list = [0, 0, 0, 0, 0]
    reward_count_list = [0, 0, 0, 0, 0]

    mock_items["live_id"] = live_id_list
    mock_items["author_id"] = author_id_list
    mock_items["play_time"] = play_time_list
    mock_items["timestamp"] = timestamp_list
    mock_items["op_hetu_tag_id"] = hetu_tag_list
    mock_items["page_frame_cluster_id"] = cluster_id_list
    mock_items["reward"] = reward_list
    mock_items["reward_count"] = reward_count_list

    item_list_len = 5

    live_item_field_and_size  = [
      ("live_id", 8, "Q"),
      ("author_id", 4, "I"),
      ("play_time", 4, "I"),
      ("timestamp", 4, "I"),
      ("op_hetu_tag_id", 4, "I"),
      ("page_frame_cluster_id", 4, "I"),
      ("reward", 4, "I"),
      ("reward_count", 2, "H"),
    ]
    item_size = 0
    for (name, size, format_str) in live_item_field_and_size:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in live_item_field_and_size:
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size

    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_colossusLiveItemV3", response_json_str=json.dumps(resp))
    return user_id, cluster_id

  def mock_ksib_colossus(self):
    mock_items = {}
    user_id = 666
    photo_id_list = [150000326772295, 150100619663871, 150100618272776, 150100067044658, 150000379167079] * 4
    mock_items["photo_id"] = photo_id_list
    author_id_list = [150000148354736, 150000148186556, 150000148354736, 150000148354736, 150000007591566] * 4
    mock_items["author_id"] = author_id_list
    play_time_list = [7, 155, 12, 2, 57] * 4
    mock_items["play_time"] = play_time_list
    duration_list = [52, 187, 11, 151, 55] * 4
    mock_items["duration"] = duration_list
    timestamp_list = [1661477079, 1661477069, 1661476908, 1661476898, 1661476898] * 4
    mock_items["timestamp"] = timestamp_list
    label_tag_list = [925763, 67, 67, 90, 925772] * 4
    mock_items["label_tag"] = label_tag_list
    channel_list = [33, 33, 33, 33, 33] * 4
    mock_items["channel"] = channel_list
    item_list_len = 5 * 4

    ksib_item_field_and_size = [
      ("timestamp", 4, "I"),
      ("photo_id", 8, "Q"),
      ("author_id", 8, "Q"),
      ("duration", 2, "H"),
      ("play_time", 2, "H"),
      ("label_tag", 4, "I"),
      ("channel", 1, "B")
    ]
    item_size = sum([size for (name, size, format_str) in ksib_item_field_and_size])
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in ksib_item_field_and_size:
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size

    resp = dict(
      item_key=user_id,
      flatten_items=encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_colossusLongKsibVideoItemB", response_json_str=json.dumps(resp))
    return user_id

  def mock_colossus_v4(self):
    mock_items = {}
    user_id = 666
    cluster_id = 426
    live_id_list = [9611138711, 9611138711, 9611138711, 9674425995, 9674386592, 9674336243, 9674454689, 9674383969, 9686886286, 9684696721, 9704553751]
    timestamp_list = [1654458036, 1654458036, 1654458123, 1655998550, 1655998733, 1655999201, 1655999225, 1655999266, 1656314115, 1656406127, 1656739203]
    author_id_list =  [666, 666, 666, 2011777021, 1291205167, 941901644, 2899812133, 586064739, 670333339, 2470293124, 916369360]
    play_time_list =  [0, 1, 17, 151, 3, 2, 4, 2, 4, 24, 2]
    auto_play_time_list =  [0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    hetu_tag_channel_list =  [39168, 39168, 39168, 506626, 39170, 39170, 165890, 87298, 51, 361989, 236034]
    cluster_id_list =  [426, 426, 426, 576, 204, 204, 243, 926, 698, 445, 675]
    label_list = [0, 1, 4, 0, 0, 0, 0, 0, 0, 256, 0]
    reward_list =  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    reward_count_list =  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    item_id_list =  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    audience_count_list =  [0, 0, 0, 18, 2, 6, 11, 1, 25163, 6, 5]
    user_latitude_list =  [33.605000, 33.605000, 33.605000, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.603462]
    user_longitude_list =  [113.266068, 113.266068, 113.266068, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.272285]
    author_latitude_list =  [33.605000, 33.605000, 33.605000, 33.971607, 33.432060, 36.142181, 1.000000, 33.975880, 35.045761, -1.000000, 32.531445]
    author_longitude_list =  [113.266068, 113.266068, 113.266068, 113.220009, 113.368248, 113.814651, 1.000000, 113.222862, 118.300484, -1.000000, 112.375496]
    order_price_list =  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]

    mock_items["live_id"] = live_id_list
    mock_items["timestamp"] = timestamp_list
    mock_items["author_id"] = author_id_list
    mock_items["play_time"] = play_time_list
    mock_items["auto_play_time"] = auto_play_time_list
    mock_items["hetu_tag_channel"] = hetu_tag_channel_list
    mock_items["cluster_id"] = cluster_id_list
    mock_items["label"] = label_list
    mock_items["reward"] = reward_list
    mock_items["reward_count"] = reward_count_list
    mock_items["item_id"] = item_id_list
    mock_items["audience_count"] = audience_count_list
    mock_items["user_latitude"] = user_latitude_list
    mock_items["user_longitude"] = user_longitude_list
    mock_items["author_latitude"] = author_latitude_list
    mock_items["author_longitude"] = author_longitude_list
    mock_items["order_price"] = order_price_list

    item_list_len = 10

    live_item_field_and_size  = [
      ("live_id", 8, "Q"),
      ("timestamp", 4, "I"),
      ("author_id", 4, "I"),
      ("play_time", 2, "H"),
      ("auto_play_time", 2, "H"),
      ("hetu_tag_channel", 4, "I"),
      ("cluster_id", 2, "H"),
      ("label", 2, "H"),
      ("reward", 4, "I"),
      ("reward_count", 2, "H"),
      ("item_id", 8, "Q"),
      ("audience_count", 4, "I"),
      ("user_latitude", 4, "f"),
      ("user_longitude", 4, "f"),
      ("author_latitude", 4, "f"),
      ("author_longitude", 4, "f"),
      ("order_price", 2, "H")
    ]
    item_size = 0
    for (name, size, format_str) in live_item_field_and_size:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in live_item_field_and_size:
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size

    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_colossusLiveItemV4", response_json_str=json.dumps(resp))
    return user_id, cluster_id

  def mock_colossus_sim_v2(self):
    mock_items = {}
    user_id = 666
    photo_id_list = [75694025719, 75823594133, 75862597365, 75990765646, 76036159567,
                     76116630125, 76250349781, 80211142862, 55676225157, 73137452716]
    author_id_list = [671995135, 671995135, 671995135, 671995135, 671995135,
                      671995135, 671995135, 1305330190, 671995135, 2268917398]
    duration_list = [17, 9, 59, 90, 140, 17, 70, 7, 66, 7]
    play_time_list = [1, 1, 26, 1, 2, 1, 2, 1, 3, 2]
    tag_list = [1042, 1097, 1097, 1097, 1097, 5, 1097, 1096, 1096, 0]
    channel_list = [2, 2, 2, 2, 2, 2, 2, 62, 2, 5]
    label_list = [2592, 0, 4864, 4608, 4608, 0, 4672, 0, 4608, 1]
    timestamp_list = [1660620244, 1660620244, 1660620244, 1660620244,
                      1660620244, 1660620244, 1660620244, 1660620244, 1660620266, 1660620288]
    user_latitude_list = [33.605000, 33.605000, 33.604996, 33.604996,
                          33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.603462]
    user_longitude_list = [113.266068, 113.266068, 113.266083, 113.266083,
                           113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.272285]
    photo_latitude_list = [33.605000, 33.605000, 33.971607, 33.432060,
                           36.142181, 1.000000, 33.975880, 35.045761, -1.000000, 32.531445]
    photo_longitude_list = [113.266068, 113.266068, 113.220009, 113.368248,
                            113.814651, 1.000000, 113.222862, 118.300484, -1.000000, 112.375496]
    mock_items["photo_id"] = photo_id_list
    mock_items["author_id"] = author_id_list
    mock_items["duration"] = duration_list
    mock_items["play_time"] = play_time_list
    mock_items["tag"] = tag_list
    mock_items["channel"] = channel_list
    mock_items["label"] = label_list
    mock_items["timestamp"] = timestamp_list
    mock_items["user_latitude"] = user_latitude_list
    mock_items["user_longitude"] = user_longitude_list
    mock_items["photo_latitude"] = photo_latitude_list
    mock_items["photo_longitude"] = photo_longitude_list

    item_list_len = 10
    item_field_and_size = [
      ("photo_id", 8, "Q"),
      ("author_id", 4, "I"),
      ("duration", 2, "H"),
      ("play_time", 2, "H"),
      ("tag", 4, "I"),
      ("channel", 1, "B"),
      ("label", 2, "H"),
      ("timestamp", 4, "I"),
      ("user_latitude", 4, "f"),
      ("user_longitude", 4, "f"),
      ("photo_latitude", 4, "f"),
      ("photo_longitude", 4, "f"),
    ]
    item_size = 0
    for (_, size, _) in item_field_and_size:
      item_size += size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in item_field_and_size:
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size

    resp = dict(
        item_key=user_id,
        flatten_items=encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(
        service_name="grpc_colossusSimV2", response_json_str=json.dumps(resp))
    return user_id
  
  def mock_ksib_live_colossus(self):
    mock_items = {}
    user_id = 478801757
    live_id_list = [202442473, 202557558, 202971449, 202963351, 202970399]
    mock_items["live_id"] = live_id_list
    author_id_list = [150001531432244, 150001297792668, 150000147301454, 150001342763415, 150000238807894]
    mock_items["author_id"] = author_id_list
    play_time_list = [1024, 2226, 7658, 7002, 4231]
    mock_items["play_time"] = play_time_list
    auto_play_time_list = [1024, 3226, 2658, 3002, 4231]
    mock_items["auto_play_time"] = auto_play_time_list
    timestamp_list = [1721492777, 1721492777, 1721492777, 1721492777, 1721492777]
    mock_items["timestamp"] = timestamp_list
    req_type_list = [0, 8, 0, 8, 8]
    mock_items["tag"] = req_type_list
    label_list = [4, 4, 4, 4, 2092]
    mock_items["label"] = label_list
    item_list_len = 5

    live_item_field_and_size  = [
      ("live_id", 8, "Q"),
      ("author_id", 8, "Q"),
      ("play_time", 4, "I"),
      ("auto_play_time", 4, "I"),
      ("timestamp", 4, "I"),
      ("tag", 4, "I"),
      ("label", 4, "I")
    ]
    item_size = 0
    for (name, size, format_str) in live_item_field_and_size:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in live_item_field_and_size:
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size

    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_colossusKsibLiveItemNew", response_json_str=json.dumps(resp))
    return user_id

  @unittest.skip("skip unit test")
  def test_live_author_list_from_colossus(self):
    user_id, _ = self.mock_colossus_v4()
    flow = GsuFlow(name="test_live_author_list_from_colossus")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusLiveItemV4',
        client_type='common_item_client',
        output_attr="live_colossus_output",
        debug_uids="478801757",
        print_items=True) \
      .live_author_list_from_colossus(
        colossus_resp_attr='live_colossus_output',
        output_prefix='colossus_author_',
        live_item_version=4,
        limit_author_num=50)

    leaf = self.__init_service(flow)
    leaf.request_time = int(time.time() * 1000)
    for i in range(1):
      item = leaf.add_item(i)
      item["aId"] = 177628711
    leaf.user_id = user_id
    leaf.run("test_live_author_list_from_colossus")
    attrs = ["author_id", "play_time_sum", "reward_sum", "reward_count", "unseen_days",
                    "comment_count", "like_count", "follow_count"]
    attrs = ["colossus_author_" + x for x in attrs]
    for attr in attrs:
      print(attr, leaf[attr])
      self.assertNotEqual(leaf[attr], None, 'not found attr {}'.format(attr))
 
  @unittest.skip("skip unit test")
  def test_ksib_live_gsu_retriever_with_colossus_resp_v2(self):
    user_id = self.mock_ksib_live_colossus()
    flow = GsuFlow(name="test_ksib_live_colossus_resp_retriever_v2")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusKsibLiveItemNew',
        client_type='common_item_client',
        output_attr='live_colossus_output',
        debug_uids='478801757') \
      .ksib_live_gsu_retriever_with_colossus_resp_v2(
        colossus_resp_attr='live_colossus_output',
        filter_future_attr=True,
        filter_play_time_threshold=7000,
        filter_auto_play_time_threshold=10000,
        save_live_id_to_attr='live_id',
        save_author_id_to_attr='author_id',
        save_duration_to_attr='hist_duration',
        save_play_time_to_attr='hist_playtime',
        save_timestamp_to_attr='hist_timestamp',
        save_label_to_attr="hist_labels",
        save_auto_play_time_to_attr='hist_auto_playtime',
        save_result_to_common_attr="hist_lives_new")

    leaf = self.__init_service(flow)
    leaf.request_time = 1722829648446
    self.assertNotEqual(leaf['hist_lives_new'], None, 'not found attr {}'.format('hist_lives_new'))
    # leaf.user_id = user_id

  def test_gsu_with_time_clock(self):
    client_kconf = 'colossus.kconf_client.video_item'
    user_id = 666
    mock_items = {
      "photo_id": [1, 2, 3],
      "author_id": [4, 5, 6],
      "duration": [10, 11, 12],
      "play_time": [13, 14, 15],
      "tag": [16, 17, 18],
      "channel": [16, 17, 18],
      "label": [19, 20, 21],
      "timestamp": [22, 23, 24]
    }
    item_list_len = len(next(iter(mock_items.values())))
    field_infos = self._get_colossusdb_sim_field_infos(client_kconf, mock_items)
    resp = self._serialize_colossusdb_sim_data(item_list_len, mock_items, field_infos)
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_clsdb_sim-video-2_short-video",
                                 response_json_str=json.dumps(resp))
    flow = GsuFlow(name="test_gsu_with_time_clock") \
      .gsu_common_colossusv2_enricher(kconf=client_kconf,
                                      partial_schema_output_attr="video_item_schema",
                                      item_fields=dict(photo_id="photo_id_list",
                                                       author_id="author_id_list",
                                                       duration="durations_list",
                                                       play_time="play_time_list",
                                                       tag="tag_list",
                                                       channel="channel_list",
                                                       label="label_list",
                                                       timestamp="timestamp_list")) \
      .gsu_common_colossus_resp_retriever(
       from_colossus_sim_v2=True,
       print_item_fields=True,
       partial_schema_input_attr="video_item_schema",
       item_key_field="photo_id",
       item_time_field="timestamp",
       input_item_fields=dict(photo_id="photo_id_list",
                     author_id="author_id_list",
                     duration="durations_list",
                     play_time="play_time_list",
                     tag="tag_list",
                     channel="channel_list",
                     label="label_list",
                     timestamp="timestamp_list"),
       item_fields=dict(photo_id="photo_id_list",
             author_id="author_id_list",
             duration="durations_list",
             play_time="play_time_list",
             tag="tag_list",
             channel="channel_list",
             label="label_list",
             timestamp="timestamp_list")) \
      .gsu_with_time_clock(
        colossus_resp_attr='colossus_output',
        limit_num=50,
        filter_time=-1,
        output_sign_attr="living_gsu_signs_ev",
        output_slot_attr="living_gsu_slots_ev",
        slots_id=[21000, 21001, 21002, 21003, 21004, 21005],
        mio_slots_id=[21000, 21001, 21002, 21003, 21004, 21005],
      )

    leaf = self.__init_service(flow)
    leaf.request_time = int(23 * 1000)
    for i in range(3):
      item = leaf.add_item(i)
    leaf.user_id = user_id
    leaf.run('test_gsu_with_time_clock')
    self.assertNotEqual(leaf['living_gsu_signs_ev'], None, 'not found common attr colossus_slots')
    self.assertNotEqual(leaf['living_gsu_slots_ev'], None, 'not found common attr colossus_signs')

  @unittest.skip("skip unit test")
  def test_gsu_with_remote_dot_product(self):
    pass

  def test_gsu_with_living(self):
    user_id = self.mock_colossus_sim_v2()
    flow = GsuFlow(name="test_gsu_with_living")
    flow.user_id = user_id
    flow \
      .colossus(
        service_name="grpc_colossusSimV2",
        client_type="common_item_client",
        output_attr="colossus_output",
        debug_uids=str(user_id),
        print_items=True) \
      .enrich_attr_by_lua(
        export_common_attr=["uStandardLivingPhotoShowIDSeqPhotoIDList", "uStandardLivingPhotoShowIDSeqTimeList"],
        function_for_common="calculate",
        lua_script="""
          function calculate()
            return {75694025719, 75823594133, 75862597365, 75990765646}, {1660620244, 1660620244, 1660620244, 1660620244}
          end
        """) \
      .gsu_with_living(
        colossus_resp_attr='colossus_output',
        living_pid_list_attr='uStandardLivingPhotoShowIDSeqPhotoIDList',
        living_ts_list_attr='uStandardLivingPhotoShowIDSeqTimeList',
        limit_num=50,
        limit_min_play_time=-1,
        limit_max_play_time=10000,
        output_sign_attr="living_gsu_signs_ev",
        output_slot_attr="living_gsu_slots_ev",
        cluster_id_service_type="embedding_server",
        kess_service='kws-kuaishou-full-rank-embedding-mmu-hetu-cluster-id-long',
        # kess_service='mmu_hetu_cluster_id_query_server_nearby_follow_online',
        shards=4,
        timeout_ms=10 * 5000,
        slots_id=[21000, 21001, 21002, 21003, 21004, 21005],
        mio_slots_id=[21000, 21001, 21002, 21003, 21004, 21005],
      )

    leaf = self.__init_service(flow)
    leaf.request_time = int(time.time() * 1000)
    for i in range(1):
      item = leaf.add_item(i)
      item['aId'] = 177628711
    leaf.user_id = user_id
    leaf.run('test_gsu_with_living')
    self.assertNotEqual(leaf['living_gsu_signs_ev'], None, 'not found common attr colossus_slots')
    self.assertNotEqual(leaf['living_gsu_slots_ev'], None, 'not found common attr colossus_signs')

  def test_gsu_with_living_v2(self):
    user_id = self.mock_colossus_sim_v2()
    flow = GsuFlow(name="test_gsu_with_living_v2")
    flow.user_id = user_id
    flow \
      .colossus(
        service_name="grpc_colossusSimV2",
        client_type="common_item_client",
        output_attr="colossus_output",
        debug_uids=str(user_id),
        print_items=True) \
      .enrich_attr_by_lua(
        export_common_attr=["uStandardLivingPhotoShowIDSeqPhotoIDList", "uStandardLivingPhotoShowIDSeqTimeList"],
        function_for_common="calculate",
        lua_script="""
          function calculate()
            return {75694025719, 75823594133, 75862597365, 75990765646}, {1660620244, 1660620244, 1660620244, 1660620244}
          end
        """) \
      .gsu_with_living_v2(
        colossus_resp_attr='colossus_output',
        living_pid_list_attr='uStandardLivingPhotoShowIDSeqPhotoIDList',
        living_ts_list_attr='uStandardLivingPhotoShowIDSeqTimeList',
        limit_num=50,
        limit_min_play_time=-1,
        limit_max_play_time=10000,
        output_sign_attr="living_gsu_signs_v2",
        output_slot_attr="living_gsu_slots_v2",
        cluster_id_service_type="embedding_server",
        kess_service='kws-kuaishou-full-rank-embedding-mmu-hetu-cluster-id-long',
        # kess_service='mmu_hetu_cluster_id_query_server_nearby_follow_online',
        shards=4,
        timeout_ms=10 * 5000,
        slots_id=[21000, 21001, 21002, 21003, 21004, 21005, 31000, 31001, 31002, 31003, 31004, 31005],
        mio_slots_id=[21000, 21001, 21002, 21003, 21004, 21005, 31000, 31001, 31002, 31003, 31004, 31005],
      )

    leaf = self.__init_service(flow)
    leaf.request_time = int(time.time() * 1000)
    for i in range(1):
      item = leaf.add_item(i)
      item['aId'] = 177628711
    leaf.user_id = user_id
    leaf.run('test_gsu_with_living_v2')
    self.assertNotEqual(leaf['living_gsu_signs_v2'], None, 'not found common attr colossus_slots')
    self.assertNotEqual(leaf['living_gsu_slots_v2'], None, 'not found common attr colossus_signs')

  def test_video_author_list_from_colossus(self):
    user_id = self.mock_colossus_sim_v2()
    flow = GsuFlow(name="test_video_author_list_from_colossus")
    flow.user_id = user_id
    flow \
      .colossus(
        service_name="grpc_colossusSimV2",
        client_type="common_item_client",
        output_attr="colossus_output",
        debug_uids=str(user_id),
        print_items=True) \
      .video_author_list_from_colossus(
        colossus_resp_attr='colossus_output',
        output_prefix='video_author_',
        sim_item_version=2,
        limit_author_num=50)

    leaf = self.__init_service(flow)
    leaf.request_time = int(time.time() * 1000)
    for i in range(1):
      item = leaf.add_item(i)
      item['aId'] = 177628711
    leaf.user_id = user_id
    leaf.run('test_video_author_list_from_colossus')
    attrs = ["author_id", "duration_sum", "play_time_sum", "unseen_days",
             "like_count", "follow_count", "foward_count", "comment_count", "record_count"]
    attrs = ["video_author_" + x for x in attrs]
    for attr in attrs:
      print(attr, leaf[attr])
      self.assertNotEqual(leaf[attr], None, 'not found attr {}'.format(attr))

  def test_pxtrs_colossus(self):
    user_id = self.mock_colossus_sim_v2()
    flow = GsuFlow(name="test_pxtrs_colossus")
    flow.user_id = user_id
    flow \
      .gsu_common_colossusv2_enricher(kconf="colossus.kconf_client.hot_video_pxtr_item",
                            item_fields=dict(photo_id="photo_id_list",
                                             timestamp="timestamp_list",
                                             author_id="author_id_list")) \
      .pxtrs_colossus(
        photo_id_from="photo_id_list",
        author_id_from="author_id_list",
        timestamp_from="timestamp_list",
        colossus_resp_attr='colossus_resp',
        save_photo_id_to_attr='colossus_pid',
        save_author_id_to_attr='colossus_aid',
        save_timestamp_to_attr='colossus_ts',
        save_result_to_common_attr="filter_colossus_pid")

    leaf = self.__init_service(flow)
    leaf.request_time = int(time.time() * 1000)
    leaf.user_id = user_id
    leaf.run('test_pxtrs_colossus')

  def test_live_gsu_with_aid(self):
    user_id, cluster_id = self.mock_colossus()
    flow = GsuFlow(name="test_gsu_with_aid")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusLiveItem',
        client_type='common_item_client',
        output_attr="live_colossus_output",
        debug_uids="478801757",
        print_items=True) \
      .live_gsu_with_aid(
        colossus_resp_attr="live_colossus_output",
        limit_num=50,
        output_sign_attr="live_gsu_signs",
        output_slot_attr="live_gsu_slots",
        batch_request=False,
        slots_id=[26, 128, 349, 348, 350],
        mio_slots_id=[346, 347, 349, 348, 350],
        target_cluster_attr = "aId",
        # use_LiveItemV2=True,
        use_gift_info=True) \

    leaf = self.__init_service(flow)

    for i in range(1):
      item = leaf.add_item(i)
      item["aId"] = 177628711
    leaf["limit_num"] = 50
    leaf.user_id = user_id

    leaf.run("test_gsu_with_aid")
    for item in leaf.items:
      # print(f"gsu sim return cluster_id: {item['cluster_attr_live']}")
      self.assertNotEqual(item['live_gsu_signs'], None, 'not found common attr colossus_slots')
      self.assertNotEqual(item['live_gsu_slots'], None, 'not found common attr colossus_signs')

  def test_live_gsu_with_aid_v2(self):
    user_id, cluster_id = self.mock_colossus()
    flow = GsuFlow(name="test_gsu_with_aid_v2")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusLiveItem',
        client_type='common_item_client',
        output_attr="live_colossus_output",
        debug_uids="478801757",
        print_items=True) \
      .enrich_attr_by_lua(
      export_common_attr=["limit_num"],
      function_for_common="calculate",
      lua_script=f"""
        function calculate()
          return 50
        end
      """) \
      .live_gsu_with_aid_v2(
        colossus_resp_attr="live_colossus_output",
        limit_num_attr="limit_num",
        output_sign_attr="live_gsu_signs",
        output_slot_attr="live_gsu_slots",
        slots_id=[26, 128, 349, 348, 350],
        mio_slots_id=[346, 347, 349, 348, 350],
        target_cluster_attr = "aId",) \

    leaf = self.__init_service(flow)

    for i in range(1):
      item = leaf.add_item(i)
      item["aId"] = 177628711
    leaf["limit_num"] = 50
    leaf.user_id = user_id

    leaf.run("test_gsu_with_aid_v2")
    for item in leaf.items:
      # print(f"gsu sim return cluster_id: {item['cluster_attr_live']}")
      self.assertNotEqual(item['live_gsu_signs'], None, 'not found common attr colossus_slots')
      self.assertNotEqual(item['live_gsu_slots'], None, 'not found common attr colossus_signs')

  def test_live_gsu_with_cluster(self):
    user_id, cluster_id = self.mock_colossus()
    flow = GsuFlow(name="test_gsu_with_cluster")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusLiveItem',
        client_type='common_item_client',
        output_attr="live_colossus_output",
        debug_uids="478801757",
        print_items=True) \
      .live_gsu_with_cluster(
        hetu_cluster_config_key_name="reco.model2.liveHetuEmbeddingIndex100",
        colossus_resp_attr="live_colossus_output",
        limit_num_attr="limit_num",
        output_sign_attr="live_gsu_signs",
        output_slot_attr="live_gsu_slots",
        output_aid_list="sim_aid_list",
        output_cluster_attr="cluster_attr_live",
        slots_id=[26, 128, 375, 373, 374],
        mio_slots_id=[371, 372, 375, 373, 374],
        target_cluster_attr = "lHetuCoverEmbeddingCluster",) \

    leaf = self.__init_service(flow)

    for i in range(1):
      item = leaf.add_item(i)
      item["lHetuCoverEmbeddingCluster"] = cluster_id
    leaf["limit_num"] = 50
    leaf.user_id = user_id

    leaf.run("test_gsu_with_cluster")
    for item in leaf.items:
      # print(f"gsu sim return cluster_id: {item['cluster_attr_live']}")
      self.assertEqual(item["cluster_attr_live"], cluster_id)


  def test_live_gsu_with_cluster_v3(self):
    user_id, cluster_id = self.mock_colossus_v3()
    flow = GsuFlow(name="test_gsu_with_cluster_v3")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusLiveItemV3',
        client_type='common_item_client',
        output_attr="live_colossus_output",
        debug_uids="478801757",
        print_items=True) \
      .live_gsu_with_cluster_v3(
        hetu_cluster_config_key_name="reco.model2.liveHetuEmbeddingIndex100",
        colossus_resp_attr="live_colossus_output",
        limit_num_attr="limit_num",
        latest_time_thres=10,
        limit_play_time="limit_play_time",
        output_sign_attr="live_gsu_signs",
        output_slot_attr="live_gsu_slots",
        output_aid_list="sim_aid_list",
        output_cluster_attr="cluster_attr_live",
        slots_id=[26, 128, 375, 373, 374, 375, 376],
        mio_slots_id=[371, 372, 375, 373, 374, 375, 376],
        target_cluster_attr = "lHetuCoverEmbeddingCluster",) \

    leaf = self.__init_service(flow)

    for i in range(1):
      item = leaf.add_item(i)
      item["lHetuCoverEmbeddingCluster"] = [cluster_id]
    leaf["limit_num"] = 50
    leaf["limit_play_time"] = -1
    leaf.request_time = 1640762952000
    leaf.user_id = user_id

    leaf.run("test_gsu_with_cluster_v3")
    self.assertEqual(len(leaf["u_latest_live_id"]) > 0, 1)
    # print(f"colossus v3 debug log {leaf['u_latest_live_id']}")
    for item in leaf.items:
      # print(f"gsu sim return cluster_id: {item['cluster_attr_live']}")
      self.assertEqual(item["cluster_attr_live"], cluster_id)

  def test_live_prerank_gsu_with_cluster_v3(self):
    user_id, cluster_id = self.mock_colossus_v3()
    flow = GsuFlow(name="test_prerank_gsu_with_cluster_v3")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusLiveItemV3',
        client_type='common_item_client',
        output_attr="live_colossus_output",
        debug_uids="478801757",
        print_items=True) \
      .live_prerank_gsu_with_cluster_v3(
        hetu_cluster_config_key_name="reco.model2.liveHetuEmbeddingIndex100",
        colossus_resp_attr="live_colossus_output",
        limit_num_attr="limit_num",
        latest_time_thres=10,
        limit_play_time="limit_play_time",
        output_sign_attr="live_gsu_signs",
        output_slot_attr="live_gsu_slots",
        output_aid_list="sim_aid_list",
        slot_as_attr_name=False,
        output_cluster_attr="cluster_attr_live",
        slots_id=[26, 128, 375, 373, 374, 375, 376],
        mio_slots_id=[371, 372, 375, 373, 374, 375, 376],
        target_cluster_attr = "lHetuCoverEmbeddingCluster",) \

    leaf = self.__init_service(flow)

    for i in range(1):
      item = leaf.add_item(i)
      item["lHetuCoverEmbeddingCluster"] = [cluster_id]
    leaf["limit_num"] = 50
    leaf["limit_play_time"] = -1
    leaf.request_time = 1640762952000
    leaf.user_id = user_id

    leaf.run("test_prerank_gsu_with_cluster_v3")
    self.assertEqual(len(leaf["u_latest_live_id"]) > 0, 1)
    # print(f"colossus v3 debug log {leaf['u_latest_live_id']}")
    for item in leaf.items:
      # print(f"gsu sim return cluster_id: {item['cluster_attr_live']}")
      # self.assertEqual(item["cluster_attr_live"], cluster_id)
      self.assertNotEqual(item['live_gsu_signs'], None, 'not found common attr colossus_slots')
      self.assertNotEqual(item['live_gsu_slots'], None, 'not found common attr colossus_signs')

  @unittest.skip("v4 skipping")
  def test_live_gsu_with_aid_v4(self):
    user_id, cluster_id = self.mock_colossus_v4()
    flow = GsuFlow(name="test_gsu_with_aid_v4")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusLiveItemV4',
        client_type='common_item_client',
        output_attr="live_colossus_output",
        debug_uids="666",
        parse_to_pb=False,
        print_items=True) \
      .live_gsu_with_aid_v4(
        hetu_cluster_config_key_name="reco.model2.liveHetuEmbeddingIndex100",
        colossus_resp_attr="live_colossus_output",
        limit_num=50,
        output_sign_attr="live_gsu_signs",
        output_slot_attr="live_gsu_slots",
        batch_request=True,
        filter_living=True,
        slots_id=[26, 128, 1430, 1431, 1432, 1433, 1434, 1435, 1436, 1437, 1438, 1439],
        mio_slots_id=[1428, 1429, 1430, 1431, 1432, 1433, 1434, 1435, 1436, 1437, 1438, 1439],
        target_cluster_attr = "aId",) \

    leaf = self.__init_service(flow)
    leaf.request_time = 1754458036000
    for i in range(1):
      item = leaf.add_item(i)
      item["aId"] = 2011777021
    leaf.user_id = user_id

    leaf.run("test_gsu_with_aid_v4")
    for item in leaf.items:
      # print(f"gsu sim return cluster_id: {item['cluster_attr_live']}")
      self.assertNotEqual(item['live_gsu_signs'], None, 'not found common attr colossus_slots')
      self.assertNotEqual(item['live_gsu_slots'], None, 'not found common attr colossus_signs')


  @unittest.skip("v4 skipping")
  def test_live_gsu_with_cluster_v4(self):
    user_id, cluster_id = self.mock_colossus_v4()
    flow = GsuFlow(name="test_gsu_with_cluster_v4")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusLiveItemV4',
        client_type='common_item_client',
        output_attr="live_colossus_output",
        debug_uids="666",
        parse_to_pb=False,
        print_items=True) \
      .live_gsu_with_cluster_v4(
        hetu_cluster_config_key_name="reco.model2.liveHetuEmbeddingIndex100",
        colossus_resp_attr="live_colossus_output",
        limit_num_attr="limit_num",
        output_sign_attr="live_gsu_signs",
        output_slot_attr="live_gsu_slots",
        output_aid_list="sim_aid_list",
        # filter_e_shop=True,
        output_cluster_attr="cluster_attr_live",
        slots_id=[26, 374, 128, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388],
        mio_slots_id= [1140, 1123, 1124, 1125, 1126, 1127, 1128, 1129, 1130, 1131, 1132, 1133, 1134, 1135, 1136, 1137, 1138, 1139, 1140],
        target_cluster_attr = "lHetuCoverEmbeddingCluster",) \

    leaf = self.__init_service(flow)
    leaf.request_time = 1754458036000
    for i in range(1):
      item = leaf.add_item(i)
      item["lHetuCoverEmbeddingCluster"] = cluster_id
    leaf["limit_num"] = 3
    leaf.user_id = user_id

    leaf.run("test_gsu_with_cluster_v4")

    # print("aid list", leaf["sim_aid_list"])
    self.assertEqual(leaf["sim_aid_list"], [666], 'merge fail')
    for item in leaf.items:
      print(f"gsu sim return cluster_id: {item['cluster_attr_live']}\n \
            gsu signs: {item['live_gsu_signs']},\n \
            gsu slots: {item['live_gsu_slots']};")
      self.assertNotEqual(item['live_gsu_signs'], None, 'not found common attr colossus_slots')
      self.assertNotEqual(item['live_gsu_slots'], None, 'not found common attr colossus_signs')
      for sign in item['live_gsu_signs']:
        # print(sign, sign >> 54, sign & (( 1 << 54 ) - 1))
        if (int(sign) >> 54) == 378:
          self.assertEqual(sign & (( 1 << 54 ) - 1), 5, "label merge fail")
      self.assertEqual(item["cluster_attr_live"], cluster_id)

  def mock_derive_colossus_v4(self):
    mock_items = {}
    user_id = 666
    cluster_id = 204
    live_id_list = [9611138711, 9611138711, 9674425995, 9674386592, 9674336243, 9674454689, 9674383969, 9686886286, 9684696721, 9704553751]
    timestamp_list = [1654458036, 1654458123, 1655998550, 1655998733, 1655999201, 1655999225, 1655999266, 1656314115, 1656406127, 1656739203]
    author_id_list =  [666, 666, 2011777021, 1291205167, 941901644, 2899812133, 586064739, 670333339, 2470293124, 916369360]
    item_id_list =  [66600, 66600, 2011777000, 1291205100, 941901600, 2899812100, 586064700, 670333300, 2470293100, 916369300]
    play_time_list =  [0, 17, 151, 3, 5, 2, 5, 4, 24, 5]
    auto_play_time_list =  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    hetu_tag_channel_list =  [39168, 39168, 506626, 39170, 39170, 165890, 87298, 50, 361989, 236034]
    cluster_id_list =  [426, 204, 576, 204, 204, 243, 243, 698, 445, 675]
    label_list = [256, 256, 256, 256, 256, 256, 256, 0, 256, 0]
    reward_list =  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    reward_count_list =  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    audience_count_list =  [0, 0, 18, 2, 6, 11, 1, 25163, 6, 5]
    user_latitude_list =  [33.605000 ,33.605000, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.603462]
    user_longitude_list =  [113.266068 ,113.266068, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.272285]
    author_latitude_list =  [33.605000 ,33.605000, 33.971607, 33.432060, 36.142181, 1.000000, 33.975880, 35.045761, -1.000000, 32.531445]
    author_longitude_list =  [113.266068 ,113.266068, 113.220009, 113.368248, 113.814651, 1.000000, 113.222862, 118.300484, -1.000000, 112.375496]
    order_price_list =  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]

    mock_items["live_id"] = live_id_list
    mock_items["timestamp"] = timestamp_list
    mock_items["author_id"] = author_id_list
    mock_items["play_time"] = play_time_list
    mock_items["auto_play_time"] = auto_play_time_list
    mock_items["hetu_tag_channel"] = hetu_tag_channel_list
    mock_items["cluster_id"] = cluster_id_list
    mock_items["label"] = label_list
    mock_items["reward"] = reward_list
    mock_items["reward_count"] = reward_count_list
    mock_items["item_id"] = item_id_list
    mock_items["audience_count"] = audience_count_list
    mock_items["user_latitude"] = user_latitude_list
    mock_items["user_longitude"] = user_longitude_list
    mock_items["author_latitude"] = author_latitude_list
    mock_items["author_longitude"] = author_longitude_list
    mock_items["order_price"] = order_price_list

    item_list_len = 10

    live_item_field_and_size  = [
      ("live_id", 8, "Q"),
      ("timestamp", 4, "I"),
      ("author_id", 4, "I"),
      ("play_time", 2, "H"),
      ("auto_play_time", 2, "H"),
      ("hetu_tag_channel", 4, "I"),
      ("cluster_id", 2, "H"),
      ("label", 2, "H"),
      ("reward", 4, "I"),
      ("reward_count", 2, "H"),
      ("item_id", 8, "Q"),
      ("audience_count", 4, "I"),
      ("user_latitude", 4, "f"),
      ("user_longitude", 4, "f"),
      ("author_latitude", 4, "f"),
      ("author_longitude", 4, "f"),
      ("order_price", 2, "H")
    ]
    item_size = 0
    for (name, size, format_str) in live_item_field_and_size:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in live_item_field_and_size:
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size

    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_colossusLiveItemV4", response_json_str=json.dumps(resp))
    return user_id, cluster_id

  @unittest.skip("skip unit test")
  def test_live_gsu_longterm_item_derive(self):
    user_id, cluster_id = self.mock_derive_colossus_v4()
    flow = GsuFlow(name="test_live_gsu_longterm_item_derive")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusLiveItemV4',
        client_type='common_item_client',
        output_attr="live_colossus_output",
        debug_uids="666",
        parse_to_pb=False,
        print_items=True) \
      .live_gsu_longterm_item_derive(
        colossus_resp_attr="live_colossus_output",
        limit_num_attr="limit_num",
        output_sign_attr="live_gsu_derive_signs",
        output_slot_attr="live_gsu_derive_slots",
        output_aid_list="sim_aid_list",
        output_cluster_ids="output_cluster_ids",
        slots_id=[26, 374, 128, 373, 1126, 1127, 375],
        mio_slots_id=[1140, 1123, 1124, 1125, 1126, 1127, 1128],
        cal_scope = 0,
        cal_basis = 0,
        duration_limit = 2,
        k_num = 6,
        obtain_e_shop=True) \

    leaf = self.__init_service(flow)
    leaf.request_time = 1656999266000
    leaf["limit_num"] = 50
    leaf.user_id = user_id

    leaf.run("test_live_gsu_longterm_item_derive")
    print("aid list", leaf["sim_aid_list"])
    print("top/latest attr ids list", leaf["output_cluster_ids"])
    print(f"gsu longterm derive return output_cluster_ids: {leaf['output_cluster_ids']}\n \
          gsu signs: {leaf['live_gsu_derive_signs']},\n \
          gsu slots: {leaf['live_gsu_derive_slots']};")
    self.assertNotEqual(leaf['live_gsu_derive_signs'], None, 'not found common attr colossus_slots')
    self.assertNotEqual(leaf['live_gsu_derive_slots'], None, 'not found common attr colossus_signs')

  def test_live_gsu_with_kgnn_aids(self):
    user_id, cluster_id = self.mock_colossus()
    flow = GsuFlow(name="test_live_gsu_with_kgnn_aids")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusLiveItem',
        client_type='common_item_client',
        output_attr='live_colossus_output',
        debug_uids='478801757') \
      .live_gsu_with_kgnn_aids(
        colossus_resp_attr='live_colossus_output',
        output_sign_attr='live_gsu_signs',
        output_slot_attr='live_gsu_slots',
        limit_num_attr='limit_num',
        target_aids_attr='kgnn_aids')

    leaf = self.__init_service(flow)

    for i in range(1):
      item = leaf.add_item(i)
      item['kgnn_aids'] = [177628711, 177628712]
    leaf["limit_num"] = 50
    leaf.request_time = 1641062952000
    # leaf.user_id = user_id

    leaf.run("test_live_gsu_with_kgnn_aids")
    for item in leaf.items:
      print(f"kgnn sim return gsu signs: {item['live_gsu_signs']},\n \
              gsu_slots: {item['live_gsu_slots']};")
      self.assertNotEqual(item["live_gsu_slots"], None, "not found item attr gsu_slots")
      self.assertNotEqual(item["live_gsu_signs"], None, "not found item attr gsu_signs")
    
  @unittest.skip("skip unit test")
  def test_ksib_live_gsu_with_aids(self):
    user_id = self.mock_ksib_live_colossus()
    flow = GsuFlow(name="test_ksib_live_gsu_with_aids")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusKsibLiveItemNew',
        client_type='common_item_client',
        output_attr='live_colossus_output',
        debug_uids='478801757') \
      .ksib_live_gsu_with_aids(
        colossus_resp_attr='live_colossus_output',
        output_sign_attr='live_gsu_signs',
        output_slot_attr='live_gsu_slots',
        slots_id = [100, 101, 422, 423, 424],
        mio_slots_id = [420, 421, 422, 423, 424],
        limit_num_attr='limit_num',
        target_aids_attr='kgnn_aids')

    leaf = self.__init_service(flow)

    for i in range(1):
      item = leaf.add_item(i)
      item['kgnn_aids'] = [150001342763415, 150000238807894]
    leaf["limit_num"] = 50
    leaf.request_time = 1722829648446
    # leaf.user_id = user_id

    leaf.run("test_ksib_live_gsu_with_aids")
    for item in leaf.items:
      print(f"sim return gsu signs: {item['live_gsu_signs']},\n \
              gsu_slots: {item['live_gsu_slots']};")
      self.assertNotEqual(item["live_gsu_slots"], None, "not found item attr gsu_slots")
      self.assertNotEqual(item["live_gsu_signs"], None, "not found item attr gsu_signs")

  def test_gsu_common_colossus_resp_retriever(self):
    user_id = 666
    item_list_len = 5
    mock_items = {}
    photo_id_list = [1, 2, 3, 4, 5]
    mock_items["photo_id"] = photo_id_list
    self.assertEqual(item_list_len, len(photo_id_list))
    author_id_list = [10, 20, 30, 40, 50]
    mock_items["author_id"] = author_id_list
    self.assertEqual(item_list_len, len(author_id_list))
    duration_list = [100, 200, 300, 400, 500]
    mock_items["duration"] = duration_list
    self.assertEqual(item_list_len, len(duration_list))
    play_time_list = [10, 20, 30, 40, 50]
    mock_items["play_time"] = play_time_list
    self.assertEqual(item_list_len, len(play_time_list))
    tag_list = [21, 22, 23, 24, 25]
    mock_items["tag"] = tag_list
    self.assertEqual(item_list_len, len(tag_list))
    channel_list = [5, 4, 3, 2, 1]
    mock_items["channel"] = channel_list
    self.assertEqual(item_list_len, len(channel_list))
    label_list = [11, 12, 13, 14, 15]
    mock_items["label"] = label_list
    self.assertEqual(item_list_len, len(label_list))
    timestamp_list = [1650088495, 1650088955, 1650089335, 1650089583, 1650089641]
    mock_items["timestamp"] = timestamp_list
    self.assertEqual(item_list_len, len(timestamp_list))
    user_latitude_list = [0.0, 0.0, 0.0, 0.0, 0.0]
    mock_items["user_latitude"] = user_latitude_list
    self.assertEqual(item_list_len, len(user_latitude_list))
    user_longitude_list = [0.0, 0.0, 0.0, 0.0, 0.0]
    mock_items["user_longitude"] = user_longitude_list
    self.assertEqual(item_list_len, len(user_longitude_list))
    photo_latitude_list = [0.0, 0.0, 0.0, 0.0, 0.0]
    mock_items["photo_latitude"] = photo_latitude_list
    self.assertEqual(item_list_len, len(photo_latitude_list))
    photo_longitude_list = [0.0, 0.0, 0.0, 0.0, 0.0]
    mock_items["photo_longitude"] = photo_longitude_list
    self.assertEqual(item_list_len, len(photo_longitude_list))
    # field_name, size, python struct format
    # struct format reference: https://docs.python.org/zh-cn/3/library/struct.html
    sim_item_v2_field_and_sizes = [
      ("photo_id", 8, 'Q'),
      ("author_id", 4, 'I'),
      ("duration", 2, 'H'),
      ("play_time", 2, 'H'),
      ("tag", 4, 'I'),
      ("channel", 1, 'B'),
      ("label", 2, 'H'),
      ("timestamp", 4, 'I'),
      ("user_latitude", 4, 'f'),
      ("user_longitude", 4, 'f'),
      ("photo_latitude", 4, 'f'),
      ("photo_longitude", 4, 'f')
    ]
    # construct SimItemV2 raw bytes
    item_size = 0
    for (name, size, format_str) in sim_item_v2_field_and_sizes:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in sim_item_v2_field_and_sizes:
        #print(name, i, type(i), mock_items[name])
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size
    # construct CommonItemResponse
    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_colossusSimV2", response_json_str=json.dumps(resp))
    flow = GsuFlow(name="gsu_common_colossus_resp_retriever")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusSimV2',
        client_type='common_item_client',
        output_attr="sim_v2_colossus_output",
        parse_to_pb=False,
        debug_uids="666",
        print_items=True) \

    flow.gsu_common_colossus_resp_retriever(
        colossus_resp_attr="sim_v2_colossus_output",
        colossus_service_name="grpc_colossusSimV2",
        item_key_field="photo_id",
        item_time_field="timestamp",
        to_common_attr=True,
        print_item_fields=True,
        item_fields=dict(photo_id="photo_id",
                      author_id="author_id",
                      duration="duration",
                      play_time="play_time",
                      tag="tag",
                      channel="channel",
                      label="label",
                      timestamp="timestamp",
                      user_latitude="user_latitude",
                      user_longitude="user_longitude",
                      photo_latitude="photo_latitude",
                      photo_longitude="photo_longitude"))

    leaf = self.__init_service(flow)
    leaf.run("gsu_common_colossus_resp_retriever")
    for (attr_name, size, format_str) in sim_item_v2_field_and_sizes:
      self.assertNotEqual(leaf[attr_name], None, f'not found common attr {attr_name}')
      self.assertEqual(len(leaf[attr_name]), item_list_len)
      for i in range(item_list_len):
        self.assertEqual(leaf[attr_name][i], mock_items[attr_name][i])

  def test_gsu_common_colossus_resp_retriever_with_colossus_sim_v2(self):
    client_kconf = 'colossus.kconf_client.video_item'
    user_id = 666
    mock_items = {
      "photo_id": [1, 2, 3],
      "author_id": [4, 5, 6],
      "author_id": [7, 8, 9],
      "duration": [10, 11, 12],
      "play_time": [13, 14, 15],
      "tag": [16, 17, 18],
      "channel": [16, 17, 18],
      "label": [19, 20, 21],
      "timestamp": [22, 23, 24]
    }
    item_list_len = len(next(iter(mock_items.values())))
    field_infos = self._get_colossusdb_sim_field_infos(client_kconf, mock_items)
    resp = self._serialize_colossusdb_sim_data(item_list_len, mock_items, field_infos)
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_clsdb_sim-video-2_short-video",
                                 response_json_str=json.dumps(resp))
    flow = GsuFlow(name="test_gsu_common_colossus_resp_retriever_with_colossus_sim_v2")
    flow\
        .gsu_common_colossusv2_enricher(kconf=client_kconf,
                                        partial_schema_output_attr="video_item_schema",
                                        item_fields=dict(photo_id="photo_id_list",
                                                         author_id="author_id_list",
                                                         duration="duration_list",
                                                         play_time="play_time_list",
                                                         tag="tag_list",
                                                         channel="channel_list",
                                                         label="label_list",
                                                         timestamp="timestamp_list")) \
      .gsu_common_colossus_resp_retriever(
        from_colossus_sim_v2=True,
        print_item_fields=True,
        partial_schema_input_attr="video_item_schema",
        item_key_field="photo_id",
        item_time_field="timestamp",
        input_item_fields=dict(photo_id="photo_id_list",
                      author_id="author_id_list",
                      duration="duration_list",
                      play_time="play_time_list",
                      tag="tag_list",
                      channel="channel_list",
                      label="label_list",
                      timestamp="timestamp_list"),
        item_fields=dict(photo_id="pids",
                      author_id="aids",
                      duration="drs",
                      play_time="pls",
                      tag="tags",
                      channel="cns",
                      label="lbs",
                      timestamp="tss")) \
        .log_debug_info(item_attrs=[
                                    "pids",
                                    "aids",
                                    "tss",
                                  ],)

    leaf = self.__init_service(flow)
    leaf.user_id = user_id
    leaf.run("test_gsu_common_colossus_resp_retriever_with_colossus_sim_v2")
    for i, item in enumerate(leaf.items):
      index = item_list_len - i - 1
      self.assertEqual(item["pids"], mock_items['photo_id'][index])
      self.assertEqual(item["aids"], mock_items['author_id'][index])
      self.assertEqual(item["drs"], mock_items['duration'][index])
      self.assertEqual(item["pls"], mock_items['play_time'][index])
      self.assertEqual(item["tags"], mock_items['tag'][index])
      self.assertEqual(item["cns"], mock_items['channel'][index])
      self.assertEqual(item["lbs"], mock_items['label'][index])
      self.assertEqual(item["tss"], mock_items['timestamp'][index])

  def _get_colossusdb_sim_field_infos(self, client_kconf, mock_items=None):
    clt_kconf_json = get_json_config(client_kconf)
    schema_kconf_json = get_json_config(clt_kconf_json['schema_kconf_path'])
    schema_json = schema_kconf_json['schemas']
    version = schema_json['consume_version']
    schema = schema_json['versions'][version]
    fields_json = schema['fields']
    field_num = len(fields_json)
    field_infos = []
    # struct format reference: https://docs.python.org/zh-cn/3/library/struct.html
    # field_name, field_size, field_format
    # field_infos 中各个 field 的顺序必须与线上服务 kconf 里面的顺序一致，否则解析出来的顺序会混乱
    type_metas = {
      'char': (1, 'c'),
      'int8_t': (1, 'b'),
      'uint8_t': (1, 'B'),
      'int16_t': (2, 'h'),
      'uint16_t': (2, 'H'),
      'int32_t': (4, 'i'),
      'uint32_t': (4, 'I'),
      'int64_t': (8, 'q'),
      'uint64_t': (8, 'Q'),
      'float': (4, 'f'),
      'double': (8, 'd'),
    }
    sort_indexes = schema['sort_index']
    sort_index_num = 0
    for i in range(0, field_num):
      for field_name, field_meta in fields_json.items():
        if mock_items and field_name not in mock_items:
          continue
        if i == field_meta['index']:
          field_info = (field_name,) + type_metas[field_meta['type']]
          field_infos.append(field_info)
          if i in sort_indexes:
            sort_index_num = sort_index_num + 1
          continue
    self.assertEqual(len(sort_indexes), sort_index_num, f'need all sort_index field: {sort_indexes}')
    return field_infos

  def _serialize_colossusdb_sim_data(self, item_list_len, mock_items, field_infos):
    self.assertEqual(len(mock_items), len(field_infos))
    # construct LiveItemV4 raw bytes
    item_size = 0
    for (name, size, format_str) in field_infos:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for (name, size, format_str) in field_infos:
      for i in range(item_list_len):
        # print(name, i, type(i), mock_items[name])
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size
    # construct colossusdb.Response
    resp = dict(
      error_code = 1,
      datas = [encode_bytes(buffer)],
      data_type = 2,
      compress_type = 0,
      error_msg = '',
      groupby_result = [],
      select_columns = [],
      column_alias = []
    )
    return resp

  def _serialize_clotho_data_for_clotho(self, client_kconf, item_list_len, mock_items, field_infos):
    self.assertEqual(len(mock_items), len(field_infos))
    # get schema version
    clt_kconf_json = get_json_config(client_kconf)
    schema_kconf_json = get_json_config(clt_kconf_json['schema_kconf_path'])
    schema_json = schema_kconf_json['schemas']
    schema_version_str = schema_json['consume_version']
    from datetime import datetime
    schema_version = int(datetime.strptime(schema_version_str, '%Y_%m%d_%H%M%S').timestamp())

    # construct raw bytes
    item_size = 0
    for (name, size, format_str) in field_infos:
      item_size = item_size + size
    buffer = bytearray(4 + item_size * item_list_len)
    # set schema version
    struct.pack_into('I', buffer, 0, schema_version)
    offset = 4
    for (name, size, format_str) in field_infos:
      for i in range(item_list_len):
        # print(name, i, type(i), mock_items[name])
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size
    return buffer

  def _serialize_colossusdb_sim_update(self, item_list_len, key, schema_version, mock_items, field_infos):
    self.assertEqual(len(mock_items), len(field_infos))
    # construct LiveItemV4 raw bytes
    item_size = 0
    for (name, size, format_str) in field_infos:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for (name, size, format_str) in field_infos:
      for i in range(item_list_len):
        # print(name, i, type(i), mock_items[name])
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size
    # construct colossusdb.UpdateItems
    resp = dict(
      items = [encode_bytes(buffer)],
      keys = [key],
      update_type = 1,
      data_type = 2,
      schema = dict(
        schema_version = schema_version
      )
    )
    return resp

  def test_gsu_common_colossusv2_enricher(self):
    client_kconf = 'colossus.kconf_client.auto_live_item'
    user_id = 666
    mock_items = {
      "live_id": [1, 2, 3],
      "timestamp": [4, 5, 6],
      "author_id": [7, 8, 9],
      "play_time": [10, 11, 12],
      "auto_play_time": [13, 14, 15],
      "hetu_tag_channel": [16, 17, 18],
      "cluster_id": [16, 17, 18],
      "label": [19, 20, 21],
      "reward": [22, 23, 24],
      "reward_count": [25, 26, 27],
      "item_id": [28, 29, 30],
      "audience_count": [31, 32, 33],
      "user_latitude": [1.0, 2.0, 3.0],
      "user_longitude": [4.0, 5.0, 6.0],
      "order_price": [34, 35, 36]
    }
    item_list_len = len(next(iter(mock_items.values())))
    field_infos = self._get_colossusdb_sim_field_infos(client_kconf, mock_items)
    resp = self._serialize_colossusdb_sim_data(item_list_len, mock_items, field_infos)
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_clsdb_sim-live-1_auto_live_item",
                                 response_json_str=json.dumps(resp))
    flow = GsuFlow(name="gsu_common_colossusv2_enricher")
    flow\
        .gsu_common_colossusv2_enricher(kconf=client_kconf,
                                        v1_colossus_resp='live_item_v4_colossus_resp',
                                        v1_service_name='grpc_colossusLiveItemV4B',
                                        # 转成老版 colossus 的格式，同时也把每列解析到 common attr
                                        also_save_to_v2=True,
                                        item_fields=dict(live_id="live_id_list",
                                                         timestamp="timestamp_list",
                                                         author_id="author_id_list",
                                                         play_time="play_time_list",
                                                         auto_play_time="auto_play_time_list",
                                                         hetu_tag_channel="hetu_tag_channel_list",
                                                         cluster_id="cluster_id_list",
                                                         label="label_list",
                                                         reward="reward_list",
                                                         reward_count="reward_count_list",
                                                         item_id="item_id_list",
                                                         audience_count="audience_count_list",
                                                         user_latitude="user_latitude_list",
                                                         user_longitude="user_longitude_list",
                                                         order_price="order_price_list")) \
        .log_debug_info(common_attrs=[
                                    "live_id_list",
                                    "timestamp_list",
                                  ],)

    leaf = self.__init_service(flow)
    leaf.user_id = user_id
    leaf.run("gsu_common_colossusv2_enricher")
    for (attr_name, size, format_str) in field_infos:
      attr_vals = leaf[attr_name + '_list']
      self.assertNotEqual(attr_vals, None, f'not found common attr {attr_name}_list')
      self.assertEqual(len(attr_vals), item_list_len, f'lenght not match, {attr_name}')
      for i in range(item_list_len):
        self.assertEqual(attr_vals[i], mock_items[attr_name][i], f'value not match, {attr_name}')
  
  def test_gsu_common_colossusv2_batch_enricher(self):
    flow = GsuFlow(name="gsu_common_colossusv2_batch_enricher")
    leaf = self.__init_service(flow)
    leaf.user_id = 666
    # colossusdb client 暂时不支持 MockRPC 先跳过，等支持了再打开
    return

  def test_gsu_common_colossusv2_debug_enricher(self):
    flow = GsuFlow(name="gsu_common_colossusv2_debug_enricher")
    leaf = self.__init_service(flow)
    leaf.user_id = 666
    # 测试用的 processor 没必要加单侧
    return

  def test_gsu_common_colossusv2_checkpoint_enricher(self):
    flow = GsuFlow(name="gsu_common_colossusv2_checkpoint_enricher")
    leaf = self.__init_service(flow)
    leaf.user_id = 666
    return

  def test_gsu_retriever_with_colossus_resp_batch_v2(self):
    flow = GsuFlow(name="gsu_retriever_with_colossus_resp_batch_v2")
    leaf = self.__init_service(flow)
    leaf.user_id = 666
    return
  
  def test_gsu_common_colossusv2_batch_parse_enricher(self):
    flow = GsuFlow(name="gsu_common_colossusv2_batch_parse_enricher")
    leaf = self.__init_service(flow)
    leaf.user_id = 666
     # colossusdb client 暂时不支持 MockRPC 先跳过，等支持了再打开
    return

  def test_gsu_common_colossusv2_batch_decode_enricher(self):
    flow = GsuFlow(name="gsu_common_colossusv2_batch_decode_enricher")
    leaf = self.__init_service(flow)
    leaf.user_id = 666
     # colossusdb client 暂时不支持 MockRPC 先跳过，等支持了再打开
    return

  def test_gsu_common_colossusv2_json_enricher(self):
    flow = GsuFlow(name="gsu_common_colossusv2_json_enricher")
    leaf = self.__init_service(flow)
    leaf.user_id = 666
    # 测试用的 processor 没必要加单侧
    return

  def test_gsu_common_colossusv2_clotho_merge_enricher(self):
    client_kconf = 'colossus.kconf_client.auto_live_item'
    user_id = 666
    mock_items = {
      "live_id": [1, 2, 3],
      "timestamp": [10, 20, 30],
      "author_id": [100, 200, 300],
      "play_time": [1000, 2000, 3000],
      "auto_play_time": [10000, 20000, 30000],
    }
    mock_clotho_items = {
      "live_id": [2, 3, 4],
      "timestamp": [20, 30, 40],
      "author_id": [200, 300, 400],
      "play_time": [2000, 3000, 4000],
      "auto_play_time": [20000, 30000, 4000],
    }
    merged_items = {
      "live_id": [1, 2, 3, 4],
      "timestamp": [10, 20, 30, 40],
      "author_id": [100, 200, 300, 400],
      "play_time": [1000, 2000, 3000, 4000],
      "auto_play_time": [10000, 20000, 30000, 40000],
    }
    item_list_len = len(next(iter(mock_items.values())))
    field_infos = self._get_colossusdb_sim_field_infos(client_kconf, mock_items)
    resp = self._serialize_colossusdb_sim_data(item_list_len, mock_items, field_infos)
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_clsdb_sim-live-1_auto_live_item",
                                 response_json_str=json.dumps(resp))
    # mock clotho data
    clotho_data = self._serialize_clotho_data_for_clotho(client_kconf, item_list_len, mock_items, field_infos)

    flow = GsuFlow(name="gsu_common_colossusv2_clotho_merge_enricher")
    flow\
        .gsu_common_colossusv2_enricher(
        debug_print_item_num=10,
        #query_key="update_user_id",
        kconf="colossus.kconf_client.auto_live_item",
        item_datas_output_attr="auto_live_item_data",
        reflection_output_attr="auto_live_item_reflection",
        do_not_parse_to_common_attr=True,
        item_fields=dict(live_id="",
                          timestamp="",
                          author_id="",
                          play_time="",
                          auto_play_time="")) \
      .gsu_common_colossusv2_clotho_merge_enricher(
          client_kconf="colossus.kconf_client.auto_live_item",
          debug_print_item_num=10,
          colossus_reflection_from="auto_live_item_reflection",
          colossus_data_from="auto_live_item_data",
          # clotho_data_from="clotho_value",
          # output_data_to="output_merged_items",
          item_fields=dict(live_id="live_id_list",
                            timestamp="timestamp_list",
                            author_id="author_id_list",
                            play_time="play_time_list",
                            auto_play_time="auto_play_time_list")) \
        .log_debug_info(common_attrs=[
                                    "live_id_list",
                                    "timestamp_list",
                                  ],)

    leaf = self.__init_service(flow)
    leaf.user_id = user_id
    # 这个 buffer 没法设置成一个 PtrCommonAttr<std::string>
    # 跑不过，先不验证 merge clotho 的功能了
    # leaf['clotho_value'] = clotho_data
    leaf.run("gsu_common_colossusv2_clotho_merge_enricher")
    for (attr_name, size, format_str) in field_infos:
      attr_vals = leaf[attr_name + '_list']
      self.assertNotEqual(attr_vals, None, f'not found common attr {attr_name}_list')
      self.assertEqual(len(attr_vals), item_list_len, f'lenght not match, {attr_name}')
      for i in range(item_list_len):
        self.assertEqual(attr_vals[i], mock_items[attr_name][i], f'value not match, {attr_name}')

  def test_gsu_common_colossusv2_parse_update_enricher(self):
    user_id = 666
    mock_items = {
      "photo_id": [1, 2, 3],
      "author_id": [4, 5, 6],
      "duration": [7, 8, 9],
      "play_time": [10, 11, 12],
      "tag": [13, 14, 15],
      "channel": [16, 17, 18],
      "label": [16, 17, 18],
      "timestamp": [19, 20, 21],
      "profile_stay_time": [22, 23, 24],
      "comment_stay_time": [25, 26, 27],
      "profile_feed_mode_stay_time": [28, 29, 30],
      "real_show_index": [31, 32, 33]
    }
    # struct format reference: https://docs.python.org/zh-cn/3/library/struct.html
    # field_name, field_size, field_format
    # field_infos 中各个 field 的顺序必须与线上服务 kconf 里面的顺序一致，否则解析出来的顺序会混乱
    field_infos = [
      ("photo_id", 8, 'Q'),
      ("author_id", 4, 'I'),
      ("duration", 2, 'H'),
      ("play_time", 2, 'H'),
      ("tag", 4, 'I'),
      ("channel", 1, 'B'),
      ("label", 2, 'H'),
      ("timestamp", 4, 'I'),
      ("profile_stay_time", 2, 'H'),
      ("comment_stay_time", 2, 'H'),
      ("profile_feed_mode_stay_time", 2, 'H'),
      ("real_show_index", 4, 'I')
    ]
    item_list_len = len(next(iter(mock_items.values())))
    # schema version string: 2023_0919_120000
    version = 1695096000
    update_items = self._serialize_colossusdb_sim_update(item_list_len, user_id, version, mock_items, field_infos)
    update_items_json_str = json.dumps(update_items)
    print(update_items_json_str)
    flow = GsuFlow(name="gsu_common_colossusv2_parse_update_enricher")
    flow.gsu_common_colossusv2_parse_update_enricher(schema_kconf="colossus.tables.video_item",
                    mock_json_from="mock_update_json",
                    input_data_from="raw_update_str",
                    debug_print_item_num=10,
                    output_key_attr="uid",
                    output_fields=dict(photo_id="photo_id",
                                        author_id="author_id",
                                        duration="duration",
                                        play_time="play_time",
                                        tag="tag",
                                        channel="channel",
                                        label="label",
                                        profile_stay_time="profile_stay_time",
                                        comment_stay_time="comment_stay_time",
                                        profile_feed_mode_stay_time="profile_feed_mode_stay_time",
                                        real_show_index="real_show_index",
                                        timestamp="timestamp"))
    leaf = self.__init_service(flow)
    leaf.user_id = 666
    leaf['mock_update_json'] = update_items_json_str
    leaf.run('gsu_common_colossusv2_parse_update_enricher')
    self.assertEqual(leaf['uid'], user_id)
    for (attr_name, field_datas) in mock_items.items():
      self.assertNotEqual(leaf[attr_name], None, f'not found common attr {attr_name}')
      self.assertEqual(len(leaf[attr_name]), item_list_len)
      for i in range(item_list_len):
        self.assertEqual(leaf[attr_name][i], mock_items[attr_name][i])

  def test_gsu_common_colossusv2_build_update_enricher(self):
    user_id = 666
    mock_items = {
      "photo_id": [1, 2, 3],
      "author_id": [4, 5, 6],
      "duration": [7, 8, 9],
      "play_time": [10, 11, 12],
      "hetu_tag": [13, 14, 15],
      "timestamp": [16, 17, 18],
      "photo_pxtr": [0.41401011, 0.35320508, 0.92683393, 0.19147882, 0.31912136, 0.024423093, 0.58906645, 0.60334772, 0.98029935, 0.11221036, 0.14104462, 0.035211135, 0.72105628, 0.66508436, 0.98243964, 0.059646953, 0.069921836, 0.052327637]
    }
    item_list_len = 3
    flow = GsuFlow(name="gsu_common_colossusv2_build_update_enricher")
    flow.gsu_common_colossusv2_build_update_enricher(client_kconf="colossus.kconf_client.hot_video_pxtr_item",
                    key_from="key_from",
                    schema_version_str="2024_0117_120000",
                    output_attr="output_attr",
                    debug_print_item_num=10,
                    input_fields=dict(photo_id="photo_id_from",
                                        author_id="author_id_from",
                                        duration="duration_from",
                                        play_time="play_time_from",
                                        photo_pxtr="photo_pxtr_from",
                                        hetu_tag="hetu_tag_from",
                                        timestamp="timestamp_from")) \
        .gsu_common_colossusv2_parse_update_enricher(schema_kconf="colossus.tables.hot_video_pxtr_item",
                    input_data_from="output_attr",
                    debug_print_item_num=10,
                    output_fields=dict(photo_id="photo_id",
                                        author_id="author_id",
                                        duration="duration",
                                        play_time="play_time",
                                        photo_pxtr="photo_pxtr",
                                        hetu_tag="hetu_tag",
                                        timestamp="timestamp"))

    leaf = self.__init_service(flow)
    leaf['key_from'] = 666
    for (attr_name, field_datas) in mock_items.items():
      leaf[attr_name + '_from'] = list(field_datas)
    leaf.run('gsu_common_colossusv2_build_update_enricher')
    for (attr_name, field_datas) in mock_items.items():
      if attr_name == 'photo_pxtr':
        continue
      self.assertNotEqual(leaf[attr_name], None, f'not found common attr {attr_name}')
      self.assertEqual(len(leaf[attr_name]), item_list_len, f'field: {attr_name}')
      for i in range(item_list_len):
        self.assertEqual(leaf[attr_name][i], field_datas[i], f'field: {attr_name} index: {i}')

  def test_gsu_single_edge_feature_enricher(self):
    client_kconf = 'colossus.kconf_client.video_item'
    user_id = 666
    # 多个 response 的 mock 目前不太好搞，先不 mock 了
    flow = GsuFlow(name="test_gsu_single_edge_feature_enricher")
    flow.gsu_common_colossusv2_enricher(
          kconf=client_kconf,
          item_fields=dict(photo_id="photo_id_list",
                            author_id="author_id_list",
                            duration="duration_list",
                            play_time="play_time_list",
                            tag="tag_list",
                            channel="channel_list",
                            label="label_list",
                            timestamp="timestamp_list")) \
        .gsu_single_edge_feature_enricher(
          cache_configs=dict(
              shard_num=59,
              cache_size=50000000,
              # 禁止 warmup ，避免去读取 HDFS
              enable_warmup=False),
          kconf="colossus.kconf_client.photo_info_item",
          input_keys_from="photo_id_list",
          item_fields=dict(
              timestamp="fake_timestamp",
              collection_score="collection_score_list",
              collection_type="collection_type_list",
              hetu_cate_v3="hetu_cate_v3_list")) \
        .log_debug_info(item_attrs=[
                                    "pids",
                                    "aids",
                                    "tss",
                                  ],)

    leaf = self.__init_service(flow)
    leaf.user_id = user_id
    leaf.run("test_gsu_single_edge_feature_enricher")
  
  def test_gsu_retriever_with_colossus_resp_v2(self):
    client_kconf = 'colossus.kconf_client.video_item'
    user_id = 666
    mock_items = {
      "photo_id": [1, 2, 3],
      "author_id_v2": [4, 5, 6],
      "duration": [7, 8, 9],
      "play_time": [10, 11, 12],
      "tag": [13, 14, 15],
      "channel": [16, 17, 18],
      "label": [16, 17, 18],
      "timestamp": [19, 20, 21]
    }
    item_list_len = len(next(iter(mock_items.values())))
    field_infos = self._get_colossusdb_sim_field_infos(client_kconf, mock_items)
    resp = self._serialize_colossusdb_sim_data(item_list_len, mock_items, field_infos)
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_clsdb_sim-video-2_short-video",
                                 response_json_str=json.dumps(resp))
    flow = GsuFlow(name="test_gsu_retriever_with_colossus_resp_v2")
    flow\
        .gsu_common_colossusv2_enricher(kconf=client_kconf,
                    item_fields=dict(photo_id="",
                                      author_id_v2="",
                                      duration="",
                                      play_time="",
                                      tag="",
                                      channel="",
                                      label="",
                                      timestamp=""),
                    reflection_output_attr="video_item_reflection",
                    item_datas_output_attr="video_item_colossus_data") \
        .gsu_retriever_with_colossus_resp_v2(
                        colossusv2_reflection_input_attr="video_item_reflection",
                        colossusv2_item_datas_input_attr="video_item_colossus_data",
                        save_photo_id_to_attr="pid",
                        save_author_id_to_attr="aid",
                        save_duration_to_attr="duration",
                        save_play_time_to_attr="play",
                        save_tag_to_attr="tag",
                        save_channel_to_attr="channel",
                        save_label_to_attr="label",
                        save_timestamp_to_attr="time") \
        .log_debug_info(item_attrs=[
                                    "pid",
                                    "aid",
                                    "times",
                                  ],)

    leaf = self.__init_service(flow)
    leaf.user_id = user_id
    leaf.run("test_gsu_retriever_with_colossus_resp_v2")
    for i, item in enumerate(leaf.items):
      index = item_list_len - i - 1
      self.assertEqual(item["pid"], mock_items['photo_id'][index])
      self.assertEqual(item["aid"], mock_items['author_id_v2'][index])
      self.assertEqual(item["duration"], mock_items['duration'][index])
      self.assertEqual(item["play"], mock_items['play_time'][index])
      self.assertEqual(item["tag"], mock_items['tag'][index])
      self.assertEqual(item["channel"], mock_items['channel'][index])
      self.assertEqual(item["label"], mock_items['label'][index])
      self.assertEqual(item["time"], mock_items['timestamp'][index])

  def test_gsu_retriever_with_colossus_resp(self):
    client_kconf = 'colossus.kconf_client.video_item'
    user_id = 666
    mock_items = {
      "photo_id": [1, 2, 3],
      "author_id": [4, 5, 6],
      "author_id": [7, 8, 9],
      "duration": [10, 11, 12],
      "play_time": [13, 14, 15],
      "tag": [16, 17, 18],
      "label": [19, 20, 21],
      "timestamp": [22, 23, 24]
    }
    item_list_len = len(next(iter(mock_items.values())))
    field_infos = self._get_colossusdb_sim_field_infos(client_kconf, mock_items)
    resp = self._serialize_colossusdb_sim_data(item_list_len, mock_items, field_infos)
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_clsdb_sim-video-2_short-video",
                                 response_json_str=json.dumps(resp))
    flow = GsuFlow(name="test_gsu_retriever_with_colossus_resp")
    flow\
        .gsu_common_colossusv2_enricher(kconf=client_kconf,
                                        item_fields=dict(photo_id="photo_id_list",
                                                         author_id="author_id_list",
                                                         duration="duration_list",
                                                         play_time="play_time_list",
                                                         tag="tag_list",
                                                         label="label_list",
                                                         timestamp="timestamp_list")) \
        .gsu_retriever_with_colossus_resp(from_colossus_sim_v2=True,
                        photo_id_from="photo_id_list",
                        author_id_from="author_id_list",
                        duration_from="duration_list",
                        play_time_from="play_time_list",
                        tag_from="tag_list",
                        label_from="label_list",
                        timestamp_from="timestamp_list",
                        save_photo_id_to_attr="pid",
                        save_author_id_to_attr="aid",
                        save_duration_to_attr="duration",
                        save_play_time_to_attr="play",
                        save_tag_to_attr="tag",
                        save_channel_to_attr="channel",
                        save_label_to_attr="label",
                        save_timestamp_to_attr="time") \
        .log_debug_info(item_attrs=[
                                    "pid",
                                    "aid",
                                    "times",
                                  ],)

    leaf = self.__init_service(flow)
    leaf.user_id = user_id
    leaf.run("test_gsu_retriever_with_colossus_resp")
    for i, item in enumerate(leaf.items):
      index = item_list_len - i - 1
      self.assertEqual(item["pid"], mock_items['photo_id'][index])
      self.assertEqual(item["aid"], mock_items['author_id'][index])
      self.assertEqual(item["duration"], mock_items['duration'][index])
      self.assertEqual(item["play"], mock_items['play_time'][index])
      self.assertEqual(item["tag"], mock_items['tag'][index])
      self.assertEqual(item["label"], mock_items['label'][index])
      self.assertEqual(item["time"], mock_items['timestamp'][index])

  def test_gsu_retrieve_from_colossus_v2_checkpoint(self):
    flow = GsuFlow(name="test_gsu_retrieve_from_colossus_v2_checkpoint")
    flow.gsu_retrieve_from_colossus_v2_checkpoint(
                        save_photo_id_to_attr="pid",
                        save_author_id_to_attr="aid",
                        save_duration_to_attr="duration",
                        save_play_time_to_attr="play",
                        save_tag_to_attr="tag",
                        save_label_to_attr="label",
                        save_timestamp_to_attr="time",
                        read_thread_num=8,
                        buffer_size=10000,
                        version=1,
                        loop_interval=365*24*60*60*1000) \
        .log_debug_info(
            for_debug_request_only=True,
            item_attrs=[
                "pid",
                "aid",
                "duration",
                "play",
                "tag",
                "label",
                "time"])

    # leaf = self.__init_service(flow)
    # leaf.run("test_gsu_retriever_with_colossus_resp")
    # 依赖 client 初始化和读取 HDFS 上的 checkpoint ，先不加单侧了

  def test_gsu_fetch_embedding_server_enricher(self):
    mock_items = {
        123: '1,2,3',
        456: '4,5,6',
        789: '7,8,9',
    }
    # construct ks.reco.bt_embd_s.BatchEmbeddingsResponse
    aids = []
    items = {}
    for aid, fea in mock_items.items():
      items[aid] = encode_bytes(str.encode(fea))
      aids.append(aid)
    resp = dict(
      items = items
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(
        service_name="grpc_real_seller_main_goods_cate",
        response_json_str=json.dumps(resp))
    flow = GsuFlow(name="gsu_fetch_embedding_server_enricher")
    flow\
        .gsu_fetch_embedding_server_enricher(service_name="grpc_real_seller_main_goods_cate",
                                             shard_num=1,
                                             input_attr="aid_list",
                                             output_attr="fea_list",
                                             # ut 里面这个必须设置
                                             enable_gsu_ut=True,
                                             debug_print_fea_num=10)\
    .log_debug_info(
        for_debug_request_only=False,
        common_attrs=['aid_list', 'fea_list'])
    leaf = self.__init_service(flow)
    leaf["aid_list"] = aids
    leaf.run("gsu_fetch_embedding_server_enricher")
    for (aid, fea) in mock_items.items():
      aid_str = str(aid)
      self.assertNotEqual(leaf[aid_str], None, f'not found common attr {aid_str}')
      self.assertEqual(leaf[aid_str], fea)

  def _construct_ksib_colossus_resp(self):
    user_id = 666 #150001046756106
    item_list_len = 5
    mock_items = {}
    photo_id_list = [150060391157539, 150060202001814, 150060122482247, 150060116562192, 150060391678159]
    mock_items["photo_id"] = photo_id_list
    self.assertEqual(item_list_len, len(photo_id_list))
    author_id_list = [150001130950225, 150000924067417, 150000277972475,150000953195939, 150000207300188]
    mock_items["author_id"] = author_id_list
    self.assertEqual(item_list_len, len(author_id_list))
    duration_list = [48, 8, 12, 10, 45]
    mock_items["duration"] = duration_list
    self.assertEqual(item_list_len, len(duration_list))
    play_time_list = [18, 4, 16, 9, 1]
    mock_items["play_time"] = play_time_list
    self.assertEqual(item_list_len, len(play_time_list))
    label_tag_list = [1597517, 1556560, 1597517, 1556575, 1597520]
    mock_items["label_tag"] = label_tag_list
    self.assertEqual(item_list_len, len(label_tag_list))
    channel_list = [33, 33, 33, 33, 33]
    mock_items["channel"] = channel_list
    self.assertEqual(item_list_len, len(channel_list))
    timestamp_list = [1640218285, 1640218285, 1640218247, 1640218227, 1640192105]
    mock_items["timestamp"] = timestamp_list
    self.assertEqual(item_list_len, len(timestamp_list))


    # field_name, size, python struct format
    # struct format reference: https://docs.python.org/zh-cn/3/library/struct.html

    ksib_video_item_field_and_sizes = [
      ("timestamp", 4, 'I'),
      ("photo_id", 8, 'Q'),
      ("author_id", 8, 'Q'),
      ("duration", 2, 'H'),
      ("play_time", 2, 'H'),
      ("label_tag", 4, 'I'),
      ("channel", 1, 'B')
    ]

    # construct SimItemV2 raw bytes
    item_size = 0
    for (name, size, format_str) in ksib_video_item_field_and_sizes:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in ksib_video_item_field_and_sizes:
        #print(name, i, type(i), mock_items[name])
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size
    # construct CommonItemResponse
    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    return mock_items, resp

  def test_ksib_extract_feature_from_colossus_response(self):
    mock_items, resp = self._construct_ksib_colossus_resp()
    rpc_mocker = self.__service.rpc_mocker()
    test_rpc_name = "grpc_colossusKsibVideoItem"
    rpc_mocker.mock_rpc_response(service_name=test_rpc_name, response_json_str=json.dumps(resp))
    flow = GsuFlow(name="test_colossus")
    flow.user_id = resp['item_key']
    flow.colossus(
        service_name=test_rpc_name,
        client_type='common_item_client',
        output_attr="colossus_output",
        parse_to_pb=False,
        debug_uids="666",
        print_items=True
    )

    # 需要确保这个输出正确性
    flow.ksib_extract_feature_from_colossus_response(
        colossus_output="colossus_output",
        output_signs_attr="colossus_signs",
        output_slots_attr="colossus_slots",
        output_pid_signs_attr="colossus_pid_signs",
        output_pid_slots_attr="colossus_pid_slots",
        item_slot_id=1006, # does not matter
        timediff_bias_slot_id=797,
        play_bias_slot_id=798, # slot does not matter, make sure the sign is correct
        label_bias_slot_id=799, # not used
        channel_bias_slot_id=800, # nod used
        output_mask_bias_attr="colossus_is_null",
        colossus_output_type="common_item",
    )
    leaf = self.__init_service(flow)
    leaf.run("test_colossus")

    self.assertNotEqual(leaf['colossus_slots'], None, 'not found common attr colossus_slots')
    self.assertNotEqual(leaf['colossus_signs'], None, 'not found common attr colossus_signs')

  def test_ksib_gsu_with_multi_head_index(self):
    mask24 = (1<<24) - 1
    # copy from self.mock_ksib_colossus()
    photo_id_list = [150000326772295, 150100619663871, 150100618272776, 150100067044658, 150000379167079]
    author_id_list = [150000148354736, 150000148186556, 150000148354736, 150000148354736, 150000007591566]
    duration_list = [52, 187, 11, 151, 55]
    play_time_list = [7, 155, 12, 2, 57]
    timestamp_list = [1661477079, 1661477069, 1661476908, 1661476898, 1661476898]
    label_tag_list = [925763, 67, 67, 90, 925772]
    channel_list = [33, 33, 33, 33, 33]
    label_list = [e >> 24 for e in label_tag_list]
    print("label_list", label_list)
    tag_list = [e&mask24 for e in label_tag_list]
    print("tag_list", tag_list)

    user_id = self.mock_ksib_colossus()

    flow = GsuFlow(name="test_gsu_multi")
    flow.user_id = user_id
    flow.colossus(
        service_name="grpc_colossusLongKsibVideoItemB",
        client_type='common_item_client',
        output_attr="colossus_output",
        parse_to_pb=False,
        debug_uids="666",
        print_items=True
    )

    gsu_slots_int =  [790, 791, 792, 793, 794, 798, 797, 795, 796, 799, 800]
    # 需要确保这个输出正确性
    flow.ksib_gsu_with_multi_head_index(
      colossus_output_type="common_item",
      colossus_output_attr="colossus_output",
      topn_index_attr="topk_indices",
      topn_value_attr="topk_values",
      output_item_colossus_pid_attr="item_colossus_pid",
      head_num=4,
      top_n=5,
      output_sign_attr="gsu_signs",
      output_slot_attr="gsu_slots",
      slots_id=gsu_slots_int,
      mio_slots_id=gsu_slots_int,
    )

    leaf = self.__init_service(flow)
    request_time = (1671098001 + 59) * 1000
    leaf.request_time = request_time
    leaf.user_id = user_id
    item1 = leaf.add_item(1)
    item1['topk_indices'] = [0,1,2,3] * 5
    # item1['topk_values'] = [0.0,1.0,2.0,3.0]
    leaf.run("test_gsu_multi")

    mask48 = ((1<<48) - 1)
    for item in leaf.items:
      self.assertNotEqual(item['gsu_slots'], None, 'not found common attr gsu_slots')
      self.assertNotEqual(item['gsu_signs'], None, 'not found common attr gsu_signs')
      # pid
      slots_len = len(gsu_slots_int)
      self.assertEqual(
        [e & mask48 for e in item['gsu_signs'][::slots_len]],
        photo_id_list[:4]
      )
      # aid
      aid_in_signs = [e & mask48 for e in item['gsu_signs'][1:][::slots_len]]
      self.assertEqual(aid_in_signs, author_id_list[:4])
      # tag
      self.assertEqual(
        [e & mask48 for e in item['gsu_signs'][2:][::slots_len]],
        tag_list[:4], "tags not equal"
      )
      # playtime
      self.assertEqual(
        [e & mask48 for e in item['gsu_signs'][3:][::slots_len]],
        [(p << 24) | d for p,d in zip(play_time_list, duration_list) ][:4]
      )
      # timediff
      self.assertEqual(
        [e & mask48 for e in item['gsu_signs'][4:][::slots_len]],
        [(request_time // 1000 - e) // 86400 for e in timestamp_list][:4]
      )
      # label
      self.assertEqual(
        [e & mask48 for e in item['gsu_signs'][7:][::slots_len]],
        label_list[:4]
      )
      # channel
      self.assertEqual(
        [e & mask48 for e in item['gsu_signs'][8:][::slots_len]],
        channel_list[:4]
      )

  def test_ksib_gsu_readjust_sign_enricher(self):
    # define flow
    bucket = "bucket"
    common_slots = "ksib_gsu_slots_c"
    common_signs = "ksib_gsu_signs_c"
    item_slots = "ksib_gsu_slots_i"
    item_signs = "ksib_gsu_signs_i"
    flow = GsuFlow(name="test_ksib_gsu_readjust_sign")\
        .ksib_gsu_readjust_sign(
          common=True,
          bucket_attr=bucket,
          gsu_slots_attr=common_slots,
          gsu_signs_attr=common_signs,
        ) \
        .ksib_gsu_readjust_sign(
          common=False,
          bucket_attr=bucket,
          gsu_slots_attr=item_slots,
          gsu_signs_attr=item_signs,
        )

    # mock running data
    bucket_value = 26
    input_common_slots = [1, 2, 3, 4]
    input_common_signs = [11, 692, 692, 22]
    input_item_slots = [
      [1, 2, 3, 4, 6, 89],
      [1, 2, 3, 4, 6],
      [1, 2, 3, 4, 6, 89],
      [1, 2, 3, 4, 6, 89, 77],
      [1, 2, 3, 4],
    ]
    input_item_signs = [
      [11, 692, 692, 22, 692, 33],
      [213, 34, 45, 3, 692],
      [394, 23, 692, 22, 692, 909],
      [8, 692, 692, 22, 83, 9, 692],
      [11, 0, 692, 22],
    ]

    # generate assert cases
    assert_common_signs = [11, 28587302322868, 28587302322868, 22]
    assert_item_signs = [
      [11, 28587302322868, 28587302322868, 22, 28587302322868, 33],
      [213, 34, 45, 3, 28587302322868],
      [394, 23, 28587302322868, 22, 28587302322868, 909],
      [8, 28587302322868, 28587302322868, 22, 83, 9, 28587302322868],
      [11, 0, 28587302322868, 22],
    ]

    # run test
    leaf = self.__init_service(flow)
    leaf[bucket] = bucket_value
    leaf[common_slots] = copy.deepcopy(input_common_slots)
    leaf[common_signs] = copy.deepcopy(input_common_signs)
    for i in range(len(input_item_slots)):
      item = leaf.add_item(100+i)
      item[item_slots] = copy.deepcopy(input_item_slots[i])
      item[item_signs] = copy.deepcopy(input_item_signs[i])
    leaf.run("test_ksib_gsu_readjust_sign")

    # assert
    self.assertEqual(1, 1)

    self.assertEqual(len(leaf[common_slots]), len(input_common_slots))
    for i in range(len(input_common_slots)):
      self.assertEqual(leaf[common_slots][i], input_common_slots[i])

    self.assertEqual(len(leaf[common_signs]), len(assert_common_signs))
    for i in range(len(assert_common_signs)):
      self.assertEqual(leaf[common_signs][i], assert_common_signs[i])

    for i in range(len(leaf.items)):
      item = leaf.items[i]
      self.assertEqual(len(item[item_slots]), len(input_item_slots[i]))
      for j in range(len(input_item_slots[i])):
        self.assertEqual(item[item_slots][j], input_item_slots[i][j])

      self.assertEqual(len(item[item_signs]), len(assert_item_signs[i]))
      for j in range(len(assert_item_signs[i])):
        self.assertEqual(item[item_signs][j], assert_item_signs[i][j])

  def test_ksib_marm_gsu_with_index(self):
    # define flow
    flow = GsuFlow(name="test_ksib_marm_gsu_with_index")\
        .ksib_marm_gsu_with_index(slot_input="slot_input0",
                                    sign_input="sign_input0",
                                    topk_index_attr="topk0",
                                    topk=20,
                                    output_slot_attr="slot_output0",
                                    output_sign_attr="sign_output0") \

    slot_input = [10 + i for i in range(10)]
    sign_input = [1000 + i for i in range(10)]

    item_ids = [10 + i for i in range(5)]
    item_top_k = []
    for i in range(5):
        item_top_k.append([random.randint(0, 9) for idx in range(5)])
    slot_outputs = []
    sign_outputs = []
    for idx in range(5):
        tmp_slot = []
        tmp_sign = []
        for topk_idx in item_top_k[idx]:
            tmp_slot.append(slot_input[topk_idx])
            tmp_sign.append(sign_input[topk_idx])
        slot_outputs.append(tmp_slot)
        sign_outputs.append(tmp_sign)
    # run test
    leaf = self.__init_service(flow)
    leaf["slot_input0"] = copy.deepcopy(slot_input)
    leaf["sign_input0"] = copy.deepcopy(sign_input)
    for idx,e in enumerate(item_ids):
      item = leaf.add_item(e)
      item["item_id"] = e
      item["topk0"] = copy.deepcopy(item_top_k[idx])
    leaf.run("test_ksib_marm_gsu_with_index")

    # assert
    self.assertEqual(1, 1)

    # item attr
    for i in range(len(leaf.items)):
      item = leaf.items[i]
      self.assertEqual(item["slot_output0"], slot_outputs[i])
      self.assertEqual(item["sign_output0"], sign_outputs[i])
  
  def test_ksib_slot_sign_trans(self):
    # define flow
    flow = GsuFlow(name="test_ksib_slot_sign_trans")\
        .ksib_slot_sign_trans(slot_input="slot_input0",
                                    sign_input="sign_input0",
                                    slot_as_attr_name=True,
                                    is_common=True,
                                    slots=["690", "691"]) \

    slot_input = [690, 691, 690, 691]
    sign_input = [1, 2, 3, 4]
    
    res = {}
    res["690"] = [1, 3]
    res["691"] = [2, 4]
    # run test
    leaf = self.__init_service(flow)
    leaf["slot_input0"] = copy.deepcopy(slot_input)
    leaf["sign_input0"] = copy.deepcopy(sign_input)
    leaf.run("test_ksib_slot_sign_trans")

    # assert
    self.assertEqual(1, 1)
    
    self.assertEqual(leaf["690"], res["690"])
    self.assertEqual(leaf["691"], res["691"])

  def test_marm_colossus_join(self):
    # define flow
    flow = GsuFlow(name="test_marm_colossus_join")\
        .marm_colossus_join( pid_attr = "pid_list",
                     join_pid_attr = "colossus_pids",
                     output_pid_attr = "output_pid_list",
                     pid_bits = 24
                    )
    
    colossus_pids = [1234567, 1234568, 1234569, 1234560]
    pid_list = [1234567, 1234568, 12311111, 1234560] 
    output_pid_attr = [1234567, 1234568, 0, 1234560]
    
    # run test
    leaf = self.__init_service(flow)
    leaf["colossus_pids"] = copy.deepcopy(colossus_pids)
    leaf["pid_list"] = copy.deepcopy(pid_list)
    leaf.run("test_marm_colossus_join")

    # assert
    self.assertEqual(1, 1)
    
    self.assertEqual(leaf["output_pid_list"], output_pid_attr)

  def test_generate_slot_sign_for_ids(self):
    # define flow
    flow = GsuFlow(name="test_generate_slot_sign_for_ids")\
        .generate_slot_sign_for_ids(
          bucket_attr="bucket",
          from_common_attr="gsu_items",
          item_sign_type="PID_48BIT_DEFAULT",
          output_signs_attr="gsu_item_signs",
          output_slots_attr= [
            dict(attr_name="fake_slot1", slot_id=123),
            dict(attr_name="fake_slot2", slot_id=343),
          ],
        )\
        .generate_slot_sign_for_ids(
          from_common_attr="cluster_id",
          slot_id_in_sign=555,
          slot_high_bits=16,
          item_sign_type="SLOT_SIGN",
          output_signs_attr="cluster_signs",
          output_slots_attr= [
            dict(attr_name="cluster_slots", slot_id=123),
          ],
        )\
        .generate_slot_sign_for_ids(
          item_sign_type="PID_48BIT_DEFAULT",
          output_signs_attr="gsu_item_signs",
          output_slots_attr= [
            dict(attr_name="fake_slot1", slot_id=123),
            dict(attr_name="fake_slot2", slot_id=343),
          ],
        )\
        .generate_slot_sign_for_ids(
          item_sign_type="SLOT_SIGN",
          from_item_attr="item_id",
          slot_id_in_sign=555,
          slot_high_bits=16,
          output_signs_attr="icluster_signs",
          output_slots_attr= [
            dict(attr_name="icluster_slots1", slot_id=123),
            dict(attr_name="icluster_slots2", slot_id=343),
          ],
        )\

    # mock running data
    item_ids = [10 + i for i in range(5)]

    # generate assert cases
    assert_slots1 = [123 for i in range(5)]
    assert_slots2 = [343 for i in range(5)]
    cluster_signs = [(555 << 48) | e for e in item_ids]
    assert_signs = copy.deepcopy(item_ids)

    # run test
    leaf = self.__init_service(flow)
    leaf["gsu_items"] = copy.deepcopy(item_ids)
    leaf["cluster_id"] = copy.deepcopy(item_ids)
    for e in item_ids:
      item = leaf.add_item(e)
      item["item_id"] = e
    leaf.run("test_generate_slot_sign_for_ids")

    # assert
    self.assertEqual(1, 1)

    # common attr
    for i in range(len(assert_slots1)):
      self.assertEqual(leaf["fake_slot1"][i], assert_slots1[i])
      self.assertEqual(leaf["fake_slot2"][i], assert_slots2[i])
      self.assertEqual(leaf["gsu_item_signs"][i], assert_signs[i])

      self.assertEqual(leaf["cluster_slots"][i], assert_slots1[i])
      self.assertEqual(leaf["cluster_signs"][i], cluster_signs[i])

    # item attr
    for i in range(len(leaf.items)):
      item = leaf.items[i]
      self.assertEqual(len(item["fake_slot1"]), 1)
      self.assertEqual(len(item["fake_slot2"]), 1)
      self.assertEqual(len(item["gsu_item_signs"]), 1)
      self.assertEqual(item["fake_slot1"][0], assert_slots1[i])
      self.assertEqual(item["fake_slot2"][0], assert_slots2[i])
      self.assertEqual(item["gsu_item_signs"][0], assert_signs[i])

      self.assertEqual(len(item["icluster_slots1"]), 1)
      self.assertEqual(len(item["icluster_slots2"]), 1)
      self.assertEqual(len(item["icluster_signs"]), 1)
      self.assertEqual(item["icluster_slots1"][0], assert_slots1[i])
      self.assertEqual(item["icluster_slots2"][0], assert_slots2[i])
      self.assertEqual(item["icluster_signs"][0], cluster_signs[i])

  def _gsu_with_cluster_uniq_topk(self, filter_timestamp, item_clusters, colossus_items, cluster_neighbors, topk_limit):
    target_topk = list()  # list of list
    clusters_topk = dict()
    clusters_colos_item = dict()
    invalid_topk = -1

    self.assertEqual(colossus_items.check_format(), True)
    scanned_items = set([])
    for i in range(colossus_items.item_num-1, -1, -1):
      item_id = colossus_items.items["photo_id"][i]
      item_ts = colossus_items.items["timestamp"][i]
      if item_ts > filter_timestamp or item_id in scanned_items:
        # print(f"UT_EXP timestamp filter: {item_ts} vs {filter_timestamp}, item[{i}]: {item_id}")
        continue
      scanned_items.add(item_id)
      cluster_id = colossus_items.items["cluster_id"][i]
      if cluster_id in clusters_colos_item:
        clusters_colos_item[cluster_id].append(i)
      else:
        clusters_colos_item[cluster_id] = [i]
    # for key, value in clusters_colos_item.items(): print(f"UT_EXP1 cluster[{key}] - items({len(value)}): ", [colossus_items.items["photo_id"][i] for i in value], file=sys.stderr)

    for cluster_id, neighbors in cluster_neighbors.items():
      cls_topk = list()
      for i in range(len(neighbors)-1, -1, -1):
        if len(cls_topk) >= topk_limit: break
        cls_topk = cls_topk + clusters_colos_item.get(neighbors[i], list())
      if len(cls_topk) > topk_limit: cls_topk = cls_topk[:topk_limit]
      for i in range(len(cls_topk), topk_limit): cls_topk.append(invalid_topk)

      clusters_topk[cluster_id] = cls_topk
      # print(f"UT_EXP2 cluster[{cluster_id}] - topk({len(cls_topk)}): ", [colossus_items.items["photo_id"][i] for i in cls_topk], file=sys.stderr)

    for cluster_id in item_clusters:
      topk = clusters_topk.get(cluster_id, [invalid_topk for i in range(topk_limit)])
      target_topk.append(topk)
      self.assertEqual(len(topk), topk_limit)

    return target_topk


  def _gsu_with_cluster_uniq_restore_topk_cluster_uniq(self, target_topk_index, uniq_topk_items, topk_limit):
    print(f"cluster_uniq, target num: {len(target_topk_index)}, uniq item num: {len(uniq_topk_items)}", file=sys.stderr)
    self.assertEqual(len(uniq_topk_items) % topk_limit == 0, True)
    for i in range(topk_limit): self.assertEqual(uniq_topk_items[i], -1)

    target_topk = list()
    max_index = len(uniq_topk_items) // topk_limit
    for offset in target_topk_index:
      self.assertEqual(0 <= offset and offset < max_index, True)
      target_topk.append(uniq_topk_items[offset*topk_limit : (offset+1)*topk_limit])
    return target_topk

  def _gsu_with_cluster_uniq_restore_topk_all_uniq(self, target_topk_index, uniq_topk_items, topk_limit):
    print(f"all_uni, target num: {len(target_topk_index)}, uniq item num: {len(uniq_topk_items)}", file=sys.stderr)
    self.assertEqual(uniq_topk_items[0], -1)
    target_topk = list()
    for topk_index in target_topk_index:
      self.assertEqual(len(topk_index),  topk_limit)
      topk = list()
      for idx in topk_index:
        self.assertEqual(0<= idx and idx < len(uniq_topk_items), True)
        topk.append(uniq_topk_items[idx])
      target_topk.append(topk)
    return target_topk

  def _gsu_with_cluster_uniq_assert_result(self, result, expect, item_clusters, loose=False):
    print(f"result size: {len(result)}, expect size: {len(expect)}, loose: {loose}")
    self.assertEqual(len(result), len(expect))
    self.assertEqual(len(result), len(item_clusters))

    invalid_topk = -1
    for i in range(len(result)):
      succ = True
      if len(result[i]) != len(expect[i]):
        succ = False
      else:
        for j in range(len(result[i])):
          if loose:
            succ = result[i][j]==expect[i][j] or result[i][j]==invalid_topk
          else:
            succ = result[i][j] == expect[i][j]
          if not succ: break
      if not succ:
        print(f"assert result[{i}] - cluster[{item_clusters[i]}] - topk({len(result[i])}): ", result[i], file=sys.stderr)
        print(f"assert expect[{i}] - cluster[{item_clusters[i]}] - topk({len(expect[i])}): ", expect[i], file=sys.stderr)
      self.assertEqual(succ, True)

  def test_gsu_with_cluster_uniq(self):
    # mock running data
    colossus_items = ColossusSimItemList(user_id=666,
                                         schema= [("photo_id", 8, 'Q'),
                                                  ("author_id", 4, 'I'),
                                                  ("timestamp", 4, 'I'),
                                                  ("duration", 2, 'H'),
                                                  ("play_time", 2, 'H'),
                                                  ("cluster_id", 2, 'H'),
                                                  ],
                                         )
    cluster_num = 700
    colossus_item_num = 20000
    cur_timestamp = int(time.time() - 60)  # second timestamp

    for i in range(colossus_item_num):
      colossus_items.add_item(dict(
        photo_id=random.randint(100000, 1000000000),
        author_id=random.randint(1000, 100000000),
        timestamp=random.randint(cur_timestamp-100*86400, cur_timestamp),
        duration=random.randint(10, 1000),
        play_time=random.randint(1, 1000),
        cluster_id=random.randint(0, 999),
      ))

    mock_colossus_service = "grpc_colossusLongSimItem"
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name=mock_colossus_service, response_json_str=json.dumps(colossus_items.serialize()))

    # define flow
    flow = GsuFlow(name="test_gsu_with_cluster_uniq")\
        .colossus(
          service_name=mock_colossus_service,
          client_type='common_item_client',
          output_attr="sim_v2_colossus_output",
          parse_to_pb=False,
          debug_uids="666",
          print_items=False,
        )\
        .gsu_with_cluster_uniq(
          kess_service="kws-kuaishou-full-rank-embedding-mmu-hetu-cluster-id-long",
          shards=8,
          timeout_ms=10,
          limit_num=50,
          max_uniq_item_num=colossus_item_num,
          max_uniq_cluster_num=cluster_num,
          colossus_resp_attr="sim_v2_colossus_output",
          colossus_item_cluster_id_field_name="cluster_id",
          target_item_cluster_id_attr="icluster_id",
          target_topk_index_attr="photo_cluster_idx1",
          uniq_topk_items_attr="uniq_colossus_million_topk_items1",
          uniq_type="cluster_uniq",
          colossus_reflect_schema_type=mock_colossus_service,
          local_cluster_neighbors=cluster_neighbors,
        )\
        .gsu_with_cluster_uniq(
          kess_service="kws-kuaishou-full-rank-embedding-mmu-hetu-cluster-id-long",
          shards=8,
          timeout_ms=10,
          limit_num=50,
          max_uniq_item_num=colossus_item_num,
          max_uniq_cluster_num=cluster_num,
          colossus_resp_attr="sim_v2_colossus_output",
          colossus_item_cluster_id_field_name="cluster_id",
          target_item_cluster_id_attr="icluster_id",
          target_topk_index_attr="photo_topk_idx2",
          uniq_topk_items_attr="uniq_colossus_million_topk_items2",
          uniq_topk_items_cluster_attr="uniq_colossus_million_topk_items2_cluster",
          uniq_type="all_uniq",
          colossus_reflect_schema_type=mock_colossus_service,
          local_cluster_neighbors=cluster_neighbors,
        )\
        .gsu_with_cluster_uniq(
          kess_service="kws-kuaishou-full-rank-embedding-mmu-hetu-cluster-id-long",
          shards=8,
          timeout_ms=10,
          limit_num=50,
          max_uniq_item_num=colossus_item_num//2,
          max_uniq_cluster_num=cluster_num//2,
          colossus_resp_attr="sim_v2_colossus_output",
          colossus_item_cluster_id_field_name="cluster_id",
          target_item_cluster_id_attr="icluster_id",
          target_topk_index_attr="photo_cluster_idx3",
          uniq_topk_items_attr="uniq_colossus_million_topk_items3",
          uniq_type="cluster_uniq",
          colossus_reflect_schema_type=mock_colossus_service,
          local_cluster_neighbors=cluster_neighbors,
        )\
        .gsu_with_cluster_uniq(
          kess_service="kws-kuaishou-full-rank-embedding-mmu-hetu-cluster-id-long",
          shards=8,
          timeout_ms=10,
          limit_num=50,
          max_uniq_item_num=colossus_item_num//2,
          max_uniq_cluster_num=cluster_num//2,
          colossus_resp_attr="sim_v2_colossus_output",
          colossus_item_cluster_id_field_name="cluster_id",
          target_item_cluster_id_attr="icluster_id",
          target_topk_index_attr="photo_topk_idx4",
          uniq_topk_items_attr="uniq_colossus_million_topk_items4",
          uniq_type="all_uniq",
          colossus_reflect_schema_type=mock_colossus_service,
          local_cluster_neighbors=cluster_neighbors,
        )\

    # run test
    # -- mock leaf context --
    leaf = self.__init_service(flow)
    leaf.request_time = int(time.time() * 1000)
    for i in range(1000):
      item = leaf.add_item(i+1)
      item["icluster_id"] = [random.randint(0, cluster_num-1)]

    leaf.run("test_gsu_with_cluster_uniq")

    # assert
    item_clusters = [item["icluster_id"][0] for item in leaf.items]
    item_ids = [item.item_id for item in leaf.items]
    expect_topk = self._gsu_with_cluster_uniq_topk(leaf.request_time/1000 - 60, item_clusters, colossus_items, cluster_neighbors, 50)

    # photo_cluster_idx1
    print(f"assert phase1 ...", file=sys.stderr)
    target_topk_index = [int(item["photo_cluster_idx1"][0]) for item in leaf.items]
    uniq_topk_items = leaf["uniq_colossus_million_topk_items1"]
    target_topk = self._gsu_with_cluster_uniq_restore_topk_cluster_uniq(target_topk_index, uniq_topk_items, 50)
    self._gsu_with_cluster_uniq_assert_result(target_topk, expect_topk, item_clusters)

    # photo_topk_idx2
    print(f"assert phase2 ...", file=sys.stderr)
    target_topk_index = [[int(e) for e in item["photo_topk_idx2"]] for item in leaf.items]
    uniq_topk_items = leaf["uniq_colossus_million_topk_items2"]
    target_topk = self._gsu_with_cluster_uniq_restore_topk_all_uniq(target_topk_index, uniq_topk_items, 50)
    self._gsu_with_cluster_uniq_assert_result(target_topk, expect_topk, item_clusters)
    uniq_topk_items_cluster = leaf["uniq_colossus_million_topk_items2_cluster"]
    self.assertEqual(len(uniq_topk_items_cluster), len(uniq_topk_items))

    # photo_cluster_idx3
    print(f"assert phase3 ...", file=sys.stderr)
    target_topk_index = [int(item["photo_cluster_idx3"][0]) for item in leaf.items]
    uniq_topk_items = leaf["uniq_colossus_million_topk_items3"]
    target_topk = self._gsu_with_cluster_uniq_restore_topk_cluster_uniq(target_topk_index, uniq_topk_items, 50)
    self._gsu_with_cluster_uniq_assert_result(target_topk, expect_topk, item_clusters, True)

    # photo_topk_idx4
    print(f"assert phase4 ...", file=sys.stderr)
    target_topk_index = [[int(e) for e in item["photo_topk_idx4"]] for item in leaf.items]
    uniq_topk_items = leaf["uniq_colossus_million_topk_items4"]
    target_topk = self._gsu_with_cluster_uniq_restore_topk_all_uniq(target_topk_index, uniq_topk_items, 50)
    self._gsu_with_cluster_uniq_assert_result(target_topk, expect_topk, item_clusters, True)

  def test_general_extract_feature_from_colossus_response(self):
    # 呃，构造 colossus response 太复杂了，先略过本 test
    # define flow
    flow = GsuFlow(name="test_general_extract_feature_from_colossus_response")\
        .general_extract_feature_from_colossus_response(
          bucket_attr="photo_bucket",
          colossus_output="colossus_output",
          colossus_output_type="ksib_colossus_batch_item",
          output_signs_attr="colossus_signs",
          output_slots_attr="colossus_slots",
          output_colossus_item_key_attr="gsu_items",
          output_mask_bias_attr="gsu_item_mask",
          filter_type="FILTER_PLAYTIME",
          extract_config = [
            dict(name="ksib_pid", slot_id_in_sign=123, slot_id_in_model=690),
            dict(name="ksib_aid", slot_id_in_sign=122, slot_id_in_model=691),
            dict(name="ksib_tag", slot_id_in_sign=692),
            dict(name="play_time", slot_id_in_sign=694),
            dict(name="time_diff", slot_id_in_sign=693),
          ],
          )

    # mock running data

    # run test
    leaf = self.__init_service(flow)
    leaf["nonsense"] = 1
    leaf.run("test_general_extract_feature_from_colossus_response")

    # assert
    self.assertEqual(leaf["nonsense"], 1)

  def test_gsu_with_multi_tag(self):
    user_id = 666
    item_list_len = 10
    mock_items = {}
    photo_id_list = range(item_list_len)
    mock_items["photo_id"] = photo_id_list
    author_id_list = range(10, item_list_len*10+10, 10)
    mock_items["author_id"] = author_id_list
    self.assertEqual(item_list_len, len(author_id_list))
    duration_list = range(100, item_list_len*100+100, 100)
    mock_items["duration"] = duration_list
    self.assertEqual(item_list_len, len(duration_list))
    play_time_list = [x for x in np.random.randint(120, size=item_list_len)]
    mock_items["play_time"] = play_time_list
    self.assertEqual(item_list_len, len(play_time_list))
    tag_list = [1838, 1112, 224, 1048, 9, 16, 28, 126, 224, 7]
    mock_items["tag"] = tag_list
    self.assertEqual(item_list_len, len(tag_list))
    timestamp_list = [1650000000 + x for x in np.random.randint(90000, size=item_list_len)]
    mock_items["timestamp"] = timestamp_list
    # field_name, size, python struct format
    # struct format reference: https://docs.python.org/zh-cn/3/library/struct.html
    sim_item_field_and_sizes = [
      ("photo_id", 8, 'Q'),
      ("author_id", 4, 'I'),
      ("duration", 2, 'H'),
      ("play_time", 2, 'H'),
      ("tag", 4, 'I'),
      ("timestamp", 4, 'I'),
    ]
    # construct SimItemV2 raw bytes
    item_size = 0
    for (name, size, format_str) in sim_item_field_and_sizes:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in sim_item_field_and_sizes:
        #print(name, i, type(i), mock_items[name])
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size
    # construct CommonItemResponse
    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpcColossus2", response_json_str=json.dumps(resp))
    flow = GsuFlow(name="test_gsu_with_multi_tag")\
            .colossus(
              service_name='grpcColossus2',
              shard_number=60,
              input_attr = 'uid',
              output_attr='colossus_output',
              print_items = True,
            ).gsu_with_multi_tag(
              colossus_resp_attr = "colossus_output",
              output_sign_attr = "tag_gsu_sign",
              output_slot_attr = "tag_gsu_slot",
              sign_ids_list = [1366, 1367, 1368, 1369, 1370],
              slot_ids_list = [1366, 1367, 1368, 1369, 1370],
              target_tag_attr = "hetu_highest_value",
              additional_target_tag_attr = "hetu_add_values",
              limit_num = 50,
              shuffle = True,
              keep_effective = True,
            )
    item_num = 10
    leaf = self.__init_service(flow)
    leaf.request_time = int(time.time() * 1000)
    leaf["uid"] = user_id
    for i in range(item_num):
      item = leaf.add_item(i)
      item["type"] = 1 if i < 5 else 2
      if (i == 0):
        item["hetu_highest_value"] = 1838
        item["hetu_add_values"] = [327,10]
      elif (i == 1):
        item["hetu_highest_value"] = 1838
        item["hetu_add_values"] = [224,1048,6]
      elif (i == 5):
        item["hetu_highest_value"] = 1112
        item["hetu_add_values"] = [410,9,119,232]
      else:
        item["hetu_highest_value"] = 318 + i*17
        item["hetu_add_values"] = [100 + i*6, i*28+7, i*36 +10]
    leaf.run("test_gsu_with_multi_tag")

    self.assertEqual(item_num, len(leaf.items))
    for i in range(len(leaf.items)):
      item = leaf.items[i]
      if (item["tag_gsu_sign"] is not None):
        self.assertEqual(len(item["tag_gsu_sign"]), len(item["tag_gsu_slot"]))

  def test_gsu_with_multi_tag_v2(self):
    user_id = 666
    item_list_len = 10
    mock_items = {}
    photo_id_list = range(item_list_len)
    mock_items["photo_id"] = photo_id_list
    author_id_list = range(10, item_list_len*10+10, 10)
    mock_items["author_id"] = author_id_list
    self.assertEqual(item_list_len, len(author_id_list))
    duration_list = range(100, item_list_len*100+100, 100)
    mock_items["duration"] = duration_list
    self.assertEqual(item_list_len, len(duration_list))
    play_time_list = [x for x in np.random.randint(120, size=item_list_len)]
    mock_items["play_time"] = play_time_list
    self.assertEqual(item_list_len, len(play_time_list))
    tag_list = [1838, 1112, 224, 1048, 9, 16, 28, 126, 224, 7]
    mock_items["tag"] = tag_list
    self.assertEqual(item_list_len, len(tag_list))
    timestamp_list = [1650000000 + x for x in np.random.randint(90000, size=item_list_len)]
    mock_items["timestamp"] = timestamp_list
    mock_items["label"] = [11, 12, 13, 14, 15, 16, 17, 18, 19, 20]
    mock_items["channel"] = [33] * 10
    mock_items["user_latitude"] = [0.0] * 10
    mock_items["user_longitude"] = [0.0] * 10
    mock_items["photo_latitude"] = [0.0] * 10
    mock_items["photo_longitude"] = [0.0] * 10
    # field_name, size, python struct format
    # struct format reference: https://docs.python.org/zh-cn/3/library/struct.html

    sim_item_field_and_sizes = [
      ("photo_id", 8, "Q"),
      ("author_id", 4, "I"),
      ("duration", 2, "H"),
      ("play_time", 2, "H"),
      ("tag", 4, "I"),
      ("channel", 1, "B"),
      ("label", 2, "H"),
      ("timestamp", 4, "I"),
      ("user_latitude", 4, "f"),
      ("user_longitude", 4, "f"),
      ("photo_latitude", 4, "f"),
      ("photo_longitude", 4, "f"),
    ]

    # construct SimItemV2 raw bytes
    item_size = 0
    for (name, size, format_str) in sim_item_field_and_sizes:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in sim_item_field_and_sizes:
        #print(name, i, type(i), mock_items[name])
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size
    # construct CommonItemResponse
    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_colossusSimV2", response_json_str=json.dumps(resp))
    flow = GsuFlow(name="test_gsu_with_multi_tag_v2")\
            .colossus(
              service_name='grpc_colossusSimV2',
              client_type='common_item_client',
              parse_to_pb=False,
              debug_uids="666",
              output_attr='colossus_output',
              print_items = True,
            ).gsu_with_multi_tag_v2(
              colossus_resp_attr = "colossus_output",
              output_sign_attr = "tag_gsu_sign",
              output_slot_attr = "tag_gsu_slot",
              sign_ids_list = [1366, 1367, 1368, 1369, 1370],
              slot_ids_list = [1366, 1367, 1368, 1369, 1370],
              padding_size_attr = "padding_size",
              target_tag_attr = "hetu_highest_value",
              additional_target_tag_attr = "hetu_add_values",
              limit_num = 50,
              shuffle = True,
              keep_effective = True,
            )
    item_num = 10
    leaf = self.__init_service(flow)
    leaf.request_time = int(time.time() * 1000)
    leaf["uid"] = user_id
    for i in range(item_num):
      item = leaf.add_item(i)
      item["type"] = 1 if i < 5 else 2
      if (i == 0):
        item["hetu_highest_value"] = 1838
        item["hetu_add_values"] = [327,10]
      elif (i == 1):
        item["hetu_highest_value"] = 1838
        item["hetu_add_values"] = [224,1048,6]
      elif (i == 5):
        item["hetu_highest_value"] = 1112
        item["hetu_add_values"] = [410,9,119,232]
      else:
        item["hetu_highest_value"] = 318 + i*17
        item["hetu_add_values"] = [100 + i*6, i*28+7, i*36 +10]
    leaf.run("test_gsu_with_multi_tag_v2")

    self.assertEqual(item_num, len(leaf.items))
    for i in range(len(leaf.items)):
      item = leaf.items[i]
      if (item["tag_gsu_sign"] is not None):
        self.assertEqual(len(item["tag_gsu_sign"]), len(item["tag_gsu_slot"]))

  def test_extract_feature_from_colossus_response_profile_comment(self):
    user_id = 666
    item_list_len = 5
    mock_items = {}
    photo_id_list = [1, 2, 3, 4, 5]
    mock_items["photo_id"] = photo_id_list
    self.assertEqual(item_list_len, len(photo_id_list))
    author_id_list = [10, 20, 30, 40, 50]
    mock_items["author_id"] = author_id_list
    self.assertEqual(item_list_len, len(author_id_list))
    duration_list = [100, 200, 300, 400, 500]
    mock_items["duration"] = duration_list
    self.assertEqual(item_list_len, len(duration_list))
    play_time_list = [10, 20, 30, 40, 50]
    mock_items["play_time"] = play_time_list
    self.assertEqual(item_list_len, len(play_time_list))
    tag_list = [21, 22, 23, 24, 25]
    mock_items["tag"] = tag_list
    self.assertEqual(item_list_len, len(tag_list))
    channel_list = [5, 4, 3, 2, 1]
    mock_items["channel"] = channel_list
    self.assertEqual(item_list_len, len(channel_list))
    label_list = [11, 12, 13, 14, 15]
    mock_items["label"] = label_list
    self.assertEqual(item_list_len, len(label_list))
    timestamp_list = [1650088495 - 24 * 3600, 1650088955, 1650089335, 1650089583, 1650089641]
    mock_items["timestamp"] = timestamp_list
    self.assertEqual(item_list_len, len(timestamp_list))
    profile_list = [31, 32, 33, 34, 35]
    mock_items["profile_stay_time"] = profile_list
    self.assertEqual(item_list_len, len(profile_list))
    comment_list = [41, 42, 43, 44, 45]
    mock_items["comment_stay_time"] = comment_list
    self.assertEqual(item_list_len, len(comment_list))
    profile_feed_mode_stay_time_list = [51, 52, 53, 54, 55]
    mock_items["profile_feed_mode_stay_time"] = profile_feed_mode_stay_time_list
    self.assertEqual(item_list_len, len(profile_feed_mode_stay_time_list))
    real_show_index_list = [61, 62, 63, 64, 65]
    mock_items["real_show_index"] = real_show_index_list
    self.assertEqual(item_list_len, len(real_show_index_list))
    # field_name, size, python struct format
    # struct format reference: https://docs.python.org/zh-cn/3/library/struct.html
    reco_sim_item_v3_field_and_size  = [
      ("photo_id", 8, "Q"),
      ("author_id", 4, "I"),
      ("duration", 2, "H"),
      ("play_time", 2, "H"),
      ("tag", 4, "I"),
      ("channel", 1, "B"),
      ("label", 2, "H"),
      ("timestamp", 4, "I"),
      ("profile_stay_time", 2, "H"),
      ("comment_stay_time", 2, "H"),
      ("profile_feed_mode_stay_time", 2, "H"),
      ("real_show_index", 4, "I"),
  ]
    # construct SimItemV2 raw bytes
    item_size = 0
    for (name, size, format_str) in reco_sim_item_v3_field_and_size:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in reco_sim_item_v3_field_and_size:
        #print(name, i, type(i), mock_items[name])
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size
    # construct CommonItemResponse
    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_colossusRecoSimItemV3", response_json_str=json.dumps(resp))
    print(json.dumps(resp))
    flow = GsuFlow(name="extract_feature_from_colossus_response_profile_comment")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusRecoSimItemV3',
        client_type='common_item_client',
        output_attr="sim_v3_colossus_output",
        parse_to_pb=False,
        debug_uids="666",
        print_items=True) \
      .log_debug_info(
          common_attrs=["sim_v3_colossus_output"],
          for_debug_request_only=False,
      ) \
      .extract_feature_from_colossus_response(
        colossus_service_name="grpc_colossusRecoSimItemV3",
        colossus_output="sim_v3_colossus_output",
        output_signs_attr="colossus_signs",
        output_slots_attr="colossus_slots",
        output_pid_signs_attr="colossus_pid_signs",
        output_pid_slots_attr="colossus_pid_slots",
        item_slot_id=1006,
        timediff_bias_slot_id=345,
        play_bias_slot_id=344,
        label_bias_slot_id=342,
        channel_bias_slot_id=343,
        profile_stay_time_bias_slot_id=341,
        comment_stay_time_bias_slot_id=340,
        colossus_profile_stay_time_field_name="profile_stay_time",
        colossus_comment_stay_time_field_name="comment_stay_time",
        #colossus_item_num_attr="item_num",
        output_mask_bias_attr="colossus_is_null",
        colossus_output_type="common_item") \

    leaf = self.__init_service(flow)
    leaf.request_time = (1650089641 + 59) * 1000
    leaf.run("extract_feature_from_colossus_response_profile_comment")

    # 目前跑不过了，经常阻塞 kdev 代码提交，先注释掉了
    return
    self.assertEqual(leaf["colossus_signs"], [(329 << 48) | 1,
                                              (328 << 48) | (10 << 24) | 100,
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | 5,
                                              (325 << 48) | 31,
                                              (324 << 48) | 41,
                                              (329 << 48) | 0,
                                              (328 << 48) | (20 << 24) | 200,
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | 4,
                                              (325 << 48) | 32,
                                              (324 << 48) | 42,
                                              (329 << 48) | 0,
                                              (328 << 48) | (30 << 24) | 300,
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | 3,
                                              (325 << 48) | 33,
                                              (324 << 48) | 43,
                                              (329 << 48) | 0,
                                              (328 << 48) | (40 << 24) | 400,
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | 2,
                                              (325 << 48) | 34,
                                              (324 << 48) | 44,
                                              (329 << 48) | ((1 << 48) - 1),
                                              (328 << 48) | ((1 << 48) - 1),
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | ((1 << 48) - 1),
                                              (325 << 48) | ((1 << 48) - 1),
                                              (324 << 48) | ((1 << 48) - 1),
                                              ])

    self.assertEqual(leaf["colossus_slots"], [345, 344, 342, 343, 341, 340] * 5)
    self.assertEqual(leaf["colossus_pid_signs"], [1, 2, 3, 4, 5])
    self.assertEqual(leaf["colossus_pid_slots"], [1006] * 5)
    self.assertEqual(leaf["colossus_is_null"], [0.0] * 4 + [-1000.0] * 9996)

  def test_gsu_with_multi_head_index_profile_comment(self):
    user_id = 666
    item_list_len = 10
    mock_items = {}
    photo_id_list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    mock_items["photo_id"] = photo_id_list
    self.assertEqual(item_list_len, len(photo_id_list))
    author_id_list = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
    mock_items["author_id"] = author_id_list
    self.assertEqual(item_list_len, len(author_id_list))
    duration_list = [100, 200, 300, 400, 500, 600, 700, 800, 900, 1000]
    mock_items["duration"] = duration_list
    self.assertEqual(item_list_len, len(duration_list))
    play_time_list = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
    mock_items["play_time"] = play_time_list
    self.assertEqual(item_list_len, len(play_time_list))
    tag_list = [21, 22, 23, 24, 25, 26, 27, 28, 29, 30]
    mock_items["tag"] = tag_list
    self.assertEqual(item_list_len, len(tag_list))
    channel_list = [10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
    mock_items["channel"] = channel_list
    self.assertEqual(item_list_len, len(channel_list))
    label_list = [11, 12, 13, 14, 15, 16, 17, 18, 19, 20]
    mock_items["label"] = label_list
    self.assertEqual(item_list_len, len(label_list))
    timestamp_list = [1650088495 - 24 * 3600, 1650088955, 1650089335, 1650089583, 1650089641, 1650089741, 1650089872, 1650090001, 1650092001, 1650098001]
    mock_items["timestamp"] = timestamp_list
    self.assertEqual(item_list_len, len(timestamp_list))
    profile_list = [31, 32, 33, 34, 35, 36, 37, 38, 39, 40]
    mock_items["profile_stay_time"] = profile_list
    self.assertEqual(item_list_len, len(profile_list))
    comment_list = [41, 42, 43, 44, 45, 46, 47, 48, 49, 50]
    mock_items["comment_stay_time"] = comment_list
    self.assertEqual(item_list_len, len(comment_list))
    profile_feed_mode_stay_time_list = [51, 52, 53, 54, 55, 56, 57, 58, 59, 60]
    mock_items["profile_feed_mode_stay_time"] = profile_feed_mode_stay_time_list
    self.assertEqual(item_list_len, len(profile_feed_mode_stay_time_list))
    real_show_index_list = [61, 62, 63, 64, 65, 66, 67, 68, 69, 70]
    mock_items["real_show_index"] = real_show_index_list
    self.assertEqual(item_list_len, len(real_show_index_list))
    # field_name, size, python struct format
    # struct format reference: https://docs.python.org/zh-cn/3/library/struct.html
    reco_sim_item_v3_field_and_size  = [
      ("photo_id", 8, "Q"),
      ("author_id", 4, "I"),
      ("duration", 2, "H"),
      ("play_time", 2, "H"),
      ("tag", 4, "I"),
      ("channel", 1, "B"),
      ("label", 2, "H"),
      ("timestamp", 4, "I"),
      ("profile_stay_time", 2, "H"),
      ("comment_stay_time", 2, "H"),
      ("profile_feed_mode_stay_time", 2, "H"),
      ("real_show_index", 4, "I"),
    ]
    # construct SimItemV2 raw bytes
    item_size = 0
    for (name, size, format_str) in reco_sim_item_v3_field_and_size:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in reco_sim_item_v3_field_and_size:
        #print(name, i, type(i), mock_items[name])
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size
    # construct CommonItemResponse
    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_colossusRecoSimItemV3", response_json_str=json.dumps(resp))
    flow = GsuFlow(name="test_gsu_with_multi_head_index_profile_comment")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusRecoSimItemV3',
        client_type='common_item_client',
        output_attr="sim_v3_colossus_output",
        parse_to_pb=False,
        debug_uids="666",
        print_items=True) \
      .log_debug_info(
          common_attrs=["sim_v3_colossus_output"],
          for_debug_request_only=False,
      ) \
      .gsu_with_multi_head_index(colossus_service_name="grpc_colossusRecoSimItemV3",
                                 colossus_output_type="common_item",
                                 colossus_output_attr="sim_v3_colossus_output",
                                 topn_index_attr="topk_indices",
                                 topn_value_attr="topk_values", # useless
                                 output_item_colossus_pid_attr="gsu_pids",
                                 head_num=3,
                                 top_n=6,
                                 output_sign_attr="gsu_signs",
                                 output_slot_attr="gsu_slots",
                                 colossus_profile_stay_time_field_name="profile_stay_time",
                                 colossus_comment_stay_time_field_name="comment_stay_time",
                                 slots_id=[26, 128, 349, 348, 350, 344, 345, 342, 343, 696, 700, 701, 702, 703, 704],
                                 mio_slots_id=[346, 347, 349, 348, 350, 344, 345, 342, 343, 696, 700, 701, 702, 703, 704]
                                 )

    leaf = self.__init_service(flow)
    request_time = (1650098001 + 59) * 1000
    leaf.request_time = request_time
    item1 = leaf.add_item(1)
    item1_topk_indices = [1, 0, 2, 4, 5, 8, 3, 2, 1, 0, 7, 3, 6, 8, 2, 0]
    item1["topk_indices"] = item1_topk_indices

    item2 = leaf.add_item(2)
    item2_topk_indices = [2, 1, 0, 0, 3, 7, 5, 4, 6, 2, 7, 0, 1, 8, 5, 4]
    item2["topk_indices"] = item2_topk_indices
    leaf.run("test_gsu_with_multi_head_index_profile_comment")

    def unique_indices(indices, top_n):
      indices_set = set()
      k = 0
      for index in indices:
        if index in indices_set:
          continue
        indices_set.add(index)
        yield index
        k += 1
        if k >= top_n:
          break

    def gen_sign(slot, value):
      # kuiba style
      uint_sign = (slot << 54) | (value & ((1 << 54) - 1))
      int_sign = uint_sign if uint_sign < (1 << 63) else (uint_sign - (1 << 64))
      return int_sign

    def gen_signs(index):
      yield gen_sign(26, photo_id_list[index])
      yield gen_sign(128, author_id_list[index])
      yield gen_sign(349, tag_list[index])
      yield gen_sign(348, (play_time_list[index] << 24) | duration_list[index])
      yield gen_sign(350, (request_time // 1000 - timestamp_list[index]) // 86400)
      yield gen_sign(344, (play_time_list[index] << 24) | duration_list[index])
      yield gen_sign(345, (request_time // 1000 - timestamp_list[index]) // 86400)
      yield gen_sign(343, channel_list[index])
      yield gen_sign(700, channel_list[index])
      yield gen_sign(701, profile_list[index])
      yield gen_sign(702, comment_list[index])
      yield gen_sign(703, profile_list[index])
      yield gen_sign(704, comment_list[index])

    # 目前跑不过了，经常阻塞 kdev 代码提交，先注释掉了
    return
    correct_gsu_signs = list(itertools.chain.from_iterable(gen_signs(index) for index in unique_indices(item1_topk_indices, 6)))
    print("gsu_signs", item1["gsu_signs"])
    print("correct_gsu_signs", correct_gsu_signs)
    print("gsu_slots", item1["gsu_slots"])
    print("correct_gsu_slots", [346, 347, 349, 348, 350, 344, 345, 343, 700, 701, 702, 703, 704] * 6)

    self.assertEqual(item1["gsu_signs"], correct_gsu_signs)
    self.assertEqual(item1["gsu_slots"], [346, 347, 349, 348, 350, 344, 345, 343, 700, 701, 702, 703, 704] * 6)
    self.assertEqual(item1["gsu_pids"], [photo_id_list[index] for index in unique_indices(item1_topk_indices, 6)])

    self.assertEqual(item2["gsu_signs"], list(itertools.chain.from_iterable(gen_signs(index) for index in unique_indices(item2_topk_indices, 6))))
    self.assertEqual(item2["gsu_slots"], [346, 347, 349, 348, 350, 344, 345, 343, 700, 701, 702, 703, 704] * 6)
    self.assertEqual(item2["gsu_pids"], [photo_id_list[index] for index in unique_indices(item2_topk_indices, 6)])

  def test_gsu_with_multi_head_index(self):
    user_id = 666
    item_list_len = 10
    mock_items = {}
    photo_id_list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    mock_items["photo_id"] = photo_id_list
    self.assertEqual(item_list_len, len(photo_id_list))
    # 150001106621161 is a ksib author id, share a same data structure
    author_id_list = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
    mock_items["author_id"] = author_id_list
    self.assertEqual(item_list_len, len(author_id_list))
    duration_list = [100, 200, 300, 400, 500, 600, 700, 800, 900, 1000]
    mock_items["duration"] = duration_list
    self.assertEqual(item_list_len, len(duration_list))
    play_time_list = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
    mock_items["play_time"] = play_time_list
    self.assertEqual(item_list_len, len(play_time_list))
    tag_list = [21, 22, 23, 24, 25, 26, 27, 28, 29, 30]
    mock_items["tag"] = tag_list
    self.assertEqual(item_list_len, len(tag_list))
    channel_list = [10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
    mock_items["channel"] = channel_list
    self.assertEqual(item_list_len, len(channel_list))
    label_list = [11, 12, 13, 14, 15, 16, 17, 18, 19, 20]
    mock_items["label"] = label_list
    self.assertEqual(item_list_len, len(label_list))
    timestamp_list = [1650088495 - 24 * 3600, 1650088955, 1650089335, 1650089583, 1650089641, 1650089741, 1650089872, 1650090001, 1650092001, 1650098001]
    mock_items["timestamp"] = timestamp_list
    self.assertEqual(item_list_len, len(timestamp_list))
    user_latitude_list = [0.0] * 10
    mock_items["user_latitude"] = user_latitude_list
    self.assertEqual(item_list_len, len(user_latitude_list))
    user_longitude_list = [0.0] * 10
    mock_items["user_longitude"] = user_longitude_list
    self.assertEqual(item_list_len, len(user_longitude_list))
    photo_latitude_list = [0.0] * 10
    mock_items["photo_latitude"] = photo_latitude_list
    self.assertEqual(item_list_len, len(photo_latitude_list))
    photo_longitude_list = [0.0] * 10
    mock_items["photo_longitude"] = photo_longitude_list
    self.assertEqual(item_list_len, len(photo_longitude_list))
    # field_name, size, python struct format
    # struct format reference: https://docs.python.org/zh-cn/3/library/struct.html
    sim_item_v2_field_and_sizes = [
      ("photo_id", 8, 'Q'),
      ("author_id", 4, 'I'),
      ("duration", 2, 'H'),
      ("play_time", 2, 'H'),
      ("tag", 4, 'I'),
      ("channel", 1, 'B'),
      ("label", 2, 'H'),
      ("timestamp", 4, 'I'),
      ("user_latitude", 4, 'f'),
      ("user_longitude", 4, 'f'),
      ("photo_latitude", 4, 'f'),
      ("photo_longitude", 4, 'f')
    ]
    # construct SimItemV2 raw bytes
    item_size = 0
    for (name, size, format_str) in sim_item_v2_field_and_sizes:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in sim_item_v2_field_and_sizes:
        #print(name, i, type(i), mock_items[name])
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size
    # construct CommonItemResponse
    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_colossusSimV2", response_json_str=json.dumps(resp))
    flow = GsuFlow(name="extract_gsu_with_multi_head_index")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusSimV2',
        client_type='common_item_client',
        output_attr="sim_v2_colossus_output",
        parse_to_pb=False,
        debug_uids="666",
        print_items=True) \
      .gsu_with_multi_head_index(colossus_output_type="common_item",
                                 colossus_output_attr="sim_v2_colossus_output",
                                 topn_index_attr="topk_indices",
                                 topn_value_attr="topk_values", # useless
                                 output_item_colossus_pid_attr="gsu_pids",
                                 head_num=3,
                                 top_n=6,
                                 output_sign_attr="gsu_signs",
                                 output_slot_attr="gsu_slots")

    leaf = self.__init_service(flow)
    request_time = (1650098001 + 59) * 1000
    leaf.request_time = request_time
    item1 = leaf.add_item(1)
    item1_topk_indices = [1, 0, 2, 4, 5, 8, 3, 2, 1, 0, 7, 3, 6, 8, 2, 0]
    item1["topk_indices"] = item1_topk_indices

    item2 = leaf.add_item(2)
    item2_topk_indices = [2, 1, 0, 0, 3, 7, 5, 4, 6, 2, 7, 0, 1, 8, 5, 4]
    item2["topk_indices"] = item2_topk_indices
    leaf.run("extract_gsu_with_multi_head_index")

    def unique_indices(indices, top_n):
      indices_set = set()
      k = 0
      for index in indices:
        if index in indices_set:
          continue
        indices_set.add(index)
        yield index
        k += 1
        if k >= top_n:
          break

    def gen_sign(slot, value):
      # kuiba style
      uint_sign = (slot << 54) | (value & ((1 << 54) - 1))
      int_sign = uint_sign if uint_sign < (1 << 63) else (uint_sign - (1 << 64))
      return int_sign

    def gen_signs(index):
      yield gen_sign(26, photo_id_list[index])
      yield gen_sign(128, author_id_list[index])
      yield gen_sign(349, tag_list[index])
      yield gen_sign(348, (play_time_list[index] << 24) | duration_list[index])
      yield gen_sign(350, (request_time // 1000 - timestamp_list[index]) // 86400)
      yield gen_sign(344, (play_time_list[index] << 24) | duration_list[index])
      yield gen_sign(345, (request_time // 1000 - timestamp_list[index]) // 86400)
      yield gen_sign(343, channel_list[index])
      yield gen_sign(700, channel_list[index])

    self.assertEqual(item1["gsu_signs"], list(itertools.chain.from_iterable(gen_signs(index) for index in unique_indices(item1_topk_indices, 6))))
    self.assertEqual(item1["gsu_slots"], [346, 347, 349, 348, 350, 344, 345, 343, 700] * 6)
    self.assertEqual(item1["gsu_pids"], [photo_id_list[index] for index in unique_indices(item1_topk_indices, 6)])

    self.assertEqual(item2["gsu_signs"], list(itertools.chain.from_iterable(gen_signs(index) for index in unique_indices(item2_topk_indices, 6))))
    self.assertEqual(item2["gsu_slots"], [346, 347, 349, 348, 350, 344, 345, 343, 700] * 6)
    self.assertEqual(item2["gsu_pids"], [photo_id_list[index] for index in unique_indices(item2_topk_indices, 6)])

  def test_gsu_with_multi_head_index_context_feature(self):
    user_id = 666
    item_list_len = 10
    mock_items = {}
    photo_id_list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    mock_items["photo_id"] = photo_id_list
    self.assertEqual(item_list_len, len(photo_id_list))
    author_id_list = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
    mock_items["author_id"] = author_id_list
    self.assertEqual(item_list_len, len(author_id_list))
    duration_list = [100, 200, 300, 400, 500, 600, 700, 800, 900, 1000]
    mock_items["duration"] = duration_list
    self.assertEqual(item_list_len, len(duration_list))
    play_time_list = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
    mock_items["play_time"] = play_time_list
    self.assertEqual(item_list_len, len(play_time_list))
    tag_list = [21, 22, 23, 24, 25, 26, 27, 28, 29, 30]
    mock_items["tag"] = tag_list
    self.assertEqual(item_list_len, len(tag_list))
    channel_list = [10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
    mock_items["channel"] = channel_list
    self.assertEqual(item_list_len, len(channel_list))
    label_list = [11, 12, 13, 14, 15, 16, 17, 18, 19, 20]
    mock_items["label"] = label_list
    self.assertEqual(item_list_len, len(label_list))
    timestamp_list = [1650088495 - 24 * 3600, 1650088955, 1650089335, 1650089583, 1650089641, 1650089741, 1650089872, 1650090001, 1650092001, 1650098001]
    mock_items["timestamp"] = timestamp_list
    self.assertEqual(item_list_len, len(timestamp_list))
    profile_list = [31, 32, 33, 34, 35, 36, 37, 38, 39, 40]
    mock_items["profile_stay_time"] = profile_list
    self.assertEqual(item_list_len, len(profile_list))
    comment_list = [41, 42, 43, 44, 45, 46, 47, 48, 49, 50]
    mock_items["comment_stay_time"] = comment_list
    self.assertEqual(item_list_len, len(comment_list))
    profile_feed_mode_stay_time_list = [51, 52, 53, 54, 55, 56, 57, 58, 59, 60]
    mock_items["profile_feed_mode_stay_time"] = profile_feed_mode_stay_time_list
    self.assertEqual(item_list_len, len(profile_feed_mode_stay_time_list))
    real_show_index_list = [61, 62, 63, 64, 65, 66, 67, 68, 69, 70]
    mock_items["real_show_index"] = real_show_index_list
    self.assertEqual(item_list_len, len(real_show_index_list))
    # field_name, size, python struct format
    # struct format reference: https://docs.python.org/zh-cn/3/library/struct.html
    reco_sim_item_v3_field_and_size  = [
      ("photo_id", 8, "Q"),
      ("author_id", 4, "I"),
      ("duration", 2, "H"),
      ("play_time", 2, "H"),
      ("tag", 4, "I"),
      ("channel", 1, "B"),
      ("label", 2, "H"),
      ("timestamp", 4, "I"),
      ("profile_stay_time", 2, "H"),
      ("comment_stay_time", 2, "H"),
      ("profile_feed_mode_stay_time", 2, "H"),
      ("real_show_index", 4, "I"),
    ]
    # construct SimItemV2 raw bytes
    item_size = 0
    for (name, size, format_str) in reco_sim_item_v3_field_and_size:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in reco_sim_item_v3_field_and_size:
        #print(name, i, type(i), mock_items[name])
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size
    # construct CommonItemResponse
    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_colossusRecoSimItemV3", response_json_str=json.dumps(resp))
    flow = GsuFlow(name="test_gsu_with_multi_head_index_context_feature")
    flow.user_id = user_id
    
    flow\
      .colossus(
        service_name='grpc_colossusRecoSimItemV3',
        client_type='common_item_client',
        output_attr="sim_v3_colossus_output",
        parse_to_pb=False,
        debug_uids="666",
        print_items=True) \
      .log_debug_info(
          common_attrs=["sim_v3_colossus_output"],
          for_debug_request_only=False,
      ) \
      .gsu_with_multi_head_index(colossus_service_name="grpc_colossusRecoSimItemV3",
                                 colossus_output_type="common_item",
                                 colossus_output_attr="sim_v3_colossus_output",
                                 topn_index_attr="topk_indices",
                                 topn_value_attr="topk_values", # useless
                                 output_item_colossus_pid_attr="gsu_pids",
                                 head_num=3,
                                 top_n=6,
                                 context_number=1,
                                 output_sign_attr="gsu_signs",
                                 output_slot_attr="gsu_slots",
                                 colossus_profile_stay_time_field_name="profile_stay_time",
                                 colossus_comment_stay_time_field_name="comment_stay_time",
                                 colossus_profile_feed_mode_stay_time_field_name="profile_feed_mode_stay_time",
                                 colossus_real_show_index_field_name="real_show_index",
                                 extract_label_fea_switch = True,
                                 extract_context_fea_switch = True,
                                 slots_id=[26, 128, 349, 348, 350, 344, 345, 342, 343, 696, 700, 701, 702, 703, 704, 705, 706, 707, 708, 709, 710, 711, 712, 713, 714, 715, 716, 717, 718, 719, 720],
                                 mio_slots_id=[346, 347, 349, 348, 350, 344, 345, 342, 343, 696, 700, 701, 702, 703, 704, 705, 706,  707, 708, 709, 710, 711, 712, 713, 714, 715, 716, 717, 718, 719, 720],
                                 # ignore_idxs=[21, 22, 23, 24, 25]  #ignore 21, 22, 23, 24, 25
                                 )

    leaf = self.__init_service(flow)
    request_time = (1650098001 + 59) * 1000
    leaf.request_time = request_time
    item1 = leaf.add_item(1)
    item1_topk_indices = [1, 0, 2, 4, 5, 8, 3, 2, 1, 0, 7, 3, 6, 8, 2, 0]
    item1["topk_indices"] = item1_topk_indices

    item2 = leaf.add_item(2)
    item2_topk_indices = [2, 1, 0, 0, 3, 7, 5, 4, 6, 2, 7, 0, 1, 8, 5, 4]
    item2["topk_indices"] = item2_topk_indices
    
    leaf.run("test_gsu_with_multi_head_index_context_feature")
    def unique_indices(indices, top_n):
      indices_set = set()
      k = 0
      for index in indices:
        if index in indices_set:
          continue
        indices_set.add(index)
        yield index
        k += 1
        if k >= top_n:
          break

    def gen_sign(slot, value):
      #kuiba style
      uint_sign = (slot << 54) | (value & ((1 << 54) - 1))
      int_sign = uint_sign if uint_sign < (1 << 63) else (uint_sign - (1 << 64))
      return int_sign
    
    hour_diff_list = [2, 2, 2, 2, 2, 2, 2, 2, 1, 0]
    mean_play_time_list = [20, 20, 30, 40, 50, 60, 70, 80, 80, 80]
    mean_duration_list = [200, 200, 300, 400, 500, 600, 700, 800, 800, 800]
    mean_profile_stay_time_list = [32, 32, 33, 34, 35, 36, 37, 38, 38, 38]
    mean_profile_feed_mode_stay_time_list = [52, 52, 53, 54, 55, 56, 57, 58, 58, 58]
    mean_comment_stay_time_list = [42, 42, 43, 44, 45, 46, 47, 48, 48, 48]
    
    def gen_signs(index, i):
      yield gen_sign(26, photo_id_list[index])
      yield gen_sign(128, author_id_list[index])
      yield gen_sign(349, tag_list[index])
      yield gen_sign(348, (play_time_list[index] << 24) | duration_list[index])
      yield gen_sign(350, (request_time // 1000 - timestamp_list[index]) // 86400)
      yield gen_sign(344, (play_time_list[index] << 24) | duration_list[index])
      yield gen_sign(345, (request_time // 1000 - timestamp_list[index]) // 86400)
      yield gen_sign(342, label_list[index])
      yield gen_sign(343, channel_list[index])
      yield gen_sign(696, label_list[index])
      yield gen_sign(700, channel_list[index])
      yield gen_sign(701, profile_list[index])
      yield gen_sign(702, comment_list[index])
      yield gen_sign(703, profile_list[index])
      yield gen_sign(704, comment_list[index])
      yield gen_sign(705, profile_feed_mode_stay_time_list[index])
      yield gen_sign(706, real_show_index_list[index])
      yield gen_sign(707, profile_feed_mode_stay_time_list[index])
      yield gen_sign(708, real_show_index_list[index])
      yield gen_sign(709, hour_diff_list[index])
      yield gen_sign(710, hour_diff_list[index])
      
      yield gen_sign(711, mean_play_time_list[index])
      yield gen_sign(712, mean_duration_list[index])
      yield gen_sign(713, mean_profile_stay_time_list[index])
      yield gen_sign(714, mean_comment_stay_time_list[index])
      yield gen_sign(715, mean_profile_feed_mode_stay_time_list[index])
      yield gen_sign(716, mean_play_time_list[index])
      yield gen_sign(717, mean_duration_list[index])
      yield gen_sign(718, mean_profile_stay_time_list[index])
      yield gen_sign(719, mean_comment_stay_time_list[index])
      yield gen_sign(720, mean_profile_feed_mode_stay_time_list[index])

    # 目前跑不过了，经常阻塞 kdev 代码提交，先注释掉了
    return
    correct_gsu_signs = list(itertools.chain.from_iterable(gen_signs(index, i) for i, index in enumerate(unique_indices(item1_topk_indices, 6))))
    self.assertEqual(item1["gsu_signs"], correct_gsu_signs)
    self.assertEqual(item1["gsu_slots"], [346, 347, 349, 348, 350, 344, 345, 342, 343, 696, 700, 701, 702, 703, 704, 705, 706, 707, 708, 709, 710, 711, 712, 713, 714, 715, 716, 717, 718, 719, 720] * 6)
    self.assertEqual(item1["gsu_pids"], [photo_id_list[index] for index in unique_indices(item1_topk_indices, 6)])

    self.assertEqual(item2["gsu_signs"], list(itertools.chain.from_iterable(gen_signs(index, i) for i, index in enumerate(unique_indices(item2_topk_indices, 6)))))
    self.assertEqual(item2["gsu_slots"], [346, 347, 349, 348, 350, 344, 345, 342, 343, 696, 700, 701, 702, 703, 704, 705, 706, 707, 708, 709, 710, 711, 712, 713, 714, 715, 716, 717, 718, 719, 720] * 6)
    self.assertEqual(item2["gsu_pids"], [photo_id_list[index] for index in unique_indices(item2_topk_indices, 6)]) 

  def test_extract_feature_from_colossus_response(self):
    user_id = 666
    item_list_len = 5
    mock_items = {}
    photo_id_list = [1, 2, 3, 4, 5]
    mock_items["photo_id"] = photo_id_list
    self.assertEqual(item_list_len, len(photo_id_list))
    author_id_list = [10, 20, 30, 40, 50]
    mock_items["author_id"] = author_id_list
    self.assertEqual(item_list_len, len(author_id_list))
    duration_list = [100, 200, 300, 400, 500]
    mock_items["duration"] = duration_list
    self.assertEqual(item_list_len, len(duration_list))
    play_time_list = [10, 20, 30, 40, 50]
    mock_items["play_time"] = play_time_list
    self.assertEqual(item_list_len, len(play_time_list))
    tag_list = [21, 22, 23, 24, 25]
    mock_items["tag"] = tag_list
    self.assertEqual(item_list_len, len(tag_list))
    channel_list = [5, 4, 3, 2, 1]
    mock_items["channel"] = channel_list
    self.assertEqual(item_list_len, len(channel_list))
    label_list = [11, 12, 13, 14, 15]
    mock_items["label"] = label_list
    self.assertEqual(item_list_len, len(label_list))
    timestamp_list = [1650088495 - 24 * 3600, 1650088955, 1650089335, 1650089583, 1650089641]
    mock_items["timestamp"] = timestamp_list
    self.assertEqual(item_list_len, len(timestamp_list))
    user_latitude_list = [0.0, 0.0, 0.0, 0.0, 0.0]
    mock_items["user_latitude"] = user_latitude_list
    self.assertEqual(item_list_len, len(user_latitude_list))
    user_longitude_list = [0.0, 0.0, 0.0, 0.0, 0.0]
    mock_items["user_longitude"] = user_longitude_list
    self.assertEqual(item_list_len, len(user_longitude_list))
    photo_latitude_list = [0.0, 0.0, 0.0, 0.0, 0.0]
    mock_items["photo_latitude"] = photo_latitude_list
    self.assertEqual(item_list_len, len(photo_latitude_list))
    photo_longitude_list = [0.0, 0.0, 0.0, 0.0, 0.0]
    mock_items["photo_longitude"] = photo_longitude_list
    self.assertEqual(item_list_len, len(photo_longitude_list))
    # field_name, size, python struct format
    # struct format reference: https://docs.python.org/zh-cn/3/library/struct.html
    sim_item_v2_field_and_sizes = [
      ("photo_id", 8, 'Q'),
      ("author_id", 4, 'I'),
      ("duration", 2, 'H'),
      ("play_time", 2, 'H'),
      ("tag", 4, 'I'),
      ("channel", 1, 'B'),
      ("label", 2, 'H'),
      ("timestamp", 4, 'I'),
      ("user_latitude", 4, 'f'),
      ("user_longitude", 4, 'f'),
      ("photo_latitude", 4, 'f'),
      ("photo_longitude", 4, 'f')
    ]
    # construct SimItemV2 raw bytes
    item_size = 0
    for (name, size, format_str) in sim_item_v2_field_and_sizes:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in sim_item_v2_field_and_sizes:
        #print(name, i, type(i), mock_items[name])
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size
    # construct CommonItemResponse
    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_colossusSimV2", response_json_str=json.dumps(resp))
    flow = GsuFlow(name="extract_feature_from_colossus_response")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusSimV2',
        client_type='common_item_client',
        output_attr="sim_v2_colossus_output",
        parse_to_pb=False,
        debug_uids="666",
        print_items=True) \
      .extract_feature_from_colossus_response(
        colossus_output="sim_v2_colossus_output",
        output_signs_attr="colossus_signs",
        output_slots_attr="colossus_slots",
        output_pid_signs_attr="colossus_pid_signs",
        output_pid_slots_attr="colossus_pid_slots",
        item_slot_id=1006,
        timediff_bias_slot_id=345,
        play_bias_slot_id=344,
        label_bias_slot_id=342,
        channel_bias_slot_id=343,
        #colossus_item_num_attr="item_num",
        output_mask_bias_attr="colossus_is_null",
        colossus_output_type="common_item") \

    leaf = self.__init_service(flow)
    leaf.request_time = (1650089641 + 59) * 1000
    leaf.run("extract_feature_from_colossus_response")

    self.assertEqual(leaf["colossus_signs"], [(329 << 48) | 1,
                                              (328 << 48) | (10 << 24) | 100,
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | 5,
                                              (329 << 48) | 0,
                                              (328 << 48) | (20 << 24) | 200,
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | 4,
                                              (329 << 48) | 0,
                                              (328 << 48) | (30 << 24) | 300,
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | 3,
                                              (329 << 48) | 0,
                                              (328 << 48) | (40 << 24) | 400,
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | 2,
                                              (329 << 48) | ((1 << 48) - 1),
                                              (328 << 48) | ((1 << 48) - 1),
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | ((1 << 48) - 1)])

    self.assertEqual(leaf["colossus_slots"], [345, 344, 342, 343] * 5)
    self.assertEqual(leaf["colossus_pid_signs"], [1, 2, 3, 4, 5])
    self.assertEqual(leaf["colossus_pid_slots"], [1006] * 5)
    self.assertEqual(leaf["colossus_is_null"], [0.0] * 4 + [-1000.0] * 9996)

  def test_extract_feature_from_colossus_response_long(self):
    user_id = 666
    item_list_len = 5
    mock_items = {}
    photo_id_list = [1, 2, 3, 4, 5]
    mock_items["photo_id"] = photo_id_list
    self.assertEqual(item_list_len, len(photo_id_list))
    author_id_list = [10, 20, 30, 40, 50]
    mock_items["author_id"] = author_id_list
    self.assertEqual(item_list_len, len(author_id_list))
    duration_list = [100, 200, 300, 400, 500]
    mock_items["duration"] = duration_list
    self.assertEqual(item_list_len, len(duration_list))
    play_time_list = [10, 20, 30, 40, 50]
    mock_items["play_time"] = play_time_list
    self.assertEqual(item_list_len, len(play_time_list))
    cluster_id_list = [21, 22, 23, 24, 25]
    mock_items["cluster_id"] = cluster_id_list
    self.assertEqual(item_list_len, len(cluster_id_list))
    timestamp_list = [1650088495 - 24 * 3600, 1650088955, 1650089335, 1650089583, 1650089641]
    mock_items["timestamp"] = timestamp_list
    self.assertEqual(item_list_len, len(timestamp_list))
    # field_name, size, python struct format
    # struct format reference: https://docs.python.org/zh-cn/3/library/struct.html
    sim_item_v2_field_and_sizes = [
      ("photo_id", 8, 'Q'),
      ("author_id", 4, 'I'),
      ("timestamp", 4, 'I'),
      ("duration", 2, 'H'),
      ("play_time", 2, 'H'),
      ("cluster_id", 2, 'H'),
    ]
    # construct SimItemV2 raw bytes
    item_size = 0
    for (name, size, format_str) in sim_item_v2_field_and_sizes:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in sim_item_v2_field_and_sizes:
        #print(name, i, type(i), mock_items[name])
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size
    # construct CommonItemResponse
    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_colossusLongSimItem", response_json_str=json.dumps(resp))
    flow = GsuFlow(name="extract_feature_from_colossus_response_long")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusLongSimItem',
        client_type='common_item_client',
        output_attr="sim_v2_colossus_output",
        parse_to_pb=False,
        debug_uids="666",
        print_items=True) \
      .extract_feature_from_colossus_response(
        colossus_output="sim_v2_colossus_output",
        output_signs_attr="colossus_signs",
        output_slots_attr="colossus_slots",
        output_pid_signs_attr="colossus_pid_signs",
        output_pid_slots_attr="colossus_pid_slots",
        item_slot_id=1006,
        timediff_bias_slot_id=345,
        play_bias_slot_id=344,
        label_bias_slot_id=342,
        channel_bias_slot_id=343,
        #colossus_item_num_attr="item_num",
        output_mask_bias_attr="colossus_is_null",
        colossus_output_type="common_item",
        colossus_service_name="grpc_colossusLongSimItem",
        colossus_photo_id_field_name="photo_id",
        colossus_author_id_field_name="author_id",
        colossus_play_time_field_name="play_time",
        colossus_duration_field_name="duration",
        colossus_timestamp_field_name="timestamp",
        colossus_tag_field_name="cluster_id",
        colossus_channel_field_name="",
        colossus_label_field_name="") \

    leaf = self.__init_service(flow)
    leaf.request_time = (1650089641 + 59) * 1000
    leaf.run("extract_feature_from_colossus_response_long")

    self.assertEqual(leaf["colossus_signs"], [(329 << 48) | 1,
                                              (328 << 48) | (10 << 24) | 100,
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | 0,
                                              (329 << 48) | 0,
                                              (328 << 48) | (20 << 24) | 200,
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | 0,
                                              (329 << 48) | 0,
                                              (328 << 48) | (30 << 24) | 300,
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | 0,
                                              (329 << 48) | 0,
                                              (328 << 48) | (40 << 24) | 400,
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | 0,
                                              (329 << 48) | ((1 << 48) - 1),
                                              (328 << 48) | ((1 << 48) - 1),
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | ((1 << 48) - 1)])

    self.assertEqual(leaf["colossus_slots"], [345, 344, 342, 343] * 5)
    self.assertEqual(leaf["colossus_pid_signs"], [1, 2, 3, 4, 5])
    self.assertEqual(leaf["colossus_pid_slots"], [1006] * 5)
    self.assertEqual(leaf["colossus_is_null"], [0.0] * 4 + [-1000.0] * 9996)

  def test_gsu_with_multi_head_index_long(self):
    user_id = 666
    item_list_len = 10
    mock_items = {}
    photo_id_list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    mock_items["photo_id"] = photo_id_list
    self.assertEqual(item_list_len, len(photo_id_list))
    author_id_list = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
    mock_items["author_id"] = author_id_list
    self.assertEqual(item_list_len, len(author_id_list))
    duration_list = [100, 200, 300, 400, 500, 600, 700, 800, 900, 1000]
    mock_items["duration"] = duration_list
    self.assertEqual(item_list_len, len(duration_list))
    play_time_list = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
    mock_items["play_time"] = play_time_list
    self.assertEqual(item_list_len, len(play_time_list))
    cluster_id_list = [21, 22, 23, 24, 25, 26, 27, 28, 29, 30]
    mock_items["cluster_id"] = cluster_id_list
    self.assertEqual(item_list_len, len(cluster_id_list))
    timestamp_list = [1650088495 - 24 * 3600, 1650088955, 1650089335, 1650089583, 1650089641, 1650089741, 1650089872, 1650090001, 1650092001, 1650098001]
    mock_items["timestamp"] = timestamp_list
    self.assertEqual(item_list_len, len(timestamp_list))
    # field_name, size, python struct format
    # struct format reference: https://docs.python.org/zh-cn/3/library/struct.html
    sim_item_v2_field_and_sizes = [
      ("photo_id", 8, 'Q'),
      ("author_id", 4, 'I'),
      ("timestamp", 4, 'I'),
      ("duration", 2, 'H'),
      ("play_time", 2, 'H'),
      ("cluster_id", 2, 'H'),
    ]
    # construct SimItemV2 raw bytes
    item_size = 0
    for (name, size, format_str) in sim_item_v2_field_and_sizes:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in sim_item_v2_field_and_sizes:
        #print(name, i, type(i), mock_items[name])
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size
    # construct CommonItemResponse
    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_colossusLongSimItem", response_json_str=json.dumps(resp))
    flow = GsuFlow(name="extract_gsu_with_multi_head_index_long")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusLongSimItem',
        client_type='common_item_client',
        output_attr="sim_v2_colossus_output",
        parse_to_pb=False,
        debug_uids="666",
        print_items=True) \
      .gsu_with_multi_head_index(colossus_output_type="common_item",
                                 colossus_output_attr="sim_v2_colossus_output",
                                 topn_index_attr="topk_indices",
                                 topn_value_attr="topk_values", # useless
                                 output_item_colossus_pid_attr="gsu_pids",
                                 head_num=3,
                                 top_n=6,
                                 output_sign_attr="gsu_signs",
                                 output_slot_attr="gsu_slots",
                                 colossus_service_name="grpc_colossusLongSimItem",
                                 colossus_photo_id_field_name="photo_id",
                                 colossus_author_id_field_name="author_id",
                                 colossus_play_time_field_name="play_time",
                                 colossus_duration_field_name="duration",
                                 colossus_timestamp_field_name="timestamp",
                                 colossus_tag_field_name="cluster_id",
                                 colossus_channel_field_name="",
                                 colossus_label_field_name="")

    leaf = self.__init_service(flow)
    request_time = (1650098001 + 59) * 1000
    leaf.request_time = request_time
    item1 = leaf.add_item(1)
    item1_topk_indices = [1, 0, 2, 4, 5, 8, 3, 2, 1, 0, 7, 3, 6, 8, 2, 0]
    item1["topk_indices"] = item1_topk_indices

    item2 = leaf.add_item(2)
    item2_topk_indices = [2, 1, 0, 0, 3, 7, 5, 4, 6, 2, 7, 0, 1, 8, 5, 4]
    item2["topk_indices"] = item2_topk_indices
    leaf.run("extract_gsu_with_multi_head_index_long")

    def unique_indices(indices, top_n):
      indices_set = set()
      k = 0
      for index in indices:
        if index in indices_set:
          continue
        indices_set.add(index)
        yield index
        k += 1
        if k >= top_n:
          break

    def gen_sign(slot, value):
      # kuiba style
      uint_sign = (slot << 54) | (value & ((1 << 54) - 1))
      int_sign = uint_sign if uint_sign < (1 << 63) else (uint_sign - (1 << 64))
      return int_sign

    def gen_signs(index):
      yield gen_sign(26, photo_id_list[index])
      yield gen_sign(128, author_id_list[index])
      yield gen_sign(349, cluster_id_list[index])
      yield gen_sign(348, (play_time_list[index] << 24) | duration_list[index])
      yield gen_sign(350, (request_time // 1000 - timestamp_list[index]) // 86400)
      yield gen_sign(344, (play_time_list[index] << 24) | duration_list[index])
      yield gen_sign(345, (request_time // 1000 - timestamp_list[index]) // 86400)
      yield gen_sign(343, 0)
      yield gen_sign(700, 0)

    self.assertEqual(item1["gsu_signs"], list(itertools.chain.from_iterable(gen_signs(index) for index in unique_indices(item1_topk_indices, 6))))
    self.assertEqual(item1["gsu_slots"], [346, 347, 349, 348, 350, 344, 345, 343, 700] * 6)
    self.assertEqual(item1["gsu_pids"], [photo_id_list[index] for index in unique_indices(item1_topk_indices, 6)])

    self.assertEqual(item2["gsu_signs"], list(itertools.chain.from_iterable(gen_signs(index) for index in unique_indices(item2_topk_indices, 6))))
    self.assertEqual(item2["gsu_slots"], [346, 347, 349, 348, 350, 344, 345, 343, 700] * 6)
    self.assertEqual(item2["gsu_pids"], [photo_id_list[index] for index in unique_indices(item2_topk_indices, 6)])

  def test_gsu_common_colossus_resp_retriever_long(self):
    user_id = 666
    item_list_len = 5
    mock_items = {}
    photo_id_list = [1, 2, 3, 4, 5]
    mock_items["photo_id"] = photo_id_list
    self.assertEqual(item_list_len, len(photo_id_list))
    author_id_list = [10, 20, 30, 40, 50]
    mock_items["author_id"] = author_id_list
    self.assertEqual(item_list_len, len(author_id_list))
    duration_list = [100, 200, 300, 400, 500]
    mock_items["duration"] = duration_list
    self.assertEqual(item_list_len, len(duration_list))
    play_time_list = [10, 20, 30, 40, 50]
    mock_items["play_time"] = play_time_list
    self.assertEqual(item_list_len, len(play_time_list))
    cluster_list = [21, 22, 23, 24, 25]
    mock_items["cluster_id"] = cluster_list
    self.assertEqual(item_list_len, len(cluster_list))
    timestamp_list = [1650088495, 1650088955, 1650089335, 1650089583, 1650089641]
    mock_items["timestamp"] = timestamp_list
    self.assertEqual(item_list_len, len(timestamp_list))
    # field_name, size, python struct format
    # struct format reference: https://docs.python.org/zh-cn/3/library/struct.html
    sim_item_v2_field_and_sizes = [
      ("photo_id", 8, 'Q'),
      ("author_id", 4, 'I'),
      ("timestamp", 4, 'I'),
      ("duration", 2, 'H'),
      ("play_time", 2, 'H'),
      ("cluster_id", 2, 'H'),
    ]
    # construct SimItemV2 raw bytes
    item_size = 0
    for (name, size, format_str) in sim_item_v2_field_and_sizes:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in sim_item_v2_field_and_sizes:
        #print(name, i, type(i), mock_items[name])
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size
    # construct CommonItemResponse
    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_colossusLongSimItem", response_json_str=json.dumps(resp))
    flow = GsuFlow(name="gsu_common_colossus_resp_retriever_long")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusLongSimItem',
        client_type='common_item_client',
        output_attr="sim_v2_colossus_output",
        parse_to_pb=False,
        debug_uids="666",
        print_items=True) \
      .gsu_common_colossus_resp_retriever(
        colossus_resp_attr="sim_v2_colossus_output",
        colossus_service_name="grpc_colossusLongSimItem",
        item_key_field="photo_id",
        item_time_field="timestamp",
        to_common_attr=True,
        print_item_fields=True,
        item_fields=dict(
          photo_id="photo_id",
          author_id="author_id",
          duration="duration",
          play_time="play_time",
          cluster_id="cluster_id",
          timestamp="timestamp",
        ))

    leaf = self.__init_service(flow)
    leaf.run("gsu_common_colossus_resp_retriever_long")
    for (attr_name, size, format_str) in sim_item_v2_field_and_sizes:
      self.assertNotEqual(leaf[attr_name], None, f'not found common attr {attr_name}')
      self.assertEqual(len(leaf[attr_name]), item_list_len)
      for i in range(item_list_len):
        self.assertEqual(leaf[attr_name][i], mock_items[attr_name][i])

  def test_gsu_with_cluster_general(self):
    user_id = 666
    item_list_len = 6
    mock_items = {}
    photo_id_list = [1, 2, 3, 4, 5, 6]
    mock_items["photo_id"] = photo_id_list
    self.assertEqual(item_list_len, len(photo_id_list))
    author_id_list = [10, 20, 30, 40, 50, 60]
    mock_items["author_id"] = author_id_list
    self.assertEqual(item_list_len, len(author_id_list))
    duration_list = [100, 200, 300, 400, 500, 600]
    mock_items["duration"] = duration_list
    self.assertEqual(item_list_len, len(duration_list))
    play_time_list = [10, 20, 30, 40, 50, 60]
    mock_items["play_time"] = play_time_list
    self.assertEqual(item_list_len, len(play_time_list))
    cluster_list = [687, 430, 880, 149, 370, 687]
    mock_items["cluster_id"] = cluster_list
    self.assertEqual(item_list_len, len(cluster_list))
    timestamp_list = [1650088495 - 24 * 3600, 1650088955, 1650089335, 1650089583, 1650089641, 1650089655]
    mock_items["timestamp"] = timestamp_list
    self.assertEqual(item_list_len, len(timestamp_list))
    # field_name, size, python struct format
    # struct format reference: https://docs.python.org/zh-cn/3/library/struct.html
    sim_item_v2_field_and_sizes = [
      ("photo_id", 8, 'Q'),
      ("author_id", 4, 'I'),
      ("timestamp", 4, 'I'),
      ("duration", 2, 'H'),
      ("play_time", 2, 'H'),
      ("cluster_id", 2, 'H'),
    ]
    # construct SimItemV2 raw bytes
    item_size = 0
    for (name, size, format_str) in sim_item_v2_field_and_sizes:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in sim_item_v2_field_and_sizes:
        #print(name, i, type(i), mock_items[name])
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size
    # construct CommonItemResponse
    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_colossusLongSimItem", response_json_str=json.dumps(resp))
    flow = GsuFlow(name="gsu_with_cluster_general")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusLongSimItem',
        client_type='common_item_client',
        output_attr="sim_v2_colossus_output",
        parse_to_pb=False,
        debug_uids="666",
        print_items=True) \
      .gsu_with_cluster_general(colossus_resp_attr='sim_v2_colossus_output',
                                output_sign_attr='signs',
                                output_slot_attr='slots',
                                target_cluster_attr='cluster',
                                colossus_item_cluster_id_name='cluster_id',
                                limit_num=2,
                                skip_latest_items_num=2,
                                skip_latest_items_seconds=60,
                                colossus_service_name="grpc_colossusLongSimItem",
                                extra_sign_feature_configs=[
                                  dict(name="photo_id", slot=26, mio_slot=346),
                                  dict(name="author_id", slot=128, mio_slot=347),
                                  dict(name="cluster_id", slot=349),
                                ],
                                kess_service='kws-kuaishou-full-rank-embedding-mmu-hetu-cluster-id-long',
                                shards=8)

    leaf = self.__init_service(flow)
    leaf.request_time = (1650089655 + 59) * 1000

    item1 = leaf.add_item(123)
    item1["cluster"] = 19

    item2 = leaf.add_item(124)
    item2["cluster"] = 20

    leaf.run("gsu_with_cluster_general")

    self.assertEqual(item1["slots"], [348, 350, 346, 347, 349, 348, 350, 346, 347, 349])
    self.assertEqual(item1["signs"], [(348 << 54) | (40 << 24) | 400,
                                      (350 << 54) | 0,
                                      (26 << 54) | 4,
                                      (128 << 54) | 40,
                                      (349 << 54) | 149,
                                      (348 << 54) | (10 << 24) | 100,
                                      (350 << 54) | 1,
                                      (26 << 54) | 1,
                                      (128 << 54) | 10,
                                      (349 << 54) | 687])
    self.assertEqual(item2["slots"], [348, 350, 346, 347, 349, 348, 350, 346, 347, 349])
    self.assertEqual(item2["signs"], [(348 << 54) | (30 << 24) | 300,
                                      (350 << 54) | 0,
                                      (26 << 54) | 3,
                                      (128 << 54) | 30,
                                      (349 << 54) | 880,
                                      (348 << 54) | (20 << 24) | 200,
                                      (350 << 54) | 0,
                                      (26 << 54) | 2,
                                      (128 << 54) | 20,
                                      (349 << 54) | 430])

  def test_live_tag_gsu_for_retr_v2(self):
    flow = GsuFlow(name="test_live_tag_gsu_for_retr_v2") \
      .live_tag_gsu_for_retr_v2(
        is_train_mode = 0,
        sort_mode = 0,
        padding_num = 0,
        colossus_resp_attr='live_colossus_output',
        limit_num=10,
        output_sign_attr="live_gsu_signs",
        output_slot_attr="live_gsu_slots")

    leaf = self.__init_service(flow)
    leaf.run("test_live_tag_gsu_for_retr_v2")
    leaf["nonsense"] = 1
    self.assertEqual(leaf['nonsense'], 1)

  def test_slide_gsu_with_cluster(self):
    flow = GsuFlow(name="test_slide_gsu_with_cluster") \
      .slide_gsu_with_cluster(
      colossus_resp_attr="colossus_output",
      limit_num_attr="limit_num",
      kess_service='kws-kuaishou-nebula-embedding-mmu-hetu-id-online-for-ltr',
      timeous_ms=30000,
      shards=4,
      output_cluster_attr="cluster_id",
      output_pid_attr="searched_pid_list"
    )
    leaf = self.__init_service(flow)
    leaf.run("test_slide_gsu_with_cluster")
    leaf["nonsense"] = 1
    self.assertEqual(leaf['nonsense'], 1)

  def test_kmeans_aggregate(self):
    flow = GsuFlow(name="test_kmeans_aggregate")
    flow.kmeans_aggregate(
        cluster_action_index_common_attr_name="cluster_action_index",
        cluster_action_num_common_attr_name="cluster_action_num",
        action_list_id_common_attr_name = "action_list_id",
        output_common_attr_name = "kmeans_agg_output",
        photo_id_field_name = "photo_id",
        author_id_field_name = "author_id",
        duration_field_name = "duration",
        play_time_field_name = "play_time",
        timestamp_field_name = "timestamp",
        cluster_id_field_name = "cluster_id"
      )\
     .log_debug_info(
       common_attrs=["kmeans_agg_output"],
       for_debug_request_only=False
     )

    leaf = self.__init_service(flow)

    user_id = 666
    kmeans_item = []
    kmeans_item_num = 10
    photo_id = [i+1 for i in range(kmeans_item_num)]
    author_id = [i+11 for i in range(kmeans_item_num)]
    duration = [i+21 for i in range(kmeans_item_num)]
    play_time = [i+31 for i in range(kmeans_item_num)]
    cluster_id = [i+41 for i in range(kmeans_item_num)]
    timestamp = [i+51 for i in range(kmeans_item_num)]
    leaf.user_id = user_id
    for i in range(kmeans_item_num):
      item_key = photo_id[i]
      item = leaf.new_item(item_key)
      item["photo_id"] = photo_id[i]
      item["author_id"] = author_id[i]
      item["duration"] = duration[i]
      item["play_time"] = play_time[i]
      item["cluster_id"] = cluster_id[i]
      item["timestamp"] = timestamp[i]
      kmeans_item.append(item)

    cluster_action_index = [0,1,2, 3,4,5, 6,7,8, 9]
    cluster_action_num = [3,3,3,1]
    kmeans_re_len = len(cluster_action_num)
    action_list_id = photo_id
    leaf["cluster_action_index"] = cluster_action_index
    leaf["cluster_action_num"] = cluster_action_num
    leaf["action_list_id"] = action_list_id

    kmeans_results = {}
    kmeans_results["photo_id"] = [1, 4, 7, 10]
    kmeans_results["author_id"] = [11, 14, 17, 20]
    kmeans_results["duration"] = [(21+22+23)//3, (24+25+26)//3, (27+28+29)//3, 30]
    kmeans_results["play_time"] = [(31+32+33)//3, (34+35+36)//3, (37+38+39)//3, 40]
    kmeans_results["cluster_id"] = [41, 44, 47, 50]
    kmeans_results["timestamp"] = [51, 54, 57, 60]
    sim_item_v3_field_and_sizes = [
      ("photo_id", 8, 'Q'),
      ("author_id", 4, 'I'),
      ("timestamp", 4, 'I'),
      ("duration", 2, 'H'),
      ("play_time", 2, 'H'),
      ("cluster_id", 2, 'H'),
    ]

    item_size = 0
    for (name, size, format_str) in sim_item_v3_field_and_sizes:
      item_size = item_size + size
    buffer = bytearray(item_size * kmeans_re_len)
    offset = 0
    for i in range(kmeans_re_len):
      for (name, size, format_str) in sim_item_v3_field_and_sizes:
        #print(name, i, type(i), mock_items[name])
        struct.pack_into(format_str, buffer, offset, kmeans_results[name][i])
        offset = offset + size

    # print("buffer:", buffer)

    leaf.run("test_kmeans_aggregate")

    self.assertNotEqual(leaf.get_bytes("kmeans_agg_output"), None, f'not found common attr kmeans_agg_output')
    self.assertEqual(leaf.get_bytes("kmeans_agg_output"), buffer)

  def test_kmeans_aggregate_retriever(self):
    flow = GsuFlow(name="test_kmeans_aggregate_retriever")
    flow.kmeans_aggregate(
        cluster_action_index_common_attr_name="cluster_action_index",
        cluster_action_num_common_attr_name="cluster_action_num",
        action_list_id_common_attr_name = "action_list_id",
        output_common_attr_name = "kmeans_agg_output",
        photo_id_field_name = "photo_id",
        author_id_field_name = "author_id",
        duration_field_name = "duration",
        play_time_field_name = "play_time",
        timestamp_field_name = "timestamp",
        cluster_id_field_name = "cluster_id"
      )\
     .log_debug_info(
       common_attrs=["kmeans_agg_output"],
       for_debug_request_only=False
     ) \
    .limit(0) \
    .gsu_common_colossus_resp_retriever(
      colossus_resp_attr="kmeans_agg_output",
      colossus_service_name="grpc_colossusLongSimItem",
      item_key_field="photo_id",
      item_time_field="timestamp",
      to_common_attr=True,
      print_item_fields=True,
      item_fields=dict(
        photo_id="photo_id",
        author_id="author_id",
        duration="duration",
        play_time="play_time",
        cluster_id="cluster_id",
        timestamp="timestamp",
      )
    )

    leaf = self.__init_service(flow)

    user_id = 666
    kmeans_item = []
    kmeans_item_num = 10
    photo_id = [i+1 for i in range(kmeans_item_num)]
    author_id = [i+11 for i in range(kmeans_item_num)]
    duration = [i+21 for i in range(kmeans_item_num)]
    play_time = [i+31 for i in range(kmeans_item_num)]
    cluster_id = [i+41 for i in range(kmeans_item_num)]
    timestamp = [i+51 for i in range(kmeans_item_num)]
    leaf.user_id = user_id
    for i in range(kmeans_item_num):
      item_key = photo_id[i]
      item = leaf.new_item(item_key)
      item["photo_id"] = photo_id[i]
      item["author_id"] = author_id[i]
      item["duration"] = duration[i]
      item["play_time"] = play_time[i]
      item["cluster_id"] = cluster_id[i]
      item["timestamp"] = timestamp[i]
      kmeans_item.append(item)

    cluster_action_index = [0,1,2, 3,4,5, 6,7,8, 9]
    cluster_action_num = [3,3,3,1]
    kmeans_re_len = len(cluster_action_num)
    action_list_id = photo_id
    leaf["cluster_action_index"] = cluster_action_index
    leaf["cluster_action_num"] = cluster_action_num
    leaf["action_list_id"] = action_list_id

    kmeans_results = {}
    kmeans_results["photo_id"] = [1, 4, 7, 10]
    kmeans_results["author_id"] = [11, 14, 17, 20]
    kmeans_results["duration"] = [(21+22+23)//3, (24+25+26)//3, (27+28+29)//3, 30]
    kmeans_results["play_time"] = [(31+32+33)//3, (34+35+36)//3, (37+38+39)//3, 40]
    kmeans_results["cluster_id"] = [41, 44, 47, 50]
    kmeans_results["timestamp"] = [51, 54, 57, 60]

    sim_item_v3_field_and_sizes = [
      ("photo_id", 8, 'Q'),
      ("author_id", 4, 'I'),
      ("timestamp", 4, 'I'),
      ("duration", 2, 'H'),
      ("play_time", 2, 'H'),
      ("cluster_id", 2, 'H'),
    ]

    leaf.run("test_kmeans_aggregate_retriever")
    for (attr_name, size, format_str) in sim_item_v3_field_and_sizes:
      self.assertNotEqual(leaf[attr_name], None, f'not found common attr {attr_name}')
      self.assertEqual(len(leaf[attr_name]), kmeans_re_len)
      for i in range(kmeans_re_len):
        self.assertEqual(leaf[attr_name][i], kmeans_results[attr_name][i])

  def _mock_colossus_response_long_sim(self):
    colossus_items = ColossusSimItemList(user_id=666,
                                         schema= [("photo_id", 8, 'Q'),
                                                  ("author_id", 4, 'I'),
                                                  ("timestamp", 4, 'I'),
                                                  ("duration", 2, 'H'),
                                                  ("play_time", 2, 'H'),
                                                  ("cluster_id", 2, 'H'),
                                                  ],
                                         )

    # self.assertEqual(colossus_items.random_initialize(10), True)
    # colossus_items.clear()

    item_list_len = 5
    for i in range(1,1+item_list_len):
      colossus_items.add_item(dict(
        photo_id=i,
        author_id=i*10,
        timestamp=int(time.time() - 60 - i * 86400),
        duration=i*100,
        play_time=i*10,
        cluster_id=20+i,
      ))

    print(f"mock item_num: {colossus_items.item_num}", file=sys.stderr)
    self.assertEqual(colossus_items.check_format(), True)

    return colossus_items.serialize(), colossus_items.items

  def test_general_extract_feature_by_colossus_reflect(self):
    # mock running data
    mock_colossus_service = "grpc_colossusLongSimItem"
    resp, mock_data = self._mock_colossus_response_long_sim()
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name=mock_colossus_service, response_json_str=json.dumps(resp))

    # define flow
    read_fields = [
      dict(field="photo_id", default_int=0, is_item_id=True),
      dict(field="author_id", default_int=0),
      dict(field="timestamp", default_int=0),
      dict(field="play_time", default_int=0),
      dict(field="duration", default_int=0),
      dict(field="cluster_id", default_int=0),
      dict(field="blabla_whatever", default_int=-1),
    ]
    filter_config= [
      dict(name="play_time", field="play_time", playtime_lowerbound=11),
    ]
    extract_config = [
      dict(name="plain", field="photo_id", slot_id_in_sign=255, slot_id_in_model=1255),
      dict(name="plain", field="author_id", slot_id_in_sign=256, slot_id_in_model=1256),
      dict(name="plain", field="cluster_id", slot_id_in_sign=257, slot_id_in_model=1257),
      dict(name="play_time", field="play_time", slot_id_in_sign=694, duration_field="duration"),
      dict(name="time_diff", field="timestamp", slot_id_in_sign=693),
      dict(name="plain", field="blabla_whatever", slot_id_in_sign=694),  # test missing field
    ]
    flow = GsuFlow(name="test_general_extract_feature_by_colossus_reflect")\
        .colossus(
          service_name=mock_colossus_service,
          client_type='common_item_client',
          output_attr="colossus_output",
          parse_to_pb=False,
          debug_uids="666",
          print_items=True,
        )\
        .general_extract_feature_by_colossus_reflect(
          colossus_output="colossus_output",
          colossus_output_type="common_item",
          colossus_reflect_schema_type=mock_colossus_service,
          colossus_item_num_limit=5,
          output_signs_attr="colossus_signs",
          output_slots_attr="colossus_slots",
          output_colossus_item_key_attr="gsu_items",
          output_mask_bias_attr="gsu_item_mask",
          read_fields = read_fields,
          filter_config= filter_config,
          extract_config = extract_config,
          )\
        .general_extract_feature_by_colossus_reflect(
          colossus_output="colossus_output",
          colossus_output_type="common_item",
          colossus_reflect_schema_type=mock_colossus_service,
          colossus_item_num_limit=5,
          keep_filtered_item=True,
          output_signs_attr="colossus_signs2",
          output_slots_attr="colossus_slots2",
          output_colossus_item_key_attr="gsu_items2",
          output_mask_bias_attr="gsu_item_mask2",
          read_fields = read_fields,
          filter_config= filter_config,
          extract_config = extract_config,
          )

    # run test
    leaf = self.__init_service(flow)
    leaf.request_time = int(time.time() * 1000)
    leaf.run("test_general_extract_feature_by_colossus_reflect")

    # assert
    slot_in_model_list = [1255, 1256, 1257, 694, 693, 694]
    slot_in_sign_list = [255, 256, 257, 694, 693, 694]
    valid_slots, valid_signs = list(), list()
    for i in range(1, 5):
      valid_slots = valid_slots + slot_in_model_list
      valid_signs.append(slot_high_val_low_sign(255, mock_data["photo_id"][i]))
      valid_signs.append(slot_high_val_low_sign(256, mock_data["author_id"][i]))
      valid_signs.append(slot_high_val_low_sign(257, mock_data["cluster_id"][i]))
      valid_signs.append(slot_high_val_low_sign(694, (mock_data["play_time"][i] << 24) | mock_data["duration"][i]))
      valid_signs.append(slot_high_val_low_sign(693, int((leaf.request_time/1000 - 60 - mock_data["timestamp"][i])/86400)))
      valid_signs.append(slot_high_val_low_sign(694, (1<<48) -1))

    self.assertNotEqual(leaf["colossus_output"], None)

    self.assertEqual(len(leaf["gsu_items"]), 4)
    for i in range(4): self.assertEqual(leaf["gsu_items"][i], i+2)

    self.assertEqual(len(leaf["gsu_item_mask"]), 5)
    for i in range(4): self.assertEqual(leaf["gsu_item_mask"][i], 0)
    self.assertEqual(leaf["gsu_item_mask"][4], -1000.0)

    slots, signs = valid_slots, valid_signs
    self.assertEqual(len(leaf["colossus_slots"]), len(slots))
    self.assertEqual(len(leaf["colossus_signs"]), len(signs))
    for i in range(len(slots)): self.assertEqual(leaf["colossus_slots"][i], slots[i])
    for i in range(len(signs)): self.assertEqual(leaf["colossus_signs"][i], signs[i])

    # ---------------------------

    self.assertEqual(len(leaf["gsu_items2"]), 5)
    for i in range(5): self.assertEqual(leaf["gsu_items2"][i], i+1)

    self.assertEqual(len(leaf["gsu_item_mask2"]), 5)
    self.assertEqual(leaf["gsu_item_mask2"][0], -1000.0)
    for i in range(1, 5): self.assertEqual(leaf["gsu_item_mask2"][i], 0)

    slots = slot_in_model_list + valid_slots
    signs = [slot_high_val_low_sign(slot, (1<<48) -1) for slot in slot_in_sign_list] + valid_signs
    self.assertEqual(len(leaf["colossus_slots2"]), len(slots))
    self.assertEqual(len(leaf["colossus_signs2"]), len(signs))
    for i in range(len(slots)): self.assertEqual(leaf["colossus_slots2"][i], slots[i])
    for i in range(len(signs)): self.assertEqual(leaf["colossus_signs2"][i], signs[i])

  def test_kmeans_aggregate_extract_fea(self):
    flow = GsuFlow(name="test_kmeans_aggregate_extract_fea")
    flow.kmeans_aggregate(
        cluster_action_index_common_attr_name="cluster_action_index",
        cluster_action_num_common_attr_name="cluster_action_num",
        action_list_id_common_attr_name = "action_list_id",
        output_common_attr_name = "kmeans_agg_output",
        photo_id_field_name = "photo_id",
        author_id_field_name = "author_id",
        duration_field_name = "duration",
        play_time_field_name = "play_time",
        timestamp_field_name = "timestamp",
        cluster_id_field_name = "cluster_id"
      )\
     .log_debug_info(
       common_attrs=["kmeans_agg_output"],
       for_debug_request_only=False
     ) \
    .extract_feature_from_colossus_response(
        colossus_output="kmeans_agg_output",
        output_signs_attr="colossus_signs",
        output_slots_attr="colossus_slots",
        output_pid_signs_attr="colossus_pid_signs",
        output_pid_slots_attr="colossus_pid_slots",
        item_slot_id=1006,
        timediff_bias_slot_id=345,
        play_bias_slot_id=344,
        label_bias_slot_id=342,
        channel_bias_slot_id=343,
        #colossus_item_num_attr="item_num",
        output_mask_bias_attr="colossus_is_null",
        colossus_output_type="common_item",
        colossus_service_name="grpc_colossusLongSimItem",
        colossus_photo_id_field_name="photo_id",
        colossus_author_id_field_name="author_id",
        colossus_play_time_field_name="play_time",
        colossus_duration_field_name="duration",
        colossus_timestamp_field_name="timestamp",
        colossus_tag_field_name="cluster_id",
        colossus_channel_field_name="",
        colossus_label_field_name="") \

    leaf = self.__init_service(flow)

    user_id = 666
    kmeans_item = []
    kmeans_item_num = 10
    photo_id = [i+1 for i in range(kmeans_item_num)]
    author_id = [i+11 for i in range(kmeans_item_num)]
    play_time = [i+21 for i in range(kmeans_item_num)]
    duration = [i+31 for i in range(kmeans_item_num)]
    cluster_id = [i+41 for i in range(kmeans_item_num)]
    timestamp = [1650088495 - 24 * 3600,1650088955,1650089335, 1650089583,1650089582,1650089581, 1650089580,1650089579,1650089578, 1650089641]
    leaf.user_id = user_id
    for i in range(kmeans_item_num):
      item_key = photo_id[i]
      item = leaf.new_item(item_key)
      item["photo_id"] = photo_id[i]
      item["author_id"] = author_id[i]
      item["duration"] = duration[i]
      item["play_time"] = play_time[i]
      item["cluster_id"] = cluster_id[i]
      item["timestamp"] = timestamp[i]
      kmeans_item.append(item)

    cluster_action_index = [0,1,2, 3,4,5, 6,7,8, 9]
    cluster_action_num = [3,3,3,1]
    kmeans_re_len = len(cluster_action_num)
    action_list_id = photo_id
    leaf["cluster_action_index"] = cluster_action_index
    leaf["cluster_action_num"] = cluster_action_num
    leaf["action_list_id"] = action_list_id

    kmeans_results = {}
    kmeans_results["photo_id"] = [1, 4, 7, 10]
    kmeans_results["author_id"] = [11, 14, 17, 20]
    kmeans_results["play_time"] = [(21+22+23)//3, (24+25+26)//3, (27+28+29)//3, 30]
    kmeans_results["duration"] = [(31+32+33)//3, (34+35+36)//3, (37+38+39)//3, 40]
    kmeans_results["cluster_id"] = [41, 44, 47, 50]
    kmeans_results["timestamp"] = [1650088495 - 24 * 3600, 1650089583, 1650089580, 1650089641]

    leaf.request_time = (1650089641 + 59) * 1000
    leaf.run("test_kmeans_aggregate_extract_fea")
    # time 345-16=329, play 344-16=328, channel 343-16=327, label 342-16=326, profile 325, comment 324
    self.assertEqual(leaf["colossus_signs"], [(329 << 48) | 1,
                                              (328 << 48) | (22 << 24) | 32,
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | 0,
                                              (329 << 48) | 0,
                                              (328 << 48) | (25 << 24) | 35,
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | 0,
                                              (329 << 48) | 0,
                                              (328 << 48) | (28 << 24) | 38,
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | 0,
                                              (329 << 48) | ((1 << 48) - 1),
                                              (328 << 48) | ((1 << 48) - 1),
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | ((1 << 48) - 1)])

    self.assertEqual(leaf["colossus_slots"], [345, 344, 342, 343] * kmeans_re_len)
    self.assertEqual(leaf["colossus_pid_signs"], [1, 4, 7, 10])
    self.assertEqual(leaf["colossus_pid_slots"], [1006] * kmeans_re_len)
    self.assertEqual(leaf["colossus_is_null"], [0.0] * 3 + [-1000.0]*9997)

  def test_kmeans_aggregate_gsu_with_multi_head_index(self):
    kmeans_results = {}
    kmeans_results["photo_id"] = [1, 4, 7, 10]
    kmeans_results["author_id"] = [11, 14, 17, 20]
    kmeans_results["play_time"] = [(21+22+23)//3, (24+25+26)//3, (27+28+29)//3, 30]
    kmeans_results["duration"] = [(31+32+33)//3, (34+35+36)//3, (37+38+39)//3, 40]
    kmeans_results["cluster_id"] = [41, 44, 47, 50]
    kmeans_results["timestamp"] = [1650088495 - 24 * 3600, 1650089583, 1650089580, 1650089641]
    kmeans_re_len = len(kmeans_results["photo_id"])
    sim_item_v3_field_and_sizes = [
      ("photo_id", 8, 'Q'),
      ("author_id", 4, 'I'),
      ("timestamp", 4, 'I'),
      ("duration", 2, 'H'),
      ("play_time", 2, 'H'),
      ("cluster_id", 2, 'H'),
    ]

    item_size = 0
    for (name, size, format_str) in sim_item_v3_field_and_sizes:
      item_size = item_size + size
    buffer = bytearray(item_size * kmeans_re_len)
    offset = 0
    for i in range(kmeans_re_len):
      for (name, size, format_str) in sim_item_v3_field_and_sizes:
        #print(name, i, type(i), mock_items[name])
        struct.pack_into(format_str, buffer, offset, kmeans_results[name][i])
        offset = offset + size
    user_id = 666
    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_colossusLongSimItem", response_json_str=json.dumps(resp))

    flow = GsuFlow(name="test_kmeans_aggregate_gsu_with_multi_head_index")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusLongSimItem',
        client_type='common_item_client',
        output_attr="kmeans_agg_output",
        parse_to_pb=False,
        debug_uids="666",
        print_items=True) \
      .gsu_with_multi_head_index(colossus_output_type="common_item",
                                 colossus_output_attr="kmeans_agg_output",
                                 topn_index_attr="topk_indices",
                                 topn_value_attr="topk_values", # useless
                                 output_item_colossus_pid_attr="gsu_pids",
                                 head_num=4,
                                 top_n=3,
                                 output_sign_attr="gsu_signs",
                                 output_slot_attr="gsu_slots",
                                 colossus_service_name="grpc_colossusLongSimItem",
                                 colossus_photo_id_field_name="photo_id",
                                 colossus_author_id_field_name="author_id",
                                 colossus_play_time_field_name="play_time",
                                 colossus_duration_field_name="duration",
                                 colossus_timestamp_field_name="timestamp",
                                 colossus_tag_field_name="cluster_id",
                                 colossus_channel_field_name="",
                                 colossus_label_field_name="")

    leaf = self.__init_service(flow)

    request_time = (1650089641 + 59) * 1000
    leaf.request_time = request_time

    item1 = leaf.add_item(1)
    item1_topk_indices = [1,0,2,1, 0,3,0,3, 2,0,1,2]
    item1["topk_indices"] = item1_topk_indices

    item2 = leaf.add_item(2)
    item2_topk_indices = [2,1,0,0, 3,1,1,2, 3,0,2,1]
    item2["topk_indices"] = item2_topk_indices
    leaf.run("test_kmeans_aggregate_gsu_with_multi_head_index")

    def unique_indices(indices, top_n):
      indices_set = set()
      k = 0
      for index in indices:
        if index in indices_set:
          continue
        indices_set.add(index)
        yield index
        k += 1
        if k >= top_n:
          break

    def gen_sign(slot, value):
      # kuiba style
      uint_sign = (slot << 54) | (value & ((1 << 54) - 1))
      int_sign = uint_sign if uint_sign < (1 << 63) else (uint_sign - (1 << 64))
      return int_sign

    def gen_signs(index):
      yield gen_sign(26, kmeans_results["photo_id"][index])
      yield gen_sign(128, kmeans_results["author_id"][index])
      yield gen_sign(349, kmeans_results["cluster_id"][index])
      yield gen_sign(348, (kmeans_results["play_time"][index] << 24) | kmeans_results["duration"][index])
      yield gen_sign(350, (request_time // 1000 - kmeans_results["timestamp"][index]) // 86400)
      yield gen_sign(344, (kmeans_results["play_time"][index] << 24) | kmeans_results["duration"][index])
      yield gen_sign(345, (request_time // 1000 - kmeans_results["timestamp"][index]) // 86400)
      yield gen_sign(343, 0)
      yield gen_sign(700, 0)

    self.assertEqual(item1["gsu_signs"], list(itertools.chain.from_iterable(gen_signs(index) for index in unique_indices(item1_topk_indices, 3))))
    self.assertEqual(item1["gsu_slots"], [346, 347, 349, 348, 350, 344, 345, 343, 700] * 3)
    self.assertEqual(item1["gsu_pids"], [kmeans_results["photo_id"][index] for index in unique_indices(item1_topk_indices, 3)])

    self.assertEqual(item2["gsu_signs"], list(itertools.chain.from_iterable(gen_signs(index) for index in unique_indices(item2_topk_indices, 3))))
    self.assertEqual(item2["gsu_slots"], [346, 347, 349, 348, 350, 344, 345, 343, 700] * 3)
    self.assertEqual(item2["gsu_pids"], [kmeans_results["photo_id"][index] for index in unique_indices(item2_topk_indices, 3)])

  def test_gsu_with_explore(self):
    user_id = 666
    item_list_len = 6
    mock_items = {}
    photo_id_list = [1, 2, 3, 4, 5, 6]
    author_id_list = [10, 20, 30, 40, 50, 60]
    duration_list = [100, 200, 300, 400, 500, 600]
    play_time_list = [10, 20, 30, 40, 50, 60]
    tag_list = [21, 22, 23, 24, 25, 26]
    channel_list = [1, 0, 37, 38, 77, 1]
    label_list = [11, 12, 13, 14, 15, 16]
    timestamp_list = [1650088495 - 24 * 3600, 1650088955, 1650089335, 1650089583, 1650089641, 1650089680]

    mock_items["photo_id"] = photo_id_list
    mock_items["author_id"] = author_id_list
    mock_items["duration"] = duration_list
    mock_items["play_time"] = play_time_list
    mock_items["tag"] = tag_list
    mock_items["channel"] = channel_list
    mock_items["label"] = label_list
    mock_items["timestamp"] = timestamp_list
    # field_name, size, python struct format
    # struct format reference: https://docs.python.org/zh-cn/3/library/struct.html
    sim_item_v2_field_and_sizes = [
      ("photo_id", 8, 'Q'),
      ("author_id", 4, 'I'),
      ("duration", 2, 'H'),
      ("play_time", 2, 'H'),
      ("tag", 4, 'I'),
      ("channel", 1, 'B'),
      ("label", 2, 'H'),
      ("timestamp", 4, 'I'),
    ]
    # construct SimItemV2 raw bytes
    item_size = 0
    for (name, size, format_str) in sim_item_v2_field_and_sizes:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in sim_item_v2_field_and_sizes:
        #print(name, i, type(i), mock_items[name])
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size
    # construct CommonItemResponse
    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_colossusSimV2", response_json_str=json.dumps(resp))
    flow = GsuFlow(name="gsu_with_explore")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusSimV2',
        client_type='common_item_client',
        output_attr="sim_v2_colossus_output",
        parse_to_pb=False,
        debug_uids="666",
        print_items=True) \
      .gsu_retriever_with_colossus_resp_v2(
        colossus_resp_attr="sim_v2_colossus_output",
        save_author_id_to_attr="colossus_aid",
        save_duration_to_attr="colossus_duration",
        save_play_time_to_attr="colossus_play",
        save_tag_to_attr="colossus_tag",
        save_timestamp_to_attr="colossus_time",
        save_label_to_attr="colossus_label",
        save_channel_to_attr="colossus_channel",
        save_result_to_common_attr="colossus_pid",
        filter_future_attr=True) \
      .gsu_with_explore(
        item_list_from_attr="colossus_pid",
        colossus_aid_attr="colossus_aid",
        colossus_duration_attr="colossus_duration",
        colossus_play_attr="colossus_play",
        colossus_time_attr="colossus_time",
        colossus_label_attr="colossus_label",
        colossus_tag_attr="colossus_tag",
        colossus_channel_attr="colossus_channel",
        save_explore_pids_to_attr="explore_pids",
        save_explore_aids_to_attr="explore_aids",
        save_explore_labels_to_attr="explore_labels",
        save_explore_plays_to_attr="explore_plays",
        save_explore_timestamps_to_attr="explore_timestamps",
        save_explore_tags_to_attr="explore_tags",
        save_explore_channels_to_attr="explore_channels",
        save_explore_tag_play_avg_to_attr="explore_tag_play_avg",
        save_explore_tag_like_rate_to_attr="explore_tag_like_rate",
        save_explore_tag_follow_rate_to_attr="explore_tag_follow_rate",
        save_explore_tag_forward_rate_to_attr="explore_tag_forward_rate",
        save_explore_tag_hate_rate_to_attr="explore_tag_hate_rate",
        save_explore_tag_comment_rate_to_attr="explore_tag_comment_rate",
        save_explore_tag_enter_profile_rate_to_attr="explore_tag_enter_profile_rate",
        save_explore_tag_enter_comment_rate_to_attr="explore_tag_enter_comment_rate",
        save_search_pids_to_attr="search_pids",
        save_search_aids_to_attr="search_aids",
        save_search_labels_to_attr="search_labels",
        save_search_plays_to_attr="search_plays",
        save_search_timestamps_to_attr="search_timestamps",
        save_search_tags_to_attr="search_tags",
        save_search_tag_play_avg_to_attr="search_tag_play_avg",
        save_search_tag_like_rate_to_attr="search_tag_like_rate",
        save_search_tag_follow_rate_to_attr="search_tag_follow_rate",
        save_search_tag_forward_rate_to_attr="search_tag_forward_rate",
        save_search_tag_hate_rate_to_attr="search_tag_hate_rate",
        save_search_tag_comment_rate_to_attr="search_tag_comment_rate",
        save_search_tag_enter_profile_rate_to_attr="search_tag_enter_profile_rate",
        save_search_tag_enter_comment_rate_to_attr="search_tag_enter_comment_rate") \

    leaf = self.__init_service(flow)
    leaf.request_time = (1650089641 + 59) * 1000
    leaf.run("gsu_with_explore")

    # self.assertEqual(leaf["colossus_signs"], [(329 << 48) | 1,
    #                                           (328 << 48) | (10 << 24) | 100,
    #                                           (326 << 48) | ((1 << 48) - 1),
    #                                           (327 << 48) | 5,
    #                                           (329 << 48) | 0,
    #                                           (328 << 48) | (20 << 24) | 200,
    #                                           (326 << 48) | ((1 << 48) - 1),
    #                                           (327 << 48) | 4,
    #                                           (329 << 48) | 0,
    #                                           (328 << 48) | (30 << 24) | 300,
    #                                           (326 << 48) | ((1 << 48) - 1),
    #                                           (327 << 48) | 3,
    #                                           (329 << 48) | 0,
    #                                           (328 << 48) | (40 << 24) | 400,
    #                                           (326 << 48) | ((1 << 48) - 1),
    #                                           (327 << 48) | 2,
    #                                           (329 << 48) | ((1 << 48) - 1),
    #                                           (328 << 48) | ((1 << 48) - 1),
    #                                           (326 << 48) | ((1 << 48) - 1),
    #                                           (327 << 48) | ((1 << 48) - 1)])

    # self.assertEqual(leaf["colossus_slots"], [345, 344, 342, 343] * 5)
    # self.assertEqual(leaf["colossus_pid_signs"], [1, 2, 3, 4, 5])
    # self.assertEqual(leaf["colossus_pid_slots"], [1006] * 5)
    # self.assertEqual(leaf["colossus_is_null"], [0.0] * 4 + [-1000.0] * 9996)

  def test_parse_bitwise_label(self):
    flow = GsuFlow(name="test_parse_bitwise_label")
    flow \
      .parse_bitwise_label(
        input_attr="label_list",
        bit_configs=[
          dict(
            bit_index=0,
            output_attr="label_0",
          ),
          dict(
            bit_index=1,
            output_attr="label_1",
          )
        ]
      ) \
      .parse_bitwise_label(
        input_attr="item_label",
        is_common=False,
        bit_configs=[
          dict(
            bit_index=0,
            output_attr="item_label_0",
          ),
          dict(
            bit_index=1,
            output_attr="item_label_1",
          )
        ]
      )
    leaf = self.__init_service(flow) 
    label_list = [1, 2, 3, 4, 5]
    leaf["label_list"] = label_list
    for i in range(5):
      item = leaf.add_item(i)
      item["item_label"] = i

    leaf.run("test_parse_bitwise_label")
    self.assertEqual([x & 1 for x in label_list], leaf["label_0"])
    self.assertEqual([(x >> 1) & 1 for x in label_list], leaf["label_1"])
    for item in leaf.items:
      self.assertEqual(item["item_label"] & 1, item["item_label_0"])
      self.assertEqual((item["item_label"] >> 1) & 1, item["item_label_1"])

  def test_extract_feature_from_kmeans_colossus(self):
    user_id = 666
    item_list_len = 5
    mock_items = {}
    photo_id_list = [1, 2, 3, 4, 5]
    mock_items["photo_id"] = photo_id_list
    self.assertEqual(item_list_len, len(photo_id_list))
    author_id_list = [10, 20, 30, 40, 50]
    mock_items["author_id"] = author_id_list
    self.assertEqual(item_list_len, len(author_id_list))
    duration_list = [100, 200, 300, 400, 500]
    mock_items["duration"] = duration_list
    self.assertEqual(item_list_len, len(duration_list))
    play_time_list = [10, 20, 30, 40, 50]
    mock_items["play_time"] = play_time_list
    self.assertEqual(item_list_len, len(play_time_list))
    cluster_id_list = [10, 9, 8, 7, 6]
    mock_items["cluster_id"] = cluster_id_list
    self.assertEqual(item_list_len, len(cluster_id_list))
    timestamp_list = [1650088495 - 24 * 3600, 1650088955, 1650089335, 1650089583, 1650089641]
    mock_items["timestamp"] = timestamp_list
    self.assertEqual(item_list_len, len(timestamp_list))
    # field_name, size, python struct format
    # struct format reference: https://docs.python.org/zh-cn/3/library/struct.html
    sim_item_v2_field_and_sizes = [
      ("photo_id", 8, 'Q'),
      ("author_id", 4, 'I'),
      ("timestamp", 4, 'I'),
      ("duration", 2, 'H'),
      ("play_time", 2, 'H'),
      ("cluster_id", 2, 'H'),
    ]
    # construct SimItemV2 raw bytes
    item_size = 0
    for (name, size, format_str) in sim_item_v2_field_and_sizes:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in sim_item_v2_field_and_sizes:
        #print(name, i, type(i), mock_items[name])
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size
    # construct CommonItemResponse
    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_colossusLongSimItem", response_json_str=json.dumps(resp))
    flow = GsuFlow(name="extract_feature_from_kmeans_colossus")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusLongSimItem',
        client_type='common_item_client',
        output_attr="sim_v2_colossus_output",
        parse_to_pb=False,
        debug_uids="666",
        print_items=True) \
      .extract_feature_from_kmeans_colossus(
        colossus_output="sim_v2_colossus_output",
        output_signs_attr="colossus_signs",
        output_slots_attr="colossus_slots",
        output_pid_signs_attr="colossus_pid_signs",
        output_pid_slots_attr="colossus_pid_slots",
        cluster_size_attr="cluster_size_list",
        item_slot_id=1006,
        timediff_bias_slot_id=445,
        play_bias_slot_id=444,
        #colossus_item_num_attr="item_num",
        output_mask_bias_attr="colossus_is_null",
        colossus_output_type="common_item",
        colossus_service_name="grpc_colossusLongSimItem",
        colossus_photo_id_field_name="photo_id",
        colossus_author_id_field_name="author_id",
        colossus_play_time_field_name="play_time",
        colossus_duration_field_name="duration",
        colossus_timestamp_field_name="timestamp",
        colossus_tag_field_name="cluster_id",
        colossus_channel_field_name="",
        colossus_label_field_name="") \

    leaf = self.__init_service(flow)
    leaf.request_time = (1650089641 + 59) * 1000
    leaf.run("extract_feature_from_kmeans_colossus")

    self.assertEqual(leaf["colossus_signs"], [(429 << 48) | 1,
                                              (428 << 48) | (10 << 24) | 100,
                                              (429 << 48) | 0,
                                              (428 << 48) | (20 << 24) | 200,
                                              (429 << 48) | 0,
                                              (428 << 48) | (30 << 24) | 300,
                                              (429 << 48) | 0,
                                              (428 << 48) | (40 << 24) | 400,
                                              (429 << 48) | 0,
                                              (428 << 48) | (50 << 24) | 500])

    self.assertEqual(leaf["colossus_slots"], [445, 444] * 5)
    self.assertEqual(leaf["colossus_pid_signs"], [1, 2, 3, 4, 5])
    self.assertEqual(leaf["colossus_pid_slots"], [1006] * 5)
    self.assertEqual(leaf["cluster_size_list"], [10.0, 9.0, 8.0, 7.0, 6.0] + [0.0] * 9995)
    self.assertEqual(leaf["colossus_is_null"], [0.0] * 5 + [-1000.0] * 9995)
  def test_colossus_concat_realtime_behavior(self):
    # define flow
    flow = GsuFlow(name="test_colossus_concat_realtime_behavior")\
                  .colossus_concat_realtime_behavior(user_profile_attr="user_profile",
                                        colossus_input_attr="colossus_output",
                                        colossus_output_attr="colossus_output_new",
                                        )

    # mock running data

    # run test
    leaf = self.__init_service(flow)
    leaf["nonsense"] = 1
    leaf.run("test_colossus_concat_realtime_behavior")

    # assert
    self.assertEqual(leaf["nonsense"], 1)

  def test_gsu_calculate_colossus_length_enricher(self):
    colossus_items = [("photo_id", 8, 'Q'),
                      ("author_id", 4, 'I'),
                      ("timestamp", 4, 'I'),
                      ("duration", 2, 'H'),
                      ("play_time", 2, 'H'),
                      ("cluster_id", 2, 'H'),
                      ]
    item_size = 0
    user_id = 666
    colossus_item_num = 20
    cluster_num = 700
    cur_timestamp = int(time.time() - 60)  # second timestamp
    mock_items = {}
    import numpy as np
    mock_items["photo_id"] = np.random.randint(100000, 1000000000, size = colossus_item_num)
    mock_items["author_id"] = np.random.randint(1000, 10000000, size = colossus_item_num)
    mock_items["timestamp"] = np.random.randint(cur_timestamp-100*86400, cur_timestamp, size = colossus_item_num)
    mock_items["duration"] = np.random.randint(10, 1000, size = colossus_item_num)
    mock_items["play_time"] = np.random.randint(1, 1000, size = colossus_item_num)
    mock_items["cluster_id"] = np.random.randint(0, 999, size = colossus_item_num)
    for (name, size, format_str) in colossus_items:
      item_size = item_size + size
    buffer = bytearray(item_size * colossus_item_num)
    offset = 0
    for i in range(colossus_item_num):
      for (name, size, format_str) in colossus_items:
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size

    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    mock_colossus_service = "grpc_colossusLongSimItem"
    rpc_mocker.mock_rpc_response(service_name=mock_colossus_service, response_json_str=json.dumps(resp))
    # rpc_mocker = self.__service.rpc_mocker()
    # rpc_mocker.mock_rpc_response(service_name=mock_colossus_service, response_json_str=json.dumps(colossus_items.serialize()))
    # define flow
    flow = GsuFlow(name="test_gsu_calculate_colossus_length_enricher")\
          .colossus(
            service_name=mock_colossus_service,
            client_type='common_item_client',
            output_attr="sim_v2_colossus_output",
            parse_to_pb=False,
            debug_uids="666",
            print_items=False,
          ) \
          .gsu_calculate_colossus_length_enricher(colossus_resp_attr = "sim_v2_colossus_output",
                    colossus_service_name = mock_colossus_service,
                    output_attr_name = "output_length",
                    colossus_is_string = False) \

    # run test
    leaf = self.__init_service(flow)
    leaf.request_time = int(time.time() * 1000)
    # for i in range(1):
    #   item = leaf.add_item(i+1)
    #   item["icluster_id"] = [random.randint(0, cluster_num-1)]
    leaf.run("test_gsu_calculate_colossus_length_enricher")
    # assert
    self.assertEqual(leaf["output_length"], colossus_item_num)

  def test_gsu_calculate_colossusv2_length_enricher(self):
    client_kconf = 'colossus.kconf_client.video_item'
    user_id = 666
    mock_items = {
      "photo_id": [1, 2, 3],
      "author_id": [4, 5, 6],
      "duration": [10, 11, 12],
      "play_time": [13, 14, 15],
      "tag": [16, 17, 18],
      "channel": [16, 17, 18],
      "label": [19, 20, 21],
      "timestamp": [22, 23, 24]
    }
    item_list_len = len(next(iter(mock_items.values())))
    field_infos = self._get_colossusdb_sim_field_infos(client_kconf, mock_items)
    resp = self._serialize_colossusdb_sim_data(item_list_len, mock_items, field_infos)
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_clsdb_sim-video-2_short-video",
                                 response_json_str=json.dumps(resp))
    flow = GsuFlow(name="test_gsu_calculate_colossusv2_length_enricher") \
      .gsu_common_colossusv2_enricher(kconf=client_kconf,
                                      reflection_output_attr="video_item_reflection",
                                      item_datas_output_attr="video_item_data",
                                      item_fields=dict(photo_id="photo_id_list",
                                                       author_id="author_id_list",
                                                       duration="durations_list",
                                                       play_time="play_time_list",
                                                       tag="tag_list",
                                                       channel="channel_list",
                                                       label="label_list",
                                                       timestamp="timestamp_list")) \
      .gsu_calculate_colossusv2_length_enricher(colossus_resp_attr="video_item_data",
                                                colossus_resp_type="colossusv2",
                                                reflection_attr_name="video_item_reflection",
                                                output_attr_name="output_length") \
      .debug_log(common_attrs=["output_length"])
    # run test
    leaf = self.__init_service(flow)
    leaf.user_id = user_id
    leaf.run("test_gsu_calculate_colossusv2_length_enricher")
    # assert
    self.assertEqual(leaf["output_length"], 3)

  def test_extract_interval_feature_from_colossus_response(self):
    user_id = 666
    item_list_len = 5
    mock_items = {}
    photo_id_list = [1, 2, 3, 4, 5]
    mock_items["photo_id"] = photo_id_list
    self.assertEqual(item_list_len, len(photo_id_list))
    author_id_list = [10, 20, 30, 40, 50]
    mock_items["author_id"] = author_id_list
    self.assertEqual(item_list_len, len(author_id_list))
    duration_list = [100, 200, 300, 400, 500]
    mock_items["duration"] = duration_list
    self.assertEqual(item_list_len, len(duration_list))
    play_time_list = [10, 20, 30, 40, 50]
    mock_items["play_time"] = play_time_list
    self.assertEqual(item_list_len, len(play_time_list))
    tag_list = [21, 22, 23, 24, 25]
    mock_items["tag"] = tag_list
    self.assertEqual(item_list_len, len(tag_list))
    channel_list = [5, 4, 3, 2, 1]
    mock_items["channel"] = channel_list
    self.assertEqual(item_list_len, len(channel_list))
    label_list = [11, 12, 13, 14, 15]
    mock_items["label"] = label_list
    self.assertEqual(item_list_len, len(label_list))
    timestamp_list = [1693815432 - 24 * 3600, 1691815432, 1613815432, 1693815432 + 3600, 1650089641]
    mock_items["timestamp"] = timestamp_list
    self.assertEqual(item_list_len, len(timestamp_list))
    user_latitude_list = [0.0, 0.0, 0.0, 0.0, 0.0]
    mock_items["user_latitude"] = user_latitude_list
    self.assertEqual(item_list_len, len(user_latitude_list))
    user_longitude_list = [0.0, 0.0, 0.0, 0.0, 0.0]
    mock_items["user_longitude"] = user_longitude_list
    self.assertEqual(item_list_len, len(user_longitude_list))
    photo_latitude_list = [0.0, 0.0, 0.0, 0.0, 0.0]
    mock_items["photo_latitude"] = photo_latitude_list
    self.assertEqual(item_list_len, len(photo_latitude_list))
    photo_longitude_list = [0.0, 0.0, 0.0, 0.0, 0.0]
    mock_items["photo_longitude"] = photo_longitude_list
    self.assertEqual(item_list_len, len(photo_longitude_list))
    # field_name, size, python struct format
    # struct format reference: https://docs.python.org/zh-cn/3/library/struct.html
    sim_item_v2_field_and_sizes = [
      ("photo_id", 8, 'Q'),
      ("author_id", 4, 'I'),
      ("duration", 2, 'H'),
      ("play_time", 2, 'H'),
      ("tag", 4, 'I'),
      ("channel", 1, 'B'),
      ("label", 2, 'H'),
      ("timestamp", 4, 'I'),
      ("user_latitude", 4, 'f'),
      ("user_longitude", 4, 'f'),
      ("photo_latitude", 4, 'f'),
      ("photo_longitude", 4, 'f')
    ]
    # construct SimItemV2 raw bytes
    item_size = 0
    for (name, size, format_str) in sim_item_v2_field_and_sizes:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in sim_item_v2_field_and_sizes:
        #print(name, i, type(i), mock_items[name])
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size
    # construct CommonItemResponse
    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_colossusSimV2", response_json_str=json.dumps(resp))
    flow = GsuFlow(name="extract_interval_feature_from_colossus_response")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusSimV2',
        client_type='common_item_client',
        output_attr="sim_v2_colossus_output",
        parse_to_pb=False,
        debug_uids="666",
        print_items=True) \
      .extract_interval_feature_from_colossus_response(
        colossus_output="sim_v2_colossus_output",
        colossus_output_type="common_item",
        output_signs_attr="colossus_day_interval_signs",
        output_slots_attr="colossus_day_interval_slots",
        output_pid_signs_attr="colossus_day_interval_pid_signs",
        output_pid_slots_attr="colossus_day_interval_pid_slots",
        output_aid_signs_attr="colossus_day_interval_aid_signs",
        output_aid_slots_attr="colossus_day_interval_aid_slots",
        item_slot_id=1900,
        author_slot_id=1901,
        timediff_slot_id=1902,
        play_slot_id=1903,
        label_slot_id=1904,
        channel_slot_id=1905,
        profile_stay_time_slot_id=1906,
        comment_stay_time_slot_id=1907,
        profile_feed_mode_stay_time_slot_id_=1908,
        real_show_index_slot_id=1909,
        dist_slot_id=1910,
        tag_slot_id=1911,
        mio_slot_type=1,
        mio_author_slot_id=999,
        colossus_service_name="grpc_colossusSimV2",
        extract_label_fea_switch=True,
        interval_point_offset_s=0,
        interval_every_k_s=3600*24,
        interval_lower_bound_s=5400,
        interval_upper_bound_s=5400,
        interval_latest_offset_s=3600*10,
        interval_oldest_offset_s=3600*24*30,
        interval_bound_max_nums=40,
        interval_total_max_nums=60
      )

    leaf = self.__init_service(flow)
    leaf.request_time = (1693815432 + 60) * 1000
    leaf.run("extract_interval_feature_from_colossus_response")

    self.assertEqual(leaf["colossus_day_interval_signs"], [(334 << 48) | 1,
                                              (332 << 48) | (10 << 24) | 100,
                                              (336 << 48) | 11,
                                              (335 << 48) | 5,
                                              (1910 << 48) | (1 << 24 | 60),
                                              (333 << 48) | 21])

    self.assertEqual(leaf["colossus_day_interval_slots"], [1902, 1903, 1904, 1905, 1910, 1911] * 1)
    self.assertEqual(leaf["colossus_day_interval_pid_signs"], [((346 - 16) << 48) | 1])
    self.assertEqual(leaf["colossus_day_interval_pid_slots"], [1900] * 1)
    self.assertEqual(leaf["colossus_day_interval_aid_signs"], [(999 << 48) | 10])
    self.assertEqual(leaf["colossus_day_interval_aid_slots"], [1901] * 1)

  def test_extract_user_stat_feature_from_colossus_response(self):
    user_id = 666
    item_list_len = 5
    mock_items = {}
    photo_id_list = [1, 2, 3, 4, 5]
    mock_items["photo_id"] = photo_id_list
    self.assertEqual(item_list_len, len(photo_id_list))
    author_id_list = [10, 20, 30, 40, 50]
    mock_items["author_id"] = author_id_list
    self.assertEqual(item_list_len, len(author_id_list))
    duration_list = [100, 200, 300, 400, 500]
    mock_items["duration"] = duration_list
    self.assertEqual(item_list_len, len(duration_list))
    play_time_list = [10, 20, 30, 40, 50]
    mock_items["play_time"] = play_time_list
    self.assertEqual(item_list_len, len(play_time_list))
    tag_list = [21, 22, 23, 24, 25]
    mock_items["tag"] = tag_list
    self.assertEqual(item_list_len, len(tag_list))
    channel_list = [5, 4, 3, 2, 1]
    mock_items["channel"] = channel_list
    self.assertEqual(item_list_len, len(channel_list))
    label_list = [11, 12, 13, 14, 15]
    mock_items["label"] = label_list
    self.assertEqual(item_list_len, len(label_list))
    timestamp_list = [1693815432 - 24 * 3600, 1691815432, 1613815432, 1693815432 + 3600, 1650089641]
    mock_items["timestamp"] = timestamp_list
    self.assertEqual(item_list_len, len(timestamp_list))
    user_latitude_list = [0.0, 0.0, 0.0, 0.0, 0.0]
    mock_items["user_latitude"] = user_latitude_list
    self.assertEqual(item_list_len, len(user_latitude_list))
    user_longitude_list = [0.0, 0.0, 0.0, 0.0, 0.0]
    mock_items["user_longitude"] = user_longitude_list
    self.assertEqual(item_list_len, len(user_longitude_list))
    photo_latitude_list = [0.0, 0.0, 0.0, 0.0, 0.0]
    mock_items["photo_latitude"] = photo_latitude_list
    self.assertEqual(item_list_len, len(photo_latitude_list))
    photo_longitude_list = [0.0, 0.0, 0.0, 0.0, 0.0]
    mock_items["photo_longitude"] = photo_longitude_list
    self.assertEqual(item_list_len, len(photo_longitude_list))
    # field_name, size, python struct format
    # struct format reference: https://docs.python.org/zh-cn/3/library/struct.html
    sim_item_v2_field_and_sizes = [
      ("photo_id", 8, 'Q'),
      ("author_id", 4, 'I'),
      ("duration", 2, 'H'),
      ("play_time", 2, 'H'),
      ("tag", 4, 'I'),
      ("channel", 1, 'B'),
      ("label", 2, 'H'),
      ("timestamp", 4, 'I'),
      ("user_latitude", 4, 'f'),
      ("user_longitude", 4, 'f'),
      ("photo_latitude", 4, 'f'),
      ("photo_longitude", 4, 'f')
    ]
    # construct SimItemV2 raw bytes
    item_size = 0
    for (name, size, format_str) in sim_item_v2_field_and_sizes:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in sim_item_v2_field_and_sizes:
        #print(name, i, type(i), mock_items[name])
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size
    # construct CommonItemResponse
    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_colossusSimV2", response_json_str=json.dumps(resp))
    flow = GsuFlow(name="extract_user_stat_feature_from_colossus_response")
    flow.user_id = user_id
    flow\
      .colossus(
        service_name='grpc_colossusSimV2',
        client_type='common_item_client',
        output_attr="sim_v2_colossus_output",
        parse_to_pb=False,
        debug_uids="666",
        print_items=True) \
      .extract_user_stat_feature_from_colossus_response(
        colossus_output="sim_v2_colossus_output",
        colossus_output_type="common_item",
        output_values_attr="stat_values",
        output_dura1_attr="stat_dura1",
        output_dura2_attr="stat_dura2",
        output_dura3_attr="stat_p75_index",
        output_perc_attr="stat_percentile"
      )

    leaf = self.__init_service(flow)
    leaf.request_time = (1693815432 + 60) * 1000
    leaf.run("extract_user_stat_feature_from_colossus_response")

    self.assertNotIn(-1, leaf["stat_values"])
    self.assertNotIn(-1, leaf["stat_dura1"])
    self.assertNotIn(61, leaf["stat_dura1"])
    self.assertNotIn(-1, leaf["stat_dura2"])
    self.assertNotIn(-1, leaf["stat_p75_index"])
    self.assertNotIn(602, leaf["stat_p75_index"])
    self.assertNotIn(-1, leaf["stat_percentile"])

  def test_gsu_concat_attr_enricher(self):
    colossus_items = ColossusSimItemList(user_id=666,
                                         schema= [("photo_id", 8, 'Q'),
                                                  ("author_id", 4, 'I'),
                                                  ("timestamp", 4, 'I'),
                                                  ("duration", 2, 'H'),
                                                  ("play_time", 2, 'H'),
                                                  ("cluster_id", 2, 'H'),
                                                  ],
                                         )
    colossus_item_num = 20
    cur_timestamp = int(time.time() - 60)  # second timestamp

    for i in range(colossus_item_num):
      colossus_items.add_item(dict(
        photo_id=random.randint(100000, 1000000000),
        author_id=random.randint(1000, 100000000),
        timestamp=random.randint(cur_timestamp-100*86400, cur_timestamp),
        duration=random.randint(10, 1000),
        play_time=random.randint(1, 1000),
        cluster_id=random.randint(0, 999),
      ))

    mock_colossus_service = "grpc_colossusLongSimItem"
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name=mock_colossus_service, response_json_str=json.dumps(colossus_items.serialize()))
    # define flow
    flow = GsuFlow(name="test_gsu_concat_attr_enricher")\
         .colossus(
            service_name=mock_colossus_service,
            client_type='common_item_client',
            output_attr="sim_v2_colossus_output",
            parse_to_pb=False,
            debug_uids="666",
            print_items=False,
          ) \
          .colossus(
            service_name=mock_colossus_service,
            client_type='common_item_client',
            output_attr="sim_v2_colossus_output_v1",
            parse_to_pb=False,
            debug_uids="666",
            print_items=False,
          ) \
          .gsu_concat_attr_enricher(colossus_concat_attr = ["sim_v2_colossus_output", "sim_v2_colossus_output_v1"],
              colossus_service_name = mock_colossus_service,
              output_attr_name = "sim_colossus_output",
              max_resp_item_num = 10000,
              colossus_is_string = False) \
          .gsu_calculate_colossus_length_enricher(colossus_resp_attr = "sim_colossus_output",
            colossus_service_name = mock_colossus_service,
            output_attr_name = "output_length",
            colossus_is_string = False) \

    # run test
    leaf = self.__init_service(flow)
    # leaf["nonsense"] = 1
    leaf.run("test_gsu_concat_attr_enricher")
    # self.assertEqual(leaf["nonsense"], 1)
    # assert
    self.assertEqual(leaf["output_length"], colossus_item_num * 2)

  def test_gsu_concat_colossusv2_enricher(self):
    client_kconf = 'colossus.kconf_client.video_item'
    user_id = 666
    mock_items = {
      "photo_id": [1, 2, 3],
      "author_id": [4, 5, 6],
      "duration": [10, 11, 12],
      "play_time": [13, 14, 15],
      "tag": [16, 17, 18],
      "channel": [16, 17, 18],
      "label": [19, 20, 21],
      "timestamp": [22, 23, 24]
    }
    item_list_len = len(next(iter(mock_items.values())))
    field_infos = self._get_colossusdb_sim_field_infos(client_kconf, mock_items)
    resp = self._serialize_colossusdb_sim_data(item_list_len, mock_items, field_infos)
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_clsdb_sim-video-2_short-video",
                                 response_json_str=json.dumps(resp))
    flow = GsuFlow(name="test_gsu_concat_colossusv2_enricher") \
      .gsu_common_colossusv2_enricher(kconf=client_kconf,
                                      reflection_output_attr="video_item_reflection",
                                      item_datas_output_attr="video_item_data_1",
                                      item_fields=dict(photo_id="photo_id_list",
                                                       author_id="author_id_list",
                                                       duration="durations_list",
                                                       play_time="play_time_list",
                                                       tag="tag_list",
                                                       channel="channel_list",
                                                       label="label_list",
                                                       timestamp="timestamp_list")) \
      .gsu_concat_colossusv2_enricher(colossus_concat_attr=['video_item_data_1'],
                                      reflection_attr_name='video_item_reflection',
                                      max_resp_item_num=10000,
                                      output_attr_name='concat_video_1') \
      .gsu_calculate_colossusv2_length_enricher(colossus_resp_attr="concat_video_1",
                                                colossus_resp_type="concat_video_item",
                                                output_attr_name="output_length_1") \
      .gsu_common_colossusv2_enricher(kconf=client_kconf,
                                      reflection_output_attr="video_item_reflection",
                                      item_datas_output_attr="video_item_data_2",
                                      item_fields=dict(photo_id="photo_id_list",
                                                       author_id="author_id_list",
                                                       duration="durations_list",
                                                       play_time="play_time_list",
                                                       tag="tag_list",
                                                       channel="channel_list",
                                                       label="label_list",
                                                       timestamp="timestamp_list")) \
      .gsu_common_colossusv2_enricher(kconf=client_kconf,
                                      reflection_output_attr="video_item_reflection",
                                      item_datas_output_attr="video_item_data_3",
                                      item_fields=dict(photo_id="photo_id_list",
                                                       author_id="author_id_list",
                                                       duration="durations_list",
                                                       play_time="play_time_list",
                                                       tag="tag_list",
                                                       channel="channel_list",
                                                       label="label_list",
                                                       timestamp="timestamp_list")) \
      .gsu_common_colossusv2_enricher(kconf=client_kconf,
                                      reflection_output_attr="video_item_reflection",
                                      item_datas_output_attr="video_item_data_4",
                                      item_fields=dict(photo_id="photo_id_list",
                                                       author_id="author_id_list",
                                                       duration="durations_list",
                                                       play_time="play_time_list",
                                                       tag="tag_list",
                                                       channel="channel_list",
                                                       label="label_list",
                                                       timestamp="timestamp_list")) \
      .gsu_concat_colossusv2_enricher(colossus_concat_attr=['video_item_data_2', 'video_item_data_3', 'video_item_data_4'],
                                      reflection_attr_name='video_item_reflection',
                                      print_concat_result=True,
                                      max_resp_item_num=10000,
                                      output_attr_name='concat_video_2') \
      .gsu_calculate_colossusv2_length_enricher(colossus_resp_attr="concat_video_2",
                                                colossus_resp_type="concat_video_item",
                                                output_attr_name="output_length_2") \
      .gsu_concat_colossusv2_enricher(colossus_concat_attr=['video_item_data_2', 'video_item_data_3', 'video_item_data_4'],
                                      reflection_attr_name='video_item_reflection',
                                      max_resp_item_num=7,
                                      print_concat_result=True,
                                      output_attr_name='concat_video_3') \
      .gsu_calculate_colossusv2_length_enricher(colossus_resp_attr="concat_video_3",
                                                colossus_resp_type="concat_video_item",
                                                output_attr_name="output_length_3") \
      .gsu_concat_colossusv2_enricher(colossus_concat_attr=['video_item_data_2', 'video_item_data_3', 'video_item_data_4'],
                                      reflection_attr_name='video_item_reflection',
                                      max_resp_item_num=7,
                                      print_concat_result=True,
                                      truncate_from_start=False,
                                      output_attr_name='concat_video_4') \
      .gsu_calculate_colossusv2_length_enricher(colossus_resp_attr="concat_video_4",
                                                colossus_resp_type="concat_video_item",
                                                output_attr_name="output_length_4") \
      .debug_log(common_attrs=["output_length_1","output_length_2","output_length_3","output_length_4"])
    # run test
    leaf = self.__init_service(flow)
    leaf.user_id = user_id
    leaf.run("test_gsu_concat_colossusv2_enricher")
    # assert
    self.assertEqual(leaf["output_length_1"], 3)
    self.assertEqual(leaf["output_length_2"], 9)
    self.assertEqual(leaf["output_length_3"], 7)
    self.assertEqual(leaf["output_length_4"], 7)

  def test_gsu_with_multi_head_index_using_colossusv2(self):
    user_id = 666
    item_list_len = 10
    photo_id_list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    author_id_list = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
    duration_list = [100, 200, 300, 400, 500, 600, 700, 800, 900, 1000]
    play_time_list = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
    tag_list = [21, 22, 23, 24, 25, 26, 27, 28, 29, 30]
    channel_list = [10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
    label_list = [11, 12, 13, 14, 15, 16, 17, 18, 19, 20]
    timestamp_list = [1650088495 - 24 * 3600, 1650088955, 1650089335, 1650089583, 1650089641, 1650089741, 1650089872, 1650090001, 1650092001, 1650098001]
    client_kconf = 'colossus.kconf_client.video_item'
    mock_items = {
      "photo_id": photo_id_list,
      "author_id": author_id_list,
      "duration": duration_list,
      "play_time": play_time_list,
      "tag": tag_list,
      "channel": channel_list,
      "label": label_list,
      "timestamp": timestamp_list
    }
    item_list_len = len(next(iter(mock_items.values())))
    field_infos = self._get_colossusdb_sim_field_infos(client_kconf, mock_items)
    resp = self._serialize_colossusdb_sim_data(item_list_len, mock_items, field_infos)
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_clsdb_sim-video-2_short-video",
                                 response_json_str=json.dumps(resp))
    flow = GsuFlow(name="extract_gsu_with_multi_head_index_using_colossusv2")
    flow.user_id = user_id
    flow\
      .gsu_common_colossusv2_enricher(kconf=client_kconf,
                                      reflection_output_attr="video_item_reflection",
                                      item_datas_output_attr="video_item_data",
                                      item_fields=dict(photo_id="photo_id_list",
                                                       author_id="author_id_list",
                                                       duration="durations_list",
                                                       play_time="play_time_list",
                                                       tag="tag_list",
                                                       channel="channel_list",
                                                       label="label_list",
                                                       timestamp="timestamp_list")) \
      .gsu_concat_colossusv2_enricher(colossus_concat_attr=['video_item_data'],
                                      reflection_attr_name='video_item_reflection',
                                      max_resp_item_num=10000,
                                      print_concat_result=True,
                                      output_attr_name='concat_video_item_data') \
      .gsu_calculate_colossusv2_length_enricher(colossus_resp_attr="concat_video_item_data",
                                                colossus_resp_type="concat_video_item",
                                                output_attr_name="output_length") \
      .gsu_with_multi_head_index(colossus_output_type="colossusv2",
                                 colossus_output_attr="concat_video_item_data",
                                 topn_index_attr="topk_indices",
                                 topn_value_attr="topk_values", # useless
                                 output_item_colossus_pid_attr="gsu_pids",
                                 head_num=3,
                                 top_n=6,
                                 output_sign_attr="gsu_signs",
                                 output_slot_attr="gsu_slots") \
      .debug_log(common_attrs=["output_length"],
                 item_attrs = ["gsu_signs","gsu_slots","gsu_pids"])

    leaf = self.__init_service(flow)
    request_time = (1650098001 + 59) * 1000
    leaf.request_time = request_time
    leaf.user_id = user_id
    item1 = leaf.add_item(1)
    item1_topk_indices = [1, 0, 2, 4, 5, 8, 3, 2, 1, 0, 7, 3, 6, 8, 2, 0]
    item1["topk_indices"] = item1_topk_indices

    item2 = leaf.add_item(2)
    item2_topk_indices = [2, 1, 0, 0, 3, 7, 5, 4, 6, 2, 7, 0, 1, 8, 5, 4]
    item2["topk_indices"] = item2_topk_indices
    leaf.run("extract_gsu_with_multi_head_index_using_colossusv2")
    self.assertEqual(leaf["output_length"], 10)

    def unique_indices(indices, top_n):
      indices_set = set()
      k = 0
      for index in indices:
        if index in indices_set:
          continue
        indices_set.add(index)
        yield index
        k += 1
        if k >= top_n:
          break

    def gen_sign(slot, value):
      # kuiba style
      uint_sign = (slot << 54) | (value & ((1 << 54) - 1))
      int_sign = uint_sign if uint_sign < (1 << 63) else (uint_sign - (1 << 64))
      return int_sign

    def gen_signs(index):
      yield gen_sign(26, photo_id_list[index])
      yield gen_sign(128, author_id_list[index])
      yield gen_sign(349, tag_list[index])
      yield gen_sign(348, (play_time_list[index] << 24) | duration_list[index])
      yield gen_sign(350, (request_time // 1000 - timestamp_list[index]) // 86400)
      yield gen_sign(344, (play_time_list[index] << 24) | duration_list[index])
      yield gen_sign(345, (request_time // 1000 - timestamp_list[index]) // 86400)
      yield gen_sign(343, channel_list[index])
      yield gen_sign(700, channel_list[index])

    self.assertEqual(item1["gsu_signs"], list(itertools.chain.from_iterable(gen_signs(index) for index in unique_indices(item1_topk_indices, 6))))
    self.assertEqual(item1["gsu_slots"], [346, 347, 349, 348, 350, 344, 345, 343, 700] * 6)
    self.assertEqual(item1["gsu_pids"], [photo_id_list[index] for index in unique_indices(item1_topk_indices, 6)])

    self.assertEqual(item2["gsu_signs"], list(itertools.chain.from_iterable(gen_signs(index) for index in unique_indices(item2_topk_indices, 6))))
    self.assertEqual(item2["gsu_slots"], [346, 347, 349, 348, 350, 344, 345, 343, 700] * 6)
    self.assertEqual(item2["gsu_pids"], [photo_id_list[index] for index in unique_indices(item2_topk_indices, 6)])

  def test_extract_feature_from_colossus_response_using_colossusv2(self):
    user_id = 666
    item_list_len = 5
    photo_id_list = [1, 2, 3, 4, 5]
    author_id_list = [10, 20, 30, 40, 50]
    duration_list = [100, 200, 300, 400, 500]
    play_time_list = [10, 20, 30, 40, 50]
    tag_list = [21, 22, 23, 24, 25]
    channel_list = [5, 4, 3, 2, 1]
    label_list = [11, 12, 13, 14, 15]
    timestamp_list = [1650088495 - 24 * 3600, 1650088955, 1650089335, 1650089583, 1650089641]
    client_kconf = 'colossus.kconf_client.video_item'
    mock_items = {
      "photo_id": photo_id_list,
      "author_id": author_id_list,
      "duration": duration_list,
      "play_time": play_time_list,
      "tag": tag_list,
      "channel": channel_list,
      "label": label_list,
      "timestamp": timestamp_list
    }
    item_list_len = len(next(iter(mock_items.values())))
    field_infos = self._get_colossusdb_sim_field_infos(client_kconf, mock_items)
    resp = self._serialize_colossusdb_sim_data(item_list_len, mock_items, field_infos)
    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_clsdb_sim-video-2_short-video",
                                 response_json_str=json.dumps(resp))
    flow = GsuFlow(name="extract_feature_from_colossus_response_using_colossusv2")
    flow.user_id = user_id
    flow\
      .gsu_common_colossusv2_enricher(kconf=client_kconf,
                                      reflection_output_attr="video_item_reflection",
                                      item_datas_output_attr="video_item_data",
                                      item_fields=dict(photo_id="photo_id_list",
                                                       author_id="author_id_list",
                                                       duration="durations_list",
                                                       play_time="play_time_list",
                                                       tag="tag_list",
                                                       channel="channel_list",
                                                       label="label_list",
                                                       timestamp="timestamp_list")) \
      .gsu_concat_colossusv2_enricher(colossus_concat_attr=['video_item_data'],
                                      reflection_attr_name='video_item_reflection',
                                      max_resp_item_num=10000,
                                      print_concat_result=True,
                                      output_attr_name='concat_video_item_data') \
      .gsu_calculate_colossusv2_length_enricher(colossus_resp_attr="concat_video_item_data",
                                                colossus_resp_type="concat_video_item",
                                                output_attr_name="output_length") \
      .extract_feature_from_colossus_response(
        colossus_output="concat_video_item_data",
        output_signs_attr="colossus_signs",
        output_slots_attr="colossus_slots",
        output_pid_signs_attr="colossus_pid_signs",
        output_pid_slots_attr="colossus_pid_slots",
        item_slot_id=1006,
        timediff_bias_slot_id=345,
        play_bias_slot_id=344,
        label_bias_slot_id=342,
        channel_bias_slot_id=343,
        #colossus_item_num_attr="item_num",
        output_mask_bias_attr="colossus_is_null",
        colossus_output_type="colossusv2") \
      .debug_log(common_attrs=["output_length", 'colossus_signs', 'colossus_slots', 'colossus_pid_signs', 'colossus_pid_slots', 'colossus_is_null'])

    leaf = self.__init_service(flow)
    leaf.user_id = user_id
    leaf.request_time = (1650089641 + 59) * 1000
    leaf.run("extract_feature_from_colossus_response_using_colossusv2")
    self.assertEqual(leaf["output_length"], 5)
    self.assertEqual(leaf["colossus_signs"], [(329 << 48) | 1,
                                              (328 << 48) | (10 << 24) | 100,
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | 5,
                                              (329 << 48) | 0,
                                              (328 << 48) | (20 << 24) | 200,
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | 4,
                                              (329 << 48) | 0,
                                              (328 << 48) | (30 << 24) | 300,
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | 3,
                                              (329 << 48) | 0,
                                              (328 << 48) | (40 << 24) | 400,
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | 2,
                                              (329 << 48) | ((1 << 48) - 1),
                                              (328 << 48) | ((1 << 48) - 1),
                                              (326 << 48) | ((1 << 48) - 1),
                                              (327 << 48) | ((1 << 48) - 1)])

    self.assertEqual(leaf["colossus_slots"], [345, 344, 342, 343] * 5)
    self.assertEqual(leaf["colossus_pid_signs"], [1, 2, 3, 4, 5])
    self.assertEqual(leaf["colossus_pid_slots"], [1006] * 5)
    self.assertEqual(leaf["colossus_is_null"], [0.0] * 4 + [-1000.0] * 9996)

  def test_sim_compute_offload(self):
    item_id = [10, 11, 12, 13, 14, 15, 16, 17]
    item_type = [0, 2, 0, 0, 1, 1, 1, 2]

    # data for mock response
    int_list = [4, 3, 132, 4, 1, 23, 34, 4]
    double_list = [0.3, 23, 0.199999, 0.234, 0.56, 0.56, 4.23, -5.4]
    string_list = ["rabbit", "poet", "back", "cake", "reader", "hospital", "atomic", "tall"]

    packed_int_list = [[-1, 3, 4], [67], [], [2, 3], [5],[-3], [], [789]]
    packed_double_list = [[0.66], [1, 3, 4], [], [0.56], [], [657], [4.0, 3, 21], [789.999]]
    packed_string_list = [["for"], ["boat", "sea", "paid"], ["factor"], ["weak"], \
                          ["swung"], ["ourselves", "blue"], ["about"], ["dropped"]]

    # construct mock response
    resp = dict(
      item = [],
      common_attr = [],
      item_attr = {}
    )
    for i in range(len(item_id)):
      item_attr = dict(
        type = "FLOAT_ATTR",
        name = encode_bytes(str2bytes('ctr')),
        floatValue = double_list[i],
      )
      item = dict(
        item_id = item_id[i],
        item_type = item_type[i],
        item_attr = [item_attr],
      )
      resp["item"].append(item)

    common_attr = dict(
      type = "INT_ATTR",
      name = encode_bytes(str2bytes('int_common_attr')),
      int_value = 1
    )
    resp["common_attr"].append(common_attr)

    item_attr = dict(
      item_keys = [],
      attr_values = []
    )
    for i in range(len(item_id)):
      item_key = gen_key_sign(item_type[i], item_id[i])
      item_attr["item_keys"].append(item_key)

    int_item_attr = dict(
      name = "int_item_attr",
      value = encode_bytes(b''.join(int2bytes(data) for data in int_list)),
      value_type = "INT64"
    )
    item_attr["attr_values"].append(int_item_attr)

    double_item_attr = dict(
      name = "double_item_attr",
      value = encode_bytes(b''.join(float2bytes(data) for data in double_list)),
      value_type = "FLOAT64"
    )
    item_attr["attr_values"].append(double_item_attr)

    string_item_attr = dict(
      name = "string_item_attr",
      value = encode_bytes(b''.join(str2bytes(data) for data in string_list)),
      value_type = "STRING",
      value_length = [6,4,4,4,6,8,6,4]
    )
    item_attr["attr_values"].append(string_item_attr)

    int_list_item_attr = dict(
      name = "int_list_item_attr",
      value = encode_bytes(b''.join(int2bytes(data) for data in list(itertools.chain.from_iterable(packed_int_list)))),
      value_type = "INT64_LIST",
      value_length = [3,1,0,2,1,1,0,1]
    )
    item_attr["attr_values"].append(int_list_item_attr)

    double_list_item_attr = dict(
      name = "double_list_item_attr",
      value = encode_bytes(b''.join(float2bytes(data) for data in list(itertools.chain.from_iterable(packed_double_list)))),
      value_type = "FLOAT64_LIST",
      value_length = [1,3,0,1,0,1,3,1]
    )
    item_attr["attr_values"].append(double_list_item_attr)

    string_list_item_attr = dict(
      name = "string_list_item_attr",
      value = encode_bytes(b''.join(str2bytes(data) for data in list(itertools.chain.from_iterable(packed_string_list)))),
      value_type = "STRING_LIST",
      value_length = [1,3,3,4,3,4,1,6,1,4,1,5,2,9,4,1,5,1,7]
    )
    item_attr["attr_values"].append(string_list_item_attr)

    resp["item_attr"] = item_attr

    rpc_mocker = self.__service.rpc_mocker()
    rpc_mocker.mock_rpc_response(service_name="grpc_SimComputeOffloadTest", response_json_str=json.dumps(resp))

    flow = GsuFlow(name="test_sim_compute_offload") \
      .sim_compute_offload(
        kconf='colossus.kconf_client.sim_compute_offload_uttest',
        recv_common_attrs = ["int_common_attr"],
        recv_item_attrs = ["ctr", \
                           "int_item_attr", "double_item_attr", "string_item_attr", \
                           "int_list_item_attr", "double_list_item_attr", "string_list_item_attr"],
        use_packed_item_attr = "{{dynamic_common_attr}}",
        offload_per_million = 1000000,
      )
    leaf = self.__init_service(flow)

    leaf["dynamic_common_attr"] = True
    for index, id in enumerate(item_id):
      leaf.add_item_by_type(item_type[index], id)

    # 暂时不支持 MockRPC, 等支持了再打开
    return
    leaf.run("test_sim_compute_offload")

    for index, item in enumerate(leaf.items):
      self.assertEqual(item.item_id, item_id[index])
      self.assertEqual(item.item_type, item_type[index])
      self.assertAlmostEqual(item["ctr"], double_list[index],6)
      self.assertEqual(item["int_item_attr"], int_list[index])
      self.assertEqual(item["double_item_attr"], double_list[index])
      self.assertEqual(item["string_item_attr"], string_list[index])
      self.assertListEqual(item["int_list_item_attr"], packed_int_list[index])
      self.assertListEqual(item["double_list_item_attr"], packed_double_list[index])
      self.assertListEqual(item["string_list_item_attr"], packed_string_list[index])

def test_extract_attr_for_good_colossus_v2_enricher(self):
    flow = GsuFlow(name="extract_attr_for_good_colossus_v2_enricher")
    flow.enrich_attr_by_lua(
      export_common_attr=[
        "_REQ_TIME_"
      ],
      function_for_common="calculate",
      lua_script="""
        function calculate()
          return 1736944398052
        end
      """
    ) \
    .enrich_attr_by_lua(
      export_item_attr=[
        "good_click_cate2cate_cate1_list",
        "good_click_cate2cate_cate2_list",
        "good_click_cate2cate_cate3_list",
        "good_click_cate2cate_category_list",
        "good_click_cate2cate_click_flow_type_list",
        "good_click_cate2cate_click_from_list",
        "good_click_cate2cate_click_index_list",
        "good_click_cate2cate_item_id_list",
        "good_click_cate2cate_lag_list",
        "good_click_cate2cate_real_price_list",
        "good_click_cate2cate_real_seller_id_list",
        "good_click_cate2cate_seller_id_list",
        "good_click_cate2cate_timestamp_list",
        "good_click_cate2cate_item_id_list_extend",
        "good_click_cate2cate_seller_id_list_extend",
        "good_click_cate2cate_real_seller_id_list_extend",
        "good_click_cate2cate_lag_list_extend",
        "good_click_cate2cate_cate1_list_extend",
        "good_click_cate2cate_cate2_list_extend",
        "good_click_cate2cate_cate3_list_extend",
        "good_click_cate2cate_category_list_extend",
        "good_click_cate2cate_carry_type_list_extend",
        "good_click_cate2cate_click_type_list_extend",
        "good_click_cate2cate_click_from_list_extend",
        "good_click_cate2cate_real_price_list_extend",
        "good_click_cate2cate_index_list_extend",
        "good_click_cate2cate_lag_hour_list_extend",
        "good_click_cate2cate_lag_min_list_extend"
      ],
      function_for_item="calculate",
      lua_script="""
        function calculate()
          return {1107}, {1354}, {3037}, {311598614803450847}, {196608}, {23104808714867}, {0}, {23104808714867}, {64}, {301}, {3897298867}, {3897298867}, {1731333347}, 
            {23104808714867}, {3897298867}, {3897298867}, {64}, {1107}, {1354}, {3037}, {311598614803450847}, {3}, {0}, {23104808714867}, {3}, {0}, {1558}, {93480}
        end
      """
    ) \
    .extract_attr_for_good_colossus_v2_enricher(
      request_time_attr="_REQ_TIME_",
      cate1_list_attr="good_click_cate2cate_cate1_list",
      cate2_list_attr="good_click_cate2cate_cate2_list",
      cate3_list_attr="good_click_cate2cate_cate3_list",
      category_list_attr="good_click_cate2cate_category_list",
      flow_type_list_attr="good_click_cate2cate_click_flow_type_list",
      from_list_attr="good_click_cate2cate_click_from_list",
      index_list_attr="good_click_cate2cate_click_index_list",
      photo_id_list_attr="good_click_cate2cate_item_id_list",
      lag_list_attr="good_click_cate2cate_lag_list",
      real_price_attr="good_click_cate2cate_real_price_list",
      real_seller_id_list_attr="good_click_cate2cate_real_seller_id_list",
      seller_id_list_attr="good_click_cate2cate_seller_id_list",
      timestamp_list_attr="good_click_cate2cate_timestamp_list",
      resp_item_id_list_attr="good_click_cate2cate_item_id_list_extend",
      resp_seller_id_list_attr="good_click_cate2cate_seller_id_list_extend",
      resp_real_seller_id_list_attr="good_click_cate2cate_real_seller_id_list_extend",
      resp_lag_list_attr="good_click_cate2cate_lag_list_extend",
      resp_cate1_list_attr="good_click_cate2cate_cate1_list_extend",
      resp_cate2_list_attr="good_click_cate2cate_cate2_list_extend",
      resp_cate3_list_attr="good_click_cate2cate_cate3_list_extend",
      resp_category_list_attr="good_click_cate2cate_category_list_extend",
      resp_carry_type_list_attr="good_click_cate2cate_carry_type_list_extend",
      resp_click_type_list_attr="good_click_cate2cate_click_type_list_extend",
      resp_click_from_list_attr="good_click_cate2cate_click_from_list_extend",
      resp_real_price_list_attr="good_click_cate2cate_real_price_list_extend",
      resp_index_list_attr="good_click_cate2cate_index_list_extend",
      resp_lag_hour_list_attr="good_click_cate2cate_lag_hour_list_extend",
      resp_lag_min_list_attr="good_click_cate2cate_lag_min_list_extend"
    ) \
    .log_debug_info(
        common_attrs=[
            "_REQ_TIME_"
        ],
        item_attrs=[
          "good_click_cate2cate_cate1_list",
          "good_click_cate2cate_cate2_list",
          "good_click_cate2cate_cate3_list",
          "good_click_cate2cate_category_list",
          "good_click_cate2cate_click_flow_type_list",
          "good_click_cate2cate_click_from_list",
          "good_click_cate2cate_click_index_list",
          "good_click_cate2cate_item_id_list",
          "good_click_cate2cate_lag_list",
          "good_click_cate2cate_real_price_list",
          "good_click_cate2cate_real_seller_id_list",
          "good_click_cate2cate_seller_id_list",
          "good_click_cate2cate_timestamp_list",
          "good_click_cate2cate_item_id_list_extend",
          "good_click_cate2cate_seller_id_list_extend",
          "good_click_cate2cate_real_seller_id_list_extend",
          "good_click_cate2cate_lag_list_extend",
          "good_click_cate2cate_cate1_list_extend",
          "good_click_cate2cate_cate2_list_extend",
          "good_click_cate2cate_cate3_list_extend",
          "good_click_cate2cate_category_list_extend",
          "good_click_cate2cate_carry_type_list_extend",
          "good_click_cate2cate_click_type_list_extend",
          "good_click_cate2cate_click_from_list_extend",
          "good_click_cate2cate_real_price_list_extend",
          "good_click_cate2cate_index_list_extend",
          "good_click_cate2cate_lag_hour_list_extend",
          "good_click_cate2cate_lag_min_list_extend"
        ]
    )
    leaf = self.__init_service(flow)
    leaf.user_id = 4377073829
    leaf.request_time=1736944398052
    leaf.run("gsu_common_colossusv2_enricher")
    self.assertNotEqual(leaf['good_click_cate2cate_lag_min_list_extend'], None, 'not found for good_click_cate2cate_lag_min_list_extend')

def test_extract_attr_for_eshop_video_colossus_enricher(self):
    flow = GsuFlow(name="extract_attr_for_good_colossus_v2_enricher")
    flow.enrich_attr_by_lua(
      export_common_attr=[
        "_REQ_TIME_"
      ],
      function_for_common="calculate",
      lua_script="""
        function calculate()
          return 1736944398052
        end
      """
    ) \
    .extract_attr_for_eshop_video_colossus_enricher(
        request_time_attr="_REQ_TIME_",
        list_limit_size=10,
        filt_type=0,
        photo_id_list_attr="photo_colossus_photo_id_list",
        author_id_list_attr="photo_colossus_author_id_list",
        duration_list_attr="photo_colossus_duration_list",
        play_time_list_attr="photo_colossus_play_time_list",
        channel_list_attr="photo_colossus_channel_list",
        label_list_attr="photo_colossus_label_list",
        timestamp_list_attr="photo_colossus_timestamp_list",
        spu_id_list_attr="photo_colossus_spu_id_list",
        category_list_attr="photo_colossus_category_list",
        resp_pid_list_attr="cart_photo_exposure_pid_list_expand_v2",
        resp_aid_list_attr="cart_photo_exposure_aid_list_expand_v2",
        resp_duration_list_attr="cart_photo_exposure_duration_list_expand_v2",
        resp_play_time_list_attr="cart_photo_exposure_play_time_list_expand_v2",
        resp_channel_list_attr="cart_photo_exposure_channel_list_expand_v2",
        resp_label_list_attr="cart_photo_exposure_label_list_expand_v2",
        resp_lag_min_list_attr="cart_photo_exposure_lag_min_list_expand_v2",
        resp_spu_id_list_attr="cart_photo_exposure_spu_id_list_expand_v2",
        resp_category_list_attr="cart_photo_exposure_category_list_expand_v2"
    )
    leaf = self.__init_service(flow)
    leaf.user_id = 4377073829
    leaf.request_time=1736944398052
    leaf.run("gsu_common_colossusv2_enricher")
    self.assertNotEqual(leaf['photo_colossus_photo_id_list'], None, 'not found for photo_colossus_photo_id_list')

def test_inner_ad_action_enricher(self):
    flow = GsuFlow(name="inner_ad_action_test_flow")
    flow.enrich_attr_by_lua(
      export_common_attr=[
        "_REQ_TIME_",
        "timestamp_list",
        "photo_id_list", 
        "item_id_list",
        "channel_list",
        "author_id_list",
        "spu_id_list",
        "category_level1_list",
        "category_level2_list", 
        "category_level3_list",
        "category_level4_list",
        "account_id_list",
        "label_list"
      ],
      function_for_common="calculate",
      lua_script="""
        function calculate()
          return 1000000, {100, 200, 300}, {1001, 1002, 1003}, {2001, 2002, 2003}, 
                 {1, 1, 2}, {101, 102, 103}, {6001, 6002, 6003}, {10, 10, 20}, {11, 12, 21}, 
                 {111, 121, 211}, {1111, 1211, 2111}, {5001, 5002, 5003}, {0, 1, 2}
        end
      """
    ) \
    .inner_ad_action(
        request_time_attr="_REQ_TIME_",
        timestamp_list_attr="timestamp_list",
        photo_id_list_attr="photo_id_list",
        item_id_list_attr="item_id_list",
        channel_list_attr="channel_list",
        author_id_list_attr="author_id_list",
        spu_id_list_attr="spu_id_list",
        category_level1_list_attr="category_level1_list",
        category_level2_list_attr="category_level2_list",
        category_level3_list_attr="category_level3_list",
        category_level4_list_attr="category_level4_list",
        account_id_list_attr="account_id_list",
        label_list_attr="label_list",
        resp_timestamp_list_attr="output_timestamp_list",
        resp_photo_id_list_attr="output_photo_id_list",
        resp_item_id_list_attr="output_item_id_list",
        resp_channel_list_attr="output_channel_list",
        resp_author_id_list_attr="output_author_id_list",
        resp_spu_id_list_attr="output_spu_id_list",
        resp_category_level1_list_attr="output_category_level1_list",
        resp_category_level2_list_attr="output_category_level2_list",
        resp_category_level3_list_attr="output_category_level3_list",
        resp_category_level4_list_attr="output_category_level4_list",
        resp_account_id_list_attr="output_account_id_list",
        filt_type=0,
        list_limit_size=1000
    )
    leaf = self.__init_service(flow)
    leaf.user_id = ********
    leaf.request_time = 1000000
    leaf.run("inner_ad_action_test_flow")
    self.assertNotEqual(leaf['output_photo_id_list'], None, 'not found for output_photo_id_list')

@unittest.skip("skip ut for gsu_clotho_get for client doesn't support kess")
def test_gsu_clotho_get(self):
    pass
@unittest.skip("skip ut for ksib_gsu_retriever_with_colossus_resp")
def test_ksib_gsu_retriever_with_colossus_resp(self):
    pass


if __name__ == '__main__':
  suite = unittest.TestSuite()

  suite.addTests(unittest.TestLoader().loadTestsFromName('gsu_api_test.TestFlowFunc'))

  runner = unittest.TextTestRunner(verbosity=2)
  result = runner.run(suite)
  if result.failures or result.errors:
    exit(1)
  else:
    exit(0)
