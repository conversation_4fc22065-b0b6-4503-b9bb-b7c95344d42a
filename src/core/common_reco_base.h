#pragma once

#include <glog/logging.h>
#include <boost/any.hpp>

#include <algorithm>
#include <cmath>
#include <memory>
#include <string>
#include <type_traits>
#include <utility>
#include <vector>

#include "absl/strings/str_cat.h"
#include "absl/strings/substitute.h"
#include "base/common/basic_types.h"
#include "base/thread/internal/synchronization/lock.h"
#include "dragon/src/core/common_reco_define.h"
#include "dragon/src/core/common_reco_statics.h"
#include "dragon/src/table_api/table.h"
#include "dragon/src/util/logging_util.h"
#include "folly/container/F14Map.h"
#include "ks/common_reco/util/key_sign_util.h"
#include "ks/reco_pub/reco/distributed_photo_info/protoutil/flattened_attr_kv_item.h"
#include "third_party/abseil/absl/container/flat_hash_map.h"
#include "third_party/abseil/absl/strings/string_view.h"
#include "third_party/abseil/absl/types/optional.h"
#include "third_party/abseil/absl/types/span.h"
#include "third_party/croaring/roaring.hh"

DECLARE_int32(attr_io_check_level);
DECLARE_int64(attr_usage_perflog_interval);
DECLARE_string(enable_attr_usage_perflog_kconf_key);
DECLARE_bool(enable_attr_usage_perflog);
DECLARE_bool(record_filter_reason);
DECLARE_bool(all_processor_record_filter_reason);
DECLARE_int32(attr_type_check_level);
DECLARE_bool(check_attr_type_get);
DECLARE_bool(save_desired_attr_type);
DECLARE_bool(record_reason_list);
DECLARE_int64(attr_counter_interval);
DECLARE_bool(convert_nan_inf_to_zero);
DECLARE_int64(attr_value_size_growth_threshold);
DECLARE_double(attr_value_size_growth_factor);
DECLARE_int64(attr_value_size_growth_step);

#define CHECK_COMMON_ATTR_OVERWRITE()    \
  if (value_type != AttrType::UNKNOWN) { \
    if (if_not_exist) return false;      \
    if (read_only) {                     \
      ++read_only_counter;               \
      return false;                      \
    }                                    \
    if (check_overwrite) {               \
      ++overwrite_counter;               \
    }                                    \
  }

#define CHECK_COMMON_ATTR_APPEND(TYPE)   \
  if (value_type != AttrType::UNKNOWN) { \
    if (read_only) {                     \
      ++read_only_counter;               \
      return false;                      \
    }                                    \
    if (value_type != TYPE) {            \
      ++inconsistent_counter;            \
    }                                    \
  }

#define CHECK_WRITE_AND_EXPAND(TYPE, VALUE_SIZE, VALUES, HAS_VALUES)                                  \
  {                                                                                                   \
    if (read_only) {                                                                                  \
      ++read_only_counter;                                                                            \
      return false;                                                                                   \
    }                                                                                                 \
    CHECK_DESIRED_VALUE_TYPE_SET(TYPE, false)                                                         \
    if (value_type != AttrType::UNKNOWN && value_type != TYPE) {                                      \
      ++inconsistent_counter;                                                                         \
      std::fill(HAS_VALUES.begin(), HAS_VALUES.end(), 0);                                             \
    }                                                                                                 \
    value_type = TYPE;                                                                                \
    if (attr_index >= HAS_VALUES.size() || attr_index >= (VALUES.size() / VALUE_SIZE)) {              \
      if (!CanExpand()) {                                                                             \
        ++no_expand_counter;                                                                          \
        return false;                                                                                 \
      }                                                                                               \
      size_t new_size =                                                                               \
          CalcAttrValueSize(std::min(HAS_VALUES.size(), VALUES.size() / VALUE_SIZE), attr_index + 1); \
      VALUES.resize(VALUE_SIZE *new_size);                                                            \
      HAS_VALUES.resize(new_size, 0);                                                                 \
    }                                                                                                 \
  }

#define CHECK_WRITE_AND_EXPAND_WITHOUT_CHECK(TYPE, VALUE_SIZE, VALUES, HAS_VALUES)                    \
  {                                                                                                   \
    if (read_only) {                                                                                  \
      return false;                                                                                   \
    }                                                                                                 \
    if (value_type != AttrType::UNKNOWN && value_type != TYPE) {                                      \
      std::fill(HAS_VALUES.begin(), HAS_VALUES.end(), 0);                                             \
    }                                                                                                 \
    value_type = TYPE;                                                                                \
    if (attr_index >= HAS_VALUES.size() || attr_index >= (VALUES.size() / VALUE_SIZE)) {              \
      if (!CanExpand()) {                                                                             \
        return false;                                                                                 \
      }                                                                                               \
      size_t new_size =                                                                               \
          CalcAttrValueSize(std::min(HAS_VALUES.size(), VALUES.size() / VALUE_SIZE), attr_index + 1); \
      VALUES.resize(VALUE_SIZE *new_size);                                                            \
      HAS_VALUES.resize(new_size, 0);                                                                 \
    }                                                                                                 \
  }

#define CHECK_DESIRED_VALUE_TYPE_SET(TYPE, DEFAULT_RETURN)                         \
  if (desired_value_type == AttrType::UNKNOWN) {                                   \
    desired_value_type = TYPE; /* not specified in config, set when first write */ \
  } else if (FLAGS_attr_type_check_level >= 1 && desired_value_type != TYPE) {     \
    ++wrong_type_set_counter;                                                      \
    if (FLAGS_attr_type_check_level >= 2) {                                        \
      return DEFAULT_RETURN;                                                       \
    }                                                                              \
  }

#define CHECK_DESIRED_VALUE_TYPE_GET(TYPE, DEFAULT_RETURN)                                           \
  if (FLAGS_check_attr_type_get && FLAGS_attr_type_check_level >= 1 && desired_value_type != TYPE) { \
    ++wrong_type_get_counter;                                                                        \
    if (FLAGS_attr_type_check_level >= 2) {                                                          \
      return DEFAULT_RETURN;                                                                         \
    }                                                                                                \
  }

#define TRY_TO_WRITE(VALUES, HAS_VALUES, VALUE_SIZE) \
  {                                                  \
    can_write = true;                                \
    if (to_index >= HAS_VALUES.size()) {             \
      if (!CanExpand()) {                            \
        ++no_expand_counter;                         \
        can_write = false;                           \
      } else {                                       \
        VALUES.resize(VALUE_SIZE *(to_index + 1));   \
        HAS_VALUES.resize(to_index + 1, 0);          \
      }                                              \
    }                                                \
    if (can_write && HAS_VALUES[to_index]) {         \
      if (if_not_exist) {                            \
        can_write = false;                           \
      } else {                                       \
        if (check_overwrite) ++overwrite_counter;    \
      }                                              \
    }                                                \
    if (can_write) {                                 \
      HAS_VALUES[to_index] = true;                   \
    }                                                \
  }

#define RESIZE_ITEM_ATTR(VALUES, HAS_VALUES, VALUE_SIZE)                                 \
  {                                                                                      \
    if (attr_index >= HAS_VALUES.size() || attr_index >= (VALUES.size() / VALUE_SIZE)) { \
      if (!CanExpand()) {                                                                \
        ++no_expand_counter;                                                             \
        return false;                                                                    \
      }                                                                                  \
      VALUES.resize(VALUE_SIZE *(attr_index + 1));                                       \
      HAS_VALUES.resize(attr_index + 1, 0);                                              \
    }                                                                                    \
  }

#define SET_ITEM_ATTR_VALUE(VAR, HAS_VAR, VALUE) \
  {                                              \
    if (HAS_VAR[attr_index]) {                   \
      if (if_not_exist) return false;            \
      if (check_overwrite) ++overwrite_counter;  \
    } else {                                     \
      HAS_VAR[attr_index] = true;                \
    }                                            \
    VAR[attr_index] = VALUE;                     \
    return true;                                 \
  }

#define APPEND_LIST_ITEM_ATTR_VALUE(VAR, HAS_VAR, VALUE) \
  {                                                      \
    if (!HAS_VAR[attr_index]) {                          \
      HAS_VAR[attr_index] = true;                        \
      VAR[attr_index].clear();                           \
    }                                                    \
    VAR[attr_index].push_back(VALUE);                    \
    return true;                                         \
  }

#define RESET_LIST_ITEM_ATTR_VALUE(VAR, HAS_VAR) \
  {                                              \
    if (HAS_VAR[attr_index]) {                   \
      if (if_not_exist) return false;            \
      if (check_overwrite) ++overwrite_counter;  \
    } else {                                     \
      HAS_VAR[attr_index] = true;                \
    }                                            \
    VAR[attr_index].clear();                     \
    VAR[attr_index].reserve(capacity);           \
    return true;                                 \
  }

#define MOVE_VALUE(VALUE)                                   \
  {                                                         \
    if (move_value) {                                       \
      VALUE[to_index] = std::move(from->VALUE[from_index]); \
    } else {                                                \
      VALUE[to_index] = from->VALUE[from_index];            \
    }                                                       \
  }

namespace ks {
namespace platform {

#define unlikely(x) __builtin_expect(!!(x), 0)
#define likely(x) __builtin_expect(!!(x), 1)

static bool IsAttrUsagePerflogEnabled() {
  static auto kconf =
      ks::infra::KConf().Get(FLAGS_enable_attr_usage_perflog_kconf_key, FLAGS_enable_attr_usage_perflog);
  return kconf->Get();
}

class ReadableRecoContextInterface;
class MutableRecoContextInterface;
class CommonRecoContext;
class AttrTable;
class AttrPayload;

struct CommonRecoResult;
struct AttrValue;

// 表/列的别名，兼容已有代码
using DataFrame = AttrTable;
using ItemAttr = AttrValue;
using CommonAttr = AttrValue;

using FlatIndexItem = ks::reco::protoutil::FlattenedAttrKvItem;
using Column = ks::platform::tableapi::Column;
using Row = ks::platform::tableapi::Row;

typedef std::vector<CommonRecoResult>::const_iterator RecoResultConstIter;

/**
 * AttrValue 用来存储一组 item 的某个 attr 的紧密集合。
 * 一个 AttrValue 因此有一个唯一的 name 和 value_type。
 * 一个 item 可以用他的 index 来查询自己的对应属性值。
 */
struct AttrValue : public ks::platform::tableapi::Column {
  friend class ReadableRecoContextInterface;
  friend class MutableRecoContextInterface;
  friend class CommonRecoContext;
  AttrType value_type = AttrType::UNKNOWN;
  AttrType desired_value_type = AttrType::UNKNOWN;
  bool enable_attr_type_check = false;

  std::atomic_int ref_count{0};

  // counter 统计
  mutable int64 used_counter = 0;
  mutable int64 access_counter = 0;
  int overwrite_counter = 0;
  int read_only_counter = 0;
  int no_expand_counter = 0;
  int inconsistent_counter = 0;
  bool restore_mutable = false;
  mutable int64 wrong_type_get_counter = 0;
  mutable int64 wrong_type_set_counter = 0;

  // flat index 相关信息
  bool is_from_flat_index = false;
  // attr 在 flat index data 段中的存储偏移
  uint32 flat_index_offset = 0;
  // attr 在 flat index 中的序号，用于快速判断 exist
  uint32 flat_index_order = 0;

  explicit AttrValue(absl::string_view attr_name, bool for_common_data = false)
      : name_(attr_name)
      , value_type(AttrType::UNKNOWN)
      , read_only(false)
      , for_common_data_(for_common_data) {}

  bool SwapPayloadFrom(AttrValue *from) {
    if (!from || this == from) {
      return false;
    }

    value_type = from->value_type;
    name_ = from->name_;
    is_from_flat_index = from->is_from_flat_index;
    flat_index_offset = from->flat_index_offset;
    flat_index_order = from->flat_index_order;

    used_counter = from->used_counter;
    access_counter = from->access_counter;
    overwrite_counter = from->overwrite_counter;
    read_only_counter = from->read_only_counter;
    no_expand_counter = from->no_expand_counter;
    inconsistent_counter = from->inconsistent_counter;

    index_ = from->index_;
    read_only = from->read_only;
    no_expand = from->no_expand;
    for_common_data_ = from->for_common_data_;

    ref_count.store(from->ref_count.load());
    restore_mutable = from->restore_mutable;

    std::swap(single_values, from->single_values);
    std::swap(list_values, from->list_values);
    std::swap(string_list_values, from->string_list_values);
    std::swap(extra_values, from->extra_values);
    std::swap(has_values, from->has_values);
    std::swap(has_list_values, from->has_list_values);
    std::swap(has_extra_values, from->has_extra_values);
    std::swap(default_values, from->default_values);
    std::swap(default_string_list_value, from->default_string_list_value);
    return true;
  }

  const std::string &name() const final {
    return name_;
  }

  size_t index() const final {
    return index_;
  }

  void SetIndex(size_t index) {
    index_ = index;
  }

  void IncrRefCount() {
    int old_count = ref_count++;
    if (old_count == 0 && !IsReadOnly()) {
      restore_mutable = true;
    }
    DisableExpand();
  }

  void DescRefCount() {
    int new_count = --ref_count;
    if (new_count == 0) {
      EnableExpand();
      if (restore_mutable) {
        MarkMutable();
        restore_mutable = false;
      }
    }
  }

  bool DeepCloneFrom(const AttrValue *from) {
    if (!from || this == from) {
      return false;
    }
    if (IsReadOnly()) {
      read_only_counter++;
      return false;
    }

    if (value_type != AttrType::UNKNOWN) {
      overwrite_counter++;
      if (value_type != from->value_type) {
        inconsistent_counter++;
      }
    }

    value_type = from->value_type;
    if (from->IsReadOnly()) {
      MarkReadOnly();
    }
    is_from_flat_index = from->is_from_flat_index;
    flat_index_offset = from->flat_index_offset;
    flat_index_order = from->flat_index_order;

    single_values = from->single_values;
    list_values = from->list_values;
    string_list_values = from->string_list_values;
    extra_values = from->extra_values;

    has_values = from->has_values;
    has_list_values = from->has_list_values;
    has_extra_values = from->has_extra_values;

    default_values = from->default_values ? std::make_unique<std::string>(*(from->default_values)) : nullptr;
    default_string_list_value =
        from->default_string_list_value
            ? std::make_unique<std::vector<std::string>>(*(from->default_string_list_value))
            : nullptr;
    return true;
  }

  bool MoveItemValue(AttrValue *from, size_t from_index, size_t to_index, bool if_not_exist = false,
                     bool check_overwrite = true, bool move_value = true) {
    if (read_only) {
      ++read_only_counter;
      return false;
    }

    if (value_type != from->value_type) {
      inconsistent_counter++;
      return false;
    }

    bool can_write = true;
    if (value_type == AttrType::EXTRA) {
      if (from_index < from->has_extra_values.size() && from->has_extra_values[from_index] &&
          from_index < from->extra_values.size()) {
        TRY_TO_WRITE(extra_values, has_extra_values, 1);
        if (can_write) {
          MOVE_VALUE(extra_values);
        }
        return true;
      }
    } else if (value_type == AttrType::STRING) {
      // STRING 类型使用 list_values 存储值，使用 has_values 判断存在
      if (from_index < from->has_values.size() && from->has_values[from_index] &&
          from_index < from->list_values.size()) {
        TRY_TO_WRITE(list_values, has_values, 1);
        if (can_write) {
          MOVE_VALUE(list_values);
        }
        return true;
      }
    } else if (value_type == AttrType::STRING_LIST) {
      // STRING_LIST 类型使用 string_list_values 存储值，使用 has_list_values 判断存在
      if (from_index < from->has_list_values.size() && from->has_list_values[from_index] &&
          from_index < from->string_list_values.size()) {
        TRY_TO_WRITE(string_list_values, has_list_values, 1);
        if (can_write) {
          MOVE_VALUE(string_list_values);
        }
        return true;
      }
    } else if (IsListAttrType(value_type)) {
      // 普通 list 类型使用 list_values 存储值，has_list_values 判断存在
      if (from_index < from->has_list_values.size() && from->has_list_values[from_index] &&
          from_index < from->list_values.size()) {
        TRY_TO_WRITE(list_values, has_list_values, 1);
        if (can_write) {
          MOVE_VALUE(list_values);
        }
        return true;
      }
    } else {
      // 普通单值类型使用 single_values 存储值，has_values 判断存在
      if (from_index < from->has_values.size() && from->has_values[from_index] &&
          from_index < from->single_values.size()) {
        auto type_size = from->single_values.size() / from->has_values.size();
        TRY_TO_WRITE(single_values, has_values, type_size);
        if (can_write) {
          memcpy(const_cast<char *>(single_values.data() + to_index * type_size),
                 from->single_values.data() + from_index * type_size, type_size);
        }
        return true;
      }
    }
    return false;
  }

  void CopyItemDefaultValue(const AttrValue *from, size_t from_index, size_t to_index,
                            bool if_not_exist = false, bool check_overwrite = true) {
    if (read_only) {
      ++read_only_counter;
      return;
    }

    if (value_type != from->value_type) {
      inconsistent_counter++;
      return;
    }

    bool can_write = true;
    if (value_type == AttrType::STRING) {
      // STRING 类型使用 list_values 存储值，使用 has_values 判断存在
      if (from->default_values) {
        TRY_TO_WRITE(list_values, has_values, 1);
        if (can_write) {
          list_values[to_index] = *(from->default_values);
        }
      }
    } else if (value_type == AttrType::STRING_LIST) {
      // STRING_LIST 类型使用 string_list_values 存储值，使用 has_list_values 判断存在
      if (from->default_string_list_value) {
        TRY_TO_WRITE(string_list_values, has_list_values, 1);
        if (can_write) {
          string_list_values[to_index] = *(from->default_string_list_value);
        }
      }
    } else if (IsListAttrType(value_type)) {
      // 普通 list 类型使用 list_values 存储值，has_list_values 判断存在
      if (from->default_values) {
        TRY_TO_WRITE(list_values, has_list_values, 1);
        if (can_write) {
          list_values[to_index] = *(from->default_values);
        }
      }
    } else {
      // 普通单值类型使用 single_values 存储值，has_values 判断存在
      if (from->default_values) {
        auto type_size = GetSingleValueAttrTypeSize(from->value_type);
        TRY_TO_WRITE(single_values, has_values, type_size);
        if (can_write) {
          memcpy(const_cast<char *>(single_values.data() + to_index * type_size),
                 from->default_values->data(), type_size);
        }
      }
    }
  }

  void MergeItemValue(AttrValue *from, size_t from_index, size_t to_index, bool if_not_exist = false,
                      bool check_overwrite = true, bool move_value = true) {
    if (read_only) {
      ++read_only_counter;
      return;
    }

    if (value_type != from->value_type) {
      inconsistent_counter++;
      return;
    }
    if (!MoveItemValue(from, from_index, to_index, if_not_exist, check_overwrite, move_value)) {
      CopyItemDefaultValue(from, from_index, to_index, if_not_exist, check_overwrite);
    }
  }

  void Clear(bool clear_used_counter = true) {
    // NOTE(fangjianbing): 必须在 ClearAllValues() 前将 read_only 重置为 false
    read_only = false;
    no_expand = false;
    CHECK(ClearAllValues(false));

    is_from_flat_index = false;
    flat_index_offset = 0;
    flat_index_order = 0;

    overwrite_counter = 0;
    read_only_counter = 0;
    no_expand_counter = 0;
    inconsistent_counter = 0;
    wrong_type_set_counter = 0;
    wrong_type_get_counter = 0;
    if (clear_used_counter) {
      used_counter = 0;
      access_counter = 0;
    }
    printer_ = nullptr;

#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
    // NOTE(caohongjin): backup_attr 不用清空, 第一次确定后可以一直使用
    // backup_attr = nullptr;
#endif
  }

  void SetOwner(const AttrTable *table) {
    owner = table;
  }

  void SetDesiredAttrType(AttrType attr_type) {
    desired_value_type = attr_type;
  }

  void EnableAttrTypeCheck() {
    enable_attr_type_check = true;
  }

  const AttrTable *GetOwner() const {
    return owner;
  }

  bool IsOwnedBy(const AttrTable *table) const {
    return owner == table;
  }

  template <typename T>
  absl::optional<T> GetDefaultValue() const {
    if (value_type == GetAttrType<T>() && default_values && default_values->size() == sizeof(T)) {
      ++used_counter;
      return *(reinterpret_cast<const T *>(default_values->data()));
    }
#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
    if (backup_attr && for_common_data_) {
      VLOG(10) << "GetDefaultValue from backup_attr: " << name();
      return backup_attr->GetDefaultValue<T>();
    }
#endif
    return absl::nullopt;
  }

  template <typename T>
  absl::optional<T> GetSingularValue() const {
    return GetSingularValue<T>(0, nullptr);
  }
  template <typename T>
  absl::optional<T> GetSingularValue(size_t attr_index,
                                     const FlatIndexItem *flat_index_addr = nullptr) const {
    ++access_counter;
    auto type = GetAttrType<T>();
    CHECK_DESIRED_VALUE_TYPE_GET(type, absl::nullopt);
    if (value_type != type) {
#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
      if (backup_attr && for_common_data_) {
        VLOG(10) << "GetSingularValue from backup_attr: " << name();
        return backup_attr->GetSingularValue<T>(attr_index, flat_index_addr);
      }
#endif
      return absl::nullopt;
    }
#ifndef DISABLE_FLAT_INDEX
    if (is_from_flat_index) {
      if (flat_index_addr && (value_type == AttrType::INT || value_type == AttrType::FLOAT)) {
        auto return_value = flat_index_addr->GetVal<T>(flat_index_offset, flat_index_order);
        if (return_value) {
          ++used_counter;
          return return_value;
        }
      }
      return GetDefaultValue<T>();
    }
#endif
    if (attr_index >= has_values.size() || !has_values[attr_index]) {
#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
      if (backup_attr && for_common_data_) {
        VLOG(10) << "GetSingularValue from backup_attr: " << name();
        return backup_attr->GetSingularValue<T>(attr_index, flat_index_addr);
      }
#endif
      return GetDefaultValue<T>();
    }
    ++used_counter;
    return *(reinterpret_cast<const T *>(single_values.data()) + attr_index);
  }

  template <typename T>
  absl::optional<T> GetSingularValueWithoutCheck(size_t attr_index,
                                                 const FlatIndexItem *flat_index_addr = nullptr) const {
    auto type = GetAttrType<T>();
    if (value_type != type) {
#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
      if (backup_attr && for_common_data_) {
        VLOG(10) << "GetSingularValue from backup_attr: " << name();
        return backup_attr->GetSingularValue<T>(attr_index, flat_index_addr);
      }
#endif
      return absl::nullopt;
    }
#ifndef DISABLE_FLAT_INDEX
    if (is_from_flat_index) {
      if (flat_index_addr && (value_type == AttrType::INT || value_type == AttrType::FLOAT)) {
        auto return_value = flat_index_addr->GetVal<T>(flat_index_offset, flat_index_order);
        if (return_value) {
          return return_value;
        }
      }
      return GetDefaultValue<T>();
    }
#endif
    if (attr_index >= has_values.size() || !has_values[attr_index]) {
#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
      if (backup_attr && for_common_data_) {
        VLOG(10) << "GetSingularValue from backup_attr: " << name();
        return backup_attr->GetSingularValue<T>(attr_index, flat_index_addr);
      }
#endif
      return GetDefaultValue<T>();
    }
    return *(reinterpret_cast<const T *>(single_values.data()) + attr_index);
  }

  template <typename T>
  absl::optional<absl::Span<const T>> GetDefaultListValue() const {
    if (value_type == GetListAttrType<T>() && default_values) {
      ++used_counter;
      return absl::Span<const T>(reinterpret_cast<const T *>(default_values->data()),
                                 default_values->size() / sizeof(T));
    }
#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
    if (backup_attr && for_common_data_) {
      VLOG(10) << "GetDefaultListValue from backup_attr: " << name();
      return backup_attr->GetDefaultListValue<T>();
    }
#endif
    return absl::nullopt;
  }

  template <typename T>
  absl::optional<absl::Span<const T>> GetListValue() const {
    return GetListValue<T>(0, nullptr);
  }
  template <typename T>
  absl::optional<absl::Span<const T>> GetListValue(size_t attr_index,
                                                   const FlatIndexItem *flat_index_addr = nullptr) const {
    auto type = GetListAttrType<T>();
    CHECK_DESIRED_VALUE_TYPE_GET(type, absl::nullopt);
    if (value_type != type) {
#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
      if (!backup_attr || !for_common_data_) {
        return absl::nullopt;
      } else {
        VLOG(10) << "GetListValue from backup_attr: " << name();
        return backup_attr->GetListValue<T>(attr_index, flat_index_addr);
      }
#else
      return absl::nullopt;
#endif
    }
    if (is_from_flat_index) {
      if (flat_index_addr && (value_type == AttrType::INT_LIST || value_type == AttrType::FLOAT_LIST ||
                              value_type == AttrType::FLOAT32_LIST)) {
        auto return_value = flat_index_addr->GetListVal<T>(flat_index_offset, flat_index_order);
        if (return_value) {
          ++used_counter;
          return return_value;
        }
      }
      return GetDefaultListValue<T>();
    } else {
      if (attr_index >= has_list_values.size() || !has_list_values[attr_index]) {
#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
        if (!backup_attr || !for_common_data_) {
          return GetDefaultListValue<T>();
        } else {
          VLOG(10) << "GetListValue from backup_attr: " << name();
          return backup_attr->GetListValue<T>(attr_index, flat_index_addr);
        }
#else
        return GetDefaultListValue<T>();
#endif
      }
      ++used_counter;
      auto &value = list_values[attr_index];
      return absl::Span<const T>(reinterpret_cast<const T *>(value.data()), value.size() / sizeof(T));
    }
  }

  template <typename T>
  bool SetDefaultValue(T value) {
    auto type = GetAttrType<T>();
    CHECK_DESIRED_VALUE_TYPE_SET(type, false);
    if (value_type != type && value_type != AttrType::UNKNOWN) {
      return false;
    }
    value_type = type;
    default_values = std::make_unique<std::string>(sizeof(T), char());
    memcpy(const_cast<T *>(reinterpret_cast<const T *>(default_values->data())), &value, sizeof(T));

    return true;
  }

  template <typename T>
  bool SetSingularValue(size_t attr_index, T value, bool if_not_exist = false, bool check_overwrite = true) {
    auto type = GetAttrType<T>();
    auto type_size = sizeof(T);
    CHECK_WRITE_AND_EXPAND(type, type_size, single_values, has_values);
    if (has_values[attr_index]) {
      if (if_not_exist) return false;
      if (check_overwrite) ++overwrite_counter;
    } else {
      has_values[attr_index] = 1;
    }
    memcpy(const_cast<T *>(reinterpret_cast<const T *>(single_values.data()) + attr_index), &value,
           sizeof(T));
    return true;
  }

  template <typename T>
  bool SetSingularValueWithoutCheck(size_t attr_index, T value, bool if_not_exist = false,
                                    bool check_overwrite = true) {
    auto type = GetAttrType<T>();
    auto type_size = sizeof(T);
    CHECK_WRITE_AND_EXPAND_WITHOUT_CHECK(type, type_size, single_values, has_values);
    if (has_values[attr_index]) {
      if (if_not_exist) return false;
    } else {
      has_values[attr_index] = 1;
    }
    memcpy(const_cast<T *>(reinterpret_cast<const T *>(single_values.data()) + attr_index), &value,
           sizeof(T));
    return true;
  }

  template <typename T>
  bool SetDefaultListValue(std::vector<T> value) {
    auto type = GetListAttrType<T>();
    CHECK_DESIRED_VALUE_TYPE_SET(type, false);
    if (value_type != type && value_type != AttrType::UNKNOWN) {
      return false;
    }
    value_type = type;
    default_values =
        std::make_unique<std::string>(reinterpret_cast<char *>(value.data()), value.size() * sizeof(T));

    return true;
  }
  template <typename T>
  bool SetListValue(std::vector<T> &&value, bool if_not_exist = false, bool check_overwrite = true) {
    return SetListValue<T>(0, std::move(value), if_not_exist, check_overwrite);
  }
  template <typename T>
  bool SetListValue(size_t attr_index, std::vector<T> &&value, bool if_not_exist = false,
                    bool check_overwrite = true) {
    auto type = GetListAttrType<T>();
    CHECK_WRITE_AND_EXPAND(type, 1, list_values, has_list_values);
    if (has_list_values[attr_index]) {
      if (if_not_exist) return false;
      if (check_overwrite) ++overwrite_counter;
    } else {
      has_list_values[attr_index] = 1;
    }
    std::vector<T> move_value = std::move(value);
    list_values[attr_index].assign(reinterpret_cast<const char *>(move_value.data()),
                                   move_value.size() * sizeof(T));
    return true;
  }

  template <typename T>
  bool ResetListValue(int capacity = 0, bool if_not_exist = false, bool check_overwrite = true) {
    return ResetListValue<T>(0, capacity, if_not_exist, check_overwrite);
  }
  template <typename T>
  bool ResetListValue(size_t attr_index, int capacity = 0, bool if_not_exist = false,
                      bool check_overwrite = true) {
    auto type = GetListAttrType<T>();
    CHECK_WRITE_AND_EXPAND(type, 1, list_values, has_list_values);
    if (has_list_values[attr_index]) {
      if (if_not_exist) return false;
      if (check_overwrite) ++overwrite_counter;
    } else {
      has_list_values[attr_index] = 1;
    }
    auto &value = list_values[attr_index];
    value.clear();
    value.reserve(capacity);
    return true;
  }

  template <typename T>
  bool AppendListValue(T value) {
    return AppendListValue<T>(0, value);
  }

  template <typename T>
  bool AppendListValue(size_t attr_index, T value) {
    auto type = GetListAttrType<T>();
    CHECK_WRITE_AND_EXPAND(type, 1, list_values, has_list_values);
    if (!has_list_values[attr_index]) {
      has_list_values[attr_index] = 1;
      list_values[attr_index].clear();
    }
    auto &list_value = list_values[attr_index];
    auto old_size = list_value.size();
    list_value.resize(old_size + sizeof(T));
    memcpy(const_cast<T *>(reinterpret_cast<const T *>(list_value.data() + old_size)), &value, sizeof(T));
    return true;
  }

  bool HasValue(size_t attr_index = 0, const FlatIndexItem *flat_index_addr = nullptr,
                bool check_default_value = true) const {
    ++access_counter;
    bool exist = false;
    if (is_from_flat_index && flat_index_addr) {
      exist = flat_index_addr->Exist(flat_index_offset, flat_index_order);
    } else {
      switch (value_type) {
        case AttrType::EXTRA:
          exist = attr_index < has_extra_values.size() && has_extra_values[attr_index];
          break;
        default:
          if (IsListAttrType(value_type)) {
            exist = attr_index < has_list_values.size() && has_list_values[attr_index];
          } else {
            exist = attr_index < has_values.size() && has_values[attr_index];
          }
          break;
      }
    }
    if (!exist && check_default_value) {
      exist = HasDefaultValue();
    }
#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
    // NOTE(caohongjin): 如果当前 attr 没有 value， 则尝试在 backup attr 中查找
    if (!exist && backup_attr && for_common_data_) {
      VLOG(10) << "check HasValue from backup_attr: " << name();
      return backup_attr->HasValue(attr_index, flat_index_addr, check_default_value);
    }
#endif
    if (exist) {
      ++used_counter;
    }
    return exist;
  }

  bool HasDefaultValue() const {
    if (value_type == AttrType::STRING_LIST) {
      if (default_string_list_value) {
        return true;
      }
    } else {
      if (default_values) {
        return true;
      }
    }
#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
    if (backup_attr && for_common_data_) {
      VLOG(10) << "check HasDefaultValue from backup_attr: " << name();
      return backup_attr->HasDefaultValue();
    }
#endif
    return false;
  }

  absl::optional<int64> GetIntValue(size_t attr_index = 0,
                                    const FlatIndexItem *flat_index_addr = nullptr) const {
    if (!enable_attr_check) {
      return GetSingularValueWithoutCheck<int64>(attr_index, flat_index_addr);
    } else {
      return GetSingularValue<int64>(attr_index, flat_index_addr);
    }
  }
  absl::optional<double> GetDoubleValue(size_t attr_index = 0,
                                        const FlatIndexItem *flat_index_addr = nullptr) const {
    if (!enable_attr_check) {
      return GetSingularValueWithoutCheck<double>(attr_index, flat_index_addr);
    } else {
      return GetSingularValue<double>(attr_index, flat_index_addr);
    }
  }
  absl::optional<absl::string_view> GetStringValue() const {
    return GetStringValue(0, nullptr);
  }
  absl::optional<absl::string_view> GetStringValue(size_t attr_index,
                                                   const FlatIndexItem *flat_index_addr = nullptr) const {
    ++access_counter;
    CHECK_DESIRED_VALUE_TYPE_GET(AttrType::STRING, absl::nullopt);
    if (value_type != AttrType::STRING) {
#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
      if (!backup_attr || !for_common_data_) {
        return GetDefaultStringValue();
      } else {
        VLOG(10) << "GetStringValue from backup_attr: " << name();
        return backup_attr->GetStringValue(attr_index, flat_index_addr);
      }
#else
      return GetDefaultStringValue();
#endif
    }
    if (is_from_flat_index) {
      if (flat_index_addr) {
        auto return_value = flat_index_addr->GetStringVal(flat_index_offset, flat_index_order);
        if (return_value) {
          ++used_counter;
          return return_value;
        }
      }
      return GetDefaultStringValue();
    } else {
      if (attr_index >= has_values.size() || !has_values[attr_index]) {
#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
        if (!backup_attr || !for_common_data_) {
          return GetDefaultStringValue();
        } else {
          VLOG(10) << "GetStringValue from backup_attr: " << name();
          return backup_attr->GetStringValue(attr_index, flat_index_addr);
        }
#else
        return GetDefaultStringValue();
#endif
      }
      ++used_counter;
      return list_values[attr_index];
    }
  }
  absl::optional<absl::Span<const int64>> GetIntListValue(
      size_t attr_index = 0, const FlatIndexItem *flat_index_addr = nullptr) const {
    return GetListValue<int64>(attr_index, flat_index_addr);
  }
  absl::optional<absl::Span<const double>> GetDoubleListValue(
      size_t attr_index = 0, const FlatIndexItem *flat_index_addr = nullptr) const {
    return GetListValue<double>(attr_index, flat_index_addr);
  }
  absl::optional<std::vector<absl::string_view>> GetStringListValue() const {
    return GetStringListValue(0, nullptr);
  }
  absl::optional<std::vector<absl::string_view>> GetStringListValue(
      size_t attr_index, const FlatIndexItem *flat_index_addr = nullptr) const {
    ++access_counter;
    CHECK_DESIRED_VALUE_TYPE_GET(AttrType::STRING_LIST, absl::nullopt);
    if (value_type != AttrType::STRING_LIST) {
#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
      if (!backup_attr || !for_common_data_) {
        return GetDefaultStringListValue();
      } else {
        VLOG(10) << "GetStringListValue from backup_attr: " << name();
        return backup_attr->GetStringListValue(attr_index, flat_index_addr);
      }
#else
      return GetDefaultStringListValue();
#endif
    }
    if (is_from_flat_index) {
      if (flat_index_addr) {
        auto return_value = flat_index_addr->GetStringListVal(flat_index_offset, flat_index_order);
        if (return_value) {
          ++used_counter;
          return return_value;
        }
      }
      return GetDefaultStringListValue();
    } else {
      if (attr_index >= has_list_values.size() || !has_list_values[attr_index]) {
#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
        if (!backup_attr || !for_common_data_) {
          return GetDefaultStringListValue();
        } else {
          VLOG(10) << "GetStringListValue from backup_attr: " << name();
          return backup_attr->GetStringListValue(attr_index, flat_index_addr);
        }
#else
        return GetDefaultStringListValue();
#endif
      }
      ++used_counter;
      std::vector<absl::string_view> views;
      views.reserve(list_values.size());
      for (const auto &str : string_list_values[attr_index]) {
        views.emplace_back(str);
      }
      return views;
    }
  }

  const boost::any *GetExtraValue() const {
    return GetExtraValue(0);
  }
  const boost::any *GetExtraValue(size_t attr_index) const {
    ++access_counter;
    CHECK_DESIRED_VALUE_TYPE_GET(AttrType::EXTRA, nullptr);
    if (value_type != AttrType::EXTRA || attr_index >= has_extra_values.size() ||
        !has_extra_values[attr_index]) {
#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
      if (!backup_attr || !for_common_data_) {
        return nullptr;
      } else {
        VLOG(10) << "GetExtraValue from backup_attr: " << name();
        return backup_attr->GetExtraValue(attr_index);
      }
#else
      return nullptr;
#endif
    }
    ++used_counter;
    return &(extra_values[attr_index]);
  }

  bool ClearAllValues(bool check_overwrite = true) {
    if (value_type != AttrType::UNKNOWN) {
      if (read_only) {
        ++read_only_counter;
        return false;
      }
      value_type = AttrType::UNKNOWN;
      if (check_overwrite) {
        ++overwrite_counter;
      }
    }

    static auto enable_clear_optimize = ks::infra::KConf().Get("reco.dragonOpt.skipAttrDeepClear", false);

    if (!enable_clear_optimize->Get()) {
      single_values.clear();
      list_values.clear();
      string_list_values.clear();
      extra_values.clear();
    }

    has_values.clear();
    has_list_values.clear();
    has_extra_values.clear();

    default_values.reset();
    default_string_list_value.reset();

    return true;
  }

  bool ClearValue(size_t attr_index = 0, bool check_overwrite = true) {
    if (read_only) {
      ++read_only_counter;
      return false;
    }

    if (check_overwrite) {
      // 当 common attr 为空，执行 ClearValue 时，不增加覆写统计
      if (!for_common_data_ || value_type != AttrType::UNKNOWN) {
        ++overwrite_counter;
      }
    }

    if (attr_index < has_values.size()) has_values[attr_index] = false;
    if (attr_index < has_list_values.size()) has_list_values[attr_index] = false;
    if (attr_index < has_extra_values.size()) has_extra_values[attr_index] = false;

    if (for_common_data_) {
      single_values.clear();
      list_values.clear();
      string_list_values.clear();
      extra_values.clear();
      value_type = AttrType::UNKNOWN;
    }

    return true;
  }

  // 这里直接全部分配了
  bool ResizeItemAttrValue(AttrType type, size_t attr_index = 0) {
    switch (type) {
      case AttrType::INT:
        RESIZE_ITEM_ATTR(single_values, has_values, sizeof(int64));
        break;
      case AttrType::INT32:
        RESIZE_ITEM_ATTR(single_values, has_values, sizeof(int32));
        break;
      case AttrType::INT16:
        RESIZE_ITEM_ATTR(single_values, has_values, sizeof(int16));
        break;
      case AttrType::INT8:
        RESIZE_ITEM_ATTR(single_values, has_values, sizeof(int8));
        break;
      case AttrType::FLOAT:
        RESIZE_ITEM_ATTR(single_values, has_values, sizeof(double));
        break;
      case AttrType::FLOAT32:
        RESIZE_ITEM_ATTR(single_values, has_values, sizeof(float));
        break;
      case AttrType::FLOAT16:
        RESIZE_ITEM_ATTR(single_values, has_values, sizeof(float16_t));
        break;
      case AttrType::STRING:
        RESIZE_ITEM_ATTR(list_values, has_values, 1);
        break;
      case AttrType::STRING_LIST:
        RESIZE_ITEM_ATTR(string_list_values, has_list_values, 1);
        break;
      case AttrType::EXTRA:
        RESIZE_ITEM_ATTR(extra_values, has_extra_values, 1);
        break;
      case AttrType::UNKNOWN:
        RESIZE_ITEM_ATTR(single_values, has_values, sizeof(int64));
        RESIZE_ITEM_ATTR(list_values, has_list_values, 1);
        RESIZE_ITEM_ATTR(string_list_values, has_list_values, 1);
        RESIZE_ITEM_ATTR(extra_values, has_extra_values, 1);
        break;
      default: {
        if (IsListAttrType(type)) {
          RESIZE_ITEM_ATTR(list_values, has_list_values, 1);
        } else {
          RESIZE_ITEM_ATTR(single_values, has_values, sizeof(int64));
        }
        break;
      }
    }
    return true;
  }

  bool SetIntValue(int64 val, bool if_not_exist = false, bool check_overwrite = true) {
    if (!enable_attr_check) {
      return SetSingularValueWithoutCheck<int64>(0, val, if_not_exist, check_overwrite);
    } else {
      return SetSingularValue<int64>(0, val, if_not_exist, check_overwrite);
    }
  }
  bool SetIntValue(size_t attr_index, int64 val, bool if_not_exist = false, bool check_overwrite = true) {
    if (!enable_attr_check) {
      return SetSingularValueWithoutCheck<int64>(attr_index, val, if_not_exist, check_overwrite);
    } else {
      return SetSingularValue<int64>(attr_index, val, if_not_exist, check_overwrite);
    }
  }
  bool SetDoubleValue(double val, bool if_not_exist = false, bool check_overwrite = true) {
    if (unlikely(FLAGS_convert_nan_inf_to_zero && !std::isfinite(val))) {
      val = 0.0;
    }
    if (!enable_attr_check) {
      return SetSingularValueWithoutCheck<double>(0, val, if_not_exist, check_overwrite);
    } else {
      return SetSingularValue<double>(0, val, if_not_exist, check_overwrite);
    }
  }
  bool SetDoubleValue(size_t attr_index, double val, bool if_not_exist = false, bool check_overwrite = true) {
    if (unlikely(FLAGS_convert_nan_inf_to_zero && !std::isfinite(val))) {
      val = 0.0;
    }
    if (!enable_attr_check) {
      return SetSingularValueWithoutCheck<double>(attr_index, val, if_not_exist, check_overwrite);
    } else {
      return SetSingularValue<double>(attr_index, val, if_not_exist, check_overwrite);
    }
  }
  bool SetStringValue(std::string val, bool if_not_exist = false, bool check_overwrite = true) {
    return SetStringValue(0, std::move(val), if_not_exist, check_overwrite);
  }
  bool SetStringValue(size_t attr_index, std::string &&val, bool if_not_exist = false,
                      bool check_overwrite = true) {
    CHECK_WRITE_AND_EXPAND(AttrType::STRING, 1, list_values, has_values);
    SET_ITEM_ATTR_VALUE(list_values, has_values, std::move(val));
  }
  bool SetIntListValue(std::vector<int64> &&val, bool if_not_exist = false, bool check_overwrite = true) {
    return SetListValue<int64>(0, std::move(val), if_not_exist, check_overwrite);
  }
  bool SetIntListValue(size_t attr_index, std::vector<int64> &&val, bool if_not_exist = false,
                       bool check_overwrite = true) {
    return SetListValue<int64>(attr_index, std::move(val), if_not_exist, check_overwrite);
  }
  bool SetDoubleListValue(std::vector<double> &&val, bool if_not_exist = false, bool check_overwrite = true) {
    return SetListValue<double>(0, std::move(val), if_not_exist, check_overwrite);
  }
  bool SetDoubleListValue(size_t attr_index, std::vector<double> &&val, bool if_not_exist = false,
                          bool check_overwrite = true) {
    return SetListValue<double>(attr_index, std::move(val), if_not_exist, check_overwrite);
  }
  bool SetStringListValue(std::vector<std::string> &&val, bool if_not_exist = false,
                          bool check_overwrite = true) {
    return SetStringListValue(0, std::move(val), if_not_exist, check_overwrite);
  }
  bool SetStringListValue(size_t attr_index, std::vector<std::string> &&val, bool if_not_exist = false,
                          bool check_overwrite = true) {
    CHECK_WRITE_AND_EXPAND(AttrType::STRING_LIST, 1, string_list_values, has_list_values);
    SET_ITEM_ATTR_VALUE(string_list_values, has_list_values, std::move(val));
  }
  bool SetExtraValue(boost::any &&val, bool if_not_exist = false, bool check_overwrite = true) {
    return SetExtraValue(0, std::move(val), if_not_exist, check_overwrite);
  }
  bool SetExtraValue(size_t attr_index, boost::any &&val, bool if_not_exist = false,
                     bool check_overwrite = true) {
    CHECK_WRITE_AND_EXPAND(AttrType::EXTRA, 1, extra_values, has_extra_values);
    SET_ITEM_ATTR_VALUE(extra_values, has_extra_values, std::move(val));
  }
  bool AppendIntListValue(int64 val) {
    return AppendListValue<int64>(0, val);
  }
  bool AppendIntListValue(size_t attr_index, int64 val) {
    return AppendListValue<int64>(attr_index, val);
  }
  bool AppendDoubleListValue(double val) {
    return AppendListValue<double>(0, val);
  }
  bool AppendDoubleListValue(size_t attr_index, double val) {
    return AppendListValue<double>(attr_index, val);
  }
  bool AppendStringListValue(std::string val) {
    return AppendStringListValue(0, std::move(val));
  }
  bool AppendStringListValue(size_t attr_index, std::string &&val) {
    CHECK_WRITE_AND_EXPAND(AttrType::STRING_LIST, 1, string_list_values, has_list_values);
    APPEND_LIST_ITEM_ATTR_VALUE(string_list_values, has_list_values, std::move(val));
  }
  bool ResetIntListValue(int capacity = 0, bool if_not_exist = false, bool check_overwrite = true) {
    return ResetListValue<int64>(0, capacity, if_not_exist, check_overwrite);
  }
  bool ResetIntListValue(size_t attr_index, int capacity = 0, bool if_not_exist = false,
                         bool check_overwrite = true) {
    return ResetListValue<int64>(attr_index, capacity, if_not_exist, check_overwrite);
  }
  bool ResetDoubleListValue(int capacity = 0, bool if_not_exist = false, bool check_overwrite = true) {
    return ResetListValue<double>(0, capacity, if_not_exist, check_overwrite);
  }
  bool ResetDoubleListValue(size_t attr_index, int capacity = 0, bool if_not_exist = false,
                            bool check_overwrite = true) {
    return ResetListValue<double>(attr_index, capacity, if_not_exist, check_overwrite);
  }
  bool ResetStringListValue(int capacity = 0, bool if_not_exist = false, bool check_overwrite = true) {
    return ResetStringListValue(0, capacity, if_not_exist, check_overwrite);
  }
  bool ResetStringListValue(size_t attr_index, int capacity = 0, bool if_not_exist = false,
                            bool check_overwrite = true) {
    CHECK_WRITE_AND_EXPAND(AttrType::STRING_LIST, 1, string_list_values, has_list_values);
    RESET_LIST_ITEM_ATTR_VALUE(string_list_values, has_list_values);
  }

  absl::optional<absl::string_view> GetDefaultStringValue() const {
    if (value_type == AttrType::STRING && default_values) {
      ++used_counter;
      return *default_values;
    }
    return absl::nullopt;
  }

  absl::optional<std::vector<absl::string_view>> GetDefaultStringListValue() const {
    if (value_type != AttrType::STRING_LIST || !default_string_list_value) {
      return absl::nullopt;
    }
    ++used_counter;
    std::vector<absl::string_view> views;
    views.reserve(default_string_list_value->size());
    for (const auto &str : *default_string_list_value) {
      views.emplace_back(str);
    }
    return views;
  }

  bool SetDefaultIntValue(int64 value) {
    return SetDefaultValue<int64>(value);
  }
  bool SetDefaultDoubleValue(double value) {
    return SetDefaultValue<double>(value);
  }
  bool SetDefaultStringValue(std::string value) {
    CHECK_DESIRED_VALUE_TYPE_SET(AttrType::STRING, false);
    if (value_type != AttrType::UNKNOWN && value_type != AttrType::STRING) {
      return false;
    }
    value_type = AttrType::STRING;
    default_values = std::make_unique<std::string>(std::move(value));
    return true;
  }
  bool SetDefaultIntListValue(std::vector<int64> &&value) {
    return SetDefaultListValue<int64>(std::move(value));
  }
  bool SetDefaultDoubleListValue(std::vector<double> &&value) {
    return SetDefaultListValue<double>(std::move(value));
  }
  bool SetDefaultStringListValue(std::vector<std::string> &&value) {
    CHECK_DESIRED_VALUE_TYPE_SET(AttrType::STRING_LIST, false);
    if (value_type != AttrType::UNKNOWN && value_type != AttrType::STRING_LIST) {
      return false;
    }
    value_type = AttrType::STRING_LIST;
    default_string_list_value = std::make_unique<std::vector<std::string>>(std::move(value));
    return true;
  }

  // 与 SetPtrItemAttr 成对使用
  template <typename T>
  const T *GetPtrValue(size_t attr_index = 0) const {
    auto *ptr = GetAnyValue<std::shared_ptr<const T>>(attr_index);
    return ptr ? ptr->get() : nullptr;
  }
  template <typename T>
  T *GetMutablePtrValue(size_t attr_index = 0) {
    auto *ptr = GetAnyValue<std::shared_ptr<const T>>(attr_index);
    return ptr ? const_cast<T *>(ptr->get()) : nullptr;
  }
  // 与 GetPtrItemAttr 成对使用
  template <typename Ptr>
  void SetPtrValue(size_t attr_index, Ptr &&ptr) {
    using T = typename std::pointer_traits<std::remove_reference_t<Ptr>>::element_type;
    if (std::is_same<T *, std::remove_reference_t<Ptr>>::value) {
      // 裸指针转为 shared_ptr 存储，但是 context 不负责释放，调用方需要自己保证这个指针在 context
      // 的生命周期内可访问。
      // FIXME(huiyiqun): 这个调用方式相对比较危险，最好可以避免这种 use case
      SetExtraValue(attr_index, std::shared_ptr<const T>(&*ptr, [](auto) {}));
    } else {
      // 其他指针类型(包括 unique_ptr) 转为 shared_ptr 存储
      SetExtraValue(attr_index, std::shared_ptr<const T>(std::forward<Ptr>(ptr)));
    }
  }

  template <typename Ptr>
  void SetPtrValue(Ptr &&ptr) {
    SetPtrValue(0, std::forward<Ptr>(ptr));
  }

  void ConvertSingularValueToListValue() {
    if (value_type == AttrType::UNKNOWN || IsListAttrType(value_type) || IsReadOnly()) {
      return;
    }
    has_list_values.resize(has_values.size(), 0);
    if (value_type == AttrType::STRING) {
      string_list_values.resize(has_values.size());
      for (int i = 0; i < has_values.size(); i++) {
        if (has_values[i]) {
          has_list_values[i] = true;
          string_list_values[i].assign({std::move(list_values[i])});
        }
      }
      has_values.clear();
      list_values.clear();
      value_type = AttrType::STRING_LIST;
    } else {
      auto new_type = ConvertToListType(value_type);
      if (new_type == AttrType::UNKNOWN) {
        return;
      }
      list_values.resize(has_values.size());
      if (has_values.size() > 0) {
        auto attr_size = single_values.size() / has_values.size();
        for (int i = 0; i < has_values.size(); i++) {
          if (has_values[i]) {
            has_list_values[i] = true;
            list_values[i].assign(single_values.data() + attr_size * i, attr_size);
          }
        }
      }
      has_values.clear();
      single_values.clear();
      value_type = new_type;
    }
  }

  void SetPrintter(std::function<std::string(const CommonRecoResult &)> function) {
    printer_ = std::move(function);
  }

  void MarkReadOnly() {
    read_only = true;
  }

  void MarkMutable() {
    read_only = false;
  }

  bool IsReadOnly() const {
    return read_only;
  }

  void EnableExpand() {
    no_expand = false;
  }

  void DisableExpand() {
    no_expand = true;
  }

  void EnableAttrCheck() {
    enable_attr_check = true;
  }

  void DisableAttrCheck() {
    enable_attr_check = false;
  }

  bool IsNoExpand() const {
    return no_expand;
  }

  bool CanExpand() const {
    return !no_expand && !read_only;
  }

  std::string GetDebugString(const std::string &multi_line_prefix = "") const;
  std::string GetDebugString(const CommonRecoResult &result, const std::string &multi_line_prefix = "") const;

  // 单值以连续内存格式存储在 single_values
  // —————————————————————————————————————————————————————————————
  // data : value0,     | value1,       | value2,       | ...
  // size : sizeof(T),  | sizeof(T),    | sizeof(T),    | ...
  // slot : 0,          | 1             | 2             | ...
  // —————————————————————————————————————————————————————————————
  std::string single_values;
  // list 值、STRING 类型的值存储在 list_values
  // 每个 list 值以连续内存格式独占一个 string，格式如单值的说明
  std::vector<std::string> list_values;
  // STRING_LIST 类型的值存储在 string_list_values
  std::vector<std::vector<std::string>> string_list_values;

  std::vector<boost::any> extra_values;

  // 单值和 STRING 类型值的存在标记
  std::vector<int8> has_values;
  // list 值和 STRING_LIST 类型值的存在标记
  std::vector<int8> has_list_values;
  std::vector<int8> has_extra_values;

  std::unique_ptr<std::string> default_values;
  std::unique_ptr<std::vector<std::string>> default_string_list_value;

 private:
  template <typename T>
  const T *GetAnyValue(size_t attr_index) const {
    try {
      return boost::any_cast<T>(GetExtraValue(attr_index));
    } catch (const boost::bad_any_cast &e) {
      CL_LOG_ERROR("extra_attr", "boost::bad_any_cast") << e.what();
      return nullptr;
    }
  }

  size_t CalcAttrValueSize(size_t current_size, size_t target_size) {
    size_t final_size = 0;
    if (current_size < FLAGS_attr_value_size_growth_threshold) {
      final_size = current_size * FLAGS_attr_value_size_growth_factor;
    } else {
      final_size = current_size + FLAGS_attr_value_size_growth_step;
    }
    if (final_size < target_size) {
      final_size = target_size;
    }
    return final_size;
  }

 private:
  // attr 的名字
  std::string name_;
  // 记录该 attr 由哪个 AttrTable 管理
  const AttrTable *owner = nullptr;
  // 记录该 attr 在 table 中的行号，是从 0 开始的连续数字
  size_t index_ = 0;
  // 标记该 attr 是否为只读
  bool read_only = false;
  // 标记该 attr 的内存空间是否可扩容
  bool no_expand = false;
  // 标记该 attr 是否是 common 侧数据
  bool for_common_data_ = false;
  // 是否开启 Get/Set 接口的异常计数
  bool enable_attr_check = true;
  // 自定义的日志打印函数
  std::function<std::string(const CommonRecoResult &)> printer_;

#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
  // NOTE(caohongjin): 用作二次查询的 attr 指针，目前只针对 common attr 生效
  const AttrValue *backup_attr = nullptr;
#endif
};

/**
 * CommonRecoResult 表示单个推荐结果
 * NOTE: 该结构为新框架下所有 processor 的处理对象
 */
struct CommonRecoResult {
  // item keysign
  uint64 item_key = 0;
  // 分数
  double score = 0.0;
  // 推荐原因
  int reason = 0;
  // channel 通常标记该 item 属于哪路业务场景
  int channel = 0;

  CommonRecoResult(size_t i, uint64 k, int r) : item_key(k), score(0.0), reason(r), attr_index(i) {}
  CommonRecoResult(size_t i, uint64 k, int r, double s) : item_key(k), score(s), reason(r), attr_index(i) {}
  CommonRecoResult(size_t i, uint64 k, int r, double s, int c)
      : item_key(k), score(s), reason(r), attr_index(i), channel(c) {}

  uint64 ItemKey() const {
    return item_key;
  }

  double Score() const {
    return score;
  }

  int Reason() const {
    return reason;
  }

  int Channel() const {
    return channel;
  }

  uint64 key() const {
    return item_key;
  }

  uint64 GetId() const {
    return Util::GetId(item_key);
  }
  int GetType() const {
    return Util::GetType(item_key);
  }

  void SetFlatIndexItemAddr(const ks::reco::protoutil::FlattenedAttrKvItem *item) {
    flat_index_addr = item;
  }

  const ks::reco::protoutil::FlattenedAttrKvItem *GetFlatIndexItemAddr() const {
    return flat_index_addr;
  }

  void SetTable(const AttrTable *p) {
    table = p;
  }

  const AttrTable *GetTable() const {
    return table;
  }

  // 封装一组针对高性能场景的 item_attr get/set 接口
  bool HasAttr(const AttrValue *item_attr, bool check_multi_table = false) const;
  absl::optional<int64> GetIntAttr(const AttrValue *item_attr, bool check_multi_table = false) const;
  absl::optional<double> GetDoubleAttr(const AttrValue *item_attr, bool check_multi_table = false) const;
  absl::optional<absl::string_view> GetStringAttr(const AttrValue *item_attr,
                                                  bool check_multi_table = false) const;
  absl::optional<absl::Span<const int64>> GetIntListAttr(const AttrValue *item_attr,
                                                         bool check_multi_table = false) const;
  absl::optional<absl::Span<const double>> GetDoubleListAttr(const AttrValue *item_attr,
                                                             bool check_multi_table = false) const;
  absl::optional<std::vector<absl::string_view>> GetStringListAttr(const AttrValue *item_attr,
                                                                   bool check_multi_table = false) const;
  const boost::any *GetExtraAttr(const AttrValue *item_attr, bool check_multi_table = false) const;
  bool ClearAttr(AttrValue *item_attr, bool check_multi_table = false) const;
  bool SetIntAttr(AttrValue *item_attr, int64 val, bool if_not_exist = false, bool check_overwrite = true,
                  bool check_multi_table = false) const;
  bool SetDoubleAttr(AttrValue *item_attr, double val, bool if_not_exist = false, bool check_overwrite = true,
                     bool check_multi_table = false) const;
  bool SetStringAttr(AttrValue *item_attr, std::string val, bool if_not_exist = false,
                     bool check_overwrite = true, bool check_multi_table = false) const;
  bool SetIntListAttr(AttrValue *item_attr, std::vector<int64> &&val, bool if_not_exist = false,
                      bool check_overwrite = true, bool check_multi_table = false) const;
  bool SetDoubleListAttr(AttrValue *item_attr, std::vector<double> &&val, bool if_not_exist = false,
                         bool check_overwrite = true, bool check_multi_table = false) const;
  bool SetStringListAttr(AttrValue *item_attr, std::vector<std::string> &&val, bool if_not_exist = false,
                         bool check_overwrite = true, bool check_multi_table = false) const;
  bool AppendIntListAttr(AttrValue *item_attr, int64 val, bool check_multi_table = false) const;
  bool AppendDoubleListAttr(AttrValue *item_attr, double val, bool check_multi_table = false) const;
  bool AppendStringListAttr(AttrValue *item_attr, std::string val, bool check_multi_table = false) const;
  bool ResetIntListAttr(AttrValue *item_attr, int capacity = 0, bool if_not_exist = false,
                        bool check_overwrite = true, bool check_multi_table = false) const;
  bool ResetDoubleListAttr(AttrValue *item_attr, int capacity = 0, bool if_not_exist = false,
                           bool check_overwrite = true, bool check_multi_table = false) const;
  bool ResetStringListAttr(AttrValue *item_attr, int capacity = 0, bool if_not_exist = false,
                           bool check_overwrite = true, bool check_multi_table = false) const;
  bool SetExtraAttr(AttrValue *item_attr, boost::any &&val, bool if_not_exist = false,
                    bool check_overwrite = true, bool check_multi_table = false) const;

  size_t GetAttrIndex() const {
    return attr_index;
  }

  template <typename T>
  const T *GetPtrAttr(const AttrValue *item_attr, bool check_multi_table = false) const {
    return item_attr->GetPtrValue<T>(attr_index);
  }

  template <typename T>
  T *GetMutablePtrAttr(AttrValue *item_attr) {
    return item_attr->GetMutablePtrValue<T>(attr_index);
  }

  template <typename T>
  std::enable_if_t<std::is_base_of<google::protobuf::Message, T>::value, const T *> GetProtoMessagePtrAttr(
      const AttrValue *item_attr) const {
    auto *ptr = GetPtrAttr<google::protobuf::Message>(item_attr);
    if (ptr) {
      return google::protobuf::down_cast<const T *>(ptr);
    } else {
      return GetPtrAttr<T>(item_attr);
    }
  }

  template <typename T>
  void SetPtrAttr(AttrValue *item_attr, T &&ptr) const {
    item_attr->SetPtrValue<T>(attr_index, std::forward<T>(ptr));
  }

  //  封装一组接口，方便在模版函数中使用
  template <typename T>
  std::enable_if_t<std::is_integral<T>::value, bool> SetScalarAttr(AttrValue *item_attr, T val,
                                                                   bool if_not_exist = false,
                                                                   bool check_overwrite = true) const {
    return SetIntAttr(item_attr, val, if_not_exist, check_overwrite);
  }

  template <typename T>
  std::enable_if_t<std::is_floating_point<T>::value, bool> SetScalarAttr(AttrValue *item_attr, T val,
                                                                         bool if_not_exist = false,
                                                                         bool check_overwrite = true) const {
    return SetDoubleAttr(item_attr, val, if_not_exist, check_overwrite);
  }

  template <typename T>
  std::enable_if_t<std::is_integral<T>::value, bool> SetListAttr(AttrValue *item_attr, std::vector<T> &&val,
                                                                 bool if_not_exist = false,
                                                                 bool check_overwrite = true) const {
    std::vector<int64> tmp_value(val.begin(), val.end());
    return SetIntListAttr(item_attr, std::move(tmp_value), if_not_exist, check_overwrite);
  }

  template <typename T>
  std::enable_if_t<std::is_floating_point<T>::value, bool> SetListAttr(AttrValue *item_attr,
                                                                       std::vector<T> &&val,
                                                                       bool if_not_exist = false,
                                                                       bool check_overwrite = true) const {
    std::vector<double> tmp_value(val.begin(), val.end());
    return SetDoubleListAttr(item_attr, std::move(tmp_value), if_not_exist, check_overwrite);
  }

  //  在一些情况下，能比使用上面的接口减少一次数据拷贝
  template <typename T>
  std::enable_if_t<std::is_integral<T>::value, bool> SetListAttr(AttrValue *item_attr, const T *val, int size,
                                                                 bool if_not_exist = false,
                                                                 bool check_overwrite = true) const {
    std::vector<int64> tmp_value(val, val + size);
    return SetIntListAttr(item_attr, std::move(tmp_value), if_not_exist, check_overwrite);
  }

  template <typename T>
  std::enable_if_t<std::is_floating_point<T>::value, bool> SetListAttr(AttrValue *item_attr, const T *val,
                                                                       int size, bool if_not_exist = false,
                                                                       bool check_overwrite = true) const {
    std::vector<double> tmp_value(val, val + size);
    return SetDoubleListAttr(item_attr, std::move(tmp_value), if_not_exist, check_overwrite);
  }

 private:
  // item attr index
  size_t attr_index;
  const ks::reco::protoutil::FlattenedAttrKvItem *flat_index_addr = nullptr;
  const AttrTable *table = nullptr;
};

template <>
inline bool CommonRecoResult::SetListAttr<double>(AttrValue *item_attr, std::vector<double> &&val,
                                                  bool if_not_exist, bool check_overwrite) const {
  return SetDoubleListAttr(item_attr, std::move(val), if_not_exist, check_overwrite);
}

template <>
inline bool CommonRecoResult::SetListAttr<int64>(AttrValue *item_attr, std::vector<int64> &&val,
                                                 bool if_not_exist, bool check_overwrite) const {
  return SetIntListAttr(item_attr, std::move(val), if_not_exist, check_overwrite);
}

template <>
const ::google::protobuf::Message *CommonRecoResult::GetPtrAttr<::google::protobuf::Message>(
    const AttrValue *item_attr, bool check_multi_table) const;
template <>
const std::vector<float> *CommonRecoResult::GetPtrAttr<const std::vector<float>>(
    const AttrValue *item_attr, bool check_multi_table) const;

class AttrPayload {
 public:
  AttrPayload() {}

  ~AttrPayload() {}

  void Clear(const std::string &table_name, const std::string &request_type, int64 process_counter);

  bool SwapFrom(AttrPayload *from_data) {
    if (!from_data || this == from_data) {
      return false;
    }

    std::swap(item_attr_index_map_, from_data->item_attr_index_map_);
    std::swap(max_item_index_, from_data->max_item_index_);

    std::swap(item_flat_index_addr_map_, from_data->item_flat_index_addr_map_);

    return true;
  }

  void CopyItemIndex(AttrPayload *from_p) {
    if (from_p == this) {
      return;
    }
    item_attr_index_map_ = from_p->item_attr_index_map_;
    max_item_index_ = from_p->max_item_index_;
    item_flat_index_addr_map_ = from_p->item_flat_index_addr_map_;
  }

  absl::optional<size_t> GetItemAttrIndex(uint64 item_key) const {
    auto it = item_attr_index_map_.find(item_key);
    if (it != item_attr_index_map_.end()) {
      return it->second;
    } else {
      return absl::nullopt;
    }
  }

  size_t GetOrInsertItemAttrIndex(uint64 item_key) {
    auto pr = item_attr_index_map_.insert({item_key, max_item_index_ + 1});
    if (pr.second) {
      max_item_index_ = std::max<int64>(max_item_index_, pr.first->second);
    }
    return pr.first->second;
  }

  size_t InsertItemAttrIndex(uint64 item_key, size_t index) {
    item_attr_index_map_[item_key] = index;
    max_item_index_ = std::max<int64>(max_item_index_, index);
    return index;
  }

  size_t GetItemIndexNum() const {
    return item_attr_index_map_.size();
  }

  bool IsValidItemAttrIndex(size_t index) const {
    return index <= max_item_index_;
  }

  void SetItemFlatIndexAddr(uint64 item_key, const ks::reco::protoutil::FlattenedAttrKvItem *internal_item) {
    item_flat_index_addr_map_[item_key] = internal_item;
  }

  const ks::reco::protoutil::FlattenedAttrKvItem *GetItemFlatIndexAddr(uint64 item_key) const {
    auto it = item_flat_index_addr_map_.find(item_key);
    if (it != item_flat_index_addr_map_.end()) {
      return it->second;
    } else {
      return nullptr;
    }
  }

  const folly::F14FastMap<uint64, const ks::reco::protoutil::FlattenedAttrKvItem *> &GetItemFlatIndexAddrMap()
      const {
    return item_flat_index_addr_map_;
  }

 private:
  // key 是主键，value 是行索引 id
  folly::F14FastMap<uint64, uint64> item_attr_index_map_;
  // 记录当前 result 最大 index;
  int64 max_item_index_ = -1;
  // key 是主键，value 是正排索引指针
  folly::F14FastMap<uint64, const ks::reco::protoutil::FlattenedAttrKvItem *> item_flat_index_addr_map_;
};

class AttrTable : public ks::platform::tableapi::DataFrame {
  friend class CommonRecoContext;

 public:
  explicit AttrTable(const std::string &name, bool for_common_data = false)
      : name_(name), for_common_data_(for_common_data) {
    attr_payload_ = std::make_shared<AttrPayload>();
    reason_attr_ = GetOrInsertAttr(kReasonAttr);
  }

  AttrTable(const std::string &name, AttrTable *from_table) : name_(name) {
    reason_attr_ = GetOrInsertAttr(kReasonAttr);
    is_logical_ = true;
    from_table->is_shared_ = true;
    attr_payload_ = from_table->GetAttrPayload();
  }

  ~AttrTable() {
    for (const auto &pr : item_attrs_) {
      if (pr.second->IsOwnedBy(this)) {
        delete pr.second;
      }
    }
  }

  void SetAttrPayload(std::shared_ptr<AttrPayload> payload) {
    attr_payload_ = payload;
  }

  std::shared_ptr<AttrPayload> GetAttrPayload() const {
    return attr_payload_;
  }

  const std::string &name() const final {
    return name_;
  }

  const std::string &type_name() const final {
    static const std::string type_name = "AttrTable";
    return type_name;
  }

  bool SwapPayloadFrom(AttrTable *from_table) {
    if (!from_table || this == from_table || IsExternal()) {
      return false;
    }

    attr_payload_->SwapFrom(from_table->GetAttrPayload().get());

    std::swap(reco_results_, from_table->reco_results_);

    // 交换前后重新设置 Attr 的 owner，避免资源泄漏
    for (auto pr : item_attrs_) {
      pr.second->SetOwner(from_table);
    }
    std::swap(item_attrs_, from_table->item_attrs_);
    std::swap(item_attrs_list_, from_table->item_attrs_list_);
    for (auto pr : item_attrs_) {
      pr.second->SetOwner(this);
    }

    std::swap(reason_attr_, from_table->reason_attr_);

    std::swap(for_common_data_, from_table->for_common_data_);

    std::swap(attr_index_bit_map_, from_table->attr_index_bit_map_);
    std::swap(attr_index_to_item_, from_table->attr_index_to_item_);
    std::swap(filter_results_, from_table->filter_results_);
    std::swap(filter_reason_offsets_, from_table->filter_reason_offsets_);
    std::swap(filter_reasons_, from_table->filter_reasons_);

    std::swap(filter_reason_map_, from_table->filter_reason_map_);
    std::swap(is_filter_results_reserved_, from_table->is_filter_results_reserved_);

    return true;
  }

  // 获取行总数
  size_t row_num() const final {
    return reco_results_.size();
  }
  // 获取列总数
  size_t column_num() const final {
    return item_attrs_.size();
  }
  // 按列名获取列
  Column *col(absl::string_view column_name) const final {
    auto it = item_attrs_.find(column_name);
    if (it == item_attrs_.end()) {
      return nullptr;
    }
    return it->second;
  }
  // 按下标获取列
  Column *col_at(size_t index) const final {
    return index < item_attrs_list_.size() ? item_attrs_list_[index] : nullptr;
  }
  // 按 key 获取行
  // TODO(qianlei): 目前查找的性能代价较大，暂不实现。后续有应用场景再改
  Row *row(uint64 key) const final {
    return nullptr;
  }
  // 按下标获取行
  Row *row_at(size_t index) const final {
    return nullptr;
  }
  // 增加一行
  Row *AppendRow(uint64 key) final {
    return nullptr;
  }
  // 增加一列
  Column *AddColumn(absl::string_view name, ValueType type) final {
    return GetOrInsertAttr(name);
  }

  void Clear(const std::string &request_type, int64 process_counter);

  void Clear() final {
    Clear("", 0);
  }

  const std::string &GetName() const {
    return name_;
  }

  std::vector<CommonRecoResult> *GetRecoResults() {
    return &reco_results_;
  }

  const std::vector<CommonRecoResult> &GetCommonRecoResults() const {
    return reco_results_;
  }

  bool CloneTargetResults(AttrTable *from_table, RecoResultConstIter begin, RecoResultConstIter end) {
    if (from_table == nullptr) {
      return false;
    }
    attr_payload_->CopyItemIndex(from_table->GetAttrPayload().get());
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      reco_results_.emplace_back(result);
      reco_results_.back().SetTable(this);
    });
    if (FLAGS_record_filter_reason) {
      attr_index_bit_map_ = from_table->attr_index_bit_map_;
      attr_index_to_item_ = from_table->attr_index_to_item_;
    }
    return true;
  }

  bool IsValidItemAttrIndex(size_t index) const {
    return attr_payload_->IsValidItemAttrIndex(index);
  }

  absl::optional<size_t> GetItemAttrIndex(uint64 item_key) const {
    return attr_payload_->GetItemAttrIndex(item_key);
  }

  size_t GetOrInsertItemAttrIndex(uint64 item_key) {
    return attr_payload_->GetOrInsertItemAttrIndex(item_key);
  }

  size_t InsertItemAttrIndex(uint64 item_key, size_t index) {
    return attr_payload_->InsertItemAttrIndex(item_key, index);
  }

  size_t GetItemIndexNum() const {
    return attr_payload_->GetItemIndexNum();
  }

  void SetItemFlatIndexAddr(uint64 item_key, const ks::reco::protoutil::FlattenedAttrKvItem *internal_item) {
    attr_payload_->SetItemFlatIndexAddr(item_key, internal_item);
  }

  const ks::reco::protoutil::FlattenedAttrKvItem *GetItemFlatIndexAddr(uint64 item_key) const {
    return attr_payload_->GetItemFlatIndexAddr(item_key);
  }

  const folly::F14FastMap<uint64, const ks::reco::protoutil::FlattenedAttrKvItem *> &GetItemFlatIndexAddrMap()
      const {
    return attr_payload_->GetItemFlatIndexAddrMap();
  }

  AttrValue *GetOrInsertAttr(absl::string_view attr_name) {
    auto it = item_attrs_.find(attr_name);
    if (it == item_attrs_.end()) {
      auto *attr = new AttrValue(attr_name, for_common_data_);
      attr->SetOwner(this);
      attr->SetIndex(item_attrs_list_.size());
      item_attrs_list_.push_back(attr);
      item_attrs_.insert({attr->name(), attr});
      if (FLAGS_save_desired_attr_type && FLAGS_attr_type_check_level >= 1) {
        auto config_it = attr_types_from_config_.find(attr_name);
        if (config_it != attr_types_from_config_.end()) {
          attr->SetDesiredAttrType(config_it->second);
        }
      }
      return attr;
    }
    return it->second;
  }

  AttrValue *GetOrBorrowAttr(AttrValue *from) {
    auto pr = item_attrs_.insert({absl::string_view(from->name()), from});
    if (pr.second) {
      item_attrs_list_.push_back(from);
    }
    return pr.first->second;
  }

  AttrValue *GetAttr(absl::string_view attr_name) const {
    auto it = item_attrs_.find(attr_name);
    if (it == item_attrs_.end()) {
      return nullptr;
    }
    return it->second;
  }

  CommonRecoResult NewCommonRecoResult(uint64 item_key, int reason, double score, int channel) {
    auto index = GetOrInsertItemAttrIndex(item_key);
    CommonRecoResult result = CommonRecoResult(index, item_key, reason, score, channel);
    result.SetTable(this);
    auto index_addr = GetItemFlatIndexAddr(item_key);
    if (index_addr) {
      result.SetFlatIndexItemAddr(index_addr);
    }
    if (FLAGS_record_filter_reason && index >= attr_index_to_item_.size()) {
      attr_index_to_item_.resize(index + 1, {0, 0, 0, 0});
      attr_index_to_item_[index] = result;
      filter_reason_offsets_.resize(index + 1, -1);
      attr_index_bit_map_.add(index);
    }
    return result;
  }

  CommonRecoResult &AddCommonRecoResult(
      uint64 item_key, int reason, double score, int channel,
      const ks::reco::protoutil::FlattenedAttrKvItem *flat_index_addr = nullptr,
      bool override_reason_attr = false) {
    reco_results_.emplace_back(NewCommonRecoResult(item_key, reason, score, channel));
    auto &result = reco_results_.back();
    if (flat_index_addr && !result.GetFlatIndexItemAddr()) {
      result.SetFlatIndexItemAddr(flat_index_addr);
      SetItemFlatIndexAddr(item_key, flat_index_addr);
    }
    result.SetIntAttr(reason_attr_, result.reason, !override_reason_attr);
    return result;
  }

  // NOTE(zhaoyang09): 该函数并不安全，无法校验多个 item_key 的是否有同一个 index , 并不能填充 flat_index_addr
  // 该函数只应用在解析请求自带 item 时。
  void AddCommonRecoResultFromRequest(CommonRecoResult *result) {
    auto index = result->GetAttrIndex();
    InsertItemAttrIndex(result->item_key, index);
    result->SetTable(this);
    reco_results_.emplace_back(*result);
    if (FLAGS_record_filter_reason) {
      if (index >= attr_index_to_item_.size()) {
        attr_index_to_item_.resize(index + 1, {0, 0, 0, 0});
        filter_reason_offsets_.resize(index + 1, -1);
        attr_index_bit_map_.add(index);
      }
      attr_index_to_item_[index] = *result;
    }
  }

  void SpecifyAttrType(const std::string &attr_name, AttrType attr_type) {
    attr_types_from_config_[attr_name] = attr_type;
  }

  roaring::Roaring *GetResultBitMap() {
    return &attr_index_bit_map_;
  }

  void ReserveFilterResults() {
    if (is_filter_results_reserved_) {
      filter_results_.reserve(attr_index_to_item_.size());
      is_filter_results_reserved_ = false;
    }
  }

  bool AddFilterResult(size_t index, const std::string &filter_reason) {
    if (index >= attr_index_to_item_.size()) return false;
    SetFilterReason(index, filter_reason);
    filter_results_.emplace_back(index);
    return true;
  }

  bool SetFilterReason(size_t index, const std::string &filter_reason) {
    if (index >= filter_reason_offsets_.size()) return false;
    if (filter_reason_offsets_[index] != -1) return false;
    auto it = filter_reason_map_.find(filter_reason);
    if (it == filter_reason_map_.end()) {
      filter_reasons_.emplace_back(filter_reason);
      auto ret = filter_reason_map_.emplace(filter_reason, filter_reasons_.size() - 1);
      it = ret.first;
    }
    filter_reason_offsets_[index] = it->second;
    return true;
  }

  bool SetFilterReason(const CommonRecoResult &result, const std::string &filter_reason) {
    size_t attr_index = result.GetAttrIndex();
    return SetFilterReason(attr_index, filter_reason);
  }

  int GetFilterResultsSize() const {
    return filter_results_.size();
  }

  const CommonRecoResult *GetFilterResult(int index) const {
    if (index < 0 || index >= filter_results_.size()) {
      return nullptr;
    }
    size_t attr_index = filter_results_[index];
    if (attr_index > attr_index_to_item_.size()) {
      return nullptr;
    }
    return &attr_index_to_item_[attr_index];
  }

  absl::optional<absl::string_view> GetFilterReason(const CommonRecoResult &result) const {
    size_t attr_index = result.GetAttrIndex();
    if (attr_index >= filter_reason_offsets_.size()) {
      return absl::nullopt;
    }
    int offset = filter_reason_offsets_[attr_index];
    if (offset == -1 || offset >= filter_reasons_.size()) {
      return absl::nullopt;
    }
    return filter_reasons_[offset];
  }

  void MergeFilterReason(AttrTable *from_table) {
    if (!FLAGS_record_filter_reason) return;
    for (int i = 0; i < from_table->filter_results_.size(); i++) {
      const CommonRecoResult *result = from_table->GetFilterResult(i);
      if (!result) continue;
      auto filter_reason = from_table->GetFilterReason(*result);
      if (filter_reason) {
        size_t attr_index = GetOrInsertItemAttrIndex(result->item_key);
        AddFilterResult(attr_index, {filter_reason->data(), filter_reason->size()});
      }
    }
  }

  void ClearFilterResults();

  const std::vector<AttrValue *> &GetAllItemAttrs() const {
    return item_attrs_list_;
  }

  void DeleteResult(const CommonRecoResult &result) {
    std::remove_if(
        reco_results_.begin(), reco_results_.end(),
        [&result](const CommonRecoResult &reco_result) { return reco_result.item_key == result.item_key; });
  }

  void SetOwner(const CommonRecoContext *context) {
    owner_ = context;
  }

  const CommonRecoContext *GetOwner() const {
    return owner_;
  }

  bool IsOwnedBy(const CommonRecoContext *context) const {
    return owner_ == context;
  }

  bool IsShared() const {
    return is_shared_;
  }

  void SetLogical(bool is_logical) {
    is_logical_ = is_logical;
  }

  bool IsLogical() const {
    return is_logical_;
  }

 private:
  bool IsExternal() {
    return is_external_;
  }

  void MarkExternal() {
    is_external_ = true;
  }

  const std::string &LevelInfo() {
    static const std::string common_level = "common";
    static const std::string item_level = "item";
    if (for_common_data_) {
      return common_level;
    } else {
      return item_level;
    }
  }

  void ClearRecoResults() {
    reco_results_.clear();
  }

 private:
  std::string name_;
  std::shared_ptr<AttrPayload> attr_payload_;
  std::vector<CommonRecoResult> reco_results_;

  // 列数据
  folly::F14FastMap<absl::string_view, AttrValue *, absl::Hash<absl::string_view>> item_attrs_;
  std::vector<AttrValue *> item_attrs_list_;
  // 系统默认 列 提前构造
  ItemAttr *reason_attr_ = nullptr;
  bool for_common_data_ = false;

  std::vector<CommonRecoResult> attr_index_to_item_;
  absl::flat_hash_map<std::string, AttrType> attr_types_from_config_;

  roaring::Roaring attr_index_bit_map_;
  std::vector<size_t> filter_results_;
  std::vector<int32> filter_reason_offsets_;
  std::vector<std::string> filter_reasons_;
  folly::F14FastMap<std::string, int> filter_reason_map_;
  bool is_filter_results_reserved_ = true;

  // 外部管理数据的表，context 不负责此类表的数据回收。
  bool is_external_ = false;
  // 逻辑表，payload 和一部分 attrs 引用自其他物理表。逻辑表不会清理 payload 和引用的 attrs。
  bool is_logical_ = false;
  // 是否被逻辑表引用，被引用的表不能对 attrs 执行 move 语意的操作。
  bool is_shared_ = false;

  // 记录该 AttrTable 由哪个 CommonRecoContext 管理
  const CommonRecoContext *owner_ = nullptr;
};

/**
 * CommonRecoRetrieveResult 用于处理召回结果
 */
struct CommonRecoRetrieveResult {
  // item keysign
  uint64 item_key = 0;
  // 召回初始分数
  double score = 0.0;
  // 推荐原因
  int reason = 0;

  CommonRecoRetrieveResult() {}
  CommonRecoRetrieveResult(uint64 k, int r) : item_key(k), score(0.0), reason(r) {}
  CommonRecoRetrieveResult(uint64 k, int r, double s) : item_key(k), score(s), reason(r) {}
};

}  // namespace platform
}  // namespace ks
