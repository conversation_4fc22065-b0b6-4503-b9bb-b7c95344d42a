#pragma once

#include <lua-5.4.4/src/lua.hpp>

#include <gflags/gflags.h>
#include <algorithm>
#include <iterator>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "base/encoding/url_encode.h"
#include "base/hash_function/city.h"
#include "base/strings/string_number_conversions.h"
#include "base/strings/string_printf.h"
#include "base/strings/string_split.h"
#include "base/strings/string_util.h"
#include "base/time/timestamp.h"
#include "dragon/src/core/common_reco_base.h"
#include "dragon/src/core/common_reco_context_interface.h"
#include "dragon/src/core/common_reco_define.h"
#include "dragon/src/core/common_reco_local_async_waiter.h"
#include "dragon/src/util/common_util.h"
#include "dragon/src/util/logging_util.h"
#include "folly/container/F14Map.h"
#include "ks/common_reco/index/proto/common_index.pb.h"
#include "ks/common_reco/util/key_sign_util.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.pb.h"
#include "learning/kuiba/proto/common_sample_log.pb.h"
#include "serving_base/util/array.h"
#include "serving_base/util/math.h"
#include "serving_base/util/scope_exit.h"
#include "serving_base/utility/system_util.h"
#include "teams/reco-arch/colossusdb/flatkv/flatkv.h"
#include "third_party/abseil/absl/strings/match.h"
#include "third_party/abseil/absl/strings/numbers.h"
#include "third_party/abseil/absl/strings/str_join.h"
#include "third_party/abseil/absl/strings/str_split.h"

namespace ks {
namespace platform {
DECLARE_bool(zero_copy_request_data);
DECLARE_bool(zero_copy_response_data);

#define EXTRACT_SINGLE_ITEM_ATTR(TYPE)                                                 \
  {                                                                                    \
    base::ConstArray<TYPE> value_list;                                                 \
    value_list.SetData(attr_value.value());                                            \
    int i = 0;                                                                         \
    for (TYPE val : value_list) {                                                      \
      while (attr_value.value_length_size() != 0 && attr_value.value_length(i) != 1) { \
        ++i;                                                                           \
      }                                                                                \
      if (i >= items.size()) {                                                         \
        invalid_packed_value = true;                                                   \
        break;                                                                         \
      }                                                                                \
      if (append_value) {                                                              \
        attr_accessor->AppendListValue<TYPE>(items[i].GetAttrIndex(), val);            \
      } else {                                                                         \
        attr_accessor->SetSingularValue<TYPE>(items[i].GetAttrIndex(), val);           \
      }                                                                                \
      stat.AddValue(static_cast<double>(val));                                         \
      ++i;                                                                             \
    }                                                                                  \
  }

#define EXTRACT_LIST_ITEM_ATTR(TYPE)                                                  \
  {                                                                                   \
    base::ConstArray<TYPE> value_list;                                                \
    int i = 0;                                                                        \
    int offset = 0;                                                                   \
    for (int len : attr_value.value_length()) {                                       \
      if (i >= items.size()) {                                                        \
        invalid_packed_value = true;                                                  \
        break;                                                                        \
      }                                                                               \
      if (len >= 0) {                                                                 \
        int data_size = len * sizeof(TYPE);                                           \
        value_list.SetData(attr_value.value().data() + offset, data_size);            \
        if (append_value) {                                                           \
          for (auto val : value_list) {                                               \
            attr_accessor->AppendListValue<TYPE>(items[i].GetAttrIndex(), val);       \
          }                                                                           \
        } else {                                                                      \
          std::vector<TYPE> val(value_list.begin(), value_list.end());                \
          attr_accessor->SetListValue<TYPE>(items[i].GetAttrIndex(), std::move(val)); \
        }                                                                             \
        stat.IncrCount();                                                             \
        offset += data_size;                                                          \
      }                                                                               \
      ++i;                                                                            \
    }                                                                                 \
  }

#define BUILD_SINGLE_PACKED_ITEM_ATTR(TYPE, ENUM)                                                        \
  {                                                                                                      \
    attr_value->set_value_type(PackedItemAttrValue_ValueType_##ENUM);                                    \
    std::for_each(begin, end, [&](const CommonRecoResult &result) {                                      \
      auto val = accessor->GetSingularValue<TYPE>(result.GetAttrIndex(), result.GetFlatIndexItemAddr()); \
      if (val) {                                                                                         \
        ++success_count;                                                                                 \
        TYPE v = *val;                                                                                   \
        payload->append(reinterpret_cast<const char *>(&v), sizeof(TYPE));                               \
        attr_value->add_value_length(1);                                                                 \
      } else {                                                                                           \
        attr_value->add_value_length(-1);                                                                \
      }                                                                                                  \
    });                                                                                                  \
    if (success_count == total_item_num) {                                                               \
      attr_value->clear_value_length();                                                                  \
    }                                                                                                    \
    break;                                                                                               \
  }

#define BUILD_LIST_PACKED_ITEM_ATTR(TYPE, ENUM)                                                      \
  {                                                                                                  \
    attr_value->set_value_type(PackedItemAttrValue_ValueType_##ENUM);                                \
    std::for_each(begin, end, [&](const CommonRecoResult &result) {                                  \
      auto val = accessor->GetListValue<TYPE>(result.GetAttrIndex(), result.GetFlatIndexItemAddr()); \
      if (val) {                                                                                     \
        ++success_count;                                                                             \
        payload->append(reinterpret_cast<const char *>(val->data()), sizeof(TYPE) * val->size());    \
        attr_value->add_value_length(val->size());                                                   \
      } else {                                                                                       \
        attr_value->add_value_length(-1);                                                            \
      }                                                                                              \
    });                                                                                              \
    break;                                                                                           \
  }

enum class AbtestParamType : int { UNKNOWN = -1, INT, DOUBLE, STRING, BOOLEAN };

enum class KconfParamType : int {
  UNKNOWN = -1,
  INT64,
  DOUBLE,
  STRING,
  BOOLEAN,
  INT64_LIST,
  DOUBLE_LIST,
  STRING_LIST,
  BOOLEAN_LIST,
  JSON,
  INT64_SET,
  STRING_SET,
  STRING_BOOL_MAP,
  STRING_INT64_MAP,
  STRING_DOUBLE_MAP,
  STRING_STRING_MAP,
  TAIL_NUMBER
};

enum class ProcessorType : int { BASE = 0, RETRIEVER, ENRICHER, ARRANGER, OBSERVER, MIXER };

enum class DegraderType : int { UNKNOWN = -1, CIRCUIT_BREAKER, BULK_HEAD, ADAPTIVE_LIMITER, RANDOM_DROP };

enum class AnnUserKeyMode : int { UNKNOWN = 0, DID_ONLY = 1, UID_ONLY, UID_OR_DID };

struct AttrConfig {
  std::string alias;
  CopyMode copy_mode = CopyMode::OVERWRITE;
};

struct TableAttrs {
  std::string table_name;
  std::vector<std::string> attrs;
  folly::F14FastMap<std::string, AttrConfig> copy_attrs;
};

class StatisticInfo {
 public:
  int count() const {
    return count_;
  }
  double sum() const {
    return sum_;
  }
  double max() const {
    return max_;
  }
  double avg() const {
    if (count_ == 0) return 0.0;
    return sum_ / count_;
  }

  void AddValue(double val) {
    if (count_ == 0) {
      max_ = val;
    } else {
      if (val > max_) {
        max_ = val;
      }
    }
    sum_ += val;
    ++count_;
  }

  void IncrCount(int count = 1) {
    count_ += count;
  }

 private:
  int count_ = 0;
  double sum_ = 0.0;
  double max_ = 0.0;
};

class PredictUtil {
 public:
  static bool IsInvalidUser(const uint64 user_id, const std::string &device_id) {
    return (user_id <= 0 && device_id.empty());
  }
  static bool IsUnLoginUser(const uint64 user_id) {
    return (user_id <= 0);
  }
};

class RecoUtil {
  using AttrTypeMap = std::unordered_map<std::string, AttrType>;
  using AbtestParamTypeMap = std::unordered_map<std::string, AbtestParamType>;
  using KconfParamTypeMap = std::unordered_map<std::string, KconfParamType>;
  using DegraderTypeMap = std::unordered_map<std::string, DegraderType>;
  using AnnUserKeyModeMap = std::unordered_map<std::string, AnnUserKeyMode>;

 public:
  static void ResetThreadLocals(const ReadableRecoContextInterface *context) {
    LoggingUtil::ResetLoggingEnabled(context->GetUserId(), context->GetDeviceId());
    GlobalHolder::SetCurrentRequestType(context->GetRequestType());
    AttrIOCheckUtil::Reset(context->GetProcessCounter());
  }

  static bool ParseTableAttrs(const base::Json *config, std::vector<TableAttrs> *table_attrs_vec) {
    if (config && config->IsArray()) {
      for (const auto *table_config : config->array()) {
        if (table_config->IsObject()) {
          std::string table_name = table_config->GetString("table_name");
          TableAttrs node;
          node.table_name = table_name;

          const auto *attrs = table_config->Get("attrs");
          if (attrs && attrs->IsArray()) {
            for (const auto *attr : attrs->array()) {
              if (attr->IsString()) {
                node.attrs.push_back(attr->StringValue());
              }
            }
          }

          table_attrs_vec->push_back(std::move(node));
        }
      }
    }
    return true;
  }

  static bool ParseAttrsConfig(const base::Json *attrs_config,
                               std::unordered_map<std::string, std::string> *attrs_map) {
    if (attrs_config && attrs_config->IsArray()) {
      for (const auto *attr : attrs_config->array()) {
        if (attr->IsObject()) {
          std::string attr_name = attr->GetString("name");
          if (attr_name.empty()) {
            return false;
          }

          std::string alias = attr->GetString("as", attr_name);
          attrs_map->emplace(attr_name, alias);
        } else if (attr->IsString()) {
          const std::string &attr_name = attr->StringValue();
          if (attr_name.empty()) {
            return false;
          }
          attrs_map->emplace(attr_name, attr_name);
        }
      }
    }
    return true;
  }

  static bool ParseAttrsConfig(const base::Json *attrs_config,
                               folly::F14FastMap<std::string, AttrConfig> *attrs_map) {
    if (attrs_config && attrs_config->IsArray()) {
      for (const auto attr_config : attrs_config->array()) {
        if (attr_config->IsString()) {
          auto attr_name = attr_config->StringValue();
          if (attr_name.empty()) {
            return false;
          }
          AttrConfig cfg;
          cfg.alias = attr_name;
          cfg.copy_mode = CopyMode::OVERWRITE;
          attrs_map->insert({attr_name, cfg});
        } else if (attr_config->IsObject()) {
          auto attr_name = attr_config->GetString("name", "");
          if (attr_name.empty()) {
            return false;
          }
          std::string copy_mode;
          auto *copy_mode_config = attr_config->Get("copy_mode");
          if (copy_mode_config) {
            copy_mode = copy_mode_config->StringValue("OVERWRITE");
          } else {
            copy_mode = attr_config->GetString("merge_mode", "OVERWRITE");
          }
          auto alias = attr_config->GetString("as", attr_name);
          AttrConfig cfg;
          cfg.alias = alias;
          cfg.copy_mode = TransCopyModeFromString(copy_mode);
          attrs_map->insert({attr_name, cfg});
          if (cfg.copy_mode == CopyMode::UNKNOWN) {
            LOG(ERROR) << "CommonRecoTableGroupByMixer init failed! 'copy_mode' is not available.";
            return false;
          }
        } else {
          LOG(ERROR) << "CommonRecoTableGroupByMixer init failed! 'copy_attrs' must be list of string or "
                        "dict.";
          return false;
        }
      }
    }
    return true;
  }

  // 分片原理：根据 expected_partition_size 估算分片大小，在此基础上保证每个片之间的大小差距尽量小
  static std::vector<int> BalancedPartition(int expected_partition_size, int total_num) {
    std::vector<int> partitions;
    if (total_num > 0 && expected_partition_size > 0) {
      int partition_num = std::ceil(total_num / (double)expected_partition_size);
      int partition_size = total_num / partition_num;
      int remain_num = total_num - partition_size * partition_num;

      partitions.resize(partition_num, partition_size);
      for (int i = 0; i < remain_num; ++i) {
        partitions[i]++;
      }
    }
    return partitions;
  }

  static AttrType ParseAttrType(const std::string &type) {
    auto it = attr_type_map_.find(type);
    return it == attr_type_map_.end() ? AttrType::UNKNOWN : it->second;
  }

  static const std::string &GetAttrTypeName(AttrType type) {
    static const std::string kTextUnknown = "UNKNOWN";
    int index = static_cast<int>(type);
    if (0 <= index && index < attr_type_name_vec_.size()) {
      return attr_type_name_vec_[index];
    }
    return kTextUnknown;
  }

  static uint64 ResetItemType(int reset_type, uint64 item_key) {
    if (reset_type >= 0) {
      return Util::GenKeysign(reset_type, Util::GetId(item_key));
    }
    return item_key;
  }

  static bool OnlyUpperDigitUnderline(absl::string_view attr_name) {
    for (char c : attr_name) {
      if (!(std::isdigit(c) || c == '_' || (std::isalpha(c) && std::isupper(c)))) {
        return false;
      }
    }
    return true;
  }

  static bool IsBuiltInAttrName(absl::string_view attr_name) {
    if (attr_name.size() <= 0) {
      return false;
    }
    if (attr_name.front() == '$') {
      return true;
    }
    if (attr_name.front() == '_' && attr_name.back() == '_' && OnlyUpperDigitUnderline(attr_name)) {
      return true;
    }
    return false;
  }

  static CommonIndexEnum::AttrType CastCommonIndexAttrType(AttrType type) {
    switch (type) {
      case AttrType::INT:
        return CommonIndexEnum::INT_ATTR;
      case AttrType::FLOAT:
        return CommonIndexEnum::FLOAT_ATTR;
      case AttrType::STRING:
        return CommonIndexEnum::STRING_ATTR;
      case AttrType::INT_LIST:
        return CommonIndexEnum::INT_LIST_ATTR;
      case AttrType::FLOAT_LIST:
        return CommonIndexEnum::FLOAT_LIST_ATTR;
      case AttrType::STRING_LIST:
        return CommonIndexEnum::STRING_LIST_ATTR;
      default:
        return CommonIndexEnum::UNKNOW_ATTR;
    }
  }

  static AttrType CastAttrTypeFromCommonIndex(CommonIndexEnum::AttrType type) {
    switch (type) {
      case CommonIndexEnum::INT_ATTR:
        return AttrType::INT;
      case CommonIndexEnum::FLOAT_ATTR:
        return AttrType::FLOAT;
      case CommonIndexEnum::STRING_ATTR:
        return AttrType::STRING;
      case CommonIndexEnum::INT_LIST_ATTR:
        return AttrType::INT_LIST;
      case CommonIndexEnum::FLOAT_LIST_ATTR:
        return AttrType::FLOAT_LIST;
      case CommonIndexEnum::STRING_LIST_ATTR:
        return AttrType::STRING_LIST;
      default:
        return AttrType::UNKNOWN;
    }
  }

  static AttrType CastAttrTypeFromFlatKv(FlatKvValueType type) {
    switch (type) {
      case FlatKvValueType::kInt:
        return AttrType::INT;
      case FlatKvValueType::kFloat:
        return AttrType::FLOAT;
      case FlatKvValueType::kString:
        return AttrType::STRING;
      case FlatKvValueType::kIntList:
        return AttrType::INT_LIST;
      case FlatKvValueType::kFloatList:
        return AttrType::FLOAT32_LIST;
      case FlatKvValueType::kStringList:
        return AttrType::STRING_LIST;
      default:
        return AttrType::UNKNOWN;
    }
  }

  static AbtestParamType ParseAbtestParamType(const std::string &type) {
    auto it = ab_param_type_map_.find(type);
    return it == ab_param_type_map_.end() ? AbtestParamType::UNKNOWN : it->second;
  }

  static KconfParamType ParseKconfParamType(const std::string &type) {
    auto it = kconf_param_type_map_.find(type);
    return it == kconf_param_type_map_.end() ? KconfParamType::UNKNOWN : it->second;
  }

  static DegraderType ParseDegraderType(const std::string &type) {
    auto it = degrader_type_map_.find(type);
    return it == degrader_type_map_.end() ? DegraderType::UNKNOWN : it->second;
  }

  static AnnUserKeyMode ParseAnnUserKeyMode(const std::string &type) {
    auto it = ann_user_key_mode_map_.find(type);
    return it == ann_user_key_mode_map_.end() ? AnnUserKeyMode::UNKNOWN : it->second;
  }

  static absl::optional<absl::string_view> ExtractCommonAttrFromExpr(const base::Json *json) {
    if (!json || !json_is_string(json->get())) return absl::nullopt;
    return ExtractCommonAttrFromExpr(json_string_value(json->get()));
  }

  static bool IsDynamicParameter(absl::string_view expr) {
    return absl::StartsWith(expr, "{{") && absl::EndsWith(expr, "}}");
  }

  static bool IsLuaExpr(absl::string_view expr) {
    return absl::StartsWith(expr, "return ");
  }

  static absl::optional<absl::string_view> ExtractCommonAttrFromExpr(absl::string_view expr) {
    // 如果是 {{ }} 格式的动态参数，则抽取中间部分
    if (IsDynamicParameter(expr)) {
      return expr.substr(2, expr.size() - 4);
    }
    return absl::nullopt;
  }

  template <typename T>
  static std::enable_if_t<std::is_base_of<google::protobuf::Message, T>::value, std::shared_ptr<const T>>
  GetOrParseProtoMessage(const ReadableRecoContextInterface *context, absl::string_view attr_name) {
    auto *ptr = context->GetProtoMessagePtrCommonAttr<T>(attr_name);
    if (ptr) return {ptr, [](auto) {}};
    auto str_val = context->GetStringCommonAttr(attr_name);
    if (!str_val || str_val->empty()) return nullptr;
    auto msg = std::make_shared<T>();
    if (msg->ParseFromArray(str_val->data(), str_val->size())) {
      return std::move(msg);
    } else {
      return nullptr;
    }
  }

  static int64 GetDynamicIntParamFromContext(const ReadableRecoContextInterface *context,
                                             const base::Json *json, int64 default_val) {
    GetDynamicIntParamFromContext(context, json, &default_val);
    return default_val;
  }

  static bool GetDynamicIntParamFromContext(const ReadableRecoContextInterface *context,
                                            const base::Json *json, int64 *int_val) {
    if (!json) {
      return false;
    }
    if (json->IsInteger()) {
      return json->IntValue(int_val);
    }
    if (json->IsString()) {
      if (auto attr_name = RecoUtil::ExtractCommonAttrFromExpr(json)) {
        auto val = context->GetIntCommonAttr(*attr_name);
        if (val) {
          *int_val = *val;
          return true;
        }
      }
    }
    return false;
  }

  static absl::string_view GetDynamicStringParamFromContext(const ReadableRecoContextInterface *context,
                                                            const base::Json *json,
                                                            absl::string_view default_val = "") {
    GetDynamicStringParamFromContext(context, json, &default_val);
    return default_val;
  }

  static bool GetDynamicStringParamFromContext(const ReadableRecoContextInterface *context,
                                               const base::Json *json, absl::string_view *sv) {
    if (!json) {
      return false;
    }
    if (json->IsString()) {
      if (auto attr_name = RecoUtil::ExtractCommonAttrFromExpr(json)) {
        auto val = context->GetStringCommonAttr(*attr_name);
        if (val) {
          *sv = *val;
          return true;
        }
      } else {
        *sv = absl::NullSafeStringView(json_string_value(json->get()));
        return true;
      }
    }
    return false;
  }

  static std::string GetRequestInfoForLog(const ReadableRecoContextInterface *context) {
    std::string result = " | kess_service: " + GlobalHolder::GetServiceIdentifier();
    if (context) {
      result += ", request_type: " + context->GetRequestType() + ", request_id: " + context->GetRequestId() +
                ", user_id: " + base::Uint64ToString(context->GetUserId()) +
                ", device_id: " + context->GetDeviceId() +
                ", is_debug: " + (context->IsDebugRequest() ? "true" : "false");
    }
    return result;
  }

  template <template <typename...> class Container>
  static bool ExtractIntListFromJsonConfig(const base::Json *json, Container<int64> *result,
                                           bool strict = false) {
    if (!json || !json->IsArray()) return false;
    for (const auto *attr : json->array()) {
      int64 val;
      if (attr->IntValue(&val)) {
        result->push_back(val);
      } else if (strict) {
        return false;
      }
    }
    return true;
  }

  template <template <typename...> class Container>
  static bool ExtractIntSetFromJsonConfig(const base::Json *json, Container<int64> *result,
                                          bool strict = false, bool allow_single_value = false) {
    if (!json) return false;
    int64 single_val;
    if (allow_single_value && json->IntValue(&single_val)) {
      result->insert(single_val);
      return true;
    }
    if (!json->IsArray()) return false;
    for (const auto *attr : json->array()) {
      if (attr->IntValue(&single_val)) {
        result->insert(single_val);
      } else if (strict) {
        return false;
      }
    }
    return true;
  }

  template <template <typename...> class Container>
  static bool ExtractStringListFromJsonConfig(const base::Json *json, Container<std::string> *result,
                                              bool skip_empty_str = true, bool strict = false) {
    if (!json || !json->IsArray()) return false;
    for (const auto *attr : json->array()) {
      if (strict && !attr->IsString()) {
        return false;
      }
      std::string val = attr->StringValue();
      if (!skip_empty_str || !val.empty()) {
        result->push_back(std::move(val));
      }
    }
    return true;
  }

  template <template <typename...> class Container>
  static bool ExtractStringSetFromJsonConfig(const base::Json *json, Container<std::string> *result,
                                             bool skip_empty_str = true, bool strict = false) {
    if (!json || !json->IsArray()) return false;
    for (const auto *attr : json->array()) {
      if (strict && !attr->IsString()) {
        return false;
      }
      std::string val = attr->StringValue();
      if (!skip_empty_str || !val.empty()) {
        result->insert(std::move(val));
      }
    }
    return true;
  }

  template <template <typename...> class Container>
  static bool ExtractStringMapFromJsonConfig(const base::Json *json,
                                             Container<std::string, std::string> *result,
                                             bool skip_empty_str = true, bool strict = false) {
    if (!json || !json->IsObject()) return false;
    for (auto &kv : json->objects()) {
      const base::Json *val = kv.second;
      if (val == nullptr || (strict && !val->IsString())) {
        return false;
      }
      std::string val_str = val->StringValue();
      if (!skip_empty_str || !val_str.empty()) {
        result->insert({kv.first, val_str});
      }
    }
    return true;
  }

  template <template <typename...> class Container>
  static bool ExtractDoubleListFromJsonConfig(const base::Json *json, Container<double> *result,
                                              bool strict = false) {
    if (!json || !json->IsArray()) return false;
    for (const auto *attr : json->array()) {
      if (strict && !attr->IsDouble() && !attr->IsInteger()) {
        return false;
      }
      result->push_back(attr->NumberValue(0.0f));
    }
    return true;
  }

  template <template <typename...> class Container>
  static bool ExtractFloatListFromJsonConfig(const base::Json *json, Container<float> *result,
                                             bool strict = false) {
    if (!json || !json->IsArray()) return false;
    for (const auto *attr : json->array()) {
      if (strict && !attr->IsDouble() && !attr->IsInteger()) {
        return false;
      }
      result->push_back(attr->NumberValue(0.0f));
    }
    return true;
  }

  template <template <typename...> class Container>
  static bool ExtractAttrListFromJsonConfig(const base::Json *attrs_config,
                                            Container<std::string, std::string> *attrs_map) {
    if (!attrs_config || !attrs_config->IsArray()) {
      return false;
    }

    for (const auto *c : attrs_config->array()) {
      if (c->IsObject()) {
        std::string attr_name = c->GetString("name");
        if (attr_name.empty()) {
          return false;
        }
        std::string alias = c->GetString("as", attr_name);
        attrs_map->emplace(std::move(attr_name), std::move(alias));
      } else if (c->IsString()) {
        std::string attr_name = c->StringValue();
        if (attr_name.empty()) {
          return false;
        }
        attrs_map->emplace(attr_name, attr_name);
      }
    }

    return true;
  }

  // 等待当前所有的 callback response, 返回 pair: <callback 数目, 等待耗时>
  static std::pair<int, int64> WaitCallbacks(MutableRecoContextInterface *context,
                                             const std::string &trigger_name,
                                             const std::string &trigger_alias) {
    base::ScopeExit on_scope_exit([context]() { context->SetRunningProcessor(nullptr); });
    int64 local_wait_duration = 0;
    auto *local_async_waiter = context->GetLocalAsyncWaiter(trigger_name, false);
    int local_async_num = local_async_waiter ? local_async_waiter->Size() : 0;
    int64 rpc_wait_duration = 0;
    auto *batch_waiter = context->GetBatchWaiter(trigger_name, false);
    int rpc_num = batch_waiter ? batch_waiter->Size() : 0;
    if (local_async_num > 0 || rpc_num > 0) {
      CL_LOG(INFO) << "waiting for " << local_async_num << " local and " << rpc_num
                   << " rpc async callback(s) return... before: " << trigger_alias;
    }

    if (local_async_num > 0) {
      int64 start_ts = base::GetTimestamp();
      local_async_waiter->Wait();
      local_wait_duration = base::GetTimestamp() - start_ts;
    }
    if (rpc_num > 0) {
      int64 start_ts = base::GetTimestamp();
      batch_waiter->Wait();
      rpc_wait_duration = base::GetTimestamp() - start_ts;
    }

    int total_wait_num = rpc_num + local_async_num;
    int total_wait_time = rpc_wait_duration + local_wait_duration;
    if (total_wait_num > 0) {
      std::vector<std::string> *async_upstream_processors =
          context->GetAsyncUpstreamProcessors(trigger_name, false);
      std::string upstream_processor_names;
      if (async_upstream_processors && async_upstream_processors->size() > 0) {
        const size_t max_len = 10;
        auto beg = async_upstream_processors->begin();
        auto end = beg + std::min(max_len, async_upstream_processors->size());
        upstream_processor_names = absl::StrJoin(beg, end, ", ");
        if (async_upstream_processors->size() > max_len) {
          upstream_processor_names.append("...");
        }
        async_upstream_processors->clear();
      }
      CL_LOG(INFO) << local_async_num << " local async callback(s) executed in "
                   << local_wait_duration / 1000.0 << " ms, " << rpc_num
                   << " rpc async callback(s) executed in " << rpc_wait_duration / 1000.0
                   << " ms, before: " << trigger_alias << ", produced by: " << upstream_processor_names;
    }

    return std::make_pair(total_wait_num, total_wait_time);
  }

  static uint64 GenUserHash(uint64 uid, const std::string &did) {
    return uid == 0 ? base::CityHash64(did.data(), did.size()) : uid;
  }

  template <typename Iter>
  static int BuildPackedColumnsPayload(Iter begin, Iter end, AttrValue *accessor,
                                       PackedItemAttrValue *attr_value, bool check_multi_table = false) {
    int success_count = 0;
    const int total_item_num = std::distance(begin, end);
    std::string *payload = attr_value->mutable_value();
    attr_value->mutable_value_length()->Reserve(total_item_num);
    if (check_multi_table && accessor->value_type == AttrType::UNKNOWN) {
      for (auto it = begin; it != end; it++) {
        const AttrTable *table = it->GetTable();
        AttrValue *attr_accessor = table->GetAttr(accessor->name());
        if (attr_accessor && attr_accessor->value_type != AttrType::UNKNOWN) {
          accessor = attr_accessor;
          break;
        }
      }
    }
    switch (accessor->value_type) {
      case AttrType::INT:
        attr_value->set_type(kuiba::CommonSampleEnum::INT_ATTR);
        attr_value->set_value_type(PackedItemAttrValue_ValueType_INT64);
        if (FLAGS_zero_copy_response_data) {
          attr_value->set_value_concat_mode(
              ks::platform::PackedItemAttrValue_ValueConcatMode_ALIGN_WITH_ITEM_INDEX);
          std::swap(*payload, accessor->single_values);
          auto *value_length = attr_value->mutable_value_length();
          value_length->Resize(total_item_num, 0);
          int i = 0;
          for (auto it = begin; it != end; it++, i++) {
            if (accessor->HasValue(it->GetAttrIndex())) {
              value_length->Set(i, 1);
              success_count++;
            } else {
              value_length->Set(i, -1);
            }
          }
        } else {
          std::for_each(begin, end, [&](const CommonRecoResult &result) {
            auto val = result.GetIntAttr(accessor, check_multi_table);
            if (val) {
              ++success_count;
              int64 v = *val;
              payload->append(reinterpret_cast<const char *>(&v), sizeof(int64));
              attr_value->add_value_length(1);
            } else {
              attr_value->add_value_length(-1);
            }
          });
        }
        if (success_count == total_item_num) {
          attr_value->clear_value_length();
        }
        break;

      case AttrType::FLOAT:
        attr_value->set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
        attr_value->set_value_type(PackedItemAttrValue_ValueType_FLOAT64);
        if (FLAGS_zero_copy_response_data) {
          attr_value->set_value_concat_mode(
              ks::platform::PackedItemAttrValue_ValueConcatMode_ALIGN_WITH_ITEM_INDEX);
          std::swap(*payload, accessor->single_values);
          auto *value_length = attr_value->mutable_value_length();
          value_length->Resize(total_item_num, 0);
          int i = 0;
          for (auto it = begin; it != end; it++, i++) {
            if (accessor->HasValue(it->GetAttrIndex())) {
              value_length->Set(i, 1);
              success_count++;
            } else {
              value_length->Set(i, -1);
            }
          }
        } else {
          std::for_each(begin, end, [&](const CommonRecoResult &result) {
            auto val = result.GetDoubleAttr(accessor, check_multi_table);
            if (val) {
              ++success_count;
              double v = *val;
              payload->append(reinterpret_cast<const char *>(&v), sizeof(double));
              attr_value->add_value_length(1);
            } else {
              attr_value->add_value_length(-1);
            }
          });
        }
        if (success_count == total_item_num) {
          attr_value->clear_value_length();
        }
        break;

      case AttrType::STRING: {
        attr_value->set_type(kuiba::CommonSampleEnum::STRING_ATTR);
        attr_value->set_value_type(PackedItemAttrValue_ValueType_STRING);
        size_t payload_capacity = 0;
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          auto val = result.GetStringAttr(accessor, check_multi_table);
          if (val) {
            payload_capacity += val->size();
          }
        });
        payload->reserve(payload_capacity);
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          auto val = result.GetStringAttr(accessor, check_multi_table);
          if (val) {
            ++success_count;
            payload->append(val->data(), val->size());
            attr_value->add_value_length(val->size());
          } else {
            attr_value->add_value_length(-1);
          }
        });
        break;
      }
      case AttrType::INT_LIST:
        attr_value->set_type(kuiba::CommonSampleEnum::INT_LIST_ATTR);
        attr_value->set_value_type(PackedItemAttrValue_ValueType_INT64_LIST);
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          auto val = result.GetIntListAttr(accessor, check_multi_table);
          if (val) {
            ++success_count;
            payload->append(reinterpret_cast<const char *>(val->data()), sizeof(int64) * val->size());
            attr_value->add_value_length(val->size());
          } else {
            attr_value->add_value_length(-1);
          }
        });
        break;

      case AttrType::FLOAT_LIST:
        attr_value->set_type(kuiba::CommonSampleEnum::FLOAT_LIST_ATTR);
        attr_value->set_value_type(PackedItemAttrValue_ValueType_FLOAT64_LIST);
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          auto val = result.GetDoubleListAttr(accessor, check_multi_table);
          if (val) {
            ++success_count;
            payload->append(reinterpret_cast<const char *>(val->data()), sizeof(double) * val->size());
            attr_value->add_value_length(val->size());
          } else {
            attr_value->add_value_length(-1);
          }
        });
        break;

      case AttrType::STRING_LIST: {
        attr_value->set_type(kuiba::CommonSampleEnum::STRING_LIST_ATTR);
        attr_value->set_value_type(PackedItemAttrValue_ValueType_STRING_LIST);
        size_t payload_capacity = 0;
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          auto val = result.GetStringListAttr(accessor, check_multi_table);
          if (val) {
            for (auto &str : *val) {
              payload_capacity += str.size();
            }
          }
        });
        payload->reserve(payload_capacity);
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          auto val = result.GetStringListAttr(accessor, check_multi_table);
          if (val) {
            ++success_count;
            attr_value->add_value_length(val->size());
            for (auto str : *val) {
              payload->append(str.data(), str.size());
              attr_value->add_value_length(str.size());
            }
          } else {
            attr_value->add_value_length(-1);
          }
        });
        break;
      }

      case AttrType::INT32:
        BUILD_SINGLE_PACKED_ITEM_ATTR(int32, INT32);
      case AttrType::INT16:
        BUILD_SINGLE_PACKED_ITEM_ATTR(int16, INT16);
      case AttrType::INT8:
        BUILD_SINGLE_PACKED_ITEM_ATTR(int8, INT8);
      case AttrType::FLOAT32:
        BUILD_SINGLE_PACKED_ITEM_ATTR(float, FLOAT32);
      case AttrType::FLOAT16:
        BUILD_SINGLE_PACKED_ITEM_ATTR(float16_t, FLOAT16);
      case AttrType::INT32_LIST:
        BUILD_LIST_PACKED_ITEM_ATTR(int32, INT32_LIST);
      case AttrType::INT16_LIST:
        BUILD_LIST_PACKED_ITEM_ATTR(int16, INT16_LIST);
      case AttrType::INT8_LIST:
        BUILD_LIST_PACKED_ITEM_ATTR(int8, INT8_LIST);
      case AttrType::FLOAT32_LIST:
        BUILD_LIST_PACKED_ITEM_ATTR(float, FLOAT32_LIST);
      case AttrType::FLOAT16_LIST:
        BUILD_LIST_PACKED_ITEM_ATTR(float16_t, FLOAT16_LIST);
      case AttrType::EXTRA: {
        int extra_value_type = 0;
        std::find_if(begin, end, [&](const CommonRecoResult &result) {
          if (result.GetPtrAttr<::google::protobuf::Message>(accessor, check_multi_table)) {
            extra_value_type = 1;
            return true;
          }
          if (result.GetPtrAttr<const std::vector<float>>(accessor, check_multi_table)) {
            extra_value_type = 2;
            return true;
          }
          return false;
        });

        if (extra_value_type == 1) {
          attr_value->set_type(kuiba::CommonSampleEnum::STRING_ATTR);
          std::for_each(begin, end, [&](const CommonRecoResult &result) {
            if (auto *p = result.GetPtrAttr<::google::protobuf::Message>(accessor, check_multi_table)) {
              ++success_count;
              const std::string &val = p->SerializeAsString();
              payload->append(val);
              attr_value->add_value_length(val.size());
            } else {
              attr_value->add_value_length(-1);
            }
          });
        } else if (extra_value_type == 2) {
          attr_value->set_type(kuiba::CommonSampleEnum::FLOAT_LIST_ATTR);
          std::for_each(begin, end, [&](const CommonRecoResult &result) {
            if (auto *p = result.GetPtrAttr<const std::vector<float>>(accessor, check_multi_table)) {
              ++success_count;
              for (float val : *p) {
                double v = val;
                payload->append(reinterpret_cast<const char *>(&v), sizeof(double));
              }
              attr_value->add_value_length(p->size());
            } else {
              attr_value->add_value_length(-1);
            }
          });
        } else {
          attr_value->set_type(kuiba::CommonSampleEnum::UNKNOWN_ATTR);
        }
        break;
      }
      default:
        attr_value->set_type(kuiba::CommonSampleEnum::UNKNOWN_ATTR);
        break;
    }
    return success_count;
  }

  template <typename Iter>
  static std::vector<int> BuildPackedTableColumns(
      Iter begin, Iter end, const std::vector<AttrValue *> &attr_accessors, DataTable *item_data,
      folly::F14FastMap<int, int> *item_type_count = nullptr,
      folly::F14FastMap<int, int> *reason_count = nullptr,
      const std::vector<absl::string_view> *rename_attrs = nullptr) {
    const int total_item_num = std::distance(begin, end);
    std::vector<int> counter(attr_accessors.size(), 0);
    std::for_each(begin, end,
                  [item_data, item_type_count, reason_count](const CommonRecoResult &result) mutable {
                    auto *item = item_data->add_item_list();
                    item->set_index(result.GetAttrIndex());
                    item->set_item_key(result.item_key);
                    item->set_reason(result.reason);
                    item->set_score(result.score);
                    if (item_type_count) {
                      ++(*item_type_count)[result.GetType()];
                    }
                    if (reason_count) {
                      ++(*reason_count)[result.reason];
                    }
                  });
    for (int i = 0; i < attr_accessors.size(); ++i) {
      auto *accessor = attr_accessors[i];
      auto *column = item_data->add_columns();
      if (rename_attrs && i < rename_attrs->size()) {
        const auto &name = rename_attrs->at(i);
        if (name.empty()) {
          column->set_name(accessor->name());
        } else {
          column->set_name(name.data(), name.size());
        }
      } else {
        column->set_name(accessor->name());
      }
      counter[i] = BuildPackedColumnsPayload(begin, end, accessor, column);
      VLOG(100) << "build packed table columns for " << accessor->name() << ", succ: " << counter[i]
                << ", total: " << total_item_num;
    }
    return counter;
  }

  template <typename Iter>
  static std::vector<int> BuildPackedItemAttrFromItems(
      Iter begin, Iter end, const std::vector<ItemAttr *> &attr_accessors, PackedItemAttr *packed_item_attr,
      const std::vector<absl::string_view> *rename_attrs = nullptr,
      const std::vector<uint64> *replace_item_keys = nullptr, bool check_multi_table = false) {
    const int total_item_num = std::distance(begin, end);
    packed_item_attr->Clear();
    if (replace_item_keys) {
      CHECK_EQ(total_item_num, replace_item_keys->size());
      for (uint64 item_key : *replace_item_keys) {
        packed_item_attr->add_item_keys(item_key);
      }
    } else {
      std::for_each(begin, end, [packed_item_attr](const CommonRecoResult &result) {
        packed_item_attr->add_item_keys(result.item_key);
      });
    }

    std::vector<int> counter(attr_accessors.size(), 0);
    if (attr_accessors.empty()) return counter;

    for (int i = 0; i < attr_accessors.size(); ++i) {
      int &success_count = counter[i];
      auto *accessor = attr_accessors[i];
      auto *attr_value = packed_item_attr->add_attr_values();
      if (rename_attrs && i < rename_attrs->size()) {
        const auto &name = rename_attrs->at(i);
        if (name.empty()) {
          attr_value->set_name(accessor->name());
        } else {
          attr_value->set_name(name.data(), name.size());
        }
      } else {
        attr_value->set_name(accessor->name());
      }

      success_count = BuildPackedColumnsPayload(begin, end, accessor, attr_value, check_multi_table);

      VLOG(100) << "build packed item attr for " << accessor->name() << ", succ: " << success_count
                << ", total: " << total_item_num;
    }

    return counter;
  }

  static bool AlignPackedItemAttrNum(
      const PackedItemAttr &packed_item_attr, const std::vector<CommonRecoResult> &items,
      MutableRecoContextInterface *context, std::vector<CommonRecoResult> *aligned_items,
      const folly::F14FastMap<uint64, std::vector<CommonRecoResult>> *item_key_mapping = nullptr) {
    folly::F14FastMap<uint64, int> item_key_pos;
    const int packed_item_num = packed_item_attr.item_keys_size();
    bool need_align = false;
    if (packed_item_num == items.size()) {
      for (int i = 0; i < items.size(); ++i) {
        uint64 item_key = packed_item_attr.item_keys(i);
        if (item_key_mapping != nullptr && !item_key_mapping->empty()) {
          auto item_key_it = item_key_mapping->find(item_key);
          int &pos = item_key_pos[item_key];
          if (item_key_it != item_key_mapping->end() && item_key_it->second.size() > pos) {
            item_key = item_key_it->second[pos].item_key;
            pos++;
          }
        }
        if (items[i].item_key != item_key) {
          need_align = true;
          break;
        }
      }
    } else {
      need_align = true;
    }

    item_key_pos.clear();
    if (need_align && context && aligned_items) {
      aligned_items->clear();
      aligned_items->reserve(packed_item_num);
      for (uint64 item_key : packed_item_attr.item_keys()) {
        const CommonRecoResult *result = nullptr;
        if (item_key_mapping != nullptr && !item_key_mapping->empty()) {
          auto item_key_it = item_key_mapping->find(item_key);
          int &pos = item_key_pos[item_key];
          if (item_key_it != item_key_mapping->end() && item_key_it->second.size() > pos) {
            result = &(item_key_it->second[pos]);
            pos++;
          }
        }
        if (result) {
          aligned_items->emplace_back(*result);
        } else {
          aligned_items->emplace_back(context->NewCommonRecoResult(item_key, -1));
        }
      }
    }

    return need_align;
  }

  static bool ExtractPackedItemAttrToContext(ItemAttr *attr_accessor, const PackedItemAttrValue &attr_value,
                                             const CommonRecoResult &result, bool append_value = false) {
    std::vector<CommonRecoResult> items;
    items.push_back(result);
    auto static_info = ExtractPackedItemAttrToContext(attr_accessor, attr_value, &items, append_value);
    if (static_info.count()) {
      return true;
    } else {
      return false;
    }
  }

  static StatisticInfo ExtractPackedItemAttrToContext(ItemAttr *attr_accessor,
                                                      const PackedItemAttrValue &attr_value,
                                                      const std::vector<CommonRecoResult> *p_items,
                                                      bool append_value = false,
                                                      bool check_multi_table = false) {
    StatisticInfo stat;
    if (!p_items) return stat;

    const auto &items = *p_items;
    base::ConstArray<int64> int_list;
    base::ConstArray<double> double_list;
    bool invalid_packed_value = false;
    if (attr_value.type() == kuiba::CommonSampleEnum::INT_ATTR ||
        attr_value.value_type() == PackedItemAttrValue_ValueType_INT64) {
      int_list.SetData(attr_value.value());
      int i = 0;
      for (int64 val : int_list) {
        while (attr_value.value_length_size() != 0 && attr_value.value_length(i) != 1) {
          ++i;
        }
        if (i >= items.size()) {
          invalid_packed_value = true;
          break;
        }
        if (append_value) {
          items[i].AppendIntListAttr(attr_accessor, val, check_multi_table);
        } else {
          items[i].SetIntAttr(attr_accessor, val, false, true, check_multi_table);
        }
        stat.AddValue(static_cast<double>(val));
        ++i;
      }
    } else if (attr_value.type() == kuiba::CommonSampleEnum::FLOAT_ATTR ||
               attr_value.value_type() == PackedItemAttrValue_ValueType_FLOAT64) {
      double_list.SetData(attr_value.value());
      int i = 0;
      for (auto val : double_list) {
        while (attr_value.value_length_size() != 0 && attr_value.value_length(i) != 1) {
          ++i;
        }
        if (i >= items.size()) {
          invalid_packed_value = true;
          break;
        }
        if (append_value) {
          items[i].AppendDoubleListAttr(attr_accessor, val, check_multi_table);
        } else {
          items[i].SetDoubleAttr(attr_accessor, val, false, true, check_multi_table);
        }
        stat.AddValue(static_cast<double>(val));
        ++i;
      }
    } else if (attr_value.type() == kuiba::CommonSampleEnum::STRING_ATTR ||
               attr_value.value_type() == PackedItemAttrValue_ValueType_STRING) {
      int i = 0;
      int offset = 0;
      for (int len : attr_value.value_length()) {
        if (i >= items.size()) {
          invalid_packed_value = true;
          break;
        }
        if (len >= 0) {
          if (append_value) {
            items[i].AppendStringListAttr(attr_accessor, attr_value.value().substr(offset, len),
                                          check_multi_table);
          } else {
            items[i].SetStringAttr(attr_accessor, attr_value.value().substr(offset, len), false, true,
                                   check_multi_table);
          }
          stat.IncrCount();
          offset += len;
        }
        ++i;
      }
    } else if (attr_value.type() == kuiba::CommonSampleEnum::INT_LIST_ATTR ||
               attr_value.value_type() == PackedItemAttrValue_ValueType_INT64_LIST) {
      int i = 0;
      int offset = 0;
      for (int len : attr_value.value_length()) {
        if (i >= items.size()) {
          invalid_packed_value = true;
          break;
        }
        if (len >= 0) {
          int data_size = len * sizeof(int64);
          int_list.SetData(attr_value.value().data() + offset, data_size);
          if (append_value) {
            for (auto val : int_list) {
              items[i].AppendIntListAttr(attr_accessor, val, check_multi_table);
            }
          } else {
            std::vector<int64> val(int_list.begin(), int_list.end());
            items[i].SetIntListAttr(attr_accessor, std::move(val), false, true, check_multi_table);
          }
          stat.IncrCount();
          offset += data_size;
        }
        ++i;
      }
    } else if (attr_value.type() == kuiba::CommonSampleEnum::FLOAT_LIST_ATTR ||
               attr_value.value_type() == PackedItemAttrValue_ValueType_FLOAT64_LIST) {
      int i = 0;
      int offset = 0;
      for (int len : attr_value.value_length()) {
        if (i >= items.size()) {
          invalid_packed_value = true;
          break;
        }
        if (len >= 0) {
          int data_size = len * sizeof(double);
          double_list.SetData(attr_value.value().data() + offset, data_size);
          if (append_value) {
            for (auto val : double_list) {
              items[i].AppendDoubleListAttr(attr_accessor, val, check_multi_table);
            }
          } else {
            std::vector<double> val(double_list.begin(), double_list.end());
            items[i].SetDoubleListAttr(attr_accessor, std::move(val), false, true, check_multi_table);
          }
          stat.IncrCount();
          offset += data_size;
        }
        ++i;
      }
    } else if (attr_value.type() == kuiba::CommonSampleEnum::STRING_LIST_ATTR ||
               attr_value.value_type() == PackedItemAttrValue_ValueType_STRING_LIST) {
      int i = 0;
      int k = 0;
      int offset = 0;
      while (k < attr_value.value_length_size()) {
        if (i >= items.size()) {
          invalid_packed_value = true;
          break;
        }
        int len = attr_value.value_length(k++);
        if (len >= 0) {
          if (append_value) {
            while (len--) {
              int str_len = attr_value.value_length(k++);
              items[i].AppendStringListAttr(attr_accessor, attr_value.value().substr(offset, str_len),
                                            check_multi_table);
              offset += str_len;
            }
          } else {
            std::vector<std::string> val;
            val.reserve(len);
            while (len--) {
              int str_len = attr_value.value_length(k++);
              val.emplace_back(attr_value.value().substr(offset, str_len));
              offset += str_len;
            }
            items[i].SetStringListAttr(attr_accessor, std::move(val), false, true, check_multi_table);
          }
          stat.IncrCount();
        }
        ++i;
      }
    } else if (attr_value.value_type() == PackedItemAttrValue_ValueType_INT32) {
      EXTRACT_SINGLE_ITEM_ATTR(int32);
    } else if (attr_value.value_type() == PackedItemAttrValue_ValueType_INT16) {
      EXTRACT_SINGLE_ITEM_ATTR(int16);
    } else if (attr_value.value_type() == PackedItemAttrValue_ValueType_INT8) {
      EXTRACT_SINGLE_ITEM_ATTR(int8);
    } else if (attr_value.value_type() == PackedItemAttrValue_ValueType_FLOAT32) {
      EXTRACT_SINGLE_ITEM_ATTR(float);
    } else if (attr_value.value_type() == PackedItemAttrValue_ValueType_FLOAT16) {
      base::ConstArray<float16_t> value_list;
      value_list.SetData(attr_value.value());
      int i = 0;
      for (float16_t val : value_list) {
        while (attr_value.value_length_size() != 0 && attr_value.value_length(i) != 1) {
          ++i;
        }
        if (i >= items.size()) {
          invalid_packed_value = true;
          break;
        }
        if (append_value) {
          attr_accessor->AppendListValue<float16_t>(items[i].GetAttrIndex(), val);
        } else {
          attr_accessor->SetSingularValue<float16_t>(items[i].GetAttrIndex(), val);
        }
        stat.IncrCount();
        ++i;
      }
    } else if (attr_value.value_type() == PackedItemAttrValue_ValueType_INT32_LIST) {
      EXTRACT_LIST_ITEM_ATTR(int32);
    } else if (attr_value.value_type() == PackedItemAttrValue_ValueType_INT16_LIST) {
      EXTRACT_LIST_ITEM_ATTR(int16);
    } else if (attr_value.value_type() == PackedItemAttrValue_ValueType_INT8_LIST) {
      EXTRACT_LIST_ITEM_ATTR(int8);
    } else if (attr_value.value_type() == PackedItemAttrValue_ValueType_FLOAT32_LIST) {
      EXTRACT_LIST_ITEM_ATTR(float);
    } else if (attr_value.value_type() == PackedItemAttrValue_ValueType_FLOAT16_LIST) {
      EXTRACT_LIST_ITEM_ATTR(float16_t);
    }

    if (invalid_packed_value) {
      CL_LOG_EXCEPTION("[DANGER!!!] inconsistent packed_item_attr size: " + attr_accessor->name())
          << "[DANGER!!!] inconsistent packed_item_attr size. attr name: " << attr_accessor->name()
          << ", results size = " << items.size();
    }

    return stat;
  }

  // NOTE(zhaoyang09): 该函数判断依赖解析 request 插入的 result 的 index， 务必保证该函数执行前无其他增加
  // result 操作。
  static bool CouldZeroCopyToContext(const PackedItemAttrValue &attr_value, size_t field_length,
                                     size_t item_size, bool use_assigned_index = false,
                                     bool has_duplicate_key = false) {
    if (!(FLAGS_zero_copy_request_data || use_assigned_index)) {
      return false;
    }

    if (has_duplicate_key) {
      return false;
    }

    // item 与 item value 长度有差异则不支持零拷贝。例如压缩场景
    if (!use_assigned_index) {
      if (item_size * field_length != attr_value.value().size()) {
        return false;
      }
      if (attr_value.value_length_size() != 0 && attr_value.value_length_size() != item_size) {
        return false;
      }
    }

    return true;
  }

  static void MoveFixedValueToContext(ItemAttr *attr_accessor, PackedItemAttrValue *attr_value,
                                      const std::vector<CommonRecoResult> *p_items, AttrType attr_type,
                                      bool use_assigned_index = false, bool has_duplicate_key = false) {
    size_t field_length = GetSingleValueAttrTypeSize(attr_type);
    if (use_assigned_index) {
      int value_num = attr_value->value().size() / field_length;
      attr_accessor->has_values.clear();
      if (value_num > 0) {
        attr_accessor->has_values.resize(value_num, 0);
        for (int i = 0; i < p_items->size(); ++i) {
          if (attr_value->value_length(i) > 0) {
            attr_accessor->has_values[(*p_items)[i].GetAttrIndex()] = 1;
          }
        }
      }
    } else {
      if (has_duplicate_key) {
        attr_accessor->has_values.clear();
        attr_accessor->has_values.resize(p_items->size(), 0);
        for (auto &item : *p_items) {
          attr_accessor->has_values[item.GetAttrIndex()] = 1;
        }
      } else {
        attr_accessor->has_values.clear();
        attr_accessor->has_values.resize(p_items->size(), 1);
      }
    }
    attr_accessor->value_type = attr_type;
    std::string *value = attr_value->mutable_value();
    std::swap(*value, attr_accessor->single_values);
  }

  // NOTE(zhaoyang09): 内部包含了 zero_copy 逻辑，需要满足 执行前无 result 插入。仅在解析 request 时使用
  static StatisticInfo ExtractPackedItemAttrToContextFromRequest(
      ItemAttr *attr_accessor, PackedItemAttrValue *attr_value, const std::vector<CommonRecoResult> *p_items,
      bool append_value = false, bool use_assigned_index = false, bool has_duplicate_key = false) {
    StatisticInfo stat;
    if (!p_items) return stat;
    bool has_zero_copied = false;

    if (attr_value->type() == kuiba::CommonSampleEnum::INT_ATTR ||
        attr_value->value_type() == PackedItemAttrValue_ValueType_INT64) {
      if (CouldZeroCopyToContext(*attr_value, sizeof(int64), p_items->size(), use_assigned_index,
                                 has_duplicate_key)) {
        MoveFixedValueToContext(attr_accessor, attr_value, p_items, AttrType::INT, use_assigned_index,
                                has_duplicate_key);
        has_zero_copied = true;
      }
    } else if (attr_value->type() == kuiba::CommonSampleEnum::FLOAT_ATTR ||
               attr_value->value_type() == PackedItemAttrValue_ValueType_FLOAT64) {
      if (CouldZeroCopyToContext(*attr_value, sizeof(double), p_items->size(), use_assigned_index,
                                 has_duplicate_key)) {
        MoveFixedValueToContext(attr_accessor, attr_value, p_items, AttrType::FLOAT, use_assigned_index,
                                has_duplicate_key);
        has_zero_copied = true;
      }
    }
    if (!has_zero_copied) {
      stat = ExtractPackedItemAttrToContext(attr_accessor, *attr_value, p_items, append_value);
    }
    return stat;
  }

  static void ClearLuaStack(lua_State *lua_state) {
    lua_settop(lua_state, 0);
  }

  static void CheckLuaStack(lua_State *lua_state, int require_num) {
    CHECK(lua_checkstack(lua_state, require_num))
        << "cannot allocate extra " << require_num << " slots in lua stack!";
  }

  static void PushVectorToLuaTable(lua_State *lua_state, absl::Span<const int64> vec) {
    lua_createtable(lua_state, vec.size(), 0);
    for (int i = 0; i < vec.size(); ++i) {
      lua_pushinteger(lua_state, vec[i]);
      // lua 数组的下标习惯以 1 开始
      lua_rawseti(lua_state, -2, i + 1);
    }
  }

  static void PushVectorToLuaTable(lua_State *lua_state, absl::Span<const double> vec) {
    lua_createtable(lua_state, vec.size(), 0);
    for (int i = 0; i < vec.size(); ++i) {
      lua_pushnumber(lua_state, vec[i]);
      // lua 数组的下标习惯以 1 开始
      lua_rawseti(lua_state, -2, i + 1);
    }
  }

  static void PushVectorToLuaTable(lua_State *lua_state, const std::vector<absl::string_view> &vec) {
    lua_createtable(lua_state, vec.size(), 0);
    for (int i = 0; i < vec.size(); ++i) {
      lua_pushlstring(lua_state, vec[i].data(), vec[i].size());
      // lua 数组的下标习惯以 1 开始
      lua_rawseti(lua_state, -2, i + 1);
    }
  }

  static void ImportCommonAttrsToLuaState(const ReadableRecoContextInterface *context,
                                          const std::vector<std::string> &import_common_attrs,
                                          lua_State *lua_state) {
    for (const auto &attr_name : import_common_attrs) {
      if (auto double_val = context->GetDoubleCommonAttr(attr_name)) {
        lua_pushnumber(lua_state, *double_val);
      } else if (auto int_val = context->GetIntCommonAttr(attr_name)) {
        lua_pushinteger(lua_state, *int_val);
      } else if (auto string_val = context->GetStringCommonAttr(attr_name)) {
        lua_pushlstring(lua_state, string_val->data(), string_val->size());
      } else if (auto int_list_val = context->GetIntListCommonAttr(attr_name)) {
        PushVectorToLuaTable(lua_state, *int_list_val);
      } else if (auto double_list_val = context->GetDoubleListCommonAttr(attr_name)) {
        PushVectorToLuaTable(lua_state, *double_list_val);
      } else if (auto string_list_val = context->GetStringListCommonAttr(attr_name)) {
        PushVectorToLuaTable(lua_state, *string_list_val);
      } else if (context->HasCommonAttr(attr_name)) {
        lua_pushboolean(lua_state, true);
      } else {
        lua_pushnil(lua_state);
      }

      lua_setglobal(lua_state, attr_name.data());
    }
  }

 private:
  static std::vector<std::string> attr_type_name_vec_;
  static AttrTypeMap attr_type_map_;
  static AbtestParamTypeMap ab_param_type_map_;
  static KconfParamTypeMap kconf_param_type_map_;
  static DegraderTypeMap degrader_type_map_;
  static AnnUserKeyModeMap ann_user_key_mode_map_;
};

}  // namespace platform
}  // namespace ks
