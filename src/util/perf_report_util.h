#pragma once

#include "base/common/basic_types.h"
#include "gflags/gflags.h"
#include "serving_base/perfutil/perfutil_wrapper.h"

DECLARE_double(perf_report_rate);
DECLARE_string(perf_report_rate_kconf_key);

namespace ks {
namespace platform {

class PerfReportUtil {
 public:
  static bool Reset();

  static bool IsEnabled() {
    return perf_enabled_;
  }

  static bool IsWhiteListEnabled() {
    return white_list_perf_enabled_;
  }

  static bool IsBlackListEnabled() {
    return black_list_perf_enabled_;
  }

  static void SetEnable(bool value) {
    perf_enabled_ = value;
  }

 private:
  static thread_local bool perf_enabled_;
  static thread_local bool white_list_perf_enabled_;
  static thread_local bool black_list_perf_enabled_;
};

class SetPerfEnabledOneshot {
 public:
  explicit SetPerfEnabledOneshot(bool value) {
    original_value_ = PerfReportUtil::IsEnabled();
    PerfReportUtil::SetEnable(value);
  }
  ~SetPerfEnabledOneshot() {
    PerfReportUtil::SetEnable(original_value_);
  }

 private:
  bool original_value_;

  DISALLOW_COPY_AND_ASSIGN(SetPerfEnabledOneshot);
};

}  // namespace platform
}  // namespace ks

#define CL_PERF_INTERVAL(...)                                       \
  if (PerfReportUtil::IsEnabled()) {                                \
    base::perfutil::PerfUtilWrapper::IntervalLogStash(__VA_ARGS__); \
  }

#define CL_PERF_COUNT(...)                                       \
  if (PerfReportUtil::IsEnabled()) {                             \
    base::perfutil::PerfUtilWrapper::CountLogStash(__VA_ARGS__); \
  }

#define CL_WHITELIST_PERF_INTERVAL(...)                             \
  if (PerfReportUtil::IsWhiteListEnabled()) {                       \
    base::perfutil::PerfUtilWrapper::IntervalLogStash(__VA_ARGS__); \
  }

#define CL_WHITELIST_PERF_COUNT(...)                             \
  if (PerfReportUtil::IsWhiteListEnabled()) {                    \
    base::perfutil::PerfUtilWrapper::CountLogStash(__VA_ARGS__); \
  }

#define CL_BLACKLIST_PERF_INTERVAL(...)                             \
  if (PerfReportUtil::IsBlackListEnabled()) {                       \
    base::perfutil::PerfUtilWrapper::IntervalLogStash(__VA_ARGS__); \
  }

#define CL_BLACKLIST_PERF_COUNT(...)                             \
  if (PerfReportUtil::IsBlackListEnabled()) {                    \
    base::perfutil::PerfUtilWrapper::CountLogStash(__VA_ARGS__); \
  }
