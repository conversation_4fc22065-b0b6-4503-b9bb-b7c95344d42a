#pragma once
#include <map>
#include <memory>
#include <string>
#include <vector>
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "teams/aiplatform/inference_sdk/src/infer_kess_client.h"
#include "teams/aiplatform/inference_sdk/src/infer_request_context.h"

namespace ks {
namespace platform {

class VgsGoodsTakeSidFromBartServiceEnricher : public CommonRecoBaseEnricher {
 public:
  VgsGoodsTakeSidFromBartServiceEnricher() {}

 public:
  bool InitProcessor() override;

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool BuildBartRequest(ks::platform::MutableRecoContextInterface* context, std::vector<uint32_t>* input_ids,
                        std::vector<uint32_t>* input_dims, std::vector<uint32_t>* input_id_length,
                        std::vector<uint32_t>* input_id_length_dims);

  void OperateAfterBartService(ks::platform::MutableRecoContextInterface* context, int dim,
                               const uint8_t* output_raw_data, std::vector<std::string>* output_ids,
                               const std::vector<uint32_t>* offsets, int L, int eos_id);

 private:
  std::shared_ptr<::kwai_infer_front::InferKessClient> bart_client_ = nullptr;
  int64_t model_version_ = -1;
  std::string kess_service_;
  std::string bart_model_name_;
  int64 timeout_ms_ = 200;
  uint32_t d_model_ = 768;
};

}  // namespace platform
}  // namespace ks
