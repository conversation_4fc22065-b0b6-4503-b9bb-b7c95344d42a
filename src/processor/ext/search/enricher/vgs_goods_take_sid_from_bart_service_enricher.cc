#include "dragon/src/processor/ext/search/enricher/vgs_goods_take_sid_from_bart_service_enricher.h"
#include <string>
#include <unordered_set>
#include <vector>
#include <utility>
#include <memory>
#include <algorithm>
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.pb.h"
#include "teams/aiplatform/inference_sdk/src/infer_kess_client.h"
#include "teams/aiplatform/inference_sdk/src/infer_request_context.h"

namespace ks {
namespace platform {

bool VgsGoodsTakeSidFromBartServiceEnricher::InitProcessor() {
  model_version_ = config()->GetInt("model_version", -1);
  timeout_ms_ = config()->GetInt("timeout_ms", 100);
  kess_service_ = config()->GetString("kess_service", "grpc_vgs_oneretrieval_4090_v2");
  bart_model_name_ = config()->GetString("bart_model_name", "vgs-goods-onesearch-bart-4090-v2");
  ::ks::kess::rpc::grpc::OptionsForClient client_options(GlobalHolder::GetServiceIdentifier(), kess_service_);
  bart_client_ = std::make_shared<::kwai_infer_front::InferKessClient>(client_options, timeout_ms_);
  return true;
}

void VgsGoodsTakeSidFromBartServiceEnricher::Enrich(MutableRecoContextInterface *context,
                                                    RecoResultConstIter begin, RecoResultConstIter end) {
  std::vector<uint32_t> input_ids;
  std::vector<uint32_t> input_dims;
  std::vector<uint32_t> input_id_length, input_id_length_dims;

  auto vo_list_opt = context->GetIntListCommonAttr("vocab_offset");
  if (!vo_list_opt || vo_list_opt->empty()) {
      return;
  }
  std::vector<uint32_t> vocab_offsets(vo_list_opt->begin(), vo_list_opt->end());
  uint64_t sid_length = context->GetIntCommonAttr("sid_length").value_or(3);
  uint64_t eos_id = context->GetIntCommonAttr("eos_id").value_or(0);

  auto user_token_num = context->GetIntCommonAttr("user_token_num").value_or(0);
  auto user_embs_ptr = context->GetDoubleListCommonAttr("user_behavior_embed");

  std::vector<float> user_embs;
  if (user_embs_ptr) {
    user_embs.reserve(user_embs_ptr->size());
    std::copy(user_embs_ptr->begin(), user_embs_ptr->end(), std::back_inserter(user_embs));
  }

  if (BuildBartRequest(context, &input_ids, &input_dims, &input_id_length, &input_id_length_dims)) {
    if (kess_service_.empty() || bart_model_name_.empty()) {
      CL_LOG_ERROR_EVERY("bart_service", "no_kess_service:" + GetName(), 100)
          << "bart_service request cancelled: no kess_service! processor: " << GetName();
      return;
    }

    if (timeout_ms_ <= 0) {
      CL_LOG_ERROR_EVERY("bart_service", "timeout zero:" + GetName(), 100)
          << "bart_service request cancelled: timeout zero! processor: " << GetName();
      return;
    }

    // call bart service
    ::kwai_infer_front::InferRequestContext request_context(bart_client_.get());
    request_context.Clear();

    if (input_id_length_dims.size() < 2) {
      return;
    }

    if (input_dims.size() < 2) {
      return;
    }

    if (!request_context.AddInferInput("INPUT__0", reinterpret_cast<uint8_t *>(input_id_length.data()),
                                       input_id_length_dims[0] * input_id_length_dims[1] * sizeof(uint32_t),
                                       input_id_length_dims)) {
      return;
    }
    if (!request_context.AddInferInput("INPUT__1", reinterpret_cast<uint8_t *>(input_ids.data()),
                                       input_dims[0] * input_dims[1] * sizeof(uint32_t), input_dims)) {
      return;
    }
    if (user_token_num > 0) {
      std::vector<uint32_t> user_feat_dim = {uint32_t(1), uint32_t(user_token_num), d_model_};
      if (!request_context.AddInferInput("INPUT__2",
                                        reinterpret_cast<const uint8_t *>(user_embs.data()),
                                        user_embs.size() * sizeof(float),
                                        user_feat_dim)) {
        LOG_EVERY_N(ERROR, 100) << "AddInferInput INPUT__2 failed!";
        return;
      }
    }
    if (!request_context.RequestInferOuput("OUTPUT__0")) {
      return;
    }

    if (!request_context.Infer(bart_model_name_, model_version_)) {
      return;
    }

    std::vector<uint32_t> output_dims;
    const uint8_t *output_raw_data = nullptr;
    uint32_t output_raw_byte_size = 0;
    if (!request_context.GetInferOutput(0, &output_raw_data, &output_raw_byte_size, output_dims)) {
      return;
    }

    if (output_raw_data == nullptr) {
      return;
    }

    int dim = output_raw_byte_size / sizeof(int);
    std::vector<std::string> sid_str_list;

    OperateAfterBartService(context, dim, output_raw_data, &sid_str_list, &vocab_offsets, sid_length, eos_id);

    context->SetStringListCommonAttr("sid_str_list", std::move(sid_str_list));
  }
}

bool VgsGoodsTakeSidFromBartServiceEnricher::BuildBartRequest(
    ks::platform::MutableRecoContextInterface *context, std::vector<uint32_t> *input_ids,
    std::vector<uint32_t> *input_dims, std::vector<uint32_t> *input_id_length,
    std::vector<uint32_t> *input_id_length_dims) {
  auto bart_token_id_list = context->GetIntListCommonAttr("bart_token_id_list");
  if (bart_token_id_list) {
    if (!bart_token_id_list->empty()) {
      for (auto token_id : *bart_token_id_list) {
        input_ids->emplace_back(token_id);
      }

      input_dims->push_back(1);
      input_dims->push_back(input_ids->size());

      // step3: 填充 length
      input_id_length->push_back(input_ids->size());
      input_id_length_dims->push_back(1);
      input_id_length_dims->push_back(input_id_length->size());

      return true;
    }
  }

  return false;
}

void VgsGoodsTakeSidFromBartServiceEnricher::OperateAfterBartService(
    ks::platform::MutableRecoContextInterface* /*context*/, int dim, const uint8_t* output_raw_data,
    std::vector<std::string>* sid_str_list, const std::vector<uint32_t>* offsets,
    int sid_length,  // 期望的长度
    int eos_id) {    // 默认 1
  // 基础参数校验
  if (!sid_str_list || !output_raw_data || dim <= 0) {
    return;
  }
  if (!offsets || offsets->empty()) {
    return;
  }

  sid_str_list->clear();
  const int* ids = reinterpret_cast<const int*>(output_raw_data);

  // 入口日志：尺寸、长度约束、预览 tokens 与 offsets
  std::vector<int> cur;                 // 累积当前段 tokens（int 便于防御性检查）
  std::unordered_set<std::string> dedup;  // 去重

  auto flush_current = [&]() {
    // 1) 长度必须等于 sid_length
    if (sid_length > 0 && static_cast<int>(cur.size()) != sid_length) {
      cur.clear();
      return;
    }
    if (cur.empty()) {
      return;
    }

    // 2) 不能越界：cur.size() 不得超过 offsets.size()
    if (cur.size() > offsets->size()) {
      cur.clear();
      return;
    }

    // 3) 逐位合法性检查 + 拼接（确保 cur[i] - offsets[i] 非负）
    std::string s;
    for (size_t i = 0; i < cur.size(); ++i) {
      if (cur[i] < 0) {
        cur.clear();
        return;
      }
      const uint32_t tok = static_cast<uint32_t>(cur[i]);
      const uint32_t off = offsets->at(i);
      if (tok < off) {
        cur.clear();
        return;
      }
      if (i) s.push_back('_');
      const uint32_t delta = tok - off;
      s += std::to_string(delta);
    }

    // 4) 去重并保存
    if (dedup.insert(s).second) {
      sid_str_list->push_back(std::move(s));
    }
    cur.clear();
  };

  // 主循环：按 eos_id 切分
  for (int i = 0; i < dim; ++i) {
    const int token = ids[i];
    if (token == eos_id) {
      flush_current();
    } else {
      cur.push_back(token);
    }
  }
}


typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, VgsGoodsTakeSidFromBartServiceEnricher,
                 VgsGoodsTakeSidFromBartServiceEnricher)

}  // namespace platform
}  // namespace ks
