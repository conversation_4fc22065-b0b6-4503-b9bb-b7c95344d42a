#pragma once

#include <string>
#include <utility>
#include <vector>
#include <unordered_map>

#include "dragon/src/processor/base/common_reco_base_observer.h"

namespace ks {
namespace platform {

class FollowRankHitratePerflogObserver : public CommonRecoBaseObserver {
 public:
  FollowRankHitratePerflogObserver() {}

  void Observe(ReadableRecoContextInterface *context, RecoResultConstIter begin,
               RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    return true;
  }
  void InitAbParam(ReadableRecoContextInterface *context);

 private:
  std::string exp_tag_;
  DISALLOW_COPY_AND_ASSIGN(FollowRankHitratePerflogObserver);
};

}  // namespace platform
}  // namespace ks
