#include "dragon/src/processor/ext/follow_leaf/observer/follow_mix_final_perflog_observer.h"

#include <unordered_map>
#include <string>

#include "dragon/src/processor/ext/follow_leaf/util/follow_mix_rank_type.h"
#include "dragon/src/processor/ext/follow_leaf/context/context.h"

namespace ks {
namespace platform {

void FollowMixFinalPerflogObserver::Observe(ReadableRecoContextInterface *context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
  if (!sample_attr_name_.empty()) {
    auto is_sample = context->GetIntCommonAttr(sample_attr_name_).value_or(0);
    if (!is_sample) {
      return;
    }
  }
  result_size_ = context->GetIntCommonAttr("page_size").value_or(12);
  bool is_top_fresh = context->GetIntCommonAttr("uIsTopFresh").value_or(0) == 1;
  std::string is_top_fresh_str = is_top_fresh ? "1" : "0";
  std::string product_type_str = context->GetIntCommonAttr("is_nebula_user")
    .value_or(0) == 1 ? "nebula" : "main";
  product_type_str = std::string(
    context->GetStringCommonAttr("mix_perf_type_str").value_or(product_type_str));

  base::perfutil::PerfUtilWrapper::IntervalLogStash(
    result_size_, "reco.follow", "follow.mix.page_size", is_top_fresh_str, product_type_str);

  auto *biz_type_accessor = context->GetItemAttrAccessor("biz_type");
  auto *biz_type_list_accessor = context->GetItemAttrAccessor("biz_type_list");
  auto *item_type_accessor = context->GetItemAttrAccessor("item_type");
  auto *p_video_duration_ms_accessor = context->GetItemAttrAccessor("pVideoDurationMs");
  auto *cpm_accessor = context->GetItemAttrAccessor("ad_cpm");

  std::string tag_name(context->GetStringCommonAttr(tag_name_).value_or("base"));
  int index = 0;
  int live_count = 0;
  int photo_count = 0;
  int p_video_duration_ms_total = 0;

  bool is_top4_has_live = false;
  int top4_live_count = 0;
  int top1_live_count = 0;
  int ad_live_count = 0;

  std::unordered_map<int, uint32> biz_type_count;
  std::unordered_map<int, uint32> biz_type_list_count;
  int type_size = static_cast<int>(FollowMix::BizType::TYPE_SIZE);
  for (int i = 1; i < type_size; i++) {
    biz_type_count[i] = 0;
    biz_type_list_count[i] = 0;
  }

  for (auto it = begin; it != end && index < result_size_; ++it, index++) {
    auto biz_type = context->GetIntItemAttr(*it, biz_type_accessor).value_or(0);
    auto item_type = context->GetIntItemAttr(*it, item_type_accessor).value_or(0);
    if (item_type == follow::LIVE_TYPE) {
      live_count++;
      if (index < 4) {
        top4_live_count++;
        is_top4_has_live = true;
      }
      if (index == 0) {
        top1_live_count++;
      }
    } else {
      photo_count++;
      p_video_duration_ms_total += context->GetIntItemAttr(*it, p_video_duration_ms_accessor).value_or(0);
    }
    biz_type_count[biz_type]++;
    auto biz_type_list = context->GetIntListItemAttr(*it, biz_type_list_accessor);
    if (biz_type_list) {
      for (auto biz_type : *biz_type_list) {
        biz_type_list_count[biz_type]++;
      }
    }
    // perf top 6
    if (index < 6) {
      base::perfutil::PerfUtilWrapper::CountLogStash("reco.follow", "follow.final.result",
        FollowMix::BizTypeToString(biz_type), std::to_string(index), std::to_string(item_type),
        tag_name, product_type_str);
    }

    if (biz_type == static_cast<int>(FollowMix::BizType::AD_DSP)) {
      if (item_type == follow::LIVE_TYPE) {
        ad_live_count++;
      }
      base::perfutil::PerfUtilWrapper::IntervalLogStash(1e+4 * context->GetDoubleItemAttr(
        *it, cpm_accessor).value_or(0.0), "follow.final.ad", "ad_cpm", tag_name, product_type_str);
    }
  }

  // 分人群 perf
  std::string user_type = "empty";
  // 付费
  std::string pay_user_type = "";
  int32_t is_big_g = context->GetIntCommonAttr("uIsBigGForFollowKV").value_or(0);
  int32_t is_big_r = context->GetIntCommonAttr("uIsBigRForFollowKV").value_or(0);
  int32_t pay_value = context->GetIntCommonAttr("uUserClassLivePayingTypeMainApp").value_or(0);
  if (is_big_r > 0) {
    pay_user_type = "bigR";
  } else if (is_big_g > 0) {
    pay_user_type = "bigG";
  } else if (pay_value > 2) {
    pay_user_type = "highPayUser";
  } else if (pay_value > 0) {
    pay_user_type = "payUser";
  } else {
    pay_user_type = "noPay";
  }
  // 电商
  std::string merchant_user_type(context->GetStringCommonAttr("uBuyerEffectiveType").value_or("U0-null"));
  // 偏好
  int64 follow_watch_live_times_28d = context->GetIntCommonAttr("follow_watch_live_times_28d").value_or(0);
  int64 follow_photo_play_time_28d = context->GetIntCommonAttr("follow_photo_play_time_28d").value_or(0);
  std::string live_prefer_level = "LP0";
  if (follow_watch_live_times_28d > 17000) {
    live_prefer_level = "LP4";
  } else if (follow_watch_live_times_28d > 2000) {
    live_prefer_level = "LP3";
  } else if (follow_watch_live_times_28d > 100) {
    live_prefer_level = "LP2";
  } else if (follow_watch_live_times_28d > 1) {
    live_prefer_level = "LP1";
  } else {
    live_prefer_level = "LP0";
  }
  std::string photo_prefer_level = "PP0";
  if (follow_photo_play_time_28d > 32000) {
    photo_prefer_level = "PP4";
  } else if (follow_photo_play_time_28d > 15000) {
    photo_prefer_level = "PP3";
  } else if (follow_photo_play_time_28d > 7000) {
    photo_prefer_level = "PP2";
  } else if (follow_photo_play_time_28d > 2000) {
    photo_prefer_level = "PP1";
  } else {
    photo_prefer_level = "PP0";
  }

  user_type = pay_user_type + "," + merchant_user_type + "," +
              live_prefer_level + "," + photo_prefer_level;

  if (photo_count > 0) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(p_video_duration_ms_total / photo_count,
      "reco.follow", "follow.final.photo.duration.avg", tag_name, product_type_str);
  }
  base::perfutil::PerfUtilWrapper::IntervalLogStash(
    photo_count, "reco.follow", "follow.final.photo.num", tag_name, user_type, product_type_str);
  base::perfutil::PerfUtilWrapper::IntervalLogStash(
    live_count, "reco.follow", "follow.final.live.num", tag_name, user_type, product_type_str);
  base::perfutil::PerfUtilWrapper::IntervalLogStash(
    top1_live_count, "reco.follow", "follow.final.result.top1.live_count", tag_name, user_type,
    product_type_str);
  base::perfutil::PerfUtilWrapper::IntervalLogStash(
    top4_live_count, "reco.follow", "follow.final.result.top4.live_count", tag_name, user_type,
    product_type_str);
  base::perfutil::PerfUtilWrapper::CountLogStash("reco.follow", "follow.final.result.top4.has.live",
    std::to_string(is_top4_has_live), tag_name, user_type, product_type_str);
  for (const auto& pair : biz_type_count) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
      pair.second, "reco.follow", "follow.final.biz.type.counter",
      FollowMix::BizTypeToString(pair.first), tag_name, user_type, product_type_str);
  }
  for (const auto& pair : biz_type_list_count) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
      pair.second, "reco.follow", "follow.final.biz.type.list.counter",
      FollowMix::BizTypeToString(pair.first), tag_name, user_type, product_type_str);
  }
  base::perfutil::PerfUtilWrapper::IntervalLogStash(
      ad_live_count, "reco.follow", "follow.final.biz.type.counter", "ad_live", tag_name, user_type,
      product_type_str);
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, FollowMixFinalPerflogObserver, FollowMixFinalPerflogObserver)

}  // namespace platform
}  // namespace ks
