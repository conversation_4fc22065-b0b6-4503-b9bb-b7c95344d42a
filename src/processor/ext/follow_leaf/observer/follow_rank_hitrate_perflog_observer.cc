#include "dragon/src/processor/ext/follow_leaf/observer/follow_rank_hitrate_perflog_observer.h"

#include <unordered_map>
#include <unordered_set>
#include <string>
#include <sstream>
#include <algorithm>
#include <vector>

#include "dragon/src/processor/ext/follow_leaf/util/follow_mix_rank_type.h"
#include "dragon/src/processor/ext/follow_leaf/context/context.h"

namespace ks {
namespace platform {

double calculate_hitrate(const std::vector<int64_t>& source, const std::vector<int64_t>& target) {
    std::unordered_set<int64_t> target_set(target.begin(), target.end());
    int hit_count = 0;
    for (auto& item : source) {
        if (target_set.count(item)) {
            hit_count++;
        }
    }
    return static_cast<double>(hit_count) / std::max(static_cast<int>(target.size()), 1);
}

void FollowRankHitratePerflogObserver::InitAbParam(ReadableRecoContextInterface *context) {
  exp_tag_ = GetStringProcessorParameter(context, "hitrate_exp_tag", "default");
}

void FollowRankHitratePerflogObserver::Observe(ReadableRecoContextInterface *context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
  std::vector<std::string> source_top_item_list = {
      "follow_fr_l2r_top_item_list",
      "follow_fr_final_top_item_list"
  };

  std::vector<std::string> target_top_item_lists = {
      "follow_fr_l2r_top_item_list",
      "follow_fr_final_top_item_list",
      "follow_fr_pctr_top_item_list",
      "follow_fr_pltr_top_item_list",
      "follow_fr_pwt_top_item_list",
      "follow_fr_pfinish_top_item_list",
      "follow_fr_plvtr_top_item_list"
  };

  InitAbParam(context);

  for (const auto& source_list : source_top_item_list) {
      auto source_item_ids_ptr = context->GetIntListCommonAttr(source_list);
      if (!source_item_ids_ptr) {
        continue;
      }
      std::vector<int64_t> source_item_ids;
      for (int i = 0; source_item_ids_ptr && i < source_item_ids_ptr->size(); i++) {
          source_item_ids.push_back((*source_item_ids_ptr)[i]);
      }
      for (const auto& target_list : target_top_item_lists) {
          auto target_item_ids_ptr = context->GetIntListCommonAttr(target_list);
          if (!target_item_ids_ptr) {
            continue;
          }
          std::vector<int64_t> target_item_ids;
          for (int i = 0; target_item_ids_ptr && i < target_item_ids_ptr->size(); i++) {
              target_item_ids.push_back((*target_item_ids_ptr)[i]);
          }

        std::vector<int> topNs = {5, 10, 25, 50, 100};
        for (int topN : topNs) {
            int actualTopNSource = std::min(topN, static_cast<int>(source_item_ids.size()));
            std::vector<int64_t> truncated_source(source_item_ids.begin(),
                source_item_ids.begin() + actualTopNSource);
            int actualTopNTarget = std::min(topN, static_cast<int>(target_item_ids.size()));
            std::vector<int64_t> truncated_target(target_item_ids.begin(),
                target_item_ids.begin() + actualTopNTarget);

            double hitrate = calculate_hitrate(truncated_source, truncated_target);

            std::string tag = "hitrate." + source_list + ".in." + target_list + ".top" + std::to_string(topN);
            base::perfutil::PerfUtilWrapper::IntervalLogStash(
                static_cast<int>(hitrate * 100),
                "reco.follow",
                tag,
                std::to_string(topN),
                std::string(exp_tag_));
          }
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, FollowRankHitratePerflogObserver, FollowRankHitratePerflogObserver);


}  // namespace platform
}  // namespace ks
