#pragma once

#include <string>
#include "dragon/src/processor/ext/follow_leaf/arranger/filter_op/base_filter_op.h"

namespace ks {
namespace platform {
namespace follow {

class TopLiveAuthorFilterOp : public BaseFilterOp {
 public:
  TopLiveAuthorFilterOp() : BaseFilterOp("top_live_author_filter") {}

 protected:
  bool IsFilterInternal(const CommonRecoResult &result, const CommonPhotoInfo &photo_info) override;
  bool InitContextInternal(MutableRecoContextInterface *context) override;
  bool InitConfigInternal(const base::Json *common_attr_config) override;

 private:
  const ItemAttr *is_top_live_author_accessor_ = nullptr;

  std::string filter_top_live_author_prob_path_;
  int filter_top_live_author_prob_ = 100;  // 默认 100% 概率

  DISALLOW_COPY_AND_ASSIGN(TopLiveAuthorFilterOp);
};

}  // namespace follow
}  // namespace platform
}  // namespace ks
