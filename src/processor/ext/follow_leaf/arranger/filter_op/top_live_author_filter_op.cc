#include "dragon/src/processor/ext/follow_leaf/arranger/filter_op/top_live_author_filter_op.h"

#include "ks/reco_proto/proto/realtime_reco.pb.h"

namespace ks {
namespace platform {
namespace follow {

REGISTER_FOLLOW_FILTER_OP(TopLiveAuthorFilterOp)

bool TopLiveAuthorFilterOp::InitConfigInternal(const base::Json *common_attr_config) {
  filter_top_live_author_prob_path_ = common_attr_config->GetString("filter_top_live_author_prob");
  if (filter_top_live_author_prob_path_.empty()) {
    filter_top_live_author_prob_ = 100;
  }
  return true;
}

bool TopLiveAuthorFilterOp::InitContextInternal(MutableRecoContextInterface *context) {
  is_top_live_author_accessor_ = context->GetItemAttrAccessor("is_top_live_author");
  if (is_top_live_author_accessor_ == nullptr) {
    return false;
  }
  if (!filter_top_live_author_prob_path_.empty()) {
    auto prob_value = context->GetIntCommonAttr(filter_top_live_author_prob_path_);
    if (prob_value) {
      filter_top_live_author_prob_ = prob_value.value();
    } else {
      filter_top_live_author_prob_ = 100;
    }
  } else {
    filter_top_live_author_prob_ = 100;
  }
  return true;
}

bool TopLiveAuthorFilterOp::IsFilterInternal(const CommonRecoResult &result,
                                              const CommonPhotoInfo &photo_info) {
  // 读 is_top_live_author
  auto is_top_live_author = Context().GetIntItemAttr(result, is_top_live_author_accessor_);
  // 如果 is_top_live_author = 1，则根据概率过滤
  if (is_top_live_author && is_top_live_author.value() == 1) {
    if (filter_top_live_author_prob_ <= 0) {
      return false;
    }
    if (filter_top_live_author_prob_ >= 100) {
      return true;
    }
    // 基于 photo_id 的哈希值生成伪随机数，避免每次调用随机数生成器
    int64_t photo_id = result.GetId();
    // 确保类型安全
    uint64_t hash_input = static_cast<uint64_t>(photo_id);
    int hash_value = static_cast<int>((hash_input * 2654435761ULL) % 100);
    bool should_filter = hash_value < filter_top_live_author_prob_;
    return should_filter;
  }
  // 其他情况不过滤
  return false;
}

}  // namespace follow
}  // namespace platform
}  // namespace ks
