#pragma once

#include <algorithm>
#include <map>
#include <memory>
#include <string>
#include <vector>

#include "folly/container/F14Map.h"
#include "folly/container/F14Set.h"
#include "ks/reco_proto/proto/realtime_reco.pb.h"

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_arranger.h"
#include "dragon/src/processor/ext/follow_leaf/context/context.h"

namespace ks {
namespace platform {

class FollowLeafRetrievalEffectUaFilterArranger : public CommonRecoBaseArranger {
 public:
  FollowLeafRetrievalEffectUaFilterArranger() {}

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;
  void OnPipelineExit(ReadableRecoContextInterface *context) override {
    std::vector<CommonRecoResult> result_vec;
    new_result_vec_.swap(result_vec);
  }

 private:
  bool InitProcessor() override {
    user_info_path_ = config()->GetString("user_info_path", "");
    if (user_info_path_.empty()) {
      LOG(ERROR) << "FollowLeafRetrievalEffectUaFilterArranger user info path is empty";
      return false;
    }
    follow_list_attr_ = config()->GetString("follow_list_attr", "follow_list");
    if (follow_list_attr_.empty()) {
      LOG(ERROR) << "follow_list_attr is empty";
      return false;
    }
    return true;
  }
  bool Init(MutableRecoContextInterface *context);
  void InitAbParam(MutableRecoContextInterface *context);

 private:
  // 结果
  std::vector<CommonRecoResult> new_result_vec_;
  std::string user_info_path_;
  std::string follow_list_attr_;
  const ks::reco::RealTimeFollowRecoUserInfo *user_info_ = nullptr;
  follow::FollowContext *follow_context_ = nullptr;
  static const int64 one_day_us_ = 24 * 3600L * 1000000L;

  bool enable_recall_ua_filter_ = false;
  bool enable_recall_ua_filter_v1_ = false;
  bool enable_recall_ua_filter_v2_ = false;
  int ua_filter_follow_time_ = 30;
  int ua_filter_follow_num_ = 100;
  int mod_num_ = 100;
  int judge_num_min_ = 1;
  int judge_num_max_ = 1;
  int ua_filter_min_photo_num_threadhold_ = 100;
  std::string setting_follow_timestamp_ms_ = "1756057496000";

  DISALLOW_COPY_AND_ASSIGN(FollowLeafRetrievalEffectUaFilterArranger);
};

}  // namespace platform
}  // namespace ks
