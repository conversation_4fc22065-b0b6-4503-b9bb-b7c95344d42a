#include "dragon/src/processor/ext/follow_leaf/enricher/follow_biz_type_enricher.h"

#include <algorithm>
#include <vector>
#include <utility>

#include "dragon/src/processor/ext/follow_leaf/util/follow_mix_rank_type.h"

namespace ks {
namespace platform {
void FollowBizTypeEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
  InitItemAttr(context);

  IsNatural(context, begin, end);
  IsAd(context, begin, end);
  IsMerchant(context, begin, end);
  IsGift(context, begin, end);
  IsStoreWide(context, begin, end);
  IsLocalLife(context, begin, end);
  IsMerchantCart(context, begin, end);
  IsRecruitPhoto(context, begin, end);
  IsRecruitLive(context, begin, end);

  auto *biz_type_list_accessor = context->GetItemAttrAccessor("biz_type_list");
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    std::vector<int64> biz_type_list;
    if (context->GetIntItemAttr(result, is_natural_accessor_).value_or(0) == 1) {
      biz_type_list.push_back(static_cast<int>(FollowMix::BizType::NATURAL));
    }
    if (context->GetIntItemAttr(result, is_ad_accessor_).value_or(0) == 1) {
      biz_type_list.push_back(static_cast<int>(FollowMix::BizType::AD_DSP));
    }
    if (context->GetIntItemAttr(result, is_merchant_accessor_).value_or(0) == 1) {
      biz_type_list.push_back(static_cast<int>(FollowMix::BizType::MERCHANT));
    }
    if (context->GetIntItemAttr(result, is_gift_accessor_).value_or(0) == 1) {
      biz_type_list.push_back(static_cast<int>(FollowMix::BizType::GIFT));
    }
    if (context->GetIntItemAttr(result, is_store_wide_accessor_).value_or(0) == 1) {
      biz_type_list.push_back(static_cast<int>(FollowMix::BizType::STORE_WIDE));
    }
    if (context->GetIntItemAttr(result, is_local_life_accessor_).value_or(0) == 1) {
      biz_type_list.push_back(static_cast<int>(FollowMix::BizType::LOCAL_LIFE));
    }
    if (context->GetIntItemAttr(result, is_merchant_cart_accessor_).value_or(0) == 1) {
      biz_type_list.push_back(static_cast<int>(FollowMix::BizType::MERCHANT_CART));
    }
    if (context->GetIntItemAttr(result, is_recruit_photo_accessor_).value_or(0) == 1) {
      biz_type_list.push_back(static_cast<int>(FollowMix::BizType::RECRUIT_PHOTO));
    }
    if (context->GetIntItemAttr(result, is_recruit_live_accessor_).value_or(0) == 1) {
      biz_type_list.push_back(static_cast<int>(FollowMix::BizType::RECRUIT_LIVE));
    }
    if (!is_house_photo_attr_.empty() &&
        context->GetIntItemAttr(result, is_house_photo_accessor_).value_or(0) == 1) {
      biz_type_list.push_back(static_cast<int>(FollowMix::BizType::HOUSE_PHOTO));
    }
    if (!is_house_live_attr_.empty() &&
        context->GetIntItemAttr(result, is_house_live_accessor_).value_or(0) == 1) {
      biz_type_list.push_back(static_cast<int>(FollowMix::BizType::HOUSE_LIVE));
    }
    context->SetIntListItemAttr(result, biz_type_list_accessor, std::move(biz_type_list));
  });
}

void FollowBizTypeEnricher::InitItemAttr(MutableRecoContextInterface *context) {
  is_natural_accessor_ = context->GetItemAttrAccessor("is_natural");
  is_ad_accessor_ = context->GetItemAttrAccessor("is_ad");
  is_merchant_accessor_ = context->GetItemAttrAccessor("is_merchant");
  is_gift_accessor_ = context->GetItemAttrAccessor("is_gift");
  is_store_wide_accessor_ = context->GetItemAttrAccessor("is_store_wide");
  is_local_life_accessor_ = context->GetItemAttrAccessor("is_local_life");
  is_merchant_cart_accessor_ = context->GetItemAttrAccessor("is_merchant_cart");
  is_recruit_photo_accessor_ = context->GetItemAttrAccessor(is_recruit_photo_attr_);
  is_recruit_live_accessor_ = context->GetItemAttrAccessor(is_recruit_live_attr_);
  if (!is_house_photo_attr_.empty()) {
    is_house_photo_accessor_ = context->GetItemAttrAccessor(is_house_photo_attr_);
  }
  if (!is_house_live_attr_.empty()) {
    is_house_live_accessor_ = context->GetItemAttrAccessor(is_house_live_attr_);
  }
}

void FollowBizTypeEnricher::IsNatural(MutableRecoContextInterface *context,
    RecoResultConstIter begin, RecoResultConstIter end) {
  // 在合并广告前 set 了，这里不需要 set
}

void FollowBizTypeEnricher::IsAd(MutableRecoContextInterface *context,
                      RecoResultConstIter begin, RecoResultConstIter end) {
  auto *is_ad_dsp_accessor = context->GetItemAttrAccessor("is_ad_dsp");
  auto *is_ad_fans_top_accessor = context->GetItemAttrAccessor("is_ad_fans_top");
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    if (context->GetIntItemAttr(result, is_ad_dsp_accessor).value_or(0) == 1
    || context->GetIntItemAttr(result, is_ad_fans_top_accessor).value_or(0) == 1) {
      context->SetIntItemAttr(result, is_ad_accessor_, 1);
    }
  });
}

void FollowBizTypeEnricher::IsMerchant(MutableRecoContextInterface *context,
    RecoResultConstIter begin, RecoResultConstIter end) {
  auto *is_merchant_live_accessor = context->GetItemAttrAccessor("is_merchant_live");
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    if (context->GetIntItemAttr(result, is_merchant_live_accessor).value_or(0) == 1) {
      context->SetIntItemAttr(result, is_merchant_accessor_, 1);
    }
  });
}

void FollowBizTypeEnricher::IsGift(MutableRecoContextInterface *context,
                      RecoResultConstIter begin, RecoResultConstIter end) {
  // 所有直播都视为 gift
  auto *item_type_accessor = context->GetItemAttrAccessor("item_type");
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    if (context->GetIntItemAttr(result, item_type_accessor).value_or(0) == follow::LIVE_TYPE) {
      context->SetIntItemAttr(result, is_gift_accessor_, 1);
    }
  });
}

void FollowBizTypeEnricher::IsStoreWide(MutableRecoContextInterface *context,
                      RecoResultConstIter begin, RecoResultConstIter end) {
  // prepare 阶段已经设置是否是 store_wide
  // std::for_each(begin, end, [&](const CommonRecoResult &result) {
  //   context->SetIntItemAttr(result, is_store_wide_accessor_, 1);
  // });
}

void FollowBizTypeEnricher::IsLocalLife(MutableRecoContextInterface *context,
                      RecoResultConstIter begin, RecoResultConstIter end) {
  auto *is_locallife_live_accessor = context->GetItemAttrAccessor("has_locallife_live_score");
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    if (context->GetIntItemAttr(result, is_locallife_live_accessor).value_or(0) == 1) {
      context->SetIntItemAttr(result, is_local_life_accessor_, 1);
    }
  });
}

void FollowBizTypeEnricher::IsMerchantCart(MutableRecoContextInterface *context,
                      RecoResultConstIter begin, RecoResultConstIter end) {
  auto *is_merchant_cart_accessor = context->GetItemAttrAccessor("is_merchant_cart");
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    if (context->GetIntItemAttr(result, is_merchant_cart_accessor).value_or(0) == 1) {
      context->SetIntItemAttr(result, is_merchant_cart_accessor_, 1);
    }
  });
}

void FollowBizTypeEnricher::IsRecruitPhoto(MutableRecoContextInterface *context,
                      RecoResultConstIter begin, RecoResultConstIter end) {
  // 在合并广告前 set 了，这里不需要 set
}

void FollowBizTypeEnricher::IsRecruitLive(MutableRecoContextInterface *context,
                      RecoResultConstIter begin, RecoResultConstIter end) {
  // 在合并广告前 set 了，这里不需要 set
}


typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, FollowBizTypeEnricher, FollowBizTypeEnricher);

}  // namespace platform
}  // namespace ks
