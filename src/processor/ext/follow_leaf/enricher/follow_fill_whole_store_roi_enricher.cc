#include "dragon/src/processor/ext/follow_leaf/enricher/follow_fill_whole_store_roi_enricher.h"

#include <algorithm>
#include <vector>

namespace ks {
namespace platform {
void FollowFillWholeStoreRoiPhotoEnricher::Enrich(MutableRecoContextInterface *context,
                                                  RecoResultConstIter begin, RecoResultConstIter end) {
  // read
  auto *is_living_photo_accessor = context->GetItemAttrAccessor("is_living_photo");
  auto *living_photo_live_key_accessor = context->GetItemAttrAccessor("living_photo_live_key");
  auto *is_product_photo_accessor = context->GetItemAttrAccessor("is_product_photo");
  auto *is_store_wide_accessor = context->GetItemAttrAccessor("is_store_wide");
  auto *merchant_cart_pgmv_accessor = context->GetItemAttrAccessor("merchant_cart_pgmv");
  auto *merchant_living_photo_pgmv_accessor = context->GetItemAttrAccessor("merchant_living_photo_pgmv");
  auto *auto_roi_accessor = context->GetItemAttrAccessor("autoRoi");
  auto *photo_ad_roi_ratio_accessor = context->GetItemAttrAccessor("iGoodsAdOwnerRoiRatio");
  auto *photo_ad_trans_info_accessor = context->GetItemAttrAccessor("iGoodsAdOwnerAdTransInfo");
  auto *photo_creative_id_accessor = context->GetItemAttrAccessor("iGoodsAdOwnerCreativeId");
  auto *merchant_item_id_accessor = context->GetItemAttrAccessor("merchant_item_id");
  auto *live_ad_roi_ratio_accessor = context->GetItemAttrAccessor("ad_roi_ratio_score");
  auto *live_ad_trans_info_accessor = context->GetItemAttrAccessor("ad_trans_info");
  auto *live_creative_id_accessor = context->GetItemAttrAccessor("ad_creative_id");

  // write
  auto *ad_gpm_score_accessor = context->GetItemAttrAccessor("resp_ad_gpm_score");
  auto *ad_boost_cpm_score_accessor = context->GetItemAttrAccessor("resp_ad_boost_cpm_score");
  auto *store_wide_cpm_accessor = context->GetItemAttrAccessor("resp_store_wide_cpm");
  auto *ad_auto_roi_score_accessor = context->GetItemAttrAccessor("resp_ad_auto_roi_score");
  auto *ad_roi_ratio_score_accessor = context->GetItemAttrAccessor("resp_ad_roi_ratio_score");
  auto *ad_trans_info_accessor = context->GetItemAttrAccessor("resp_ad_trans_info");
  auto *ad_creative_id_accessor = context->GetItemAttrAccessor("resp_ad_creative_id");
  auto *photo_store_wide_type_accessor = context->GetItemAttrAccessor("resp_photo_store_wide_type");
  auto *has_store_wide_accessor = context->GetItemAttrAccessor("resp_has_store_wide");
  auto *photo_store_wide_live_id_accessor = context->GetItemAttrAccessor("resp_photo_store_wide_live_id");
  auto *merchant_product_id_accessor = context->GetItemAttrAccessor("resp_merchant_product_id");
  auto *is_whole_store_roi_ad_accessor = context->GetItemAttrAccessor("resp_is_whole_store_roi_ad");

  int follow_store_wide_photo_live_limit =
      context->GetIntCommonAttr("follow_store_wide_photo_live_limit").value_or(2);
  int follow_store_wide_merchant_cart_limit =
      context->GetIntCommonAttr("follow_store_wide_merchant_cart_limit").value_or(2);
  bool enable_whole_store_live_photo =
      context->GetIntCommonAttr("enable_whole_store_live_photo").value_or(0) == 1;
  bool enable_storewide_trans_is_whole_store_roi_ad =
      context->GetIntCommonAttr("enable_storewide_trans_is_whole_store_roi_ad").value_or(0) == 1;

  int photo_cnt = 0;
  int photo_live_cnt = 0;
  for (auto it = begin; it != end; it++) {
    int64 living_photo_live_key = context->GetIntItemAttr(*it, living_photo_live_key_accessor).value_or(0);
    bool is_photo_live =
        (context->GetIntItemAttr(*it, is_living_photo_accessor).value_or(0) == 1) &&
        enable_whole_store_live_photo &&
        (context->GetIntItemAttr(living_photo_live_key, is_store_wide_accessor).value_or(0) == 1);
    bool is_photo = (context->GetIntItemAttr(*it, is_product_photo_accessor).value_or(0) == 1) &&
                    (context->GetIntItemAttr(*it, is_store_wide_accessor).value_or(0) == 1);
    if (is_photo_live && is_photo) {
      double photo_live_roi_ratio =
          context->GetDoubleItemAttr(living_photo_live_key, live_ad_roi_ratio_accessor).value_or(0.0);
      double photo_roi_ratio = context->GetDoubleItemAttr(*it, photo_ad_roi_ratio_accessor).value_or(0.0);
      if (photo_roi_ratio <= photo_live_roi_ratio) {
        is_photo_live = false;
      } else {
        is_photo = false;
      }
    }
    double gmv = 0.0;
    int photo_type = 0;
    if (is_photo_live) {
      if (photo_live_cnt > follow_store_wide_photo_live_limit) {
        continue;
      }
      gmv = context->GetDoubleItemAttr(*it, merchant_living_photo_pgmv_accessor).value_or(0.0);
      photo_type = 3;
      context->SetIntItemAttr(*it, photo_store_wide_live_id_accessor, Util::GetId(living_photo_live_key));
      double gpm = gmv * 100;  // 商业化那边要求传的单位是分 这里单元是元进行转换
      double auto_roi = context->GetDoubleItemAttr(living_photo_live_key, auto_roi_accessor).value_or(0.0);
      double ad_boost_cpm_score = auto_roi > 0.0 ? gpm / auto_roi : 0.0;
      context->SetDoubleItemAttr(*it, ad_gpm_score_accessor, gpm);
      context->SetDoubleItemAttr(*it, ad_boost_cpm_score_accessor, ad_boost_cpm_score);
      context->SetDoubleItemAttr(*it, store_wide_cpm_accessor, ad_boost_cpm_score);
      context->SetDoubleItemAttr(*it, ad_auto_roi_score_accessor, auto_roi);
      context->SetDoubleItemAttr(
          *it, ad_roi_ratio_score_accessor,
          context->GetDoubleItemAttr(living_photo_live_key, live_ad_roi_ratio_accessor).value_or(0.0));
      context->SetStringItemAttr(
          *it, ad_trans_info_accessor,
          std::string(
              context->GetStringItemAttr(living_photo_live_key, live_ad_trans_info_accessor).value_or("")));
      context->SetIntItemAttr(
          *it, ad_creative_id_accessor,
          context->GetIntItemAttr(living_photo_live_key, live_creative_id_accessor).value_or(0));
      context->SetIntItemAttr(*it, photo_store_wide_type_accessor, photo_type);
      context->SetIntItemAttr(*it, has_store_wide_accessor, 1);
      photo_live_cnt++;
      // living photo set auto roi
      context->SetDoubleItemAttr(*it, auto_roi_accessor, auto_roi);
      context->SetIntItemAttr(*it, is_store_wide_accessor, 1);
      if (enable_storewide_trans_is_whole_store_roi_ad) {
        context->SetIntItemAttr(*it, is_whole_store_roi_ad_accessor, 1);
      }
    } else if (is_photo) {
      if (photo_cnt > follow_store_wide_merchant_cart_limit) {
        continue;
      }
      gmv = context->GetDoubleItemAttr(*it, merchant_cart_pgmv_accessor).value_or(0.0);
      photo_type = 2;
      context->SetIntItemAttr(*it, merchant_product_id_accessor,
                              context->GetIntItemAttr(*it, merchant_item_id_accessor).value_or(0));
      double gpm = gmv * 100;  // 商业化那边要求传的单位是分 这里单元是元进行转换
      double auto_roi = context->GetDoubleItemAttr(*it, auto_roi_accessor).value_or(0.0);
      double ad_boost_cpm_score = auto_roi > 0.0 ? gpm / auto_roi : 0.0;
      context->SetDoubleItemAttr(*it, ad_gpm_score_accessor, gpm);
      context->SetDoubleItemAttr(*it, ad_boost_cpm_score_accessor, ad_boost_cpm_score);
      context->SetDoubleItemAttr(*it, store_wide_cpm_accessor, ad_boost_cpm_score);
      context->SetDoubleItemAttr(*it, ad_auto_roi_score_accessor, auto_roi);
      context->SetDoubleItemAttr(*it, ad_roi_ratio_score_accessor,
                                 context->GetDoubleItemAttr(*it, photo_ad_roi_ratio_accessor).value_or(0.0));
      context->SetStringItemAttr(
          *it, ad_trans_info_accessor,
          std::string(context->GetStringItemAttr(*it, photo_ad_trans_info_accessor).value_or("")));
      context->SetIntItemAttr(*it, ad_creative_id_accessor,
                              context->GetIntItemAttr(*it, photo_creative_id_accessor).value_or(0));
      context->SetIntItemAttr(*it, photo_store_wide_type_accessor, photo_type);
      context->SetIntItemAttr(*it, has_store_wide_accessor, 1);
      if (enable_storewide_trans_is_whole_store_roi_ad) {
        context->SetIntItemAttr(*it, is_whole_store_roi_ad_accessor, 1);
      }
      photo_cnt++;
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, FollowFillWholeStoreRoiPhotoEnricher,
                 FollowFillWholeStoreRoiPhotoEnricher);

void FollowFillWholeStoreRoiLiveEnricher::Enrich(MutableRecoContextInterface *context,
                                                 RecoResultConstIter begin, RecoResultConstIter end) {
  // read
  auto *fr_pctr_accessor = context->GetItemAttrAccessor("fr_pctr");
  auto *eshop_cart_cvr_accessor = context->GetItemAttrAccessor("eshop_cart_cvr");
  auto *eshop_live_price_accessor = context->GetItemAttrAccessor("eshop_live_price");
  auto *auto_roi_accessor = context->GetItemAttrAccessor("autoRoi");
  auto *live_ad_roi_ratio_accessor = context->GetItemAttrAccessor("ad_roi_ratio_score");
  auto *live_ad_trans_info_accessor = context->GetItemAttrAccessor("ad_trans_info");
  auto *live_creative_id_accessor = context->GetItemAttrAccessor("ad_creative_id");

  // write
  auto *ad_gpm_score_accessor = context->GetItemAttrAccessor("resp_ad_gpm_score");
  auto *ad_boost_cpm_score_accessor = context->GetItemAttrAccessor("resp_ad_boost_cpm_score");
  auto *ad_auto_roi_score_accessor = context->GetItemAttrAccessor("resp_ad_auto_roi_score");
  auto *ad_roi_ratio_score_accessor = context->GetItemAttrAccessor("resp_ad_roi_ratio_score");
  auto *ad_trans_info_accessor = context->GetItemAttrAccessor("resp_ad_trans_info");
  auto *ad_creative_id_accessor = context->GetItemAttrAccessor("resp_ad_creative_id");
  auto *store_wide_cpm_accessor = context->GetItemAttrAccessor("resp_store_wide_cpm");
  auto *has_store_wide_accessor = context->GetItemAttrAccessor("resp_has_store_wide");
  auto *is_whole_store_roi_ad_accessor = context->GetItemAttrAccessor("resp_is_whole_store_roi_ad");

  int ad_roi_ratio_alpha = context->GetDoubleCommonAttr("ad_roi_ratio_alpha").value_or(0.0);
  bool enable_store_wide_in_mix = context->GetIntCommonAttr("enable_store_wide_in_mix").value_or(0) == 1;
  bool enable_storewide_trans_is_whole_store_roi_ad =
      context->GetIntCommonAttr("enable_storewide_trans_is_whole_store_roi_ad").value_or(0) == 1;

  for (auto it = begin; it != end; it++) {
    double ctr = context->GetDoubleItemAttr(*it, fr_pctr_accessor).value_or(0.0);
    double merchant_cvr = context->GetDoubleItemAttr(*it, eshop_cart_cvr_accessor).value_or(0.0);
    double merchant_price = context->GetDoubleItemAttr(*it, eshop_live_price_accessor).value_or(0.0);
    double gpm = ctr * merchant_price * merchant_cvr;
    double auto_roi = context->GetDoubleItemAttr(*it, auto_roi_accessor).value_or(0.0);
    double roi_ratio = context->GetDoubleItemAttr(*it, live_ad_roi_ratio_accessor).value_or(0.0);
    double ad_boost_cpm_score = 0.0;
    if (auto_roi > 0 && roi_ratio > 0) {
      ad_boost_cpm_score =
          gpm * (ad_roi_ratio_alpha / roi_ratio + (1 - ad_roi_ratio_alpha) / auto_roi) * 10;  // 单位为元/千次
    }
    context->SetDoubleItemAttr(*it, ad_gpm_score_accessor, gpm);
    context->SetDoubleItemAttr(*it, ad_boost_cpm_score_accessor, ad_boost_cpm_score);
    context->SetDoubleItemAttr(*it, ad_auto_roi_score_accessor, auto_roi);
    context->SetDoubleItemAttr(*it, ad_roi_ratio_score_accessor, roi_ratio);
    context->SetStringItemAttr(
        *it, ad_trans_info_accessor,
        std::string(context->GetStringItemAttr(*it, live_ad_trans_info_accessor).value_or("")));
    context->SetIntItemAttr(*it, ad_creative_id_accessor,
                            context->GetIntItemAttr(*it, live_creative_id_accessor).value_or(0));
    if (enable_store_wide_in_mix) {
      context->SetDoubleItemAttr(*it, store_wide_cpm_accessor, ad_boost_cpm_score);
      context->SetIntItemAttr(*it, has_store_wide_accessor, 1);
      if (enable_storewide_trans_is_whole_store_roi_ad) {
        context->SetIntItemAttr(*it, is_whole_store_roi_ad_accessor, 1);
      }
    } else {
      if (!enable_storewide_trans_is_whole_store_roi_ad) {
        context->SetIntItemAttr(*it, is_whole_store_roi_ad_accessor, 1);
      }
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, FollowFillWholeStoreRoiLiveEnricher, FollowFillWholeStoreRoiLiveEnricher);

}  // namespace platform
}  // namespace ks
