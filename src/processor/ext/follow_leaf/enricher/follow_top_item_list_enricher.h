#pragma once

#include <memory>
#include <stdexcept>
#include <string>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "ks/reco_proto/proto/realtime_reco.pb.h"

namespace ks {
namespace platform {

class FollowTopItemListEnricher : public CommonRecoBaseEnricher {
 public:
  FollowTopItemListEnricher() {}
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end);
  void Init(MutableRecoContextInterface *context);
  void GetTopItems(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end);

 private:
  bool InitProcessor() override {
    return true;
  }
};
}  // namespace platform
}  // namespace ks
