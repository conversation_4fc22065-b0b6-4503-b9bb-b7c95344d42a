#include "dragon/src/processor/ext/follow_leaf/enricher/follow_top_item_list_enricher.h"

#include <algorithm>
#include <string>
#include <utility>
#include <vector>

namespace ks {
namespace platform {
void FollowTopItemListEnricher::Enrich(MutableRecoContextInterface *context,
                                       RecoResultConstIter begin,
                                       RecoResultConstIter end) {
    GetTopItems(context, begin, end);
}

void FollowTopItemListEnricher::GetTopItems(MutableRecoContextInterface *context,
                                            RecoResultConstIter begin,
                                            RecoResultConstIter end) {
    // 曝光空间下的的 accessors 列表
    std::vector<std::pair<const char*, const char*>> realshow_accessors = {
        {"fr_l2r_score", "follow_fr_l2r_top_item_list"},
        {"fr_final_score", "follow_fr_final_top_item_list"},
        {"fr_pctr", "follow_fr_pctr_top_item_list"},
    };

    // 点击空间下的 accessors 列表
    std::vector<std::pair<const char*, const char*>> click_accessors = {
        {"fr_pltr", "follow_fr_pltr_top_item_list"},
        {"fr_pwatch_time", "follow_fr_pwt_top_item_list"},
        {"fr_pfinish", "follow_fr_pfinish_top_item_list"},
        {"fr_plvtr", "follow_fr_plvtr_top_item_list"},
    };

    auto *photo_id_accessor = context->GetItemAttrAccessor("photo_id");
    auto *pctr_accessor = context->GetItemAttrAccessor("fr_pctr");

    // 曝光空间
    for (const auto &accessor_pairs : realshow_accessors) {
        auto *score_accessor = context->GetItemAttrAccessor(accessor_pairs.first);
        std::vector<std::pair<double, int64_t>> score_photoid_pairs;
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
            double score = context->GetDoubleItemAttr(result, score_accessor).value_or(0.0);
            int64_t photo_id = context->GetIntItemAttr(result, photo_id_accessor).value_or(0);
            score_photoid_pairs.emplace_back(score, photo_id);
        });
        std::stable_sort(score_photoid_pairs.begin(), score_photoid_pairs.end(),
                         [](const auto &a, const auto &b) { return a.first > b.first; });
        if (score_photoid_pairs.size() > 100) {
            score_photoid_pairs.resize(100);
        }
        std::vector<int64_t> top_photo_ids;
        for (const auto &pair : score_photoid_pairs) {
            top_photo_ids.push_back(pair.second);
        }
        context->SetIntListCommonAttr(std::string(accessor_pairs.second), std::move(top_photo_ids));
    }

    // 点击空间
    for (const auto &accessor_pairs : click_accessors) {
        auto *score_accessor = context->GetItemAttrAccessor(accessor_pairs.first);
        std::vector<std::pair<double, int64_t>> score_photoid_pairs;
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
            double score = context->GetDoubleItemAttr(result, score_accessor).value_or(0.0);
            double pctr = context->GetDoubleItemAttr(result, pctr_accessor).value_or(1.0);
            score *= pctr;  // 将得分乘以 fr_pctr
            int64_t photo_id = context->GetIntItemAttr(result, photo_id_accessor).value_or(0);
            score_photoid_pairs.emplace_back(score, photo_id);
        });
        std::stable_sort(score_photoid_pairs.begin(), score_photoid_pairs.end(),
                  [](const auto &a, const auto &b) { return a.first > b.first; });
        if (score_photoid_pairs.size() > 100) {
            score_photoid_pairs.resize(100);
        }
        std::vector<int64_t> top_photo_ids;
        for (const auto &pair : score_photoid_pairs) {
            top_photo_ids.push_back(pair.second);
        }
        context->SetIntListCommonAttr(std::string(accessor_pairs.second), std::move(top_photo_ids));
    }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, FollowTopItemListEnricher, FollowTopItemListEnricher);

}  // namespace platform
}  // namespace ks
