#pragma once

#include <memory>
#include <string>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/processor/ext/follow_leaf/context/context.h"

namespace ks {
namespace platform {
class FollowBizTypeEnricher : public CommonRecoBaseEnricher {
 public:
  FollowBizTypeEnricher() {}
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end);

 private:
  bool InitProcessor() override {
    is_recruit_photo_attr_ = config()->GetString("is_recruit_photo_attr", "");
    if (is_recruit_photo_attr_.empty()) {
      LOG(ERROR) << "FollowBizTypeEnricher is_recruit_photo_attr is empty";
      return false;
    }
    is_recruit_live_attr_ = config()->GetString("is_recruit_live_attr", "");
    if (is_recruit_live_attr_.empty()) {
      LOG(ERROR) << "FollowBizTypeEnricher is_recruit_live_attr is empty";
      return false;
    }
    is_house_photo_attr_ = config()->GetString("is_house_photo_attr", "");
    is_house_live_attr_ = config()->GetString("is_house_live_attr", "");
    return true;
  }
  void InitItemAttr(MutableRecoContextInterface *context);
  void IsNatural(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end);
  void IsAd(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end);
  void IsMerchant(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end);
  void IsGift(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end);
  void IsStoreWide(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end);
  void IsLocalLife(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end);
  void IsMerchantCart(MutableRecoContextInterface *context,
                      RecoResultConstIter begin, RecoResultConstIter end);
  void IsRecruitPhoto(MutableRecoContextInterface *context,
                      RecoResultConstIter begin, RecoResultConstIter end);
  void IsRecruitLive(MutableRecoContextInterface *context,
                     RecoResultConstIter begin, RecoResultConstIter end);
  void Clear() {}

 private:
  ItemAttr* is_natural_accessor_ = nullptr;
  ItemAttr* is_ad_accessor_ = nullptr;
  ItemAttr* is_merchant_accessor_ = nullptr;
  ItemAttr* is_gift_accessor_ = nullptr;
  ItemAttr* is_store_wide_accessor_ = nullptr;
  ItemAttr* is_local_life_accessor_ = nullptr;
  ItemAttr* is_merchant_cart_accessor_ = nullptr;
  ItemAttr* is_recruit_photo_accessor_ = nullptr;
  ItemAttr* is_recruit_live_accessor_ = nullptr;
  ItemAttr* is_house_photo_accessor_ = nullptr;
  ItemAttr* is_house_live_accessor_ = nullptr;

  std::string is_recruit_photo_attr_;
  std::string is_recruit_live_attr_;
  std::string is_house_photo_attr_;
  std::string is_house_live_attr_;
  DISALLOW_COPY_AND_ASSIGN(FollowBizTypeEnricher);
};
}  // namespace platform
}  // namespace ks
