#pragma once

#include <algorithm>
#include <cmath>
#include <cstdint>
#include <cstdlib>
#include <ctime>
#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>
#include "dragon/src/module/common_reco_light_function.h"
#include "dragon/src/processor/ext/follow_leaf/context/context.h"
#include "ks/reco_proto/proto/realtime_base.pb.h"
#include "ks/reco_proto/proto/reco_follow_exptag.pb.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"
#include "teams/reco-arch/colossus/item/common_item_types.h"
#include "ks/base/abtest/abtest_globals.h"
#include "ks/base/abtest/abtest_instance.h"
#include "ks/base/abtest/metrics/abtest_metric.h"
#include "ks/base/container/hashset.h"
#include "ks/realtime_reco/index/cdoc_convertor.h"
#include "dragon/src/processor/ext/follow_leaf/util/json_helper.h"
#include "ks/reco_proto/ad/ad_proto/kuaishou/ad/ad_info.pb.h"

using google::protobuf::Message;

namespace ks {
namespace platform {

class FollowLeafLightFunctionSet : public CommonRecoBaseLightFunctionSet {
 public:
  FollowLeafLightFunctionSet() {
    REGISTER_LIGHT_FUNCTION(JudgePhotoRetrievalSuccess);
    REGISTER_LIGHT_FUNCTION(GenLiveAuthorList);
    REGISTER_LIGHT_FUNCTION(JudgeIsLivingPhoto);
    REGISTER_LIGHT_FUNCTION(StringToSet);
    REGISTER_LIGHT_FUNCTION(MarkLocalLifeLive);
    REGISTER_LIGHT_FUNCTION(CalRecruitGpm);
    REGISTER_LIGHT_FUNCTION(CalHouseGpm);
    REGISTER_LIGHT_FUNCTION(ParseUserConsumeType);
    REGISTER_LIGHT_FUNCTION(SourceToExpTag);
    REGISTER_LIGHT_FUNCTION(ParsePhotoColossusInfo);
    REGISTER_LIGHT_FUNCTION(ParseLiveColossusInfo);
    REGISTER_LIGHT_FUNCTION(ParseHighValueAuthorV2ids);
    REGISTER_LIGHT_FUNCTION(CheckMixCriticAction);
    REGISTER_LIGHT_FUNCTION(JudgeUseRecallCache);
    REGISTER_LIGHT_FUNCTION(JudgePhotoGifStrategy);
    REGISTER_LIGHT_FUNCTION(CalLiveAutoPlayWeight);
    REGISTER_LIGHT_FUNCTION(JudgeIsRealshow);
    REGISTER_LIGHT_FUNCTION(BoostItemScore);
    REGISTER_LIGHT_FUNCTION(IsStringUint64VectorMapEmpty);
    REGISTER_LIGHT_FUNCTION(JudgeIsRecruitPhoto);
    REGISTER_LIGHT_FUNCTION(CalcRecruitPhotoScore);
    REGISTER_LIGHT_FUNCTION(JudgeIsRecruitLive);
    REGISTER_LIGHT_FUNCTION(DecodeDurationWtd);
    REGISTER_LIGHT_FUNCTION(ParseKeyDoubleValueMap);
    REGISTER_LIGHT_FUNCTION(GenShowPidList);
    REGISTER_LIGHT_FUNCTION(GenPhotoFrXtrBias);
    REGISTER_LIGHT_FUNCTION(SerializeAdInfoStr);
    REGISTER_LIGHT_FUNCTION(SocialDataHitCache);
    REGISTER_LIGHT_FUNCTION(ParseConsumePreferType);
    REGISTER_LIGHT_FUNCTION(ParseTopBarVVStr);
    REGISTER_LIGHT_FUNCTION(FillContextFullrankFea);
    REGISTER_LIGHT_FUNCTION(GenLastAdPos);
    REGISTER_LIGHT_FUNCTION(GenLastAdPosOutSide);
    REGISTER_LIGHT_FUNCTION(GenIndexItemKey);
    REGISTER_LIGHT_FUNCTION(EndAdRetrieval);
  }

  static bool JudgePhotoRetrievalSuccess(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    int64 follow_list_shard_num = context.GetIntCommonAttr("follow_list_shard_num").value_or(1);

    // 有一个成功就算成功
    bool is_all_success = true;
    bool is_one_success = false;
    for (int i = 0; i < follow_list_shard_num; i++) {
      if (context.GetIntCommonAttr("get_photo_success_" + std::to_string(i)).value_or(0) != 1) {
        is_all_success = false;
      } else {
        is_one_success = true;
      }
    }
    base::perfutil::PerfUtilWrapper::CountLogStash(kPerfNs, "follow.retieval.photo.all",
                                                   GlobalHolder::GetServiceIdentifier(),
                                                   std::to_string(is_all_success));
    base::perfutil::PerfUtilWrapper::CountLogStash(kPerfNs, "follow.retieval.photo.one",
                                                   GlobalHolder::GetServiceIdentifier(),
                                                   std::to_string(is_one_success));
    context.SetIntCommonAttr("get_photo_success", is_one_success);
    return true;
  }

  static bool GenLiveAuthorList(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
    std::set<int64> live_author_set;
    auto follow_list = context.GetIntListCommonAttr("follow_list");
    auto query_index_accessor = context.GetIntItemAttr("query_index");
    int query_index_error = 0;
    if (follow_list) {
      for (auto it = begin; it != end; it++) {
        int64 query_index = query_index_accessor(*it).value_or(-1);
        if (query_index < 0 || query_index > follow_list->size()) {
          query_index_error++;
        } else {
          live_author_set.insert((*follow_list)[query_index]);
        }
      }
    }
    std::vector<int64> live_author_list(live_author_set.begin(), live_author_set.end());
    context.SetIntListCommonAttr("live_author_list", std::move(live_author_list));
    base::perfutil::PerfUtilWrapper::IntervalLogStash(query_index_error, kPerfNs, "follow.live.author",
                                                      GlobalHolder::GetServiceIdentifier(), "error");
    return true;
  }

  static bool JudgeIsLivingPhoto(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto author_id_getter = context.GetIntItemAttr("author_id");
    auto item_type_getter = context.GetIntItemAttr("item_type");
    auto is_living_photo_setter = context.SetIntItemAttr("is_living_photo");
    auto living_photo_live_key_setter = context.SetIntItemAttr("living_photo_live_key");
    std::unordered_map<int64, int64> aid_to_key_map;
    for (auto it = begin; it != end; it++) {
      if (item_type_getter(*it).value_or(0) == follow::LIVE_TYPE) {
        aid_to_key_map.insert({author_id_getter(*it).value_or(0), it->ItemKey()});
      }
    }
    for (auto it = begin; it != end; it++) {
      if (item_type_getter(*it).value_or(0) == follow::PHOTO_TYPE) {
        int64 aid = author_id_getter(*it).value_or(0);
        if (aid != 0 && aid_to_key_map.count(aid) > 0) {
          is_living_photo_setter(*it, 1);
          living_photo_live_key_setter(*it, aid_to_key_map[aid]);
        }
      }
    }
    return true;
  }

  static bool StringToSet(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                          RecoResultConstIter end) {
    auto value_str = std::string(context.GetStringCommonAttr("input_value").value_or(""));
    std::vector<std::string> datas;
    std::shared_ptr<folly::F14FastSet<int64>> value_set(new folly::F14FastSet<int64>);
    base::SplitStringWithOptions(value_str, ",", true, true, &datas);
    for (const auto& data : datas) {
      std::vector<std::string> id_kv;
      uint64 id = 0;
      base::SplitStringWithOptions(data, ":", true, true, &id_kv);
      if (id_kv.size() >= 1 && base::StringToUint64(id_kv.at(0), &id)) {
        value_set->insert(id);
      }
    }
    context.SetPtrCommonAttr("output_value", value_set);
    return true;
  }

  static bool MarkLocalLifeLive(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
    auto *locallife_live_authors_set =
        context.GetPtrCommonAttr<folly::F14FastSet<uint64>>("locallife_live_authors_set");
    if (locallife_live_authors_set == nullptr) {
      return true;
    }
    auto author_id_getter = context.GetIntItemAttr("author_id");
    auto is_locallife_live_setter = context.SetIntItemAttr("is_locallife_live");
    for (auto it = begin; it != end; it++) {
      if (locallife_live_authors_set->count(author_id_getter(*it).value_or(0)) > 0) {
        is_locallife_live_setter(*it, 1);
      }
    }
    return true;
  }

  static bool CalRecruitGpm(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                            RecoResultConstIter end) {
    double calibration_coeff1 = context.GetDoubleCommonAttr("calibration_coeff1").value_or(1.0);
    double calibration_coeff2 = context.GetDoubleCommonAttr("calibration_coeff2").value_or(1.0);
    bool enable_calibration = context.GetIntCommonAttr("enable_calibration").value_or(0) == 1;
    auto recruit_deliver_type_list = context.GetIntListCommonAttr("uPropertyUserCluster");
    bool is_deliver_user = false;
    if (recruit_deliver_type_list) {
      if (std::find(recruit_deliver_type_list->begin(), recruit_deliver_type_list->end(), 102) !=
          recruit_deliver_type_list->end()) {  //  102  代表已投递用户
        is_deliver_user = true;
      }
    }
    double boost_coef = context.GetDoubleCommonAttr("boost_coef").value_or(1.0);
    auto origin_score_getter = context.GetDoubleItemAttr("origin_score");
    auto output_score_setter = context.SetDoubleItemAttr("output_score");
    auto has_output_score_setter = context.SetIntItemAttr("has_output_score");
    for (auto it = begin; it != end; it++) {
      auto origin_score = origin_score_getter(*it);
      if (origin_score && std::fabs(*origin_score) > 1e-8) {
        double output_score = *origin_score;
        if (enable_calibration) {
          output_score *= calibration_coeff1;
          if (is_deliver_user) {
            output_score *= calibration_coeff2;
          }
        }
        output_score *= boost_coef;
        output_score_setter(*it, output_score);
        has_output_score_setter(*it, 1);
      }
    }
    return true;
  }

  static bool CalHouseGpm(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                            RecoResultConstIter end) {
    double calibration_coeff1 = context.GetDoubleCommonAttr("calibration_coeff1").value_or(1.0);
    double calibration_coeff2 = context.GetDoubleCommonAttr("calibration_coeff2").value_or(1.0);
    bool enable_calibration = context.GetIntCommonAttr("enable_calibration").value_or(0) == 1;
    auto u_house_user_type_list = context.GetIntListCommonAttr("uHouseInteractObjectIdList");
    bool is_house_core_user = false;
    if (u_house_user_type_list && !u_house_user_type_list->empty()) {
      is_house_core_user = true;
    }
    double boost_coef = context.GetDoubleCommonAttr("boost_coef").value_or(1.0);
    auto origin_score_getter = context.GetDoubleItemAttr("origin_score");
    auto output_score_setter = context.SetDoubleItemAttr("output_score");
    auto has_output_score_setter = context.SetIntItemAttr("has_output_score");
    for (auto it = begin; it != end; it++) {
      auto origin_score = origin_score_getter(*it);
      if (origin_score && std::fabs(*origin_score) > 1e-8) {
        double output_score = *origin_score;
        if (enable_calibration) {
          output_score *= calibration_coeff1;
          if (is_house_core_user) {
            output_score *= calibration_coeff2;
          }
        }
        output_score *= boost_coef;
        output_score_setter(*it, output_score);
        has_output_score_setter(*it, 1);
      }
    }
    return true;
  }

  static bool ParseUserConsumeType(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    std::string user_consume_type_str(context.GetStringCommonAttr(
      "user_consume_type_str").value_or(""));
    if (user_consume_type_str.empty()) {
      return true;
    }
    base::Json json_data(base::StringToJson(user_consume_type_str));
    auto consume_prefer_type_str = json_data.GetString("follow_consume_perference_type_14d", "");
    auto active_degree_30d_str = json_data.GetString("follow_user_active_degree_30d", "");
    int consume_prefer_type = 0;
    int active_degree_30d = 0;
    if (consume_prefer_type_str == "社交") {
      consume_prefer_type = 1;
    } else if (consume_prefer_type_str == "视频") {
      consume_prefer_type = 2;
    } else if (consume_prefer_type_str == "直播") {
      consume_prefer_type = 3;
    } else if (consume_prefer_type_str == "综合") {
      consume_prefer_type = 4;
    } else {
      consume_prefer_type = 0;
    }
    if (active_degree_30d_str == "low_active" || (active_degree_30d_str == "single_low_active")) {
      active_degree_30d = 1;
    } else if (active_degree_30d_str == "full_active") {
      active_degree_30d = 2;
    } else if (active_degree_30d_str == "high_active") {
      active_degree_30d = 3;
    } else if (active_degree_30d_str == "middle_active") {
      active_degree_30d = 4;
    } else {
      active_degree_30d = 0;
    }
    context.SetIntCommonAttr("uConsumePreferType", consume_prefer_type);
    context.SetIntCommonAttr("uActiveDegree30d", active_degree_30d);
    return true;
  }

  static bool SourceToExpTag(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                             RecoResultConstIter end) {
    auto source_type_getter = context.GetIntItemAttr("source");
    auto exp_tag_setter = context.SetIntItemAttr("exp_tag");
    for (auto it = begin; it != end; it++) {
      int64 source_type = source_type_getter(*it).value_or(0);
      int64 exp_tag = reco::follow::RecoFollowExpTag::FOLLOW_NORMAL_REASON;
      if (reco::RecoEnum::RETRIEVAL_SOURCE_UNSEEN_REBOOST == source_type) {                 // 72
        exp_tag = reco::follow::RecoFollowExpTag::FOLLOW_REBOOST_UNSEEN_RECENT_REASON;      // 101
      } else if (reco::RecoEnum::RETRIEVAL_SOURCE_BIDIRECTIONAL_FOLLOW == source_type) {    // 18 friend
        exp_tag = reco::follow::RecoFollowExpTag::FOLLOW_BIDIRECTIONAL_REASON;              // 81
      } else if (reco::RecoEnum::RETRIEVAL_SOURCE_FOLLOW_FAVORITE_AUTHOR == source_type) {  // 99
        exp_tag = reco::follow::RecoFollowExpTag::FOLLOW_FAVORITE_AUTHOR_REASON;            // 108
      }
      exp_tag_setter(*it, exp_tag);
    }
    return true;
  }

  static bool ParsePhotoColossusInfo(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    auto photo_id_list_ptr = context.GetIntListCommonAttr("colossus_photo_id");
    auto play_time_list_ptr = context.GetIntListCommonAttr("colossus_play_time");
    auto timestamp_list_ptr = context.GetIntListCommonAttr("colossus_timestamp");
    auto channel_list_ptr = context.GetIntListCommonAttr("colossus_channel");
    auto author_id_list_ptr = context.GetIntListCommonAttr("colossus_author_id_v2");
    auto tag_list_ptr = context.GetIntListCommonAttr("colossus_tag");
    auto duration_list_ptr = context.GetIntListCommonAttr("colossus_duration");
    if (!photo_id_list_ptr || !play_time_list_ptr || !timestamp_list_ptr || !channel_list_ptr
    || !author_id_list_ptr || !tag_list_ptr || !duration_list_ptr) {
      return true;
    }

    if (photo_id_list_ptr->size() != play_time_list_ptr->size()
      || photo_id_list_ptr->size() != timestamp_list_ptr->size()
      || photo_id_list_ptr->size() != channel_list_ptr->size()
      || photo_id_list_ptr->size() != author_id_list_ptr->size()
      || photo_id_list_ptr->size() != tag_list_ptr->size()
      || photo_id_list_ptr->size() != duration_list_ptr->size()) {
      return false;
    }

    std::shared_ptr<::Json::Value> json_default_val = std::make_shared<::Json::Value>();
    static auto kconf_config = ks::infra::KConf().Get("reco.model.hetuIndex", json_default_val);
    std::shared_ptr<::Json::Value> tag_json_val = std::make_shared<::Json::Value>();
    if (kconf_config != nullptr) {
      tag_json_val = kconf_config->Get();
    }

    int64 follow_photo_play_time_1d = 0;
    int64 follow_photo_play_time_7d = 0;
    int64 follow_photo_play_time_14d = 0;
    int64 follow_photo_play_time_28d = 0;

    folly::F14FastMap<int, int> sim_user_follow_photo_tag_list_map;
    folly::F14FastMap<int, int> sim_user_private_photo_tag_list_map;
    folly::F14FastMap<uint64, int> sim_user_follow_photo_author_list_play_map;
    folly::F14FastMap<uint64, int> sim_user_follow_photo_author_list_effect_map;
    folly::F14FastMap<uint64, PhotoTopCntInfo> sim_user_follow_photo_photo_list_play_14dmap;
    folly::F14FastMap<uint64, int32> sim_user_follow_photo_author_list_play_14dmap;
    folly::F14FastMap<uint64, int32> sim_user_follow_photo_author_list_effect_14dmap;
    sim_user_follow_photo_tag_list_map.clear();
    sim_user_private_photo_tag_list_map.clear();
    sim_user_follow_photo_author_list_play_map.clear();
    sim_user_follow_photo_author_list_effect_map.clear();
    sim_user_follow_photo_photo_list_play_14dmap.clear();
    sim_user_follow_photo_author_list_play_14dmap.clear();
    sim_user_follow_photo_author_list_effect_14dmap.clear();

    std::vector<int64> sim_user_follow_photo_list_lately_200;
    std::vector<int64> sim_user_follow_photo_list_effect_play_lately_200;
    std::vector<int64> sim_user_follow_photo_list_long_play_lately_200;

     // 最近 14 天播放过的 200 个 photo list
    std::vector<int64> sim_user_follow_photo_list_lately14d_200;
     // 最近 14 天播放过 7s 的 200 个 photo list
    std::vector<int64> sim_user_follow_photo_list_effect_play_lately14d_200;
     // 最近 14 天播放过 30s 的 200 个 photo list
    std::vector<int64> sim_user_follow_photo_list_long_play_lately14d_200;
     // 最近 14 天播放过的 200 个 photo 对应的 duration list
    std::vector<int64> sim_user_follow_duration_list_lately14d_200;
     // 最近 14 天播放过的 200 个 photo 对应的 hetutag list
    std::vector<int64> sim_user_follow_tag_list_lately14d_200;
     // 最近 14 天播放过的 200 个 photo 对应的 author list
    std::vector<int64> sim_user_follow_author_list_lately14d_200;
     // 最近 14 天播放过的 200 个 photo 对应的 playtime list
    std::vector<int64> sim_user_follow_playtime_list_lately14d_200;
    // 最近 14 天播放过的 200 个 photo 对应的消费时间 gap ( 不是发布时间 gap ) list
    std::vector<int64> sim_user_follow_timegap_list_lately14d_200;

    uint64 cur_time = base::GetTimestamp() / 1000000;

    for (int i = photo_id_list_ptr->size() - 1; i >= 0; i--) {
      if (photo_id_list_ptr->at(i) <= 0) {
        continue;
      }
      if (author_id_list_ptr->at(i) <= 0) {
        continue;
      }
      uint16 play_time = play_time_list_ptr->at(i);
      uint32 timestamp = timestamp_list_ptr->at(i);
      uint8 channel_type = channel_list_ptr->at(i);
      uint64 photo_id = photo_id_list_ptr->at(i);
      uint64 author_id = author_id_list_ptr->at(i);
      uint32 hetu_tag_last = tag_list_ptr->at(i);
      uint64 duration = duration_list_ptr->at(i);
      uint32 hetu_tag_two = 0;
      uint32 hetu_tag_one = 0;
      std::vector<int> hetutag_list;
      if (JsonHelper::SafeGetIntArray(*tag_json_val,
          base::IntToString(hetu_tag_last).c_str(), &hetutag_list)) {
        if (hetutag_list.size() > 1) {
          hetu_tag_two = hetutag_list[1];  // 如果有二级类目就用二级类目
        } else if (hetutag_list.size() > 0) {
          hetu_tag_two = hetutag_list[0];  // 无二级类目用一级类目
        }
        if (hetutag_list.size() > 0) {
          hetu_tag_one = hetutag_list[0];
        }
      }
      // 距今天时间天数
      int play_time_gap_day = static_cast<int>((cur_time - timestamp) / (24 * 3600));
      bool is_follow = channel_type == 3 || channel_type == 20 || channel_type == 32
      || channel_type == 33 || channel_type == 86 || channel_type == 87;

      if (is_follow && play_time > 0 && sim_user_follow_photo_list_lately_200.size() < 200) {
        sim_user_follow_photo_list_lately_200.emplace_back(photo_id);
      }

      if (is_follow && play_time > 7 && sim_user_follow_photo_list_effect_play_lately_200.size() < 200) {
        sim_user_follow_photo_list_effect_play_lately_200.emplace_back(photo_id);
      }
      if (is_follow && play_time > 30 && sim_user_follow_photo_list_long_play_lately_200.size() < 200) {
        sim_user_follow_photo_list_long_play_lately_200.emplace_back(photo_id);
      }
      // 近 14 天消费序列
      if (play_time_gap_day <= 14
          && is_follow && play_time > 0
          && sim_user_follow_photo_list_lately14d_200.size() < 200) {
        sim_user_follow_photo_list_lately14d_200.emplace_back(photo_id);
      }
      if (play_time_gap_day <= 14
          && is_follow && play_time > 7
          && sim_user_follow_photo_list_effect_play_lately14d_200.size() < 200) {
        sim_user_follow_photo_list_effect_play_lately14d_200.emplace_back(photo_id);
      }
      if (play_time_gap_day <= 14
          && is_follow && play_time > 30
          && sim_user_follow_photo_list_long_play_lately14d_200.size() < 200) {
        sim_user_follow_photo_list_long_play_lately14d_200.emplace_back(photo_id);
      }
      if (play_time_gap_day <= 14
          && is_follow && play_time > 0
          && sim_user_follow_duration_list_lately14d_200.size() < 200) {
        sim_user_follow_duration_list_lately14d_200.emplace_back(duration);
      }
      if (play_time_gap_day <= 14
          && is_follow && play_time > 0
          && sim_user_follow_tag_list_lately14d_200.size() < 200) {
        sim_user_follow_tag_list_lately14d_200.emplace_back(hetu_tag_one);
      }
      if (play_time_gap_day <= 14
          && is_follow && play_time > 0
          && sim_user_follow_timegap_list_lately14d_200.size() < 200) {
        sim_user_follow_timegap_list_lately14d_200.emplace_back(play_time_gap_day);
      }
      if (play_time_gap_day <= 14
          && is_follow && play_time > 0
          && sim_user_follow_author_list_lately14d_200.size() < 200) {
        sim_user_follow_author_list_lately14d_200.emplace_back(author_id);
      }
      if (play_time_gap_day <= 14
          && is_follow && play_time > 0
          && sim_user_follow_playtime_list_lately14d_200.size() < 200) {
        sim_user_follow_playtime_list_lately14d_200.emplace_back(play_time);
      }

      if (play_time > 0 && hetu_tag_two > 0) {
        if (is_follow) {
          if (sim_user_follow_photo_tag_list_map.find(hetu_tag_two) ==
              sim_user_follow_photo_tag_list_map.end()) {
            sim_user_follow_photo_tag_list_map[hetu_tag_two] = play_time;
          } else {
            sim_user_follow_photo_tag_list_map[hetu_tag_two] += play_time;
          }
        }
        if (sim_user_private_photo_tag_list_map.find(hetu_tag_two) ==
            sim_user_private_photo_tag_list_map.end()) {
          sim_user_private_photo_tag_list_map[hetu_tag_two] = play_time;
        } else {
          sim_user_private_photo_tag_list_map[hetu_tag_two] += play_time;
        }
      }

      if (is_follow && play_time > 0) {
        if (sim_user_follow_photo_author_list_play_map.find(author_id) ==
            sim_user_follow_photo_author_list_play_map.end()) {
          sim_user_follow_photo_author_list_play_map[author_id] = play_time;
        } else {
          sim_user_follow_photo_author_list_play_map[author_id] += play_time;
        }
        if (play_time_gap_day <= 14) {
          if (sim_user_follow_photo_author_list_play_14dmap.find(author_id) ==
            sim_user_follow_photo_author_list_play_14dmap.end()) {
              sim_user_follow_photo_author_list_play_14dmap[author_id] = play_time;
            } else {
              sim_user_follow_photo_author_list_play_14dmap[author_id] += play_time;
            }
        }
      }

      if (is_follow && play_time > 7) {
        if (sim_user_follow_photo_author_list_effect_map.find(author_id) ==
            sim_user_follow_photo_author_list_effect_map.end()) {
          sim_user_follow_photo_author_list_effect_map[author_id] = 1;
        } else {
          sim_user_follow_photo_author_list_effect_map[author_id] += 1;
        }
        if (play_time_gap_day <= 14) {
          if (sim_user_follow_photo_author_list_effect_14dmap.find(author_id) ==
            sim_user_follow_photo_author_list_effect_14dmap.end()) {
              sim_user_follow_photo_author_list_effect_14dmap[author_id] = play_time;
            } else {
              sim_user_follow_photo_author_list_effect_14dmap[author_id] += play_time;
            }
        }
      }
      if (is_follow && play_time > 0 && play_time_gap_day <= 14) {
        if (sim_user_follow_photo_photo_list_play_14dmap.find(photo_id) ==
            sim_user_follow_photo_photo_list_play_14dmap.end()) {
              PhotoTopCntInfo photo_top_cnt_info;
              photo_top_cnt_info.follow_photo_time_14d = play_time;
              photo_top_cnt_info.author_id = author_id;
              photo_top_cnt_info.tag = hetu_tag_one;
              photo_top_cnt_info.duration = duration;
              photo_top_cnt_info.playtime = play_time;
              photo_top_cnt_info.timegap = play_time_gap_day;
              sim_user_follow_photo_photo_list_play_14dmap.insert({photo_id, photo_top_cnt_info});
        } else {
          auto& photo_top_cnt_info = sim_user_follow_photo_photo_list_play_14dmap[photo_id];
          photo_top_cnt_info.follow_photo_time_14d += play_time;
          photo_top_cnt_info.author_id = author_id;
          photo_top_cnt_info.tag = hetu_tag_one;
          photo_top_cnt_info.duration = duration;
          photo_top_cnt_info.playtime = play_time;
          photo_top_cnt_info.timegap = play_time_gap_day;
        }
      }

      // U 粒度历史播放时长
      if (is_follow) {
        if (play_time_gap_day <= 1) {
          follow_photo_play_time_1d += play_time;
        }
        if (play_time_gap_day <= 7) {
          follow_photo_play_time_7d += play_time;
        }
        if (play_time_gap_day <= 14) {
          follow_photo_play_time_14d += play_time;
        }
        if (play_time_gap_day <= 28) {
          follow_photo_play_time_28d += play_time;
        }
      }
    }

    std::vector<int64> sim_user_follow_photo_tag_list_Top_50;
    std::vector<int64> sim_user_private_photo_tag_list_Top_50;
    std::vector<int64> sim_user_follow_photo_author_list_playTop_50;
    std::vector<int64> sim_user_follow_photo_author_list_effectTop_50;

     // 最近 14 天播放时长 Top50 作者 list
    std::vector<int64> sim_user_follow_author_list_play_14dtop50;
     // 最近 14 天有效播放次数 Top50 作者 list
    std::vector<int64> sim_user_follow_author_list_effect_14dtop50;
     // 最近 14 天播放时长 Top50 photolist
    std::vector<int64> sim_user_follow_photo_list_play_14dtop50;
     // 最近 14 天播放时长 Top50 photo 对应 taglist
    std::vector<int64> sim_user_follow_tag_list_play_14dtop50;
     // 最近 14 天播放时长 Top50 photo 对应 duration list
    std::vector<int64> sim_user_follow_duration_list_play_14dtop50;
     // 最近 14 天播放时长 Top50 photo 对应 playtime list
    std::vector<int64> sim_user_follow_playtime_list_play_14dtop50;
     // 最近 14 天播放时长 Top50 photo 对应 author list
    std::vector<int64> sim_user_follow_photo_author_list_play_14dtop50;
     // 最近 14 天播放时长 Top50 photo 对应 playtime list
    std::vector<int64> sim_user_follow_timegap_list_play_14dtop50;

    // 最近 14 天播放时长 Top50 作者 list
    std::vector<std::pair<uint64, int32>> author_play_top_cnt_14d_vec(
      sim_user_follow_photo_author_list_play_14dmap.begin(),
      sim_user_follow_photo_author_list_play_14dmap.end());
    std::sort(author_play_top_cnt_14d_vec.begin(), author_play_top_cnt_14d_vec.end(), [](
      const std::pair<uint64, int32>& a, const std::pair<uint64, int32>& b) {
      return a.second > b.second;
    });
    uint64 author_play_top_list_14d_size =
      author_play_top_cnt_14d_vec.size() > 50 ? 50 : author_play_top_cnt_14d_vec.size();
    for (uint64 i = 0; i < author_play_top_list_14d_size; ++i) {
      sim_user_follow_author_list_play_14dtop50.push_back(author_play_top_cnt_14d_vec[i].first);
    }

    // 最近 14 天有效播放次数 Top50 作者 list
    std::vector<std::pair<uint64, int32>> author_effectplay_top_cnt_14d_vec(
      sim_user_follow_photo_author_list_effect_14dmap.begin(),
      sim_user_follow_photo_author_list_effect_14dmap.end());
    std::sort(author_effectplay_top_cnt_14d_vec.begin(), author_effectplay_top_cnt_14d_vec.end(), [](
      const std::pair<uint64, int32>& a, const std::pair<uint64, int32>& b) {
      return a.second > b.second;
    });
    uint64 author_effectplay_top_list_14d_size =
      author_effectplay_top_cnt_14d_vec.size() > 50 ? 50 : author_effectplay_top_cnt_14d_vec.size();
    for (uint64 i = 0; i < author_effectplay_top_list_14d_size; ++i) {
      sim_user_follow_author_list_effect_14dtop50.push_back(author_effectplay_top_cnt_14d_vec[i].first);
    }

    // 最近 14 天播放时长 Top50 photolist & sideinfo
    std::vector<std::pair<uint64, PhotoTopCntInfo>> photo_top_cnt_info_vec(
      sim_user_follow_photo_photo_list_play_14dmap.begin(),
      sim_user_follow_photo_photo_list_play_14dmap.end());
    std::sort(photo_top_cnt_info_vec.begin(), photo_top_cnt_info_vec.end(), [](
      const std::pair<uint64, PhotoTopCntInfo>& a, const std::pair<uint64, PhotoTopCntInfo>& b) {
      return a.second.follow_photo_time_14d > b.second.follow_photo_time_14d;
    });

    uint64 photo_top_list_size = photo_top_cnt_info_vec.size() > 50 ? 50 : photo_top_cnt_info_vec.size();
    for (uint64 i = 0; i < photo_top_list_size; ++i) {
      sim_user_follow_photo_list_play_14dtop50.push_back(photo_top_cnt_info_vec[i].first);
      sim_user_follow_tag_list_play_14dtop50.push_back(photo_top_cnt_info_vec[i].second.tag);
      sim_user_follow_duration_list_play_14dtop50.push_back(photo_top_cnt_info_vec[i].second.duration);
      sim_user_follow_playtime_list_play_14dtop50.push_back(photo_top_cnt_info_vec[i].second.playtime);
      sim_user_follow_photo_author_list_play_14dtop50.push_back(photo_top_cnt_info_vec[i].second.author_id);
      sim_user_follow_timegap_list_play_14dtop50.push_back(photo_top_cnt_info_vec[i].second.timegap);
    }

    // 不区分 14d toplist
    std::vector<std::pair<int, int>> follow_tag_index_play(
    sim_user_follow_photo_tag_list_map.begin(),
    sim_user_follow_photo_tag_list_map.end());
    std::sort(follow_tag_index_play.begin(), follow_tag_index_play.end(),
          [](const std::pair<int, int> &x, const std::pair<int, int> &y) -> bool {
          return x.second > y.second;
    });
    int top_limit = 0;
    for (auto it = follow_tag_index_play.begin(); it != follow_tag_index_play.end(); ++it) {
      uint32 tagid = it->first;
      sim_user_follow_photo_tag_list_Top_50.emplace_back(tagid);
      top_limit++;
      if (top_limit >= 50) {break;}
    }
    std::vector<std::pair<int, int>> private_tag_index_play(
    sim_user_private_photo_tag_list_map.begin(),
    sim_user_private_photo_tag_list_map.end());
    std::sort(private_tag_index_play.begin(), private_tag_index_play.end(),
          [](const std::pair<int, int> &x, const std::pair<int, int> &y) -> bool {
          return x.second > y.second;
    });
    top_limit = 0;
    for (auto it = private_tag_index_play.begin(); it != private_tag_index_play.end(); ++it) {
      int64 tagid = it->first;
      sim_user_private_photo_tag_list_Top_50.emplace_back(tagid);
      top_limit++;
      if (top_limit >= 50) {break;}
    }
    std::vector<std::pair<uint64, int>> follow_author_index_play(
    sim_user_follow_photo_author_list_play_map.begin(),
    sim_user_follow_photo_author_list_play_map.end());
    std::sort(follow_author_index_play.begin(), follow_author_index_play.end(),
          [](const std::pair<uint64, int> &x, const std::pair<uint64, int> &y) -> bool {
          return x.second > y.second;
    });
    top_limit = 0;
    for (auto it = follow_author_index_play.begin(); it !=
      follow_author_index_play.end(); ++it) {
      uint64 aid = it->first;
      sim_user_follow_photo_author_list_playTop_50.emplace_back(aid);
      top_limit++;
      if (top_limit >= 50) {break;}
    }
    std::vector<std::pair<uint64, int>> follow_author_index_effect_play(
    sim_user_follow_photo_author_list_effect_map.begin(),
    sim_user_follow_photo_author_list_effect_map.end());
    std::sort(follow_author_index_effect_play.begin(), follow_author_index_effect_play.end(),
          [](const std::pair<uint64, int> &x, const std::pair<uint64, int> &y) -> bool {
          return x.second > y.second;
    });
    top_limit = 0;
    for (auto it = follow_author_index_effect_play.begin(); it !=
        follow_author_index_effect_play.end(); ++it) {
      int64 aid_ = it->first;
      sim_user_follow_photo_author_list_effectTop_50.emplace_back(aid_);
      top_limit++;
      if (top_limit >= 50) {break;}
    }

    context.SetIntListCommonAttr("sim_user_follow_photo_list_lately200",
      std::move(sim_user_follow_photo_list_lately_200));
    context.SetIntListCommonAttr("sim_user_follow_photo_list_effectplay_lately200",
      std::move(sim_user_follow_photo_list_effect_play_lately_200));
    context.SetIntListCommonAttr("sim_user_follow_photo_list_longplay_lately200",
      std::move(sim_user_follow_photo_list_long_play_lately_200));

    context.SetIntListCommonAttr("sim_user_follow_photo_tag_list_play_top50",
      std::move(sim_user_follow_photo_tag_list_Top_50));
    context.SetIntListCommonAttr("sim_user_photo_tag_list_play_top50",
      std::move(sim_user_private_photo_tag_list_Top_50));
    context.SetIntListCommonAttr("sim_user_follow_author_list_play_top50",
      std::move(sim_user_follow_photo_author_list_playTop_50));
    context.SetIntListCommonAttr("sim_user_follow_author_list_effectplay_top50",
      std::move(sim_user_follow_photo_author_list_effectTop_50));

    context.SetIntCommonAttr("follow_photo_play_time_1d", follow_photo_play_time_1d);
    context.SetIntCommonAttr("follow_photo_play_time_7d", follow_photo_play_time_7d);
    context.SetIntCommonAttr("follow_photo_play_time_14d", follow_photo_play_time_14d);
    context.SetIntCommonAttr("follow_photo_play_time_28d", follow_photo_play_time_28d);

    // 14d follow lately list
    context.SetIntListCommonAttr("sim_user_follow_photo_list_lately14d_200",
      std::move(sim_user_follow_photo_list_lately14d_200));
    context.SetIntListCommonAttr("sim_user_follow_photo_list_effect_play_lately14d_200",
      std::move(sim_user_follow_photo_list_effect_play_lately14d_200));
    context.SetIntListCommonAttr("sim_user_follow_photo_list_long_play_lately14d_200",
      std::move(sim_user_follow_photo_list_long_play_lately14d_200));
    context.SetIntListCommonAttr("sim_user_follow_duration_list_lately14d_200",
      std::move(sim_user_follow_duration_list_lately14d_200));
    context.SetIntListCommonAttr("sim_user_follow_tag_list_lately14d_200",
      std::move(sim_user_follow_tag_list_lately14d_200));
    context.SetIntListCommonAttr("sim_user_follow_author_list_lately14d_200",
      std::move(sim_user_follow_author_list_lately14d_200));
    context.SetIntListCommonAttr("sim_user_follow_playtime_list_lately14d_200",
      std::move(sim_user_follow_playtime_list_lately14d_200));
    context.SetIntListCommonAttr("sim_user_follow_timegap_list_lately14d_200",
      std::move(sim_user_follow_timegap_list_lately14d_200));

    // 14d follow top list
    context.SetIntListCommonAttr("sim_user_follow_author_list_play_14dtop50",
      std::move(sim_user_follow_author_list_play_14dtop50));
    context.SetIntListCommonAttr("sim_user_follow_author_list_effect_14dtop50",
      std::move(sim_user_follow_author_list_effect_14dtop50));
    context.SetIntListCommonAttr("sim_user_follow_photo_list_play_14dtop50",
      std::move(sim_user_follow_photo_list_play_14dtop50));
    context.SetIntListCommonAttr("sim_user_follow_tag_list_play_14dtop50",
      std::move(sim_user_follow_tag_list_play_14dtop50));
    context.SetIntListCommonAttr("sim_user_follow_duration_list_play_14dtop50",
      std::move(sim_user_follow_duration_list_play_14dtop50));
    context.SetIntListCommonAttr("sim_user_follow_playtime_list_play_14dtop50",
      std::move(sim_user_follow_playtime_list_play_14dtop50));
    context.SetIntListCommonAttr("sim_user_follow_photo_author_list_play_14dtop50",
      std::move(sim_user_follow_photo_author_list_play_14dtop50));
    context.SetIntListCommonAttr("sim_user_follow_timegap_list_play_14dtop50",
      std::move(sim_user_follow_timegap_list_play_14dtop50));

    return true;
  }

  struct LiveTopAuthorCntInfo {
    uint32 follow_live_vv_14d = 0;
    uint32 follow_live_time_14d = 0;
    uint32 follow_live_like_14d = 0;
    uint32 follow_live_comment_14d = 0;
    uint32 follow_live_vv_7d = 0;
    uint32 follow_live_time_7d = 0;
    uint32 follow_live_like_7d = 0;
    uint32 follow_live_comment_7d = 0;
  };

  struct PhotoTopCntInfo {
    uint64 author_id = 0;
    uint64 duration = 0;
    uint64 tag = 0;
    uint64 playtime = 0;
    uint32 follow_photo_time_14d = 0;
    uint32 timegap = 0;
  };

  static bool ParseLiveColossusInfo(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    const auto* colossus_sim_v2_live_items = context.GetPtrCommonAttr<std::vector<const Message *>>(
      "colossus_resp_live_item_ptr");
    if (colossus_sim_v2_live_items == nullptr || colossus_sim_v2_live_items->empty()) {
      return true;
    }
    uint64 cur_time = base::GetTimestamp() / 1000000;
    int64 follow_watch_live_times_1d = 0;
    int64 follow_watch_live_times_7d = 0;
    int64 follow_watch_live_times_14d = 0;
    int64 follow_watch_live_times_28d = 0;
    folly::F14FastMap<uint64, LiveTopAuthorCntInfo> live_author_top_cnt_info_map;
    for (const Message * msg : *colossus_sim_v2_live_items) {
      const colossus::LiveItemV4* live_item_v4 = static_cast<const colossus::LiveItemV4*>(msg);
      if (live_item_v4 == nullptr) {
        continue;
      }
      const colossus::LiveItemV4& item = *live_item_v4;
      if (item.live_id() <= 0) {
        continue;
      }
      if (item.author_id() <= 0) {
        continue;
      }
      uint32 follow_live_vv_in_14d = 0;
      uint32 follow_live_time_in_14d = 0;
      uint32 follow_live_like_in_14d = 0;
      uint32 follow_live_comment_in_14d = 0;

      uint32 follow_live_vv_in_7d = 0;
      uint32 follow_live_time_in_7d = 0;
      uint32 follow_live_like_in_7d = 0;
      uint32 follow_live_comment_in_7d = 0;
      // LiveItemV4 定义：https://docs.corp.kuaishou.com/d/home/<USER>
      // teams/reco-arch/colossus/proto/common_item.proto
      uint64 timestamp = item.timestamp();
      uint32 play_time = item.play_time();   // 单位 second，这个是播放时长
      uint32 auto_play_time = item.auto_play_time();
      uint64 play_time_all = play_time + auto_play_time;
      uint32 label = item.label();
      uint32 live_like = label & 1;
      uint32 live_comment = label & (1<<2);
      //  高 24bits 存 hetu_tag， 低 8bits 存 channel
      uint32 hetu_tag_channel = item.hetu_tag_channel();
      uint32 channel_type = hetu_tag_channel & 0xff;
      // 距今天时间天数
      int play_time_gap_day = static_cast<int>((cur_time - timestamp) / (24 * 3600));
      bool is_follow = channel_type == 3 || channel_type == 32 || channel_type == 33
        || channel_type == 86 || channel_type == 87;
      // U 粒度历史播放时长
      if (play_time_gap_day <= 1) {
        if (is_follow) {
          follow_watch_live_times_1d += play_time_all;
        }
      }
      if (play_time_gap_day <= 7) {
        if (is_follow) {
          follow_watch_live_times_7d += play_time_all;
          follow_live_vv_in_7d = 1;
          follow_live_time_in_7d = play_time_all;
          follow_live_like_in_7d = live_like;
          follow_live_comment_in_7d = live_comment;
        }
      }
      if (play_time_gap_day <= 14) {
        if (is_follow) {
          follow_watch_live_times_14d += play_time_all;
          follow_live_vv_in_14d = 1;
          follow_live_time_in_14d = play_time_all;
          follow_live_like_in_14d = live_like;
          follow_live_comment_in_14d = live_comment;
        }
      }
      if (play_time_gap_day <= 28) {
        if (is_follow) {
          follow_watch_live_times_28d += play_time_all;
        }
      }
      if (live_author_top_cnt_info_map.find(item.author_id()) != live_author_top_cnt_info_map.end()) {
        auto& live_author_top_cnt_info = live_author_top_cnt_info_map[item.author_id()];
        live_author_top_cnt_info.follow_live_vv_14d += follow_live_vv_in_14d;
        live_author_top_cnt_info.follow_live_time_14d += follow_live_time_in_14d;
        live_author_top_cnt_info.follow_live_like_14d += follow_live_like_in_14d;
        live_author_top_cnt_info.follow_live_comment_14d += follow_live_comment_in_14d;
        live_author_top_cnt_info.follow_live_vv_7d += follow_live_vv_in_7d;
        live_author_top_cnt_info.follow_live_time_7d += follow_live_time_in_7d;
        live_author_top_cnt_info.follow_live_like_7d += follow_live_like_in_7d;
        live_author_top_cnt_info.follow_live_comment_7d += follow_live_comment_in_7d;
      } else {
        LiveTopAuthorCntInfo live_author_top_cnt_info;
        live_author_top_cnt_info.follow_live_vv_14d = follow_live_vv_in_14d;
        live_author_top_cnt_info.follow_live_time_14d = follow_live_time_in_14d;
        live_author_top_cnt_info.follow_live_like_14d = follow_live_like_in_14d;
        live_author_top_cnt_info.follow_live_comment_14d = follow_live_comment_in_14d;
        live_author_top_cnt_info.follow_live_vv_7d = follow_live_vv_in_7d;
        live_author_top_cnt_info.follow_live_time_7d = follow_live_time_in_7d;
        live_author_top_cnt_info.follow_live_like_7d = follow_live_like_in_7d;
        live_author_top_cnt_info.follow_live_comment_7d = follow_live_comment_in_7d;
        live_author_top_cnt_info_map.insert({item.author_id(), live_author_top_cnt_info});
      }
    }
    std::vector<std::pair<uint64, LiveTopAuthorCntInfo>> live_author_info_vec(
      live_author_top_cnt_info_map.begin(), live_author_top_cnt_info_map.end());
    std::sort(live_author_info_vec.begin(), live_author_info_vec.end(), [](
      const std::pair<uint64, LiveTopAuthorCntInfo>& a, const std::pair<uint64, LiveTopAuthorCntInfo>& b) {
      return a.second.follow_live_time_14d > b.second.follow_live_time_14d;
    });
    uint64 author_list_size = live_author_info_vec.size() > 20 ? 20 : live_author_info_vec.size();
    std::vector<int64> user_recent_14d_play_top_author_list;
    std::vector<int64> user_recent_14d_play_top_time_list;
    std::vector<int64> user_recent_7d_play_top_author_list;
    std::vector<int64> user_recent_7d_play_top_time_list;
    for (uint64 i = 0; i < author_list_size; ++i) {
      user_recent_14d_play_top_author_list.push_back(live_author_info_vec[i].first);
      user_recent_14d_play_top_time_list.push_back(live_author_info_vec[i].second.follow_live_time_14d);
    }
    std::sort(live_author_info_vec.begin(), live_author_info_vec.end(), [](
      const std::pair<uint64, LiveTopAuthorCntInfo>& a, const std::pair<uint64, LiveTopAuthorCntInfo>& b) {
      return a.second.follow_live_time_7d > b.second.follow_live_time_7d;
    });
    for (uint64 i = 0; i < author_list_size; ++i) {
      user_recent_7d_play_top_author_list.push_back(live_author_info_vec[i].first);
      user_recent_7d_play_top_time_list.push_back(live_author_info_vec[i].second.follow_live_time_7d);
    }

    context.SetIntListCommonAttr("user_recent_14d_play_top_author_list",
      std::move(user_recent_14d_play_top_author_list));
    context.SetIntListCommonAttr("user_recent_14d_play_top_time_list",
      std::move(user_recent_14d_play_top_time_list));
    context.SetIntListCommonAttr("user_recent_7d_play_top_author_list",
      std::move(user_recent_7d_play_top_author_list));
    context.SetIntListCommonAttr("user_recent_7d_play_top_time_list",
      std::move(user_recent_7d_play_top_time_list));

    context.SetIntCommonAttr("follow_watch_live_times_1d", follow_watch_live_times_1d);
    context.SetIntCommonAttr("follow_watch_live_times_7d", follow_watch_live_times_7d);
    context.SetIntCommonAttr("follow_watch_live_times_14d", follow_watch_live_times_14d);
    context.SetIntCommonAttr("follow_watch_live_times_28d", follow_watch_live_times_28d);
    return true;
  }


  static bool ParseHighValueAuthorV2ids(
    const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    std::string highvalue_author_v2_ids_str(context.GetStringCommonAttr(
      "highvalue_author_v2_ids_str").value_or(""));
    if (highvalue_author_v2_ids_str.empty()) {
      return true;
    }

    // 获取高价值 ua 兴趣分 2.0 对
    std::vector<std::string> datas;
    std::vector<int64> highvalue_author_v2_ids_vec;
    std::shared_ptr<folly::F14FastMap<uint64, double>> highvalue_author_ids_with_offline_score(
      new folly::F14FastMap<uint64, double>);
    base::SplitStringWithOptions(highvalue_author_v2_ids_str, ",", true, true, &datas);
    for (int i = 0; i < datas.size(); i++) {
      std::vector<std::string> aid_kv;
      int64 author_id = 0;
      base::SplitStringWithOptions(datas.at(i), ":", true, true, &aid_kv);
      if (aid_kv.size() >= 1 && base::StringToInt64(aid_kv.at(0), &author_id)) {
        // B 端实验验证
        if (ks::abtest::AbtestInstance::GetBoolean(
          ks::AbtestBiz::RECO_FOLLOW, "enable_highvalue_author_boost_for_Bclient", author_id, "", true)) {
          highvalue_author_v2_ids_vec.emplace_back(author_id);
        }

        // 填充离线 uascore
        double ua_score = 0.0;
        if (aid_kv.size() >= 2 && absl::SimpleAtod(aid_kv.at(1), &ua_score)) {
          highvalue_author_ids_with_offline_score->emplace(author_id, ua_score);
        }
      }
    }

    context.SetIntListCommonAttr("uHighvalueAuthorListV1", std::move(highvalue_author_v2_ids_vec));
    context.SetPtrCommonAttr("highvalue_author_ids_with_offline_score_ptr",
      highvalue_author_ids_with_offline_score);
    return true;
  }

  static bool FillContextFullrankFea(
    const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
      auto author_id_getter = context.GetIntItemAttr("author_id");
      auto duration_ms_getter = context.GetIntItemAttr("duration_ms");
      auto timestamp_getter = context.GetIntItemAttr("timestamp");
      auto hetuone_getter = context.GetIntListItemAttr("pHetuLevel1Id");
      auto hetutwo_getter = context.GetIntListItemAttr("pHetuLevel2Id");
      int sample_size = 100;
      int64 post_time = base::GetTimestamp() / 1000;
      uint32 seed = static_cast<uint32>(base::GetTimestamp() % kInt32Max);
      std::vector<int64> context_pre_rank_pids_100;
      std::vector<int64> context_pre_rank_aids_100;
      std::vector<int64> context_pre_rank_duration_100;
      std::vector<int64> context_pre_rank_tagsone_100;
      std::vector<int64> context_pre_rank_tagstwo_100;
      std::vector<int64> context_pre_rank_timegap_100;
      context_pre_rank_pids_100.clear();
      context_pre_rank_aids_100.clear();
      context_pre_rank_duration_100.clear();
      context_pre_rank_tagsone_100.clear();
      context_pre_rank_tagstwo_100.clear();
      context_pre_rank_timegap_100.clear();

      context_pre_rank_pids_100.reserve(sample_size);
      context_pre_rank_aids_100.reserve(sample_size);
      context_pre_rank_duration_100.reserve(sample_size);
      context_pre_rank_tagsone_100.reserve(sample_size);
      context_pre_rank_tagstwo_100.reserve(sample_size);
      context_pre_rank_timegap_100.reserve(sample_size);

      int32 index = 0;

      for (auto it = begin; it != end; it++, ++index) {
        uint64 pid_ = it->GetId();
        uint64 aid_ = author_id_getter(*it).value_or(0);
        uint64 duration_ms_ = duration_ms_getter(*it).value_or(0) / 1000;
        uint64 timestamp_ = timestamp_getter(*it).value_or(0) / 1000;
        uint64 timegap_ = (post_time - timestamp_) / 1000 / 3600 / 24;
        absl::optional<absl::Span<const int64>> hetuone_list =
          hetuone_getter(*it).value_or(std::vector<int64>());
        absl::optional<absl::Span<const int64>> hetutwo_list =
          hetutwo_getter(*it).value_or(std::vector<int64>());
        uint64 hetuone_ =
          hetuone_list.has_value() ?
          hetuone_list.value().size() > 0 ?
          hetuone_list.value()[0] : 0 : 0;
        uint64 hetutwo_ =
          hetutwo_list.has_value() ?
          hetutwo_list.value().size() > 0 ?
          hetutwo_list.value()[0] : 0 : 0;
        if (index < sample_size) {
          context_pre_rank_pids_100.emplace_back(pid_);
          context_pre_rank_aids_100.emplace_back(aid_);
          context_pre_rank_duration_100.emplace_back(duration_ms_);
          context_pre_rank_tagsone_100.emplace_back(hetuone_);
          context_pre_rank_tagstwo_100.emplace_back(hetutwo_);
          context_pre_rank_timegap_100.emplace_back(timegap_);
        } else {
          uint32 r = rand_r(&seed) % (index + 1);
          if (r < static_cast<uint32>(sample_size)) {
            context_pre_rank_pids_100[r] = pid_;
            context_pre_rank_aids_100[r] = aid_;
            context_pre_rank_duration_100[r] = duration_ms_;
            context_pre_rank_tagsone_100[r] = hetuone_;
            context_pre_rank_tagstwo_100[r] = hetutwo_;
            context_pre_rank_timegap_100[r] = timegap_;
          }
        }
      }

      context.SetIntListCommonAttr("context_pre_rank_pids_100", std::move(context_pre_rank_pids_100));
      context.SetIntListCommonAttr("context_pre_rank_aids_100", std::move(context_pre_rank_aids_100));
      context.SetIntListCommonAttr("context_pre_rank_duration_100", std::move(context_pre_rank_duration_100));
      context.SetIntListCommonAttr("context_pre_rank_tagsone_100", std::move(context_pre_rank_tagsone_100));
      context.SetIntListCommonAttr("context_pre_rank_tagstwo_100", std::move(context_pre_rank_tagstwo_100));
      context.SetIntListCommonAttr("context_pre_rank_timegap_100", std::move(context_pre_rank_timegap_100));
      return true;
    }

  static bool CheckMixCriticAction(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    auto item_type_getter = context.GetIntItemAttr("item_type");
    int photo_size = 0;
    int live_size = 0;
    for (auto it = begin; it != end; it++) {
      if (item_type_getter(*it).value_or(0) == follow::LIVE_TYPE) {
        live_size++;
      } else {
        photo_size++;
      }
    }
    double mix_critic_actions = context.GetDoubleCommonAttr("mix_critic_actions").value_or(0);
    int64 mix_critic_code = static_cast<int64>(mix_critic_actions + 1e-3);
    int64 mix_seq_length = context.GetIntCommonAttr("follow_mix_rank_seq_length").value_or(12);
    int photo_count = 0;
    int live_count = 0;
    int check_code = 0;
    for (int i = mix_seq_length - 1; i >= 0; i--) {
      int bit_type = mix_critic_code & (1 << i);
      if (bit_type == 0 && photo_count < photo_size) {  // photo
        check_code <<= 1;
        photo_count++;
      }
      if (bit_type > 0 && live_count < live_size) {  // live
        check_code <<= 1;
        check_code |= 1;
        live_count++;
      }
    }
    // 补满
    int fill_size = mix_seq_length - photo_count - live_count;
    if (fill_size > 0) {
      if (photo_size > photo_count) {
        int fill_num = std::min(fill_size, photo_size - photo_count);
        check_code <<= fill_num;
      }
      if (live_size > live_count) {
        int fill_num = std::min(fill_size, live_size - live_count);
        check_code <<= fill_num;
        check_code |= (1 << fill_num) - 1;
      }
    }
    context.SetIntCommonAttr("mix_seq_model_final_key", check_code);
    if (check_code == mix_critic_code) {
      base::perfutil::PerfUtilWrapper::CountLogStash(ks::platform::follow::kPerfNsFollow, "mix.seq.model",
                                                     GlobalHolder::GetServiceIdentifier(), "code_same");
    } else {
      base::perfutil::PerfUtilWrapper::CountLogStash(ks::platform::follow::kPerfNsFollow, "mix.seq.model",
                                                     GlobalHolder::GetServiceIdentifier(), "code_diff");
    }
    return true;
  }

  static bool JudgeUseRecallCache(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    const auto *message = context.GetPtrCommonAttr<google::protobuf::Message>("follow_cache");
    const auto* follow_cache = dynamic_cast<const ks::reco::FollowCache *>(message);
    if (follow_cache == nullptr) {
      return true;
    }
    int64 time_gap = base::GetTimestamp() / 1000 - follow_cache->timestamp();
    int item_size = follow_cache->item_size();

    int max_time_gap = context.GetIntCommonAttr("max_time_gap").value_or(0);
    // cache 超过某个时间不用
    if (time_gap > max_time_gap) {
      return true;
    }

    int min_item_size = context.GetIntCommonAttr("min_item_size").value_or(5000);
    // size 小于某个阈值不用
    if (item_size < min_item_size) {
      return true;
    }

    context.SetIntCommonAttr("use_redis_cache", 1);
    return true;
  }

  static bool JudgePhotoGifStrategy(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    const auto *message = context.GetPtrCommonAttr<google::protobuf::Message>("user_info_pb");
    if (!message) {
      return true;
    }
    const auto *user_info = dynamic_cast<const ks::reco::RealTimeFollowRecoUserInfo *>(message);
    if (!user_info) {
      return true;
    }
    bool user_with_no_play_flag =
        user_info->current_play_cnt() <= context.GetIntCommonAttr("zero_play_cnt_low").value_or(0);
    bool filter_down_fresh = !(user_info->api_request_info().is_down_fresh());
    int today_top_request_count = 0;
    if (user_info->user_recent_status_size() > 0) {
      today_top_request_count =
          user_info->user_recent_status()[user_info->user_recent_status_size() - 1].top_fresh_count();
    }
    bool is_photo_gif_user =
        user_with_no_play_flag &&
        today_top_request_count >=
            context.GetIntCommonAttr("photo_gif_today_top_fresh_threshold").value_or(0) &&
        filter_down_fresh;
    auto product_type = user_info->follow_page_product_type();
    bool is_nebula_user = product_type == ks::reco::RealTimeFollowRecoUserInfo::TWO_COLUMN_KUAISHOU_NEBULA;

    if (is_photo_gif_user && !is_nebula_user) {
      context.SetIntCommonAttr("photo_gif_strategy", 1);
    }
    return true;
  }

  static bool CalLiveAutoPlayWeight(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto item_type_getter = context.GetIntItemAttr("item_type");
    auto ctr_getter = context.GetDoubleItemAttr("fr_pctr");
    auto ltr_getter = context.GetDoubleItemAttr("fr_pltr");
    auto lvtr_getter = context.GetDoubleItemAttr("fr_plvtr");
    auto watch_time_getter = context.GetDoubleItemAttr("fr_pwatch_time");
    double photo_gif_bias = context.GetDoubleCommonAttr("photo_gif_bias").value_or(0.0);
    double live_gif_bias = context.GetDoubleCommonAttr("live_gif_bias").value_or(0.0);
    double pos_weight = context.GetDoubleCommonAttr("weight_photo_dynamic_cover_by_position").value_or(0.0);
    double ctr_weight = context.GetDoubleCommonAttr("weight_photo_dynamic_cover_by_ctr").value_or(0.0);
    double ltr_weight = context.GetDoubleCommonAttr("weight_photo_dynamic_cover_by_ltr").value_or(0.0);
    double lvtr_weight = context.GetDoubleCommonAttr("weight_photo_dynamic_cover_by_lvtr").value_or(0.0);
    double watch_time_weight =
        context.GetDoubleCommonAttr("weight_photo_dynamic_cover_by_watchtime").value_or(0.0);
    auto live_auto_play_weight_setter = context.SetDoubleItemAttr("live_auto_play_weight");
    int index = 0;
    for (auto it = begin; it != end; it++, index++) {
      double position_score = 1.0 / (index + 1.0);
      double ctr_score = ctr_getter(*it).value_or(0.0);
      double ltr_score = ltr_getter(*it).value_or(0.0);
      double lvtr_score = lvtr_getter(*it).value_or(0.0);
      double watch_time_score = watch_time_getter(*it).value_or(0.0);
      double gif_bias = 0.0;
      int64 item_type = item_type_getter(*it).value_or(0);
      if (item_type == follow::LIVE_TYPE) {
        gif_bias = live_gif_bias;
      }
      if (item_type == follow::PHOTO_TYPE) {
        gif_bias = photo_gif_bias;
      }
      double score = position_score * pos_weight + ctr_score * ctr_weight + ltr_score * ltr_weight +
                     lvtr_score * lvtr_weight + watch_time_score * watch_time_weight + gif_bias;
      live_auto_play_weight_setter(*it, score);
    }
    return true;
  }

  static bool JudgeIsRealshow(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                              RecoResultConstIter end) {
    const auto *message = context.GetPtrCommonAttr<google::protobuf::Message>("user_info_pb");
    if (!message) {
      return true;
    }
    const auto *user_info = dynamic_cast<const ks::reco::RealTimeFollowRecoUserInfo *>(message);
    if (!user_info) {
      return true;
    }
    int64 user_id = context.GetIntCommonAttr("user_id").value_or(0);
    bool enable_fix_browsed_set = context.GetIntCommonAttr("enable_fix_browsed_set").value_or(0) == 1;
    std::unordered_set<int64> browsed_set;
    DuplicateHash<bool> browsed_set_old(1 << 16);
    if (enable_fix_browsed_set) {
      for (int i = 0; i < user_info->global_show_browsed_photo_ids_size(); ++i) {
        browsed_set.insert(user_info->global_show_browsed_photo_ids(i));
      }
    } else {
      for (int i = 0; i < user_info->global_show_browsed_photo_ids_size(); ++i) {
        browsed_set_old.Insert(user_info->global_show_browsed_photo_ids(i), true);
      }
    }
    std::unordered_set<uint64> last_feed_showed_livestream_set;
    for (int i = 0; i < user_info->feed_list_size(); ++i) {
      auto &feed = user_info->feed_list(i);
      if (feed.is_realshow() && feed.type() == ks::reco::RecoEnum::ITEM_TYPE_LIVESTREAM) {
        uint64 key_sign = ks::reco::ItemMemoryInfo::GenKeysign(
            feed.type(), ks::reco::RecoEnum::PAGE_TYPE_FOLLOW, feed.id());
        last_feed_showed_livestream_set.insert(key_sign);
      }
    }
    auto item_type_getter = context.GetIntItemAttr("item_type");
    auto author_id_getter = context.GetIntItemAttr("author_id");
    auto is_realshow_setter = context.SetIntItemAttr("is_realshow");
    auto exp_tag_setter = context.SetIntItemAttr("exp_tag");
    for (auto it = begin; it != end; it++) {
      int64 item_type = item_type_getter(*it).value_or(0);
      if (item_type == follow::LIVE_TYPE) {
        uint64 key_sign = ks::reco::ItemMemoryInfo::GenKeysign(
            ks::reco::RecoEnum::ITEM_TYPE_LIVESTREAM, ks::reco::RecoEnum::PAGE_TYPE_FOLLOW, it->GetId());
        if (last_feed_showed_livestream_set.count(key_sign) > 0) {
          is_realshow_setter(*it, 1);
        } else {
          is_realshow_setter(*it, 0);
        }
      } else if (item_type == follow::PHOTO_TYPE) {
        if (enable_fix_browsed_set) {
          if (browsed_set.count(it->GetId()) > 0) {
            is_realshow_setter(*it, 1);
          } else {
            is_realshow_setter(*it, 0);
          }
        } else {
          if (browsed_set_old.Lookup(it->GetId()) >= 0) {
            is_realshow_setter(*it, 1);
          } else {
            is_realshow_setter(*it, 0);
          }
        }
      } else {
        is_realshow_setter(*it, 0);
      }
      if (author_id_getter(*it).value_or(0) == user_id) {
        exp_tag_setter(*it, reco::follow::RecoFollowExpTag::FOLLOW_SELF_REASON);  // 85
      }
    }
    return true;
  }

  static bool BoostItemScore(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                             RecoResultConstIter end) {
    double boost_value = context.GetDoubleCommonAttr("boost_value").value_or(1.0);
    auto origin_score_getter = context.GetDoubleItemAttr("origin_score");
    auto output_score_setter = context.SetDoubleItemAttr("output_score");
    for (auto it = begin; it != end; it++) {
      double origin_score = origin_score_getter(*it).value_or(0.0);
      double output_score = origin_score * boost_value;
      output_score_setter(*it, output_score);
    }
    return true;
  }

  static bool IsStringUint64VectorMapEmpty(const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
    auto distribution_map = context.GetPtrCommonAttr<
      const folly::F14FastMap<std::string, std::vector<uint64>>>("string_uint64_vector_map");
    if (!distribution_map) {
      context.SetIntCommonAttr("is_value_empty", 1);
      return true;
    }
    if (distribution_map->empty()) {
      context.SetIntCommonAttr("is_value_empty", 1);
    }
    return true;
  }

  static bool JudgeIsRecruitPhoto(const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
    const auto *message = context.GetPtrCommonAttr<google::protobuf::Message>("user_info_pb");
    if (!message) {
      return false;
    }
    const auto *user_info = dynamic_cast<const ks::reco::RealTimeFollowRecoUserInfo *>(message);
    if (!user_info) {
      return false;
    }

    auto current_time = std::chrono::system_clock::now();
    auto current_time_seconds = std::chrono::duration_cast<std::chrono::seconds>(
      current_time.time_since_epoch()).count();

    folly::F14FastMap<uint64, uint64> u_item_last_show_time_7d_map;
    for (const auto& browse_photo_info : user_info->browse_photo_info_v2()) {
      u_item_last_show_time_7d_map[browse_photo_info.liveid()] = browse_photo_info.last_show_time();
    }
    auto item_show_time_gap_thresh_ = context.GetIntCommonAttr("item_show_time_gap_thresh").value_or(0);
    auto plc_business_type_getter = context.GetIntItemAttr("plc_business_type");
    auto is_recruit_photo_setter = context.SetIntItemAttr("is_recruit_photo");
    for (auto it = begin; it != end; it++) {
      int64 plc_business_type = plc_business_type_getter(*it).value_or(0);
      if (plc_business_type != 44 && plc_business_type != 73) {
        continue;
      }
      // 低于当前时间阈值内 停止重复打分
      auto iter = u_item_last_show_time_7d_map.find(it->GetId());
      if (iter != u_item_last_show_time_7d_map.end()) {
        uint64 item_time_seconds = iter->second;
        // 计算时间差（秒）
        uint64 time_difference = current_time_seconds * 1000 - item_time_seconds;
        uint64 photo_id_show_time_gap = time_difference / 1000;
        if (photo_id_show_time_gap > 0 && photo_id_show_time_gap <= item_show_time_gap_thresh_) {
          continue;
        }
      }
      is_recruit_photo_setter(*it, 1);
    }

    return true;
  }

  static bool CalcRecruitPhotoScore(const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
    // 分人群校准开关
    bool enable_recruit_photo_cvr_calibration = context.GetIntCommonAttr(
      "enable_recruit_photo_cvr_calibration").value_or(0) == 1;
    bool is_deliver_user = false;
    // 分人群校准系数 1
    double recruit_photo_cvr_calibration_coeff1 = 1.0;
    // 分人群校准系数 2
    double recruit_photo_cvr_calibration_coeff2 = 1.0;
    if (enable_recruit_photo_cvr_calibration) {
      auto recruit_deliver_type_list = context.GetIntListCommonAttr("uPropertyUserCluster");
      if (recruit_deliver_type_list) {
        for (auto id : *recruit_deliver_type_list) {
          if (id == 102) {
            is_deliver_user = true;
            break;
          }
        }
      }
      recruit_photo_cvr_calibration_coeff1 = context.GetDoubleCommonAttr(
        "recruit_photo_follow_page_cvr_calibration_coeff1").value_or(1.0);
      recruit_photo_cvr_calibration_coeff2 = context.GetDoubleCommonAttr(
        "recruit_photo_follow_page_cvr_calibration_coeff2").value_or(1.0);
    }
    double recruit_photo_follow_v5_page_gpm_coef = context.GetDoubleCommonAttr(
        "recruit_photo_follow_v5_page_gpm_coef").value_or(1.0);
    auto plc_cvr_getter = context.GetDoubleItemAttr("recruit_plc_cvr");
    auto is_recruit_photo_setter = context.SetIntItemAttr("is_recruit_photo");
    auto recruit_photo_score_setter = context.SetDoubleItemAttr("recruit_photo_score");
    auto recruit_photo_gpm_setter = context.SetDoubleItemAttr("recruit_photo_gpm");
    for (auto it = begin; it != end; it++) {
      auto plc_cvr = plc_cvr_getter(*it).value_or(0.0);
      if (std::fabs(plc_cvr) < 1e-8) {
        is_recruit_photo_setter(*it, 0);
        continue;
      }
      if (enable_recruit_photo_cvr_calibration) {
        plc_cvr *= recruit_photo_cvr_calibration_coeff1;
        if (is_deliver_user) {
          plc_cvr *= recruit_photo_cvr_calibration_coeff2;
        }
      }
      recruit_photo_score_setter(*it, plc_cvr);
      recruit_photo_gpm_setter(*it, plc_cvr * recruit_photo_follow_v5_page_gpm_coef);
    }
    return true;
  }

  static bool JudgeIsRecruitLive(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    const auto *message = context.GetPtrCommonAttr<google::protobuf::Message>("user_info_pb");
    if (!message) {
      return false;
    }
    const auto *user_info = dynamic_cast<const ks::reco::RealTimeFollowRecoUserInfo *>(message);
    if (!user_info) {
      return false;
    }

    auto current_time = std::chrono::system_clock::now();
    auto current_time_seconds =
        std::chrono::duration_cast<std::chrono::seconds>(current_time.time_since_epoch()).count();
    // 最近用户浏览物料集合
    folly::F14FastMap<uint64, uint64> ua_live_last_show_ts_map;
    for (const auto &browse_live_info : user_info->browse_author_live_info_v2()) {
      ua_live_last_show_ts_map[browse_live_info.author_id()] = browse_live_info.last_show_timestamp();
    }
    auto item_show_time_gap_thresh_ = context.GetIntCommonAttr("item_show_time_gap_thresh").value_or(0);
    auto data_set_tag_new_list_getter = context.GetIntListItemAttr("data_set_tag_new_list");
    auto author_id_getter = context.GetIntItemAttr("author_id");
    auto is_recruit_live_setter = context.SetIntItemAttr("is_recruit_live");
    for (auto it = begin; it != end; it++) {
      bool is_recruit_live = false;
      auto data_set_tag_new = data_set_tag_new_list_getter(*it);
      if (data_set_tag_new) {
        is_recruit_live = std::any_of(data_set_tag_new->begin(), data_set_tag_new->end(),
                                      [](const int &tag) { return tag == 73; });
      }
      if (!is_recruit_live) {
        continue;
      }
      // 低于当前时间阈值内 停止重复打分
      int64 author_id = author_id_getter(*it).value_or(0);
      auto iter = ua_live_last_show_ts_map.find(author_id);
      if (iter != ua_live_last_show_ts_map.end()) {
        uint64 item_time_seconds = iter->second;
        // 计算时间差（秒）
        uint64 time_difference = current_time_seconds * 1000 - item_time_seconds;
        uint64 live_id_show_time_gap = time_difference / 1000;
        if (live_id_show_time_gap > 0 && live_id_show_time_gap <= item_show_time_gap_thresh_) {
          continue;
        }
      }
      is_recruit_live_setter(*it, 1);
    }

    return true;
  }


  static bool DecodeDurationWtd(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
    static double duration_table[15][50] = {
      {0, 715, 837, 917, 981, 1041, 1096, 1151, 1200, 1249, 1301, 1352, 1403, 1458, 1514, 1572, 1632,
        1695, 1763, 1837, 1919, 2002, 2100, 2204, 2318, 2447, 2593, 2757, 2943, 3153, 3401, 3671, 3977,
        4313, 4707, 5152, 5662, 6260, 6939, 7696, 8617, 9693, 11065, 12690, 14799, 17630, 21404, 27093,
        36893, 58831},
      {0, 641, 782, 868, 935, 992, 1043, 1101, 1153, 1224, 1279, 1327, 1388, 1446, 1513, 1581, 1668,
        1740, 1854, 1947, 2029, 2151, 2278, 2408, 2545, 2733, 2936, 3089, 3330, 3538, 3767, 3997, 4191,
        4398, 4621, 4863, 5057, 5229, 5394, 5573, 5747, 5891, 6058, 6539, 7674, 8996, 10630, 12835,
        15409, 17800},
      {0, 728, 870, 972, 1061, 1146, 1227, 1312, 1403, 1497, 1596, 1710, 1832, 1964, 2116, 2298, 2496,
        2722, 2986, 3268, 3592, 3933, 4298, 4689, 5107, 5553, 6023, 6453, 6873, 7141, 7330, 7525, 7736,
        7932, 8114, 8310, 8521, 8719, 8930, 9113, 9309, 9544, 9775, 10007, 10819, 12656, 15232, 17934,
        21619, 26870},
      {0, 744, 890, 992, 1085, 1170, 1259, 1348, 1441, 1539, 1647, 1765, 1895, 2036, 2195, 2374, 2576,
        2800, 3055, 3354, 3692, 4077, 4519, 5025, 5566, 6160, 6822, 7514, 8240, 8986, 9745, 10231, 10549,
        10875, 11167, 11455, 11599, 11848, 12160, 12520, 12879, 13249, 13613, 14025, 14438, 14881, 15998,
        19490, 24352, 32223},
      {0, 755, 906, 1015, 1109, 1200, 1291, 1388, 1492, 1602, 1721, 1855, 2003, 2168, 2359, 2574, 2818,
        3092, 3413, 3786, 4214, 4699, 5272, 5904, 6615, 7412, 8259, 9184, 10186, 11274, 12399, 13562, 14733,
        15194, 15472, 15761, 16074, 16405, 16745, 17069, 17415, 17759, 18131, 18527, 18958, 19407, 19864,
        21507, 27249, 36385},
      {0, 747, 896, 1005, 1100, 1190, 1280, 1377, 1476, 1582, 1701, 1832, 1973, 2130, 2311, 2516, 2742,
        3001, 3298, 3648, 4048, 4507, 5061, 5695, 6428, 7237, 8178, 9236, 10352, 11686, 13178, 14834,
        16594, 18435, 20093, 20709, 21299, 21922, 22569, 23221, 23907, 24634, 25362, 26086, 26888,
        27704, 28576, 29530, 32018, 43533},
      {0, 761, 914, 1023, 1121, 1213, 1305, 1405, 1513, 1623, 1744, 1883, 2039, 2214, 2412, 2632, 2890,
        3188, 3529, 3926, 4375, 4916, 5545, 6280, 7118, 8066, 9143, 10391, 11781, 13466, 15405, 17533,
        20047, 22752, 25580, 28463, 30332, 31086, 31797, 32539, 33307, 34117, 34910, 35688, 36432, 37235,
        38093, 38977, 39956, 48754},
      {0, 763, 914, 1028, 1125, 1223, 1320, 1422, 1534, 1652, 1781, 1929, 2101, 2285, 2500, 2747, 3016,
        3323, 3670, 4109, 4613, 5218, 5905, 6710, 7670, 8743, 9963, 11384, 13049, 15004, 17219, 19745,
        22505, 25594, 29088, 32773, 36442, 39778, 40786, 41549, 42367, 43189, 44051, 44935, 45756, 46708,
        47680, 48666, 49696, 55892},
      {0, 767, 913, 1021, 1116, 1206, 1298, 1397, 1501, 1612, 1737, 1869, 2021, 2196, 2383, 2601, 2838,
        3121, 3440, 3809, 4240, 4754, 5343, 6051, 6858, 7824, 8908, 10137, 11626, 13322, 15355, 17696,
        20344, 23425, 26966, 30882, 35089, 39665, 44636, 49233, 50884, 51933, 52916, 53934, 55005, 56117,
        57218, 58225, 59429, 62830},
      {0, 770, 923, 1037, 1135, 1230, 1327, 1429, 1540, 1654, 1792, 1940, 2102, 2290, 2501, 2737, 3004,
        3317, 3689, 4129, 4644, 5231, 5920, 6716, 7640, 8729, 9983, 11472, 13249, 15357, 17725, 20447,
        23591, 27236, 31240, 35560, 40306, 45501, 51088, 56676, 60127, 60934, 61814, 62835, 63865, 65032,
        66160, 67386, 68745, 70852},
      {0, 757, 911, 1023, 1121, 1214, 1310, 1412, 1520, 1637, 1763, 1909, 2070, 2253, 2456, 2693, 2960,
        3252, 3597, 4007, 4481, 5046, 5714, 6487, 7410, 8449, 9681, 11165, 12919, 14985, 17348, 20165,
        23431, 27231, 31487, 36167, 41254, 46925, 53260, 60320, 67394, 71112, 72971, 74798, 76835, 78999,
        81315, 83860, 86571, 89233},
      {0, 757, 911, 1021, 1112, 1206, 1298, 1397, 1500, 1613, 1736, 1872, 2024, 2195, 2390, 2600, 2846,
        3131, 3455, 3831, 4267, 4781, 5383, 6107, 6966, 7952, 9081, 10444, 12056, 14026, 16431, 19292,
        22670, 26749, 31299, 36518, 42400, 48944, 56191, 64121, 73267, 83667, 91120, 94060, 97134, 100504,
        103997, 107879, 112307, 117378},
      {0, 749, 903, 1011, 1106, 1194, 1288, 1378, 1477, 1583, 1703, 1834, 1974, 2137, 2311, 2509, 2736,
        2997, 3286, 3617, 4006, 4445, 4970, 5589, 6285, 7109, 8066, 9199, 10576, 12274, 14378, 16870,
        20050, 23779, 28198, 33363, 39562, 46677, 54876, 64587, 75687, 88374, 103527, 119654, 125828,
        132487, 140068, 148649, 158714, 170257},
      {0, 738, 895, 1012, 1107, 1198, 1279, 1378, 1479, 1582, 1692, 1813, 1950, 2103, 2265, 2457, 2661,
        2895, 3161, 3468, 3836, 4244, 4711, 5255, 5916, 6668, 7559, 8606, 9850, 11374, 13251, 15524, 18298,
        21799, 25960, 31147, 37397, 44958, 53957, 64626, 77951, 94156, 113909, 139161, 170097, 188833,
        202867, 219672, 239977, 265594},
      {0, 736, 888, 994, 1077, 1161, 1251, 1339, 1433, 1526, 1629, 1739, 1860, 2000, 2146, 2312, 2493,
        2699, 2921, 3178, 3474, 3801, 4197, 4651, 5170, 5781, 6495, 7326, 8255, 9372, 10666, 12229, 14168,
        16473, 19292, 22630, 26635, 31445, 37322, 44455, 53278, 63979, 78103, 96244, 120666, 154752,
        204075, 279553, 327963, 385486}
    };
    auto duration_ms_getter = context.GetIntItemAttr("duration_ms");
    auto duration_wtd_index_getter = context.GetDoubleItemAttr("duration_wtd_index");
    auto duration_wtd_setter = context.SetIntItemAttr("duration_wtd");
    for (auto it = begin; it != end; it++) {
      int64 duration_ms = duration_ms_getter(*it).value_or(0);
      double pred = duration_wtd_index_getter(*it).value_or(0.0);
      if ((pred <= 0.0) || (pred > 1.0)) {
        duration_wtd_setter(*it, 0.0);
        continue;
      }
      int photo_idx = 0;
      double photo_duration_s = static_cast<double> (duration_ms) / 1000.0;

      if (photo_duration_s == 0.0) {
        photo_idx = 0;
      } else if (photo_duration_s < 6.0) {
        photo_idx = 1;
      } else if (photo_duration_s < 10.0) {
        photo_idx = 2;
      } else if (photo_duration_s < 15.0) {
        photo_idx = 3;
      } else if (photo_duration_s < 20.0) {
        photo_idx = 4;
      } else if (photo_duration_s < 30.0) {
        photo_idx = 5;
      } else if (photo_duration_s < 40.0) {
        photo_idx = 6;
      } else if (photo_duration_s < 50.0) {
        photo_idx = 7;
      } else if (photo_duration_s < 60.0) {
        photo_idx = 8;
      } else if (photo_duration_s < 70.0) {
        photo_idx = 9;
      } else if (photo_duration_s < 90.0) {
        photo_idx = 10;
      } else if (photo_duration_s < 120.0) {
        photo_idx = 11;
      } else if (photo_duration_s < 180.0) {
        photo_idx = 12;
      } else if (photo_duration_s < 300.0) {
        photo_idx = 13;
      } else {
        photo_idx = 14;
      }
      int bucket_id = static_cast<int>(pred * 50.0);
      bucket_id = std::min(bucket_id, 49);
      int right_id = std::min(bucket_id + 1, 49);

      double left_bound = duration_table[photo_idx][bucket_id];
      double right_bound = duration_table[photo_idx][right_id];

      double value = left_bound + (
        pred * 50.0 - static_cast<double>(bucket_id)) * (right_bound - left_bound);
      duration_wtd_setter(*it, value);
    }
    return true;
  }

  static bool ParseKeyDoubleValueMap(
    const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    std::string key_value_map_str(context.GetStringCommonAttr(
      "key_value_map_str").value_or(""));
    if (key_value_map_str.empty()) {
      return true;
    }

    std::vector<std::string> datas;
    std::shared_ptr<folly::F14FastMap<uint64, double>> key_value_map_ptr(
      new folly::F14FastMap<uint64, double>);
    base::SplitStringWithOptions(key_value_map_str, ",", true, true, &datas);
    for (const auto& data : datas) {
      std::vector<std::string> aid_kv;
      uint64 id = 0;
      double score = 0;
      base::SplitStringWithOptions(data, "_", true, true, &aid_kv);
      if (aid_kv.size() >= 2 && base::StringToUint64(aid_kv.at(0), &id)
          && absl::SimpleAtod(aid_kv.at(1), &score)) {
        key_value_map_ptr->emplace(id, score);
      }
    }
    context.SetPtrCommonAttr("key_value_map_ptr", key_value_map_ptr);
    return true;
  }

  static bool GenShowPidList(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    const auto *message = context.GetPtrCommonAttr<google::protobuf::Message>("user_info_pb");
    if (!message) {
      return true;
    }
    const auto *user_info = dynamic_cast<const ks::reco::RealTimeFollowRecoUserInfo *>(message);
    if (!user_info) {
      return true;
    }
    int showed_photo_candsize = context.GetIntCommonAttr("showed_photo_cand_maxsize").value_or(100);

    int count_showed_photo = 0;
    int showed_index = user_info->browsed_photo_ids_in_session_size();
    std::vector<int64> request_photos;
    for (int i = showed_index - 1; i >= 0 ; --i) {
      auto pid = user_info->browsed_photo_ids_in_session(i);
      if (count_showed_photo >= showed_photo_candsize) {
        break;
      }
      count_showed_photo++;
      request_photos.push_back(pid);
    }
    context.SetIntListCommonAttr("show_pid_list", std::move(request_photos));
    return true;
  }

  static bool GenPhotoFrXtrBias(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    double like_ratio_30d = context.GetDoubleCommonAttr("sim_like_ratio_30d").value_or(0.0);
    double comment_ratio_30d = context.GetDoubleCommonAttr("sim_comment_ratio_30d").value_or(0.0);
    double pltr_boost_weight = context.GetDoubleCommonAttr(
      "pltr_boost_weight_photo_second_es").value_or(0.0);
    double pltr_deboost_weight = context.GetDoubleCommonAttr(
      "pltr_deboost_weight_photo_second_es").value_or(0.0);
    double pcmtr_boost_weight = context.GetDoubleCommonAttr(
      "pcmtr_boost_weight_photo_second_es").value_or(0.0);
    double pcmtr_deboost_weight = context.GetDoubleCommonAttr(
      "pcmtr_deboost_weight_photo_second_es").value_or(0.0);
    int play_cnt_30d = context.GetIntCommonAttr("sim_play_cnt_30d").value_or(0);

    auto fr_pltr_getter = context.GetDoubleItemAttr("fr_pltr");
    auto fr_pcmtr_getter = context.GetDoubleItemAttr("fr_pcmtr");
    auto bias_pltr_setter = context.SetDoubleItemAttr("bias_pltr");
    auto pltr_avg_diff_setter = context.SetDoubleItemAttr("pltr_avg_diff");
    auto bias_pcmtr_setter = context.SetDoubleItemAttr("bias_pcmtr");
    auto pcmtr_avg_diff_setter = context.SetDoubleItemAttr("pcmtr_avg_diff");
    for (auto it = begin; it != end; it++) {
      double fr_pltr = fr_pltr_getter(*it).value_or(0.0);
      double bias_pltr = fr_pltr > like_ratio_30d ?
        fr_pltr * pltr_boost_weight : fr_pltr * pltr_deboost_weight;
      if (play_cnt_30d < 100) {
        bias_pltr = fr_pltr;
      }
      bias_pltr_setter(*it, bias_pltr);
      pltr_avg_diff_setter(*it, fr_pltr - like_ratio_30d);

      double fr_pcmtr = fr_pcmtr_getter(*it).value_or(0.0);
      double bias_pcmtr = fr_pcmtr > comment_ratio_30d ?
        fr_pcmtr * pcmtr_boost_weight : fr_pcmtr * pcmtr_deboost_weight;
      if (play_cnt_30d < 100) {
        bias_pcmtr = fr_pcmtr;
      }
      bias_pcmtr_setter(*it, bias_pcmtr);
      pcmtr_avg_diff_setter(*it, fr_pcmtr - comment_ratio_30d);
    }
    return true;
  }

  static bool SerializeAdInfoStr(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto ad_dsp_getter = context.GetPtrItemAttr<mix::kuaishou::ad::DspAdInfo>("ad_dsp");
    auto ad_dsp_str_setter = context.SetStringItemAttr("ad_dsp_str");
    for (auto it = begin; it != end; it++) {
      auto* ad_dsp_ptr = ad_dsp_getter(*it);
      if (ad_dsp_ptr == nullptr) {
        continue;
      }
      std::string ad_dsp_str;
      ad_dsp_ptr->SerializeToString(&ad_dsp_str);
      ad_dsp_str_setter(*it, std::move(ad_dsp_str));
    }
    return true;
  }

  static bool SocialDataHitCache(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    std::string cache_ratio_str(
        context.GetStringCommonAttr("social_data_mix_model_rank_cache_ratio_str").value_or(""));
    int64 product_type = context.GetIntCommonAttr("product_type").value_or(0);
    int64 user_id = context.GetIntCommonAttr("uid").value_or(0);
    double cache_ratio = 1.0;
    struct tm now_time;
    std::time_t cur_time = std::time(0);
    if (localtime_r(&cur_time, &now_time) != nullptr) {
      auto tm_hour = now_time.tm_hour;
      std::vector<std::string> cache_ratios;
      base::SplitStringWithOptions(cache_ratio_str, ",", true, true, &cache_ratios);
      if (tm_hour < cache_ratios.size()) {
        if (!absl::SimpleAtod(cache_ratios[tm_hour], &cache_ratio)) {
          cache_ratio = 1.0;
        }
      }
    }
    int hit_cache = 0;
    uint32 seed = static_cast<uint32>(base::GetTimestamp() % kInt32Max);
    if ((double)(rand_r(&seed) % 10000) / 10000.0 < cache_ratio) {
      hit_cache = 1;
    }
    if (product_type == 28) {
      product_type = 26;
    }
    std::string redis_key = "fproduct" + std::to_string(product_type) + "_pxtr_" + std::to_string(user_id);
    context.SetIntCommonAttr("social_data_hit_cache", hit_cache);
    context.SetStringCommonAttr("social_data_redis_key", redis_key);
    return true;
  }

  static bool ParseConsumePreferType(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto value_str = std::string(context.GetStringCommonAttr("user_consume_type_str").value_or(""));
    base::Json json_data(base::StringToJson(value_str));
    auto consume_prefer_type_str = json_data.GetString("follow_consume_perference_type_14d", "");
    auto active_degree_30d_str = json_data.GetString("follow_user_active_degree_30d", "");
    uint32 consume_prefer_type = 0;
    if (consume_prefer_type_str == "社交") {
      consume_prefer_type = 1;
    } else if (consume_prefer_type_str == "视频") {
      consume_prefer_type = 2;
    } else if (consume_prefer_type_str == "直播") {
      consume_prefer_type = 3;
    } else if (consume_prefer_type_str == "综合") {
      consume_prefer_type = 4;
    } else {
      consume_prefer_type = 0;
    }
    uint32 active_degree_30d = 0;
    if (active_degree_30d_str == "low_active" || (active_degree_30d_str == "single_low_active")) {
      active_degree_30d = 1;
    } else if (active_degree_30d_str == "full_active") {
      active_degree_30d = 2;
    } else if (active_degree_30d_str == "high_active") {
      active_degree_30d = 3;
    } else if (active_degree_30d_str == "middle_active") {
      active_degree_30d = 4;
    } else {
      active_degree_30d = 0;
    }
    context.SetIntCommonAttr("uConsumePreferType", consume_prefer_type);
    context.SetIntCommonAttr("uFollowActiveDegree30D", active_degree_30d);
    return true;
  }

  static bool ParseTopBarVVStr(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto value_str = std::string(context.GetStringCommonAttr("top_bar_vv_str").value_or(""));
    std::vector<std::string> datas;
    base::SplitStringWithOptions(value_str, ",", true, true, &datas);
    bool valid = false;
    int64 top_bar_vv = 0;
    int64 total_vv = 0;
    if (datas.size() >= 2) {
      if (base::StringToInt64(datas[0], &top_bar_vv) && base::StringToInt64(datas[1], &total_vv)) {
        valid = true;
      }
    }
    if (!valid || total_vv < top_bar_vv || top_bar_vv <= 0) {
      top_bar_vv = 0;
      total_vv = 0;
    }
    context.SetIntCommonAttr("uTopBarLiveView7d", top_bar_vv);
    context.SetIntCommonAttr("uSlideLiveView7d", total_vv - top_bar_vv);
    return true;
  }

  static bool GenLastAdPos(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    const auto *message = context.GetPtrCommonAttr<google::protobuf::Message>("user_info_pb");
    if (!message) {
      return true;
    }
    const auto *user_info = dynamic_cast<const ks::reco::RealTimeFollowRecoUserInfo *>(message);
    if (!user_info) {
      return true;
    }
    int last_ad_pos = -1000;
    int ad_pos = 1;
    bool find_end = false;
    bool find = false;
    static folly::F14FastSet<uint32> ad_reasons { 5, 53, 512, 517, 518 };
    uint64 entry_feed_id = 0;
    uint64 current_ts = base::GetTimestamp();
    if (user_info->has_slide_mode_context_info()) {
      entry_feed_id = user_info->slide_mode_context_info().entry_feed_id();
    }
    for (int i = user_info->recent_inner_session_follow_reco_result_size() - 1; i >= 0; --i) {
      if (find_end) break;
      if (find) break;
      auto &reco_result = user_info->recent_inner_session_follow_reco_result(i);
      uint64 cur_entry_feed_id = 0;
      if (reco_result.has_slide_mode_context_info()) {
        cur_entry_feed_id = reco_result.slide_mode_context_info().entry_feed_id();
        if (cur_entry_feed_id == entry_feed_id && reco_result.slide_mode_context_info().cursor_index() == 0) {
          find_end = true;
        }
      }
      if (reco_result.create_time() <= 0 || cur_entry_feed_id != entry_feed_id
        || (current_ts - reco_result.create_time() * 1000L) / base::Time::kMicrosecondsPerHour > 24) {
        continue;
      }
      for (int j = reco_result.myfollow_reco_feed_size() - 1; j >= 0; --j) {
        auto& feed = reco_result.myfollow_reco_feed(j);
        if (ad_reasons.count(feed.reason()) > 0) {
          find = true;
          break;
        } else {
          ad_pos++;
        }
      }
    }
    if (find) {
      last_ad_pos = -ad_pos;
    }
    context.SetIntCommonAttr("last_ad_pos", last_ad_pos);
    return true;
  }

  static bool GenLastAdPosOutSide(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    const auto *message = context.GetPtrCommonAttr<google::protobuf::Message>("user_info_pb");
    if (!message) {
      return true;
    }
    const auto *user_info = dynamic_cast<const ks::reco::RealTimeFollowRecoUserInfo *>(message);
    if (!user_info) {
      return true;
    }
    bool find = false;
    int ad_pos = 1;
    int last_ad_pos = -1000;
    static folly::F14FastSet<uint32> ad_reasons { 5, 53, 512, 517, 518 };
    if (user_info->has_api_request_info()) {
      auto &api_request_info = user_info->api_request_info();
      if (api_request_info.is_down_fresh()) {
        int last_feed_list_cursor = api_request_info.memcache_cursor();
        int index = std::min(last_feed_list_cursor, user_info->feed_list_size()) - 1;
        for (int i = index; i >= 0; i--) {
          auto &feed = user_info->feed_list(i);
          if (ad_reasons.count(feed.reason()) > 0) {
            find = true;
            break;
          } else {
            ad_pos++;
          }
        }
      }
    }
    if (find) {
      last_ad_pos = -ad_pos;
    }
    context.SetIntCommonAttr("last_ad_pos", last_ad_pos);
    return true;
  }

  static bool GenIndexItemKey(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    uint64 item_type = context.GetIntCommonAttr("index_item_type").value_or(0);
    auto item_id_accessor = context.GetIntItemAttr("index_item_id");
    auto item_key_accessor = context.SetIntItemAttr("index_item_key");
    for (auto it = begin; it != end; it++) {
      uint64 item_id = item_id_accessor(*it).value_or(0);
      uint64 item_key = (item_type << 56) | item_id;
      item_key_accessor(*it, item_key);
    }
    return true;
  }

  static bool EndAdRetrieval(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                             RecoResultConstIter end) {
    auto *waiter = const_cast<ks::kess::rpc::BatchWaiter *>(
        context.GetPtrCommonAttr<ks::kess::rpc::BatchWaiter>("ad_athena_batch_waiter"));
    if (waiter) {
      waiter->Wait();
    }
    return true;
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(FollowLeafLightFunctionSet);
};

}  // namespace platform
}  // namespace ks
