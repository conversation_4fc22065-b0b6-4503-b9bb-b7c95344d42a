#include "dragon/src/processor/ext/follow_leaf/retriever/follow_pack_results_retriever.h"

#include <algorithm>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "dragon/src/processor/ext/follow_leaf/context/context.h"

namespace ks {
namespace platform {

void FollowPackResultsRetriever::Retrieve(AddibleRecoContextInterface *context) {
  // pack 序列之外的结果到主 table
  auto *result_key_reason_map =
      context->GetMutablePtrCommonAttr<std::unordered_map<int64, int>>("results_key_reason_map_ptr");
  auto *common_reco_context = static_cast<CommonRecoContext *>(context);
  auto *results = common_reco_context->GetRecoResults();
  std::unordered_set<int64> seq_result_key_set;
  for (auto &res : (*results)) {
    seq_result_key_set.insert(res.ItemKey());
  }

  auto *photo_table = context->GetOrInsertDataTable(photo_table_name_);
  auto *photo_results = photo_table->GetRecoResults();
  auto *live_table = context->GetOrInsertDataTable(live_table_name_);
  auto *live_results = live_table->GetRecoResults();

  // 合入剩余 es 结果
  for (auto &res : (*live_results)) {
    if (seq_result_key_set.count(res.ItemKey()) <= 0) {
      common_reco_context->AddCommonRecoResult(res.ItemKey(), res.Reason());
      seq_result_key_set.insert(res.ItemKey());
    }
  }
  for (auto &res : (*photo_results)) {
    if (seq_result_key_set.count(res.ItemKey()) <= 0) {
      common_reco_context->AddCommonRecoResult(res.ItemKey(), res.Reason());
      seq_result_key_set.insert(res.ItemKey());
    }
  }
  // 合入主表剩余结果
  if (result_key_reason_map == nullptr) {
    return;
  }
  for (auto &pair : (*result_key_reason_map)) {
    if (seq_result_key_set.count(pair.first) <= 0) {
      common_reco_context->AddCommonRecoResult(pair.first, pair.second);
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, FollowPackResultsRetriever, FollowPackResultsRetriever);
}  // namespace platform
}  // namespace ks
