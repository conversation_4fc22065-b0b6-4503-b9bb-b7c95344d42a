#include "dragon/src/processor/ext/follow_leaf/retriever/follow_mix_select_final_seq_retriever.h"

#include <algorithm>
#include <memory>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "dragon/src/processor/ext/follow_leaf/context/context.h"

namespace ks {
namespace platform {

void FollowMixSelectFinalSeqRetriever::Retrieve(AddibleRecoContextInterface *context) {
  auto *seq_results_table = context->GetOrInsertDataTable(seq_results_table_name_);
  auto *seq_results = seq_results_table->GetRecoResults();
  if (seq_results == nullptr || seq_results->empty()) {
    return;
  }

  // 取第一个序列
  CommonRecoResult seq_result = seq_results->at(0);
  auto seq_item_list_accessor = seq_results_table->GetOrInsertAttr("item_list");
  auto seq_item_list = context->GetIntListItemAttr(seq_result, seq_item_list_accessor);
  // 模型生效时, 使用模型结果
  if (context->GetIntCommonAttr("enable_follow_mix_model").value_or(0) == 1) {
    auto model_seq_key = context->GetIntCommonAttr("mix_seq_model_final_key");
    if (model_seq_key) {
      uint64 seq_key = *model_seq_key;
      seq_item_list = context->GetIntListItemAttr(seq_key, seq_item_list_accessor);
      if (seq_key == seq_result.ItemKey()) {
        base::perfutil::PerfUtilWrapper::CountLogStash(ks::platform::follow::kPerfNsFollow, "mix.seq.model",
                                                       GlobalHolder::GetServiceIdentifier(),
                                                       "final_retrieval_same");
      } else {
        base::perfutil::PerfUtilWrapper::CountLogStash(ks::platform::follow::kPerfNsFollow, "mix.seq.model",
                                                       GlobalHolder::GetServiceIdentifier(),
                                                       "final_retrieval_diff");
      }
    }
  }
  if (!seq_item_list || seq_item_list->size() == 0) {
    return;
  }

  // 序列结果同步到 dragon 的主 table
  auto *common_reco_context = static_cast<CommonRecoContext *>(context);
  auto *results = common_reco_context->GetRecoResults();
  auto result_key_reason_map = std::make_shared<std::unordered_map<int64, int>>();
  for (auto &res : (*results)) {
    (*result_key_reason_map)[res.ItemKey()] = res.Reason();
  }
  results->clear();

  for (auto item_key : (*seq_item_list)) {
    int32 reason = (*result_key_reason_map)[item_key];
    common_reco_context->AddCommonRecoResult(item_key, reason);
  }
  context->SetPtrCommonAttr("results_key_reason_map_ptr", result_key_reason_map);
  return;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, FollowMixSelectFinalSeqRetriever, FollowMixSelectFinalSeqRetriever);
}  // namespace platform
}  // namespace ks
