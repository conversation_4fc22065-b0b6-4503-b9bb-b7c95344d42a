#pragma once

#include <string>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_retriever.h"

namespace ks {
namespace platform {

class FollowPackResultsRetriever : public CommonRecoBaseRetriever {
 public:
  FollowPackResultsRetriever() {}

  void Retrieve(AddibleRecoContextInterface *context) override;

 private:
  bool InitProcessor() override {
    photo_table_name_ = config()->GetString("photo_table", "photo_mix_table");
    if (photo_table_name_.empty()) {
      LOG(ERROR) << "FollowPackResultsRetriever init with empty photo_table";
      return false;
    }
    live_table_name_ = config()->GetString("live_table", "live_mix_table");
    if (live_table_name_.empty()) {
      LOG(ERROR) << "FollowPackResultsRetriever init with empty live_table";
      return false;
    }
    return true;
  }

 private:
  std::string photo_table_name_;
  std::string live_table_name_;

  DISALLOW_COPY_AND_ASSIGN(FollowPackResultsRetriever);
};

}  // namespace platform
}  // namespace ks
