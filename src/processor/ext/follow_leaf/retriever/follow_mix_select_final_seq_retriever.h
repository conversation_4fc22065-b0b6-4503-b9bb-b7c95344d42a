#pragma once

#include <string>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_retriever.h"

namespace ks {
namespace platform {

class FollowMixSelectFinalSeqRetriever : public CommonRecoBaseRetriever {
 public:
  FollowMixSelectFinalSeqRetriever() {}

  void Retrieve(AddibleRecoContextInterface *context) override;

 private:
  bool InitProcessor() override {
    seq_results_table_name_ = config()->GetString("seq_results_table", "seq_results_table");
    if (seq_results_table_name_.empty()) {
      LOG(ERROR) << "FollowMixSelectFinalSeqRetriever init with empty seq_results_table";
      return false;
    }
    return true;
  }

 private:
  std::string seq_results_table_name_;

  DISALLOW_COPY_AND_ASSIGN(FollowMixSelectFinalSeqRetriever);
};

}  // namespace platform
}  // namespace ks
