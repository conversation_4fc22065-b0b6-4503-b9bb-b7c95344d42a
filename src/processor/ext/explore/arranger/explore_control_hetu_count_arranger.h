#pragma once

#include <string>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_arranger.h"

namespace ks {
namespace platform {

// 实现功能: 根据用户的兴趣分布 打散规则 控制候选集中每个河图的个数
class ExploreControlHetuCountArranger : public CommonRecoBaseArranger {
 public:
  ExploreControlHetuCountArranger() {}

  RecoResultIter Arrange(
      MutableRecoContextInterface *context,
      RecoResultIter begin,
      RecoResultIter end) override;

 private:
  bool InitProcessor() override {
    user_hetu_stat_attr_ = config()->GetString("user_hetu_stat_attr");
    user_hetu_distribution_attr_ = config()->GetString("user_hetu_distribution_attr");
    user_actual_distribution_attr_ = config()->GetString("user_actual_distribution_attr");
    avg_reward_coeff_hetu_stat_attr_ = config()->GetString("avg_reward_coeff_hetu_stat_attr");
    hetu_level_one_attr_ = config()->GetString("hetu_level_one_attr");
    hetu_level_two_attr_ = config()->GetString("hetu_level_two_attr");
    hetu_level_five_attr_ = config()->GetString("hetu_level_five_attr");
    duration_ms_attr_ = config()->GetString("duration_ms_attr");
    author_attr_ = config()->GetString("author_attr");
    cluster_id_attr_ = config()->GetString("cluster_id_attr", "");
    return true;
  }
  bool ControlInterest(MutableRecoContextInterface *context, RecoResultIter iter);
  bool ControlSameAuthor(MutableRecoContextInterface *context, RecoResultIter iter);
  bool ControlDuration(MutableRecoContextInterface *context, RecoResultIter iter);
  bool ControlDiversity(MutableRecoContextInterface *context, RecoResultIter iter);
  bool ControlDiversityHetuList(MutableRecoContextInterface *context,
            RecoResultIter iter,
            const ItemAttr *hetu_level_accessor,
            int hetu_max_size, int default_tag, int none_hetu_max_size);
  bool ControlDiversityHetuListV2(MutableRecoContextInterface *context,
            RecoResultIter iter,
            const ItemAttr *hetu_level_accessor,
            int default_tag);
  bool ControlClusterIdDiversity(MutableRecoContextInterface *context, RecoResultIter iter);
  void CalcDynamicHetuControlDiversityQuota(
        MutableRecoContextInterface *context, RecoResultIter begin, RecoResultIter end, int keep_size);
  void DynamicHetuControlDiversityNormal(int keep_size);

 private:
  std::string user_hetu_stat_attr_;
  std::string hetu_level_one_attr_;
  std::string hetu_level_two_attr_;
  std::string hetu_level_five_attr_;
  std::string avg_reward_coeff_hetu_stat_attr_;
  std::string duration_ms_attr_;
  std::string author_attr_;
  std::string cluster_id_attr_;
  std::vector<int> interest_keys_;
  std::vector<int> diversity_keys_;
  std::vector<int> cluster_id_keys_;
  std::vector<int> duration_keys_;
  std::vector<uint64_t> same_author_keys_;
  int cluster_id_max_size_ = 0;
  int hetu1_max_size_ = 0;
  int hetu2_max_size_ = 0;
  int hetu5_max_size_ = 0;
  int duration_0_7s_max_size_ = 0;
  int duration_7_9s_max_size_ = 0;
  int duration_9_12s_max_size_ = 0;
  int duration_12_17s_max_size_ = 0;
  int duration_17_20s_max_size_ = 0;
  int duration_20_58s_max_size_ = 0;
  int duration_58_120s_max_size_ = 0;
  int duration_120_300s_max_size_ = 0;
  int duration_300_400s_max_size_ = 0;
  int duration_400s_inf_max_size_ = 0;
  int same_author_max_size_ = 0;
  ItemAttr* hetu_level_one_accessor_ = nullptr;
  ItemAttr* hetu_level_two_accessor_ = nullptr;
  ItemAttr* hetu_level_five_accessor_ = nullptr;
  ItemAttr* duration_ms_accessor_ = nullptr;
  ItemAttr* author_accessor_ = nullptr;
  ItemAttr* cluster_id_accessor_ = nullptr;
  folly::F14FastMap<int, int> target_hetu_quota_map_;
  folly::F14FastMap<int, int> target_diversity_quota_map_;
  folly::F14FastMap<int, int> target_duration_quota_map_;
  folly::F14FastMap<int, int> target_same_author_quota_map_;
  folly::F14FastMap<int, double> target_hetu_diversity_rate_map_;
  folly::F14FastMap<int, int> dynamic_hetu_diversity_quota_map_;
  folly::F14FastMap<int, int> target_cluster_id_diversity_quota_map_;
  std::string user_hetu_distribution_attr_;
  bool enable_hetu_diversity_control_ = false;
  // 用户真实兴趣配额调整
  folly::F14FastMap<int, double> target_hetu_actual_rate_map_;
  std::string user_actual_distribution_attr_;
  bool enable_actual_hetu_control_ = false;
  double hetu_adjust_max_value_ = 1.0;
  double hetu_adjust_min_value_ = 1.0;
  double hetu_adjust_coef_ = 1.0;
  bool enable_dynamic_hetu_control_diversity_ = false;
  bool enable_dynamic_hetu_control_diversity_v2_ = false;
  bool enable_hetu_control_diversity_none_hetu_ = false;
  int none_hetu1_max_size_ = 0;
  int none_hetu2_max_size_ = 0;
  int none_hetu5_max_size_ = 0;
  DISALLOW_COPY_AND_ASSIGN(ExploreControlHetuCountArranger);
};

}  // namespace platform
}  // namespace ks
