#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>

#include "dragon/src/processor/base/common_reco_base_arranger.h"
#include "dragon/src/processor/ext/explore/util/explore_util.h"
#include "ks/reco_proto/proto/reco.pb.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/base_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/lifecate_pic_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/not_in_index_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/server_show_aid_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/photo_status_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/over_180_days_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/upload_type_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/short_duration_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/mid_video_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/picture_type_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/source_aid_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/low_fans_lite_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/low_server_show_lite_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/long_term_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/zero_duration_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/auto_audit_hot_cover_bad_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/content_duplicate_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/hate_author_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/duration_random_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/emprical_ctr_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/duration_emp_watchtime_sample_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/xtab_life_index_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_exptag_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/fresh_request_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/multi_audit_gray_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/high_hot_audit_gray_show_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/audit_rule_adjust_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/merchant_holdout_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/video_quality_assessment_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/video_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/specified_group_audit_all_gray_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/audit_user_experiment_level_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/personified_author_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/movie_copyright_holdout_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/young_inc_tags_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/be_black_author_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/negative_retr_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/light_inc_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/fans_count_random_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/second_tab_category_id_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/low_comment_cnt_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/audit_hack_photo_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/audit_cold_review_level_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/user_reco_neg_photo_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/data_set_tags_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/short_term_negative_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/hetu_tag_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/hetu_sim_cluster_id_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/quality_audit_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/quality_control_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/dynamic_xtr_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/merchant_attempt_flag_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/hetu_author_category_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/empirical_xtr_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_low_quality_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/data_set_tags_bit_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/merchant_cart_holdout_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/high_photo_count_author_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/douyin_author_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/short_play_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_author_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_low_cost_marketing_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_mmu_hetu_tag_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_data_set_tags_bit_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_secure_grade_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_audit_cold_review_level_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_mix_interact_rate_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_bad_cover_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_low_cost_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_hack_act_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_long_live_photo_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_low_act_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_sexy_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_liezhi_author_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_xinxing_author_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/mid_long_video_holdout_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/explore_produce_type_filter.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/explore_magic_id_filter.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/global_emphtr_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/first_slide_impression_audit_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/continuous_hitting_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/repost_photo_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/minority_photo_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/first_refresh_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/social_holdout_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/sirius_distribution_photo_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/mmu_merchant_photo_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/first_fresh_ad_impression_audit_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/proximate_audit_hot_high_bad_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/no_cover_audit_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/reason_3125_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/protogenetic_advertise_tags_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/live_photo_flag_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/terrible_quality_style_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/emp_xtr_decrease_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/emp_xtr_decrease_tonpson_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/tnu_impression_audit_bad_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/negative_aid_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/valid_play_ratio_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/explore_over_distribute_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/serial_cover_photo_collection_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/merchant_hetu_tag_photo_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/tnu_content_control_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/emotions_pic_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_ecology_high_report_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_ecology_high_neg_pos_rate_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_ecology_high_short_play_rate_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/teenager_author_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_ecology_bad_avg_view_time_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/high_emp_ntpr_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/pic_ecology_mix_interact_rate_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/induced_author_black_list_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/lower_emp_xtr_act_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/llm_negative_photos_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/hot_point_pid_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/author_shop_score_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/author_goods_score_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/source_dup_content_id_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/short_term_report_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/emp_neg_feedback_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/high_report_photo_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/audit_overtime_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/marketing_static_video_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/coldstart_holdout_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/sexy_induce_author_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/poor_quality_author_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/topn_screen_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/fangpin_aid_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/plc_business_type_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/valuable_photo_open_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/star_holdout_filter_processor.h"
#include "dragon/src/processor/ext/explore/module/filter_processor/hot_spot_holdout_filter_processor.h"

namespace ks {
namespace platform {

class ExploreRetrievalFilterArranger : public CommonRecoBaseArranger {
 public:
  ExploreRetrievalFilterArranger() {}

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  bool InitProcessor() override {
    user_info_ptr_attr_ = config()->GetString("user_info_ptr_attr");
    if (user_info_ptr_attr_.empty()) {
      LOG(ERROR) << "ExploreRetrievalFilterArranger init failed! Missing \"user_info_ptr_attr\" config!";
      return false;
    }

    item_attr_map_config_ = config()->Get("item_attr_map");
    if (!item_attr_map_config_ || !item_attr_map_config_->IsObject()) {
      LOG(ERROR) << "ExploreRetrievalFilterArranger init failed! Missing \"item_attr_map\" config!";
      return false;
    }

    const auto *filter_list_config = config()->Get("filters");
    if (!filter_list_config || !filter_list_config->IsArray()) {
      LOG(ERROR) << "ExploreRetrievalFilterArranger init failed! Missing \"filters\" config!";
      return false;
    }

    filter_vec_.reserve(filter_list_config->array().size());
    for (const auto *filter_config : filter_list_config->array()) {
      if (!filter_config->IsObject()) {
        continue;
      }

      std::string name = filter_config->GetString("name");
      if (name.empty()) {
        continue;
      }

      auto iter = filter_map_.find(name);
      if (iter == filter_map_.end()) {
        continue;
      }

      std::string save_filtered_pid_list = filter_config->GetString("save_filtered_pid_list_to_attr");
      filter_vec_.push_back(Filter {
        .name = name,
        .enable = false,
        .config = filter_config,
        .processor = iter->second,
        .save_pid_list_attr = save_filtered_pid_list,
      });
    }

    return true;
  }

 private:
  void InitTruncationMap(MutableRecoContextInterface *context);
  void Perf(const std::string &request_type);

  class UserSelfFilterProcessor : public explore::BaseFilterProcessor {
   public:
    UserSelfFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    uint64 user_id_;
    const ItemAttr *author_id_attr_accessor_;
    DISALLOW_COPY_AND_ASSIGN(UserSelfFilterProcessor);
  };

  class PercentPunishFilterProcessor : public explore::BaseFilterProcessor {
   public:
    PercentPunishFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    DISALLOW_COPY_AND_ASSIGN(PercentPunishFilterProcessor);
  };

  class BreakCircleFilterProcessor : public explore::BaseFilterProcessor {
   public:
    BreakCircleFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    int age_segment_;
    ItemAttr *is_break_circle_good_photo_attr_accessor_;
    DISALLOW_COPY_AND_ASSIGN(BreakCircleFilterProcessor);
  };

  class OutdateNewsFilterProcessor : public explore::BaseFilterProcessor {
   public:
    OutdateNewsFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    int64 request_time_;
    ItemAttr *explore_operation_c_review_level_attr_accessor_;
    ItemAttr *upload_time_attr_accessor_;
    DISALLOW_COPY_AND_ASSIGN(OutdateNewsFilterProcessor);
  };

  class ValueableFilterProcessor : public explore::BaseFilterProcessor {
   public:
    ValueableFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    folly::F14FastSet<std::string> valuable_author_type_set_;
    const ItemAttr *is_cuckoo_photo_attr_accessor_;
    const ItemAttr *cuckoo_author_type_attr_accessor_;
    DISALLOW_COPY_AND_ASSIGN(ValueableFilterProcessor);
  };

  class MagicFreqControlFilterProcessor : public explore::BaseFilterProcessor {
   public:
    MagicFreqControlFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    bool is_magic_kk_freq_control_;
    folly::F14FastSet<int64> magic_kk_id_set_;
    const ItemAttr *magic_face_id_attr_accessor_;
    const ItemAttr *kuaishan_id_attr_accessor_;
    const ItemAttr *upload_type_attr_accessor_;
    const ItemAttr *outer_material_id_attr_accessor_;
    DISALLOW_COPY_AND_ASSIGN(MagicFreqControlFilterProcessor);
  };

  class TopkAuditBadFilterProcessor : public explore::BaseFilterProcessor {
   public:
    TopkAuditBadFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    folly::F14FastSet<int64> topk_audit_white_tag_set_;
    folly::F14FastSet<int64> topk_audit_black_tag_set_;
    int recall_filter_ = 0;
    int use_global_data_ = 0;
    int credible_ques_cnt_ = 30;
    int recall_mode_ = 0;
    double ques_info_pos_threshold_ = 1.0;
    double ques_info_unsure_threshold_ = 1.0;  // 问卷不确定率阈值
    double ques_info_neg_threshold_ = 1.0;  // 问卷不满意率阈值
    double ques_info_hate_threshold_ = 1.0;  // 负反馈率阈值
    DISALLOW_COPY_AND_ASSIGN(TopkAuditBadFilterProcessor);
  };

  class HighHotAuditBadFilterProcessor : public explore::BaseFilterProcessor {
   public:
    HighHotAuditBadFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    static constexpr int HIGH_HOT_IMPROPER_GUIDING = 291;
    folly::F14FastSet<int64> high_hot_audit_white_tag_set_;
    folly::F14FastSet<int64> high_hot_audit_black_tag_set_;
    folly::F14FastSet<int64> user_sexy_interest_exemption_tag_set_;
    const ItemAttr *audit_hot_high_tag_level_attr_accessor_;
    const ItemAttr *explore_operation_c_review_level_attr_accessor_;
    bool enable_user_sexy_interest_exemption_ = false;
    bool enable_user_sexy_interest_white_tag_ = false;
    bool enable_user_sexy_interest_hate_rate_ = false;
    int64 hate_threshold_ = 0;
    double hate_rate_threshold_ = 0.0;
    DISALLOW_COPY_AND_ASSIGN(HighHotAuditBadFilterProcessor);
  };

  class ImpressionAuditFilterProcessor : public explore::BaseFilterProcessor {
   public:
    ImpressionAuditFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    folly::F14FastSet<int64> impression_audit_white_tag_set_;
    folly::F14FastSet<int64> impression_audit_black_tag_set_;
    folly::F14FastSet<int64> user_sexy_interest_extra_filter_tag_set_;
    const ItemAttr *level_hot_online_attr_accessor_;
    const ItemAttr *audit_b_second_tag_attr_accessor_;
    bool enable_user_sexy_interest_extra_filter_ = false;
    DISALLOW_COPY_AND_ASSIGN(ImpressionAuditFilterProcessor);
  };

  class ZeroImpressionAuditFilterProcessor : public explore::BaseFilterProcessor {
   public:
    ZeroImpressionAuditFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    int32 explore_zero_play_days_15d_ = 0;
    DISALLOW_COPY_AND_ASSIGN(ZeroImpressionAuditFilterProcessor);
  };

  class EyeshotLongTermFilterProcessor : public explore::BaseFilterProcessor {
   public:
    EyeshotLongTermFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    const ItemAttr *is_eyeshot_longterm_photo_accessor_;
    DISALLOW_COPY_AND_ASSIGN(EyeshotLongTermFilterProcessor);
  };

  class TnuExtendIndexFilterProcessor : public explore::BaseFilterProcessor {
   public:
    TnuExtendIndexFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    const ItemAttr *is_tnu_extend_index_photo_accessor_;
    DISALLOW_COPY_AND_ASSIGN(TnuExtendIndexFilterProcessor);
  };

  class HighHotAuditSubdivisionLevelFilterProcessor : public explore::BaseFilterProcessor {
   public:
    HighHotAuditSubdivisionLevelFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    const ItemAttr *audit_hot_high_subdivision_level_accessor_;
    DISALLOW_COPY_AND_ASSIGN(HighHotAuditSubdivisionLevelFilterProcessor);
  };

  class BrowseScreenAidFilterProcessor : public explore::BaseFilterProcessor {
   public:
    BrowseScreenAidFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    folly::F14FastSet<int64> browse_screen_aid_set_;
    const ItemAttr *author_id_attr_accessor_;
    DISALLOW_COPY_AND_ASSIGN(BrowseScreenAidFilterProcessor);
  };

  class FollowAuthorFilterProcessor : public explore::BaseFilterProcessor {
   public:
    FollowAuthorFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    folly::F14FastSet<int64> follow_aid_set_;
    folly::F14FastSet<int64> ignore_exptag_set_;
    int64 now_ms_;
    int64 filter_timegap_ = 30;
    const ItemAttr *author_id_attr_accessor_;
    const ItemAttr *upload_time_attr_accessor_;
    DISALLOW_COPY_AND_ASSIGN(FollowAuthorFilterProcessor);
  };

  class BlackExemptLevelV1AuditFilterProcessor : public explore::BaseFilterProcessor {
   public:
    BlackExemptLevelV1AuditFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    const ItemAttr *auto_audit_black_exempt_level_v1_attr_accessor_;
    DISALLOW_COPY_AND_ASSIGN(BlackExemptLevelV1AuditFilterProcessor);
  };

  class LongTermHighLevelFilterProcessor : public explore::BaseFilterProcessor {
   public:
    LongTermHighLevelFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    const ItemAttr *long_term_high_level_photo_attr_accessor_;
    DISALLOW_COPY_AND_ASSIGN(LongTermHighLevelFilterProcessor);
  };

  class JianguanRiskFilterProcessor : public explore::BaseFilterProcessor {
   public:
    JianguanRiskFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    const ItemAttr *is_jianguan_risk_photo_attr_accessor_;
    DISALLOW_COPY_AND_ASSIGN(JianguanRiskFilterProcessor);
  };

  class BlackAuthorFilterProcessor : public explore::BaseFilterProcessor {
   public:
    BlackAuthorFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    folly::F14FastSet<int64> black_author_set_;
    const ItemAttr *author_id_attr_accessor_;
    DISALLOW_COPY_AND_ASSIGN(BlackAuthorFilterProcessor);
  };

  class ContentDupFilterProcessor : public explore::BaseFilterProcessor {
   public:
    ContentDupFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;
    bool SkipFilterAcrossSlide();

   private:
    ReadableRecoContextInterface *context_;
    std::vector<const ItemAttr *> content_type_attr_accessor_list_;
    std::vector<const ItemAttr *> content_type_attr_accessor_list_for_pic_;
    const ItemAttr *dup_cluster_id_attr_accessor_;
    const ItemAttr *pic_and_selfdup_id_attr_accessor_;
    const ItemAttr *sim_remove_dup_id_attr_accessor_ = nullptr;
    std::vector<int64> content_id_vec_;
    std::vector<int64> content_id_vec_for_pic_;
    folly::F14FastSet<int64> pid_set_;
    folly::F14FastSet<int64> pid_set_for_pic_;
    int32 skip_high_xtr_dup_filter_ = 0;
    int32 skip_high_hot_quality_pic_ = 1;
    int32 explore_skip_high_hot_quality_ = 1;
    int32 skip_dup_realshow_threshold_ = 100000;
    int32 skip_dup_watchtime_threshold_ = 100000;
    double fvtr_threshold_ = 0.0;
    double ctr_threshold_ = 0.0;

    DISALLOW_COPY_AND_ASSIGN(ContentDupFilterProcessor);
  };

  class FollowBrowseSetFilterProcessor : public explore::BaseFilterProcessor {
   public:
    FollowBrowseSetFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    folly::F14FastSet<int64> follow_browse_set_;

    DISALLOW_COPY_AND_ASSIGN(FollowBrowseSetFilterProcessor);
  };

  class ReportAuthorFilterProcessor : public explore::BaseFilterProcessor {
   public:
    ReportAuthorFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    folly::F14FastSet<uint64> report_aid_set_;
    folly::F14FastSet<uint32> report_hetu_set_;
    int64 enable_report_hetu_short_ = 0;
    int64 short_report_hetu_minutes_ = 0;
    int64 long_report_hetu_minutes_ = 0;
    const ItemAttr *author_id_attr_accessor_ = nullptr;

    DISALLOW_COPY_AND_ASSIGN(ReportAuthorFilterProcessor);
  };

  class CommerceExtendIndexFilterProcessor : public explore::BaseFilterProcessor {
   public:
    CommerceExtendIndexFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    const ItemAttr *is_high_other_photo_accessor_ = nullptr;

    DISALLOW_COPY_AND_ASSIGN(CommerceExtendIndexFilterProcessor);
  };

  class BackFreshClimbFilterProcessor : public explore::BaseFilterProcessor {
   public:
    BackFreshClimbFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    bool is_matched_user_;
    const ItemAttr *show_level_a_accessor_ = nullptr;

    DISALLOW_COPY_AND_ASSIGN(BackFreshClimbFilterProcessor);
  };

  class LowPornReportFilterProcessor : public explore::BaseFilterProcessor {
   public:
    LowPornReportFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    const ItemAttr *photo_low_report_count_accessor_ = nullptr;
    const ItemAttr *author_low_report_count_accessor_ = nullptr;

    DISALLOW_COPY_AND_ASSIGN(LowPornReportFilterProcessor);
  };

  class PictureFilterProcessor : public explore::BaseFilterProcessor {
   public:
    PictureFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    int only_filter_long_and_set_ = 0;
    int64 only_filter_high_value_pic_ = 0;
    DISALLOW_COPY_AND_ASSIGN(PictureFilterProcessor);
  };

  class TotalReportFilterProcessor : public explore::BaseFilterProcessor {
   public:
    TotalReportFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    DISALLOW_COPY_AND_ASSIGN(TotalReportFilterProcessor);
  };

  class EvilTitleFilterProcessor : public explore::BaseFilterProcessor {
   public:
    EvilTitleFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    DISALLOW_COPY_AND_ASSIGN(EvilTitleFilterProcessor);
  };

  class ShortTermHateFilterProcessor : public explore::BaseFilterProcessor {
   public:
    ShortTermHateFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    static constexpr int HATE_CONTENT_TAG = 12;
    static constexpr int HATE_REASON_AUTHOR = 4;

    int32 enable_short_hate_l5_filter_ = 0;
    int32 hetu_tag_l5_minutes_cut_ = 30;
    int32 enable_long_hate_filter_ = 0;
    int32 hetu_tag_long_term_minutes_cut_ = 300;
    int32 hetu_l2_long_filter_threshold_ = 5;
    int32 hetu_otherl_long_filter_threshold_ = 3;
    int32 enable_hate_content_reason_filter_ = 0;
    int32 hate_content_reason_minutes_cut_ = 60;
    int32 hetu_tag_l3_minutes_cut_ = 30;
    int32 enable_hate_author_skip_hetu_filter_ = 0;
    int32 explore_high_hetu_num_threshold_ = 0;
    int32 explore_low_hetu_num_threshold_ = 0;
    folly::F14FastSet<uint32> hate_hetu_level2_set_;
    folly::F14FastSet<uint32> hate_hetu_level3_set_;
    folly::F14FastSet<uint32> hate_hetu_level5_set_;
    folly::F14FastSet<uint32> hate_hetu_face_id_set_;
    folly::F14FastSet<uint32> hate_hetu_tag_set_;

    folly::F14FastMap<uint32, uint32> longterm_hate_hetu_level2_map_;
    folly::F14FastMap<uint32, uint32> longterm_hate_hetu_level3_map_;
    folly::F14FastMap<uint32, uint32> longterm_hate_hetu_level5_map_;
    folly::F14FastMap<uint32, uint32> longterm_hate_hetu_face_id_map_;
    folly::F14FastMap<uint32, uint32> longterm_hate_hetu_tag_map_;

    DISALLOW_COPY_AND_ASSIGN(ShortTermHateFilterProcessor);
  };

  class HighPhtrFilterProcessor : public explore::BaseFilterProcessor {
   public:
    HighPhtrFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    int64 emp_realshow_show_threshold_ = 12000;
    int64 emp_realshow_show_high_threshold_ = 12000;
    int64 emp_hate_cnt_filter_threshold_ = 12000;
    double emphtr_filter_threshold_ = 0.05;
    int32 enable_hate_cost_ = 0;
    int32 enable_hate_count_filter_ = 0;
    double ctr_weight_ = 0.0;
    double ltr_weight_ = 0.0;
    double wtr_weight_ = 0.0;
    double ftr_weight_ = 0.0;
    double cmtr_weight_ = 0.0;
    double time_weight_ = 0.0;
    double normal_time_weight_ = 0.0;
    double report_weight_ = 0.0;
    double coeff_max_ = 1.0;
    double coeff_min_ = 1.0;
    double alpha_ = 1.0;
    double beta_ = 1.0;
    double omega_ = 100000.0;
    double exp_upper_ = 10.0;
    int32 enable_adpt_threshold_ = 0;
    int32 enable_adpt_threshold_by_realshow_ = 0;
    std::vector<double> emphtr_filter_threshold_vec_;

    DISALLOW_COPY_AND_ASSIGN(HighPhtrFilterProcessor);
  };

  class ShortPicHetuFilterProcessor : public explore::BaseFilterProcessor {
   public:
    ShortPicHetuFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    folly::F14FastSet<uint64> hetu_set_;
    static constexpr int64 short_pic_upload_type_ = 7;

    DISALLOW_COPY_AND_ASSIGN(ShortPicHetuFilterProcessor);
  };

  class LongPicFilterProcessor : public explore::BaseFilterProcessor {
   public:
    LongPicFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    folly::F14FastSet<uint64> long_pic_upload_type_set_;
    folly::F14FastSet<uint64> long_pic_picture_type_set_;

    DISALLOW_COPY_AND_ASSIGN(LongPicFilterProcessor);
  };

  class Face90DegreeFilterProcessor : public explore::BaseFilterProcessor {
   public:
    Face90DegreeFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    const folly::F14FastSet<uint64> *pid_set_ = nullptr;
    DISALLOW_COPY_AND_ASSIGN(Face90DegreeFilterProcessor);
  };

  class PicWallpaperFilterProcessor : public explore::BaseFilterProcessor {
   public:
    PicWallpaperFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    folly::F14FastSet<uint64> wallpaper_hetu_set_;
    bool enable_wallpaper_caption_keep_ = false;
    int32 caption_keep_thresh_ = 200;
    DISALLOW_COPY_AND_ASSIGN(PicWallpaperFilterProcessor);
  };

  class AuditHotCoverLevelFilterProcessor : public explore::BaseFilterProcessor {
   public:
    AuditHotCoverLevelFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    DISALLOW_COPY_AND_ASSIGN(AuditHotCoverLevelFilterProcessor);
  };

  class AuditGrayCoverLevelFilterProcessor : public explore::BaseFilterProcessor {
   public:
    AuditGrayCoverLevelFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    bool is_not_cover_filter_ = false;
    folly::F14FastSet<int> hetu_v3_level_one_white_tag_set_;
    int64 fans_threshold_ = 0;
    DISALLOW_COPY_AND_ASSIGN(AuditGrayCoverLevelFilterProcessor);
  };

  class ImpressionAuditGrayFilterProcessor : public explore::BaseFilterProcessor {
   public:
    ImpressionAuditGrayFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    DISALLOW_COPY_AND_ASSIGN(ImpressionAuditGrayFilterProcessor);
  };

  class MmuLowCoverFilterProcessor : public explore::BaseFilterProcessor {
   public:
    MmuLowCoverFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    std::unordered_map<int64, double> lower_cover_mmu_map_;
    int skip_beauty_photo_filter_ = 0;
    int enable_follow_author_exemption_ = 0;
    int64 user_gender_attr_ = 0;
    int64 enable_explore_gender_ = 0;
    int enable_impression_good_ignore_ = 0;
    folly::F14FastSet<int64> follow_author_id_set_;
    DISALLOW_COPY_AND_ASSIGN(MmuLowCoverFilterProcessor);
  };

  class NewMarketingSenseFilterProcessor : public explore::BaseFilterProcessor {
   public:
    NewMarketingSenseFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    int enable_cart_photo_filter_ = 0;
    int enable_hetu_filter_ = 0;
    int enable_audit_tag_filter_ = 0;
    const ItemAttr *merchant_item_id_list_attr_accessor_ = nullptr;
    const ItemAttr *merchant_photo_cart_relation_attr_accessor_ = nullptr;
    const ItemAttr *hetu_level_two_tag_list_attr_accessor_ = nullptr;
    const ItemAttr *hetu_tag_list_attr_accessor_ = nullptr;
    DISALLOW_COPY_AND_ASSIGN(NewMarketingSenseFilterProcessor);
  };

  class NotAuditPhotoFilterProcessor : public explore::BaseFilterProcessor {
   public:
    NotAuditPhotoFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    double cold_start_breakout_score_threshold_ = 0.0;
    int high_fans_threshold_ = -1;
    double ctr_threshold_ = -1.0;
    int higher_action_threshold_ = -1;
    int need_high_quality_mmu_score_ = 0;
    int skip_not_audit_zero_value_ = 0;
    int skip_not_audit_follow_author_ = 0;
    std::unordered_map<int64, double> high_quality_mmu_map_;
    folly::F14FastSet<int64> follow_author_id_set_;
    DISALLOW_COPY_AND_ASSIGN(NotAuditPhotoFilterProcessor);
  };

  class QuestionaireInfoFilterProcessor : public explore::BaseFilterProcessor {
   public:
     QuestionaireInfoFilterProcessor() {}
     void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
      double questionaire_info_negative_rate_threshold_ = 0.0;
      double questionaire_info_negative_rate_high_threshold_ = 0.0;
      double questionaire_info_positive_rate_threshold_ = 0.0;
      double questionaire_info_unsure_rate_threshold_ = 0.0;
      double neg_weight_ = 0.0;
      double pos_weight_ = 0.0;
      double unsure_weight_ = 0.0;
      double click_weight_ = 0.0;
      double unclick_weight_ = 0.0;
      int credible_questionnaire_total_count_ = 100;
      int replace_topk_result_ = 0;
      int topk_level_threshold_ = 0;
      int audit_level_threshold_ = 0;
      int questionaire_thompson_filter_ = 0;
      int use_global_data_ = 0;
      std::mt19937 random_engine_;
      DISALLOW_COPY_AND_ASSIGN(QuestionaireInfoFilterProcessor);
  };

  class RiskManRiskPhotoFilterProcessor : public explore::BaseFilterProcessor {
   public:
    RiskManRiskPhotoFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    int64 user_level_ = 2;
    int is_tmp_risk_user_ = 0;
    int64 user_risk_min_ = 4;
    int black_white_change_ = 0;
    DISALLOW_COPY_AND_ASSIGN(RiskManRiskPhotoFilterProcessor);
  };

  class NeedShufflePhotoFilterProcessor : public explore::BaseFilterProcessor {
   public:
    NeedShufflePhotoFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    int64 is_need_shuffle_man_ = 0;
    int is_tmp_shuffle_user_ = 0;
    int black_white_change_ = 0;
    DISALLOW_COPY_AND_ASSIGN(NeedShufflePhotoFilterProcessor);
  };

  class LowRealShowFilterProcessor : public explore::BaseFilterProcessor {
   public:
    LowRealShowFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    int64 threshold_of_low_real_show_ = 0;
    folly::F14FastSet<uint64> black_hetu_set_;
    DISALLOW_COPY_AND_ASSIGN(LowRealShowFilterProcessor);
  };

  class LowFansFilterProcessor : public explore::BaseFilterProcessor {
   public:
    LowFansFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    int64 threshold_of_low_fans_ = 0;
    folly::F14FastSet<uint64> black_hetu_set_;
    DISALLOW_COPY_AND_ASSIGN(LowFansFilterProcessor);
  };

  class HighExploreShowRateFilterProcessor : public explore::BaseFilterProcessor {
   public:
    HighExploreShowRateFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    double rate_of_high_explore_show_ = 1.0;
    int64 min_show_of_high_explore_show_ = 0.0;
    int64 max_show_of_high_explore_show_ = 0.0;
    folly::F14FastSet<uint64> black_hetu_set_;
    DISALLOW_COPY_AND_ASSIGN(HighExploreShowRateFilterProcessor);
  };

  class PictureBeforeAdminFilterProcessor : public explore::BaseFilterProcessor {
   public:
    PictureBeforeAdminFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    std::unordered_map<int64, double> mmu_type_map_;
    int64 explore_server_show_threshold = 12000;
    double explore_ctr_threshold = 0.15;
    DISALLOW_COPY_AND_ASSIGN(PictureBeforeAdminFilterProcessor);
  };

  class SiriusPhotoFilterProcessor : public explore::BaseFilterProcessor {
   public:
    SiriusPhotoFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    DISALLOW_COPY_AND_ASSIGN(SiriusPhotoFilterProcessor);
  };

  class DownloadDisabledPicFilterProcessor : public explore::BaseFilterProcessor {
   public:
    DownloadDisabledPicFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    DISALLOW_COPY_AND_ASSIGN(DownloadDisabledPicFilterProcessor);
  };

  class UnpersonifiedAuthorPicFilterProcessor : public explore::BaseFilterProcessor {
   public:
    UnpersonifiedAuthorPicFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    const folly::F14FastSet<uint64> *unpersonified_author_set_ = nullptr;
    DISALLOW_COPY_AND_ASSIGN(UnpersonifiedAuthorPicFilterProcessor);
  };

  class BlackPhotosFilterProcessor : public explore::BaseFilterProcessor {
   public:
    BlackPhotosFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    folly::F14FastSet<uint64> black_photos_;
    DISALLOW_COPY_AND_ASSIGN(BlackPhotosFilterProcessor);
  };

  class PictureSupportAuthorRealshowCtrFilterProcessor : public explore::BaseFilterProcessor {
   public:
    PictureSupportAuthorRealshowCtrFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;
   private:
    double support_author_picture_realshow_threshold_ = 10000;
    double support_author_picture_ctr_threshold_ = 0.001;
    const folly::F14FastMap<uint64, folly::F14FastMap<uint64, double>>* support_author_aid_map_ = nullptr;
   private:
    DISALLOW_COPY_AND_ASSIGN(PictureSupportAuthorRealshowCtrFilterProcessor);
  };

  class ImpressionAuditGrayShowFilterProcessor : public explore::BaseFilterProcessor {
   public:
    ImpressionAuditGrayShowFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;
   private:
    int64 impression_audit_gray_show_limit_ = 50000;
    folly::F14FastSet<int64> impression_audit_gray_tag_set_;
   private:
    DISALLOW_COPY_AND_ASSIGN(ImpressionAuditGrayShowFilterProcessor);
  };

  class PhotoLifeFilterProcessor : public explore::BaseFilterProcessor {
   public:
    PhotoLifeFilterProcessor() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    int photo_life_max_hours_ = 168;
    folly::F14FastSet<int64> follow_aid_set_;

    DISALLOW_COPY_AND_ASSIGN(PhotoLifeFilterProcessor);
  };

  class ExplorePunishFilterProcessor : public explore::BaseFilterProcessor {
   public:
    ExplorePunishFilterProcessor() {}
    void Init(ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info,
              const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    DISALLOW_COPY_AND_ASSIGN(ExplorePunishFilterProcessor);
  };

  class ExplorePunishCityFilterProcessor : public explore::BaseFilterProcessor {
   public:
    ExplorePunishCityFilterProcessor() {}
    void Init(ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info,
              const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    int64 request_city_id_ = -1;
    DISALLOW_COPY_AND_ASSIGN(ExplorePunishCityFilterProcessor);
  };
  class NegativeThompsonFilter : public explore::BaseFilterProcessor {
   public:
    NegativeThompsonFilter() {}
    void Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    double thompson_filter_threshold_ = 0.0;
    int enable_interaction_base_ = 0;
    double thompson_filter_realshow_divisor_ = 1000.0;
    int enable_explore_cnt_ = 1;
    int enable_fountain_cnt_ = 0;
    int enable_thanos_cnt_ = 0;
    int enable_nebula_cnt_ = 0;
    int enable_skip_filter_ = 0;
    double ctr_weight_ = 0.0;
    double ltr_weight_ = 0.0;
    double wtr_weight_ = 0.0;
    double ftr_weight_ = 0.0;
    double cmtr_weight_ = 0.0;
    double time_weight_ = 0.0;
    double normal_time_weight_ = 0.0;
    double lvtr_weight_ = 0.0;
    double report_weight_ = 0.0;
    double no_click_weight_ = 0.0;
    double realshow_weight_ = 0.0;
    double emp_htr_threshold_ = 0.0;
    std::mt19937 random_engine_;
    DISALLOW_COPY_AND_ASSIGN(NegativeThompsonFilter);
  };

  class ExploreBoostPhotoFilterProcessor : public explore::BaseFilterProcessor {
   public:
    ExploreBoostPhotoFilterProcessor() {}
    void Init(ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info,
              const base::Json *config) override;
    bool NeedFilter(const CommonRecoResult &result) override;

   private:
    folly::F14FastSet<uint64> hetu_set_one_;
    folly::F14FastSet<uint64> hetu_set_two_;
    folly::F14FastSet<uint64> hetu_set_three_;
    folly::F14FastSet<uint64> target_reason_;

    DISALLOW_COPY_AND_ASSIGN(ExploreBoostPhotoFilterProcessor);
  };

  folly::F14FastMap<std::string, std::shared_ptr<explore::BaseFilterProcessor>> filter_map_ {
    { "not_in_index",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::NotInIndexFilterProcessor()) },
    { "user_self", std::shared_ptr<explore::BaseFilterProcessor>(new UserSelfFilterProcessor()) },
    { "short_duration",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::ShortDurationFilterProcessor()) },
    { "percent_punish", std::shared_ptr<explore::BaseFilterProcessor>(new PercentPunishFilterProcessor()) },
    { "break_circle", std::shared_ptr<explore::BaseFilterProcessor>(new BreakCircleFilterProcessor()) },
    { "over_180_days",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::Over180DaysFilterProcessor()) },
    { "outdate_news", std::shared_ptr<explore::BaseFilterProcessor>(new OutdateNewsFilterProcessor()) },
    { "valueable", std::shared_ptr<explore::BaseFilterProcessor>(new ValueableFilterProcessor()) },
    { "magic_freq_control",
      std::shared_ptr<explore::BaseFilterProcessor>(new MagicFreqControlFilterProcessor()) },
    { "topk_audit_bad", std::shared_ptr<explore::BaseFilterProcessor>(new TopkAuditBadFilterProcessor()) },
    { "high_hot_audit_bad",
      std::shared_ptr<explore::BaseFilterProcessor>(new HighHotAuditBadFilterProcessor()) },
    { "high_hot_audit_gray_show",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::HighHotAuditGrayShowFilterProcessor()) },
    { "impression_audit_bad",
      std::shared_ptr<explore::BaseFilterProcessor>(new ImpressionAuditFilterProcessor()) },
    { "zero_impression_level_hot_good",
      std::shared_ptr<explore::BaseFilterProcessor>(new ZeroImpressionAuditFilterProcessor()) },
    { "eyeshot_long_term",
      std::shared_ptr<explore::BaseFilterProcessor>(new EyeshotLongTermFilterProcessor()) },
    { "tnu_extend_index",
      std::shared_ptr<explore::BaseFilterProcessor>(new TnuExtendIndexFilterProcessor()) },
    { "high_hot_audit_subdivision_level",
      std::shared_ptr<explore::BaseFilterProcessor>(new HighHotAuditSubdivisionLevelFilterProcessor()) },
    { "browse_screen_aid",
      std::shared_ptr<explore::BaseFilterProcessor>(new BrowseScreenAidFilterProcessor()) },
    { "follow_author", std::shared_ptr<explore::BaseFilterProcessor>(new FollowAuthorFilterProcessor()) },
    { "black_exempt_level_v1_audit",
      std::shared_ptr<explore::BaseFilterProcessor>(new BlackExemptLevelV1AuditFilterProcessor()) },
    { "long_term_high_level",
      std::shared_ptr<explore::BaseFilterProcessor>(new LongTermHighLevelFilterProcessor()) },
    { "photo_status",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PhotoStatusFilterProcessor()) },
    { "jianguan_risk", std::shared_ptr<explore::BaseFilterProcessor>(new JianguanRiskFilterProcessor()) },
    { "black_author", std::shared_ptr<explore::BaseFilterProcessor>(new BlackAuthorFilterProcessor()) },
    { "content_dup", std::shared_ptr<explore::BaseFilterProcessor>(new ContentDupFilterProcessor()) },
    { "follow_browse_set",
      std::shared_ptr<explore::BaseFilterProcessor>(new FollowBrowseSetFilterProcessor()) },
    { "report_author", std::shared_ptr<explore::BaseFilterProcessor>(new ReportAuthorFilterProcessor()) },
    { "hate_author",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::HateAuthorFilterProcessor()) },
    { "commerce_extend_index",
      std::shared_ptr<explore::BaseFilterProcessor>(new CommerceExtendIndexFilterProcessor()) },
    { "back_fresh_climb",
      std::shared_ptr<explore::BaseFilterProcessor>(new BackFreshClimbFilterProcessor()) },
    { "low_porn_report", std::shared_ptr<explore::BaseFilterProcessor>(new LowPornReportFilterProcessor()) },
    { "picture", std::shared_ptr<explore::BaseFilterProcessor>(new PictureFilterProcessor()) },
    { "total_report", std::shared_ptr<explore::BaseFilterProcessor>(new TotalReportFilterProcessor()) },
    { "evil_title", std::shared_ptr<explore::BaseFilterProcessor>(new EvilTitleFilterProcessor()) },
    { "short_term_hate", std::shared_ptr<explore::BaseFilterProcessor>(new ShortTermHateFilterProcessor()) },
    { "short_pic_hetu", std::shared_ptr<explore::BaseFilterProcessor>(new ShortPicHetuFilterProcessor()) },
    { "long_pic", std::shared_ptr<explore::BaseFilterProcessor>(new LongPicFilterProcessor()) },
    { "face_90_degree", std::shared_ptr<explore::BaseFilterProcessor>(new Face90DegreeFilterProcessor()) },
    { "pic_wallpaper", std::shared_ptr<explore::BaseFilterProcessor>(new PicWallpaperFilterProcessor()) },
    { "auto_audit_hot_cover_level_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::AutoAuditHotCoverLevelFilterProcessor()) },
    { "audit_hot_cover_level_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new AuditHotCoverLevelFilterProcessor()) },
    { "audit_gray_cover_level_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new AuditGrayCoverLevelFilterProcessor()) },
    { "impression_audit_gray",
      std::shared_ptr<explore::BaseFilterProcessor>(new ImpressionAuditGrayFilterProcessor())},
    { "mmu_low_cover_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new MmuLowCoverFilterProcessor())},
    { "new_marketing_sense",
      std::shared_ptr<explore::BaseFilterProcessor>(new NewMarketingSenseFilterProcessor())},
    { "not_audit_level_b",
      std::shared_ptr<explore::BaseFilterProcessor>(new NotAuditPhotoFilterProcessor())},
    { "quetionaire_info_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new QuestionaireInfoFilterProcessor())},
    { "risk_man_risk_photo",
      std::shared_ptr<explore::BaseFilterProcessor>(new RiskManRiskPhotoFilterProcessor())},
    { "need_shuffle_photo",
      std::shared_ptr<explore::BaseFilterProcessor>(new NeedShufflePhotoFilterProcessor())},
    { "low_real_show", std::shared_ptr<explore::BaseFilterProcessor>(new LowRealShowFilterProcessor())},
    { "low_fans", std::shared_ptr<explore::BaseFilterProcessor>(new LowFansFilterProcessor())},
    { "high_explore_show",
      std::shared_ptr<explore::BaseFilterProcessor>(new HighExploreShowRateFilterProcessor())},
    { "pic_filter_before_admin",
      std::shared_ptr<explore::BaseFilterProcessor>(new PictureBeforeAdminFilterProcessor())},
    { "is_sirius_punish", std::shared_ptr<explore::BaseFilterProcessor>(new SiriusPhotoFilterProcessor())},
    { "download_disabled_pic",
      std::shared_ptr<explore::BaseFilterProcessor>(new DownloadDisabledPicFilterProcessor())},
    { "black_photos", std::shared_ptr<explore::BaseFilterProcessor>(new BlackPhotosFilterProcessor())},
    { "pic_support_author_realshow_ctr_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new PictureSupportAuthorRealshowCtrFilterProcessor())},
    { "impression_audit_gray_show",
      std::shared_ptr<explore::BaseFilterProcessor>(new ImpressionAuditGrayShowFilterProcessor())},
    { "high_emp_phtr_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new HighPhtrFilterProcessor())},
    { "unpersonified_author_pic_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new UnpersonifiedAuthorPicFilterProcessor())},
    { "photo_life",
      std::shared_ptr<explore::BaseFilterProcessor>(new PhotoLifeFilterProcessor())},
    { "server_show_aid",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::ServerShowAidFilterProcessor())},
    { "explore_punish_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new ExplorePunishFilterProcessor())},
    { "explore_punish_city_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new ExplorePunishCityFilterProcessor())},
    { "upload_type",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::UploadTypeFilterProcessor())},
    { "negative_thompson_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new NegativeThompsonFilter())},
    { "explore_boost_photo_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new ExploreBoostPhotoFilterProcessor())},
    { "mid_video",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::MidVideoFilterProcessor())},
    { "picture_type",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PictureTypeFilterProcessor())},
    { "source_aid",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::SourceAidFilterProcessor())},
    { "low_fans_lite",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::LowFansLiteFilterProcessor())},
    { "low_server_show_lite",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::LowServerShowLiteFilterProcessor())},
    { "long_term",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::LongTermFilterProcessor())},
    { "zero_duration",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::ZeroDurationFilterProcessor())},
    { "content_dup_v2",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::ContentDuplicateFilterProcessor())},
    { "duration_random_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::DurationRandomFilterProcessor())},
    { "emprical_ctr",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::LowCtrFilterProcessor())},
    { "duration_emp_watchtime_sample_filter",
      std::shared_ptr<explore::BaseFilterProcessor>
      (new explore::DurationEmpWatchTimeSampleFilterProcessor())},
    { "xtab_life_index_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::XTabLifeIndexFilterProcessor())},
    { "lifecate_pic_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::LifecatePicFilterProcessor())},
    { "pic_exptag_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PicExpTagFilterProcessor())},
    { "fresh_request_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::FreshRequestFilterProcessor())},
    { "multi_audit_gray_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::MultiAuditGrayFilterProcessor())},
    { "audit_rule_adjust_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::AuditRuleAdjustFilterProcessor())},
    { "empirical_xtr",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::EmpiricalXtrFilterProcessor())},
    { "merchant_holdout_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::MerchantHoldoutFilterProcessor())},
    { "video_quality_assessment_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::VideoQualityAssessmentFilterProcessor())},
    { "video_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::VideoFilterProcessor())},
    { "specified_group_gray_audit_filter",
      std::shared_ptr<explore::BaseFilterProcessor>
      (new explore::SpecifiedGroupAuditAllGrayFilterProcessor())},
    { "second_tab_hetu_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::SecondTabCategoryIdFilterProcessor())},
    { "dynamic_xtr_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::DynamicXtrFilterProcessor())},
    { "pic_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PicFilterProcessor())},
    { "audit_user_experiment_level_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::AuditUserExperimentLevelProcessor())},
    { "personified_author_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PersonifiedAuthorFilterProcessor())},
    { "movie_copyright_holdout_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::MovieCopyrightHoldoutFilterProcessor())},
    { "star_holdout_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::StarHoldoutFilterProcessor())},
    { "young_inc_tags_holdout_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::YoungIncTagsFilterProcessor())},
    { "be_black_author_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::BeBlackAuthorFilterProcessor())},
    { "negative_retr_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::NegativeRetrFilterProcessor())},
    { "fans_count_random_holdout_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::FansCountRandomFilterProcessor())},
    { "light_inc_holdout_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::LightIncFilterProcessor())},
    { "low_comment_cnt_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::LowCommentCntFilterProcessor())},
    { "audit_hack_photo_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::AuditHackPhotoFilterProcessor())},
    { "audit_cold_review_level_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::AuditColdReviewLevelFilterProcessor())},
    { "user_reco_neg_photo_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::UserRecoNegPhotoFilterProcessor())},
    { "data_set_tags_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::DataSetTagsFilterProcessor())},
    { "short_term_negative_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::ShortTermNegativeFilterProcessor())},
    { "hetu_tag_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::HetuTagFilterProcessor())},
    { "hetu_sim_cluster_id_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::HetuSimClusterIdFilterProcessor())},
    { "quality_audit_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::QualityAuditFilterProcessor())},
    { "quality_control_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::QualityControlFilterProcessor())},
    { "ecom_intent_score_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::MerchantAttemptFlagFilterProcessor())},
    { "hetu_author_category_holdout_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::HetuAuthorCategoryFilterProcessor())},
    { "pic_low_quality_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PicLowQualityFilterProcessor())},
    { "data_set_tags_bit_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::DataSetTagsBitFilterProcessor())},
    { "merchant_cart_holdout_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::MerchantCartHoldoutFilterProcessor())},
    { "high_photo_count_author_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::HighPhotoCountAuthorFilterProcessor())},
    { "douyin_author_holdout_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::DouyinAuthorFilterProcessor())},
    { "short_play_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::ShortPlayFilterProcessor())},
    { "pic_author_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PicAuthorFilterProcessor())},
    { "pic_low_cost_marketing_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PicLowCostMarketingFilterProcessor())},
    { "pic_mmu_hetu_tag_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PicMmuHetuTagFilterProcessor())},
    { "pic_data_set_tags_bit_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PicDataSetTagsBitFilterProcessor())},
    { "pic_secure_grade_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PicSecureGradeFilterProcessor())},
    { "pic_audit_cold_review_level_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PicAuditColdReviewLevelFilterProcessor())},
    { "pic_mix_interact_rate_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PicMixInteractRateFilterProcessor())},
    { "pic_bad_cover_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PicBadCoverFilterProcessor())},
    { "high_emp_ntpr_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::HighEmpNtprFilterProcessor())},
    { "pic_low_cost_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PicLowCostFilterProcessor())},
    { "pic_hack_act_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PicHackActFilterProcessor())},
    { "pic_long_live_photo_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PicLongLivePhotoFilterProcessor())},
    { "pic_low_act_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PicLowActFilterProcessor())},
    { "pic_sexy_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PicSexyFilterProcessor())},
    { "pic_liezhi_author_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PicLiezhiAuthorFilterProcessor())},
    { "pic_xinxing_author_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PicXinXingAuthorFilterProcessor())},
    { "mid_long_video_holdout_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::MidLongVideoFilterProcessor())},
    { "produce_type_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::ProduceTypeFilterProcessor())},
    { "magic_id_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::MagicIdFilterProcessor())},
    { "high_global_emphtr_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::GlobalEmphtrFilterProcessor())},
    { "first_slide_impression_audit_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::FirstSlideImpressionAuditFilterProcessor())},
    { "continuous_hitting_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::ContinuousHittingFilterProcessor())},
    { "repost_photo_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::RepostPhotoFilterProcessor())},
    { "minority_photo_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::MinorityPhotoFilterProcessor())},
    { "first_refresh_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::FirstRefreshFilterProcessor())},
    { "social_holdout_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::SocialHoldoutFilterProcessor())},
    { "sirius_distribution_photo_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::SiriusDistributionPhotoFilterProcessor())},
    { "mmu_merchant_photo_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::MMUMerchantPhotoFilterProcessor())},
    { "first_fresh_ad_impression_audit_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::FirstFreshAdImpressionAuditFilterProcessor())},
    { "proximate_audit_hot_high_bad_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::ProximateAuditHotHighBadFilterProcessor())},
    { "no_cover_audit_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::NoCoverAuditFilterProcessor())},
    { "reason_3125_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::Reason3125FilterProcessor())},
    { "protogenetic_advertise_tags_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::ProtogeneticAdvertiseTagsFilterProcessor())},
    { "live_photo_flag_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::LivePhotoFlagFilterProcessor())},
    { "terrible_quality_style_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::TerribleQualityStyleFilterProcessor())},
    { "emp_xtr_decrease_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::EmpXtrDecreaseFilterProcessor())},
    { "emp_xtr_decrease_tonpson_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::EmpXtrDecreaseTonpsonFilterProcessor())},
    { "tnu_impression_audit_bad_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::TnuImpressionAuditBadFilterProcessor())},
    { "negative_aid_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::NegativeAidFilterProcessor())},
    { "valid_play_ratio_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::ValidPlayRatioFilterProcessor())},
    { "over_distribute_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::ExploreOverDistributeFilterProcessor())},
    { "serial_cover_photo_collection_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::SerialCoverPhotoCollectionFilterProcessor())},
    { "merchant_hetu_tag_photo_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::MerchantHetuTagPhotoFilterProcessor())},
    { "tnu_content_control_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::TnuContentControlFilterProcessor())},
    { "emotions_pic_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::EmotionsPicFilterProcessor())},
    { "pic_ecology_high_report_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::PicEcologyHighReportFilterProcessor())},
    { "pic_ecology_high_neg_pos_rate_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::PicEcologyHighNegPosRateFilterProcessor())},
    { "pic_ecology_high_short_play_rate_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::PicEcologyHighShortPlayRateFilterProcessor())},
    { "teenager_author_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::TeenagerAuthorFilterProcessor())},
    { "pic_ecology_bad_avg_time_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::PicEcologyBadAvgViewTimeFilterProcessor())},
    { "pic_ecology_high_release_author_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::DataSetTagsBitFilterProcessor())},
    { "pic_ecology_high_delete_author_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::DataSetTagsBitFilterProcessor())},
    { "pic_ecology_mix_interact_rate_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::PicEcologyMixInteractRateFilterProcessor())},
    { "induced_author_black_list_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::InducedAuthorBlackListFilterProcessor())},
    { "lower_emp_xtr_act_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::LowerEmpXtrActFilterProcessor())},
    { "llm_negative_photos_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::LlmNegativePhotosFilterProcessor())},
    { "hot_point_pid_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::HotPointPidFilterProcessor())},
    { "author_shop_score_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::AuthorShopScoreFilterProcessor())},
    { "author_goods_score_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::AuthorGoodsScoreFilterProcessor())},
    { "audit_overtime_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::AuditOvertimeFilterProcessor())},
    { "short_term_report_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::ShortTermReportFilterProcessor())},
    { "source_dup_content_id_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(
      new explore::SourceDupContentIdFilterProcessor())},
    { "emp_neg_feedback_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::EmpNegFeedbackFilterProcessor())},
    { "high_report_photo_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::HighReportPhotoFilterProcessor())},
    { "marketing_static_video_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::MarketingStaticVideoFilterProcessor())},
    { "coldstart_holdout_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::ColdstartHoldoutFilterProcessor())},
    { "sexy_induce_author_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::SexyInduceAuthorFilterProcessor())},
    { "poor_quality_author_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PoorQualityAuthorFilterProcessor())},
    { "topn_screen_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::TopnScreenFilterProcessor())},
    { "fangpin_aid_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::FangpinAidFilterProcessor())},
    { "plc_business_type_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::PlcBusinessTypeFilterProcessor())},
    { "valuable_photo_open_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::ValuablePhotoOpenFilterProcessor())},
    { "hot_spot_holdout_filter",
      std::shared_ptr<explore::BaseFilterProcessor>(new explore::HotSpotHoldoutFilterProcessor())},
  };

  struct Filter {
    std::string name;
    bool enable = false;
    const base::Json *config = nullptr;
    std::shared_ptr<explore::BaseFilterProcessor> processor;
    std::string save_pid_list_attr;
    std::vector<int64> pid_vec;
  };

  struct FilterCounter {
    int total_num = 0;
    int pic_num = 0;
  };

  std::string user_info_ptr_attr_;
  std::vector<Filter> filter_vec_;
  folly::F14FastMap<int, int> truncation_map_;
  std::vector<CommonRecoResult> new_result_vec_;
  folly::F14FastMap<int, int> reason_num_map_;
  folly::F14FastMap<int, folly::F14FastMap<int, FilterCounter>> filter_num_map_;
  const base::Json *item_attr_map_config_ = nullptr;
  folly::F14FastMap<std::string, const ItemAttr *> item_accessor_map_;

  DISALLOW_COPY_AND_ASSIGN(ExploreRetrievalFilterArranger);
};

}  // namespace platform
}  // namespace ks
