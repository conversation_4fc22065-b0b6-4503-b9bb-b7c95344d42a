#include "dragon/src/processor/ext/explore/arranger/explore_control_hetu_count_arranger.h"

#include <algorithm>
#include <unordered_set>
#include <vector>
#include <map>

namespace ks {
namespace platform {

RecoResultIter ExploreControlHetuCountArranger::Arrange(
                                      MutableRecoContextInterface *context,
                                      RecoResultIter begin, RecoResultIter end) {
  // 获取多样性打散和兴趣调节开关
  bool enable_hetu_control_interest =
      GetBoolProcessorParameter(context, "enable_hetu_control_interest", false);
  bool enable_hetu_control_diversity =
      GetBoolProcessorParameter(context, "enable_hetu_control_diversity", false);
  enable_hetu_control_diversity_none_hetu_ =
      GetBoolProcessorParameter(context, "enable_hetu_control_diversity_none_hetu", false);
  bool enable_duration_control_diversity =
      GetBoolProcessorParameter(context, "enable_duration_control_diversity", false);
  bool enable_author_control_diversity =
      GetBoolProcessorParameter(context, "enable_author_control_diversity", false);
  bool enable_cluster_id_control_diversity =
      GetBoolProcessorParameter(context, "enable_cluster_id_control_diversity", false);
  enable_hetu_diversity_control_ =
      GetBoolProcessorParameter(context, "enable_hetu_diversity_control", false);
  enable_actual_hetu_control_ =
      GetBoolProcessorParameter(context, "enable_actual_hetu_control", false);
  enable_dynamic_hetu_control_diversity_ =
      GetBoolProcessorParameter(context, "enable_dynamic_hetu_control_diversity", false);
  // 将未挂载垂类视频整体当做一个垂类
  enable_dynamic_hetu_control_diversity_v2_ =
      GetBoolProcessorParameter(context, "enable_dynamic_hetu_control_diversity_v2", false);
  hetu_adjust_coef_ = GetDoubleProcessorParameter(context, "hetu_adjust_coef", 1.0);
  hetu_adjust_min_value_ = GetDoubleProcessorParameter(context, "hetu_adjust_min_value", 0.5);
  hetu_adjust_max_value_ = GetDoubleProcessorParameter(context, "hetu_adjust_max_value", 2.0);

  const int keep_size = GetIntProcessorParameter(context, "keep_size", -1);
  if (keep_size <= 0) {
    CL_LOG(ERROR) << "fail to ExploreControlHetuCountArranger, keep size must greater than 0!";
    return end;
  }

  // 获取各调控策略的 range start
  const int hetu_control_interest_start =
           GetIntProcessorParameter(context, "hetu_control_interest_start", 0);
  const int hetu_control_diversity_start =
           GetIntProcessorParameter(context, "hetu_control_diversity_start", 0);
  const int duration_control_diversity_start =
           GetIntProcessorParameter(context, "duration_control_diversity_start", 0);
  const int author_control_diversity_start =
           GetIntProcessorParameter(context, "author_control_diversity_start", 0);
  const int cluster_id_control_diversity_start =
           GetIntProcessorParameter(context, "cluster_id_control_diversity_start", 0);
  double smooth =
          GetDoubleProcessorParameter(context, "diversity_control_smooth", 1.0);
  double alpha =
          GetDoubleProcessorParameter(context, "diversity_control_alpha", 1.0);

  // 根据兴趣分布计算目标河图配额
  target_hetu_quota_map_.clear();
  if (enable_hetu_control_interest) {
    const auto* user_hetu_stat_ptr =
        context->GetPtrCommonAttr<folly::F14FastMap<int, double>>(user_hetu_stat_attr_);
    // 存在因为统计不置信就没有设置该字段的正常情况
    if (user_hetu_stat_ptr) {
      for (auto iter = user_hetu_stat_ptr->begin(); iter != user_hetu_stat_ptr->end(); ++iter) {
        target_hetu_quota_map_[iter->first] = static_cast<int>(iter->second * keep_size);
      }
    } else {
      enable_hetu_control_interest = false;
    }
  }
  // 根据用户历史浏览分布调整 hetu 配额
  target_hetu_diversity_rate_map_.clear();
  if (enable_hetu_diversity_control_) {
    const auto* user_hetu_distribution_ptr =
        context->GetPtrCommonAttr<folly::F14FastMap<int, double>>(user_hetu_distribution_attr_);
    if (user_hetu_distribution_ptr) {
      for (auto iter = user_hetu_distribution_ptr->begin();
          iter != user_hetu_distribution_ptr->end(); ++iter) {
        target_hetu_diversity_rate_map_[iter->first] = pow(smooth / (smooth + iter->second), alpha);
      }
    } else {
      enable_hetu_diversity_control_ = false;
    }
  }
  // 根据用户真实后验兴趣调整 hetu 配额
  target_hetu_actual_rate_map_.clear();
  if (enable_actual_hetu_control_) {
    const auto* user_actual_distribution_ptr =
        context->GetPtrCommonAttr<folly::F14FastMap<int, double>>(user_actual_distribution_attr_);
    if (user_actual_distribution_ptr) {
      for (auto iter = user_actual_distribution_ptr->begin();
          iter != user_actual_distribution_ptr->end(); ++iter) {
        target_hetu_actual_rate_map_[iter->first] =
          pow(std::min(std::max(hetu_adjust_min_value_, iter->second), hetu_adjust_max_value_),
              hetu_adjust_coef_);
      }
    } else {
      enable_actual_hetu_control_ = false;
    }
  }
  // 根据 hetu 打散规则限制目标河图配额
  target_diversity_quota_map_.clear();
  if (enable_hetu_control_diversity) {
    hetu1_max_size_ = GetIntProcessorParameter(context, "hetu1_max_size", keep_size);
    hetu2_max_size_ = GetIntProcessorParameter(context, "hetu2_max_size", keep_size);
    hetu5_max_size_ = GetIntProcessorParameter(context, "hetu5_max_size", keep_size);
    none_hetu1_max_size_ = GetIntProcessorParameter(context, "none_hetu1_max_size", keep_size);
    none_hetu2_max_size_ = GetIntProcessorParameter(context, "none_hetu2_max_size", keep_size);
    none_hetu5_max_size_ = GetIntProcessorParameter(context, "none_hetu5_max_size", keep_size);
  }
  // 根据候选动态计算配额
  dynamic_hetu_diversity_quota_map_.clear();
  if (enable_dynamic_hetu_control_diversity_) {
    CalcDynamicHetuControlDiversityQuota(context, begin, end, keep_size);
  }
  // 根据 duration 打散规则限制配额
  target_duration_quota_map_.clear();
  if (enable_duration_control_diversity) {
    duration_0_7s_max_size_ = GetIntProcessorParameter(context, "duration_0_7s_max_size", keep_size);
    duration_7_9s_max_size_ = GetIntProcessorParameter(context, "duration_7_9s_max_size", keep_size);
    duration_9_12s_max_size_ = GetIntProcessorParameter(context, "duration_9_12s_max_size", keep_size);
    duration_12_17s_max_size_ = GetIntProcessorParameter(context, "duration_12_17s_max_size", keep_size);
    duration_17_20s_max_size_ = GetIntProcessorParameter(context, "duration_17_20s_max_size", keep_size);
    duration_20_58s_max_size_ = GetIntProcessorParameter(context, "duration_20_58s_max_size", keep_size);
    duration_58_120s_max_size_ = GetIntProcessorParameter(context, "duration_58_120s_max_size", keep_size);
    duration_120_300s_max_size_ = GetIntProcessorParameter(context, "duration_120_300s_max_size", keep_size);
    duration_300_400s_max_size_ = GetIntProcessorParameter(context, "duration_300_400s_max_size", keep_size);
    duration_400s_inf_max_size_ = GetIntProcessorParameter(context, "duration_400s_inf_max_size", keep_size);
  }

  // 根据作者打散规则限制配额
  target_same_author_quota_map_.clear();
  if (enable_author_control_diversity) {
    same_author_max_size_ = GetIntProcessorParameter(context, "same_author_max_size", keep_size);
  }

  // 根据 cid 打散规则限制配额
  target_cluster_id_diversity_quota_map_.clear();
  if (enable_cluster_id_control_diversity) {
    cluster_id_max_size_ = GetIntProcessorParameter(context, "cluster_id_max_size", keep_size);
  }

  folly::F14FastSet<uint64> keep_items;
  std::vector<uint64> candidate_items;
  candidate_items.reserve(std::distance(begin, end));
  hetu_level_one_accessor_ = context->GetItemAttrAccessor(hetu_level_one_attr_);
  hetu_level_two_accessor_ = context->GetItemAttrAccessor(hetu_level_two_attr_);
  hetu_level_five_accessor_ = context->GetItemAttrAccessor(hetu_level_five_attr_);
  duration_ms_accessor_ = context->GetItemAttrAccessor(duration_ms_attr_);
  author_accessor_ = context->GetItemAttrAccessor(author_attr_);
  cluster_id_accessor_ = context->GetItemAttrAccessor(cluster_id_attr_);

  int index = 0;

  for (auto iter = begin; iter != end; ++iter) {
    uint64 item_key = iter->item_key;
    index++;
    interest_keys_.clear();
    diversity_keys_.clear();
    duration_keys_.clear();
    same_author_keys_.clear();
    cluster_id_keys_.clear();

    // 兴趣配额调整
    if (enable_hetu_control_interest && index > hetu_control_interest_start) {
      if (!ControlInterest(context, iter)) {
        candidate_items.emplace_back(item_key);
        continue;
      }
    }

    // 时长配额调整
    if (enable_duration_control_diversity && index > duration_control_diversity_start) {
      if (!ControlDuration(context, iter)) {
        candidate_items.emplace_back(item_key);
        continue;
      }
    }

    // 多样性配额调整
    if (enable_hetu_control_diversity && index > hetu_control_diversity_start) {
      if (!ControlDiversity(context, iter)) {
        candidate_items.emplace_back(item_key);
        continue;
      }
    }

    // 作者配额调整
    if (enable_author_control_diversity && index > author_control_diversity_start) {
      if (!ControlSameAuthor(context, iter)) {
        candidate_items.emplace_back(item_key);
        continue;
      }
    }

    // cid 配额调整
    if (enable_cluster_id_control_diversity && index > cluster_id_control_diversity_start) {
      if (!ControlClusterIdDiversity(context, iter)) {
        candidate_items.emplace_back(item_key);
        continue;
      }
    }

    // 当前河图仍有配额 选择
    keep_items.insert(item_key);
    // 减 quota
    for (auto& key : interest_keys_) {
      target_hetu_quota_map_[key]--;
    }
    for (auto& key : duration_keys_) {
      target_duration_quota_map_[key]--;
    }
    for (auto& key : diversity_keys_) {
      target_diversity_quota_map_[key]--;
    }
    for (auto& key : same_author_keys_) {
      target_same_author_quota_map_[key]--;
    }
    for (auto& key : cluster_id_keys_) {
      target_cluster_id_diversity_quota_map_[key]--;
    }

    if (keep_items.size() >= keep_size) {
      break;
    }
  }

  // 根据河图配额挑选不足时 , 从备选队列中依次补足
  if (keep_items.size() < keep_size) {
    int diff_size = std::min(keep_size - keep_items.size(), candidate_items.size());
    for (int i = 0; i < diff_size ; ++i) {
      keep_items.insert(candidate_items[i]);
    }
  }

  auto new_end = std::remove_if(begin, end, [&keep_items](const CommonRecoResult &result) {
    return (keep_items.count(result.item_key) == 0);
  });
  return new_end;
}

bool ExploreControlHetuCountArranger::ControlInterest(MutableRecoContextInterface *context,
                                                      RecoResultIter iter) {
  if (hetu_level_one_accessor_) {
    auto hetu_level_one_list = context->GetIntListItemAttr(*iter, hetu_level_one_accessor_);
    if (hetu_level_one_list && hetu_level_one_list->size() > 0) {
      int hetu1 = hetu_level_one_list->at(0);
      auto quota_iter = target_hetu_quota_map_.find(hetu1);
      if (quota_iter != target_hetu_quota_map_.end() && quota_iter->second > 0) {
        interest_keys_.emplace_back(hetu1);
      } else {
        return false;
      }
    }
  }
  return true;
}

bool ExploreControlHetuCountArranger::ControlSameAuthor(MutableRecoContextInterface *context,
                                                      RecoResultIter iter) {
  if (author_accessor_) {
    auto author_ptr = context->GetIntItemAttr(*iter, author_accessor_);
    if (author_ptr) {
      auto aid = *author_ptr;
      target_same_author_quota_map_.insert({aid, same_author_max_size_});
      if (target_same_author_quota_map_[aid] > 0) {
        same_author_keys_.emplace_back(aid);
      } else {
        return false;
      }
    }
  }
  return true;
}

bool ExploreControlHetuCountArranger::ControlClusterIdDiversity(MutableRecoContextInterface *context,
                                                      RecoResultIter iter) {
  if (cluster_id_accessor_) {
    auto hetu_cluster_id = context->GetIntItemAttr(*iter, cluster_id_accessor_);
    if (hetu_cluster_id) {
      target_cluster_id_diversity_quota_map_.insert({*hetu_cluster_id, cluster_id_max_size_});
      if (target_cluster_id_diversity_quota_map_[*hetu_cluster_id] > 0) {
        cluster_id_keys_.emplace_back(*hetu_cluster_id);
      } else {
        return false;
      }
    }
  }
  return true;
}

bool ExploreControlHetuCountArranger::ControlDuration(MutableRecoContextInterface *context,
                                                      RecoResultIter iter) {
  if (duration_ms_accessor_) {
    auto duration_ms_ptr = context->GetIntItemAttr(*iter, duration_ms_accessor_);
    if (duration_ms_ptr) {
      int duration_ms = *duration_ms_ptr;
      int bucket = 0, bucket_max = 0;
      if (duration_ms < 7000) {
        bucket = 7;
        bucket_max = duration_0_7s_max_size_;
      } else if (duration_ms < 9000) {
        bucket = 9;
        bucket_max = duration_7_9s_max_size_;
      } else if (duration_ms < 12000) {
        bucket = 12;
        bucket_max = duration_9_12s_max_size_;
      } else if (duration_ms < 17000) {
        bucket = 17;
        bucket_max = duration_12_17s_max_size_;
      } else if (duration_ms < 20000) {
        bucket = 20;
        bucket_max = duration_17_20s_max_size_;
      } else if (duration_ms < 58000) {
        bucket = 58;
        bucket_max = duration_20_58s_max_size_;
      } else if (duration_ms < 120000) {
        bucket = 120;
        bucket_max = duration_58_120s_max_size_;
      } else if (duration_ms < 300000) {
        bucket = 300;
        bucket_max = duration_120_300s_max_size_;
      } else if (duration_ms < 400000) {
        bucket = 400;
        bucket_max = duration_300_400s_max_size_;
      } else if (duration_ms >= 400000) {
        bucket = 1000000;
        bucket_max = duration_400s_inf_max_size_;
      } else {
        return true;
      }
      target_duration_quota_map_.insert({bucket, bucket_max});
      if (target_duration_quota_map_[bucket] > 0) {
        duration_keys_.emplace_back(bucket);
      } else {
        return false;
      }
    }
  }
  return true;
}

bool ExploreControlHetuCountArranger::ControlDiversity(MutableRecoContextInterface *context,
                                                       RecoResultIter iter) {
  if (!enable_dynamic_hetu_control_diversity_v2_) {
    // ControlDiversityHetuList 函数实现不合理，不应该把 quota 的计算放到遍历中
    // hetu1
    if (hetu_level_one_accessor_) {
      if (!ControlDiversityHetuList(context, iter, hetu_level_one_accessor_,
          hetu1_max_size_, -1, none_hetu1_max_size_)) {
        return false;
      }
    }
    // hetu2
    if (hetu_level_two_accessor_) {
      if (!ControlDiversityHetuList(context, iter, hetu_level_two_accessor_,
          hetu2_max_size_, -2, none_hetu2_max_size_)) {
        return false;
      }
    }
    // hetu5
    if (hetu_level_five_accessor_) {
      if (!ControlDiversityHetuList(context, iter, hetu_level_five_accessor_,
          hetu5_max_size_, -5, none_hetu5_max_size_)) {
        return false;
      }
    }
  } else {
    // hetu1
    if (hetu_level_one_accessor_) {
      if (!ControlDiversityHetuListV2(context, iter, hetu_level_one_accessor_, -1)) {
        return false;
      }
    }
    // hetu2
    if (hetu_level_two_accessor_) {
      if (!ControlDiversityHetuListV2(context, iter, hetu_level_two_accessor_, -2)) {
        return false;
      }
    }
  }
  return true;
}

bool ExploreControlHetuCountArranger::ControlDiversityHetuListV2(
    MutableRecoContextInterface *context, RecoResultIter iter,
    const ItemAttr *hetu_level_accessor, int default_tag) {
  auto hetu_list = context->GetIntListItemAttr(*iter, hetu_level_accessor);
  int hetu = hetu_list && hetu_list->size() > 0? hetu_list->at(0): default_tag;
  if (target_diversity_quota_map_[hetu] > 0) {
    diversity_keys_.emplace_back(hetu);
  } else {
    return false;
  }
  return true;
}

bool ExploreControlHetuCountArranger::ControlDiversityHetuList(
            MutableRecoContextInterface *context,
            RecoResultIter iter,
            const ItemAttr *hetu_level_accessor,
            int hetu_max_size, int default_tag, int none_hetu_max_size) {
  auto hetu_level_list = context->GetIntListItemAttr(*iter, hetu_level_accessor);
  if (hetu_level_list && hetu_level_list->size() > 0) {
    for (const auto& hetu : *hetu_level_list) {
      auto quota_iter_diversity = target_diversity_quota_map_.find(hetu);
      if (enable_dynamic_hetu_control_diversity_ && dynamic_hetu_diversity_quota_map_.count(hetu) > 0) {
        hetu_max_size = dynamic_hetu_diversity_quota_map_[hetu];
      }
      if (enable_actual_hetu_control_) {
        if (target_hetu_actual_rate_map_.find(hetu) !=
            target_hetu_actual_rate_map_.end() &&
            target_hetu_actual_rate_map_[hetu] > 1e-9) {
          hetu_max_size =
            static_cast<int>(hetu_max_size * target_hetu_actual_rate_map_[hetu]);
        }
      }
      if (enable_hetu_diversity_control_) {
        if (target_hetu_diversity_rate_map_.find(hetu) !=
            target_hetu_diversity_rate_map_.end() &&
            target_hetu_diversity_rate_map_[hetu] > 1e-9) {
          hetu_max_size =
            static_cast<int>(hetu_max_size / target_hetu_diversity_rate_map_[hetu]);
        }
      }
      target_diversity_quota_map_.insert({hetu, hetu_max_size});
      if (target_diversity_quota_map_[hetu] > 0) {
        diversity_keys_.emplace_back(hetu);
      } else {
        return false;
      }
    }
  } else if (enable_hetu_control_diversity_none_hetu_) {
    target_diversity_quota_map_.insert({default_tag, none_hetu_max_size});
    if (target_diversity_quota_map_[default_tag] > 0) {
      diversity_keys_.emplace_back(default_tag);
    } else {
      return false;
    }
  }
  return true;
}

void ExploreControlHetuCountArranger::CalcDynamicHetuControlDiversityQuota(
    MutableRecoContextInterface *context, RecoResultIter begin, RecoResultIter end, int keep_size) {
  double dynamic_hetu_control_diversity_coeff =
      GetDoubleProcessorParameter(context, "dynamic_hetu_control_diversity_coeff", 1.0);
  bool enable_dynamic_hetu_control_diversity_level_one =
      GetBoolProcessorParameter(context, "enable_dynamic_hetu_control_diversity_level_one", false);
  bool enable_dynamic_hetu_control_diversity_level_two =
      GetBoolProcessorParameter(context, "enable_dynamic_hetu_control_diversity_level_two", false);
  bool enable_adjust_quota_by_avg_reward_coeff =
      GetBoolProcessorParameter(context, "enable_adjust_quota_by_avg_reward_coeff", false);
  // quota 动态归一化
  bool enable_dynamic_hetu_control_diversity_normal =
      GetBoolProcessorParameter(context, "enable_dynamic_hetu_control_diversity_normal", false);
  const auto* avg_reward_coeff_hetu_stat_ptr =
        context->GetPtrCommonAttr<folly::F14FastMap<int, double>>(avg_reward_coeff_hetu_stat_attr_);
  for (auto iter = begin; iter != end; ++iter) {
    if (enable_dynamic_hetu_control_diversity_level_one) {
      auto hetu_list = context->GetIntListItemAttr(*iter, hetu_level_one_accessor_);
      int hetu_one = hetu_list && hetu_list->size() > 0? hetu_list->at(0): -1;
      if (dynamic_hetu_diversity_quota_map_.count(hetu_one) == 0) {
        dynamic_hetu_diversity_quota_map_[hetu_one] = 0;
      }
      dynamic_hetu_diversity_quota_map_[hetu_one] += 1;
    }
    if (enable_dynamic_hetu_control_diversity_level_two) {
      auto hetu_list = context->GetIntListItemAttr(*iter, hetu_level_two_accessor_);
      int hetu_two = hetu_list && hetu_list->size() > 0? hetu_list->at(0): -2;
      if (dynamic_hetu_diversity_quota_map_.count(hetu_two) == 0) {
        dynamic_hetu_diversity_quota_map_[hetu_two] = 0;
      }
      dynamic_hetu_diversity_quota_map_[hetu_two] += 1;
    }
  }
  int total = end - begin;
  double base_quota_coeff = 1.0;
  if (total > 0) {
    base_quota_coeff = dynamic_hetu_control_diversity_coeff * keep_size / total;
  }
  for (auto kv : dynamic_hetu_diversity_quota_map_) {
    double avg_reward_coeff = 1.0;
    if (enable_adjust_quota_by_avg_reward_coeff && avg_reward_coeff_hetu_stat_ptr) {
      if (avg_reward_coeff_hetu_stat_ptr->find(kv.first) != avg_reward_coeff_hetu_stat_ptr->end()) {
        avg_reward_coeff = avg_reward_coeff_hetu_stat_ptr->at(kv.first);
      }
    }
    dynamic_hetu_diversity_quota_map_[kv.first] =
      static_cast<int>(kv.second * base_quota_coeff * avg_reward_coeff) + 1;
    if (enable_dynamic_hetu_control_diversity_v2_) {
      target_diversity_quota_map_[kv.first] = dynamic_hetu_diversity_quota_map_[kv.first];
    }
  }
  if (enable_dynamic_hetu_control_diversity_normal) {
    DynamicHetuControlDiversityNormal(keep_size);
  }
}

void ExploreControlHetuCountArranger::DynamicHetuControlDiversityNormal(int keep_size) {
  int count_total_level1 = 0;
  int count_total_level2 = 0;
  for (auto& kv : dynamic_hetu_diversity_quota_map_) {
    if (kv.first <= 100) {
      // 河图一级
      count_total_level1 += kv.second;
    } else {
      // 河图二级
      count_total_level2 += kv.second;
    }
  }
  for (auto& kv : dynamic_hetu_diversity_quota_map_) {
    if (kv.first <= 100 && count_total_level1 > 0) {
      // 河图一级
      kv.second = static_cast<int>(1.0 * kv.second * keep_size / count_total_level1);
    } else if (kv.first <= 1000 && count_total_level2 > 0) {
      // 河图二级
      kv.second = static_cast<int>(1.0 * kv.second * keep_size / count_total_level2);
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, ExploreControlHetuCountArranger, ExploreControlHetuCountArranger)

}  // namespace platform
}  // namespace ks
