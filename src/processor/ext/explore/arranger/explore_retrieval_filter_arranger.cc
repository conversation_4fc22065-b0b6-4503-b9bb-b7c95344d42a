#include "dragon/src/processor/ext/explore/arranger/explore_retrieval_filter_arranger.h"
#include "dragon/src/processor/ext/explore/util/explore_util.h"

#include <algorithm>
#include <utility>

#include "ks/reco_proto/proto/reco_base.pb.h"
#include "ks/reco_pub/reco/util/photo_info_util.h"

namespace ks {
namespace platform {

RecoResultIter ExploreRetrievalFilterArranger::Arrange(
    MutableRecoContextInterface *context, RecoResultIter begin, RecoResultIter end) {
  if (item_accessor_map_.empty()) {
    for (const auto &pair : item_attr_map_config_->objects()) {
      std::string attr = pair.second->StringValue();
      if (!attr.empty()) {
        item_accessor_map_[pair.first] = context->GetItemAttrAccessor(attr);
      }
    }
  }

  if (truncation_map_.empty()) {
    InitTruncationMap(context);
  }

  explore::CommonPhotoInfo common_photo_info(item_accessor_map_);
  const auto *user_info = context->GetProtoMessagePtrCommonAttr<ks::reco::UserInfo>(user_info_ptr_attr_);
  for (auto &filter : filter_vec_) {
    filter.enable = GetBoolProcessorParameter(context, (filter.config)->Get("enable"));
    if (filter.enable) {
      (filter.processor)->Init(context, user_info, filter.config);
      (filter.processor)->SetCommonPhotoInfo(&common_photo_info);
      if (!filter.save_pid_list_attr.empty()) {
        filter.pid_vec.reserve(512);
      }
    }
  }

  new_result_vec_.clear();
  reason_num_map_.clear();
  filter_num_map_.clear();
  std::for_each(begin, end, [this, context, &common_photo_info](const CommonRecoResult &result) {
    common_photo_info.ResetPhoto(context, result);
    int threshold = truncation_map_.at(0);
    auto iter = truncation_map_.find(result.reason);
    if (iter != truncation_map_.end()) {
      threshold = iter->second;
    }
    if (reason_num_map_[result.reason] >= threshold) {
      context->SetTracebackFilterReason(result, "truncation");
      ++filter_num_map_[-1][result.reason].total_num;
      if (common_photo_info.IsPicture()) {
        ++filter_num_map_[-1][result.reason].pic_num;
      }
      return;
    }

    for (int i = 0; i < filter_vec_.size(); ++i) {
      if (filter_vec_[i].enable && (filter_vec_[i].processor)->NeedFilter(result)) {
        if (!filter_vec_[i].save_pid_list_attr.empty()) {
          filter_vec_[i].pid_vec.push_back(result.GetId());
        }
        context->SetTracebackFilterReason(result, filter_vec_[i].name);
        ++filter_num_map_[i][result.reason].total_num;
        if (common_photo_info.IsPicture()) {
          ++filter_num_map_[i][result.reason].pic_num;
        }
        return;
      }
    }

    ++reason_num_map_[result.reason];
    new_result_vec_.push_back(std::move(result));
  });

  for (int i = 0; i < filter_vec_.size(); ++i) {
    if (filter_vec_[i].enable) {
      if (filter_num_map_.find(i) == filter_num_map_.end()) {
        filter_num_map_.emplace(i, folly::F14FastMap<int, FilterCounter>());
      }
      for (const auto &pair : reason_num_map_) {
        filter_num_map_[i].emplace(pair.first, FilterCounter());
      }
    }
  }

  for (int i = 0; i < filter_vec_.size(); ++i) {
    if (!filter_vec_[i].save_pid_list_attr.empty()) {
      context->SetIntListCommonAttr(filter_vec_[i].save_pid_list_attr, std::move(filter_vec_[i].pid_vec));
    }
  }
  Perf(context->GetRequestType());

  return std::move(new_result_vec_.begin(), new_result_vec_.end(), begin);
}

void ExploreRetrievalFilterArranger::InitTruncationMap(MutableRecoContextInterface *context) {
  static constexpr int DEFAULT_TRUNCATION_NUM = 500;
  truncation_map_.emplace(0, DEFAULT_TRUNCATION_NUM);

  const auto *truncation_map_config = config()->Get("truncation_map");
  if (!truncation_map_config || !truncation_map_config->IsObject()) {
    return;
  }

  for (auto iter = truncation_map_config->object_begin(); iter != truncation_map_config->object_end();
      ++iter) {
    int num = GetIntProcessorParameter(context, iter->second);
    if (num == 0) {
      continue;
    }

    if (iter->first == "default") {
      truncation_map_[0] = num;
      continue;
    }

    int reason;
    if (absl::SimpleAtoi(iter->first, &reason)) {
      truncation_map_.emplace(reason, num);
    }
  }
}

void ExploreRetrievalFilterArranger::Perf(const std::string &request_type) {
  static const std::string NS = "common.leaf";
  static const std::string SUB_TAG = "explore_retrieval_filter";
  static const std::string SUB_TAG_PIC = "explore_retrieval_filter_pic";

  for (const auto &pair : filter_num_map_) {
    std::string reason;
    if (pair.first == -1) {
      reason = "truncation";
    } else {
      reason = "filter_by_" + filter_vec_[pair.first].name;
    }

    int total = 0;
    int pic_total = 0;
    for (const auto &pair2 : pair.second) {
      total += pair2.second.total_num;
      pic_total += pair2.second.pic_num;
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
          pair2.second.total_num, NS, SUB_TAG, ks::platform::GlobalHolder::GetServiceIdentifier(),
          request_type, reason, std::to_string(pair2.first));
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
          pair2.second.pic_num, NS, SUB_TAG_PIC, ks::platform::GlobalHolder::GetServiceIdentifier(),
          request_type, reason, std::to_string(pair2.first));
    }

    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        total, NS, SUB_TAG, ks::platform::GlobalHolder::GetServiceIdentifier(),
        request_type, reason, "all");
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        pic_total, NS, SUB_TAG_PIC, ks::platform::GlobalHolder::GetServiceIdentifier(),
        request_type, reason, "all");
  }
}

// user_self
void ExploreRetrievalFilterArranger::UserSelfFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  user_id_ = context->GetUserId();
  author_id_attr_accessor_ = explore::GetItemAttrAccessor(context, config, "author_id_attr");
}

bool ExploreRetrievalFilterArranger::UserSelfFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (author_id_attr_accessor_) {
    auto author_id = result.GetIntAttr(author_id_attr_accessor_);
    return author_id && *author_id == user_id_;
  }

  return false;
}

// percent_punish
void ExploreRetrievalFilterArranger::PercentPunishFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
}

bool ExploreRetrievalFilterArranger::PercentPunishFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  return false;
}

// break_circle
void ExploreRetrievalFilterArranger::BreakCircleFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  if (user_info && user_info->has_basic_info() && user_info->basic_info().has_age_segment()) {
    age_segment_ = user_info->basic_info().age_segment();
  } else {
    age_segment_ = 0;
  }

  is_break_circle_good_photo_attr_accessor_ =
      explore::GetItemAttrAccessor(context, config, "is_break_circle_good_photo_attr");
}

bool ExploreRetrievalFilterArranger::BreakCircleFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (age_segment_ != 3 && age_segment_ != 4 && is_break_circle_good_photo_attr_accessor_) {
    auto is_break_circle_good_photo = result.GetIntAttr(is_break_circle_good_photo_attr_accessor_);
    return is_break_circle_good_photo && *is_break_circle_good_photo;
  }

  return false;
}

// outdate_news
void ExploreRetrievalFilterArranger::OutdateNewsFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  request_time_ = context->GetRequestTime();
  explore_operation_c_review_level_attr_accessor_ =
      explore::GetItemAttrAccessor(context, config, "explore_operation_c_review_level_attr");
  upload_time_attr_accessor_ = explore::GetItemAttrAccessor(context, config, "upload_time_attr");
}

bool ExploreRetrievalFilterArranger::OutdateNewsFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  static constexpr int64 DAYS_7_MS = 7 * base::Time::kMillisecondsPerDay;
  if (explore_operation_c_review_level_attr_accessor_ && upload_time_attr_accessor_) {
    auto explore_operation_c_review_level =
        result.GetIntAttr(explore_operation_c_review_level_attr_accessor_);
    if (!explore_operation_c_review_level
        || (*explore_operation_c_review_level != 2000001074 && *explore_operation_c_review_level != 345)) {
      return false;
    }

    auto upload_time = result.GetIntAttr(upload_time_attr_accessor_);
    return upload_time && (request_time_ - *upload_time) > DAYS_7_MS;
  }

  return false;
}

// valueable
void ExploreRetrievalFilterArranger::ValueableFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  valuable_author_type_set_.clear();
  std::string attr = config->GetString("valuable_author_type_list_attr");
  if (!attr.empty()) {
    auto list = context->GetStringListCommonAttr(attr);
    if (list) {
      for (auto item : *list) {
        valuable_author_type_set_.insert(std::string(item));
      }
    }
  }

  is_cuckoo_photo_attr_accessor_ = explore::GetItemAttrAccessor(context, config, "is_cuckoo_photo_attr");
  cuckoo_author_type_attr_accessor_ =
      explore::GetItemAttrAccessor(context, config, "cuckoo_author_type_attr");
}

bool ExploreRetrievalFilterArranger::ValueableFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (is_cuckoo_photo_attr_accessor_ && cuckoo_author_type_attr_accessor_) {
    auto is_cuckoo_photo = result.GetIntAttr(is_cuckoo_photo_attr_accessor_);
    if (!is_cuckoo_photo || !*is_cuckoo_photo) {
      return false;
    }

    auto cuckoo_author_type = result.GetStringAttr(cuckoo_author_type_attr_accessor_);
    return cuckoo_author_type
        && valuable_author_type_set_.find(std::string(*cuckoo_author_type))
            == valuable_author_type_set_.end();
  }

  return false;
}

// magic_freq_control
void ExploreRetrievalFilterArranger::MagicFreqControlFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  static constexpr int MAX_REAL_SHOW_COUNT = 40;
  static constexpr absl::string_view MF_PREFIX = "MF_";
  static constexpr absl::string_view FT_PREFIX = "FT_";
  static constexpr absl::string_view KY_PREFIX = "KY_";

  is_magic_kk_freq_control_ = false;
  magic_kk_id_set_.clear();
  magic_face_id_attr_accessor_ = explore::GetItemAttrAccessor(context, config, "magic_face_id_attr");
  kuaishan_id_attr_accessor_ = explore::GetItemAttrAccessor(context, config, "kuaishan_id_attr");
  upload_type_attr_accessor_ = explore::GetItemAttrAccessor(context, config, "upload_type_attr");
  outer_material_id_attr_accessor_ = explore::GetItemAttrAccessor(context, config, "outer_material_id_attr");

  std::string attr = config->GetString("magic_kk_id_list_attr");
  if (!attr.empty()) {
    auto list = context->GetIntListCommonAttr(attr);
    if (list) {
      magic_kk_id_set_.insert(list->begin(), list->end());
    }
  }

  if (magic_kk_id_set_.size() == 0 || !user_info || !user_info->has_user_profile_v1()) {
    return;
  }

  int index = 0;
  for (const auto &item : user_info->user_profile_v1().real_show_list()) {
    if (index >= MAX_REAL_SHOW_COUNT) {
      break;
    }

    if (!item.has_extra_filed() || item.extra_filed().size() == 0) {
      continue;
    }

    std::vector<std::string> fileds = absl::StrSplit(item.extra_filed(), ",", absl::SkipWhitespace());
    for (const std::string &filed : fileds) {
      if (absl::StartsWith(filed, MF_PREFIX)) {
        std::vector<std::string> values = absl::StrSplit(filed.substr(3), "$", absl::SkipWhitespace());
        for (const std::string &value : values) {
          int64 id;
          if (absl::SimpleAtoi(value, &id) && magic_kk_id_set_.find(id) != magic_kk_id_set_.end()) {
            is_magic_kk_freq_control_ = true;
            return;
          }
        }
      }
      if (absl::StartsWith(filed, FT_PREFIX) || absl::StartsWith(filed, KY_PREFIX)) {
        int64 id;
        if (absl::SimpleAtoi(filed.substr(3), &id) && magic_kk_id_set_.find(id) != magic_kk_id_set_.end()) {
          is_magic_kk_freq_control_ = true;
          return;
        }
      }
    }

    ++index;
  }
}

bool ExploreRetrievalFilterArranger::MagicFreqControlFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (!is_magic_kk_freq_control_) {
    return false;
  }

  if (magic_face_id_attr_accessor_) {
    auto magic_face_id = result.GetIntAttr(magic_face_id_attr_accessor_);
    if (magic_face_id && magic_kk_id_set_.find(*magic_face_id) != magic_kk_id_set_.end()) {
      return true;
    }
  }

  if (kuaishan_id_attr_accessor_) {
    auto kuaishan_id = result.GetIntAttr(kuaishan_id_attr_accessor_);
    if (kuaishan_id && magic_kk_id_set_.find(*kuaishan_id) != magic_kk_id_set_.end()) {
      return true;
    }
  }

  if (upload_type_attr_accessor_ && outer_material_id_attr_accessor_) {
    auto upload_type = result.GetIntAttr(upload_type_attr_accessor_);
    if (!upload_type || *upload_type != 16) {
      return false;
    }

    auto outer_material_id_str = result.GetStringAttr(outer_material_id_attr_accessor_);
    int64 outer_material_id;
    if (outer_material_id_str && absl::SimpleAtoi(*outer_material_id_str, &outer_material_id)
        && magic_kk_id_set_.find(outer_material_id) != magic_kk_id_set_.end()) {
      return true;
    }
  }

  return false;
}

// topk_audit_bad
void ExploreRetrievalFilterArranger::TopkAuditBadFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  topk_audit_white_tag_set_.clear();
  topk_audit_black_tag_set_.clear();
  auto white_tag_list = explore::GetIntListCommonAttr(context, config, "topk_audit_white_tag_list_attr");
  if (white_tag_list) {
    topk_audit_white_tag_set_.insert(white_tag_list->begin(), white_tag_list->end());
  }
  auto black_tag_list = explore::GetIntListCommonAttr(context, config, "topk_audit_black_tag_list_attr");
  if (black_tag_list) {
    topk_audit_black_tag_set_.insert(black_tag_list->begin(), black_tag_list->end());
  }
  recall_filter_ = explore::GetIntCommonAttr(
      context, config, "topk_audit_bad_recall_filter_attr").value_or(0);
  use_global_data_ = explore::GetIntCommonAttr(
      context, config, "topk_audit_bad_recall_filter_use_global_attr").value_or(0);
  credible_ques_cnt_ = explore::GetIntCommonAttr(
      context, config, "topk_audit_bad_recall_filter_credible_ques_cnt_attr").value_or(30);
  recall_mode_ = explore::GetIntCommonAttr(
      context, config, "topk_audit_bad_recall_filter_mode_attr").value_or(0);
  ques_info_pos_threshold_ = explore::GetDoubleCommonAttr(
        context, config, "topk_audit_bad_recall_filter_pos_threshold_attr").value_or(1.0);
  ques_info_unsure_threshold_ = explore::GetDoubleCommonAttr(
        context, config, "topk_audit_bad_recall_filter_unsure_threshold_attr").value_or(1.0);
  ques_info_neg_threshold_ = explore::GetDoubleCommonAttr(
        context, config, "topk_audit_bad_recall_filter_neg_threshold_attr").value_or(1.0);
  ques_info_hate_threshold_ = explore::GetDoubleCommonAttr(
        context, config, "topk_audit_bad_recall_filter_hate_threshold_attr").value_or(1.0);
}

bool ExploreRetrievalFilterArranger::TopkAuditBadFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  auto topk_audit_tag = common_photo_info_->GetTopkAuditTag();
  if (topk_audit_tag) {
    if (topk_audit_black_tag_set_.find(*topk_audit_tag) != topk_audit_black_tag_set_.end()) {
      return true;
    }

    if (topk_audit_white_tag_set_.find(*topk_audit_tag) != topk_audit_white_tag_set_.end()) {
      return false;
    }
  }

  auto topk_audit_level = common_photo_info_->GetTopkAuditLevel();
  bool is_audit_bad_fllter = topk_audit_level && *topk_audit_level == 1;
  if (is_audit_bad_fllter && recall_filter_) {  // 自治回捞逻辑
    auto neg_cnt = common_photo_info_->GetExploreQuestionaireInfoNegativeCount().value_or(0);
    auto pos_cnt = common_photo_info_->GetExploreQuestionaireInfoPositiveCount().value_or(0);
    auto unsure_cnt = common_photo_info_->GetExploreQuestionaireInfoUnsureCount().value_or(0);
    const auto explore_hate_cnt = common_photo_info_->GetExploreNegative().value_or(0);
    const auto explore_like_cnt = common_photo_info_->GetExploreLike().value_or(0);
    const auto fountain_hate_cnt = common_photo_info_->GetFountainNegative().value_or(0);
    const auto fountain_like_cnt = common_photo_info_->GetFountainLike().value_or(0);
    if (use_global_data_) {
      neg_cnt += common_photo_info_->GetQuestionaireInfoNegativeCount().value_or(0);
      pos_cnt += common_photo_info_->GetQuestionaireInfoPositiveCount().value_or(0);
      unsure_cnt += common_photo_info_->GetQuestionaireInfoUnsureCount().value_or(0);
    }
    int click_cnt = neg_cnt + pos_cnt + unsure_cnt;
    bool ques_recall = false;
    if (click_cnt > credible_ques_cnt_) {
      double pos_rate = static_cast<double>(pos_cnt) / click_cnt;
      double neg_rate = static_cast<double>(neg_cnt) / click_cnt;
      double unsure_rate = static_cast<double>(unsure_cnt) / click_cnt;
      if (pos_rate > ques_info_pos_threshold_ && neg_rate < ques_info_neg_threshold_
       && unsure_rate < ques_info_unsure_threshold_) {
        ques_recall = true;
      }
    }
    int hate_cnt = explore_hate_cnt + fountain_hate_cnt;
    int like_cnt = explore_like_cnt + fountain_like_cnt;
    bool hate_recall = (static_cast<double>(hate_cnt) / (like_cnt + 1))
      < ques_info_hate_threshold_ ? true : false;
    switch (recall_mode_) {
      case 0: {
        if (ques_recall) {
          is_audit_bad_fllter = false;
        }
        break;
      }
      case 1: {
        if (ques_recall && hate_recall) {
          is_audit_bad_fllter = false;
        }
        break;
      }
      case 2: {
        if (ques_recall || hate_recall) {
          is_audit_bad_fllter = false;
        }
        break;
      }
    }
  }

  return is_audit_bad_fllter;
}

// high_hot_audit_bad
void ExploreRetrievalFilterArranger::HighHotAuditBadFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  high_hot_audit_white_tag_set_.clear();
  high_hot_audit_black_tag_set_.clear();
  std::string attr = config->GetString("high_hot_audit_white_tag_list_attr");
  if (!attr.empty()) {
    auto list = context->GetIntListCommonAttr(attr);
    if (list) {
      high_hot_audit_white_tag_set_.insert(list->begin(), list->end());
    }
  }
  attr = config->GetString("high_hot_audit_black_tag_list_attr");
  if (!attr.empty()) {
    auto list = context->GetIntListCommonAttr(attr);
    if (list) {
      high_hot_audit_black_tag_set_.insert(list->begin(), list->end());
    }
  }

  enable_user_sexy_interest_exemption_ = false;
  attr = config->GetString("user_sexy_interest_score_attr");
  double user_sexy_interest_score = -1.0;
  if (!attr.empty()) {
    user_sexy_interest_score = context->GetDoubleCommonAttr(attr).value_or(-1.0);
  }
  attr = config->GetString("user_sexy_interest_score_ignore_threshold_attr");
  double user_sexy_interest_score_threshold = -1.0;
  if (!attr.empty()) {
    user_sexy_interest_score_threshold = context->GetDoubleCommonAttr(attr).value_or(0.0);
  }
  if (user_sexy_interest_score >= 0.0 && user_sexy_interest_score_threshold > 0.0
      && user_sexy_interest_score > user_sexy_interest_score_threshold) {
    enable_user_sexy_interest_exemption_ = true;
  }
  attr = config->GetString("user_sexy_interest_exemption_age_list_attr");
  if (enable_user_sexy_interest_exemption_ && !attr.empty()) {
    int64 age_segment = explore::GetIntCommonAttr(context, config, "user_age_segment_attr").value_or(0);
    auto age_list = context->GetIntListCommonAttr(attr);
    for (int i = 0; i < age_list->size(); ++i) {
      if (age_segment == age_list->at(i)) {
        enable_user_sexy_interest_exemption_ = false;
        break;
      }
    }
  }
  attr = config->GetString("user_sexy_interest_exemption_city_level_list_attr");
  if (enable_user_sexy_interest_exemption_ && !attr.empty()) {
    auto city_level = explore::GetStringCommonAttr(context, config, "user_city_level_attr").value_or("-");
    auto block_city_level_list = context->GetStringListCommonAttr(attr);
    for (int i = 0; i < block_city_level_list->size(); ++i) {
      if (city_level == block_city_level_list->at(i)) {
        enable_user_sexy_interest_exemption_ = false;
        break;
      }
    }
  }
  user_sexy_interest_exemption_tag_set_.clear();
  enable_user_sexy_interest_white_tag_ = false;
  enable_user_sexy_interest_hate_rate_ = false;
  hate_threshold_ = 0;
  hate_rate_threshold_ = 0.0;
  if (enable_user_sexy_interest_exemption_) {
    attr = config->GetString("user_sexy_interest_exemption_tag_list_attr");
    if (!attr.empty()) {
      auto list = context->GetIntListCommonAttr(attr);
      if (list) {
        user_sexy_interest_exemption_tag_set_.insert(list->begin(), list->end());
      }
    }
    enable_user_sexy_interest_white_tag_ = explore::GetIntCommonAttr(
      context, config, "enable_user_sexy_interest_exemption_high_hot_white_tag_attr").value_or(0) > 0;
    enable_user_sexy_interest_hate_rate_ = explore::GetIntCommonAttr(
      context, config, "enable_user_sexy_interest_exemption_hate_rate_attr").value_or(0) > 0;
    hate_threshold_ = explore::GetIntCommonAttr(
      context, config, "user_sexy_interest_exemption_hate_threshold_attr").value_or(0);
    hate_rate_threshold_ = explore::GetDoubleCommonAttr(
      context, config, "user_sexy_interest_exemption_hate_rate_threshold_attr").value_or(0.0);
  }

  audit_hot_high_tag_level_attr_accessor_ =
      explore::GetItemAttrAccessor(context, config, "audit_hot_high_tag_level_attr");
  explore_operation_c_review_level_attr_accessor_ =
      explore::GetItemAttrAccessor(context, config, "explore_operation_c_review_level_attr");
}

bool ExploreRetrievalFilterArranger::HighHotAuditBadFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  bool should_ignore = false;
  if (explore_operation_c_review_level_attr_accessor_) {
    auto explore_operation_c_review_level =
        result.GetIntAttr(explore_operation_c_review_level_attr_accessor_);
    if (explore_operation_c_review_level) {
      if (high_hot_audit_black_tag_set_.find(*explore_operation_c_review_level)
          != high_hot_audit_black_tag_set_.end()) {
        return true;
      }

      if (high_hot_audit_white_tag_set_.find(*explore_operation_c_review_level)
          != high_hot_audit_white_tag_set_.end()) {
        return false;
      }
    }
    if (explore_operation_c_review_level == HIGH_HOT_IMPROPER_GUIDING) {
      should_ignore = true;
    }
  }

  if (enable_user_sexy_interest_exemption_) {
    const auto audit_b_second_tag = common_photo_info_->GetAuditBSecondTag();
    bool hate_rate_ignore = true;
    if (enable_user_sexy_interest_hate_rate_) {
      const auto explore_hate_cnt = common_photo_info_->GetExploreNegative().value_or(0);
      const auto explore_like_cnt = common_photo_info_->GetExploreLike().value_or(0);
      const auto fountain_hate_cnt = common_photo_info_->GetFountainNegative().value_or(0);
      const auto fountain_like_cnt = common_photo_info_->GetFountainLike().value_or(0);
      int hate_cnt = explore_hate_cnt + fountain_hate_cnt;
      int like_cnt = explore_like_cnt + fountain_like_cnt;
      bool hate_cnt_legal = hate_cnt >= hate_threshold_;
      bool hate_rate_legal = (static_cast<double>(hate_cnt) / (like_cnt + 1)) >= hate_rate_threshold_;
      hate_rate_ignore = !(hate_cnt_legal && hate_rate_legal);
    }
    if ((enable_user_sexy_interest_white_tag_ && should_ignore)
        && hate_rate_ignore
        && (audit_b_second_tag
            && user_sexy_interest_exemption_tag_set_.find(*audit_b_second_tag)
            != user_sexy_interest_exemption_tag_set_.end())) {
      return false;
    }
  }

  if (audit_hot_high_tag_level_attr_accessor_) {
    auto audit_hot_high_tag_level = result.GetIntAttr(audit_hot_high_tag_level_attr_accessor_);
    return audit_hot_high_tag_level && *audit_hot_high_tag_level == 1;
  }

  return false;
}

// impression_audit_bad
void ExploreRetrievalFilterArranger::ImpressionAuditFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  impression_audit_white_tag_set_.clear();
  impression_audit_black_tag_set_.clear();
  std::string attr = config->GetString("impression_audit_white_tag_list_attr");
  if (!attr.empty()) {
    auto list = context->GetIntListCommonAttr(attr);
    if (list) {
      impression_audit_white_tag_set_.insert(list->begin(), list->end());
    }
  }
  attr = config->GetString("impression_audit_black_tag_list_attr");
  if (!attr.empty()) {
    auto list = context->GetIntListCommonAttr(attr);
    if (list) {
      impression_audit_black_tag_set_.insert(list->begin(), list->end());
    }
  }

  enable_user_sexy_interest_extra_filter_ = false;
  attr = config->GetString("user_sexy_interest_score_attr");
  double user_sexy_interest_score = -1.0;
  if (!attr.empty()) {
    user_sexy_interest_score = context->GetDoubleCommonAttr(attr).value_or(-1.0);
  }
  attr = config->GetString("user_sexy_interest_score_ignore_threshold_attr");
  double user_sexy_interest_score_threshold = -1.0;
  if (!attr.empty()) {
    user_sexy_interest_score_threshold = context->GetDoubleCommonAttr(attr).value_or(0.0);
  }
  if (user_sexy_interest_score >= 0.0 && user_sexy_interest_score_threshold > 0.0
      && user_sexy_interest_score < user_sexy_interest_score_threshold) {
    enable_user_sexy_interest_extra_filter_ = true;
  }
  user_sexy_interest_extra_filter_tag_set_.clear();
  if (enable_user_sexy_interest_extra_filter_) {
    attr = config->GetString("user_sexy_interest_extra_filter_tag_list_attr");
    if (!attr.empty()) {
      auto list = context->GetIntListCommonAttr(attr);
      if (list) {
        user_sexy_interest_extra_filter_tag_set_.insert(list->begin(), list->end());
      }
    }
  }

  level_hot_online_attr_accessor_ = explore::GetItemAttrAccessor(context, config, "level_hot_online_attr");
  audit_b_second_tag_attr_accessor_ =
      explore::GetItemAttrAccessor(context, config, "audit_b_second_tag_attr");
}

bool ExploreRetrievalFilterArranger::ImpressionAuditFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (audit_b_second_tag_attr_accessor_) {
    auto audit_b_second_tag = result.GetIntAttr(audit_b_second_tag_attr_accessor_);
    if (audit_b_second_tag) {
      if (impression_audit_black_tag_set_.find(*audit_b_second_tag)
          != impression_audit_black_tag_set_.end()) {
        return true;
      }

      if (impression_audit_white_tag_set_.find(*audit_b_second_tag)
          != impression_audit_white_tag_set_.end()) {
        return false;
      }
    }
    if (enable_user_sexy_interest_extra_filter_) {
      if (user_sexy_interest_extra_filter_tag_set_.find(*audit_b_second_tag)
          != user_sexy_interest_extra_filter_tag_set_.end()) {
        return true;
      }
    }
  }

  if (level_hot_online_attr_accessor_) {
    auto level_hot_online = result.GetIntAttr(level_hot_online_attr_accessor_);
    return level_hot_online && *level_hot_online == 1;
  }

  return false;
}

// zero_impression_level_hot_good
void ExploreRetrievalFilterArranger::ZeroImpressionAuditFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  explore_zero_play_days_15d_ = explore::GetIntCommonAttr(
      context, config, "explore_zero_play_days_15d_attr").value_or(0);
}

bool ExploreRetrievalFilterArranger::ZeroImpressionAuditFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  auto impression_audit = common_photo_info_->GetImpressionAudit();
  if (explore_zero_play_days_15d_ >= 6) {
    if (impression_audit && (*impression_audit == 5)) {
      return false;
    }
    return true;
  }

  return false;
}

// eyeshot_long_term
void ExploreRetrievalFilterArranger::EyeshotLongTermFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  is_eyeshot_longterm_photo_accessor_ =
      explore::GetItemAttrAccessor(context, config, "is_eyeshot_longterm_photo_attr");
}

bool ExploreRetrievalFilterArranger::EyeshotLongTermFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (is_eyeshot_longterm_photo_accessor_) {
    auto is_eyeshot_longterm_photo = result.GetIntAttr(is_eyeshot_longterm_photo_accessor_);
    return is_eyeshot_longterm_photo && *is_eyeshot_longterm_photo;
  }

  return false;
}

// tnu_extend_index
void ExploreRetrievalFilterArranger::TnuExtendIndexFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  is_tnu_extend_index_photo_accessor_ =
      explore::GetItemAttrAccessor(context, config, "is_tnu_extend_index_photo_attr");
}

bool ExploreRetrievalFilterArranger::TnuExtendIndexFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (is_tnu_extend_index_photo_accessor_) {
    auto is_tnu_extend_index_photo = result.GetIntAttr(is_tnu_extend_index_photo_accessor_);
    return is_tnu_extend_index_photo && *is_tnu_extend_index_photo;
  }

  return false;
}

// high_hot_audit_subdivision_level
void ExploreRetrievalFilterArranger::HighHotAuditSubdivisionLevelFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  audit_hot_high_subdivision_level_accessor_ =
      explore::GetItemAttrAccessor(context, config, "audit_hot_high_subdivision_level_attr");
}

bool ExploreRetrievalFilterArranger::HighHotAuditSubdivisionLevelFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (audit_hot_high_subdivision_level_accessor_) {
    auto audit_hot_high_subdivision_level = result.GetIntAttr(audit_hot_high_subdivision_level_accessor_);
    return audit_hot_high_subdivision_level && *audit_hot_high_subdivision_level == 1;
  }

  return false;
}

// browse_screen_aid
void ExploreRetrievalFilterArranger::BrowseScreenAidFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  browse_screen_aid_set_.clear();
  std::string attr = config->GetString("browse_screen_aid_list_attr");
  if (!attr.empty()) {
    auto list = context->GetIntListCommonAttr(attr);
    if (list) {
      browse_screen_aid_set_.insert(list->begin(), list->end());
    }
  }

  author_id_attr_accessor_ = explore::GetItemAttrAccessor(context, config, "author_id_attr");
}

bool ExploreRetrievalFilterArranger::BrowseScreenAidFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (author_id_attr_accessor_) {
    auto author_id = result.GetIntAttr(author_id_attr_accessor_);
    return author_id && browse_screen_aid_set_.find(*author_id) != browse_screen_aid_set_.end();
  }

  return false;
}

// follow_author
void ExploreRetrievalFilterArranger::FollowAuthorFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  follow_aid_set_.clear();
  if (user_info) {
    for (const auto &item : user_info->follow_list()) {
      if (item.has_user()) {
        follow_aid_set_.insert(item.user().id());
      }
    }
  }

  ignore_exptag_set_.clear();
  std::string attr = config->GetString("follow_author_ignore_exptag_list_attr");
  if (!attr.empty()) {
    auto list = context->GetIntListCommonAttr(attr);
    if (list) {
      ignore_exptag_set_.insert(list->begin(), list->end());
    }
  }
  filter_timegap_ = 30;
  std::string timegap_attr = config->GetString("follow_author_filter_timegap_attr");
  if (!timegap_attr.empty()) {
    auto p = context->GetIntCommonAttr(timegap_attr);
    if (p) {
      filter_timegap_ = *p;
    }
  }
  now_ms_ = base::GetTimestamp() / base::Time::kMicrosecondsPerMillisecond;
  author_id_attr_accessor_ = explore::GetItemAttrAccessor(context, config, "author_id_attr");
  upload_time_attr_accessor_ = explore::GetItemAttrAccessor(context, config, "upload_time_attr");
}

bool ExploreRetrievalFilterArranger::FollowAuthorFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (ignore_exptag_set_.find(result.reason) == ignore_exptag_set_.end()) {
    if (author_id_attr_accessor_) {
      auto author_id = result.GetIntAttr(author_id_attr_accessor_);
      if (follow_aid_set_.find(*author_id) != follow_aid_set_.end()) {
        if (upload_time_attr_accessor_) {
          auto upload_time = result.GetIntAttr(upload_time_attr_accessor_);
          return upload_time && now_ms_ - *upload_time >= filter_timegap_ * base::Time::kMillisecondsPerDay;
        }
      }
    }
  }
  return false;
}

// black_exempt_level_v1_audit
void ExploreRetrievalFilterArranger::BlackExemptLevelV1AuditFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  auto_audit_black_exempt_level_v1_attr_accessor_ =
      explore::GetItemAttrAccessor(context, config, "auto_audit_black_exempt_level_v1_attr");
}

bool ExploreRetrievalFilterArranger::BlackExemptLevelV1AuditFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (common_photo_info_->IsHighHotQualityAudit()) {
    return false;
  }

  if (auto_audit_black_exempt_level_v1_attr_accessor_) {
    auto auto_audit_black_exempt_level_v1 =
        result.GetIntAttr(auto_audit_black_exempt_level_v1_attr_accessor_);
    return auto_audit_black_exempt_level_v1 && *auto_audit_black_exempt_level_v1 == 1;
  }

  return false;
}

// long_term_high_level
void ExploreRetrievalFilterArranger::LongTermHighLevelFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  long_term_high_level_photo_attr_accessor_ =
      explore::GetItemAttrAccessor(context, config, "long_term_high_level_photo_attr");
}

bool ExploreRetrievalFilterArranger::LongTermHighLevelFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (long_term_high_level_photo_attr_accessor_) {
    auto long_term_high_level_photo = result.GetIntAttr(long_term_high_level_photo_attr_accessor_);
    return long_term_high_level_photo && *long_term_high_level_photo;
  }

  return false;
}

// jianguan_risk
void ExploreRetrievalFilterArranger::JianguanRiskFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  is_jianguan_risk_photo_attr_accessor_ =
      explore::GetItemAttrAccessor(context, config, "is_jianguan_risk_photo_attr");
}

bool ExploreRetrievalFilterArranger::JianguanRiskFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (is_jianguan_risk_photo_attr_accessor_) {
    auto is_jianguan_risk_photo = result.GetIntAttr(is_jianguan_risk_photo_attr_accessor_);
    return is_jianguan_risk_photo && *is_jianguan_risk_photo;
  }

  return false;
}

// black_author
void ExploreRetrievalFilterArranger::BlackAuthorFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  black_author_set_.clear();
  if (user_info) {
    black_author_set_.insert(user_info->black_author_list().begin(), user_info->black_author_list().end());
  }

  author_id_attr_accessor_ = explore::GetItemAttrAccessor(context, config, "author_id_attr");
}

bool ExploreRetrievalFilterArranger::BlackAuthorFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (author_id_attr_accessor_) {
    auto author_id = result.GetIntAttr(author_id_attr_accessor_);
    return author_id && black_author_set_.find(*author_id) != black_author_set_.end();
  }

  return false;
}

// content_dup
void ExploreRetrievalFilterArranger::ContentDupFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  static const std::string COMMON_INDEX_PREFIX = "mmu_content_ids_";
  context_ = context;
  content_type_attr_accessor_list_.clear();
  content_type_attr_accessor_list_for_pic_.clear();
  pid_set_.clear();
  pid_set_for_pic_.clear();

  std::string attr = config->GetString("filter_content_type_list_attr");
  if (!attr.empty()) {
    auto content_type_list = context->GetIntListCommonAttr(attr);
    if (content_type_list) {
      content_type_attr_accessor_list_.reserve(content_type_list->size());
      for (const int64 content_type : *content_type_list) {
        content_type_attr_accessor_list_.push_back(
            context->GetItemAttrAccessor(COMMON_INDEX_PREFIX + std::to_string(content_type)));
      }
    }
  }

  auto content_type_list_for_pic = explore::GetIntListCommonAttr(
    context, config, "filter_content_type_list_for_pic_attr");
  if (content_type_list_for_pic) {
    content_type_attr_accessor_list_for_pic_.reserve(content_type_list_for_pic->size());
    for (const int64 content_type : *content_type_list_for_pic) {
      content_type_attr_accessor_list_for_pic_.push_back(
          context->GetItemAttrAccessor(COMMON_INDEX_PREFIX + std::to_string(content_type)));
    }
  }


  dup_cluster_id_attr_accessor_ = explore::GetItemAttrAccessor(context, config, "dup_cluster_id_attr");
  pic_and_selfdup_id_attr_accessor_ =
      explore::GetItemAttrAccessor(context, config, "pic_and_selfdup_id_attr");
  sim_remove_dup_id_attr_accessor_ = explore::GetItemAttrAccessor(context, config, "sim_remove_dup_id_attr");

  skip_high_xtr_dup_filter_ = explore::GetIntCommonAttr(
      context, config, "skip_high_xtr_dup_filter_attr").value_or(skip_high_xtr_dup_filter_);
  skip_high_hot_quality_pic_ = explore::GetIntCommonAttr(
      context, config, "skip_high_hot_quality_pic_attr").value_or(skip_high_hot_quality_pic_);
  explore_skip_high_hot_quality_ = explore::GetIntCommonAttr(
      context, config, "explore_skip_high_hot_quality_attr").value_or(1);
  skip_dup_realshow_threshold_ = explore::GetIntCommonAttr(
      context, config, "skip_dup_realshow_threshold_attr").value_or(skip_dup_realshow_threshold_);
  skip_dup_watchtime_threshold_ = explore::GetIntCommonAttr(
      context, config, "skip_dup_watchtime_threshold_attr").value_or(skip_dup_watchtime_threshold_);
  fvtr_threshold_ = explore::GetDoubleCommonAttr(
      context, config, "skip_dup_fvtr_threshold_attr").value_or(fvtr_threshold_);
  ctr_threshold_ = explore::GetDoubleCommonAttr(
      context, config, "skip_dup_ctr_threshold_attr").value_or(ctr_threshold_);
}

bool ExploreRetrievalFilterArranger::ContentDupFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (common_photo_info_->IsHighHotQualityAudit()) {
    // TODO(yangshuyi03): 实验一个星期，若没有效果做代码清理
    bool skip_high_hot_quality = explore_skip_high_hot_quality_;
    if (!skip_high_hot_quality_pic_ && common_photo_info_->IsPicture()) {
      skip_high_hot_quality = false;
    }
    if (skip_high_hot_quality) {
      return false;
    }
  }

  content_id_vec_.clear();
  for (const auto *attr_accessor : content_type_attr_accessor_list_) {
    auto content_id_list = result.GetIntListAttr(attr_accessor);
    if (content_id_list) {
      content_id_vec_.insert(content_id_vec_.begin(), content_id_list->begin(), content_id_list->end());
    }
  }

  if (common_photo_info_->IsPicture()) {
    content_id_vec_for_pic_.clear();
    for (const auto *attr_accessor : content_type_attr_accessor_list_for_pic_) {
      auto content_id_list = result.GetIntListAttr(attr_accessor);
      if (content_id_list) {
        content_id_vec_for_pic_.insert(content_id_vec_for_pic_.begin(),
          content_id_list->begin(), content_id_list->end());
      }
    }
  }

  if (dup_cluster_id_attr_accessor_) {
    auto id = result.GetIntAttr(dup_cluster_id_attr_accessor_);
    if (id) {
      content_id_vec_.push_back(*id);
    } else {
      content_id_vec_.push_back(result.GetId());
    }
  }
  if (pic_and_selfdup_id_attr_accessor_) {
    auto id = result.GetIntAttr(pic_and_selfdup_id_attr_accessor_);
    if (id) {
      content_id_vec_.push_back(*id);
    }
  }
  if (sim_remove_dup_id_attr_accessor_) {
    auto id = result.GetIntAttr(sim_remove_dup_id_attr_accessor_);
    if (id) {
      content_id_vec_.push_back(*id);
    }
  }

  for (const int64 content_id : content_id_vec_) {
    if ((!SkipFilterAcrossSlide() && context_->InBrowseSet(content_id)) ||
      pid_set_.find(content_id) != pid_set_.end()) {
      return true;
    }
  }

  pid_set_.insert(content_id_vec_.begin(), content_id_vec_.end());

  if (common_photo_info_->IsPicture()) {
    for (const int64 content_id : content_id_vec_for_pic_) {
      if ((!SkipFilterAcrossSlide() && context_->InBrowseSet(content_id)) ||
        pid_set_for_pic_.find(content_id) != pid_set_for_pic_.end()) {
        return true;
      }
    }

    pid_set_for_pic_.insert(content_id_vec_for_pic_.begin(),
      content_id_vec_for_pic_.end());
  }

  return false;
}

bool ExploreRetrievalFilterArranger::ContentDupFilterProcessor::SkipFilterAcrossSlide() {
  // 刷次间是否 skip 去重
  if (!skip_high_xtr_dup_filter_) {
    return false;
  }

  const auto realshow = common_photo_info_->GetExploreRealShow();
  const auto click = common_photo_info_->GetExploreClick();
  const auto long_view = common_photo_info_->GetExploreViewLengthSum();
  const auto duration_ms = common_photo_info_->GetDurationMs();
  double ctr = *realshow > 0 ? *click / (*realshow + 1.0) : 0.0;
  double watch_time = *click > 0 ? *long_view / (*click + 1.0) : 0.0;
  double fvtr = *duration_ms > 0 ? watch_time / *duration_ms : 0.0;

  //  高 xtr skip 去重
  if ((fvtr > fvtr_threshold_ || watch_time > skip_dup_watchtime_threshold_)
    && ctr > ctr_threshold_ && *realshow > skip_dup_realshow_threshold_) {
    return true;
  }
  return false;
}

// follow_browse_set
void ExploreRetrievalFilterArranger::FollowBrowseSetFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  follow_browse_set_.clear();
  if (user_info) {
    follow_browse_set_.insert(
        user_info->follow_browsed_photo_ids().begin(), user_info->follow_browsed_photo_ids().end());
  }
}

bool ExploreRetrievalFilterArranger::FollowBrowseSetFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (common_photo_info_->IsHighHotQualityAudit()) {
    return false;
  }

  return follow_browse_set_.find(result.GetId()) != follow_browse_set_.end();
}

// report_author
void ExploreRetrievalFilterArranger::ReportAuthorFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  report_aid_set_.clear();
  report_hetu_set_.clear();
  enable_report_hetu_short_ = explore::GetIntCommonAttr(context, config
    , "enable_report_hetu_short_attr").value_or(0);
  short_report_hetu_minutes_ = explore::GetIntCommonAttr(context, config
    , "short_report_hetu_minutes_attr").value_or(0);
  long_report_hetu_minutes_ = explore::GetIntCommonAttr(context, config
    , "long_report_hetu_minutes_attr").value_or(0);
  if (user_info && user_info->has_user_profile_v1()) {
    for (const auto &report : user_info->user_profile_v1().report_list()) {
      if (report.has_author_id()) {
        report_aid_set_.insert(report.author_id());
      }
      // report 退场
      if (enable_report_hetu_short_) {
        if (report.page_type() != ks::reco::RecoEnumSummary::EXPLORE_PAGE) {
          continue;
        }
        if (report.has_hetu_tag_level_info()) {
          int64 cur_time_ms = base::GetTimestamp() / base::Time::kMicrosecondsPerMillisecond;
          auto fetch_hate_hetu_tag_set = [&report, cur_time_ms](
              int short_term_minutes,
              const google::protobuf::RepeatedField<uint32> &hetu_tag_list,
              folly::F14FastSet<uint32> *hetu_tag_set) {
            int minutes = short_term_minutes;
            if (cur_time_ms - report.time_ms() >
              minutes * base::Time::kMillisecondsPerMinute) {
              return;
            }
            hetu_tag_set->insert(hetu_tag_list.begin(), hetu_tag_list.end());
          };
          fetch_hate_hetu_tag_set(short_report_hetu_minutes_, report.hetu_tag_level_info().hetu_level_two(),
            &report_hetu_set_);
          fetch_hate_hetu_tag_set(long_report_hetu_minutes_, report.hetu_tag_level_info().hetu_level_three(),
              &report_hetu_set_);
          fetch_hate_hetu_tag_set(
              short_report_hetu_minutes_, report.hetu_tag_level_info().hetu_level_five(), &report_hetu_set_);
          fetch_hate_hetu_tag_set(long_report_hetu_minutes_, report.hetu_tag_level_info().hetu_face_id(),
            &report_hetu_set_);
          fetch_hate_hetu_tag_set(long_report_hetu_minutes_, report.hetu_tag_level_info().hetu_tag(),
            &report_hetu_set_);
        }
      }
    }
  }

  author_id_attr_accessor_ = explore::GetItemAttrAccessor(context, config, "author_id_attr");
}

bool ExploreRetrievalFilterArranger::ReportAuthorFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  auto contain_tag = [](
      absl::optional<absl::Span<const int64>> hetu_tag_list, const folly::F14FastSet<uint32> &hetu_tag_set) {
    if (hetu_tag_list) {
      for (const int64 tag : *hetu_tag_list) {
        if (hetu_tag_set.find(tag) != hetu_tag_set.end()) {
          return true;
        }
      }
    }

    return false;
  };

  if (author_id_attr_accessor_) {
    auto author_id = result.GetIntAttr(author_id_attr_accessor_);
    return (author_id && report_aid_set_.find(*author_id) != report_aid_set_.end()) ||
      (contain_tag(common_photo_info_->GetHetuLevelTwoTagList(), report_hetu_set_)
      || contain_tag(common_photo_info_->GetHetuLevelThreeTagList(), report_hetu_set_)
      || contain_tag(common_photo_info_->GetHetuLevelFiveTagList(), report_hetu_set_)
      || contain_tag(common_photo_info_->GetHetuFaceIdTagList(), report_hetu_set_)
      || contain_tag(common_photo_info_->GetHetuTagList(), report_hetu_set_));
  }

  return false;
}

// commerce_extend_index
void ExploreRetrievalFilterArranger::CommerceExtendIndexFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  is_high_other_photo_accessor_ = explore::GetItemAttrAccessor(context, config, "is_high_other_photo_attr");
}

bool ExploreRetrievalFilterArranger::CommerceExtendIndexFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  auto is_high_other_photo = result.GetIntAttr(is_high_other_photo_accessor_);
  return is_high_other_photo && *is_high_other_photo;
}

// back_fresh_climb
void ExploreRetrievalFilterArranger::BackFreshClimbFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  is_matched_user_ = user_info && user_info->has_reco_fresh_type_bit()
      && (user_info->reco_fresh_type_bit() & 8);
  show_level_a_accessor_ = explore::GetItemAttrAccessor(context, config, "show_level_a_attr");
}

bool ExploreRetrievalFilterArranger::BackFreshClimbFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (!is_matched_user_) {
    return false;
  }

  auto show_level_a = result.GetIntAttr(show_level_a_accessor_);
  return show_level_a && !*show_level_a;
}

// low_porn_report
void ExploreRetrievalFilterArranger::LowPornReportFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  photo_low_report_count_accessor_ =
      explore::GetItemAttrAccessor(context, config, "photo_low_report_count_attr");
  author_low_report_count_accessor_ =
      explore::GetItemAttrAccessor(context, config, "author_low_report_count_attr");
}

bool ExploreRetrievalFilterArranger::LowPornReportFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (common_photo_info_->IsHighHotQualityAudit()) {
    return false;
  }

  auto photo_low_report_count = result.GetIntAttr(photo_low_report_count_accessor_);
  if (photo_low_report_count
      && *photo_low_report_count > common_photo_info_->GetValueByPhotoLevel(1, 8, 1)) {
    return true;
  }

  auto author_low_report_count = result.GetIntAttr(author_low_report_count_accessor_);
  if (author_low_report_count
      && *author_low_report_count > common_photo_info_->GetValueByPhotoLevel(3, 200, 99999)) {
    return true;
  }

  return false;
}

// picture
void ExploreRetrievalFilterArranger::PictureFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  only_filter_long_and_set_ = explore::GetIntCommonAttr(context, config,
                                 "only_filter_picture_long_and_set_attr").value_or(0);
  only_filter_high_value_pic_ = explore::GetIntCommonAttr(context, config,
                                 "only_filter_high_value_pic_attr").value_or(0);
}

bool ExploreRetrievalFilterArranger::PictureFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (only_filter_long_and_set_) {
    return common_photo_info_->IsLongPicture() || common_photo_info_->IsPictureSet();
  }
  if (only_filter_high_value_pic_) {
    return common_photo_info_->GetHighValuePicFlag().value_or(0) == 1;
  }
  return common_photo_info_->IsPicture();
}

// total_report
void ExploreRetrievalFilterArranger::TotalReportFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
}

bool ExploreRetrievalFilterArranger::TotalReportFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (common_photo_info_->IsHighHotQualityAudit()) {
    return false;
  }

  auto photo_total_report_count = common_photo_info_->GetPhotoTotalReportCount();
  if (photo_total_report_count
      && *photo_total_report_count > common_photo_info_->GetValueByPhotoLevel(19, 39, 19)) {
    return true;
  }

  auto author_total_report_count = common_photo_info_->GetAuthorTotalReportCount();
  if (author_total_report_count
      && *author_total_report_count > common_photo_info_->GetValueByPhotoLevel(79, 99999, 99999)) {
    return true;
  }

  return false;
}

// evil_title
void ExploreRetrievalFilterArranger::EvilTitleFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
}

bool ExploreRetrievalFilterArranger::EvilTitleFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (common_photo_info_->IsHighHotQualityAudit()) {
    return false;
  }

  auto title_evil_level = common_photo_info_->GetTitleEvilLevel();
  auto ocr_cover_text_evil_level = common_photo_info_->GetOcrCoverTextEvilLevel();
  return common_photo_info_->GetValueByPhotoLevel(true, false, true)
      && ((title_evil_level && *title_evil_level > 0.9)
      || (ocr_cover_text_evil_level && *ocr_cover_text_evil_level > 0.9));
}

// short term hate
void ExploreRetrievalFilterArranger::ShortTermHateFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  static constexpr int HETU_TAG_1_DOWN = 50000;
  static constexpr int HETU_TAG_1_UP = 88000;
  static constexpr int HETU_TAG_2_DOWN = 500005;
  static constexpr int HETU_TAG_2_UP = 4000006;

  std::string enable_short_hate_l5_filter_attr = config->GetString("enable_short_hate_l5_filter_attr");
  if (!enable_short_hate_l5_filter_attr.empty()) {
    auto p = context->GetIntCommonAttr(enable_short_hate_l5_filter_attr);
    if (p) {
      enable_short_hate_l5_filter_ = *p;
    }
  }
  std::string hetu_tag_l5_minutes_cut_attr = config->GetString("hetu_tag_l5_minutes_cut_attr");
  if (!hetu_tag_l5_minutes_cut_attr.empty()) {
    auto p = context->GetIntCommonAttr(hetu_tag_l5_minutes_cut_attr);
    if (p) {
      hetu_tag_l5_minutes_cut_ = *p;
    }
  }
  std::string enable_long_hate_filter_attr = config->GetString("enable_long_hate_filter_attr");
  if (!enable_long_hate_filter_attr.empty()) {
    auto p = context->GetIntCommonAttr(enable_long_hate_filter_attr);
    if (p) {
      enable_long_hate_filter_ = *p;
    }
  }
  std::string hetu_tag_long_term_minutes_cut_attr = config->GetString("hetu_tag_long_term_minutes_cut_attr");
  if (!hetu_tag_long_term_minutes_cut_attr.empty()) {
    auto p = context->GetIntCommonAttr(hetu_tag_long_term_minutes_cut_attr);
    if (p) {
      hetu_tag_long_term_minutes_cut_ = *p;
    }
  }
  std::string hetu_l2_long_filter_threshold_attr = config->GetString("hetu_l2_long_filter_threshold_attr");
  if (!hetu_l2_long_filter_threshold_attr.empty()) {
    auto p = context->GetIntCommonAttr(hetu_l2_long_filter_threshold_attr);
    if (p) {
      hetu_l2_long_filter_threshold_ = *p;
    }
  }
  std::string hetu_otherl_long_filter_threshold_attr =
    config->GetString("hetu_otherl_long_filter_threshold_attr");
  if (!hetu_otherl_long_filter_threshold_attr.empty()) {
    auto p = context->GetIntCommonAttr(hetu_otherl_long_filter_threshold_attr);
    if (p) {
      hetu_otherl_long_filter_threshold_ = *p;
    }
  }
  std::string hetu_tag_l3_minutes_cut_attr =
    config->GetString("hetu_tag_l3_minutes_cut_attr");
  if (!hetu_tag_l3_minutes_cut_attr.empty()) {
    auto p = context->GetIntCommonAttr(hetu_tag_l3_minutes_cut_attr);
    if (p) {
      hetu_tag_l3_minutes_cut_ = *p;
    }
  }
  enable_hate_author_skip_hetu_filter_ = explore::GetIntCommonAttr(
      context, config, "enable_hate_author_skip_hetu_filter_attr").value_or(0);
  explore_high_hetu_num_threshold_ = explore::GetIntCommonAttr(
      context, config, "high_hetu_num_threshold_attr").value_or(0);
  explore_low_hetu_num_threshold_ = explore::GetIntCommonAttr(
      context, config, "low_hetu_num_threshold_attr").value_or(0);
  longterm_hate_hetu_level2_map_.clear();
  longterm_hate_hetu_level3_map_.clear();
  longterm_hate_hetu_level5_map_.clear();
  longterm_hate_hetu_face_id_map_.clear();
  longterm_hate_hetu_tag_map_.clear();

  hate_hetu_level2_set_.clear();
  hate_hetu_level3_set_.clear();
  hate_hetu_level5_set_.clear();
  hate_hetu_face_id_set_.clear();
  hate_hetu_tag_set_.clear();

  if (user_info && user_info->has_user_profile_v1()) {
    for (const auto &hate_item : user_info->user_profile_v1().hate_list()) {
      if (hate_item.page_type() != ks::reco::RecoEnumSummary::EXPLORE_PAGE
        || hate_item.label().like() || hate_item.label().follow() || hate_item.label().forward()
        || hate_item.label().collect()) {
          continue;
      }

      if (enable_hate_author_skip_hetu_filter_) {
        bool skip = false;
        for (const int reason : hate_item.hate_reason()) {
          if (reason == HATE_REASON_AUTHOR) {
            skip = true;
            break;
          }
        }
        if (skip) {
          continue;
        }
      }

      if (hate_item.has_hetu_tag_level_info()) {
        int64 cur_time_ms = base::GetTimestamp() / base::Time::kMicrosecondsPerMillisecond;

        auto fetch_hate_hetu_tag_set = [&hate_item, cur_time_ms](
            int enable_long_hate,
            int short_term_minutes,
            int long_term_minutes,
            int hate_num_threshold,
            const google::protobuf::RepeatedField<uint32> &hetu_tag_list,
            folly::F14FastSet<uint32> *hetu_tag_set,
            folly::F14FastMap<uint32, uint32> *hetu_tag_map,
            int hetu_num_threshold) {
          if (enable_long_hate > 0 && long_term_minutes > short_term_minutes) {  // 长期 hate
             if (cur_time_ms - hate_item.time_ms() > long_term_minutes * base::Time::kMillisecondsPerMinute
              && hetu_tag_set->size() >= hetu_num_threshold) {
              return;
            }
            for (const auto &tag : hetu_tag_list) {
              (*hetu_tag_map)[tag] += 1;
              if (hetu_tag_set->count(tag) <= 0 && (*hetu_tag_map)[tag] >= hate_num_threshold) {
                hetu_tag_set->insert(tag);
              }
            }
          }

          int minutes = short_term_minutes;
          if (cur_time_ms - hate_item.time_ms() >
            minutes * base::Time::kMillisecondsPerMinute) {  // 短期 hate
            return;
          }

          hetu_tag_set->insert(hetu_tag_list.begin(), hetu_tag_list.end());
        };

        fetch_hate_hetu_tag_set(enable_long_hate_filter_, 5, hetu_tag_long_term_minutes_cut_,
          hetu_l2_long_filter_threshold_, hate_item.hetu_tag_level_info().hetu_level_two(),
          &hate_hetu_level2_set_, &longterm_hate_hetu_level2_map_,
          explore_high_hetu_num_threshold_);
        fetch_hate_hetu_tag_set(enable_long_hate_filter_, hetu_tag_l3_minutes_cut_,
            hetu_tag_long_term_minutes_cut_,
            hetu_otherl_long_filter_threshold_, hate_item.hetu_tag_level_info().hetu_level_three(),
            &hate_hetu_level3_set_, &longterm_hate_hetu_level3_map_,
            explore_low_hetu_num_threshold_);
        if (enable_short_hate_l5_filter_ > 0 && hetu_tag_l5_minutes_cut_ > 0) {
          fetch_hate_hetu_tag_set(enable_long_hate_filter_,
            hetu_tag_l5_minutes_cut_, hetu_tag_long_term_minutes_cut_, hetu_otherl_long_filter_threshold_,
            hate_item.hetu_tag_level_info().hetu_level_five(),
            &hate_hetu_level5_set_, &longterm_hate_hetu_level5_map_,
            explore_high_hetu_num_threshold_);
        }
        fetch_hate_hetu_tag_set(enable_long_hate_filter_, 30,
          hetu_tag_long_term_minutes_cut_, 3, hate_item.hetu_tag_level_info().hetu_face_id(),
          &hate_hetu_face_id_set_, &longterm_hate_hetu_face_id_map_,
          explore_low_hetu_num_threshold_);

        // hetu tag 特殊处理
        if (cur_time_ms - hate_item.time_ms() <= 30 * base::Time::kMillisecondsPerMinute) {  // hetu tag 短期
          for (const uint32 tag : hate_item.hetu_tag_level_info().hetu_tag()) {
            if ((tag >= HETU_TAG_1_DOWN && tag < HETU_TAG_1_UP) ||
              (tag > HETU_TAG_2_DOWN && tag < HETU_TAG_2_UP)) {
              hate_hetu_tag_set_.insert(tag);
            }
          }
        }
        if (enable_long_hate_filter_ > 0 &&
            cur_time_ms - hate_item.time_ms() <=
            hetu_tag_long_term_minutes_cut_ * base::Time::kMillisecondsPerMinute) {
          for (const uint32 tag : hate_item.hetu_tag_level_info().hetu_tag()) {  // hetu tag 长期
            if (hate_hetu_tag_set_.count(tag)) {
              continue;
            }
            if ((tag >= HETU_TAG_1_DOWN && tag < HETU_TAG_1_UP) ||
              (tag > HETU_TAG_2_DOWN && tag < HETU_TAG_2_UP)) {
              longterm_hate_hetu_tag_map_[tag] += 1;
              if (hate_hetu_tag_set_.count(tag) <= 0 &&
                longterm_hate_hetu_tag_map_[tag] >= hetu_otherl_long_filter_threshold_) {
                hate_hetu_tag_set_.insert(tag);
              }
            }
          }
        }
      }
    }
  }
}

bool ExploreRetrievalFilterArranger::ShortTermHateFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  auto contain_tag = [](
      absl::optional<absl::Span<const int64>> hetu_tag_list, const folly::F14FastSet<uint32> &hetu_tag_set) {
    if (hetu_tag_list) {
      for (const int64 tag : *hetu_tag_list) {
        if (hetu_tag_set.find(tag) != hetu_tag_set.end()) {
          return true;
        }
      }
    }

    return false;
  };

  return contain_tag(common_photo_info_->GetHetuLevelTwoTagList(), hate_hetu_level2_set_)
      || contain_tag(common_photo_info_->GetHetuLevelThreeTagList(), hate_hetu_level3_set_)
      || contain_tag(common_photo_info_->GetHetuLevelFiveTagList(), hate_hetu_level5_set_)
      || contain_tag(common_photo_info_->GetHetuFaceIdTagList(), hate_hetu_face_id_set_)
      || contain_tag(common_photo_info_->GetHetuTagList(), hate_hetu_tag_set_);
}

// high emp htr
void ExploreRetrievalFilterArranger::HighPhtrFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  std::string emp_realshow_show_threshold_attr = config->GetString("emp_realshow_show_threshold_attr");
  if (!emp_realshow_show_threshold_attr.empty()) {
    auto p = context->GetIntCommonAttr(emp_realshow_show_threshold_attr);
    if (p) {
      emp_realshow_show_threshold_ = *p;
    }
  }
  std::string emphtr_filter_threshold_attr = config->GetString("emphtr_filter_threshold_attr");
  if (!emphtr_filter_threshold_attr.empty()) {
    auto p = context->GetDoubleCommonAttr(emphtr_filter_threshold_attr);
    if (p) {
      emphtr_filter_threshold_ = *p;
    }
  }
  enable_hate_cost_ = explore::GetIntCommonAttr(
      context, config, "enable_hate_cost_attr").value_or(0);
  ctr_weight_ = explore::GetDoubleCommonAttr(
      context, config, "emphtr_filter_ctr_weight_attr").value_or(0.0);
  ltr_weight_ = explore::GetDoubleCommonAttr(
      context, config, "emphtr_filter_ltr_weight_attr").value_or(0.0);
  wtr_weight_ = explore::GetDoubleCommonAttr(
      context, config, "emphtr_filter_wtr_weight_attr").value_or(0.0);
  ftr_weight_ = explore::GetDoubleCommonAttr(
      context, config, "emphtr_filter_ftr_weight_attr").value_or(0.0);
  cmtr_weight_ = explore::GetDoubleCommonAttr(
      context, config, "emphtr_filter_cmtr_weight_attr").value_or(0.0);
  time_weight_ = explore::GetDoubleCommonAttr(
      context, config, "emphtr_filter_time_weight_attr").value_or(0.0);
  normal_time_weight_ = explore::GetDoubleCommonAttr(
      context, config, "emphtr_filter_normal_time_weight_attr").value_or(0.0);
  report_weight_ = explore::GetDoubleCommonAttr(
      context, config, "emphtr_filter_report_weight_attr").value_or(0.0);
  coeff_max_ = explore::GetDoubleCommonAttr(
      context, config, "emphtr_filter_adpt_threshold_coeff_max_attr").value_or(1.0);
  coeff_min_ = explore::GetDoubleCommonAttr(
      context, config, "emphtr_filter_adpt_threshold_coeff_min_attr").value_or(1.0);
  alpha_ = explore::GetDoubleCommonAttr(
      context, config, "emphtr_filter_adpt_threshold_alpha_attr").value_or(1.0);
  beta_ = explore::GetDoubleCommonAttr(
      context, config, "emphtr_filter_adpt_threshold_beta_attr").value_or(1.0);
  omega_ = explore::GetDoubleCommonAttr(
      context, config, "emphtr_filter_adpt_threshold_omega_attr").value_or(100000.0);
  exp_upper_ = explore::GetDoubleCommonAttr(
      context, config, "emphtr_filter_adpt_threshold_exp_upper_attr").value_or(10.0);
  enable_adpt_threshold_ = explore::GetIntCommonAttr(
      context, config, "enable_adpt_threshold_attr").value_or(0);
  enable_adpt_threshold_by_realshow_ = explore::GetIntCommonAttr(
      context, config, "enable_adpt_threshold_by_realshow_attr").value_or(0);
  auto emphtr_filter_threshold_list = explore::GetDoubleListCommonAttr(
      context, config, "emphtr_filter_threshold_list_attr");
  enable_hate_count_filter_ = explore::GetIntCommonAttr(
      context, config, "enable_hate_count_filter_attr").value_or(0);
  emp_realshow_show_high_threshold_ = explore::GetIntCommonAttr(
      context, config, "emp_realshow_show_high_threshold_attr").value_or(0);
  emp_hate_cnt_filter_threshold_ = explore::GetIntCommonAttr(
      context, config, "emp_hate_cnt_filter_threshold_attr").value_or(0);
  if (emphtr_filter_threshold_list) {
    emphtr_filter_threshold_vec_.insert(emphtr_filter_threshold_vec_.begin(),
        emphtr_filter_threshold_list->begin(), emphtr_filter_threshold_list->end());
  }
}

bool ExploreRetrievalFilterArranger::HighPhtrFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  // emp htr 过滤
  int64 explore_real_show = 0;
  int64 explore_hate = 0;
  int64 explore_hate_and_report = 0;
  double explore_emp_htr = 0.0;
  const auto explore_real_show_ptr = common_photo_info_->GetExploreRealShow();
  const auto explore_hate_ptr = common_photo_info_->GetExploreNegative();

  if (explore_real_show_ptr) {
    explore_real_show = *explore_real_show_ptr;
  }
  if (explore_real_show < emp_realshow_show_threshold_) {
    return false;  // 还未到达曝光阈值
  }
  if (explore_hate_ptr) {
    explore_hate = *explore_hate_ptr;
  }

  if (report_weight_ > 0.0) {
    const auto report = common_photo_info_->GetPhotoTotalReportCount().value_or(0);
    explore_hate_and_report = explore_hate + report_weight_ * report;
  } else {
    explore_hate_and_report = explore_hate;
  }

  if (enable_hate_cost_) {
    const auto click = common_photo_info_->GetExploreClick().value_or(0);
    const auto like = common_photo_info_->GetExploreLike().value_or(0);
    const auto follow = common_photo_info_->GetExploreFollow().value_or(0);
    const auto forward = common_photo_info_->GetExploreForward().value_or(0);
    const auto comment = common_photo_info_->GetExploreComment().value_or(0);
    const auto watch_time = common_photo_info_->GetExploreViewLengthSum().value_or(0.0);
    const auto duration_ms = common_photo_info_->GetDurationMs().value_or(0.0);
    const auto normal_watch_time = duration_ms > 0 ? watch_time / duration_ms : 0.0;
    explore_emp_htr = explore_hate_and_report / (ctr_weight_ * click + ltr_weight_ * like +
      wtr_weight_ * follow + ftr_weight_ * forward + cmtr_weight_ * comment +
      time_weight_ * watch_time + normal_time_weight_ * normal_watch_time + 1.0);
  } else {
    explore_emp_htr = explore_hate_and_report / (explore_real_show + 1.0);
  }
  if (enable_adpt_threshold_) {
    const auto tag_level = common_photo_info_->GetAuditHotHighTagLevel();
    if (tag_level && !emphtr_filter_threshold_vec_.empty()) {
      if (*tag_level >=0 && *tag_level < emphtr_filter_threshold_vec_.size()) {
        emphtr_filter_threshold_ = emphtr_filter_threshold_vec_[*tag_level];
      }
    }
  }
  double adp_weight = 1.0;
  if (enable_adpt_threshold_by_realshow_) {
    double exper = beta_ * (explore_real_show - omega_);
    exper = exper >= 0 ? std::min(exper, exp_upper_) : std::max(exper, -exp_upper_);
    adp_weight = alpha_ / (1 + std::exp(exper));
    adp_weight = std::min(coeff_max_, std::max(coeff_min_, adp_weight));
  }
  if (explore_emp_htr > emphtr_filter_threshold_ * adp_weight) {
    return true;  // emphtr 高于阈值过滤
  }
  if (enable_hate_count_filter_ && explore_real_show > emp_realshow_show_high_threshold_
    && explore_hate_and_report > emp_hate_cnt_filter_threshold_) {
    return true;
  }
  return false;
}

// short pic hetu filter
void ExploreRetrievalFilterArranger::ShortPicHetuFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
      hetu_set_.clear();
      std::string filtered_hetu_tag_list_attr = config->GetString("filtered_hetu_tag_list_attr");
      if (filtered_hetu_tag_list_attr.empty()) {
        return;
      }
      auto hetu_list = context->GetIntListCommonAttr(filtered_hetu_tag_list_attr);
      if (!hetu_list) {
        return;
      }
      hetu_set_.insert(hetu_list->begin(), hetu_list->end());
      return;
}

bool ExploreRetrievalFilterArranger::ShortPicHetuFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
    if (hetu_set_.size() == 0) {
      return false;
    }
    auto upload_type = common_photo_info_->GetUploadType();
    if (upload_type
        && *upload_type != short_pic_upload_type_) {
          return false;
    }

    auto contain_tag = [](
      absl::optional<absl::Span<const int64>> hetu_tag_list, const folly::F14FastSet<uint64> &hetu_tag_set) {
      if (hetu_tag_list) {
        for (const int64 tag : *hetu_tag_list) {
          if (hetu_tag_set.find(tag) != hetu_tag_set.end()) {
            return true;
          }
        }
      }

      return false;
    };

    return !(contain_tag(common_photo_info_->GetHetuLevelTwoTagList(), hetu_set_)
      || contain_tag(common_photo_info_->GetHetuLevelThreeTagList(), hetu_set_)
      || contain_tag(common_photo_info_->GetHetuLevelFourTagList(), hetu_set_));
}

// long pic filter
void ExploreRetrievalFilterArranger::LongPicFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
      long_pic_upload_type_set_.clear();
      std::string upload_type_str_ = config->GetString("filter_long_pic_upload_type");
      auto upload_type_list = context->GetIntListCommonAttr(upload_type_str_);
      if (upload_type_list) {
        long_pic_upload_type_set_.insert(upload_type_list->begin(), upload_type_list->end());
      }

      long_pic_picture_type_set_.clear();
      std::string picture_type_str_ = config->GetString("filter_long_pic_picture_type");
      auto picture_type_list = context->GetIntListCommonAttr(picture_type_str_);
      if (picture_type_list) {
        long_pic_picture_type_set_.insert(picture_type_list->begin(), picture_type_list->end());;
      }
      return;
}

bool ExploreRetrievalFilterArranger::LongPicFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
    auto upload_type = common_photo_info_->GetUploadType();
    if (upload_type
        && long_pic_upload_type_set_.count(*upload_type)) {
          return true;
    }
    auto picture_type = common_photo_info_->GetPictureType();
    if (picture_type
        && long_pic_picture_type_set_.count(*picture_type)) {
          return true;
    }
    return false;
}

// face 90 degree filter
void ExploreRetrievalFilterArranger::Face90DegreeFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  std::string data_key = config->GetString("face_90_degree_pids_data_key");
  pid_set_ = context->GetPtrCommonAttr<folly::F14FastSet<uint64>>(data_key);
}

bool ExploreRetrievalFilterArranger::Face90DegreeFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  return pid_set_ && pid_set_->count(result.GetId()) > 0;
}

// pic wallpaper filter
void ExploreRetrievalFilterArranger::PicWallpaperFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  wallpaper_hetu_set_.clear();
  std::string filter_pic_wallpaper_hetu_tag = config->GetString("filter_pic_wallpaper_hetu_tag");
  if (filter_pic_wallpaper_hetu_tag.empty()) {
    return;
  }
  auto hetu_list = context->GetIntListCommonAttr(filter_pic_wallpaper_hetu_tag);
  if (!hetu_list) {
    return;
  }
  wallpaper_hetu_set_.insert(hetu_list->begin(), hetu_list->end());
  std::string enable_wallpaper_caption_keep_attr =
    config->GetString("enable_pic_wallpaper_filter_caption_keep_attr");
  if (!enable_wallpaper_caption_keep_attr.empty()) {
    enable_wallpaper_caption_keep_ = *context->GetIntCommonAttr(enable_wallpaper_caption_keep_attr);
    if (enable_wallpaper_caption_keep_ > 0) {
      std::string caption_keep_thresh_attr = config->GetString("pic_wallpaper_caption_keep_thresh_attr");
      if (!caption_keep_thresh_attr.empty()) {
        caption_keep_thresh_ = *context->GetIntCommonAttr(caption_keep_thresh_attr);
      }
    }
  }
  return;
}

bool ExploreRetrievalFilterArranger::PicWallpaperFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  // 过滤图片壁纸
  if (wallpaper_hetu_set_.size() == 0) {
    return false;
  }
  if (!common_photo_info_->IsPicture()) {
    return false;
  }
  // 长 caption 放过
  if (enable_wallpaper_caption_keep_ > 0
      && common_photo_info_->GetCaptionLength() >= caption_keep_thresh_) {
    return false;
  }
  if (common_photo_info_->GetHetuTagList()) {
    auto photo_hetu_tag_list = common_photo_info_->GetHetuTagList();
    for (auto hetu_tag : *photo_hetu_tag_list) {
      if (wallpaper_hetu_set_.find(hetu_tag) != wallpaper_hetu_set_.end()) {
        return true;
      }
    }
  }
  return false;
}

// audit hot cover level filter
void ExploreRetrievalFilterArranger::AuditHotCoverLevelFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
}

bool ExploreRetrievalFilterArranger::AuditHotCoverLevelFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  const auto audit_level = common_photo_info_->GetAuditHotCoverLevel();
  static constexpr int64 filter_level = 2023746;
  return audit_level && *audit_level == filter_level;
}

// audit gray cover level filter
void ExploreRetrievalFilterArranger::AuditGrayCoverLevelFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  int64 page = explore::GetIntCommonAttr(context, config, "page_attr").value_or(0);
  int64 enable_tnu_not_cover_photo_filter =
    explore::GetIntCommonAttr(context, config, "enable_tnu_and_reflux_not_cover_photo_filter_attr")
    .value_or(0);
  int64 enable_not_cover_photo_filter =
    explore::GetIntCommonAttr(context, config, "enable_not_cover_photo_filter_attr").value_or(0);
  int64 enable_first_page_not_cover_photo_filter =
    explore::GetIntCommonAttr(context, config, "enable_first_page_not_cover_photo_filter_attr").value_or(0);
  hetu_v3_level_one_white_tag_set_.clear();
  fans_threshold_ =
    explore::GetIntCommonAttr(context, config, "hetu_v3_white_tag_fans_threshold_attr").value_or(0);
  auto hetu_v3_tags =
    explore::GetStringCommonAttr(context, config, "hetu_v3_level_one_white_tag_list_attr").value_or("");
  explore::StringToIntSet(std::string(hetu_v3_tags), ",", &hetu_v3_level_one_white_tag_set_);
  int64 max_page_threshold =
    explore::GetIntCommonAttr(context, config, "max_page_threshold_attr").value_or(1);
  int64 is_first_page = (page >= 1 && page <= max_page_threshold);
  int64 is_tnu_crowd = 0;
  if ((user_info->true_new_user()  // tnu
    || user_info->user_class().new_device_status()  // 当日新
    || user_info->is_growth_reflux()  // 当日回
    || (!user_info->user_class().new_device_status()
      && (user_info->user_class().nr_user_type() & (1 << 11)) > 0)  // 2-14 新
    || (!user_info->is_growth_reflux()
      && (user_info->user_class().nr_user_type() & (1 << 10)) > 0))  // 2-14 回
  ) {
    is_tnu_crowd = 1;
  }
  is_not_cover_filter_ = (enable_not_cover_photo_filter
    || (enable_tnu_not_cover_photo_filter && is_tnu_crowd))
    && (!enable_first_page_not_cover_photo_filter || is_first_page);  //首刷判断
}

bool ExploreRetrievalFilterArranger::AuditGrayCoverLevelFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  const auto audit_level = common_photo_info_->GetAuditHotCoverLevel();
  static constexpr int64 lower_gray_level = 2023742;
  static constexpr int64 upper_gray_level = 2023745;
  if (is_not_cover_filter_ && audit_level && *audit_level <= 0) {  // 未进审核过滤
    const auto hetu_tag_list = common_photo_info_->GetHetuV2LevelOneTagList();
    const auto author_fans = common_photo_info_->GetAuthorFansCount();
    if (hetu_tag_list && author_fans && *author_fans >= fans_threshold_) {
      for (const int64 hetu_tag : *hetu_tag_list) {
        if (hetu_v3_level_one_white_tag_set_.count(reco::PhotoInfoUtils::getTagIdFromHetuTag(hetu_tag))
        ) {
          return false;
        }
      }
    }
    return true;
  }
  return audit_level && *audit_level >= lower_gray_level && *audit_level <= upper_gray_level;
}

// impression audit gray
void ExploreRetrievalFilterArranger::ImpressionAuditGrayFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
}

bool ExploreRetrievalFilterArranger::ImpressionAuditGrayFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  const auto impression_gray = common_photo_info_->GetImpressionAudit();
  const auto audit_b_second_tag = common_photo_info_->GetAuditBSecondTag();

  if (audit_b_second_tag && impression_gray) {
    if (*impression_gray == 2 && *audit_b_second_tag > 0) {
      return true;
    }
  }

  return false;
}

// mmu low cover filter
void ExploreRetrievalFilterArranger::MmuLowCoverFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
      std::string lower_cover_mmu_map_strs_attr = config->GetString("lower_cover_mmu_map_strs_attr");
      std::string skip_beauty_photo_filter_attr = config->GetString("skip_beauty_photo_filter_attr");
      user_gender_attr_ = explore::GetIntCommonAttr(context, config, "user_gender_attr").value_or(0);
      enable_explore_gender_ = explore::GetIntCommonAttr(context, config,
          "enable_explore_gender_attr").value_or(0);
      lower_cover_mmu_map_.clear();
      if ((user_info->true_new_user()  // tnu
        || user_info->user_class().new_device_status()  // 当日新
        || user_info->is_growth_reflux()  // 当日回
        || (!user_info->user_class().new_device_status()
          && (user_info->user_class().nr_user_type() & (1 << 11)) > 0)  // 2-14 新
        || (!user_info->is_growth_reflux()
          && (user_info->user_class().nr_user_type() & (1 << 10)) > 0))  // 2-14 回
        || ((user_gender_attr_ == 2) && (enable_explore_gender_ == 1))) {
          lower_cover_mmu_map_strs_attr = config->GetString("lower_cover_mmu_map_tnu_reflux_strs_attr");
      }

      if (!lower_cover_mmu_map_strs_attr.empty()) {
        auto lower_cover_mmu_map_strs = context->GetStringCommonAttr(lower_cover_mmu_map_strs_attr);
        if (lower_cover_mmu_map_strs) {
          bool parse_flag = explore::StringToMap(std::string(*lower_cover_mmu_map_strs),
                                                 &lower_cover_mmu_map_);
        }
      }
      skip_beauty_photo_filter_ = 0;
      if (!skip_beauty_photo_filter_attr.empty()) {
        auto skip_beauty_photo_filter_ptr = context->GetIntCommonAttr(skip_beauty_photo_filter_attr);
        if (skip_beauty_photo_filter_ptr) {
          skip_beauty_photo_filter_ = *skip_beauty_photo_filter_ptr;
        }
      }
      follow_author_id_set_.clear();
      enable_follow_author_exemption_ = explore::GetIntCommonAttr(context, config,
                                "mmu_enable_follow_author_exemption_attr").value_or(0);
      if (enable_follow_author_exemption_ == 1 && user_info) {
        for (const auto &item : user_info->follow_list()) {
          if (item.has_user()) {
            follow_author_id_set_.insert(item.user().id());
          }
        }
      }
      enable_impression_good_ignore_ = explore::GetIntCommonAttr(context, config,
                                "mmu_enable_impression_good_ignore_attr").value_or(0);
      return;
}

bool ExploreRetrievalFilterArranger::MmuLowCoverFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
    static constexpr int64 fans_threshold = 1000000;
    for (auto iter = lower_cover_mmu_map_.begin(); iter != lower_cover_mmu_map_.end(); iter++) {
      auto mmu_type = iter->first;
      auto mmu_value_threshold = iter->second;
      double mmu_value = 0.0;
      mmu_value = common_photo_info_->GetMmuLowQualityModelScore(mmu_type);
      if (skip_beauty_photo_filter_ && mmu_type == 143 && mmu_value != 0.0) {
        const auto author_fans = common_photo_info_->GetAuthorFansCount();
        const auto hetu_tag_list = common_photo_info_->GetHetuV2LevelOneTagList();
        if (hetu_tag_list) {
          for (const int64 hetu_tag : *hetu_tag_list) {
            if (reco::PhotoInfoUtils::getTagIdFromHetuTag(hetu_tag) == 8
                && author_fans && *author_fans >= fans_threshold) {
              return false;
            }
          }
        }
      }
      if (mmu_type == 164 && mmu_value != 0.0) {
        bool to_ignore = false;
        const auto author_fans = common_photo_info_->GetAuthorFansCount();
        const auto hetu_tag_list = common_photo_info_->GetHetuV2LevelOneTagList();
        if (hetu_tag_list && author_fans && *author_fans >= fans_threshold) {
          for (const int64 hetu_tag : *hetu_tag_list) {
            if (reco::PhotoInfoUtils::getTagIdFromHetuTag(hetu_tag) == 8) {
              to_ignore = true;
              break;
            }
          }
        }
        if (!to_ignore && enable_impression_good_ignore_ == 1) {
          const auto impression_gray = common_photo_info_->GetImpressionAudit();
          const auto audit_b_second_tag = common_photo_info_->GetAuditBSecondTag();
          if ((impression_gray && *impression_gray >= 3)
              && (audit_b_second_tag && *audit_b_second_tag > 0)) {
            to_ignore = true;
          }
        }
        if (to_ignore) {
          continue;
        }
      }
      if ((mmu_type == 163 || mmu_type == 164) && mmu_value != 0.0 &&
           !follow_author_id_set_.empty()) {
        auto author_id = common_photo_info_->GetAuthorId();
        if (author_id && follow_author_id_set_.count(*author_id)) {
          continue;
        }
      }
      bool is_low_type = (mmu_type == 40 || mmu_type == 150);
      if ((is_low_type && mmu_value > 0 && mmu_value <= mmu_value_threshold)
          || (!is_low_type && mmu_value >= mmu_value_threshold)) {
        return true;
      }
    }
    return false;
}

// new marketing sense filter
void ExploreRetrievalFilterArranger::NewMarketingSenseFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  std::string enable_cart_photo_filter_attr = config->GetString("enable_cart_photo_filter_attr");
  std::string enable_hetu_filter_attr = config->GetString("enable_hetu_filter_attr");
  std::string enable_audit_tag_filter_attr = config->GetString("enable_audit_tag_filter_attr");
  enable_cart_photo_filter_ = 0;
  enable_hetu_filter_ = 0;
  enable_audit_tag_filter_ = 0;
  if (!enable_cart_photo_filter_attr.empty()) {
    auto enable_cart_photo_filter_ptr = context->GetIntCommonAttr(enable_cart_photo_filter_attr);
    if (enable_cart_photo_filter_ptr) {
      enable_cart_photo_filter_ = *enable_cart_photo_filter_ptr;
    }
  }
  if (!enable_hetu_filter_attr.empty()) {
    auto enable_hetu_filter_ptr = context->GetIntCommonAttr(enable_hetu_filter_attr);
    if (enable_hetu_filter_ptr) {
      enable_hetu_filter_ = *enable_hetu_filter_ptr;
    }
  }
  if (!enable_audit_tag_filter_attr.empty()) {
    auto enable_audit_tag_filter_ptr = context->GetIntCommonAttr(enable_audit_tag_filter_attr);
    if (enable_audit_tag_filter_ptr) {
      enable_audit_tag_filter_ = *enable_audit_tag_filter_ptr;
    }
  }
}

bool ExploreRetrievalFilterArranger::NewMarketingSenseFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  static constexpr int64 audit_tag_threshold = 2019675;
  if (enable_audit_tag_filter_) {
    const auto audit_b_second_tag = common_photo_info_->GetAuditBSecondTag();
    if (audit_b_second_tag && *audit_b_second_tag == audit_tag_threshold) {
      return true;
    }
  }

  bool is_cart_photo = false;
  bool is_marketing_sense = false;
  const auto merchant_item_id_list = common_photo_info_->GetMerchantItemIdList();
  const auto merchant_photo_cart_relation = common_photo_info_->GetMerchantPhotoCartRelation();
  const auto hetu_level_two_tag_list = common_photo_info_->GetHetuLevelTwoTagList();
  const auto hetu_tag_list = common_photo_info_->GetHetuTagList();
  if (enable_cart_photo_filter_ && merchant_item_id_list
        && merchant_photo_cart_relation) {
    is_cart_photo = explore::IsMerchantCartPhoto(merchant_item_id_list, merchant_photo_cart_relation);
  }
  if (enable_hetu_filter_ && hetu_level_two_tag_list && hetu_tag_list) {
    is_marketing_sense = explore::IsMarketingSensePhoto(hetu_level_two_tag_list, hetu_tag_list);
  }
  return is_cart_photo || is_marketing_sense;
}

// not audit level b filter
void ExploreRetrievalFilterArranger::NotAuditPhotoFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {

  skip_not_audit_follow_author_ = explore::GetIntCommonAttr(context, config,
                                "skip_not_audit_follow_author_attr").value_or(0);
  follow_author_id_set_.clear();
  if (skip_not_audit_follow_author_ && user_info) {
    for (const auto &item : user_info->follow_list()) {
      if (item.has_user()) {
        follow_author_id_set_.insert(item.user().id());
      }
    }
  }

  auto getDoubleAttr = [config, context](const std::string &key) -> double {
    std::string attr = config->GetString(key);
    if (!attr.empty()) {
      auto value_ptr = context->GetDoubleCommonAttr(attr);
      if (value_ptr) {
        return *value_ptr;
      }
    }
    return -1.0;
  };

  skip_not_audit_zero_value_ = explore::GetIntCommonAttr(context, config,
                                "skip_not_audit_zero_value_attr").value_or(0);
  cold_start_breakout_score_threshold_ = getDoubleAttr("cold_start_breakout_score_threshold_attr");
  ctr_threshold_ = getDoubleAttr("ctr_threshold_attr");

  auto getIntAttr = [config, context](const std::string &key) -> double {
    std::string attr = config->GetString(key);
    if (!attr.empty()) {
      auto value_ptr = context->GetIntCommonAttr(attr);
      if (value_ptr) {
        return *value_ptr;
      }
    }
    return -1;
  };

  high_fans_threshold_ = getIntAttr("high_fans_threshold_attr");
  higher_action_threshold_ = getIntAttr("higher_action_threshold_attr");
  need_high_quality_mmu_score_ = getIntAttr("need_high_quality_mmu_score_attr");

  std::string high_quality_mmu_map_strs_attr = config->GetString("high_quality_mmu_map_strs_attr");
  high_quality_mmu_map_.clear();
  if (!high_quality_mmu_map_strs_attr.empty()) {
    auto high_quality_mmu_map_strs = context->GetStringCommonAttr(high_quality_mmu_map_strs_attr);
    if (high_quality_mmu_map_strs) {
      bool parse_flag = explore::StringToMap(std::string(*high_quality_mmu_map_strs),
                                             &high_quality_mmu_map_);
      if (!parse_flag) {
        high_quality_mmu_map_.clear();
      }
    }
  }
}

bool ExploreRetrievalFilterArranger::NotAuditPhotoFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  const auto audit_b_second_tag = common_photo_info_->GetAuditBSecondTag();
  if (audit_b_second_tag && *audit_b_second_tag > 0) {
    return false;
  }
  if (cold_start_breakout_score_threshold_ >= 0.0) {
    const auto cold_start_breakout_score = common_photo_info_->GetColdStartBreakoutScore();
    if (cold_start_breakout_score && *cold_start_breakout_score > cold_start_breakout_score_threshold_) {
      return false;
    }
  }
  if (high_fans_threshold_ >= 0) {
    const auto author_fans_count = common_photo_info_->GetAuthorFansCount();
    if (author_fans_count && *author_fans_count > high_fans_threshold_) {
      return false;
    }
  }
  if (ctr_threshold_ >= 0.0) {
    const auto explore_real_show = common_photo_info_->GetExploreRealShow();
    const auto explore_click = common_photo_info_->GetExploreClick();
    if (explore_real_show && explore_click &&
        *explore_click / (*explore_real_show + 1.0) > ctr_threshold_) {
      return false;
    }
  }
  if (higher_action_threshold_ >= 0) {
    int higher_action_count = 0;
    const auto like_count = common_photo_info_->GetExploreLike();
    higher_action_count += like_count.value_or(0);
    const auto follow_count = common_photo_info_->GetExploreFollow();
    higher_action_count += follow_count.value_or(0);
    const auto forward_count = common_photo_info_->GetExploreForward();
    higher_action_count += forward_count ? *forward_count : 0;
    const auto comment_count = common_photo_info_->GetExploreComment();
    higher_action_count += comment_count ? *comment_count : 0;
    if (higher_action_count > higher_action_threshold_) {
      return false;
    }
  }
  if (!follow_author_id_set_.empty()) {
    auto author_id = common_photo_info_->GetAuthorId();
    if (author_id && follow_author_id_set_.count(*author_id)) {
      return false;
    }
  }
  if (need_high_quality_mmu_score_ == 1) {
    for (auto iter = high_quality_mmu_map_.begin(); iter != high_quality_mmu_map_.end(); iter++) {
      auto mmu_type = iter->first;
      auto mmu_value_threshold = iter->second;
      double mmu_value = common_photo_info_->GetMmuLowQualityModelScore(mmu_type);
      if (skip_not_audit_zero_value_ && mmu_value == 0.0) {
        continue;
      }
      if (mmu_value >= mmu_value_threshold) {
        return false;
      }
    }
  }
  return true;
}

// questionaire_info_filter
void ExploreRetrievalFilterArranger::QuestionaireInfoFilterProcessor::Init(
        ReadableRecoContextInterface *context,
        const ks::reco::UserInfo *user_info,
        const base::Json *config) {
  questionaire_thompson_filter_ = explore::GetIntCommonAttr(
      context, config, "questionaire_thompson_filter_attr").value_or(0);
  use_global_data_ = explore::GetIntCommonAttr(
      context, config, "questionaire_use_global_data_attr").value_or(0);
  if (questionaire_thompson_filter_) {
      neg_weight_ = explore::GetDoubleCommonAttr(
      context, config, "questionaire_filter_neg_weight_attr").value_or(0.0);
    pos_weight_ = explore::GetDoubleCommonAttr(
        context, config, "questionaire_filter_pos_weight_attr").value_or(0.0);
    unsure_weight_ = explore::GetDoubleCommonAttr(
        context, config, "questionaire_filter_unsure_weight_attr").value_or(0.0);
    click_weight_ = explore::GetDoubleCommonAttr(
        context, config, "questionaire_filter_click_weight_attr").value_or(0.0);
    unclick_weight_ = explore::GetDoubleCommonAttr(
        context, config, "questionaire_filter_unclick_weight_attr").value_or(0.0);
  } else {
    questionaire_info_positive_rate_threshold_ = explore::GetDoubleCommonAttr(
        context, config, "questionaire_info_positive_rate_threhold_attr").value_or(0.0);
    questionaire_info_unsure_rate_threshold_ = explore::GetDoubleCommonAttr(
        context, config, "questionaire_info_unsure_rate_threhold_attr").value_or(0.0);
  }
  questionaire_info_negative_rate_threshold_ = explore::GetDoubleCommonAttr(
      context, config, "questionaire_info_negtive_rate_threhold_attr").value_or(0.0);
  questionaire_info_negative_rate_high_threshold_ = explore::GetDoubleCommonAttr(
      context, config, "questionaire_info_negtive_rate_high_threhold_attr").value_or(0.0);
  credible_questionnaire_total_count_ = explore::GetIntCommonAttr(
      context, config, "questionaire_info_credible_total_count_attr").value_or(50);
  // topk
  replace_topk_result_ = explore::GetIntCommonAttr(
      context, config, "questionaire_info_replace_topk_result").value_or(0);
  topk_level_threshold_ = explore::GetIntCommonAttr(
      context, config, "questionaire_info_topk_level_threshold_attr").value_or(0);
  audit_level_threshold_ = explore::GetIntCommonAttr(
      context, config, "questionaire_info_audit_level_threshold_attr").value_or(0);
}

bool ExploreRetrievalFilterArranger::QuestionaireInfoFilterProcessor::NeedFilter(
  const CommonRecoResult &result) {
  const auto explore_neg_count_opt = common_photo_info_->GetExploreQuestionaireInfoNegativeCount();
  const auto explore_pos_count_opt = common_photo_info_->GetExploreQuestionaireInfoPositiveCount();
  const auto explore_unsure_count_opt = common_photo_info_->GetExploreQuestionaireInfoUnsureCount();
  int negative_count = explore_neg_count_opt? *explore_neg_count_opt : 0;
  int positive_count = explore_pos_count_opt? *explore_pos_count_opt : 0;
  int unsure_count = explore_unsure_count_opt? *explore_unsure_count_opt : 0;
  if (use_global_data_) {
    const auto thanos_neg_count_opt = common_photo_info_->GetQuestionaireInfoNegativeCount();
    const auto thanos_pos_count_opt = common_photo_info_->GetQuestionaireInfoPositiveCount();
    const auto thanos_unsure_count_opt = common_photo_info_->GetQuestionaireInfoUnsureCount();
    negative_count += thanos_neg_count_opt? *thanos_neg_count_opt : 0;
    positive_count += thanos_pos_count_opt? *thanos_pos_count_opt : 0;
    unsure_count += thanos_unsure_count_opt? *thanos_unsure_count_opt : 0;
  }
  int click_count = negative_count + positive_count + unsure_count;
  bool is_questionnaire_fllter = false;
  if (questionaire_thompson_filter_) {
      const auto explore_exposure_count_opt = common_photo_info_->GetExploreQuestionaireInfoExposureCount();
      int exposure_count = explore_exposure_count_opt ? *explore_exposure_count_opt : 0;
      if (use_global_data_) {
        const auto thanos_exposure_count_opt = common_photo_info_->GetQuestionaireInfoExposureCount();
        exposure_count += thanos_exposure_count_opt ? *thanos_exposure_count_opt : 0;;
      }
      int unclick_count = std::max(exposure_count - click_count, 0);
    if (exposure_count + click_count > credible_questionnaire_total_count_) {
      double alpha = negative_count * neg_weight_ + unsure_count * unsure_weight_
        + unclick_count * unclick_weight_ + 1.0;
      double beta = positive_count * pos_weight_ + click_count * click_weight_ + 1.0;
      boost::random::beta_distribution<> beta_engine(alpha, beta);
      double filter_prob = beta_engine(random_engine_);
      if (filter_prob > questionaire_info_negative_rate_threshold_) {
        is_questionnaire_fllter = true;
      }
    }
  } else {
    if (click_count > credible_questionnaire_total_count_) {
      double neg_rate = static_cast<double>(negative_count) / click_count;
      double pos_rate = static_cast<double>(positive_count) / click_count;
      double undef_rate = static_cast<double>(unsure_count) / click_count;
      if (neg_rate > questionaire_info_negative_rate_threshold_
        || pos_rate < questionaire_info_positive_rate_threshold_
        || undef_rate > questionaire_info_unsure_rate_threshold_) {
        is_questionnaire_fllter = true;
      }
    }
  }
  // 根据 topk 审与高热审进行回捞
  if (replace_topk_result_
    && (static_cast<double>(negative_count) / click_count)
    < questionaire_info_negative_rate_high_threshold_) {
    // topk 审
    const auto topk_audit_level = common_photo_info_->GetTopkAuditLevel();
    // 高热审
    const auto audit_level = common_photo_info_->GetAuditHotHighTagLevel();
    if ((topk_audit_level && *topk_audit_level >= topk_level_threshold_)
      || (audit_level && *audit_level >= audit_level_threshold_)) {  // topk 通过 or 高热优普
      is_questionnaire_fllter = false;
    }
  }

  return is_questionnaire_fllter;
}

// risk_man_risk_level
void ExploreRetrievalFilterArranger::RiskManRiskPhotoFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  user_level_ = 2;
  is_tmp_risk_user_ = 0;
  user_risk_min_ = 4;
  if (user_info) {
    if (user_info->has_feature_collection() && user_info->feature_collection().has_risk_level()) {
      user_level_ = user_info->feature_collection().risk_level();
    } else {
      user_level_ = 0;
    }
  }
  if (user_level_ == 0) {
    user_level_ = 2;
  }

  std::string attr = config->GetString("tmp_be_risk_user_attr");
  if (!attr.empty()) {
    auto tmp_risk_user = context->GetIntCommonAttr(attr);
    if (tmp_risk_user) {
      is_tmp_risk_user_ = *tmp_risk_user;
    }
  }

  std::string explore_user_risk_min_attr = config->GetString("explore_user_risk_min_attr");
  if (!explore_user_risk_min_attr.empty()) {
    auto user_risk_min_ptr = context->GetIntCommonAttr(explore_user_risk_min_attr);
    if (user_risk_min_ptr) {
      user_risk_min_ = *user_risk_min_ptr;
    }
  }

  std::string balck_white_change_attr = config->GetString("black_white_change_risk");
  if (!balck_white_change_attr.empty()) {
    auto balck_white_change_ptr = context->GetIntCommonAttr(attr);
    if (balck_white_change_ptr) {
      black_white_change_ = *balck_white_change_ptr;
    }
  }
}

bool ExploreRetrievalFilterArranger::RiskManRiskPhotoFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  const auto risk_man_risk_photo = common_photo_info_->GetRiskManRiskPhoto();
  int risk_photo = 2;
  if (risk_man_risk_photo) {
    risk_photo = *risk_man_risk_photo;
  }
  if (risk_photo == 0) {
    risk_photo = 2;
  }

  if (black_white_change_ > 0 && is_tmp_risk_user_ > 0) {
    return false;
  }

  if (user_level_ >= user_risk_min_) {
    if (risk_photo < user_level_) {
      return true;
    }
  }

  if (is_tmp_risk_user_ > 0) {
    if (risk_photo < 5) {
      return true;
    }
  }

  return false;
}

// need_shuffle_photo
void ExploreRetrievalFilterArranger::NeedShufflePhotoFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  is_need_shuffle_man_ = 0;
  is_tmp_shuffle_user_ = 0;
  if (user_info) {
    if (user_info->has_feature_collection() && user_info->feature_collection().has_is_shuffle()) {
      is_need_shuffle_man_ = user_info->feature_collection().is_shuffle();
    } else {
      is_need_shuffle_man_ = 0;
    }
  }
  std::string attr = config->GetString("tmp_be_shuffle_user_attr");
  if (!attr.empty()) {
    auto tmp_risk_user = context->GetIntCommonAttr(attr);
    if (tmp_risk_user) {
      is_tmp_shuffle_user_ = *tmp_risk_user;
    }
  }

  std::string balck_white_change_attr = config->GetString("black_white_change_shuffle");
  if (!balck_white_change_attr.empty()) {
    auto balck_white_change_ptr = context->GetIntCommonAttr(attr);
    if (balck_white_change_ptr) {
      black_white_change_ = *balck_white_change_ptr;
    }
  }
}

bool ExploreRetrievalFilterArranger::NeedShufflePhotoFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  const auto need_shuffle_photo = common_photo_info_->GetNeedShufflePhoto();

  if (black_white_change_ > 0 && is_tmp_shuffle_user_ > 0) {
    return false;
  }

  if (is_need_shuffle_man_ > 0 || is_tmp_shuffle_user_ > 0) {
    if (need_shuffle_photo && *need_shuffle_photo > 0) {
      return true;
    }
  }

  return false;
}

// low_real_show
void ExploreRetrievalFilterArranger::LowRealShowFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  threshold_of_low_real_show_ = 0;
  black_hetu_set_.clear();
  std::string attr = config->GetString("low_real_show_threshold");
  if (!attr.empty()) {
    auto threshold = context->GetIntCommonAttr(attr);
    if (threshold) {
      threshold_of_low_real_show_ = *threshold;
    }
  }

  std::string black_set_attr = config->GetString("black_hetu_set_low_real_show_attr");
  if (!black_set_attr.empty()) {
    auto black_set = context->GetIntListCommonAttr(black_set_attr);
    if (black_set) {
      black_hetu_set_.insert(black_set->begin(), black_set->end());
    }
  }
}

bool ExploreRetrievalFilterArranger::LowRealShowFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (common_photo_info_->IsPicture()) {
    return false;
  }
  const auto hetu_one_list = common_photo_info_->GetHetuLevelOneTagList();
  const auto hetu_two_list = common_photo_info_->GetHetuLevelTwoTagList();
  const auto hetu_three_list = common_photo_info_->GetHetuLevelThreeTagList();

  const auto explore_real_show = common_photo_info_->GetExploreRealShow();
  const auto nebula_real_show = common_photo_info_->GetNebulaRealShow();
  const auto thanos_real_show = common_photo_info_->GetThanosRealShow();
  const auto fountain_real_show = common_photo_info_->GetFountainRealShow();

  bool in_black_set = explore::ContainTag(hetu_one_list, black_hetu_set_)
                      || explore::ContainTag(hetu_two_list, black_hetu_set_)
                      || explore::ContainTag(hetu_three_list, black_hetu_set_);

  int64 explore_real_show_num = 0;
  int64 nebula_real_show_num = 0;
  int64 thanos_real_show_num = 0;
  int64 fountain_real_show_num = 0;

  if (explore_real_show) {
    explore_real_show_num = *explore_real_show;
  }
  if (nebula_real_show) {
    nebula_real_show_num = *nebula_real_show;
  }
  if (thanos_real_show) {
    thanos_real_show_num = *thanos_real_show;
  }
  if (fountain_real_show) {
    fountain_real_show_num = *fountain_real_show;
  }
  int64 all_show = explore_real_show_num + nebula_real_show_num + thanos_real_show_num
                  + fountain_real_show_num;

  if (in_black_set && all_show < threshold_of_low_real_show_) {
    return true;
  }

  return false;
}

// low_fans
void ExploreRetrievalFilterArranger::LowFansFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  threshold_of_low_fans_ = 0;
  black_hetu_set_.clear();
  std::string attr = config->GetString("low_fans_threshold");
  if (!attr.empty()) {
    auto threshold = context->GetIntCommonAttr(attr);
    if (threshold) {
      threshold_of_low_fans_ = *threshold;
    }
  }

  std::string black_set_attr = config->GetString("black_hetu_set_low_fans_attr");
  if (!black_set_attr.empty()) {
    auto black_set = context->GetIntListCommonAttr(black_set_attr);
    if (black_set) {
      black_hetu_set_.insert(black_set->begin(), black_set->end());
    }
  }
}

bool ExploreRetrievalFilterArranger::LowFansFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (common_photo_info_->IsPicture()) {
    return false;
  }
  const auto author_fans = common_photo_info_->GetAuthorFansCount();

  const auto hetu_one_list = common_photo_info_->GetHetuLevelOneTagList();
  const auto hetu_two_list = common_photo_info_->GetHetuLevelTwoTagList();
  const auto hetu_three_list = common_photo_info_->GetHetuLevelThreeTagList();

  bool in_black_set = explore::ContainTag(hetu_one_list, black_hetu_set_)
                      || explore::ContainTag(hetu_two_list, black_hetu_set_)
                      || explore::ContainTag(hetu_three_list, black_hetu_set_);

  if (author_fans) {
    if (in_black_set && *author_fans < threshold_of_low_fans_) {
      return true;
    }
  }

  return false;
}

// high_explore_show_rate
void ExploreRetrievalFilterArranger::HighExploreShowRateFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  rate_of_high_explore_show_ = 1.0;
  std::string attr = config->GetString("rate_of_high_explore_show");
  if (!attr.empty()) {
    auto rate = context->GetDoubleCommonAttr(attr);
    if (rate) {
      rate_of_high_explore_show_ = *rate;
    }
  }

  min_show_of_high_explore_show_ = 0;
  std::string min_attr = config->GetString("min_show_of_high_explore_show");
  if (!min_attr.empty()) {
    auto min = context->GetIntCommonAttr(min_attr);
    if (min) {
      min_show_of_high_explore_show_ = *min;
    }
  }

  max_show_of_high_explore_show_ = 0;
  std::string max_attr = config->GetString("max_show_of_high_explore_show");
  if (!max_attr.empty()) {
    auto max = context->GetIntCommonAttr(max_attr);
    if (max) {
      max_show_of_high_explore_show_ = *max;
    }
  }

  std::string black_set_attr = config->GetString("black_hetu_set_high_explore_show_attr");
  if (!black_set_attr.empty()) {
    auto black_set = context->GetIntListCommonAttr(black_set_attr);
    if (black_set) {
      black_hetu_set_.insert(black_set->begin(), black_set->end());
    }
  }
}

bool ExploreRetrievalFilterArranger::HighExploreShowRateFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (common_photo_info_->IsPicture()) {
    return false;
  }

  const auto hetu_one_list = common_photo_info_->GetHetuLevelOneTagList();
  const auto hetu_two_list = common_photo_info_->GetHetuLevelTwoTagList();
  const auto hetu_three_list = common_photo_info_->GetHetuLevelThreeTagList();

  bool in_black_set = explore::ContainTag(hetu_one_list, black_hetu_set_)
                      || explore::ContainTag(hetu_two_list, black_hetu_set_)
                      || explore::ContainTag(hetu_three_list, black_hetu_set_);

  if (!in_black_set) {
    return false;
  }

  const auto explore_real_show = common_photo_info_->GetExploreRealShow();
  const auto nebula_real_show = common_photo_info_->GetNebulaRealShow();
  const auto thanos_real_show = common_photo_info_->GetThanosRealShow();
  const auto fountain_real_show = common_photo_info_->GetFountainRealShow();
  int64 explore_real_show_num = 0;
  int64 nebula_real_show_num = 0;
  int64 thanos_real_show_num = 0;
  int64 fountain_real_show_num = 0;

  if (explore_real_show) {
    explore_real_show_num = *explore_real_show;
  }
  if (nebula_real_show) {
    nebula_real_show_num = *nebula_real_show;
  }
  if (thanos_real_show) {
    thanos_real_show_num = *thanos_real_show;
  }
  if (fountain_real_show) {
    fountain_real_show_num = *fountain_real_show;
  }
  int64 all_show = explore_real_show_num + nebula_real_show_num + thanos_real_show_num
                  + fountain_real_show_num;

  if (min_show_of_high_explore_show_ > 0 && all_show < min_show_of_high_explore_show_) {
    return false;
  }
  if (max_show_of_high_explore_show_ > 0 && all_show > max_show_of_high_explore_show_) {
    return false;
  }

  const auto rate = explore_real_show_num / (all_show + 10.0);
  if (rate > rate_of_high_explore_show_) {
    return true;
  }

  return false;
}

// pic_filter_before_admin
void ExploreRetrievalFilterArranger::PictureBeforeAdminFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  std::string mmu_type_attr_name = config->GetString("pic_mmu_low_quality_type_map");
  std::string server_show_threshold_attr_name = config->GetString("explore_server_show_threshold");
  std::string ctr_threshold_attr_name = config->GetString("explore_ctr_threshold");
  mmu_type_map_.clear();
  if (!mmu_type_attr_name.empty()) {
    auto mmu_type_attr_value = context->GetStringCommonAttr(mmu_type_attr_name);
    if (mmu_type_attr_value) {
      explore::StringToMap(std::string(*mmu_type_attr_value), &mmu_type_map_);
    }
  }
  if (!server_show_threshold_attr_name.empty()) {
    auto server_show_threshold_attr_value = context->GetIntCommonAttr(server_show_threshold_attr_name);
    if (server_show_threshold_attr_value) {
      explore_server_show_threshold = *server_show_threshold_attr_value;
    }
  }
  if (!ctr_threshold_attr_name.empty()) {
    auto ctr_threshold_attr_value = context->GetDoubleCommonAttr(ctr_threshold_attr_name);
    if (ctr_threshold_attr_value) {
      explore_ctr_threshold = *ctr_threshold_attr_value;
    }
  }
}

bool ExploreRetrievalFilterArranger::PictureBeforeAdminFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (!common_photo_info_->IsPicture()) {
    return false;
  }
  // item 当前值
  int64 explore_server_show = 0;
  int64 explore_real_show = 0;
  int64 explore_click = 0;
  double explore_ctr = 0.0;
  const auto explore_server_show_ptr = common_photo_info_->GetExploreServerShow();
  const auto explore_real_show_ptr = common_photo_info_->GetExploreRealShow();
  const auto explore_click_ptr = common_photo_info_->GetExploreClick();

  if (explore_server_show_ptr) {
    explore_server_show = *explore_server_show_ptr;
  }
  if (explore_server_show < explore_server_show_threshold) {
    return false;  // 还未到达曝光阈值
  }
  if (explore_real_show_ptr) {
    explore_real_show = *explore_real_show_ptr;
  }
  if (explore_click_ptr) {
    explore_click = *explore_click_ptr;
  }
  explore_ctr = explore_click / (explore_real_show + 1.0);
  if (explore_ctr < explore_ctr_threshold) {
    return false;  // ctr 低于阈值，不做过滤
  }
  if (!mmu_type_map_.empty()) {
    // 遍历所有配置的过滤项
    for (auto iter = mmu_type_map_.begin(); iter != mmu_type_map_.end(); iter++) {
      auto mmu_type = iter->first;
      auto mmu_value_threshold = iter->second;
      double mmu_value = 0.0;
      mmu_value = common_photo_info_->GetMmuLowQualityModelScore(mmu_type);
      // server >= 阈值 && ctr 高于阈值 && mmu_score 高于 阈值 则过滤
      if (mmu_value >= mmu_value_threshold) {
        return true;
      }
    }
  }
  return false;
}

void ExploreRetrievalFilterArranger::SiriusPhotoFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
}

bool ExploreRetrievalFilterArranger::SiriusPhotoFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  const auto ptr = common_photo_info_->GetIsSiriusPunish();
  return ptr && (*ptr);
}

void ExploreRetrievalFilterArranger::DownloadDisabledPicFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
}

bool ExploreRetrievalFilterArranger::DownloadDisabledPicFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (!common_photo_info_->IsPicture()) {
    return false;
  }
  auto enable_download = common_photo_info_->GetEnableDownload();
  return enable_download && (*enable_download) == 0;
}

void ExploreRetrievalFilterArranger::UnpersonifiedAuthorPicFilterProcessor::Init(
  ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  unpersonified_author_set_ =
    context->GetPtrCommonAttr<folly::F14FastSet<uint64>>("unpersonified_author_set");
}

bool ExploreRetrievalFilterArranger::UnpersonifiedAuthorPicFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (!common_photo_info_->IsPicture()) {
    return false;
  }
  auto author_id_ptr = common_photo_info_->GetAuthorId();
  if (!author_id_ptr) {
    return false;
  }
  return unpersonified_author_set_ && unpersonified_author_set_->count(*author_id_ptr) > 0;
}

void ExploreRetrievalFilterArranger::BlackPhotosFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  black_photos_.clear();
  std::string black_photos_attr = config->GetString("black_photos_attr");
  if (!black_photos_attr.empty()) {
    auto black_photos_set = context->GetIntListCommonAttr(black_photos_attr);
    if (black_photos_set) {
      black_photos_.insert(black_photos_set->begin(), black_photos_set->end());
    }
  }
}

bool ExploreRetrievalFilterArranger::BlackPhotosFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (black_photos_.count(result.GetId()) > 0) {
    return true;
  }
  return false;
}

void ExploreRetrievalFilterArranger::PictureSupportAuthorRealshowCtrFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  std::string support_author_picture_realshow_threshold_attr_name =
    config->GetString("support_author_picture_realshow_threshold");
  std::string support_author_picture_low_ctr_threshold_attr_name =
    config->GetString("support_author_picture_ctr_threshold");
  std::string support_author_author_id_memory_map_data =
    config->GetString("support_author_memory_data");
  if (!support_author_picture_realshow_threshold_attr_name.empty()) {
    auto support_author_picture_realshow_threshold_attr_value =
      context->GetDoubleCommonAttr(support_author_picture_realshow_threshold_attr_name);
    if (support_author_picture_realshow_threshold_attr_value) {
      support_author_picture_realshow_threshold_ =
        *support_author_picture_realshow_threshold_attr_value;
    }
  }
  if (!support_author_picture_low_ctr_threshold_attr_name.empty()) {
    auto support_author_picture_low_ctr_threshold_attr_value =
      context->GetDoubleCommonAttr(support_author_picture_low_ctr_threshold_attr_name);
    if (support_author_picture_low_ctr_threshold_attr_value) {
      support_author_picture_ctr_threshold_ =
        *support_author_picture_low_ctr_threshold_attr_value;
    }
  }
  if (!support_author_author_id_memory_map_data.empty()) {
    support_author_aid_map_ =
      context->GetPtrCommonAttr<folly::F14FastMap<uint64, folly::F14FastMap<uint64, double>>>(
        support_author_author_id_memory_map_data);
  }
}

bool ExploreRetrievalFilterArranger::PictureSupportAuthorRealshowCtrFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (!common_photo_info_->IsPicture()) {
    return false;
  }
  auto author_id_ptr = common_photo_info_->GetAuthorId();
  if (!support_author_aid_map_
    || !author_id_ptr
    || support_author_aid_map_->find(*author_id_ptr) == support_author_aid_map_->end()) {
    return false;
  }
  // item 当前值
  int64 explore_real_show = 0;
  int64 explore_click = 0;
  double explore_ctr = 0.0;
  const auto explore_real_show_ptr = common_photo_info_->GetExploreRealShow();
  const auto explore_click_ptr = common_photo_info_->GetExploreClick();
  if (explore_real_show_ptr) {
    explore_real_show = *explore_real_show_ptr;
  }
  if (explore_click_ptr) {
    explore_click = *explore_click_ptr;
  }
  explore_ctr = explore_click / (explore_real_show + 1.0);
  // 对扶持作者作品高曝光低 ctr 作品 filter
  if (explore_real_show >= support_author_picture_realshow_threshold_
      && explore_ctr < support_author_picture_ctr_threshold_) {
    return true;
  }
  return false;
}

// impression_audit_gray_limit
void ExploreRetrievalFilterArranger::ImpressionAuditGrayShowFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  std::string impression_audit_gray_show_limit_attr =
    config->GetString("impression_audit_gray_show_limit_attr");
  if (!impression_audit_gray_show_limit_attr.empty()) {
    auto ptr = context->GetIntCommonAttr(impression_audit_gray_show_limit_attr);
    if (ptr) {
      impression_audit_gray_show_limit_ = *ptr;
    }
  }
  impression_audit_gray_tag_set_.clear();
  auto impression_audit_gray_tag_list =
        explore::GetIntListCommonAttr(context, config, "impression_audit_gray_tag_list_attr");
  if (impression_audit_gray_tag_list) {
    impression_audit_gray_tag_set_.insert(impression_audit_gray_tag_list->begin(),
                                                        impression_audit_gray_tag_list->end());
  }
}

bool ExploreRetrievalFilterArranger::ImpressionAuditGrayShowFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  const auto impression_gray = common_photo_info_->GetImpressionAudit();
  const auto audit_b_second_tag = common_photo_info_->GetAuditBSecondTag();
  const auto explore_real_show = common_photo_info_->GetExploreRealShow();
  const auto audit_hot_high_level = common_photo_info_->GetAuditHotHighTagLevel();
  if (audit_b_second_tag && impression_gray && explore_real_show) {
    bool isImpressionGray = false;
    if (impression_audit_gray_tag_set_.size() > 0) {
      isImpressionGray = (*impression_gray == 2)
            && impression_audit_gray_tag_set_.count(*audit_b_second_tag);
    } else {
      isImpressionGray = (*impression_gray == 2) && (*audit_b_second_tag) > 0;
    }
    if (isImpressionGray && (!audit_hot_high_level || *audit_hot_high_level <= 2)
      && *explore_real_show >= impression_audit_gray_show_limit_) {
      return true;
    }
  }
  return false;
}

// photo_life
void ExploreRetrievalFilterArranger::PhotoLifeFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  photo_life_max_hours_ = explore::GetIntCommonAttr(
      context, config, "photo_life_max_hours_attr").value_or(168);
  follow_aid_set_.clear();
  auto enable_skip_follow_author = explore::GetIntCommonAttr(
      context, config, "enable_skip_follow_author_attr");
  if (enable_skip_follow_author && *enable_skip_follow_author && user_info) {
    for (const auto &item : user_info->follow_list()) {
      if (item.has_user()) {
        follow_aid_set_.insert(item.user().id());
      }
    }
  }
}

bool ExploreRetrievalFilterArranger::PhotoLifeFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  auto long_term_photo = common_photo_info_->GetLongTermPhoto();
  if (long_term_photo && *long_term_photo) {
    return false;
  }

  if (!follow_aid_set_.empty()) {
    auto author_id = common_photo_info_->GetAuthorId();
    if (author_id && follow_aid_set_.count(*author_id)) {
      return false;
    }
  }

  int64 current_time_ms = base::GetTimestamp() / base::Time::kMicrosecondsPerMillisecond;
  auto upload_time = common_photo_info_->GetUploadTime();
  int64 photo_life_hours = (current_time_ms - upload_time.value_or(0)) / base::Time::kMillisecondsPerHour;
  return photo_life_hours >= photo_life_max_hours_;
}

void ExploreRetrievalFilterArranger::ExplorePunishFilterProcessor::Init(ReadableRecoContextInterface *context,
                                                                        const ks::reco::UserInfo *user_info,
                                                                        const base::Json *config) {}

bool ExploreRetrievalFilterArranger::ExplorePunishFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  return common_photo_info_->GetExplorePunish() > 0;
}

void ExploreRetrievalFilterArranger::ExplorePunishCityFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  request_city_id_ = user_info->request_location_new().city_id();
}

bool ExploreRetrievalFilterArranger::ExplorePunishCityFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  const auto explore_punish_city = common_photo_info_->GetExplorePunishCity();
  if (explore_punish_city) {
    for (auto punish_city : *explore_punish_city) {
      if (request_city_id_ == punish_city) {
        return true;
      }
    }
  }
  return false;
}
void ExploreRetrievalFilterArranger::NegativeThompsonFilter::Init(
  ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  std::string attr = config->GetString("thompson_filter_threshold_attr");
  if (!attr.empty()) {
    thompson_filter_threshold_ = context->GetDoubleCommonAttr(attr).value_or(0.03);
  }
  attr = config->GetString("enable_interaction_base_attr");
  if (!attr.empty()) {
    enable_interaction_base_ = context->GetIntCommonAttr(attr).value_or(0);
  }
  attr = config->GetString("thompson_filter_realshow_divisor_attr");
  if (!attr.empty()) {
    thompson_filter_realshow_divisor_ = context->GetDoubleCommonAttr(attr).value_or(1000.0);
  }
  enable_fountain_cnt_ = explore::GetIntCommonAttr(
      context, config, "thompson_filter_enable_fountain_cnt").value_or(0);
  enable_thanos_cnt_ = explore::GetIntCommonAttr(
      context, config, "thompson_filter_enable_thanos_cnt").value_or(0);
  enable_nebula_cnt_ = explore::GetIntCommonAttr(
      context, config, "thompson_filter_enable_nebula_cnt").value_or(0);
  enable_explore_cnt_ = explore::GetIntCommonAttr(
      context, config, "thompson_filter_enable_explore_cnt").value_or(1);
  enable_skip_filter_ = explore::GetIntCommonAttr(
      context, config, "thompson_filter_enable_skip_low_emphtr_attr").value_or(0);
  ctr_weight_ = explore::GetDoubleCommonAttr(
      context, config, "thompson_filter_ctr_weight_attr").value_or(0.0);
  ltr_weight_ = explore::GetDoubleCommonAttr(
      context, config, "thompson_filter_ltr_weight_attr").value_or(1.0);
  wtr_weight_ = explore::GetDoubleCommonAttr(
      context, config, "thompson_filter_wtr_weight_attr").value_or(0.0);
  ftr_weight_ = explore::GetDoubleCommonAttr(
      context, config, "thompson_filter_ftr_weight_attr").value_or(0.0);
  cmtr_weight_ = explore::GetDoubleCommonAttr(
      context, config, "thompson_filter_cmtr_weight_attr").value_or(0.0);
  time_weight_ = explore::GetDoubleCommonAttr(
      context, config, "thompson_filter_time_weight_attr").value_or(0.0);
  normal_time_weight_ = explore::GetDoubleCommonAttr(
      context, config, "thompson_filter_normal_time_weight_attr").value_or(0.0);
  lvtr_weight_ = explore::GetDoubleCommonAttr(
      context, config, "thompson_filter_lvtr_weight_attr").value_or(0.0);
  realshow_weight_ = explore::GetDoubleCommonAttr(
      context, config, "thompson_filter_realshow_weight_attr").value_or(0.0);
  report_weight_ = explore::GetDoubleCommonAttr(
      context, config, "thompson_filter_report_weight_attr").value_or(0.0);
  no_click_weight_ = explore::GetDoubleCommonAttr(
      context, config, "thompson_filter_no_click_weight_attr").value_or(0.0);
  emp_htr_threshold_ = explore::GetDoubleCommonAttr(
      context, config, "thompson_filter_low_emphtr_threshold_attr").value_or(0.0);
}

bool ExploreRetrievalFilterArranger::NegativeThompsonFilter::NeedFilter(const CommonRecoResult &result) {
  // 各个页面的计数和
  int64 realshow_cnt = 0;
  int64 negative_cnt = 0;
  int64 report = 0;
  int64 click = 0;
  int64 like = 0;
  int64 follow = 0;
  int64 forward = 0;
  int64 comment = 0;
  int64 long_play_cnt = 0;
  int64 watch_time = 0;
  int64 duration_ms = 0;
  int64 no_click = 0;

  // 默认使用外流计数
  if (enable_explore_cnt_) {
    realshow_cnt += common_photo_info_->GetExploreRealShow().value_or(0);
    negative_cnt += common_photo_info_->GetExploreNegative().value_or(0);
    report += common_photo_info_->GetPhotoTotalReportCount().value_or(0);
    click += common_photo_info_->GetExploreClick().value_or(0);
    like += common_photo_info_->GetExploreLike().value_or(0);
    follow += common_photo_info_->GetExploreFollow().value_or(0);
    forward += common_photo_info_->GetExploreForward().value_or(0);
    comment += common_photo_info_->GetExploreComment().value_or(0);
    long_play_cnt += common_photo_info_->GetExploreLongPlayCount().value_or(0);
    watch_time += common_photo_info_->GetExploreViewLengthSum().value_or(0);
    duration_ms += common_photo_info_->GetDurationMs().value_or(0);
    no_click += (realshow_cnt - click);
  }

  if (enable_fountain_cnt_) {
    realshow_cnt += common_photo_info_->GetFountainRealShow().value_or(0);
    negative_cnt += common_photo_info_->GetFountainNegative().value_or(0);
    like += common_photo_info_->GetFountainLike().value_or(0);
  }

  if (enable_thanos_cnt_) {
    realshow_cnt += common_photo_info_->GetThanosRealShow().value_or(0);
    negative_cnt += common_photo_info_->GetThanosNegative().value_or(0);
    like += common_photo_info_->GetThanosLike().value_or(0);
  }

  if (enable_nebula_cnt_) {
    realshow_cnt += common_photo_info_->GetNebulaRealShow().value_or(0);
    negative_cnt += common_photo_info_->GetNebulaNegative().value_or(0);
    like += common_photo_info_->GetNebulaLike().value_or(0);
  }

  // low emphtr skip filter
  if (enable_skip_filter_ && (negative_cnt / (realshow_cnt + 1.0)) < emp_htr_threshold_) {
    return false;
  }

  double normal_watch_time = duration_ms > 0 ? watch_time / duration_ms : 0.0;
  double hate_and_report = negative_cnt + report_weight_ * report;
  if (no_click_weight_ > 0.0 && no_click > 0) {
    hate_and_report += no_click_weight_ * no_click;
  }
  double high_interact_score = ctr_weight_ * click + ltr_weight_ * like +
    wtr_weight_ * follow + ftr_weight_ * forward + cmtr_weight_ * comment +
    time_weight_ * watch_time + normal_time_weight_ * normal_watch_time +
    lvtr_weight_ * long_play_cnt + realshow_weight_ * std::max(realshow_cnt - negative_cnt, 0L);

  if (hate_and_report <= 0) {
    return false;
  }
  double beta = 1.0;
  if (enable_interaction_base_) {
    beta = high_interact_score + 1.0;
  } else {
    beta = (realshow_cnt - hate_and_report) /
      (thompson_filter_realshow_divisor_  + 1.0) + 1.0;
  }
  boost::random::beta_distribution<> beta_engine(hate_and_report, beta);
  double filter_prob = beta_engine(random_engine_);  // hate 越多， filter_prob 的期望越大
  if (filter_prob > thompson_filter_threshold_) {
    return true;
  }
  return false;
}

void ExploreRetrievalFilterArranger::ExploreBoostPhotoFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  target_reason_.clear();
  std::string attr = config->GetString("boost_photo_reason_list_attr");
  if (!attr.empty()) {
    auto list = context->GetIntListCommonAttr(attr);
    if (list) {
      target_reason_.insert(list->begin(), list->end());
    }
  }
  hetu_set_one_.clear();
  hetu_set_two_.clear();
  hetu_set_three_.clear();
  auto user_hetu_filled = [&](const auto& ids, const auto& scores, folly::F14FastSet<uint64>* hetu_set) {
    if (ids.size() == scores.size()) {
      for (int i = 0; i < ids.size(); ++i) {
        if (scores[i] >= 0) {
          hetu_set->insert(ids[i]);
        }
      }
    }
  };
  user_hetu_filled(user_info->user_interest_profile().hetu_level_one_long_term_id(),
    user_info->user_interest_profile().hetu_level_one_long_term_score(), &hetu_set_one_);
  user_hetu_filled(user_info->user_interest_profile().hetu_level_two_long_term_id(),
    user_info->user_interest_profile().hetu_level_two_long_term_score(), &hetu_set_two_);
  user_hetu_filled(user_info->user_interest_profile().hetu_level_three_long_term_id(),
    user_info->user_interest_profile().hetu_level_three_long_term_score(), &hetu_set_three_);
}

bool ExploreRetrievalFilterArranger::ExploreBoostPhotoFilterProcessor::NeedFilter(
    const CommonRecoResult &result) {
  if (target_reason_.count(result.reason) == 0) {
    return false;
  }
  const auto hetu_one_list = common_photo_info_->GetHetuLevelOneTagList();
  const auto hetu_two_list = common_photo_info_->GetHetuLevelTwoTagList();
  const auto hetu_three_list = common_photo_info_->GetHetuLevelThreeTagList();

  bool not_filter_set = explore::ContainTag(hetu_one_list, hetu_set_one_)
                      || explore::ContainTag(hetu_two_list, hetu_set_two_)
                      || explore::ContainTag(hetu_three_list, hetu_set_three_);
  if (!not_filter_set) {
    return true;
  }
  return false;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, ExploreRetrievalFilterArranger, ExploreRetrievalFilterArranger)

}  // namespace platform
}  // namespace ks
