#include "dragon/src/processor/ext/explore/enricher/explore_short_item_adjust_enricher.h"
#include "dragon/src/processor/ext/explore/util/explore_util.h"
#include <algorithm>

namespace ks {
namespace platform {

void ExploreShortItemAdjustEnricher::Enrich(MutableRecoContextInterface *context,
    RecoResultConstIter begin, RecoResultConstIter end) {
  auto enable_tagnex = GetIntProcessorParameter(context, "enable_tagnex_score", 0);
  auto enable_cluster = GetIntProcessorParameter(context, "enable_cluster_score", 0);
  auto enable_hetu2 = GetIntProcessorParameter(context, "enable_hetu2_score", 0);
  auto enable_hashtag = GetIntProcessorParameter(context, "enable_hashtag_score", 0);
  auto enable_hetu_tag = GetIntProcessorParameter(context, "enable_hetu_tag_score", 0);

  if (!tagnex_attr_.empty()) {
    tagnex_accessor_ = context->GetItemAttrAccessor(tagnex_attr_);
  }
  if (!cluster_id_attr_.empty()) {
    cluster_id_accessor_ = context->GetItemAttrAccessor(cluster_id_attr_);
  }
  if (!hetu_level2_attr_.empty()) {
    hetu_level2_accessor_ = context->GetItemAttrAccessor(hetu_level2_attr_);
  }
  if (!hashtag_attr_.empty()) {
    hashtag_accessor_ = context->GetItemAttrAccessor(hashtag_attr_);
  }

  // 负向兴趣：曝光未点击中的 tagnex
  folly::F14FastSet<int64> negative_tagnex_set, negative_cluster_set, negative_hetu2_set,
    negative_hashtag_set, negative_hetu_tag_set;
  int negative_item_count = 0;
  // 正向兴趣：点击列表中的 tagnex
  folly::F14FastSet<int64> positive_tagnex_set, positive_cluster_set, positive_hetu2_set,
    positive_hashtag_set, positive_hetu_tag_set;
  int positive_item_count = 0;

  // 记录负向兴趣
  CollectInterests(context, false, enable_tagnex, enable_cluster,
      enable_hetu2, enable_hashtag, enable_hetu_tag,
      &negative_tagnex_set, &negative_cluster_set, &negative_hetu2_set,
      &negative_hashtag_set, &negative_hetu_tag_set, &negative_item_count);

  // 记录正向兴趣
  CollectInterests(context, true, enable_tagnex, enable_cluster,
      enable_hetu2, enable_hashtag, enable_hetu_tag,
      &positive_tagnex_set, &positive_cluster_set, &positive_hetu2_set,
      &positive_hashtag_set, &positive_hetu_tag_set, &positive_item_count);

  auto output_tagnex_score_accessor = context->GetItemAttrAccessor(output_tagnex_score_attr_);
  auto output_cluster_score_accessor = context->GetItemAttrAccessor(output_cluster_score_attr_);
  auto output_hetu2_score_accessor = context->GetItemAttrAccessor(output_hetu2_score_attr_);
  auto output_hashtag_score_accessor = context->GetItemAttrAccessor(output_hashtag_score_attr_);
  auto output_hetu_tag_score_accessor = context->GetItemAttrAccessor(output_hetu_tag_score_attr_);
  valid_interest_cids_set_.clear();
  auto valid_interest_list = explore::GetIntListCommonAttr(context,
      config(), "valid_interest_list_attr");
  if (valid_interest_list && enable_cluster) {
    valid_interest_cids_set_.insert(valid_interest_list->begin(), valid_interest_list->end());
  }

  for (auto iter = begin; iter != end; ++iter) {
    ComputeScore(context, *iter, enable_tagnex, tagnex_accessor_,
        output_tagnex_score_accessor, positive_tagnex_set, negative_tagnex_set,
        positive_item_count, negative_item_count, "tagnex_adjust_alpha_coeff",
        "tagnex_adjust_beta_coeff");
    ComputeScore(context, *iter, enable_cluster, cluster_id_accessor_,
        output_cluster_score_accessor, positive_cluster_set, negative_cluster_set,
        positive_item_count, negative_item_count, "cluster_id_adjust_alpha_coeff",
        "cluster_id_adjust_beta_coeff");
    ComputeScore(context, *iter, enable_hetu2, hetu_level2_accessor_,
        output_hetu2_score_accessor, positive_hetu2_set, negative_hetu2_set,
        positive_item_count, negative_item_count, "hetu_level2_adjust_alpha_coeff",
        "hetu_level2_adjust_beta_coeff");
    ComputeScore(context, *iter, enable_hashtag, hashtag_accessor_,
        output_hashtag_score_accessor, positive_hashtag_set, negative_hashtag_set,
        positive_item_count, negative_item_count, "hashtag_adjust_alpha_coeff",
        "hashtag_adjust_beta_coeff");
    ComputeScore(context, *iter, enable_hetu_tag, tagnex_accessor_,
        output_hetu_tag_score_accessor, positive_hetu_tag_set, negative_hetu_tag_set,
        positive_item_count, negative_item_count, "hetu_tag_adjust_alpha_coeff",
        "hetu_tag_adjust_beta_coeff");
  }
}

// 记录正/负向兴趣
void ExploreShortItemAdjustEnricher::CollectInterests(MutableRecoContextInterface *context,
    bool is_positive, bool enable_tagnex, bool enable_cluster, bool enable_hetu2,
    bool enable_hashtag, bool enable_hetu_tag, folly::F14FastSet<int64> *tagnex_set,
    folly::F14FastSet<int64> *cluster_set, folly::F14FastSet<int64> *hetu2_set,
    folly::F14FastSet<int64> *hashtag_set, folly::F14FastSet<int64> *hetu_tag_set,
    int *item_count) {
  auto attr_min = GetIntProcessorParameter(context, "attr_min", 200000000);
  auto attr_max = GetIntProcessorParameter(context, "attr_max", 300000000);
  auto time_window = GetIntProcessorParameter(context, "time_window", 7200);
  auto item_list = is_positive ?
      explore::GetIntListCommonAttr(context, config(), "click_list_attr") :
      explore::GetIntListCommonAttr(context, config(), "realshow_list_attr");
  auto realshow_list_timestamp = explore::GetIntListCommonAttr(context,
      config(), "realshow_list_timestamp_attr");
  auto realshow_list_label = explore::GetIntListCommonAttr(context,
      config(), "realshow_list_label_attr");

  if (!item_list || (!is_positive && (!realshow_list_timestamp || !realshow_list_label))) {
    return;
  }
  if (!is_positive && (item_list->size() != realshow_list_timestamp->size() ||
      item_list->size() != realshow_list_label->size())) {
    return;
  }

  int64 stop_timestamp = base::GetTimestamp() / base::Time::kMicrosecondsPerSecond - time_window;
  for (size_t i = 0; i < item_list->size(); i++) {
    if (!is_positive) {
      if ((*realshow_list_timestamp)[i] / base::Time::kMillisecondsPerSecond < stop_timestamp) {
        continue;
      }
      if (((*realshow_list_label)[i] & 1) != 0) {
        continue;
      }
    }
    (*item_count)++;
    int64 item_id = (*item_list)[i];
    // tagnex
    if (enable_tagnex && tagnex_accessor_) {
      if (auto item_tagnex_list = context->GetIntListItemAttr(item_id, tagnex_accessor_)) {
        for (auto tagnex : *item_tagnex_list) {
          if (tagnex >= attr_min && tagnex <= attr_max) {
            tagnex_set->insert(tagnex);
          }
        }
      }
    }
    // cluster_id
    if (enable_cluster && cluster_id_accessor_) {
      if (auto item_cluster_id = context->GetIntItemAttr(item_id, cluster_id_accessor_)) {
        cluster_set->insert(*item_cluster_id);
      }
    }
    // hetu_level2
    if (enable_hetu2 && hetu_level2_accessor_) {
      if (auto item_hetu2_list = context->GetIntListItemAttr(item_id, hetu_level2_accessor_)) {
        for (auto hetu2_id : *item_hetu2_list) {
          hetu2_set->insert(hetu2_id);
        }
      }
    }
    // hashtag
    if (enable_hashtag && hashtag_accessor_) {
      if (auto item_hashtag_list = context->GetIntListItemAttr(item_id, hashtag_accessor_)) {
        for (auto hashtag : *item_hashtag_list) {
          hashtag_set->insert(hashtag);
        }
      }
    }
     // hetu_tag
    if (enable_hetu_tag && tagnex_accessor_) {
      if (auto item_tagnex_list = context->GetIntListItemAttr(item_id, tagnex_accessor_)) {
        for (auto tagnex : *item_tagnex_list) {
          if (tagnex >= CONST_TAG_CONTENT_LOWER_ && tagnex <= CONST_TAG_CONTENT_UPPER_) {
            hetu_tag_set->insert(tagnex);
          }
        }
      }
    }
  }
}

// 计算各个兴趣维度的分数
void ExploreShortItemAdjustEnricher::ComputeScore(MutableRecoContextInterface *context,
    const CommonRecoResult& item, bool enable, ItemAttr* input_accessor, ItemAttr *output_accessor,
    const folly::F14FastSet<int64> &positive_set, const folly::F14FastSet<int64> &negative_set,
    int positive_item_count, int negative_item_count, const std::string& alpha_param_name,
    const std::string& beta_param_name) {
  if (!enable || !input_accessor || !output_accessor) return;
  auto enable_use_set_ratio = GetIntProcessorParameter(context, "enable_use_set_ratio", 0);
  double pos_cnt = 0.0, neg_cnt = 0.0;
  double valid_interest_coeff = GetDoubleProcessorParameter(context, "valid_interest_coeff", 1.0);
  double invalid_interest_coeff = GetDoubleProcessorParameter(context, "invalid_interest_coeff", 1.0);
  double interest_coeff = 1.0;
  bool is_cluster_dimension = (input_accessor == cluster_id_accessor_);
  switch (input_accessor->value_type) {
    case AttrType::INT: {
      if (auto val = context->GetIntItemAttr(item, input_accessor)) {
        pos_cnt += positive_set.count(*val);
        neg_cnt += negative_set.count(*val);
        if (is_cluster_dimension) {
          if (valid_interest_cids_set_.count(*val) > 0) {
            interest_coeff = valid_interest_coeff;
          } else {
            interest_coeff = invalid_interest_coeff;
          }
        }
      }
      break;
    }
    case AttrType::INT_LIST: {
      if (auto item_list = context->GetIntListItemAttr(item, input_accessor)) {
        for (auto v : *item_list) {
          pos_cnt += positive_set.count(v);
          neg_cnt += negative_set.count(v);
        }
      }
      break;
    }
    default:
      break;
  }
  double alpha = GetDoubleProcessorParameter(context, alpha_param_name, 0.0);
  double beta = GetDoubleProcessorParameter(context, beta_param_name, 0.0);
  double ratio_positive = GetDoubleProcessorParameter(context, "ratio_positive_coeff", 1.0);
  double ratio_negative = GetDoubleProcessorParameter(context, "ratio_negative_coeff", 1.0);
  double min_ratio = GetDoubleProcessorParameter(context, "min_ratio_coeff", 0.05);
  double max_ratio = GetDoubleProcessorParameter(context, "max_ratio_coeff", 0.95);

  if (enable_use_set_ratio) {
    int total_set_size = positive_set.size() + negative_set.size();
    if (total_set_size > 0 && !positive_set.empty() && !negative_set.empty()) {
      ratio_positive = static_cast<double>(positive_set.size()) / total_set_size;
      ratio_negative = static_cast<double>(negative_set.size()) / total_set_size;
    }
  } else {
    int total_count = positive_item_count + negative_item_count;
    if (total_count > 0 && positive_item_count > 0 && negative_item_count > 0) {
      ratio_positive = static_cast<double>(positive_item_count) / total_count;
      ratio_negative = static_cast<double>(negative_item_count) / total_count;
    }
  }
  ratio_positive = std::clamp(ratio_positive, min_ratio, max_ratio);
  ratio_negative = std::clamp(ratio_negative, min_ratio, max_ratio);
  double factor = (1.0 + alpha * pos_cnt * ratio_negative) /
                  (1.0 + beta * neg_cnt * ratio_positive * interest_coeff);
  factor = std::clamp(factor, 0.0, 5.0);
  context->SetDoubleItemAttr(item, output_accessor, factor);
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, ExploreShortItemAdjustEnricher,
                 ExploreShortItemAdjustEnricher)

}  // namespace platform
}  // namespace ks
