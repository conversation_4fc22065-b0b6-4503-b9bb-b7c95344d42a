#include "dragon/src/processor/ext/explore/enricher/explore_interest_tagnex_chase_enricher.h"
#include "dragon/src/processor/ext/explore/util/explore_util.h"
#include <algorithm>

namespace ks {
namespace platform {

void ExploreInterestTagnexChaseEnricher::Enrich(MutableRecoContextInterface *context,
    RecoResultConstIter begin, RecoResultConstIter end) {
  auto user_positive_pid_list = context->GetIntListCommonAttr(user_positive_pid_list_attr_);
  if (!user_positive_pid_list) {
    return;
  }
  auto user_weight = context->GetDoubleCommonAttr(user_weight_attr_).value_or(0.0);
  auto tagnex_min_val = GetIntProcessorParameter(context, "tagnex_min_val", 200000000);
  auto tagnex_max_val = GetIntProcessorParameter(context, "tagnex_max_val", 300000000);
  auto adjust_coeff = GetDoubleProcessorParameter(context, "adjust_coeff", 1.0);
  auto scale_bound = GetDoubleProcessorParameter(context, "scale_bound", 5.0);
  auto base_weight = GetDoubleProcessorParameter(context, "base_weight", 1.0);
  tagnex_accessor_ = context->GetItemAttrAccessor(tagnex_attr_);

  // 正向兴趣 tagnex 集合
  folly::F14FastSet<int64> positive_tagnex_set;
  for (int i = 0; i < user_positive_pid_list->size(); i++) {
    auto item_tagnex_list = context->GetIntListItemAttr(
      user_positive_pid_list->at(i), tagnex_accessor_);
    if (item_tagnex_list) {
      for (auto tagnex : *item_tagnex_list) {
        if (tagnex >= tagnex_min_val && tagnex <= tagnex_max_val) {
          positive_tagnex_set.insert(tagnex);
        }
      }
    }
  }
  if (positive_tagnex_set.size() == 0) {
    return;
  }

  // 计算兴趣追打分数
  auto output_score_accessor = context->GetItemAttrAccessor(output_score_attr_);
  if (output_score_accessor) {
    for (auto iter = begin; iter != end; ++iter) {
      double pos_cnt = 0.0;
      auto item_tagnex_list = context->GetIntListItemAttr(*iter, tagnex_accessor_);
      if (item_tagnex_list) {
        for (auto tagnex : *item_tagnex_list) {
          if (tagnex >= tagnex_min_val && tagnex <= tagnex_max_val
              && positive_tagnex_set.count(tagnex)) {
            pos_cnt += 1.0;
          }
        }
        if (pos_cnt > 0) {
          double factor = (1.0 + adjust_coeff * user_weight) * pos_cnt;
          factor = std::max(1.0, std::min(scale_bound, factor));
          double score = factor * base_weight;
          context->SetDoubleItemAttr(*iter, output_score_accessor, score);
        }
      }
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, ExploreInterestTagnexChaseEnricher,
                 ExploreInterestTagnexChaseEnricher)

}  // namespace platform
}  // namespace ks
