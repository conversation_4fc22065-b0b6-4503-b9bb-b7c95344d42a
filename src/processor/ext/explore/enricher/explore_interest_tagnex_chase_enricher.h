#pragma once

#include <string>
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class ExploreInterestTagnexChaseEnricher : public CommonRecoBaseEnricher {
 public:
  ExploreInterestTagnexChaseEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    user_positive_pid_list_attr_ = config()->GetString("user_positive_pid_list_attr", "");
    user_weight_attr_ = config()->GetString("user_weight_attr", "");
    tagnex_attr_ = config()->GetString("tagnex_attr", "");
    output_score_attr_ = config()->GetString("output_score_attr", "");
    if (tagnex_attr_.empty() || output_score_attr_.empty()) {
      CL_LOG(ERROR) << "ExploreInterestTagnexChaseEnricher"
                    << " init failed! Missing \"tagnex_attr\" or \"output_score_attr\" config.";
      return false;
    }
    return true;
  }

  std::string user_positive_pid_list_attr_;
  std::string user_weight_attr_;
  std::string tagnex_attr_;
  std::string output_score_attr_;
  ItemAttr* tagnex_accessor_ = nullptr;

  DISALLOW_COPY_AND_ASSIGN(ExploreInterestTagnexChaseEnricher);
};

}  // namespace platform
}  // namespace ks
