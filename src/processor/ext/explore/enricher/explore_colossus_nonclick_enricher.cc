#include "dragon/src/processor/ext/explore/enricher/explore_colossus_nonclick_enricher.h"

namespace ks {
namespace platform {

void ExploreColossusNonclickEnricher::Enrich(
  MutableRecoContextInterface *context,
  RecoResultConstIter begin,
  RecoResultConstIter end) {
  InitCommonParameters(context);
  InitHetuMapping();
  folly::F14FastMap<int32, double> consecutive_nonclick_exit_intensity;
  folly::F14FastMap<int32, int64> colossus_hetu_two_count;
  CollectColossusNonclick(context, &consecutive_nonclick_exit_intensity, &colossus_hetu_two_count);
  CollectItemTagNonclickExitIntensity(context, begin, end, consecutive_nonclick_exit_intensity,
                                      colossus_hetu_two_count);
}

void ExploreColossusNonclickEnricher::InitCommonParameters(MutableRecoContextInterface* context) {
  colossus_profile_recent_minutes_ = GetIntProcessorParameter(context, "colossus_profile_recent_minutes");
  enable_only_fountain_nonclick_ = GetBoolProcessorParameter(
                                        context, "enable_only_fountain_nonclick");
  enable_handle_hetu_two_missing_item_ = GetBoolProcessorParameter(
                                        context, "enable_handle_hetu_two_missing_item");
  enable_time_aware_nonclick_weight_ = GetBoolProcessorParameter(
                                        context, "enable_time_aware_nonclick_weight");
  hetu_two_missing_item_scaling_ratio_ = GetDoubleProcessorParameter(
                                        context, "hetu_two_missing_item_scaling_ratio");
  int32 positive_interact_nonclick_handle_mode_input = GetIntProcessorParameter(
                                        context, "positive_interact_nonclick_handle_mode");
  InitPositiveInteractHandleMode(positive_interact_nonclick_handle_mode_input);
  time_aware_nonclick_weight_slope_ = GetDoubleProcessorParameter(
                                        context, "time_aware_nonclick_weight_slope");
  time_aware_nonclick_weight_bias_ = GetDoubleProcessorParameter(
                                        context, "time_aware_nonclick_weight_bias");
  short_view_play_time_threshold_ = GetIntProcessorParameter(context, "short_view_play_time_threshold");
}

void ExploreColossusNonclickEnricher::InitHetuMapping() {
  hetu_level4_level2_map_ = hetu_level4_level2_map_ptr_->Get();
  hetu_level3_level2_map_ = hetu_level3_level2_map_ptr_->Get();
}

void ExploreColossusNonclickEnricher::InitPositiveInteractHandleMode(
  const int32 &positive_interact_nonclick_handle_mode_input) {
  switch (positive_interact_nonclick_handle_mode_input) {
    case (1):
      positive_interact_nonclick_handle_mode_ = PositiveInteractHandleMode::SKIP;
      break;
    case (2):
      positive_interact_nonclick_handle_mode_ = PositiveInteractHandleMode::INTERRUPT;
      break;
    default:
      positive_interact_nonclick_handle_mode_ = PositiveInteractHandleMode::NOTHING;
  }
}

void ExploreColossusNonclickEnricher::CollectColossusNonclick(
  MutableRecoContextInterface *context,
  folly::F14FastMap<int32, double> *consecutive_nonclick_exit_intensity,
  folly::F14FastMap<int32, int64> *colossus_hetu_two_count) {
  uint64 current_time = base::GetTimestamp() / base::Time::kMicrosecondsPerSecond;
  const auto* items_ptr = context->GetPtrCommonAttr<std::pair<const void*, int32>>(colossus_v2_attr_name_);
  if (!items_ptr || !items_ptr->first || items_ptr->second == 0) {
    CL_LOG(WARNING) << "colossus v2 items empty";
    return;
  }
  const auto* temp_ptr = reinterpret_cast<const colossus::SimItemV2T *>(items_ptr->first);
  folly::F14FastSet<int32> tag_has_found_click;
  // 按时间戳从大到小遍历
  for (int i = items_ptr->second - 1; i >= 0; --i) {
    const auto *item_ptr = temp_ptr + i;
    if (item_ptr == nullptr) {
      continue;
    }
    if (enable_only_fountain_nonclick_ && item_ptr->channel != FOUNTAIN_CHANNEL_) {
      continue;
    }
    // 不在 colossus_profile_recent_minutes_ 分钟内的不统计
    uint64 time_gap = current_time - item_ptr->timestamp;
    if (time_gap > colossus_profile_recent_minutes_ * base::Time::kSecondsPerMinute) {
      break;
    }
    int32 hetu_level_two = 0;
    hetu_level_two = explore::GetHetuLevelTwo(item_ptr->tag, hetu_level4_level2_map_,
                                              hetu_level3_level2_map_);
    if (hetu_level_two <= 0) {
      continue;
    }
    // 已经找到最近点击的标签不再累积连续不点击次数
    if (tag_has_found_click.find(hetu_level_two) != tag_has_found_click.end()) {
      continue;
    }
    bool is_short_view = item_ptr->play_time <= short_view_play_time_threshold_;
    if (!is_short_view) {
      tag_has_found_click.insert(hetu_level_two);
      continue;
    }
    // 处理正向互动
    // SKIP: 跳过正向互动 item 继续计数
    // INTERRUPT: 视为非短播，中断连续计数
    if (positive_interact_nonclick_handle_mode_ != PositiveInteractHandleMode::NOTHING) {
      uint32 label = item_ptr->label;
      bool like = label & 0x01;
      bool follow = label & (1 << 1);
      bool forward = label & (1 << 2);
      bool report = label & (1 << 3);
      bool comment = label & (1 << 4);
      bool has_entered_profile = label & (1 << 6);
      bool has_comment_stay_time_value = label & (1 << 8);
      bool dislike = label & (1 << 13);
      if ((like || follow || forward || comment || has_entered_profile || has_comment_stay_time_value)
          && !(report || dislike)) {
        if (positive_interact_nonclick_handle_mode_ == PositiveInteractHandleMode::SKIP) {
          continue;
        } else if (positive_interact_nonclick_handle_mode_ == PositiveInteractHandleMode::INTERRUPT) {
          tag_has_found_click.insert(hetu_level_two);
          continue;
        }
      }
    }

    (*consecutive_nonclick_exit_intensity)[hetu_level_two] += enable_time_aware_nonclick_weight_ ?
      time_aware_nonclick_weight_slope_ * time_gap / base::Time::kSecondsPerMinute +
                                                            time_aware_nonclick_weight_bias_ : 1.0;
    ++(*colossus_hetu_two_count)[hetu_level_two];
    ++(*colossus_hetu_two_count)[0];
  }
}

void ExploreColossusNonclickEnricher::CollectItemTagNonclickExitIntensity(
  MutableRecoContextInterface* context, RecoResultConstIter begin, RecoResultConstIter end,
  const folly::F14FastMap<int32, double> &consecutive_nonclick_exit_intensity,
  const folly::F14FastMap<int32, int64> &colossus_hetu_two_count) {
  hetu_accessor_ = context->GetItemAttrAccessor(hetu_attr_);
  consecutive_nonclick_exit_intensity_accessor_ = context->GetItemAttrAccessor(
                                                            save_consecutive_nonclick_exit_intensity_);
  double predicted_nonclick_for_hetu_two_missing_item = 0.0;
  if (enable_handle_hetu_two_missing_item_) {
    auto colossus_hetu_two_total_count_iter = colossus_hetu_two_count.find(0);
    if (colossus_hetu_two_total_count_iter != colossus_hetu_two_count.end() &&
        colossus_hetu_two_total_count_iter->second > 0) {
      predicted_nonclick_for_hetu_two_missing_item = std::accumulate(
        colossus_hetu_two_count.begin(), colossus_hetu_two_count.end(), 0.0,
        [&](double predicted_nonclick, const auto& iter) {
        double prob = static_cast<double>(iter.second) / colossus_hetu_two_total_count_iter->second;
        auto it = consecutive_nonclick_exit_intensity.find(iter.first);
        if (it != consecutive_nonclick_exit_intensity.end()) {
          return predicted_nonclick + prob * it->second;
        } else {
          return predicted_nonclick;
        }
      });
    }
  }

  for (auto iter = begin; iter != end; ++iter) {
    auto hetu_list = context->GetIntListItemAttr(*iter, hetu_accessor_);
    if (hetu_list && hetu_list->size() > 0) {
      int32 hetu_tag = hetu_list->at(0);
      auto consecutive_nonclick_exit_intensity_iter = consecutive_nonclick_exit_intensity.find(hetu_tag);
      if (consecutive_nonclick_exit_intensity_iter != consecutive_nonclick_exit_intensity.end()) {
        context->SetDoubleItemAttr(*iter, consecutive_nonclick_exit_intensity_accessor_,
                    consecutive_nonclick_exit_intensity_iter->second);
      }
    } else if (enable_handle_hetu_two_missing_item_ && predicted_nonclick_for_hetu_two_missing_item > 0) {
      context->SetDoubleItemAttr(*iter, consecutive_nonclick_exit_intensity_accessor_,
                  hetu_two_missing_item_scaling_ratio_ * predicted_nonclick_for_hetu_two_missing_item);
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, ExploreColossusNonclickEnricher, ExploreColossusNonclickEnricher)

}  // namespace platform
}  // namespace ks
