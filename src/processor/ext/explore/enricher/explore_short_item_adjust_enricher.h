#pragma once

#include <string>
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "folly/container/F14Set.h"

namespace ks {
namespace platform {

class ExploreShortItemAdjustEnricher : public CommonRecoBaseEnricher {
 public:
  ExploreShortItemAdjustEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    tagnex_attr_ = config()->GetString("tagnex_attr", "");
    cluster_id_attr_ = config()->GetString("cluster_id_attr", "");
    hetu_level2_attr_ = config()->GetString("hetu_level2_attr", "");
    hashtag_attr_ = config()->GetString("hashtag_attr", "");
    output_tagnex_score_attr_ = config()->GetString("output_tagnex_score_attr", "");
    output_cluster_score_attr_ = config()->GetString("output_cluster_score_attr", "");
    output_hetu2_score_attr_ = config()->GetString("output_hetu2_score_attr", "");
    output_hashtag_score_attr_ = config()->GetString("output_hashtag_score_attr", "");
    output_hetu_tag_score_attr_ = config()->GetString("output_hetu_tag_score_attr", "");
    if (output_tagnex_score_attr_.empty()) {
      CL_LOG(ERROR) << "ExploreShortItemAdjustEnricher"
                    << " init failed! Missing \"output_tagnex_score_attr\" config.";
      return false;
    }
    if (output_cluster_score_attr_.empty()) {
      CL_LOG(ERROR) << "ExploreShortItemAdjustEnricher"
                    << " init failed! Missing \"output_cluster_score_attr\" config.";
      return false;
    }
    if (output_hetu2_score_attr_.empty()) {
      CL_LOG(ERROR) << "ExploreShortItemAdjustEnricher"
                    << " init failed! Missing \"output_hetu2_score_attr\" config.";
      return false;
    }
    if (output_hashtag_score_attr_.empty()) {
      CL_LOG(ERROR) << "ExploreShortItemAdjustEnricher"
                    << " init failed! Missing \"output_hashtag_score_attr\" config.";
      return false;
    }
    if (output_hetu_tag_score_attr_.empty()) {
      CL_LOG(ERROR) << "ExploreShortItemAdjustEnricher"
                    << " init failed! Missing \"output_hetu_tag_score_attr\" config.";
      return false;
    }
    return true;
  }


  void CollectInterests(MutableRecoContextInterface *context,
      bool is_positive, bool enable_tagnex, bool enable_cluster, bool enable_hetu2,
      bool enable_hashtag, bool enable_hetu_tag, folly::F14FastSet<int64> *tagnex_set,
      folly::F14FastSet<int64> *cluster_set, folly::F14FastSet<int64> *hetu2_set,
      folly::F14FastSet<int64> *hashtag_set, folly::F14FastSet<int64> *hetu_tag_set,
      int *item_count);
  void ComputeScore(MutableRecoContextInterface *context,
      const CommonRecoResult& item, bool enable, ItemAttr* input_accessor,
      ItemAttr *output_accessor, const folly::F14FastSet<int64> &positive_set,
      const folly::F14FastSet<int64> &negative_set, int positive_item_count,
      int negative_item_count, const std::string& alpha_param_name,
      const std::string& beta_param_name);

 private:
  std::string tagnex_attr_;
  std::string cluster_id_attr_;
  std::string hetu_level2_attr_;
  std::string hashtag_attr_;
  std::string output_tagnex_score_attr_;
  std::string output_cluster_score_attr_;
  std::string output_hetu2_score_attr_;
  std::string output_hashtag_score_attr_;
  std::string output_hetu_tag_score_attr_;
  ItemAttr* tagnex_accessor_ = nullptr;
  ItemAttr* cluster_id_accessor_ = nullptr;
  ItemAttr* hetu_level2_accessor_ = nullptr;
  ItemAttr* hashtag_accessor_ = nullptr;
  static const int CONST_TAG_CONTENT_LOWER_ = 500000;
  static const int CONST_TAG_CONTENT_UPPER_ = 4000000;
  folly::F14FastSet<int64> valid_interest_cids_set_;

  DISALLOW_COPY_AND_ASSIGN(ExploreShortItemAdjustEnricher);
};

}  // namespace platform
}  // namespace ks
