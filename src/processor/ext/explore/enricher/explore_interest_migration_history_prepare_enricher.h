#pragma once

#include <string>
#include <vector>
#include <memory>
#include <map>
#include <utility>

#include "ks/reco_proto/proto/reco.pb.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/processor/ext/explore/util/explore_util.h"
#include "teams/reco-arch/colossus/flat_generated/flat_generated_helper.h"
#include "teams/reco-arch/colossus/flat_generated/long_term_service_generated.h"
#include "teams/reco-arch/colossus/item/common_item_types.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"

namespace ks {
namespace platform {
class ExploreInterestMigrationHistoryPrepareEnricher : public CommonRecoBaseEnricher {
 public:
  ExploreInterestMigrationHistoryPrepareEnricher() {}

  bool IsAsync() const override {
    return false;
  }

  void Enrich(
      MutableRecoContextInterface *context,
      RecoResultConstIter begin,
      RecoResultConstIter end) override;
  // 初始化一些 ab 参数
  void InitCommonParameters(MutableRecoContextInterface *context);
  void CollectMigrationPhotos(MutableRecoContextInterface *context);
  void CollectRealshowPhotos(MutableRecoContextInterface *context);
  double CalculateTimeScore(const colossus::SimItemV2T *item_ptr);
  double CalculateActiveScore(const colossus::SimItemV2T *item_ptr);
  double GetUserPagePreferScore(int hot_vv_cnt, int bs_vv_cnt,
      int hot_play_time_cnt, int bs_play_time_cnt,
      double hot_active_score, double bs_active_score);

 private:
  bool InitProcessor() override {
    colossus_v2_attr_ = config()->GetString("colossus_v2_attr_name", "");
    // colossus 信息缺失直接退出
    if (colossus_v2_attr_.empty()) {
      return false;
    }
    user_info_ptr_attr_ = config()->GetString("user_info_ptr_name", "");
    ignore_channel_attr_ = config()->GetString("ignore_channel_name", "");
    output_id_list_name_ = config()->GetString("output_id_list_name", "");
    if (output_id_list_name_.empty()) {
      return false;
    }
    output_score_list_name_ = config()->GetString("output_score_list_name", "");
    if (output_score_list_name_.empty()) {
      return false;
    }
    output_realshow_list_name_ = config()->GetString("output_realshow_list_name", "");
    if (output_realshow_list_name_.empty()) {
      return false;
    }
    output_is_degraded_flag_name_ = config()->GetString("output_is_degraded_flag_name", "");
    output_user_page_prefer_score_name_ = config()->GetString("output_user_page_prefer_score_name", "");
    bs_short_view_output_id_list_name_ = config()->GetString("bs_short_view_output_id_list_name", "");
    return true;
    return true;
  }

  std::string colossus_v2_attr_;
  std::string user_info_ptr_attr_;
  std::string ignore_channel_attr_;
  std::string output_id_list_name_;
  std::string output_score_list_name_;
  std::string output_realshow_list_name_;
  std::string output_is_degraded_flag_name_;
  std::string output_user_page_prefer_score_name_;
  std::string bs_short_view_output_id_list_name_;

  int time_second_upper_ = 604800;
  int colossus_num_limit_ = 1000;
  int realshow_num_limit_ = 100;
  int hot_cnt_threshold_ = 100;
  int long_rate_vv_threshold_ = 100;
  int bs_short_view_mins_upper_ = 0;
  int bs_short_view_num_upper_ = 0;

  double playtime_weight_ = 1.0;
  double not_effective_view_weight_ = 0.7;
  double like_weight_ = 3.0;
  double follow_weight_ = 7.0;
  double forward_weight_ = 3.0;
  double comment_weight_ = 3.0;
  double profile_weight_ = 3.0;
  double collection_weight_ = 3.0;
  double hot_rate_threshold_ = 1.0;
  double vv_rate_weight_ = 1.0;
  double play_time_rate_weight_ = 0.0;
  double active_rate_weight_ = 0.0;

  bool enable_collection_list_ = false;

  const ks::reco::UserInfo *user_info_ptr_ = nullptr;

  static constexpr int CHANNEL_HOT = 1;
  static constexpr int CHANNEL_BOTTOM_SELECTION = 37;
  static constexpr int CHANNEL_DETAIL_FOUNTAIN = 77;

  folly::F14FastSet<int64> ignore_channel_set_;
  DISALLOW_COPY_AND_ASSIGN(ExploreInterestMigrationHistoryPrepareEnricher);
};

}  // namespace platform
}  // namespace ks
