#pragma once

#include <string>
#include <memory>
#include <map>
#include <utility>
#include <numeric>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/processor/ext/explore/util/explore_util.h"
#include "teams/reco-arch/colossus/item/common_item_types.h"

namespace ks {
namespace platform {
// 从 colossus 里统计 Item 对应的河图标签近期连续曝光未点击的次数
// 输出 consecutive_nonclick_exit_intensity 一个 Double ItemAttr
class ExploreColossusNonclickEnricher : public CommonRecoBaseEnricher {
 public:
  ExploreColossusNonclickEnricher() {}

  bool IsAsync() const override {
    return false;
  }

  void Enrich(
    MutableRecoContextInterface *context,
    RecoResultConstIter begin,
    RecoResultConstIter end) override;
  // 初始化一些 ab 参数
  void InitCommonParameters(MutableRecoContextInterface *context);
  void InitHetuMapping();
  void InitPositiveInteractHandleMode(const int32 &positive_interact_nonclick_handle_mode_input);
  void CollectColossusNonclick(
    MutableRecoContextInterface *context,
    folly::F14FastMap<int32, double> *consecutive_nonclick_exit_intensity,
    folly::F14FastMap<int32, int64> *colossus_hetu_two_count);
  void CollectItemTagNonclickExitIntensity(
    MutableRecoContextInterface* context, RecoResultConstIter begin, RecoResultConstIter end,
    const folly::F14FastMap<int32, double> &consecutive_nonclick_exit_intensity,
    const folly::F14FastMap<int32, int64> &colossus_hetu_two_count);

 private:
  enum class PositiveInteractHandleMode {
    NOTHING = 0,
    SKIP = 1,
    INTERRUPT = 2
  };

  bool InitProcessor() override {
    colossus_v2_attr_name_ = config()->GetString("colossus_v2_attr_name");
    save_consecutive_nonclick_exit_intensity_ = config()->GetString(
                                      "save_consecutive_nonclick_exit_intensity_to_attr");
    hetu_attr_ = config()->GetString("hetu_attr");
    auto default_val = std::make_shared<std::map<std::string, int32>>();
    auto parser = [](const std::string &key, std::string *val) -> bool {
      *val = key;
      return true;
    };
    // kconf 初始化
    hetu_level4_level2_map_ptr_ =
      ks::infra::KConf().GetMap<std::string, int32>("reco.offline.hetutagLevel4ToLevel2",
                                            default_val, parser);
    hetu_level3_level2_map_ptr_ =
      ks::infra::KConf().GetMap<std::string, int32>("reco.offline.hetutagLevel3ToLevel2",
                                            default_val, parser);
    // 这里定一下 colossus 信息缺失直接退出
    if (colossus_v2_attr_name_.empty()) {
      return false;
    }
    return true;
  }

  std::string colossus_v2_attr_name_;
  std::string save_consecutive_nonclick_exit_intensity_;
  std::string hetu_attr_;
  int32 colossus_profile_recent_minutes_ = 30;
  bool enable_only_fountain_nonclick_ = false;
  bool enable_handle_hetu_two_missing_item_ = false;
  bool enable_time_aware_nonclick_weight_ = false;
  double hetu_two_missing_item_scaling_ratio_ = 1.0;
  double time_aware_nonclick_weight_slope_ = 0.0;
  double time_aware_nonclick_weight_bias_ = 1.0;
  int32 short_view_play_time_threshold_ = 3;
  PositiveInteractHandleMode positive_interact_nonclick_handle_mode_ = PositiveInteractHandleMode::NOTHING;

  // kconf 配置：获取河图二级标签
  std::shared_ptr<ks::infra::KsConfig<std::shared_ptr
                <std::map<std::string, int32>>>> hetu_level4_level2_map_ptr_;
  std::shared_ptr<ks::infra::KsConfig<std::shared_ptr
                <std::map<std::string, int32>>>> hetu_level3_level2_map_ptr_;
  std::shared_ptr<std::map<std::string, int32>> hetu_level4_level2_map_;
  std::shared_ptr<std::map<std::string, int32>> hetu_level3_level2_map_;

  ItemAttr* hetu_accessor_ = nullptr;
  ItemAttr* consecutive_nonclick_exit_intensity_accessor_ = nullptr;

  static const uint32 FOUNTAIN_CHANNEL_ = 77;
  DISALLOW_COPY_AND_ASSIGN(ExploreColossusNonclickEnricher);
};

}  // namespace platform
}  // namespace ks
