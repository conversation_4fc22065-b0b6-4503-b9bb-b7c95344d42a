#include "dragon/src/processor/ext/explore/enricher/explore_interest_migration_history_prepare_enricher.h"

#include <boost/math/distributions.hpp>
#include <algorithm>
#include <utility>

namespace ks {
namespace platform {

void ExploreInterestMigrationHistoryPrepareEnricher::Enrich(
    MutableRecoContextInterface *context,
    RecoResultConstIter begin,
    RecoResultConstIter end) {
  InitCommonParameters(context);
  CollectMigrationPhotos(context);
  CollectRealshowPhotos(context);
}

void ExploreInterestMigrationHistoryPrepareEnricher::InitCommonParameters(
    MutableRecoContextInterface* context) {
  time_second_upper_ = GetIntProcessorParameter(context, "time_scecond_upper", 604800);
  colossus_num_limit_ = GetIntProcessorParameter(context, "colossus_num_limit_attr", 1000);
  realshow_num_limit_ = GetIntProcessorParameter(context, "realshow_num_limit_attr", 100);
  hot_cnt_threshold_ = GetIntProcessorParameter(context, "hot_cnt_threshold_attr", 100);
  long_rate_vv_threshold_ = GetIntProcessorParameter(context, "long_rate_vv_threshold_attr", 100);

  playtime_weight_ = GetDoubleProcessorParameter(context, "playtime_weight", 1.0);
  not_effective_view_weight_ = GetDoubleProcessorParameter(context, "not_effective_view_weight", 0.7);
  like_weight_ = GetDoubleProcessorParameter(context, "like_weight_attr", 3.0);
  follow_weight_ = GetDoubleProcessorParameter(context, "follow_weight_attr", 7.0);
  forward_weight_ = GetDoubleProcessorParameter(context, "forward_weight_attr", 3.0);
  comment_weight_ = GetDoubleProcessorParameter(context, "comment_weight_attr", 3.0);
  profile_weight_ = GetDoubleProcessorParameter(context, "profile_weight_attr", 3.0);
  collection_weight_ = GetDoubleProcessorParameter(context, "collection_weight_attr", 3.0);
  hot_rate_threshold_ = GetDoubleProcessorParameter(context, "hot_rate_threshold_attr", 1.0);

  vv_rate_weight_ = GetDoubleProcessorParameter(context, "vv_rate_weight_attr", 1.0);
  play_time_rate_weight_ = GetDoubleProcessorParameter(context, "play_time_rate_weight_attr", 0.0);
  active_rate_weight_ = GetDoubleProcessorParameter(context, "active_rate_weight_attr", 0.0);

  enable_collection_list_ = GetDoubleProcessorParameter(context, "enable_collection_list_attr", 0) > 0;

  ignore_channel_set_.clear();
  if (!ignore_channel_attr_.empty()) {
    auto ignore_channel_list = context->GetIntListCommonAttr(ignore_channel_attr_);
    if (ignore_channel_list) {
      ignore_channel_set_.insert(ignore_channel_list->begin(), ignore_channel_list->end());
    }
  }

  if (!user_info_ptr_attr_.empty()) {
    user_info_ptr_ = context->GetProtoMessagePtrCommonAttr<ks::reco::UserInfo>(user_info_ptr_attr_);
  }

  bs_short_view_mins_upper_ = GetIntProcessorParameter(context, "bs_short_view_mins_upper", 30);
  bs_short_view_num_upper_ = GetIntProcessorParameter(context, "bs_short_view_num_upper", 0);
}

void ExploreInterestMigrationHistoryPrepareEnricher::CollectMigrationPhotos(
    MutableRecoContextInterface *context) {
  const auto* items_ptr = context->GetPtrCommonAttr<std::pair<const void*, int>>(colossus_v2_attr_);
  if (!items_ptr || !items_ptr->first || items_ptr->second == 0) {
    CL_LOG(WARNING) << "colossus v2 items empty";
    return;
  }
  const auto* item_begin_ptr = reinterpret_cast<const colossus::SimItemV2T *>(items_ptr->first);
  std::vector<int64> bs_short_view_output_id_list;
  bs_short_view_output_id_list.reserve(bs_short_view_num_upper_);
  std::vector<int64> output_id_list;
  std::vector<double> output_score_list;
  output_id_list.reserve(colossus_num_limit_);
  output_score_list.reserve(colossus_num_limit_);
  int valid_cnt = 0;
  int hot_vv_cnt = 0;
  int bs_vv_cnt = 0;
  int hot_play_time_cnt = 0;
  int bs_play_time_cnt = 0;
  double hot_active_score = 0.0;
  double bs_active_score = 0.0;
  uint64 current_time = base::GetTimestamp() / base::Time::kMicrosecondsPerSecond;
  uint64 short_view_max_time_gap = bs_short_view_mins_upper_ * base::Time::kSecondsPerMinute;
  for (int i = 0; i < items_ptr->second; ++i) {
    const auto *item_ptr = item_begin_ptr + i;
    if (item_ptr == nullptr) {
      continue;
    }
    uint32 timestamp = item_ptr->timestamp;
    if (current_time - timestamp > time_second_upper_) {
      continue;
    }
    double is_active = CalculateActiveScore(item_ptr);
    if (item_ptr->channel == CHANNEL_HOT) {
      ++hot_vv_cnt;
      hot_play_time_cnt += item_ptr->play_time;
      hot_active_score += is_active;
    }
    if (item_ptr->channel == CHANNEL_BOTTOM_SELECTION) {
      ++bs_vv_cnt;
      bs_play_time_cnt += item_ptr->play_time;
      bs_active_score += is_active;
      if (bs_short_view_output_id_list.size() < bs_short_view_num_upper_
          && current_time - timestamp <= short_view_max_time_gap) {
        uint32 duration = item_ptr->duration;
        uint32 playtime = item_ptr->play_time;
        if (explore::IsShortView(duration, playtime)) {
          bs_short_view_output_id_list.push_back(item_ptr->photo_id);
        }
      }
    }
    if (ignore_channel_set_.count(item_ptr->channel)) {
      continue;
    }
    double score = CalculateTimeScore(item_ptr);
    output_id_list.push_back(item_ptr->photo_id);
    output_score_list.push_back(score);
    ++valid_cnt;
    if (valid_cnt >= colossus_num_limit_) {
      break;
    }
  }

  if (enable_collection_list_ && user_info_ptr_ && user_info_ptr_->has_user_profile_v1()) {
    const auto &collect_list = user_info_ptr_->user_profile_v1().collect_list();
    for (const auto &action_item : collect_list) {
      if (current_time - action_item.time_ms() / base::Time::kMillisecondsPerSecond
          > time_second_upper_) {
        continue;
      }
      if (action_item.page_type() != ks::reco::RecoEnumSummary::EXPLORE_PAGE) {
        output_id_list.push_back(action_item.photo_id());
        output_score_list.push_back(collection_weight_);
      }
    }
  }

  double hot_rate = (double) hot_vv_cnt / (double) (bs_vv_cnt + 1);
  bool need_to_save_flag = context->GetNeedStepInfo();
  if (hot_rate > hot_rate_threshold_ && hot_vv_cnt > hot_cnt_threshold_) {
    output_id_list.clear();
    output_score_list.clear();
    if (need_to_save_flag && !output_is_degraded_flag_name_.empty()) {
      context->SetIntCommonAttr(output_is_degraded_flag_name_, 1);
    }
  }
  if (!bs_short_view_output_id_list_name_.empty()) {
    context->SetIntListCommonAttr(bs_short_view_output_id_list_name_,
        std::move(bs_short_view_output_id_list));
  }
  context->SetIntListCommonAttr(output_id_list_name_, std::move(output_id_list));
  context->SetDoubleListCommonAttr(output_score_list_name_, std::move(output_score_list));

  double user_page_prefer_score = -1.0;
  if (hot_vv_cnt + bs_vv_cnt > long_rate_vv_threshold_ && hot_vv_cnt <= hot_cnt_threshold_) {
    user_page_prefer_score = GetUserPagePreferScore(hot_vv_cnt, bs_vv_cnt,
                                 hot_play_time_cnt, bs_play_time_cnt,
                                 hot_active_score, bs_active_score);
  }
  if (!output_user_page_prefer_score_name_.empty()) {
    context->SetDoubleCommonAttr(output_user_page_prefer_score_name_, user_page_prefer_score);
  }
}

void ExploreInterestMigrationHistoryPrepareEnricher::CollectRealshowPhotos(
    MutableRecoContextInterface *context) {
  if (!user_info_ptr_ || !user_info_ptr_->has_user_profile_v1()) {
    return;
  }
  std::vector<int64> output_realshow_list;
  output_realshow_list.reserve(realshow_num_limit_);
  int explore_play_count = 0;
  const auto &explore_show_list = user_info_ptr_->user_profile_v1().real_show_list();
  for (const auto &action_item : explore_show_list) {
    if (action_item.page_type() == ks::reco::RecoEnumSummary::EXPLORE_PAGE) {
      output_realshow_list.push_back(action_item.photo_id());
      ++explore_play_count;
      if (explore_play_count >= realshow_num_limit_) {
        break;
      }
    }
  }
  context->SetIntListCommonAttr(output_realshow_list_name_, std::move(output_realshow_list));
}

double ExploreInterestMigrationHistoryPrepareEnricher::CalculateTimeScore(
    const colossus::SimItemV2T *item_ptr) {
  double score = 0.0;
  uint32 duration = item_ptr->duration;
  uint32 playtime = item_ptr->play_time;
  score += playtime_weight_ * playtime / 60.0;
  if (!explore::IsEffectiveView(duration, playtime)) {
    score += not_effective_view_weight_;
  }
  return score;
}

double ExploreInterestMigrationHistoryPrepareEnricher::CalculateActiveScore(
    const colossus::SimItemV2T *item_ptr) {
  double score = 0.0;
  uint16_t label = item_ptr->label;
  if (label & 0x01) {  // like
    score += like_weight_;
  }
  if (label & (1 << 1)) {  // follow
    score += follow_weight_;
  }
  if (label & (1 << 2)) {  // forward
    score += forward_weight_;
  }
  if (label & (1 << 4)) {  // comment
    score += comment_weight_;
  }
  if (label & (1 << 6)) {  // profile
    score += profile_weight_;
  }
  return score;
}

double ExploreInterestMigrationHistoryPrepareEnricher::GetUserPagePreferScore(
    int hot_vv_cnt, int bs_vv_cnt,
    int hot_play_time_cnt, int bs_play_time_cnt,
    double hot_active_score, double bs_active_score) {
  double score = 0.0;
  score += vv_rate_weight_ * (double) bs_vv_cnt / std::max((double) hot_vv_cnt + bs_vv_cnt, 1.0);
  score += play_time_rate_weight_ * (double) bs_play_time_cnt
      / std::max((double) hot_play_time_cnt + bs_play_time_cnt, 1.0);
  score += active_rate_weight_ * bs_active_score / std::max(hot_active_score + bs_active_score, 1.0);
  return score;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, ExploreInterestMigrationHistoryPrepareEnricher,
    ExploreInterestMigrationHistoryPrepareEnricher)

}  // namespace platform
}  // namespace ks
