#pragma once

#include <string>

#include "dragon/src/core/common_reco_context_interface.h"
#include "dragon/src/processor/ext/explore/util/explore_util.h"

namespace ks {
namespace platform {
namespace explore {

class CommonPhotoInfo {
 public:
  explicit CommonPhotoInfo(const folly::F14FastMap<std::string, const ItemAttr *> &item_accessor_map);
  void ResetPhoto(ReadableRecoContextInterface *context, const CommonRecoResult &result);

  inline absl::optional<int64> GetPhotoId() const {
    return photo_id_;
  }

  inline absl::optional<int64> GetAuditHotHighTagLevel() const {
    return audit_hot_high_tag_level_;
  }

  inline absl::optional<int64> GetDurationMs() const {
    return duration_ms_;
  }

  inline absl::optional<int64> GetUploadType() const {
    return upload_type_;
  }

  inline absl::optional<int64> GetShowLevelA() const {
    return show_level_a_;
  }

  inline absl::optional<int64> GetReviewPassLevelB() const {
    return review_pass_level_b_;
  }

  inline absl::optional<int64> GetPhotoTotalReportCount() const {
    return photo_total_report_count_;
  }

  inline absl::optional<int64> GetAuthorTotalReportCount() const {
    return author_total_report_count_;
  }

  inline absl::optional<double> GetTitleEvilLevel() const {
    return title_evil_level_;
  }

  inline absl::optional<double> GetOcrCoverTextEvilLevel() const {
    return ocr_cover_text_evil_level_;
  }

  inline absl::optional<absl::Span<const int64>> GetHetuLevelOneTagList() const {
    return hetu_level_one_tag_list_;
  }

  inline absl::optional<absl::Span<const int64>> GetHetuLevelTwoTagList() const {
    return hetu_level_two_tag_list_;
  }

  inline absl::optional<absl::Span<const int64>> GetHetuLevelThreeTagList() const {
    return hetu_level_three_tag_list_;
  }

  inline absl::optional<absl::Span<const int64>> GetHetuLevelFourTagList() const {
    return hetu_level_four_tag_list_;
  }

  inline absl::optional<absl::Span<const int64>> GetHetuLevelFiveTagList() const {
    return hetu_level_five_tag_list_;
  }

  inline absl::optional<absl::Span<const int64>> GetHetuFaceIdTagList() const {
    return hetu_face_id_tag_list_;
  }

  inline absl::optional<absl::Span<const int64>> GetHetuTagList() const {
    return hetu_tag_list_;
  }

  inline absl::optional<absl::Span<const int64>> GetHetuV2LevelOneTagList() const {
    return hetu_v2_level_one_tag_list_;
  }

  inline absl::optional<absl::Span<const int64>> GetHetuV2LevelTwoTagList() const {
    return hetu_v2_level_two_tag_list_;
  }

  inline absl::optional<absl::Span<const int64>> GetHetuV2LevelThreeTagList() const {
    return hetu_v2_level_three_tag_list_;
  }

  inline absl::optional<absl::Span<const int64>> GetHetuV2TagList() const {
    return hetu_v2_tag_list_;
  }

  inline absl::optional<absl::Span<const int64>> GetManjiaoMarkcode() const {
    return manjiao_markcode_;
  }

  inline absl::optional<absl::Span<const int64>> GetHetuV3LevelOneTagList() const {
    return hetu_v3_level_one_tag_list_;
  }

  inline absl::optional<int64> GetAuditHotCoverLevel() const {
    return audit_hot_cover_level_;
  }

  inline absl::optional<int64> GetCaptionLength() const {
    return caption_length_;
  }

  inline absl::optional<int64> GetPictureType() const {
    return picture_type_;
  }

  inline absl::optional<int64> GetImpressionAudit() const {
    return impression_audit_;
  }

  inline absl::optional<int64> GetAuditBSecondTag() const {
    return audit_b_second_tag_;
  }

  inline absl::optional<int64> GetVideoQualityAssessmentFlag() const {
    return video_quality_assessment_flag_;
  }

  inline absl::optional<int64> GetRiskManRiskPhoto() const {
    return risk_man_risk_photo_;
  }

  inline absl::optional<absl::string_view> GetMmuPhotoLowQualityModel() const {
    return mmu_photo_low_quality_model_;
  }

  inline absl::optional<double> GetMmuLowQualityModelScore40() const {
    return mmu_low_quality_model_score_40_;
  }

  inline absl::optional<double> GetMmuLowQualityModelScore42() const {
    return mmu_low_quality_model_score_42_;
  }

  inline absl::optional<double> GetMmuLowQualityModelScore46() const {
    return mmu_low_quality_model_score_46_;
  }

  inline absl::optional<double> GetMmuLowQualityModelScore52() const {
    return mmu_low_quality_model_score_52_;
  }

  inline absl::optional<double> GetMmuLowQualityModelScore63() const {
    return mmu_low_quality_model_score_63_;
  }

  inline absl::optional<double> GetMmuLowQualityModelScore64() const {
    return mmu_low_quality_model_score_64_;
  }

  inline absl::optional<double> GetMmuLowQualityModelScore90() const {
    return mmu_low_quality_model_score_90_;
  }

  inline absl::optional<double> GetMmuLowQualityModelScore104() const {
    return mmu_low_quality_model_score_104_;
  }

  inline absl::optional<double> GetMmuLowQualityModelScore123() const {
    return mmu_low_quality_model_score_123_;
  }

  inline absl::optional<double> GetMmuLowQualityModelScore143() const {
    return mmu_low_quality_model_score_143_;
  }

  inline absl::optional<double> GetMmuLowQualityModelScore145() const {
    return mmu_low_quality_model_score_145_;
  }

  inline absl::optional<double> GetMmuLowQualityModelScore150() const {
    return mmu_low_quality_model_score_150_;
  }

  inline absl::optional<double> GetMmuLowQualityModelScore151() const {
    return mmu_low_quality_model_score_151_;
  }

  inline absl::optional<double> GetMmuLowQualityModelScore163() const {
    return mmu_low_quality_model_score_163_;
  }

  inline absl::optional<double> GetMmuLowQualityModelScore164() const {
    return mmu_low_quality_model_score_164_;
  }

  inline absl::optional<absl::Span<const int64>> GetMerchantItemIdList() const {
    return merchant_item_id_list_;
  }

  inline absl::optional<double> GetMerchantPhotoCartRelation() const {
    return merchant_photo_cart_relation_;
  }

  inline absl::optional<int64> GetNeedShufflePhoto() const {
    return need_shuffle_photo_;
  }

  inline absl::optional<int64> GetAuthorFansCount() const {
    return author_fans_count_;
  }

  inline absl::optional<int64> GetExploreServerShow() const {
    return explore_server_show_;
  }

  inline absl::optional<int64> GetExploreClick() const {
    return explore_click_;
  }

  inline absl::optional<int64> GetExploreRealShow() const {
    return explore_real_show_;
  }

  inline absl::optional<int64> GetExploreViewLengthSum() const {
    return explore_view_length_sum_;
  }

  inline absl::optional<int64> GetExploreLongPlayCount() const {
    return explore_long_play_count_;
  }

  inline absl::optional<int64> GetExploreNegative() const {
    return explore_negative_;
  }

  inline absl::optional<int64> GetExploreLike() const {
    return explore_like_;
  }

  inline absl::optional<int64> GetExploreFollow() const {
    return explore_follow_;
  }

  inline absl::optional<int64> GetExploreForward() const {
    return explore_forward_;
  }

  inline absl::optional<int64> GetExploreComment() const {
    return explore_comment_;
  }

  inline absl::optional<int64> GetExploreCollect() const {
    return explore_collect_;
  }

  inline absl::optional<int64> GetNebulaRealShow() const {
    return nebula_real_show_;
  }

  inline absl::optional<int64> GetNebulaNegative() const {
    return nebula_negative_;
  }

  inline absl::optional<int64> GetNebulaLike() const {
    return nebula_like_;
  }

  inline absl::optional<int64> GetNebulaFollow() const {
    return nebula_follow_;
  }

  inline absl::optional<int64> GetNebulaComment() const {
    return nebula_comment_;
  }

  inline absl::optional<int64> GetNebulaForward() const {
    return nebula_forward_;
  }

  inline absl::optional<int64> GetThanosRealShow() const {
    return thanos_real_show_;
  }

  inline absl::optional<int64> GetThanosNegative() const {
    return thanos_negative_;
  }

  inline absl::optional<int64> GetThanosLike() const {
    return thanos_like_;
  }

  inline absl::optional<int64> GetThanosFollow() const {
    return thanos_follow_;
  }

  inline absl::optional<int64> GetThanosComment() const {
    return thanos_comment_;
  }

  inline absl::optional<int64> GetThanosForward() const {
    return thanos_forward_;
  }

  inline absl::optional<int64> GetFountainRealShow() const {
    return fountain_real_show_;
  }

  inline absl::optional<int64> GetFountainNegative() const {
    return fountain_negative_;
  }

  inline absl::optional<int64> GetFountainLike() const {
    return fountain_like_;
  }

  inline absl::optional<int64> GetFountainFollow() const {
    return fountain_follow_;
  }

  inline absl::optional<int64> GetFountainComment() const {
    return fountain_comment_;
  }

  inline absl::optional<int64> GetFountainForward() const {
    return fountain_forward_;
  }

  inline absl::optional<int64> GetIsSiriusPunish() const {
    return is_sirius_punish_;
  }

  inline absl::optional<int64> GetEnableDownload() const {
    return enable_download_;
  }

  inline absl::optional<int64> GetAuthorId() const {
    return author_id_;
  }

  inline absl::optional<double> GetColdStartBreakoutScore() const {
    return cold_start_breakout_score_;
  }

  inline absl::optional<int64> GetQuestionaireInfoExposureCount() const {
    return questionaire_info_exposure_count_;
  }

  inline absl::optional<int64> GetQuestionaireInfoNegativeCount() const {
    return questionaire_info_negative_count_;
  }

  inline absl::optional<int64> GetQuestionaireInfoPositiveCount() const {
    return questionaire_info_positive_count_;
  }

  inline absl::optional<int64> GetQuestionaireInfoUnsureCount() const {
    return questionaire_info_unsure_count_;
  }

  inline absl::optional<int64> GetExploreQuestionaireInfoExposureCount() const {
    return explore_questionaire_info_exposure_count_;
  }

  inline absl::optional<int64> GetExploreQuestionaireInfoNegativeCount() const {
    return explore_questionaire_info_negative_count_;
  }

  inline absl::optional<int64> GetExploreQuestionaireInfoPositiveCount() const {
    return explore_questionaire_info_positive_count_;
  }

  inline absl::optional<int64> GetExploreQuestionaireInfoUnsureCount() const {
    return explore_questionaire_info_unsure_count_;
  }

  inline absl::optional<int64> GetUploadTime() const {
    return upload_time_;
  }

  inline absl::optional<int64> GetLongTermPhoto() const {
    return long_term_photo_;
  }

  inline absl::optional<int64> GetExplorePunish() const {
    return explore_punish_;
  }

  inline absl::optional<absl::Span<const int64>> GetExplorePunishCity() const {
    return explore_punish_city_;
  }

  inline absl::optional<int64> GetPhotoStatus() const {
    return photo_status_;
  }

  inline absl::optional<int64> GetTopkAuditTag() const {
    return topk_audit_tag_;
  }

  inline absl::optional<int64> GetTopkAuditLevel() const {
    return topk_audit_level_;
  }

  inline absl::optional<int64> GetIsMidVideoPhoto() const {
    return is_mid_video_photo_;
  }

  inline absl::optional<int64> GetDupClusterId() const {
    return dup_cluster_id_;
  }

  inline absl::optional<int64> GetPicAndSelfdupId() const {
    return pic_and_selfdup_id_;
  }

  inline bool IsHighHotQualityAudit() const {
    return audit_hot_high_tag_level_ && *audit_hot_high_tag_level_ == 4;
  }

  inline bool IsPicture() const {
    return explore::IsPicture(duration_ms_, upload_type_);
  }

  inline bool IsLongPicture() const {
    return explore::IsLongPicture(duration_ms_, upload_type_, picture_type_);
  }

  inline bool IsPictureSet() const {
    return explore::IsPictureSet(duration_ms_, upload_type_, picture_type_);
  }

  inline bool IsUsefulHighValuePicture() const {
    return explore::IsUsefulHighValuePicture(high_value_pic_flag_,
      hetu_v2_level_one_tag_list_, data_set_tags_bit_);
  }

  inline absl::optional<double> GetWidth() const {
    return width_;
  }

  inline absl::optional<double> GetHeight() const {
    return height_;
  }

  inline absl::optional<int64> GetHetuSimClusterId() const {
    return hetu_sim_cluster_id_;
  }

  template <typename T>
  inline T GetValueByPhotoLevel(const T hc_value, const T unimportant_value, const T normal_value) const {
    if (show_level_a_ && !*show_level_a_) {
      return hc_value;
    }
    if (review_pass_level_b_ && *review_pass_level_b_) {
      return unimportant_value;
    }
    return normal_value;
  }

  inline double GetMmuLowQualityModelScore(int mmu_type) const {
    double mmu_value = 0.0;
    switch (mmu_type) {
      case 40: {
        const auto mmu_value_ptr = GetMmuLowQualityModelScore40();
        if (mmu_value_ptr) {
          mmu_value = *mmu_value_ptr;
        }
        break;
      }
      case 42: {
        const auto mmu_value_ptr = GetMmuLowQualityModelScore42();
        if (mmu_value_ptr) {
          mmu_value = *mmu_value_ptr;
        }
        break;
      }
      case 46: {
        const auto mmu_value_ptr = GetMmuLowQualityModelScore46();
        if (mmu_value_ptr) {
          mmu_value = *mmu_value_ptr;
        }
        break;
      }
      case 52: {
        const auto mmu_value_ptr = GetMmuLowQualityModelScore52();
        if (mmu_value_ptr) {
          mmu_value = *mmu_value_ptr;
        }
        break;
      }
      case 63: {
        const auto mmu_value_ptr = GetMmuLowQualityModelScore63();
        if (mmu_value_ptr) {
          mmu_value = *mmu_value_ptr;
        }
        break;
      }
      case 64: {
        const auto mmu_value_ptr = GetMmuLowQualityModelScore64();
        if (mmu_value_ptr) {
          mmu_value = *mmu_value_ptr;
        }
        break;
      }
      case 90: {
        const auto mmu_value_ptr = GetMmuLowQualityModelScore90();
        if (mmu_value_ptr) {
          mmu_value = *mmu_value_ptr;
        }
        break;
      }
      case 104: {
        const auto mmu_value_ptr = GetMmuLowQualityModelScore104();
        if (mmu_value_ptr) {
          mmu_value = *mmu_value_ptr;
        }
        break;
      }
      case 123: {
        const auto mmu_value_ptr = GetMmuLowQualityModelScore123();
        if (mmu_value_ptr) {
          mmu_value = *mmu_value_ptr;
        }
        break;
      }
      case 143: {
        const auto mmu_value_ptr = GetMmuLowQualityModelScore143();
        if (mmu_value_ptr) {
          mmu_value = *mmu_value_ptr;
        }
        break;
      }
      case 145: {
        const auto mmu_value_ptr = GetMmuLowQualityModelScore145();
        if (mmu_value_ptr) {
          mmu_value = *mmu_value_ptr;
        }
        break;
      }
      case 150: {
        const auto mmu_value_ptr = GetMmuLowQualityModelScore150();
        if (mmu_value_ptr) {
          mmu_value = *mmu_value_ptr;
        }
        break;
      }
      case 151: {
        const auto mmu_value_ptr = GetMmuLowQualityModelScore151();
        if (mmu_value_ptr) {
          mmu_value = *mmu_value_ptr;
        }
        break;
      }
      case 163: {
        const auto mmu_value_ptr = GetMmuLowQualityModelScore163();
        if (mmu_value_ptr) {
          mmu_value = *mmu_value_ptr;
        }
        break;
      }
      case 164: {
        const auto mmu_value_ptr = GetMmuLowQualityModelScore164();
        if (mmu_value_ptr) {
          mmu_value = *mmu_value_ptr;
        }
        break;
      }
      default:
        break;
    }
    return mmu_value;
  }

  inline absl::optional<absl::Span<const int64>> GetMmuContentIds(int content_type) const {
    // NOTE(huzengyi) 待实现
    return absl::nullopt;
  }

  inline absl::optional<int64> GetHighHotAuditTagV2() const {
    return high_hot_audit_tag_v2_;
  }

  inline absl::optional<int64> GetAuditUserExperimentLevel() const {
    return audit_user_experiment_level_;
  }

  inline absl::optional<absl::string_view> GetPhotoDynamicXtrsStr() const {
    return photo_dynamic_xtrs_str_;
  }

  inline absl::optional<int64> GetEyeshotSource() const {
    return eyeshot_source_;
  }

  inline absl::optional<absl::string_view> GetYoungIncTags() const {
    return young_inc_tags_;
  }

  inline absl::optional<int64> GetFinalCrossSectionFirstClassId() const {
    return final_cross_section_first_class_id_;
  }

  inline absl::optional<int64> GetLightIncPhotoFlag() const {
    return light_inc_photo_flag_;
  }

  inline absl::optional<int64> GetHighValuePicFlag() const {
    return high_value_pic_flag_;
  }

  inline absl::optional<int64> GetAuditColdReviewLevel() const {
    return audit_cold_review_level_;
  }

  inline absl::optional<absl::Span<const int64>> GetDataSetTags() const {
    return data_set_tags_;
  }

  inline absl::optional<int64> GetAuditRiskImmdTag() const {
    return audit_risk_immd_tag_;
  }

  inline absl::optional<uint32_t> GetEcomIntentScore() const {
    return ecom_intent_score_;
  }

  inline absl::optional<int64> GetDataSetTagsBit() const {
    return data_set_tags_bit_;
  }

  inline absl::optional<int64> GetExploreShortPlay() const {
    return explore_short_play_;
  }

  inline bool IsClimbingPhoto() const {
    return explore::IsClimbingPhoto(explore_click_, explore_real_show_, fountain_real_show_,
                    nebula_real_show_, thanos_real_show_, upload_time_);
  }

  inline absl::optional<int64> GetMagicFaceType() const {
    return magic_face_type_;
  }

  inline absl::optional<int64> GetMagicFaceId() const {
    return magic_face_id_;
  }

  inline absl::optional<int64> GetIsRepostPhoto() const {
    return is_repost_photo_;
  }

  inline absl::optional<absl::Span<const int64>> GetSiriusDistributionInfoMarkCod() const {
    return sirius_distribution_info__mark_cod_;
  }

  inline absl::optional<int64> GetLivePhotoFlag() const {
    return live_photo_flag_;
  }

  inline absl::optional<int64> GetExploreEffectivePlayCount() const {
    return explore_effective_play_count_;
  }

  inline absl::optional<int64> GetAuthorGradeKey() const {
    return author_grade_key_;
  }

  inline absl::optional<int64> GetMerchantHetuTagIdPhoto() const {
    return merchant_hetu_tag_id_photo_;
  }
  inline absl::optional<int64> GetExploreReportCount() const {
    return explore_stats_report_count_;
  }
  inline absl::optional<int64> GetFountainReportCount() const {
    return fountain_stats_report_count_;
  }
  inline absl::optional<int64> GetThanosReportCount() const {
    return thanos_stats_report_count_;
  }
  inline absl::optional<int64> GetNebulaReportCount() const {
    return nebula_stats_report_count_;
  }

  inline absl::optional<int64> GetAuthorAgeSegment() const {
    return author_age_segment_;
  }

  inline absl::optional<double> GetAuthorShopScore() const {
    return author_shop_score_;
  }

  inline absl::optional<double> GetAuthorMaxItemScore() const {
    return author_max_item_score_;
  }

  inline absl::optional<int64> GetSecureGradingActionCode() const {
    return secure_grading_action_code_;
  }

  inline absl::optional<int64> GetTimelinessFlag() const {
    return timeliness_flag_;
  }

  inline absl::optional<int64> GetColdstartsGuaranteeValue() const {
    return coldstart_guarantee_value_;
  }

  inline absl::optional<double> GetFangpinAidFilterRatio() const {
    return fangpin_aid_filter_ratio_;
  }

  inline absl::optional<double> GetPlcBusinessType() const {
    return plc_business_type_;
  }

  inline absl::optional<int64> GetAuthorTailGalaxy() const {
    return author_tail_galaxy_;
  }

  inline absl::optional<int64> GetAuthorTailClimb() const {
    return author_tail_climb_;
  }

  inline absl::optional<int64> GetAuthorTailVcs() const {
    return author_tail_vcs_;
  }

  inline absl::optional<int64> GetAuthorLiezhiPicCount() const {
    return author_liezhi_pic_count_;
  }

  inline absl::optional<absl::Span<const int64>> GetAuthorHashTagIdList() const {
    return author_hash_tag_id_list_;
  }

  inline absl::optional<double> GetLivePhotoDuration() const {
    return live_photo_duration_;
  }

  // 单双列共用，支持影视版权内容屏蔽使用
  inline absl::optional<int64> GetIsTvStationBottomBar() const {
    return is_tv_station_bottom_bar_;
  }

  inline absl::optional<absl::Span<const int64>> GetHotTrendGeneralizedInfoSource() const {
    return hot_trend_generalized_info_source_;
  }
  /*
    公域播放: 发现页外流为 click 其他页面为 real_show
  */
  inline int64 GetPublicDomainPlayCount() const {
    int64 public_domain_count = 0;
    if (explore_click_) {
      public_domain_count += *explore_click_;
    }
    if (fountain_real_show_) {
      public_domain_count += *fountain_real_show_;
    }
    if (thanos_real_show_) {
      public_domain_count += *thanos_real_show_;
    }
    if (nebula_real_show_) {
      public_domain_count += *nebula_real_show_;
    }
    return public_domain_count;
  }
  /*
    公域举报计数
  */
  inline int64 GetPublicDomainReportCount() const {
    int64 public_domain_count = 0;
    if (explore_stats_report_count_) {
      public_domain_count += *explore_stats_report_count_;
    }
    if (fountain_stats_report_count_) {
      public_domain_count += *fountain_stats_report_count_;
    }
    if (thanos_stats_report_count_) {
      public_domain_count += *thanos_stats_report_count_;
    }
    if (nebula_stats_report_count_) {
      public_domain_count += *nebula_stats_report_count_;
    }
    return public_domain_count;
  }
  /*
    公域点赞计数
  */
  inline int64 GetPublicDomainLikeCount() const {
    int64 public_domain_count = 0;
    if (explore_like_) {
      public_domain_count += *explore_like_;
    }
    if (fountain_like_) {
      public_domain_count += *fountain_like_;
    }
    if (thanos_like_) {
      public_domain_count += *thanos_like_;
    }
    if (nebula_like_) {
      public_domain_count += *nebula_like_;
    }
    return public_domain_count;
  }
  /*
    公域收藏计数
  */
  inline int64 GetPublicDomainCollectCount() const {
    int64 public_domain_count = 0;
    if (explore_collect_) {
      public_domain_count += *explore_collect_;
    }
    if (fountain_collect_) {
      public_domain_count += *fountain_collect_;
    }
    if (thanos_collect_) {
      public_domain_count += *thanos_collect_;
    }
    if (nebula_collect_) {
      public_domain_count += *nebula_collect_;
    }
    return public_domain_count;
  }
  /*
    公域评论计数
  */
  inline int64 GetPublicDomainCommentCount() const {
    int64 public_domain_count = 0;
    if (explore_comment_) {
      public_domain_count += *explore_comment_;
    }
    if (fountain_comment_) {
      public_domain_count += *fountain_comment_;
    }
    if (thanos_comment_) {
      public_domain_count += *thanos_comment_;
    }
    if (nebula_comment_) {
      public_domain_count += *nebula_comment_;
    }
    return public_domain_count;
  }
  /*
    公域分享计数
  */
  inline int64 GetPublicDomainForwardCount() const {
    int64 public_domain_count = 0;
    if (explore_forward_) {
      public_domain_count += *explore_forward_;
    }
    if (fountain_forward_) {
      public_domain_count += *fountain_forward_;
    }
    if (thanos_forward_) {
      public_domain_count += *thanos_forward_;
    }
    if (nebula_forward_) {
      public_domain_count += *nebula_forward_;
    }
    return public_domain_count;
  }
  /*
    公域关注计数
  */
  inline int64 GetPublicDomainFollowCount() const {
    int64 public_domain_count = 0;
    if (explore_follow_) {
      public_domain_count += *explore_follow_;
    }
    if (fountain_follow_) {
      public_domain_count += *fountain_follow_;
    }
    if (thanos_follow_) {
      public_domain_count += *thanos_follow_;
    }
    if (nebula_follow_) {
      public_domain_count += *nebula_follow_;
    }
    return public_domain_count;
  }
  inline int64 GetPublicDomainMixInteractCount() const {
    return GetPublicDomainLikeCount()
            + GetPublicDomainCommentCount()
            + GetPublicDomainForwardCount()
            + GetPublicDomainFollowCount()
            + GetPublicDomainCollectCount();
  }
  /*
    公域负反馈计数
  */
  inline int64 GetPublicDomainNegativeCount() const {
    int64 public_domain_count = 0;
    if (explore_negative_) {
      public_domain_count += *explore_negative_;
    }
    if (fountain_negative_) {
      public_domain_count += *fountain_negative_;
    }
    if (thanos_negative_) {
      public_domain_count += *thanos_negative_;
    }
    if (nebula_negative_) {
      public_domain_count += *nebula_negative_;
    }
    return public_domain_count;
  }
  /*
    公域短播计数
  */
  inline int64 GetPublicDomainShorPlayCount() const {
    int64 public_domain_count = 0;
    if (explore_short_play_) {
      public_domain_count += *explore_short_play_;
    }
    if (fountain_stats_short_play_count_) {
      public_domain_count += *fountain_stats_short_play_count_;
    }
    if (thanos_stats_short_play_count_) {
      public_domain_count += *thanos_stats_short_play_count_;
    }
    if (nebula_stats_short_play_count_) {
      public_domain_count += *nebula_stats_short_play_count_;
    }
    return public_domain_count;
  }

  /*
    公域播放累积时长，单位转为秒
    view_length_sum 单位是毫秒，累加可能会溢出，因此先转为秒再累加
  */
  inline int64 GetPublicDomainViewLengthSum() const {
    int64 public_domain_sum = 0;
    if (explore_view_length_sum_) {
      public_domain_sum += std::ceil(*explore_view_length_sum_ / 1000);
    }
    if (fountain_stats_view_length_sum_) {
      public_domain_sum += std::ceil(*fountain_stats_view_length_sum_ / 1000);
    }
    if (thanos_stats_view_length_sum_) {
      public_domain_sum += std::ceil(*thanos_stats_view_length_sum_ / 1000);
    }
    if (nebula_stats_view_length_sum_) {
      public_domain_sum += std::ceil(*nebula_stats_view_length_sum_ / 1000);
    }
    return public_domain_sum;
  }

 private:
  static inline const ItemAttr *GetItemAttrAccessor(
      const folly::F14FastMap<std::string, const ItemAttr *> &item_accessor_map,
      const std::string &attr_name) {
    auto iter = item_accessor_map.find(attr_name);
    return iter == item_accessor_map.end() ? nullptr : iter->second;
  }

  const ItemAttr *photo_id_accessor_ = nullptr;
  const ItemAttr *audit_hot_high_tag_level_accessor_ = nullptr;
  const ItemAttr *duration_ms_accessor_ = nullptr;
  const ItemAttr *upload_type_accessor_ = nullptr;
  const ItemAttr *show_level_a_accessor_ = nullptr;
  const ItemAttr *review_pass_level_b_accessor_ = nullptr;
  const ItemAttr *photo_total_report_count_accessor_ = nullptr;
  const ItemAttr *author_total_report_count_accessor_ = nullptr;
  const ItemAttr *title_evil_level_accessor_ = nullptr;
  const ItemAttr *ocr_cover_text_evil_level_accessor_ = nullptr;
  const ItemAttr *manjiao_markcode_accessor_ = nullptr;
  const ItemAttr *hetu_level_one_tag_list_accessor_ = nullptr;
  const ItemAttr *hetu_level_two_tag_list_accessor_ = nullptr;
  const ItemAttr *hetu_level_three_tag_list_accessor_ = nullptr;
  const ItemAttr *hetu_level_four_tag_list_accessor_ = nullptr;
  const ItemAttr *hetu_level_five_tag_list_accessor_ = nullptr;
  const ItemAttr *hetu_face_id_tag_list_accessor_ = nullptr;
  const ItemAttr *hetu_tag_list_accessor_ = nullptr;
  const ItemAttr *hetu_v2_level_one_tag_list_accessor_ = nullptr;
  const ItemAttr *hetu_v2_level_two_tag_list_accessor_ = nullptr;
  const ItemAttr *hetu_v2_level_three_tag_list_accessor_ = nullptr;
  const ItemAttr *hetu_v2_tag_list_accessor_ = nullptr;
  const ItemAttr *hetu_v3_level_one_tag_list_accessor_ = nullptr;
  const ItemAttr *audit_hot_cover_level_accessor_ = nullptr;
  const ItemAttr *caption_length_accessor_ = nullptr;
  const ItemAttr *picture_type_accessor_ = nullptr;
  const ItemAttr *impression_audit_accessor_ = nullptr;
  const ItemAttr *audit_b_second_tag_accessor_ = nullptr;
  const ItemAttr *video_quality_assessment_flag_accessor_ = nullptr;
  const ItemAttr *mmu_photo_low_quality_model_accessor_ = nullptr;
  const ItemAttr *mmu_low_quality_model_score_40_accessor_ = nullptr;
  const ItemAttr *mmu_low_quality_model_score_42_accessor_ = nullptr;
  const ItemAttr *mmu_low_quality_model_score_46_accessor_ = nullptr;
  const ItemAttr *mmu_low_quality_model_score_52_accessor_ = nullptr;
  const ItemAttr *mmu_low_quality_model_score_63_accessor_ = nullptr;
  const ItemAttr *mmu_low_quality_model_score_64_accessor_ = nullptr;
  const ItemAttr *mmu_low_quality_model_score_90_accessor_ = nullptr;
  const ItemAttr *mmu_low_quality_model_score_104_accessor_ = nullptr;
  const ItemAttr *mmu_low_quality_model_score_123_accessor_ = nullptr;
  const ItemAttr *mmu_low_quality_model_score_143_accessor_ = nullptr;
  const ItemAttr *mmu_low_quality_model_score_145_accessor_ = nullptr;
  const ItemAttr *mmu_low_quality_model_score_150_accessor_ = nullptr;
  const ItemAttr *mmu_low_quality_model_score_151_accessor_ = nullptr;
  const ItemAttr *mmu_low_quality_model_score_163_accessor_ = nullptr;
  const ItemAttr *mmu_low_quality_model_score_164_accessor_ = nullptr;
  const ItemAttr *merchant_item_id_list_accessor_ = nullptr;
  const ItemAttr *merchant_photo_cart_relation_accessor_ = nullptr;
  const ItemAttr *risk_man_risk_photo_accessor_ = nullptr;
  const ItemAttr *need_shuffle_photo_accessor_ = nullptr;
  const ItemAttr *author_fans_count_accessor_ = nullptr;
  const ItemAttr *explore_server_show_accessor_ = nullptr;
  const ItemAttr *explore_click_accessor_ = nullptr;
  const ItemAttr *explore_real_show_accessor_ = nullptr;
  const ItemAttr *explore_view_length_sum_accessor_ = nullptr;
  const ItemAttr *explore_long_play_count_accessor_ = nullptr;
  const ItemAttr *explore_negative_accessor_ = nullptr;
  const ItemAttr *explore_like_accessor_ = nullptr;
  const ItemAttr *explore_forward_accessor_ = nullptr;
  const ItemAttr *explore_follow_accessor_ = nullptr;
  const ItemAttr *explore_comment_accessor_ = nullptr;
  const ItemAttr *explore_collect_accessor_ = nullptr;
  const ItemAttr *upload_time_accessor_ = nullptr;
  const ItemAttr *long_term_photo_accessor_ = nullptr;
  const ItemAttr *nebula_real_show_accessor_ = nullptr;
  const ItemAttr *nebula_negative_accessor_ = nullptr;
  const ItemAttr *nebula_like_accessor_ = nullptr;
  const ItemAttr *nebula_follow_accessor_ = nullptr;
  const ItemAttr *nebula_comment_accessor_ = nullptr;
  const ItemAttr *nebula_collect_accessor_ = nullptr;
  const ItemAttr *nebula_forward_accessor_ = nullptr;
  const ItemAttr *thanos_real_show_accessor_ = nullptr;
  const ItemAttr *thanos_negative_accessor_ = nullptr;
  const ItemAttr *thanos_like_accessor_ = nullptr;
  const ItemAttr *thanos_follow_accessor_ = nullptr;
  const ItemAttr *thanos_comment_accessor_ = nullptr;
  const ItemAttr *thanos_collect_accessor_ = nullptr;
  const ItemAttr *thanos_forward_accessor_ = nullptr;
  const ItemAttr *fountain_real_show_accessor_ = nullptr;
  const ItemAttr *fountain_negative_accessor_ = nullptr;
  const ItemAttr *fountain_like_accessor_ = nullptr;
  const ItemAttr *fountain_follow_accessor_ = nullptr;
  const ItemAttr *fountain_comment_accessor_ = nullptr;
  const ItemAttr *fountain_collect_accessor_ = nullptr;
  const ItemAttr *fountain_forward_accessor_ = nullptr;
  const ItemAttr *is_sirius_punish_accessor_ = nullptr;
  const ItemAttr *enable_download_accessor_ = nullptr;
  const ItemAttr *author_id_accessor_ = nullptr;
  const ItemAttr *cold_start_breakout_score_accessor_ = nullptr;
  const ItemAttr *questionaire_info_exposure_count_accessor_ = nullptr;
  const ItemAttr *questionaire_info_negative_count_accessor_ = nullptr;
  const ItemAttr *questionaire_info_positive_count_accessor_ = nullptr;
  const ItemAttr *questionaire_info_unsure_count_accessor_ = nullptr;
  const ItemAttr *explore_questionaire_info_exposure_count_accessor_ = nullptr;
  const ItemAttr *explore_questionaire_info_negative_count_accessor_ = nullptr;
  const ItemAttr *explore_questionaire_info_positive_count_accessor_ = nullptr;
  const ItemAttr *explore_questionaire_info_unsure_count_accessor_ = nullptr;
  const ItemAttr *explore_punish_accessor_ = nullptr;
  const ItemAttr *explore_punish_city_accessor_ = nullptr;
  const ItemAttr *photo_status_accessor_ = nullptr;
  const ItemAttr *topk_audit_tag_accessor_ = nullptr;
  const ItemAttr *topk_audit_level_accessor_ = nullptr;
  const ItemAttr *is_mid_video_photo_accessor_ = nullptr;
  const ItemAttr *dup_cluster_id_accessor_ = nullptr;
  const ItemAttr *pic_and_selfdup_id_accessor_ = nullptr;
  const ItemAttr *width_accessor_ = nullptr;
  const ItemAttr *height_accessor_ = nullptr;
  const ItemAttr *high_hot_audit_tag_v2_accessor_ = nullptr;
  const ItemAttr *audit_user_experiment_level_accessor_ = nullptr;
  const ItemAttr *eyeshot_source_accessor_ = nullptr;
  const ItemAttr *young_inc_tags_accessor_ = nullptr;
  const ItemAttr *final_cross_section_first_class_id_accessor_ = nullptr;
  const ItemAttr *light_inc_photo_flag_accessor_ = nullptr;
  const ItemAttr *high_value_pic_flag_accessor_ = nullptr;
  const ItemAttr *audit_cold_review_level_accessor_ = nullptr;
  const ItemAttr *data_set_tags_accessor_ = nullptr;
  const ItemAttr *audit_risk_immd_tag_accessor_ = nullptr;
  const ItemAttr *photo_dynamic_xtrs_str_accessor_ = nullptr;
  const ItemAttr *ecom_intent_score_accessor_ = nullptr;
  const ItemAttr *data_set_tags_bit_accessor_ = nullptr;
  const ItemAttr *explore_short_play_accessor_ = nullptr;
  const ItemAttr *magic_face_type_accessor_ = nullptr;
  const ItemAttr *magic_face_id_accessor_ = nullptr;
  const ItemAttr *is_repost_photo_accessor_ = nullptr;
  const ItemAttr *sirius_distribution_info__mark_cod_accessor_ = nullptr;
  const ItemAttr *live_photo_flag_accessor_ = nullptr;
  const ItemAttr *explore_effective_play_count_accessor_ = nullptr;
  const ItemAttr *author_grade_key_accessor_ = nullptr;
  const ItemAttr *merchant_hetu_tag_id_photo_accessor_ = nullptr;
  const ItemAttr *explore_stats_report_count_accessor_ = nullptr;
  const ItemAttr *fountain_stats_report_count_accessor_ = nullptr;
  const ItemAttr *thanos_stats_report_count_accessor_ = nullptr;
  const ItemAttr *nebula_stats_report_count_accessor_ = nullptr;
  const ItemAttr *fountain_stats_short_play_count_accessor_ = nullptr;
  const ItemAttr *thanos_stats_short_play_count_accessor_ = nullptr;
  const ItemAttr *nebula_stats_short_play_count_accessor_ = nullptr;
  const ItemAttr *hetu_sim_cluster_id_accessor_ = nullptr;
  const ItemAttr *author_age_segment_accessor_ = nullptr;
  const ItemAttr *nebula_stats_view_length_sum_accessor_ = nullptr;
  const ItemAttr *thanos_stats_view_length_sum_accessor_ = nullptr;
  const ItemAttr *fountain_stats_view_length_sum_accessor_ = nullptr;
  const ItemAttr *author_shop_score_accessor_ = nullptr;
  const ItemAttr *author_max_item_score_accessor_ = nullptr;
  const ItemAttr *secure_grading_action_code_accessor_ = nullptr;
  const ItemAttr *timeliness_flag_accessor_ = nullptr;
  const ItemAttr *coldstart_guarantee_value_accessor_ = nullptr;
  const ItemAttr *fangpin_aid_filter_ratio_accessor_ = nullptr;
  const ItemAttr *plc_business_type_accessor_ = nullptr;
  const ItemAttr *author_tail_galaxy_accessor_ = nullptr;
  const ItemAttr *author_tail_climb_accessor_ = nullptr;
  const ItemAttr *author_tail_vcs_accessor_ = nullptr;
  const ItemAttr *author_liezhi_pic_count_accessor_ = nullptr;
  const ItemAttr *author_hash_tag_id_list_accessor_ = nullptr;
  const ItemAttr *live_photo_duration_accessor_ = nullptr;
  const ItemAttr *is_tv_station_bottom_bar_accessor_ = nullptr;
  const ItemAttr *hot_trend_generalized_info_source_accessor_ = nullptr;

  absl::optional<int64> photo_id_;
  absl::optional<int64> audit_hot_high_tag_level_;
  absl::optional<int64> duration_ms_;
  absl::optional<int64> upload_type_;
  absl::optional<int64> show_level_a_;
  absl::optional<int64> review_pass_level_b_;
  absl::optional<int64> photo_total_report_count_;
  absl::optional<int64> author_total_report_count_;
  absl::optional<double> title_evil_level_;
  absl::optional<double> ocr_cover_text_evil_level_;
  absl::optional<absl::Span<const int64>> hetu_level_one_tag_list_;
  absl::optional<absl::Span<const int64>> hetu_level_two_tag_list_;
  absl::optional<absl::Span<const int64>> hetu_level_three_tag_list_;
  absl::optional<absl::Span<const int64>> hetu_level_four_tag_list_;
  absl::optional<absl::Span<const int64>> hetu_level_five_tag_list_;
  absl::optional<absl::Span<const int64>> hetu_face_id_tag_list_;
  absl::optional<absl::Span<const int64>> hetu_tag_list_;
  absl::optional<absl::Span<const int64>> hetu_v2_level_one_tag_list_;
  absl::optional<absl::Span<const int64>> hetu_v2_level_two_tag_list_;
  absl::optional<absl::Span<const int64>> hetu_v2_level_three_tag_list_;
  absl::optional<absl::Span<const int64>> hetu_v2_tag_list_;
  absl::optional<absl::Span<const int64>> hetu_v3_level_one_tag_list_;
  absl::optional<int64> audit_hot_cover_level_;
  absl::optional<int64> caption_length_;
  absl::optional<int64> picture_type_;
  absl::optional<int64> impression_audit_;
  absl::optional<int64> audit_b_second_tag_;
  absl::optional<int64> video_quality_assessment_flag_;
  absl::optional<absl::string_view> mmu_photo_low_quality_model_;
  absl::optional<double> mmu_low_quality_model_score_40_;
  absl::optional<double> mmu_low_quality_model_score_42_;
  absl::optional<double> mmu_low_quality_model_score_46_;
  absl::optional<double> mmu_low_quality_model_score_52_;
  absl::optional<double> mmu_low_quality_model_score_63_;
  absl::optional<double> mmu_low_quality_model_score_64_;
  absl::optional<double> mmu_low_quality_model_score_90_;
  absl::optional<double> mmu_low_quality_model_score_104_;
  absl::optional<double> mmu_low_quality_model_score_123_;
  absl::optional<double> mmu_low_quality_model_score_143_;
  absl::optional<double> mmu_low_quality_model_score_145_;
  absl::optional<double> mmu_low_quality_model_score_150_;
  absl::optional<double> mmu_low_quality_model_score_151_;
  absl::optional<double> mmu_low_quality_model_score_163_;
  absl::optional<double> mmu_low_quality_model_score_164_;
  absl::optional<absl::Span<const int64>> merchant_item_id_list_;
  absl::optional<double> merchant_photo_cart_relation_;
  absl::optional<absl::string_view> photo_dynamic_xtrs_str_;
  absl::optional<int64> risk_man_risk_photo_;
  absl::optional<int64> need_shuffle_photo_;
  absl::optional<int64> author_fans_count_;
  absl::optional<int64> explore_server_show_;
  absl::optional<int64> explore_click_;
  absl::optional<int64> explore_real_show_;
  absl::optional<int64> explore_view_length_sum_;
  absl::optional<int64> explore_long_play_count_;
  absl::optional<int64> explore_negative_;
  absl::optional<int64> explore_like_;
  absl::optional<int64> explore_follow_;
  absl::optional<int64> explore_forward_;
  absl::optional<int64> explore_comment_;
  absl::optional<int64> explore_collect_;
  absl::optional<int64> upload_time_;
  absl::optional<int64> long_term_photo_;
  absl::optional<int64> nebula_real_show_;
  absl::optional<int64> nebula_negative_;
  absl::optional<int64> nebula_like_;
  absl::optional<int64> nebula_follow_;
  absl::optional<int64> nebula_comment_;
  absl::optional<int64> nebula_collect_;
  absl::optional<int64> nebula_forward_;
  absl::optional<int64> thanos_real_show_;
  absl::optional<int64> thanos_negative_;
  absl::optional<int64> thanos_like_;
  absl::optional<int64> thanos_follow_;
  absl::optional<int64> thanos_comment_;
  absl::optional<int64> thanos_collect_;
  absl::optional<int64> thanos_forward_;
  absl::optional<int64> fountain_real_show_;
  absl::optional<int64> fountain_negative_;
  absl::optional<int64> fountain_like_;
  absl::optional<int64> fountain_follow_;
  absl::optional<int64> fountain_comment_;
  absl::optional<int64> fountain_collect_;
  absl::optional<int64> fountain_forward_;
  absl::optional<int64> is_sirius_punish_;
  absl::optional<int64> enable_download_;
  absl::optional<int64> author_id_;
  absl::optional<double> cold_start_breakout_score_;
  absl::optional<int64> questionaire_info_exposure_count_;
  absl::optional<int64> questionaire_info_negative_count_;
  absl::optional<int64> questionaire_info_positive_count_;
  absl::optional<int64> questionaire_info_unsure_count_;
  absl::optional<int64> explore_questionaire_info_exposure_count_;
  absl::optional<int64> explore_questionaire_info_negative_count_;
  absl::optional<int64> explore_questionaire_info_positive_count_;
  absl::optional<int64> explore_questionaire_info_unsure_count_;
  absl::optional<int64> explore_punish_;
  absl::optional<absl::Span<const int64>> explore_punish_city_;
  absl::optional<int64> photo_status_;
  absl::optional<int64> topk_audit_tag_;
  absl::optional<int64> topk_audit_level_;
  absl::optional<int64> is_mid_video_photo_;
  absl::optional<int64> dup_cluster_id_;
  absl::optional<int64> pic_and_selfdup_id_;
  absl::optional<double> width_;
  absl::optional<double> height_;
  absl::optional<int64> high_hot_audit_tag_v2_;
  absl::optional<int64> audit_user_experiment_level_;
  absl::optional<int64> eyeshot_source_;
  absl::optional<absl::string_view> young_inc_tags_;
  absl::optional<int64> final_cross_section_first_class_id_;
  absl::optional<int64> light_inc_photo_flag_;
  absl::optional<int64> high_value_pic_flag_;
  absl::optional<int64> audit_cold_review_level_;
  absl::optional<absl::Span<const int64>> data_set_tags_;
  absl::optional<int64> audit_risk_immd_tag_;
  absl::optional<int64> ecom_intent_score_;
  absl::optional<int64> data_set_tags_bit_;
  absl::optional<int64> explore_short_play_;
  absl::optional<int64> magic_face_type_;
  absl::optional<int64> magic_face_id_;
  absl::optional<int64> is_repost_photo_;
  absl::optional<absl::Span<const int64>> sirius_distribution_info__mark_cod_;
  absl::optional<int64> live_photo_flag_;
  absl::optional<int64> explore_effective_play_count_;
  absl::optional<int64> author_grade_key_;
  absl::optional<int64> merchant_hetu_tag_id_photo_;
  absl::optional<int64> explore_stats_report_count_;
  absl::optional<int64> fountain_stats_report_count_;
  absl::optional<int64> thanos_stats_report_count_;
  absl::optional<int64> nebula_stats_report_count_;
  absl::optional<int64> fountain_stats_short_play_count_;
  absl::optional<int64> thanos_stats_short_play_count_;
  absl::optional<int64> nebula_stats_short_play_count_;
  absl::optional<int64> hetu_sim_cluster_id_;
  absl::optional<int64> author_age_segment_;
  absl::optional<int64> nebula_stats_view_length_sum_;
  absl::optional<int64> thanos_stats_view_length_sum_;
  absl::optional<int64> fountain_stats_view_length_sum_;
  absl::optional<double> author_shop_score_;
  absl::optional<double> author_max_item_score_;
  absl::optional<int64> secure_grading_action_code_;
  absl::optional<int64> timeliness_flag_;
  absl::optional<int64> coldstart_guarantee_value_;
  absl::optional<absl::Span<const int64>> manjiao_markcode_;
  absl::optional<double> fangpin_aid_filter_ratio_;
  absl::optional<int64> plc_business_type_;
  absl::optional<int64> author_tail_galaxy_;
  absl::optional<int64> author_tail_climb_;
  absl::optional<int64> author_tail_vcs_;
  absl::optional<int64> author_liezhi_pic_count_;
  absl::optional<absl::Span<const int64>> author_hash_tag_id_list_;
  absl::optional<double> live_photo_duration_;
  absl::optional<int64> is_tv_station_bottom_bar_;
  absl::optional<absl::Span<const int64>> hot_trend_generalized_info_source_;
};

}  // namespace explore
}  // namespace platform
}  // namespace ks
