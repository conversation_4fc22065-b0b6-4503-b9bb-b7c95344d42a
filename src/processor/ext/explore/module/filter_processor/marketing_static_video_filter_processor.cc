#include "dragon/src/processor/ext/explore/module/filter_processor/marketing_static_video_filter_processor.h"

namespace ks {
namespace platform {
namespace explore {

void MarketingStaticVideoFilterProcessor::Init(
    ReadableRecoContextInterface *context, const ks::reco::UserInfo *user_info, const base::Json *config) {
  dis_ = std::uniform_real_distribution<double>(0.0, 1.0);
  static_video_tag_id_ = explore::GetIntCommonAttr(context, config,
    "static_video_tag_id_attr").value_or(4009921);
  static_video_tag_prob_thd_ = explore::GetDoubleCommonAttr(context, config,
    "static_video_tag_prob_thd_attr").value_or(0.99);
  base_vv_threshold_ = explore::GetIntCommonAttr(context, config,
    "base_vv_threshold_attr").value_or(1000);
  skip_market_mark_ = explore::GetIntCommonAttr(context, config,
    "skip_market_mark_attr").value_or(0);
  marketing_mark_cod_set_.clear();
  auto author_filter_mark_cod_str = explore::GetStringCommonAttr(
    context, config, "marketing_mark_cod_str_attr");
  if (author_filter_mark_cod_str) {
    StringToIntSet(std::string(*author_filter_mark_cod_str), ",",
      &marketing_mark_cod_set_);
  }
  interact_rate_thresholds_vec_.clear();
  auto interact_rate_thresholds_str = explore::GetStringCommonAttr(
    context, config, "interact_rate_thresholds_str_attr");
  if (interact_rate_thresholds_str) {
    explore::StringToDoubleVector(std::string(*interact_rate_thresholds_str), ",",
      &interact_rate_thresholds_vec_);
  }
  vv_thresholds_vec_.clear();
  auto vv_thresholds_str = explore::GetStringCommonAttr(
    context, config, "vv_thresholds_str_attr");
  if (vv_thresholds_str) {
    explore::StringToIntVector(std::string(*vv_thresholds_str), ",",
      &vv_thresholds_vec_);
  }
  filter_probs_vec_.clear();
  auto filter_probs_str = explore::GetStringCommonAttr(
    context, config, "filter_probs_str_attr");
  if (filter_probs_str) {
    explore::StringToDoubleVector(std::string(*filter_probs_str), ",",
      &filter_probs_vec_);
  }
}

bool MarketingStaticVideoFilterProcessor::NeedFilter(const CommonRecoResult &result) {
  if (interact_rate_thresholds_vec_.size() == 0 ||
      interact_rate_thresholds_vec_.size() != vv_thresholds_vec_.size() ||
      interact_rate_thresholds_vec_.size() != filter_probs_vec_.size() ||
      interact_rate_thresholds_vec_.size() >= 5) {  // 限制互动率分组数量
    return false;
  }
  const auto duration_ms = common_photo_info_->GetDurationMs();
  const auto upload_type = common_photo_info_->GetUploadType();
  const auto pic_type = common_photo_info_->GetPictureType();
  if (explore::IsPictureV2(duration_ms, upload_type, pic_type)) {  // 图文不过滤
    return false;
  }
  auto photo_hetu_tag_list = common_photo_info_->GetHetuV2TagList();
  if (photo_hetu_tag_list) {
    for (auto hetu_tag : *photo_hetu_tag_list) {
      int64 tag_id = GetTagIdFromHetuV2Tag(hetu_tag);
      float prob = GetProbFromHetuTag(hetu_tag);
      if (tag_id == static_video_tag_id_ && prob >= static_video_tag_prob_thd_) {  // 命中泛单图视频
        auto mark_cod_list = common_photo_info_->GetSiriusDistributionInfoMarkCod();
        if (mark_cod_list) {
          for (const auto& cod : *mark_cod_list) {
            if (skip_market_mark_ || marketing_mark_cod_set_.count(cod)) {  // 命中营销号
              if (base_vv_threshold_ == 0) {  // 直接过滤
                return true;
              }
              int64 public_domain_play_count = common_photo_info_->GetPublicDomainPlayCount();
              if (public_domain_play_count >= base_vv_threshold_) {
                double interact_rate = common_photo_info_->GetPublicDomainMixInteractCount() / (
                                      public_domain_play_count + 1.0);
                for (int i = 0; i < interact_rate_thresholds_vec_.size(); i++) {  // 3-4 个分位点
                  if (interact_rate < interact_rate_thresholds_vec_[i]) {
                    if (public_domain_play_count >= vv_thresholds_vec_[i] ||
                        dis_(engine_) < filter_probs_vec_[i]) {
                      return true;
                    }
                    break;
                  }
                }
              }
              break;
            }
          }
        }
        break;
      }
    }
  }
  return false;
}
}  // namespace explore
}  // namespace platform
}  // namespace ks
