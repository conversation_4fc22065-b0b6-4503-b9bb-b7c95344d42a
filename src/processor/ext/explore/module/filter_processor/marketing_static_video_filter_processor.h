#pragma once

#include <string>
#include <vector>
#include "dragon/src/processor/ext/explore/module/filter_processor/base_filter_processor.h"

namespace ks {
namespace platform {
namespace explore {

class MarketingStaticVideoFilterProcessor : public BaseFilterProcessor {
 public:
  MarketingStaticVideoFilterProcessor() {}
  void Init(
    ReadableRecoContextInterface *context,
    const ks::reco::UserInfo *user_info,
    const base::Json *config) override;
  bool NeedFilter(const CommonRecoResult &result) override;

 private:
  int64 base_vv_threshold_ = 1000;
  int64 static_video_tag_id_ = 4009921;
  int64 skip_market_mark_ = 0;
  double static_video_tag_prob_thd_ = 0.99;
  folly::F14FastSet<int> marketing_mark_cod_set_;
  std::vector<double> interact_rate_thresholds_vec_;
  std::vector<int> vv_thresholds_vec_;
  std::vector<double> filter_probs_vec_;
  std::default_random_engine engine_;
  std::uniform_real_distribution<double> dis_;
  DISALLOW_COPY_AND_ASSIGN(MarketingStaticVideoFilterProcessor);
};

}  // namespace explore
}  // namespace platform
}  // namespace ks
