#include "dragon/src/processor/ext/explore/module/common_photo_info.h"

namespace ks {
namespace platform {
namespace explore {

CommonPhotoInfo::CommonPhotoInfo(const folly::F14FastMap<std::string, const ItemAttr *> &item_accessor_map) {
  photo_id_accessor_ = GetItemAttrAccessor(item_accessor_map, "photo_id_attr");
  audit_hot_high_tag_level_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "audit_hot_high_tag_level_attr");
  duration_ms_accessor_ = GetItemAttrAccessor(item_accessor_map, "duration_ms_attr");
  upload_type_accessor_ = GetItemAttrAccessor(item_accessor_map, "upload_type_attr");
  show_level_a_accessor_ = GetItemAttrAccessor(item_accessor_map, "show_level_a_attr");
  review_pass_level_b_accessor_ = GetItemAttrAccessor(item_accessor_map, "review_pass_level_b_attr");
  photo_total_report_count_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "photo_total_report_count_attr");
  author_total_report_count_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "author_total_report_count_attr");
  title_evil_level_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "title_evil_level_attr");
  ocr_cover_text_evil_level_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "ocr_cover_text_evil_level_attr");
  manjiao_markcode_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "manjiao_markcode_attr");
  hetu_level_one_tag_list_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "hetu_level_one_tag_list_attr");
  hetu_level_two_tag_list_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "hetu_level_two_tag_list_attr");
  hetu_level_three_tag_list_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "hetu_level_three_tag_list_attr");
  hetu_level_four_tag_list_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "hetu_level_four_tag_list_attr");
  hetu_level_five_tag_list_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "hetu_level_five_tag_list_attr");
  hetu_face_id_tag_list_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "hetu_face_id_tag_list_attr");
  hetu_tag_list_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "hetu_tag_list_attr");
  hetu_v2_level_one_tag_list_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "hetu_v2_level_one_tag_list_attr");
  hetu_v2_level_two_tag_list_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "hetu_v2_level_two_tag_list_attr");
  hetu_v2_level_three_tag_list_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "hetu_v2_level_three_tag_list_attr");
  hetu_v2_tag_list_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "hetu_tag_level_info_v2__hetu_tag_attr");
  hetu_v3_level_one_tag_list_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "hetu_v3_level_one_tag_list_attr");
  audit_hot_cover_level_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "audit_hot_cover_level_attr");
  caption_length_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "caption_length_attr");
  picture_type_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "picture_type_attr");
  impression_audit_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "level_hot_online_attr");
  audit_b_second_tag_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "audit_b_second_tag_attr");
  video_quality_assessment_flag_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "video_quality_assessment_flag_attr");
  mmu_photo_low_quality_model_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "mmu_photo_low_quality_model_attr");
  mmu_low_quality_model_score_40_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "mmu_low_quality_model_score_40_attr");
  mmu_low_quality_model_score_42_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "mmu_low_quality_model_score_42_attr");
  mmu_low_quality_model_score_46_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "mmu_low_quality_model_score_46_attr");
  mmu_low_quality_model_score_52_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "mmu_low_quality_model_score_52_attr");
  mmu_low_quality_model_score_63_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "mmu_low_quality_model_score_63_attr");
  mmu_low_quality_model_score_64_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "mmu_low_quality_model_score_64_attr");
  mmu_low_quality_model_score_90_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "mmu_low_quality_model_score_90_attr");
  mmu_low_quality_model_score_104_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "mmu_low_quality_model_score_104_attr");
  mmu_low_quality_model_score_123_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "mmu_low_quality_model_score_123_attr");
  mmu_low_quality_model_score_143_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "mmu_low_quality_model_score_143_attr");
  mmu_low_quality_model_score_145_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "mmu_low_quality_model_score_145_attr");
  mmu_low_quality_model_score_150_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "mmu_low_quality_model_score_150_attr");
  mmu_low_quality_model_score_163_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "mmu_low_quality_model_score_163_attr");
  mmu_low_quality_model_score_164_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "mmu_low_quality_model_score_164_attr");
  merchant_item_id_list_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "merchant_item_id_list_attr");
  merchant_photo_cart_relation_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "merchant_photo_cart_relation_attr");
  risk_man_risk_photo_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "risk_man_risk_photo_attr");
  need_shuffle_photo_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "need_shuffle_photo_attr");
  author_fans_count_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "author_fans_count_attr");
  explore_server_show_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "explore_server_show_attr");
  explore_click_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "explore_click_attr");
  explore_real_show_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "explore_real_show_attr");
  explore_view_length_sum_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "explore_view_length_sum_attr");
  explore_long_play_count_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "explore_long_play_count_attr");
  explore_negative_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "explore_negative_attr");
  explore_like_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "explore_like_attr");
  explore_follow_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "explore_follow_attr");
  explore_forward_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "explore_forward_attr");
  explore_comment_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "explore_comment_attr");
  explore_collect_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "explore_collect_attr");
  upload_time_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "upload_time_attr");
  long_term_photo_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "long_term_photo_attr");
  nebula_real_show_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "nebula_real_show_attr");
  nebula_negative_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "nebula_negative_attr");
  nebula_like_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "nebula_like_attr");
  nebula_follow_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "nebula_follow_attr");
  nebula_comment_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "nebula_comment_attr");
  nebula_collect_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "nebula_collect_attr");
  nebula_forward_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "nebula_forward_attr");
  thanos_real_show_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "thanos_real_show_attr");
  thanos_negative_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "thanos_negative_attr");
  thanos_like_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "thanos_like_attr");
  thanos_follow_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "thanos_follow_attr");
  thanos_comment_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "thanos_comment_attr");
  thanos_collect_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "thanos_collect_attr");
  thanos_forward_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "thanos_forward_attr");
  fountain_real_show_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "fountain_real_show_attr");
  fountain_negative_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "fountain_negative_attr");
  fountain_like_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "fountain_like_attr");
  fountain_follow_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "fountain_follow_attr");
  fountain_comment_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "fountain_comment_attr");
  fountain_collect_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "fountain_collect_attr");
  fountain_forward_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "fountain_forward_attr");
  is_sirius_punish_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "is_sirius_punish_attr");
  enable_download_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "enable_download_attr");
  photo_dynamic_xtrs_str_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "photo_dynamic_xtrs_str_attr");
  author_id_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "author_id_attr");
  cold_start_breakout_score_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "cold_start_breakout_score_attr");
  questionaire_info_exposure_count_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "questionaire_info_exposure_count_attr");
  questionaire_info_negative_count_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "questionaire_info_negative_count_attr");
  questionaire_info_positive_count_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "questionaire_info_positive_count_attr");
  questionaire_info_unsure_count_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "questionaire_info_unsure_count_attr");
  explore_questionaire_info_exposure_count_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "explore_questionaire_info_exposure_count_attr");
  explore_questionaire_info_negative_count_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "explore_questionaire_info_negative_count_attr");
  explore_questionaire_info_positive_count_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "explore_questionaire_info_positive_count_attr");
  explore_questionaire_info_unsure_count_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "explore_questionaire_info_unsure_count_attr");
  explore_punish_accessor_ = GetItemAttrAccessor(item_accessor_map, "explore_punish_attr");
  explore_punish_city_accessor_ = GetItemAttrAccessor(item_accessor_map, "explore_punish_city_attr");
  photo_status_accessor_ = GetItemAttrAccessor(item_accessor_map, "photo_status_attr");
  topk_audit_tag_accessor_ = GetItemAttrAccessor(item_accessor_map, "topk_audit_tag_attr");
  topk_audit_level_accessor_ = GetItemAttrAccessor(item_accessor_map, "topk_audit_level_attr");
  is_mid_video_photo_accessor_ = GetItemAttrAccessor(item_accessor_map, "is_mid_video_photo_attr");
  dup_cluster_id_accessor_ = GetItemAttrAccessor(item_accessor_map, "dup_cluster_id_attr");
  pic_and_selfdup_id_accessor_ = GetItemAttrAccessor(item_accessor_map, "pic_and_selfdup_id_attr");
  width_accessor_ = GetItemAttrAccessor(item_accessor_map, "width_attr");
  height_accessor_ = GetItemAttrAccessor(item_accessor_map, "height_attr");
  high_hot_audit_tag_v2_accessor_ = GetItemAttrAccessor(item_accessor_map, "high_hot_audit_tag_v2_attr");
  audit_user_experiment_level_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "audit_user_experiment_level_attr");
  eyeshot_source_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "eyeshot_source_attr");
  young_inc_tags_accessor_ = GetItemAttrAccessor(item_accessor_map, "young_inc_tags_attr");
  final_cross_section_first_class_id_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "final_cross_section_first_class_id_attr");
  light_inc_photo_flag_accessor_ = GetItemAttrAccessor(item_accessor_map, "light_inc_photo_flag_attr");
  high_value_pic_flag_accessor_ = GetItemAttrAccessor(item_accessor_map, "high_value_pic_flag_attr");
  audit_cold_review_level_accessor_ = GetItemAttrAccessor(item_accessor_map, "audit_cold_review_level_attr");
  data_set_tags_accessor_ = GetItemAttrAccessor(item_accessor_map, "data_set_tags_attr");
  audit_risk_immd_tag_accessor_ = GetItemAttrAccessor(item_accessor_map, "audit_risk_immd_tag_attr");
  ecom_intent_score_accessor_ = GetItemAttrAccessor(item_accessor_map, "ecom_intent_score_attr");
  data_set_tags_bit_accessor_ = GetItemAttrAccessor(item_accessor_map, "data_set_tags_bit_attr");
  explore_short_play_accessor_ = GetItemAttrAccessor(item_accessor_map, "explore_short_play_attr");
  magic_face_type_accessor_ = GetItemAttrAccessor(item_accessor_map, "magic_face_type_attr");
  magic_face_id_accessor_ = GetItemAttrAccessor(item_accessor_map, "magic_face_id_attr");
  is_repost_photo_accessor_ = GetItemAttrAccessor(item_accessor_map, "is_repost_photo_attr");
  sirius_distribution_info__mark_cod_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "sirius_distribution_info__mark_cod_attr");
  live_photo_flag_accessor_ = GetItemAttrAccessor(item_accessor_map, "live_photo_flag_attr");
  explore_effective_play_count_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "explore_effective_play_count_attr");
  author_grade_key_accessor_ = GetItemAttrAccessor(item_accessor_map, "author_grade_key_attr");
  merchant_hetu_tag_id_photo_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "merchant_hetu_tag_id_photo_attr");
  explore_stats_report_count_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "explore_stats_report_count_attr");
  fountain_stats_report_count_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "fountain_stats_report_count_attr");
  thanos_stats_report_count_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "thanos_stats_report_count_attr");
  nebula_stats_report_count_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "nebula_stats_report_count_attr");
  fountain_stats_short_play_count_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "fountain_stats_short_play_count_attr");
  thanos_stats_short_play_count_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "thanos_stats_short_play_count_attr");
  nebula_stats_short_play_count_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "nebula_stats_short_play_count_attr");
  hetu_sim_cluster_id_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "hetu_sim_cluster_id_attr");
  author_age_segment_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "author_age_segment_attr");
  nebula_stats_view_length_sum_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "nebula_stats_view_length_sum_attr");
  thanos_stats_view_length_sum_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "thanos_stats_view_length_sum_attr");
  fountain_stats_view_length_sum_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "fountain_stats_view_length_sum_attr");
  author_shop_score_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "author_shop_score_attr");
  author_max_item_score_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "author_max_item_score_attr");
  secure_grading_action_code_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "secure_grading_action_code_attr");
  timeliness_flag_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "timeliness_flag_attr");
  coldstart_guarantee_value_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "coldstart_guarantee_value_attr");
  fangpin_aid_filter_ratio_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "fangpin_aid_filter_ratio_attr");
  plc_business_type_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "plc_business_type_attr");
  author_tail_galaxy_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "author_tail_galaxy_attr");
  author_tail_climb_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "author_tail_climb_attr");
  author_tail_vcs_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "author_tail_vcs_attr");
  author_liezhi_pic_count_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "author_liezhi_pic_count_attr");
  author_hash_tag_id_list_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "author_hash_tag_id_list_attr");
  live_photo_duration_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "live_photo_duration_attr");
  is_tv_station_bottom_bar_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "is_tv_station_bottom_bar_attr");
  hot_trend_generalized_info_source_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "hot_trend_generalized_info_source_attr");
}

void CommonPhotoInfo::ResetPhoto(ReadableRecoContextInterface *context, const CommonRecoResult &result) {
  photo_id_ = context->GetIntItemAttr(result, photo_id_accessor_);
  audit_hot_high_tag_level_ = context->GetIntItemAttr(result, audit_hot_high_tag_level_accessor_);
  duration_ms_ = context->GetIntItemAttr(result, duration_ms_accessor_);
  upload_type_ = context->GetIntItemAttr(result, upload_type_accessor_);
  show_level_a_ = context->GetIntItemAttr(result, show_level_a_accessor_);
  review_pass_level_b_ = context->GetIntItemAttr(result, review_pass_level_b_accessor_);
  photo_total_report_count_ = context->GetIntItemAttr(result, photo_total_report_count_accessor_);
  author_total_report_count_ = context->GetIntItemAttr(result, author_total_report_count_accessor_);
  title_evil_level_ = context->GetDoubleItemAttr(result, title_evil_level_accessor_);
  ocr_cover_text_evil_level_ = context->GetDoubleItemAttr(result, ocr_cover_text_evil_level_accessor_);
  manjiao_markcode_ = context->GetIntListItemAttr(result, manjiao_markcode_accessor_);
  hetu_level_one_tag_list_ = context->GetIntListItemAttr(result, hetu_level_one_tag_list_accessor_);
  hetu_level_two_tag_list_ = context->GetIntListItemAttr(result, hetu_level_two_tag_list_accessor_);
  hetu_level_three_tag_list_ = context->GetIntListItemAttr(result, hetu_level_three_tag_list_accessor_);
  hetu_level_four_tag_list_ = context->GetIntListItemAttr(result, hetu_level_four_tag_list_accessor_);
  hetu_level_five_tag_list_ = context->GetIntListItemAttr(result, hetu_level_five_tag_list_accessor_);
  hetu_face_id_tag_list_ = context->GetIntListItemAttr(result, hetu_face_id_tag_list_accessor_);
  hetu_tag_list_ = context->GetIntListItemAttr(result, hetu_tag_list_accessor_);
  hetu_v2_level_one_tag_list_ =
      context->GetIntListItemAttr(result, hetu_v2_level_one_tag_list_accessor_);
  hetu_v2_level_two_tag_list_ =
      context->GetIntListItemAttr(result, hetu_v2_level_two_tag_list_accessor_);
  hetu_v2_level_three_tag_list_ =
      context->GetIntListItemAttr(result, hetu_v2_level_three_tag_list_accessor_);
  hetu_v2_tag_list_ =
      context->GetIntListItemAttr(result, hetu_v2_tag_list_accessor_);
  hetu_v3_level_one_tag_list_ =
      context->GetIntListItemAttr(result, hetu_v3_level_one_tag_list_accessor_);
  audit_hot_cover_level_ = context->GetIntItemAttr(result, audit_hot_cover_level_accessor_);
  caption_length_ = context->GetIntItemAttr(result, caption_length_accessor_);
  picture_type_ = context->GetIntItemAttr(result, picture_type_accessor_);
  impression_audit_ = context->GetIntItemAttr(result, impression_audit_accessor_);
  audit_b_second_tag_ = context->GetIntItemAttr(result, audit_b_second_tag_accessor_);
  video_quality_assessment_flag_ = context->GetIntItemAttr(result, video_quality_assessment_flag_accessor_);
  mmu_photo_low_quality_model_ =
      context->GetStringItemAttr(result, mmu_photo_low_quality_model_accessor_);
  mmu_low_quality_model_score_40_ =
      context->GetDoubleItemAttr(result, mmu_low_quality_model_score_40_accessor_);
  mmu_low_quality_model_score_42_ =
      context->GetDoubleItemAttr(result, mmu_low_quality_model_score_42_accessor_);
  mmu_low_quality_model_score_46_ =
      context->GetDoubleItemAttr(result, mmu_low_quality_model_score_46_accessor_);
  mmu_low_quality_model_score_52_ =
      context->GetDoubleItemAttr(result, mmu_low_quality_model_score_52_accessor_);
  mmu_low_quality_model_score_63_ =
      context->GetDoubleItemAttr(result, mmu_low_quality_model_score_63_accessor_);
  mmu_low_quality_model_score_64_ =
      context->GetDoubleItemAttr(result, mmu_low_quality_model_score_64_accessor_);
  mmu_low_quality_model_score_90_ =
      context->GetDoubleItemAttr(result, mmu_low_quality_model_score_90_accessor_);
  mmu_low_quality_model_score_104_ =
      context->GetDoubleItemAttr(result, mmu_low_quality_model_score_104_accessor_);
  mmu_low_quality_model_score_123_ =
      context->GetDoubleItemAttr(result, mmu_low_quality_model_score_123_accessor_);
  mmu_low_quality_model_score_143_ =
      context->GetDoubleItemAttr(result, mmu_low_quality_model_score_143_accessor_);
  mmu_low_quality_model_score_145_ =
      context->GetDoubleItemAttr(result, mmu_low_quality_model_score_145_accessor_);
  mmu_low_quality_model_score_150_ =
      context->GetDoubleItemAttr(result, mmu_low_quality_model_score_150_accessor_);
  mmu_low_quality_model_score_163_ =
      context->GetDoubleItemAttr(result, mmu_low_quality_model_score_163_accessor_);
  mmu_low_quality_model_score_164_ =
      context->GetDoubleItemAttr(result, mmu_low_quality_model_score_164_accessor_);
  photo_dynamic_xtrs_str_ = context->GetStringItemAttr(result, photo_dynamic_xtrs_str_accessor_);
  merchant_item_id_list_ = context->GetIntListItemAttr(result, merchant_item_id_list_accessor_);
  merchant_photo_cart_relation_ =
      context->GetDoubleItemAttr(result, merchant_photo_cart_relation_accessor_);
  risk_man_risk_photo_ = context->GetIntItemAttr(result, risk_man_risk_photo_accessor_);
  need_shuffle_photo_ = context->GetIntItemAttr(result, need_shuffle_photo_accessor_);
  author_fans_count_ = context->GetIntItemAttr(result, author_fans_count_accessor_);
  explore_server_show_ = context->GetIntItemAttr(result, explore_server_show_accessor_);
  explore_click_ = context->GetIntItemAttr(result, explore_click_accessor_);
  explore_real_show_ = context->GetIntItemAttr(result, explore_real_show_accessor_);
  explore_view_length_sum_ = context->GetIntItemAttr(result, explore_view_length_sum_accessor_);
  explore_long_play_count_ = context->GetIntItemAttr(result, explore_long_play_count_accessor_);
  explore_negative_ = context->GetIntItemAttr(result, explore_negative_accessor_);
  explore_like_ = context->GetIntItemAttr(result, explore_like_accessor_);
  explore_follow_ = context->GetIntItemAttr(result, explore_follow_accessor_);
  explore_forward_ = context->GetIntItemAttr(result, explore_forward_accessor_);
  explore_comment_ = context->GetIntItemAttr(result, explore_comment_accessor_);
  explore_collect_ = context->GetIntItemAttr(result, explore_collect_accessor_);
  upload_time_ = context->GetIntItemAttr(result, upload_time_accessor_);
  long_term_photo_ = context->GetIntItemAttr(result, long_term_photo_accessor_);
  nebula_real_show_ = context->GetIntItemAttr(result, nebula_real_show_accessor_);
  nebula_negative_ = context->GetIntItemAttr(result, nebula_negative_accessor_);
  nebula_like_ = context->GetIntItemAttr(result, nebula_like_accessor_);
  nebula_follow_ = context->GetIntItemAttr(result, nebula_follow_accessor_);
  nebula_comment_ = context->GetIntItemAttr(result, nebula_comment_accessor_);
  nebula_collect_ = context->GetIntItemAttr(result, nebula_collect_accessor_);
  nebula_forward_ = context->GetIntItemAttr(result, nebula_forward_accessor_);
  thanos_real_show_ = context->GetIntItemAttr(result, thanos_real_show_accessor_);
  thanos_negative_ = context->GetIntItemAttr(result, thanos_negative_accessor_);
  thanos_like_ = context->GetIntItemAttr(result, thanos_like_accessor_);
  thanos_follow_ = context->GetIntItemAttr(result, thanos_follow_accessor_);
  thanos_comment_ = context->GetIntItemAttr(result, thanos_comment_accessor_);
  thanos_collect_ = context->GetIntItemAttr(result, thanos_collect_accessor_);
  thanos_forward_ = context->GetIntItemAttr(result, thanos_forward_accessor_);
  fountain_real_show_ = context->GetIntItemAttr(result, fountain_real_show_accessor_);
  fountain_negative_ = context->GetIntItemAttr(result, fountain_negative_accessor_);
  fountain_like_ = context->GetIntItemAttr(result, fountain_like_accessor_);
  fountain_follow_ = context->GetIntItemAttr(result, fountain_follow_accessor_);
  fountain_comment_ = context->GetIntItemAttr(result, fountain_comment_accessor_);
  fountain_collect_ = context->GetIntItemAttr(result, fountain_collect_accessor_);
  fountain_forward_ = context->GetIntItemAttr(result, fountain_forward_accessor_);
  is_sirius_punish_ = context->GetIntItemAttr(result, is_sirius_punish_accessor_);
  enable_download_ = context->GetIntItemAttr(result, enable_download_accessor_);
  author_id_ = context->GetIntItemAttr(result, author_id_accessor_);
  cold_start_breakout_score_ =
      context->GetDoubleItemAttr(result, cold_start_breakout_score_accessor_);
  questionaire_info_exposure_count_ =
      context->GetIntItemAttr(result, questionaire_info_exposure_count_accessor_);
  questionaire_info_negative_count_ =
      context->GetIntItemAttr(result, questionaire_info_negative_count_accessor_);
  questionaire_info_positive_count_ =
      context->GetIntItemAttr(result, questionaire_info_positive_count_accessor_);
  questionaire_info_unsure_count_ =
      context->GetIntItemAttr(result, questionaire_info_unsure_count_accessor_);
  explore_questionaire_info_exposure_count_ =
      context->GetIntItemAttr(result, explore_questionaire_info_exposure_count_accessor_);
  explore_questionaire_info_negative_count_ =
      context->GetIntItemAttr(result, explore_questionaire_info_negative_count_accessor_);
  explore_questionaire_info_positive_count_ =
      context->GetIntItemAttr(result, explore_questionaire_info_positive_count_accessor_);
  explore_questionaire_info_unsure_count_ =
      context->GetIntItemAttr(result, explore_questionaire_info_unsure_count_accessor_);
  explore_punish_ = context->GetIntItemAttr(result, explore_punish_accessor_);
  explore_punish_city_ = context->GetIntListItemAttr(result, explore_punish_city_accessor_);
  photo_status_ = context->GetIntItemAttr(result, photo_status_accessor_);
  topk_audit_tag_ = context->GetIntItemAttr(result, topk_audit_tag_accessor_);
  topk_audit_level_ = context->GetIntItemAttr(result, topk_audit_level_accessor_);
  is_mid_video_photo_ = context->GetIntItemAttr(result, is_mid_video_photo_accessor_);
  dup_cluster_id_ = context->GetIntItemAttr(result, dup_cluster_id_accessor_);
  pic_and_selfdup_id_ = context->GetIntItemAttr(result, pic_and_selfdup_id_accessor_);
  width_ = context->GetDoubleItemAttr(result, width_accessor_);
  height_ = context->GetDoubleItemAttr(result, height_accessor_);
  high_hot_audit_tag_v2_ = context->GetIntItemAttr(result, high_hot_audit_tag_v2_accessor_);
  audit_user_experiment_level_ = context->GetIntItemAttr(result, audit_user_experiment_level_accessor_);
  eyeshot_source_ = context->GetIntItemAttr(result, eyeshot_source_accessor_);
  young_inc_tags_ = context->GetStringItemAttr(result, young_inc_tags_accessor_);
  final_cross_section_first_class_id_ =
    context->GetIntItemAttr(result, final_cross_section_first_class_id_accessor_);
  light_inc_photo_flag_ = context->GetIntItemAttr(result, light_inc_photo_flag_accessor_);
  high_value_pic_flag_ = context->GetIntItemAttr(result, high_value_pic_flag_accessor_);
  audit_cold_review_level_ = context->GetIntItemAttr(result, audit_cold_review_level_accessor_);
  data_set_tags_ = context->GetIntListItemAttr(result, data_set_tags_accessor_);
  audit_risk_immd_tag_ = context->GetIntItemAttr(result, audit_risk_immd_tag_accessor_);
  ecom_intent_score_ = context->GetIntItemAttr(result, ecom_intent_score_accessor_);
  data_set_tags_bit_ = context->GetIntItemAttr(result, data_set_tags_bit_accessor_);
  explore_short_play_ = context->GetIntItemAttr(result, explore_short_play_accessor_);
  magic_face_type_ = context->GetIntItemAttr(result, magic_face_type_accessor_);
  magic_face_id_ = context->GetIntItemAttr(result, magic_face_id_accessor_);
  is_repost_photo_ = context->GetIntItemAttr(result, is_repost_photo_accessor_);
  sirius_distribution_info__mark_cod_ =
    context->GetIntListItemAttr(result, sirius_distribution_info__mark_cod_accessor_);
  live_photo_flag_ = context->GetIntItemAttr(result, live_photo_flag_accessor_);
  explore_effective_play_count_ = context->GetIntItemAttr(result, explore_effective_play_count_accessor_);
  author_grade_key_ = context->GetIntItemAttr(result, author_grade_key_accessor_);
  merchant_hetu_tag_id_photo_ = context->GetIntItemAttr(result, merchant_hetu_tag_id_photo_accessor_);
  explore_stats_report_count_ = context->GetIntItemAttr(result, explore_stats_report_count_accessor_);
  fountain_stats_report_count_ = context->GetIntItemAttr(result, fountain_stats_report_count_accessor_);
  thanos_stats_report_count_ = context->GetIntItemAttr(result, thanos_stats_report_count_accessor_);
  nebula_stats_report_count_ = context->GetIntItemAttr(result, nebula_stats_report_count_accessor_);
  fountain_stats_short_play_count_ =
    context->GetIntItemAttr(result, fountain_stats_short_play_count_accessor_);
  thanos_stats_short_play_count_ = context->GetIntItemAttr(result, thanos_stats_short_play_count_accessor_);
  nebula_stats_short_play_count_ = context->GetIntItemAttr(result, nebula_stats_short_play_count_accessor_);
  hetu_sim_cluster_id_ = context->GetIntItemAttr(result, hetu_sim_cluster_id_accessor_);
  author_age_segment_ = context->GetIntItemAttr(result, author_age_segment_accessor_);
  nebula_stats_view_length_sum_ = context->GetIntItemAttr(result, nebula_stats_view_length_sum_accessor_);
  thanos_stats_view_length_sum_ = context->GetIntItemAttr(result, thanos_stats_view_length_sum_accessor_);
  fountain_stats_view_length_sum_ = context->GetIntItemAttr(result, fountain_stats_view_length_sum_accessor_);
  author_shop_score_ = context->GetDoubleItemAttr(result, author_shop_score_accessor_);
  author_max_item_score_ = context->GetDoubleItemAttr(result, author_max_item_score_accessor_);
  secure_grading_action_code_ = context->GetIntItemAttr(result, secure_grading_action_code_accessor_);
  timeliness_flag_ = context->GetIntItemAttr(result, timeliness_flag_accessor_);
  coldstart_guarantee_value_ = context->GetIntItemAttr(result, coldstart_guarantee_value_accessor_);
  fangpin_aid_filter_ratio_ = context->GetDoubleItemAttr(result, fangpin_aid_filter_ratio_accessor_);
  plc_business_type_ = context->GetIntItemAttr(result, plc_business_type_accessor_);
  author_tail_galaxy_ = context->GetIntItemAttr(result, author_tail_galaxy_accessor_);
  author_tail_climb_ = context->GetIntItemAttr(result, author_tail_climb_accessor_);
  author_tail_vcs_ = context->GetIntItemAttr(result, author_tail_vcs_accessor_);
  author_liezhi_pic_count_ = context->GetIntItemAttr(result, author_liezhi_pic_count_accessor_);
  author_hash_tag_id_list_ = context->GetIntListItemAttr(result, author_hash_tag_id_list_accessor_);
  live_photo_duration_ = context->GetDoubleItemAttr(result, live_photo_duration_accessor_);
  is_tv_station_bottom_bar_ = context->GetIntItemAttr(result, is_tv_station_bottom_bar_accessor_);
  hot_trend_generalized_info_source_ =
    context->GetIntListItemAttr(result, hot_trend_generalized_info_source_accessor_);
}

}  // namespace explore
}  // namespace platform
}  // namespace ks
