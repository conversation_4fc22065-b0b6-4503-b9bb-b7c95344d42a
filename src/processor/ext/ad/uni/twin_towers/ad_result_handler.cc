#include "dragon/src/processor/ext/ad/uni/twin_towers/ad_result_handler.h"
#include <tensorflow/core/framework/tensor.h>
#include <tensorflow/core/framework/tensor_shape.h>
#include <tensorflow/core/framework/types.pb.h>

#include <algorithm>
#include <cstdint>
#include <limits>
#include <memory>
#include <set>
#include <string>
#include <unordered_set>
#include <vector>
#include <utility>
#include "base/common/logging.h"
#include "dragon/src/processor/ext/ad/uni/twin_towers/result_handler/result_handler_base.h"
#include "dragon/src/processor/ext/ad/uni/twin_towers/constant.h"
#include "result_handler/result_handler_base.h"
#include "teams/ad/ad_proto/kuaishou/retrieval/bits_index.pb.h"
#include "teams/reco-arch/embedding_manager/embedding_data/parameters.h"
#include "teams/reco-arch/embedding_manager/embedding_data/embedding_store_manager.h"


using kuaishou::ad::algorithm::UniversePredictResponse;
using reco_arch::emb_manager::Parameters;

namespace ks {
namespace platform {

#define CONFIG_CHECK(cond, msg)                                   \
  if (!(cond)) {                                                  \
    CL_LOG(ERROR) << "AdResultHandlerRetriever init failed! " << (msg); \
    return false;                                                 \
  }

bool AdResultHandlerRetriever::enable_secondary_id_ = false;
bool AdResultHandlerRetriever::return_score_ = false;

void AdResultHandlerRetriever::Retrieve(AddibleRecoContextInterface *context) {
  auto slot_idx = context->GetIntCommonAttr(kAdTwinTowersSlotAttrName);
  if (!slot_idx) {
    LOG(ERROR) << "Get slot_idx failed";
    return;
  }

  const kuaishou::retr::BitsIndex* target_index = &empty_target_index_;
  if (!target_index_attr_.empty()) {
    target_index = context->GetProtoMessagePtrCommonAttr<kuaishou::retr::BitsIndex>(target_index_attr_);
  }
  if (!target_index) {
    LOG(ERROR) << "Get target_index failed";
    return;
  }
  if (output_ops_.size() < 2) {
    LOG(ERROR) << "output_ops size < 2";
    return;
  }
  results_.clear();
  auto topk_score = context->GetDoubleListCommonAttr(output_ops_[0]);
  auto topk_index = context->GetIntListCommonAttr(output_ops_[1]);
  // topk score
  if (!topk_score || !topk_index) {
    LOG(ERROR) << "topk_score==null or topk_index==null: topk_score=" << topk_score.has_value()
      << ", topk_index=" << topk_score.has_value();
    return;
  }
  tensorflow::Tensor tensor_score(tensorflow::DataType::DT_FLOAT,
                                  tensorflow::TensorShape({1, (int64)topk_score->size()}));
  std::copy_n(topk_score->data(), topk_score->size(), tensor_score.flat<float>().data());
  results_.emplace_back(std::move(tensor_score));

  tensorflow::Tensor tensor_index(tensorflow::DataType::DT_INT32,
                                  tensorflow::TensorShape({1, (int64)topk_index->size()}));
  std::copy_n(topk_index->data(), topk_index->size(), tensor_index.flat<int>().data());
  results_.emplace_back(std::move(tensor_index));

  LOG(INFO) << "tensor_score: " << results_[0].DebugString(16)
    << ", tensor_index:" << results_[1].DebugString(16);

  // results_.push_back();
  auto& emb_manager_instance = reco_arch::emb_manager::EmbeddingStoreManager::Instance();
  result_handler_->Reset();

  result_option_.result_width = topk_index->size();
  result_option_.result_count = context->GetRequestNum();
  if (result_option_.result_count > topk_index->size()) {
    result_option_.result_count = topk_index->size();
    LOG(WARNING) << "request num is great than topk size";
  }
  result_option_.topk_size = topk_index->size();
  result_handler_->HandleResult(result_option_, emb_manager_instance.GetPhotoOcpcIndex(slot_idx.value()),
    emb_manager_instance.GetPhotoOcpcIndexShort(slot_idx.value()),
    *target_index, results_, output_ops_);

  auto const& result = result_handler_->Result();
  if (result.empty()) {
    LOG(ERROR) << "result handler is empty";
    return;
  }

  // 对齐老架构的打点
  size_t result_size = result[0].size();
  if (result_size == 0) {
    LOG_EVERY_N(ERROR, 80000) << "Empty result.";
    context->SetExecutionStatus(ExecutionStatus::FAILED);
    return;
  }
  float sum = 0.0f;
  float result_max = std::numeric_limits<float>::lowest();
  float result_min = std::numeric_limits<float>::max();
  float result_median = result[0][result_size >> 1].Score(0);

  // secondray id 对曝光影响很大
  std::vector<int64> secondary_ids;
  if (enable_secondary_id_) {
    secondary_ids.reserve(result_size);
  }
  int response_item_count = 0;
  int request_item_count = context->GetRequestNum();
  for (auto &item_id : result[0]) {
    float score = return_score_ ? item_id.Score(0) : 0.0f;
    context->AddCommonRecoResult(item_id.Id(0), 0, score, 0);
    if (enable_secondary_id_) {
      int64 secondary_id = item_id.Id(1);
      secondary_ids.emplace_back(secondary_id);
    }
    float item_score = item_id.Score(0);
    sum += item_score;
    result_max = std::max<float>(result_max, item_score);
    result_min = std::min<float>(result_min, item_score);
    // result_hanlder_ 的填充逻辑不能保证 result[0] 返回的数量跟 request_num 严格匹配, 因此要在这里做一个检查
    ++response_item_count;
    if (response_item_count >= request_item_count) {
      break;
    }
  }
  if (enable_secondary_id_) {
    bool ret = context->SetIntListCommonAttr(
      kAdTwinTowersSecondaryIdAttrName, std::move(secondary_ids));
    if (!ret) {
      CL_LOG(ERROR) << "Set common attr failed: " << kAdTwinTowersSecondaryIdAttrName
                    << ", user id: " << context->GetUserId();
    }
  }
  // 对齐老架构的打点
  float avg = sum / result_size;
  falcon::Stat("twin_towers_service.top_value", static_cast<int64_t>(result_max * 1e6));
  falcon::Stat("twin_towers_service.min_value", static_cast<int64_t>(result_min * 1e6));
  falcon::Stat("twin_towers_service.median_value", static_cast<int64_t>(result_median * 1e6));
  falcon::Stat("twin_towers_service.sum_value", static_cast<int64_t>(sum * 1e3));
  falcon::Stat("twin_towers_service.avg_value", static_cast<int64_t>(avg * 1e6));
  falcon::Stat("twin_towers_service.result_size", static_cast<int64_t>(result_size));
}

bool AdResultHandlerRetriever::InitProcessor() {
  static std::once_flag init_flag;
  std::call_once(init_flag, []() {
    enable_secondary_id_ = Parameters::Instance().EnableSecondaryId();
    return_score_ = Parameters::Instance().GetReturnScore();
    LOG(INFO) << "Enable secondary id: " << enable_secondary_id_
              << ", return score: " << return_score_;
    return true;
  });

  auto result_config = ks::ad_twin_towers::TwinTowersKconfManager::Instance()
                           ->GetBlockConfig<ks::ad_twin_towers::ResultHandlerConfig>("result_handler");
  // ad 代码里神奇的逻辑，抄过来.....
  int max_return_unit_per_photo =
      base::DynamicJsonConfig::GetConfig(false, false)->GetInt("max_return_unit_per_photo", -1);
  if (max_return_unit_per_photo > 0) {
    result_config->max_return_unit_per_photo_ = max_return_unit_per_photo;
  }
  bool enable_bid = base::DynamicJsonConfig::GetConfig(false, false)->GetBoolean("enable_bid", false);
  if (enable_bid) {
    result_config->enable_bid_ = enable_bid;
  }
  result_handler_ = CreateResultHandler(result_config);
  // TODO(kongyu) HandleOption 不确定会不会在运行中变化，暂时先固定
  result_option_.result_width = 5000;
  // 本质上是 topk, topk 有两种配置来源，一种是 compute_engine 里面取到
  // 的 "topk" 字段，另一个是 cluster_search 里面的 "topk_of_combine_key"
  result_option_.result_count = 5000;
  // trigger_input_tensor 的第 0 维，一般是 1
  result_option_.zeroth_dim_size = 1;
  result_option_.result_index_list = result_config->result_index_list_;
  // output_ops,
  RecoUtil::ExtractStringListFromJsonConfig(config()->Get("output_ops"), &output_ops_);
  if (output_ops_.empty()) {
    CL_LOG(ERROR) << "output_ops is empty";
    return false;
  }
  target_index_attr_ = config()->GetString("target_index");
  if (target_index_attr_.empty()) {
    CL_LOG(INFO) << "target_index is empty";
  }
  return true;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AdResultHandlerRetriever, AdResultHandlerRetriever)

}  // namespace platform
}  // namespace ks
