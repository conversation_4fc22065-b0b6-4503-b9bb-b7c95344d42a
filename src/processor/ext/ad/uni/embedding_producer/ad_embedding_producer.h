#pragma once

#include <memory>
#include <map>
#include <string>
#include <vector>

#include "serving_base/jansson/json.h"
#include "teams/ad/ad_nn/embedding_data/item_info_impl.h"
#include "teams/ad/ad_nn/embedding_data/statistical_filter.h"
#include "teams/ad/ad_nn/feature/feature_extractor_bundle.h"
#include "teams/ad/ad_nn/feature/feature_file_info.h"
#include "teams/ad/ad_nn/feature/type_define.h"
#include "teams/ad/ad_nn/feature_store/common.h"
#include "teams/ad/ad_nn/feature_store/extract_feature_store.h"
#include "teams/ad/ad_nn/feature_store/feature_manager.h"
#include "teams/ad/ad_nn/kconf_manager/cluster_type.h"
#include "teams/ad/ad_nn/kconf_manager/embedding_producer_config.h"
#include "teams/ad/ad_nn/kconf_manager/twin_towers_kconf_manager.h"
#include "teams/ad/ad_nn/model/hybrid_embedding.h"

using ks::ad_nn::AdFeatureExtractorBundle;
using ks::ad_nn::ClusterType;
using ks::ad_nn::ExtractFeatureStore;
using ks::ad_nn::FeatureManager;
using ks::ad_nn::FeatureData;
using ks::ad_nn::FeatureFileInfo;
using ks::ad_nn::FeatureStructInfo;
using ks::ad_nn::GraphType;
using ks::ad_nn::Operation;
using ks::ad_nn::FeatureOrgLayout;
using ks::ad_nn::ExtractType;
using ks::ad_nn::StorageType;

namespace ks {
namespace platform {

class ProduceEmbeddingCore {
 public:
  ~ProduceEmbeddingCore() {}

  static ProduceEmbeddingCore &Singleton() {
    static ProduceEmbeddingCore instance;
    return instance;
  }

  void Start();

 private:
  ProduceEmbeddingCore() {}

  inline bool IsBtqModel(const std::string &model_src) { return model_src == "btq"; }

  void InitFilterConfig();

  void GetX7Id();
  void AcquireFeatureInfo(const std::vector<FeatureData>& data, Operation op);
  uint32_t GetEmbeddingDim(uint32_t field);
  void FetchFeatureInfo(const std::string &feature_path);
  void StartFeatureManager();

 private:
  base::Json *config_ = nullptr;
  std::shared_ptr<ks::ad_twin_towers::EmbeddingSourceConfig> embedding_config_;
  std::shared_ptr<ks::ad_nn::ClusterSearchConfig> cluster_search_config_;

  std::string feature_path_;
  bool cluster_search_ = false;
  ClusterType cluster_type_ = ClusterType::CREATIVE;
  GraphType graph_type_ = GraphType::E2E;
  int score_threshold_version_ = -1;
  bool enable_bid_ = false;
  bool enable_sid_ = false;
  int x7_cateid_common_attr_nameid_ = 61085;
  bool sort_bid_ = false;
  bool mio_model_ = false;
  bool enable_continuous_check_ = true;

  ks::ad_twin_towers::FilterCreativeConfig filter_config_;
  ks::ad_twin_towers::StatisticalFilter statistical_filter_;

  ks::ad_nn::ConcurrentItemInfoMap updating_ids_;

  std::map<std::string, ks::ad_nn::FieldEmbeddingInfoMap> emp_shard_infos_;

  // Used to manager all the embedding feature.
  std::unique_ptr<FeatureManager> feature_manager_;
  ExtractFeatureStore* active_feature_ = nullptr;
  std::shared_ptr<FeatureFileInfo> feature_file_info_;
  std::unique_ptr<AdFeatureExtractorBundle> fea_ext_bundle_;
  FeatureStructInfo feature_struct_info_;

  DISALLOW_COPY_AND_ASSIGN(ProduceEmbeddingCore);
};

}  // namespace platform
}  // namespace ks
