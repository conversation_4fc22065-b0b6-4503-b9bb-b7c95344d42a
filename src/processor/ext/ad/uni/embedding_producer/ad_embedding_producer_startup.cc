#include "dragon/src/processor/ext/ad/uni/embedding_producer/ad_embedding_producer_startup.h"

#include <string>

#include "dragon/src/processor/ext/ad/uni/embedding_producer/ad_embedding_producer.h"
#include "teams/reco-arch/embedding_manager/embedding_data/parameters.h"
#include "teams/reco-arch/embedding_manager/tools/common_tools.h"
#include "teams/ad/ad_nn/monitor/monitor.h"

namespace ks {
namespace platform {

using reco_arch::emb_manager::Parameters;
using reco_arch::tools::VerifyCode;

bool AdEmbeddingProducerStartupEnricher::InitProcessor() {
  static std::once_flag init_flag;
  std::call_once(init_flag, []() {
    // set verify code
    std::string cmd = Parameters::Instance().GetCmd();
    VerifyCode::Instance().Initialize(cmd);

    ks::ad_nn::Role::Instance().SetRole(ks::ad_nn::RoleType::EMBEDDING_PRODUCER);
    // 从本地加载 json
    ks::ad_twin_towers::TwinTowersKconfManager::Instance()->Start(true);

    // 物料消费
    ProduceEmbeddingCore::Singleton().Start();

    // start monitor
    ks::ad_nn::monitor::MonitorOption option;
    option.grpc_name = Parameters::Instance().GetKessName();
    ks::ad_nn::monitor::Monitor::Instance()->Start(option);

    CL_LOG(INFO) << "ad_embedding_producer startup succeeds";
  });
  return true;
}

void AdEmbeddingProducerStartupEnricher::Enrich(MutableRecoContextInterface *context,
                                        RecoResultConstIter begin,
                                        RecoResultConstIter end) {
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AdEmbeddingProducerStartupEnricher, AdEmbeddingProducerStartupEnricher)
}  // namespace platform
}  // namespace ks
