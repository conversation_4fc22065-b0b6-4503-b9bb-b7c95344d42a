#pragma once

#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class AdEmbeddingProducerStartupEnricher : public CommonRecoBaseEnricher {
 public:
  AdEmbeddingProducerStartupEnricher() {
  }

  virtual ~AdEmbeddingProducerStartupEnricher() {
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  bool IsAsync() const override {
    return false;
  }

 private:
  bool InitProcessor() override;

 private:
  DISALLOW_COPY_AND_ASSIGN(AdEmbeddingProducerStartupEnricher);
};

}  // namespace platform
}  // namespace ks
