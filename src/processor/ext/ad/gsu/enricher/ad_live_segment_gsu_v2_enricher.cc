#include "dragon/src/processor/ext/ad/gsu/enricher/ad_live_segment_gsu_v2_enricher.h"

#include <iostream>
#include <algorithm>
#include <ctime>

#include "kconf/kconf.h"
#include "base/thread/thread_pool.h"
#include "base/common/closure.h"
#include "base/common/sleep.h"
#include "base/hash_function/city.h"
#include "learning/kuiba/parameter/parameter.h"
#include "teams/reco-arch/colossus/flat_generated/flat_generated_helper.h"

namespace ks {
namespace platform {

bool AdLiveSegmentGsuV2Enricher::GetIntList(const std::string &key, std::vector<int> *vec) {
  // 若不配则使用默认值，若配则长度必须等于和默认值长度相等
  const auto json_obj = config()->Get(key);
  if (!json_obj) {
    LOG(WARNING) << "key not find, the default slots config will be used, key = " << key;
    return true;
  }
  if (!json_obj->IsArray()) {
    LOG(ERROR) << "object is not a list, key = " << key;
    return false;
  }
  std::vector<int> cur_vec;
  for (auto *item : json_obj->array()) {
    int val = 0;
    cur_vec.push_back(item->IntValue(val));
  }
  vec->resize(cur_vec.size());
  std::copy(cur_vec.begin(), cur_vec.end(), vec->begin());
  return true;
}

bool AdLiveSegmentGsuV2Enricher::InitProcessor() {
  output_sign_attr_ = config()->GetString("output_sign_attr", "");
  if (output_sign_attr_.empty()) {
    LOG(ERROR) << "miss output_sign_attr";
    return false;
  }
  output_slot_attr_ = config()->GetString("output_slot_attr", "");
  if (output_slot_attr_.empty()) {
    LOG(ERROR) << "miss output_slot_attr";
    return false;
  }
  colossus_resp_attr_ = config()->GetString("colossus_resp_attr", "");
  if (colossus_resp_attr_.empty()) {
    LOG(ERROR) << "miss colossus_resp_attr";
    return false;
  }
  limit_num_ = config()->GetInt("limit_num", 1000);
  item_limit_num_ = config()->GetInt("item_limit_num", 10);
  target_aids_attr_ = config()->GetString("target_aids_attr", "aid");
  if (target_aids_attr_.empty()) {
    LOG(INFO) << "miss target_aids_attr";
    return false;
  }
  min_seconds_ago_ = config()->GetInt("min_seconds_ago", 60);
  if (min_seconds_ago_ < 0) {
    LOG(ERROR) << "miss min_seconds_ago";
    return false;
  }
  enable_new_product_category_ = config()->GetString(
      "enable_new_product_category", "enable_new_product_category");

  if (!GetIntList("slots_id", &slots_ids_)) return false;
  if (!GetIntList("mio_slots_id", &mio_slots_ids_)) return false;

  // remap_util 初始化
  fused_slot_sign_remap_ = config()->GetBoolean("fused_slot_sign_remap", false);
  if (fused_slot_sign_remap_ && !remap_util_.Init(config())) {
    LOG(ERROR) << "remap util init failed";
    return false;
  }
  LOG(INFO) << "fused_slot_sign_remap: " << fused_slot_sign_remap_;
  only_output_basic_slots_ = config()->GetBoolean("only_output_basic_slots", false);
  LOG(INFO) << "only_output_basic_slots: " << only_output_basic_slots_;

  use_ad_slot_size_ = config()->GetBoolean("use_ad_slot_size", true);
  parameter_sign_bits_ = config()->GetInt("parameter_sign_bits", 52);
  SIGN_MASK = ((1ul << parameter_sign_bits_) - 1);
  slot_as_attr_name_ = config()->GetBoolean("slot_as_attr_name", false);
  slot_as_attr_name_prefix_ = config()->GetString("slot_as_attr_name_prefix", "");

  return true;
}

const uint16 AdLiveSegmentGsuV2Enricher::kInvalidAuthorId = 0;  // 0xFFFF

void AdLiveSegmentGsuV2Enricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                              RecoResultConstIter end) {
  timer_.Start();
  int enable_new_cate = 1;
  if (context->GetIntCommonAttr(enable_new_product_category_)) {
    enable_new_cate = *context->GetIntCommonAttr(enable_new_product_category_);
  }

  // get history live info from colossus_resp
  auto items = context->GetPtrCommonAttr<AllLiveSegmentItems>(colossus_resp_attr_);
  if (!items || items->items_size() == 0) {
    CL_LOG(WARNING) << "colossus items empty";
    return;
  }
  CL_LOG(INFO) << "history items size: " << items->items_size() << ", colossu response";
  timer_.AppendCostMs("get_colossus_resp");

  auto item_size = items->items_size();
  // // h +8h 为北京时间
  // std::time_t timestamp_time_t = static_cast<std::time_t>(context->GetRequestTime() / 1000);
  // int h = std::localtime(&timestamp_time_t)->tm_hour;
  // if (h == 15 || h == 16 || h == 17) {  // 23, 00, 01
  //   min_seconds_ago_ = 120 * 60;
  // }
  int64 filter_time = context->GetRequestTime() / 1000 - min_seconds_ago_;

  std::vector<int64_t> output_slot;
  std::vector<int64_t> output_sign;
  output_slot.reserve(limit_num_ * slots_ids_.size());
  output_sign.reserve(limit_num_ * slots_ids_.size());

#define ADD_SIGN(i, expr)                                                                            \
  do {                                                                                               \
    if (i < mio_slots_ids_.size()) {                                                                 \
      uint64_t slot_sign = ((uint64_t)(slots_ids_[i]) << parameter_sign_bits_) | (SIGN_MASK & expr); \
      uint16_t slot_new = 0;                                                                         \
      uint64_t sign_new = 0;                                                                         \
      if (remap_util_.RemapSlotSign(mio_slots_ids_[i], slot_sign, &slot_new, &sign_new,              \
                                    use_ad_slot_size_)) {                                            \
        output_slot.emplace_back(slot_new);                                                          \
        output_sign.emplace_back(sign_new);                                                          \
      }                                                                                              \
    }                                                                                                \
  } while (0)

  int64 result_photo_num = 0;
  if (only_output_basic_slots_) {
    for (int i = item_size - 1; i >= 0; --i) {
      auto *item = &(items->items(i));
      if (item->timestamp() > filter_time) {
          continue;
      }
      ADD_SIGN(0, ((1L << 48) - 1) & item->live_id());
      ADD_SIGN(1, item->author_id());
      ADD_SIGN(2, (filter_time - item->timestamp()) / 3600 / 24);  // 小时间隔
      ADD_SIGN(3, item->duration_play_time() & 0xFFFF);
      ADD_SIGN(4, item->duration_play_time() >> 16);
      ADD_SIGN(5, (item->label() >> 16) & 0xF);
      ADD_SIGN(6, item->label());
      ADD_SIGN(7, item->live_play_time_gmv() >> 16);
      ADD_SIGN(8, item->channel());
      for (int t = 0; t < item_limit_num_; t++) {
        if (t >= item->item_id_size()) {
          ADD_SIGN(9, 0);
        } else {
          ADD_SIGN(9, item->item_id(t));
        }
        if (t >= item->item_category_size()) {
          ADD_SIGN(10, 0);
        } else {
          ADD_SIGN(10, item->item_category(t) / 10000);  // cid2
        }
        if (t >= item->item_category_size()) {
          ADD_SIGN(11, 0);
        } else {
          ADD_SIGN(11, item->item_category(t));  // cid3
        }
        if (t >= item->item_label_size()) {
          ADD_SIGN(12, 0);
          ADD_SIGN(13, 0);
        } else {
          ADD_SIGN(12, (item->item_label(t) & (1 << 21)) >> 21);  // click
          ADD_SIGN(13, (item->item_label(t) & (1 << 23)) >> 23);  // buy
        }
        if (t >= item->item_volume_size()) {
          ADD_SIGN(14, 0);
        } else {
          ADD_SIGN(14, item->item_volume(t) & 0x3FFFFF);  // volume_in_live
        }
      }
      ADD_SIGN(15, (item->living_attr_2() >> 25) & 0x7FFFF);  // total_order_nums
      ADD_SIGN(16, (item->living_attr_3() >> 41) & 0x7FFFFF);  // item_click_num
      if (++result_photo_num >= limit_num_) {
        break;
      }
    }
  } else {
    for (int i = item_size - 1; i >= 0; --i) {
      auto *item = &(items->items(i));
      if (item->timestamp() > filter_time) {
          continue;
      }
      ADD_SIGN(0, ((1L << 48) - 1) & item->live_id());
      ADD_SIGN(1, item->author_id());
      ADD_SIGN(2, (filter_time - item->timestamp()) / 3600 / 24);  // 小时间隔
      ADD_SIGN(3, item->duration_play_time() & 0xFFFF);
      ADD_SIGN(4, item->duration_play_time() >> 16);
      ADD_SIGN(5, (item->label() >> 16) & 0xF);
      ADD_SIGN(6, item->label());
      ADD_SIGN(7, item->live_play_time_gmv() & 0xFFFF);
      ADD_SIGN(8, item->live_play_time_gmv() >> 16);
      ADD_SIGN(9, item->channel());
      for (int t = 0; t < item_limit_num_; t++) {
        if (t >= item->item_id_size()) {
          ADD_SIGN(10, 0);
        } else {
          ADD_SIGN(10, item->item_id(t));
        }
        if (enable_new_cate) {
          if (t >= item->item_new_spu_size()) {
            ADD_SIGN(11, 0);
          } else {
            ADD_SIGN(11, item->item_new_spu(t));
          }
        } else {
          if (t >= item->item_spu_size()) {
            ADD_SIGN(11, 0);
          } else {
            ADD_SIGN(11, item->item_spu(t));
          }
        }
        if (t >= item->item_category_size()) {
          ADD_SIGN(12, 0);
        } else {
          ADD_SIGN(12, item->item_category(t) / 10000);  // cid2
        }
        if (t >= item->item_category_size()) {
          ADD_SIGN(13, 0);
        } else {
          ADD_SIGN(13, item->item_category(t));  // cid3
        }
        if (t >= item->item_label_size()) {
          ADD_SIGN(14, 0);
          ADD_SIGN(15, 0);
          ADD_SIGN(16, 0);
        } else {
          ADD_SIGN(14, (item->item_label(t) & (1 << 21)) >> 21);  // click
          ADD_SIGN(15, (item->item_label(t) & (1 << 22)) >> 22);  // ecomm_buy
          ADD_SIGN(16, (item->item_label(t) & (1 << 23)) >> 23);  // buy
        }
        if (t >= item->item_volume_size()) {
          ADD_SIGN(17, 0);
          ADD_SIGN(18, 0);
        } else {
          ADD_SIGN(17, item->item_volume(t) & 0x3FFFFF);  // volume_in_live
          ADD_SIGN(18, (item->item_volume(t) >> 22) & 0x1FFFFF);  // days_of_volume
        }
      }
      ADD_SIGN(19, item->living_attr_2() & 0x1FFFFFF);  // gmv
      ADD_SIGN(20, (item->living_attr_2() >> 25) & 0x7FFFF);  // total_order_nums
      ADD_SIGN(21, (item->living_attr_3() >> 41) & 0x7FFFFF);  // item_click_num
      ADD_SIGN(22, item->living_attr_4() & 0xFFFFFFF);  // live_show_pv
      if (++result_photo_num >= limit_num_) {
        break;
      }
    }
  }
#undef ADD_SIGN
  timer_.AppendCostMs("extract_signs");

  if (slot_as_attr_name_) {
    for (size_t i = 0; i < output_slot.size(); ++i) {
      context->AppendIntListCommonAttr(slot_as_attr_name_prefix_ + std::to_string(output_slot[i]),
        output_sign[i]);
    }
  }
  context->SetIntListCommonAttr(output_slot_attr_, std::move(output_slot));
  context->SetIntListCommonAttr(output_sign_attr_, std::move(output_sign));
  timer_.AppendCostMs("fill_attr");

  CL_LOG(INFO) << ", colossus_item_size: "  << item_size
               << ", limit_num: " << limit_num_ << ", filter_time: " << filter_time
               << ", result_photo_num: " << result_photo_num << ", timer: " << timer_.display()
               << " total:" << (timer_.Stop() / 1000.f) << "ms";
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AdLiveSegmentGsuV2Enricher, AdLiveSegmentGsuV2Enricher);

}  // namespace platform
}  // namespace ks
