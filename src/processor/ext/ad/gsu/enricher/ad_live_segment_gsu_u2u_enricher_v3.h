#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>
#include <unordered_map>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/core/common_reco_base.h"
#include "redis_proxy_client/redis_proxy_client.h"
#include "kess/rpc/grpc/grpc_client_builder.h"
#include "ks/reco_proto/proto/predict_kess_service.kess.grpc.pb.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "serving_base/utility/timer.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"
#include "teams/ad/ad_algorithm/live_model/proto/picasso_item.pb.h"
#include "teams/ad/ad_nn/feature_extract/processors/slot_sign_remap_util.h"
#include "teams/ad/picasso/sdk/proto/record.pb.h"
#include "teams/ad/picasso/sdk/proto/flat_generated/flat_chash_helper.h"

namespace ks {
namespace platform {

class AdLiveSegmentGsuU2uEnricherV3 : public CommonRecoBaseEnricher {
  using AllLiveSegmentItems = ks::ad_picasso::sdk::AllLiveSegmentItems;

 public:
  AdLiveSegmentGsuU2uEnricherV3() {}
  ~AdLiveSegmentGsuU2uEnricherV3() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;
  bool GetIntList(const std::string &key, std::vector<int> *vec);

  static const uint16 kInvalidAuthorId_;  // default value, 0xFFFF

  std::string output_sign_attr_;
  std::string output_slot_attr_;
  std::string colossus_resp_attr_;
  std::string limit_num_attr_;
  int limit_num_ = 100;
  int item_limit_num_ = 1;
  int list_top_k_ = 1;
  uint64_t sign_mask_ = ((1ul << 52) - 1);
  std::string target_aids_attr_;
  std::string explain_item_id_;
  std::string explain_item_x7_cat1_;
  std::string author_hist_x7_cat1_list_;
  std::string yellow_car_x7_cat1_list_;
  std::string enable_new_product_category_;
  std::string slot_as_attr_name_prefix_;
  bool fix_new_product_category_option_ = false;
  bool slot_as_attr_name_ = false;
  bool use_ad_slot_size_ = false;
  int parameter_sign_bits_ = 52;
  serving_base::Timer timer_;
  std::vector<int> slots_ids_;
  std::vector<int> mio_slots_ids_;
  bool fused_slot_sign_remap_ = false;
  bool only_output_basic_slots_ = false;
  SlotSignRemapUtil remap_util_;

  DISALLOW_COPY_AND_ASSIGN(AdLiveSegmentGsuU2uEnricherV3);
};

}  // namespace platform
}  // namespace ks

