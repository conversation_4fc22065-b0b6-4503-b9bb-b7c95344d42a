#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "absl/types/span.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/core/common_reco_base.h"
#include "serving_base/utility/timer.h"

namespace ks {
namespace platform {

class SeqSideinfoEnricher : public CommonRecoBaseEnricher {
 public:
  SeqSideinfoEnricher() {}
  ~SeqSideinfoEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;

  // 解析 category ，提取 1-3 级类目
  void ExtractCategoryLevels(absl::Span<const int64> category_list,
                            std::vector<int64> *cate1_list,
                            std::vector<int64> *cate2_list,
                            std::vector<int64> *cate3_list);

  // 停留时间分桶（10 个桶）
  void BucketizeStayTime(absl::Span<const int64> stay_time_list,
                        std::vector<int64> *bucket_list);

  // 计算所有时间差距类型
  void CalculateTimeGaps(absl::Span<const int64> timestamp_list,
                        int64 request_time,
                        std::vector<int64> *day_gap_list,
                        std::vector<int64> *hour_gap_list,
                        std::vector<int64> *hour_gap_v1_list);

  // 价格分桶 V1 版本（线性分桶，300 个桶）
  void BucketizePriceV1(absl::Span<const int64> price_list,
                       std::vector<int64> *bucket_list);

  // 价格分桶 V2 版本（非线性分桶，500 个桶）
  void BucketizePriceV2(absl::Span<const int64> price_list,
                       std::vector<int64> *bucket_list);

  // 提取 click_flow_type 的 16-23 bit
  void ExtractFlowTypeBits(absl::Span<const int64> flow_type_list,
                          std::vector<int64> *extracted_bits_list);

  // 解析 label 的不同 bit 位
  void ExtractLabelBits(absl::Span<const int64> label_list,
                       std::vector<int64> *order_list,
                       std::vector<int64> *shopping_list,
                       std::vector<int64> *interaction_list);



 private:
  // 输入属性配置
  std::string input_category_attr_;
  std::string input_detail_content_stay_time_attr_;
  std::string input_timestamp_attr_;
  std::string input_price_attr_;
  std::string input_click_flow_type_attr_;
  std::string input_label_attr_;

  // 输出属性配置
  std::string output_cate1_attr_;
  std::string output_cate2_attr_;
  std::string output_cate3_attr_;
  std::string output_stay_time_bucket_attr_;
  std::string output_day_gap_attr_;
  std::string output_hour_gap_attr_;
  std::string output_hour_gap_v1_attr_;
  std::string output_price_bucket_attr_;
  std::string output_flow_type_attr_;
  std::string output_order_attr_;
  std::string output_shopping_attr_;
  std::string output_interaction_attr_;
  std::string output_index_attr_;

  serving_base::Timer timer_;

  DISALLOW_COPY_AND_ASSIGN(SeqSideinfoEnricher);
};

}  // namespace platform
}  // namespace ks
