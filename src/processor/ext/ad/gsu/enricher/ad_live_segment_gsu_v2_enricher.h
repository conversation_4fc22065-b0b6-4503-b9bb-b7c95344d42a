#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>
#include <unordered_map>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/core/common_reco_base.h"
#include "redis_proxy_client/redis_proxy_client.h"
#include "kess/rpc/grpc/grpc_client_builder.h"
#include "ks/reco_proto/proto/predict_kess_service.kess.grpc.pb.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "serving_base/utility/timer.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"
#include "teams/ad/ad_algorithm/live_model/proto/picasso_item.pb.h"
#include "teams/ad/ad_nn/feature_extract/processors/slot_sign_remap_util.h"
#include "teams/ad/picasso/sdk/proto/record.pb.h"
#include "teams/ad/picasso/sdk/proto/flat_generated/flat_chash_helper.h"

namespace ks {
namespace platform {

class AdLiveSegmentGsuV2Enricher : public CommonRecoBaseEnricher {
  using AllLiveSegmentItems = ks::ad_picasso::sdk::AllLiveSegmentItems;

 public:
  AdLiveSegmentGsuV2Enricher() {}
  ~AdLiveSegmentGsuV2Enricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;
  bool GetIntList(const std::string &key, std::vector<int> *vec);

  static const uint16 kInvalidAuthorId;  // default value, 0xFFFF

  std::string output_sign_attr_;
  std::string output_slot_attr_;
  std::string colossus_resp_attr_;
  int limit_num_ = 1000;
  int item_limit_num_ = 10;
  int min_seconds_ago_ = 60;
  std::string target_aids_attr_;
  std::string enable_new_product_category_;
  bool use_ad_slot_size_ = true;
  int parameter_sign_bits_ = 52;
  uint64_t SIGN_MASK = ((1ul << 52) - 1);
  bool slot_as_attr_name_ = false;
  std::string slot_as_attr_name_prefix_ = "";

  serving_base::Timer timer_;
  std::vector<int> slots_ids_;
  std::vector<int> mio_slots_ids_;
  bool fused_slot_sign_remap_ = false;
  bool only_output_basic_slots_ = false;
  SlotSignRemapUtil remap_util_;

  DISALLOW_COPY_AND_ASSIGN(AdLiveSegmentGsuV2Enricher);
};

}  // namespace platform
}  // namespace ks

