#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_base.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "kess/rpc/grpc/grpc_client_builder.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "ks/reco_proto/proto/predict_kess_service.kess.grpc.pb.h"
#include "redis_proxy_client/redis_proxy_client.h"
#include "serving_base/utility/timer.h"
#include "teams/ad/ad_nn/feature_extract/processors/slot_sign_remap_util.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"

namespace ks {
namespace platform {

class AdLiveUserGoodsClickOptV1Enricher : public CommonRecoBaseEnricher {
 public:
  AdLiveUserGoodsClickOptV1Enricher() = default;
  ~AdLiveUserGoodsClickOptV1Enricher() = default;

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;
  bool GetIntList(const std::string &key, std::vector<int> *vec);
  int64_t log_x_plus_1(int64_t x);
  void CalculateStatistics(const std::vector<int64_t> &numbers, double *mean, int64_t *max, int64_t *min,
                           int64_t *cnt);
  int64_t min_int64(int64_t x, int64_t y);
  std::vector<int64_t> GetTopKKeys(const std::unordered_map<int64_t, int> &dict, int k);

  template <typename K, typename V>
  V get_value(const std::unordered_map<K, V> &m, const K &key, const V &default_value) {
    auto it = m.find(key);
    if (it != m.end()) {
      return it->second;
    } else {
      return default_value;
    }
  }

 private:
  // item id
  std::string colossus_goods_id_attr_ = "item_id_list";
  std::string colossus_order_goods_id_attr_ = "order_item_id_list";
  // time info
  std::string colossus_timestamp_attr_ = "click_timestamp_list";
  std::string colossus_submit_timestamp_attr_ = "submit_timestamp_list";
  std::string colossus_pay_timestamp_attr_ = "pay_timestamp_list";
  std::string detail_page_view_time_attr_ = "detail_page_view_time_list";
  // side info
  std::string colossus_category_attr_ = "category_a_list";            // x3
  std::string colossus_host_attr_ = "seller_id_list";                 // 1
  std::string colossus_shop_attr_ = "real_seller_id_list";            // 1
  std::string colossus_real_price_attr_ = "real_price_list";          // 1
  std::string colossus_label_attr_ = "label_list";                    // 1
  std::string colossus_uniform_spu_id_attr_ = "uniform_spu_id_list";  // 1
  // position info
  std::string click_flow_type_attr_ = "click_flow_type_list";
  std::string click_from_attr_ = "click_from_list";
  // seq config
  int limit_user_num_ = 1000;
  int min_seconds_ago_ = 60;
  int max_seconds_ago_ = 30 * 24 * 3600;

  // output config
  bool use_ad_slot_size_ = true;
  int parameter_sign_bits_ = 52;
  uint64_t SIGN_MASK_ = ((1ul << 52) - 1);
  std::vector<int> mio_slots_ids_ = {2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010, 2011,
                                     2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020, 2021};
  std::vector<int> slots_ids_ = {2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010, 2011,
                                 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020, 2021};
  std::vector<int> interest_weight_ = {10, 10, 1, 2, 1};
  std::string output_common_slot_attr_ = "ad_live_click_order_v2_slot";
  std::string output_common_sign_attr_ = "ad_live_click_order_v2_sign";
  std::string output_dense_timestamp_attr_ = "ad_live_click_order_dense_timestamp";
  std::string output_dense_hour_weight_attr_ = "ad_live_click_order_dense_hourweight";
  std::string output_dense_cat_score_attr_ = "ad_live_click_order_dense_catscore";
  serving_base::Timer timer_;
  SlotSignRemapUtil remap_util_;
  bool slot_as_attr_name_ = false;
  std::string slot_as_common_attr_name_prefix_ = "";

  DISALLOW_COPY_AND_ASSIGN(AdLiveUserGoodsClickOptV1Enricher);
};

}  // namespace platform
}  // namespace ks
