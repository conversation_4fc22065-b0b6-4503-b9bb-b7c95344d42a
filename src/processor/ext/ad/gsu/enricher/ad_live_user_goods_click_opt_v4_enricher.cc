#include "dragon/src/processor/ext/ad/gsu/enricher/ad_live_user_goods_click_opt_v4_enricher.h"

#include <algorithm>
#include <cmath>
#include <ctime>
#include <functional>
#include <iostream>
#include <limits>
#include <queue>
#include <set>
#include <unordered_set>
#include <vector>

#include "base/common/closure.h"
#include "base/common/sleep.h"
#include "base/hash_function/city.h"
#include "base/thread/thread_pool.h"
#include "folly/container/F14Map.h"
#include "kconf/kconf.h"
#include "learning/kuiba/parameter/parameter.h"
#include "teams/reco-arch/colossus/flat_generated/flat_generated_helper.h"
#include "teams/reco-arch/colossus/item/common_item_types.h"

namespace ks {
namespace platform {

bool AdLiveUserGoodsClickOptV4Enricher::GetIntList(const std::string &key, std::vector<int> *vec) {
  if (!vec) {
    LOG(WARNING) << "vec is null";
    return false;
  }
  // 若不配则使用默认值，若配则长度必须等于和默认值长度相等
  const auto json_obj = config()->Get(key);
  if (!json_obj) {
    LOG(WARNING) << "key not find, the default config will be used, key = " << key;
    return true;
  }
  if (!json_obj->IsArray()) {
    LOG(WARNING) << "object is not a list, key = " << key;
    return false;
  }
  std::vector<int> cur_vec;
  for (auto *item : json_obj->array()) {
    int val = 0;
    if (item != nullptr) cur_vec.push_back(item->IntValue(val));
  }
  if (cur_vec.size() != vec->size()) {
    LOG(WARNING) << "slots size must equal default size:" << slots_ids_.size() << " key= " << key;
    return false;
  }
  std::copy(cur_vec.begin(), cur_vec.end(), vec->begin());
  return true;
}

std::vector<int64_t> AdLiveUserGoodsClickOptV4Enricher::GetTopKKeys(
    const std::unordered_map<int64_t, int> &dict, int k) {
  // Step 1: Transfer map to vector
  std::vector<std::pair<int64_t, int>> vec(dict.begin(), dict.end());

  // Step 2: Sort the vector based on the values in descending order
  std::sort(vec.begin(), vec.end(), [](const auto &a, const auto &b) { return a.second > b.second; });

  // Step 3: Select the top k keys
  std::vector<int64_t> result;
  for (int i = 0; i < k && i < vec.size(); ++i) {
    result.emplace_back(vec[i].first);
  }
  while (result.size() < k) {
    result.emplace_back(0L);
  }
  return result;
}

int64_t AdLiveUserGoodsClickOptV4Enricher::log_x_plus_1(int64_t x) {
  // x must be non-negative for the log function to be valid
  if (x < 0) {
    return 0;
  }
  return static_cast<int64_t>(round(std::log2(static_cast<double>(x) + 1.0)));
}

int64_t AdLiveUserGoodsClickOptV4Enricher::min_int64(int64_t x, int64_t y) {
  // x and y which is smaller
  if (x < y) {
    return x;
  }
  return y;
}

// 计算值、最大值和最小值的函数
void AdLiveUserGoodsClickOptV4Enricher::CalculateStatistics(const std::vector<int64_t> &numbers, double *mean,
                                                            int64_t *max, int64_t *min, int64_t *cnt) {
  if (numbers.empty() || mean == nullptr || max == nullptr || min == nullptr || cnt == nullptr) {
    return;
  }

  int64_t sum = 0;
  *max = 0;   // 初始化为下限 0
  *min = 27;  // 初始化为上限 27
  *cnt = numbers.size();
  int64_t valid_cnt = 0;
  for (int64_t num : numbers) {
    if (num > 0) {
      valid_cnt++;
      sum += num;
      if (num > *max) {
        *max = num;
      }
      if (num < *min) {
        *min = num;
      }
    }
  }
  if (valid_cnt == 0) {
    *min = 0;
    *max = 0;
    *mean = 0.;
    return;
  }
  *mean = static_cast<double>(sum) / valid_cnt;
}

bool AdLiveUserGoodsClickOptV4Enricher::InitProcessor() {  // 算子初始化
  colossus_goods_id_attr_ = config()->GetString("colossus_goods_id_attr", "item_id_list");
  if (colossus_goods_id_attr_.empty()) {
    LOG(ERROR) << "miss colossus_goods_id_attr";
    return false;
  }
  colossus_order_goods_id_attr_ = config()->GetString("colossus_order_goods_id_attr", "order_item_id_list");
  if (colossus_order_goods_id_attr_.empty()) {
    LOG(ERROR) << "miss colossus_order_goods_id_attr";
    return false;
  }
  colossus_timestamp_attr_ = config()->GetString("colossus_timestamp_attr", "click_timestamp_list");
  if (colossus_timestamp_attr_.empty()) {
    LOG(ERROR) << "miss colossus_timestamp_attr";
    return false;
  }
  colossus_submit_timestamp_attr_ =
      config()->GetString("colossus_submit_timestamp_attr", "submit_timestamp_list");
  if (colossus_submit_timestamp_attr_.empty()) {
    LOG(ERROR) << "miss colossus_submit_timestamp_attr";
    return false;
  }
  colossus_pay_timestamp_attr_ = config()->GetString("colossus_pay_timestamp_attr", "pay_timestamp_list");
  if (colossus_pay_timestamp_attr_.empty()) {
    LOG(ERROR) << "miss colossus_pay_timestamp_attr";
    return false;
  }
  detail_page_view_time_attr_ =
      config()->GetString("detail_page_view_time_attr", "detail_page_view_time_list");
  if (detail_page_view_time_attr_.empty()) {
    LOG(ERROR) << "miss detail_page_view_time_attr";
    return false;
  }
  colossus_category_attr_ = config()->GetString("colossus_category_attr", "category_a_list");
  if (colossus_category_attr_.empty()) {
    LOG(ERROR) << "miss colossus_category_attr";
    return false;
  }
  colossus_host_attr_ = config()->GetString("colossus_host_attr", "seller_id_list");
  if (colossus_host_attr_.empty()) {
    LOG(ERROR) << "miss colossus_host_attr";
    return false;
  }
  colossus_shop_attr_ = config()->GetString("colossus_shop_attr", "real_seller_id_list");
  if (colossus_shop_attr_.empty()) {
    LOG(ERROR) << "miss colossus_shop_attr";
    return false;
  }
  colossus_real_price_attr_ = config()->GetString("colossus_real_price_attr", "real_price_list");
  if (colossus_real_price_attr_.empty()) {
    LOG(ERROR) << "miss colossus_real_price_attr";
    return false;
  }
  colossus_label_attr_ = config()->GetString("colossus_label_attr", "label_list");
  if (colossus_label_attr_.empty()) {
    LOG(ERROR) << "miss colossus_label_attr";
    return false;
  }
  colossus_uniform_spu_id_attr_ = config()->GetString("colossus_uniform_spu_id_attr", "uniform_spu_id_list");
  if (colossus_uniform_spu_id_attr_.empty()) {
    LOG(ERROR) << "miss colossus_uniform_spu_id_attr";
    return false;
  }
  click_flow_type_attr_ = config()->GetString("click_flow_type_attr", "click_flow_type_list");
  if (click_flow_type_attr_.empty()) {
    LOG(ERROR) << "miss click_flow_type_attr";
    return false;
  }
  click_from_attr_ = config()->GetString("click_from_attr", "click_from_list");
  if (click_from_attr_.empty()) {
    LOG(ERROR) << "miss click_from_attr";
    return false;
  }
  output_dense_timestamp_attr_ =
      config()->GetString("output_dense_timestamp_attr", "ad_live_click_order_dense_timestamp");
  if (output_dense_timestamp_attr_.empty()) {
    LOG(ERROR) << "miss output_dense_timestamp_attr";
    return false;
  }
  output_dense_hour_weight_attr_ =
      config()->GetString("output_dense_hour_weight_attr", "ad_live_click_order_dense_hourweight");
  if (output_dense_hour_weight_attr_.empty()) {
    LOG(ERROR) << "miss output_dense_hour_weight_attr";
    return false;
  }
  output_dense_cat_score_attr_ =
      config()->GetString("output_dense_cat_score_attr", "ad_live_click_order_dense_catscore");
  if (output_dense_cat_score_attr_.empty()) {
    LOG(ERROR) << "miss output_dense_cat_score_attr";
    return false;
  }
  output_seq_length_attr_ = config()->GetString("output_seq_length_attr", "ad_live_click_order_seq_length");
  if (output_seq_length_attr_.empty()) {
    LOG(ERROR) << "miss output_seq_length_attr";
    return false;
  }

  limit_user_num_ = config()->GetInt("limit_user_num", 1000);  // 用户序列长度
  if (limit_user_num_ <= 0) {
    LOG(ERROR) << "miss limit_user_num";
    return false;
  }
  min_seconds_ago_ = config()->GetInt("min_seconds_ago", 60);  // 最近 time
  if (min_seconds_ago_ < 0) {
    LOG(ERROR) << "miss min_seconds_ago";
    return false;
  }
  max_seconds_ago_ = config()->GetInt("max_seconds_ago", 30 * 24 * 3600);  //最远 time
  use_ad_slot_size_ = config()->GetBoolean("use_ad_slot_size", true);
  parameter_sign_bits_ = config()->GetInt("parameter_sign_bits", 52);
  if (parameter_sign_bits_ < 0) {
    LOG(ERROR) << "miss parameter_sign_bits";
    return false;
  }
  SIGN_MASK_ = ((1ul << parameter_sign_bits_) - 1);
  if (!GetIntList("mio_slots_id", &mio_slots_ids_)) return false;
  if (!GetIntList("slots_id", &slots_ids_)) return false;
  if (slots_ids_.size() != mio_slots_ids_.size()) return false;
  if (!GetIntList("interest_weight", &interest_weight_)) return false;
  output_common_slot_attr_ =
      config()->GetString("output_common_slot_attr", "ad_live_user_goods_click_v1_slot");  // 输出 sparse slot
  if (output_common_slot_attr_.empty()) {
    LOG(ERROR) << "miss output_common_slot_attr";
    return false;
  }
  output_common_sign_attr_ =
      config()->GetString("output_common_sign_attr", "ad_live_user_goods_click_v1_sign");  // 输出 sparse sign
  if (output_common_sign_attr_.empty()) {
    LOG(ERROR) << "miss output_common_sign_attr";
    return false;
  }
  if (!remap_util_.Init(config())) {
    LOG(ERROR) << "remap util init failed";
    return false;
  }
  slot_as_attr_name_ = config()->GetBoolean("slot_as_attr_name", false);
  zero_by_first_item_ = config()->GetBoolean("zero_by_first_item", true);
  slot_as_common_attr_name_prefix_ = config()->GetString("slot_as_common_attr_name_prefix", "");

  CL_LOG_EVERY_N(INFO, 10000) << "AdLiveUserGoodsClickOptV4Enricher: "
                              << ", colossus_goods_id_attr_: " << colossus_goods_id_attr_
                              << ", colossus_order_goods_id_attr_: " << colossus_order_goods_id_attr_
                              << ", colossus_timestamp_attr_: " << colossus_timestamp_attr_
                              << ", colossus_submit_timestamp_attr_: " << colossus_submit_timestamp_attr_
                              << ", colossus_pay_timestamp_attr_: " << colossus_pay_timestamp_attr_
                              << ", detail_page_view_time_attr_: " << detail_page_view_time_attr_
                              << ", colossus_category_attr_: " << colossus_category_attr_
                              << ", colossus_host_attr_: " << colossus_host_attr_
                              << ", colossus_shop_attr_: " << colossus_shop_attr_
                              << ", colossus_real_price_attr_: " << colossus_real_price_attr_
                              << ", colossus_label_attr_: " << colossus_label_attr_
                              << ", colossus_uniform_spu_id_attr_: " << colossus_uniform_spu_id_attr_
                              << ", click_flow_type_attr_: " << click_flow_type_attr_
                              << ", click_from_attr_: " << click_from_attr_
                              << ", limit_user_num_: " << limit_user_num_
                              << ", min_seconds_ago_: " << min_seconds_ago_
                              << ", max_seconds_ago_: " << max_seconds_ago_
                              << ", use_ad_slot_size_: " << use_ad_slot_size_
                              << ", parameter_sign_bits_: " << parameter_sign_bits_
                              << ", SIGN_MASK_: " << SIGN_MASK_
                              << ", mio_slots_ids_[0]: " << mio_slots_ids_[0]
                              << ", slots_ids_[0]: " << slots_ids_[0]
                              << ", output_common_slot_attr_: " << output_common_slot_attr_
                              << ", output_common_sign_attr_: " << output_common_sign_attr_;
  return true;
}

void AdLiveUserGoodsClickOptV4Enricher::Enrich(MutableRecoContextInterface *context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
  auto goods_ids = context->GetIntListCommonAttr(colossus_goods_id_attr_);
  auto order_goods_ids = context->GetIntListCommonAttr(colossus_order_goods_id_attr_);
  auto timestamps = context->GetIntListCommonAttr(colossus_timestamp_attr_);
  auto submit_timestamps = context->GetIntListCommonAttr(colossus_submit_timestamp_attr_);
  auto pay_timestamps = context->GetIntListCommonAttr(colossus_pay_timestamp_attr_);
  auto detail_page_view_times = context->GetIntListCommonAttr(detail_page_view_time_attr_);
  auto categorys = context->GetIntListCommonAttr(colossus_category_attr_);
  auto hosts = context->GetIntListCommonAttr(colossus_host_attr_);
  auto shops = context->GetIntListCommonAttr(colossus_shop_attr_);
  auto real_prices = context->GetIntListCommonAttr(colossus_real_price_attr_);
  auto labels = context->GetIntListCommonAttr(colossus_label_attr_);
  auto uniform_spu_ids = context->GetIntListCommonAttr(colossus_uniform_spu_id_attr_);
  auto flow_types = context->GetIntListCommonAttr(click_flow_type_attr_);
  auto from_types = context->GetIntListCommonAttr(click_from_attr_);

  if (!goods_ids || !timestamps || !detail_page_view_times || !categorys || !hosts || !shops ||
      !real_prices || !labels || !uniform_spu_ids || !flow_types || !from_types || goods_ids->empty()) {
    FB_LOG_EVERY_MS(WARNING, 1000) << "colossus no data."
                                   << "colossus length invalid: " << goods_ids->size()
                                   << ", timestamps->size()=" << timestamps->size()
                                   << ", detail_page_view_times->size()" << detail_page_view_times->size()
                                   << ", categorys->size()" << categorys->size() << ", hosts->size()"
                                   << hosts->size() << ", shops->size()" << shops->size()
                                   << ", real_prices->size()" << real_prices->size() << ", labels->size()"
                                   << labels->size() << ", uniform_spu_ids->size()" << uniform_spu_ids->size()
                                   << ", flow_types->size()" << flow_types->size() << ", from_types->size()"
                                   << from_types->size();
    return;
  }

  int colossus_length = goods_ids->size();
  if (timestamps->size() != colossus_length || detail_page_view_times->size() != colossus_length ||
      categorys->size() != colossus_length || hosts->size() != colossus_length ||
      shops->size() != colossus_length || real_prices->size() != colossus_length ||
      labels->size() != colossus_length || uniform_spu_ids->size() != colossus_length ||
      flow_types->size() != colossus_length || from_types->size() != colossus_length) {
    FB_LOG_EVERY_MS(WARNING, 1000) << "colossus click length invalid: " << colossus_length
                                   << ", timestamps->size()=" << timestamps->size()
                                   << ", detail_page_view_times->size()" << detail_page_view_times->size()
                                   << ", categorys->size()" << categorys->size() << ", hosts->size()"
                                   << hosts->size() << ", shops->size()" << shops->size()
                                   << ", real_prices->size()" << real_prices->size() << ", labels->size()"
                                   << labels->size() << ", uniform_spu_ids->size()" << uniform_spu_ids->size()
                                   << ", flow_types->size()" << flow_types->size() << ", from_types->size()"
                                   << from_types->size();
    return;
  }
  int order_length = 0;
  if (order_goods_ids.has_value() && submit_timestamps.has_value() && pay_timestamps.has_value()) {
    order_length = order_goods_ids->size();
    if (submit_timestamps->size() != order_length || pay_timestamps->size() != order_length) {
      FB_LOG_EVERY_MS(WARNING, 1000) << "colossus order length invalid: " << order_length
                                     << ", submit_timestamps->size()=" << submit_timestamps->size()
                                     << ", pay_timestamps->size()" << pay_timestamps->size();
      return;
    }
  }
  // 处理输入信息
  std::vector<double> output_dense_timestamp;
  std::vector<double> output_dense_hour_weight;
  std::vector<double> output_dense_cat_scores;
  // 分 24 小时类目得分
  std::unordered_map<int, std::unordered_map<int64_t, int>> cat1scores;
  std::unordered_map<int, std::unordered_map<int64_t, int>> cat2scores;
  std::unordered_map<int, std::unordered_map<int64_t, int>> cat3scores;
  std::vector<int64_t> output_slot_common;
  std::vector<int64_t> output_sign_common;
  output_slot_common.reserve(limit_user_num_ * slots_ids_.size());
  output_sign_common.reserve(limit_user_num_ * slots_ids_.size());

#define ADD_SIGN(i, expr)                                                                             \
  do {                                                                                                \
    if (i < mio_slots_ids_.size()) {                                                                  \
      uint64_t slot_sign = ((uint64_t)(slots_ids_[i]) << parameter_sign_bits_) | (SIGN_MASK_ & expr); \
      uint16_t slot_new = 0;                                                                          \
      uint64_t sign_new = 0;                                                                          \
      if (remap_util_.RemapSlotSign(mio_slots_ids_[i], slot_sign, &slot_new, &sign_new,               \
                                    use_ad_slot_size_)) {                                             \
        output_slot->emplace_back(slot_new);                                                          \
        output_sign->emplace_back(sign_new);                                                          \
      }                                                                                               \
    }                                                                                                 \
  } while (0)

  thread_local folly::F14FastMap<int, folly::F14FastMap<std::string, std::vector<int64>>> target_result;
  target_result.clear();

  int out_lenth = std::min(colossus_length, limit_user_num_);
  int64_t first_ts = timestamps->at(colossus_length - out_lenth);  // 第一个时间戳
  ////////////////////////////////// 解析请求时间信息 //////////////////////////////////
  int64_t request_time = context->GetRequestTime() / 1000;  // 以请求时间为 0 点
  time_t raw_time_request = static_cast<time_t>(request_time);
  struct tm request_time_info;

  localtime_r(&raw_time_request, &request_time_info);
  // 获取小时
  int rq_hour_achor = request_time_info.tm_hour;

  ////////////////////////////////// 归因订单序列 //////////////////////////////////
  std::unordered_map<int64_t, std::queue<int64_t>> id2idx;
  int j = order_length - 1;  // order id index
  while ((j >= 0) && (submit_timestamps->at(j) > first_ts)) {
    id2idx[order_goods_ids->at(j)].push(j);
    j--;
  }

  ////////////////////////////////// 初始化参数 //////////////////////////////////
  // 倒序取, 最新的数据优先
  auto output_sign = &output_sign_common;
  auto output_slot = &output_slot_common;
  // 添加请求时间戳特征
  if (zero_by_first_item_) {
    output_dense_timestamp.emplace_back(static_cast<double>((request_time - first_ts) / 1800));
    ADD_SIGN(1, (request_time - first_ts) / 1800);  // 第一次时间戳 0 点
  } else {
    output_dense_timestamp.emplace_back(0.0);
    ADD_SIGN(1, 0);  // 请求时间戳为 0 点
  }

  int64_t sub_seq_label_parsed = 0;
  int64_t sub_seq_length = 0;
  int64_t sub_seq_timestamp_bias = 0;         // 初始化偏移时间戳
  int64_t sub_seq_click_time_gap = 0;         // 初始化时间间隔
  int64_t sub_seq_detail_page_view_time = 0;  // 初始化子序列浏览总时长
  int64_t sub_seq_submit_time_gap = 0;        // 初始化子序列提交订单总时长
  int64_t sub_seq_pay_time_gap = 0;           // 初始化子序列支付订单总时长
  int64_t sub_seq_pay_cnt = 0;                // 初始化子序列支付订单总数
  int seq_cnt = 0;
  int seq_final_cnt = 0;

  int64_t last_seq_item = -1;
  int64_t last_seq_timestamp = -1;
  // 遍历 colossus 字段
  for (int i = colossus_length - 1; i >= 0; --i) {
    if (i > 0) {
      last_seq_item = goods_ids->at(i - 1);
      last_seq_timestamp = timestamps->at(i - 1);
    }
    ////////////////////////////////// 获取当前商品信息 //////////////////////////////////
    int64_t goods_id = goods_ids->at(i);
    int64_t timestamp = timestamps->at(i);
    int64_t timestamp_bias = 0;
    if (zero_by_first_item_) {
      timestamp_bias = (timestamp - first_ts) / 1800;  // 半小时间隔
    } else {
      timestamp_bias = (request_time - timestamp) / 1800;  // 半小时间隔
    }
    int64_t detail_page_view_time = min_int64(detail_page_view_times->at(i), 3600);
    int64_t submit_timestamp = 0;
    int64_t pay_timestamp = 0;
    int64_t category = categorys->at(i);
    int64_t x7cate1 = (category >> 48) & 0xffff;
    int64_t x7cate2 = (category >> 32) & 0xffff;
    int64_t x7cate3 = (category >> 16) & 0xffff;
    int64_t host = hosts->at(i);
    int64_t shop = shops->at(i);
    int64_t price = real_prices->at(i);
    int64_t uniform_spu_id = uniform_spu_ids->at(i);
    int64_t label = labels->at(i);
    int64_t flow_type = flow_types->at(i);
    int64_t from_type = from_types->at(i);
    j = -1;
    // 近一分钟的交互不用，防穿越
    if (timestamp > request_time - min_seconds_ago_) {
      continue;
    }
    int64_t submit_time_gap = 0;
    int64_t pay_time_gap = 0;
    int64_t click_time_gap = 0;
    int64_t label_parsed = 0;
    int hour = 25;
    int weekday = 32;
    int month = 8;
    int mday = 13;
    // 计算相邻时间间隔
    if (i > 0) {
      click_time_gap = (timestamps->at(i) - timestamps->at(i - 1)) / 1800;
    }
    // 解析时间戳
    time_t raw_time = static_cast<time_t>(timestamp);
    struct tm time_info;
    if (localtime_r(&raw_time, &time_info)) {
      hour = time_info.tm_hour;     // 获取小时
      weekday = time_info.tm_wday;  // 获取星期几 (0 表示星期日, 6 表示星期六)
      month = time_info.tm_mon;     // 获取月数 (0 表示一月, 11 表示十二月)
      mday = time_info.tm_mday;     // 获取日期数
      FB_LOG_EVERY_MS(INFO, 10000) << "timestamp" << timestamp << ";hour" << hour << ";weekday" << weekday
                                   << ";month" << month << ";mday" << mday;
    }
    // 归因订单序列
    while (!id2idx[goods_id].empty() &&
           (submit_timestamps->at(id2idx[goods_id].front()) > timestamps->at(i))) {
      j = id2idx[goods_id].front();
      id2idx[goods_id].pop();
    }
    if (j >= 0) {
      submit_timestamp = submit_timestamps->at(j);
      pay_timestamp = pay_timestamps->at(j);
      submit_time_gap = min_int64(submit_timestamp - timestamp, 3600 * 2);
      if (pay_timestamp > submit_timestamp) {
        pay_time_gap = min_int64(pay_timestamp - submit_timestamp, 3600);
      }
    }
    // 解析 label 字段 立即购买,加购,店铺,客服,评论 [10 10 1 5 2]
    ++label_parsed;
    for (int idx_w = 0; idx_w < interest_weight_.size(); ++idx_w) {
      label_parsed = label_parsed + (label & (1 << idx_w)) * interest_weight_.at(idx_w);
    }

    ////////////////////////////////// 子序列信息聚合处理 //////////////////////////////////
    ++seq_cnt;                                               // 统计实际序列长度
    ++sub_seq_length;                                        // 统计聚合子序列长度
    sub_seq_timestamp_bias += timestamp_bias;                // 统计偏移时间戳
    sub_seq_click_time_gap += click_time_gap;                // 统计时间间隔
    sub_seq_detail_page_view_time += detail_page_view_time;  // 统计子序列浏览总时长
    sub_seq_submit_time_gap += submit_time_gap;              // 统计子序列提交订单总时长
    sub_seq_pay_time_gap += pay_time_gap;                    // 统计子序列支付订单总时长
    sub_seq_pay_cnt += (pay_timestamp > 0);                  // 统计子序列支付订单总数
    sub_seq_label_parsed += label_parsed;                    // 统计聚合需要聚合的字段 label

    ////////////////////////////////// 保存当前商品信息 //////////////////////////////////
    if (goods_id != last_seq_item || timestamp - last_seq_timestamp > 3600 || i == 0) {
      // 输出 curr info
      ADD_SIGN(0, goods_id);
      ADD_SIGN(1, sub_seq_timestamp_bias / sub_seq_length);
      ADD_SIGN(2, sub_seq_click_time_gap / sub_seq_length);
      ADD_SIGN(3, sub_seq_detail_page_view_time);  // 子序列浏览总时长
      int64_t submit_time_gap_avg = sub_seq_pay_cnt > 0 ? sub_seq_submit_time_gap / sub_seq_pay_cnt : 0;
      ADD_SIGN(4, submit_time_gap_avg);
      int64_t pay_time_gap_avg = sub_seq_pay_cnt > 0 ? sub_seq_pay_time_gap / sub_seq_pay_cnt : 0;
      ADD_SIGN(5, pay_time_gap_avg);
      ADD_SIGN(6, x7cate1);
      ADD_SIGN(7, x7cate2);
      ADD_SIGN(8, x7cate3);
      ADD_SIGN(9, host);
      ADD_SIGN(10, shop);
      ADD_SIGN(11, price);
      ADD_SIGN(12, uniform_spu_id);
      ADD_SIGN(13, label_parsed);
      ADD_SIGN(14, hour);
      ADD_SIGN(15, mday);
      ADD_SIGN(16, weekday);
      ADD_SIGN(17, month);
      ADD_SIGN(18, flow_type);
      ADD_SIGN(19, from_type);
      output_dense_timestamp.emplace_back(static_cast<double>(sub_seq_timestamp_bias) /
                                          static_cast<double>(sub_seq_length));
      FB_LOG_EVERY_MS(INFO, 10000) << "request_time: " << request_time << ", seq goods_id: " << goods_id
                                   << ", seq timestamp: " << timestamp << ", seq category: " << category
                                   << ", seq host: " << host << ", seq shop: " << shop
                                   << ", seq price: " << price << ", seq uniform_spu_id: " << uniform_spu_id
                                   << ", seq label: " << label << ", seq flow_type: " << flow_type
                                   << ", seq from_type: " << from_type
                                   << ", seq timestamp_bias: " << sub_seq_timestamp_bias / sub_seq_length;

      sub_seq_length = 0;                 // 初始化聚合子序列长度
      sub_seq_timestamp_bias = 0;         // 初始化偏移时间戳
      sub_seq_click_time_gap = 0;         // 初始化时间间隔
      sub_seq_detail_page_view_time = 0;  // 初始化子序列浏览总时长
      sub_seq_submit_time_gap = 0;        // 初始化子序列提交订单总时长
      sub_seq_pay_time_gap = 0;           // 初始化子序列支付订单总时长
      sub_seq_pay_cnt = 0;                // 初始化子序列支付订单总数
      sub_seq_label_parsed = 0;           // 初始化需要聚合的字段 label
      seq_final_cnt++;                    // 统计输出序列长度
    }

    if (seq_final_cnt >= limit_user_num_) {
      break;
    }
  }

  for (int i = colossus_length - 1; i >= 0; --i) {
    int64_t timestamp = timestamps->at(i);
    int64_t category = categorys->at(i);
    int64_t x7cate1 = (category >> 48) & 0xffff;
    int64_t x7cate2 = (category >> 32) & 0xffff;
    int64_t x7cate3 = (category >> 16) & 0xffff;
    int64_t label = labels->at(i);
    // 新增分级时间特征
    if (timestamp < request_time - max_seconds_ago_) {
      break;
    }
    // 使用 localtime 将时间戳转换为 tm 结构体
    time_t raw_time = static_cast<time_t>(timestamp);
    struct tm time_info;
    if (localtime_r(&raw_time, &time_info)) {
      // 获取小时
      int hour = time_info.tm_hour;
      // label 0: 立即购买, 1: 加购物车, 2: 点击店铺, 3: 点击客服, 4: 点击评论
      // https://docs.corp.kuaishou.com/d/home/<USER>
      // interest_weight_ 控制不同标签权重
      cat1scores[hour][x7cate1]++;
      cat2scores[hour][x7cate2]++;
      cat3scores[hour][x7cate3]++;
      for (int idx_w = 0; idx_w < interest_weight_.size(); ++idx_w) {
        cat1scores[hour][x7cate1] =
            cat1scores[hour][x7cate1] + (label & (1 << idx_w)) * interest_weight_.at(idx_w);
        cat2scores[hour][x7cate2] =
            cat2scores[hour][x7cate2] + (label & (1 << idx_w)) * interest_weight_.at(idx_w);
        cat3scores[hour][x7cate3] =
            cat3scores[hour][x7cate3] + (label & (1 << idx_w)) * interest_weight_.at(idx_w);
      }
    }
  }
  // 加周期类目特征
  int K = 3;
  // cat1
  for (j = 0; j < 24; ++j) {
    int hour_diff = std::min(abs(rq_hour_achor - j), 24 - abs(rq_hour_achor - j));
    if (cat1scores[j].empty()) {
      for (int i = 0; i < K; i++) {
        ADD_SIGN(20, 0);
        output_dense_cat_scores.emplace_back(0.0);
        output_dense_hour_weight.emplace_back(0.0);
      }
      continue;
    }
    std::vector<int64_t> cat1score_T_vec = GetTopKKeys(cat1scores[j], K);
    for (int i = 0; i < K; i++) {
      if (i < cat1score_T_vec.size()) {
        ADD_SIGN(20, cat1score_T_vec[i]);
        output_dense_cat_scores.emplace_back(static_cast<double>(cat1scores[j][cat1score_T_vec[i]]));
        output_dense_hour_weight.emplace_back(exp(-pow(static_cast<double>(hour_diff), 2) / 2.0));
      } else {
        ADD_SIGN(20, 0);
        output_dense_cat_scores.emplace_back(0.0);
        output_dense_hour_weight.emplace_back(0.0);
      }
    }
  }
  // cat2
  for (j = 0; j < 24; ++j) {
    int hour_diff = std::min(abs(rq_hour_achor - j), 24 - abs(rq_hour_achor - j));
    if (cat2scores[j].empty()) {
      for (int i = 0; i < K; i++) {
        ADD_SIGN(20, 0);
        output_dense_cat_scores.emplace_back(0.0);
        output_dense_hour_weight.emplace_back(0.0);
      }
      continue;
    }
    std::vector<int64_t> cat2score_T_vec = GetTopKKeys(cat2scores[j], K);
    for (int i = 0; i < K; i++) {
      if (i < cat2score_T_vec.size()) {
        ADD_SIGN(20, cat2score_T_vec[i]);
        output_dense_cat_scores.emplace_back(static_cast<double>(cat2scores[j][cat2score_T_vec[i]]));
        output_dense_hour_weight.emplace_back(exp(-pow(static_cast<double>(hour_diff), 2) / 2.0));
      } else {
        ADD_SIGN(20, 0);
        output_dense_cat_scores.emplace_back(0.0);
        output_dense_hour_weight.emplace_back(0.0);
      }
    }
  }
  // cat3
  for (j = 0; j < 24; ++j) {
    int hour_diff = std::min(abs(rq_hour_achor - j), 24 - abs(rq_hour_achor - j));
    if (cat3scores[j].empty()) {
      for (int i = 0; i < K; i++) {
        ADD_SIGN(20, 0);
        output_dense_cat_scores.emplace_back(0.0);
        output_dense_hour_weight.emplace_back(0.0);
      }
      continue;
    }
    std::vector<int64_t> cat3score_T_vec = GetTopKKeys(cat3scores[j], K);
    for (int i = 0; i < K; i++) {
      if (i < cat3score_T_vec.size()) {
        ADD_SIGN(20, cat3score_T_vec[i]);
        output_dense_cat_scores.emplace_back(static_cast<double>(cat3scores[j][cat3score_T_vec[i]]));
        output_dense_hour_weight.emplace_back(exp(-pow(static_cast<double>(hour_diff), 2) / 2.0));
      } else {
        ADD_SIGN(20, 0);
        output_dense_cat_scores.emplace_back(0.0);
        output_dense_hour_weight.emplace_back(0.0);
      }
    }
  }

#undef ADD_SIGN
  while (seq_final_cnt < limit_user_num_) {
    seq_final_cnt++;
    output_dense_timestamp.emplace_back(0.0);
  }

  // 生效 common slots
  int64_t last_slot = output_slot_common.size() > 0 ? output_slot_common.back() : -1;
  int64_t last_sign = output_sign_common.size() > 0 ? output_sign_common.back() : -1;
  FB_LOG_EVERY_MS(INFO, 10000) << "request_time: " << request_time << ", seq_cnt: " << seq_cnt
                               << ", output_slot_common.size: " << output_slot_common.size()
                               << ", output_sign_common.size: " << output_sign_common.size()
                               << ", output_slot[-1]: " << last_slot << ", output_sign[-1]: " << last_sign;
  if (slot_as_attr_name_) {
    for (size_t i = 0; i < output_slot_common.size(); ++i) {
      context->AppendIntListCommonAttr(
          slot_as_common_attr_name_prefix_ + std::to_string(output_slot_common[i]), output_sign_common[i]);
    }
  }
  context->SetIntListCommonAttr(output_common_slot_attr_, std::move(output_slot_common));
  context->SetIntListCommonAttr(output_common_sign_attr_, std::move(output_sign_common));
  // 生效 dense 特征
  context->SetDoubleListCommonAttr(output_dense_timestamp_attr_, std::move(output_dense_timestamp));
  context->SetDoubleListCommonAttr(output_dense_hour_weight_attr_, std::move(output_dense_hour_weight));
  context->SetDoubleListCommonAttr(output_dense_cat_score_attr_, std::move(output_dense_cat_scores));
  context->SetIntCommonAttr(output_seq_length_attr_, seq_cnt);
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AdLiveUserGoodsClickOptV4Enricher, AdLiveUserGoodsClickOptV4Enricher);

}  // namespace platform
}  // namespace ks
