#include "dragon/src/processor/ext/ad/gsu/enricher/ad_live_segment_gsu_u2u_enricher_v3.h"

#include <iostream>
#include <algorithm>
#include <unordered_set>

#include "kconf/kconf.h"
#include "base/thread/thread_pool.h"
#include "base/common/closure.h"
#include "base/common/sleep.h"
#include "base/hash_function/city.h"
#include "learning/kuiba/parameter/parameter.h"
#include "teams/reco-arch/colossus/flat_generated/flat_generated_helper.h"
#include "folly/container/F14Map.h"

namespace ks {
namespace platform {

bool AdLiveSegmentGsuU2uEnricherV3::GetIntList(const std::string &key, std::vector<int> *vec) {
  // 若不配则使用默认值，若配则长度必须等于和默认值长度相等
  const auto json_obj = config()->Get(key);
  if (!json_obj) {
    LOG(WARNING) << "key not find, the default slots config will be used, key = " << key;
    return true;
  }
  if (!json_obj->IsArray()) {
    LOG(ERROR) << "object is not a list, key = " << key;
    return false;
  }
  std::vector<int> cur_vec;
  for (auto *item : json_obj->array()) {
    int val = 0;
    cur_vec.push_back(item->IntValue(val));
  }
  vec->resize(cur_vec.size());
  std::copy(cur_vec.begin(), cur_vec.end(), vec->begin());
  return true;
}

bool AdLiveSegmentGsuU2uEnricherV3::InitProcessor() {
  output_sign_attr_ = config()->GetString("output_sign_attr", "");
  if (output_sign_attr_.empty()) {
    LOG(ERROR) << "miss output_sign_attr";
    return false;
  }
  output_slot_attr_ = config()->GetString("output_slot_attr", "");
  if (output_slot_attr_.empty()) {
    LOG(ERROR) << "miss output_slot_attr";
    return false;
  }
  colossus_resp_attr_ = config()->GetString("colossus_resp_attr", "");
  if (colossus_resp_attr_.empty()) {
    LOG(ERROR) << "miss colossus_resp_attr";
    return false;
  }
  limit_num_ = config()->GetInt("limit_num", 100);
  item_limit_num_ = config()->GetInt("item_limit_num", 1);
  list_top_k_ = config()->GetInt("list_top_k", 1);
  slot_as_attr_name_ = config()->GetBoolean("slot_as_attr_name", true);
  target_aids_attr_ = config()->GetString("target_aids_attr", "aid");
  use_ad_slot_size_ = config()->GetBoolean("use_ad_slot_size", true);
  parameter_sign_bits_ = config()->GetInt("parameter_sign_bits", 52);
  sign_mask_ = ((1ul << parameter_sign_bits_) - 1);
  if (target_aids_attr_.empty()) {
    LOG(INFO) << "miss target_aids_attr";
    return false;
  }
  slot_as_attr_name_prefix_ = config()->GetString("slot_as_attr_name_prefix", "lsp_u2u_gsu_v2_");
  explain_item_id_ =  config()->GetString("explain_item_id_attr", "explain_item_id");
  explain_item_x7_cat1_ =
    config()->GetString("explain_item_x7_cat1_attr", "explain_item_x7_cat1");
  author_hist_x7_cat1_list_ =
      config()->GetString("author_hist_x7_cat1_list_attr", "author_hist_x7_cat1_list");
  yellow_car_x7_cat1_list_ =
      config()->GetString("yellow_car_x7_cat1_list_attr", "yellow_car_x7_cat1_list");
  enable_new_product_category_ = config()->GetString(
      "enable_new_product_category", "enable_new_product_category");
  fix_new_product_category_option_ = config()->GetBoolean("fix_new_product_category_option", false);
  if (!GetIntList("slots_id", &slots_ids_)) return false;
  if (!GetIntList("mio_slots_id", &mio_slots_ids_)) return false;

  // remap_util 初始化
  fused_slot_sign_remap_ = config()->GetBoolean("fused_slot_sign_remap", false);
  if (fused_slot_sign_remap_ && !remap_util_.Init(config())) {
    LOG(ERROR) << "remap util init failed";
    return false;
  }
  LOG(INFO) << "fused_slot_sign_remap: " << fused_slot_sign_remap_;
  only_output_basic_slots_ = config()->GetBoolean("only_output_basic_slots", false);
  LOG(INFO) << "only_output_basic_slots: " << only_output_basic_slots_;
  return true;
}

const uint16 AdLiveSegmentGsuU2uEnricherV3::kInvalidAuthorId_ = 0;  // 0xFFFF

void AdLiveSegmentGsuU2uEnricherV3::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                              RecoResultConstIter end) {
  timer_.Start();
  int64_t enable_new_cate = 1;
  if (!fix_new_product_category_option_) {
    enable_new_cate = *context->GetIntCommonAttr(enable_new_product_category_);
    falcon::Stat("enable_new_product_category_value", enable_new_cate);
  }
  // get history live info from colossus_resp
  auto items = context->GetPtrCommonAttr<AllLiveSegmentItems>(colossus_resp_attr_);
  if (!items || items->items_size() == 0) {
    CL_LOG(WARNING) << "colossus items empty";
    return;
  }
  CL_LOG(INFO) << "history items size: " << items->items_size() << ", colossu response";
  timer_.AppendCostMs("get_colossus_resp");

  // unique target cluster id -> photo idx, key may be kInvalidClusterId
  std::unordered_map<int, std::vector<int>> target_result_idx;
  // unique target cluster id -> output slot / output sign, key may be kInvalidClusterId
  std::unordered_map<int, std::unordered_map<std::string, std::vector<int64>>> target_result;
  target_result.clear();
  // 切片侧 cat 聚合
  folly::F14FastMap<int64_t, std::vector<int>> cat1_map;

  auto item_size = items->items_size();
  int64 filter_time = context->GetRequestTime() / 1000 - 60;

  // 1. 切片侧 所属 cat 及对应切片的聚合
  for (int i = item_size-1; i >= 0; i--) {
    auto *item = &(items->items(i));
    if (item->timestamp() > filter_time) {
        continue;
    }

    // item_category, item_x7category, item_x7_category_new1, item_x7_category_new2
    int64_t x7cate1 = item->item_x7_category_new1_size() > 0 ?
      (item->item_x7_category_new1(0) & 0xFFFF) : -1L;  // x7cat_lev3
    if (cat1_map[x7cate1].size() < limit_num_) {
      cat1_map[x7cate1].emplace_back(i);
    }
  }

  // 2. 候选直播依次获取最终召回的切片结果
  int item_idx = 0;
  auto explain_item_id_accessor = context->GetItemAttrAccessor(explain_item_id_);
  auto explain_item_x7_cat1_accessor = context->GetItemAttrAccessor(explain_item_x7_cat1_);
  auto author_hist_x7_cat1_list_accessor = context->GetItemAttrAccessor(author_hist_x7_cat1_list_);
  auto yellow_car_x7_cat1_list_accessor = context->GetItemAttrAccessor(yellow_car_x7_cat1_list_);
  for (auto it = begin; it != end; ++it) {
    // 保存 index
    std::unordered_set<int> index_set;
    std::unordered_set<int64_t> cat_used;

    // cat1
    if (index_set.size() < limit_num_ && context->HasItemAttr(*it, explain_item_x7_cat1_accessor)) {
      auto explain_item_id = *context->GetIntItemAttr(*it, explain_item_id_accessor);
      auto explain_item_x7_cat1 = *context->GetIntItemAttr(*it, explain_item_x7_cat1_accessor);
      cat_used.insert(explain_item_x7_cat1);
      const auto& index_vec = cat1_map[explain_item_x7_cat1];
      index_set.insert(index_vec.begin(), index_vec.end());
    }

    if (index_set.size() < limit_num_ && context->HasItemAttr(*it, author_hist_x7_cat1_list_accessor)) {
      auto author_hist_x7_cat1_list = *context->GetIntListItemAttr(*it, author_hist_x7_cat1_list_accessor);
      int dist = std::min((int)author_hist_x7_cat1_list.size(), list_top_k_);
      std::vector<int64_t> tmp_list =
        {author_hist_x7_cat1_list.begin(), author_hist_x7_cat1_list.begin()+dist};
      for (auto cat1 : tmp_list) {
        if (cat_used.count(cat1)) {
          continue;
        }
        cat_used.insert(cat1);
        const auto& index_vec = cat1_map[cat1];
        index_set.insert(index_vec.begin(), index_vec.end());
      }
    }

    if (index_set.size() < limit_num_ && context->HasItemAttr(*it, yellow_car_x7_cat1_list_accessor)) {
      auto yellow_car_x7_cat1_list = *context->GetIntListItemAttr(*it, yellow_car_x7_cat1_list_accessor);
      int dist = std::min((int)yellow_car_x7_cat1_list.size(), list_top_k_);
      std::vector<int64_t> tmp_list =
        {yellow_car_x7_cat1_list.begin(), yellow_car_x7_cat1_list.begin()+dist};
      for (auto cat1 : tmp_list) {
        if (cat_used.count(cat1)) {
          continue;
        }
        cat_used.insert(cat1);
        const auto& index_vec = cat1_map[cat1];
        index_set.insert(index_vec.begin(), index_vec.end());
      }
    }

    std::vector<int> tmp = {index_set.begin(), index_set.end()};  // 未保序
    target_result_idx[item_idx] = {tmp.begin(), tmp.begin()+std::min(limit_num_, (int)tmp.size())};
    item_idx += 1;
  }

#define ADD_SIGN(i, expr)                                                                            \
  do {                                                                                               \
    if (i < mio_slots_ids_.size()) {                                                                 \
      uint64_t slot_sign = ((uint64_t)(slots_ids_[i]) << parameter_sign_bits_) | (sign_mask_ & expr); \
      uint16_t slot_new = 0;                                                                         \
      uint64_t sign_new = 0;                                                                         \
      if (remap_util_.RemapSlotSign(mio_slots_ids_[i], slot_sign, &slot_new, &sign_new,              \
                                    use_ad_slot_size_)) {                                            \
        output_slot->emplace_back(slot_new);                                                         \
        output_sign->emplace_back(sign_new);                                                         \
      }                                                                                              \
    }                                                                                                \
  } while (0)

  int64 result_photo_num = 0;
  for (const auto& it : target_result_idx) {
    int cluster_id = it.first;
    auto output_sign = &target_result[cluster_id][output_sign_attr_];
    auto output_slot = &target_result[cluster_id][output_slot_attr_];
    for (int offset : it.second) {
      auto *item = &(items->items(offset));
      ADD_SIGN(0, ((1L << 48) - 1) & item->live_id());
      ADD_SIGN(1, item->author_id());
      ADD_SIGN(2, (filter_time - item->timestamp()) / 3600);  // 小时
      ADD_SIGN(3, item->duration_play_time() & 0xFFFF);
      ADD_SIGN(4, item->duration_play_time() >> 16);
      ADD_SIGN(5, (item->label() >> 16) & 0xF);
      ADD_SIGN(6, item->label());
      ADD_SIGN(7, item->live_play_time_gmv() & 0xFFFF);
      ADD_SIGN(8, item->live_play_time_gmv() >> 16);
      ADD_SIGN(9, item->channel());

      for (int t = 0; t < item_limit_num_; t++) {
        if (t >= item->item_id_size()) {
          ADD_SIGN(10, 0);
        } else {
          ADD_SIGN(10, item->item_id(t));
        }
        if (enable_new_cate) {
          if (t >= item->item_new_spu_size()) {
            ADD_SIGN(11, 0);
          } else {
            ADD_SIGN(11, item->item_new_spu(t));
          }
        } else {
          if (t >= item->item_spu_size()) {
            ADD_SIGN(11, 0);
          } else {
            ADD_SIGN(11, item->item_spu(t));
          }
        }
        if (t >= item->item_category_size()) {
          ADD_SIGN(12, 0);
        } else {
          ADD_SIGN(12, item->item_category(t) / 10000);  // cid2
        }
        if (t >= item->item_category_size()) {
          ADD_SIGN(13, 0);
        } else {
          ADD_SIGN(13, item->item_category(t));  // cid3
        }
        if (t >= item->item_label_size()) {
          ADD_SIGN(14, 0);
          ADD_SIGN(15, 0);
          ADD_SIGN(16, 0);
        } else {
          ADD_SIGN(14, (item->item_label(t) & (1 << 21)) >> 21);  // click
          ADD_SIGN(15, (item->item_label(t) & (1 << 22)) >> 22);  // ecomm_buy
          ADD_SIGN(16, (item->item_label(t) & (1 << 23)) >> 23);  // buy
        }
        if (t >= item->item_volume_size()) {
          ADD_SIGN(17, 0);
          ADD_SIGN(18, 0);
        } else {
          ADD_SIGN(17, item->item_volume(t) & 0x3FFFFF);  // volume_in_live
          ADD_SIGN(18, (item->item_volume(t) >> 22) & 0x1FFFFF);  // days_of_volume
        }
      }
      ADD_SIGN(19, item->living_attr_2() & 0x1FFFFFF);  // gmv
      ADD_SIGN(20, (item->living_attr_2() >> 25) & 0x7FFFF);  // total_order_nums
      ADD_SIGN(21, (item->living_attr_3() >> 41) & 0x7FFFFF);  // item_click_num
      ADD_SIGN(22, item->living_attr_4() & 0xFFFFFFF);  // live_show_pv
    }
    result_photo_num += target_result_idx[cluster_id].size();
  }
#undef ADD_SIGN
  timer_.AppendCostMs("extract_signs");
  for (auto result_iter = begin; result_iter != end; ++result_iter) {
    for (const auto& it : target_result[result_iter-begin]) {
      context->SetIntListItemAttr(result_iter->item_key, it.first, std::vector<int64>(it.second));
    }
    if (slot_as_attr_name_) {
      const auto& output_slot = target_result[result_iter-begin][output_slot_attr_];
      const auto& output_sign = target_result[result_iter-begin][output_sign_attr_];
      for (size_t i = 0; i < output_slot.size(); ++i) {
        context->AppendIntListItemAttr(result_iter->item_key, slot_as_attr_name_prefix_ +
          std::to_string(output_slot[i]), output_sign[i]);
      }
    }
  }
  timer_.AppendCostMs("fill_attr");

  CL_LOG(INFO) << "cluster_size: " << target_result_idx.size()
               << ", colossus_item_size: "  << item_size
               << ", limit_num_: " << limit_num_ << ", filter_time: " << filter_time
               << ", result_photo_num: " << result_photo_num << ", timer: " << timer_.display()
               << " total:" << (timer_.Stop() / 1000.f) << "ms";
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AdLiveSegmentGsuU2uEnricherV3, AdLiveSegmentGsuU2uEnricherV3);

}  // namespace platform
}  // namespace ks
