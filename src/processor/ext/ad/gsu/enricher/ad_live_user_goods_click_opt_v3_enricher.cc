#include "dragon/src/processor/ext/ad/gsu/enricher/ad_live_user_goods_click_opt_v3_enricher.h"

#include <algorithm>
#include <cmath>
#include <ctime>
#include <functional>
#include <iostream>
#include <limits>
#include <queue>
#include <set>
#include <unordered_set>
#include <vector>

#include "base/common/closure.h"
#include "base/common/sleep.h"
#include "base/hash_function/city.h"
#include "base/thread/thread_pool.h"
#include "folly/container/F14Map.h"
#include "kconf/kconf.h"
#include "learning/kuiba/parameter/parameter.h"
#include "teams/reco-arch/colossus/flat_generated/flat_generated_helper.h"
#include "teams/reco-arch/colossus/item/common_item_types.h"

namespace ks {
namespace platform {

bool AdLiveUserGoodsClickOptV3Enricher::GetIntList(const std::string &key, std::vector<int> *vec) {
  if (!vec) {
    LOG(WARNING) << "vec is null";
    return false;
  }
  // 若不配则使用默认值，若配则长度必须等于和默认值长度相等
  const auto json_obj = config()->Get(key);
  if (!json_obj) {
    LOG(WARNING) << "key not find, the default config will be used, key = " << key;
    return true;
  }
  if (!json_obj->IsArray()) {
    LOG(WARNING) << "object is not a list, key = " << key;
    return false;
  }
  std::vector<int> cur_vec;
  for (auto *item : json_obj->array()) {
    int val = 0;
    if (item != nullptr) cur_vec.push_back(item->IntValue(val));
  }
  if (cur_vec.size() != vec->size()) {
    LOG(WARNING) << "slots size must equal default size:" << slots_ids_.size() << " key= " << key;
    return false;
  }
  std::copy(cur_vec.begin(), cur_vec.end(), vec->begin());
  return true;
}

std::vector<int64_t> AdLiveUserGoodsClickOptV3Enricher::GetTopKKeys(
    const std::unordered_map<int64_t, int> &dict, int k) {
  // Step 1: Transfer map to vector
  std::vector<std::pair<int64_t, int>> vec(dict.begin(), dict.end());

  // Step 2: Sort the vector based on the values in descending order
  std::sort(vec.begin(), vec.end(), [](const auto &a, const auto &b) { return a.second > b.second; });

  // Step 3: Select the top k keys
  std::vector<int64_t> result;
  for (int i = 0; i < k && i < vec.size(); ++i) {
    result.emplace_back(vec[i].first);
  }
  while (result.size() < k) {
    result.emplace_back(0L);
  }
  return result;
}

int64_t AdLiveUserGoodsClickOptV3Enricher::log_x_plus_1(int64_t x) {
  // x must be non-negative for the log function to be valid
  if (x < 0) {
    return 0;
  }
  return static_cast<int64_t>(round(std::log2(static_cast<double>(x) + 1.0)));
}

int64_t AdLiveUserGoodsClickOptV3Enricher::min_int64(int64_t x, int64_t y) {
  // x and y which is smaller
  if (x < y) {
    return x;
  }
  return y;
}

// 计算值、最大值和最小值的函数
void AdLiveUserGoodsClickOptV3Enricher::CalculateStatistics(const std::vector<int64_t> &numbers, double *mean,
                                                            int64_t *max, int64_t *min, int64_t *cnt) {
  if (numbers.empty() || mean == nullptr || max == nullptr || min == nullptr || cnt == nullptr) {
    return;
  }

  int64_t sum = 0;
  *max = 0;   // 初始化为下限 0
  *min = 27;  // 初始化为上限 27
  *cnt = numbers.size();
  int64_t valid_cnt = 0;
  for (int64_t num : numbers) {
    if (num > 0) {
      valid_cnt++;
      sum += num;
      if (num > *max) {
        *max = num;
      }
      if (num < *min) {
        *min = num;
      }
    }
  }
  if (valid_cnt == 0) {
    *min = 0;
    *max = 0;
    *mean = 0.;
    return;
  }
  *mean = static_cast<double>(sum) / valid_cnt;
}

bool AdLiveUserGoodsClickOptV3Enricher::InitProcessor() {  // 算子初始化
  colossus_goods_id_attr_ = config()->GetString("colossus_goods_id_attr", "item_id_list");
  if (colossus_goods_id_attr_.empty()) {
    LOG(ERROR) << "miss colossus_goods_id_attr";
    return false;
  }
  colossus_timestamp_attr_ = config()->GetString("colossus_timestamp_attr", "click_timestamp_list");
  if (colossus_timestamp_attr_.empty()) {
    LOG(ERROR) << "miss colossus_timestamp_attr";
    return false;
  }
  explain_item_id_attr_ = config()->GetString("explain_item_id_attr", "explain_item_id");
  if (explain_item_id_attr_.empty()) {
    LOG(ERROR) << "miss explain_item_id_attr";
    return false;
  }
  explain_item_spu_id_attr_ = config()->GetString("explain_item_spu_id_attr", "explain_item_spu_id");
  if (explain_item_spu_id_attr_.empty()) {
    LOG(ERROR) << "miss explain_item_spu_id_attr";
    return false;
  }
  explain_item_category_id_attr_ =
      config()->GetString("explain_item_category_id_attr", "explain_item_category_id");
  if (explain_item_category_id_attr_.empty()) {
    LOG(ERROR) << "miss explain_item_category_id_attr";
    return false;
  }
  explain_item_category1_id_attr_ =
      config()->GetString("explain_item_category1_id_attr", "explain_item_category1_id");
  if (explain_item_category1_id_attr_.empty()) {
    LOG(ERROR) << "miss explain_item_category1_id_attr";
    return false;
  }
  explain_item_category2_id_attr_ =
      config()->GetString("explain_item_category2_id_attr", "explain_item_category2_id");
  if (explain_item_category2_id_attr_.empty()) {
    LOG(ERROR) << "miss explain_item_category2_id_attr";
    return false;
  }
  explain_item_category3_id_attr_ =
      config()->GetString("explain_item_category3_id_attr", "explain_item_category3_id");
  if (explain_item_category3_id_attr_.empty()) {
    LOG(ERROR) << "miss explain_item_category3_id_attr";
    return false;
  }
  explain_item_min_price_attr_ = config()->GetString("explain_item_min_price_attr", "explain_item_min_price");
  if (explain_item_min_price_attr_.empty()) {
    LOG(ERROR) << "miss explain_item_min_price_attr";
    return false;
  }

  limit_user_num_ = config()->GetInt("limit_user_num", 1000);  // 用户序列长度
  if (limit_user_num_ <= 0) {
    LOG(ERROR) << "miss limit_user_num";
    return false;
  }
  min_seconds_ago_ = config()->GetInt("min_seconds_ago", 60);  // 最近 time
  if (min_seconds_ago_ < 0) {
    LOG(ERROR) << "miss min_seconds_ago";
    return false;
  }
  max_seconds_ago_ = config()->GetInt("max_seconds_ago", 30 * 24 * 3600);  //最远 time
  use_ad_slot_size_ = config()->GetBoolean("use_ad_slot_size", true);
  parameter_sign_bits_ = config()->GetInt("parameter_sign_bits", 52);
  if (parameter_sign_bits_ < 0) {
    LOG(ERROR) << "miss parameter_sign_bits";
    return false;
  }
  SIGN_MASK_ = ((1ul << parameter_sign_bits_) - 1);
  if (!GetIntList("mio_slots_id", &mio_slots_ids_)) return false;
  if (!GetIntList("slots_id", &slots_ids_)) return false;
  if (slots_ids_.size() != mio_slots_ids_.size()) return false;
  if (!GetIntList("interest_weight", &interest_weight_)) return false;

  output_item_slot_attr_ =
      config()->GetString("output_item_slot_attr", "ad_live_user_goods_click_v1_slot");  // 输出 sparse slot
  if (output_item_slot_attr_.empty()) {
    LOG(ERROR) << "miss output_item_slot_attr";
    return false;
  }
  output_item_sign_attr_ =
      config()->GetString("output_item_sign_attr", "ad_live_user_goods_click_v1_sign");  // 输出 sparse sign
  if (output_item_sign_attr_.empty()) {
    LOG(ERROR) << "miss output_item_sign_attr";
    return false;
  }
  if (!remap_util_.Init(config())) {
    LOG(ERROR) << "remap util init failed";
    return false;
  }
  slot_as_attr_name_ = config()->GetBoolean("slot_as_attr_name", false);
  zero_by_first_item_ = config()->GetBoolean("zero_by_first_item", true);
  slot_as_item_attr_name_prefix_ = config()->GetString("slot_as_item_attr_name_prefix", "");

  CL_LOG_EVERY_N(INFO, 10000) << "AdLiveUserGoodsClickOptV3Enricher: "
                              << ", colossus_goods_id_attr_: " << colossus_goods_id_attr_
                              << ", colossus_timestamp_attr_: " << colossus_timestamp_attr_
                              << ", limit_user_num_: " << limit_user_num_
                              << ", min_seconds_ago_: " << min_seconds_ago_
                              << ", max_seconds_ago_: " << max_seconds_ago_
                              << ", use_ad_slot_size_: " << use_ad_slot_size_
                              << ", parameter_sign_bits_: " << parameter_sign_bits_
                              << ", SIGN_MASK_: " << SIGN_MASK_
                              << ", mio_slots_ids_[0]: " << mio_slots_ids_[0]
                              << ", slots_ids_[0]: " << slots_ids_[0]
                              << ", output_item_slot_attr_: " << output_item_slot_attr_
                              << ", output_item_sign_attr_: " << output_item_sign_attr_;
  return true;
}

void AdLiveUserGoodsClickOptV3Enricher::Enrich(MutableRecoContextInterface *context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
  // 获取输入信息
  auto explain_item_id_accessor = context->GetItemAttrAccessor(explain_item_id_attr_);
  auto explain_item_spu_id_accessor = context->GetItemAttrAccessor(explain_item_spu_id_attr_);
  auto explain_item_category_id_accessor = context->GetItemAttrAccessor(explain_item_category_id_attr_);
  auto explain_item_x7_cat1_accessor = context->GetItemAttrAccessor(explain_item_category1_id_attr_);
  auto explain_item_x7_cat2_accessor = context->GetItemAttrAccessor(explain_item_category2_id_attr_);
  auto explain_item_x7_cat3_accessor = context->GetItemAttrAccessor(explain_item_category3_id_attr_);
  auto explain_item_min_price_accessor = context->GetItemAttrAccessor(explain_item_min_price_attr_);

  auto goods_ids = context->GetIntListCommonAttr(colossus_goods_id_attr_);
  auto timestamps = context->GetIntListCommonAttr(colossus_timestamp_attr_);

  if (!goods_ids || !timestamps || goods_ids->empty()) {
    FB_LOG_EVERY_MS(WARNING, 1000) << "colossus no data."
                                   << "colossus length invalid: " << goods_ids->size()
                                   << ", timestamps->size()=" << timestamps->size();
    return;
  }

  int colossus_length = goods_ids->size();
  if (timestamps->size() != colossus_length) {
    FB_LOG_EVERY_MS(WARNING, 1000) << "colossus click length invalid: " << colossus_length
                                   << ", timestamps->size()=" << timestamps->size();
    return;
  }

#define ADD_SIGN(i, expr)                                                                             \
  do {                                                                                                \
    if (i < mio_slots_ids_.size()) {                                                                  \
      uint64_t slot_sign = ((uint64_t)(slots_ids_[i]) << parameter_sign_bits_) | (SIGN_MASK_ & expr); \
      uint16_t slot_new = 0;                                                                          \
      uint64_t sign_new = 0;                                                                          \
      if (remap_util_.RemapSlotSign(mio_slots_ids_[i], slot_sign, &slot_new, &sign_new,               \
                                    use_ad_slot_size_)) {                                             \
        output_slot->emplace_back(slot_new);                                                          \
        output_sign->emplace_back(sign_new);                                                          \
      }                                                                                               \
    }                                                                                                 \
  } while (0)

  thread_local folly::F14FastMap<int, folly::F14FastMap<std::string, std::vector<int64>>> target_result;
  target_result.clear();

  int item_idx = 0;
  int seq_cnt = 0;

  int64_t request_time = context->GetRequestTime() / 1000;
  time_t raw_time_request = static_cast<time_t>(request_time);
  struct tm request_time_info;

  localtime_r(&raw_time_request, &request_time_info);
  // 获取小时
  int rq_hour_achor = request_time_info.tm_hour;
  int rq_hour_last = (rq_hour_achor + 24 - 1) % 24;
  int rq_hour_next = (rq_hour_achor + 1) % 24;
  // 获取星期几 (0 表示星期日, 6 表示星期六)
  int rq_weekday = request_time_info.tm_wday;
  // 获取月数 (0 表示一月, 11 表示十二月)
  int rq_month = request_time_info.tm_mon;
  // 获取日期数
  int rq_mday = request_time_info.tm_mday;
  // 倒序取, 最新的数据优先
  int out_lenth = std::min(colossus_length, limit_user_num_);
  int64_t first_ts = timestamps->at(colossus_length - out_lenth);

  // item slots
  for (auto it = begin; it != end; ++it) {
    auto output_sign = &target_result[item_idx][output_item_sign_attr_];
    auto output_slot = &target_result[item_idx][output_item_slot_attr_];

    if (context->HasItemAttr(*it, explain_item_id_accessor)) {
      auto explain_item_id = *context->GetIntItemAttr(*it, explain_item_id_accessor);
      ADD_SIGN(0, explain_item_id);
    } else {
      ADD_SIGN(0, 0);
    }
    if (context->HasItemAttr(*it, explain_item_category_id_accessor)) {
      auto explain_item_x7_cat = *context->GetIntItemAttr(*it, explain_item_category_id_accessor);
      int64_t explain_item_x7_cat1 = (explain_item_x7_cat >> 48) & 0xffff;
      ADD_SIGN(6, explain_item_x7_cat1);
    } else {
      ADD_SIGN(6, 0);
    }
    if (context->HasItemAttr(*it, explain_item_x7_cat2_accessor)) {
      auto explain_item_x7_cat2 = *context->GetIntItemAttr(*it, explain_item_x7_cat2_accessor);
      ADD_SIGN(7, explain_item_x7_cat2);
    } else {
      ADD_SIGN(7, 0);
    }
    if (context->HasItemAttr(*it, explain_item_x7_cat3_accessor)) {
      auto explain_item_x7_cat3 = *context->GetIntItemAttr(*it, explain_item_x7_cat3_accessor);
      ADD_SIGN(8, explain_item_x7_cat3);
    } else {
      ADD_SIGN(8, 0);
    }
    if (context->HasItemAttr(*it, explain_item_min_price_accessor)) {
      auto explain_item_min_price = *context->GetIntItemAttr(*it, explain_item_min_price_accessor);
      ADD_SIGN(11, explain_item_min_price);
    } else {
      ADD_SIGN(11, 0);
    }

    ADD_SIGN(14, rq_hour_achor);
    ADD_SIGN(14, rq_hour_last);
    ADD_SIGN(14, rq_hour_next);
    ADD_SIGN(15, rq_mday);
    ADD_SIGN(16, rq_weekday);
    ADD_SIGN(17, rq_month);
    if (zero_by_first_item_) {
      ADD_SIGN(1, (request_time - first_ts) / 1800);  // 第一次时间戳 0 点
    } else {
      ADD_SIGN(1, 0);  // 请求时间戳为 0 点
    }
    ++item_idx;
  }

#undef ADD_SIGN
  // 生效 item slots
  timer_.AppendCostMs("extract_signs");
  for (auto result_iter = begin; result_iter != end; ++result_iter) {
    for (const auto &it : target_result[result_iter - begin]) {
      context->SetIntListItemAttr(result_iter->item_key, it.first, std::vector<int64>(it.second));
    }
    if (slot_as_attr_name_) {
      const auto &output_slot = target_result[result_iter - begin][output_item_slot_attr_];
      const auto &output_sign = target_result[result_iter - begin][output_item_sign_attr_];
      for (size_t i = 0; i < output_slot.size(); ++i) {
        context->AppendIntListItemAttr(result_iter->item_key,
                                       slot_as_item_attr_name_prefix_ + std::to_string(output_slot[i]),
                                       output_sign[i]);
      }
    }
  }
  timer_.AppendCostMs("fill_attr");
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AdLiveUserGoodsClickOptV3Enricher, AdLiveUserGoodsClickOptV3Enricher);

}  // namespace platform
}  // namespace ks
