#include "dragon/src/processor/ext/ad/predict/enricher/ad_merge_feature_enricher.h"
#include <set>
#include <string>
#include <utility>
#include "ks/base/shared_data/shared_data.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/bs_log.h"
#include "teams/ad/ad_nn/bs_kconf/bs_kconf_util.h"
#include "teams/ad/ad_nn/feature/feature_stat_control.h"
#include "teams/ad/ad_nn/feature/protocol/define.h"
#include "teams/ad/ad_nn/feature/protocol/protocol_utils.h"
#include "teams/ad/ad_nn/service/debug_sample.h"
#include "teams/ad/ad_nn/service/service_util.h"
#include "teams/ad/ad_nn/service_status/service_status.h"

namespace ks {
namespace ad_nn {
DECLARE_uint64(slot_offset_mask);
}  // namespace ad_nn

namespace platform {
using ks::ad_nn::DebugProduceType;
using ks::ad_nn::DebugSample;
using ks::ad_nn::FeatureStat;
using FeatureDataType = ks::ad_algorithm::FeatureDataType;
using ks::ad_nn::protocol::ProtocolUtil;
using kuaishou::ad::algorithm::InferFeatureList;

bool AdMergeFeatureEnricher::InitProcessor() {
  name_ = ks::ad_nn::GetKcsModelCmd();
  auto json_config = config();
  bs_log_attr_name_ = json_config->GetString("bs_log_attr", "BslogBsItem");
  feature_list_attr_name_ = json_config->GetString("infer_feature_list_attr", "FeatureResult");
  check_duplicated_item_ = json_config->GetBoolean("check_duplicated_item", true);
  if (check_duplicated_item_) {
    duplicated_item_attr_name_ = json_config->GetString("duplicated_item_attr", "DragonDuplicatedItem");
  }
  use_protocol_accessor_ = json_config->GetBoolean("use_protocol_accessor", false);
  serialize_feature_ = json_config->GetBoolean("serialize_feature", true);
  serialized_features_attr_name_ = json_config->GetString("serialized_features_attr", "serialized_features");
  need_feature_extractor_ = json_config->GetBoolean("need_feature_extractor", true);
  compress_feature_ = json_config->GetBoolean("compress_feature", false);
  compress_level_ = json_config->GetInt("compress_level", -20);
  if (compress_level_ > 22) {
    LOG(WARNING) << "invalid compress_level: " << compress_level_ << ", use default.";
    compress_level_ = -20;
  }
  LOG(INFO) << "serialize_feature: " << serialize_feature_ << ", compress_feature: " << compress_feature_
            << ", compress_level: " << compress_level_;
  if (ks::SharedData<FeatureFileInfo>::has_name(name_)) {
    feature_file_info_ = ks::SharedData<FeatureFileInfo>::mutable_data(name_);
  } else {
    // Load feature file
    auto feature_path = json_config->GetString("feature_path");
    // 也支持通过设置 model feature 生成
    auto model_feature = json_config->GetString("model_feature");
    if (!model_feature.empty()) {
      feature_path = ks::ad_nn::GetModelPsRoot(name_);
    }

    if (need_feature_extractor_) {
      if (feature_path.empty()) {
        CL_LOG(ERROR) << "feature path is not configured";
        return false;
      }
    } else {
      feature_path = "";
    }

    auto ret = ks::SharedData<FeatureFileInfo>::Create(name_);
    if (!ret) {
      CL_LOG(INFO) << "create FeatureFileInfo " << name_ << " failed";
      return false;
    }
    feature_file_info_ = ks::SharedData<FeatureFileInfo>::mutable_data(name_);
    if (0 != ks::ad_nn::FeatureFileInfo::LoadFeatureFile(feature_path, true, feature_file_info_.get(), true,
                                                         false)) {
      CL_LOG(ERROR) << "load feature file:[" << feature_path << "] failed";
      return false;
    }
    if (!need_feature_extractor_) {
      std::set<int32_t> attr_ids;
      auto model_config = ks::ad_nn::PredictServiceKconfUtil::GetModelConfig(name_);
      ks::ad_nn::FeatureUtils::GetFeatureUsedBsAttrs(*feature_file_info_, &attr_ids);
      ks::ad_nn::FeatureUtils::GetDragonUsedBsAttrs(model_config, &attr_ids);
      auto json = ks::ad_nn::PredictServiceKconfUtil::featureConfigMap()->data;
      if (json && ks::ad_algorithm::BSInfoStatic::Instance().InitFromJson(*json)) {
        LOG(INFO) << "Success to init BSInfoStatic.";
      } else {
        LOG(FATAL) << "error to init BSInfoStatic.";
      }
      ks::ad_nn::RegisterBsAttrs(attr_ids, name_);
    }
    DebugSample::GetInstance()->UpdateFeatureConfig(feature_file_info_.get());
    CL_LOG(INFO) << "FeatureFileInfo init succeeds";
  }
  if (ks::SharedData<FeatureStat>::has_name(name_)) {
    feature_stat_ = ks::SharedData<FeatureStat>::mutable_data(name_);
  } else {
    auto ret = ks::SharedData<FeatureStat>::Create(name_);
    if (!ret) {
      CL_LOG(INFO) << "create feature stat " << name_ << " failed";
      return false;
    }
    feature_stat_ = ks::SharedData<FeatureStat>::mutable_data(name_);
    feature_stat_->UpdateName2DenseFlag(feature_file_info_->features);
    int stat_fea_value_mode = ks::ad_nn::SetStatFeatureMode(json_config->GetInt("stat_instance_num", 3));
    feature_stat_->SetStatFeaValueMode(name_, stat_fea_value_mode);
    feature_stat_->SetStatFeaWithCmdKey(json_config->GetBoolean("stat_fea_with_cmdkey", false));
    feature_stat_->SetStatFeaValueFreq(json_config->GetInt("stat_fea_value_frequence", 100));
    feature_stat_->SetStatFeaValueMaxItemCount(json_config->GetInt("stat_fea_value_max_item_count", 10));
  }
  InitFeature(json_config);
  UpdateDenseConfig(*feature_file_info_);
  return true;
}
void AdMergeFeatureEnricher::InitFeature(const base::Json *config) {
  if (!config) {
    LOG(FATAL) << "kconf model config is nullptr";
  }
  auto template_keys = config->Get("template_pipeline_keys");
  if (template_keys != nullptr && template_keys->IsArray() && template_keys->size() > 1) {
    LOG(FATAL) << "not support template dragon.";
  }
  auto slot_keys_json = config->Get("item_slot_attrs");
  if (slot_keys_json != nullptr && slot_keys_json->IsArray() && slot_keys_json->size() > 0) {
    item_dragon_slot_keys_.resize(slot_keys_json->size());
    for (int idx = 0; idx < slot_keys_json->size(); ++idx) {
      if (!slot_keys_json->GetString(idx, &item_dragon_slot_keys_[idx])) {
        std::ostringstream oss;
        oss << "item_dragon_slot_keys_ parse failed idx " << idx;
        ks::ad_nn::ServiceStatus::Get().GetModule(MODULE_DEFAULT)->LogFail(oss.str());
        LOG(FATAL) << "item_dragon_slot_keys_ parse failed idx " << idx;
      }
    }
  }
  auto sign_keys_json = config->Get("item_sign_attrs");
  if (sign_keys_json != nullptr && sign_keys_json->IsArray() && sign_keys_json->size() > 0) {
    item_dragon_sign_keys_.resize(sign_keys_json->size());
    for (int idx = 0; idx < sign_keys_json->size(); ++idx) {
      if (!sign_keys_json->GetString(idx, &item_dragon_sign_keys_[idx])) {
        std::ostringstream oss;
        oss << "item_dragon_sign_keys_ parse failed idx " << idx;
        ks::ad_nn::ServiceStatus::Get().GetModule(MODULE_DEFAULT)->LogFail(oss.str());
        LOG(FATAL) << "item_dragon_sign_keys_ parse failed idx " << idx;
      }
    }
  }
  auto user_slot_keys_json = config->Get("user_slot_attrs");
  if (user_slot_keys_json != nullptr && user_slot_keys_json->IsArray() && user_slot_keys_json->size() > 0) {
    user_dragon_slot_keys_.resize(user_slot_keys_json->size());
    for (int idx = 0; idx < user_slot_keys_json->size(); ++idx) {
      if (!user_slot_keys_json->GetString(idx, &user_dragon_slot_keys_[idx])) {
        std::ostringstream oss;
        oss << "user_dragon_slot_keys_ parse failed idx " << idx;
        ks::ad_nn::ServiceStatus::Get().GetModule(MODULE_DEFAULT)->LogFail(oss.str());
        LOG(FATAL) << "user_dragon_slot_keys_ parse failed idx " << idx;
      }
    }
  }
  auto user_sign_keys_json = config->Get("user_sign_attrs");
  if (user_sign_keys_json != nullptr && user_sign_keys_json->IsArray() && user_sign_keys_json->size() > 0) {
    user_dragon_sign_keys_.resize(user_sign_keys_json->size());
    for (int idx = 0; idx < user_sign_keys_json->size(); ++idx) {
      if (!user_sign_keys_json->GetString(idx, &user_dragon_sign_keys_[idx])) {
        std::ostringstream oss;
        oss << "user_dragon_sign_keys_ parse failed idx " << idx;
        ks::ad_nn::ServiceStatus::Get().GetModule(MODULE_DEFAULT)->LogFail(oss.str());
        LOG(FATAL) << "user_dragon_sign_keys_ parse failed idx " << idx;
      }
    }
  }

  // dense 配置的 attr 名字直接就是特征名
  auto user_dense_attrs = config->Get("user_dense_attrs");
  if (user_dense_attrs && user_dense_attrs->IsArray()) {
    for (const auto &val : user_dense_attrs->array()) {
      auto attr = val->StringValue();
      config_user_dense_attrs_[attr] = nullptr;
    }
  }
  auto item_dense_attrs = config->Get("item_dense_attrs");
  if (item_dense_attrs && item_dense_attrs->IsArray()) {
    for (const auto &val : item_dense_attrs->array()) {
      auto attr = val->StringValue();
      config_item_dense_attrs_[attr] = nullptr;
    }
  }
  if (item_dragon_slot_keys_.size() != item_dragon_sign_keys_.size()) {
    LOG(FATAL) << "item slot is different from sign";
  }
  if (user_dragon_slot_keys_.size() != user_dragon_sign_keys_.size()) {
    LOG(FATAL) << "user slot is different from sign";
  }
  CL_LOG(INFO) << "template dragon init success, item attr size: " << item_dragon_slot_keys_.size()
               << ", user attr size: " << user_dragon_slot_keys_.size();
}
bool AdMergeFeatureEnricher::FetchOutputAttr(
    DragonOutputResult *dragon_result, std::shared_ptr<Executor> executor,
    const std::vector<std::string> &item_slot_attrs, const std::vector<std::string> &item_sign_attrs,
    const std::vector<std::string> &user_slot_attrs, const std::vector<std::string> &user_sign_attrs,
    const std::vector<std::string> &item_dense_attrs, const std::vector<std::string> &user_dense_attrs) {
  if (dragon_result == nullptr) {
    CL_LOG(ERROR) << "dragon_result is nullptr";
    return false;
  }
  // 处理 user 数据
  auto &user_signs = dragon_result->user_signs;
  // 处理 user sparse 数据
  for (int i = 0; i < user_slot_attrs.size(); ++i) {
    auto result_slots = executor->GetIntList(user_slot_attrs[i]);
    auto result_signs = executor->GetIntList(user_sign_attrs[i]);
    if (result_slots.has_value() && result_signs.has_value() && result_slots->size() != 0 &&
        result_signs->size() != 0 && result_slots->size() == result_signs->size()) {
      // spin the result
      const auto &slots = result_slots.value();
      const auto &signs = result_signs.value();
      for (int i = 0; i < slots.size(); ++i) {
        user_signs[slots[i]].emplace_back(signs[i]);
      }
    } else {
      if (!result_slots.has_value()) {
        CL_LOG(ERROR) << "user slot attr: " << user_slot_attrs[i] << " is empty.";
      }
      if (!result_signs.has_value()) {
        CL_LOG(ERROR) << "user sign attr: " << user_sign_attrs[i] << " is empty.";
      }
      if (result_slots.has_value() && result_signs.has_value() &&
          result_slots->size() != result_signs->size()) {
        CL_LOG(ERROR) << "user slot and sign size mismatch, slot attr: " << user_slot_attrs[i]
                      << ", sign attr: " << user_sign_attrs[i] << " -- " << result_slots->size() << " vs "
                      << result_signs->size();
      }
    }
  }
  // 处理 user dense 数据
  auto &user_values = dragon_result->user_values;
  auto &int_user_values = dragon_result->int_user_values;
  // 这里是否可以跳过 FetchOutput,直接 Merge ?
  for (int i = 0; i < user_dense_attrs.size(); ++i) {
    auto &attr_name = user_dense_attrs[i];
    auto config_iter = config_user_dense_attrs_.find(attr_name);
    if (config_iter == config_user_dense_attrs_.end() || config_iter->second == nullptr) {
      CL_LOG(ERROR) << "user dense attr " << attr_name << " not found in config";
      return false;
    }
    auto data_type = config_iter->second->data_type;
    if (data_type == FeatureDataType::DT_FLOAT) {
      auto &dense_value = user_values[attr_name];
      auto result_values = executor->GetDoubleList(attr_name);  // 这里为啥直接 Double? 有约定?
      if (result_values.has_value() && result_values->size() != 0) {
        // append the result
        const auto &values = result_values.value();
        for (int i = 0; i < values.size(); ++i) {
          dense_value.emplace_back(values[i]);
        }
      } else {
        CL_LOG(ERROR) << "user dense attr " << attr_name << " is empty";
        continue;
      }
    } else if (data_type == FeatureDataType::DT_INT32 || data_type == FeatureDataType::DT_INT64) {
      auto &dense_value = int_user_values[attr_name];
      auto result_values = executor->GetIntList(attr_name);
      if (result_values.has_value() && result_values->size() != 0) {
        // append the result
        const auto &values = result_values.value();
        for (int i = 0; i < values.size(); ++i) {
          dense_value.emplace_back(values[i]);
        }
      } else {
        CL_LOG(ERROR) << "user dense attr " << attr_name << " is empty";
        continue;
      }
    }
  }
  // 双塔召回场景下， 会有 fake item 的逻辑，但是 ps 侧没有 item 相关的特征，会导致下面
  // item_signs.size() != dragon_items.size() 这个检查不通过，导致失败
  if (skip_item_part_) {
    LOG_EVERY_N(INFO, 1000000) << "skip item part";
    return true;
  }
  // 处理 item 数据
  const auto &dragon_items = executor->GetItemList();
  if (0 == dragon_items.size()) {
    CL_LOG(ERROR) << "dragon pipeline executed failed, result items is empty";
    return false;
  }
  auto &item_signs = dragon_result->item_signs;
  auto &item_values = dragon_result->item_values;
  auto &int_item_values = dragon_result->int_item_values;
  absl::optional<absl::Span<const int64>> bslog2pipeline;
  if (check_duplicated_item_) {
    bslog2pipeline = executor->GetIntList(duplicated_item_attr_name_);
  }
  if (check_duplicated_item_ && bslog2pipeline.has_value()) {
    if (item_signs.size() != bslog2pipeline->size()) {
      CL_LOG(ERROR) << "item size different, bslog2pipeline size: " << bslog2pipeline->size()
                    << ", input size: " << item_signs.size();
      return false;
    }
  } else {
    if (item_signs.size() != dragon_items.size()) {
      CL_LOG(ERROR) << "item size different, dragon item size: " << dragon_items.size()
                    << ", input size: " << item_signs.size();
      return false;
    }
  }
  for (int item_index = 0; item_index < item_signs.size(); ++item_index) {
    auto real_index = item_index;
    if (check_duplicated_item_ && bslog2pipeline.has_value()) {
      real_index = (*bslog2pipeline)[item_index];
    }
    // dragon_items 有真实数据
    auto &dragon_item = dragon_items[real_index];
    auto raw_item_index = item_index;
    absl::flat_hash_map<uint32_t, std::vector<int64>> &slot_signs = item_signs[raw_item_index];
    // 处理 item sparse 数据
    for (int i = 0; i < item_slot_attrs.size(); ++i) {
      auto dragon_result_slots = dragon_item.GetIntList(item_slot_attrs[i]);
      auto dragon_result_signs = dragon_item.GetIntList(item_sign_attrs[i]);
      if (dragon_result_slots.has_value() && dragon_result_signs.has_value() &&
          dragon_result_slots->size() != 0 && dragon_result_signs->size() != 0 &&
          dragon_result_slots->size() == dragon_result_signs->size()) {
        // spin the result
        const auto &slots = dragon_result_slots.value();
        const auto &signs = dragon_result_signs.value();
        for (int i = 0; i < dragon_result_slots->size(); ++i) {
          slot_signs[slots[i]].emplace_back(signs[i]);
        }
      } else {
        if (!dragon_result_slots.has_value()) {
          CL_LOG(ERROR) << "item slot attr: " << item_slot_attrs[i] << " is empty.";
        }
        if (!dragon_result_signs.has_value()) {
          CL_LOG(ERROR) << "item sign attr: " << item_sign_attrs[i] << " is empty.";
        }
        if (dragon_result_slots.has_value() && dragon_result_signs.has_value() &&
            dragon_result_slots->size() != dragon_result_slots->size()) {
          CL_LOG(ERROR) << "item slot and sign size mismatch, slot attr: " << item_slot_attrs[i]
                        << ", sign attr: " << item_sign_attrs[i] << " -- " << dragon_result_slots->size()
                        << " vs " << dragon_result_signs->size();
        }
      }
    }
    // 处理 item dense 数据
    auto &dense_values = item_values[raw_item_index];
    auto &int_dense_values = int_item_values[raw_item_index];
    for (int i = 0; i < item_dense_attrs.size(); ++i) {
      auto &attr_name = item_dense_attrs[i];
      auto config_iter = config_item_dense_attrs_.find(attr_name);
      if (config_iter == config_item_dense_attrs_.end() || config_iter->second == nullptr) {
        CL_LOG(ERROR) << "item dense attr " << attr_name << " not found in config";
        return false;
      }
      auto data_type = config_iter->second->data_type;
      if (data_type == FeatureDataType::DT_FLOAT) {
        auto &dense_value = dense_values[attr_name];
        auto result_values = dragon_item.GetDoubleList(attr_name);
        if (result_values.has_value() && result_values->size() != 0) {
          // append the result
          const auto &values = result_values.value();
          for (int i = 0; i < values.size(); ++i) {
            dense_value.emplace_back(values[i]);
          }
        } else {
          CL_LOG(ERROR) << "item dense attr " << attr_name << " is empty";
        }
      } else if (data_type == FeatureDataType::DT_INT32 || data_type == FeatureDataType::DT_INT64) {
        auto &dense_value = int_dense_values[attr_name];
        auto result_values = dragon_item.GetIntList(attr_name);
        if (result_values.has_value() && result_values->size() != 0) {
          // append the result
          const auto &values = result_values.value();
          for (int i = 0; i < values.size(); ++i) dense_value.emplace_back(values[i]);
        } else {
          CL_LOG(ERROR) << "item dense attr " << attr_name << " is empty";
        }
      }
    }
  }
  return true;
}
void AdMergeFeatureEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
  auto bs_log = context->GetPtrCommonAttr<ks::ad_algorithm::BSLog>(bs_log_attr_name_);
  auto feature_list = context->GetPtrCommonAttr<InferFeatureList>(feature_list_attr_name_);
  if (!bs_log) {
    LOG_EVERY_N(WARNING, 1000) << "no bs_log found, return directly";
    return;
  }
  if (!feature_list) {
    if (need_feature_extractor_) {
      falcon::Inc("feature_server.no_infer_feature_list", 1);
      context->SetExecutionStatus(ExecutionStatus::TERMINATED);
      LOG_EVERY_N(WARNING, 1000) << "no infer_feature_list found, return directly";
      return;
    }
    feature_list_.reset(new InferFeatureList());
    auto item_count = bs_log->item_size();
    auto item_features = feature_list_->mutable_item_features();
    item_features->Reserve(item_count);
    for (size_t idx = 0; idx < item_count; ++idx) {
      auto item_feature = item_features->Add();
      item_feature->set_item_id(bs_log->item_id(idx));
    }
    LOG_FIRST_N(INFO, 10000) << "infer_feature_list not found, just generate one.";
  } else {
    LOG_EVERY_N(INFO, 10000) << "InferFeatureList has exist.";
    feature_list_.reset(const_cast<InferFeatureList *>(feature_list), [](const InferFeatureList *p) {});
  }

  DragonOutputResult result;
  result.item_signs.resize(bs_log->item_size());
  result.item_values.resize(bs_log->item_size());
  auto executor = std::make_shared<Executor>(context);
  if (!FetchOutputAttr(&result, executor, item_dragon_slot_keys_, item_dragon_sign_keys_,
                       user_dragon_slot_keys_, user_dragon_sign_keys_, item_dense_attrs_,
                       user_dense_attrs_)) {
    falcon::Inc("feature_server.dragon_feature_fetch_fail", 1);
    LOG_EVERY_N(WARNING, 100) << "dragon feature fetch output failed, bs_log:" << bs_log
                              << " llsid:" << bs_log->llsid();
    return;
  }
  MergeFeature(*bs_log, const_cast<InferFeatureList *>(feature_list_.get()), result);
  if (context->IsDebugRequest() && FLAGS_logging_switch_uid_mod_divisor == 1) {
    std::ofstream dst("extractor_feature_" + context->GetRequestId());
    dst << "extractor feature, llsid:" << context->GetRequestId()
        << " content:" << feature_list_->DebugString();
    dst.close();
  }
  if (feature_stat_) {
    feature_stat_->DoFeaValueStat(*feature_list_, name_);
  }
  if (serialize_feature_ && use_protocol_accessor_) {
    SeralizeByProtocolAccessor(context);
  } else if (serialize_feature_) {
    thread_local std::string serialized_features;
    serialized_features.clear();
    feature_list_->SerializeToString(&serialized_features);
    if (compress_feature_) {
      std::string compressed_features;
      compressed_features.clear();
      ad_nn::CompressionUtil::Compress(ad_nn::CompressionUtil::CompressType::ZSTD, serialized_features,
                                       &compressed_features, compress_level_);
      CL_LOG(INFO) << "raw feature size: " << serialized_features.size()
                   << ", compressed feature size: " << compressed_features.size();
      context->SetStringCommonAttr(serialized_features_attr_name_, std::move(compressed_features));
    } else {
      context->SetStringCommonAttr(serialized_features_attr_name_, std::move(serialized_features));
    }
  } else {
    context->SetPtrCommonAttr(serialized_features_attr_name_, feature_list_);
  }
  // 一致性校验 特征值写入到 kafka 中
  if (DebugSample::GetInstance()->IsNeedProduce(bs_log->llsid())) {
    auto req_time = context->GetRequestTime();
    uint64_t llsid = bs_log->llsid();
    uint64_t user_id = context->GetUserId();
    std::shared_ptr<InferFeatureList> sample = std::make_shared<InferFeatureList>();
    sample->CopyFrom(*feature_list_);
    switch (DebugSample::GetInstance()->ProduceType()) {
      case DebugProduceType::AdLogWithFeatures: {
        auto to_str = [sample, req_time, llsid, user_id]() -> std::string {
          std::string output_str;
          kuaishou::ad::algorithm::AdJointLabeledLogWithFeatures adlog_with_features;
          DebugSample::GetInstance()->ConvertToAdLogWithFeatures(*sample, req_time, llsid, user_id, nullptr,
                                                                 &adlog_with_features);
          adlog_with_features.SerializeToString(&output_str);
          return output_str;
        };
        DebugSample::GetInstance()->AsyncProduce(to_str);
        break;
      }
      case DebugProduceType::SampleJoinApiRequest: {
        auto to_str = [sample, req_time, llsid, user_id]() -> std::string {
          std::string output_str;
          ks::reco::SampleJoinApiRequest join_req;
          DebugSample::GetInstance()->ConvertToSampleJoinApiRequest(*sample, req_time, llsid, user_id,
                                                                    &join_req);
          join_req.SerializeToString(&output_str);
          return output_str;
        };
        DebugSample::GetInstance()->AsyncProduce(to_str);
        break;
      }
      default:
        break;
    }
  }
}

void AdMergeFeatureEnricher::SeralizeByProtocolAccessor(MutableRecoContextInterface *context) {
  const auto serialize_start = ks::ad_nn::Clock::now();
  thread_local std::string serialized_features;
  serialized_features.clear();
  thread_local ad_nn::protocol::InferFeatureAccessorInterface *feature_accessor =
      ProtocolUtil::CreateFeatureAccessor(FLAGS_infer_feature_protocol);

  if (feature_accessor == nullptr) {
    LOG_EVERY_N(ERROR, 100) << "failed to get feature accessor by protocol: " << FLAGS_infer_feature_protocol;
    return;
  }

  if (!feature_accessor->SerializeInferFeatureList(*feature_list_, &serialized_features)) {
    LOG_EVERY_N(ERROR, 100) << "failed to get serialize infer feature list, protocol: "
                            << FLAGS_infer_feature_protocol;
    return;
  }

  falcon::Stat("feature_server.serialize_time_cost", ks::ad_nn::GetMicroseconds(serialize_start));
  if (compress_feature_) {
    std::string compressed_features;
    compressed_features.clear();
    ad_nn::CompressionUtil::Compress(ad_nn::CompressionUtil::CompressType::ZSTD, serialized_features,
                                     &compressed_features, compress_level_);
    CL_LOG(INFO) << "raw feature size: " << serialized_features.size()
                 << ", compressed feature size: " << compressed_features.size();
    context->SetStringCommonAttr(serialized_features_attr_name_, std::move(compressed_features));
  } else {
    context->SetStringCommonAttr(serialized_features_attr_name_, std::move(serialized_features));
  }
}

void AdMergeFeatureEnricher::MergeFeature(const ks::ad_algorithm::AdLogInterface &ad_log,
                                          InferFeatureList *features,
                                          const DragonOutputResult &dragon_result) {
  if (KS_UNLIKELY(features == nullptr)) {
    CL_LOG(ERROR) << "features is a nullptr";
    return;
  }
  // 0. 添加特征名
  auto is_bs = (ad_log.log_type() == ad_algorithm::LogType::LT_BS);
  for (auto &config : user_dragon_features_) {
    FeatureUtils::AddFeatureName(*config, features->mutable_user_feature_names(), is_bs);
  }
  for (auto &config : item_dragon_features_) {
    FeatureUtils::AddFeatureName(*config, features->mutable_item_feature_names(), is_bs);
  }
  // 1. 处理 user dragon 特征
  if (user_dragon_features_.size() > 0) {
    auto &user_signs = dragon_result.user_signs;
    auto &user_values = dragon_result.user_values;
    auto &int_user_values = dragon_result.int_user_values;
    auto user_features = features->mutable_user_features();
    auto offset = features->mutable_user_feature_offset();
    offset->Reserve(offset->size() + user_dragon_features_.size());
    for (auto &feature_config : user_dragon_features_)
      FillWithDragonResult(*feature_config, user_values, user_signs, int_user_values, offset, user_features,
                           add_default_sign_);
  }
  // 2. 处理 item dragon 特征
  if (item_dragon_features_.size() > 0) {
    auto &item_signs = dragon_result.item_signs;
    auto &item_values = dragon_result.item_values;
    auto &int_item_values = dragon_result.int_item_values;
    auto item_count = ad_log.item_size();
    auto item_features = features->mutable_item_features();
    for (size_t idx = 0; idx < item_count; ++idx) {
      auto &item_feature = (*item_features)[idx];
      auto features = item_feature.mutable_features();
      auto item_offset = item_feature.mutable_feature_offset();
      item_offset->Reserve(item_offset->size() + item_dragon_features_.size());
      for (auto &feature_config : item_dragon_features_)
        FillWithDragonResult(*feature_config, item_values[idx], item_signs[idx], int_item_values[idx],
                             item_offset, features, add_default_sign_);
    }
  }
}

bool AdMergeFeatureEnricher::FillWithDragonResult(
    const ks::ad_nn::FeatureConfig &feature_config,
    const absl::flat_hash_map<std::string, std::vector<float>> &dense_values,
    const absl::flat_hash_map<uint32_t, std::vector<int64>> &sparse_signs,
    const absl::flat_hash_map<std::string, std::vector<int64_t>> &int_dense_values,
    ::google::protobuf::RepeatedField<uint32_t> *offsets, ks::ad_nn::FeatureValueList *feature_values,
    bool add_default_sign) {
  // 填充 dense
  if (feature_config.is_dense) {
    if (feature_config.data_type == FeatureDataType::DT_FLOAT) {
      auto dst_values = feature_values->mutable_value();
      auto value_it = dense_values.find(feature_config.class_name);
      if (value_it != dense_values.end()) {
        for (float v : value_it->second) {
          dst_values->Add(v);
        }
      } else {
        LOG_EVERY_N(WARNING, 100000) << "dragon pipeline dense result without attr="
                                     << feature_config.class_name;
      }
      offsets->Add(dst_values->size());
    } else if (feature_config.data_type == FeatureDataType::DT_INT32) {
      auto dst_signs = feature_values->mutable_sign();
      auto value_it = int_dense_values.find(feature_config.class_name);
      if (value_it != int_dense_values.end()) {
        for (int64_t v : value_it->second) {
          dst_signs->Add(v);
        }
      } else {
        LOG_EVERY_N(WARNING, 100000) << "dragon pipeline dense int result without attr="
                                     << feature_config.class_name;
      }
      offsets->Add(dst_signs->size());
    } else {
      LOG(ERROR) << "unsupported data type: " << static_cast<int32_t>(feature_config.data_type);
      return false;
    }
    return true;
  }
  // 填充 sparse
  auto dst_signs = feature_values->mutable_sign();
  // 根据配置有哈希的需要移除前缀，无哈希的不移除
  bool need_hash = feature_config.field_size > 0;
  auto value_it = sparse_signs.find(feature_config.dragon_slot_id);
  if (value_it != sparse_signs.end()) {
    // 填充稀疏特征，填充 offset
    for (int64 v : value_it->second) {
      if (need_hash) {
        dst_signs->Add(v & ad_nn::FLAGS_slot_offset_mask);
      } else {
        dst_signs->Add(v);
      }
    }
  } else {
    LOG_EVERY_N(WARNING, 100000) << "dragon pipeline sparse result without key="
                                 << feature_config.dragon_slot_id;
  }
  // dragon 追加默认特征
  if (add_default_sign) {
    dst_signs->Add(feature_config.field_size);
  }
  offsets->Add(dst_signs->size());
  return true;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AdMergeFeatureEnricher, AdMergeFeatureEnricher)
}  // namespace platform
}  // namespace ks
