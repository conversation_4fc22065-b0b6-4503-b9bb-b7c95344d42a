#include "dragon/src/processor/ext/ad/predict/enricher/ad_infer_feature_enricher.h"

#include <string>
#include <utility>
#include "ks/base/shared_data/shared_data.h"
#include "teams/ad/ad_nn/feature/protocol/protocol_utils.h"
#include "teams/ad/ad_nn/feature_extract/processors/constant.h"
#include "teams/ad/ad_nn/service/service_util.h"
#include "teams/ad/ad_nn/utils/compress.h"
#include "teams/ad/ad_nn/utils/const.h"
#include "teams/ad/ad_nn/utils/rpc_name_tools.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_online_feature.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.grpc.pb.h"
#include "teams/reco-arch/embedding_manager/utils/reused_pb_arena.h"

namespace ks {
namespace platform {
using ks::ad_nn::protocol::ProtocolUtil;
using kuaishou::ad::algorithm::FeatureRequest;
using kuaishou::ad::algorithm::InferFeatureList;
using kuaishou::ad::algorithm::UniversePredictResponse;

bool AdInferFeatureEnricher::InitProcessor() {
  input_serialized_features_attr_ =
      config()->GetString("input_serialized_features_attr", "serialized_features");
  output_features_attr_ =
      config()->GetString("output_features_attr", ks::ad_nn::constant::DragonInputFeatureList);
  feature_compressed_ = config()->GetBoolean("feature_compressed", false);
  use_protocol_accessor_ = config()->GetBoolean("use_protocol_accessor", false);

  LOG(INFO) << "ad_infer_feature init succeeds, input_serialized_features_attr: "
            << input_serialized_features_attr_ << ", output_features_attr: " << output_features_attr_
            << ", feature_compressed: " << feature_compressed_
            << ", use_protocol_accessor: " << use_protocol_accessor_;
  return true;
}

void AdInferFeatureEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
  const auto &raw_features = context->GetStringCommonAttr(input_serialized_features_attr_);
  if (!raw_features || raw_features->empty()) {
    falcon::Inc("infer_server.empty_infer_feature_list", 1);
    context->SetExecutionStatus(ExecutionStatus::TERMINATED);
    LOG_EVERY_N(WARNING, 100) << "got empty infer features";
    return;
  }

  if (use_protocol_accessor_) {
    EnrichByProtoAccessor(raw_features, context, begin, end);
    return;
  }

  static thread_local reco_arch::utils::ReusedPbArena arena(1 << 20);
  arena.ResetArena();
  infer_req_ctx_.reset(new ks::ad_nn::InferRequestContext());

  auto feature_list = Arena::CreateMessage<InferFeatureList>(arena.GetArena());
  if (feature_compressed_) {
    std::string decompressed_feature;
    ad_nn::CompressionUtil::Decompress(ad_nn::CompressionUtil::CompressType::ZSTD, raw_features->data(),
                                       raw_features->size(), &decompressed_feature);
    if (!feature_list->ParseFromArray(decompressed_feature.data(), decompressed_feature.size())) {
      falcon::Inc("infer_server.infer_feature_parse_fail", 1);
      LOG_EVERY_N(WARNING, 100) << "infer features parse failed";
      return;
    }
  } else {
    if (!feature_list->ParseFromArray(raw_features->data(), raw_features->size())) {
      falcon::Inc("infer_server.infer_feature_parse_fail", 1);
      LOG_EVERY_N(WARNING, 100) << "infer features parse failed";
      return;
    }
  }
  uint64_t feature_count = feature_list->user_features().sign_size();
  for (auto &item_feature : feature_list->item_features()) {
    feature_count += item_feature.features().sign_size();
  }
  falcon::Stat("dnn_predict_server.sparse_feature_count", feature_count);

  if (context->IsDebugRequest()) {
    // TODO(yangjialin) debug 请求与 老 PS pb 解耦
    auto fake_req = Arena::Create<FeatureRequest>(arena.GetArena());
    auto fake_res = Arena::Create<UniversePredictResponse>(arena.GetArena());
    fake_req->set_is_debug_req(true);
    infer_req_ctx_->debug_kv_collector =
        Arena::Create<ks::ad_nn::InferServiceKvCollector>(arena.GetArena(), *fake_req, fake_res);
    context->SetPtrCommonAttr("ad_debug_fake_res", fake_res);
    if (infer_req_ctx_->debug_kv_collector == nullptr) {
      LOG(ERROR) << "Create PsKvCollector failed";
      return;
    }
    if (FLAGS_logging_switch_uid_mod_divisor == 1) {
      std::ofstream dst("extractor_feature_" + context->GetRequestId());
      dst << "extractor feature, llsid:" << context->GetRequestId()
          << " content:" << feature_list->DebugString();
      dst.close();
    }
  }

  context->SetPtrCommonAttr(output_features_attr_, feature_list);
  context->SetPtrCommonAttr(ks::ad_nn::constant::DragonInputInferRequest, infer_req_ctx_.get());
  // TODO(yangjialin) 支持模型校验
  context->SetIntCommonAttr(ks::ad_nn::constant::DragonValidateRequest, false);
}

void AdInferFeatureEnricher::EnrichByProtoAccessor(const absl::optional<absl::string_view> raw_features,
                                                   MutableRecoContextInterface *context,
                                                   RecoResultConstIter begin, RecoResultConstIter end) {
  infer_req_ctx_.reset(new ks::ad_nn::InferRequestContext());
  thread_local ad_nn::protocol::InferFeatureAccessorInterface *feature_accessor =
      ProtocolUtil::CreateFeatureAccessor(FLAGS_infer_feature_protocol);
  if (feature_accessor == nullptr) {
    LOG_EVERY_N(ERROR, 100) << "failed to get feature accessor by protocol " << FLAGS_infer_feature_protocol;
    context->SetExecutionStatus(ExecutionStatus::TERMINATED);
    return;
  }

  static thread_local reco_arch::utils::ReusedPbArena arena(1 << 20);

  if (feature_compressed_) {
    std::string decompressed_feature;
    ad_nn::CompressionUtil::Decompress(ad_nn::CompressionUtil::CompressType::ZSTD, raw_features->data(),
                                       raw_features->size(), &decompressed_feature);
    if (!feature_accessor->ParseFeature(decompressed_feature.data(), decompressed_feature.size(), &arena)) {
      falcon::Inc("infer_server.infer_feature_parse_fail", 1);
      LOG_EVERY_N(WARNING, 100) << "infer features parse failed";
      return;
    }
  } else {
    if (!feature_accessor->ParseFeature(raw_features->data(), raw_features->size(), &arena)) {
      falcon::Inc("infer_server.infer_feature_parse_fail", 1);
      LOG_EVERY_N(WARNING, 100) << "infer features parse failed";
      return;
    }
  }

  falcon::Stat("dnn_predict_server.sparse_feature_count", feature_accessor->SignCount());

  if (context->IsDebugRequest()) {
    SaveDebugAttrs(feature_accessor->UseArena(), &arena, context);
  }

  context->SetPtrCommonAttr(output_features_attr_, feature_accessor);
  context->SetPtrCommonAttr(ks::ad_nn::constant::DragonInputInferRequest, infer_req_ctx_.get());
  // TODO(yangjialin) 支持模型校验
  context->SetIntCommonAttr(ks::ad_nn::constant::DragonValidateRequest, false);
}

bool AdInferFeatureEnricher::SaveDebugAttrs(bool use_arena, reco_arch::utils::ReusedPbArena *arena,
                                            MutableRecoContextInterface *context) {
  if (!use_arena) {
    arena->ResetArena();
  }
  auto fake_req = Arena::Create<FeatureRequest>(arena->GetArena());
  auto fake_res = Arena::Create<UniversePredictResponse>(arena->GetArena());
  fake_req->set_is_debug_req(true);
  infer_req_ctx_->debug_kv_collector =
      Arena::Create<ks::ad_nn::InferServiceKvCollector>(arena->GetArena(), *fake_req, fake_res);
  context->SetPtrCommonAttr("ad_debug_fake_res", fake_res);
  if (infer_req_ctx_->debug_kv_collector == nullptr) {
    LOG(ERROR) << "Create PsKvCollector failed";
    return false;
  }
  return true;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AdInferFeatureEnricher, AdInferFeatureEnricher)
}  // namespace platform
}  // namespace ks
