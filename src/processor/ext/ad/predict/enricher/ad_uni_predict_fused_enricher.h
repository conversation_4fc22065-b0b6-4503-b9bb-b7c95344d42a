#pragma once

#include <functional>
#include <map>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "base/thread/rw_mutex.h"
#include "teams/reco-arch/uni-predict-v2/src/batching/batch_system.h"
#include "teams/reco-arch/uni-predict-v2/src/predictor/predictor.h"

#include "teams/ad/ad_nn/feature/ad_slot_config.h"
#include "teams/ad/ad_nn/infer_dragon/idt_data.h"
#include "teams/ad/ad_nn/model/concurrent_embedding.h"
#include "teams/ad/ad_nn/model/emp_embedding.h"
#include "teams/ad/ad_nn/model/micro_batch_model_validate.h"
#include "teams/ad/ad_nn/model/mio_btq_embedding.h"
// clang-format off
// NOTE: idt_model_loader.h 没有 include string_utils.h 头文件, 这里必须把 string_utils.h 放在其前面
#include "teams/ad/ad_nn/utils/string_utils.h"
#include "teams/ad/ad_nn/model/model_loader/idt_model_loader.h"
#include "teams/ad/ad_nn/feature/protocol/feature_accessor/feature_accessor_interface.h"
// clang-format on
#include "teams/ad/ad_nn/model/model_loader/nohash_btq_model_rollbacker.h"
#include "teams/ad/ad_nn/model/nohash_btq_embedding.h"

#include "teams/reco-arch/embedding_manager/utils/btq_wrapper.h"

#include "teams/reco-arch/third_party/onnx/onnx/onnx.pb.h"
#include "tensorflow/core/framework/graph.pb.h"

namespace ks::platform {

class AdUniPredictFusedInternalEnricher;

class AdUniPredictFusedEnricher : public CommonRecoBaseEnricher {
 public:
  AdUniPredictFusedEnricher() = default;

  void FalconReportQPS(MutableRecoContextInterface *context);
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end);

 private:
  bool InitProcessor() override;

 private:
  std::string model_key_;
  std::shared_ptr<AdUniPredictFusedInternalEnricher> internal_enricher_;
  DISALLOW_COPY_AND_ASSIGN(AdUniPredictFusedEnricher);
};

class AdUniPredictFusedInternalEnricher : public CommonRecoBaseEnricher {
  /// NOTE: 这里 Internal 的实现必须是单例, 否则每个线程实例化一个该对象的话, 每个对象内部的 embedding cache
  /// 和 model rollback 都会 mmap 一下 embedding table, 会把 shared memory 打爆的.

  // TODO(jiabw):
  // 1. model instance 支持 model validate
  // 2. calibration 支持
  // 3. fp16 等低精度数据类型的支持
  // 4. TensorFlow, TVM 等 model loader 的支持
 public:
  struct OutputConfig {
    std::string tensor_name;
    // 一个 tensor 对应多个 attr 的情况
    std::vector<std::string> attr_name;
  };
  struct InputConfig {
    std::string class_name;
    int field;
    bool is_common;
  };

 public:
  AdUniPredictFusedInternalEnricher() = default;
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  bool InitProcessor() override;

  std::function<void()> Purge() override {
    return [this]() {
      if (processor_config_) {
        delete processor_config_;
      }
    };
  }

  bool Load() {
    return true;
  }

  void UpdateConfig(const base::Json *config) {
    if (processor_config_) {
      delete processor_config_;
    }
    processor_config_ = new base::Json(base::StringToJson(config->ToString()));
  }

 private:
  // init
  bool InitGraphDef();
  bool InitFeatureFieldConfigs();
  bool InitModelConfig();
  bool InitEmpServiceConfig();
  bool InitEmbeddingStorage();
  bool InitEmbeddingUpdater();
  bool InitEmbeddingFetcher();
  bool CheckMetaAndFeature() const;
  bool LoadGraph();

  bool InitUniPredict();
  bool InitIdtAndModelRollbacker();

  void ReportModelLoadDelay();
  void EnrichWithProtocolAccessor(const ad_nn::protocol::InferFeatureAccessorInterface *feature_accessor_ptr,
                                  MutableRecoContextInterface *context, RecoResultConstIter begin,
                                  RecoResultConstIter end);

 private:
  bool UpdateWithDoubleBuffer();
  bool InitAndUpdateWithIdt();
  bool InitAndUpdateWithRollbacker();

 private:
  // predict
  bool MakePrediction(MutableRecoContextInterface *context, RecoResultConstIter begin,
                      RecoResultConstIter end, int num_items,
                      const ad_nn::protocol::InferFeatureAccessorInterface *feature_accessor,
                      const std::vector<ad_nn::InferFeatureInfo> &user_feature_infos,
                      const std::vector<ad_nn::InferFeatureInfo> &item_feature_infos,
                      const std::vector<uint64_t> &item_ids, const ad_nn::InferRequestContext *infer_context,
                      bool is_valid_req, reco_arch::uni_predict::ModelInstance *model_instance);

  // pre-process and post-process
  reco_arch::uni_predict::CallbackStatus FillInput(
      MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end, int num_items,
      const ad_nn::protocol::InferFeatureAccessorInterface *feature_accessor,
      const std::vector<ad_nn::InferFeatureInfo> &user_feature_infos,
      const std::vector<ad_nn::InferFeatureInfo> &item_feature_infos, const std::vector<uint64_t> &item_ids,
      const ad_nn::InferRequestContext *infer_context,
      std::shared_ptr<ad_nn::FieldLookupContext> lookup_context,
      const reco_arch::uni_predict::PreallocatedTensors &preallocated);

  reco_arch::uni_predict::CallbackStatus FetchOutput(
      MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end, int num_items,
      const reco_arch::uni_predict::PreallocatedTensors &preallocated);

 private:
  bool MioBtqCallback(const std::string &msg);
  bool RollbackCallback(bool rollback);
  bool DoubleBufferUpdateCallback(bool rollback);
  bool IdtPutWeights(uint64_t version_ms);
  void ResetModelData();
  bool CheckFeatureFileVersion(const std::string &feature_path) const;
  uint32_t GetEmbeddingDim(uint32_t field,
                           const std::map<std::string, ad_nn::FieldEmbeddingInfoMap> &emp_shard_infos) const;
  void CollectDebugInfo(const std::vector<uint64_t> &item_ids,
                        const ad_nn::protocol::InferFeatureAccessorInterface *feature_accessor,
                        const std::vector<ad_nn::InferFeatureInfo> &user_feature_infos,
                        const std::vector<ad_nn::InferFeatureInfo> &item_feature_infos,
                        const ad_nn::InferRequestContext *context);

 private:
  enum class GraphType {
    kUNKNOWN = 0,
    kTF = 1,
    kONNX = 2,
    // maybe more in the future...
  };

  std::string model_cmd_;
  const base::Json *processor_config_ = nullptr;
  std::string model_meta_path_;
  std::string model_root_path_;
  std::string model_load_path_;  // 存储模型参数 dnn_model_bin 和参数版本 version 的路径
  std::string deploy_model_name_;

  std::string queue_prefix_;
  std::string model_key_;

  // graph
  GraphType graph_type_ = GraphType::kUNKNOWN;
  tensorflow::GraphDef graph_def_;
  onnx::ModelProto onnx_model_;

  std::string embedding_node_prefix_;
  bool remove_sign_prefix_;
  bool kai_nohash_model_;
  uint64_t embedding_capacity_;
  std::string embedding_cache_type_;
  float embedding_min_fill_ratio_;

  /// model input, output and param
  std::vector<InputConfig> inputs_;
  // key: tensor_name, value: attr_names
  folly::F14FastMap<std::string, std::vector<std::string>> outputs_;
  std::vector<std::string> output_op_names_;
  std::vector<reco_arch::uni_predict::ParamMeta> param_metas_;
  std::vector<std::string> param_names_;
  std::vector<int> param_sizes_;
  reco_arch::uni_predict::IOTensorInfo io_tensor_info_;
  uint64_t param_total_len_{0};
  std::shared_ptr<reco_arch::uni_predict::DenseModelManager> dense_model_manager_;

  /// uni-predict
  std::shared_ptr<reco_arch::uni_predict::BatchingSystem> batch_system_;
  std::shared_ptr<reco_arch::uni_predict::Predictor> predictor_;

  // feature
  bool use_bs_fast_feature_ = false;
  bool use_protocol_accessor_ = false;
  ad_nn::FeatureFileInfo feature_file_info_;

  /// embedding 配置
  // 所有 emp_service 信息, 分 shard 存储的.
  std::map<std::string, ad_nn::FieldEmbeddingInfoMap> emp_shard_infos_;
  // 需要 cache 的 embedding 信息, emp_service::local
  ad_nn::FieldEmbeddingInfoMap field_embedding_infos_;
  std::shared_ptr<ad_nn::EmpEmbedding> emp_embedding_;
  // 作为 embedding cache 的而存在的, 是 MioBtqEmbedding 的基类, 下面两个指向同一个实例,
  // 只不过用的地方不同而已...
  std::shared_ptr<ad_nn::MioBtqEmbedding> mio_btq_embedding_;
  std::shared_ptr<ad_nn::NohashBtqEmbedding> nohash_btq_embedding_;

  std::shared_ptr<ad_nn::ConcurrentEmbedding> concurrent_embedding_;
  bool enable_run_sub_graph_{false};
  int max_field_size_ = 0;
  // key: feature_name
  folly::F14FastMap<std::string, ad_nn::InferFeatureInfo> feature_infos_;
  folly::F14FastMap<std::string, ad_nn::AdTensorInputConfig> ad_field_configs_;
  // 所有 field 的 embedding 长度, 索引为 field
  std::vector<std::pair<std::string, uint32_t>> field_input_names_;
  // 存储 sparse input 的 size, 也就是 embedding 表大小
  std::vector<uint32_t> sparse_field_sizes_;
  // 存储 sparse input 的总长度
  std::vector<uint32_t> embedding_vector_lens_;
  std::vector<uint32_t> feature_embed_len_;
  int total_field_count_ = 0, used_field_count_ = 0;
  uint32_t total_sparse_field_count_ = 0, total_dense_field_count_ = 0;
  uint32_t used_sparse_field_count_ = 0, used_dense_field_count_ = 0;
  ad_nn::InputFeatureMeta fea_meta_;
  ad_nn::Type2RetIndices sparse_ret_indices_;

  // 双 Buffer 相关配置
  bool use_double_buffer_loader_ = false;
  // 当前 serving 的 buffer
  std::shared_ptr<ad_nn::NohashBtqModelRollbacker> serving_buffer_;
  // 用于 参数更新的 buffer
  std::shared_ptr<ad_nn::NohashBtqModelRollbacker> not_serving_buffer_;
  // 双 buffer 更新的锁
  ::thread::RWMutex double_buffer_model_param_mutex_;

  /// idt, model_rollbacker etc.
  std::shared_ptr<ad_nn::IdtData> idt_data_;
  reco_arch::utils::BtqReader btq_reader_;
  std::shared_ptr<ad_nn::IdtModelLoader> idt_model_loader_;
  std::unordered_map<std::string, std::vector<float>> idt_weights_;

  std::shared_ptr<ad_nn::NohashBtqModelRollbacker> rollbacker_;
  std::atomic<bool> rollback_model_;
  bool idt_success_ = false;
  // 是否把特征抽取和预估融合到同一个 pipeline 中
  bool use_fused_infer_and_feature_ = false;
  std::string dragon_input_feature_list_;
  reco_arch::uni_predict::DenseFormat dense_format_;
  std::thread report_model_load_delay_thread_;

  DISALLOW_COPY_AND_ASSIGN(AdUniPredictFusedInternalEnricher);
};

}  // namespace ks::platform
