#include "dragon/src/processor/ext/ad/predict/enricher/ad_uni_predict_fused_enricher.h"

#include <chrono>
#include <map>
#include <unordered_map>
#include <utility>
#include <vector>

#include "teams/ad/ad_nn/common_idt_server/common_idt_reader.h"
#include "teams/ad/ad_nn/common_idt_server/common_idt_utils.h"
#include "teams/ad/ad_nn/feature/ad_log_adaptor.h"
#include "teams/ad/ad_nn/feature/extractor_utils.h"
#include "teams/ad/ad_nn/feature/protocol/feature_accessor/protobuf_accessor.h"
#include "teams/ad/ad_nn/model/debug_util.h"
#include "teams/ad/ad_nn/model/emp_embedding.h"
#include "teams/ad/ad_nn/model/hybrid_embedding.h"
#include "teams/ad/ad_nn/model/mio_btq_embedding.h"
#include "teams/ad/ad_nn/model/model_loader/kai_btq_model_rollbacker.h"
#include "teams/ad/ad_nn/service/service_util.h"
#include "teams/ad/ad_nn/utils/const.h"

#include "teams/reco-arch/uni-predict-v2/src/model_loader/mio_tf/kai_dense_model_manager.h"
#include "teams/reco-arch/uni-predict-v2/src/utils/config_import.h"
#include "teams/reco-arch/uni-predict-v2/src/utils/kai_types.h"
#include "teams/reco-arch/uni-predict-v2/src/utils/utils_cpu.h"

#include "teams/reco-arch/embedding_manager/utils/reused_pb_arena.h"

#include "infra/falcon_counter/src/falcon/counter.h"
#include "ks/base/container/common.h"
#include "serving_base/util/scope_exit.h"

#define CHECK_RETURN_FALSE(cond, msg) \
  do {                                \
    if (!(cond)) {                    \
      LOG(ERROR) << (msg);            \
      return false;                   \
    }                                 \
  } while (0)

DEFINE_bool(force_disable_model_update_debug_only, false,
            "disable btq model param update, read model from shm, DEBUG OBLY, DON'T USE IN PRODUCTION");
DEFINE_bool(skip_check_meta_and_feature, false,
            "skip check meta and feature, DEBUG OBLY, DON'T USE IN PRODUCTION");

namespace reco_arch {
namespace uni_predict {
DECLARE_string(model_load_local_path);
DECLARE_int32(mio_tf_dnn_model_version_buffer_size);
}  // namespace uni_predict
}  // namespace reco_arch

namespace ks {
namespace ad_nn {
DECLARE_bool(zero_for_miss_sign);
DECLARE_bool(deploy_model_name_as_kess_name);
}  // namespace ad_nn
namespace platform {

DECLARE_int32(btq_read_batch_size);
DECLARE_int32(btq_model_max_lag);

namespace {
ad_nn::AdTensorInputConfig ConstructAdTensorInputConfig(const std::string &name, int dim, bool common) {
  ad_nn::AdTensorInputConfig ad_tensor_input;
  ad_tensor_input.input_name = name;
  ad_tensor_input.dim = dim;
  ad_tensor_input.dtype = "float32";
  ad_tensor_input.expand = 1;
  ad_tensor_input.common = common;
  return ad_tensor_input;
}
ad_nn::AdSlotConfig ConstructAdSlotConfig(const std::string &name, int field, const std::string &category,
                                          int size, int capacity, int remap_slot, int prefix, bool use_sign) {
  ad_nn::AdSlotConfig asc;
  asc.feature_name = name;
  asc.field = field;
  asc.category = category;
  asc.size = size;
  asc.capacity = capacity;
  asc.remap_slot = remap_slot;
  asc.prefix = prefix;
  asc.use_sign = use_sign;
  return asc;
}
std::string LogTensor(const void *ptr, int item_count, int dim, bool common) {
  const float *data = reinterpret_cast<const float *>(ptr);
  std::ostringstream oss;
  oss << "{";
  int count = common ? 1 : item_count;
  for (int i = 0; i < count; ++i) {
    auto tmp = data + i * dim;
    oss << "[";
    for (int j = 0; j < dim; ++j) {
      if (isfinite(tmp[j])) {
        oss << tmp[j] << ", ";
      } else {
        oss << "invalid, ";
      }
    }
    oss << "], ";
  }
  oss << "}";
  return oss.str();
}
}  // namespace

void AdUniPredictFusedInternalEnricher::Enrich(MutableRecoContextInterface *context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
  if (use_protocol_accessor_) {
    auto feature_accessor_ptr =
        context->GetPtrCommonAttr<ad_nn::protocol::InferFeatureAccessorInterface>(dragon_input_feature_list_);
    if (!feature_accessor_ptr) {
      LOG(ERROR) << "features protocol is not set";
      context->SetExecutionStatus(ExecutionStatus::TERMINATED);
      return;
    }
    EnrichWithProtocolAccessor(feature_accessor_ptr, context, begin, end);
  } else {
    auto *features_ptr = context->GetPtrCommonAttr<ad_nn::InferFeatureList>(dragon_input_feature_list_);
    if (!features_ptr) {
      CL_LOG(ERROR) << "no features";
      context->SetExecutionStatus(ExecutionStatus::TERMINATED);
      return;
    }
    auto accessor_ptr = std::make_unique<ad_nn::protocol::PBFeatureAccessor>(features_ptr);
    EnrichWithProtocolAccessor(accessor_ptr.get(), context, begin, end);
  }
}

void AdUniPredictFusedInternalEnricher::EnrichWithProtocolAccessor(
    const ad_nn::protocol::InferFeatureAccessorInterface *feature_accessor_ptr,
    MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end) {
  // 这里是 ad 的 infer context, 也是以指针的形式放在了 dragon 的 common attr 中
  const ad_nn::InferRequestContext *infer_context = nullptr;
  std::shared_ptr<ad_nn::InferRequestContext> shared_infer_context = nullptr;
  if (!use_fused_infer_and_feature_) {
    infer_context =
        context->GetPtrCommonAttr<ad_nn::InferRequestContext>(ad_nn::constant::DragonInputInferRequest);
  } else {
    shared_infer_context = std::make_shared<ad_nn::InferRequestContext>();
    infer_context = shared_infer_context.get();
  }
  if (!infer_context) {
    CL_LOG(ERROR) << "infer_request_context is null.";
    return;
  }

  // TODO(jiabw): valid req and model instance
  int num_items = feature_accessor_ptr->ItemCount();
  if (num_items == 0) {
    CL_LOG(INFO) << "skip as reco result is empry.";
    return;
  }

  thread_local std::vector<ad_nn::InferFeatureInfo> user_feature_infos;
  thread_local std::vector<ad_nn::InferFeatureInfo> item_feature_infos;
  thread_local std::vector<uint64_t> item_ids;
  user_feature_infos.clear();
  item_feature_infos.clear();
  item_ids.clear();

  int user_feature_count = feature_accessor_ptr->UserFeatureCount();
  int item_feature_count = feature_accessor_ptr->ItemFeatureCount();
  int received_field_count = user_feature_count + item_feature_count;

  // 使用到的特征数和发送来的特征数应该一致
  if (KS_UNLIKELY(used_field_count_ != received_field_count)) {
    falcon::Inc("dnn_predict_server.invalid_feature_count", 1);
    CL_LOG(ERROR) << "features invalid, field_count: " << used_field_count_
                  << ", item_feature_size: " << item_feature_count
                  << ", user_feature_size: " << user_feature_count;
    return;
  }
  for (size_t i = 0; i < user_feature_count; ++i) {
    const char *user_feature_name = feature_accessor_ptr->UserFeatureName(i);  // 获取 string 内容
    const auto &iter = feature_infos_.find(user_feature_name);
    if (KS_UNLIKELY(iter == feature_infos_.end())) {
      falcon::Inc("dnn_predict_server.invalid_user_feature", 1);
      CL_LOG(ERROR) << "got invalid user feature: " << user_feature_name;
      return;
    }
    user_feature_infos.emplace_back(iter->second);
  }
  for (size_t i = 0; i < item_feature_count; ++i) {
    auto item_feature_name = feature_accessor_ptr->ItemFeatureName(i);
    const auto &iter = feature_infos_.find(item_feature_name);
    if (KS_UNLIKELY(iter == feature_infos_.end())) {
      falcon::Inc("dnn_predict_server.invalid_item_feature", 1);
      CL_LOG(ERROR) << "got invalid item feature: " << item_feature_name;
      return;
    }
    item_feature_infos.emplace_back(iter->second);
  }
  feature_accessor_ptr->GetItemId(&item_ids);

  if (infer_context->debug_kv_collector) {
    CollectDebugInfo(item_ids, feature_accessor_ptr, user_feature_infos, item_feature_infos, infer_context);
  }

  auto ret = MakePrediction(context, begin, end, num_items, feature_accessor_ptr, user_feature_infos,
                            item_feature_infos, item_ids, infer_context,
                            /*is_valid_req*/ true, /*model_instance*/ nullptr);
  if (!ret) {
    CL_LOG(ERROR) << "predict failed.";
    return;
  }
}

bool AdUniPredictFusedInternalEnricher::MakePrediction(
    MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end, int num_items,
    const ad_nn::protocol::InferFeatureAccessorInterface *feature_accessor,
    const std::vector<ad_nn::InferFeatureInfo> &user_feature_infos,
    const std::vector<ad_nn::InferFeatureInfo> &item_feature_infos, const std::vector<uint64_t> &item_ids,
    const ad_nn::InferRequestContext *infer_context, bool is_valid_req,
    reco_arch::uni_predict::ModelInstance *model_instance) {
  std::shared_ptr<ad_nn::FieldLookupContext> lookup_context =
      concurrent_embedding_->PrefetchRemoteEmbedding(feature_accessor, user_feature_infos, item_feature_infos,
                                                     max_field_size_, 0, nullptr, 0, enable_run_sub_graph_);

  auto fill_input = [&](const reco_arch::uni_predict::PreallocatedTensors &preallocated)
      -> reco_arch::uni_predict::CallbackStatus {
    LoggingUtil::ResetLoggingEnabled(context->GetUserId(), context->GetDeviceId());
    return FillInput(context, begin, end, num_items, feature_accessor, user_feature_infos, item_feature_infos,
                     item_ids, infer_context, lookup_context, preallocated);
  };

  auto fetch_output = [&](const reco_arch::uni_predict::PreallocatedTensors &preallocated)
      -> reco_arch::uni_predict::CallbackStatus {
    LoggingUtil::ResetLoggingEnabled(context->GetUserId(), context->GetDeviceId());
    return FetchOutput(context, begin, end, num_items, preallocated);
  };

  reco_arch::uni_predict::CallbackStatus status = reco_arch::uni_predict::CallbackStatus::OK;
  reco_arch::uni_predict::Notification done;
  reco_arch::uni_predict::Notification fill_done;
  serving_base::Timer timer;
  auto task = std::make_unique<reco_arch::uni_predict::BasicBatchingTask>();
  task->InitDoneNotification(&done);
  task->InitStatus(&status);
  task->fill_done_ = &fill_done;
  task->batch_wait_ = fill_input;
  task->fetch_output_ = fetch_output;
  task->item_size_ = num_items;
  task->timer_ = &timer;
  if (!task->TaskReady()) {
    CL_LOG(ERROR) << "BasicBatchingTask not properly set.";
    return false;
  }
  timer.Start();

  if (!batch_system_->Schedule(std::move(task))) {
    CL_LOG(ERROR) << "Task Schedule error.";
    return false;
  }
  done.WaitForNotification();
  CL_LOG(INFO) << timer.display();
  return true;
}

reco_arch::uni_predict::CallbackStatus AdUniPredictFusedInternalEnricher::FillInput(
    MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end, int num_items,
    const ad_nn::protocol::InferFeatureAccessorInterface *feature_accessor,
    const std::vector<ad_nn::InferFeatureInfo> &user_feature_infos,
    const std::vector<ad_nn::InferFeatureInfo> &item_feature_infos, const std::vector<uint64_t> &item_ids,
    const ad_nn::InferRequestContext *infer_context,
    std::shared_ptr<ad_nn::FieldLookupContext> lookup_context,
    const reco_arch::uni_predict::PreallocatedTensors &preallocated) {
  serving_base::Timer timer;
  for (const auto &[tensor_name, flat_tensor] : preallocated.tensors) {
    memset(flat_tensor.ptr, 0, flat_tensor.num_bytes);
  }

  ad_nn::RecoFlatTensorList input_flat_tensors;
  input_flat_tensors.reserve(used_field_count_);
  // NOTE: field_input_names_ 中 tensor_idx 的顺序和 feature_infos_中 tensor_idx
  // 的顺序是对应的
  for (const auto &[feature_name, idx] : field_input_names_) {
    auto iter = preallocated.tensors.find(feature_name);
    if (iter == preallocated.tensors.end()) {
      LOG(ERROR) << "cannot find tensor: `" << feature_name << "` in feature file.";
      continue;
    }
    const auto &flat_tensor = iter->second;
    input_flat_tensors.emplace_back(feature_name, reinterpret_cast<float *>(flat_tensor.ptr));
  }

  timer.Start();
  ad_nn::EmbeddingData embedding_data;
  embedding_data.reco_flat_addrs = &input_flat_tensors;
  auto cur_concurrent_embedding = concurrent_embedding_;
  if (use_double_buffer_loader_) {
    ::thread::ReaderAutoLock lk(&double_buffer_model_param_mutex_);
    cur_concurrent_embedding = concurrent_embedding_;
  }
  auto fill_data = cur_concurrent_embedding->FillDatas(feature_accessor, user_feature_infos,
                                                       item_feature_infos, &embedding_data, max_field_size_,
                                                       0, nullptr, 0, lookup_context, enable_run_sub_graph_);

  auto duration = timer.Interval();
  falcon::Stat("dnn_predict_server.infer_fill_tensor_cost", duration);
  if (!fill_data) {
    falcon::Inc("dnn_predict_server.infer_fill_tensor_fail", 1);
    CL_LOG(ERROR) << "Failed to fill embedding.";
    context->SetIntCommonAttr(ad_nn::constant::DragonInferStatus, ad_nn::InferStatus::EMP_ERROR);
    // if EMP_ERROR do not infer anymore
    context->SetExecutionStatus(ExecutionStatus::TERMINATED);
    return reco_arch::uni_predict::CallbackStatus::ERROR;
  }

  ks::ad_nn::debug::CollectEmbeddings(ad_field_configs_, item_ids, input_flat_tensors, feature_embed_len_,
                                      false, used_sparse_field_count_, infer_context->debug_kv_collector);
  return reco_arch::uni_predict::CallbackStatus::OK;
}

reco_arch::uni_predict::CallbackStatus AdUniPredictFusedInternalEnricher::FetchOutput(
    MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end, int num_items,
    const reco_arch::uni_predict::PreallocatedTensors &preallocated) {
  auto infer_status = ad_nn::InferStatus::OK;
  auto on_exit =
      base::ScopeExit([&]() { context->SetIntCommonAttr(ad_nn::constant::DragonInferStatus, infer_status); });

  if (preallocated.device.type != reco_arch::uni_predict::DeviceType::CPU) {
    CL_LOG(ERROR) << "output buffer should be in host memory.";
    infer_status = ad_nn::InferStatus::GPU_INFER_ERROR;
    return reco_arch::uni_predict::CallbackStatus::ERROR;
  }

  for (const auto &[tensor_name, attr_names] : outputs_) {
    auto iter = preallocated.tensors.find(tensor_name);
    if (iter == preallocated.tensors.end()) {
      CL_LOG(WARNING) << "cannot find output tensor in preallocated tensors: " << tensor_name;
      continue;
    }
    const reco_arch::uni_predict::TensorMeta *tensor_meta = nullptr;
    for (const auto &meta : io_tensor_info_.output_meta) {
      if (meta.tensor_name == tensor_name) {
        tensor_meta = &meta;
        break;
      }
    }

    if (tensor_meta->dim.size() != 1) {
      CL_LOG(ERROR) << "multi-dimensional output is not supported yet, tensor_name: " << tensor_name
                    << ", dim: " << tensor_meta->dim.size();
      continue;
    }
    // 多个 attr 对应单个 tensor output
    // ad 目前把所有的输出全部存在 common attr 了.
    auto flat_tensor = iter->second;
    auto req_id = context->GetRequestId();
    auto dim = tensor_meta->dim[0];
    for (const auto &attr_name : attr_names) {
      if (flat_tensor.dtype == reco_arch::uni_predict::DataType::FLOAT32) {
        float *data = static_cast<float *>(flat_tensor.ptr);
        context->SetPtrCommonAttr(attr_name, data);
        context->SetIntCommonAttr(attr_name + "_layer_len", dim);
      } else if (flat_tensor.dtype == reco_arch::uni_predict::DataType::INT32) {
        int32_t *data = static_cast<int32_t *>(flat_tensor.ptr);
        context->SetPtrCommonAttr(attr_name, data);
        context->SetIntCommonAttr(attr_name + "_layer_len", dim);
      } else {
        CL_LOG_ERROR("uni_predict", "output_with_unsupport_shape_" + attr_name)
            << "Output only support int32 and float32, but got: " << AsInteger(flat_tensor.dtype)
            << ", attr_name: " << attr_name;
      }
    }
  }
  return reco_arch::uni_predict::CallbackStatus::OK;
}

bool AdUniPredictFusedInternalEnricher::InitProcessor() {
  // processor 初始化从逻辑上来讲应该有如下几个方面
  // 1. embedding 存储和 embedding 拉取的 fetcher
  // 2. 预估相关的包括 batching system 和 predictor
  // 3. dense 和 sparse 特征管理相关的, idt/rollback 等

  model_cmd_ = ad_nn::GetKcsModelCmd();

  model_meta_path_ = processor_config_->GetString("model_meta_path");
  CHECK_RETURN_FALSE(!model_meta_path_.empty(),
                     "`AdUniPredictFusedInternalEnricher` model_meta_path is empty.");
  model_root_path_ = model_meta_path_.substr(0, model_meta_path_.rfind("/"));

  // 设置 validator 相关路径配置
  std::string hdfs_root = processor_config_->GetString("hdfs_root", "");
  std::string ps_root = processor_config_->GetString("ps_root", "");
  if (hdfs_root.empty() || ps_root.empty()) {
    LOG(ERROR) << "hdfs_root or ps_root is empty";
    return false;
  }
  ad_nn::MicroBatchModelValidate::GetInstance()->SetPath(hdfs_root, ps_root);
  ad_nn::MicroBatchModelValidate::GetInstance()->Start();

  queue_prefix_ = processor_config_->GetString("queue_prefix");
  model_key_ = processor_config_->GetString("key", queue_prefix_);
  use_fused_infer_and_feature_ = processor_config_->GetBoolean("use_fused_infer_and_feature", false);
  LOG(INFO) << "cmd: " << model_cmd_ << ", model_meta_path: " << model_meta_path_
            << ", model_root_path: " << model_root_path_ << ", queue_prefix: " << queue_prefix_
            << ", model_key: " << model_key_;
  dragon_input_feature_list_ =
      processor_config_->GetString("dragon_input_feature_list", ad_nn::constant::DragonInputFeatureList);

  use_double_buffer_loader_ = processor_config_->GetBoolean("use_double_buffer_loader", false);

  auto feature_path = processor_config_->GetString("feature_path", "");
  use_bs_fast_feature_ = processor_config_->GetBoolean("use_bs_fast_feature", false);
  use_protocol_accessor_ = processor_config_->GetBoolean("use_protocol_accessor", false);
  LOG(INFO) << "feature_path: " << feature_path << ", use_bs_fast_feature: " << use_bs_fast_feature_
            << ", use_protocol_accessor: " << use_protocol_accessor_;
  if (!feature_path.empty() && !CheckFeatureFileVersion(feature_path)) {
    LOG(ERROR) << "Feature file version check failed.";
    return false;
  }
  auto dragon_feature_config = processor_config_->Get("dragon_feature_config");
  ad_nn::FeatureFileInfo::LoadFeatureFile(feature_path, use_bs_fast_feature_, &feature_file_info_, true,
                                          false, "", dragon_feature_config);

  // 加载 graph，确定 graph type
  CHECK_RETURN_FALSE(LoadGraph(), "`AdUniPredictFusedInternalEnricher` load graph failed.");

  // 使用 emp_service 初始化 emp_shared_infos_和 field_embedding_infos_
  CHECK_RETURN_FALSE(InitEmpServiceConfig(),
                     "`AdUniPredictFusedInternalEnricher` init EmpServiceCondig failed.");

  // feature
  CHECK_RETURN_FALSE(InitFeatureFieldConfigs(),
                     "`AdUniPredictFusedInternalEnricher` init FeatureFieldConfigs failed.");

  // outputs, params, dense_model_manager
  CHECK_RETURN_FALSE(InitModelConfig(), "`AdUniPredictFusedInternalEnricher` init model configs failed.");

  // 使用 emp_service::local 下的配置初始化 mio_btq_embedding_ 和 nohash_btq_embedding_
  CHECK_RETURN_FALSE(InitEmbeddingStorage(),
                     "`AdUniPredictFusedInternalEnricher` init embedding storage failed.");

  // 初始化用于 embedding 更新的 BTQ 或 hdfs 文件读取器
  CHECK_RETURN_FALSE(InitEmbeddingUpdater(),
                     "`AdUniPredictFusedInternalEnricher` init embedding updater failed.");

  // 初始化 emp_service_ 和 concurrent_embedding_, 依赖 InitFeatureFieldConfigs
  CHECK_RETURN_FALSE(InitEmbeddingFetcher(),
                     "`AdUniPredictFusedInternalEnricher` init embedding fetcher failed.");

  // 初始化 idt_model_loader_, btq_reader_ 和 rollbacker_
  CHECK_RETURN_FALSE(InitIdtAndModelRollbacker(),
                     "`AdUniPredictFusedInternalEnricher` init idt and robacker faield.");

  // 初始化 predictor_ 和 batch_system_, 依赖 idt 和 rollbacker 拉取 sparse 和 dense 参数
  CHECK_RETURN_FALSE(InitUniPredict(), "`AdUniPredictFusedInternalEnricher` init uni-predict failed.");

  // 初始化的最后做一些校验
  CHECK_RETURN_FALSE(FLAGS_skip_check_meta_and_feature || CheckMetaAndFeature(),
                     "`AdUniPredictFusedInternalEnricher` check meta and feature failed.");
  return true;
}

bool AdUniPredictFusedInternalEnricher::InitFeatureFieldConfigs() {
  for (const auto &feature : feature_file_info_.features) {
    auto category = feature.category;
    auto feature_type = reco_arch::uni_predict::GetFeatureTypeFromName(category);
    bool is_user = reco_arch::uni_predict::IsUserFeature(feature_type);
    bool feature_used = feature.feature_in_sub_graph;
    if (feature.is_dense) {
      total_dense_field_count_ += 1;
      used_dense_field_count_ += feature_used ? 1 : 0;
    } else {
      size_t feature_count = feature.prefix_name.empty() ? 1 : feature.prefix_name.size();
      total_sparse_field_count_ += feature_count;
      used_sparse_field_count_ += feature_used ? feature_count : 0;
    }
  }
  total_field_count_ = total_dense_field_count_ + total_sparse_field_count_;
  used_field_count_ = used_dense_field_count_ + used_sparse_field_count_;
  LOG(INFO) << "total_field_count: " << total_field_count_
            << ", total_dense_field_count: " << total_dense_field_count_
            << ", total_sparse_field_count: " << total_sparse_field_count_
            << ", used_field_count: " << used_field_count_
            << ", used_dense_field_count: " << used_dense_field_count_
            << ", used_sparse_field_count: " << used_sparse_field_count_;

  // 直接使用所有的 features 创建 ad_field_configs, 期间给每个 input 重新赋予一个新的编号作为 remap_slot
  // 这里没做任何的 input 融合,因此每个 feature 对应一个 input
  size_t remap_slot_index = 0;
  size_t sparse_index = 0;
  for (auto &feature : feature_file_info_.features) {
    if (!feature.feature_in_sub_graph) {
      LOG(WARNING) << "model: " << model_key_ << ", skip feature not in sub_graph: " << feature.class_name;
      enable_run_sub_graph_ = true;
      continue;
    }
    std::string input_name = feature.class_name;
    // BS 的去掉 BS 前缀 "BS"
    if (!feature.is_dragon && use_bs_fast_feature_) {
      input_name = input_name.substr(2);
    }
    auto category = feature.category;
    auto feature_type = reco_arch::uni_predict::GetFeatureTypeFromName(category);
    bool is_user = reco_arch::uni_predict::IsUserFeature(feature_type);
    uint32_t dim = 0;

    /// 处理 dense 的情况
    if (feature.is_dense) {
      // dense input 的 dim 是 size
      dim = feature.field_size + 1;
      auto ad_input_config = ConstructAdTensorInputConfig(input_name, dim, is_user);

      auto remap_slot = remap_slot_index++;
      // dense input 的 field_index 没啥意义
      bool use_sign =
          !feature.is_dense || (feature.is_dense && ks::ad_nn::IsFeatureIntValue(feature.data_type));
      auto asc = ConstructAdSlotConfig(input_name, feature.field_index, category, feature.field_size,
                                       feature.field_capacity, remap_slot, 0, use_sign);

      ad_input_config.ad_slot_configs.push_back(asc);
      ad_field_configs_[input_name] = ad_input_config;
      continue;
    }
    /// 处理 sparse 的情况
    if (feature.prefix_name.size() > 0) {
      for (size_t i = 0; i < feature.prefix_name.size(); ++i) {
        auto field = feature.prefix_index[i];
        dim = GetEmbeddingDim(field, emp_shard_infos_);
        auto size = feature.prefix_size[i];
        auto prefix_name = feature.prefix_name[i];

        std::string real_input_name = input_name + "_" + std::to_string(static_cast<int>(prefix_name));
        auto ad_input_config = ConstructAdTensorInputConfig(real_input_name, dim, is_user);

        auto remap_slot = remap_slot_index++;

        auto asc = ConstructAdSlotConfig(real_input_name, field, category, size, size - 1, remap_slot,
                                         static_cast<int>(prefix_name), true);
        ad_input_config.ad_slot_configs.push_back(asc);
        ad_field_configs_[real_input_name] = ad_input_config;
      }
    } else {
      dim = GetEmbeddingDim(feature.field_index, emp_shard_infos_);
      auto ad_input_config = ConstructAdTensorInputConfig(input_name, dim, is_user);

      auto remap_slot = remap_slot_index++;
      auto asc = ConstructAdSlotConfig(input_name, feature.field_index, category, feature.field_size,
                                       feature.field_capacity, remap_slot, 0, true);
      ad_input_config.ad_slot_configs.push_back(asc);
      ad_field_configs_[input_name] = ad_input_config;
    }
  }
  max_field_size_ = total_field_count_;
  LOG(INFO) << "max_field_size: " << max_field_size_ << ", enable_run_sub_graph: " << enable_run_sub_graph_;

  // 这里处理的都是被使用到的 feature
  field_input_names_.resize(used_field_count_);
  feature_embed_len_.resize(used_field_count_, 0);
  int auto_sparse_index = 0;
  for (const auto &[input_name, ad_input_tensor] : ad_field_configs_) {
    auto tensor_len = ad_input_tensor.dim * ad_input_tensor.expand;
    // 因为没做任何 input 优化,这里只可能是一个
    CHECK_EQ(ad_input_tensor.ad_slot_configs.size(), 1);
    const auto &config = ad_input_tensor.ad_slot_configs[0];

    LOG(INFO) << "AdTensorInputConfig, [name: " << ad_input_tensor.input_name
              << ", dim: " << ad_input_tensor.dim << ", is_common: " << ad_input_tensor.common
              << ", tensor_len: " << tensor_len << "], SlotConfig, [name: " << config.feature_name
              << ", field: " << config.field << ", category: " << config.category << ", size: " << config.size
              << ", capacity: " << config.capacity << ", remap_slot: " << config.remap_slot
              << ", prefix: " << config.prefix << "]";

    bool is_user = ad_nn::IsUser(config.category);
    bool is_sparse = ad_nn::IsSparse(config.category);
    ad_nn::InferFeatureInfo info;
    info.field_idx = config.field;
    info.tensor_idx = config.remap_slot;
    info.tensor_offset = 0;
    info.field_tensor_len = tensor_len;
    info.is_user = is_user;
    info.is_dense = !is_sparse;
    info.tensor_len = tensor_len;
    info.feature_in_meta = true;
    info.use_sign = config.use_sign;
    if (is_sparse) {
      // 从 emp_service 中更新一下 embedding_len, op 和 CONCAT op 类型的 tensor length
      for (const auto &[shard, field_embedding_infos] : emp_shard_infos_) {
        auto iter = field_embedding_infos.find(config.field);
        if (iter == field_embedding_infos.end()) {
          continue;
        }
        info.embedding_len = iter->second.embedding_len;
        info.op = iter->second.op;
        if (info.op == ad_nn::EmbeddingOperation::CONCAT) {
          info.tensor_len = iter->second.concat_len;
          info.field_tensor_len = iter->second.concat_len;
        }
        break;
      }
    }
    feature_infos_.emplace(config.feature_name, info);

    field_input_names_[config.remap_slot] = {config.feature_name, 0};
    feature_embed_len_[config.remap_slot] = info.tensor_len;
    if (is_sparse) {
      auto feature_type = ad_algorithm::GetFeatureTypeFromName(config.category);
      sparse_ret_indices_[feature_type].push_back(config.field);
    }
  }
  for (const auto &[feature_name, info] : feature_infos_) {
    LOG(INFO) << "feature: " << feature_name << ", info: [field_idx: " << info.field_idx
              << ", tensor_idx: " << info.tensor_idx << ", tensor_offset: " << info.tensor_offset
              << ", field_tensor_len: " << info.field_tensor_len << ", tensor_len: " << info.tensor_len
              << ", embedding_len: " << info.embedding_len << ", user: " << info.is_user
              << ", dense: " << info.is_dense << ", use_sign: " << info.use_sign << "]";
  }

  // 这两个初始化的变量会被 ConcurrentEmbedding 使用, 初始化为全量的 feature 特征
  sparse_field_sizes_.resize(total_sparse_field_count_);
  embedding_vector_lens_.resize(total_sparse_field_count_);
  for (const auto &[shard, field_embedding_info_map] : emp_shard_infos_) {
    for (const auto &[field, field_embedding_info] : field_embedding_info_map) {
      if (field_embedding_info.op == ad_nn::EmbeddingOperation::CONCAT) {
        embedding_vector_lens_[field] = field_embedding_info.concat_len;
      } else {
        embedding_vector_lens_[field] = field_embedding_info.embedding_len;
      }
      sparse_field_sizes_[field] = field_embedding_info.field_size;
    }
  }
  return true;
}

bool AdUniPredictFusedInternalEnricher::InitUniPredict() {
  // 初始化 predictor 和 batching system, 依赖 idt
  auto model_loader_config = processor_config_->Get("model_loader_config");
  if (!model_loader_config) {
    LOG(ERROR) << "model_loader_config is required.";
    return false;
  }
  auto model_loader_type = model_loader_config->GetString("type", "KaiTFExecutedByTensorRTModelLoader");
  if (model_loader_type.empty()) {
    LOG(ERROR) << "model_loader_config::type is required.";
    return false;
  }
  std::string service_name;
  if (use_fused_infer_and_feature_) {
    // 一体化部署采用 _predict_server 结尾的 kess name
    service_name = ad_nn::GetKcsKessName();
  } else {
    // 分开部署采用 _infer_server 结尾的 kess name
    service_name = ad_nn::GetInferServerKessName("");
  }
  if (service_name.empty()) {
    service_name = "grpc_" + GlobalHolder::GetServiceIdentifier();
  }
  base::Json config_json(base::StringToJson(processor_config_->ToString()));
  LOG(INFO) << "kess_name: " << service_name;
  config_json.set("__kess_name__", service_name);
  config_json.set("idt_skip_init_weights_load", idt_success_);
  predictor_ = reco_arch::uni_predict::Predictor::GetOrCreateInstance(model_key_);
  // 正常模式 idt 成功，使用 idt_weights_ 更新模型
  // 回滚模式 idt 成功，使用 rollbacker_ 拿到的权重更新模型
  // idt 失败，正常启动
  LOG(INFO) << "start init predictor, idt status: " << (idt_success_ ? "success" : "fail");
  if (idt_success_ && !use_double_buffer_loader_) {
    if (ad_nn::BtqModelLoader::loader_info_.GetCurrentRollbackMode()) {
      auto rollback_embedding = rollbacker_->GetNohashEmbedding();
      if (!predictor_->SetUpdateStatus(false)) {
        LOG(ERROR) << "stop uni-predict auto update failed.";
        return false;
      }
      predictor_->Initialize(true, config_json.ToString(), model_loader_type);
      concurrent_embedding_->SetEmbeddingStorage(rollback_embedding);
      rollback_model_.store(true);
      ad_nn::BtqModelLoader::loader_info_.Set(rollbacker_->GetLoaderId(), rollbacker_->GetCurrentVersion(),
                                              true);
      LOG(INFO) << "embedding switch to rollback model with hybrid embedding.";
    } else {
      predictor_->Initialize(true, config_json.ToString(), model_loader_type);
    }
  } else {
    // 双 Buffer 模型禁用 BTQ 更新
    predictor_->Initialize(!use_double_buffer_loader_ && !FLAGS_force_disable_model_update_debug_only,
                           config_json.ToString(), model_loader_type);
  }
  // 注册 IDT 回调
  auto idt_func = [&]() -> bool {
    if (!idt_data_) {
      LOG(ERROR) << "idt_data is nullptr";
      return false;
    }
    idt_data_->UpdateModelWeights();
    return true;
  };
  predictor_->SetIdtFunc(idt_func);
  // BTQ 模型启动回滚参数监控线程
  if (rollbacker_ && !rollbacker_->StartBackgroundWork(queue_prefix_)) {
    LOG(ERROR) << "start rollbacker background work failed, rollbacker_id: " << rollbacker_->GetLoaderId();
    return false;
  }
  // 双 Buffer 模型启动后台参数更新监控线程
  if ((serving_buffer_ && !serving_buffer_->StartBackgroundWork(queue_prefix_)) ||
      (not_serving_buffer_ && !not_serving_buffer_->StartBackgroundWork(queue_prefix_))) {
    LOG(ERROR) << "start double buffer loader background work failed";
    return false;
  }
  batch_system_ = reco_arch::uni_predict::BatchingSystem::GetOrCreateInstance(model_key_);
  reco_arch::uni_predict::BatchingSystemOption options;
  auto batching_config = processor_config_->Get("batching_config");
  if (!batching_config) {
    LOG(ERROR) << "batching_config is required.";
    return false;
  }
  options.alignment = batching_config->GetInt("alignment", 256);
  options.batch_timeout_micros = batching_config->GetInt("batch_timeout_micros", 8000);
  options.max_enqueued_batches = batching_config->GetInt("max_enqueued_batches", 16);
  options.max_batch_size = batching_config->GetInt("max_batch_size", 512);
  options.process_thread_num = batching_config->GetInt("process_thread_num", 0);
  options.cpu_only = false;
  options.model_key = model_key_;

  if (model_loader_type == "KaiTFExecutedByTensorRTModelLoader") {
    options.type = "BasicBatchingTask";
    options.task_type = reco_arch::uni_predict::BatchTaskType::kBasicBatchingTask;
  } else if (model_loader_type == "KaiTFExecutedByTensorFlowModelLoader") {
#ifdef USE_GPU
    options.type = "BasicBatchingTask";
    options.task_type = reco_arch::uni_predict::BatchTaskType::kBasicBatchingTask;
#else
    options.type = "BatchTensorflowTask";
    options.task_type = reco_arch::uni_predict::BatchTaskType::kBatchTensorflowTask;
#endif
  } else if (model_loader_type == "KaiTFExecutedByOnnxTRTModelLoader") {
    options.type = "BasicBatchingTask";
    options.task_type = reco_arch::uni_predict::BatchTaskType::kBasicBatchingTask;
  } else {
    LOG(ERROR) << "model_loader_type: " << model_loader_type << " is not support yet.";
    return false;
  }

  batch_system_->Initialize(options, predictor_);

  if (!predictor_->GetCurrentExecutor()->GetIOTensorInfo(&io_tensor_info_)) {
    LOG(ERROR) << "Get io_tensor_info failed.";
    return false;
  }
  for (const auto &input_param : io_tensor_info_.input_meta) {
    LOG(INFO) << "io_tensor_input, name: " << input_param.tensor_name << ", common: " << input_param.common
              << ", dtype: " << input_param.dtype
              << ", dim: " << reco_arch::uni_predict::ListToStr(input_param.dim);
  }
  for (const auto &output_param : io_tensor_info_.output_meta) {
    LOG(INFO) << "io_tensor_output, name: " << output_param.tensor_name << ", common: " << output_param.common
              << ", dtype: " << output_param.dtype
              << ", dim: " << reco_arch::uni_predict::ListToStr(output_param.dim);
  }
  for (const auto &batch_size : io_tensor_info_.buffer_batchsize) {
    LOG(INFO) << "io_tensor_batch_size: " << batch_size;
  }
  // load delay 上报
  report_model_load_delay_thread_ = std::thread([this]() { ReportModelLoadDelay(); });
  return true;
}

bool AdUniPredictFusedInternalEnricher::InitEmbeddingFetcher() {
  ad_nn::FeatureOrgLayout layout = ad_nn::FeatureOrgLayout::BY_FIELD;

  auto embed_fetcher_config = processor_config_->Get("embedding_fetcher_config");
  bool reordering_feature = false;
  int exponent_per_bucket = 12;
  bool weights_in_tensor = false;
  ad_nn::CombineType combine_type = ad_nn::CombineType::kSum;

  if (embed_fetcher_config) {
    reordering_feature = embed_fetcher_config->GetBoolean("reordering_feature", false);
    exponent_per_bucket = embed_fetcher_config->GetInt("exponent_per_bucket", 12);
    weights_in_tensor = embed_fetcher_config->GetBoolean("weights_in_tensor", false);
    combine_type = static_cast<ad_nn::CombineType>(embed_fetcher_config->GetInt("combine_type", 0));
    layout = static_cast<ad_nn::FeatureOrgLayout>(embed_fetcher_config->GetInt("in_fea_org_layout", 1));
  }

  // use sparse_ret_indices_
  fea_meta_.type2field_indices = sparse_ret_indices_;
  fea_meta_.feature_layout = layout;

  // 这里 model_name 传入空值, 会根据当前 CMD 自动生成 kess_name
  // NOTE: 不确定这里 fea_meta_.type2field_indices 是干啥的, 目前传入的是用到的 feature 的 index
  emp_embedding_ = std::make_shared<ad_nn::EmpEmbedding>("", emp_shard_infos_, fea_meta_);
  // NOTE: 这里 sparse_field_sizes_ 和 embedding_vector_lens_ 可能都没用;
  // 在我们的场景下 embedding 查找用的外挂的 nohash_btq_embedding_ 和 emp_embedding_;
  // 目前这两个变量都是传入的全量的 feature 的信息.
  concurrent_embedding_ = std::make_shared<ad_nn::ConcurrentEmbedding>(
      sparse_field_sizes_, embedding_vector_lens_, weights_in_tensor, combine_type, reordering_feature,
      exponent_per_bucket, /*for local*/ nohash_btq_embedding_, /*for remote*/ emp_embedding_, true);
  return true;
}

bool AdUniPredictFusedInternalEnricher::InitEmbeddingStorage() {
  /// 初始化需要本地 cache 的 embedding 的存储结果, 这部分是配置在 emp_service::local 下面的
  auto embed_storage_config = processor_config_->Get("embedding_storage_config");
  int slot_offset = 52;
  uint64_t embedding_capacity_gb = 128;
  if (!embed_storage_config) {
    LOG(WARNING) << "`embedding_storage_config` miss, use default value";
    embedding_node_prefix_ = "embedidng";
    remove_sign_prefix_ = false;
    kai_nohash_model_ = false;
    embedding_cache_type_ = "shm";
    embedding_min_fill_ratio_ = 0.65;
  } else {
    embedding_node_prefix_ = embed_storage_config->GetString("embedding_node_prefix", "embedding");
    remove_sign_prefix_ = embed_storage_config->GetBoolean("remove_sign_prefix", false);
    kai_nohash_model_ = embed_storage_config->GetBoolean("kai_nohash_model", false);
    auto remap_slot_sign_feature = embed_storage_config->Get("remap_slot_sign_feature");
    if (remap_slot_sign_feature && remap_slot_sign_feature->IsObject()) {
      auto output_slot_bit_num = remap_slot_sign_feature->GetInt("output_slot_bit_num", 64 - slot_offset);
      if (output_slot_bit_num >= 64) {
        LOG(WARNING) << "invalid output_slot_bit_num: " << output_slot_bit_num;
      } else {
        slot_offset = 64 - output_slot_bit_num;
        LOG(INFO) << "output_slot_bit_num: " << output_slot_bit_num << ", set slot_offset: " << slot_offset;
      }
    }
    embedding_capacity_gb = embed_storage_config->GetInt("embedding_capacity_gb", 128);
    embedding_cache_type_ = embed_storage_config->GetString("embedding_cache_type", "shm");
    embedding_min_fill_ratio_ = embed_storage_config->GetFloat("embedding_min_fill_ratio", 0.65);
  }
  embedding_capacity_ = embedding_capacity_gb << 30;

  LOG(INFO) << "embedding_node_prefix: " << embedding_node_prefix_
            << ", remove_sign_prefix: " << remove_sign_prefix_ << ", kai_nohash_model: " << kai_nohash_model_
            << ", embedding_cache_type: " << embedding_cache_type_
            << ", embedding_min_fill_ratio: " << embedding_min_fill_ratio_
            << ", embedding_capacity_gb: " << embedding_capacity_gb << ", slot_offset: " << slot_offset;

  mio_btq_embedding_ = std::make_shared<ad_nn::MioBtqEmbedding>(embedding_node_prefix_, slot_offset,
                                                                remove_sign_prefix_, kai_nohash_model_);
  nohash_btq_embedding_ = mio_btq_embedding_;
  nohash_btq_embedding_->SetCapacity(embedding_capacity_);
  auto load_status =
      nohash_btq_embedding_->Load(model_root_path_, field_embedding_infos_, embedding_cache_type_,
                                  embedding_min_fill_ratio_, ad_nn::FLAGS_zero_for_miss_sign);
  if (load_status != 0) {
    LOG(ERROR) << "nohash btq embedding storage init failed.";
    return false;
  }
  return true;
}

bool AdUniPredictFusedInternalEnricher::InitEmbeddingUpdater() {
  /// 初始化模型 local embedding 更新相关
  if (use_double_buffer_loader_) {
    // 双 buffer 模型只允许有一个 shard
    if (emp_shard_infos_.size() != 1) {
      LOG(ERROR) << "double buffer model must has only 1 local embedding shard, but have "
                 << emp_shard_infos_.size() << " shards";
      return false;
    }
    // 双 Buffer 模型从 hdfs 文件更新 embedding
    serving_buffer_ = std::make_shared<ad_nn::KaiBtqModelRollbacker>();
    serving_buffer_->SetModelVersionPath(processor_config_->GetString("model_path"));
    serving_buffer_->SetDoubleBufferModelUpdateCb(
        [&](bool rollback) { return DoubleBufferUpdateCallback(rollback); });
    serving_buffer_->SetModelKey(model_key_);
    serving_buffer_->SetBtqQueueShard("local");
    serving_buffer_->SetLoaderId(0);
    ad_nn::IdtDataSources::GetOrCreateInstance(model_key_)->Put("model_data_0_source", serving_buffer_.get());

    not_serving_buffer_ = std::make_shared<ad_nn::KaiBtqModelRollbacker>();
    not_serving_buffer_->SetModelVersionPath(processor_config_->GetString("model_path"));
    not_serving_buffer_->SetDoubleBufferModelUpdateCb(
        [&](bool rollback) { return DoubleBufferUpdateCallback(rollback); });
    not_serving_buffer_->SetModelKey(model_key_);
    not_serving_buffer_->SetBtqQueueShard("local");
    not_serving_buffer_->SetLoaderId(1);
    ad_nn::IdtDataSources::GetOrCreateInstance(model_key_)
        ->Put("model_data_1_source", not_serving_buffer_.get());

    ad_nn::BtqModelLoader::support_double_buffer_ = true;
    if (!serving_buffer_->Init("", model_root_path_, queue_prefix_, graph_def_, output_op_names_,
                               embedding_node_prefix_, field_embedding_infos_, embedding_capacity_,
                               embedding_cache_type_, embedding_min_fill_ratio_, nullptr, true)) {
      LOG(ERROR) << "serving buffer of double buffer model init failed.";
      return false;
    }
    if (!not_serving_buffer_->Init("", model_root_path_, queue_prefix_, graph_def_, output_op_names_,
                                   embedding_node_prefix_, field_embedding_infos_, embedding_capacity_,
                                   embedding_cache_type_, embedding_min_fill_ratio_, nullptr, true)) {
      LOG(ERROR) << "not_serving buffer of double buffer model init failed.";
      return false;
    }
  } else {
    // 模型 sparse 参数的 btq_reader 初始化
    auto an_hour_ago = std::chrono::high_resolution_clock::now() - std::chrono::hours(1);
    int64_t timestamp_ms =
        std::chrono::duration_cast<std::chrono::microseconds>(an_hour_ago.time_since_epoch()).count();
    if (nohash_btq_embedding_->batch_version_ms() > 0 &&
        nohash_btq_embedding_->batch_version_ms() > timestamp_ms / 1000) {
      timestamp_ms = nohash_btq_embedding_->batch_version_ms() * 1000;
      LOG(INFO) << "start to read btq from recovered version timestamp_ms: " << timestamp_ms;
    }
    std::string btq_queue_name = queue_prefix_ + "_queue_local";

    int btq_model_read_thread_num = 3;
    int btq_model_read_qps = 100;
    int btq_model_worker_num = 8;
    auto btq_reader_config = processor_config_->Get("btq_reader_config");
    if (btq_reader_config) {
      btq_model_read_thread_num = processor_config_->GetInt("btq_model_reader_thread_num", 3);
      btq_model_read_qps = processor_config_->GetInt("btq_model_read_qps", 100);
      btq_model_worker_num = processor_config_->GetInt("btq_model_worker_num", 8);
    }

    base::JsonObject btq_config;
    btq_config.set("btq_queue_name", btq_queue_name);
    btq_config.set("time_offset", timestamp_ms);
    btq_config.set("read_thread_num", btq_model_read_thread_num);
    btq_config.set("read_qps", btq_model_read_qps);
    btq_config.set("worker_num", btq_model_worker_num);
    btq_reader_.SetReadCallback([this](const std::string &msg) { return MioBtqCallback(msg); });
    if (!btq_reader_.Start(btq_config)) {
      LOG(ERROR) << "btq reader start failed.";
      return false;
    }
  }
  return true;
}

bool AdUniPredictFusedInternalEnricher::InitModelConfig() {
  /// 初始化模型相关信息, 包括 input, output, param, dense_model_manager 等
  /// 由于配置中没有 inputs 和 param 字段, 所以这些信息需要从 model_meta_path 和 feature_path 中读取
  // outputs
  auto outputs_config = processor_config_->Get("outputs");
  if (!outputs_config || !outputs_config->IsArray()) {
    LOG(ERROR) << "miss `outputs` config or not and array.";
    return false;
  }
  for (auto cfg : outputs_config->array()) {
    const auto &tensor_name = cfg->GetString("tensor_name");
    std::string tensor_name_without_num_index = reco_arch::uni_predict::StripTensorName(tensor_name);
    if (tensor_name.empty() || tensor_name_without_num_index.empty()) {
      LOG(ERROR) << "`tensor_name` in outputs should is empty or invalid.";
      return false;
    }
    const auto &attr_name = cfg->GetString("attr_name");
    if (attr_name.empty()) {
      LOG(ERROR) << "`attr_name` in outputs should not be empty.";
      return false;
    }
    outputs_[tensor_name].push_back(attr_name);
    output_op_names_.push_back(tensor_name_without_num_index);
  }
  LOG(INFO) << "output_op_names: " << reco_arch::uni_predict::ListToStr(output_op_names_);

  // params
  if (graph_type_ == GraphType::kTF) {
    if (!ParseParamMetaFromGraphMetaFile(model_meta_path_, output_op_names_, &param_metas_,
                                         &param_total_len_)) {
      LOG(ERROR) << "parse param meta failed.";
      return false;
    }
  } else if (graph_type_ == GraphType::kONNX) {
    // onnx 优先从 param config 解析
    auto param_config = processor_config_->Get("param");
    if (param_config) {
      if (!ExtractParamsConfig(param_config, false, &param_metas_, &param_total_len_)) {
        LOG(ERROR) << "parse param from config failed.";
        return false;
      }
    } else {
      if (!ParseParamMetaFromOnnxModelFile(model_meta_path_, output_op_names_, &param_metas_,
                                           &param_total_len_)) {
        LOG(ERROR) << "parse param from onnx model file failed.";
        return false;
      }
    }
  } else {
    LOG(ERROR) << "unsupported graph type: " << static_cast<int>(graph_type_);
    return false;
  }
  auto model_loader_config = processor_config_->Get("model_loader_config");
  auto dense_format = model_loader_config ? model_loader_config->GetString("dense_format", "btq") : "btq";
  dense_format_ = reco_arch::uni_predict::StrToDenseFormat(dense_format);
  if (dense_format_ == reco_arch::uni_predict::DenseFormat::BTQ) {
    // 从图中 parse 的 BTQ 格式的 dense 参数不一定是全部的, 需要从 layer_config 中获取 param 修正一下
    auto layer_config = processor_config_->Get("layer_config");
    if (layer_config) {
      ParseParamMetaFromLayerConfig(layer_config, &param_metas_, &param_total_len_);
    } else {
      LOG(WARNING) << "BTQ dense format miss `layer_config`";
    }
  }
  // param 会被 idt 和 rollbcker 使用, 初始化相关数据结构
  for (const auto &param_meta : param_metas_) {
    param_names_.push_back(param_meta.name);
    auto param_size = param_meta.rown * param_meta.coln;
    param_sizes_.push_back(param_size);
    idt_weights_[param_meta.name].resize(param_size, 0.f);
  }
  // inputs
  // dense_model_manager
  std::string model_queue = queue_prefix_ + "_dnn_model";
  model_load_path_ = reco_arch::uni_predict::FLAGS_model_load_local_path + "/" + model_key_;
  if (dense_format_ == reco_arch::uni_predict::DenseFormat::KAI) {
    dense_model_manager_ = std::make_shared<reco_arch::uni_predict::KaiDenseModelManager>(
        model_queue, "unknown", model_load_path_, model_key_, param_metas_, param_total_len_);
  } else if (dense_format_ == reco_arch::uni_predict::DenseFormat::BTQ) {
    auto model_type = reco_arch::uni_predict::DenseModelManager::ModelType::BIN_MODEL;
    // kai model loader 仅支持分片加载
    auto message_type = reco_arch::uni_predict::BTQDenseModelManager::MessageType::MODEL_PARTITION;
    dense_model_manager_ = std::make_shared<reco_arch::uni_predict::BTQDenseModelManager>(
        model_type, message_type, model_queue, "unknown", model_load_path_, queue_prefix_, param_total_len_,
        reco_arch::uni_predict::FLAGS_mio_tf_dnn_model_version_buffer_size);
  } else if (dense_format_ == reco_arch::uni_predict::DenseFormat::BTQ_V2) {
    dense_model_manager_ = std::make_shared<reco_arch::uni_predict::BTQDenseModelManagerV2>(
        model_queue, "unknown", model_load_path_, queue_prefix_, &param_metas_, param_total_len_,
        reco_arch::uni_predict::FLAGS_mio_tf_dnn_model_version_buffer_size);
  }
  LOG(INFO) << "total_param_len: " << param_total_len_ << ", model_load_local_path: " << model_load_path_
            << ", model_queue: " << model_queue << ", dense_format: " << dense_format
            << ", param meta size: " << param_metas_.size();
  return true;
}

bool AdUniPredictFusedInternalEnricher::InitIdtAndModelRollbacker() {
  /// 尝试使用 idt 和 rollback 获取 dense 和 sparse 参数
  // 先使用 idt 更新 dense 和 sparse
  if (!use_double_buffer_loader_) {
    if (!InitAndUpdateWithIdt()) {
      LOG(ERROR) << "update with idt failed.";
    }
    // 再使用 rollback 更新 dense 和 sparse, idt 成功时跳过
    if (!idt_success_ && !InitAndUpdateWithRollbacker()) {
      LOG(ERROR) << "update with rollbacker failed.";
    }
  } else {
    // 双 buffer 模型启动时使用 idt 参数或者 hdfs 文件更新
    if (!UpdateWithDoubleBuffer()) {
      LOG(ERROR) << "update with double buffer failed.";
    }
  }
  // 当模型 embedding lag 低于指定值时才能认为加载成功，避免与 ps 版本不一致导致效果有问题
  uint64_t lag = (base::GetTimestamp() / 1000 - nohash_btq_embedding_->batch_version_ms()) / 1000;
  // idt 成功的忽略 lag
  if (!idt_success_) {
    while (lag > FLAGS_btq_model_max_lag) {
      LOG_EVERY_N(INFO, 10) << "Waiting for embedding ready, current version: "
                            << nohash_btq_embedding_->batch_version_ms() << ", lag: " << lag << " seconds, "
                            << " received: " << nohash_btq_embedding_->GetEmbeddingTotalCount() << " keys";
      std::this_thread::sleep_for(std::chrono::seconds(1));
      auto cur_time_ms = base::GetTimestamp() / 1000;
      // 判断 batch_version_ms 拿到的时间戳是否可能为微秒
      if (nohash_btq_embedding_->batch_version_ms() > cur_time_ms) {
        lag = (cur_time_ms - nohash_btq_embedding_->batch_version_ms() / 1000) / 1000;
      } else {
        lag = (cur_time_ms - nohash_btq_embedding_->batch_version_ms()) / 1000;
      }
    }
  }
  LOG(INFO) << "nohash btq model embedding init succeeds";
  btq_reader_.SetBatchSize(FLAGS_btq_read_batch_size);  // 服务启动后降低 batch size 以避免抖动
  return true;
}

bool AdUniPredictFusedInternalEnricher::LoadGraph() {
  auto model_loader_config = processor_config_->Get("model_loader_config");
  if (model_loader_config == nullptr) {
    LOG(ERROR) << "model_loader_config is nullptr.";
    return false;
  }
  auto model_loader_type = model_loader_config->GetString("type", "KaiTFExecutedByTensorRTModelLoader");
  if (model_loader_type.empty()) {
    LOG(ERROR) << "model_loader_config::type is required.";
    return false;
  }

  // 初始化 graph
  if (model_loader_type == "KaiTFExecutedByTensorRTModelLoader" ||
      model_loader_type == "KaiTFExecutedByTensorFlowModelLoader") {
    graph_type_ = GraphType::kTF;
    CHECK_RETURN_FALSE(reco_arch::uni_predict::LoadGraphFromMetaFile(model_meta_path_, &graph_def_),
                       "`AdUniPredictFusedInternalEnricher` load graph_def from meta file failed.");
  } else if (model_loader_type == "KaiTFExecutedByOnnxTRTModelLoader") {
    graph_type_ = GraphType::kONNX;
    CHECK_RETURN_FALSE(reco_arch::uni_predict::LoadModelFromOnnxFile(model_meta_path_, &onnx_model_),
                       "`AdUniPredictFusedInternalEnricher` load onnx_model from onnx file failed.");
  } else {
    LOG(ERROR) << "AdUniPredictFusedInternalEnricher unsupported model_loader_type: " << model_loader_type;
    return false;
  }
  return true;
}

void AdUniPredictFusedInternalEnricher::ReportModelLoadDelay() {
  LOG(INFO) << "dragon report model load delay thread start";
  // 线上 serving 的模型版本
  int64_t updated_model_version = 0;
  // 回滚参数版本，双 buffer 模型中是指 not serving 的模型版本
  int64_t rollback_model_version = 0;
  // std::string falcon_key = "dnn_predict_server." + deploy_model_name_ + "_load_delay";
  while (true) {
    std::this_thread::sleep_for(std::chrono::seconds(60));
    auto now = serving_base::TimeHelper::GetCurrentTimestamp(serving_base::TimeHelper::kMinute);

    // 回滚模式下仍会以回滚文件中的 version 更新 predictor 的 model_version
    updated_model_version = predictor_->GetRefittedModelVersion();
    if (updated_model_version != 0) {
      // now 单位是毫秒，updated_model_version 为秒，监控上报单位为分钟
      auto updated_model_load_delay = (now / 1000 / 1000 - updated_model_version) / 60;
      falcon::Set("dnn_predict_server.model_load_delay", updated_model_load_delay, falcon::kNonAdditiveGauge);
      // falcon::Set(falcon_key.c_str(), updated_model_load_delay, falcon::kNonAdditiveGauge);
      ks::infra::PerfUtil::IntervalLogStash(updated_model_load_delay, "ad_predict",
                                            "dnn_predict_server.model_load_delay", model_cmd_);
      LOG(INFO) << "report model load delay, updated model version (s): " << updated_model_version;
    }

    // 双 buffer not_serving 模型 load delay
    if (use_double_buffer_loader_) {
      ::thread::ReaderAutoLock lk(&double_buffer_model_param_mutex_);
      if (not_serving_buffer_) {
        rollback_model_version = not_serving_buffer_->GetDenseVersion();
      }
      if (rollback_model_version != 0) {
        auto rollback_model_load_delay = (now / 1000 / 1000 - rollback_model_version / 1000) / 60;
        falcon::Set("dnn_predict_server.double_buffer_rollback_model_load_delay", rollback_model_load_delay,
                    falcon::kNonAdditiveGauge);
        LOG(INFO) << "report model load delay, rollback model version (s): " << rollback_model_load_delay;
      }
    }
  }
  LOG(INFO) << "dragon report model load delay thread end";
}

bool AdUniPredictFusedInternalEnricher::InitEmpServiceConfig() {
  /// 初始化 emp_service 相关信息, 供 embedding fetcher 和 embedding cache storage 实例化使用
  auto emp_config = processor_config_->Get("emp_service");
  if (!emp_config) {
    LOG(ERROR) << "no `emp_service` found for model: " << model_cmd_;
    return false;
  }
  for (const auto &[shard, config] : emp_config->objects()) {
    if (config->IsArray() && !ad_nn::GetShardFieldConfig(*emp_config, shard, &emp_shard_infos_[shard])) {
      LOG(ERROR) << "parse shard config failed from `emp_service`.";
      return false;
    }
  }
  field_embedding_infos_.clear();
  if (emp_shard_infos_.find("local") != emp_shard_infos_.end()) {
    const auto &emp_local_shard_infos = emp_shard_infos_["local"];
    for (const auto &feature : feature_file_info_.features) {
      if (!feature.feature_in_sub_graph) {
        continue;
      }
      if (feature.prefix_name.empty()) {
        const auto &iter = emp_local_shard_infos.find(feature.field_index);
        if (iter != emp_local_shard_infos.end()) {
          field_embedding_infos_.emplace(feature.field_index, emp_local_shard_infos.at(feature.field_index));
        }
      } else {
        for (const auto field_index : feature.prefix_index) {
          const auto &iter = emp_local_shard_infos.find(field_index);
          if (iter != emp_local_shard_infos.end()) {
            field_embedding_infos_.emplace(field_index, emp_local_shard_infos.at(field_index));
          }
        }
      }
    }
    LOG(INFO) << "model_key: " << model_key_ << ", local_shard_info_size: " << emp_local_shard_infos.size()
              << ", after filter: " << field_embedding_infos_.size();
  }
  return true;
}

/// callback
bool AdUniPredictFusedInternalEnricher::MioBtqCallback(const std::string &msg) {
  model::ModelUpdateMessage model_data;
  if (!model_data.ParseFromString(msg)) {
    LOG(ERROR) << "parse btq message failed.";
    return false;
  }
  if (model_data.has_model_part()) {
    LOG(ERROR) << "sparse btq message with dense data.";
    return false;
  }
  // 做一下流式更新的模型的校验, 仅 kai 格式的 dense 参数支持微批校验
  if (dense_format_ == reco_arch::uni_predict::DenseFormat::KAI) {
    bool valid = ad_nn::MicroBatchModelValidate::GetInstance()->WaitForSparse(model_data.version() * 1000,
                                                                              model_data.item_size());
    if (!valid) {
      LOG(ERROR) << "skip dirty model, version: " << model_data.version() * 1000;
      return false;
    }
  }
  return mio_btq_embedding_->UpdateCallback(model_data);
}

bool AdUniPredictFusedInternalEnricher::DoubleBufferUpdateCallback(bool rollback) {
  /// 双 Buffer 模型参数 更新/回滚 回调处理
  auto current_update_mode = rollback ? "rollback" : "normal";
  LOG(INFO) << "double buffer model " << current_update_mode << " mode, start update model weights";
  std::unordered_map<std::string, std::vector<float>> weights;
  int64_t dense_version_ms = 0;
  // 从 not serving 的 buffer 取出参数
  if (!not_serving_buffer_->GetNNWeightsByLayerNames(param_names_, &weights, &dense_version_ms)) {
    LOG(ERROR) << "double buffer model rollback get nn weight failed";
    return false;
  }
  if (!predictor_) {
    LOG(ERROR) << "double buffer model update callback, but predictor is not ready yet";
    return false;
  }
  // 更新双 Buffer 模型 Dense 参数到不在 serving 的 engine
  auto executor = predictor_->UpdateWeightsWithoutSwitch(weights, true, dense_version_ms);
  if (!executor) {
    LOG(ERROR) << "double buffer model predictor update weights failed.";
    return false;
  }
  // 更新双 Buffer 模型 Sparse & dense 参数
  {
    // 加写锁
    ::thread::WriterAutoLock lk(&double_buffer_model_param_mutex_);
    // 切换 sparse 指针
    auto not_serving_buffer_embedding = not_serving_buffer_->GetNohashEmbedding();
    concurrent_embedding_->SetEmbeddingStorage(not_serving_buffer_embedding);
    // 切换 dense 指针
    if (!predictor_->SwitchReplica(executor, dense_version_ms)) {
      LOG(ERROR) << "double buffer model switch replica failed.";
      return false;
    }
    rollback_model_.store(rollback);
    ad_nn::BtqModelLoader::loader_info_.Set(not_serving_buffer_->GetLoaderId(),
                                            not_serving_buffer_->GetCurrentVersion(), rollback);
    LOG(INFO) << "double buffer model update successfully, sparse embedding switch to " << current_update_mode
              << " mode with hybrid embedding " << not_serving_buffer_embedding.get()
              << ", sparse version: " << not_serving_buffer_embedding->batch_version_ms()
              << ", dense version: " << dense_version_ms;
    std::swap(not_serving_buffer_, serving_buffer_);
  }
  return true;
}

bool AdUniPredictFusedInternalEnricher::RollbackCallback(bool rollback) {
  if (rollback) {
    LOG(INFO) << "start rollback";
    std::unordered_map<std::string, std::vector<float>> weights;
    int64_t dense_version_ms = 0;
    if (!rollbacker_->GetNNWeightsByLayerNames(param_names_, &weights, &dense_version_ms)) {
      LOG(ERROR) << "rollback get nn weight failed";
      return false;
    }
    // 关闭 predictor 的自动更新, 使用 rollbacker 的 weights 回滚
    if (!predictor_->SetUpdateStatus(false)) {
      LOG(ERROR) << "stop uni-predict predictor auto update failed.";
      return false;
    }
    if (!predictor_->UpdateModelWithWeights(weights, true, dense_version_ms)) {
      LOG(ERROR) << "predictor update weights failed.";
      return false;
    }
    // 同时需要回滚 embedding cache 存储结果的内容
    auto rollback_embedding = rollbacker_->GetNohashEmbedding();
    concurrent_embedding_->SetEmbeddingStorage(rollback_embedding);
    rollback_model_.store(true);
    ad_nn::BtqModelLoader::loader_info_.Set(rollbacker_->GetLoaderId(), rollbacker_->GetCurrentVersion(),
                                            rollback);
    LOG(INFO) << "embedding switch to rollback mode with hybrid embedding: " << rollback_embedding.get();
  } else {
    LOG(INFO) << "start recover";
    // 关闭回滚模式
    if (!predictor_->SetUpdateStatus(true, true)) {
      LOG(ERROR) << "recover uni-predict model failed";
      return false;
    }
    concurrent_embedding_->SetEmbeddingStorage(nohash_btq_embedding_);
    rollback_model_.store(false);
    ad_nn::BtqModelLoader::loader_info_.Set(0, std::to_string(nohash_btq_embedding_->batch_version_ms()),
                                            false);
    LOG(INFO) << "embedding switch to normal mode with hybrid embedding " << nohash_btq_embedding_.get();
  }
  return true;
}

bool AdUniPredictFusedInternalEnricher::IdtPutWeights(uint64_t version_ms) {
  auto weight_table = reco_arch::uni_predict::WeightTable::Create(param_total_len_, version_ms / 1000);
  size_t offset = 0;
  for (const auto &param_meta : param_metas_) {
    const auto &param_name = param_meta.name;
    const auto param_size = param_meta.rown * param_meta.coln;
    auto iter = idt_weights_.find(param_name);
    if (iter == idt_weights_.end()) {
      LOG(ERROR) << "param weight not found: " << param_name;
      return false;
    }
    const auto &weights = iter->second;
    if (weights.empty()) {
      LOG(WARNING) << "weights if empty, use 0 default. config_size: " << param_size
                   << ", value size: " << weights.size() << ", param: " << param_name;
      std::fill_n(weight_table->Data() + offset, param_size, 0.f);
    } else if (weights.size() != param_size) {
      LOG(ERROR) << "weight size different, config_size: " << param_size << ", value size: " << weights.size()
                 << ", param: " << param_name;
      return false;
    } else {
      std::copy_n(weights.begin(), param_size, weight_table->Data() + offset);
    }
    offset += param_size;
  }
  dense_model_manager_->SaveTableAsBinModel(weight_table, nullptr);
  idt_weights_.clear();
  return true;
}
void AdUniPredictFusedInternalEnricher::ResetModelData() {
  idt_weights_.clear();
  nohash_btq_embedding_->SetVersion(0);
  idt_model_loader_->SetCurrentVersion("");
  ad_nn::ModelLoaderInfo::Instance().GetLoaderMetaBak().Set(0, "", false);
}
bool AdUniPredictFusedInternalEnricher::InitAndUpdateWithIdt() {
  /// 初始化 idt 和支持模型回滚相关的结构, 然后使用 idt 拉取 dense 和 sparse 参数.
  /// dense 参数会存储到 dense_model_manager 中, sparse 参数会存储到 embedding cache storage 结构中.
  // 1. idt_data 实例化
  idt_data_ = ad_nn::IdtData::GetOrCreateInstance(model_key_);
  if (!idt_data_) {
    LOG(ERROR) << "idt_data instance failed.";
    return false;
  }
  idt_data_->Init(param_names_, param_sizes_, param_total_len_, dense_model_manager_);

  auto idt_get_version = [&]() { return dense_model_manager_->GetVersion(); };
  auto idt_get_weights = [&](std::vector<std::string> *param_names,
                             std::vector<std::vector<float>> *param_weights, int64_t *batch_version) {
    return idt_data_->GetModelWeights(param_names, param_weights, batch_version);
  };
  idt_model_loader_ = std::make_shared<ad_nn::IdtModelLoader>(idt_get_weights, nohash_btq_embedding_,
                                                              idt_get_version, &idt_weights_, 0);
  /// idt 拉取数据
  bool use_idt = true;
  uint64_t version_before_idt = nohash_btq_embedding_->batch_version_ms();
  uint64_t lag_before_idt = (base::GetTimestamp() / 1000 - version_before_idt) / 1000;
  std::string pod_name = ad_nn::GetPodName();
  // 1. 获取 idt 的配置
  base::JsonObject common_idt_config;
  std::string data_type = "model_data_0";
  if (!ad_nn::GetOneIdtHandlerConfig(processor_config_->Get("idt_config"), data_type, &common_idt_config)) {
    LOG(ERROR) << "get idt handler config for " << data_type << " failed. DO NOT use idt.";
    use_idt = false;
  }
  if (use_idt) {
    LOG(INFO) << "use_idt: current embedding version before idt(ms): " << version_before_idt
              << ", embedding lag before idt(s): " << lag_before_idt
              << ", embedding total count: " << nohash_btq_embedding_->GetEmbeddingTotalCount();
    nohash_btq_embedding_->ResetIdtUpdateEmbeddingCount();
    ad_nn::IdtDataSources::GetOrCreateInstance(model_key_)
        ->Put("model_data_0_source", idt_model_loader_.get());
    ad_nn::CommonIdtReader common_idt_reader;
    bool idt_sparse_success = false, idt_dense_success = false;
    // 支持单机多模型，增加 idt_model_key 字段
    auto idt_handler_config = common_idt_config.Get("idt_handler_config");
    if (idt_handler_config) {
      idt_handler_config->set("idt_model_key", model_key_);
    }
    if (!common_idt_reader.Start(common_idt_config)) {
      LOG(WARNING) << "idt reader start failed, can not get data from other server through idt service";
      ResetModelData();
      return false;
    }

    // NOTE: idt 读取成功是怎么和 idt_model_loader 和 dense_model_manager 联系起来的?
    // 这里 common_idt_reader 中会创建 AdModelIdtHandler 类型的实例, 该实例中会通过 IdTDataSources::Put 的
    // idt_model_loader_ 来接收 dense & sparse

    // 判断 sparse 是否通过 idt 更新成功
    auto server_embedding_count = nohash_btq_embedding_->GetIdtServerEmbeddingCount();
    auto update_embedding_count = nohash_btq_embedding_->GetTotalIdtUpdateEmbeddingCount();
    auto embedding_total_count = nohash_btq_embedding_->GetEmbeddingTotalCount();
    LOG(INFO) << "idt server embedding count: " << server_embedding_count
              << ", idt update embedding count: " << update_embedding_count
              << ", current embedding total count " << embedding_total_count;
    if (server_embedding_count > 0 && embedding_total_count > 0.9 * server_embedding_count) {
      auto server_embedding_version = nohash_btq_embedding_->GetIdtServerEmbeddingVersion();
      auto local_embedding_version = nohash_btq_embedding_->batch_version_ms();
      if (server_embedding_version == local_embedding_version) {
        ++server_embedding_version;
      }
      idt_sparse_success = true;
      nohash_btq_embedding_->UpdateVersion(server_embedding_version, true, false);
      LOG(INFO) << "idt update sparse success, update embedding version to: " << server_embedding_version;
    }

    // 判断 dense 是否通过 idt 更新成功
    auto dense_version_ms = idt_model_loader_->GetDenseVersion();
    if (dense_version_ms > 0) {
      idt_dense_success = true;
      LOG(INFO) << "idt update dense success, version: " << dense_version_ms;
    }

    if (idt_sparse_success && idt_dense_success) {
      // 都更新成功则接收
      auto &meta_data_bak = ad_nn::ModelLoaderInfo::Instance().GetLoaderMetaBak();
      ad_nn::BtqModelLoader::loader_info_.Set(meta_data_bak.GetCurrentServingLoaderId(),
                                              meta_data_bak.GetCurrentServingVersion(),
                                              meta_data_bak.GetCurrentRollbackMode());
      meta_data_bak.Set(0, "", false);
      if (!ad_nn::BtqModelLoader::loader_info_.GetCurrentRollbackMode()) {
        if (IdtPutWeights(dense_version_ms)) {
          LOG(INFO) << "idt success, update idt dense weights success, dense version ms: "
                    << dense_version_ms;
          idt_success_ = true;
        } else {
          LOG(INFO) << "idt success, but update idt dense weights failed.";
        }
      } else {
        LOG(INFO) << "rollback mode, do not need to put online idt dense weights.";
      }
      LOG(INFO) << "use idt: dense version after idt: " << dense_version_ms
                << ", sparse version after idt: " << meta_data_bak.GetCurrentServingVersion();
    }
    if (!idt_sparse_success || !idt_dense_success) {
      // 否则清空数据
      LOG(WARNING) << "idt failed, reset model data";
      ResetModelData();
      return false;
    }
  }
  return true;
}

bool AdUniPredictFusedInternalEnricher::UpdateWithDoubleBuffer() {
  // 启动的时候，IDT 可能会将 not_serving_buffer_ 置为 IsServing，需要切换一下
  if (not_serving_buffer_ && not_serving_buffer_->IsServing() && not_serving_buffer_->WaitReady()) {
    std::swap(serving_buffer_, not_serving_buffer_);
  }
  // 更新 sparse 和 dense 参数
  if (serving_buffer_ && serving_buffer_->IsServing() && serving_buffer_->WaitReady()) {
    int64_t dense_version_ms = 0;
    if (!serving_buffer_->GetNNWeightsByLayerNames(param_names_, &idt_weights_, &dense_version_ms)) {
      LOG(ERROR) << "serving buffer get nn weight failed";
      return false;
    }
    if (!IdtPutWeights(dense_version_ms)) {
      LOG(ERROR) << "current buffer is serving, but idt put dense weights failed";
      return false;
    }
    auto serving_buffer_embedding = serving_buffer_->GetNohashEmbedding();
    concurrent_embedding_->SetEmbeddingStorage(serving_buffer_embedding);
    ad_nn::BtqModelLoader::loader_info_.Set(serving_buffer_->GetLoaderId(),
                                            serving_buffer_->GetCurrentVersion(),
                                            ad_nn::BtqModelLoader::loader_info_.GetCurrentRollbackMode());
    idt_success_ = true;
    LOG(INFO) << "current buffer is serving, and write idt dense weights success, dense version: "
              << dense_version_ms << ", sparse version: " << serving_buffer_embedding->batch_version_ms();
  }
  return true;
}

bool AdUniPredictFusedInternalEnricher::InitAndUpdateWithRollbacker() {
  auto instant_rollback = processor_config_->GetBoolean("instant_rollback", true);
  // TODO(xucaiyi) 当前仅支持 tensorflow graph rollback，onnx 需要手动设置 "instant_rollback": false
  if (instant_rollback) {
    LOG(INFO) << "enable rollback, use KaiBtqModelRollbacker";
    if (graph_type_ == GraphType::kTF) {
      rollbacker_ = std::make_shared<ad_nn::KaiBtqModelRollbacker>();
      rollbacker_->SetModelVersionPath(processor_config_->GetString("model_path"));
      rollbacker_->SetModelKey(model_key_);
      rollbacker_->SetLoaderId(1);  // 1. 表示回滚模型
      rollbacker_->SetBtqQueueShard("local");
      rollbacker_->SetRollbackCb([&](bool rollback) { return RollbackCallback(rollback); });
      ad_nn::IdtDataSources::GetOrCreateInstance(model_key_)->Put("model_data_1_source", rollbacker_.get());
      if (!rollbacker_->Init("", model_root_path_, queue_prefix_, graph_def_, output_op_names_,
                             embedding_node_prefix_, field_embedding_infos_, embedding_capacity_,
                             embedding_cache_type_, embedding_min_fill_ratio_, nullptr, true)) {
        LOG(ERROR) << "rollback init failed.";
        rollbacker_ = nullptr;
        return false;
      }
    } else if (graph_type_ == GraphType::kONNX) {
      rollbacker_ = nullptr;
      LOG(ERROR) << "onnx model rollbacker is not supported yet.";
      return false;
    } else {
      rollbacker_ = nullptr;
      LOG(ERROR) << "rollbacker init failed, unsupported graph type: " << static_cast<int>(graph_type_);
      return false;
    }
  }

  // 读最新的回滚状态
  auto rollback_mode_now = ad_nn::PredictServiceKconfUtil::IsRollBackMode(model_cmd_);
  // 回滚模式等 idt 成功; idt 失败等回滚文件的读取完成
  if (rollbacker_ && (rollback_mode_now || rollbacker_->IsServing()) && rollbacker_->WaitReady()) {
    int64_t dense_version_ms = 0;
    if (!rollbacker_->GetNNWeightsByLayerNames(param_names_, &idt_weights_, &dense_version_ms)) {
      LOG(ERROR) << "rollbacker get nn weight failed";
      return false;
    }
    if (!IdtPutWeights(dense_version_ms)) {
      LOG(ERROR) << "current rollbacker is serving, but idt put dense weights failed";
      return false;
    }
    idt_success_ = true;
    LOG(INFO) << "current rollbacker is serving, and write idt dense weights success";
  }
  return true;
}

bool AdUniPredictFusedInternalEnricher::CheckFeatureFileVersion(const std::string &feature_path) const {
  std::ifstream ifs(feature_path);
  if (!ifs) {
    LOG(ERROR) << "feature file not exist: " << feature_path;
    return false;
  }
  std::string line;
  std::getline(ifs, line);
  ifs.close();
  std::vector<std::string> versions;
  base::SplitStringWithOptions(line, "= \t", true, true, &versions);
  if (versions.size() == 2 && versions[0] == "version" && versions[1] == "2") {
    LOG(ERROR) << "Version 2 feature file is not support yet.";
    return false;
  }
  return true;
}
uint32_t AdUniPredictFusedInternalEnricher::GetEmbeddingDim(
    uint32_t field, const std::map<std::string, ad_nn::FieldEmbeddingInfoMap> &emp_shard_infos) const {
  for (const auto &[shard, infos] : emp_shard_infos) {
    auto iter = infos.find(field);
    if (iter != infos.end()) {
      return iter->second.embedding_len;
    }
  }
  LOG(FATAL) << "can not find embedding info in emp_service.";
  return -1;
}

bool AdUniPredictFusedInternalEnricher::CheckMetaAndFeature() const {
  for (const auto &meta : io_tensor_info_.input_meta) {
    if (reco_arch::uni_predict::IsInternalInputBatchIndex(meta.tensor_name)) {
      continue;
    }
    auto iter = feature_infos_.find(meta.tensor_name);
    if (iter == feature_infos_.end()) {
      LOG(ERROR) << "Can not find input meta: `" << meta.tensor_name << "` from feature file";
      return false;
    }
    auto input_tensor_dim = reco_arch::uni_predict::MetaVolume(meta);
    auto feature_info_dim = iter->second.tensor_len;
    if (input_tensor_dim != feature_info_dim) {
      LOG(INFO) << "Input tensor dim inconsistent from meta and feature file, feature: " << meta.tensor_name
                << " " << input_tensor_dim << " vs " << feature_info_dim;
      return false;
    }
  }
  for (const auto &[feature_name, feature_info] : feature_infos_) {
    auto iter = std::find_if(io_tensor_info_.input_meta.begin(), io_tensor_info_.input_meta.end(),
                             [&feature_name = feature_name](const reco_arch::uni_predict::TensorMeta &meta) {
                               return meta.tensor_name == feature_name;
                             });
    if (iter == io_tensor_info_.input_meta.end()) {
      LOG(ERROR) << "Can not find feature: `" << feature_name << "` in meta.";
      return false;
    }
    auto input_tensor_dim = reco_arch::uni_predict::MetaVolume(*iter);
    auto feature_info_dim = feature_info.tensor_len;
    if (input_tensor_dim != feature_info_dim) {
      LOG(INFO) << "Input tensor dim inconsistent from meta and feature file, feature: " << feature_name
                << " " << input_tensor_dim << " vs " << feature_info_dim;
      return false;
    }
  }
  return true;
}

/// =====================================

void AdUniPredictFusedEnricher::FalconReportQPS(MutableRecoContextInterface *context) {
  std::vector<const char *> tags;
  auto cmdkey_list = context->GetStringListCommonAttr("cmdkey_list");
  auto cmdkey_end_pos = context->GetIntListCommonAttr("cmdkey_end_pos");
  if (!cmdkey_list || !cmdkey_end_pos || cmdkey_list->size() <= 0 ||
      cmdkey_list->size() != cmdkey_end_pos->size()) {
    LOG_EVERY_N(WARNING, 1000) << "invalid cmd key got";
  } else {
    bool is_offline_traffic = ::ks::ad_nn::IsOfflineTraffic();
    if (is_offline_traffic) {
      tags.emplace_back("offline");
    } else if (cmdkey_list->size() > 0) {
      for (const auto &cmdkey : *cmdkey_list) {
        tags.emplace_back(cmdkey.data());
      }
    }
    for (const auto &tag : tags) {
      falcon::Inc(base::StringPrintf("dnn_predict_server.predict_cmdkey_%s", tag).c_str(), 1);
    }
  }
  falcon::Inc("dnn_predict_server.predict", 1);
}

void AdUniPredictFusedEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                       RecoResultConstIter end) {
  FalconReportQPS(context);
  internal_enricher_->Enrich(context, begin, end);
}
bool AdUniPredictFusedEnricher::InitProcessor() {
  model_key_ = config()->GetString("key", "");
  if (model_key_.empty()) {
    LOG(ERROR) << "config `key` is empty.";
    return false;
  }
  if (!SharedData<AdUniPredictFusedInternalEnricher>::has_name(model_key_)) {
    auto ret = SharedData<AdUniPredictFusedInternalEnricher>::Create(model_key_);
    if (!ret) {
      LOG(ERROR) << "Create internal enicher failed: " << model_key_;
      return false;
    }
    LOG(INFO) << "Create internal enricher success: " << model_key_;
    internal_enricher_ = SharedData<AdUniPredictFusedInternalEnricher>::mutable_data(model_key_);
    internal_enricher_->UpdateConfig(config());
    if (!internal_enricher_->InitProcessor()) {
      LOG(ERROR) << "Init internal enricher failed: " << model_key_;
      return false;
    }
    LOG(INFO) << "Init internal enricher success: " << model_key_;
  }
  internal_enricher_ = SharedData<AdUniPredictFusedInternalEnricher>::mutable_data(model_key_);
  return true;
}

void AdUniPredictFusedInternalEnricher::CollectDebugInfo(
    const std::vector<uint64_t> &item_ids,
    const ad_nn::protocol::InferFeatureAccessorInterface *feature_accessor,
    const std::vector<ad_nn::InferFeatureInfo> &user_feature_infos,
    const std::vector<ad_nn::InferFeatureInfo> &item_feature_infos,
    const ad_nn::InferRequestContext *context) {
  static const std::string kField = "field";
  const ad_nn::protocol::PBFeatureAccessor *pb_accessor =
      dynamic_cast<const ad_nn::protocol::PBFeatureAccessor *>(feature_accessor);
  if (pb_accessor != nullptr) {
    ks::ad_nn::debug::CollectRawFeatures(kField, item_ids, *pb_accessor->GetFeatureList(), user_feature_infos,
                                         item_feature_infos, context->debug_kv_collector);
  } else {
    thread_local kuaishou::ad::algorithm::InferFeatureList feature_list;
    feature_accessor->ConvertToPbFeatureList(&feature_list);
    ks::ad_nn::debug::CollectRawFeatures(kField, item_ids, feature_list, user_feature_infos,
                                         item_feature_infos, context->debug_kv_collector);
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AdUniPredictFusedEnricher, AdUniPredictFusedEnricher)
}  // namespace platform
}  // namespace ks
