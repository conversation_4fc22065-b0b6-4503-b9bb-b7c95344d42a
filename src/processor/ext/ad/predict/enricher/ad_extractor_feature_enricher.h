#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "teams/ad/ad_nn/feature/concurrent_feature_extractor.h"
#include "teams/ad/ad_nn/feature/preprocess/feature_processor.h"
#include "teams/ad/ad_nn/feature_extract/extract_framework/include/dynamic_batched_samples.h"
#include "teams/ad/ad_nn/feature_extract/processors/constant.h"
#include "teams/ad/ad_nn/utils/semaphore.h"
#include "teams/reco-arch/embedding_manager/utils/reused_pb_arena.h"

namespace ks {
namespace platform {
using kuaishou::ad::algorithm::InferFeatureList;

class AdExtractorFeatureEnricher : public CommonRecoBaseEnricher {
 public:
  AdExtractorFeatureEnricher() {}
  virtual ~AdExtractorFeatureEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;
  bool IsAsync() const override {
    return false;  // TODO(yangjialin) 改为异步模式
  }

 private:
  bool InitProcessor() override;

  void SeralizeByProtocolAccessor(const InferFeatureList &feature_list, MutableRecoContextInterface *context);

 private:
  std::string name_;  // 模型的名称，带斜杠
  std::shared_ptr<ks::ad_nn::FeatureFileInfo> feature_file_info_;
  std::shared_ptr<ks::ad_nn::ConcurrentFeatureExtractor> extractor_;
  std::shared_ptr<ks::ad_nn::FeatureStat> feature_stat_;
  std::shared_ptr<ks::ad_nn::FeatureProcessor> feature_processor_;
  std::shared_ptr<ks::ad_nn::CompactFeatureCache> item_feature_cache_;
  bool serialize_feature_;
  bool need_feature_processor_ = true;
  bool use_protocol_accessor_ = false;
  std::string output_features_attr_name_;
  std::string input_bslog_attr_ = ks::ad_nn::constant::kDragonInputBsItem;
  DISALLOW_COPY_AND_ASSIGN(AdExtractorFeatureEnricher);
};

}  // namespace platform
}  // namespace ks
