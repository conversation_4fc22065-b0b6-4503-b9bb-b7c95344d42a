#include "dragon/src/processor/ext/ad/predict/enricher/ad_item_enricher.h"
#include <algorithm>
#include <utility>
#include "ks/base/shared_data/shared_data.h"
#include "teams/ad/ad_nn/feature_extract/processors/constant.h"
#include "teams/ad/ad_nn/flatten_raw_feature/adlog_helper.h"
#include "teams/ad/ad_nn/flatten_raw_feature/flatten_helper.h"
#include "teams/ad/ad_nn/flatten_raw_feature/utils.h"
#include "teams/ad/ad_nn/item_union/item_config.h"
#include "teams/ad/ad_nn/item_union/item_manager.h"
#include "teams/ad/ad_nn/model/cleanup.h"
#include "teams/ad/ad_nn/service/service_util.h"
#include "teams/ad/ad_nn/service/wait_service_ready.h"
#include "teams/ad/ad_nn/utils/compress.h"

namespace ks {
namespace platform {

using ::google::protobuf::Arena;
using ks::ad_nn::BSItemWrapper;
using ks::ad_nn::FeatureManager;
using ks::ad_nn::ItemInfoType;
using ks::ad_nn::RawFeatureStore;
using kuaishou::ad::reco::IndexRequest;
using kuaishou::ad::reco::IndexResponse;
using kuaishou::ad::reco::kess::IndexService;
namespace flatten = ks::ad_nn::flatten;

bool AdItemEnricher::InitProcessor() {
  // 默认使用优化的 raw feature
  FLAGS_use_simplified_raw_fea = true;
  FLAGS_use_simplified_raw_fea_list_type = true;
  // 模型名, 非模型服务默认为空
  name_ = ks::ad_nn::GetKcsModelCmd();
  if (name_ == "") {
    is_model_ = false;
    LOG(INFO) << "not has cmd name, use map wrapper.";
  }
  auto processor_config = config();
  if (processor_config == nullptr) return false;
  use_component_item_ = processor_config->GetBoolean("use_component_item", false);
  feature_manager_name_ = processor_config->GetString("feature_manager_name", "default_name");
  item_id_info_attr_ = processor_config->GetString("item_id_info_attr", "item_id_info");
  item_miss_attr_ = processor_config->GetString("item_miss_attr", "item_miss");
  bs_item_attr_ = processor_config->GetString("bs_item_attr", "bs_item");
  item_context_attr_ = processor_config->GetString("item_context_attr", "");
  LOG(INFO) << "use_component_item: " << use_component_item_
            << ", feature_manager_name: " << feature_manager_name_
            << ", item_id_info_attr: " << item_id_info_attr_ << ", item_miss_attr: " << item_miss_attr_
            << ", bs_item_attr: " << bs_item_attr_ << ", item_context_attr: " << item_context_attr_;
  if (!ks::SharedData<FeatureManager>::has_name(feature_manager_name_)) {
    auto ret = ks::SharedData<FeatureManager>::Create(feature_manager_name_);
    if (!ret) {
      LOG(INFO) << "create feature manager " << feature_manager_name_ << " failed";
      return false;
    }
    LOG(INFO) << "create feature manager " << feature_manager_name_ << " success";
    auto manager = ks::SharedData<FeatureManager>::mutable_data(feature_manager_name_);
    if (!ks::ad_nn::ItemManager::Instance().Init(*processor_config, manager.get())) {
      LOG(FATAL) << "component item init failed.";
      return false;
    }
    ks::ad_nn::FeatureThreadResourceManager::InitAll(
        processor_config->GetBoolean("auto_adjust_thread_pool", false));
    if (!manager->Start()) {
      LOG(ERROR) << "feature manager start failed";
      return false;
    }
    LOG(INFO) << "feature manager start succeeds";
  }
  return true;
}

bool AdItemEnricher::ConstructComponentItem() {
  int req_item_count = end_ - begin_;
  absl::optional<absl::string_view> cmd_mapping = context_->GetStringCommonAttr(item_id_info_attr_);
  if (cmd_mapping.has_value()) {
    if (!pv_req_.ParseFromArray(cmd_mapping->data(), cmd_mapping->size())) {
      LOG_EVERY_N(ERROR, 10) << "cmd_mapping parse failed.";
      context_->SetExecutionStatus(ExecutionStatus::TERMINATED);
      return false;
    }
    if (pv_req_.item_id_info_size() != req_item_count) {
      LOG_EVERY_N(ERROR, 100) << "item id info size: " << pv_req_.item_id_info_size()
                              << ", item id size: " << req_item_count;
      return false;
    }
    falcon::Inc("component_item.get_item_id_info");
  } else {
    LOG_EVERY_N(ERROR, 1000) << "no item id info.";
    falcon::Inc("component_item.no_item_id_info");
    context_->SetExecutionStatus(ExecutionStatus::TERMINATED);
    return false;
  }

  auto req_time = base::GetTimestamp() / 1000000;
  if (!ks::ad_nn::ItemManager::Instance().AsyncPrepareBSComponentItem(
          pv_req_.item_id_info(), req_time, bs_items_.get(), item_feature_cache_.get(),
          has_item_context_ ? &item_contexts_ : nullptr)) {
    LOG_EVERY_N(ERROR, 100) << "get item failed.";
    context_->SetExecutionStatus(ExecutionStatus::TERMINATED);
    return false;
  }
  return true;
}

bool AdItemEnricher::ConstructItem() {
  auto req_time = base::GetTimestamp() / 1000000;
  ::google::protobuf::RepeatedPtrField<uint64_t> item_ids;
  for (auto iter = begin_; iter < end_; ++iter) {
    item_ids.Add(iter->GetId());
  }
  if (!ks::ad_nn::ItemManager::Instance().AsyncPrepareBSItem(item_ids, req_time, bs_items_.get(),
                                                             has_item_context_ ? &item_contexts_ : nullptr)) {
    LOG_EVERY_N(ERROR, 100) << "get item failed.";
    context_->SetExecutionStatus(ExecutionStatus::TERMINATED);
    return false;
  }
  return true;
}

void AdItemEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                            RecoResultConstIter end) {
  // 设置 store init bs 的回调
  static std::once_flag init_flag;
  auto set_component_option = [this]() {
    bs_sample_attr_id2idx_ = ks::SharedData<ks::ad_nn::AttrId2Idx>::mutable_data(name_ + "_sample");
    if (!bs_sample_attr_id2idx_) {
      LOG(FATAL) << "invaild situation, bs_sample_attr_id2idx is nullptr";
    }
    ks::ad_nn::ItemManager::Instance().SetOnlineOption(name_, bs_sample_attr_id2idx_);
    item_feature_cache_ = ks::SharedData<ks::ad_nn::CompactFeatureCache>::mutable_data(name_);
  };
  std::call_once(init_flag, set_component_option);
  // 重置请求相关的变量
  context_ = context;
  begin_ = begin;
  end_ = end;
  falcon::Stat("dnn_predict_server.request_item_id_count", end_ - begin_);
  bs_items_ = std::make_shared<std::vector<std::shared_ptr<ks::ad_nn::BSItemWrapper>>>();
  arena_.ResetArena();
  is_debug_req_ = context_->IsDebugRequest();

  has_item_context_ = false;
  if (!item_context_attr_.empty()) {
    LOG_FIRST_N(INFO, 100) << "construct item_context";
    // item_context
    item_contexts_.Clear();
    item_contexts_.Reserve(end_ - begin_);
    for (auto iter = begin_; iter < end_; ++iter) {
      auto item_key = iter->item_key;
      auto serialized_item_context = context_->GetStringItemAttr(item_key, item_context_attr_);
      ItemContext cur_item_context;
      if (serialized_item_context) {
        has_item_context_ = true;
        if (!cur_item_context.ParseFromArray(serialized_item_context->data(),
                                             serialized_item_context->size())) {
          CL_LOG(ERROR) << "ParseFromString failed";
        }
      }
      item_contexts_.Add(std::move(cur_item_context));
    }
  }
  if (use_component_item_) {
    ConstructComponentItem();
  } else {
    ConstructItem();
  }

  WriteToContext();
}

bool AdItemEnricher::WriteToContext() {
  auto item_size = bs_items_->size();
  auto miss_accessor = context_->GetItemAttrAccessor(item_miss_attr_);
  if (item_size > 0) {
    uint32_t empty_item_count = 0;
    for (size_t idx = 0; idx < bs_items_->size(); ++idx) {
      auto &item = (*bs_items_)[idx];
      if (!item) {
        ++empty_item_count;
        context_->SetIntItemAttr(*(begin_ + idx), miss_accessor, 1);
      }
    }
    falcon::Stat("feature_server.empty_item_rate", 1e6 * empty_item_count / item_size);
  }
  context_->SetPtrCommonAttr(bs_item_attr_, bs_items_);
  return true;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AdItemEnricher, AdItemEnricher)
}  // namespace platform
}  // namespace ks
