#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "teams/ad/ad_nn/feature/ad_log_adaptor.h"
#include "teams/ad/ad_nn/feature/extractor_utils.h"
#include "teams/ad/ad_nn/feature/feature_file_info.h"
#include "teams/ad/ad_nn/feature/feature_stat.h"
#include "teams/ad/ad_nn/feature/feature_utils.h"
#include "teams/ad/ad_nn/feature/protocol/feature_accessor/protobuf_accessor.h"
#include "teams/ad/ad_nn/utils/semaphore.h"
#include "teams/reco-arch/embedding_manager/utils/reused_pb_arena.h"

namespace ks {
namespace platform {

using ks::ad_nn::FeatureFileInfo;
using ks::ad_nn::FeatureUtils;
using kuaishou::ad::algorithm::InferFeatureList;

struct DragonOutputResult {
  // 从外到内的维度依次是 item [ [ < slot, signs> ] ]
  std::vector<absl::flat_hash_map<uint32_t, std::vector<int64>>> item_signs;
  // 从外到内的维度依次是 [ < slot, signs> ]
  absl::flat_hash_map<uint32_t, std::vector<int64>> user_signs;
  // 从外到内的维度依次是 item [ [ < feature_name, values> ] ]
  std::vector<absl::flat_hash_map<std::string, std::vector<float>>> item_values;
  // 从外到内的维度依次是 [ < feature_name, values> ]
  absl::flat_hash_map<std::string, std::vector<float>> user_values;
  // 从外到内的维度依次是 item [ [ < feature_name, values> ] ] 用于 int dense
  std::vector<absl::flat_hash_map<std::string, std::vector<int64_t>>> int_item_values;
  // 从外到内的维度依次是 [ < feature_name, values> ] 用于 int dense
  absl::flat_hash_map<std::string, std::vector<int64_t>> int_user_values;

  void clear() {
    item_signs.clear();
    user_signs.clear();
    item_values.clear();
    user_values.clear();
  }
  void resize(int size) {
    item_signs.resize(size);
    item_values.resize(size);
  }
};

class AdMergeFeatureEnricher : public CommonRecoBaseEnricher {
 private:
  class Executor {
   public:
    class Item {
     public:
      Item(MutableRecoContextInterface *ctx, uint64_t item_key) : context_(ctx), item_key_(item_key) {}
      Item(Item &&item) : context_(item.context_), item_key_(item.item_key_) {}
      uint64 GetItemKey() const {
        return item_key_;
      }
      auto GetDoubleList(const std::string &name) const {
        return context_->GetDoubleListItemAttr(GetItemKey(), name);
      }
      auto GetIntList(const std::string &name) const {
        return context_->GetIntListItemAttr(GetItemKey(), name);
      }

     private:
      MutableRecoContextInterface *context_ = nullptr;
      uint64_t item_key_ = 0;
    };  // end class Item

   public:
    explicit Executor(MutableRecoContextInterface *context) : context_(context) {}

    absl::optional<absl::Span<const int64>> GetIntList(const std::string &name) const {
      return context_->GetIntListCommonAttr(name);
    }
    absl::optional<absl::Span<const double>> GetDoubleList(const std::string &name) const {
      return context_->GetDoubleListCommonAttr(name);
    }
    std::vector<Item> GetItemList() {
      std::vector<Item> ret;
      const auto &results = context_->GetCommonRecoResults();
      ret.reserve(results.size());
      for (const auto &result : results) {
        ret.emplace_back(context_, result.item_key);
      }
      return ret;
    }

   private:
    MutableRecoContextInterface *context_ = nullptr;
  };  // end class Executor

 public:
  AdMergeFeatureEnricher() {}
  virtual ~AdMergeFeatureEnricher() {}

  void UpdateDenseConfig(const FeatureFileInfo &feature_file_info) {
    // 遍历获取 dragon dense 特征配置
    for (auto &feature_config : feature_file_info.features) {
      if (!feature_config.is_dragon) {
        continue;
      }
      if (ks::ad_nn::IsUser(feature_config.category)) {
        user_dragon_features_.emplace_back(&feature_config);
        if (feature_config.is_dense) {
          if (config_user_dense_attrs_.find(feature_config.class_name) == config_user_dense_attrs_.end()) {
            LOG(FATAL) << "user dragon dense attr not config: " << feature_config.class_name;
          }
          config_user_dense_attrs_[feature_config.class_name] = &feature_config;
          user_dense_attrs_.emplace_back(feature_config.class_name);
        }
        LOG(INFO) << "user dragon feature:" << feature_config.class_name << " config:" << &feature_config;
      } else {
        item_dragon_features_.emplace_back(&feature_config);
        if (feature_config.is_dense) {
          if (config_item_dense_attrs_.find(feature_config.class_name) == config_item_dense_attrs_.end()) {
            LOG(FATAL) << "item dragon dense attr not config: " << feature_config.class_name;
          }
          config_item_dense_attrs_[feature_config.class_name] = &feature_config;
          item_dense_attrs_.emplace_back(feature_config.class_name);
        }
        LOG(INFO) << "item dragon feature:" << feature_config.class_name << " config:" << &feature_config;
      }
    }
    if (item_dense_attrs_.size() != config_item_dense_attrs_.size() ||
        user_dense_attrs_.size() != config_user_dense_attrs_.size()) {
      LOG(WARNING) << "need dense attr is less than config attr, "
                   << ", user_dense_attrs " << user_dense_attrs_.size()
                   << ", config_user_dense_attrs: " << config_user_dense_attrs_.size()
                   << ", item_dragon_features: " << item_dragon_features_.size()
                   << ", config_item_dense_attrs: " << config_item_dense_attrs_.size();
    }
  }

  bool FetchOutputAttr(DragonOutputResult *dragon_result, std::shared_ptr<Executor> executor,
                       const std::vector<std::string> &item_slot_attrs,
                       const std::vector<std::string> &item_sign_attrs,
                       const std::vector<std::string> &user_slot_attrs,
                       const std::vector<std::string> &user_sign_attrs,
                       const std::vector<std::string> &item_dense_attrs,
                       const std::vector<std::string> &user_dense_attrs);

  bool FillWithDragonResult(const ks::ad_nn::FeatureConfig &feature_config,
                            const absl::flat_hash_map<std::string, std::vector<float>> &dense_values,
                            const absl::flat_hash_map<uint32_t, std::vector<int64>> &sparse_signs,
                            const absl::flat_hash_map<std::string, std::vector<int64_t>> &int_dense_values,
                            ::google::protobuf::RepeatedField<uint32_t> *offsets,
                            ks::ad_nn::FeatureValueList *feature_values, bool add_default_sign);

  void MergeFeature(const ks::ad_algorithm::AdLogInterface &ad_log, InferFeatureList *features,
                    const DragonOutputResult &dragon_result);

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;
  bool IsAsync() const override {
    return false;
  }

 private:
  bool InitProcessor() override;
  void InitFeature(const base::Json *config);
  void SeralizeByProtocolAccessor(MutableRecoContextInterface *context);
  std::string name_;  // 模型的名称，带斜杠

  std::shared_ptr<ks::ad_nn::FeatureFileInfo> feature_file_info_;
  std::shared_ptr<ks::ad_nn::FeatureStat> feature_stat_;

  bool skip_item_part_ = false;
  // item dragon sparse 配置
  std::vector<std::string> item_dragon_slot_keys_;
  std::vector<std::string> item_dragon_sign_keys_;
  // user dragon sparse 配置
  std::vector<std::string> user_dragon_slot_keys_;
  std::vector<std::string> user_dragon_sign_keys_;

  // item dragon dense 配置
  std::vector<std::string> item_dense_attrs_;
  // user dragon dense 配置
  std::vector<std::string> user_dense_attrs_;

  // config dragon dense attr, 这里会比 user/item_dragon_attrs 多, 可以 check 优化?
  std::unordered_map<std::string, const ks::ad_nn::FeatureConfig *> config_user_dense_attrs_;
  std::unordered_map<std::string, const ks::ad_nn::FeatureConfig *> config_item_dense_attrs_;

  bool serialize_feature_ = true;
  bool compress_feature_ = false;
  bool use_protocol_accessor_ = false;
  int compress_level_ = -20;

  /* 各该 Processor AttrName 可配置*/
  // bslog
  std::string bs_log_attr_name_ = "BslogBsItem";
  // infer_feature_list
  std::string feature_list_attr_name_ = "FeatureResult";
  bool check_duplicated_item_ = true;
  // item 位置对应 pipeline 中 item result 的位置
  std::string duplicated_item_attr_name_ = "DragonDuplicatedItem";
  // serialized infer input
  std::string serialized_features_attr_name_ = "serialized_features";

  // 填充结果相关
  std::vector<const ks::ad_nn::FeatureConfig *> user_dragon_features_;
  std::vector<const ks::ad_nn::FeatureConfig *> item_dragon_features_;

  std::shared_ptr<InferFeatureList> feature_list_;
  bool add_default_sign_ = false;
  bool need_feature_extractor_ = false;
  DISALLOW_COPY_AND_ASSIGN(AdMergeFeatureEnricher);
};

}  // namespace platform
}  // namespace ks
