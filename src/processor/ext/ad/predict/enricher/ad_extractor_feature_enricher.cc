#include "dragon/src/processor/ext/ad/predict/enricher/ad_extractor_feature_enricher.h"
#include <algorithm>
#include <set>
#include <string>
#include <unordered_set>
#include <utility>
#include "ks/base/shared_data/shared_data.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/bs_log.h"
#include "teams/ad/ad_algorithm/label_centre/label_centre_wrapper.h"
#include "teams/ad/ad_nn/bs_kconf/bs_kconf_util.h"
#include "teams/ad/ad_nn/feature/feature_file_info.h"
#include "teams/ad/ad_nn/feature/feature_stat_control.h"
#include "teams/ad/ad_nn/feature/feature_utils.h"
#include "teams/ad/ad_nn/feature/protocol/define.h"
#include "teams/ad/ad_nn/feature/protocol/protocol_utils.h"
#include "teams/ad/ad_nn/feature_extract/processors/constant.h"
#include "teams/ad/ad_nn/service/service_util.h"

DECLARE_string(early_return_if_timeout_kconf_key);
DECLARE_bool(use_exist_fix_version);

namespace ks {
namespace platform {
using ks::ad_nn::ConcurrentFeatureExtractor;
using ks::ad_nn::FeatureProcessor;
using ks::ad_nn::FeatureStat;
using ks::ad_nn::protocol::ProtocolUtil;

bool AdExtractorFeatureEnricher::InitProcessor() {
  FLAGS_use_exist_fix_version = config()->GetBoolean("use_exist_fix_version", true);
  name_ = ks::ad_nn::GetKcsModelCmd();
  // 保证只进行一次 attr 注册
  bool init_extractor = false;
  need_feature_processor_ = config()->GetBoolean("need_feature_processor", true);
  if (ks::SharedData<ConcurrentFeatureExtractor>::has_name(name_)) {
    extractor_ = ks::SharedData<ConcurrentFeatureExtractor>::mutable_data(name_);
    feature_stat_ = ks::SharedData<FeatureStat>::mutable_data(name_);
  } else {
    init_extractor = true;
    // Load feature file
    auto json_config = config();
    auto feature_path = json_config->GetString("feature_path");
    // 也支持通过设置 model feature 生成
    auto model_feature = json_config->GetString("model_feature");
    if (!model_feature.empty()) {
      feature_path = ks::ad_nn::GetModelPsRoot(name_);
    }
    if (feature_path.empty()) {
      LOG(ERROR) << "feature path is not configured";
      return false;
    }
    use_protocol_accessor_ = json_config->GetBoolean("use_protocol_accessor", false);
    FLAGS_dragon_grpc_use_multi_eventloop =
        json_config->GetBoolean("dragon_grpc_use_multi_eventloop", FLAGS_dragon_grpc_use_multi_eventloop);
    FLAGS_early_return_if_timeout_kconf_key = json_config->GetString(
        "dragon_early_return_if_timeout_kconf_key", FLAGS_early_return_if_timeout_kconf_key);
    std::string default_bs_feature_config_map = ks::ad_nn::GetModelPsRoot(name_) + "bs_feature_config_map";
    FLAGS_bs_feature_config_map = config()->GetString("bs_feature_config_map", default_bs_feature_config_map);
    // 允许 bs_feature_config_map 为空，每次启动都从 kconf 获取最新数据，而不是本地文件
    if (FLAGS_bs_feature_config_map == "") {
      LOG(WARNING) << "bs_feature_config_map is empty, will get data from kconf every time";
    }
    feature_file_info_.reset(new ks::ad_nn::FeatureFileInfo());
    if (0 != ks::ad_nn::FeatureFileInfo::LoadFeatureFile(feature_path, true, feature_file_info_.get(), true,
                                                         false)) {
      LOG(ERROR) << "load feature file:[" << feature_path << "] failed";
      return false;
    }
    auto ret = ks::SharedData<FeatureStat>::Create(name_);
    if (!ret) {
      LOG(INFO) << "create feature stat " << name_ << " failed";
      return false;
    }
    ret = ks::SharedData<ConcurrentFeatureExtractor>::Create(name_, *feature_file_info_);
    if (!ret) {
      LOG(INFO) << "create feature extractor " << name_ << " failed";
      return false;
    }
    extractor_ = ks::SharedData<ConcurrentFeatureExtractor>::mutable_data(name_);
    // Feature stat
    feature_stat_ = ks::SharedData<FeatureStat>::mutable_data(name_);
    feature_stat_->UpdateName2DenseFlag(feature_file_info_->features);
    int stat_fea_value_mode = ks::ad_nn::SetStatFeatureMode(json_config->GetInt("stat_instance_num", 3));
    feature_stat_->SetStatFeaValueMode(name_, stat_fea_value_mode);
    feature_stat_->SetStatFeaWithCmdKey(json_config->GetBoolean("stat_fea_with_cmdkey", false));
    feature_stat_->SetStatFeaValueFreq(json_config->GetInt("stat_fea_value_frequence", 100));
    feature_stat_->SetStatFeaValueMaxItemCount(json_config->GetInt("stat_fea_value_max_item_count", 10));
    extractor_->SetFeatureStat(feature_stat_.get());
    // Feature cache
    uint32_t item_feature_expire_time = json_config->GetInt("item_feature_expire_time", 120);
    uint64_t item_feature_cache_capacity = json_config->GetInt("item_feature_cache_capacity", 0);
    uint64_t item_feature_cache_capacity_in_gb = json_config->GetInt("item_feature_cache_capacity_in_gb", 0);
    if (item_feature_cache_capacity_in_gb == 0) {
      item_feature_cache_capacity_in_gb = item_feature_cache_capacity >> 30;
    }
    if (item_feature_cache_capacity_in_gb > 0) {
      const int32_t shard_count = json_config->GetInt("item_feature_cache_shard_num", 0);
      if (ks::SharedData<ks::ad_nn::CompactFeatureCache>::Create(
              name_, item_feature_cache_capacity_in_gb << 30, item_feature_expire_time, true, shard_count)) {
        item_feature_cache_ = ks::SharedData<ks::ad_nn::CompactFeatureCache>::mutable_data(name_);
        extractor_->SetFeatureCache(item_feature_cache_.get());
        LOG(INFO) << "create feature cache, item_feature_cache_capacity: "
                  << item_feature_cache_capacity_in_gb << " gb, "
                  << " item_feature_expire_time: " << item_feature_expire_time;
      } else {
        LOG(WARNING) << "create feature cache " << name_ << " failed";
      }
    } else {
      LOG(WARNING) << "item feature cache not config";
    }
    LOG(INFO) << "extractor_ init succeeds";
    ks::ad_algorithm::label_centre::LabelCentreMapping::Instance().SetServerName(name_);
    ks::ad_algorithm::label_centre::LabelCentreMapping::Instance().Init();
    LOG(INFO) << "LabelCentreMapping init succeeds with " << name_;
  }
  auto model_config = ks::ad_nn::PredictServiceKconfUtil::GetModelConfig(name_);
  if (need_feature_processor_) {
    auto preprocessor_config = config()->Get("feature_process_config");
    if (preprocessor_config == nullptr) {
      preprocessor_config = model_config == nullptr ? nullptr : model_config->Get("feature_process_config");
      CL_LOG(INFO) << "feature_process_config from model_config: " << (preprocessor_config != nullptr);
    }
    if (preprocessor_config != nullptr) {
      LOG(INFO) << "feature_process_config: " << preprocessor_config->ToString();
      if (ks::SharedData<FeatureProcessor>::has_name(name_)) {
        feature_processor_ = ks::SharedData<FeatureProcessor>::mutable_data(name_);
      } else {
        auto ret = ks::SharedData<FeatureProcessor>::Create(name_);
        if (!ret) {
          LOG(INFO) << "create feature_processor: " << name_ << " failed";
          return false;
        }
        ks::ad_nn::FLAGS_feature_processor_thread_count = config()->GetInt(
            "feature_processor_thread_count", ks::ad_nn::FLAGS_feature_processor_thread_count);
        feature_processor_ = ks::SharedData<FeatureProcessor>::mutable_data(name_);
        feature_processor_->Init(preprocessor_config, true);
        LOG(INFO) << "feature_process_config init success";
      }
    } else {
      LOG(INFO) << "feature_process_config not found";
    }
    LOG(INFO) << "feature extractor init succeeds";
  }
  // Regsiter BS attrs
  if (init_extractor) {
    std::set<int32_t> attr_ids;
    ks::ad_nn::FeatureUtils::GetFeatureUsedBsAttrs(*feature_file_info_, &attr_ids);
    ks::ad_nn::FeatureUtils::GetDragonUsedBsAttrs(model_config, &attr_ids);
    // for preprocessors
    if (feature_processor_) {
      feature_processor_->FillAllAttrMetas(&attr_ids);
    }
    if (!ks::ad_nn::RegisterBsAttrs(attr_ids, name_)) {
      LOG(ERROR) << "register bs attr to item server failed";
      return false;
    }
    LOG(INFO) << "register bs attr to item server succedds";
  }
  serialize_feature_ = config()->GetBoolean("serialize_feature", false);
  output_features_attr_name_ =
      config()->GetString("output_features_attr", ks::ad_nn::constant::kFeatureResult);
  input_bslog_attr_ = config()->GetString("input_bslog_attr", ks::ad_nn::constant::kDragonInputBsItem);
  return true;
}
void AdExtractorFeatureEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                        RecoResultConstIter end) {
  auto bs_log = context->GetPtrCommonAttr<ks::ad_algorithm::BSLog>(input_bslog_attr_);
  if (!bs_log) {
    falcon::Inc("feature_server.no_bs_log", 1);
    LOG_EVERY_N(WARNING, 1000) << "no bs_log found, return directly";
    return;
  }

  if (feature_processor_) {
    auto pc = std::make_shared<ks::ad_nn::ProcessorContext>();
    pc->start = ks::ad_nn::Clock::now();
    pc->bs_log.reset(const_cast<ks::ad_algorithm::BSLog *>(bs_log), [](const ks::ad_algorithm::BSLog *p) {});
    pc->is_bs = true;
    std::unordered_set<std::string> names;
    feature_processor_->AsyncProcessWithNames(pc, names);
  }

  static thread_local reco_arch::utils::ReusedPbArena arena(1 << 20);
  arena.ResetArena();
  auto feature_list = Arena::CreateMessage<InferFeatureList>(arena.GetArena());
  if (!extractor_->Extract(*bs_log, feature_list)) {
    falcon::Inc("feature_server.extractor_feature_extract_fail", 1);
    LOG_EVERY_N(WARNING, 100) << "extract feature failed, bs_log:" << bs_log << " llsid:" << bs_log->llsid();
    return;
  }
  if (context->IsDebugRequest() && FLAGS_logging_switch_uid_mod_divisor == 1) {
    std::ofstream dst("extractor_feature_" + context->GetRequestId());
    dst << "extractor feature, llsid:" << context->GetRequestId()
        << " content:" << feature_list->DebugString();
    dst.close();
  }
  if (feature_stat_) {
    feature_stat_->DoFeaValueStat(*feature_list, name_);
  }
  if (!serialize_feature_) {
    context->SetPtrCommonAttr(output_features_attr_name_, feature_list);
    return;
  }

  if (use_protocol_accessor_) {
    SeralizeByProtocolAccessor(*feature_list, context);
    return;
  }

  thread_local std::string serialized_features;
  serialized_features.clear();
  feature_list->SerializeToString(&serialized_features);
  context->SetStringCommonAttr(output_features_attr_name_, std::move(serialized_features));
}

void AdExtractorFeatureEnricher::SeralizeByProtocolAccessor(const InferFeatureList &feature_list,
                                                            MutableRecoContextInterface *context) {
  const auto serialize_start = ks::ad_nn::Clock::now();
  thread_local std::string serialized_features;
  serialized_features.clear();
  thread_local ad_nn::protocol::InferFeatureAccessorInterface *feature_accessor =
      ProtocolUtil::CreateFeatureAccessor(FLAGS_infer_feature_protocol);

  if (feature_accessor == nullptr) {
    LOG_EVERY_N(ERROR, 100) << "failed to get feature accessor by protocol: " << FLAGS_infer_feature_protocol;
    return;
  }

  if (!feature_accessor->SerializeInferFeatureList(feature_list, &serialized_features)) {
    LOG_EVERY_N(ERROR, 100) << "failed to get serialize infer feature list, protocol: "
                            << FLAGS_infer_feature_protocol;
    return;
  }

  falcon::Stat("feature_server.serialize_time_cost", ks::ad_nn::GetMicroseconds(serialize_start));
  context->SetStringCommonAttr(output_features_attr_name_, std::move(serialized_features));
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AdExtractorFeatureEnricher, AdExtractorFeatureEnricher)
}  // namespace platform
}  // namespace ks
