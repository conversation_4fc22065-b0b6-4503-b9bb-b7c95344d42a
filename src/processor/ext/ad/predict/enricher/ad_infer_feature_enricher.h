#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>
#include "teams/ad/ad_nn/utils/semaphore.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "teams/ad/ad_nn/feature/ad_log_adaptor.h"
#include "teams/reco-arch/embedding_manager/utils/reused_pb_arena.h"

namespace ks {
namespace platform {

class AdInferFeatureEnricher : public CommonRecoBaseEnricher {
 public:
  AdInferFeatureEnricher() {
  }
  virtual ~AdInferFeatureEnricher() {
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;
  bool IsAsync() const override { return false; }

 private:
  bool InitProcessor() override;

  void EnrichByProtoAccessor(const absl::optional<absl::string_view> raw_features,
                             MutableRecoContextInterface *context,
                             RecoResultConstIter begin, RecoResultConstIter end);
  bool SaveDebugAttrs(bool use_arene, reco_arch::utils::ReusedPbArena *arena,
                      MutableRecoContextInterface *context);

 private:
  std::shared_ptr<ks::ad_nn::InferRequestContext> infer_req_ctx_;
  std::string input_serialized_features_attr_;
  std::string output_features_attr_;
  bool feature_compressed_ = false;
  bool use_protocol_accessor_ = false;

  DISALLOW_COPY_AND_ASSIGN(AdInferFeatureEnricher);
};

}  // namespace platform
}  // namespace ks
