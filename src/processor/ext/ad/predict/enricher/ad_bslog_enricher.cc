#include "dragon/src/processor/ext/ad/predict/enricher/ad_bslog_enricher.h"
#include <algorithm>
#include <fstream>
#include <utility>
#include "ks/base/shared_data/shared_data.h"
#include "teams/ad/ad_nn/service/service_util.h"
#include "teams/ad/ad_nn/service/wait_service_ready.h"
#include "teams/ad/ad_nn/utils/compress.h"
#include "teams/ad/ad_nn/flatten_raw_feature/flatten_helper.h"
#include "teams/ad/ad_nn/flatten_raw_feature/adlog_helper.h"
#include "teams/ad/ad_nn/flatten_raw_feature/utils.h"
#include "teams/ad/ad_nn/model/cleanup.h"
#include "teams/ad/ad_nn/feature_extract/processors/constant.h"
#include "teams/ad/ad_nn/feature/compact_feature_cache.h"
#include "teams/ad/ad_nn/item_union/item_manager.h"

DECLARE_bool(use_bs_reco_userinfo);
namespace ks {
namespace platform {
using ::google::protobuf::Arena;
using kuaishou::ad::reco::IndexRequest;
using kuaishou::ad::reco::IndexResponse;
using kuaishou::ad::reco::kess::IndexService;
using ks::ad_nn::BSItemWrapper;
using ks::ad_nn::BatchedSamples;
using ks::ad_nn::FeatureManager;
using ks::ad_nn::RawFeatureStore;
namespace flatten = ks::ad_nn::flatten;
bool AdBslogEnricher::InitProcessor() {
  // 默认使用优化的 raw feature
  FLAGS_use_simplified_raw_fea = true;
  FLAGS_use_simplified_raw_fea_list_type = true;
  FLAGS_use_bs_reco_userinfo = true;
  name_ = ks::ad_nn::GetKcsModelCmd();
  auto json_config = config();
  if (json_config == nullptr) return false;
  aa_alias_ = json_config->GetString("aa_alias", "");
  CL_LOG(INFO) << "bslog enricher set aa_alias to " << aa_alias_;
  item_already_ready_ = json_config->GetBoolean("item_already_ready", false);
  // 防止原分域架构迁移后使用非分域配置无法发现
  auto model_config = ks::ad_nn::PredictServiceKconfUtil::GetModelConfig(name_);
  if (((model_config && model_config->GetBoolean("use_component_item", false)) ||
      (json_config && json_config->GetBoolean("use_component_item", false))) && !item_already_ready_) {
    LOG(FATAL) << "component item must use AdItemEnricher and item_already_ready set true.";
    return false;
  }
  generate_bslog_ = json_config->GetBoolean("generate_bslog", true);
  bs_item_attr_ = json_config->GetString("bs_item_attr", "bs_item");
  item_miss_attr_ = json_config->GetString("item_miss_attr", "item_miss");
  bs_log_attr_ = json_config->GetString("bs_log_attr", "BslogBsItem");
  need_fake_user_ = json_config->GetBoolean("need_fake_user", false);

  if (item_already_ready_) {
    feature_manager_name_ = json_config->GetString("feature_manager_name", "default_name");
    if (!ks::SharedData<FeatureManager>::has_name(feature_manager_name_)) {
      LOG(FATAL) << "bslog enricher use item processor, but no init " << feature_manager_name_;
    }
    main_type_ = ks::ad_nn::ItemManager::Instance().GetMainType();
    CL_LOG(INFO) << "bslog enricher use item processor, skip init item.";
    return true;
  }
  // 非分域
  feature_manager_name_ = json_config->GetString("btq_incr_topic", "");
  if (feature_manager_name_.empty()) {
    LOG(ERROR) << "btq_incr_topic is not found in config";
    return false;
  }
  if (ks::SharedData<FeatureManager>::has_name(feature_manager_name_)) {
    auto manager = ks::SharedData<FeatureManager>::mutable_data(feature_manager_name_);
    main_type_ = ks::ad_nn::ItemManager::Instance().GetMainType();
  } else {
    auto ret = ks::SharedData<FeatureManager>::Create(feature_manager_name_);
    if (!ret) {
      LOG(INFO) << "create feature manager " << feature_manager_name_ << " failed";
      return false;
    }
    LOG(INFO) << "create feature manager " << feature_manager_name_ << " success";
    auto manager = ks::SharedData<FeatureManager>::mutable_data(feature_manager_name_);
    if (!ks::ad_nn::ItemManager::Instance().Init(*json_config, manager.get())) {
      LOG(FATAL) << "ItemManager start failed";
      return false;
    }
    main_type_ = ks::ad_nn::ItemManager::Instance().GetMainType();
    if (!manager->Start()) {
      LOG(ERROR) << "feature manager start failed";
      return false;
    }
    LOG(INFO) << "feature manager start succeeds";
    auto info_ptr = ks::ad_nn::ItemManager::Instance().GetItemComponentInfo(main_type_);
    if (info_ptr == nullptr) {
      LOG(FATAL) << "GetItemComponentInfo failed";
    }
  }
  return true;
}
void AdBslogEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) {
  falcon::Inc("feature_server.predict", 1);
  static std::once_flag init_flag;
  auto set_cb = [this]() {
    // 设置 store init bs 的回调
    if (!item_already_ready_) {
      bs_sample_attr_id2idx_ = ks::SharedData<ks::ad_nn::AttrId2Idx>::mutable_data(name_ + "_sample");
      auto bs_item_init_cb = [this](std::shared_ptr<BSItemWrapper> *wrapper) -> bool {
        if (wrapper == nullptr && !(*wrapper)) {
          LOG_EVERY_N(WARNING, 1000000) << "wrapper is a nullptr";
          return false;
        }
        return (*wrapper)->Init(bs_sample_attr_id2idx_->valid_index_cnt, bs_sample_attr_id2idx_, true);
      };
      auto info_ptr = ks::ad_nn::ItemManager::Instance().GetItemComponentInfo(main_type_);
      info_ptr->item_store->SetElementInitCallback<std::shared_ptr<BSItemWrapper> *>(bs_item_init_cb);
    }
    // 设置 item 更新 callback
    auto manager = ks::SharedData<FeatureManager>::mutable_data(feature_manager_name_);
    auto reader_name = ks::ad_nn::ItemInfoTypeStr(main_type_);
    CL_LOG(INFO) << "item get reader name: " << reader_name;
    auto reader = manager->GetReader(reader_name);
    auto item_feature_cache = ks::SharedData<ks::ad_nn::CompactFeatureCache>::mutable_data(name_);
    auto update_cb = [item_feature_cache](const std::vector<ks::ad_nn::FeatureData> &items,
                                          ks::ad_nn::Operation op) {
      for (auto &item : items) {
        item_feature_cache->Erase(item.item_id);
      }
      LOG_EVERY_N(INFO, 100000) << "clean " << items.size() << " updated items from item feature cache";
      falcon::Set("predict_server.item_feature_cache_total", item_feature_cache->Size(),
                  falcon::kNonAdditiveGauge);
    };
    if (item_feature_cache != nullptr && reader != nullptr) {
      reader->AddSubscriber("item_feature_cache", update_cb);
      LOG(INFO) << "AdBslogEnricher set_cb succ";
    } else {
      LOG(WARNING) << "AdBslogEnricher set_cb skip, item_feature_cache is nullptr: "
                   << (item_feature_cache != nullptr);
    }
  };
  std::call_once(init_flag, set_cb);
  // 重置请求相关的变量
  context_ = context;
  begin_ = begin;
  end_ = end;
  arena_.ResetArena();
  is_debug_req_ = context_->IsDebugRequest();
  // 构建 debug 请求的 batched_samples
  if (is_debug_req_) {
    LOG(INFO) << "A debug request";
    if (!BuildDebugBS()) {
      LOG(ERROR) << "debug bs build failed";
      return;
    }
    bs_log_.reset(new ks::ad_algorithm::BSLog());
    bs_log_->SetBS(simplified_debug_samples_.get());
    context_->SetPtrCommonAttr(bs_log_attr_, bs_log_.get());
    LOG(INFO) << "Debug BSlog Added";
    return;
  }

  auto bs_log = context->GetPtrCommonAttr<ks::ad_algorithm::BSLog>(bs_log_attr_);
  if (!bs_log) {
    bs_log_.reset(new ks::ad_algorithm::BSLog());
  } else {
    // 其他 processor 生产了 bslog, 生命周期由生产者负责
    LOG_EVERY_N(ERROR, 1000) << "has bslog already, just use it.";
    bs_log_.reset(const_cast<ks::ad_algorithm::BSLog *>(bs_log), [](const ks::ad_algorithm::BSLog *p) {});
  }

  batched_samples_.reset(new ks::ad_nn::DynamicBatchedSamples(0));
  bs_log_->SetBS(batched_samples_.get());
  custom_feature_builder_.reset(new flatbuffers::FlatBufferBuilder());
  batched_samples_->SetTimestamp(context_->GetRequestTime());
  batched_samples_->SetPrimaryKey(*context_->GetIntCommonAttr("llsid"));
  bs_items_.clear();
  fake_item_count_ = 0;
  item_res_count_ = 0;
  user_success_ = true;
  bool all_item_ready = false;
  if (!item_already_ready_)
    ConstructItem(&all_item_ready);
  user_success_ = ConstructUser();
  wait_user_ready_.Release();
  if (all_item_ready || item_already_ready_) {
    // 请求里的 item 都命中了本地缓存，直接 build bs
    // 有远程物料请求时在异步回调线程里调用
    BuildBatchedSamples();
  }
  // 进行行业 5.0 的预映射, 避免后续特征抽取时进行重复映射
  uint64_t unique_key = ks::ad_algorithm::label_centre::GenerateUniqueKey();
  if (ks::ad_algorithm::label_centre::LabelCentreMapping::Instance().Inited()) {
    auto pre_mapping_start = ks::ad_nn::Clock::now();
    bs_log_->set_unique_key(unique_key);
    if (!ks::ad_algorithm::label_centre::LabelCentreMapping::Instance().PreMapping(*bs_log_)) {
      LOG_EVERY_N(WARNING, 1000) << "LabelCentreMapping failed";
    }
    falcon::Stat("feature_server.label_centre_pre_mapping_cost",
                 ks::ad_nn::GetMicroseconds(pre_mapping_start));
  }
}
bool AdBslogEnricher::BuildDebugBS() {
  const BatchedSamples* debug_bs = nullptr;
  // 获取原始字符串
  auto bs_debug_raw_data = *context_->GetStringCommonAttr("bs_debug_raw_data");
  // raw str -> BatchedSamples
  debug_bs = flatten::StringToTable<ks::cofea_fbs::BatchedSamples>(bs_debug_raw_data.data(),
                                                                    bs_debug_raw_data.size());
  if (debug_bs == nullptr) {
    LOG(ERROR) << "debug request parse batched samples failed from the payload";
    return false;
  }
  // PrintBatchedSamples(*debug_bs);
  // BatchedSamples -> StaticBatcedSamples and Initialization
  if (!bs_user_attr_id2idx_) {
    bs_user_attr_id2idx_ = ks::SharedData<ks::ad_nn::AttrId2Idx>::mutable_data(name_ + "_user");
    bs_all_prune_attr_id2idx_ = ks::SharedData<ks::ad_nn::AttrId2Idx>::mutable_data(name_ + "_all");
    LOG(INFO) << "shared_data name:" << name_
              << "bs_user_attr_id2idx:" << bs_user_attr_id2idx_.get()
              << " bs_all_prune_attr_id2idx:" << bs_all_prune_attr_id2idx_.get();
  }
  if (!bs_sample_attr_id2idx_) {
    bs_sample_attr_id2idx_ = ks::SharedData<ks::ad_nn::AttrId2Idx>::mutable_data(name_ + "_sample");
  }
  simplified_debug_samples_.reset(new ks::ad_nn::SimplifiedSample(debug_bs, bs_sample_attr_id2idx_,
                                  bs_user_attr_id2idx_, bs_all_prune_attr_id2idx_, true));
  if (!simplified_debug_samples_->Initialize()) {
    LOG(ERROR) << "debug simplified batched samples initialize failed";
    return false;
  }
  return true;
}
void AdBslogEnricher::PrintBatchedSamples(const BatchedSamples& bs) {
  auto print_bs_info = [](const std::string& prefix, const ks::cofea_fbs::FeatureSetsT* ft) {
    LOG(INFO) << "[Debug] prefix: " << prefix
              << ", is nullptr: " << (ft == nullptr);
    if (ft != nullptr) {
      for (int i = 0; i < ft->feature_columns.size(); ++i) {
        if (ft->feature_columns[i]) {
          LOG(INFO) << "[Debug] index: " << i
                    << ", " << flatten::DebugString(*ft->feature_columns[i]);
        } else {
          LOG(INFO) << "[Debug] index: " << i
                    << ", nullptr";
        }
      }
    }
  };
  std::unique_ptr<ks::cofea_fbs::BatchedSamplesT> bst(bs.UnPack());
  LOG(INFO) << "[Debug] primary key: " << bst->primary_key
            << ", timestamp: " << bst->timestamp;
  print_bs_info("context", bst->context.get());
  print_bs_info("sub_context", bst->sub_context[0].get());
  print_bs_info("samples", bst->samples.get());
  return;
}
bool AdBslogEnricher::ConstructUser() {
  bs_opt_userinfo_ = absl::string_view();
  auto data_map_names = context_->GetStringListCommonAttr("flatten_data_map_name");
  auto data_map_values = context_->GetStringListCommonAttr("flatten_data_map_value");
  if (data_map_names && !data_map_names->empty()) {
    if (!data_map_values || data_map_names->size() != data_map_values->size()) {
      LOG_EVERY_N(WARNING, 1000) << "full user info build failed";
      falcon::Inc("feature_server.bs_build_invalid_data_map", 1);
      return false;
    }
    for (size_t idx = 0; idx < data_map_names->size(); ++idx) {
      if ((*data_map_names)[idx] == "bs_opt_userinfo") {
        bs_opt_userinfo_ = (*data_map_values)[idx];
      }
    }
  }
  if (!bs_user_attr_id2idx_) {
    bs_user_attr_id2idx_ = ks::SharedData<ks::ad_nn::AttrId2Idx>::mutable_data(name_ + "_user");
    bs_all_prune_attr_id2idx_ = ks::SharedData<ks::ad_nn::AttrId2Idx>::mutable_data(name_ + "_all");
    LOG(INFO) << "shared_data name:" << name_
              << "bs_user_attr_id2idx:" << bs_user_attr_id2idx_.get()
              << " bs_all_prune_attr_id2idx:" << bs_all_prune_attr_id2idx_.get();
  }
  batched_samples_->SetAllPruneAttrId2Idx(bs_all_prune_attr_id2idx_);
  if (!need_fake_user_ && bs_opt_userinfo_.empty()) {
    falcon::Inc("feature_server.no_bs_opt_user", 1);
    LOG_EVERY_N(WARNING, 10000) << "no user info found";
    context_->SetExecutionStatus(ExecutionStatus::TERMINATED);
    return false;
  } else {
    std::shared_ptr<ks::ad_nn::BSUserWrapper> bs_user_wrapper = std::make_shared<ks::ad_nn::BSUserWrapper>();
    bs_user_wrapper->Init(bs_user_attr_id2idx_, true);
    batched_samples_->SetUserWrapper(bs_user_wrapper);
    if (!bs_opt_userinfo_.empty()) {
      auto simple_req_sub_context = ks::ad_nn::flatten::StringToTable<ks::ad_nn::SimplifiedRawFeature>(
          bs_opt_userinfo_.data(), bs_opt_userinfo_.size());
      if (simple_req_sub_context == nullptr) {
        LOG_EVERY_N(WARNING, 10000) << "Failed to convert bs_opt_userinfo";
        return false;
      }
      auto fields = simple_req_sub_context->fields();
      if (fields == nullptr) {
        LOG_EVERY_N(ERROR, 100000) << "simple fields get failed.";
        return false;
      }
      bs_user_wrapper->BuildRawFeasSimple(*fields);
    }
  }

  // reco_user_info 保留 pb 格式
  if (!FLAGS_use_bs_reco_userinfo &&
                        !FillRecoUserInfo(std::string(*context_->GetStringCommonAttr("reco_user_info")),
                        *context_->GetIntCommonAttr("reco_user_info_compress_flag"),
                        *context_->GetIntCommonAttr("reco_user_info_uncompressed_size"))) {
    falcon::Inc("feature_server.fill_reco_user_info_fail", 1);
  }
  // trick features
  if (!FillCustomFeature()) {
    falcon::Inc("feature_server.bs_build_custom_feature_fail", 1);
  }
  return true;
}

bool AdBslogEnricher::FillBSAttrRealTimeAction() {
  auto name_list = context_->GetStringListCommonAttr("attr_real_time_action_name");
  auto value_list = context_->GetStringListCommonAttr("attr_real_time_action_value");
  if (!name_list || !value_list || name_list->size() <=0 || value_list->size() <= 0) {
    LOG_EVERY_N(INFO, 10000) << "got no attr realtime action";
    return false;
  }
  if (name_list->size() != value_list->size()) {
    LOG_EVERY_N(WARNING, 10000) << "attr realtime action name list size:" << name_list->size()
                                << " is not equal to value list size:" << value_list->size();
    return false;
  }
  for (size_t idx = 0; idx < name_list->size(); ++idx) {
    auto &value = (*value_list)[idx];
    auto attr_bs = ks::ad_nn::flatten::StringToTable<ks::cofea_fbs::FeatureSets>(value.data(), value.size());
    if (attr_bs == nullptr) {
      falcon::Inc("feature_server.flatten_attr_real_time_action_to_table_fail", 1);
      LOG_EVERY_N(WARNING, 10000) << "attr real time featuresets is nullptr, key:" << (*name_list)[idx]
                                  <<  " data size:" << value.size();
      continue;
    }
    if (!batched_samples_->AddFeaturesets(*attr_bs, ks::ad_nn::flatten::AttrType::kContext)) {
      falcon::Inc("feature_server.add_attr_real_time_featuresets_fail", 1);
      LOG_EVERY_N(WARNING, 100000) << "add attr real time failed" << (*name_list)[idx];
    }
  }
  return true;
}
bool AdBslogEnricher::FillRecoUserInfo(const std::string &raw_value,
                                       uint32_t compress_type, uint32_t decompress_size) {
  if (raw_value.empty()) {
    return true;
  }
  falcon::Inc("dnn_predict_server.request_with_reco_user_count", 1);
  std::string decompress_str;
  const std::string *reco_str = &raw_value;
  if (compress_type > 0) {
    decompress_str.resize(decompress_size);
    if (!ks::ad_nn::decompress(compress_type, raw_value, decompress_str)) {
      LOG_EVERY_N(WARNING, 10000) << "decompress reco user info failed";
      return false;
    }
    reco_str = &decompress_str;
  }
  if (!bs_log_->mutable_reco_user_info()->ParseFromString(*reco_str)) {
    falcon::Inc("dnn_predict_server.reco_user_parse_error_count", 1);
    LOG_EVERY_N(WARNING, 1000) << "Parse reco user info failed llsid " << context_->GetRequestId();
    return false;
  }
  return true;
}

// rank_name/aa_alias 等比较 trick 的特征数据
bool AdBslogEnricher::FillCustomFeature() {
  // TODO(yangjialin) 支持共享 feature 逻辑
  auto cmd = *context_->GetStringCommonAttr("cmd");
  // aa_alias
  std::vector<flatbuffers::Offset<ks::cofea_fbs::FeatureSet>> custom_feature_offsets;
  if (!aa_alias_.empty()) {
    ks::ad_nn::flatten::AddPbSingular("90308", aa_alias_, custom_feature_builder_.get(),
                                      &custom_feature_offsets);
  } else if (!cmd.empty()) {
    ks::ad_nn::flatten::AddPbSingular("90308", cmd, custom_feature_builder_.get(),
                           &custom_feature_offsets);
  }
  // tab_type
  auto tab_type = *context_->GetIntCommonAttr("tab_type");
  if (tab_type != kuaishou::ad::algorithm::UNKNOWN_TAB) {
    if (!ks::ad_nn::flatten::AddFeatureSet("51936", tab_type,
                                custom_feature_builder_.get(), &custom_feature_offsets)) {
      LOG_EVERY_N(WARNING, 100000) << "Add adlog.tab failed";
    }
  }
  // need fake user info
  if (need_fake_user_) {
    bool need_insert_id = true;
    const flatbuffers::Vector<flatbuffers::Offset<ks::ad_nn::Field>>* fields = nullptr;
    if (!bs_opt_userinfo_.empty())
      fields = ks::ad_nn::GetFields(bs_opt_userinfo_.data(), bs_opt_userinfo_.size(),  true);
    if (fields == nullptr) {
      LOG_EVERY_N(ERROR, 100000) << "simple fields get failed.";
    } else {
      for (int i = 0; i < fields->size(); ++i) {
        auto field = fields->Get(i);
        if (field == nullptr || field->name() == nullptr) {
          LOG_EVERY_N(INFO, 10000000) << "field is invaild, index: " << i;
          continue;
        }
        if (absl::string_view(field->name()->c_str()) == "0") {
          need_insert_id = false;
          break;
        }
      }
    }
    if (need_insert_id) {
      if (!ks::ad_nn::flatten::AddFeatureSet("0", static_cast<int64_t>(0), custom_feature_builder_.get(),
                                  &custom_feature_offsets)) {
        LOG_EVERY_N(WARNING, 100000) << "Fake user id failed, user id: " << context_->GetUserId();
      }
      if (!ks::ad_nn::flatten::AddFeatureSet("73884", 1LL, custom_feature_builder_.get(),
                                  &custom_feature_offsets)) {
        LOG_EVERY_N(WARNING, 100000) << "Fake user exists failed, user id: " << context_->GetUserId();
      }
      LOG_EVERY_N(INFO, 100000) << "Fake user id success, user id: " << context_->GetUserId();
    }
  }
  auto feature_sets_offset =
      ks::cofea_fbs::CreateFeatureSetsDirect(*custom_feature_builder_, &custom_feature_offsets);
  custom_feature_builder_->Finish(feature_sets_offset);
  char* buf = reinterpret_cast<char*>(custom_feature_builder_->GetBufferPointer());
  auto custom_feature_sets = ks::ad_nn::flatten::StringToTable<ks::cofea_fbs::FeatureSets>(
                                  buf, custom_feature_builder_->GetSize());
  if (!batched_samples_->AddSubContextAttrs(ks::ad_nn::added_bs_attr_keys::kBsCustomFeature,
                                            custom_feature_sets)) {
    falcon::Inc("feature_server.set_custom_feature_fail", 1);
    LOG_EVERY_N(WARNING, 100000) << "Set request sub context failed";
    return false;
  }
  falcon::Inc("feature_server.set_custom_feature_success", 1);
  // rank_cmd
  if (!aa_alias_.empty() && cmd.size() > 0 && !bs_opt_userinfo_.empty()) {
    auto req_sub_ret = ks::ad_nn::flatten::StringToMu2Table<ks::ad_nn::SimplifiedRawFeature>(
        bs_opt_userinfo_.data(), bs_opt_userinfo_.size());
    if (req_sub_ret == nullptr) {
      LOG_EVERY_N(WARNING, 10000) << "Failed to convert req_context";
      return false;
    }
    ks::ad_nn::flatten::SetRankCmd(std::string(cmd), aa_alias_, req_sub_ret);
  }
  return true;
}
bool AdBslogEnricher::ConstructItem(bool *all_item_ready) {
  auto info_ptr = ks::ad_nn::ItemManager::Instance().GetItemComponentInfo(main_type_);
  if (info_ptr == nullptr) return false;
  *all_item_ready = true;
  if (begin_ == end_) {
    LOG_EVERY_N(WARNING, 100000) << "get no item request";
    falcon::Inc("feature_server.no_item_req", 1);
    return true;
  }
  bs_items_.resize(end_ - begin_);
  std::unordered_map<size_t, std::vector<size_t>> local_miss_items;  // item_id->item_idx
  auto iter = begin_;
  for (size_t idx = 0; idx < bs_items_.size(); ++idx, ++iter) {
    uint64_t item_id = iter->GetId();
    auto item = info_ptr->item_store->GetStoreData<std::shared_ptr<BSItemWrapper>>(item_id);
    if (item.update_time > 0 && item.data != nullptr) {
      bs_items_[idx] = item.data;
      LOG_EVERY_N(INFO, 6000000) << "item:" << item_id
                                 << " update_time:" << item.update_time
                                 << " elapse:" << context_->GetRequestTime() / 1000 - item.update_time;
    } else {
      local_miss_items[item_id].emplace_back(idx);
    }
  }
  if (local_miss_items.empty()) {
    // 已经获取到所有数据
    falcon::Inc("feature_server.no_remote_item_req", 1);
    return true;
  }
  // 请求远程物料获取未命中 local store 的 item
  *all_item_ready = false;
  falcon::Inc("feature_server.req_item_cnt_from_remote", local_miss_items.size());
  falcon::Stat("feature_server.req_remote_item_count", local_miss_items.size());
  std::vector<std::vector<IndexRequest*>> requests;
  std::vector<std::vector<IndexResponse*>> responses;
  PrepareReqItemServer(*info_ptr, local_miss_items, &requests, &responses);
  auto options = ks::ad_base::OptionsFromMilli(info_ptr->item_service_timeout);
  size_t total_req_count = 0;
  for (size_t shard = 0; shard < requests.size(); ++shard) {
    total_req_count += requests[shard].size();
  }
  falcon::Inc("feature_server.get_remote_item_freq", total_req_count);
  for (size_t shard = 0; shard < requests.size(); ++shard) {
    std::string peer;
    for (int idx = 0; idx < requests[shard].size(); ++idx) {
      auto stub = info_ptr->item_server_clients[shard]->SelectOne(&peer);
      auto& request = requests[shard][idx];
      auto call_back = std::bind(&AdBslogEnricher::ProcessItemServerResponse,
                                 this, request, local_miss_items, total_req_count,
                                 std::placeholders::_1);
      auto future = stub->AsyncGetCreative(options, *request, responses[shard][idx],
                                           info_ptr->item_server_event_loop->SelectOne());
      RegisterAsyncCallback(context_, std::move(future), std::move(call_back));
    }
  }
  return true;
}
void AdBslogEnricher::PrepareReqItemServer(
    const ks::ad_nn::ItemServerInfo& info, const std::unordered_map<size_t, std::vector<size_t>> &items,
    std::vector<std::vector<IndexRequest*>>* requests,
    std::vector<std::vector<IndexResponse*>>* responses) {
  // 将 item 按 shard 分组
  std::vector<std::vector<uint64_t>> shard_item_ids(info.item_server_shard_num);
  for (auto &item : items) {
    auto shard_config = info.item_server_shard_config;
    auto item_id = item.first;
    size_t shard_id = shard_config->enable ? shard_config->GetShardId(item_id) : 0;
    shard_item_ids[shard_id].emplace_back(item_id);
  }
  requests->resize(info.item_server_shard_num);
  responses->resize(info.item_server_shard_num);
  for (size_t shard_id = 0; shard_id < shard_item_ids.size(); ++shard_id) {
    auto &item_ids = shard_item_ids[shard_id];
    auto &shard_req = (*requests)[shard_id];
    auto &shard_res = (*responses)[shard_id];
    for (size_t step_begin = 0; step_begin < item_ids.size();
         step_begin += info.req_item_service_batch_num) {
      size_t step = std::min(item_ids.size() - step_begin,
                             static_cast<size_t>(info.req_item_service_batch_num));
      auto req = Arena::CreateMessage<IndexRequest>(arena_.GetArena());
      req->set_req_cmd(name_);
      for (size_t sidx = 0; sidx < step; ++sidx) {
        req->mutable_creative_id()->Add(item_ids[step_begin + sidx]);
      }
      shard_req.emplace_back(req);
      shard_res.emplace_back(Arena::CreateMessage<IndexResponse>(arena_.GetArena()));
    }
  }
}
void AdBslogEnricher::ProcessItemServerResponse(
    const IndexRequest *request, const std::unordered_map<size_t, std::vector<size_t>> &local_miss_items,
    uint32_t total_req_count, IndexResponse* response) {
  auto info_ptr = ks::ad_nn::ItemManager::Instance().GetItemComponentInfo(main_type_);
  if (info_ptr == nullptr) return;
  auto finally = tensorflow::serving::MakeCleanup([&] {
    if (item_res_count_.fetch_add(1) == total_req_count - 1) {
      BuildBatchedSamples();
    }
  });
  if (response->creative_info_size() != request->creative_id_size()) {
    LOG_EVERY_N(WARNING, 100000) << "response and request not match, request size = "
        << request->creative_id_size() << ", response size = " << response->creative_info_size();
    falcon::Inc("feature_server.get_remote_item_fail", 1);
    return;
  }
  int local_fake_item_count = 0;
  for (int item_idx = 0; item_idx < response->creative_info_size(); ++item_idx) {
    auto item_id = request->creative_id(item_idx);
    if (local_miss_items.count(item_id) <= 0) {
      LOG_EVERY_N(INFO, 100000) << "item id not found in locall miss items";
      continue;
    }
    for (auto dst_idx : local_miss_items.at(item_id)) {
      auto& raw_data = response->creative_info(item_idx).value();
      if (raw_data.empty()) {
        ++local_fake_item_count;
        LOG_EVERY_N(INFO, 100000) << "item id not found, will fake it: " << item_id;
        continue;
      }
      auto bs_item_wrapper = std::make_shared<BSItemWrapper>();
      bs_item_wrapper->flat_str = raw_data;
      bs_item_wrapper->need_convert_str2simple = false;
      if (!ks::ad_nn::flatten::Verify<ks::ad_nn::SimplifiedRawFeature>(
              bs_item_wrapper->flat_str.c_str(),
              bs_item_wrapper->flat_str.size())) {
        LOG_EVERY_N(ERROR, 1000) << "verify str failed for item: " << item_id;
        ++local_fake_item_count;
        falcon::Inc("dnn_predict_server.flatten_verify_except", 1);
        continue;
      }
      ks::ad_nn::StoreData<std::shared_ptr<BSItemWrapper>> store(
          static_cast<int64_t>(context_->GetRequestTime() / 1000), bs_item_wrapper);
      bs_item_wrapper->data_source = ks::ad_nn::DataSource::DS_RPC;
      if (!info_ptr->item_store->TryAddMutableStoreData<std::shared_ptr<BSItemWrapper>>(
              static_cast<int64_t>(item_id), &store)) {
        LOG_EVERY_N(WARNING, 100000) << "TryAddMutableStoreData failed for item:" << item_id;
      }
      bs_items_[dst_idx] = bs_item_wrapper;
    }
  }
  fake_item_count_.fetch_add(local_fake_item_count);
}
bool AdBslogEnricher::BuildBatchedSamples() {
  if (item_already_ready_) {
    auto items_ptr = context_->GetPtrCommonAttr<
          std::vector<std::shared_ptr<ks::ad_nn::BSItemWrapper>>>(bs_item_attr_);
    if (items_ptr == nullptr) {
      LOG_EVERY_N(ERROR, 1000) << "invaild situation, not get item ptr.";
      return false;
    }
    auto item_size = items_ptr->size();
    std::vector<std::shared_ptr<BSItemWrapper>> items = *items_ptr;
    batched_samples_->SetSamples(std::move(items));
    batched_samples_->SetSampleLens({item_size});
  } else {
    auto item_size = bs_items_.size();
    auto miss_accessor = context_->GetItemAttrAccessor(item_miss_attr_);
    if (item_size > 0) {
      uint32_t empty_item_count = 0;
      for (size_t idx = 0; idx < bs_items_.size(); ++idx) {
        auto &item = bs_items_[idx];
        if (!item || item->flat_str.empty()) {
          ++empty_item_count;
          context_->SetIntItemAttr(*(begin_ + idx), miss_accessor, 1);
        }
      }
      falcon::Stat("feature_server.fake_remote_item_rate", 1e6 * fake_item_count_ / item_size);
      falcon::Stat("feature_server.empty_item_rate", 1e6 * empty_item_count / item_size);
    }
    std::unordered_map<int, std::vector<std::shared_ptr<BSItemWrapper>>> bs_item_wrappers_map;
    bs_item_wrappers_map[ks::ad_nn::ItemInfoType::FULL] = std::move(bs_items_);
    batched_samples_->SetSamples(std::move(bs_item_wrappers_map));
    batched_samples_->SetSampleLens({item_size});
  }
  auto callback_event_accessor = context_->GetItemAttrAccessor("callback_event");
  for (auto iter = begin_; iter != end_; ++iter) {
    auto attr_val = iter->GetIntAttr(callback_event_accessor);
    if (!attr_val) {
      LOG_EVERY_N(WARNING, 100000) << "no valid callback event";
      break;
    }
    bs_log_->add_ad_callback_event(static_cast<::bs::kuaishou::ad::AdCallbackLog::EventType>(*attr_val));
  }
  auto bs_init_start = ks::ad_nn::Clock::now();
  wait_user_ready_.Acquire();
  if (!user_success_) {
    LOG_EVERY_N(WARNING, 1000) << "build user failed, return directly";
    return false;
  }
  // TODO(yangjialin) 改为并发?
  if (!batched_samples_->Initialize()) {
    falcon::Inc("feature_server.init_batched_samples_fail", 1);
    LOG(ERROR) << "batched samples init failed";
  }
  if (context_->IsDebugRequest() && FLAGS_logging_switch_uid_mod_divisor == 1) {
    auto raw_bs = batched_samples_->ToString();
    std::ofstream dst("bs_" + context_->GetRequestId());
    dst << "batched sample, llsid:" << context_->GetRequestId() << " content:"
        << ks::ad_nn::flatten::DebugString(*ks::ad_nn::flatten::StringToTable<BatchedSamples>(raw_bs));
    dst.close();
  }
  falcon::Stat("feature_server.construct_bs_init_cost", ks::ad_nn::GetMicroseconds(bs_init_start));
  if (generate_bslog_) context_->SetPtrCommonAttr(bs_log_attr_, bs_log_.get());
  return true;
}
typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AdBslogEnricher, AdBslogEnricher)
}  // namespace platform
}  // namespace ks
