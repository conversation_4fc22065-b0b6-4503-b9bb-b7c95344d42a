#include "dragon/src/processor/ext/uni_predict_v2/embedding_fetcher/embedding_server_fetcher.h"

#include <algorithm>
#include <atomic>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/util/logging_util.h"
#include "serving_base/server_base/kess_client.h"
#include "serving_base/utility/timer.h"
#include "teams/reco-arch/uni-predict-v2/src/utils/monitor.h"

DEFINE_bool(local_fetch_embedding_ignore_size, false, "ignore local fetch embedding size");
DEFINE_bool(cache_empty_embedding, false, "cache empty embedding.");

namespace ks {
namespace platform {
namespace embedding_fetcher {

#define BASE_CONFIG_CHECK(cond, msg) \
  if (!(cond)) {                     \
    *err_msg = (msg);                \
    return false;                    \
  }

#define CONFIG_CHECK(cond, msg) \
  if (!(cond)) {                \
    *err_msg = (msg);           \
    return nullptr;             \
  }

bool BuildBaseParam(const base::Json *config, const std::string &kess_service,
                    BaseEmbeddingServerFetchParameter *base_param, std::string *err_msg) {
  base_param->kess_service = kess_service;
  auto embedding_timeout_ms = config->GetInt("timeout_ms", 10);
  BASE_CONFIG_CHECK(embedding_timeout_ms > 0, "timeout_ms must be > 0");
  auto max_signs_per_request = config->GetInt("max_signs_per_request", 0);
  auto index = config->GetInt("fetcher_index", 0);

  base_param->index = index;
  base_param->timeout_ms = embedding_timeout_ms;
  base_param->max_signs_per_request = max_signs_per_request;
  base_param->local_cache_config.key = base_param->kess_service;

  // parse embedding cache args
  // 如果没有 cache_config 这个选项, 那么都从 flags 里读取. 因为 EmbeddingCacheConfig 就已经从 flags 读了,
  // 所以直接构造 config 就行
  if (config->Get("cache_config")) {
    auto cache_config = config->Get("cache_config");
    base_param->enable_cache = cache_config->GetBoolean("enable_cache", base_param->enable_cache);
    base_param->embedding_skip_cache_ratio =
        cache_config->GetFloat("skip_ratio", base_param->embedding_skip_cache_ratio);
    base_param->local_cache_version = cache_config->GetInt("version", base_param->local_cache_version);
    base_param->local_cache_config.key_capacity =
        cache_config->GetInt("key_capacity", base_param->local_cache_config.key_capacity);
    base_param->local_cache_config.expire_seconds =
        cache_config->GetInt("expire_seconds", base_param->local_cache_config.expire_seconds);
    base_param->local_cache_config.lookup_thread_num =
        cache_config->GetInt("lookup_thread_num", base_param->local_cache_config.lookup_thread_num);
    base_param->local_cache_config.mem_capacity =
        cache_config->GetInt("mem_capacity", base_param->local_cache_config.mem_capacity);
    base_param->local_cache_config.shard_num =
        cache_config->GetInt("shard_num", base_param->local_cache_config.shard_num);
    base_param->local_cache_config.update_thread_num =
        cache_config->GetInt("update_thread_num", base_param->local_cache_config.update_thread_num);
  }
  return true;
}

std::shared_ptr<BtEmbeddingServerFetcher> BuildBtEmbeddingServerFetcherFromConfig(const base::Json *config,
                                                                                  std::string *err_msg) {
  BaseEmbeddingServerFetchParameter base_param;
  BtEmbeddingServerExtraParameter bt_extra_param;
  // 1. parse embedding server args
  auto kess_service = config->GetString("kess_service");
  CONFIG_CHECK(!kess_service.empty(), "kess_service of bt embedding server not specified");
  auto kess_cluster = config->GetString("kess_cluster", "PRODUCTION");
  auto kess_thread_num = config->GetInt("thread_num", 1);
  auto shards = config->GetInt("shards", -1);
  CONFIG_CHECK(shards > 0, "the config of shards is invalid");
  auto client_side_shard = config->GetBoolean("client_side_shard", false);
  bt_extra_param.num_shards = shards;
  bt_extra_param.client_side_shard = client_side_shard;
  bt_extra_param.kess_cluster = kess_cluster;
  bt_extra_param.kess_thread_num = kess_thread_num;
  bt_extra_param.hash_input_attr = config->GetString("hash_input_attr", "");

  // 2. 构造 base_param
  if (!BuildBaseParam(config, kess_service, &base_param, err_msg)) {
    return nullptr;
  }

  // 3. 构造 fetcher
  return std::make_shared<BtEmbeddingServerFetcher>(base_param, bt_extra_param);
}

std::shared_ptr<ColossusdbEmbeddingServerFetcher> BuildColossusdbEmbeddingServerFetcherFromConfig(
    const base::Json *config, std::string *err_msg) {
  BaseEmbeddingServerFetchParameter base_param;
  ColossusdbEmbeddingServerExtraParameter colossusdb_extra_param;
  // 1. parse embedding server args
  auto colossusdb_embd_model_name = config->GetString("colossusdb_embd_model_name", "");
  if (colossusdb_embd_model_name.empty()) {
    colossusdb_embd_model_name = config->GetString("colossusdb_embd_service_name");
  }
  auto colossusdb_embd_table_name = config->GetString("colossusdb_embd_table_name");
  auto use_kconf_client = config->GetBoolean("use_kconf_client", true);
  CONFIG_CHECK(!colossusdb_embd_model_name.empty(), "colossusdb_embd_model_name not specified");
  CONFIG_CHECK(!colossusdb_embd_table_name.empty(), "colossusdb_embd_table_name not specified");
  colossusdb_extra_param.service_name = colossusdb_embd_model_name;
  colossusdb_extra_param.table_name = colossusdb_embd_table_name;
  colossusdb_extra_param.use_kconf_client = use_kconf_client;

  // use_kconf_client 的情况下这不是真正的 kess_name, 不过不影响使用
  auto kess_service = colossusdb_embd_model_name + " " + colossusdb_embd_table_name;

  // 2. 构造 base_param
  if (!BuildBaseParam(config, kess_service, &base_param, err_msg)) {
    return nullptr;
  }

  // 3. 构造 fetcher
  return std::make_shared<ColossusdbEmbeddingServerFetcher>(base_param, colossusdb_extra_param);
}

BaseEmbeddingServerFetcher::BaseEmbeddingServerFetcher(const BaseEmbeddingServerFetchParameter &base_param)
    : base_param_(base_param) {
  // init local cache
  if (base_param_.enable_cache) {
    if (FLAGS_local_fetch_embedding_ignore_size && FLAGS_cache_empty_embedding) {
      LOG(FATAL) << "`FLAGS_cache_empty_embedding` and `FLAGS_local_fetch_embedding_ignore_size` cannot be "
                    "set at the same time";
    }
    switch (base_param_.local_cache_version) {
      case 1:
        local_cache_ =
            embedding_cache::GlobalEmbeddingCache::GetOrCreateInstance(base_param_.local_cache_config);
        break;
      case 2:
        local_cache_ =
            embedding_cache::GlobalEmbeddingCacheV2::GetOrCreateInstance(base_param_.local_cache_config);
        break;
      default:
        LOG(FATAL) << "version in cache_config should be 1 or 2, but get " << base_param_.local_cache_version;
        break;
    }
    random_seed_ = base::GetTimestamp() & 0xFFFFFFFF;  // 保留后 32 位
  }
}

bool BaseEmbeddingServerFetcher::FetchEmbedding(
    MutableRecoContextInterface *context, Map<uint64_t, uint32_t> *all_signs,
    std::vector<std::pair<const void *, size_t>> *sign_to_embedding) {
  auto &all_signs_ref = *all_signs;
  if (all_signs_ref.size() == 0) {
    CL_LOG(INFO) << "skip FetchEmbedding as parameter set is empty";
    return true;
  }
  auto &sign_to_embedding_ref = *sign_to_embedding;
  bool need_update_local_cache = base_param_.enable_cache;
  size_t cached_signs = 0, sent_signs = 0;
  // 1. fetch from local cache
  if (base_param_.enable_cache &&
      ((rand_r(&random_seed_) % 100) / 100.0 > base_param_.embedding_skip_cache_ratio)) {
    if (!FetchEmbeddingLocal(all_signs_ref, context)) {
      CL_LOG(WARNING) << "fetch embedding from local cache failed";
    }
    size_t num_empty_embedding = 0;
    for (size_t i = 0; i < local_results_.size(); ++i) {
      uint64_t key = local_results_[i].first;
      auto &value = local_results_[i].second;
      auto it = all_signs_ref.find(key);
      if (it == all_signs_ref.end()) {
        CL_LOG_ERROR("uni_predict_fused", "abnormal_sign_in_cache")
            << "local_result get invalid sign: " << key << ". debug info: "
            << "local_results_.size() = " << local_results_.size()
            << ", all_signs.size() = " << all_signs_ref.size() << ", local_result_index = " << i
            << ", key = " << key;
        continue;
      }
      int pos = it->second;
      if (sign_to_embedding_ref[pos].second != 0) {
        CL_LOG_ERROR("uni_predict_fused", "duplicate_sign_in_cache")
            << "duplicate sign found in cache: " << key;
      } else {
        if (value.data == nullptr) {
          if (value.size == CACHE_EMPTY_EMBEDDING_FLAG) {
            ++num_empty_embedding;
            value.size = 0;
          }
        }
        sign_to_embedding_ref[pos] = std::make_pair(reinterpret_cast<const char *>(value.data), value.size);
      }
    }
    if (num_empty_embedding != 0) {
      size_t local_cache_size = local_results_.size();
      local_cache_size = local_cache_size == 0 ? 1 : local_cache_size;
      CL_LOG(INFO) << "empty embedding hit_rate in embedding cache result: "
                   << num_empty_embedding * 1.0f / local_cache_size
                   << ", #empty_embedding: " << num_empty_embedding
                   << ", #cache_embedding: " << local_results_.size() << ", " << base_param_.kess_service;
    }
    for (const auto &result : local_results_) {
      all_signs_ref.erase(result.first);
    }
    cached_signs = local_results_.size();
    need_update_local_cache = false;  // 只要查了 cache 就不用更新
  }

  // 2. fetch other signs from remote
  sent_signs = all_signs_ref.size();
  if (!FetchEmbeddingRemote(all_signs_ref, context)) {
    CL_LOG(WARNING) << "fetch embedding failed from remote: " << base_param_.kess_service;
  }

  // 3. update cache
  if (need_update_local_cache) {
    UpdateLocalCache(all_signs_ref);
    local_results_.clear();  // 因为这次从 cache 读的话, local_result_ 保留了上次请求的数据, 需要清除
  }

  // 4. redirect to response and local_results
  MergeRemoteEmbedding(all_signs, sign_to_embedding);

  CL_LOG(INFO) << "signs size, cached: " << cached_signs << ", sent: " << sent_signs;
  return true;
}

bool BaseEmbeddingServerFetcher::FetchEmbeddingLocal(const Map<uint64_t, uint32_t> &signs,
                                                     MutableRecoContextInterface *context) {
  int64 start_ts = base::GetTimestamp();
  local_results_.clear();
  switch (base_param_.local_cache_version) {
    case 1:
      reinterpret_cast<embedding_cache::GlobalEmbeddingCache *>(local_cache_)
          ->BatchGet(signs, &local_results_, FLAGS_local_fetch_embedding_ignore_size);
      break;
    case 2:
      reinterpret_cast<embedding_cache::GlobalEmbeddingCacheV2 *>(local_cache_)
          ->BatchGet(signs, &local_results_);
      break;
    default:
      LOG(FATAL) << "version in cache_config should be 1 or 2, but get " << base_param_.local_cache_version;
      break;
  }
  float hit_rate = local_results_.size() * 1.0f / signs.size();
  LOG_EVERY_N(INFO, 1000) << processor_name_ << ", " << base_param_.kess_service
                          << " embedding cache hit_rate: " << hit_rate;
  int64 duration = base::GetTimestamp() - start_ts;
  reco_arch::uni_predict::MonitorManager::GetInstance().ReportFetchLocalTime(
      model_key_, base_param_.kess_service, duration);
  reco_arch::uni_predict::MonitorManager::GetInstance().ReportEmbeddingCacheHitRate(
      model_key_, base_param_.kess_service, hit_rate * 1000);
  return true;
}

BtEmbeddingServerFetcher::BtEmbeddingServerFetcher(const BaseEmbeddingServerFetchParameter &base_param,
                                                   const BtEmbeddingServerExtraParameter &extra_param)
    : BaseEmbeddingServerFetcher(base_param), extra_param_(extra_param) {
  num_sub_responses_used_ = 0;
}

bool BtEmbeddingServerFetcher::FetchEmbeddingRemote(const Map<uint64_t, uint32_t> &signs,
                                                    MutableRecoContextInterface *context) {
  if (signs.size() == 0) {
    CL_LOG(INFO) << "All keys hit cache, skip request remote embedding server";
    num_sub_responses_used_ = 0;
    return true;
  }
  int64 start_ts = base::GetTimestamp();
  CHECK_GT(extra_param_.num_shards, 0);
  batch_waiter_.Clear();
  std::string hash_input;
  auto attr_val = context->GetStringCommonAttr(extra_param_.hash_input_attr);
  if (!attr_val || attr_val->empty()) {
    hash_input = "";
  } else {
    hash_input = std::string(*attr_val);
  }
  auto send_request_to_specific_shard = [&, hash_input](int shard,
                                                        const ks::reco::GetKuibaEmbeddingRequest &request)
      -> absl::optional<ks::kess::rpc::grpc::Future<ks::reco::GetKuibaEmbeddingResponse *>> {
    std::string shard_name = "s" + std::to_string(shard);
    std::pair<bool, ks::kess::rpc::grpc::Future<::ks::reco::GetKuibaEmbeddingResponse *>> pr;
    if (hash_input.empty()) {
      pr = [&]() {
        KESS_GRPC_MULTI_EVENTLOOP_ASYNC_RETURN(base_param_.kess_service, extra_param_.kess_cluster,
                                               shard_name, base_param_.timeout_ms, request, PopSubResponse(),
                                               ks::reco::kess::PredictKessService, AsyncGetKuibaEmbedding);
      }();
    } else {
      pr = [&]() {
        uint64 hashed_value = base::CityHash64(hash_input.c_str(), hash_input.length());
        HASHED_KESS_GRPC_MULTI_EVENTLOOP_ASYNC_RETURN(
            base_param_.kess_service, extra_param_.kess_cluster, shard_name, base_param_.timeout_ms, request,
            PopSubResponse(), ks::reco::kess::PredictKessService, AsyncGetKuibaEmbedding, hashed_value);
      }();
    }
    if (pr.first) {
      return absl::optional<ks::kess::rpc::grpc::Future<ks::reco::GetKuibaEmbeddingResponse *>>(
          std::move(pr.second));
    } else {
      CL_LOG_WARNING("uni_predict_fused", "failed_to_send_embedding")
          << "Failed to send embedding to shard " << shard;
      return absl::nullopt;
    }
  };
  auto send_request_to_all_shards =
      [&](const ks::reco::GetKuibaEmbeddingRequest &request,
          std::vector<absl::optional<ks::kess::rpc::grpc::Future<ks::reco::GetKuibaEmbeddingResponse *>>>
              *futures) {
        for (int i = 0; i < extra_param_.num_shards; i++) {
          futures->emplace_back(send_request_to_specific_shard(i, request));
        }
      };

  // async send
  std::vector<absl::optional<ks::kess::rpc::grpc::Future<ks::reco::GetKuibaEmbeddingResponse *>>> futures;
  if (extra_param_.client_side_shard) {
    ResetSubResponses(base_param_.max_signs_per_request > 0
                          ? (signs.size() / base_param_.max_signs_per_request) + extra_param_.num_shards
                          : extra_param_.num_shards);

    std::vector<ks::reco::GetKuibaEmbeddingRequest> requests(extra_param_.num_shards);
    for (auto &request : requests) {
      request.set_version(0);  // TODO(qiuchuyu): model version ignored
    }
    for (const auto &it : signs) {
      auto &sign = it.first;
      int shard = sign % extra_param_.num_shards;
      auto &request = requests[shard];
      request.add_signs(sign);
      if (base_param_.max_signs_per_request > 0 &&
          request.signs_size() >= base_param_.max_signs_per_request) {
        futures.emplace_back(send_request_to_specific_shard(shard, request));
        request.clear_signs();
      }
    }
    for (int i = 0; i < extra_param_.num_shards; i++) {
      const auto &request = requests[i];
      if (request.signs_size() > 0) {
        futures.emplace_back(send_request_to_specific_shard(i, request));
      }
    }
  } else {
    ResetSubResponses(base_param_.max_signs_per_request > 0
                          ? (signs.size() / base_param_.max_signs_per_request) * extra_param_.num_shards
                          : extra_param_.num_shards);

    ks::reco::GetKuibaEmbeddingRequest request;
    request.set_version(0);  // TODO(qiuchuyu): model version ignored
    for (auto &it : signs) {
      auto &sign = it.first;
      request.add_signs(sign);
      if (base_param_.max_signs_per_request > 0 &&
          request.signs_size() >= base_param_.max_signs_per_request) {
        send_request_to_all_shards(request, &futures);
        request.clear_signs();
      }
    }
    if (request.signs_size() > 0) {
      send_request_to_all_shards(request, &futures);
    }
  }

  // wait and merge responses
  bool ret_status = true;
  auto final_callback = [context, &ret_status, this](const ::grpc::Status &status,
                                                     ks::reco::GetKuibaEmbeddingResponse *response) {
    if (!status.ok()) {
      ret_status = false;
      response->Clear();
      if (local_cache_) {
        local_cache_->IncFailedRequestCount();
      }
      CL_LOG_WARNING("uni_predict_fused", "failed_to_get_embedding")
          << "Failed to get embedding: " << status.error_message() << ". " << FAQ_GUIDE("A12");
    }
  };
  for (auto &fut : futures) {
    if (fut) {
      if (local_cache_) {
        local_cache_->IncRequestCount();
      }
      batch_waiter_.Add(std::move(*fut), final_callback);
    }
  }
  batch_waiter_.Wait();  // 阻塞等待 RPC 返回
  reco_arch::uni_predict::MonitorManager::GetInstance().ReportEmbeddingRequestCount(
      model_key_, base_param_.kess_service, signs.size(), "all");
  int64 duration = base::GetTimestamp() - start_ts;
  reco_arch::uni_predict::MonitorManager::GetInstance().ReportFetchRemoteTime(
      model_key_, base_param_.kess_service, duration);
  return ret_status;
}

ks::reco::GetKuibaEmbeddingResponse *BtEmbeddingServerFetcher::PopSubResponse() {
  int sub_responses_idx = num_sub_responses_used_++;
  if (sub_responses_idx >= sub_responses_.size()) {
    FB_LOG_EVERY_S(ERROR, 1)
        << "Too many responses are requested, the response will be managed by the grpc framework "
           "and performance may suffer";
    return nullptr;
  }
  auto &response = sub_responses_[sub_responses_idx];
  response.Clear();
  return &response;
}

void BtEmbeddingServerFetcher::ResetSubResponses(size_t num) {
  if (sub_responses_.size() < num) {
    sub_responses_.resize(num);
  }
  num_sub_responses_used_ = 0;
}

void BtEmbeddingServerFetcher::MergeRemoteEmbedding(
    Map<uint64_t, uint32_t> *all_signs, std::vector<std::pair<const void *, size_t>> *sign_to_embedding) {
  auto &all_signs_ref = *all_signs;
  auto &sign_to_embedding_ref = *sign_to_embedding;
  for (size_t i = 0; i < std::min(static_cast<size_t>(num_sub_responses_used_.load()), sub_responses_.size());
       ++i) {
    const auto &sub_resp = sub_responses_[i];
    if (sub_resp.signs_size() == sub_resp.values_size()) {
      for (size_t j = 0; j < sub_resp.signs_size(); ++j) {
        uint64_t key = sub_resp.signs(j);
        auto it = all_signs_ref.find(key);
        if (it == all_signs_ref.end()) {
          CL_LOG_ERROR("uni_predict_fused", "abnormal_sign")
              << "BtEmbedding server response get invalid sign: " << key
              << ". debug info: num_sub_responses_used = " << num_sub_responses_used_.load()
              << ", sub_responses_size() = " << sub_responses_.size() << ", cur_index = " << i
              << ", sub_response_size = " << sub_resp.signs_size() << ", key = " << key
              << ", index in this response = " << j << ", all_signs.size() = " << all_signs_ref.size();
          continue;
        }
        int pos = it->second;
        if (sign_to_embedding_ref[pos].second != 0) {
          CL_LOG_ERROR("uni_predict_fused", "duplicate_sign") << "duplicate sign found: " << key;
        } else {
          if (sub_resp.values(j).size() > 0) {
            sign_to_embedding_ref[pos] = std::make_pair(sub_resp.values(j).data(), sub_resp.values(j).size());
          } else {
            CL_LOG_ERROR("uni_predict_fused", "embedding_size_0") << "embdding size is zero " << key;
          }
        }
      }
    } else {
      CL_LOG_ERROR("uni_predict_fused", "signs_and_values_size_unmatched")
          << "the sizes of signs and values doesn't equal";
    }
  }
}

void BtEmbeddingServerFetcher::UpdateLocalCache(const Map<uint64_t, uint32_t> &all_signs) {
  std::vector<embedding_cache::EmbeddingEntry> embeddings;
  embeddings.reserve(all_signs.size());
  folly::F14FastSet<uint64_t> resp_keys;
  size_t num_response = std::min(static_cast<size_t>(num_sub_responses_used_.load()), sub_responses_.size());
  for (size_t i = 0; i < num_response; ++i) {
    const auto &sub_resp = sub_responses_[i];
    if (sub_resp.signs_size() == sub_resp.values_size()) {
      for (size_t j = 0; j < sub_resp.signs_size(); ++j) {
        auto &value = sub_resp.values(j);
        int size = value.size();
        if (size > 0) {
          const auto &key = sub_resp.signs(j);
          embeddings.emplace_back(
              key, embedding_cache::EmbeddingValue{reinterpret_cast<const int8_t *>(value.data()), size});
          if (FLAGS_cache_empty_embedding) {
            resp_keys.insert(key);
          }
        }
      }
    } else {
      CL_LOG_ERROR("uni_predict_fused", "signs_and_values_size_unmatched")
          << "the sizes of signs and values doesn't equal";
    }
  }
  if (FLAGS_cache_empty_embedding) {
    folly::F14FastSet<uint64_t> resp_keys;
    for (size_t i = 0; i < num_response; ++i) {
      const auto &sub_resp = sub_responses_[i];
      if (sub_resp.signs_size() == sub_resp.values_size()) {
        for (size_t j = 0; j < sub_resp.signs_size(); ++j) {
          const auto &key = sub_resp.signs(j);
          resp_keys.insert(key);
        }
      }
    }
    for (const auto item : all_signs) {
      const auto &key = item.first;
      if (resp_keys.find(key) == resp_keys.end()) {
        embeddings.emplace_back(key, embedding_cache::EmbeddingValue{nullptr, 0});
      }
    }
    CL_LOG(INFO) << "#(response keys): " << resp_keys.size() << ", #(all signs): " << all_signs.size()
                 << ", #(empty embedding): " << all_signs.size() - resp_keys.size();
  }
  auto report_callback = [this](double availability) {
    reco_arch::uni_predict::MonitorManager::GetInstance().ReportEmbeddingServerAvailbilityRate(
        model_key_, base_param_.local_cache_config.key, 1000.0 * availability);
  };
  int key_num = 0;
  switch (base_param_.local_cache_version) {
    case 1: {
      auto local_cache_v1 = reinterpret_cast<embedding_cache::GlobalEmbeddingCache *>(local_cache_);
      local_cache_v1->AsyncBatchUpdate(embeddings, report_callback);
      key_num = local_cache_v1->KeyNum();
      break;
    }
    case 2: {
      auto local_cache_v2 = reinterpret_cast<embedding_cache::GlobalEmbeddingCacheV2 *>(local_cache_);
      local_cache_v2->AsyncBatchUpdate(embeddings);
      key_num = local_cache_v2->KeyNum();
      break;
    }
    default:
      LOG(FATAL) << "version in cache_config should be 1 or 2, but get " << base_param_.local_cache_version;
      break;
  }
  reco_arch::uni_predict::MonitorManager::GetInstance().ReportEmbeddingCacheKeyCount(
      model_key_, base_param_.kess_service, key_num);
}

ColossusdbEmbeddingServerFetcher::ColossusdbEmbeddingServerFetcher(
    const BaseEmbeddingServerFetchParameter &base_param,
    const ColossusdbEmbeddingServerExtraParameter &extra_param)
    : base_param_(base_param), extra_param_(extra_param) {
  client_config_.service_name = extra_param_.service_name;
  client_config_.table_name = extra_param_.table_name;
  client_config_.use_kconf_client = extra_param_.use_kconf_client;
  client_config_.timeout_ms = base_param_.timeout_ms;
  client_config_.max_signs_per_request = base_param_.max_signs_per_request;
  // init local cache
  if (base_param_.enable_cache) {
    switch (base_param_.local_cache_version) {
      case 1:
        local_cache_ =
            embedding_cache::GlobalEmbeddingCache::GetOrCreateInstance(base_param_.local_cache_config);
        break;
      case 2:
        local_cache_ =
            embedding_cache::GlobalEmbeddingCacheV2::GetOrCreateInstance(base_param_.local_cache_config);
        break;
      default:
        LOG(FATAL) << "version in cache_config should be 1 or 2, but get " << base_param_.local_cache_version;
        break;
    }
    random_seed_ = base::GetTimestamp() & 0xFFFFFFFF;  // 保留后 32 位
  }
  num_rpc_data_used_ = 0;
}

std::shared_ptr<ColossusdbEmbeddingServerFetcher> BuildOptColossusdbEmbeddingServerFetcherFromConfig(
    const base::Json *config, std::string *err_msg) {
  BaseEmbeddingServerFetchParameter base_param;
  ColossusdbEmbeddingServerExtraParameter colossusdb_extra_param;
  // 1. parse embedding server args
  auto colossusdb_embd_model_name = config->GetString("colossusdb_embd_model_name", "");
  if (colossusdb_embd_model_name.empty()) {
    colossusdb_embd_model_name = config->GetString("colossusdb_embd_service_name");
  }
  auto colossusdb_embd_table_name = config->GetString("colossusdb_embd_table_name");
  auto use_kconf_client = config->GetBoolean("use_kconf_client", true);
  CONFIG_CHECK(!colossusdb_embd_model_name.empty(), "colossusdb_embd_model_name not specified");
  CONFIG_CHECK(!colossusdb_embd_table_name.empty(), "colossusdb_embd_table_name not specified");
  colossusdb_extra_param.service_name = colossusdb_embd_model_name;
  colossusdb_extra_param.table_name = colossusdb_embd_table_name;
  colossusdb_extra_param.use_kconf_client = use_kconf_client;

  // use_kconf_client 的情况下这不是真正的 kess_name, 不过不影响使用
  auto kess_service = colossusdb_embd_model_name + "_" + colossusdb_embd_table_name;

  // 2. 构造 base_param
  if (!BuildBaseParam(config, kess_service, &base_param, err_msg)) {
    return nullptr;
  }

  // 3. 构造 fetcher
  return std::make_shared<ColossusdbEmbeddingServerFetcher>(base_param, colossusdb_extra_param);
}

bool ColossusdbEmbeddingServerFetcher::FetchEmbedding(
    MutableRecoContextInterface *context, const std::vector<uint64_t> &signs,
    std::vector<std::pair<const void *, size_t>> *sign_to_embedding) {
  serving_base::Timer tt;
  auto &sign_to_emb_ref = *sign_to_embedding;
  if (sign_to_emb_ref.size() == 0) {
    CL_LOG(INFO) << "skip FetchEmbedding as parameter set is empty";
    return true;
  }
  int64 start_ts = base::GetTimestamp();
  tt.Start();
  bool need_update_local_cache = base_param_.enable_cache;
  // 1. fetch from local cache
  if (base_param_.enable_cache &&
      ((rand_r(&random_seed_) % 100) / 100.0 > base_param_.embedding_skip_cache_ratio)) {
    if (!FetchEmbeddingLocal(context, signs, sign_to_embedding)) {
      CL_LOG(WARNING) << "fetch embedding from local cache failed";
    }
    need_update_local_cache = false;  // 只要查了 cache 就不用更新
  }
  tt.AppendCostMs("cache_lookup");
  size_t missed_signs = 0;

  auto clsdb_client = GetColossusdbClient();
  if (!clsdb_client) {
    return false;
  }
  auto instance_parttions = clsdb_client->GetInstances();
  auto instace_size = instance_parttions.size();
  auto hash_op = clsdb_client->GetHashOperator();
  static const google::protobuf::MethodDescriptor *method =
      colossusdb::ps::proto::store::v1::generic_rpc::kess::KvStoreService::descriptor()->FindMethodByName(
          "BatchGetDocument");
  if (instace_size <= 0) {
    clsdb_client->RequestDummyInstance(method);
    FB_LOG_EVERY_S(ERROR, 1) << "shard number is 0, fetch embedding failed. config model_name = "
                             << client_config_.service_name << ", table_name = " << client_config_.table_name
                             << ", check whether they are correct";
    return false;
  }
  if (!hash_op) {
    clsdb_client->RequestDummyInstance(method);
    LOG(ERROR) << "hash operator is null, fetch embedding failed";
    return false;
  }
  auto parttion_num = hash_op->PartitionNum();
  // partition_packages 收集了每个 partition 对应的 key
  // 而 miss_shard_embeddings_ 收集了每个 instance 对应的 key
  thread_local std::vector<std::vector<UniqueSignEmb>> partition_packages;
  // 实际 miss_shard_embeddings_ 用到的 size 是实际的 shard，极端情况下逻辑 parttion 和实际 shard 数目相同
  if (miss_shard_embeddings_.size() != parttion_num) {
    miss_shard_embeddings_.resize(parttion_num);
  }
  if (partition_packages.size() != parttion_num) {
    partition_packages.resize(parttion_num);
  }
  for (int i = 0; i < parttion_num; ++i) {
    miss_shard_embeddings_[i].clear();
    partition_packages[i].clear();
  }

  // 多个逻辑 parttion 会对应一个 instance，去重并对这些 parttion 设置一个相同的 pos
  // 从而可以将这些逻辑 parttion 的数据 collect 到相同 pos 的 vector 里面
  std::vector<uint32_t> parttion_to_shard(hash_op->PartitionNum(), 0);
  folly::F14FastMap<colossusdb::Instance *, int32_t> instance_to_shard;
  int32_t pos = 0;
  for (auto &instance_info : instance_parttions) {
    for (auto &part : instance_info.second) {
      parttion_to_shard[part] = pos;
    }
    auto insert_ret = instance_to_shard.insert({instance_info.first.get(), pos});
    ++pos;
  }

  // 首先将所有 sign 按照逻辑 partition 分片
  for (int i = 0; i < signs.size(); ++i) {
    if (sign_to_emb_ref[i].first == nullptr) {
      if (sign_to_emb_ref[i].second != CACHE_EMPTY_EMBEDDING_FLAG) {
        missed_signs++;
        // 逻辑 partition -> 对应 instance
        auto &part_pkg = partition_packages[hash_op->Hash(signs[i])];
        part_pkg.emplace_back(std::make_pair(signs[i], sign_to_emb_ref.begin() + i));
      } else {
        // cache hit empty embedding
        sign_to_emb_ref[i].second = 0;
      }
    }
  }
  // 然后将按照 partition 划分的包 合并为 按照 instance 划分的包
  for (int i = 0; i < parttion_num; ++i) {
    auto &instance_package = miss_shard_embeddings_[parttion_to_shard[i]];
    for (int j = 0; j < partition_packages[i].size(); ++j) {
      instance_package.emplace_back(partition_packages[i][j]);
    }
  }
  // 统计每个 instance 的 key 数量
  int total_key = 0;
  for (int i = 0; i < instace_size && i < miss_shard_embeddings_.size(); ++i) {
    auto &instance_package = miss_shard_embeddings_[i];
    total_key += instance_package.size();
    reco_arch::uni_predict::MonitorManager::GetInstance().ReportEmbeddingRequestCount(
        model_key_, base_param_.kess_service, instance_package.size(), std::to_string(i));
  }
  reco_arch::uni_predict::MonitorManager::GetInstance().ReportEmbeddingRequestCount(
      model_key_, base_param_.kess_service, total_key, "all");
  tt.AppendCostMs("miss_sign_collect");
  float hit_rate = signs.size() > 0 ? 1.0f - (missed_signs * 1.0f / signs.size()) : 0;
  reco_arch::uni_predict::MonitorManager::GetInstance().ReportEmbeddingCacheHitRate(
      model_key_, base_param_.kess_service, hit_rate * 1000);

  // 异步发送请求到 embedding server
  ResetRpcDatas(base_param_.max_signs_per_request > 0
                    ? (missed_signs / base_param_.max_signs_per_request) + instance_to_shard.size()
                    : instance_to_shard.size());
  auto batch_task = std::make_shared<FetchBatchTask>(colossusdb::EventLoopPool::GetGlobalOne(),
                                                     clsdb_client->GetKessName());
  for (auto it : instance_to_shard) {
    auto instance = it.first;
    const auto &shard_embeddings = miss_shard_embeddings_[it.second];
    if (shard_embeddings.empty()) {
      continue;
    }
    auto sign_partition_size =
        base_param_.max_signs_per_request > 0 ? base_param_.max_signs_per_request : shard_embeddings.size();
    std::vector<int> partitions = RecoUtil::BalancedPartition(sign_partition_size, shard_embeddings.size());
    auto partition_begin = shard_embeddings.begin();
    auto request_idx = 0;
    for (int i = 0; i < partitions.size(); ++i) {
      if (local_cache_) {
        local_cache_->IncRequestCount();
      }
      auto partition_end = std::next(partition_begin, partitions[i]);

      auto rpc_data = PopRpcData();
      if (!rpc_data) {
        continue;
      }
      auto rpc_sub_task = batch_task->CreateSub();
      rpc_sub_task->request = &rpc_data->request;
      rpc_sub_task->response = &rpc_data->response;
      // 拷贝 sign
      auto *mutable_signs = rpc_sub_task->request->mutable_ids();
      mutable_signs->Reserve(sign_partition_size);
      for (auto sign_it = partition_begin; sign_it < partition_end; ++sign_it) {
        *mutable_signs->AddAlreadyReserved() = (*sign_it).first;
      }
      const auto call_back = [this, partition_begin, partition_end](
                                 colossusdb::ps::proto::store::v1::BatchGetDocumentResponse *response,
                                 const brpc::Controller &cntl) {
        if (!cntl.Failed()) {
          int valid_ret = 0;
          valid_ret = MergePartitionRemoteEmbedding(partition_begin, partition_end, response);
          FB_LOG_EVERY_S(INFO, 10) << "rpc callback get valid result num: " << valid_ret;
        } else {
          if (local_cache_) {
            local_cache_->IncFailedRequestCount();
          }
        }
      };
      rpc_sub_task->SetCallBack(call_back);
      rpc_sub_task->Submit(instance, method, base_param_.timeout_ms);
      partition_begin = partition_end;
    }
  }
  batch_task->Join();
  tt.AppendCostMs("send_test");

  // 3. update cache
  if (need_update_local_cache) {
    UpdateLocalCache(signs);
  }
  tt.AppendCostMs("update_cache");

  CL_LOG(INFO) << "signs size, cached: " << sign_to_emb_ref.size() - missed_signs
               << ", sent: " << missed_signs << ", time_cost=" << tt.display();
  int64 duration = base::GetTimestamp() - start_ts;
  reco_arch::uni_predict::MonitorManager::GetInstance().ReportFetchRemoteTime(
      model_key_, base_param_.kess_service, duration);
  return true;
}

bool ColossusdbEmbeddingServerFetcher::FetchEmbeddingLocal(
    MutableRecoContextInterface *context, const std::vector<uint64_t> &signs,
    std::vector<std::pair<const void *, size_t>> *sign_to_embedding) {
  if (!local_cache_) {
    return false;
  }
  int64 start_ts = base::GetTimestamp();
  switch (base_param_.local_cache_version) {
    case 1:
      reinterpret_cast<embedding_cache::GlobalEmbeddingCache *>(local_cache_)
          ->BatchGet(signs, sign_to_embedding, FLAGS_local_fetch_embedding_ignore_size);
      break;
    case 2:
      reinterpret_cast<embedding_cache::GlobalEmbeddingCacheV2 *>(local_cache_)
          ->BatchGet(signs, sign_to_embedding);
      break;
    default:
      LOG(FATAL) << "version in cache_config should be 1 or 2, but get " << base_param_.local_cache_version;
      break;
  }
  int64 duration = base::GetTimestamp() - start_ts;
  reco_arch::uni_predict::MonitorManager::GetInstance().ReportFetchLocalTime(
      model_key_, base_param_.kess_service, duration);
  return true;
}

int ColossusdbEmbeddingServerFetcher::MergePartitionRemoteEmbedding(
    UniqueSignEmbIter begin, UniqueSignEmbIter end,
    colossusdb::ps::proto::store::v1::BatchGetDocumentResponse *response) {
  auto valid_count = 0;
  auto &sub_resp = *response;
  auto rep_it = response->mutable_founds()->begin();
  auto rep_end = response->mutable_founds()->end();
  for (auto it = begin; rep_it < rep_end && it < end; ++it) {
    auto &sign = (*it).first;
    if (sign != rep_it->id()) {
      continue;
    }
    if (rep_it->value().size() > 0) {
      (*it).second->first = rep_it->value().data();
      (*it).second->second = rep_it->value().size();
      ++valid_count;
    } else {
      CL_LOG_ERROR("uni_predict_fused", "embedding_size_0") << "embdding size is zero " << sign;
    }
    rep_it++;
  }
  if (rep_it != rep_end) {
    LOG(ERROR) << (rep_end - rep_it) << ", the sizes of signs and values doesn't equal";
  }
  return valid_count;
}

void ColossusdbEmbeddingServerFetcher::UpdateLocalCache(const std::vector<uint64_t> &all_signs) {
  std::vector<embedding_cache::EmbeddingEntry> embeddings;
  embeddings.reserve(all_signs.size());
  folly::F14FastSet<uint64_t> resp_keys;
  for (size_t i = 0; i < std::min(static_cast<size_t>(num_rpc_data_used_.load()), rpc_datas_.size()); ++i) {
    const auto &sub_resp = rpc_datas_[i].response;
    for (int j = 0; j < sub_resp.founds_size(); ++j) {
      if (sub_resp.founds(j).value().size() > 0) {
        const auto &key = sub_resp.founds(j).id();
        embeddings.emplace_back(key, embedding_cache::EmbeddingValue{
                                         reinterpret_cast<const int8_t *>(sub_resp.founds(j).value().data()),
                                         (int)sub_resp.founds(j).value().size()});
        if (FLAGS_cache_empty_embedding) {
          resp_keys.insert(key);
        }
      }
    }
  }
  if (FLAGS_cache_empty_embedding) {
    for (const auto key : all_signs) {
      if (resp_keys.find(key) == resp_keys.end()) {
        embeddings.emplace_back(key, embedding_cache::EmbeddingValue{nullptr, 0});
      }
    }
    CL_LOG(INFO) << "#(response keys): " << resp_keys.size() << ", #(all signs): " << all_signs.size()
                 << ", #(empty embedding): " << all_signs.size() - resp_keys.size();
  }

  auto report_callback = [this](double availability) {
    reco_arch::uni_predict::MonitorManager::GetInstance().ReportEmbeddingServerAvailbilityRate(
        model_key_, base_param_.local_cache_config.key, 1000.0 * availability);
  };
  int key_num = 0;
  switch (base_param_.local_cache_version) {
    case 1: {
      auto local_cache_v1 = reinterpret_cast<embedding_cache::GlobalEmbeddingCache *>(local_cache_);
      local_cache_v1->AsyncBatchUpdate(embeddings, report_callback);
      key_num = local_cache_v1->KeyNum();
      break;
    }
    case 2: {
      auto local_cache_v2 = reinterpret_cast<embedding_cache::GlobalEmbeddingCacheV2 *>(local_cache_);
      local_cache_v2->AsyncBatchUpdate(embeddings);
      key_num = local_cache_v2->KeyNum();
      break;
    }
    default:
      LOG(FATAL) << "version in cache_config should be 1 or 2, but get " << base_param_.local_cache_version;
      break;
  }
  reco_arch::uni_predict::MonitorManager::GetInstance().ReportEmbeddingCacheKeyCount(
      model_key_, base_param_.kess_service, key_num);
}
}  // namespace embedding_fetcher
}  // namespace platform
}  // namespace ks
