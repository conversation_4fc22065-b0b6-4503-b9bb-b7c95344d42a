#include "dragon/src/processor/ext/uni_predict_v2/plugins/rag_fetcher/rag_by_dragon_delegate_fetcher.h"

#include <algorithm>
#include <unordered_map>
#include <string>
#include <vector>

#include "base/common/logging.h"
#include "base/time/timestamp.h"
#include "dragon/src/processor/ext/uni_predict_v2/common/common.h"
#include "dragon/src/processor/ext/uni_predict_v2/common/functions.h"
#include "dragon/src/processor/ext/uni_predict_v2/embedding_manager/embedding_manager.h"
#include "folly/futures/Future.h"
#include "ks/common_reco/util/key_sign_util.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.generic_rpc.pb.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.kess.grpc.pb.h"
#include "serving_base/perfutil/perfutil_wrapper.h"
#include "serving_base/server_base/kess_client.h"
#include "serving_base/utility/timer.h"
#include "teams/reco-arch/uni-predict-v2/src/utils/monitor.h"
#include "teams/reco-arch/uni-predict-v2/src/utils/utils.h"

namespace ks {
namespace platform {
namespace uni_predict {

namespace {

kuiba::CommonSampleEnum::AttrType TrtDtypeToKuibaDtype(nvinfer1::DataType dtype) {
  switch (dtype) {
    case nvinfer1::DataType::kINT64:
      return kuiba::CommonSampleEnum::INT_LIST_ATTR;
    case nvinfer1::DataType::kFLOAT:
    default:
      return kuiba::CommonSampleEnum::UNKNOWN_ATTR;
  }
  return kuiba::CommonSampleEnum::UNKNOWN_ATTR;
}

PackedItemAttrValue_ValueType TrtDtypeToPackedDtype(nvinfer1::DataType dtype) {
  switch (dtype) {
    case nvinfer1::DataType::kINT64:
      return PackedItemAttrValue_ValueType_INT64_LIST;
    case nvinfer1::DataType::kFLOAT:
      return PackedItemAttrValue_ValueType_FLOAT32_LIST;
    default:
      return PackedItemAttrValue_ValueType_UNKNOWN;
  }
  return PackedItemAttrValue_ValueType_UNKNOWN;
}

struct MemCpySeg {
  int64_t dst_offset = 0;
  const char *src_data = nullptr;
  int64_t cpy_size = 0;
};

}  // namespace

bool RagByDragonDelegateFetcher::DoInitialize(const base::Json &js_cfg) {
  context_.SetRequest(&request_);
  request_.set_request_type("default");
  CHECK(!node_name_.empty());
  dry_run_ = js_cfg.GetBoolean("dry_run", false);
  request_type_ = js_cfg.GetString("request_type");
  max_batch_size_ = js_cfg.GetInt("max_batch_size", 0);
  CHECK_GT(max_batch_size_, 0) << "invalid `max_batch_size`: " << max_batch_size_;
  kess_service_ = js_cfg.GetString("kess_service");
  CHECK(!kess_service_.empty()) << "kess_service";
  kess_cluster_ = js_cfg.GetString("kess_cluster", "PRODUCTION");
  kess_group_ = js_cfg.GetString("kess_group", "");
  timeout_ms_ = js_cfg.GetInt("timeout_ms", 500);
  perf_name_ = model_key_ + "::" + node_name_;
  // NOTE(zfy): 当 trtexec 使用的时候, 需要这里初始化, uni_predict_v2 使用的时候, 这里重复初始化也没有副作用
  // NOTE(zfy): 这里只需要初始化, 入参数不起作用
  embedding_manager::EmbeddingManagerGlobalHelper::GetInstance().Initialization(100, model_key_);
  auto monitor =
      std::make_shared<reco_arch::uni_predict::Monitor>("grpc_trtRagDemoClientTest", false, model_key_);
  reco_arch::uni_predict::MonitorManager::GetInstance().AddMonitor(model_key_, monitor);
  return true;
}

void RagByDragonDelegateFetcher::SetupRequest(const RagTensorMetaVec &inputs,
                                              const RagTensorMetaVec &outputs) {
  rag_req_.set_request_type(request_type_);
  rag_req_.set_user_id(context_.GetUserId());
  rag_req_.set_device_id(context_.GetDeviceId());
  rag_req_.set_time_ms(base::GetTimestamp() / 1'000);
  rag_req_.set_request_id(context_.GetRequestId());
  // NOTE(zfy): 为了性能, 要求 packed_item_attr 格式
  rag_req_.set_use_packed_item_attr(true);
  rag_req_.set_return_required_attrs_only(true);
  for (const auto &output : outputs) {
    rag_req_.add_return_item_attrs(output.attr);
  }
  // FillRequestItems
  int32_t batch = inputs[0].dim[0];
  auto *packed_item_attr = rag_req_.mutable_item_attr();
  packed_item_attr->Clear();
  for (int i = 0; i < batch; ++i) {
    // fake item_id/item_key, used to track response
    auto *item = rag_req_.add_item_list();
    uint64_t item_id = i;
    uint64_t item_key = Util::GenKeysign(item_type_, item_id);
    item->set_item_id(item_id);
    item->set_item_type(item_type_);
    packed_item_attr->add_item_keys(item_key);
    item_key_to_pos_[item_key] = i;
  }
  for (int j = 0; j < inputs.size(); ++j) {
    auto *attr_value = packed_item_attr->add_attr_values();
    attr_value->set_name(inputs[j].attr);
    std::string *payload = attr_value->mutable_value();
    attr_value->mutable_value_length()->Resize(batch, inputs[j].dim[1]);
    auto kuiba_dtype = TrtDtypeToKuibaDtype(inputs[j].dtype);
    auto packed_dtype = TrtDtypeToPackedDtype(inputs[j].dtype);
    if (kuiba_dtype == kuiba::CommonSampleEnum::UNKNOWN_ATTR &&
        packed_dtype == PackedItemAttrValue_ValueType_UNKNOWN) {
      CL_LOG_ERROR("rag_by_dragon_delegate_fetcher", absl::StrCat("request_invalid_attr:", inputs[j].attr))
          << "RagByDragonDelegateFetcher request invalid dtype: attr = " << inputs[j].attr
          << ", dtype = " << inputs[j].dtype;
      continue;
    }
    if (kuiba_dtype != kuiba::CommonSampleEnum::UNKNOWN_ATTR) {
      attr_value->set_type(kuiba_dtype);
    }
    if (packed_dtype != PackedItemAttrValue_ValueType_UNKNOWN) {
      attr_value->set_value_type(packed_dtype);
    }
    payload->assign(inputs[j].data, inputs[j].bytes);
  }
  rag_req_.set_request_num(rag_req_.item_list_size());
}

bool RagByDragonDelegateFetcher::SendRequest() {
  const std::string request_info =
      "model_key: " + model_key_ + ", node_name: " + node_name_ + ", kess_service: " + kess_service_ +
      ", kess_cluster: " + kess_cluster_ + ", kess_group: " + kess_group_ + ", kess_shard: " + kess_shard_ +
      ", request_type: " + request_type_ + ", timeout_ms: " + std::to_string(timeout_ms_);

  VLOG(1) << "Delegate request:" << rag_req_.ShortDebugString();
  bool success = [&]() {
    KESS_GRPC_RETURN(kess_service_, kess_cluster_, kess_shard_, timeout_ms_, rag_req_, &rag_res_,
                     kess::CommonRecoLeafService, Recommend, false)
    return true;
  }();
  if (!success) {
    CL_LOG_ERROR_EVERY("rag_by_dragon_delegate_fetcher", "send_request_fail: " + kess_service_, 1000)
        << "failed to send delegate enrich request! request_info = " << request_info;
    return false;
  }

  base::perfutil::PerfUtilWrapper::CountLogStash(1, kPerfNs, "rag_by_dragon_delegate_fetcher_call",
                                                 GlobalHolder::GetServiceIdentifier(), request_type_,
                                                 perf_name_, kess_service_);
  return true;
}

bool RagByDragonDelegateFetcher::HandleResponse(CommonRecoResponse *response, const RagTensorMetaVec &inputs,
                                                const RagTensorMetaVec &outputs, int32_t batch) {
  if (dry_run_) {
    // 全部填充默认值
    for (auto &output : outputs) {
      std::memset(output.data, 0, output.bytes);
    }
    return true;
  }
  if (response->status_code() != 0) {
    CL_LOG_ERROR("rag_by_dragon_delegate_fetcher",
                 absl::StrCat("response status_code=", response->status_code(), ": ", kess_service_))
        << "RagByDragonDelegateFetcher failed as non-zero status_code: " << response->status_code();
    return false;
  }
  VLOG(1) << "Delegate response:" << response->ShortDebugString();
  // NOTE(zfy): 先假设可能对不齐, 并且可能有缺失值, 如果性能不够, 可以要求下游对齐并填默认值,
  // 然后这里优化为整体拷贝
  std::vector<int32_t> pos;
  uint64_t invalid_cnt = 0;
  uint64_t invalid_item_key = 0;
  // NOTE(zfy): 暂时不考虑重复 item_key 的情况, 如果出现, 会直接覆盖
  for (auto &item_key : response->item_attr().item_keys()) {
    auto it = item_key_to_pos_.find(item_key);
    if (it == item_key_to_pos_.end()) {
      ++invalid_cnt;
      invalid_item_key = it->first;
      pos.push_back(-1);
      continue;
    }
    pos.push_back(it->second);
  }
  CHECK_EQ(pos.size(), response->item_attr().item_keys_size());
  CL_LOG_ERROR_COUNT(invalid_cnt, "rag_by_dragon_delegate_fetcher", "response_invalid_item_key")
      << "RagByDragonDelegateFetcher response invalid item_key: " << invalid_item_key;

  std::unordered_map<std::string, const PackedItemAttrValue *> attr_value_map;
  for (auto &attr_value : response->item_attr().attr_values()) {
    attr_value_map[attr_value.name()] = &attr_value;
  }
  for (int j = 0; j < outputs.size(); ++j) {
    auto &output = outputs[j];
    auto it = attr_value_map.find(output.attr);
    if (it == attr_value_map.end()) {
      CL_LOG_ERROR("rag_by_dragon_delegate_fetcher", absl::StrCat("response_miss_attr:", output.attr))
          << "RagByDragonDelegateFetcher response miss attr: " << output.attr;
      // 填充默认值: 0
      std::memset(output.data, 0, output.bytes);
      continue;
    }
    auto *attr_value = it->second;
    bool valid_type = ((attr_value->type() != kuiba::CommonSampleEnum::UNKNOWN_ATTR &&
                        attr_value->type() == output_kuiba_dtypes_[j]) ||
                       ((attr_value->value_type() != PackedItemAttrValue::UNKNOWN) &&
                        attr_value->value_type() == output_packed_dtypes_[j]));
    if (!valid_type) {
      CL_LOG_ERROR("rag_by_dragon_delegate_fetcher", absl::StrCat("response_invalid_dtype:", output.attr))
          << "RagByDragonDelegateFetcher response invalid dtype: attr = " << output.attr
          << ", expected trt type = " << static_cast<int>(output.dtype)
          << ", response kuiba type = " << attr_value->type()
          << ", response packed type = " << attr_value->value_type();
      // 填充默认值: 0
      std::memset(output.data, 0, output.bytes);
      continue;
    }
    const char *src_data = attr_value->value().data();
    int64_t src_max_size = attr_value->value().size();
    int64_t src_offset = 0;
    int64_t dst_offset = 0;
    std::vector<MemCpySeg> segs;
    for (int row = 0; row < attr_value->value_length_size() && row < pos.size(); ++row) {
      int64_t src_size = attr_value->value_length(row) * output.type_size;
      if (src_size < 0) {
        continue;
      }
      if (pos[row] < 0) {
        src_offset += src_size;
        continue;
      }
      dst_offset = output.dim[1] * pos[row] * output.type_size;
      segs.emplace_back(
          MemCpySeg{.dst_offset = dst_offset, .src_data = src_data + src_offset, .cpy_size = src_size});
      src_offset += src_size;
    }
    if (src_offset > src_max_size) {
      CL_LOG_ERROR("rag_by_dragon_delegate_fetcher", absl::StrCat("response_invalid_value:", output.attr))
          << "RagByDragonDelegateFetcher response invalid value: attr = " << output.attr
          << ", src_offset = " << src_offset << " vs src_max_size = " << src_max_size;
      // 填充默认值: 0
      std::memset(output.data, 0, output.bytes);
      continue;
    }
    std::sort(segs.begin(), segs.end(),
              [](const MemCpySeg &lhs, const MemCpySeg &rhs) { return lhs.dst_offset < rhs.dst_offset; });
    int64_t dst_last_offset = 0;
    for (auto &seg : segs) {
      if (seg.dst_offset > dst_last_offset) {
        // 填充默认值: 0
        std::memset(output.data + dst_last_offset, 0, seg.dst_offset - dst_last_offset);
      }
      std::memcpy(output.data + seg.dst_offset, seg.src_data, seg.cpy_size);
      dst_last_offset = seg.dst_offset + seg.cpy_size;
    }
    if (dst_last_offset < output.bytes) {
      // 填充默认值: 0
      std::memset(output.data + dst_last_offset, 0, output.bytes - dst_last_offset);
    }
  }
  return true;
}

bool RagByDragonDelegateFetcher::DoRag(const RagTensorMetaVec &inputs, const RagTensorMetaVec &outputs) {
  // reset
  rag_req_.Clear();
  rag_res_.Clear();
  item_key_to_pos_.clear();
  output_kuiba_dtypes_.clear();
  output_packed_dtypes_.clear();
  for (int j = 0; j < outputs.size(); ++j) {
    output_kuiba_dtypes_.push_back(TrtDtypeToKuibaDtype(outputs[j].dtype));
    output_packed_dtypes_.push_back(TrtDtypeToPackedDtype(outputs[j].dtype));
  }
  // check
  CHECK_GT(inputs.size(), 0);
  CHECK_GT(outputs.size(), 0);
  int32_t batch = inputs[0].dim[0];
  CHECK_GT(batch, 0);
  CHECK_LE(batch, max_batch_size_);
  auto lookup_func = [=]() {
    context_.ClearContext();
    context_.SetRequest(&request_);
    uni_predict_functions::ResetThreadLocalVariable(&context_);
    serving_base::Timer tt;
    tt.Start();
    SetupRequest(inputs, outputs);
    tt.AppendCostMs("setup_request");
    bool success = true;
    if (!dry_run_) {
      success = SendRequest();
      tt.AppendCostMs("send_request");
    }
    if (!success) {
      return false;
    }
    success = HandleResponse(&rag_res_, inputs, outputs, batch);
    tt.AppendCostMs("handle_response");
    CL_LOG(INFO) << "model_key: `" << model_key_ << "`, rag_by_dragon_delegate_fetcher for node: `"
                 << node_name_ << "`, time spend: " << tt.display();
    return success;
  };

  auto fut = folly::via(embedding_manager::EmbeddingManagerGlobalHelper::GetInstance().GetGlobalThreadPool(),
                        lookup_func);
  bool ret = true;
  std::move(fut)
      .thenTry([&ret](auto &&t) {
        if (!t.hasValue()) {
          // fetch task is broken. stop this request.
          ret = false;
          if (t.hasException()) {
            const auto &e = t.exception();
            if (e.class_name() == "folly::QueueFullException") {
              CL_LOG_ERROR("rag_by_dragon_delegate_fetcher", "fetcher_thread_not_enough")
                  << e.what() << ", please reset flags `embedding_fetcher_thread_num`(now "
                  << FLAGS_embedding_fetcher_thread_num << ") and `embedding_fetcher_queue_size`(now "
                  << FLAGS_embedding_fetcher_queue_size << ") to a bigger value.";
            } else {
              CL_LOG_ERROR("rag_by_dragon_delegate_fetcher", "embedding_manager_error") << e.what();
            }
          } else {
            CL_LOG_ERROR("rag_by_dragon_delegate_fetcher", "embedding_manager_error")
                << " cannot get value from fetcher futures";
          }
          return;
        }
        ret = t.value();
      })
      .wait();
  return ret;
}

using reco_arch::uni_predict::RagFetcher;

FACTORY_REGISTER(RagFetcher, RagByDragonDelegateFetcher, RagByDragonDelegateFetcher)

}  // namespace uni_predict
}  // namespace platform
}  // namespace ks
