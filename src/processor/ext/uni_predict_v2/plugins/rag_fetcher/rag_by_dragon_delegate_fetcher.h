#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_context.h"
#include "dragon/src/processor/ext/uni_predict_v2/common/common.h"
#include "teams/reco-arch/uni-predict-v2/src/operators/includes/rag_fetcher.h"
#include "teams/reco-arch/uni-predict-v2/src/utils/utils_nvinfer.h"

namespace ks {
namespace platform {
namespace uni_predict {

class RagByDragonDelegateFetcher : public reco_arch::uni_predict::RagFetcher {
 protected:
  bool DoInitialize(const base::Json &js_cfg) override;

 protected:
  bool DoRag(const RagTensorMetaVec &inputs, const RagTensorMetaVec &outputs) override;

 private:
  void SetupRequest(const RagTensorMetaVec &inputs, const RagTensorMetaVec &outputs);
  bool SendRequest();
  bool HandleResponse(CommonRecoResponse *response, const RagTensorMetaVec &inputs,
                      const RagTensorMetaVec &outputs, int32_t batch);

 private:
  bool dry_run_ = false;
  // NOTE(zfy): fake context and request
  CommonRecoRequest request_;
  CommonRecoContext context_;
  // delegate request and response
  CommonRecoRequest rag_req_;
  CommonRecoResponse rag_res_;
  // len(inputs) * max_batchsize
  int32_t max_batch_size_ = 0;
  std::string request_type_;
  std::string kess_service_;
  std::string kess_cluster_;
  std::string kess_group_;
  std::string kess_shard_ = "s0";
  std::string perf_name_;
  int32_t timeout_ms_ = 0;
  // 假设恒等于 0
  int32_t item_type_ = 0;
  // 缓存, 每次需要重设
  std::unordered_map<uint64_t, int32_t> item_key_to_pos_;
  std::vector<kuiba::CommonSampleEnum::AttrType> output_kuiba_dtypes_;
  std::vector<PackedItemAttrValue::ValueType> output_packed_dtypes_;
};

}  // namespace uni_predict
}  // namespace platform
}  // namespace ks

