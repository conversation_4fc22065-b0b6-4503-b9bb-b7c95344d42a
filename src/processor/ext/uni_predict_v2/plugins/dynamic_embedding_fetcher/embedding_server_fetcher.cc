#include "dragon/src/processor/ext/uni_predict_v2/plugins/dynamic_embedding_fetcher/embedding_server_fetcher.h"

#include <algorithm>

#include "base/common/logging.h"
#include "dragon/src/processor/ext/uni_predict_v2/common/common.h"
#include "dragon/src/processor/ext/uni_predict_v2/common/functions.h"
#include "dragon/src/processor/ext/uni_predict_v2/embedding_fetcher/embedding_server_fetcher.h"
#include "dragon/src/processor/ext/uni_predict_v2/embedding_fetcher/embedding_server_rdma_fetcher.h"
#include "dragon/src/processor/ext/uni_predict_v2/embedding_manager/embedding_manager.h"
#include "dragon/src/processor/ext/uni_predict_v2/embedding_merger/embedding_proc_util.h"
#include "folly/futures/Future.h"
#include "serving_base/perfutil/perfutil_wrapper.h"
#include "serving_base/utility/timer.h"
#include "teams/reco-arch/uni-predict-v2/src/utils/monitor.h"
#include "teams/reco-arch/uni-predict-v2/src/utils/utils.h"

namespace ks {
namespace platform {
namespace dynamic_embedding_fetcher {

namespace {
template <typename CompressType, typename DstCType>
bool MemMoveWithDefault(void *dst, const std::pair<const void *, size_t> *src, size_t embed_cnt,
                        const void *emb_dft, size_t embed_dim, const uint64_t *signs,
                        const std::string &node_name, size_t *sign_hit) {
  for (size_t i = 0; i < embed_cnt; ++i) {
    auto [ep, es] = src[i];
    if (!ep || !es) {
      // missing: fill default_embed
      memcpy(dst, emb_dft, embed_dim * sizeof(DstCType));
      continue;
    }
    size_t response_dim = CalcEmbeddingDim<CompressType>(es);
    if (embed_dim != response_dim) {
      CL_LOG_ERROR("mio_embedding", "unmatched_embedding_size")
          << "embedding size for " << signs[i] << " not match: (expected vs fetched) = (" << embed_dim
          << " vs " << response_dim << "), node_name: " << node_name << ". filled with emb_dft. "
          << FAQ_GUIDE("A27");
      memcpy(dst, emb_dft, embed_dim * sizeof(DstCType));
      continue;
    }
    embedding_merger::EmbeddingWrapper<CompressType> embed_wrapper;
    embedding_merger::BuildEmbeddingWrapper(embed_dim, ep, &embed_wrapper);
    embed_wrapper.MoveTo(static_cast<DstCType *>(dst) + i * embed_dim, 1.0);
    ++(*sign_hit);
  }
  return true;
}

}  // namespace

bool ColossusdbEmbeddingServerFetcher::DoInitialize(const base::Json &js_cfg) {
  context_.SetRequest(&request_);
  request_.set_request_type("default");
  CHECK(!node_name_.empty());
  dry_run_ = js_cfg.GetBoolean("dry_run", false);
  compress_type_ = reco_arch::uni_predict::StringToDataType(js_cfg.GetString("dtype"));
  std::string fetcher_type = js_cfg.GetString("fetcher_type");
  std::string msg;
  if (fetcher_type == "ColossusdbEmbeddingServerFetcher") {
    embed_fetcher_ = embedding_fetcher::BuildColossusdbEmbeddingServerFetcherFromConfig(&js_cfg, &msg);
  } else if (fetcher_type == "RdmaColossusdbEmbeddingServerFetcher") {
    embed_fetcher_ = embedding_fetcher::BuildRdmaColossusdbEmbeddingServerFetcherFromConfig(&js_cfg, &msg);
  } else {
    msg = "Fetcher type: `" + fetcher_type +
          "` not support by class `ColossusdbEmbeddingServerFetcher`, supported fetcher types: "
          "ColossusdbEmbeddingServerFetcher, RdmaColossusdbEmbeddingServerFetcher";
  }
  if (!embed_fetcher_) {
    LOG(ERROR) << "model_key: " << model_key_ << ", err msg: " << msg;
    return false;
  }
  embed_fetcher_->SetFetcherType(fetcher_type);
  embed_fetcher_->SetModelKey(model_key_);
  embed_fetcher_->SetProcessorName(node_name_);
  return true;
}

bool ColossusdbEmbeddingServerFetcher::FetchEmbedding(const uint64_t *id_data, size_t id_size,
                                                      reco_arch::uni_predict::DataType embed_dtype,
                                                      size_t embed_dim, const void *default_embed,
                                                      void *dst) {
  auto lookup_func = [=]() {
    size_t sign_hit = 0;
    context_.ClearContext();
    context_.SetRequest(&request_);
    uni_predict_functions::ResetThreadLocalVariable(&context_);
    serving_base::Timer tt;
    tt.Start();
    // NOTE(zfy): 这里拷贝成本应该不大, 如果成为瓶颈, 可以考虑在 embed_fetcher_ 对应的基类添加 0 拷贝的方法
    signs_.resize(id_size);
    std::memcpy(signs_.data(), id_data, sizeof(uint64_t) * id_size);
    sign_to_embedding_.clear();
    sign_to_embedding_.resize(id_size, {nullptr, 0});
    if (!dry_run_) {
      // NOTE(zfy): 目前只考虑 vector 的接口调用
      embed_fetcher_->FetchEmbedding(&context_, signs_, &sign_to_embedding_);
      const auto fetch_duration = tt.Interval();
      tt.AppendCostMs("fetch_embedding", fetch_duration / 1000.f);
    }
    CHECK_EQ(signs_.size(), sign_to_embedding_.size());

    using reco_arch::uni_predict::DataTypeToCType;
    using reco_arch::uni_predict::DataType;
#define UPV2DEFMMWD(compress_type, uni_dtype)                                                    \
  do {                                                                                           \
    if (!MemMoveWithDefault<typename EmbCompressTypeToClass<compress_type>::EmbCompressClass,    \
                            typename DataTypeToCType<uni_dtype>::CType>(                         \
            dst, sign_to_embedding_.data(), sign_to_embedding_.size(), default_embed, embed_dim, \
            signs_.data(), node_name_, &sign_hit)) {                                             \
      return false;                                                                              \
    }                                                                                            \
  } while (0)

#define UPV2DEFInnerSwitch(compress_type)                                                                  \
  do {                                                                                                     \
    switch (embed_dtype) {                                                                                 \
      case DataType::FLOAT16:                                                                              \
        UPV2DEFMMWD(compress_type, DataType::FLOAT16);                                                     \
        break;                                                                                             \
      case DataType::BFLOAT16:                                                                             \
        UPV2DEFMMWD(compress_type, DataType::BFLOAT16);                                                    \
        break;                                                                                             \
      case DataType::FLOAT32:                                                                              \
        UPV2DEFMMWD(compress_type, DataType::FLOAT32);                                                     \
        break;                                                                                             \
      default:                                                                                             \
        NOT_REACHED() << "unsupported combination (EmbCompressType, DataType): (" << compress_type << ", " \
                      << embed_dtype << ")";                                                               \
    }                                                                                                      \
  } while (0)

    switch (compress_type_) {
      case EmbCompressType::kMioInt16:
        UPV2DEFInnerSwitch(EmbCompressType::kMioInt16);
        break;
      case EmbCompressType::kScaleInt8:
        UPV2DEFInnerSwitch(EmbCompressType::kScaleInt8);
        break;
      case EmbCompressType::kScaleInt16:
        UPV2DEFInnerSwitch(EmbCompressType::kScaleInt16);
        break;
      case EmbCompressType::kFP32:
        UPV2DEFInnerSwitch(EmbCompressType::kFP32);
        break;
      case EmbCompressType::kFP16:
        UPV2DEFInnerSwitch(EmbCompressType::kFP16);
        break;
      default:
        NOT_REACHED() << "unsupported embedding compress type: " << compress_type_;
    }
#undef UPV2DEFMMWD
#undef UPV2DEFInnerSwitch

    const auto sum_pooling_duration = tt.Interval();
    tt.AppendCostMs("merge_embedding", sum_pooling_duration / 1000.f);
    reco_arch::uni_predict::MonitorManager::GetInstance().ReportSumPoolingTime(model_key_,
                                                                               sum_pooling_duration);
    sign_hit = dry_run_ ? id_size : sign_hit;
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        kPerfBase * sign_hit / std::max(id_size, size_t(1)), kPerfNs, "embedding_fetcher.item_sign_hit",
        GlobalHolder::GetServiceIdentifier(), context_.GetRequestType(), model_key_, node_name_);
    CL_LOG(INFO) << "model_key: `" << model_key_ << "`, dynamic_embedding_fetcher for node: `" << node_name_
                 << "`, post process: " << tt.display();
    return true;
  };

  auto fut = folly::via(embedding_manager::EmbeddingManagerGlobalHelper::GetInstance().GetGlobalThreadPool(),
                        lookup_func);
  bool ret = true;
  std::move(fut)
      .thenTry([&ret](auto &&t) {
        if (!t.hasValue()) {
          // fetch task is broken. stop this request.
          ret = false;
          if (t.hasException()) {
            const auto &e = t.exception();
            if (e.class_name() == "folly::QueueFullException") {
              CL_LOG_ERROR("uni_predict", "fetcher_thread_not_enough")
                  << e.what() << ", please reset flags `embedding_fetcher_thread_num`(now "
                  << FLAGS_embedding_fetcher_thread_num << ") and `embedding_fetcher_queue_size`(now "
                  << FLAGS_embedding_fetcher_queue_size << ") to a bigger value.";
            } else {
              CL_LOG_ERROR("uni_predict", "embedding_manager_error") << e.what();
            }
          } else {
            CL_LOG_ERROR("uni_predict", "embedding_manager_error")
                << " cannot get value from fetcher futures";
          }
          return;
        }
        ret = t.value();
        if (!ret) {
          // log warning fetcher failed
          CL_LOG(WARNING)
              << "embedding fetcher return false in this request. Set embeddings as zero in this fetcher";
        }
      })
      .wait();
  return ret;
}

using reco_arch::uni_predict::DynamicEmbeddingFetcher;

FACTORY_REGISTER(DynamicEmbeddingFetcher, ColossusdbEmbeddingServerFetcher, ColossusdbEmbeddingServerFetcher)

FACTORY_REGISTER(DynamicEmbeddingFetcher, RdmaColossusdbEmbeddingServerFetcher,
                 ColossusdbEmbeddingServerFetcher)

}  // namespace dynamic_embedding_fetcher
}  // namespace platform
}  // namespace ks
