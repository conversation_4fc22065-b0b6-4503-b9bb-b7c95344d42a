#pragma once

#include <memory>
#include <string>
#include <vector>
#include <utility>

#include "dragon/src/core/common_reco_context.h"
#include "dragon/src/processor/ext/uni_predict_v2/common/common.h"
#include "dragon/src/processor/ext/uni_predict_v2/embedding_fetcher/embedding_server_fetcher.h"
#include "teams/reco-arch/uni-predict-v2/src/operators/includes/dynamic_embedding_fetcher.h"

namespace ks {
namespace platform {
namespace dynamic_embedding_fetcher {

class ColossusdbEmbeddingServerFetcher : public reco_arch::uni_predict::DynamicEmbeddingFetcher {
 protected:
  bool DoInitialize(const base::Json &js_cfg) override;

 protected:
  bool FetchEmbedding(const uint64_t *id_data, size_t id_size, reco_arch::uni_predict::DataType embed_dtype,
                      size_t embed_dim, const void *default_embed, void *dst) override;

 private:
  bool dry_run_ = false;
  EmbCompressType compress_type_;
  // NOTE(zfy): fake context and request
  CommonRecoRequest request_;
  CommonRecoContext context_;
  std::shared_ptr<embedding_fetcher::BaseEmbeddingFetcher> embed_fetcher_;
  std::vector<uint64_t> signs_;
  std::vector<std::pair<const void *, size_t>> sign_to_embedding_;
};

}  // namespace dynamic_embedding_fetcher
}  // namespace platform
}  // namespace ks

