#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_observer.h"

namespace ks::platform {

class AttrDiffObserver : public CommonRecoBaseObserver {
 public:
  AttrDiffObserver() = default;

  void Observe(ReadableRecoContextInterface *context, RecoResultConstIter begin,
               RecoResultConstIter end) override;

  bool InitProcessor() override;

 private:
  using AttrPair = std::pair<std::string, std::string>;
  void PrintExtraAttrs(ReadableRecoContextInterface *context, RecoResultConstIter *iter_ptr);

  void PrintExtraCommonAttrs(ReadableRecoContextInterface *context);

  void PrintExtraItemAttrs(ReadableRecoContextInterface *context, RecoResultConstIter iter);

  bool CheckAttrPairType(ReadableRecoContextInterface *context, const AttrPair &attr_pair,
                         bool is_common_attr_pair);

  void CheckAttrPairValue(ReadableRecoContextInterface *context, RecoResultConstIter iter,
                          const AttrPair &attr_pair, bool is_common_attr_pair);

  std::vector<AttrPair> common_attrs_{};
  std::vector<AttrPair> item_attrs_{};
  std::vector<std::string> extra_common_attrs_{};
  std::vector<std::string> extra_item_attrs_{};
  float default_error_tolerance_{};
  std::ostringstream diff_info_stream_{};
  const int max_extra_item_attr_to_print_ = 10;
};

}  // namespace ks::platform
