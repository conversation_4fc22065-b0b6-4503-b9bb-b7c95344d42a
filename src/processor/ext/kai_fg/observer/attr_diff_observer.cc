#include "dragon/src/processor/ext/kai_fg/observer/attr_diff_observer.h"

#include <algorithm>
#include <cmath>
#include <iterator>
#include <sstream>
#include <string>
#include <utility>
#include <vector>

#include "third_party/glog/glog/logging.h"

namespace ks::platform {

namespace {

template <typename T>
std::string ToString(const absl::Span<T> &vals) {
  std::ostringstream ss;
  ss << "[";
  if (!vals.empty()) {
    std::copy(vals.begin(), vals.end() - 1, std::ostream_iterator<T>(ss, ", "));
    ss << vals.back();
  }
  ss << "]";
  return ss.str();
}

template <typename T>
std::string ToString(const std::vector<T> &vals) {
  std::ostringstream ss;
  ss << "[";
  if (!vals.empty()) {
    std::copy(vals.begin(), vals.end() - 1, std::ostream_iterator<T>(ss, ", "));
    ss << vals.back();
  }
  ss << "]";
  return ss.str();
}
}  // namespace

namespace {
template <typename T>
bool CheckAttr(const absl::optional<T> &v1, const absl::optional<T> &v2, const std::string &attr_a,
               const std::string &attr_b, std::ostringstream *log_stream_ptr) {
  if (!v1 && !v2) {
    return true;
  }
  if (!v1) {
    *log_stream_ptr << "attr_a: " << attr_a << " missing" << std::endl;
    return false;
  }
  if (!v2) {
    *log_stream_ptr << "attr_b: " << attr_b << " missing" << std::endl;
    return false;
  }

  if (v1 && v2) {
    if (*v1 != *v2) {
      *log_stream_ptr << "attr not equal, attr_a: " << attr_a << ", " << *v1 << " vs attr_b: " << attr_b
                      << ", " << *v2 << std::endl;
      return false;
    }
  }
  return true;
}
bool CheckAttrDouble(const absl::optional<double> &v1, const absl::optional<double> &v2,
                     const std::string &attr_a, const std::string &attr_b, double tolerance,
                     std::ostringstream *log_stream_ptr) {
  if (!v1 && !v2) {
    return true;
  }
  if (!v1) {
    *log_stream_ptr << "attr_a: " << attr_a << " missing" << std::endl;
    return false;
  }
  if (!v2) {
    *log_stream_ptr << "attr_b: " << attr_b << " missing" << std::endl;
    return false;
  }

  if (v1 && v2) {
    if (std::isnan(*v1) && std::isnan(*v2)) {
      return true;
    }
    if (!base::IsEqual(*v1, *v2, tolerance)) {
      *log_stream_ptr << "attr not equal, attr_a: " << attr_a << ", " << *v1 << " vs attr_b: " << attr_b
                      << ", " << *v2 << std::endl;
      return false;
    }
  }
  return true;
}

template <typename T>
bool CheckAttrList(const absl::optional<absl::Span<T>> &v1, const absl::optional<absl::Span<T>> &v2,
                   const std::string &attr_a, const std::string &attr_b, std::ostringstream *log_stream_ptr) {
  if (!v1 && !v2) {
    return true;
  }
  if (!v1) {
    *log_stream_ptr << "attr_a: " << attr_a << " missing" << std::endl;
    return false;
  }
  if (!v2) {
    *log_stream_ptr << "attr_b: " << attr_b << " missing" << std::endl;
    return false;
  }

  if (v1 && v2) {
    if (!std::equal(v1->begin(), v1->end(), v2->begin(), v2->end())) {
      *log_stream_ptr << "attr not equal, attr_a: " << attr_a << ", vals: " << ToString(*v1)
                      << " vs attr_b: " << attr_b << ", vals: " << ToString(*v2) << std::endl;
      return false;
    }
  }
  return true;
}

template <typename T>
bool CheckAttrList(const absl::optional<std::vector<T>> &v1, const absl::optional<std::vector<T>> &v2,
                   const std::string &attr_a, const std::string &attr_b, std::ostringstream *log_stream_ptr) {
  if (!v1 && !v2) {
    return true;
  }
  if (!v1) {
    *log_stream_ptr << "attr_a: " << attr_a << " missing" << std::endl;
    return false;
  }
  if (!v2) {
    *log_stream_ptr << "attr_b: " << attr_b << " missing" << std::endl;
    return false;
  }

  if (v1 && v2) {
    if (!std::equal(v1->begin(), v1->end(), v2->begin(), v2->end())) {
      *log_stream_ptr << "attr not equal, attr_a: " << attr_a << ", vals: " << ToString(*v1)
                      << " vs attr_b: " << attr_b << ", vals: " << ToString(*v2) << std::endl;
      return false;
    }
  }
  return true;
}

bool CheckAttrListDouble(const absl::optional<absl::Span<const double>> &v1,
                         const absl::optional<absl::Span<const double>> &v2, const std::string &attr_a,
                         const std::string &attr_b, double tolerance, std::ostringstream *log_stream_ptr) {
  if (!v1 && !v2) {
    return true;
  }
  if (!v1) {
    *log_stream_ptr << "attr_a: " << attr_a << " missing" << std::endl;
    return false;
  }
  if (!v2) {
    *log_stream_ptr << "attr_b: " << attr_b << " missing" << std::endl;
    return false;
  }

  if (v1 && v2) {
    if (!std::equal(
            v1->begin(), v1->end(), v2->begin(), v2->end(),
            [tolerance](const double &a, const double &b) { return base::IsEqual(a, b, tolerance); })) {
      *log_stream_ptr << "attr not equal, attr_a: " << attr_a << ", vals: " << ToString(*v1)
                      << " vs attr_b: " << attr_b << ", vals: " << ToString(*v2) << std::endl;
      return false;
    }
  }

  return true;
}

}  // namespace

void AttrDiffObserver::PrintExtraAttrs(ReadableRecoContextInterface *context, RecoResultConstIter *iter_ptr) {
  bool is_common_attr = (iter_ptr == nullptr);
  auto &extra_attrs = is_common_attr ? extra_common_attrs_ : extra_item_attrs_;

  if (extra_attrs.empty()) {
    return;
  }
  diff_info_stream_ << (is_common_attr ? "--- extra common attrs ---" : "--- extra item attrs ---")
                    << std::endl;
  for (const auto attr_name : extra_attrs) {
    auto attr_accessor =
        is_common_attr ? context->GetCommonAttrAccessor(attr_name) : context->GetItemAttrAccessor(attr_name);
    auto attr_type = attr_accessor->value_type;

    if (attr_type == AttrType::INT) {
      auto attr = is_common_attr ? context->GetIntCommonAttr(attr_accessor)
                                 : context->GetIntItemAttr(**iter_ptr, attr_accessor);
      diff_info_stream_ << "attr(int): " << attr_name
                        << ", val: " << (attr.has_value() ? std::to_string(*attr) : "null") << std::endl;
    } else if (attr_type == AttrType::FLOAT) {
      auto attr = is_common_attr ? context->GetDoubleCommonAttr(attr_accessor)
                                 : context->GetDoubleItemAttr(**iter_ptr, attr_accessor);
      diff_info_stream_ << "attr(double): " << attr_name
                        << ", val: " << (attr.has_value() ? std::to_string(*attr) : "null") << std::endl;
    } else if (attr_type == AttrType::STRING) {
      auto attr = is_common_attr ? context->GetStringCommonAttr(attr_accessor)
                                 : context->GetStringItemAttr(**iter_ptr, attr_accessor);
      diff_info_stream_ << "attr(string): " << attr_name << ", val: " << (attr.has_value() ? *attr : "null")
                        << std::endl;
    } else if (attr_type == AttrType::INT_LIST) {
      auto attr = is_common_attr ? context->GetIntListCommonAttr(attr_accessor)
                                 : context->GetIntListItemAttr(**iter_ptr, attr_accessor);
      diff_info_stream_ << "attr(int_list): " << attr_name
                        << ", val: " << (attr.has_value() ? ToString(*attr) : "null") << std::endl;
    } else if (attr_type == AttrType::FLOAT_LIST) {
      auto attr = is_common_attr ? context->GetDoubleListCommonAttr(attr_accessor)
                                 : context->GetDoubleListItemAttr(**iter_ptr, attr_accessor);
      diff_info_stream_ << "attr(double_list): " << attr_name
                        << ", val: " << (attr.has_value() ? ToString(*attr) : "null") << std::endl;
    } else if (attr_type == AttrType::STRING_LIST) {
      auto attr = is_common_attr ? context->GetStringListCommonAttr(attr_accessor)
                                 : context->GetStringListItemAttr(**iter_ptr, attr_accessor);
      diff_info_stream_ << "attr(string_list): " << attr_name
                        << ", val: " << (attr.has_value() ? ToString(*attr) : "null") << std::endl;
    } else {
      diff_info_stream_ << "unsupport type: " << (int)attr_type << ", attr " << attr_name << std::endl;
    }
  }
  diff_info_stream_ << std::endl;
}

void AttrDiffObserver::PrintExtraCommonAttrs(ReadableRecoContextInterface *context) {
  PrintExtraAttrs(context, nullptr);
}

void AttrDiffObserver::PrintExtraItemAttrs(ReadableRecoContextInterface *context, RecoResultConstIter iter) {
  PrintExtraAttrs(context, &iter);
}

bool AttrDiffObserver::CheckAttrPairType(ReadableRecoContextInterface *context, const AttrPair &attr_pair,
                                         bool is_common_attr_pair) {
  // 1. 获取对应 attr 和 type
  auto accessor_a = is_common_attr_pair ? context->GetCommonAttrAccessor(attr_pair.first)
                                        : context->GetItemAttrAccessor(attr_pair.first);
  auto accessor_b = is_common_attr_pair ? context->GetCommonAttrAccessor(attr_pair.second)
                                        : context->GetItemAttrAccessor(attr_pair.second);
  auto type_a = accessor_a->value_type;
  auto type_b = accessor_b->value_type;

  // 2. 检查 attr 类型是否相同。如果不同，就不必再比较 attr value
  if (type_a != type_b) {
    diff_info_stream_ << "unmatched type, attr_a: " << attr_pair.first << ", type: " << (int)type_a
                      << " -- attr_b: " << attr_pair.second << ", type: " << (int)type_b << std::endl;
    return false;
  }

  // 3. 检查 attr 类型是否为 UNKNOWN。UNKNOWN 类型表示未生成，不必再比较 attr value
  if (type_a == AttrType::UNKNOWN) {
    return false;
  }

  // 4. 检查 attr 类型是否为六个 KaiFG 支持的类型
  if (type_a == AttrType::INT || type_a == AttrType::FLOAT || type_a == AttrType::STRING ||
      type_a == AttrType::INT_LIST || type_a == AttrType::FLOAT_LIST || type_a == AttrType::STRING_LIST) {
    return true;
  }

  // 5. 如果是不支持的类型，不必再比较 attr value
  diff_info_stream_ << "unsupport type: " << (int)type_a << ", attr pair(" << attr_pair.first << ", "
                    << attr_pair.second << ")" << std::endl;
  return false;
}

void AttrDiffObserver::CheckAttrPairValue(ReadableRecoContextInterface *context, RecoResultConstIter iter,
                                          const AttrPair &attr_pair, bool is_common_attr_pair) {
  auto accessor_a = is_common_attr_pair ? context->GetCommonAttrAccessor(attr_pair.first)
                                        : context->GetItemAttrAccessor(attr_pair.first);
  auto accessor_b = is_common_attr_pair ? context->GetCommonAttrAccessor(attr_pair.second)
                                        : context->GetItemAttrAccessor(attr_pair.second);
  auto attr_type = accessor_a->value_type;
  if (attr_type == AttrType::INT) {
    auto a = is_common_attr_pair ? context->GetIntCommonAttr(accessor_a)
                                 : context->GetIntItemAttr(*iter, accessor_a);
    auto b = is_common_attr_pair ? context->GetIntCommonAttr(accessor_b)
                                 : context->GetIntItemAttr(*iter, accessor_b);
    CheckAttr(a, b, attr_pair.first, attr_pair.second, &diff_info_stream_);
  } else if (attr_type == AttrType::FLOAT) {
    auto a = is_common_attr_pair ? context->GetDoubleCommonAttr(accessor_a)
                                 : context->GetDoubleItemAttr(*iter, accessor_a);
    auto b = is_common_attr_pair ? context->GetDoubleCommonAttr(accessor_b)
                                 : context->GetDoubleItemAttr(*iter, accessor_b);
    CheckAttrDouble(a, b, attr_pair.first, attr_pair.second, default_error_tolerance_, &diff_info_stream_);
  } else if (attr_type == AttrType::STRING) {
    auto a = is_common_attr_pair ? context->GetStringCommonAttr(accessor_a)
                                 : context->GetStringItemAttr(*iter, accessor_a);
    auto b = is_common_attr_pair ? context->GetStringCommonAttr(accessor_b)
                                 : context->GetStringItemAttr(*iter, accessor_b);
    CheckAttr(a, b, attr_pair.first, attr_pair.second, &diff_info_stream_);
  } else if (attr_type == AttrType::INT_LIST) {
    auto a = is_common_attr_pair ? context->GetIntListCommonAttr(accessor_a)
                                 : context->GetIntListItemAttr(*iter, accessor_a);
    auto b = is_common_attr_pair ? context->GetIntListCommonAttr(accessor_b)
                                 : context->GetIntListItemAttr(*iter, accessor_b);
    CheckAttrList(a, b, attr_pair.first, attr_pair.second, &diff_info_stream_);
  } else if (attr_type == AttrType::FLOAT_LIST) {
    auto a = is_common_attr_pair ? context->GetDoubleListCommonAttr(accessor_a)
                                 : context->GetDoubleListItemAttr(*iter, accessor_a);
    auto b = is_common_attr_pair ? context->GetDoubleListCommonAttr(accessor_b)
                                 : context->GetDoubleListItemAttr(*iter, accessor_b);
    CheckAttrListDouble(a, b, attr_pair.first, attr_pair.second, default_error_tolerance_,
                        &diff_info_stream_);
  } else if (attr_type == AttrType::STRING_LIST) {
    auto a = is_common_attr_pair ? context->GetStringListCommonAttr(accessor_a)
                                 : context->GetStringListItemAttr(*iter, accessor_a);
    auto b = is_common_attr_pair ? context->GetStringListCommonAttr(accessor_b)
                                 : context->GetStringListItemAttr(*iter, accessor_b);
    CheckAttrList(a, b, attr_pair.first, attr_pair.second, &diff_info_stream_);
  }
}

void AttrDiffObserver::Observe(ReadableRecoContextInterface *context, RecoResultConstIter begin,
                               RecoResultConstIter end) {
  // 1. diff 所有的 common attr pair
  for (const auto &attr_pair : common_attrs_) {
    // 1.1 清空 diff_info_stream_
    diff_info_stream_.clear();
    diff_info_stream_.str("");

    // 1.2 检查 attr 类型
    bool is_need_check_attr_value = CheckAttrPairType(context, attr_pair, true);

    // 1.3 检查 attr 值
    if (is_need_check_attr_value) {
      CheckAttrPairValue(context, begin, attr_pair, true);
    }

    // 1.4 打印 diff 信息，并输出 extra attr
    if (!diff_info_stream_.str().empty()) {
      PrintExtraCommonAttrs(context);
      CL_LOG(ERROR) << diff_info_stream_.str();
    }
  }

  // 2. diff 所有的 item attr pair
  for (const auto &attr_pair : item_attrs_) {
    // 2.1 清空 diff_info_stream_
    diff_info_stream_.clear();
    diff_info_stream_.str("");

    // 2.2 检查 attr 类型
    bool is_need_check_attr_value = CheckAttrPairType(context, attr_pair, false);

    // 2.3 如果不需要进一步检查 attr value，可提前返回
    if (!is_need_check_attr_value) {
      // 2.3.1 打印 diff 信息，并输出 extra attr
      if (!diff_info_stream_.str().empty()) {
        PrintExtraCommonAttrs(context);
        PrintExtraItemAttrs(context, begin);
        CL_LOG(ERROR) << diff_info_stream_.str();
      }
      continue;
    }

    // 2.4 检查 attr value
    int current_print_num = 0;
    for (auto iter = begin; iter != end; ++iter) {
      // 2.4.1 清空 diff_info_stream_
      diff_info_stream_.clear();
      diff_info_stream_.str("");

      // 2.4.2 检查当前 item 的 attr value
      CheckAttrPairValue(context, iter, attr_pair, false);

      // 2.4.3 打印 diff 信息，并输出 extra attr
      if (!diff_info_stream_.str().empty() && current_print_num < max_extra_item_attr_to_print_) {
        current_print_num++;
        PrintExtraCommonAttrs(context);
        PrintExtraItemAttrs(context, iter);
        CL_LOG(ERROR) << diff_info_stream_.str();
      }
    }
  }
}

bool AttrDiffObserver::InitProcessor() {
  default_error_tolerance_ = config()->GetFloat("default_error_tolerance", 1e-5);

  auto common_attrs = config()->Get("common_attrs");
  if (common_attrs && common_attrs->IsArray()) {
    for (auto c : common_attrs->array()) {
      auto attr_a = c->GetString("attr_a");
      auto attr_b = c->GetString("attr_b");
      CHECK(!attr_a.empty() && !attr_b.empty()) << "attr cannot be empty.";
      common_attrs_.emplace_back(std::make_pair(attr_a, attr_b));
    }
  }

  auto item_attrs = config()->Get("item_attrs");
  if (item_attrs && item_attrs->IsArray()) {
    for (auto c : item_attrs->array()) {
      auto attr_a = c->GetString("attr_a");
      auto attr_b = c->GetString("attr_b");
      CHECK(!attr_a.empty() && !attr_b.empty()) << "attr cannot be empty.";
      item_attrs_.emplace_back(std::make_pair(attr_a, attr_b));
    }
  }

  auto extra_common_attrs = config()->Get("extra_common_attrs");
  if (extra_common_attrs && extra_common_attrs->IsArray()) {
    for (auto c : extra_common_attrs->array()) {
      auto attr_name = c->StringValue();
      CHECK(!attr_name.empty()) << "attr cannot be empty.";
      extra_common_attrs_.emplace_back(attr_name);
    }
  }

  auto extra_item_attrs = config()->Get("extra_item_attrs");
  if (extra_item_attrs && extra_item_attrs->IsArray()) {
    for (auto c : extra_item_attrs->array()) {
      auto attr_name = c->StringValue();
      CHECK(!attr_name.empty()) << "attr cannot be empty.";
      extra_item_attrs_.emplace_back(attr_name);
    }
  }

  return true;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AttrDiffObserver, AttrDiffObserver)

}  // namespace ks::platform
