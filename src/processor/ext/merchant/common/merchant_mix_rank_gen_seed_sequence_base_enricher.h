#pragma once

#include <algorithm>
#include <string>
#include <utility>
#include <vector>
#include <set>
#include <unordered_set>
#include <unordered_map>
#include <tuple>
#include <deque>
#include <memory>

#include "dragon/src/module/common_reco_score_base.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "serving_base/utility/timer.h"
#include "Eigen/Dense"

namespace ks {
namespace platform {

enum class DType {
  CID1 = 1,
  CID2 = 2,
  CID3 = 3,
  AID = 4,
  LIVE = 5,
  MMU = 6,
  AD = 7,
  LEAF = 8,
  SPU = 9,
  SESSION_LIVE = 10,
  BRAND_ID = 11,
  RECALL = 12,
  VGS_ID = 13,
  MULTI_CID2 = 14,
  MULTI_CID3 = 15,
  MULTI_LEAF = 16,
  REBUY = 17,
  MULTI_CID1 = 18,
  PW_ID = 19,
  ORDER_C_LEAF = 20
};

struct SlideWindowInfo {
  int64 window_size = 4;  // 滑动窗口长度
  int64 window_max_num = 1;  // 窗口内最多出现个数
};

struct ItemScoreInfo {
  uint64_t item_key = 1;
  int64_t item_type = 0;
  double live_merge_score = 0.0;
  std::vector<double> seq_score_list;  // 根据各队列组合权重计算的总分列表
  std::vector<double> queue_value_list;  // 各队列取值
  std::vector<double> pos_score_list;  // 正常生成序列时对应坑位分
  std::vector<double> force_insert_pos_score_list;  // 强插时对应坑位分
  double listwise_ctr = 0.0;  // listwise ctr score
  double listwise_cvr = 0.0;  // listwise cvr score
  double listwise_price = 1.0;  // listwise price score
  double listwise_goods_bonus = 1.0;  // listwise goods bonus score
  int64_t trans_cid1 = 0;  // 替换后一级类目
  int64_t trans_cid2 = 0;  // 替换后二级类目
  int64_t trans_cid3 = 0;  // 替换后三级类目
  int64_t trans_leaf = 0;  // 替换后叶子结点
  int64_t item_cid1_for_embd = 0;  // mmu 打散 key
  int64_t brand_id = 0;  // 有效品牌 id
  int64_t vgs_id = 0;  // 图搜 iGoodsVGSSpuIdV2KV, tag_id
  int64_t rebuy_variant_flag = 0;  // 是否已购叶子类目
  int64_t aid = 0;  // 店铺
  int64_t spu_id = 0;  // spu
  int64_t is_ad = 0;  // 是否广告
  double ad_ecpm = 0.0;  // 广告 ecpm
  int64_t is_recall = 0;  // 是否重定向
  int64_t pw_id = 0;  // 产品词  iGoodsProductWordKV
  int64_t order_c_leaf = 0;  // 已购类目 flag
  int64_t mix_onx_position = -1;  // 强插位置
  int onx_force_insert_flag = 0;  //  按坑位强插标识
  int64_t is_model_generator = 0;  // 是否访问了生成模型
  int64_t is_top_expert_aid = 0;  // 头达标记
  std::unordered_set<DType> skip_d_types = {};  // 豁免多样性类型
  SlideWindowInfo trans_cid1_slide_info;
  SlideWindowInfo trans_cid2_slide_info;
  SlideWindowInfo trans_cid3_slide_info;
  SlideWindowInfo trans_leaf_slide_info;
};

// 多样性软性打分公式，对相似度和坑位距离做映射
// score_weight = w1 * {1 / (exp^(w2 * (pos_gap - bias_b1) + 1) + bias_b2}
// w1 设置多样性规则强弱, w1 越大，则该多样性规则力度就越大
// w2 设置坑位 gap 影响不同 gap 的差异，gap 越小，则在 bias_w1 两侧坑位影响就越平滑，
// bias_w1 坑位距离影响阶跃点
// bias_b 权重最小值
// 默认值输出最终权重 1000，近似对应硬规则的 3 出 1
struct SoftDiversityWeightInfos {
  double rule_weight_w1 = 1.0;
  double pos_gap_weight_w2 = 20.0;
  double bias_b1 = 9.5;
  double bias_b2 = 0;
};

struct DiversityInfos {
  void clear() {
    mmu_cid1_threshold_map.clear();
    item_key_2_embedding_map.clear();
    item_pair_mmu_score_map.clear();
    soft_key_to_weight_map.clear();
  }
  std::unordered_map<int64_t, double> mmu_cid1_threshold_map;
  std::unordered_map<int64_t, std::vector<double>> item_key_2_embedding_map;
  // 保存 item pair 的 mmu 内积值，避免重复计算
  std::unordered_map<std::tuple<int64_t, int64_t>, double> item_pair_mmu_score_map;
  std::unordered_map<int64, SoftDiversityWeightInfos> soft_key_to_weight_map;
  double default_mmu_cid1_threshold_ = 0.5;
  int pos_gap_offset_ = 0;
};

class SlideWindowBase {
 public:
  explicit SlideWindowBase(DType d_type, size_t window_size, size_t window_max_num,
                          std::unordered_set<int> target_item_type = {})
      : d_type_(d_type), window_size_(window_size), window_max_num_(window_max_num),
        target_item_type_(std::move(target_item_type)) {}

  SlideWindowBase(const SlideWindowBase &s)
      : data_(s.data_), d_type_(s.d_type_), window_size_(s.window_size_), window_max_num_(s.window_max_num_),
        target_item_type_(s.target_item_type_) {}

  virtual ~SlideWindowBase() = default;

  virtual bool HardCheck(const ItemScoreInfo* score_info,
                         const int pos_index,
                         DiversityInfos* diversity_infos) {return true;}
  virtual double SoftCheck(const ItemScoreInfo* score_info,
                           const int pos_index,
                           DiversityInfos* diversity_infos) {return 0;}
  virtual void Update(const ItemScoreInfo* score_info,
                      DiversityInfos* diversity_infos,
                      const int pos_index = -1) {}

  inline DType GetDType() {
    return d_type_;
  }

  inline int GetGapMpa(const int pos_index, const int pos_gap, const int offset) {
    if (pos_index % 2 == 0 && pos_gap % 2 == 1) {
      return pos_gap + offset;
    }
    return pos_gap;
  }
  // score_weight = w1 * {1 / (exp^(w2 * (pos_gap - bias_b1) + 1) + bias_b2}
  inline double GetGapWeight(const std::unordered_map<int64, SoftDiversityWeightInfos>
                             &soft_key_to_weight_map,
                             const int pos_gap) {
    SoftDiversityWeightInfos soft_weight_infos;
    auto iter = soft_key_to_weight_map.find(static_cast<int64>(d_type_));
    if (iter != soft_key_to_weight_map.end()) {
      soft_weight_infos = iter->second;
    }
    double gap_w = soft_weight_infos.pos_gap_weight_w2 * (pos_gap - soft_weight_infos.bias_b1);
    gap_w = std::max(-10.0, gap_w);
    gap_w = std::min(10.0, gap_w);
    return soft_weight_infos.rule_weight_w1 * (1.0 / (std::exp(gap_w) + 1 ) + soft_weight_infos.bias_b2);
  }

  inline int64_t GetDiversityKey(const ItemScoreInfo* score_info) const {
    if (score_info == nullptr) {
      return -1;
    }
    switch (d_type_) {
      case DType::CID1:
        return score_info->trans_cid1;
      case DType::CID2:
        return score_info->trans_cid2;
      case DType::CID3:
        return score_info->trans_cid3;
      case DType::AID:
        return score_info->aid;
      case DType::LIVE:
        return score_info->item_type;
      case DType::MMU:
        return score_info->item_cid1_for_embd;
      case DType::AD:
        return score_info->is_ad;
      case DType::LEAF:
        return score_info->trans_leaf;
      case DType::SPU:
        return score_info->spu_id;
      case DType::SESSION_LIVE:
        return score_info->item_type;
      case DType::BRAND_ID:
        return score_info->brand_id;
      case DType::RECALL:
        return score_info->is_recall;
      case DType::VGS_ID:
        return score_info->vgs_id;
      case DType::MULTI_CID1:
        return score_info->trans_cid1;
      case DType::MULTI_CID2:
        return score_info->trans_cid2;
      case DType::MULTI_CID3:
        return score_info->trans_cid3;
      case DType::MULTI_LEAF:
        return score_info->trans_leaf;
      case DType::REBUY:
        return score_info->rebuy_variant_flag;
      case DType::PW_ID:
        return score_info->pw_id;
      case DType::ORDER_C_LEAF:
        return score_info->order_c_leaf;
      default:
        return -1;
    }
    return -1;
  }

  inline SlideWindowInfo GetDiversitySlide(const ItemScoreInfo* score_info) const {
    SlideWindowInfo default_slide_info;
    if (score_info == nullptr) {
      return default_slide_info;
    }
    switch (d_type_) {
      case DType::MULTI_CID1:
        return score_info->trans_cid1_slide_info;
      case DType::MULTI_CID2:
        return score_info->trans_cid2_slide_info;
      case DType::MULTI_CID3:
        return score_info->trans_cid3_slide_info;
      case DType::MULTI_LEAF:
        return score_info->trans_leaf_slide_info;
      default:
        return default_slide_info;
    }
    return default_slide_info;
  }

  virtual std::shared_ptr<SlideWindowBase> Clone() const {
      return std::make_shared<SlideWindowBase>(*this);
  }

 protected:
  std::deque<int64_t> data_;
  DType d_type_;
  size_t window_size_;
  size_t window_max_num_;
  std::unordered_set<int> target_item_type_;  // 空值表示所有 item 类型都处理，默认都处理
};

// 序列信息
struct SequenceInfos {
  uint64 item_key = 1;
  double sequence_score = 0.0;  // 序列分数
  int ad_num = 0;  // 包含广告数
  int ad_first_insert_pos_index = 99;  // 广告可以插入的最小坑位，此处暂未做多样性判断
  int is_insert_top_aid = 0;  // 是否已插入头达作品
  std::vector<ItemScoreInfo*> score_infos;  // ItemScoreInfo 队列
  std::vector<std::shared_ptr<SlideWindowBase>> diversity_rules;  // 多样性规则
  std::vector<int> selected_flag_list;  // 记录原始序列中被该序列的选中的 index 标识 , 0 未选择，1 已选择
  std::vector<int64> seed_sequence;  // 最后生成的队列 id 集合
  std::vector<int64> seed_sequence_item_type;
  std::vector<int64> seed_sequence_trans_cid1;
  std::vector<int64> seed_sequence_trans_cid2;
  std::vector<int64> seed_sequence_trans_cid3;
  std::vector<int64> seed_sequence_trans_leaf;
  std::vector<int64> seed_sequence_aid;
  std::vector<double> seed_sequence_ctrs;
  std::vector<double> seed_sequence_cvrs;
  std::vector<double> seed_sequence_prices;
  std::vector<double> seed_sequence_goods_bonus;
  std::vector<double> seed_sequence_diversity_score;
  std::vector<double> seed_sequence_score_flags;

  SequenceInfos() = default;

  explicit SequenceInfos(int size)
    : selected_flag_list(size, 0) {
      score_infos.reserve(size);
      seed_sequence.reserve(size);
      seed_sequence_item_type.reserve(size);
      seed_sequence_trans_cid1.reserve(size);
      seed_sequence_trans_cid2.reserve(size);
      seed_sequence_trans_cid3.reserve(size);
      seed_sequence_trans_leaf.reserve(size);
      seed_sequence_aid.reserve(size);
      seed_sequence_ctrs.reserve(size);
      seed_sequence_cvrs.reserve(size);
      seed_sequence_prices.reserve(size);
      seed_sequence_goods_bonus.reserve(size);
      seed_sequence_diversity_score.reserve(size);
      seed_sequence_score_flags.reserve(size);
    }

  SequenceInfos(const SequenceInfos& s)
    : item_key(s.item_key),
      ad_num(s.ad_num),
      ad_first_insert_pos_index(s.ad_first_insert_pos_index),
      is_insert_top_aid(s.is_insert_top_aid),
      sequence_score(s.sequence_score),
      score_infos(s.score_infos),
      selected_flag_list(s.selected_flag_list),
      seed_sequence(s.seed_sequence),
      seed_sequence_item_type(s.seed_sequence_item_type),
      seed_sequence_trans_cid1(s.seed_sequence_trans_cid1),
      seed_sequence_trans_cid2(s.seed_sequence_trans_cid2),
      seed_sequence_trans_cid3(s.seed_sequence_trans_cid3),
      seed_sequence_trans_leaf(s.seed_sequence_trans_leaf),
      seed_sequence_aid(s.seed_sequence_aid),
      seed_sequence_ctrs(s.seed_sequence_ctrs),
      seed_sequence_cvrs(s.seed_sequence_cvrs),
      seed_sequence_prices(s.seed_sequence_prices),
      seed_sequence_diversity_score(s.seed_sequence_diversity_score),
      seed_sequence_score_flags(s.seed_sequence_score_flags),
      seed_sequence_goods_bonus(s.seed_sequence_goods_bonus) {
        diversity_rules.reserve(s.diversity_rules.size());
        for (const auto& diversity_rule : s.diversity_rules) {
          diversity_rules.emplace_back(diversity_rule->Clone());
        }
    }

  SequenceInfos(SequenceInfos&& s) noexcept
    : item_key(s.item_key),
      ad_num(s.ad_num),
      ad_first_insert_pos_index(s.ad_first_insert_pos_index),
      is_insert_top_aid(s.is_insert_top_aid),
      sequence_score(s.sequence_score),
      score_infos(std::move(s.score_infos)),
      diversity_rules(std::move(s.diversity_rules)),
      selected_flag_list(std::move(s.selected_flag_list)),
      seed_sequence(std::move(s.seed_sequence)),
      seed_sequence_item_type(std::move(s.seed_sequence_item_type)),
      seed_sequence_trans_cid1(std::move(s.seed_sequence_trans_cid1)),
      seed_sequence_trans_cid2(std::move(s.seed_sequence_trans_cid2)),
      seed_sequence_trans_cid3(std::move(s.seed_sequence_trans_cid3)),
      seed_sequence_trans_leaf(std::move(s.seed_sequence_trans_leaf)),
      seed_sequence_aid(std::move(s.seed_sequence_aid)),
      seed_sequence_ctrs(std::move(s.seed_sequence_ctrs)),
      seed_sequence_cvrs(std::move(s.seed_sequence_cvrs)),
      seed_sequence_prices(std::move(s.seed_sequence_prices)),
      seed_sequence_diversity_score(std::move(s.seed_sequence_diversity_score)),
      seed_sequence_score_flags(std::move(s.seed_sequence_score_flags)),
      seed_sequence_goods_bonus(std::move(s.seed_sequence_goods_bonus)) {}

  SequenceInfos& operator=(SequenceInfos&& s) noexcept {
    if (this != &s) {
      item_key = s.item_key;
      ad_num = s.ad_num;
      ad_first_insert_pos_index = s.ad_first_insert_pos_index;
      is_insert_top_aid = s.is_insert_top_aid,
      sequence_score = s.sequence_score;
      score_infos = std::move(s.score_infos);
      diversity_rules = std::move(s.diversity_rules);
      selected_flag_list = std::move(s.selected_flag_list);
      seed_sequence = std::move(s.seed_sequence);
      seed_sequence_item_type = std::move(s.seed_sequence_item_type);
      seed_sequence_trans_cid1 = std::move(s.seed_sequence_trans_cid1);
      seed_sequence_trans_cid2 = std::move(s.seed_sequence_trans_cid2);
      seed_sequence_trans_cid3 = std::move(s.seed_sequence_trans_cid3);
      seed_sequence_trans_leaf = std::move(s.seed_sequence_trans_leaf);
      seed_sequence_aid = std::move(s.seed_sequence_aid);
      seed_sequence_ctrs = std::move(s.seed_sequence_ctrs);
      seed_sequence_cvrs = std::move(s.seed_sequence_cvrs);
      seed_sequence_prices = std::move(s.seed_sequence_prices);
      seed_sequence_goods_bonus = std::move(s.seed_sequence_goods_bonus);
      seed_sequence_diversity_score = std::move(s.seed_sequence_diversity_score);
      seed_sequence_score_flags = std::move(s.seed_sequence_score_flags);
    }
    return *this;
  }

  void CopyDiversityRule(const std::vector<std::shared_ptr<SlideWindowBase>>& from_diversity_rules) {
    diversity_rules.reserve(from_diversity_rules.size());
    for (const auto& diversity_rule : from_diversity_rules) {
      diversity_rules.emplace_back(diversity_rule->Clone());
    }
  }
};

class MerchantMixRankGenSeedSequenceBaseEnricher : public CommonRecoBaseEnricher {
 public:
  MerchantMixRankGenSeedSequenceBaseEnricher() {}

  bool InitializeConfig(MutableRecoContextInterface *context);
  void GenSeedSequenceAndKey(SequenceInfos* sequence_infos);
  void SetSequenceAttrValue(SequenceInfos* sequence_infos);
  void ConvertSequenceAttrToItemAttr(MutableRecoContextInterface *context, SequenceInfos* sequence_infos);
  void InitDiversityInfos(MutableRecoContextInterface *context,
                         RecoResultConstIter begin,
                         RecoResultConstIter end);
  void InitDiversityRule(MutableRecoContextInterface *context,
                         SequenceInfos* sequence_infos);
  bool CovertItemsToScoreInfos(MutableRecoContextInterface *context,
                                  RecoResultConstIter begin,
                                  RecoResultConstIter end,
                                  const int score_list_num,
                                  std::vector<ItemScoreInfo>* score_infos);
  // 支持子类自定义,硬规则判断
  virtual int DiversityRuleHardCheck(const SequenceInfos& sequence_infos, const ItemScoreInfo* score_info);
  // 支持子类自定义,软性打分
  virtual double DiversityRuleSoftCheck(const SequenceInfos& sequence_infos, const ItemScoreInfo* score_info);
  void DiversityRuleUpdate(const ItemScoreInfo* score_info, SequenceInfos* sequence_infos);
  void ProcessOneSequences(SequenceInfos* curr_sequences_infos,
                          const SequenceInfos& origin_sequence_infos,
                          std::unordered_set<uint64>* seq_set,
                          std::vector<int64>* retrieve_items,
                          MutableRecoContextInterface* context);
  void FillSequenceToMaxLength(SequenceInfos* curr_sequences_infos,
                              const SequenceInfos& origin_sequence_infos,
                              const int max_length);
  void GenAdFirstInsertIndex(const SequenceInfos& origin_sequence_infos, SequenceInfos* curr_sequences_infos);
  int SelectedAdIndex(const SequenceInfos& curr_sequences_infos, const SequenceInfos& origin_sequence_infos);
  int SelectedMaxPctrIndex(const SequenceInfos& curr_sequences_infos,
                           const SequenceInfos& origin_sequence_infos);
  int SelectedOnxForceInsertIndex(const SequenceInfos& curr_sequences_infos,
                      const SequenceInfos& origin_sequence_infos);
  int SelectedReddotLiveForceInsertIndex(const SequenceInfos& curr_sequences_infos,
                                         const SequenceInfos& origin_sequence_infos);
  int SelectedReddotLiveForceInsertIndexByMode(const SequenceInfos &curr_sequences_infos,
                                               const SequenceInfos &origin_sequence_infos,
                                               const std::vector<double> &index_candidate_prob_matrix);
  void UpdateCurSequence(const SequenceInfos &origin_sequence_infos, const int select_index,
                         const double score, const double diversity_score,
                         SequenceInfos *curr_sequences_infos);
  inline int FindFirstUnselectedItem(const std::vector<int>& selected_flag_list) {
    for (int i = 0; i < selected_flag_list.size(); ++i) {
      if (selected_flag_list[i] == 0) {
        return i;
      }
    }
    return 0;
  }

  const std::unordered_set<int>& get_valid_item_type_set(DType dtype,
    const std::unordered_set<int>& default_set) {
    auto iter = diversity_dtype_valid_item_type_map_.find(static_cast<int>(dtype));
    if (iter != diversity_dtype_valid_item_type_map_.end()) {
      return iter->second;
    }
    return default_set;
  }

 protected:
  struct QueueAttr {
    std::string attr_name;
    bool reverse_order = false;
    bool neg_weight = false;
    double weight_base = 0.0;  //  队列权重取值基准
    double weight_bias_range = 0.0;  //  随机取值范围
    double weight_lower_bound = 0.0;  //  lower_bound
    int64 merge_type = 0;  // 0->pow; 1-> * random_w; 2-> +
    int64 is_skip = 0;  // 1 -> skip
    double default_value = -10;  // 默认值
  };

  virtual void release() {
    diversity_infos_.clear();
    diversity_dtype_valid_item_type_map_.clear();
    force_insert_max_pctr_pos_set_.clear();
    mix_onx_position_sets_.clear();
    max_force_insert_pos_weight_list_map_.clear();
    queues_.clear();
    hard_diversity_rule_type_.clear();
    soft_diversity_rule_type_.clear();
    weight_group_list_.clear();
    gen_attr_merge_type_list_.clear();
    gen_seq_sort_type_list_.clear();
    soft_diversity_score_merge_weight_list_.clear();
  }

 protected:
  DiversityInfos diversity_infos_;
  std::unordered_map<int, std::unordered_set<int>>
    diversity_dtype_valid_item_type_map_ = {};
  std::mt19937 gen_;
  std::unordered_set<int> force_insert_max_pctr_pos_set_ = {};
  std::unordered_set<int>  mix_onx_position_sets_ = {};
  std::unordered_map<int, std::tuple<std::vector<int>, std::vector<double>>>
    max_force_insert_pos_weight_list_map_ = {};
  int is_release_ad_first_index_enable_ = 0;
  int is_ad_max_ecpm_first_ = 1;
  int beamsearch_insert_ad_diversity_enable_ = 0;
  int reddot_force_insert_live_ = 0;
  int force_insert_max_pctr_once_enable_ = 0;
  int is_max_force_insert_skip_diversity_ = 0;
  int is_onx_force_insert_enable_ = 0;
  int is_uniform_gen_ad_first_insert_pos_ = 0;
  int64 page_offset_ = 0;
  int top4_no_ad_when_live_top1_enable_ = 0;
  int enable_ad_avoid_top_expert_ = 0;
  int aid_avoid_pos_ = 0;
  int reco_expert_aid_num_ = 0;
  int mix_ad_adpos_ = 99;
  int mix_ad_windows_size_ = 4;
  int next_page_ad_offset_ = 0;
  int last_request_ad_pos_ = -99;
  int is_random_force_insert_ad_ = 0;
  int64 is_merge_ad_diversity_ = 0;
  int ad_drop_prob_ = 0;
  int ad_insert_prob_ = 50;
  double normal_dis_mean_ = 4;
  double normal_dis_std_ = 2;
  int64 listwise_new_generator_ad_enable_ = 0;
  int max_sequence_num_ = 0;
  int sequence_max_length_ = 0;
  std::vector<QueueAttr> queues_;
  int seed_sequence_generated_reason_ = 0;
  int return_item_type_ = 2;
  int64 skip_fill_sequence_early_return_ = 0;
  int is_need_origin_sequence_ = 0;
  int enable_live_disturbance_ = 0;
  int valid_sampling_length_ = 10;
  double live_merge_weight_ = 1.0;
  double soft_diversity_threshold_ = 1.0;
  int enable_soft_diversity_threshold_ = 0.0;
  int enable_fill_sequence_diversity_ = 0;
  std::unordered_set<DType> hard_diversity_rule_type_ = {};
  std::unordered_set<DType> soft_diversity_rule_type_ = {};
  // 默认为，{[ctr, ctr * cvr, ctr * cvr * price]} 的权重
  std::vector<std::vector<double>> weight_group_list_ =
    {{1, 0, 0}, {0, 1, 0}, {0, 0, 1}, {1, 1, 0}, {1, 1, 1}};
  // 支持不同权重组的融合方式不一样， 空值则使用 gen_seq_sort_type_
  int gen_seq_sort_type_ = 0;
  // 0: 权重乘，1: 权重指数
  std::vector<int> gen_attr_merge_type_list_ =
  {1, 1, 1, 1, 1};
  // 1: 加法， 0: 乘法
  std::vector<int> gen_seq_sort_type_list_ =
  {0, 0, 0, 0, 0};
  double soft_diversity_score_merge_weight_ = 0.0;
  // 支持不同权重组的软性多样性贡献权重不一样,
  std::vector<double> soft_diversity_score_merge_weight_list_ =
    {0.0, 0.0, 0.0, 0.0, 0.0};

  ItemAttr* gen_item_list_accessor_ = nullptr;
  ItemAttr* gen_item_type_list_accessor_ = nullptr;
  ItemAttr* gen_item_ctr_list_accessor_ = nullptr;
  ItemAttr* gen_item_cvr_list_accessor_ = nullptr;
  ItemAttr* gen_item_price_list_accessor_ = nullptr;
  ItemAttr* gen_item_bonus_list_accessor_ = nullptr;
  ItemAttr* gen_item_cid1_list_accessor_ = nullptr;
  ItemAttr* gen_item_cid2_list_accessor_ = nullptr;
  ItemAttr* gen_item_cid3_list_accessor_ = nullptr;
  ItemAttr* gen_item_aid_list_accessor_ = nullptr;
  ItemAttr* gen_item_diversity_list_accessor_ = nullptr;
  ItemAttr* gen_item_score_flag_list_accessor_ = nullptr;

 private:
  DISALLOW_COPY_AND_ASSIGN(MerchantMixRankGenSeedSequenceBaseEnricher);
};

class SlideWindowCounter : public SlideWindowBase {
 public:
  explicit SlideWindowCounter(DType d_type, size_t window_size, size_t window_max_num,
                              std::unordered_set<int> target_item_type = {})
      : SlideWindowBase(d_type, window_size, window_max_num, target_item_type) {}

  SlideWindowCounter(const SlideWindowCounter &s) : SlideWindowBase(s), counter_(s.counter_) {}

  inline bool HardCheck(const ItemScoreInfo* score_info,
                        const int pos_index,
                        DiversityInfos* diversity_infos) override {
    if (score_info == nullptr) {
      return false;
    }
    const auto target_type_iter = target_item_type_.find(score_info->item_type);
    // 如果设置了 target_item_type， 同时 curr_item_type 未在 set 中，则校验通过。
    if (target_item_type_.size() > 0 && target_type_iter == target_item_type_.end()) {
      return true;
    }
    const int64_t key = GetDiversityKey(score_info);
    // 未查询到有效 key，则直接校验通过，表示不需要校验
    if (key <= 0) {
      return true;
    }
    const auto iter = counter_.find(key);
    if (iter == counter_.end()) {
      return true;
    } else {
      return iter->second < window_max_num_;
    }
  }

  double SoftCheck(const ItemScoreInfo* score_info,
                   const int pos_index,
                   DiversityInfos* diversity_infos) override {
    double final_score = 0.0;
    if (score_info == nullptr) {
        return final_score;
      }
    auto target_type_iter = target_item_type_.find(score_info->item_type);
    if (target_item_type_.size() > 0 && target_type_iter == target_item_type_.end()) {
        return final_score;
    }
    const int64_t key = GetDiversityKey(score_info);
    // 未查询到 key，则相似分为 0
    if (key <= 0) {
      return final_score;
    }
    const auto iter = counter_.find(key);
    if (iter == counter_.end()) {
      return final_score;
    } else {
        int pos_gap = data_.size();
        for (const auto& curr_key : data_) {
            if (curr_key == key) {
              pos_gap = GetGapMpa(pos_index, pos_gap, diversity_infos->pos_gap_offset_);
              final_score += GetGapWeight(diversity_infos->soft_key_to_weight_map, pos_gap);
            }
            --pos_gap;
        }
      return final_score;
    }
  }

  inline void Update(const ItemScoreInfo* score_info,
                     DiversityInfos* diversity_infos,
                     const int pos_index = -1) override {
    int64_t key = GetDiversityKey(score_info);
    const auto target_type_iter = target_item_type_.find(score_info->item_type);
    // 如果设置了 target_item_type， 该商品的 item_type 与目标不一致，则直接将 key 置为 0
    if (target_item_type_.size() > 0 && target_type_iter == target_item_type_.end()) {
      key = 0;
    }
    if (data_.size() == window_size_) {
      this->Pop();
    }
    // 无论 key 是否有效，不影响窗口更新
    data_.push_back(key);
    counter_[key] += 1;
  }

  inline void Pop() {
    if (!(data_.empty())) {
      auto iter = counter_.find(data_.front());
      if (iter != counter_.end()) {
        if (iter->second == 1) {
          counter_.erase(iter);
        } else {
          iter->second -= 1;
        }
      }
      data_.pop_front();
    }
  }
  virtual std::shared_ptr<SlideWindowBase> Clone() const {
      return std::make_shared<SlideWindowCounter>(*this);
  }

 private:
  std::unordered_map<int64_t, int> counter_;
};

class MultiSlideWindowCounter : public SlideWindowBase {
 public:
  explicit MultiSlideWindowCounter(DType d_type, size_t window_size, size_t window_max_num,
                              std::unordered_set<int> target_item_type = {})
      : SlideWindowBase(d_type, window_size, window_max_num, target_item_type) {}

  MultiSlideWindowCounter(const MultiSlideWindowCounter &s) :
    SlideWindowBase(s), counter_(s.counter_), data_window_(s.data_window_),
    slide_windows_size_set_(s.slide_windows_size_set_) {}

  inline bool HardCheck(const ItemScoreInfo* score_info,
                        const int pos_index,
                        DiversityInfos* diversity_infos) override {
    if (score_info == nullptr) {
      return false;
    }
    const auto target_type_iter = target_item_type_.find(score_info->item_type);
    // 如果设置了 target_item_type， 同时 curr_item_type 未在 set 中，则校验通过。
    if (target_item_type_.size() > 0 && target_type_iter == target_item_type_.end()) {
      return true;
    }
    const int64_t key = GetDiversityKey(score_info);
    // 未查询到有效 key，则直接校验通过，表示不需要校验
    if (key <= 0) {
      return true;
    }
    const SlideWindowInfo slide_info = GetDiversitySlide(score_info);
    const auto iter = counter_.find(key);
    if (iter == counter_.end()) {
      return true;
    } else {
      return iter->second < slide_info.window_max_num;
    }
  }

  double SoftCheck(const ItemScoreInfo* score_info,
                   const int pos_index,
                   DiversityInfos* diversity_infos) override {
    double final_score = 0.0;
    if (score_info == nullptr) {
        return final_score;
      }
    auto target_type_iter = target_item_type_.find(score_info->item_type);
    if (target_item_type_.size() > 0 && target_type_iter == target_item_type_.end()) {
        return final_score;
    }
    const int64_t key = GetDiversityKey(score_info);
    // 未查询到 key，则相似分为 0
    if (key <= 0) {
      return final_score;
    }
    const auto iter = counter_.find(key);
    if (iter == counter_.end()) {
      return final_score;
    } else {
        const SlideWindowInfo slide_info = GetDiversitySlide(score_info);
        const int data_size = data_.size();
        const int begin_index = std::max(0, data_size - static_cast<int>(slide_info.window_size));
        int pos_gap = data_size - begin_index;
        for (int64 i = begin_index; i < data_size; ++i) {
            const auto& curr_key = data_[i];
            if (curr_key == key) {
              pos_gap = GetGapMpa(pos_index, pos_gap, diversity_infos->pos_gap_offset_);
              final_score += GetGapWeight(diversity_infos->soft_key_to_weight_map, pos_gap);
            }
            --pos_gap;
        }
      return final_score;
    }
  }

  inline void Update(const ItemScoreInfo* score_info,
                     DiversityInfos* diversity_infos,
                     const int pos_index = -1) override {
    int64_t key = GetDiversityKey(score_info);
    const auto target_type_iter = target_item_type_.find(score_info->item_type);
    // 如果设置了 target_item_type， 该商品的 item_type 与目标不一致，则直接将 key 置为 0
    if (target_item_type_.size() > 0 && target_type_iter == target_item_type_.end()) {
      key = 0;
    }
    const int data_size = data_.size();
    if (data_size > 0) {
      for (const int64 window_size : slide_windows_size_set_) {
        const int window_pop_index = data_size - static_cast<int>(window_size);
        // 滑动窗口长度大于等于规则窗口长度时才更新
        if (window_pop_index >= 0 && window_pop_index < data_size) {
          // 物料的窗口等于需要更新的窗口时，更新计数
          if (window_pop_index < data_window_.size() && data_window_[window_pop_index] == window_size) {
            auto iter = counter_.find(data_[window_pop_index]);
            if (iter != counter_.end()) {
              if (iter->second == 1) {
                counter_.erase(iter);
              } else {
                iter->second -= 1;
              }
            }
          }
        }
      }
    }
    // 等于最大窗口长度时 pop
    if (data_size == window_size_) {
      this->Pop();
    }
    // 无论 key 是否有效，不影响窗口更新
    const SlideWindowInfo slide_info = GetDiversitySlide(score_info);
    slide_windows_size_set_.insert(slide_info.window_size);
    data_.push_back(key);
    data_window_.push_back(slide_info.window_size);
    counter_[key] += 1;
  }

  inline void Pop() {
    if (!(data_.empty())) {
      data_.pop_front();
    }
    if (!(data_window_.empty())) {
      data_window_.pop_front();
    }
  }
  virtual std::shared_ptr<SlideWindowBase> Clone() const {
      return std::make_shared<MultiSlideWindowCounter>(*this);
  }

 private:
  std::unordered_map<int64_t, int> counter_;
  std::deque<int64> data_window_;
  std::unordered_set<int64> slide_windows_size_set_;
};

class SlideWindowListCalCounter : public SlideWindowBase {
 public:
  explicit SlideWindowListCalCounter(DType d_type, size_t window_size, size_t window_max_num,
                                     std::unordered_set<int> target_item_type = {})
      : SlideWindowBase(d_type, window_size, window_max_num, target_item_type) {}

  SlideWindowListCalCounter(const SlideWindowListCalCounter &s)
      : SlideWindowBase(s), counter_list_(s.counter_list_) {}

  std::tuple<int, double> GetCheckNumAndScore(const ItemScoreInfo* score_info,
                                              const int pos_index,
                                              DiversityInfos* diversity_infos);
  bool HardCheck(const ItemScoreInfo* score_info,
                 const int pos_index,
                 DiversityInfos* diversity_infos) override;
  double SoftCheck(const ItemScoreInfo* score_info,
                   const int pos_index,
                   DiversityInfos* diversity_infos) override;
  int UpdateCountNum(DiversityInfos* diversity_infos,
                    const ItemScoreInfo* score_info,
                    const bool is_pop,
                    const int pos_index);
  void Update(const ItemScoreInfo* score_info,
              DiversityInfos* diversity_infos,
              const int pos_index = -1) override;
  inline double CalculateInnerProduct(const std::vector<double>& vec1, const std::vector<double>& vec2) {
    double result = 0.0;
    for (size_t i = 0; i < vec1.size(); ++i) {
      result += vec1[i] * vec2[i];
    }
    return result;
  }
  virtual std::shared_ptr<SlideWindowBase> Clone() const {
      return std::make_shared<SlideWindowListCalCounter>(*this);
  }

 private:
  // 记录 每个 item 在前序窗口中已有几个相似商品
  std::unordered_map<int64_t, std::deque<std::tuple<int64_t, int>>> counter_list_;
};

}  // namespace platform
}  // namespace ks
