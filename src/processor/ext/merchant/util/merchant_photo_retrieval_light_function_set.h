#pragma once

#include <algorithm>
#include <cmath>
#include <limits>
#include <map>
#include <memory>
#include <optional>
#include <random>
#include <set>
#include <sstream>
#include <string>
#include <utility>
#include <vector>
#include "absl/strings/string_view.h"
#include "base/strings/string_split.h"
#include "dragon/src/module/common_reco_light_function.h"
#include "folly/container/F14Map.h"
#include "folly/container/F14Set.h"
#include "ks/action/kuiba_predict_photo_store_item.h"
#include "ks/reco_pub/reco/distributed_photo_info/protoutil/attr_kv_defaults.h"
#include "ks/reco_pub/reco/distributed_photo_info/protoutil/attr_kv_photo_store_item.h"
#include "ks/reco_pub/reco/util/util.h"
#include "ks/serving_util/kess_grpc_helper.h"
#include "serving_base/util/coded_message.h"
#include "serving_base/util/trim_rule.h"

namespace ks {
namespace platform {

class MerchantPhotoRetrievalLightFunctionSet : public ks::platform::CommonRecoBaseLightFunctionSet {
 public:
  MerchantPhotoRetrievalLightFunctionSet() {
    REGISTER_LIGHT_FUNCTION(GenHasPhotoInfoPtrAttr);
    REGISTER_LIGHT_FUNCTION(IsSellerAbAid);
    REGISTER_LIGHT_FUNCTION(GenIsAdSocialPerformanceOptPhotoAttr);
    REGISTER_LIGHT_FUNCTION(GenAuthorTailAttr);
    REGISTER_LIGHT_FUNCTION(GenIsFirstPageAttr);
    REGISTER_LIGHT_FUNCTION(GenMaxPictureRealshowAttr);
    REGISTER_LIGHT_FUNCTION(GenLiveCateAttr);
    REGISTER_LIGHT_FUNCTION(GenHasMerchantLivingPhotoInfoPtrAttr);
    REGISTER_LIGHT_FUNCTION(GenMaxUploadDayAttr);
    REGISTER_LIGHT_FUNCTION(GenPbHasAttr);
    REGISTER_LIGHT_FUNCTION(EnrichPhotoInfoAttrsForRetrievalEpCenter);
    REGISTER_LIGHT_FUNCTION(EnrichMerchantPhotoInfoAttrsForRetrievalEpCenter);
    REGISTER_LIGHT_FUNCTION(EnrichMerchantLivingPhotoInfoAttrsForRetrievalEpCenter);
    REGISTER_LIGHT_FUNCTION(EnrichMerchantPhotoInfoAttrsForRetrievalPerf);
    REGISTER_LIGHT_FUNCTION(AdSellerMap);
    REGISTER_LIGHT_FUNCTION(FilterByIntSet);
    REGISTER_LIGHT_FUNCTION(FilterColdStartSimilarAuthor);
    REGISTER_LIGHT_FUNCTION(FilterListByDoubleVal);
    REGISTER_LIGHT_FUNCTION(FilterLivinJewelleryAuthor);
    REGISTER_LIGHT_FUNCTION(GenColdStartA2aAuthorIds);
    REGISTER_LIGHT_FUNCTION(GetCategoryPhotoFromMemoryData);
    REGISTER_LIGHT_FUNCTION(GetCategoryPhotoFromMemoryDataV2);
    REGISTER_LIGHT_FUNCTION(GetCategoryPhotoFromMemoryDataV3);
    REGISTER_LIGHT_FUNCTION(GetDataMiningAuthorsFromGlobalData);
    REGISTER_LIGHT_FUNCTION(GetLiveNewitemAids);
    REGISTER_LIGHT_FUNCTION(GetMerchantNearest1hAdsAuthorIds);
    REGISTER_LIGHT_FUNCTION(GetPersonalAuthorsFromGlobalData);
    REGISTER_LIGHT_FUNCTION(GetStringFromMemoryData);
    REGISTER_LIGHT_FUNCTION(MerchantCartPhotosCfResultTriggers);
    REGISTER_LIGHT_FUNCTION(MerchantCartPhotosSwingSampleList);
    REGISTER_LIGHT_FUNCTION(PickColdStartSimilarAuthor);
    REGISTER_LIGHT_FUNCTION(ShuffleAndTrunc);
    REGISTER_LIGHT_FUNCTION(ShuffleAndTruncSet);
    REGISTER_LIGHT_FUNCTION(EnrichIsRefreshRequest);
    REGISTER_LIGHT_FUNCTION(GetAuthorInLivingRetr);
    REGISTER_LIGHT_FUNCTION(SetIsStressTestFlow);
    REGISTER_LIGHT_FUNCTION(GetUnifiedVideoTypeRetr);
    REGISTER_LIGHT_FUNCTION(GetValueFromDpDetail);
    REGISTER_LIGHT_FUNCTION(IsMagicFreqControl);
  }

  static bool GetValueFromDpDetail(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    // dp_detail="kwai://search?creativeId=280390627&impressionid=TENCENT_AD_IMP_ID"
    absl::optional<absl::string_view> attr = context.GetStringCommonAttr("dp_detail");
    if (!attr.has_value()) {
      return true;
    }
    absl::string_view dp_detail = attr.value();

    auto list_attr = context.GetStringListCommonAttr("dpDetailKeys");
    if (!list_attr.has_value()) {
      return true;
    }
    std::vector<absl::string_view> dpDetailKeys = list_attr.value();

    auto result = dp_detail.find("?");
    if (result == absl::string_view::npos) {
      return true;
    }
    absl::string_view kv_part = dp_detail.substr(result + 1);

    std::vector<absl::string_view> elems = absl::StrSplit(kv_part, "&", absl::SkipEmpty());
    for (auto &elem : elems) {
      std::vector<absl::string_view> kv = absl::StrSplit(elem, "=", absl::SkipEmpty());
      if (kv.size() == 2 &&
          std::find(dpDetailKeys.begin(), dpDetailKeys.end(), kv[0]) != std::end(dpDetailKeys)) {
        context.SetStringCommonAttr(kv[0], std::string(kv[1]));
      }
    }
    return true;
  }

  static bool IsPostAddCart(int32 scene_type, const std::set<int64> &cart_scene_type_set) {
    if (scene_type > 0 && cart_scene_type_set.count(scene_type)) {
      return true;
    }
    return false;
  }
  static bool IsSellerDayFirstPhotoRetr(const folly::F14FastSet<uint64> *merchant_seller_day_first_pid_set,
                                        bool is_merchant_cart, const int64 nebula_stats__real_show_count,
                                        const int64 thanos_stats__real_show_count, const int64 upload_time,
                                        const int64 realshow_thresh, int current_time_ms, const uint64 pid,
                                        int timeout = 93600000) {
    if (!is_merchant_cart) {
      return false;
    }
    uint64 all_show_count = 0;
    all_show_count += nebula_stats__real_show_count;
    all_show_count += thanos_stats__real_show_count;
    if (all_show_count < realshow_thresh && (current_time_ms - upload_time) < timeout) {
      if (merchant_seller_day_first_pid_set && merchant_seller_day_first_pid_set->size() > 0 &&
          merchant_seller_day_first_pid_set->count(pid)) {
        return true;
      }
    }
    return false;
  }

  static bool GetUnifiedVideoTypeRetr(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto scene_type_vec = context.GetIntListCommonAttr("post_add_cart_scene_types");
    std::set<int64> cart_scene_type_set(scene_type_vec->begin(), scene_type_vec->end());
    auto k_plc_rel_filter_thres = context.GetDoubleCommonAttr("k_plc_rel_filter_thres").value_or(0.0);
    bool newcart_exempt_plc_rel_score =
        context.GetIntCommonAttr("enable_merchant_seller_day_first_pid_exempt_plc_rel_score").value_or(0);
    int merchant_seller_day_first_pid_realshow_thresh =
        context.GetIntCommonAttr("merchant_seller_day_first_pid_realshow_thresh").value_or(0);
    auto enable_merchant_cart_live_shield_plc_retr =
        context.GetIntCommonAttr("enable_merchant_cart_live_shield_plc_retr").value_or(0);

    // 对于后置挂车，是否执行过滤
    auto disable_filter_post_add_cart =
        context.GetIntCommonAttr("disable_merchant_pip_filter_post_add_cart_photo").value_or(0);

    auto merchant_seller_day_first_pid_set =
        context.GetPtrCommonAttr<folly::F14FastSet<uint64>>("merchant_seller_day_first_pid_set__memory_data");

    auto is_merchant_cart_accessor = context.SetIntItemAttr("is_merchant_cart");
    auto is_merchant_living_accessor = context.SetIntItemAttr("is_merchant_living");
    auto is_merchant_item_accessor = context.SetIntItemAttr("is_merchant_item");

    auto author_in_living_accessor = context.GetIntItemAttr("author_in_living");
    auto nebula_stats__real_show_count = context.GetIntItemAttr("nebula_stats__real_show_count");
    auto thanos_stats__real_show_count = context.GetIntItemAttr("thanos_stats__real_show_count");
    auto merchant_item_info__item_id = context.GetIntItemAttr("merchant_item_info__item_id");
    auto merchant_item_info__scene_type = context.GetIntItemAttr("merchant_item_info__scene_type");
    auto upload_time = context.GetIntItemAttr("upload_time");
    auto photo_id = context.GetIntItemAttr("photo_id");
    auto merchant_photo_cart_relation = context.GetIntItemAttr("merchant_photo_cart_relation");

    int64 current_time_ms = base::GetTimestamp() / base::Time::kMicrosecondsPerMillisecond;

    for (auto iter = begin; iter != end; ++iter) {
      const CommonRecoResult &result = *iter;
      // Fill is_merchant_cart
      bool is_merchant_cart = false;
      if (merchant_item_info__item_id(result)) {
        is_merchant_cart = true;
        if (merchant_photo_cart_relation(result) &&
            merchant_photo_cart_relation(result).value_or(-1) < k_plc_rel_filter_thres) {  // plc filter
          is_merchant_cart = false;
          if (newcart_exempt_plc_rel_score &&
              IsSellerDayFirstPhotoRetr(
                  merchant_seller_day_first_pid_set, true, nebula_stats__real_show_count(result).value_or(0),
                  thanos_stats__real_show_count(result).value_or(0), upload_time(result).value_or(-1),
                  merchant_seller_day_first_pid_realshow_thresh, current_time_ms,
                  photo_id(result).value_or(-1))) {
            is_merchant_cart = true;
          }
        }
      }

      if (!disable_filter_post_add_cart &&
          IsPostAddCart(merchant_item_info__scene_type(result).value_or(-1),
                        cart_scene_type_set)) {  // 后置挂车类型，方便挂车促生产实验
        is_merchant_cart = false;
      }
      // Fill is merchant living
      auto author_id = author_in_living_accessor(result).value_or(0);
      bool is_merchant_live = (0 != author_id) ? true : false;
      is_merchant_cart_accessor(result, (is_merchant_cart ? 1 : 0));
      is_merchant_living_accessor(result, (is_merchant_live ? 1 : 0));
      // 配合精排个性化前端展示探索实验
      if (enable_merchant_cart_live_shield_plc_retr > 0) {
        is_merchant_living_accessor(result, 0);
      }
      is_merchant_item_accessor(result, merchant_item_info__item_id(result).value_or(-1) > 0);
    }
    return true;
  }

  // (zhongchunmeng) 压测流量召回动态降级调整
  static bool SetIsStressTestFlow(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    context.SetIntCommonAttr("is_stress_test_flow", ks::infra::kenv::IsStressTestFlow() ? 1 : 0);
    return true;
  }

  static bool GetAuthorInLivingRetr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto merchant_live_authors_set =
        context.GetPtrCommonAttr<folly::F14FastSet<uint64>>("merchant_live_authors_set__memory_data");
    if (!merchant_live_authors_set) {
      LOG(WARNING) << "Memory data is empty merchant_live_authors_set";
      return false;
    }

    auto author_id_getter = context.GetIntItemAttr("author__id");
    auto living_author_id_accessor = context.SetIntItemAttr("author_in_living");
    auto sign_living_author_id_accessor = context.SetIntItemAttr("sign_author_in_living");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto author_id = author_id_getter(result).value_or(-1);
      if (merchant_live_authors_set->count(author_id)) {  // 正在直播的作者
        living_author_id_accessor(result, author_id);
        auto sign_living_author_id = ((uint64)(1) << 56) | author_id;
        sign_living_author_id_accessor(result, sign_living_author_id);
      }
    });
    return true;
  }

  static bool EnrichIsRefreshRequest(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    int is_refresh_request = 0;

    // 获取用户的请求行为
    int user_refresh_type = 0;
    const auto *user_info_ptr = context.GetProtoMessagePtrCommonAttr<ks::reco::UserInfo>("user_info_ptr");
    if (!user_info_ptr) {
      CL_LOG_EVERY_N(WARNING, 100) << "BuildSimpleUserInfo could not find user_info_ptr";
      return false;
    }
    if (user_info_ptr->has_req_type()) {
      user_refresh_type =
          user_info_ptr->req_type().has_refresh_type() ? user_info_ptr->req_type().refresh_type() : 0;
    }

    // 解析刷新均值的两个区间参数
    std::string user_refresh_7d_ratio_threshold_2_intervals_str = std::string(
        context.GetStringCommonAttr("user_refresh_7d_ratio_threshold_2_intervals_str").value_or(""));

    double user_7d_refursh_ratio_parameter1_left = 0.0;
    double user_7d_refursh_ratio_parameter1_right = 0.0;
    double user_7d_refursh_ratio_parameter2_left = 0.0;
    double user_7d_refursh_ratio_parameter2_right = 0.0;

    std::vector<double> param_vec;
    ks::reco::SimpleStringToDoubleVec(user_refresh_7d_ratio_threshold_2_intervals_str, &param_vec);
    if (param_vec.size() >= 4) {
      user_7d_refursh_ratio_parameter1_left = param_vec[0];
      user_7d_refursh_ratio_parameter1_right = param_vec[1];
      user_7d_refursh_ratio_parameter2_left = param_vec[2];
      user_7d_refursh_ratio_parameter2_right = param_vec[3];
    }

    double user_7d_refursh_ratio = 0;
    std::string user_fresh_action_num_str =
        std::string(context.GetStringCommonAttr("user_fresh_action_num_str").value_or(""));
    std::vector<std::string> user_refresh_stat_info_list;
    base::SplitStringWithOptions(user_fresh_action_num_str, "|", true, true, &user_refresh_stat_info_list);

    if (user_refresh_stat_info_list.size() > 0) {
      if (!absl::SimpleAtod(user_refresh_stat_info_list[0], &user_7d_refursh_ratio)) {
        user_7d_refursh_ratio = 0;
      }
    }

    if (user_refresh_type != 0 && ((user_7d_refursh_ratio >= user_7d_refursh_ratio_parameter1_left &&
                                    user_7d_refursh_ratio <= user_7d_refursh_ratio_parameter1_right) ||
                                   (user_7d_refursh_ratio >= user_7d_refursh_ratio_parameter2_left &&
                                    user_7d_refursh_ratio <= user_7d_refursh_ratio_parameter2_right))) {
      is_refresh_request = 1;
    }

    context.SetIntCommonAttr("is_real_refresh_request", is_refresh_request);

    LOG_EVERY_N(INFO, 10000)
        << "EnrichIsRefreshRequest"
        << ", uid:" << user_info_ptr->device_id()
        << ", user_fresh_action_num_str:" << user_fresh_action_num_str
        << ", user_7d_refursh_ratio:" << user_7d_refursh_ratio
        << ", user_7d_refursh_ratio_parameter1_left:" << user_7d_refursh_ratio_parameter1_left
        << ", user_7d_refursh_ratio_parameter1_right:" << user_7d_refursh_ratio_parameter1_right
        << ", user_7d_refursh_ratio_parameter2_left:" << user_7d_refursh_ratio_parameter2_left
        << ", user_7d_refursh_ratio_parameter2_right:" << user_7d_refursh_ratio_parameter2_right
        << ", is_refresh_request:" << is_refresh_request;
    return true;
  }

  static bool ShuffleAndTruncSet(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto src_set = context.GetPtrCommonAttr<folly::F14FastSet<uint64>>("src_set");
    auto p_trunc_num = context.GetIntCommonAttr("trunc_num");
    if (nullptr == src_set || src_set->empty()) {
      return true;
    }
    int trunc_num = -1;
    if (p_trunc_num) trunc_num = *p_trunc_num;
    std::vector<int64> rst_vec(src_set->size());
    for (auto id : *src_set) {
      rst_vec.push_back(id);
    }
    if (trunc_num >= 0 && rst_vec.size() > trunc_num) {
      std::random_device rng;
      std::mt19937 urng(rng());
      std::shuffle(rst_vec.begin(), rst_vec.end(), urng);
      rst_vec.resize(trunc_num);
    }
    context.SetIntListCommonAttr("result_list", std::move(rst_vec));
    return true;
  }

  static bool ShuffleAndTrunc(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                              RecoResultConstIter end) {
    auto src_list = context.GetIntListCommonAttr("src_list");
    auto p_trunc_num = context.GetIntCommonAttr("trunc_num");
    if (!src_list || src_list->empty()) {
      return true;
    }
    int trunc_num = -1;
    if (p_trunc_num) trunc_num = *p_trunc_num;
    std::vector<int64> rst_vec(src_list->size());
    for (auto id : *src_list) {
      rst_vec.push_back(id);
    }
    if (trunc_num >= 0 && rst_vec.size() > trunc_num) {
      std::random_device rng;
      std::mt19937 urng(rng());
      std::shuffle(rst_vec.begin(), rst_vec.end(), urng);
      rst_vec.resize(trunc_num);
    }
    context.SetIntListCommonAttr("result_list", std::move(rst_vec));
    return true;
  }

  static bool PickColdStartSimilarAuthor(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    auto target_list = context.GetIntListCommonAttr("s_cs_a2a_trigger_aids");
    auto coldstart_similar_author_map =
        context.GetPtrCommonAttr<folly::F14FastMap<int32, std::vector<int32>>>(
            "merchant_coldstart_similar_author__memory_data");
    if (!target_list || !coldstart_similar_author_map) {
      return true;
    }

    std::set<int64> rst_set;
    std::vector<int64> rst_vec;
#define TRANSFER(aid) static_cast<int32>(aid - 0x80000000)
#define RECOVER(aid) static_cast<uint64>(aid + 0x80000000)
    for (auto aid : *target_list) {
      auto it = coldstart_similar_author_map->find(TRANSFER(aid));
      if (coldstart_similar_author_map->end() == it) {
        continue;
      }
      for (auto id : it->second) {
        int64 r_id = RECOVER(id);
        if (rst_set.end() != rst_set.find(r_id)) {
          continue;
        }
        rst_set.insert(r_id);
      }
    }
#undef TRANSFER
#undef RECOVER
    rst_vec.assign(rst_set.begin(), rst_set.end());
    context.SetIntListCommonAttr("trigger_list", std::move(rst_vec));
    return true;
  }

  static bool MerchantCartPhotosSwingSampleList(const CommonRecoLightFunctionContext &context,
                                                RecoResultConstIter begin, RecoResultConstIter end) {
    std::vector<std::string> key_vec;
    key_vec.push_back("uEshopPhotoClickPhotoIdList");
    key_vec.push_back("uLiveHeadClickPhotoIdList");
    key_vec.push_back("uLongPlayList");
    key_vec.push_back("uLikeList");
    key_vec.push_back("uCommentPhotoList");
    key_vec.push_back("uForwardList");
    key_vec.push_back("uEintPhotoIdList");
    key_vec.push_back("uClickList");

    auto each_category_num = context.GetIntCommonAttr("per_category_num");
    auto max_result_count = context.GetIntCommonAttr("max_enrich_user_keys");
    if (!each_category_num || !max_result_count) {
      return true;
    }

    std::set<int64> rst_set;
    std::vector<int64> rst_vec;
    for (const auto &key : key_vec) {
      auto author_id_list = context.GetIntListCommonAttr(key);
      if (!author_id_list || author_id_list->empty()) {
        continue;
      }

      int64 cur_count = 0;
      for (auto author_id : *author_id_list) {
        if (rst_set.count(author_id)) {
          continue;
        }
        ++cur_count;
        if (cur_count > *each_category_num) {
          break;
        }
        rst_set.insert(author_id);
      }
    }

    rst_vec.assign(rst_set.begin(), rst_set.end());
    if (rst_vec.size() > *max_result_count) {
      rst_vec.resize(*max_result_count);
    }

    context.SetIntListCommonAttr("trigger_ids", std::move(rst_vec));

    return true;
  }

  static bool MerchantCartPhotosCfResultTriggers(const CommonRecoLightFunctionContext &context,
                                                 RecoResultConstIter begin, RecoResultConstIter end) {
    std::vector<std::string> key_vec;
    key_vec.push_back("uPhotoClickCartPhotoIdList");
    key_vec.push_back("uPhotoClickCardPhotoIdList");
    key_vec.push_back("uPhotoBuyItemSellerIdList");
    key_vec.push_back("uMerchantBuyItemSellerIdList");
    key_vec.push_back("uEshopBuyItemSellerIdList");
    key_vec.push_back("uMerchantBuyerStatList");
    key_vec.push_back("uEshopClickSearchAuthorIdList");
    key_vec.push_back("uMerchantLongPlayLiveAuthorList");

    auto each_category_num = context.GetIntCommonAttr("per_category_num");
    auto max_result_count = context.GetIntCommonAttr("max_enrich_user_keys");
    if (!each_category_num || !max_result_count) {
      return true;
    }

    std::set<int64> rst_set;
    std::vector<int64> rst_vec;
    for (const auto &key : key_vec) {
      auto author_id_list = context.GetIntListCommonAttr(key);
      if (!author_id_list || author_id_list->empty()) {
        continue;
      }

      int64 cur_count = 0;
      for (auto author_id : *author_id_list) {
        if (rst_set.count(author_id)) {
          continue;
        }
        ++cur_count;
        if (cur_count > *each_category_num) {
          break;
        }
        rst_set.insert(author_id);
      }
    }

    rst_vec.assign(rst_set.begin(), rst_set.end());
    if (rst_vec.size() > *max_result_count) {
      rst_vec.resize(*max_result_count);
    }

    context.SetIntListCommonAttr("trigger_ids", std::move(rst_vec));

    return true;
  }

  static bool GetStringFromMemoryData(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto liveid_aid_map =
        context.GetPtrCommonAttr<folly::F14FastMap<std::string, std::string>>("liveid_aid_map");
    if (!liveid_aid_map) {
      CL_LOG(WARNING) << "Memory data is empty liveid_aid_map";
      return true;
    }

    auto liveid_vec = context.GetIntListCommonAttr("liveid_vec");
    if (!liveid_vec) {
      CL_LOG(WARNING) << "liveid_vec is empty";
      return true;
    }

    std::vector<int64> author_vec;
    for (auto liveid : *liveid_vec) {
      std::string liveid_str = base::Uint64ToString(static_cast<int64>(liveid));
      auto it = liveid_aid_map->find(liveid_str);
      if (it != liveid_aid_map->end()) {
        uint64 aid;
        base::StringToUint64(it->second, &aid);
        author_vec.push_back(static_cast<int64>(aid));
      }
    }
    context.SetIntListCommonAttr("author_vec", std::move(author_vec));
    return true;
  }

  // 从 DataMinigGlobalDataUnionRule 的新代码迁移而来
  static bool GetPersonalAuthorsFromGlobalData(const CommonRecoLightFunctionContext &context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
    folly::F14FastMap<uint64, double> coldstart_aids_mid;
    const folly::F14FastMap<uint64, double> *author_score_map(nullptr);
    auto index_author_map =
        context.GetPtrCommonAttr<folly::F14FastMap<std::string, std::vector<uint64>>>("author_score_map_ptr");
    if (index_author_map && index_author_map->size() > 0) {
      int index = 0;
      int ub_value = 0;
      auto u_type = context.GetStringCommonAttr("uBuyerEffectiveType");
      if (u_type) {
        std::string ut_str = std::string(*u_type);
        if (ut_str.find("U0-sleep") != std::string::npos) {
          ub_value = 1;
        } else if (ut_str.find("U1") != std::string::npos) {
          ub_value = 2;
        } else if (ut_str.find("U2") != std::string::npos) {
          ub_value = 3;
        } else if (ut_str.find("U3") != std::string::npos) {
          ub_value = 4;
        } else if (ut_str.find("U4") != std::string::npos) {
          ub_value = 5;
        }
      }
      index += ub_value;
      // if (user_info.has_device_basic_info() && user_info.device_basic_info().has_price()) {
      auto mod_price = context.GetIntCommonAttr("price");
      if (mod_price) {
        if (*mod_price < 2000) {
          ub_value = 10;
        } else if (*mod_price >= 2000 && *mod_price < 5000) {
          ub_value = 20;
        } else {
          ub_value = 30;
        }
      } else {
        ub_value = 90;
      }
      index += ub_value;
      auto user_age_seg = context.GetIntCommonAttr("age");  // basic_info.age_segment
      if (user_age_seg) {
        if (*user_age_seg <= 3) {  // if (*user_age_seg == reco::UserBaiscInfo_AgeSegment_AGE_0_12
          ub_value = 100;
        } else if (*user_age_seg == 4 || *user_age_seg == 5) {
          ub_value = 200;
        } else {
          ub_value = 300;
        }
      } else {
        ub_value = 900;
      }
      index += ub_value;
      auto gender = context.GetIntCommonAttr("gender");
      if (gender) {
        if (*gender == 0) {  // M   == reco::UserBaiscInfo_Gender_MALE) {
          ub_value = 1000;
        } else if (*gender == 1) {
          ub_value = 2000;
        } else {
          ub_value = 9000;
        }
      } else {
        ub_value = 9000;
      }
      index += ub_value;
      auto is_nebula_user = context.GetIntCommonAttr("is_nebula_user");
      auto is_gamora = context.GetIntCommonAttr("is_gamora_user");
      if (is_nebula_user && *is_nebula_user == 1) {
        ub_value = 10000;
      } else if (is_gamora && *is_gamora == 1) {
        ub_value = 20000;
      } else {
        ub_value = 90000;
      }
      index += ub_value;
      auto target_authors_itr = index_author_map->find(std::to_string(index));
      if (target_authors_itr != index_author_map->end()) {
        auto vvec = target_authors_itr->second;
        coldstart_aids_mid.clear();
        for (int i = 0; i < vvec.size(); ++i) {
          coldstart_aids_mid[vvec[i]] = vvec.size() - i;
        }
      }
      author_score_map = &coldstart_aids_mid;
      LOG_EVERY_N(INFO, 1000) << "[debug::lm] up sub key author_score_map size:" << author_score_map->size()
                              << "; mem data size: " << index_author_map->size() << "; index:" << index
                              << "; u_type:" << (u_type ? *u_type : "")
                              << "; mod_price:" << (mod_price ? *mod_price : -1)
                              << "; user_age_seg=" << (user_age_seg ? *user_age_seg : -1)
                              << "; gender=" << (gender ? *gender : -1)
                              << "; is_nebula_user=" << (is_nebula_user ? *is_nebula_user : -1)
                              << "; is_gamora=" << (is_gamora ? *is_gamora : -1);
    } else {
      LOG_EVERY_N(INFO, 1000) << "[debug::lm] up sub key is null, try use all living author data";
    }

    bool enable_filter = false;
    if (author_score_map && author_score_map->size() > 0) {
      enable_filter = true;
    }

    int filter_flag = 0;
    auto filter_flag_ptr = context.GetIntCommonAttr("merchant_coldstart_filter_flag");
    if (filter_flag_ptr) {
      filter_flag = *filter_flag_ptr;
    }
    std::vector<uint64> click_author_list;  // candidate 的类型不是 str
    std::vector<uint64> like_author_list;
    std::vector<uint64> follow_author_list;
    if (filter_flag > 0) {  // 并行过滤逻辑: 与 enable_filter: 基于 mem data 的过滤解耦
      auto click_list = context.GetIntListCommonAttr("click_list");
      if (click_list) {
        for (auto id : *click_list) {
          click_author_list.emplace_back(id);
        }
      }
      auto click_live_list = context.GetIntListCommonAttr("click_live_list");
      if (click_live_list) {
        for (auto id : *click_live_list) {
          click_author_list.emplace_back(id);
        }
      }
      auto like_list = context.GetIntListCommonAttr("like_list");
      if (like_list) {
        for (auto id : *like_list) {
          like_author_list.emplace_back(id);
        }
      }
      auto follow_list = context.GetIntListCommonAttr("follow_list");
      if (follow_list) {
        for (auto id : *follow_list) {
          follow_author_list.emplace_back(id);
        }
      }
      LOG_EVERY_N(INFO, 5000) << "[debug::lm] user action size click_list:"
                              << (click_list ? click_list->size() : -1)
                              << "; click_live_list=" << (click_live_list ? click_live_list->size() : -1)
                              << "; like_list=" << (like_list ? like_list->size() : -1)
                              << "; follow_list=" << (follow_list ? follow_list->size() : -1);
    }

    auto user_aid_list = context.GetDoubleListCommonAttr("user_aid_list");
    folly::F14FastMap<uint64, double> user_candidates;
    if (user_aid_list && user_aid_list->size() > 0) {  // 切更个性化的 author
      double score = user_aid_list->size();
      int i = 0;
      for (auto aid : *user_aid_list) {
        user_candidates[(uint64)aid] = score - i;
        i += 1;
      }
      author_score_map = &user_candidates;
      enable_filter = true;
    }

    auto author_candidate_set =
        context.GetPtrCommonAttr<folly::F14FastSet<uint64>>("author_candidate_set_ptr");
    int32 author_id_candidate_max_num = context.GetIntCommonAttr("author_id_candidate_max_num").value_or(0);
    std::vector<std::pair<uint64, double>> author_candidates;
    if (author_candidate_set) {
      if (enable_filter && author_score_map && author_score_map->size() > 0) {
        double max_score = 0.0;
        for (auto it = author_candidate_set->begin(); it != author_candidate_set->end(); it++) {
          auto author_score_map_it = author_score_map->find(*it);
          if (author_score_map_it != author_score_map->end()) {
            author_candidates.push_back(std::make_pair(*it, author_score_map_it->second));
            if (author_score_map_it->second > max_score) {
              max_score = author_score_map_it->second;
            }
          }
        }
      } else {
        for (auto it = author_candidate_set->begin(); it != author_candidate_set->end(); it++) {
          author_candidates.push_back(std::make_pair(*it, 0));
        }
      }
    }

    LOG_EVERY_N(INFO, 1000) << "[debug::lm] enable_filter=" << enable_filter << "all author_candidate_set:"
                            << (author_candidate_set ? author_candidate_set->size() : -1)
                            << "; filter_flag=" << filter_flag
                            << "; [filtered] author_candidates sz=" << author_candidates.size() << "; max num"
                            << author_id_candidate_max_num;
    if (author_candidates.size() > author_id_candidate_max_num) {
      // if (enable_filter) {
      if (filter_flag > 0) {  // 对用户关注的商家加权
        // 降序取 top-k
        std::sort(author_candidates.begin(), author_candidates.end(),
                  [](const std::pair<uint64, double> &a, const std::pair<uint64, double> &b) -> bool {
                    return a.second > b.second;
                  });

        std::set<uint64> user_aid_set;
        // skip_exptag_set.insert(exptag);
        if ((filter_flag & 0x1) == 1) {
          user_aid_set.insert(like_author_list.begin(), like_author_list.end());
        }
        if (((filter_flag >> 1) & 0x1) == 1) {
          user_aid_set.insert(follow_author_list.begin(), follow_author_list.end());
        }
        if (((filter_flag >> 2) & 0x1) == 1) {
          user_aid_set.insert(click_author_list.begin(), click_author_list.end());
        }
        std::sort(
            author_candidates.begin() + 1, author_candidates.end(),
            [user_aid_set](const std::pair<uint64, double> &a, const std::pair<uint64, double> &b) -> bool {
              double a_score = a.second + (user_aid_set.count(a.first) > 0 ? 1e6 : 0.0);
              double b_score = b.second + (user_aid_set.count(b.first) > 0 ? 1e6 : 0.0);
              return a_score > b_score;
            });
        author_candidates.resize(author_id_candidate_max_num);
        LOG_EVERY_N(INFO, 1000) << "[debug::lm] filter user_aid_set size: " << user_aid_set.size()
                                << ", like aids:" << like_author_list.size()
                                << ", follow aids:" << follow_author_list.size()
                                << ", click ads:" << click_author_list.size()
                                << ", filter_flag:" << filter_flag;
      } else {
        std::random_device rng;
        std::mt19937 urng(rng());
        std::shuffle(author_candidates.begin(), author_candidates.end(), urng);
        author_candidates.resize(author_id_candidate_max_num);
      }
    }
    if (author_score_map && author_score_map->size() > 0) {
      // 补充额外的 top1
      std::vector<std::pair<uint64, double>> score_vec{author_score_map->begin(), author_score_map->end()};
      std::sort(score_vec.begin(), score_vec.end(),
                [](const std::pair<uint64, double> &a, const std::pair<uint64, double> &b) -> bool {
                  return a.second > b.second;
                });
      // auto i_aid = author_candidate_set->find(score_vec[0].first);
      bool has_aid = false;
      for (auto iter : author_candidates) {
        if (iter.first == score_vec[0].first) {
          has_aid = true;
          break;
        }
      }
      if (!has_aid) {
        author_candidates.push_back(std::make_pair(score_vec[0].first, score_vec[0].second));
      }
    }
    std::vector<int64> author_ids;
    author_ids.reserve(author_candidates.size());
    for (auto it = author_candidates.begin(); it != author_candidates.end(); it++) {
      author_ids.push_back(it->first);
    }
    context.SetIntListCommonAttr("author_id_candidates", std::move(author_ids));
    return true;
  }

  static bool GetMerchantNearest1hAdsAuthorIds(const CommonRecoLightFunctionContext &context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
    std::vector<std::string> key_vec;
    std::vector<std::string> tms_vec;
    key_vec.push_back("uStandardMerchantAdClickLiveAllAuthorIdList");
    key_vec.push_back("uStandardMerchantAdClickPhotoAllAuthorIdList");
    key_vec.push_back("uStandardMerchantAdClickPhoto2liveAllAuthorIdList");
    tms_vec.push_back("uStandardMerchantAdClickLiveAllTimeList");
    tms_vec.push_back("uStandardMerchantAdClickPhotoAllTimeList");
    tms_vec.push_back("uStandardMerchantAdClickPhoto2liveAllTimeList");
    auto p_time_interval_s = context.GetIntCommonAttr("time_interval_s");
    int time_interval_s = 3600;
    if (p_time_interval_s && *p_time_interval_s > 0) time_interval_s = *p_time_interval_s;

    std::set<int64> rst_set;
    std::vector<int64> rst_vec;
    uint64 time_now = base::GetTimestamp() / 1000;
    int max_count = 200;
    for (int i = 0; i < key_vec.size(); i++) {
      auto aid_list = context.GetIntListCommonAttr(key_vec[i]);
      auto tms_list = context.GetIntListCommonAttr(tms_vec[i]);
      if (!aid_list || !tms_list || aid_list->size() != tms_list->size() || aid_list->empty()) {
        continue;
      }
      for (int j = 0; j < aid_list->size(); j++) {
        auto aid = (*aid_list)[j];
        auto times = (*tms_list)[j];
        int t_diff = (time_now - times) / 1000;
        if (t_diff >= 0 && t_diff <= time_interval_s) {
          rst_set.insert(aid);
        }
        if (rst_set.size() >= max_count) break;
      }
      if (rst_set.size() >= max_count) break;
    }
    rst_vec.reserve(rst_set.size());
    for (auto aid : rst_set) rst_vec.push_back(aid);
    CL_LOG(INFO) << "1h ads aids based on user action size: " << rst_vec.size();
    context.SetIntListCommonAttr("merchant_nearest_1h_ads_aids_list", std::move(rst_vec));
    return true;
  }

  static bool GetLiveNewitemAids(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    int64 bonus_type = context.GetIntCommonAttr("bonus_type").value_or(0);
    auto user_bonus_str =
        context.GetPtrCommonAttr<std::string>("biglive_newitem_boost_seller_map__memory_data_ptr");
    std::vector<int64> rst_vec;
    if (!user_bonus_str || user_bonus_str->length() == 0) {
      CL_LOG_EVERY_N(ERROR, 10 / 10) << "str sz=" << (user_bonus_str ? user_bonus_str->length() : -1)
                                     << ",type=" << bonus_type;
      context.SetIntListCommonAttr("result_list", std::move(rst_vec));
      return true;
    }
    folly::F14FastSet<uint64> bonus_aids;
    std::vector<absl::string_view> user_bonus_arr = absl::StrSplit(*user_bonus_str, "|", absl::SkipEmpty());
    for (auto iter = user_bonus_arr.begin(); iter != user_bonus_arr.end(); ++iter) {
      std::vector<absl::string_view> pair = absl::StrSplit(*iter, ";", absl::SkipEmpty());
      if (pair.size() < 6) {  // liveid;aid;isplat;platwei;isshop;shopwei
        continue;
      }
      uint64 aid = 0;
      double bonus = 0;
      int flags = 0;

      if (absl::SimpleAtoi(pair[1], &aid) && aid > 0) {
        if (bonus_type == 0) {
          if (absl::SimpleAtoi(pair[2], &flags) && flags > 0) {
            if (absl::SimpleAtod(pair[3], &bonus)) {
              bonus_aids.insert(aid);
            }
          }
        } else if (bonus_type == 1) {
          if (absl::SimpleAtoi(pair[4], &flags) && flags > 0) {
            if (absl::SimpleAtod(pair[5], &bonus)) {
              bonus_aids.insert(aid);
            }
          }
        } else if (bonus_type == 2) {  // 二选一
          if (absl::SimpleAtoi(pair[2], &flags) && flags > 0) {
            if (absl::SimpleAtod(pair[3], &bonus)) {
              bonus_aids.insert(aid);
            }
          } else if (absl::SimpleAtoi(pair[4], &flags) && flags > 0) {
            if (absl::SimpleAtod(pair[5], &bonus)) {
              bonus_aids.insert(aid);
            }
          }
        }
      }
    }
    CL_LOG_EVERY_N(INFO, 5000) << "str sz=" << user_bonus_str->length() << ",type=" << bonus_type
                               << ",bouns=" << bonus_aids.size();
    std::vector<int64> rst_res(bonus_aids.begin(), bonus_aids.end());
    context.SetIntListCommonAttr("result_list", std::move(rst_res));
    return true;
  }

  // 从 DataMinigGlobalDataUnionRule 迁移而来
  static bool GetDataMiningAuthorsFromGlobalData(const CommonRecoLightFunctionContext &context,
                                                 RecoResultConstIter begin, RecoResultConstIter end) {
    auto author_score_map =
        context.GetPtrCommonAttr<folly::F14FastMap<uint64, double>>("author_score_map_ptr");

    bool enable_filter = false;
    if (author_score_map && author_score_map->size() > 0) {
      enable_filter = true;
    }

    auto author_candidate_set =
        context.GetPtrCommonAttr<folly::F14FastSet<uint64>>("author_candidate_set_ptr");
    int32 author_id_candidate_max_num = context.GetIntCommonAttr("author_id_candidate_max_num").value_or(0);

    std::vector<std::pair<uint64, double>> author_candidates;
    if (author_candidate_set) {
      for (auto it = author_candidate_set->begin(); it != author_candidate_set->end(); it++) {
        if (enable_filter) {
          auto author_score_map_it = author_score_map->find(*it);
          if (author_score_map_it != author_score_map->end()) {
            author_candidates.push_back(std::make_pair(*it, author_score_map_it->second));
          }
        } else {
          author_candidates.push_back(std::make_pair(*it, 0));
        }
      }
    }
    if (author_candidates.size() > author_id_candidate_max_num) {
      if (enable_filter) {
        // 降序取 top-k
        std::sort(author_candidates.begin(), author_candidates.end(),
                  [](const std::pair<uint64, double> &a, const std::pair<uint64, double> &b) -> bool {
                    return a.second > b.second;
                  });
      } else {
        std::random_device rng;
        std::mt19937 urng(rng());
        std::shuffle(author_candidates.begin(), author_candidates.end(), urng);
      }
      author_candidates.resize(author_id_candidate_max_num);
    }
    std::vector<int64> author_ids;
    author_ids.reserve(author_candidates.size());
    for (auto it = author_candidates.begin(); it != author_candidates.end(); it++) {
      author_ids.push_back(it->first);
    }
    context.SetIntListCommonAttr("author_id_candidates", std::move(author_ids));
    return true;
  }

  static bool GetCategoryPhotoFromMemoryData(const CommonRecoLightFunctionContext &context,
                                             RecoResultConstIter begin, RecoResultConstIter end) {
    auto max_cate_num = context.GetIntCommonAttr("max_cate_num").value_or(0);
    auto single_cate_cut_num = context.GetIntCommonAttr("single_cate_cut_num").value_or(0);
    auto user_cates_str = context.GetStringCommonAttr("user_category_list_str").value_or("");
    auto cate_to_photoids = context.GetPtrCommonAttr<folly::F14FastMap<uint64, std::vector<uint64>>>(
        "video_cart_coldstart_tag2photos");

    std::vector<int64> trigger_list;
    if (0 == max_cate_num || 0 == single_cate_cut_num || 0 == user_cates_str.size()) {
      context.SetIntListCommonAttr("trigger_list", std::move(trigger_list));
      CL_LOG(WARNING) << "invalid max_cate_num or single_cate_cut_num";
      return false;
    }

    if (!cate_to_photoids) {
      CL_LOG(WARNING) << "memory data cate to photoids is nullptr";
      return false;
    }

    std::vector<absl::string_view> user_cates_vec = absl::StrSplit(user_cates_str, ",", absl::SkipEmpty());
    std::vector<uint64> final_cate_vec;
    for (size_t i = 0; i < user_cates_vec.size(); ++i) {
      uint64 cate_id;
      if (!absl::SimpleAtoi(user_cates_vec[i], &cate_id)) {
        continue;
      }
      if (i < max_cate_num) {
        final_cate_vec.push_back(cate_id);
      }
    }

    for (auto cate_id : final_cate_vec) {
      auto it = cate_to_photoids->find(cate_id);
      if (cate_to_photoids->end() == it) {
        continue;
      }
      int cur_count = 0;
      const std::vector<uint64> &photoids = it->second;
      for (size_t i = 0; i < photoids.size(); ++i) {
        cur_count++;
        if (cur_count <= single_cate_cut_num) {
          trigger_list.push_back((int64)photoids[i]);
        }
      }
    }

    context.SetIntListCommonAttr("trigger_list", std::move(trigger_list));
    return true;
  }

  static bool GetCategoryPhotoFromMemoryDataV2(const CommonRecoLightFunctionContext &context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
    auto max_cate_num = context.GetIntCommonAttr("max_cate_num").value_or(0);
    auto single_cate_cut_num = context.GetIntCommonAttr("single_cate_cut_num").value_or(0);
    auto user_cates_ptr = context.GetIntListCommonAttr("user_category_list");
    auto cate_to_photoids =
        context.GetPtrCommonAttr<folly::F14FastMap<int32, std::vector<uint64>>>("video_cart_tag2photos");
    std::vector<int64> trigger_list;
    if (!cate_to_photoids || !user_cates_ptr) {
      context.SetIntListCommonAttr("trigger_list", std::move(trigger_list));
      CL_LOG(WARNING) << "[GetCategoryPhotoFromMemoryDataV2]memory data cate to photoids is nullptr";
      return false;
    }
    std::vector<int32> user_cates_vec(user_cates_ptr->begin(), user_cates_ptr->end());
    if (0 == max_cate_num || 0 == single_cate_cut_num || 0 == user_cates_vec.size()) {
      context.SetIntListCommonAttr("trigger_list", std::move(trigger_list));
      CL_LOG(WARNING) << "[GetCategoryPhotoFromMemoryDataV2]invalid max_cate_num or single_cate_cut_num";
      return false;
    }
    for (int i = 0; i < user_cates_vec.size(); i++) {
      if (i >= max_cate_num) {
        break;
      }
      auto it = cate_to_photoids->find(user_cates_vec[i]);
      if (cate_to_photoids->end() == it) {
        continue;
      }
      int cur_count = 0;
      const std::vector<uint64> &photoids = it->second;
      for (size_t i = 0; i < photoids.size(); ++i) {
        cur_count++;
        if (cur_count <= single_cate_cut_num) {
          trigger_list.push_back((int64)photoids[i]);
        }
      }
    }
    context.SetIntListCommonAttr("trigger_list", std::move(trigger_list));
    return true;
  }

  static bool GetCategoryPhotoFromMemoryDataV3(const CommonRecoLightFunctionContext &context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
    auto max_cate_num = context.GetIntCommonAttr("max_cate_num").value_or(0);
    auto single_cate_cut_num = context.GetIntCommonAttr("single_cate_cut_num").value_or(0);
    auto single_cate_shuffle = context.GetIntCommonAttr("single_cate_shuffle").value_or(0);
    auto user_cates_ptr = context.GetIntListCommonAttr("user_category_list");
    auto cate_to_photoids =
        context.GetPtrCommonAttr<folly::F14FastMap<int32, std::vector<uint64>>>("video_cart_tag2photos");
    std::vector<int64> trigger_list;
    if (!cate_to_photoids || !user_cates_ptr) {
      context.SetIntListCommonAttr("trigger_list", std::move(trigger_list));
      CL_LOG(WARNING) << "[GetCategoryPhotoFromMemoryDataV2]memory data cate to photoids is nullptr";
      return false;
    }
    std::vector<int32> user_cates_vec(user_cates_ptr->begin(), user_cates_ptr->end());
    if (0 == max_cate_num || 0 == single_cate_cut_num || 0 == user_cates_vec.size()) {
      context.SetIntListCommonAttr("trigger_list", std::move(trigger_list));
      CL_LOG(WARNING) << "[GetCategoryPhotoFromMemoryDataV2]invalid max_cate_num or single_cate_cut_num";
      return false;
    }
    folly::F14FastSet<int64> insert_pids;
    for (int i = 0; i < user_cates_vec.size(); i++) {
      if (i >= max_cate_num) {
        break;
      }
      auto it = cate_to_photoids->find(user_cates_vec[i]);
      if (cate_to_photoids->end() == it) {
        continue;
      }
      int cur_count = 0;
      std::vector<uint64> photoids(it->second.begin(), it->second.end());
      if (single_cate_shuffle > 0 && photoids.size() > 0) {
        std::random_device rd;
        std::mt19937 g(rd());
        std::shuffle(photoids.begin(), photoids.end(), g);
      }
      for (size_t i = 0; i < photoids.size(); ++i) {
        if (single_cate_shuffle > 0 && insert_pids.count(photoids[i]) > 0) {
          continue;
        }
        cur_count++;
        if (cur_count <= single_cate_cut_num) {
          trigger_list.push_back((int64)photoids[i]);
          insert_pids.insert((int64)photoids[i]);
        }
      }
    }
    context.SetIntListCommonAttr("trigger_list", std::move(trigger_list));
    return true;
  }

  static bool GenColdStartA2aAuthorIds(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto trigger_list = context.GetIntListCommonAttr("s_cs_a2a_trigger_aids");
    auto target_list = context.GetIntListCommonAttr("s_cs_a2a_target_aids");
    int64 a2a_num = context.GetIntCommonAttr("s_cs_a2a_num").value_or(80);
    if (!trigger_list || !target_list) {
      CL_LOG(WARNING) << "trigger_list or target_list is nullptr";
      return true;
    }
    std::set<int64> trigger_aids(trigger_list->begin(), trigger_list->end());
    std::set<int64> target_aids(target_list->begin(), target_list->end());
    auto coldstart_seller_set =
        context.GetPtrCommonAttr<folly::F14FastSet<uint64>>("merchant_coldstart_seller_ids_ptr");
    if (!coldstart_seller_set) {
      CL_LOG(WARNING) << "coldstart_seller_set is nullptr";
      return false;
    }
    std::vector<int64> ids_vec;
    for (auto aid : trigger_aids) {
      if (coldstart_seller_set->count(aid)) {
        ids_vec.emplace_back(aid);
      }
    }
    int count = 0;
    for (auto aid : target_aids) {
      if (coldstart_seller_set->count(aid) && !trigger_aids.count(aid)) {
        ids_vec.emplace_back(aid);
        if (++count > a2a_num * 3) {
          break;
        }
      }
    }
    if (ids_vec.size() > a2a_num) {
      std::random_device rng;
      std::mt19937 urng(rng());
      std::shuffle(ids_vec.begin(), ids_vec.end(), urng);
      ids_vec.resize(a2a_num);
    }
    context.SetIntListCommonAttr("trigger_list", std::move(ids_vec));
    return true;
  }

  static bool FilterLivinJewelleryAuthor(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    auto liveid_aid_map = context.GetPtrCommonAttr<const folly::F14FastMap<std::string, std::string>>(
        "welfare_showcase_set_ptr");
    auto jewellery_seller_set = context.GetPtrCommonAttr<folly::F14FastSet<uint64>>("jewellery_aid_set_ptr");
    if (!liveid_aid_map || !jewellery_seller_set) {
      CL_LOG_ERROR_EVERY("data is null", "liveidmap data is null", 1000);
      return true;
    }
    std::vector<int64> ids_vec;
    for (const auto &iter : *liveid_aid_map) {
      uint64 aid_tmp = 0;
      base::StringToUint64(iter.second, &aid_tmp);
      if (jewellery_seller_set->count(aid_tmp)) {
        ids_vec.emplace_back(static_cast<int64>(aid_tmp));
      }
    }
    if (ids_vec.size() <= 0) {
      CL_LOG_ERROR_EVERY("data is null", "authorid is null", 1000);
      return true;
    }
    context.SetIntListCommonAttr("trigger_list", std::move(ids_vec));
    return true;
  }

  static bool FilterListByDoubleVal(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto cs_photo_id_list = context.GetIntListCommonAttr("cs_photo_id_list");
    auto cs_distance_list = context.GetIntListCommonAttr("cs_distance_list");
    int64 score_filter_threshold = context.GetIntCommonAttr("score_filter_threshold").value_or(0);
    int64 rank_by_desc = context.GetIntCommonAttr("rank_by_desc").value_or(0);
    if (!cs_photo_id_list || cs_photo_id_list->empty() || !cs_distance_list || cs_distance_list->empty() ||
        cs_photo_id_list->size() != cs_distance_list->size()) {
      return true;
    }
    std::vector<int64> rst_vec;
    for (int i = 0; i < cs_photo_id_list->size(); i++) {
      if ((*cs_photo_id_list)[i] > 0 &&
          ((rank_by_desc == 0 && (*cs_distance_list)[i] < score_filter_threshold) ||
           (rank_by_desc != 0 && (*cs_distance_list)[i] >= score_filter_threshold))) {
        rst_vec.push_back((*cs_photo_id_list)[i]);
      }
    }
    context.SetIntListCommonAttr("ann_photo_ids_trim", std::move(rst_vec));
    return true;
  }

  static bool FilterColdStartSimilarAuthor(const CommonRecoLightFunctionContext &context,
                                           RecoResultConstIter begin, RecoResultConstIter end) {
    auto target_list = context.GetIntListCommonAttr("s_cs_a2a_trigger_aids");
    auto coldstart_similar_author_map =
        context.GetPtrCommonAttr<folly::F14FastMap<int32, std::vector<int32>>>(
            "merchant_coldstart_similar_author__memory_data");
    if (!target_list || target_list->empty() || nullptr == coldstart_similar_author_map) {
      return true;
    }

#define TRANSFER(aid) static_cast<int32>(aid - 0x80000000)
    std::vector<int64> rst_vec;
    for (auto target_id : (*target_list)) {
      if (coldstart_similar_author_map->count(TRANSFER(target_id)) == 0) {
        continue;
      }
      rst_vec.push_back(target_id);
    }
#undef TRANSFER

    context.SetIntListCommonAttr("trigger_list", std::move(rst_vec));
    return true;
  }

  static bool FilterByIntSet(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                             RecoResultConstIter end) {
    auto src_list = context.GetIntListCommonAttr("src_list");
    auto filter_int_set = context.GetPtrCommonAttr<folly::F14FastSet<uint64>>("filter_set");
    if (!src_list || src_list->empty() || nullptr == filter_int_set) {
      return true;
    }
    std::vector<int64> rst_vec;
    for (auto id : *src_list) {
      if (!filter_int_set->count(id)) {
        continue;
      }
      rst_vec.push_back(id);
    }
    context.SetIntListCommonAttr("result_list", std::move(rst_vec));
    return true;
  }
  static bool AdSellerMap(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                          RecoResultConstIter end) {
    auto seller_ab_aid_map = std::make_shared<std::map<std::string, std::set<int>>>();
    std::string kconf_name = "reco.merchant.merchant_video_ad_seller_ab_conf_v2";
    auto default_map = std::make_shared<std::map<std::string, std::string>>();
    ks::infra::KeyParser<std::string> key_parser = [](const std::string &key, std::string *val) -> bool {
      *val = key;
      return true;
    };
    auto conf_map =
        ks::infra::KConf().GetMap<std::string, std::string>(kconf_name, default_map, key_parser)->Get();
    if (!conf_map || conf_map->size() == 0) {
    } else {
      for (auto &it : *conf_map) {
        std::set<int> tmp_ids;
        std::vector<absl::string_view> tmp_vector = absl::StrSplit(it.second, ",", absl::SkipEmpty());
        for (auto &id_str : tmp_vector) {
          int id = 0;
          if (absl::SimpleAtoi(id_str, &id)) {
            tmp_ids.insert(id);
          }
        }
        (*seller_ab_aid_map).insert(std::make_pair(it.first, tmp_ids));
      }
    }
    context.SetPtrCommonAttr("seller_ab_aid_map_ptr", seller_ab_aid_map);
    return true;
  }

  static bool GenHasPhotoInfoPtrAttr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    const auto photo_info_ptr_getter =
        context.GetProtoMessagePtrItemAttr<ks::reco::PhotoInfo>("photo_info_ptr");
    auto has_photo_info_ptr_setter = context.SetIntItemAttr("has_photo_info_ptr");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      const auto *photo_info_ptr = photo_info_ptr_getter(result);
      if (!photo_info_ptr) {
        has_photo_info_ptr_setter(result, 0);
        return;
      }
      has_photo_info_ptr_setter(result, 1);
    });
    return true;
  }
  // 这是对应参数为 MerchantContext 的
  static bool IsSellerAbAid(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                            RecoResultConstIter end) {
    // setter
    auto is_seller_ab_aid_accessor = context.SetIntItemAttr("is_seller_ab_aid");
    // getter
    // int64 enable_merchant_b_ads_combo_holdback_ab_new =
    //     context.GetIntCommonAttr("enable_merchant_b_ads_combo_holdback_ab_new").value_or(0);
    int64 enable_merchant_b_ads_combo_holdback_ab_old =
        context.GetIntCommonAttr("enable_merchant_b_ads_combo_holdback_ab_old").value_or(0);
    int64 enable_merchant_video_midmini_seller_ab =
        context.GetIntCommonAttr("enable_merchant_video_midmini_seller_ab").value_or(0);
    int64 enable_merchant_video_ad_seller_blacklist_ab =
        context.GetIntCommonAttr("enable_merchant_video_ad_seller_blacklist_ab").value_or(0);
    int64 enable_merchant_video_ojld_bshop_seller =
        context.GetIntCommonAttr("enable_merchant_video_ojld_bshop_seller").value_or(0);
    std::string merchant_video_ad_seller_ab_config_key =
        std::string(context.GetStringCommonAttr("merchant_video_ad_seller_ab_config_key").value_or(""));
    std::string merchant_video_ad_seller_nohold_key =
        std::string(context.GetStringCommonAttr("merchant_video_ad_seller_nohold_key").value_or(""));

    const auto ads_aid_set = context.GetPtrCommonAttr<const folly::F14FastSet<uint64>>(
        "merchant_coldstart_ads_seller__memory_data");

    const auto midmini_seller_set = context.GetPtrCommonAttr<const folly::F14FastSet<uint64>>(
        "merchant_video_midmini_seller__memory_data");

    const auto ads_aid_blacklist = context.GetPtrCommonAttr<const folly::F14FastSet<uint64>>(
        "merchant_video_ads_seller_blacklist__memory_data");

    const auto bshop_seller_set =
        context.GetPtrCommonAttr<const folly::F14FastSet<uint64>>("merchant_video_bshop_seller__memory_data");

    const auto *seller_ab_aid_map =
        context.GetPtrCommonAttr<const std::map<std::string, std::set<int>>>("seller_ab_aid_map_ptr");

    auto author_id_getter = context.GetIntItemAttr("author__id");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      if (!author_id_getter(result)) {
        is_seller_ab_aid_accessor(result, 0);
        return;
      }
      uint64 author_id = author_id_getter(result).value_or(-1);
      if (ads_aid_set && !ads_aid_set->count(author_id)) {
        is_seller_ab_aid_accessor(result, 0);
        return;
      }
      if (enable_merchant_b_ads_combo_holdback_ab_old) {
        // B 端全局 comboholdback
        is_seller_ab_aid_accessor(result, 1);
        return;
      }
      if (enable_merchant_video_midmini_seller_ab && midmini_seller_set &&
          midmini_seller_set->count(author_id)) {
        is_seller_ab_aid_accessor(result, 1);
        return;
      }
      if (enable_merchant_video_ad_seller_blacklist_ab && ads_aid_blacklist &&
          ads_aid_blacklist->count(author_id)) {
        is_seller_ab_aid_accessor(result, 1);
        return;
      }
      if (!enable_merchant_video_ojld_bshop_seller && bshop_seller_set &&
          bshop_seller_set->count(author_id)) {
        is_seller_ab_aid_accessor(result, 1);
        return;
      }
      if (seller_ab_aid_map) {
        auto it = seller_ab_aid_map->find(merchant_video_ad_seller_ab_config_key);
        if (it != seller_ab_aid_map->end()) {
          const auto &aid_set = it->second;
          if (aid_set.count(author_id % 10)) {
            is_seller_ab_aid_accessor(result, 1);
            return;
          }
        }
      }
      // nohold 的商家 AB 与不投到投
      if (seller_ab_aid_map) {
        auto it = seller_ab_aid_map->find(merchant_video_ad_seller_nohold_key);
        if (it != seller_ab_aid_map->end()) {
          const auto &aid_set = it->second;
          if (aid_set.count(author_id % 10)) {
            is_seller_ab_aid_accessor(result, 1);
            return;
          }
        }
      }
      is_seller_ab_aid_accessor(result, 0);
      return;
    });
    return true;
  }

  // 聚星履约期翻车率优化
  static bool IsAdSocialPerformanceOptPhoto(std::pair<uint64, uint64> current_info) {
    if (2 * current_info.second <= current_info.first) {
      return true;
    }
    return false;
  }

  static bool GenIsAdSocialPerformanceOptPhotoAttr(const CommonRecoLightFunctionContext &context,
                                                   RecoResultConstIter begin, RecoResultConstIter end) {
    const auto *adsocial_performance_current_info__memory_data =
        context.GetPtrCommonAttr<const folly::F14FastMap<std::string, std::pair<uint64, uint64>>>(
            "adsocial_performance_current_info__memory_data");
    auto adsocial_retrieval_performance_old_photo_filter_exempt =
        context.GetIntCommonAttr("adsocial_retrieval_performance_old_photo_filter_exempt").value_or(0);
    auto has_ad_social_type_getter = context.GetIntItemAttr("has_ad_social_type");
    auto ad_social_type_getter = context.GetIntItemAttr("ad_social_type");
    auto photo_id_getter = context.GetIntItemAttr("photo_id");
    auto is_ad_social_performance_opt_photo_attr_setter =
        context.SetIntItemAttr("is_ad_social_performance_opt_photo_attr");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto has_ad_social_type = has_ad_social_type_getter(result).value_or(0);
      auto ad_social_type = ad_social_type_getter(result).value_or(0);
      auto photo_id = photo_id_getter(result).value_or(0);
      if (adsocial_retrieval_performance_old_photo_filter_exempt &&
          adsocial_performance_current_info__memory_data && has_ad_social_type && ad_social_type == 0) {
        auto it = adsocial_performance_current_info__memory_data->find("D-" + base::Int64ToString(photo_id));
        if (it != adsocial_performance_current_info__memory_data->end()) {
          if (IsAdSocialPerformanceOptPhoto(it->second)) {
            is_ad_social_performance_opt_photo_attr_setter(result, 1);
          }
        }
      }
    });

    return true;
  }

  static bool GenAuthorTailAttr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
    std::string xtr_filter_author_tail_key =
        static_cast<std::string>(context.GetStringCommonAttr("xtr_filter_author_tail_key").value_or(""));
    const auto photo_info_ptr_getter =
        context.GetProtoMessagePtrItemAttr<ks::reco::PhotoInfo>("photo_info_ptr");
    auto author_tail_attr_setter = context.SetIntItemAttr("author_tail_attr");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      const auto *photo_info_ptr = photo_info_ptr_getter(result);
      int author_tail_attr = -1;
      if (photo_info_ptr) {
        auto iter = photo_info_ptr->video_cold_start_info().author_tails().find(xtr_filter_author_tail_key);
        if (iter != photo_info_ptr->video_cold_start_info().author_tails().end()) {
          author_tail_attr = iter->second;
        }
      }
      author_tail_attr_setter(result, author_tail_attr);
    });
    return true;
  }

  static bool IsMagicFreqControl(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    // 根据用户短播、长播魔表来判断是否过滤魔表视频
    auto user_info = context.GetProtoMessagePtrCommonAttr<ks::reco::UserInfo>("user_info_ptr");
    bool control = false;
    auto magic_control_conf = ks::reco::RecoConfigKey::reco_thanos_MagicFacePhotoControlMap()->Get().get();
    if (magic_control_conf == nullptr) {
      context.SetIntCommonAttr("is_hate_photo", 0);
      context.SetIntCommonAttr("is_magic_freq_control", 0);
      return true;
    }
    float magic_freq_control_time_interval_hour =
        magic_control_conf->count("magic_freq_control_time_interval_hour_")
            ? magic_control_conf->at("magic_freq_control_time_interval_hour_")
            : 0.0;
    float magic_freq_control_time_interval_day =
        magic_control_conf->count("magic_freq_control_time_interval_day_")
            ? magic_control_conf->at("magic_freq_control_time_interval_day_")
            : 0.0;
    float user_short_time_photo_svtr_control_ratio =
        magic_control_conf->count("user_short_time_photo_svtr_control_ratio_")
            ? magic_control_conf->at("user_short_time_photo_svtr_control_ratio_")
            : 0.0;
    float user_long_time_photo_svtr_control_ratio =
        magic_control_conf->count("user_long_time_photo_svtr_control_ratio_")
            ? magic_control_conf->at("user_long_time_photo_svtr_control_ratio_")
            : 0.0;
    float user_photo_lvtr_control_ratio = magic_control_conf->count("user_photo_lvtr_control_ratio_")
                                              ? magic_control_conf->at("user_photo_lvtr_control_ratio_")
                                              : 0.0;
    float short_view_control_ratio = magic_control_conf->count("short_view_control_ratio_")
                                        ? magic_control_conf->at("short_view_control_ratio_")
                                        : 0.0;
    float short_view_control_count = magic_control_conf->count("short_view_control_count_")
                                        ? magic_control_conf->at("short_view_control_count_")
                                        : 0.0;
    const int64 kMsHour = magic_freq_control_time_interval_hour * base::Time::kMillisecondsPerHour;
    const int64 kMsDay = magic_freq_control_time_interval_day * base::Time::kMillisecondsPerDay;
    ks::reco::UserProfileV1 user_profile = user_info->user_profile_v1();
    auto &hate_items = user_profile.hate_list();
    auto &video_playing_stat = user_profile.video_playing_stat();
    auto &real_show_list = user_profile.real_show_list();
    folly::F14FastSet<uint64> real_show_magic_list;
    const int64 now_ms = base::GetTimestamp() / base::Time::kMicrosecondsPerMillisecond;
    // 一天内 hate 过魔表视频过滤
    for (int i = 0; i < hate_items.size(); i++) {
      uint64 time_ms = hate_items[i].time_ms();
      std::string temp = hate_items[i].extra_filed();
      if (temp.size() == 0) {
        continue;
      }
      std::vector<std::string> temp_vec;
      base::SplitStringWithOptions(temp, ",", true, true, &temp_vec);
      for (const auto &sub_field : temp_vec) {
        std::vector<std::string> target_values;
        int id_value = -1;
        if (base::StartsWith(sub_field, "MF_", false)) {
          base::SplitStringWithOptions(sub_field.substr(3), "$", true, true, &target_values);
          for (const auto &value : target_values) {
            if (absl::SimpleAtoi(value, &id_value) && now_ms - time_ms < kMsDay && id_value > 0) {
              context.SetIntCommonAttr("is_hate_photo", 1);
              context.SetIntCommonAttr("is_magic_freq_control", 1);
              return true;
            }
          }
        }
      }
    }
    // n 小时内 real_show 的魔表视频
    for (int i = 0; i < real_show_list.size(); i++) {
      uint64 time_ms = real_show_list[i].time_ms();
      std::string temp = real_show_list[i].extra_filed();
      if (temp.size() == 0) {
        continue;
      }
      std::vector<std::string> temp_vec;
      base::SplitStringWithOptions(temp, ",", true, true, &temp_vec);
      for (const auto &sub_field : temp_vec) {
        std::vector<std::string> target_values;
        int id_value = -1;
        if (base::StartsWith(sub_field, "MF_", false)) {
          base::SplitStringWithOptions(sub_field.substr(3), "$", true, true, &target_values);
          for (const auto &value : target_values) {
            if (absl::SimpleAtoi(value, &id_value) && now_ms - time_ms < kMsHour && id_value > 0) {
              real_show_magic_list.insert(real_show_list[i].photo_id());
            }
          }
        }
      }
    }
    // n 小时内的 real show list 里的魔表 短播超过 m 个
    if (real_show_magic_list.size() > 0u) {
      int shot_view_count = 0;
      int long_view_count = 0;
      for (int i = 0; i < video_playing_stat.size(); i++) {
        if (real_show_magic_list.count(video_playing_stat[i].photo_id()) > 0) {
          float view_ratio =
              video_playing_stat[i].playing_time() / (video_playing_stat[i].video_duration() + 1.0);
          if (video_playing_stat[i].video_duration() < 10000) {
            if (view_ratio < user_short_time_photo_svtr_control_ratio ||
                video_playing_stat[i].playing_time() < 2000) {
              shot_view_count++;
            }
          } else if (video_playing_stat[i].video_duration() >= 10000) {
            if (view_ratio < user_long_time_photo_svtr_control_ratio ||
                video_playing_stat[i].playing_time() < 3000) {
              shot_view_count++;
            }
          }
          if (view_ratio > user_photo_lvtr_control_ratio && video_playing_stat[i].video_duration() > 8000) {
            long_view_count++;
          }
        }
      }
      if (shot_view_count / (real_show_magic_list.size() + 0.1) > short_view_control_ratio ||
          shot_view_count > short_view_control_count) {
        control = true;
      }
      if (long_view_count > shot_view_count &&
          shot_view_count / (real_show_magic_list.size() + 0.1) < short_view_control_ratio) {
        control = false;
      }
      LOG_EVERY_N(INFO, 50000) << "real show magic list: " << real_show_magic_list.size()
                              << "short view magic count: " << shot_view_count
                              << "long view magic count: " << long_view_count;
    }
    context.SetIntCommonAttr("is_hate_photo", 0);
    context.SetIntCommonAttr("is_magic_freq_control", control);
    return true;
  }

  static bool GenIsFirstPageAttr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto user_info = context.GetProtoMessagePtrCommonAttr<ks::reco::UserInfo>("user_info_ptr");
    if (!user_info) {
      CL_LOG(INFO) << "user_info is nullptr, u should not reached here";
      return true;
    }
    auto page_limit = context.GetIntCommonAttr("slide_first_page_picture_filter_page_limit").value_or(0);
    double hour_gap = context.GetDoubleCommonAttr("slide_first_page_picture_filter_hour_gap").value_or(0.0);
    bool is_first_page = IsFirstNColdStartPage(*user_info, hour_gap, page_limit);
    auto enable_slide_pic_change_first_page =
        context.GetIntCommonAttr("enable_slide_pic_change_first_page").value_or(0);
    if (enable_slide_pic_change_first_page) {
      is_first_page = user_info->slide_first_page();
    }
    context.SetIntCommonAttr("is_first_page", is_first_page);
    return true;
  }

  static bool GenMaxPictureRealshowAttr(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    auto slide_picture_gray_max_vv_by_user_group =
        context.GetStringCommonAttr("slide_picture_gray_max_vv_by_user_group").value_or("");
    auto audit_hot_high_tag_level_gray_picture_filter_show_threshold =
        context.GetIntCommonAttr("audit_hot_high_tag_level_gray_picture_filter_show_threshold")
            .value_or(3000000);
    auto user_pic_type = context.GetIntCommonAttr("user_picture_preference_type").value_or(0);
    folly::F14FastMap<int, float> user_pic_type_max_vv_map;
    ks::reco::SimpleStringToMap(static_cast<std::string>(slide_picture_gray_max_vv_by_user_group), ",", ":",
                                &user_pic_type_max_vv_map);
    auto max_picture_realshow = user_pic_type_max_vv_map.count(user_pic_type) > 0
                                    ? (int)user_pic_type_max_vv_map[user_pic_type]
                                    : audit_hot_high_tag_level_gray_picture_filter_show_threshold;
    context.SetIntCommonAttr("max_picture_realshow", max_picture_realshow);
    return true;
  }

  static bool GetMerchantBigVAuthorTypeKconf(const std::string &bigv_kconf_name,
                                             folly::F14FastMap<uint64, std::string> *merchant_bigv_type_map) {
    auto default_map = std::make_shared<std::map<std::string, std::string>>();
    ks::infra::KeyParser<std::string> key_parser = [](const std::string &key, std::string *val) -> bool {
      *val = key;
      return true;
    };
    auto conf_map =
        ks::infra::KConf().GetMap<std::string, std::string>(bigv_kconf_name, default_map, key_parser)->Get();
    if (!conf_map) {
      return false;
    }
    if (conf_map->size() == 0) {
      return false;
    }

    for (auto it = conf_map->begin(); it != conf_map->end(); it++) {
      std::vector<std::string> string_split_vec;
      std::string author_type = it->first;
      base::SplitStringWithOptions(it->second, ",", true, true, &string_split_vec);
      if (string_split_vec.size() == 0) continue;
      for (int i = 0; i < string_split_vec.size(); i++) {
        std::string tmp_str = string_split_vec[i];
        uint64 aid_value = 0;
        if (base::StringToUint64(tmp_str, &aid_value)) {
          (*merchant_bigv_type_map)[aid_value] = author_type;
        }
      }
    }
    return true;
  }

  static bool GenLiveCateAttr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                              RecoResultConstIter end) {
    auto is_merchant_living_getter = context.GetIntItemAttr("is_merchant_living");
    auto live_cate2_setter = context.SetIntItemAttr("live_cate2");
    auto live_cate3_setter = context.SetIntItemAttr("live_cate3");
    auto live_cate2_list_setter = context.SetIntListItemAttr("live_cate2_list");
    auto merchant_living_category_blacklist_goods_topk =
        context.GetIntCommonAttr("merchant_living_category_blacklist_goods_topk").value_or(0);
    auto merchant_bigv_kconf_name = context.GetStringCommonAttr("merchant_bigv_kconf_name").value_or("");
    folly::F14FastMap<uint64, std::string> merchant_bigv_type_map;
    GetMerchantBigVAuthorTypeKconf(static_cast<std::string>(merchant_bigv_kconf_name),
                                   &merchant_bigv_type_map);
    auto merchant_bigv_type_map_has_aid_setter = context.SetIntItemAttr("merchant_bigv_type_map_has_aid");
    auto author__id_getter = context.GetIntItemAttr("author__id");
    auto s_cart_item_is_interpreting_index = context.GetIntItemAttr("s_cart_item_is_interpreting_index");
    auto s_cart_item_cate2_id_list = context.GetIntListItemAttr("s_cart_item_cate2_id_list");
    auto s_cart_item_cate3_id_list = context.GetIntListItemAttr("s_cart_item_cate3_id_list");
    static std::vector<int64> default_int_list;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto is_merchant_living = is_merchant_living_getter(result).value_or(0);
      std::vector<int64> live_cate2_list;
      int live_cate2 = 0, live_cate3 = 0;
      int live_goods_cate2_iter = 0;
      int merchant_bigv_type_map_has_aid = 0;
      auto author__id = author__id_getter(result).value_or(0);
      if (is_merchant_living) {
        int interpreting_index = s_cart_item_is_interpreting_index(result).value_or(0);
        auto cate2_list = s_cart_item_cate2_id_list(result).value_or(default_int_list);
        auto goods_list_size = cate2_list.size();
        if (goods_list_size > 0) {
          // ty merchant_living_info 为空对 live_cate2， live_cate3 没影响
          if (interpreting_index < goods_list_size) {
            live_cate2 = cate2_list[interpreting_index];
          }
          // Live 头像拓展到看 top4 商品
          for (int idx = 0; idx < goods_list_size; idx++) {
            if (idx > merchant_living_category_blacklist_goods_topk) {
              break;
            }
            live_goods_cate2_iter = cate2_list[idx];
            live_cate2_list.emplace_back(live_goods_cate2_iter);
          }
        }
        auto cate3_list = s_cart_item_cate3_id_list(result).value_or(default_int_list);
        if (cate3_list.size() > 0) {
          if (interpreting_index < cate3_list.size()) {
            live_cate3 = cate3_list[interpreting_index];
          }
        }
      }
      if (merchant_bigv_type_map.find(author__id) != merchant_bigv_type_map.end()) {
        merchant_bigv_type_map_has_aid = 1;
      }
      live_cate2_setter(result, live_cate2);
      live_cate3_setter(result, live_cate3);
      live_cate2_list_setter(result, live_cate2_list);
      merchant_bigv_type_map_has_aid_setter(result, merchant_bigv_type_map_has_aid);
    });
    return true;
  }

  static bool GenHasMerchantLivingPhotoInfoPtrAttr(const CommonRecoLightFunctionContext &context,
                                                   RecoResultConstIter begin, RecoResultConstIter end) {
    const auto merchant_living_photo_info_ptr_getter =
        context.GetProtoMessagePtrItemAttr<ks::reco::MerchantPhotoLivingInfo>(
            "merchant_living_photo_info_ptr");
    auto has_merchant_living_photo_info_ptr_setter =
        context.SetIntItemAttr("has_merchant_living_photo_info_ptr");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto *merchant_living_photo_info_ptr = merchant_living_photo_info_ptr_getter(result);
      if (!merchant_living_photo_info_ptr) {
        has_merchant_living_photo_info_ptr_setter(result, 0);
        return;
      }
      has_merchant_living_photo_info_ptr_setter(result, 1);
    });
    return true;
  }

  static bool GenMaxUploadDayAttr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    const auto audit_high_hot_tag_skip_time_filter_max_day =
        context.GetDoubleCommonAttr("audit_high_hot_tag_skip_time_filter_max_day").value_or(90.0);
    const auto hetu_tag_timeliness_strong_filter_max_day =
        context.GetDoubleCommonAttr("hetu_tag_timeliness_strong_filter_max_day").value_or(7.0);
    const auto hetu_tag_timeliness_weak_filter_max_day =
        context.GetDoubleCommonAttr("hetu_tag_timeliness_weak_filter_max_day").value_or(30.0);
    const auto hetu_tag_level_info_v2__hetu_tag_getter =
        context.GetIntListItemAttr("hetu_tag_level_info_v2__hetu_tag");
    auto max_upload_day_setter = context.SetDoubleItemAttr("max_upload_day");

    static std::vector<int64> default_int_list;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double max_upload_day = audit_high_hot_tag_skip_time_filter_max_day;
      const auto hetu_tag_level_info_v2__hetu_tag =
          hetu_tag_level_info_v2__hetu_tag_getter(result).value_or(default_int_list);
      for (auto hetu_tag : hetu_tag_level_info_v2__hetu_tag) {
        const auto real_hetu_tag = ((hetu_tag >> 8) & 0xffffff);
        if (real_hetu_tag == 4000008) {
          max_upload_day = hetu_tag_timeliness_strong_filter_max_day;
        } else if (real_hetu_tag == 4000007) {
          max_upload_day = hetu_tag_timeliness_weak_filter_max_day;
        }
      }
      max_upload_day_setter(result, max_upload_day);
    });
    return true;
  }

  static bool GenPbHasAttr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                           RecoResultConstIter end) {
    const auto ad_social_type_getter = context.GetIntItemAttr("ad_social_type");
    const auto audit_hot_high_tag_level_getter = context.GetIntItemAttr("audit_hot_high_tag_level");
    const auto high_hot_audit_tag_v2_getter = context.GetIntItemAttr("high_hot_audit_tag_v2");

    const auto ad_social_type_setter = context.SetIntItemAttr("has_ad_social_type");
    const auto audit_hot_high_tag_level_setter = context.SetIntItemAttr("has_audit_hot_high_tag_level");
    const auto high_hot_audit_tag_v2_setter = context.SetIntItemAttr("has_high_hot_audit_tag_v2");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      const auto ad_social_type = ad_social_type_getter(result);
      const auto audit_hot_high_tag_level = audit_hot_high_tag_level_getter(result);
      const auto high_hot_audit_tag_v2 = high_hot_audit_tag_v2_getter(result);
      if (ad_social_type.has_value()) {
        ad_social_type_setter(result, 1);
      } else {
        ad_social_type_setter(result, 0);
      }
      if (audit_hot_high_tag_level.has_value()) {
        audit_hot_high_tag_level_setter(result, 1);
      } else {
        audit_hot_high_tag_level_setter(result, 0);
      }
      if (high_hot_audit_tag_v2.has_value()) {
        high_hot_audit_tag_v2_setter(result, 1);
      } else {
        high_hot_audit_tag_v2_setter(result, 0);
      }
    });
    return true;
  }

  static bool EnrichPhotoInfoAttrsForRetrievalEpCenter(const CommonRecoLightFunctionContext &context,
                                                       RecoResultConstIter begin, RecoResultConstIter end) {
    const auto photo_info_ptr_getter =
        context.GetProtoMessagePtrItemAttr<ks::reco::PhotoInfo>("photo_info_ptr");
    // setter
    auto hetu_tag_level_info_v2__hetu_tag_setter =
        context.SetIntListItemAttr("hetu_tag_level_info_v2__hetu_tag");
    auto author__id_setter = context.SetIntItemAttr("author__id");
    auto upload_time_setter = context.SetIntItemAttr("upload_time");
    auto audit_hot_high_tag_level_setter = context.SetIntItemAttr("audit_hot_high_tag_level");
    auto live_photo_info__is_living_setter = context.SetIntItemAttr("live_photo_info__is_living");
    auto is_merchant_coldstart_expansion_index_setter =
        context.SetIntItemAttr("is_merchant_coldstart_expansion_index");
    auto merchant_offline_type_setter = context.SetIntItemAttr("merchant_offline_type");
    auto data_set_tags_setter = context.SetIntListItemAttr("data_set_tags");
    auto data_set_tags_bit_setter = context.SetIntItemAttr("data_set_tags_bit");
    auto content_safety_level_with_namespace__level_hot_online_setter =
        context.SetIntItemAttr("content_safety_level_with_namespace__level_hot_online");
    auto audit_b_second_tag_setter = context.SetIntItemAttr("audit_b_second_tag");
    auto explore_operation_c_review_level_setter = context.SetIntItemAttr("explore_operation_c_review_level");
    auto topk_audit_level_setter = context.SetIntItemAttr("topk_audit_level");
    auto topk_audit_tag_setter = context.SetIntItemAttr("topk_audit_tag");
    auto questionnaire_info__positive_count_setter =
        context.SetIntItemAttr("questionnaire_info__positive_count");
    auto questionnaire_info__negative_count_setter =
        context.SetIntItemAttr("questionnaire_info__negative_count");
    auto questionnaire_info__unsure_count_setter = context.SetIntItemAttr("questionnaire_info__unsure_count");
    auto duration_ms_setter = context.SetIntItemAttr("duration_ms");
    auto upload_type_setter = context.SetIntItemAttr("upload_type");
    auto nebula_stats__real_show_count_setter = context.SetIntItemAttr("nebula_stats__real_show_count");
    auto thanos_stats__real_show_count_setter = context.SetIntItemAttr("thanos_stats__real_show_count");
    auto thanos_stats__like_count_setter = context.SetIntItemAttr("thanos_stats__like_count");
    auto nebula_stats__like_count_setter = context.SetIntItemAttr("nebula_stats__like_count");
    auto high_value_pic_flag_setter = context.SetIntItemAttr("high_value_pic_flag");
    auto is_merchant_setter = context.SetIntItemAttr("is_merchant");
    auto cuckoo_info__author_type_setter = context.SetStringItemAttr("cuckoo_info__author_type");
    auto ad_social_type_setter = context.SetIntItemAttr("ad_social_type");
    auto photo_id_setter = context.SetIntItemAttr("photo_id");
    auto video_cold_start_info__photo_dynamic_xtrs_str_setter =
        context.SetStringItemAttr("video_cold_start_info__photo_dynamic_xtrs_str");
    auto picture_type_setter = context.SetIntItemAttr("picture_type");
    auto high_hot_audit_tag_v2_setter = context.SetIntItemAttr("high_hot_audit_tag_v2");
    auto merchant_risk_control_info__action_setter =
        context.SetIntListItemAttr("merchant_risk_control_info__action");
    auto merchant_risk_control_info__source_setter =
        context.SetStringListItemAttr("merchant_risk_control_info__source");
    auto seller_main_category_level1_setter = context.SetIntItemAttr("seller_main_category_level1");
    auto is_merchant_live_slicing_photo_setter = context.SetIntItemAttr("is_merchant_live_slicing_photo");
    auto plc_similarity_label_setter = context.SetIntItemAttr("plc_similarity_label");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      const auto *photo_info_ptr = photo_info_ptr_getter(result);
      if (!photo_info_ptr) {
        return;
      }

      std::vector<int64> hetu_tag_level_info_v2__hetu_tag_list;
      if (photo_info_ptr->has_hetu_tag_level_info_v2()) {
        for (const int64 x : photo_info_ptr->hetu_tag_level_info_v2().hetu_tag()) {
          hetu_tag_level_info_v2__hetu_tag_list.emplace_back(x);
        }
      }
      hetu_tag_level_info_v2__hetu_tag_setter(result, hetu_tag_level_info_v2__hetu_tag_list);

      if (photo_info_ptr->has_author() && photo_info_ptr->author().has_id()) {
        author__id_setter(result, photo_info_ptr->author().id());
      }
      if (photo_info_ptr->has_upload_time()) {
        upload_time_setter(result, photo_info_ptr->upload_time());
      }
      if (photo_info_ptr->has_audit_hot_high_tag_level()) {
        audit_hot_high_tag_level_setter(result, photo_info_ptr->audit_hot_high_tag_level());
      }
      if (photo_info_ptr->has_live_photo_info() && photo_info_ptr->live_photo_info().has_is_living()) {
        live_photo_info__is_living_setter(result, photo_info_ptr->live_photo_info().is_living());
      }
      if (photo_info_ptr->has_is_merchant_coldstart_expansion_index()) {
        is_merchant_coldstart_expansion_index_setter(result,
                                                     photo_info_ptr->is_merchant_coldstart_expansion_index());
      }
      if (photo_info_ptr->has_merchant_offline_type()) {
        merchant_offline_type_setter(result, photo_info_ptr->merchant_offline_type());
      }

      std::vector<int64> data_set_tags_list;
      for (const int64 x : photo_info_ptr->data_set_tags()) {
        data_set_tags_list.emplace_back(x);
      }
      data_set_tags_setter(result, data_set_tags_list);

      if (photo_info_ptr->has_data_set_tags_bit()) {
        data_set_tags_bit_setter(result, photo_info_ptr->data_set_tags_bit());
      }
      if (photo_info_ptr->has_content_safety_level_with_namespace() &&
          photo_info_ptr->content_safety_level_with_namespace().has_level_hot_online()) {
        content_safety_level_with_namespace__level_hot_online_setter(
            result, photo_info_ptr->content_safety_level_with_namespace().level_hot_online());
      }
      if (photo_info_ptr->has_audit_b_second_tag()) {
        audit_b_second_tag_setter(result, photo_info_ptr->audit_b_second_tag());
      }
      if (photo_info_ptr->has_explore_operation_c_review_level()) {
        explore_operation_c_review_level_setter(result, photo_info_ptr->explore_operation_c_review_level());
      }
      if (photo_info_ptr->has_topk_audit_level()) {
        topk_audit_level_setter(result, photo_info_ptr->topk_audit_level());
      }
      if (photo_info_ptr->has_topk_audit_tag()) {
        topk_audit_tag_setter(result, photo_info_ptr->topk_audit_tag());
      }
      if (photo_info_ptr->has_questionnaire_info() &&
          photo_info_ptr->questionnaire_info().has_positive_count()) {
        questionnaire_info__positive_count_setter(result,
                                                  photo_info_ptr->questionnaire_info().positive_count());
      }
      if (photo_info_ptr->has_questionnaire_info() &&
          photo_info_ptr->questionnaire_info().has_negative_count()) {
        questionnaire_info__negative_count_setter(result,
                                                  photo_info_ptr->questionnaire_info().negative_count());
      }
      if (photo_info_ptr->has_questionnaire_info() &&
          photo_info_ptr->questionnaire_info().has_unsure_count()) {
        questionnaire_info__unsure_count_setter(result, photo_info_ptr->questionnaire_info().unsure_count());
      }
      if (photo_info_ptr->has_duration_ms()) {
        duration_ms_setter(result, photo_info_ptr->duration_ms());
      }
      if (photo_info_ptr->has_upload_type()) {
        upload_type_setter(result, photo_info_ptr->upload_type());
      }
      if (photo_info_ptr->has_nebula_stats() && photo_info_ptr->nebula_stats().has_real_show_count()) {
        nebula_stats__real_show_count_setter(result, photo_info_ptr->nebula_stats().real_show_count());
      }
      if (photo_info_ptr->has_thanos_stats() && photo_info_ptr->thanos_stats().has_real_show_count()) {
        thanos_stats__real_show_count_setter(result, photo_info_ptr->thanos_stats().real_show_count());
      }
      if (photo_info_ptr->has_thanos_stats() && photo_info_ptr->thanos_stats().has_like_count()) {
        thanos_stats__like_count_setter(result, photo_info_ptr->thanos_stats().like_count());
      }
      if (photo_info_ptr->has_nebula_stats() && photo_info_ptr->nebula_stats().has_like_count()) {
        nebula_stats__like_count_setter(result, photo_info_ptr->nebula_stats().like_count());
      }
      if (photo_info_ptr->has_high_value_pic_flag()) {
        high_value_pic_flag_setter(result, photo_info_ptr->high_value_pic_flag());
      }
      if (photo_info_ptr->has_is_merchant()) {
        is_merchant_setter(result, photo_info_ptr->is_merchant());
      }
      if (photo_info_ptr->has_cuckoo_info() && photo_info_ptr->cuckoo_info().has_author_type()) {
        cuckoo_info__author_type_setter(result, photo_info_ptr->cuckoo_info().author_type());
      }
      if (photo_info_ptr->has_ad_social_type()) {
        ad_social_type_setter(result, photo_info_ptr->ad_social_type());
      }
      if (photo_info_ptr->has_photo_id()) {
        photo_id_setter(result, photo_info_ptr->photo_id());
      }
      if (photo_info_ptr->has_video_cold_start_info() &&
          photo_info_ptr->video_cold_start_info().has_photo_dynamic_xtrs_str()) {
        video_cold_start_info__photo_dynamic_xtrs_str_setter(
            result, photo_info_ptr->video_cold_start_info().photo_dynamic_xtrs_str());
      }
      if (photo_info_ptr->has_picture_type()) {
        picture_type_setter(result, photo_info_ptr->picture_type());
      }
      if (photo_info_ptr->has_high_hot_audit_tag_v2()) {
        high_hot_audit_tag_v2_setter(result, photo_info_ptr->high_hot_audit_tag_v2());
      }

      std::vector<int64> merchant_risk_control_info__action_list;
      std::vector<std::string> merchant_risk_control_info__source_list;
      if (photo_info_ptr->has_merchant_risk_control_info()) {
        for (const int64 x : photo_info_ptr->merchant_risk_control_info().action()) {
          merchant_risk_control_info__action_list.emplace_back(x);
        }
        for (const auto &x : photo_info_ptr->merchant_risk_control_info().source()) {
          merchant_risk_control_info__source_list.emplace_back(x);
        }
      }
      merchant_risk_control_info__action_setter(result, merchant_risk_control_info__action_list);
      merchant_risk_control_info__source_setter(result, merchant_risk_control_info__source_list);

      if (photo_info_ptr->has_seller_main_category_level1()) {
        seller_main_category_level1_setter(result, photo_info_ptr->seller_main_category_level1());
      }

      if (photo_info_ptr->has_is_merchant_live_slicing_photo()) {
        is_merchant_live_slicing_photo_setter(result, photo_info_ptr->is_merchant_live_slicing_photo());
      }

      if (photo_info_ptr->has_plc_similarity_label()) {
        plc_similarity_label_setter(result, photo_info_ptr->plc_similarity_label());
      }
    });
    return true;
  }

  static bool EnrichMerchantPhotoInfoAttrsForRetrievalPerf(const CommonRecoLightFunctionContext &context,
                                                           RecoResultConstIter begin,
                                                           RecoResultConstIter end) {
    const auto photo_info_ptr_getter =
        context.GetProtoMessagePtrItemAttr<ks::reco::PhotoInfo>("photo_info_ptr");

    auto merchant_photo_info_ptr_accessor =
        context.GetProtoMessagePtrItemAttr<ks::reco::MerchantGoodsInfo>("merchant_photo_info_ptr");
    auto original_price_setter = context.SetDoubleItemAttr("original_price");
    auto activity_price_setter = context.SetDoubleItemAttr("activity_price");
    auto p_exist_ms_setter = context.SetIntItemAttr("p_exist_ms");
    uint64 cur_timestamp = base::GetTimestamp();

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      const auto *photo_info_ptr = photo_info_ptr_getter(result);
      const auto *merchant_photo_info_ptr = merchant_photo_info_ptr_accessor(result);
      if (!photo_info_ptr || !merchant_photo_info_ptr) {
        return;
      }

      int64 exist_ms =
          cur_timestamp / base::Time::kMicrosecondsPerMillisecond - photo_info_ptr->upload_time();
      p_exist_ms_setter(result, exist_ms);
      if (merchant_photo_info_ptr->has_original_price()) {
        original_price_setter(result, merchant_photo_info_ptr->original_price());
      }
      if (merchant_photo_info_ptr->has_activity_price()) {
        activity_price_setter(result, merchant_photo_info_ptr->activity_price());
      }
    });
    return true;
  }

  static bool EnrichMerchantPhotoInfoAttrsForRetrievalEpCenter(const CommonRecoLightFunctionContext &context,
                                                               RecoResultConstIter begin,
                                                               RecoResultConstIter end) {
    auto merchant_photo_info_ptr_accessor =
        context.GetProtoMessagePtrItemAttr<ks::reco::MerchantGoodsInfo>("merchant_photo_info_ptr");
    // setter
    auto shipping_not_available_area_setter = context.SetIntListItemAttr("shipping_not_available_area");
    auto item_cate2_id_setter = context.SetIntItemAttr("item_cate2_id");
    auto item_cate3_id_setter = context.SetIntItemAttr("item_cate3_id");
    auto mmu_item_cate2_id_setter = context.SetIntItemAttr("mmu_item_cate2_id");
    auto mmu_item_cate3_id_setter = context.SetIntItemAttr("mmu_item_cate3_id");
    auto item_shop_id_setter = context.SetIntItemAttr("item_shop_id");
    auto original_price_setter = context.SetDoubleItemAttr("original_price");
    auto activity_price_setter = context.SetDoubleItemAttr("activity_price");
    auto item_author_id_setter = context.SetIntItemAttr("item_author_id");
    auto item_extreme_max_price_setter = context.SetIntItemAttr("item_extreme_max_price");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      const auto *merchant_photo_info_ptr = merchant_photo_info_ptr_accessor(result);
      if (!merchant_photo_info_ptr) {
        return;
      }

      std::vector<int64> shipping_not_available_area_list;
      for (const int64 x : merchant_photo_info_ptr->shipping_not_available_area()) {
        shipping_not_available_area_list.emplace_back(x);
      }
      shipping_not_available_area_setter(result, shipping_not_available_area_list);

      if (merchant_photo_info_ptr->has_item_cate2_id()) {
        item_cate2_id_setter(result, merchant_photo_info_ptr->item_cate2_id());
      }
      if (merchant_photo_info_ptr->has_item_cate3_id()) {
        item_cate3_id_setter(result, merchant_photo_info_ptr->item_cate3_id());
      }
      if (merchant_photo_info_ptr->has_mmu_item_cate2_id()) {
        mmu_item_cate2_id_setter(result, merchant_photo_info_ptr->mmu_item_cate2_id());
      }
      if (merchant_photo_info_ptr->has_mmu_item_cate3_id()) {
        mmu_item_cate3_id_setter(result, merchant_photo_info_ptr->mmu_item_cate3_id());
      }
      if (merchant_photo_info_ptr->has_item_shop_id()) {
        item_shop_id_setter(result, merchant_photo_info_ptr->item_shop_id());
      }
      if (merchant_photo_info_ptr->has_original_price()) {
        original_price_setter(result, merchant_photo_info_ptr->original_price());
      }
      if (merchant_photo_info_ptr->has_activity_price()) {
        activity_price_setter(result, merchant_photo_info_ptr->activity_price());
      }
      if (merchant_photo_info_ptr->has_item_author_id()) {
        item_author_id_setter(result, merchant_photo_info_ptr->item_author_id());
      }
      if (merchant_photo_info_ptr->has_item_extreme_max_price()) {
        item_extreme_max_price_setter(result, merchant_photo_info_ptr->item_extreme_max_price());
      }
    });
    return true;
  }

  static bool EnrichMerchantLivingPhotoInfoAttrsForRetrievalEpCenter(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    const auto merchant_living_info_getter =
        context.GetProtoMessagePtrItemAttr<ks::reco::MerchantPhotoLivingInfo>(
            "merchant_living_photo_info_ptr");
    // setter
    auto exclude_province_code_list_setter = context.SetIntListItemAttr("exclude_province_code_list");
    auto sAduitType_setter = context.SetStringItemAttr("sAduitType");
    auto sAduitResult_setter = context.SetStringItemAttr("sAduitResult");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      const auto *merchant_living_photo_info_ptr = merchant_living_info_getter(result);
      if (!merchant_living_photo_info_ptr) {
        return;
      }

      std::vector<int64> exclude_province_code_list;
      for (const int64 x : merchant_living_photo_info_ptr->exclude_province_code_list()) {
        exclude_province_code_list.emplace_back(x);
      }
      exclude_province_code_list_setter(result, exclude_province_code_list);

      if (merchant_living_photo_info_ptr->has_saduittype()) {
        sAduitType_setter(result, merchant_living_photo_info_ptr->saduittype());
      }
      if (merchant_living_photo_info_ptr->has_saduitresult()) {
        sAduitResult_setter(result, merchant_living_photo_info_ptr->saduitresult());
      }
    });
    return true;
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(MerchantPhotoRetrievalLightFunctionSet);
};
}  // namespace platform
}  // namespace ks
