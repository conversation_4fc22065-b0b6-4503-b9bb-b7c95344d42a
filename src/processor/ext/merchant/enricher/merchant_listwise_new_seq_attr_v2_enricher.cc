#include "dragon/src/processor/ext/merchant/enricher/merchant_listwise_new_seq_attr_v2_enricher.h"

#include <algorithm>
#include <set>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "base/time/time.h"
#include "ks/common_reco/util/key_sign_util.h"

namespace ks {
namespace platform {

bool MerchantListwiseNewSeqAttrV2Enricher::InitProcessor() {
  if (!ParseAttrsConfig("int_item_attrs_map", &int_item_attrs_map_)) {
    return false;
  }
  if (!ParseAttrsConfig("double_item_attrs_map", &double_item_attrs_map_)) {
    return false;
  }
  seq_item_attr_name_ = config()->GetString("seq_item_attr_name", "generated_variant_lists");
  return true;
}

bool MerchantListwiseNewSeqAttrV2Enricher::ParseAttrsConfig(
    const std::string& config_name,
    std::unordered_map<std::string, std::string>* attrs_map) {
  auto *attrs_config = config()->Get(config_name);
  if (!attrs_config || !attrs_config->IsArray()) {
    return false;
  }
  for (const auto* c : attrs_config->array()) {
    if (c) {
      if (!c->IsObject()) {
        return false;
      }
      const std::string& item_attr_name = c->GetString("from_item");
      const std::string& pack_as_name = c->GetString("pack_as", item_attr_name);
      attrs_map->emplace(item_attr_name, pack_as_name);
    }
  }
  return true;
}


void MerchantListwiseNewSeqAttrV2Enricher::Enrich(MutableRecoContextInterface *context,
                                                  RecoResultConstIter begin,
                                                  RecoResultConstIter end) {
  std::unordered_map<std::string, ItemAttr*> int_setter_map;
  for (const auto& p : int_item_attrs_map_) {
    int_setter_map.emplace(p.first, context->GetItemAttrAccessor(p.second));
  }
  std::unordered_map<std::string, ItemAttr*> double_setter_map;
  for (const auto& p : double_item_attrs_map_) {
    double_setter_map.emplace(p.first, context->GetItemAttrAccessor(p.second));
  }
  auto multi_model_req = context->GetIntCommonAttr("bh_mix_rank_fr_multi_model_emb").value_or(0);
  auto ad_pxtr_replace_enable = context->GetIntCommonAttr("mix_rank_ad_pxtr_replace_enable").value_or(0);
  auto ad_price_replace_enable = context->GetIntCommonAttr("mix_rank_ad_price_replace_enable").value_or(0);
  auto* seq_item_attr_accessor = context->GetItemAttrAccessor(seq_item_attr_name_);
  // default item attr: ctr, cvr, item_type, mix_item
  auto* ctr_accessor = context->GetItemAttrAccessor("ctr");
  auto* cvr_accessor = context->GetItemAttrAccessor("cvr");
  auto* price_accessor = context->GetItemAttrAccessor("price");
  auto* ad_ctr_accessor = context->GetItemAttrAccessor("mix_ad_ctr");
  auto* ad_cvr_accessor = context->GetItemAttrAccessor("mix_ad_cvr");
  auto* ad_price_accessor = context->GetItemAttrAccessor("mix_ad_ltv");
  auto* item_type_accessor = context->GetItemAttrAccessor("item_type");
  auto* item_id_accessor = context->GetItemAttrAccessor("item_id");
  auto* aid_accessor = context->GetItemAttrAccessor("aId");
  auto* is_ad_accessor = context->GetItemAttrAccessor("is_ad");
  auto* fr_item_id_embedding_accessor = context->GetItemAttrAccessor("fr_item_id_embedding");
  auto* trans_item_cate1_accessor = context->GetItemAttrAccessor("TransItemCate1Id");
  auto* trans_item_cate2_accessor = context->GetItemAttrAccessor("TransItemCate2Id");
  auto* trans_item_cate3_accessor = context->GetItemAttrAccessor("TransItemCate3Id");
  auto* trans_cid2_accessor = context->GetItemAttrAccessor("trans_cid2");
  auto* trans_cid3_accessor = context->GetItemAttrAccessor("trans_cid3");
  auto* trans_leaf_accessor = context->GetItemAttrAccessor("trans_leaf");
  auto* variant_tag_accessor = context->GetItemAttrAccessor("variant_tag");
  auto* brand_id_accessor = context->GetItemAttrAccessor("brand_id");
  auto* item_in_live_explaining_accessor = context->GetItemAttrAccessor("item_in_live_explaining");
  auto* seq_ctr_list_accessor = context->GetItemAttrAccessor("seq_ctr_list");
  auto* seq_cvr_list_accessor = context->GetItemAttrAccessor("seq_cvr_list");
  auto* seq_price_list_accessor = context->GetItemAttrAccessor("seq_price_list");
  auto* seq_mix_ad_ctr_list_accessor = context->GetItemAttrAccessor("seq_mix_ad_ctr_list");
  auto* seq_mix_ad_cvr_list_accessor = context->GetItemAttrAccessor("seq_mix_ad_cvr_list");
  auto* seq_mix_ad_ltv_list_accessor = context->GetItemAttrAccessor("seq_mix_ad_ltv_list");
  auto* seq_item_type_list_accessor = context->GetItemAttrAccessor("seq_item_type_list");
  auto* is_ad_list_accessor = context->GetItemAttrAccessor("is_ad_list");
  auto* fr_item_id_embedding_pack_list_accessor = context->GetItemAttrAccessor("item_id_embedding_pack_list");
  auto* seq_mix_item_id_list_accessor = context->GetItemAttrAccessor("seq_mix_item_id_list");
  auto* live_num_context_accessor = context->GetItemAttrAccessor("live_num_context");
  auto* maxctr_context_accessor = context->GetItemAttrAccessor("maxctr_context");
  auto* maxcvr_context_accessor = context->GetItemAttrAccessor("maxcvr_context");
  auto* live_maxctr_context_accessor = context->GetItemAttrAccessor("live_maxctr_context");
  auto* live_maxcvr_context_accessor = context->GetItemAttrAccessor("live_maxcvr_context");
  auto* goods_maxctr_context_accessor = context->GetItemAttrAccessor("goods_maxctr_context");
  auto* goods_maxcvr_context_accessor = context->GetItemAttrAccessor("goods_maxcvr_context");
  auto* avgctr_context_accessor = context->GetItemAttrAccessor("avgctr_context");
  auto* avgcvr_context_accessor = context->GetItemAttrAccessor("avgcvr_context");
  auto* live_avgctr_context_accessor = context->GetItemAttrAccessor("live_avgctr_context");
  auto* live_avgcvr_context_accessor = context->GetItemAttrAccessor("live_avgcvr_context");
  auto* goods_avgctr_context_accessor = context->GetItemAttrAccessor("goods_avgctr_context");
  auto* goods_avgcvr_context_accessor = context->GetItemAttrAccessor("goods_avgcvr_context");
  auto *good_item_pos_list_context_accessor = context->GetItemAttrAccessor("good_item_pos_list");
  auto *live_item_pos_list_context_accessor = context->GetItemAttrAccessor("live_item_pos_list");
  auto *dedup_trans_item_cate1_list_context_accessor =
      context->GetItemAttrAccessor("DedupTransItemCate1Id_list");
  auto *dedup_trans_item_cate2_list_context_accessor =
      context->GetItemAttrAccessor("DedupTransItemCate2Id_list");
  auto *dedup_trans_item_cate3_list_context_accessor =
      context->GetItemAttrAccessor("DedupTransItemCate3Id_list");
  auto *cate1_num_context_accessor = context->GetItemAttrAccessor("cate1_num");
  auto *cate2_num_context_accessor = context->GetItemAttrAccessor("cate2_num");
  auto *cate3_num_context_accessor = context->GetItemAttrAccessor("cate3_num");
  auto *trans_item_cate1_list_accessor = context->GetItemAttrAccessor("TransItemCate1Id_list");
  auto *trans_item_cate2_list_accessor = context->GetItemAttrAccessor("TransItemCate2Id_list");
  auto *trans_item_cate3_list_accessor = context->GetItemAttrAccessor("TransItemCate3Id_list");
  auto *trans_cid2_list_accessor = context->GetItemAttrAccessor("seq_trans_cid2_list");
  auto *trans_cid3_list_accessor = context->GetItemAttrAccessor("seq_trans_cid3_list");
  auto *trans_leaf_list_accessor = context->GetItemAttrAccessor("seq_trans_leaf_list");
  auto *variant_tag_list_accessor = context->GetItemAttrAccessor("seq_variant_tag_list");
  auto *brand_id_list_accessor = context->GetItemAttrAccessor("seq_brand_id_list");
  auto *item_in_live_explaining_list_accessor = context->GetItemAttrAccessor("item_in_live_explaining_list");
  auto *maxprice_context_accessor = context->GetItemAttrAccessor("maxprice_context");
  auto *live_maxprice_context_accessor = context->GetItemAttrAccessor("live_maxprice_context");
  auto *goods_maxprice_context_accessor = context->GetItemAttrAccessor("goods_maxprice_context");
  auto *avgprice_context_accessor = context->GetItemAttrAccessor("avgprice_context");
  auto *live_avgprice_context_accessor = context->GetItemAttrAccessor("live_avgprice_context");
  auto *goods_avgprice_context_accessor = context->GetItemAttrAccessor("goods_avgprice_context");
  std::unordered_map<int64, size_t> item_key_index_map;
  std::vector<ItemInfo> total_item_infos;
  item_key_index_map.reserve(50);
  total_item_infos.reserve(50);
  std::vector<std::vector<size_t>> indexes(std::distance(begin, end));
  size_t item_index = 0;
  std::for_each(
      begin, end,
      [&, this, context](const CommonRecoResult &result) {
    auto list_candidates = context->GetIntListItemAttr(result, seq_item_attr_accessor);
    if (!list_candidates) {
      ++item_index;
      return;
    }
    const auto& candidates = (*list_candidates);
    size_t len = candidates.size();
    if (len > 10) {
      len = 10;
    }
    indexes[item_index].reserve(len);
    for (size_t i = 0; i < len; ++i) {
      auto iter = item_key_index_map.find(candidates[i]);
      if (iter != item_key_index_map.end()) {
        indexes[item_index].emplace_back(iter->second);
      } else {
        indexes[item_index].emplace_back(total_item_infos.size());
        item_key_index_map.emplace(candidates[i], total_item_infos.size());
        ItemInfo item_info;
        item_info.item_type = context->GetIntItemAttr(candidates[i], item_type_accessor).value_or(0);
        item_info.is_ad = context->GetIntItemAttr(candidates[i], is_ad_accessor).value_or(0);
        if (multi_model_req) {
          auto span_emb = context->GetDoubleListItemAttr(candidates[i], fr_item_id_embedding_accessor);
          if (span_emb.has_value() && span_emb.value().size() == 64) {
            item_info.fr_item_id_embedding.assign(span_emb->begin(), span_emb->end());
          } else {
            item_info.fr_item_id_embedding.resize(64, 0.0);
          }
        }
        item_info.item_id = context->GetIntItemAttr(candidates[i], item_id_accessor).value_or(0);
        item_info.aid = context->GetIntItemAttr(candidates[i], aid_accessor).value_or(0);
        item_info.price = context->GetIntItemAttr(candidates[i], price_accessor).value_or(0);
        item_info.trans_item_cid1 =
            context->GetIntItemAttr(candidates[i], trans_item_cate1_accessor).value_or(0);
        item_info.trans_item_cid2 =
            context->GetIntItemAttr(candidates[i], trans_item_cate2_accessor).value_or(0);
        item_info.trans_item_cid3 =
            context->GetIntItemAttr(candidates[i], trans_item_cate3_accessor).value_or(0);
        item_info.item_in_live_explaining =
            context->GetIntItemAttr(candidates[i], item_in_live_explaining_accessor).value_or(0);
        item_info.ctr = context->GetDoubleItemAttr(candidates[i], ctr_accessor).value_or(0.0);
        item_info.cvr = context->GetDoubleItemAttr(candidates[i], cvr_accessor).value_or(0.0);
        item_info.mix_ad_ctr = context->GetDoubleItemAttr(candidates[i], ad_ctr_accessor).value_or(0.0);
        item_info.mix_ad_cvr = context->GetDoubleItemAttr(candidates[i], ad_cvr_accessor).value_or(0.0);
        item_info.mix_ad_ltv = context->GetIntItemAttr(candidates[i], ad_price_accessor).value_or(0);
        if (ad_pxtr_replace_enable == 1 && item_info.is_ad == 1) {
          item_info.ctr = item_info.mix_ad_ctr;
          item_info.cvr = item_info.mix_ad_cvr;
          item_info.price = item_info.mix_ad_ltv;
        }
        if (ad_price_replace_enable == 1 && item_info.is_ad == 1 && item_info.price == 0) {
          item_info.price = item_info.mix_ad_ltv;
        }
        item_info.trans_cid2 =
            context->GetIntItemAttr(candidates[i], trans_cid2_accessor).value_or(0);
        item_info.trans_cid3 =
            context->GetIntItemAttr(candidates[i], trans_cid3_accessor).value_or(0);
        item_info.trans_leaf =
            context->GetIntItemAttr(candidates[i], trans_leaf_accessor).value_or(0);
        item_info.variant_tag =
            context->GetIntItemAttr(candidates[i], variant_tag_accessor).value_or(0);
        item_info.brand_id =
            context->GetIntItemAttr(candidates[i], brand_id_accessor).value_or(0);
        total_item_infos.emplace_back(std::move(item_info));
      }
    }
    ++item_index;
  });
  // reset item_index
  item_index = 0;
  std::for_each(
      begin, end,
      [&](const CommonRecoResult &result) {
    const auto& candidates = indexes[item_index];
    if (candidates.empty()) {
      ++item_index;
      return;
    }
    size_t len = candidates.size();
    std::vector<double> flattened;
    flattened.reserve(len * 64);
    std::vector<double> seq_ctr_list(len, 0.0);
    std::vector<double> seq_cvr_list(len, 0.0);
    std::vector<int64> seq_price_list(len, 0);
    std::vector<double> seq_mix_ad_ctr_list(len, 0.0);
    std::vector<double> seq_mix_ad_cvr_list(len, 0.0);
    std::vector<int64> seq_mix_ad_ltv_list(len, 0);
    std::vector<int64> seq_item_type_list(len, 0);
    std::vector<int64> is_ad_list(len, 0);
    std::vector<int64> seq_mix_item_id_list(len, 0);
    std::vector<int64> good_item_pos_list;
    std::vector<int64> live_item_pos_list;
    good_item_pos_list.reserve(len);
    live_item_pos_list.reserve(len);
    std::vector<int64> trans_item_cate1_list(len, 0);
    std::vector<int64> trans_item_cate2_list(len, 0);
    std::vector<int64> trans_item_cate3_list(len, 0);
    std::vector<int64> item_in_live_explaining_list(len, 0);
    std::vector<int64> trans_cid2_list(len, 0);
    std::vector<int64> trans_cid3_list(len, 0);
    std::vector<int64> trans_leaf_list(len, 0);
    std::vector<int64> variant_tag_list(len, 0);
    std::vector<int64> brand_id_list(len, 0);
    std::unordered_set<int64> trans_item_cate1_set;
    std::unordered_set<int64> trans_item_cate2_set;
    std::unordered_set<int64> trans_item_cate3_set;
    std::vector<int64> uniq_trans_item_cate1_list;
    std::vector<int64> uniq_trans_item_cate2_list;
    std::vector<int64> uniq_trans_item_cate3_list;
    trans_item_cate1_set.reserve(len);
    trans_item_cate2_set.reserve(len);
    trans_item_cate3_set.reserve(len);
    uniq_trans_item_cate1_list.reserve(len);
    uniq_trans_item_cate2_list.reserve(len);
    uniq_trans_item_cate3_list.reserve(len);
    int64 live_num_context = 0;
    double maxctr_context = 0.0;
    double maxcvr_context = 0.0;
    double live_maxctr_context = 0.0;
    double live_maxcvr_context = 0.0;
    double goods_maxctr_context = 0.0;
    double goods_maxcvr_context = 0.0;
    double avgctr_context = 0.0;
    double avgcvr_context = 0.0;
    double live_avgctr_context = 0.0;
    double live_avgcvr_context = 0.0;
    double goods_avgctr_context = 0.0;
    double goods_avgcvr_context = 0.0;
    double maxprice_context = 0.0;
    double live_maxprice_context = 0.0;
    double goods_maxprice_context = 0.0;
    double avgprice_context = 0.0;
    double live_avgprice_context = 0.0;
    double goods_avgprice_context = 0.0;
    for (size_t i = 0; i < len; ++i) {
      const auto& item_info = total_item_infos[candidates[i]];
      seq_ctr_list[i] = item_info.ctr;
      seq_cvr_list[i] = item_info.cvr;
      seq_price_list[i] = item_info.price;
      seq_mix_ad_ctr_list[i] = item_info.mix_ad_ctr;
      seq_mix_ad_cvr_list[i] = item_info.mix_ad_cvr;
      seq_mix_ad_ltv_list[i] = item_info.mix_ad_ltv;
      trans_item_cate1_list[i] = item_info.trans_item_cid1;
      if (trans_item_cate1_set.insert(trans_item_cate1_list[i]).second) {
        uniq_trans_item_cate1_list.emplace_back(trans_item_cate1_list[i]);
      }
      trans_item_cate2_list[i] = item_info.trans_item_cid2;
      if (trans_item_cate2_set.insert(trans_item_cate2_list[i]).second) {
        uniq_trans_item_cate2_list.emplace_back(trans_item_cate2_list[i]);
      }
      trans_item_cate3_list[i] = item_info.trans_item_cid3;
      if (trans_item_cate3_set.insert(trans_item_cate3_list[i]).second) {
        uniq_trans_item_cate3_list.emplace_back(trans_item_cate3_list[i]);
      }
      item_in_live_explaining_list[i] = item_info.item_in_live_explaining;
      seq_item_type_list[i] = item_info.item_type;
      trans_cid2_list[i] = item_info.trans_cid2;
      trans_cid3_list[i] = item_info.trans_cid3;
      trans_leaf_list[i] = item_info.trans_leaf;
      variant_tag_list[i] = item_info.variant_tag;
      brand_id_list[i] = item_info.brand_id;
      is_ad_list[i] = item_info.is_ad;
      if (multi_model_req) {
        flattened.insert(flattened.end(), item_info.fr_item_id_embedding.begin(),
                         item_info.fr_item_id_embedding.end());
      }
      if (seq_item_type_list[i] == 1) {
        live_item_pos_list.emplace_back(i);
      } else {
        good_item_pos_list.emplace_back(i);
      }
      if (seq_ctr_list[i] > maxctr_context) {
        maxctr_context = seq_ctr_list[i];
      }
      if (seq_cvr_list[i] > maxcvr_context) {
        maxcvr_context = seq_cvr_list[i];
      }
      if (seq_price_list[i] > maxprice_context) {
        maxprice_context = seq_price_list[i];
      }
      avgctr_context += seq_ctr_list[i];
      avgcvr_context += seq_cvr_list[i];
      avgprice_context += seq_price_list[i];
      if (seq_item_type_list[i] == 1) {
        seq_mix_item_id_list[i] = item_info.aid;
        live_num_context += 1;
        if (seq_ctr_list[i] > live_maxctr_context) {
          live_maxctr_context = seq_ctr_list[i];
        }
        if (seq_cvr_list[i] > live_maxcvr_context) {
          live_maxcvr_context = seq_cvr_list[i];
        }
        if (seq_price_list[i] > live_maxprice_context) {
          live_maxprice_context = seq_price_list[i];
        }
        live_avgctr_context += seq_ctr_list[i];
        live_avgcvr_context += seq_cvr_list[i];
        live_avgprice_context += seq_price_list[i];
      } else {
        seq_mix_item_id_list[i] = item_info.item_id;
        if (seq_ctr_list[i] > goods_maxctr_context) {
          goods_maxctr_context = seq_ctr_list[i];
        }
        if (seq_cvr_list[i] > goods_maxcvr_context) {
          goods_maxcvr_context = seq_cvr_list[i];
        }
        if (seq_price_list[i] > goods_maxprice_context) {
          goods_maxprice_context = seq_price_list[i];
        }
        goods_avgctr_context += seq_ctr_list[i];
        goods_avgcvr_context += seq_cvr_list[i];
        goods_avgprice_context += seq_price_list[i];
      }
    }
    if (len > 0) {
      avgctr_context /= len;
      avgcvr_context /= len;
      avgprice_context /= len;
    }
    if (live_num_context > 0) {
      live_avgctr_context /= live_num_context;
      live_avgcvr_context /= live_num_context;
      live_avgprice_context /= live_num_context;
    }
    int64 goods_num_context = len - live_num_context;
    if (goods_num_context > 0) {
      goods_avgctr_context /= goods_num_context;
      goods_avgcvr_context /= goods_num_context;
      goods_avgprice_context /= goods_num_context;
    }
    ++item_index;
    context->SetDoubleListItemAttr(result, seq_ctr_list_accessor, std::move(seq_ctr_list));
    context->SetDoubleListItemAttr(result, seq_cvr_list_accessor, std::move(seq_cvr_list));
    context->SetIntListItemAttr(result, seq_price_list_accessor, std::move(seq_price_list));
    context->SetDoubleListItemAttr(result, seq_mix_ad_ctr_list_accessor,
                                   std::move(seq_mix_ad_ctr_list));
    context->SetDoubleListItemAttr(result, seq_mix_ad_cvr_list_accessor,
                                   std::move(seq_mix_ad_cvr_list));
    context->SetIntListItemAttr(result, seq_mix_ad_ltv_list_accessor,
                                std::move(seq_mix_ad_ltv_list));
    context->SetIntListItemAttr(result, seq_item_type_list_accessor, std::move(seq_item_type_list));
    context->SetIntListItemAttr(result, is_ad_list_accessor, std::move(is_ad_list));
    context->SetIntListItemAttr(result, seq_mix_item_id_list_accessor, std::move(seq_mix_item_id_list));
    context->SetIntItemAttr(result, live_num_context_accessor, live_num_context);
    context->SetDoubleItemAttr(result, maxctr_context_accessor, maxctr_context);
    context->SetDoubleItemAttr(result, maxcvr_context_accessor, maxcvr_context);
    context->SetDoubleItemAttr(result, live_maxctr_context_accessor, live_maxctr_context);
    context->SetDoubleItemAttr(result, live_maxcvr_context_accessor, live_maxcvr_context);
    context->SetDoubleItemAttr(result, goods_maxctr_context_accessor, goods_maxctr_context);
    context->SetDoubleItemAttr(result, goods_maxcvr_context_accessor, goods_maxcvr_context);
    context->SetDoubleItemAttr(result, avgctr_context_accessor, avgctr_context);
    context->SetDoubleItemAttr(result, avgcvr_context_accessor, avgcvr_context);
    context->SetDoubleItemAttr(result, live_avgctr_context_accessor, live_avgctr_context);
    context->SetDoubleItemAttr(result, live_avgcvr_context_accessor, live_avgcvr_context);
    context->SetDoubleItemAttr(result, goods_avgctr_context_accessor, goods_avgctr_context);
    context->SetDoubleItemAttr(result, goods_avgcvr_context_accessor, goods_avgcvr_context);
    context->SetIntListItemAttr(result, trans_item_cate1_list_accessor, std::move(trans_item_cate1_list));
    context->SetIntListItemAttr(result, trans_item_cate2_list_accessor, std::move(trans_item_cate2_list));
    context->SetIntListItemAttr(result, trans_item_cate3_list_accessor, std::move(trans_item_cate3_list));
    context->SetIntListItemAttr(result, trans_cid2_list_accessor, std::move(trans_cid2_list));
    context->SetIntListItemAttr(result, trans_cid3_list_accessor, std::move(trans_cid3_list));
    context->SetIntListItemAttr(result, trans_leaf_list_accessor, std::move(trans_leaf_list));
    context->SetIntListItemAttr(result, variant_tag_list_accessor, std::move(variant_tag_list));
    context->SetIntListItemAttr(result, brand_id_list_accessor, std::move(brand_id_list));
    context->SetIntListItemAttr(result, item_in_live_explaining_list_accessor,
                                std::move(item_in_live_explaining_list));
    context->SetIntItemAttr(result, cate1_num_context_accessor, uniq_trans_item_cate1_list.size());
    context->SetIntItemAttr(result, cate2_num_context_accessor, uniq_trans_item_cate2_list.size());
    context->SetIntItemAttr(result, cate3_num_context_accessor, uniq_trans_item_cate3_list.size());
    context->SetIntListItemAttr(result, dedup_trans_item_cate1_list_context_accessor,
                                std::move(uniq_trans_item_cate1_list));
    context->SetIntListItemAttr(result, dedup_trans_item_cate2_list_context_accessor,
                                std::move(uniq_trans_item_cate2_list));
    context->SetIntListItemAttr(result, dedup_trans_item_cate3_list_context_accessor,
                                std::move(uniq_trans_item_cate3_list));
    context->SetIntListItemAttr(result, good_item_pos_list_context_accessor,
                                std::move(good_item_pos_list));
    context->SetIntListItemAttr(result, live_item_pos_list_context_accessor,
                                std::move(live_item_pos_list));
    context->SetDoubleItemAttr(result, goods_avgprice_context_accessor, goods_avgprice_context);
    context->SetDoubleItemAttr(result, live_avgprice_context_accessor, live_avgprice_context);
    context->SetDoubleItemAttr(result, avgprice_context_accessor, avgprice_context);
    context->SetDoubleItemAttr(result, goods_maxprice_context_accessor, goods_maxprice_context);
    context->SetDoubleItemAttr(result, live_maxprice_context_accessor, live_maxprice_context);
    context->SetDoubleItemAttr(result, maxprice_context_accessor, maxprice_context);
    context->SetDoubleListItemAttr(result, fr_item_id_embedding_pack_list_accessor, std::move(flattened));
  });
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, MerchantListwiseNewSeqAttrV2Enricher, MerchantListwiseNewSeqAttrV2Enricher)

}  // namespace platform
}  // namespace ks
