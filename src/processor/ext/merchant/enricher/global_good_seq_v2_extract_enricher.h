#pragma once

#include <map>
#include <memory>
#include <string>
#include <utility>
#include <vector>
#include "base/container/btree_set.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/processor/base/common_reco_base_processor.h"

namespace ks {
namespace platform {

class GlobalGoodSeqV2ExtractEnricher : public CommonRecoBaseEnricher {
 public:
  GlobalGoodSeqV2ExtractEnricher() {}
  ~GlobalGoodSeqV2ExtractEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;

 private:
  std::string request_time_;
  std::string input_good_click_colossus_item_id_;
  std::string input_good_click_colossus_timestamp_;
  std::string input_good_click_colossus_real_price_;
  std::string input_good_click_colossus_click_from_;
  std::string input_good_click_colossus_click_flow_type_;
  std::string input_good_click_colossus_label_;
  std::string input_good_click_colossus_category_;
  std::string input_good_click_colossus_seller_id_;
  std::string input_good_click_colossus_real_seller_id_;
  std::string input_good_order_colossus_item_id_;
  std::string input_good_order_colossus_timestamp_;
  std::string input_good_order_colossus_order_price_;
  std::string input_good_order_colossus_order_status_;
  std::string input_good_order_colossus_item_price_;
  std::string input_good_order_colossus_buy_from_;
  std::string input_good_order_colossus_buy_flow_type_;
  std::string input_good_order_colossus_express_cost_;
  std::string input_good_order_colossus_coupon_platform_amt_;
  std::string input_good_order_colossus_full_reduce_amt_;
  std::string input_good_order_colossus_category_;
  std::string input_good_order_colossus_seller_id_;
  std::string input_good_order_colossus_real_seller_id_;
  std::string input_aId_;
  std::string input_sCartItemCate1IdList_;
  std::string output_global_good_colossus_common_item_id_;
  std::string output_global_good_colossus_common_lag_hour_;
  std::string output_global_good_colossus_common_lag_day_;
  std::string output_global_good_colossus_common_item_price_;
  std::string output_global_good_colossus_common_from_live_;
  std::string output_global_good_colossus_common_from_photo_;
  std::string output_global_good_colossus_common_flow_type_;
  std::string output_global_good_colossus_common_label_;
  std::string output_global_good_colossus_common_cate1_;
  std::string output_global_good_colossus_common_cate2_;
  std::string output_global_good_colossus_common_cate3_;
  std::string output_global_good_colossus_common_seller_id_;
  std::string output_global_good_colossus_common_real_seller_id_;
  std::string output_global_good_colossus_common_order_order_price_;
  std::string output_global_good_colossus_common_order_order_status_;
  std::string output_global_good_colossus_common_order_express_cost_;
  std::string output_global_good_colossus_common_order_coupon_platform_amt_;
  std::string output_global_good_colossus_common_order_full_reduce_amt_;

  std::string output_global_good_colossus_item_item_id_;
  std::string output_global_good_colossus_item_lag_hour_;
  std::string output_global_good_colossus_item_lag_day_;
  std::string output_global_good_colossus_item_item_price_;
  std::string output_global_good_colossus_item_from_live_;
  std::string output_global_good_colossus_item_from_photo_;
  std::string output_global_good_colossus_item_flow_type_;
  std::string output_global_good_colossus_item_label_;
  std::string output_global_good_colossus_item_cate1_;
  std::string output_global_good_colossus_item_cate2_;
  std::string output_global_good_colossus_item_cate3_;
  std::string output_global_good_colossus_item_seller_id_;
  std::string output_global_good_colossus_item_real_seller_id_;
  std::string output_global_good_colossus_item_order_order_price_;
  std::string output_global_good_colossus_item_order_order_status_;
  std::string output_global_good_colossus_item_order_express_cost_;
  std::string output_global_good_colossus_item_order_coupon_platform_amt_;
  std::string output_global_good_colossus_item_order_full_reduce_amt_;

  void ProcessRecoResults(
    MutableRecoContextInterface* context,
    RecoResultConstIter begin, RecoResultConstIter end,
    int64 request_time,
    absl::Span<const int64> good_click_colossus_item_id,
    absl::Span<const int64> good_click_colossus_timestamp,
    absl::Span<const int64> good_click_colossus_real_price,
    absl::Span<const int64> good_click_colossus_click_from,
    absl::Span<const int64> good_click_colossus_click_flow_type,
    absl::Span<const int64> good_click_colossus_label,
    absl::Span<const int64> good_click_colossus_category,
    absl::Span<const int64> good_click_colossus_seller_id,
    absl::Span<const int64> good_click_colossus_real_seller_id,
    absl::Span<const int64> good_order_colossus_item_id,
    absl::Span<const int64> good_order_colossus_timestamp,
    absl::Span<const int64> good_order_colossus_order_price,
    absl::Span<const int64> good_order_colossus_order_status,
    absl::Span<const int64> good_order_colossus_item_price,
    absl::Span<const int64> good_order_colossus_buy_from,
    absl::Span<const int64> good_order_colossus_buy_flow_type,
    absl::Span<const int64> good_order_colossus_express_cost,
    absl::Span<const int64> good_order_colossus_coupon_platform_amt,
    absl::Span<const int64> good_order_colossus_full_reduce_amt,
    absl::Span<const int64> good_order_colossus_category,
    absl::Span<const int64> good_order_colossus_seller_id,
    absl::Span<const int64> good_order_colossus_real_seller_id);

  DISALLOW_COPY_AND_ASSIGN(GlobalGoodSeqV2ExtractEnricher);
};

}  // namespace platform
}  // namespace ks
