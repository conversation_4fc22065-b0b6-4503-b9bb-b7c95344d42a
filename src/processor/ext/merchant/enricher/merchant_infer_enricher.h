#pragma once

#include <kess/rpc/grpc/grpc_client_builder.h>
#include <atomic>
#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/processor/ext/merchant/util/RecallWithEarlyStop/src/thread_pool_management.h"
#include "dragon/src/util/service_black_list_util.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.pb.h"
#include "serving_base/utility/timer.h"

namespace ks {
namespace platform {

struct SendCommonAttr {
  SendCommonAttr(const std::string &name, const std::string &alias, bool readonly = false)
      : name(name), alias(alias), readonly(readonly) {}

  std::string name;
  std::string alias;
  bool readonly = false;
};

class MerchantInferEnricher : public CommonRecoBaseEnricher {
 public:
  MerchantInferEnricher() {}

  bool IsAsync() const override {
    return true;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  const base::Json *ItemFromTables() const final {
    return config()->Get("item_from_tables");
  }

 private:
  bool InitProcessor() override {
    kess_cluster_ = config()->GetString("kess_cluster", ks::kess::scheduler::Constants::DEFAULT_CLUSTER);

    shard_num_ = config()->GetInt("shard_num", 1);
    shard_id_offset_ = config()->GetInt("shard_id_offset", 0);
    consistent_hash_ = config()->GetBoolean("consistent_hash", false);
    rpc_compress_ = config()->GetBoolean("rpc_compress", false);
    expect_task_num_per_worker_ = config()->GetInt("expect_task_num_per_worker", 4);
    // 异步资源初始化
    if (!InitAsyncResource()) {
      CL_LOG(ERROR) << "MerchantInferEnricher init async resource failed";
      return false;
    }

    if (shard_num_ <= 0) {
      LOG(ERROR) << "MerchantInferEnricher init failed! shard_num should be > 0";
      return false;
    }

    if (shard_id_offset_ < 0) {
      LOG(ERROR) << "MerchantInferEnricher init failed! shard_num should be >= 0";
      return false;
    }

    if (!ParseAttrsConfig("send_item_attrs", &send_item_attrs_, "No item attr will be sent") ||
        !ParseCommonAttrsConfig("send_common_attrs", &send_common_attrs_, "No common attr will be sent") ||
        !ParseAttrsConfig("recv_item_attrs", &recv_item_attrs_, "All item attrs will be dropped") ||
        !ParseAttrsConfig("recv_common_attrs", &recv_common_attrs_, "All common attrs will be dropped")) {
      return false;
    }

    for (const auto &attr : send_common_attrs_) {
      send_common_attrs_set_.emplace(attr.name);
    }

    if (ItemFromTables()) {
      check_multi_table_ = true;
    }

    RecoUtil::ExtractStringSetFromJsonConfig(config()->Get("exclude_common_attrs"), &exclude_common_attrs_);
    // 是否使用 grpc_samplelist 的 user attr
    use_sample_list_attr_flag_ = config()->GetBoolean("use_sample_list_attr_flag", false);
    // use_sample_list_attr_flatten 为旧配置名称（含义取反了），需兼容处理
    flatten_sample_list_attr_ = config()->GetBoolean("flatten_sample_list_attr", false) ||
                                !config()->GetBoolean("use_sample_list_attr_flatten", true);
    sample_list_common_attr_key_ = config()->GetString("sample_list_common_attr_key");
    dynamic_common_attrs_names_ = config()->GetString("dynamic_send_common_attrs");
    dynamic_item_attrs_names_ = config()->GetString("dynamic_send_item_attrs");
    sample_list_ptr_attr_ = config()->GetString("sample_list_ptr_attr");
    flatten_sample_list_attr_to_ = config()->GetString("flatten_sample_list_attr_to", "kuiba_user_attrs");
    request_info_ = "kess_cluster: " + kess_cluster_ + ", shard_num: " + std::to_string(shard_num_) +
                    ", shard_id_offset: " + std::to_string(shard_id_offset_);

    send_common_attrs_in_request_ = config()->GetBoolean("send_common_attrs_in_request", false);
    infer_output_type_ = config()->GetInt("infer_output_type", -1);
    for_predict_ = config()->GetBoolean("for_predict", true);

    use_packed_item_attr_config_ = config()->Get("use_packed_item_attr");

    return true;
  }

  bool ParseAttrsConfig(const std::string &config_name,
                        std::unordered_map<std::string, std::string> *attrs_map,
                        const std::string &default_action_description) {
    auto *attrs_config = config()->Get(config_name);
    if (attrs_config && attrs_config->IsArray()) {
      for (const auto *c : attrs_config->array()) {
        if (c->IsObject()) {
          const std::string &attr_name = c->GetString("name");
          if (attr_name.empty()) {
            LOG(ERROR) << "MerchantInferEnricher init failed! name is required for " << config_name;
            return false;
          }

          const std::string &alias = c->GetString("as", attr_name);
          attrs_map->emplace(attr_name, alias);
        } else if (c->IsString()) {
          const std::string &attr_name = c->StringValue();
          if (attr_name.empty()) {
            LOG(ERROR) << "MerchantInferEnricher init failed! name should not "
                          "be empty for "
                       << config_name;
            return false;
          }
          attrs_map->emplace(attr_name, attr_name);
        }
      }
    } else {
      LOG_EVERY_N(INFO, 100) << "merchant_infer_enrich " << config_name << " is empty, "
                             << default_action_description;
    }
    return true;
  }

  bool ParseCommonAttrsConfig(const std::string &config_name, std::vector<SendCommonAttr> *attrs_vec,
                              const std::string &default_action_description) {
    auto *attrs_config = config()->Get(config_name);
    if (attrs_config && attrs_config->IsArray()) {
      for (const auto *c : attrs_config->array()) {
        if (c->IsObject()) {
          const std::string &attr_name = c->GetString("name");
          if (attr_name.empty()) {
            LOG(ERROR) << "MerchantInferEnricher init failed! name is required for " << config_name;
            return false;
          }

          const std::string &alias = c->GetString("as", attr_name);
          bool readonly = c->GetBoolean("readonly", false);
          attrs_vec->emplace_back(attr_name, alias, readonly);
        } else if (c->IsString()) {
          const std::string &attr_name = c->StringValue();
          if (attr_name.empty()) {
            LOG(ERROR) << "MerchantInferEnricher init failed! name should not "
                          "be empty for "
                       << config_name;
            return false;
          }
          attrs_vec->emplace_back(attr_name, attr_name);
        }
      }
    } else {
      LOG_EVERY_N(WARNING, 100) << "merchant_infer_enrich " << config_name << " is empty, "
                                << default_action_description;
    }
    return true;
  }
  bool GenerateSampleListAttr(MutableRecoContextInterface *context,
                              ::google::protobuf::RepeatedPtrField<::kuiba::SampleAttr> *attr_vc);
  bool FillCommonAttrFromSampleList(MutableRecoContextInterface *context, CommonRecoRequest *request_ptr);
  int64 CheckAndGetTimeoutMs(MutableRecoContextInterface *context, const std::string &kess_service);

 private:
  struct SendItemAttr {
    absl::string_view name;
    absl::string_view as;
    ItemAttr *accessor = nullptr;
  };

  std::string kess_cluster_;

  int shard_num_;
  int shard_id_offset_;

  bool consistent_hash_ = false;
  bool rpc_compress_ = false;

  // sample list attr 相关
  bool use_sample_list_attr_flag_ = false;
  bool flatten_sample_list_attr_ = false;
  std::string sample_list_common_attr_key_;
  std::string sample_list_ptr_attr_;
  std::string flatten_sample_list_attr_to_;

  // 是否使用 request 携带中的 common_attr
  bool send_common_attrs_in_request_ = false;
  const base::Json *use_packed_item_attr_config_ = nullptr;
  // 记录所有不支持 packed item attr 的下游服务名
  folly::F14FastSet<std::string> packed_item_attr_no_support_services_;
  // 指定 infer server 返回 pxtr 的 output_type 存储格式
  int infer_output_type_ = -1;

  bool check_multi_table_ = false;

  // request
  std::vector<SendCommonAttr> send_common_attrs_;
  std::unordered_map<std::string, std::string> send_item_attrs_;
  std::unordered_set<std::string> exclude_common_attrs_;
  absl::flat_hash_set<std::string> send_common_attrs_set_;
  absl::flat_hash_set<std::string> sample_list_attrs_;

  // dynamic attr
  std::string dynamic_item_attrs_names_;
  std::string dynamic_common_attrs_names_;
  std::vector<SendItemAttr> dynamic_send_item_attr_vec_;

  // response
  std::unordered_map<std::string, std::string> recv_item_attrs_;
  std::unordered_map<std::string, std::string> recv_common_attrs_;

  std::vector<SendItemAttr> send_item_attr_vec_;

  std::string request_info_;

  bool for_predict_ = true;

  bool InitAsyncResource() {
    static std::once_flag once_flag;
    rpc_sub_task_ctx_manager_ptr_ = std::make_unique<RpcSubTaskContextManager>(64);
    google::protobuf::ArenaOptions options;
    options.start_block_size = 2 * 1024 * 1024;  // 2MB
    options.max_block_size = 4 * 1024 * 1024;    // 4MB
    arena_ptr_ = std::make_unique<google::protobuf::Arena>(options);
    std::call_once(once_flag, [this]() { MerchantThreadPoolManager::GetInstance()->Initialize(); });
    auto start = std::chrono::steady_clock::now();
    // 如果资源超过 5 min 还没有准备好，则认为 init 失败
    while (!MerchantThreadPoolManager::GetInstance()->ThreadPoolResourceIsReady()) {
      std::this_thread::sleep_for(std::chrono::milliseconds(100));
      auto wait_time_ms =
          std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now() - start);
      if ((wait_time_ms.count() / 1000.0) >= 300) {
        CL_LOG(ERROR) << "Merchant infer enrich init merchant async thread pool failed, wait "
                      << wait_time_ms.count() << " ms.";
        return false;
      }
    }
    auto total_time_ms =
        std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now() - start);
    CL_LOG(INFO) << "Merchant infer enrich init merchant async thread pool, every op handle "
                 << expect_task_num_per_worker_ << " task per workers, use " << total_time_ms.count()
                 << " ms.";
    return true;
  }

  AsyncTaskThreadPool<bool> *GetAsyncPool(MutableRecoContextInterface *context) {
    CommonRecoContext *common_reco_context = static_cast<CommonRecoContext *>(context);
    auto thread_pool_set = MerchantThreadPoolManager::GetInstance()->GetThreadPoolSet(
        FLAGS_enable_numa_aware ? common_reco_context->GetNumaId()
                                : common_reco_context->GetCpuAffinityInfo()->numa_node_id);
    if (thread_pool_set == nullptr) {
      return nullptr;
    }
    return thread_pool_set->GetThreadPool(0);
  }

  struct RpcSubTaskContext {
    int64 ctx_id;
    explicit RpcSubTaskContext(int context_id) : ctx_id(context_id) {}
    CommonRecoRequest *request_ptr = nullptr;
    CommonRecoResponse *response_ptr = nullptr;
    folly::F14FastMap<uint64, std::vector<CommonRecoResult>> item_key_mapping;
    std::vector<std::vector<CommonRecoResult>> items_in_shard;
    std::vector<::ks::kess::rpc::grpc::Future<CommonRecoResponse *>> future_in_shard;
    void Initialize(RecoResultConstIter begin, RecoResultConstIter end, CommonRecoRequest *request_ptr,
                    const int64 &shard_num, ::google::protobuf::Arena *arena_ptr, bool is_async) {
      this->items_in_shard.resize(shard_num);
      this->future_in_shard.resize(shard_num);
      this->response_ptr = ::google::protobuf::Arena::CreateMessage<CommonRecoResponse>(arena_ptr);
      // 异步需要 copy 同步直接指针
      if (is_async) {
        this->request_ptr = ::google::protobuf::Arena::CreateMessage<CommonRecoRequest>(arena_ptr);
        this->request_ptr->CopyFrom(*request_ptr);
      } else {
        this->request_ptr = request_ptr;
      }
      this->item_key_mapping = folly::F14FastMap<uint64, std::vector<CommonRecoResult>>();
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        uint64 key = result.item_key;
        this->items_in_shard[key % this->items_in_shard.size()].emplace_back(result);
      });
    }
  };
  class RpcSubTaskContextManager {
   public:
    explicit RpcSubTaskContextManager(int context_num) {
      ctx_pool_.reserve(context_num);
      ResetContextPool(context_num);
      CL_LOG(INFO) << "RpcSubTaskContextManager init success, current have " << ctx_pool_.size()
                   << " context.";
    }
    // 根据当前请求需要处理的分区个数判断是否需要扩容
    void Reset(const int64 &current_pv_task_num) {
      // 2 倍扩容
      if (current_pv_task_num > ctx_pool_.capacity()) {
        CL_LOG(WARNING) << "Insufficient context resources, expansion triggered from " << ctx_pool_.capacity()
                        << " to " << current_pv_task_num * 2;
        ctx_pool_.reserve(current_pv_task_num * 2);
      }
      ResetContextPool(ctx_pool_.capacity());
    }

    int64 GetUsedContextNum() {
      return last_ctx_id_.load();
    }

    // 线程安全
    // TODO(panyuchen): 此处的扩容逻辑可以无锁，为了保证安全暂时先加读写锁
    RpcSubTaskContext &GetContext() {
      int current_ctx_id = last_ctx_id_.fetch_add(1);
      return ctx_pool_[current_ctx_id];
    }

    RpcSubTaskContext &GetContextById(int64 context_id) {
      // 使用 context id 获取 context 为避免越界,调用时需通过 GetUsedContextNum 作为边界限制
      return ctx_pool_[context_id];
    }

   private:
    std::atomic<int64> last_ctx_id_{0};
    std::vector<RpcSubTaskContext> ctx_pool_;

    void ResetContextPool(const int64 &context_num) {
      last_ctx_id_.store(0);
      ctx_pool_.clear();
      for (int i = 0; i < context_num; ++i) {
        ctx_pool_.emplace_back(RpcSubTaskContext(i));
      }
    }
  };
  template <typename SubTask>
  void DispatchSubTaskByPartitionSize(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                      RecoResultConstIter end, const int64 &internal_partiton_size,
                                      SubTask &&sub_task_func) {
    auto item_num = std::distance(begin, end);
    std::vector<int> partition_size_vec = RecoUtil::BalancedPartition(internal_partiton_size, item_num);
    RecoResultConstIter partition_iter = begin;
    int64 partition_num = partition_size_vec.size();
    std::vector<std::pair<RecoResultConstIter, RecoResultConstIter>> partition_sub_task_vec;
    partition_sub_task_vec.reserve(partition_num);
    // 任务划分
    for (int partition_id = 0; partition_id < partition_num; ++partition_id) {
      partition_sub_task_vec.emplace_back(partition_iter, partition_iter + partition_size_vec[partition_id]);
      partition_iter += partition_size_vec[partition_id];
    }
    // 异步资源 check 为 True 则允许异步执行
    AsyncTaskThreadPool<bool> *pool_ptr = GetAsyncPool(context);
    bool async_run = (pool_ptr != nullptr);
    if (!async_run) {
      CL_LOG(WARNING) << "MerchantInferEnrich can't fetch async thread pool resource!";
    }
    // NOTE(panyuchen): 只有当 expect_task_num_per_worker 大于 0 且任务个数也得大于
    // expect_task_num_per_worker, 反之退化为单 worker 执行
    if (async_run && expect_task_num_per_worker_ > 0 && expect_task_num_per_worker_ <= partition_num) {
      std::vector<int> worker_task_num_vec =
          RecoUtil::BalancedPartition(expect_task_num_per_worker_, partition_num);
      std::vector<std::future<bool>> future_vec;
      int64 interval_begin_idx = 0;
      future_vec.reserve(worker_task_num_vec.size());
      for (int worker_id = 0; worker_id < worker_task_num_vec.size(); ++worker_id) {
        std::future<bool> future = pool_ptr->AsyncWithBlock(
            context->GetRequestType(), "merchant infer enricher s" + std::to_string(worker_id),
            [this, interval_begin_idx, worker_id, &worker_task_num_vec, &partition_sub_task_vec,
             &sub_task_func]() -> bool {
              for (int interval_offset = 0; interval_offset < worker_task_num_vec[worker_id];
                   ++interval_offset) {
                sub_task_func(partition_sub_task_vec[interval_begin_idx + interval_offset].first,
                              partition_sub_task_vec[interval_begin_idx + interval_offset].second, true);
              }
              return true;
            });
        interval_begin_idx += worker_task_num_vec[worker_id];
        future_vec.emplace_back(std::move(future));
      }
      for (auto &future : future_vec) {
        future.get();
      }
    } else {
      for (const auto &partition_sub_task : partition_sub_task_vec) {
        sub_task_func(partition_sub_task.first, partition_sub_task.second, false);
      }
    }
  }

  void FillCommonAttrFromRequest(MutableRecoContextInterface *context, CommonRecoRequest *request);
  void FillRequestItems(MutableRecoContextInterface *context, RpcSubTaskContext *sub_ctx,
                        const int64 &shard_id);
  void SendGrpcRequest(MutableRecoContextInterface *context, const std::string &kess_service,
                       const std::string &kess_shard, const int64 &timeout_ms, RpcSubTaskContext *sub_ctx,
                       const int64 &shard_id);
  std::pair<bool, ::ks::kess::rpc::grpc::Future<CommonRecoResponse *>> MultiEventLoopAsyncGrpcReturnByGroup(
      const std::string &kess_name, const std::string &kess_cluster, const std::string &kess_group,
      const std::string &shard, const int &timeout_ms, CommonRecoRequest *request,
      CommonRecoResponse *response);
  void HandleResponse(MutableRecoContextInterface *context, const std::string &kess_service,
                      const std::vector<CommonRecoResult> &items, CommonRecoResponse *response,
                      const std::string &kess_shard,
                      const folly::F14FastMap<uint64, std::vector<CommonRecoResult>> &item_key_mapping);
  std::unique_ptr<RpcSubTaskContextManager> rpc_sub_task_ctx_manager_ptr_;
  std::unique_ptr<::google::protobuf::Arena> arena_ptr_;
  int64 expect_task_num_per_worker_;
  DISALLOW_COPY_AND_ASSIGN(MerchantInferEnricher);
};

}  // namespace platform
}  // namespace ks
