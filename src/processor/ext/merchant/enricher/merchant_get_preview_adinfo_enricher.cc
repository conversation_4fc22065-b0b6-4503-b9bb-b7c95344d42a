#include "dragon/src/processor/ext/merchant/enricher/merchant_get_preview_adinfo_enricher.h"

#include <string>
#include <utility>
#include <vector>
#include "base/encoding/base64.h"

namespace ks {
namespace platform {

constexpr char AD_PREVIEW_ADS_TYPE_ATTR[] = "preview_ads_type";
constexpr char AD_MERCHANT_GPM_ATTR[] = "merchant_gpm";
constexpr char AD_CTR_ATTR[] = "mix_ad_ctr";
constexpr char AD_CVR_ATTR[] = "mix_ad_cvr";
constexpr char AD_LTV_ATTR[] = "mix_ad_ltv";


bool MerchantGetPreviewAdinfoEnricher::InitProcessor() {
  ad_result_attr_name_ = config()->GetString("ad_result_attr", "ad_result");
  return true;
}

void MerchantGetPreviewAdinfoEnricher::Enrich(MutableRecoContextInterface* context,
                                   RecoResultConstIter begin,
                                   RecoResultConstIter end) {
  auto* ad_result_attr_accessor = context->GetItemAttrAccessor(ad_result_attr_name_);
  auto* ad_preview_ads_type_attr_accessor = context->GetItemAttrAccessor(AD_PREVIEW_ADS_TYPE_ATTR);
  auto* ad_merchant_gpm_attr_accessor = context->GetItemAttrAccessor(AD_MERCHANT_GPM_ATTR);
  auto* ad_ctr_attr_accessor = context->GetItemAttrAccessor(AD_CTR_ATTR);
  auto* ad_cvr_attr_accessor = context->GetItemAttrAccessor(AD_CVR_ATTR);
  auto* ad_ltv_attr_accessor = context->GetItemAttrAccessor(AD_LTV_ATTR);


  std::for_each(begin, end, [this, context,
                            ad_result_attr_accessor,
                            ad_merchant_gpm_attr_accessor,
                            ad_ctr_attr_accessor,
                            ad_cvr_attr_accessor,
                            ad_ltv_attr_accessor,
                            ad_preview_ads_type_attr_accessor](const CommonRecoResult& result) {
    auto ad_result = context->GetStringItemAttr(result, ad_result_attr_accessor);
    if (!ad_result) return;

    std::string ad_result_decoded;
    if (!::base::Base64Decode((*ad_result).data(), (*ad_result).size(), &ad_result_decoded)) return;

    AdResult ad_result_Parse;
    if (!ad_result_Parse.ParseFromString(ad_result_decoded)) return;

    if (ad_result_Parse.has_ad_deliver_info()) {
      const auto& ad_deliver_info = ad_result_Parse.ad_deliver_info();
      if (ad_deliver_info.has_ad_base_info() &&
          ad_deliver_info.ad_base_info().has_ac_fetcher_type()) {
        context->SetIntItemAttr(
            result,
            ad_preview_ads_type_attr_accessor,
            static_cast<int64>(ad_deliver_info.ad_base_info().ac_fetcher_type()));
      }
      if (ad_deliver_info.has_ad_base_info() &&
            ad_deliver_info.ad_base_info().has_merchant_gpm()) {
          context->SetDoubleItemAttr(result, ad_merchant_gpm_attr_accessor,
                                  ad_deliver_info.ad_base_info().merchant_gpm());
      }
      if (ad_deliver_info.has_ad_base_info() &&
            ad_deliver_info.ad_base_info().has_shelf_ctr()) {
          context->SetDoubleItemAttr(result, ad_ctr_attr_accessor,
                                  ad_deliver_info.ad_base_info().shelf_ctr());
      }
      if (ad_deliver_info.has_ad_base_info() &&
            ad_deliver_info.ad_base_info().has_shelf_cvr()) {
          context->SetDoubleItemAttr(result, ad_cvr_attr_accessor,
                                  ad_deliver_info.ad_base_info().shelf_cvr());
      }
      if (ad_deliver_info.has_ad_base_info() &&
            ad_deliver_info.ad_base_info().has_shelf_ltv()) {
          context->SetIntItemAttr(result, ad_ltv_attr_accessor,
                                  static_cast<int64>(ad_deliver_info.ad_base_info().shelf_ltv() * 100));
      }
    }
  });
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, MerchantGetPreviewAdinfoEnricher, MerchantGetPreviewAdinfoEnricher)

}  // namespace platform
}  // namespace ks
