#include "dragon/src/processor/ext/merchant/enricher/eshop_video_gsu_extract_enricher.h"

#include <memory>
#include <string>
#include <utility>
#include <vector>
#include <cmath>
#include <unordered_set>
#include <algorithm>

namespace ks {
namespace platform {

bool EshopVideoGsuExtractEnricher::InitProcessor() {
  // input common attr
  request_time_ = config()->GetString("request_time", "");
  input_eshop_video_photo_id_ = config()->GetString("input_eshop_video_photo_id", "");
  input_eshop_video_author_id_ = config()->GetString("input_eshop_video_author_id", "");
  input_eshop_video_play_time_ = config()->GetString("input_eshop_video_play_time", "");
  input_eshop_video_duration_ = config()->GetString("input_eshop_video_duration", "");
  input_eshop_video_channel_ = config()->GetString("input_eshop_video_channel", "");
  input_eshop_video_label_ = config()->GetString("input_eshop_video_label", "");
  input_eshop_video_timestamp_ = config()->GetString("input_eshop_video_timestamp", "");
  input_eshop_video_spu_id_ = config()->GetString("input_eshop_video_spu_id", "");
  input_eshop_video_category_ = config()->GetString("input_eshop_video_category", "");

  // input target attr
  input_aId_ = config()->GetString("input_aId", "");
  input_sCartItemCate1IdList_ = config()->GetString("input_sCartItemCate1IdList", "");
  input_sCartItemCate2IdList_ = config()->GetString("input_sCartItemCate2IdList", "");
  input_sCartItemCate3IdList_ = config()->GetString("input_sCartItemCate3IdList", "");

  // output item attr
  output_eshop_video_gsu_photo_id_ = config()->GetString("output_eshop_video_gsu_photo_id", "");
  output_eshop_video_gsu_author_id_ = config()->GetString("output_eshop_video_gsu_author_id", "");
  output_eshop_video_gsu_play_time_ = config()->GetString("output_eshop_video_gsu_play_time", "");
  output_eshop_video_gsu_duration_ = config()->GetString("output_eshop_video_gsu_duration", "");
  output_eshop_video_gsu_channel_ = config()->GetString("output_eshop_video_gsu_channel", "");
  output_eshop_video_gsu_label_ = config()->GetString("output_eshop_video_gsu_label", "");
  output_eshop_video_gsu_lag_ = config()->GetString("output_eshop_video_gsu_lag", "");
  output_eshop_video_gsu_spu_id_ = config()->GetString("output_eshop_video_gsu_spu_id", "");
  output_eshop_video_gsu_cate1_ = config()->GetString("output_eshop_video_gsu_cate1", "");
  output_eshop_video_gsu_cate2_ = config()->GetString("output_eshop_video_gsu_cate2", "");
  output_eshop_video_gsu_cate3_ = config()->GetString("output_eshop_video_gsu_cate3", "");
  output_eshop_video_match_aid_mcnt_ = config()->GetString("output_eshop_video_match_aid_mcnt", "");
  output_eshop_video_match_cartcate3_mcnt_ = config()->GetString(
    "output_eshop_video_match_cartcate3_mcnt", "");
  output_eshop_video_match_cartcate2_mcnt_ = config()->GetString(
    "output_eshop_video_match_cartcate2_mcnt", "");
  output_eshop_video_match_cartcate1_mcnt_ = config()->GetString(
    "output_eshop_video_match_cartcate1_mcnt", "");

  return true;
}

void EshopVideoGsuExtractEnricher::Enrich(MutableRecoContextInterface *context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
  if (context == nullptr) {
    CL_LOG(ERROR) << "context is null";
    return;
  }

  size_t reco_results_num = std::distance(begin, end);
  auto result_iter = begin;

  auto request_time = context->GetIntCommonAttr(request_time_);
  if (!request_time.has_value()) {
    CL_LOG(ERROR) << "request_time is null";
    return;
  }
  auto eshop_video_photo_id = context->GetIntListCommonAttr(input_eshop_video_photo_id_);
  if (!eshop_video_photo_id.has_value()) {
    CL_LOG(ERROR) << "eshop_video_photo_id is null";
    return;
  }
  auto eshop_video_author_id = context->GetIntListCommonAttr(input_eshop_video_author_id_);
  if (!eshop_video_author_id.has_value()) {
    CL_LOG(ERROR) << "eshop_video_author_id is null";
    return;
  }
  if (eshop_video_author_id->size() != eshop_video_photo_id->size()) {
    CL_LOG(ERROR) << "eshop_video_author_id size not equal";
    return;
  }
  auto eshop_video_play_time = context->GetIntListCommonAttr(input_eshop_video_play_time_);
  if (!eshop_video_play_time.has_value()) {
    CL_LOG(ERROR) << "eshop_video_play_time is null";
    return;
  }
  if (eshop_video_play_time->size() != eshop_video_photo_id->size()) {
    CL_LOG(ERROR) << "eshop_video_play_time size not equal";
    return;
  }
  auto eshop_video_duration = context->GetIntListCommonAttr(input_eshop_video_duration_);
  if (!eshop_video_duration.has_value()) {
    CL_LOG(ERROR) << "eshop_video_duration is null";
    return;
  }
  if (eshop_video_duration->size() != eshop_video_photo_id->size()) {
    CL_LOG(ERROR) << "eshop_video_duration size not equal";
    return;
  }
  auto eshop_video_channel = context->GetIntListCommonAttr(input_eshop_video_channel_);
  if (!eshop_video_channel.has_value()) {
    CL_LOG(ERROR) << "eshop_video_channel is null";
    return;
  }
  if (eshop_video_channel->size() != eshop_video_photo_id->size()) {
    CL_LOG(ERROR) << "eshop_video_channel size not equal";
    return;
  }
  auto eshop_video_label = context->GetIntListCommonAttr(input_eshop_video_label_);
  if (!eshop_video_label.has_value()) {
    CL_LOG(ERROR) << "eshop_video_label is null";
    return;
  }
  if (eshop_video_label->size() != eshop_video_photo_id->size()) {
    CL_LOG(ERROR) << "eshop_video_label size not equal";
    return;
  }
  auto eshop_video_timestamp = context->GetIntListCommonAttr(input_eshop_video_timestamp_);
  if (!eshop_video_timestamp.has_value()) {
    CL_LOG(ERROR) << "eshop_video_timestamp is null";
    return;
  }
  if (eshop_video_timestamp->size() != eshop_video_photo_id->size()) {
    CL_LOG(ERROR) << "eshop_video_timestamp size not equal";
    return;
  }
  auto eshop_video_spu_id = context->GetIntListCommonAttr(input_eshop_video_spu_id_);
  if (!eshop_video_spu_id.has_value()) {
    CL_LOG(ERROR) << "eshop_video_spu_id is null";
    return;
  }
  if (eshop_video_spu_id->size() != eshop_video_photo_id->size()) {
    CL_LOG(ERROR) << "eshop_video_spu_id size not equal";
    return;
  }
  auto eshop_video_category = context->GetIntListCommonAttr(input_eshop_video_category_);
  if (!eshop_video_category.has_value()) {
    CL_LOG(ERROR) << "eshop_video_category is null";
    return;
  }
  if (eshop_video_category->size() != eshop_video_photo_id->size()) {
    CL_LOG(ERROR) << "eshop_video_category size not equal";
    return;
  }

  for (size_t i = 0; i < reco_results_num; ++i) {
    if (result_iter == end) {
      CL_LOG(ERROR) << "result_iter out of bounds";
      break;
    }
    uint64 item_key = (*result_iter).item_key;

    std::vector<int64> eshop_video_gsu_photo_id;
    std::vector<int64> eshop_video_gsu_author_id;
    std::vector<int64> eshop_video_gsu_play_time;
    std::vector<int64> eshop_video_gsu_duration;
    std::vector<int64> eshop_video_gsu_channel;
    std::vector<int64> eshop_video_gsu_label;
    std::vector<int64> eshop_video_gsu_lag;
    std::vector<int64> eshop_video_gsu_spu_id;
    std::vector<int64> eshop_video_gsu_cate1;
    std::vector<int64> eshop_video_gsu_cate2;
    std::vector<int64> eshop_video_gsu_cate3;

    auto aId = context->GetIntItemAttr(item_key, input_aId_);
    if (!aId.has_value()) {
      CL_LOG(ERROR) << "aId is null";
      aId = absl::make_optional<int64>(0);
    }
    auto sCartItemCate1IdList = context->GetIntListItemAttr(item_key, input_sCartItemCate1IdList_);
    if (!sCartItemCate1IdList.has_value()) {
      CL_LOG(ERROR) << "sCartItemCate1IdList is null";
      sCartItemCate1IdList = absl::make_optional<std::vector<int64>>();
    }
    auto sCartItemCate2IdList = context->GetIntListItemAttr(item_key, input_sCartItemCate2IdList_);
    if (!sCartItemCate2IdList.has_value()) {
      CL_LOG(ERROR) << "sCartItemCate2IdList is null";
      sCartItemCate2IdList = absl::make_optional<std::vector<int64>>();
    }
    auto sCartItemCate3IdList = context->GetIntListItemAttr(item_key, input_sCartItemCate3IdList_);
    if (!sCartItemCate3IdList.has_value()) {
      CL_LOG(ERROR) << "sCartItemCate3IdList is null";
      sCartItemCate3IdList = absl::make_optional<std::vector<int64>>();
    }

    std::unordered_set<int> sCartItemCate1IdList_top3;
    std::unordered_set<int> sCartItemCate2IdList_top3;
    std::unordered_set<int> sCartItemCate3IdList_top3;
    for (int i = 0; i < std::min(3, static_cast<int>(sCartItemCate1IdList->size())); ++i) {
      if (sCartItemCate1IdList->at(i) > 0) {
        sCartItemCate1IdList_top3.insert(sCartItemCate1IdList->at(i));
      }
      if (sCartItemCate2IdList->at(i) > 0) {
        sCartItemCate2IdList_top3.insert(sCartItemCate2IdList->at(i));
      }
      if (sCartItemCate3IdList->at(i) > 0) {
        sCartItemCate3IdList_top3.insert(sCartItemCate3IdList->at(i));
      }
    }

    std::vector<int64> aid_index_list;
    std::vector<int64> cate3_index_list;
    std::vector<int64> cate2_index_list;
    std::vector<int64> cate1_index_list;
    std::vector<int64> remain_index_list;
    std::vector<int64> aid_mcnt(8, 0);
    std::vector<int64> cate3_mcnt(8, 0);
    std::vector<int64> cate2_mcnt(8, 0);
    std::vector<int64> cate1_mcnt(8, 0);

    int cur_size = 0;
    for (int index = eshop_video_photo_id->size() - 1; index >= 0; index--) {
      if (cur_size >= 3000) {
        break;
      }
      if (eshop_video_play_time->at(index) <= 10) {
        continue;
      }
      cur_size++;
      int lag = std::floor((request_time.value() - eshop_video_timestamp->at(index)) / (3600 * 24));
      int lag_hour = std::floor((request_time.value() - eshop_video_timestamp->at(index)) / 3600);
      if (lag >= 90) {
        break;
      }

      if (eshop_video_author_id->at(index) == aId && aid_index_list.size() < 100) {
        aid_index_list.emplace_back(index);
      } else if (sCartItemCate3IdList_top3.find(eshop_video_category->at(index) & 0xffff) !=
      sCartItemCate3IdList_top3.end() && cate3_index_list.size() < 100) {
        cate3_index_list.emplace_back(index);
      } else if (sCartItemCate2IdList_top3.find((eshop_video_category->at(index) >> 16) & 0xffff) !=
      sCartItemCate2IdList_top3.end() && cate2_index_list.size() < 100) {
        cate2_index_list.emplace_back(index);
      } else if (sCartItemCate1IdList_top3.find((eshop_video_category->at(index) >> 32) & 0xffff) !=
      sCartItemCate1IdList_top3.end() && cate1_index_list.size() < 100) {
        cate1_index_list.emplace_back(index);
      } else if (remain_index_list.size() < 100) {
        remain_index_list.emplace_back(index);
      }

      if (eshop_video_author_id->at(index) == aId) {
        if (lag_hour < 1) aid_mcnt[0]++;
        if (lag_hour < 3) aid_mcnt[1]++;
        if (lag_hour < 12) aid_mcnt[2]++;
        if (lag < 1) aid_mcnt[3]++;
        if (lag < 3) aid_mcnt[4]++;
        if (lag < 7) aid_mcnt[5]++;
        if (lag < 30) aid_mcnt[6]++;
        if (lag < 90) aid_mcnt[7]++;
      }
      if (sCartItemCate3IdList_top3.find(eshop_video_category->at(index) & 0xffff) !=
      sCartItemCate3IdList_top3.end()) {
        if (lag_hour < 1) cate3_mcnt[0]++;
        if (lag_hour < 3) cate3_mcnt[1]++;
        if (lag_hour < 12) cate3_mcnt[2]++;
        if (lag < 1) cate3_mcnt[3]++;
        if (lag < 3) cate3_mcnt[4]++;
        if (lag < 7) cate3_mcnt[5]++;
        if (lag < 30) cate3_mcnt[6]++;
        if (lag < 90) cate3_mcnt[7]++;
      }
      if (sCartItemCate2IdList_top3.find((eshop_video_category->at(index) >> 16) & 0xffff) !=
      sCartItemCate2IdList_top3.end()) {
        if (lag_hour < 1) cate2_mcnt[0]++;
        if (lag_hour < 3) cate2_mcnt[1]++;
        if (lag_hour < 12) cate2_mcnt[2]++;
        if (lag < 1) cate2_mcnt[3]++;
        if (lag < 3) cate2_mcnt[4]++;
        if (lag < 7) cate2_mcnt[5]++;
        if (lag < 30) cate2_mcnt[6]++;
        if (lag < 90) cate2_mcnt[7]++;
      }
      if (sCartItemCate1IdList_top3.find((eshop_video_category->at(index) >> 32) & 0xffff) !=
      sCartItemCate1IdList_top3.end()) {
        if (lag_hour < 1) cate1_mcnt[0]++;
        if (lag_hour < 3) cate1_mcnt[1]++;
        if (lag_hour < 12) cate1_mcnt[2]++;
        if (lag < 1) cate1_mcnt[3]++;
        if (lag < 3) cate1_mcnt[4]++;
        if (lag < 7) cate1_mcnt[5]++;
        if (lag < 30) cate1_mcnt[6]++;
        if (lag < 90) cate1_mcnt[7]++;
      }
    }

    int gsu_count = 0;
    for (int i = 0; i < aid_index_list.size() && gsu_count < 100; ++i) {
      eshop_video_gsu_photo_id.emplace_back(eshop_video_photo_id->at(aid_index_list[i]));
      eshop_video_gsu_author_id.emplace_back(eshop_video_author_id->at(aid_index_list[i]));
      eshop_video_gsu_play_time.emplace_back(eshop_video_play_time->at(aid_index_list[i]));
      eshop_video_gsu_duration.emplace_back(eshop_video_duration->at(aid_index_list[i]));
      eshop_video_gsu_channel.emplace_back(eshop_video_channel->at(aid_index_list[i]));
      eshop_video_gsu_label.emplace_back(eshop_video_label->at(aid_index_list[i]));
      eshop_video_gsu_lag.emplace_back((request_time.value() -
      eshop_video_timestamp->at(aid_index_list[i])) / (3600 * 24));
      eshop_video_gsu_spu_id.emplace_back(eshop_video_spu_id->at(aid_index_list[i]));
      eshop_video_gsu_cate1.emplace_back((eshop_video_category->at(aid_index_list[i]) >> 32) & 0xffff);
      eshop_video_gsu_cate2.emplace_back((eshop_video_category->at(aid_index_list[i]) >> 16) & 0xffff);
      eshop_video_gsu_cate3.emplace_back(eshop_video_category->at(aid_index_list[i]) & 0xffff);
      gsu_count++;
    }
    for (int i = 0; i < cate3_index_list.size() && gsu_count < 100; ++i) {
      eshop_video_gsu_photo_id.emplace_back(eshop_video_photo_id->at(cate3_index_list[i]));
      eshop_video_gsu_author_id.emplace_back(eshop_video_author_id->at(cate3_index_list[i]));
      eshop_video_gsu_play_time.emplace_back(eshop_video_play_time->at(cate3_index_list[i]));
      eshop_video_gsu_duration.emplace_back(eshop_video_duration->at(cate3_index_list[i]));
      eshop_video_gsu_channel.emplace_back(eshop_video_channel->at(cate3_index_list[i]));
      eshop_video_gsu_label.emplace_back(eshop_video_label->at(cate3_index_list[i]));
      eshop_video_gsu_lag.emplace_back((request_time.value() -
      eshop_video_timestamp->at(cate3_index_list[i])) / (3600 * 24));
      eshop_video_gsu_spu_id.emplace_back(eshop_video_spu_id->at(cate3_index_list[i]));
      eshop_video_gsu_cate1.emplace_back((eshop_video_category->at(cate3_index_list[i]) >> 32) & 0xffff);
      eshop_video_gsu_cate2.emplace_back((eshop_video_category->at(cate3_index_list[i]) >> 16) & 0xffff);
      eshop_video_gsu_cate3.emplace_back(eshop_video_category->at(cate3_index_list[i]) & 0xffff);
      gsu_count++;
    }
    for (int i = 0; i < cate2_index_list.size() && gsu_count < 100; ++i) {
      eshop_video_gsu_photo_id.emplace_back(eshop_video_photo_id->at(cate2_index_list[i]));
      eshop_video_gsu_author_id.emplace_back(eshop_video_author_id->at(cate2_index_list[i]));
      eshop_video_gsu_play_time.emplace_back(eshop_video_play_time->at(cate2_index_list[i]));
      eshop_video_gsu_duration.emplace_back(eshop_video_duration->at(cate2_index_list[i]));
      eshop_video_gsu_channel.emplace_back(eshop_video_channel->at(cate2_index_list[i]));
      eshop_video_gsu_label.emplace_back(eshop_video_label->at(cate2_index_list[i]));
      eshop_video_gsu_lag.emplace_back((request_time.value() -
      eshop_video_timestamp->at(cate2_index_list[i])) / (3600 * 24));
      eshop_video_gsu_spu_id.emplace_back(eshop_video_spu_id->at(cate2_index_list[i]));
      eshop_video_gsu_cate1.emplace_back((eshop_video_category->at(cate2_index_list[i]) >> 32) & 0xffff);
      eshop_video_gsu_cate2.emplace_back((eshop_video_category->at(cate2_index_list[i]) >> 16) & 0xffff);
      eshop_video_gsu_cate3.emplace_back(eshop_video_category->at(cate2_index_list[i]) & 0xffff);
      gsu_count++;
    }
    for (int i = 0; i < cate1_index_list.size() && gsu_count < 100; ++i) {
      eshop_video_gsu_photo_id.emplace_back(eshop_video_photo_id->at(cate1_index_list[i]));
      eshop_video_gsu_author_id.emplace_back(eshop_video_author_id->at(cate1_index_list[i]));
      eshop_video_gsu_play_time.emplace_back(eshop_video_play_time->at(cate1_index_list[i]));
      eshop_video_gsu_duration.emplace_back(eshop_video_duration->at(cate1_index_list[i]));
      eshop_video_gsu_channel.emplace_back(eshop_video_channel->at(cate1_index_list[i]));
      eshop_video_gsu_label.emplace_back(eshop_video_label->at(cate1_index_list[i]));
      eshop_video_gsu_lag.emplace_back((request_time.value() -
      eshop_video_timestamp->at(cate1_index_list[i])) / (3600 * 24));
      eshop_video_gsu_spu_id.emplace_back(eshop_video_spu_id->at(cate1_index_list[i]));
      eshop_video_gsu_cate1.emplace_back((eshop_video_category->at(cate1_index_list[i]) >> 32) & 0xffff);
      eshop_video_gsu_cate2.emplace_back((eshop_video_category->at(cate1_index_list[i]) >> 16) & 0xffff);
      eshop_video_gsu_cate3.emplace_back(eshop_video_category->at(cate1_index_list[i]) & 0xffff);
      gsu_count++;
    }
    for (int i = 0; i < remain_index_list.size() && gsu_count < 100; ++i) {
      eshop_video_gsu_photo_id.emplace_back(eshop_video_photo_id->at(remain_index_list[i]));
      eshop_video_gsu_author_id.emplace_back(eshop_video_author_id->at(remain_index_list[i]));
      eshop_video_gsu_play_time.emplace_back(eshop_video_play_time->at(remain_index_list[i]));
      eshop_video_gsu_duration.emplace_back(eshop_video_duration->at(remain_index_list[i]));
      eshop_video_gsu_channel.emplace_back(eshop_video_channel->at(remain_index_list[i]));
      eshop_video_gsu_label.emplace_back(eshop_video_label->at(remain_index_list[i]));
      eshop_video_gsu_lag.emplace_back((request_time.value() -
      eshop_video_timestamp->at(remain_index_list[i])) / (3600 * 24));
      eshop_video_gsu_spu_id.emplace_back(eshop_video_spu_id->at(remain_index_list[i]));
      eshop_video_gsu_cate1.emplace_back((eshop_video_category->at(remain_index_list[i]) >> 32) & 0xffff);
      eshop_video_gsu_cate2.emplace_back((eshop_video_category->at(remain_index_list[i]) >> 16) & 0xffff);
      eshop_video_gsu_cate3.emplace_back(eshop_video_category->at(remain_index_list[i]) & 0xffff);
      gsu_count++;
    }

    context->SetIntListItemAttr(item_key, output_eshop_video_gsu_photo_id_,
    std::move(eshop_video_gsu_photo_id));
    context->SetIntListItemAttr(item_key, output_eshop_video_gsu_author_id_,
    std::move(eshop_video_gsu_author_id));
    context->SetIntListItemAttr(item_key, output_eshop_video_gsu_play_time_,
    std::move(eshop_video_gsu_play_time));
    context->SetIntListItemAttr(item_key, output_eshop_video_gsu_duration_,
    std::move(eshop_video_gsu_duration));
    context->SetIntListItemAttr(item_key, output_eshop_video_gsu_channel_,
    std::move(eshop_video_gsu_channel));
    context->SetIntListItemAttr(item_key, output_eshop_video_gsu_label_, std::move(eshop_video_gsu_label));
    context->SetIntListItemAttr(item_key, output_eshop_video_gsu_lag_, std::move(eshop_video_gsu_lag));
    context->SetIntListItemAttr(item_key, output_eshop_video_gsu_spu_id_, std::move(eshop_video_gsu_spu_id));
    context->SetIntListItemAttr(item_key, output_eshop_video_gsu_cate1_, std::move(eshop_video_gsu_cate1));
    context->SetIntListItemAttr(item_key, output_eshop_video_gsu_cate2_, std::move(eshop_video_gsu_cate2));
    context->SetIntListItemAttr(item_key, output_eshop_video_gsu_cate3_, std::move(eshop_video_gsu_cate3));
    context->SetIntListItemAttr(item_key, output_eshop_video_match_aid_mcnt_, std::move(aid_mcnt));
    context->SetIntListItemAttr(item_key, output_eshop_video_match_cartcate3_mcnt_, std::move(cate3_mcnt));
    context->SetIntListItemAttr(item_key, output_eshop_video_match_cartcate2_mcnt_, std::move(cate2_mcnt));
    context->SetIntListItemAttr(item_key, output_eshop_video_match_cartcate1_mcnt_, std::move(cate1_mcnt));

    result_iter = std::next(result_iter);
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, EshopVideoGsuExtractEnricher, EshopVideoGsuExtractEnricher);
}  // namespace platform
}  // namespace ks
