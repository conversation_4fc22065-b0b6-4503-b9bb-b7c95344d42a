#include "dragon/src/processor/ext/merchant/enricher/merchant_mix_rank_gen_seed_sequence_model_enricher.h"

#include <math.h>
#include <time.h>
#include <algorithm>
#include <cmath>
#include <random>
#include <set>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "folly/String.h"

namespace ks {
namespace platform {

void MerchantMixRankGenSeedSequenceModelEnricher::Enrich(MutableRecoContextInterface *context,
                                                              RecoResultConstIter begin,
                                                              RecoResultConstIter end) {
  if (!InitializeConfig(context)) {
    return;
  }

  std::string prob_temperature_list_str = GetStringProcessorParameter(context,
      config()->Get("prob_temperature_list"), "1.0");
  std::vector<absl::string_view> prob_temperature_vec = absl::StrSplit(prob_temperature_list_str, ",");
  prob_temperature_list_.clear();
  int prob_temperature_num = prob_temperature_vec.size();
  for (int i = 0; i < prob_temperature_num; ++i) {
    double prob_temperature = 1.0;
    if (absl::SimpleAtod(prob_temperature_vec[i], &prob_temperature) &&
        prob_temperature > 0) {
      prob_temperature_list_.emplace_back(1.0 / prob_temperature);
    }
  }

  if (prob_temperature_list_.empty()) {
    prob_temperature_list_.emplace_back(1.0);
  }
  per_temperature_gen_num_ = context->GetIntCommonAttr("per_temperature_gen_num").value_or(10);
  per_index_sampling_times_ = context->GetIntCommonAttr("per_index_sampling_times").value_or(10);
  ad_improve_weight_ =  context->GetDoubleCommonAttr("ad_improve_weight").value_or(1.0);
  candidate_pre_index_prob_enable_ = context->GetIntCommonAttr(
      "candidate_pre_index_prob_enable").value_or(0);
  max_prob_gap_ = GetDoubleProcessorParameter(context, config()->Get("max_prob_gap"), 10.0);

  const int weight_group_num = weight_group_list_.size();
  std::vector<ItemScoreInfo> score_infos;
  CovertItemsToScoreInfos(context, begin, end, weight_group_num, &score_infos);
  SequenceInfos origin_sequence_infos(static_cast<int>(score_infos.size()));
  for (int i = 0; i < score_infos.size(); ++i) {
    if (listwise_new_generator_ad_enable_ == 0 && score_infos[i].is_ad == 1) {
      continue;
    }
    origin_sequence_infos.score_infos.push_back(&score_infos[i]);
    if (score_infos[i].is_ad == 1) {
      origin_sequence_infos.ad_num += 1;
    }
  }
  const int origin_item_num = origin_sequence_infos.score_infos.size();
  valid_sampling_length_ = std::min(valid_sampling_length_, origin_item_num);
  InitItemAttrWeight(&origin_sequence_infos);
  InitDiversityInfos(context, begin, end);
  InitDiversityRule(context, &origin_sequence_infos);

  std::unordered_set<uint64> seq_set;
  std::vector<int64> retrieve_items;
  max_sequence_num_ = per_temperature_gen_num_ * prob_temperature_list_.size();
  retrieve_items.reserve(max_sequence_num_ + 1);

  if (is_need_origin_sequence_) {
    SequenceInfos empty_sequence(origin_item_num);
    empty_sequence.CopyDiversityRule(origin_sequence_infos.diversity_rules);
    GenAdFirstInsertIndex(origin_sequence_infos, &empty_sequence);
    ProcessOneSequences(&empty_sequence, origin_sequence_infos,
                        &seq_set, &retrieve_items, context);
  }
  std::vector<SequenceInfos> gen_sequence_list;
  gen_sequence_list.reserve(max_sequence_num_);
  WeightSamplingSequence(origin_sequence_infos, &gen_sequence_list);
  // 后处理
  ProcessSequences(&gen_sequence_list, origin_sequence_infos, &seq_set, &retrieve_items, context);
  context->SetIntListCommonAttr(
      absl::StrFormat("retrieval_list_keys_%d", seed_sequence_generated_reason_),
      std::move(retrieve_items));
}

bool MerchantMixRankGenSeedSequenceModelEnricher::InitItemAttrWeight(SequenceInfos* sequence_infos) {
  if (sequence_infos == nullptr || sequence_infos->score_infos.size() == 0 || valid_sampling_length_ <= 0) {
    return false;
  }
  CalImproveWeight(*sequence_infos);
  multi_index_candidate_prob_matrix_.clear();
  const int candidate_num = sequence_infos->score_infos.size();

  for (int i = 0; i < prob_temperature_list_.size(); ++i) {
    std::vector<std::vector<double>> index_candidate_prob_matrix(valid_sampling_length_,
      std::vector<double>(candidate_num, 0.0));
    for (int pos_index = 0; pos_index < valid_sampling_length_; ++pos_index) {
      double curr_index_max_prob = -1e9;
      double curr_index_min_prob = 1e9;
      for (int candidate_index = 0; candidate_index < candidate_num; ++candidate_index) {
        const auto& score_info = sequence_infos->score_infos[candidate_index];
        if (pos_index < score_info->pos_score_list.size()) {
          const double prob = score_info->pos_score_list[pos_index];
          curr_index_max_prob = (prob > curr_index_max_prob) ?
            prob : curr_index_max_prob;
          curr_index_min_prob = (prob > curr_index_min_prob) ?
            curr_index_min_prob : prob;
          index_candidate_prob_matrix[pos_index][candidate_index] = prob;
        }
      }

      double max_prob_gap = curr_index_max_prob - curr_index_min_prob;
      for (int candidate_index = 0; candidate_index < candidate_num; ++candidate_index) {
        if (sequence_infos->score_infos[candidate_index]->is_model_generator == 1) {
          double prob_logit = index_candidate_prob_matrix[pos_index][candidate_index] -
            curr_index_max_prob;

          //  平滑模型预估值
          if (max_prob_gap > max_prob_gap_) {
            prob_logit = max_prob_gap_ * prob_logit / max_prob_gap;
          }
          prob_logit *= prob_temperature_list_[i];
          index_candidate_prob_matrix[pos_index][candidate_index] =
            item_improve_weights_[candidate_index] * std::exp(prob_logit);
        }
      }
    }
    multi_index_candidate_prob_matrix_.emplace_back(std::move(index_candidate_prob_matrix));
  }
  return true;
}

void MerchantMixRankGenSeedSequenceModelEnricher::CalImproveWeight(const SequenceInfos& sequence_infos) {
  item_improve_weights_.clear();
  const int candidate_num = sequence_infos.score_infos.size();
  item_improve_weights_.assign(candidate_num, 1.0);
  // 只取第一组权重计算提权
  if (!weight_group_list_.empty()) {
    const double default_queue_value = 1.0;
    const int sort_type = (gen_seq_sort_type_list_.size() == weight_group_list_.size())
        ? gen_seq_sort_type_list_[0] : gen_seq_sort_type_;
    const int merge_type = (gen_attr_merge_type_list_.size() == weight_group_list_.size())
      ? gen_attr_merge_type_list_[0] : 1;
    for (int candidate_index = 0; candidate_index < candidate_num; ++candidate_index) {
      const auto& score_info = sequence_infos.score_infos[candidate_index];
      const auto& cur_group_weight = weight_group_list_[0];
      const int valid_attr_num = std::min(cur_group_weight.size(), queues_.size());

      for (int q = 0; q < valid_attr_num; ++q) {
        if (score_info->queue_value_list.size() <= q) {
          break;
        }
        if (score_info->queue_value_list[q] <= 0) {
          score_info->queue_value_list[q] = default_queue_value;
        }
        double temp_value = (merge_type == 1)
          ? score_info->queue_value_list[q] * cur_group_weight[q]
          : pow(score_info->queue_value_list[q], cur_group_weight[q]);

        // 直播调权
        if (score_info->item_type == 1) {
          temp_value *= live_merge_weight_;
        }
        // 广告调权
        if (score_info->is_ad == 1) {
          temp_value *= ad_improve_weight_;
        }
        if (sort_type == 1) {
          item_improve_weights_[candidate_index] += temp_value;
        } else {
          item_improve_weights_[candidate_index] *= temp_value;
        }
      }
    }
  }
  return;
}
void MerchantMixRankGenSeedSequenceModelEnricher::WeightSamplingSequence(
    const SequenceInfos& origin_sequence_infos,
    std::vector<SequenceInfos>* gen_sequence_list) {
  if (gen_sequence_list == nullptr ||
      origin_sequence_infos.score_infos.size() == 0 ||
      valid_sampling_length_ == 0) {
    return;
  }

  const int origin_item_num = origin_sequence_infos.score_infos.size();
  const int diversity_hard_rule_num = hard_diversity_rule_type_.size();
  // 生成 diversity_hard_rule_num 个 1 的二进制数
  const int hard_rule_check = (1 << diversity_hard_rule_num) - 1;

  for (int i = 0; i < multi_index_candidate_prob_matrix_.size(); ++i) {
    std::vector<std::vector<double>> index_candidate_prob_matrix = multi_index_candidate_prob_matrix_[i];
    for (int seq_num = 0; seq_num < per_temperature_gen_num_; ++seq_num) {
      SequenceInfos new_sequence_infos(origin_sequence_infos.score_infos.size());
      new_sequence_infos.CopyDiversityRule(origin_sequence_infos.diversity_rules);
      GenAdFirstInsertIndex(origin_sequence_infos, &new_sequence_infos);

      std::vector<int> not_select_index_flag(origin_item_num, 1);
      std::vector<double> pre_index_candidate_max_prob(origin_item_num, 0.0);
      for (int index = 0; index < valid_sampling_length_; ++index) {
        // 若上一个坑位没有成功采集，则直接返回
        if (new_sequence_infos.score_infos.size() < index) {
          break;
        }
        int insert_item_index = -1;
        if (is_random_force_insert_ad_) {
          insert_item_index = SelectedAdIndex(new_sequence_infos, origin_sequence_infos);
        }
        // onx 强插优先级最高，可覆盖前面强插
        if (is_onx_force_insert_enable_) {
          int insert_item_index_tmp = SelectedOnxForceInsertIndex(new_sequence_infos, origin_sequence_infos);
          if (insert_item_index_tmp >= 0 && insert_item_index_tmp < origin_item_num) {
            insert_item_index = insert_item_index_tmp;
          }
        }
        if (reddot_force_insert_live_ && new_sequence_infos.score_infos.size() == 0) {
          int insert_item_index_tmp = SelectedReddotLiveForceInsertIndexByMode(
              new_sequence_infos, origin_sequence_infos, index_candidate_prob_matrix[index]);
          if (insert_item_index_tmp >= 0 && insert_item_index_tmp < origin_item_num) {
            insert_item_index = insert_item_index_tmp;
          }
        }
        if (insert_item_index >= origin_item_num || insert_item_index < 0) {
          std::vector<double> curr_index_candidate_prob_list = index_candidate_prob_matrix[index];
          int selected_index = -1;
          for (int i = 0; i < origin_item_num; ++i) {
            // 物料前序坑位未被选择时，将概率延续给下一个坑位
            if (candidate_pre_index_prob_enable_) {
              if (curr_index_candidate_prob_list[i] > pre_index_candidate_max_prob[i]) {
                pre_index_candidate_max_prob[i] = curr_index_candidate_prob_list[i];
              } else {
                curr_index_candidate_prob_list[i] = pre_index_candidate_max_prob[i];
              }
            }
            if (not_select_index_flag[i] == 0) {
              curr_index_candidate_prob_list[i] = 0;
            }
            if (origin_sequence_infos.score_infos[i]->is_ad == 1 &&
                (is_random_force_insert_ad_ || index < new_sequence_infos.ad_first_insert_pos_index)) {
              curr_index_candidate_prob_list[i] = 0;
            }
            if (is_onx_force_insert_enable_ &&
                origin_sequence_infos.score_infos[i]->onx_force_insert_flag == 1) {
              curr_index_candidate_prob_list[i] = 0;
            }
            // 赋默认值
            if (selected_index < 0 && curr_index_candidate_prob_list[i] > 0) {
              selected_index = i;
            }
          }
          if (selected_index < 0) {
            break;
          }
          int max_valid_num = 0;
          for (int sampling_times = 0; sampling_times < per_index_sampling_times_; ++sampling_times) {
            std::discrete_distribution<> dist(curr_index_candidate_prob_list.begin(),
                curr_index_candidate_prob_list.end());
            int rand_index = dist(gen_);
            if (rand_index >= not_select_index_flag.size() ||
                not_select_index_flag[rand_index] == 0 ||
                rand_index >= origin_item_num ||
                rand_index >= curr_index_candidate_prob_list.size()) {
              continue;
            }
            curr_index_candidate_prob_list[rand_index] = 0;
            const int diversity_flag = DiversityRuleHardCheck(new_sequence_infos,
                                                              origin_sequence_infos.score_infos[rand_index]);
            // 满足所有的多样性规则，则直接停止
            if (diversity_flag == hard_rule_check) {
              max_valid_num = diversity_flag;
              selected_index = rand_index;
              break;
            }
            if (diversity_flag > max_valid_num) {
              max_valid_num = diversity_flag;
              selected_index = rand_index;
            }
          }
          // 如果不满足所有多样性限制，则从所有候选集中选择多样性最好的物料
          if (max_valid_num != hard_rule_check) {
            for (int i = 0; i < origin_item_num; ++i) {
              if (new_sequence_infos.selected_flag_list.size() <= i) {
                break;
              }
              if (new_sequence_infos.selected_flag_list[i] == 1) {
                continue;
              }
              const auto& score_info = origin_sequence_infos.score_infos[i];
              if (is_onx_force_insert_enable_ && score_info->onx_force_insert_flag == 1) {
                continue;
              }
              if (is_random_force_insert_ad_ && score_info->is_ad == 1) {
                continue;
              }
              const int diversity_flag = DiversityRuleHardCheck(new_sequence_infos, score_info);

              if (diversity_flag == hard_rule_check) {
                selected_index = i;
                break;
              } else if (diversity_flag > max_valid_num) {
                max_valid_num = diversity_flag;
                selected_index = i;
              }
            }
          }
          // 如果采样到广告了，则需要将广告替换为靠前的广告
          if (origin_sequence_infos.score_infos[selected_index]->is_ad == 1) {
            double current_max_ad_ecpm = origin_sequence_infos.score_infos[selected_index]->ad_ecpm;
            for (int i = 0; i < origin_sequence_infos.score_infos.size(); ++i) {
              if (origin_sequence_infos.score_infos[i]->is_ad == 1 &&
                  origin_sequence_infos.score_infos[i]->ad_ecpm > current_max_ad_ecpm) {
                const int diversity_flag = DiversityRuleHardCheck(new_sequence_infos,
                                              origin_sequence_infos.score_infos[selected_index]);
                if (diversity_flag > max_valid_num) {
                  current_max_ad_ecpm = origin_sequence_infos.score_infos[i]->ad_ecpm;
                  selected_index = i;
                }
              }
            }
          }
          if (force_insert_max_pctr_pos_set_.find(index) != force_insert_max_pctr_pos_set_.end() &&
              origin_sequence_infos.score_infos[selected_index]->is_ad != 1 &&
              origin_sequence_infos.score_infos[selected_index]->onx_force_insert_flag != 1 &&
              origin_sequence_infos.score_infos[selected_index]->item_type != 1) {
            const int force_insert_max_pctr_index = SelectedMaxPctrIndex(
                new_sequence_infos, origin_sequence_infos);
            if (force_insert_max_pctr_index >= 0 && force_insert_max_pctr_index < origin_item_num) {
              selected_index = force_insert_max_pctr_index;
            }
          }
          insert_item_index = selected_index;
        }
        if (insert_item_index >= 0 && insert_item_index < origin_item_num) {
          double diversity_score = DiversityRuleSoftCheck(new_sequence_infos,
            origin_sequence_infos.score_infos[insert_item_index]);
          UpdateCurSequence(origin_sequence_infos, insert_item_index, 0.0,
            diversity_score, &new_sequence_infos);
          not_select_index_flag[insert_item_index] = 0;
        }
      }
      if (new_sequence_infos.score_infos.size() > 0) {
        gen_sequence_list->emplace_back(std::move(new_sequence_infos));
      }
    }
  }
}

void MerchantMixRankGenSeedSequenceModelEnricher::ProcessSequences(
    std::vector<SequenceInfos>* gen_sequence_list,
    const SequenceInfos& origin_sequence_infos,
    std::unordered_set<uint64>* seq_set,
    std::vector<int64>* retrieve_items,
    MutableRecoContextInterface* context) {
  if (gen_sequence_list == nullptr || seq_set == nullptr ||
      retrieve_items == nullptr || context == nullptr) {
    return;
  }
  for (SequenceInfos& curr_sequences_infos : *gen_sequence_list) {
    ProcessOneSequences(&curr_sequences_infos, origin_sequence_infos,
                        seq_set, retrieve_items, context);
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, MerchantMixRankGenSeedSequenceModelEnricher,
                 MerchantMixRankGenSeedSequenceModelEnricher)

}  // namespace platform
}  // namespace ks
