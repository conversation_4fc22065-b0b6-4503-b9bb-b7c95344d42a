#include "dragon/src/processor/ext/merchant/enricher/merchant_infer_enricher.h"

#include <algorithm>
#include <functional>
#include <future>
#include <unordered_set>
#include <utility>

#include "base/hash_function/city.h"
#include "dragon/src/interop/kuiba_sample_attr.h"
#include "folly/container/F14Map.h"
#include "ks/common_reco/util/key_sign_util.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.kess.grpc.pb.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.pb.h"
#include "serving_base/server_base/kess_client.h"

namespace ks {
namespace platform {

bool MerchantInferEnricher::GenerateSampleListAttr(
    MutableRecoContextInterface *context,
    ::google::protobuf::RepeatedPtrField<::kuiba::SampleAttr> *attr_vct) {
  if (attr_vct == nullptr) {
    return false;
  }

  auto kuiba_user_attr_names = context->GetStringListCommonAttr(sample_list_common_attr_key_);
  const auto *kuiba_user_item =
      sample_list_ptr_attr_.empty()
          ? nullptr
          : context->GetProtoMessagePtrCommonAttr<::kuiba::PredictItem>(sample_list_ptr_attr_);
  if ((!kuiba_user_attr_names || kuiba_user_attr_names->empty()) &&
      (!kuiba_user_item || kuiba_user_item->attr_size() == 0)) {
    if (!sample_list_common_attr_key_.empty()) {
      CL_LOG_EVERY_N(WARNING, 1000) << "cannot find string list common attr [" << sample_list_common_attr_key_
                                    << "] or it's empty" << RecoUtil::GetRequestInfoForLog(context);
    }
    if (!sample_list_ptr_attr_.empty()) {
      CL_LOG_EVERY_N(WARNING, 1000) << "cannot find ptr common attr [" << sample_list_ptr_attr_
                                    << "] or it's empty" << RecoUtil::GetRequestInfoForLog(context);
    }
    return false;
  }

  kuiba::PredictItem kuiba_user_attrs;

  if (kuiba_user_item) {
    for (const auto &attr : kuiba_user_item->attr()) {
      const auto &name = attr.name();
      if (send_common_attrs_set_.count(name) || sample_list_attrs_.count(name)) {
        continue;
      }
      sample_list_attrs_.emplace(name);
      kuiba_user_attrs.mutable_attr()->Add()->CopyFrom(attr);
    }
  }

  if (kuiba_user_attr_names) {
    for (auto name : *kuiba_user_attr_names) {
      if (send_common_attrs_set_.count(name) || sample_list_attrs_.count(name)) {
        continue;
      }
      sample_list_attrs_.emplace(std::string(name.data(), name.size()));
      if (!interop::BuildSampleAttrFromCommonAttr(context, name, kuiba_user_attrs.mutable_attr())) {
        CL_LOG_WARNING_EVERY("delegate_enrich", "kuiba_user_attr_miss:" + std::string(name), 100)
            << "cannot find sample list common attr: " << name << RecoUtil::GetRequestInfoForLog(context);
        continue;
      }
    }
  }

  if (flatten_sample_list_attr_) {
    ::kuiba::SampleAttr *sample_attr = attr_vct->Add();
    sample_attr->set_name(flatten_sample_list_attr_to_);
    sample_attr->set_type(kuiba::CommonSampleEnum::STRING_ATTR);
    std::string *str_val = sample_attr->mutable_string_value();
    kuiba_user_attrs.SerializeToString(str_val);
  } else {
    for (auto &attr : *kuiba_user_attrs.mutable_attr()) {
      attr_vct->Add()->Swap(&attr);
    }
  }
  return true;
}

bool MerchantInferEnricher::FillCommonAttrFromSampleList(MutableRecoContextInterface *context,
                                                         CommonRecoRequest *request_ptr) {
  int pre_attr_num = request_ptr->common_attr_size();
  if (!GenerateSampleListAttr(context, request_ptr->mutable_common_attr())) {
    CL_LOG_WARNING_EVERY("delegate_enrich", "generate_sample_list_attr_fail:" + GetName(), 1000)
        << "delegate enrich request cancelled: failed to generate sample list attr."
        << RecoUtil::GetRequestInfoForLog(context);
    return false;
  }
  CL_LOG_EVERY_N(INFO, 1000) << "injected " << (request_ptr->common_attr_size() - pre_attr_num)
                             << " sample list attrs, flatten_sample_list_attr: " << flatten_sample_list_attr_;
  return true;
}

void MerchantInferEnricher::FillCommonAttrFromRequest(MutableRecoContextInterface *context,
                                                      CommonRecoRequest *request_ptr) {
  const CommonRecoRequest *request_from_upstream_ptr = context->GetRequest();
  absl::flat_hash_set<std::string> send_common_attrs_in_request;
  for (const auto &attr : request_from_upstream_ptr->common_attr()) {
    if (send_common_attrs_set_.count(attr.name()) || sample_list_attrs_.count(attr.name()) ||
        exclude_common_attrs_.count(attr.name()) || send_common_attrs_in_request.count(attr.name())) {
      continue;
    }
    send_common_attrs_in_request.emplace(attr.name());
    request_ptr->mutable_common_attr()->Add()->CopyFrom(attr);
    VLOG(1) << "common attr sent: " << attr.name();
  }
}

int64 MerchantInferEnricher::CheckAndGetTimeoutMs(MutableRecoContextInterface *context,
                                                  const std::string &kess_service) {
  static constexpr int kDefaultTimeoutMs = 300;
  int64 timeout_ms = GetIntProcessorParameter(context, "timeout_ms", kDefaultTimeoutMs);
  if (timeout_ms <= 0) {
    CL_LOG_ERROR_EVERY("merchant_infer_enrich", "negative_timeout:" + GetName(), 100)
        << "timeout_ms=" << timeout_ms << " is too small, use default value " << kDefaultTimeoutMs
        << ", processor: " << GetName();
    timeout_ms = kDefaultTimeoutMs;
  } else if (timeout_ms > 2000) {
    CL_LOG_WARNING_EVERY("merchant_infer_enrich", "timeout_too_large:" + std::to_string(timeout_ms), 100)
        << "timeout_ms=" << timeout_ms << " is too large, be careful!";
  }

  return timeout_ms;
}

void MerchantInferEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
  std::string kess_service = GetStringProcessorParameter(context, "kess_service");
  if (kess_service.empty()) {
    CL_LOG_ERROR_EVERY("merchant_infer_enrich", "no_kess_service:" + GetName(), 100)
        << "infer enrich request cancelled: no kess_service! processor: " << GetName();
    return;
  }

  int64 timeout_ms = CheckAndGetTimeoutMs(context, kess_service);
  if (timeout_ms == 0) {  // 命中服务控制列表，且配置的超时时间为 0 ，才会满足这个条件，此时屏蔽该服务
    return;
  }

  auto timer = std::make_shared<serving_base::Timer>();
  timer = {timer.get(), [timer](void *) { CL_LOG(INFO) << timer->display(); }};

  if (send_item_attr_vec_.empty()) {
    send_item_attr_vec_.reserve(send_item_attrs_.size());
    for (const auto &attr : send_item_attrs_) {
      send_item_attr_vec_.push_back(SendItemAttr{
          .name = attr.first, .as = attr.second, .accessor = context->GetItemAttrAccessor(attr.first)});
    }
  } else {
    send_item_attr_vec_.resize(send_item_attrs_.size());
  }

  dynamic_send_item_attr_vec_.clear();
  if (!dynamic_item_attrs_names_.empty()) {
    auto dynamic_item_attrs_names_values = context->GetStringListCommonAttr(dynamic_item_attrs_names_);
    if (dynamic_item_attrs_names_values) {
      for (int i = 0; i < dynamic_item_attrs_names_values->size(); ++i) {
        auto item_name = (*dynamic_item_attrs_names_values)[i];
        dynamic_send_item_attr_vec_.push_back(SendItemAttr{
            .name = item_name, .as = item_name, .accessor = context->GetItemAttrAccessor(item_name)});
      }
    }
  }

  timer->AppendCostMs("config set");
  arena_ptr_->Reset();
  CommonRecoRequest *request_source_ptr =
      ::google::protobuf::Arena::CreateMessage<CommonRecoRequest>(arena_ptr_.get());
  timer->AppendCostMs("arena memory clear");
  // common attrs
  std::string request_type = GetStringProcessorParameter(context, "request_type");
  request_source_ptr->set_request_type(request_type.empty() ? context->GetRequestType() : request_type);

  request_source_ptr->set_user_id(context->GetUserId());
  request_source_ptr->set_device_id(context->GetDeviceId());
  request_source_ptr->set_time_ms(context->GetRequestTime());
  request_source_ptr->set_request_id(context->GetRequestId());
  request_source_ptr->set_debug(context->IsDebugRequest());
  request_source_ptr->set_need_traceback(context->NeedTraceback());
  if (context->GetRequest()->has_abtest_mapping_id()) {
    request_source_ptr->mutable_abtest_mapping_id()->CopyFrom(context->GetRequest()->abtest_mapping_id());
  }

  bool is_sim_request = false;
  if (auto val = context->GetIntCommonAttr(kSimulationRequest)) {
    is_sim_request = *val;
  }
  uint64 uId = 0;
  std::string dId = "";
  CommonAttr *uid_acc = context->GetCommonAttrAccessor("uId");
  CommonAttr *did_acc = context->GetCommonAttrAccessor("dId");
  if (is_sim_request) {
    auto sim_uid = context->GetIntCommonAttr(kSimulationUserId);
    auto sim_did = context->GetStringCommonAttr(kSimulationDeviceId);
    if (sim_uid && sim_did) {
      request_source_ptr->set_user_id(*sim_uid);
      if (auto real_uid = context->GetIntCommonAttr(uid_acc)) {
        uId = *real_uid;
        context->SetIntCommonAttr(uid_acc, *sim_uid);
      }
      request_source_ptr->set_device_id({sim_did->data(), sim_did->size()});
      if (auto real_did = context->GetStringCommonAttr(did_acc)) {
        dId = {real_did->data(), real_did->size()};
        context->SetStringCommonAttr(did_acc, {sim_did->data(), sim_did->size()});
      }
      ks::infra::PerfUtil::CountLogStash(1, kPerfNs, "delegate_enrich_simulation",
                                         GlobalHolder::GetServiceIdentifier(), context->GetRequestType(),
                                         std::to_string(context->GetUserId()), context->GetDeviceId(),
                                         std::to_string(*sim_uid), {sim_did->data(), sim_did->size()});
    }
  }

  base::ScopeExit restore_uid_did([context, is_sim_request, uid_acc, did_acc, uId, &dId] {
    if (is_sim_request) {
      if (uId) {
        context->SetIntCommonAttr(uid_acc, uId);
      }
      if (!dId.empty()) {
        context->SetStringCommonAttr(did_acc, dId);
      }
    }
  });

  if (use_packed_item_attr_config_) {
    request_source_ptr->set_use_packed_item_attr(
        GetBoolProcessorParameter(context, use_packed_item_attr_config_, false));
  } else if (recv_item_attrs_.empty()) {
    // XXX(fangjianbing): recv_item_attrs_ 若为空则无法通过下游 response 自动判断是否支持 packed_item_attr,
    // 为保证兼容和安全性这里必须强制不使用 packed_item_attr
    request_source_ptr->set_use_packed_item_attr(false);
  } else {
    bool use_packed_item_attr = packed_item_attr_no_support_services_.empty() ||
                                packed_item_attr_no_support_services_.count(kess_service) == 0;
    request_source_ptr->set_use_packed_item_attr(use_packed_item_attr);
    if (!use_packed_item_attr) {
      CL_LOG_WARNING_EVERY("merchant_infer_enrich", "packed_item_attr_not_support: " + kess_service, 100)
          << "found packed_item_attr no support service: " << kess_service << ", processor: " << GetName();
    }
  }

  for (const auto &attr_as : send_common_attrs_) {
    kuiba::SampleAttr *attr = interop::BuildSampleAttrFromCommonAttr(
        context, attr_as.name, request_source_ptr->mutable_common_attr(), attr_as.alias, attr_as.readonly);

    if (!attr || attr->type() == kuiba::CommonSampleEnum::UNKNOWN_ATTR) {
      CL_LOG_EVERY_N(WARNING, 1000) << "common attr " << attr_as.name << " is requested but not found";
    } else {
      VLOG(1) << "common attr sent: " << attr_as.name;
    }
  }

  if (!dynamic_common_attrs_names_.empty()) {
    auto dynamic_common_attrs_names_values = context->GetStringListCommonAttr(dynamic_common_attrs_names_);
    if (dynamic_common_attrs_names_values) {
      for (int i = 0; i < dynamic_common_attrs_names_values->size(); ++i) {
        auto commmon_name = (*dynamic_common_attrs_names_values)[i];
        kuiba::SampleAttr *attr = interop::BuildSampleAttrFromCommonAttr(
            context, commmon_name, request_source_ptr->mutable_common_attr(), commmon_name, false);
        if (!attr || attr->type() == kuiba::CommonSampleEnum::UNKNOWN_ATTR) {
          CL_LOG_EVERY_N(WARNING, 1000)
              << "dynamic common attr " << commmon_name << " is requested but not found";
        } else {
          VLOG(1) << "dynamic common attr sent: " << commmon_name;
        }
      }
    }
  }

  sample_list_attrs_.clear();
  // 获得 samplelist attr, 失败情况下不发送请求
  if (use_sample_list_attr_flag_ && !FillCommonAttrFromSampleList(context, request_source_ptr)) {
    CL_LOG(WARNING) << "MerchantInferEnricher failed to generate sample list attr.";
    return;
  }

  // 将 request 中携带的 common_attr 发送下游
  if (send_common_attrs_in_request_) {
    FillCommonAttrFromRequest(context, request_source_ptr);
  }

  if (infer_output_type_ >= 0) {
    kuiba::SampleAttr *attr = request_source_ptr->add_common_attr();
    attr->set_name(kInferOutputType);
    attr->set_type(kuiba::CommonSampleEnum::INT_ATTR);
    attr->set_int_value(infer_output_type_);
  }

  request_source_ptr->set_return_required_attrs_only(true);

  for (const auto &attr_as : recv_item_attrs_) {
    request_source_ptr->add_return_item_attrs(attr_as.first);
  }
  for (const auto &attr_as : recv_common_attrs_) {
    request_source_ptr->add_return_common_attrs(attr_as.first);
  }

  timer->AppendCostMs("prepare");
  // new sub task
  int64 internal_partition_size = GetIntProcessorParameter(context, "internal_partition_size", 50);
  int64 current_pv_task_num =
      (std::distance(begin, end) + internal_partition_size - 1) / internal_partition_size;
  rpc_sub_task_ctx_manager_ptr_->Reset(current_pv_task_num);
  DispatchSubTaskByPartitionSize(
      context, begin, end, internal_partition_size,
      [&](RecoResultConstIter sub_task_begin, RecoResultConstIter sub_task_end, bool is_async) -> bool {
        RpcSubTaskContext &rpc_sub_task_ctx = rpc_sub_task_ctx_manager_ptr_->GetContext();
        rpc_sub_task_ctx.Initialize(sub_task_begin, sub_task_end, request_source_ptr, shard_num_,
                                    arena_ptr_.get(), is_async);
        for (int shard_id = 0; shard_id < shard_num_; ++shard_id) {
          FillRequestItems(context, &rpc_sub_task_ctx, shard_id);
          std::string kess_shard = "s" + std::to_string(shard_id_offset_ + shard_id);
          SendGrpcRequest(context, kess_service, kess_shard, timeout_ms, &rpc_sub_task_ctx, shard_id);
        }
        return true;
      });
  // 串行执行
  timer->AppendCostMs("fill requests item data & send requests");
  int64 ctx_num = rpc_sub_task_ctx_manager_ptr_->GetUsedContextNum();
  for (int ctx_id = 0; ctx_id < ctx_num; ++ctx_id) {
    RpcSubTaskContext &rpc_sub_task_ctx = rpc_sub_task_ctx_manager_ptr_->GetContextById(ctx_id);
    for (int shard_id = 0; shard_id < shard_num_; ++shard_id) {
      std::string kess_shard = "s" + std::to_string(shard_id_offset_ + shard_id);
      RegisterAsyncCallback(
          context, std::move(rpc_sub_task_ctx.future_in_shard[shard_id]),
          [this, context, kess_service, kess_shard,
           items = std::move(rpc_sub_task_ctx.items_in_shard[shard_id]),
           item_key_mapping =
               std::move(rpc_sub_task_ctx.item_key_mapping)](CommonRecoResponse *response_ptr) {
            HandleResponse(context, kess_service, items, response_ptr, kess_shard, item_key_mapping);
          },
          "sub_task_" + std::to_string(ctx_id) + "_shard_" + std::to_string(shard_id));
    }
  }
  timer->AppendCostMs("register callbacks");
}

void MerchantInferEnricher::HandleResponse(
    MutableRecoContextInterface *context, const std::string &kess_service,
    const std::vector<CommonRecoResult> &items, CommonRecoResponse *response_ptr,
    const std::string &kess_shard,
    const folly::F14FastMap<uint64, std::vector<CommonRecoResult>> &item_key_mapping) {
  if (response_ptr->status_code() != 0) {
    CL_LOG_ERROR("merchant_infer_enrich",
                 absl::StrCat("response status_code=", response_ptr->status_code(), ": ", kess_service))
        << "MerchantInferEnricher failed as non-zero status_code: " << response_ptr->status_code();
    return;
  }
  VLOG(1) << "Delegate response:" << response_ptr->ShortDebugString();

  if (!recv_common_attrs_.empty()) {
    for (const auto &attr : response_ptr->common_attr()) {
      auto it = recv_common_attrs_.find(attr.name());
      if (it != recv_common_attrs_.end()) {
        interop::SaveSampleAttrToCommonAttr(context, it->second, attr);
      }
    }
  }

  folly::F14FastMap<std::string, StatisticInfo> pxtr_counter;
  int valid_item_num = 0;
  int wrong_item_key_num = 0;
  bool has_plain_item_attr = false;
  folly::F14FastMap<uint64, int> item_key_pos;
  for (const auto &item : response_ptr->item()) {
    uint64 item_key = Util::GenKeysign(item.item_type(), item.item_id());
    const CommonRecoResult *result = nullptr;
    if (!item_key_mapping.empty()) {
      auto it = item_key_mapping.find(item_key);
      int &pos = item_key_pos[item_key];
      if (it != item_key_mapping.end() && it->second.size() > pos) {
        result = &(it->second[pos]);
        pos++;
      } else {
        ++wrong_item_key_num;
        CL_LOG_EVERY_N(WARNING, 1000) << "Returned item_key=" << item_key << " not found in key mapping!";
        continue;
      }
    }

    ++valid_item_num;

    if (!recv_item_attrs_.empty()) {
      for (const auto &sample_attr : item.item_attr()) {
        has_plain_item_attr = true;
        auto it = recv_item_attrs_.find(sample_attr.name());
        if (it != recv_item_attrs_.end()) {
          interop::SaveSampleAttrToItemAttr(context, item_key, it->second, sample_attr);
          if (for_predict_ && sample_attr.type() == ::kuiba::CommonSampleEnum::FLOAT_ATTR) {
            pxtr_counter[it->second].AddValue(sample_attr.float_value());
          }
        } else {
          VLOG(1) << sample_attr.name() << " is ignored as it's not in recv_item_attrs";
        }
      }
    }
  }

  CL_LOG_WARNING_COUNT(wrong_item_key_num, "merchant_infer_enrich",
                       "returned wrong item_key: " + kess_service)
      << "Returned " << wrong_item_key_num << " wrong item_keys that not found in key mapping!";

  if (response_ptr->has_item_attr() && !recv_item_attrs_.empty()) {
    const std::vector<CommonRecoResult> *p_items = &items;
    std::vector<CommonRecoResult> aligned_items;
    if (RecoUtil::AlignPackedItemAttrNum(response_ptr->item_attr(), items, context, &aligned_items,
                                         &item_key_mapping)) {
      p_items = &aligned_items;
      VLOG(100) << "using aligned fake items due to packed_item_num and request item num mismatch: "
                << aligned_items.size() << " vs " << items.size() << ", shard: " << kess_shard;
    }

    for (const auto &attr_value : response_ptr->item_attr().attr_values()) {
      auto it = recv_item_attrs_.find(attr_value.name());
      if (it == recv_item_attrs_.end()) {
        VLOG(1) << attr_value.name() << " is ignored as it's not in recv_item_attrs";
        continue;
      }
      auto *attr_accessor = context->GetItemAttrAccessor(it->second);
      auto stat = RecoUtil::ExtractPackedItemAttrToContext(attr_accessor, attr_value, p_items, false,
                                                           check_multi_table_);
      if (for_predict_ && (attr_value.value_type() == PackedItemAttrValue_ValueType_FLOAT64 ||
                           attr_value.type() == ::kuiba::CommonSampleEnum::FLOAT_ATTR)) {
        pxtr_counter[it->second] = std::move(stat);
      }
    }
  }

  if (!use_packed_item_attr_config_ && !recv_item_attrs_.empty()) {
    if (!response_ptr->has_item_attr() && has_plain_item_attr) {
      packed_item_attr_no_support_services_.insert(kess_service);
    }
  }

  if (for_predict_) {
    if (items.size() > 0) {
      double filling_rate = (double)valid_item_num / items.size();
      base::perfutil::PerfUtilWrapper::IntervalLogStash(1000.0 * filling_rate, kPerfNs, "predict.item_hit",
                                                        GlobalHolder::GetServiceIdentifier(),
                                                        context->GetRequestType(), GetName());
    }
    base::perfutil::PerfUtilWrapper::IntervalLogStash(items.size(), kPerfNs, "predict.item_total",
                                                      GlobalHolder::GetServiceIdentifier(),
                                                      context->GetRequestType(), GetName());
    for (const auto &counter : pxtr_counter) {
      const auto &attr_name = counter.first;
      const auto &stat = counter.second;
      if (items.size() > 0) {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(
            1000.0 * stat.count() / items.size(), kPerfNs, "predict.pxtr_hit",
            GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName(), attr_name);
      }
      if (stat.count() > 0) {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(1000000 * stat.avg(), kPerfNs, "predict.pxtr_avg",
                                                          GlobalHolder::GetServiceIdentifier(),
                                                          context->GetRequestType(), GetName(), attr_name);
        base::perfutil::PerfUtilWrapper::IntervalLogStash(1000000 * stat.max(), kPerfNs, "predict.pxtr_max",
                                                          GlobalHolder::GetServiceIdentifier(),
                                                          context->GetRequestType(), GetName(), attr_name);
      }
    }
  }
}
void MerchantInferEnricher::FillRequestItems(MutableRecoContextInterface *context, RpcSubTaskContext *rst_ctx,
                                             const int64 &shard_id) {
  rst_ctx->request_ptr->clear_item_list();
  std::vector<CommonRecoResult> send_items;
  std::vector<uint64> send_item_keys;
  std::vector<int> fill_attr_succ_count;
  for (const CommonRecoResult &result : rst_ctx->items_in_shard[shard_id]) {
    uint64 item_key = result.item_key;
    uint64 item_id = result.GetId();
    int item_type = result.GetType();

    if (check_multi_table_) {
      rst_ctx->item_key_mapping[item_key].push_back(result);
    }

    auto *item = rst_ctx->request_ptr->add_item_list();
    item->set_item_id(item_id);
    item->set_item_type(item_type);
    item->set_reason(result.reason);
    item->set_score(result.score);

    if (rst_ctx->request_ptr->use_packed_item_attr()) {
      send_items.push_back(result);
      send_item_keys.push_back(item_key);
    } else {
      if (fill_attr_succ_count.empty()) {
        fill_attr_succ_count.resize(send_item_attr_vec_.size(), 0);
      }
      for (int i = 0; i < send_item_attr_vec_.size(); ++i) {
        const auto &send_item_attr = send_item_attr_vec_[i];
        kuiba::SampleAttr *attr = interop::BuildSampleAttrFromItemAttr(
            result, send_item_attr.accessor, item->mutable_item_attr(), send_item_attr.as);
        if (attr && attr->type() != kuiba::CommonSampleEnum::UNKNOWN_ATTR) {
          ++fill_attr_succ_count[i];
          VLOG(1) << "[" << result.item_key << "] item attr sent: " << send_item_attr.name;
        }
      }
      if (!dynamic_item_attrs_names_.empty()) {
        for (int i = 0; i < dynamic_send_item_attr_vec_.size(); ++i) {
          const auto &send_item_attr = dynamic_send_item_attr_vec_[i];
          kuiba::SampleAttr *attr = interop::BuildSampleAttrFromItemAttr(
              result, send_item_attr.accessor, item->mutable_item_attr(), send_item_attr.as);
          if (attr && attr->type() != kuiba::CommonSampleEnum::UNKNOWN_ATTR) {
            VLOG(1) << "[" << result.item_key << "] dynamic item attr sent: " << send_item_attr.name;
          }
        }
      }
    }
  }

  if (rst_ctx->request_ptr->use_packed_item_attr()) {
    std::vector<ItemAttr *> send_item_attr_accessors;
    std::vector<absl::string_view> rename_attrs;
    send_item_attr_accessors.reserve(send_item_attr_vec_.size());
    rename_attrs.reserve(send_item_attr_vec_.size());
    for (const auto &item : send_item_attr_vec_) {
      send_item_attr_accessors.push_back(item.accessor);
      rename_attrs.push_back(item.as);
    }
    if (!dynamic_item_attrs_names_.empty()) {
      for (const auto &item : dynamic_send_item_attr_vec_) {
        send_item_attr_accessors.push_back(item.accessor);
        rename_attrs.push_back(item.as);
      }
    }

    fill_attr_succ_count = RecoUtil::BuildPackedItemAttrFromItems(
        send_items.cbegin(), send_items.cend(), send_item_attr_accessors,
        rst_ctx->request_ptr->mutable_item_attr(), &rename_attrs, &send_item_keys, check_multi_table_);
  }

  // expect that all items are returned
  int send_item_num = rst_ctx->request_ptr->item_list_size();
  rst_ctx->request_ptr->set_request_num(send_item_num);

  for (int i = 0; i < fill_attr_succ_count.size(); ++i) {
    int miss_count = send_item_num - fill_attr_succ_count[i];
    if (miss_count > 0) {
      CL_LOG_EVERY_N(WARNING, 10000)
          << miss_count << " out of " << send_item_num << " items missing item attr "
          << (i < send_item_attr_vec_.size()
                  ? send_item_attr_vec_[i].name
                  : dynamic_send_item_attr_vec_[i - send_item_attr_vec_.size()].name);
    }
  }
}

void MerchantInferEnricher::SendGrpcRequest(MutableRecoContextInterface *context,
                                            const std::string &kess_service, const std::string &kess_shard,
                                            const int64 &timeout_ms, RpcSubTaskContext *rst_ctx,
                                            const int64 &shard_id) {
  // skip send request if both item_list and return_common_attrs is empty
  if (rst_ctx->request_ptr->item_list_size() == 0 && rst_ctx->request_ptr->return_common_attrs_size() == 0) {
    CL_LOG(INFO) << "infer enrich cancelled, both item_list and return_common_attrs are empty";
    return;
  }
  std::string kess_group = GetStringProcessorParameter(context, "kess_group");
  std::string request_type = rst_ctx->request_ptr->request_type();

  const std::pair<bool, ::ks::kess::rpc::grpc::Future<CommonRecoResponse *>> &pr =
      MultiEventLoopAsyncGrpcReturnByGroup(kess_service, kess_cluster_, kess_group, kess_shard, timeout_ms,
                                           rst_ctx->request_ptr, rst_ctx->response_ptr);

  const std::string request_info = "kess_service: " + kess_service + ", " + request_info_ +
                                   ", timeout_ms: " + std::to_string(timeout_ms) +
                                   ", kess_shard: " + kess_shard;

  if (!pr.first) {
    CL_LOG_ERROR_EVERY("merchant_infer_enrich", "send_request_fail: " + kess_service, 1000)
        << "failed to send infer enrich request! " << request_info << RecoUtil::GetRequestInfoForLog(context);
    return;
  }

  base::perfutil::PerfUtilWrapper::CountLogStash(1, kPerfNs, "delegate_enrich_call",
                                                 GlobalHolder::GetServiceIdentifier(), request_type,
                                                 GetName(), kess_service);
  // 移交 future
  rst_ctx->future_in_shard[shard_id] = std::move(pr.second);
}

std::pair<bool, ::ks::kess::rpc::grpc::Future<CommonRecoResponse *>>
MerchantInferEnricher::MultiEventLoopAsyncGrpcReturnByGroup(const std::string &kess_name,
                                                            const std::string &kess_cluster,
                                                            const std::string &kess_group,
                                                            const std::string &shard, const int &timeout_ms,
                                                            CommonRecoRequest *request_ptr,
                                                            CommonRecoResponse *response_ptr) {
  static ::ks::kess::rpc::grpc::EventLoopGroup *event_loop_group_ptr =
      new ::ks::kess::rpc::grpc::EventLoopGroup(8);

  static ::base::ClientGroupManager *client_group_manager_ptr = new ::base::ClientGroupManager();

  ks::kess::rpc::grpc::Options options;
  options.SetTimeout(std::chrono::milliseconds(timeout_ms));
  if (rpc_compress_) {
    options.SetCompressionAlgorithm(ks::kess::rpc::CompressionAlgorithm::COMPRESS_LZ4);
  }

  if (kess_name.empty()) {
    CL_LOG(ERROR) << "kess name is empty, " << kess_cluster << " " << shard << " kess grpc failed";
    return std::make_pair(false, ::ks::kess::rpc::grpc::Future<CommonRecoResponse *>());
  }

  std::shared_ptr<::base::ClientGroup> client_group_ptr =
      client_group_manager_ptr->GetClientGroup(kess_name, 4);
  if (!client_group_ptr) {
    CL_LOG(ERROR) << kess_name << " " << kess_cluster << " " << shard << " get client_ptr group failed";
    return std::make_pair(false, ::ks::kess::rpc::grpc::Future<CommonRecoResponse *>());
  }
  std::shared_ptr<::ks::kess::rpc::grpc::Client2> client_ptr = client_group_ptr->SelectOne();

  if (!client_ptr) {
    CL_LOG(ERROR) << kess_name << " " << kess_cluster << " " << shard << " get client ptr failed";
    return std::make_pair(false, ::ks::kess::rpc::grpc::Future<CommonRecoResponse *>());
  }

  std::shared_ptr<::ks::kess::rpc::grpc::Channel> select_channel_ptr =
      base::GetChannel(client_ptr.get(), kess_group, shard);
  if (!select_channel_ptr) {
    CL_LOG(ERROR) << kess_name << " " << kess_cluster << " " << shard << " get channel failed";
    return std::make_pair(false, ::ks::kess::rpc::grpc::Future<CommonRecoResponse *>());
  }

  return std::make_pair(true, select_channel_ptr->Stub<kess::CommonRecoLeafService::Stub>().AsyncRecommend(
                                  options, *request_ptr, response_ptr, event_loop_group_ptr->SelectOne()));
}
typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, MerchantInferEnricher, MerchantInferEnricher)

}  // namespace platform
}  // namespace ks
