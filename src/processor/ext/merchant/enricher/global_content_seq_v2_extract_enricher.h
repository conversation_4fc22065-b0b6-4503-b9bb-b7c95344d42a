#pragma once

#include <map>
#include <memory>
#include <string>
#include <utility>
#include <vector>
#include "base/container/btree_set.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/processor/base/common_reco_base_processor.h"

namespace ks {
namespace platform {

class GlobalContentSeqV2ExtractEnricher : public CommonRecoBaseEnricher {
 public:
  GlobalContentSeqV2ExtractEnricher() {}
  ~GlobalContentSeqV2ExtractEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;

 private:
  std::string request_time_;
  std::string input_eshop_video_colossus_photo_id_;
  std::string input_eshop_video_colossus_author_id_;
  std::string input_eshop_video_colossus_play_time_;
  std::string input_eshop_video_colossus_duration_;
  std::string input_eshop_video_colossus_channel_;
  std::string input_eshop_video_colossus_label_;
  std::string input_eshop_video_colossus_timestamp_;
  std::string input_eshop_video_colossus_spu_id_;
  std::string input_eshop_video_colossus_category_;
  std::string input_live_colossus_live_id_;
  std::string input_live_colossus_timestamp_;
  std::string input_live_colossus_author_id_;
  std::string input_live_colossus_play_time_;
  std::string input_live_colossus_auto_play_time_;
  std::string input_live_colossus_hetu_tag_channel_;
  std::string input_live_colossus_cluster_id_;
  std::string input_live_colossus_label_;
  std::string input_live_colossus_audience_count_;
  std::string input_live_colossus_order_price_;
  std::string input_live_colossus_cart_top2_leaf_cate_;
  std::string input_live_colossus_cart_top3_leaf_cate_;
  std::string input_live_colossus_cart_top4_leaf_cate_;
  std::string input_live_colossus_cart_top1_cate1_;
  std::string input_live_colossus_cart_top1_cate2_;
  std::string input_live_colossus_cart_top1_cate3_;
  std::string input_aId_;
  std::string input_sCartItemCate3IdList_;
  std::string output_global_content_colossus_common_video_photo_id_;
  std::string output_global_content_colossus_common_author_id_;
  std::string output_global_content_colossus_common_play_time_;
  std::string output_global_content_colossus_common_video_duration_;
  std::string output_global_content_colossus_common_video_channel_;
  std::string output_global_content_colossus_common_video_label_;
  std::string output_global_content_colossus_common_lag_hour_;
  std::string output_global_content_colossus_common_lag_day_;
  std::string output_global_content_colossus_common_video_spu_id_;
  std::string output_global_content_colossus_common_video_cate1_;
  std::string output_global_content_colossus_common_video_cate2_;
  std::string output_global_content_colossus_common_video_cate3_;
  std::string output_global_content_colossus_common_live_live_id_;
  std::string output_global_content_colossus_common_live_auto_play_time_;
  std::string output_global_content_colossus_common_live_hetu_tag_channel_;
  std::string output_global_content_colossus_common_live_cluster_id_;
  std::string output_global_content_colossus_common_live_label_;
  std::string output_global_content_colossus_common_live_audience_count_;
  std::string output_global_content_colossus_common_live_order_price_;
  std::string output_global_content_colossus_common_live_cart_top2_leaf_cate_;
  std::string output_global_content_colossus_common_live_cart_top3_leaf_cate_;
  std::string output_global_content_colossus_common_live_cart_top4_leaf_cate_;
  std::string output_global_content_colossus_common_live_cart_top1_cate1_;
  std::string output_global_content_colossus_common_live_cart_top1_cate2_;
  std::string output_global_content_colossus_common_live_cart_top1_cate3_;
  std::string output_global_content_colossus_item_video_photo_id_;
  std::string output_global_content_colossus_item_author_id_;
  std::string output_global_content_colossus_item_play_time_;
  std::string output_global_content_colossus_item_video_duration_;
  std::string output_global_content_colossus_item_video_channel_;
  std::string output_global_content_colossus_item_video_label_;
  std::string output_global_content_colossus_item_lag_hour_;
  std::string output_global_content_colossus_item_lag_day_;
  std::string output_global_content_colossus_item_video_spu_id_;
  std::string output_global_content_colossus_item_video_cate1_;
  std::string output_global_content_colossus_item_video_cate2_;
  std::string output_global_content_colossus_item_video_cate3_;
  std::string output_global_content_colossus_item_live_live_id_;
  std::string output_global_content_colossus_item_live_auto_play_time_;
  std::string output_global_content_colossus_item_live_hetu_tag_channel_;
  std::string output_global_content_colossus_item_live_cluster_id_;
  std::string output_global_content_colossus_item_live_label_;
  std::string output_global_content_colossus_item_live_audience_count_;
  std::string output_global_content_colossus_item_live_order_price_;
  std::string output_global_content_colossus_item_live_cart_top2_leaf_cate_;
  std::string output_global_content_colossus_item_live_cart_top3_leaf_cate_;
  std::string output_global_content_colossus_item_live_cart_top4_leaf_cate_;
  std::string output_global_content_colossus_item_live_cart_top1_cate1_;
  std::string output_global_content_colossus_item_live_cart_top1_cate2_;
  std::string output_global_content_colossus_item_live_cart_top1_cate3_;

  void ProcessRecoResults(
    MutableRecoContextInterface* context,
    RecoResultConstIter begin, RecoResultConstIter end,
    int64 request_time,
    absl::Span<const int64> eshop_video_colossus_photo_id,
    absl::Span<const int64> eshop_video_colossus_author_id,
    absl::Span<const int64> eshop_video_colossus_play_time,
    absl::Span<const int64> eshop_video_colossus_duration,
    absl::Span<const int64> eshop_video_colossus_channel,
    absl::Span<const int64> eshop_video_colossus_label,
    absl::Span<const int64> eshop_video_colossus_timestamp,
    absl::Span<const int64> eshop_video_colossus_spu_id,
    absl::Span<const int64> eshop_video_colossus_category,
    absl::Span<const int64> live_colossus_live_id,
    absl::Span<const int64> live_colossus_timestamp,
    absl::Span<const int64> live_colossus_author_id,
    absl::Span<const int64> live_colossus_play_time,
    absl::Span<const int64> live_colossus_auto_play_time,
    absl::Span<const int64> live_colossus_hetu_tag_channel,
    absl::Span<const int64> live_colossus_cluster_id,
    absl::Span<const int64> live_colossus_label,
    absl::Span<const int64> live_colossus_audience_count,
    absl::Span<const int64> live_colossus_order_price,
    absl::Span<const int64> live_colossus_cart_top2_leaf_cate,
    absl::Span<const int64> live_colossus_cart_top3_leaf_cate,
    absl::Span<const int64> live_colossus_cart_top4_leaf_cate,
    absl::Span<const int64> live_colossus_cart_top1_cate1,
    absl::Span<const int64> live_colossus_cart_top1_cate2,
    absl::Span<const int64> live_colossus_cart_top1_cate3);

  DISALLOW_COPY_AND_ASSIGN(GlobalContentSeqV2ExtractEnricher);
};

}  // namespace platform
}  // namespace ks
