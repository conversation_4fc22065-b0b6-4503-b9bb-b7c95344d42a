#pragma once

#include <string>
#include <unordered_map>
#include <vector>
#include <set>

#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class MerchantListwiseNewSeqAttrV2Enricher : public CommonRecoBaseEnricher {
 public:
  MerchantListwiseNewSeqAttrV2Enricher() {}

  bool IsAsync() const override {
    return false;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct ItemInfo {
    int64 item_type = 0;
    int64 is_ad = 0;
    std::vector<double> fr_item_id_embedding {};
    int64 item_id = 0;
    int64 aid = 0;
    int64 price = 0;
    int64 trans_item_cid1 = 0;
    int64 trans_item_cid2 = 0;
    int64 trans_item_cid3 = 0;
    int64 item_in_live_explaining = 0;
    int64 trans_cid2 = 0;
    int64 trans_cid3 = 0;
    int64 trans_leaf = 0;
    int64 variant_tag = 0;
    int64 brand_id = 0;
    double ctr = 0.0;
    double cvr = 0.0;
    double mix_ad_ctr = 0.0;
    double mix_ad_cvr = 0.0;
    int64 mix_ad_ltv = 0;
  };

 private:
  bool InitProcessor() override;
  bool ParseAttrsConfig(const std::string& config_name,
                        std::unordered_map<std::string, std::string>* attrs_map);
  std::unordered_map<std::string, std::string> int_item_attrs_map_;
  std::unordered_map<std::string, std::string> double_item_attrs_map_;
  std::string seq_item_attr_name_;
  DISALLOW_COPY_AND_ASSIGN(MerchantListwiseNewSeqAttrV2Enricher);
};

}  // namespace platform
}  // namespace ks
