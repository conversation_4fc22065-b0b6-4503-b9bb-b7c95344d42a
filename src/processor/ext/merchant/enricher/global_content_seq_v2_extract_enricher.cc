#include "dragon/src/processor/ext/merchant/enricher/global_content_seq_v2_extract_enricher.h"

#include <memory>
#include <string>
#include <utility>
#include <vector>
#include <cmath>
#include <unordered_set>
#include <algorithm>
#include <queue>
#include <functional>
#include <unordered_map>

namespace ks {
namespace platform {

bool GlobalContentSeqV2ExtractEnricher::InitProcessor() {
  // input common attr
  request_time_ = config()->GetString("request_time", "");
  input_eshop_video_colossus_photo_id_ = config()->GetString("input_eshop_video_colossus_photo_id", "");
  input_eshop_video_colossus_author_id_ = config()->GetString("input_eshop_video_colossus_author_id", "");
  input_eshop_video_colossus_play_time_ = config()->GetString("input_eshop_video_colossus_play_time", "");
  input_eshop_video_colossus_duration_ = config()->GetString("input_eshop_video_colossus_duration", "");
  input_eshop_video_colossus_channel_ = config()->GetString("input_eshop_video_colossus_channel", "");
  input_eshop_video_colossus_label_ = config()->GetString("input_eshop_video_colossus_label", "");
  input_eshop_video_colossus_timestamp_ = config()->GetString("input_eshop_video_colossus_timestamp", "");
  input_eshop_video_colossus_spu_id_ = config()->GetString("input_eshop_video_colossus_spu_id", "");
  input_eshop_video_colossus_category_ = config()->GetString("input_eshop_video_colossus_category", "");
  input_live_colossus_live_id_ = config()->GetString("input_live_colossus_live_id", "");
  input_live_colossus_timestamp_ = config()->GetString("input_live_colossus_timestamp", "");
  input_live_colossus_author_id_ = config()->GetString("input_live_colossus_author_id", "");
  input_live_colossus_play_time_ = config()->GetString("input_live_colossus_play_time", "");
  input_live_colossus_auto_play_time_ = config()->GetString("input_live_colossus_auto_play_time", "");
  input_live_colossus_hetu_tag_channel_ = config()->GetString("input_live_colossus_hetu_tag_channel", "");
  input_live_colossus_cluster_id_ = config()->GetString("input_live_colossus_cluster_id", "");
  input_live_colossus_label_ = config()->GetString("input_live_colossus_label", "");
  input_live_colossus_audience_count_ = config()->GetString("input_live_colossus_audience_count", "");
  input_live_colossus_order_price_ = config()->GetString("input_live_colossus_order_price", "");
  input_live_colossus_cart_top2_leaf_cate_ = config()->GetString(
    "input_live_colossus_cart_top2_leaf_cate", "");
  input_live_colossus_cart_top3_leaf_cate_ = config()->GetString(
    "input_live_colossus_cart_top3_leaf_cate", "");
  input_live_colossus_cart_top4_leaf_cate_ = config()->GetString(
    "input_live_colossus_cart_top4_leaf_cate", "");
  input_live_colossus_cart_top1_cate1_ = config()->GetString("input_live_colossus_cart_top1_cate1", "");
  input_live_colossus_cart_top1_cate2_ = config()->GetString("input_live_colossus_cart_top1_cate2", "");
  input_live_colossus_cart_top1_cate3_ = config()->GetString("input_live_colossus_cart_top1_cate3", "");

  // input item attr
  input_aId_ = config()->GetString("input_aId", "");
  input_sCartItemCate3IdList_ = config()->GetString("input_sCartItemCate3IdList", "");

  // output common attr
  output_global_content_colossus_common_video_photo_id_ = config()->GetString(
    "output_global_content_colossus_common_video_photo_id", "");
  output_global_content_colossus_common_author_id_ = config()->GetString(
    "output_global_content_colossus_common_author_id", "");
  output_global_content_colossus_common_play_time_ = config()->GetString(
    "output_global_content_colossus_common_play_time", "");
  output_global_content_colossus_common_video_duration_ = config()->GetString(
    "output_global_content_colossus_common_video_duration", "");
  output_global_content_colossus_common_video_channel_ = config()->GetString(
    "output_global_content_colossus_common_video_channel", "");
  output_global_content_colossus_common_video_label_ = config()->GetString(
    "output_global_content_colossus_common_video_label", "");
  output_global_content_colossus_common_lag_hour_ = config()->GetString(
    "output_global_content_colossus_common_lag_hour", "");
  output_global_content_colossus_common_lag_day_ = config()->GetString(
    "output_global_content_colossus_common_lag_day", "");
  output_global_content_colossus_common_video_spu_id_ = config()->GetString(
    "output_global_content_colossus_common_video_spu_id", "");
  output_global_content_colossus_common_video_cate1_ = config()->GetString(
    "output_global_content_colossus_common_video_cate1", "");
  output_global_content_colossus_common_video_cate2_ = config()->GetString(
    "output_global_content_colossus_common_video_cate2", "");
  output_global_content_colossus_common_video_cate3_ = config()->GetString(
    "output_global_content_colossus_common_video_cate3", "");
  output_global_content_colossus_common_live_live_id_ = config()->GetString(
    "output_global_content_colossus_common_live_live_id", "");
  output_global_content_colossus_common_live_auto_play_time_ = config()->GetString(
    "output_global_content_colossus_common_live_auto_play_time", "");
  output_global_content_colossus_common_live_hetu_tag_channel_ = config()->GetString(
    "output_global_content_colossus_common_live_hetu_tag_channel", "");
  output_global_content_colossus_common_live_cluster_id_ = config()->GetString(
    "output_global_content_colossus_common_live_cluster_id", "");
  output_global_content_colossus_common_live_label_ = config()->GetString(
    "output_global_content_colossus_common_live_label", "");
  output_global_content_colossus_common_live_audience_count_ = config()->GetString(
    "output_global_content_colossus_common_live_audience_count", "");
  output_global_content_colossus_common_live_order_price_ = config()->GetString(
    "output_global_content_colossus_common_live_order_price", "");
  output_global_content_colossus_common_live_cart_top2_leaf_cate_ = config()->GetString(
    "output_global_content_colossus_common_live_cart_top2_leaf_cate", "");
  output_global_content_colossus_common_live_cart_top3_leaf_cate_ = config()->GetString(
    "output_global_content_colossus_common_live_cart_top3_leaf_cate", "");
  output_global_content_colossus_common_live_cart_top4_leaf_cate_ = config()->GetString(
    "output_global_content_colossus_common_live_cart_top4_leaf_cate", "");
  output_global_content_colossus_common_live_cart_top1_cate1_ = config()->GetString(
    "output_global_content_colossus_common_live_cart_top1_cate1", "");
  output_global_content_colossus_common_live_cart_top1_cate2_ = config()->GetString(
    "output_global_content_colossus_common_live_cart_top1_cate2", "");
  output_global_content_colossus_common_live_cart_top1_cate3_ = config()->GetString(
    "output_global_content_colossus_common_live_cart_top1_cate3", "");

  // output item attr
  output_global_content_colossus_item_video_photo_id_ = config()->GetString(
    "output_global_content_colossus_item_video_photo_id", "");
  output_global_content_colossus_item_author_id_ = config()->GetString(
    "output_global_content_colossus_item_author_id", "");
  output_global_content_colossus_item_play_time_ = config()->GetString(
    "output_global_content_colossus_item_play_time", "");
  output_global_content_colossus_item_video_duration_ = config()->GetString(
    "output_global_content_colossus_item_video_duration", "");
  output_global_content_colossus_item_video_channel_ = config()->GetString(
    "output_global_content_colossus_item_video_channel", "");
  output_global_content_colossus_item_video_label_ = config()->GetString(
    "output_global_content_colossus_item_video_label", "");
  output_global_content_colossus_item_lag_hour_ = config()->GetString(
    "output_global_content_colossus_item_lag_hour", "");
  output_global_content_colossus_item_lag_day_ = config()->GetString(
    "output_global_content_colossus_item_lag_day", "");
  output_global_content_colossus_item_video_spu_id_ = config()->GetString(
    "output_global_content_colossus_item_video_spu_id", "");
  output_global_content_colossus_item_video_cate1_ = config()->GetString(
    "output_global_content_colossus_item_video_cate1", "");
  output_global_content_colossus_item_video_cate2_ = config()->GetString(
    "output_global_content_colossus_item_video_cate2", "");
  output_global_content_colossus_item_video_cate3_ = config()->GetString(
    "output_global_content_colossus_item_video_cate3", "");
  output_global_content_colossus_item_live_live_id_ = config()->GetString(
    "output_global_content_colossus_item_live_live_id", "");
  output_global_content_colossus_item_live_auto_play_time_ = config()->GetString(
    "output_global_content_colossus_item_live_auto_play_time", "");
  output_global_content_colossus_item_live_hetu_tag_channel_ = config()->GetString(
    "output_global_content_colossus_item_live_hetu_tag_channel", "");
  output_global_content_colossus_item_live_cluster_id_ = config()->GetString(
    "output_global_content_colossus_item_live_cluster_id", "");
  output_global_content_colossus_item_live_label_ = config()->GetString(
    "output_global_content_colossus_item_live_label", "");
  output_global_content_colossus_item_live_audience_count_ = config()->GetString(
    "output_global_content_colossus_item_live_audience_count", "");
  output_global_content_colossus_item_live_order_price_ = config()->GetString(
    "output_global_content_colossus_item_live_order_price", "");
  output_global_content_colossus_item_live_cart_top2_leaf_cate_ = config()->GetString(
    "output_global_content_colossus_item_live_cart_top2_leaf_cate", "");
  output_global_content_colossus_item_live_cart_top3_leaf_cate_ = config()->GetString(
    "output_global_content_colossus_item_live_cart_top3_leaf_cate", "");
  output_global_content_colossus_item_live_cart_top4_leaf_cate_ = config()->GetString(
    "output_global_content_colossus_item_live_cart_top4_leaf_cate", "");
  output_global_content_colossus_item_live_cart_top1_cate1_ = config()->GetString(
    "output_global_content_colossus_item_live_cart_top1_cate1", "");
  output_global_content_colossus_item_live_cart_top1_cate2_ = config()->GetString(
    "output_global_content_colossus_item_live_cart_top1_cate2", "");
  output_global_content_colossus_item_live_cart_top1_cate3_ = config()->GetString(
    "output_global_content_colossus_item_live_cart_top1_cate3", "");

  return true;
}

void GlobalContentSeqV2ExtractEnricher::Enrich(MutableRecoContextInterface *context,
  RecoResultConstIter begin, RecoResultConstIter end) {
  if (context == nullptr) {
  CL_LOG(ERROR) << "context is null";
  return;
  }

  auto request_time_ptr = context->GetIntCommonAttr(request_time_);
  if (!request_time_ptr.has_value()) {
  CL_LOG(ERROR) << "request_time is null";
  return;
  }
  const auto& request_time = *request_time_ptr;

  auto eshop_video_colossus_photo_id_ptr = context->GetIntListCommonAttr(
  input_eshop_video_colossus_photo_id_);
  if (!eshop_video_colossus_photo_id_ptr.has_value()) {
  CL_LOG(ERROR) << "eshop_video_colossus_photo_id is null";
  eshop_video_colossus_photo_id_ptr = absl::make_optional<std::vector<int64>>();
  }
  const auto& eshop_video_colossus_photo_id = *eshop_video_colossus_photo_id_ptr;

  auto eshop_video_colossus_author_id_ptr = context->GetIntListCommonAttr(
  input_eshop_video_colossus_author_id_);
  if (!eshop_video_colossus_author_id_ptr.has_value()) {
  CL_LOG(ERROR) << "eshop_video_colossus_author_id is invalid";
  eshop_video_colossus_author_id_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (eshop_video_colossus_author_id_ptr->size() != eshop_video_colossus_photo_id_ptr->size()) {
  CL_LOG(ERROR) << "eshop_video_colossus_author_id_ptr size not equal";
  return;
  }
  const auto& eshop_video_colossus_author_id = *eshop_video_colossus_author_id_ptr;

  auto eshop_video_colossus_play_time_ptr = context->GetIntListCommonAttr(
  input_eshop_video_colossus_play_time_);
  if (!eshop_video_colossus_play_time_ptr.has_value()) {
  CL_LOG(ERROR) << "eshop_video_colossus_play_time is invalid";
  eshop_video_colossus_play_time_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (eshop_video_colossus_play_time_ptr->size() != eshop_video_colossus_photo_id_ptr->size()) {
  CL_LOG(ERROR) << "eshop_video_colossus_play_time_ptr size not equal";
  return;
  }
  const auto& eshop_video_colossus_play_time = *eshop_video_colossus_play_time_ptr;

  auto eshop_video_colossus_duration_ptr = context->GetIntListCommonAttr(
  input_eshop_video_colossus_duration_);
  if (!eshop_video_colossus_duration_ptr.has_value()) {
  CL_LOG(ERROR) << "eshop_video_colossus_duration is invalid";
  eshop_video_colossus_duration_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (eshop_video_colossus_duration_ptr->size() != eshop_video_colossus_photo_id_ptr->size()) {
  CL_LOG(ERROR) << "eshop_video_colossus_duration_ptr size not equal";
  return;
  }
  const auto& eshop_video_colossus_duration = *eshop_video_colossus_duration_ptr;

  auto eshop_video_colossus_channel_ptr = context->GetIntListCommonAttr(input_eshop_video_colossus_channel_);
  if (!eshop_video_colossus_channel_ptr.has_value()) {
  CL_LOG(ERROR) << "eshop_video_colossus_channel is invalid";
  eshop_video_colossus_channel_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (eshop_video_colossus_channel_ptr->size() != eshop_video_colossus_photo_id_ptr->size()) {
  CL_LOG(ERROR) << "eshop_video_colossus_channel_ptr size not equal";
  return;
  }
  const auto& eshop_video_colossus_channel = *eshop_video_colossus_channel_ptr;

  auto eshop_video_colossus_label_ptr = context->GetIntListCommonAttr(input_eshop_video_colossus_label_);
  if (!eshop_video_colossus_label_ptr.has_value()) {
  CL_LOG(ERROR) << "eshop_video_colossus_label is invalid";
  eshop_video_colossus_label_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (eshop_video_colossus_label_ptr->size() != eshop_video_colossus_photo_id_ptr->size()) {
  CL_LOG(ERROR) << "eshop_video_colossus_label_ptr size not equal";
  return;
  }
  const auto& eshop_video_colossus_label = *eshop_video_colossus_label_ptr;

  auto eshop_video_colossus_timestamp_ptr = context->GetIntListCommonAttr(
  input_eshop_video_colossus_timestamp_);
  if (!eshop_video_colossus_timestamp_ptr.has_value()) {
  CL_LOG(ERROR) << "eshop_video_colossus_timestamp is invalid";
  eshop_video_colossus_timestamp_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (eshop_video_colossus_timestamp_ptr->size() != eshop_video_colossus_photo_id_ptr->size()) {
  CL_LOG(ERROR) << "eshop_video_colossus_timestamp_ptr size not equal";
  return;
  }
  const auto& eshop_video_colossus_timestamp = *eshop_video_colossus_timestamp_ptr;

  auto eshop_video_colossus_spu_id_ptr = context->GetIntListCommonAttr(input_eshop_video_colossus_spu_id_);
  if (!eshop_video_colossus_spu_id_ptr.has_value()) {
  CL_LOG(ERROR) << "eshop_video_colossus_spu_id is invalid";
  eshop_video_colossus_spu_id_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (eshop_video_colossus_spu_id_ptr->size() != eshop_video_colossus_photo_id_ptr->size()) {
  CL_LOG(ERROR) << "eshop_video_colossus_spu_id_ptr size not equal";
  return;
  }
  const auto& eshop_video_colossus_spu_id = *eshop_video_colossus_spu_id_ptr;

  auto eshop_video_colossus_category_ptr = context->GetIntListCommonAttr(
  input_eshop_video_colossus_category_);
  if (!eshop_video_colossus_category_ptr.has_value()) {
  CL_LOG(ERROR) << "eshop_video_colossus_category is invalid";
  eshop_video_colossus_category_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (eshop_video_colossus_category_ptr->size() != eshop_video_colossus_photo_id_ptr->size()) {
  CL_LOG(ERROR) << "eshop_video_colossus_category_ptr size not equal";
  return;
  }
  const auto& eshop_video_colossus_category = *eshop_video_colossus_category_ptr;

  auto live_colossus_live_id_ptr = context->GetIntListCommonAttr(input_live_colossus_live_id_);
  if (!live_colossus_live_id_ptr.has_value()) {
  CL_LOG(ERROR) << "live_colossus_live_id is invalid";
  live_colossus_live_id_ptr = absl::make_optional<std::vector<int64>>();
  }
  const auto& live_colossus_live_id = *live_colossus_live_id_ptr;

  auto live_colossus_timestamp_ptr = context->GetIntListCommonAttr(input_live_colossus_timestamp_);
  if (!live_colossus_timestamp_ptr.has_value()) {
  CL_LOG(ERROR) << "live_colossus_timestamp is invalid";
  live_colossus_timestamp_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (live_colossus_timestamp_ptr->size() != live_colossus_live_id_ptr->size()) {
  CL_LOG(ERROR) << "live_colossus_timestamp_ptr size not equal";
  return;
  }
  const auto& live_colossus_timestamp = *live_colossus_timestamp_ptr;

  auto live_colossus_author_id_ptr = context->GetIntListCommonAttr(input_live_colossus_author_id_);
  if (!live_colossus_author_id_ptr.has_value()) {
  CL_LOG(ERROR) << "live_colossus_author_id is invalid";
  live_colossus_author_id_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (live_colossus_author_id_ptr->size() != live_colossus_live_id_ptr->size()) {
  CL_LOG(ERROR) << "live_colossus_author_id_ptr size not equal";
  return;
  }
  const auto& live_colossus_author_id = *live_colossus_author_id_ptr;

  auto live_colossus_play_time_ptr = context->GetIntListCommonAttr(input_live_colossus_play_time_);
  if (!live_colossus_play_time_ptr.has_value()) {
  CL_LOG(ERROR) << "live_colossus_play_time is invalid";
  live_colossus_play_time_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (live_colossus_play_time_ptr->size() != live_colossus_live_id_ptr->size()) {
  CL_LOG(ERROR) << "live_colossus_play_time_ptr size not equal";
  return;
  }
  const auto& live_colossus_play_time = *live_colossus_play_time_ptr;

  auto live_colossus_auto_play_time_ptr = context->GetIntListCommonAttr(input_live_colossus_auto_play_time_);
  if (!live_colossus_auto_play_time_ptr.has_value()) {
  CL_LOG(ERROR) << "live_colossus_auto_play_time is invalid";
  live_colossus_auto_play_time_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (live_colossus_auto_play_time_ptr->size() != live_colossus_live_id_ptr->size()) {
  CL_LOG(ERROR) << "live_colossus_auto_play_time_ptr size not equal";
  return;
  }
  const auto& live_colossus_auto_play_time = *live_colossus_auto_play_time_ptr;

  auto live_colossus_hetu_tag_channel_ptr = context->GetIntListCommonAttr(
  input_live_colossus_hetu_tag_channel_);
  if (!live_colossus_hetu_tag_channel_ptr.has_value()) {
  CL_LOG(ERROR) << "live_colossus_hetu_tag_channel is invalid";
  live_colossus_hetu_tag_channel_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (live_colossus_hetu_tag_channel_ptr->size() != live_colossus_live_id_ptr->size()) {
  CL_LOG(ERROR) << "live_colossus_hetu_tag_channel_ptr size not equal";
  return;
  }
  const auto& live_colossus_hetu_tag_channel = *live_colossus_hetu_tag_channel_ptr;

  auto live_colossus_cluster_id_ptr = context->GetIntListCommonAttr(input_live_colossus_cluster_id_);
  if (!live_colossus_cluster_id_ptr.has_value()) {
  CL_LOG(ERROR) << "live_colossus_cluster_id is invalid";
  live_colossus_cluster_id_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (live_colossus_cluster_id_ptr->size() != live_colossus_live_id_ptr->size()) {
  CL_LOG(ERROR) << "live_colossus_cluster_id_ptr size not equal";
  return;
  }
  const auto& live_colossus_cluster_id = *live_colossus_cluster_id_ptr;

  auto live_colossus_label_ptr = context->GetIntListCommonAttr(input_live_colossus_label_);
  if (!live_colossus_label_ptr.has_value()) {
  CL_LOG(ERROR) << "live_colossus_label is invalid";
  live_colossus_label_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (live_colossus_label_ptr->size() != live_colossus_live_id_ptr->size()) {
  CL_LOG(ERROR) << "live_colossus_label_ptr size not equal";
  return;
  }
  const auto& live_colossus_label = *live_colossus_label_ptr;

  auto live_colossus_audience_count_ptr = context->GetIntListCommonAttr(input_live_colossus_audience_count_);
  if (!live_colossus_audience_count_ptr.has_value()) {
  CL_LOG(ERROR) << "live_colossus_audience_count is invalid";
  live_colossus_audience_count_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (live_colossus_audience_count_ptr->size() != live_colossus_live_id_ptr->size()) {
  CL_LOG(ERROR) << "live_colossus_audience_count_ptr size not equal";
  return;
  }
  const auto& live_colossus_audience_count = *live_colossus_audience_count_ptr;

  auto live_colossus_order_price_ptr = context->GetIntListCommonAttr(input_live_colossus_order_price_);
  if (!live_colossus_order_price_ptr.has_value()) {
  CL_LOG(ERROR) << "live_colossus_order_price is invalid";
  live_colossus_order_price_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (live_colossus_order_price_ptr->size() != live_colossus_live_id_ptr->size()) {
  CL_LOG(ERROR) << "live_colossus_order_price_ptr size not equal";
  return;
  }
  const auto& live_colossus_order_price = *live_colossus_order_price_ptr;

  auto live_colossus_cart_top2_leaf_cate_ptr = context->GetIntListCommonAttr(
  input_live_colossus_cart_top2_leaf_cate_);
  if (!live_colossus_cart_top2_leaf_cate_ptr.has_value()) {
  CL_LOG(ERROR) << "live_colossus_cart_top2_leaf_cate is invalid";
  live_colossus_cart_top2_leaf_cate_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (live_colossus_cart_top2_leaf_cate_ptr->size() != live_colossus_live_id_ptr->size()) {
  CL_LOG(ERROR) << "live_colossus_cart_top2_leaf_cate_ptr size not equal";
  return;
  }
  const auto& live_colossus_cart_top2_leaf_cate = *live_colossus_cart_top2_leaf_cate_ptr;

  auto live_colossus_cart_top3_leaf_cate_ptr = context->GetIntListCommonAttr(
  input_live_colossus_cart_top3_leaf_cate_);
  if (!live_colossus_cart_top3_leaf_cate_ptr.has_value()) {
  CL_LOG(ERROR) << "live_colossus_cart_top3_leaf_cate is invalid";
  live_colossus_cart_top3_leaf_cate_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (live_colossus_cart_top3_leaf_cate_ptr->size() != live_colossus_live_id_ptr->size()) {
  CL_LOG(ERROR) << "live_colossus_cart_top3_leaf_cate_ptr size not equal";
  return;
  }
  const auto& live_colossus_cart_top3_leaf_cate = *live_colossus_cart_top3_leaf_cate_ptr;

  auto live_colossus_cart_top4_leaf_cate_ptr = context->GetIntListCommonAttr(
  input_live_colossus_cart_top4_leaf_cate_);
  if (!live_colossus_cart_top4_leaf_cate_ptr.has_value()) {
  CL_LOG(ERROR) << "live_colossus_cart_top4_leaf_cate is invalid";
  live_colossus_cart_top4_leaf_cate_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (live_colossus_cart_top4_leaf_cate_ptr->size() != live_colossus_live_id_ptr->size()) {
  CL_LOG(ERROR) << "live_colossus_cart_top4_leaf_cate_ptr size not equal";
  return;
  }
  const auto& live_colossus_cart_top4_leaf_cate = *live_colossus_cart_top4_leaf_cate_ptr;

  auto live_colossus_cart_top1_cate1_ptr = context->GetIntListCommonAttr(
  input_live_colossus_cart_top1_cate1_);
  if (!live_colossus_cart_top1_cate1_ptr.has_value()) {
  CL_LOG(ERROR) << "live_colossus_cart_top1_cate1 is invalid";
  live_colossus_cart_top1_cate1_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (live_colossus_cart_top1_cate1_ptr->size() != live_colossus_live_id_ptr->size()) {
  CL_LOG(ERROR) << "live_colossus_cart_top1_cate1_ptr size not equal";
  return;
  }
  const auto& live_colossus_cart_top1_cate1 = *live_colossus_cart_top1_cate1_ptr;

  auto live_colossus_cart_top1_cate2_ptr = context->GetIntListCommonAttr(
  input_live_colossus_cart_top1_cate2_);
  if (!live_colossus_cart_top1_cate2_ptr.has_value()) {
  CL_LOG(ERROR) << "live_colossus_cart_top1_cate2 is invalid";
  live_colossus_cart_top1_cate2_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (live_colossus_cart_top1_cate2_ptr->size() != live_colossus_live_id_ptr->size()) {
  CL_LOG(ERROR) << "live_colossus_cart_top1_cate2_ptr size not equal";
  return;
  }
  const auto& live_colossus_cart_top1_cate2 = *live_colossus_cart_top1_cate2_ptr;

  auto live_colossus_cart_top1_cate3_ptr = context->GetIntListCommonAttr(
  input_live_colossus_cart_top1_cate3_);
  if (!live_colossus_cart_top1_cate3_ptr.has_value()) {
  CL_LOG(ERROR) << "live_colossus_cart_top1_cate3 is invalid";
  live_colossus_cart_top1_cate3_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (live_colossus_cart_top1_cate3_ptr->size() != live_colossus_live_id_ptr->size()) {
  CL_LOG(ERROR) << "live_colossus_cart_top1_cate3_ptr size not equal";
  return;
  }
  const auto& live_colossus_cart_top1_cate3 = *live_colossus_cart_top1_cate3_ptr;

  ProcessRecoResults(
    context, begin, end,
    request_time,
    eshop_video_colossus_photo_id,
    eshop_video_colossus_author_id,
    eshop_video_colossus_play_time,
    eshop_video_colossus_duration,
    eshop_video_colossus_channel,
    eshop_video_colossus_label,
    eshop_video_colossus_timestamp,
    eshop_video_colossus_spu_id,
    eshop_video_colossus_category,
    live_colossus_live_id,
    live_colossus_timestamp,
    live_colossus_author_id,
    live_colossus_play_time,
    live_colossus_auto_play_time,
    live_colossus_hetu_tag_channel,
    live_colossus_cluster_id,
    live_colossus_label,
    live_colossus_audience_count,
    live_colossus_order_price,
    live_colossus_cart_top2_leaf_cate,
    live_colossus_cart_top3_leaf_cate,
    live_colossus_cart_top4_leaf_cate,
    live_colossus_cart_top1_cate1,
    live_colossus_cart_top1_cate2,
    live_colossus_cart_top1_cate3);
}

void GlobalContentSeqV2ExtractEnricher::ProcessRecoResults(
  MutableRecoContextInterface* context,
  RecoResultConstIter begin, RecoResultConstIter end,
  int64 request_time,
  absl::Span<const int64> eshop_video_colossus_photo_id,
  absl::Span<const int64> eshop_video_colossus_author_id,
  absl::Span<const int64> eshop_video_colossus_play_time,
  absl::Span<const int64> eshop_video_colossus_duration,
  absl::Span<const int64> eshop_video_colossus_channel,
  absl::Span<const int64> eshop_video_colossus_label,
  absl::Span<const int64> eshop_video_colossus_timestamp,
  absl::Span<const int64> eshop_video_colossus_spu_id,
  absl::Span<const int64> eshop_video_colossus_category,
  absl::Span<const int64> live_colossus_live_id,
  absl::Span<const int64> live_colossus_timestamp,
  absl::Span<const int64> live_colossus_author_id,
  absl::Span<const int64> live_colossus_play_time,
  absl::Span<const int64> live_colossus_auto_play_time,
  absl::Span<const int64> live_colossus_hetu_tag_channel,
  absl::Span<const int64> live_colossus_cluster_id,
  absl::Span<const int64> live_colossus_label,
  absl::Span<const int64> live_colossus_audience_count,
  absl::Span<const int64> live_colossus_order_price,
  absl::Span<const int64> live_colossus_cart_top2_leaf_cate,
  absl::Span<const int64> live_colossus_cart_top3_leaf_cate,
  absl::Span<const int64> live_colossus_cart_top4_leaf_cate,
  absl::Span<const int64> live_colossus_cart_top1_cate1,
  absl::Span<const int64> live_colossus_cart_top1_cate2,
  absl::Span<const int64> live_colossus_cart_top1_cate3) {
  size_t reco_results_num = std::distance(begin, end);
  auto result_iter = begin;
  int short_seq_limit = 300;
  int long_seq_limit = 200;
  int global_content_short_video_len = 0;
  int global_content_short_live_len = 0;
  int video_index = static_cast<int>(eshop_video_colossus_timestamp.size()) - 1;
  int live_index = static_cast<int>(live_colossus_timestamp.size()) - 1;
  std::vector<int64> global_content_colossus_common_video_photo_id;
  std::vector<int64> global_content_colossus_common_author_id;
  std::vector<int64> global_content_colossus_common_play_time;
  std::vector<int64> global_content_colossus_common_video_duration;
  std::vector<int64> global_content_colossus_common_video_channel;
  std::vector<int64> global_content_colossus_common_video_label;
  std::vector<int64> global_content_colossus_common_lag_hour;
  std::vector<int64> global_content_colossus_common_lag_day;
  std::vector<int64> global_content_colossus_common_video_spu_id;
  std::vector<int64> global_content_colossus_common_video_cate1;
  std::vector<int64> global_content_colossus_common_video_cate2;
  std::vector<int64> global_content_colossus_common_video_cate3;
  std::vector<int64> global_content_colossus_common_live_live_id;
  std::vector<int64> global_content_colossus_common_live_auto_play_time;
  std::vector<int64> global_content_colossus_common_live_hetu_tag_channel;
  std::vector<int64> global_content_colossus_common_live_cluster_id;
  std::vector<int64> global_content_colossus_common_live_label;
  std::vector<int64> global_content_colossus_common_live_audience_count;
  std::vector<int64> global_content_colossus_common_live_order_price;
  std::vector<int64> global_content_colossus_common_live_cart_top2_leaf_cate;
  std::vector<int64> global_content_colossus_common_live_cart_top3_leaf_cate;
  std::vector<int64> global_content_colossus_common_live_cart_top4_leaf_cate;
  std::vector<int64> global_content_colossus_common_live_cart_top1_cate1;
  std::vector<int64> global_content_colossus_common_live_cart_top1_cate2;
  std::vector<int64> global_content_colossus_common_live_cart_top1_cate3;
  std::unordered_map<int64_t, std::vector<std::vector<int64_t>>> aid_index_map;
  std::unordered_map<int64_t, std::vector<std::vector<int64_t>>> cate3_index_map;

  while (video_index >= 0 || live_index >= 0) {
    if (video_index >= 0 && eshop_video_colossus_timestamp[video_index] > request_time - 120) {
      video_index--;
      continue;
    }
    if (live_index >= 0 && live_colossus_timestamp[live_index] > request_time - 120) {
      live_index--;
      continue;
    }
    if (video_index >= 0 && (live_index < 0 || eshop_video_colossus_timestamp[video_index] >
      live_colossus_timestamp[live_index])) {
      if (global_content_short_video_len + global_content_short_live_len < short_seq_limit) {
        global_content_colossus_common_video_photo_id.emplace_back(
          eshop_video_colossus_photo_id[video_index]);
        global_content_colossus_common_author_id.emplace_back(eshop_video_colossus_author_id[video_index]);
        global_content_colossus_common_play_time.emplace_back(eshop_video_colossus_play_time[video_index]);
        global_content_colossus_common_video_duration.emplace_back(
          eshop_video_colossus_duration[video_index]);
        global_content_colossus_common_video_channel.emplace_back(eshop_video_colossus_channel[video_index]);
        global_content_colossus_common_video_label.emplace_back(eshop_video_colossus_label[video_index]);
        global_content_colossus_common_lag_hour.emplace_back((request_time -
          eshop_video_colossus_timestamp[video_index]) / (3600));
        global_content_colossus_common_lag_day.emplace_back((request_time -
          eshop_video_colossus_timestamp[video_index]) / (3600 * 24));
        global_content_colossus_common_video_spu_id.emplace_back(eshop_video_colossus_spu_id[video_index]);
        global_content_colossus_common_video_cate1.emplace_back(
          (eshop_video_colossus_category[video_index] >> 32) & 0xffff);
        global_content_colossus_common_video_cate2.emplace_back(
          (eshop_video_colossus_category[video_index] >> 16) & 0xffff);
        global_content_colossus_common_video_cate3.emplace_back(
          (eshop_video_colossus_category[video_index]) & 0xffff);
        global_content_colossus_common_live_live_id.emplace_back(-1);
        global_content_colossus_common_live_auto_play_time.emplace_back(-1);
        global_content_colossus_common_live_hetu_tag_channel.emplace_back(-1);
        global_content_colossus_common_live_cluster_id.emplace_back(-1);
        global_content_colossus_common_live_label.emplace_back(-1);
        global_content_colossus_common_live_audience_count.emplace_back(-1);
        global_content_colossus_common_live_order_price.emplace_back(-1);
        global_content_colossus_common_live_cart_top2_leaf_cate.emplace_back(-1);
        global_content_colossus_common_live_cart_top3_leaf_cate.emplace_back(-1);
        global_content_colossus_common_live_cart_top4_leaf_cate.emplace_back(-1);
        global_content_colossus_common_live_cart_top1_cate1.emplace_back(-1);
        global_content_colossus_common_live_cart_top1_cate2.emplace_back(-1);
        global_content_colossus_common_live_cart_top1_cate3.emplace_back(-1);
        global_content_short_video_len++;
      } else {
        int64_t timestamp = eshop_video_colossus_timestamp[video_index];
        int64_t aid = eshop_video_colossus_author_id[video_index];
        int64_t cate3 = (eshop_video_colossus_category[video_index]) & 0xffff;
        auto& aid_index_list = aid_index_map[aid];
        if (aid_index_list.size() < long_seq_limit) {
          aid_index_list.emplace_back(std::vector<int64_t>{video_index, timestamp, 0});
        }
        if (cate3 > 0) {
          auto& cate3_index_list = cate3_index_map[cate3];
          if (cate3_index_list.size() < long_seq_limit) {
            cate3_index_list.emplace_back(std::vector<int64_t>{video_index, timestamp, 0});
          }
        }
      }
      video_index--;
    } else {
      if (((live_colossus_label[live_index] >> 8) & 1) > 0 && global_content_short_video_len +
      global_content_short_live_len < short_seq_limit) {
        global_content_colossus_common_video_photo_id.emplace_back(-1);
        global_content_colossus_common_author_id.emplace_back(live_colossus_author_id[live_index]);
        global_content_colossus_common_play_time.emplace_back(live_colossus_play_time[live_index]);
        global_content_colossus_common_video_duration.emplace_back(-1);
        global_content_colossus_common_video_channel.emplace_back(-1);
        global_content_colossus_common_video_label.emplace_back(-1);
        global_content_colossus_common_lag_hour.emplace_back(std::floor((request_time -
          live_colossus_timestamp[live_index]) / (3600)));
        global_content_colossus_common_lag_day.emplace_back(std::floor((request_time -
          live_colossus_timestamp[live_index]) / (3600 * 24)));
        global_content_colossus_common_video_spu_id.emplace_back(-1);
        global_content_colossus_common_video_cate1.emplace_back(-1);
        global_content_colossus_common_video_cate2.emplace_back(-1);
        global_content_colossus_common_video_cate3.emplace_back(-1);
        global_content_colossus_common_live_live_id.emplace_back(live_colossus_live_id[live_index]);
        global_content_colossus_common_live_auto_play_time.emplace_back(
          live_colossus_auto_play_time[live_index]);
        global_content_colossus_common_live_hetu_tag_channel.emplace_back(
          live_colossus_hetu_tag_channel[live_index]);
        global_content_colossus_common_live_cluster_id.emplace_back(live_colossus_cluster_id[live_index]);
        global_content_colossus_common_live_label.emplace_back(live_colossus_label[live_index]);
        global_content_colossus_common_live_audience_count.emplace_back(
          live_colossus_audience_count[live_index]);
        global_content_colossus_common_live_order_price.emplace_back(live_colossus_order_price[live_index]);
        global_content_colossus_common_live_cart_top2_leaf_cate.emplace_back(
          live_colossus_cart_top2_leaf_cate[live_index]);
        global_content_colossus_common_live_cart_top3_leaf_cate.emplace_back(
          live_colossus_cart_top3_leaf_cate[live_index]);
        global_content_colossus_common_live_cart_top4_leaf_cate.emplace_back(
          live_colossus_cart_top4_leaf_cate[live_index]);
        global_content_colossus_common_live_cart_top1_cate1.emplace_back(
          live_colossus_cart_top1_cate1[live_index]);
        global_content_colossus_common_live_cart_top1_cate2.emplace_back(
          live_colossus_cart_top1_cate2[live_index]);
        global_content_colossus_common_live_cart_top1_cate3.emplace_back(
          live_colossus_cart_top1_cate3[live_index]);
        global_content_short_live_len++;
      } else {
        int64_t timestamp = live_colossus_timestamp[live_index];
        int64_t aid = live_colossus_author_id[live_index];
        int64_t cate3 = live_colossus_cart_top1_cate3[live_index];
        auto& aid_index_list = aid_index_map[aid];
        if (aid_index_list.size() < long_seq_limit) {
          aid_index_list.emplace_back(std::vector<int64_t>{live_index, timestamp, 1});
        }
        if (cate3 > 0) {
          auto& cate3_index_list = cate3_index_map[cate3];
          if (cate3_index_list.size() < long_seq_limit) {
            cate3_index_list.emplace_back(std::vector<int64_t>{live_index, timestamp, 1});
          }
        }
      }
      live_index--;
    }
  }

  context->SetIntListCommonAttr(output_global_content_colossus_common_video_photo_id_, std::move(
    global_content_colossus_common_video_photo_id));
  context->SetIntListCommonAttr(output_global_content_colossus_common_author_id_, std::move(
    global_content_colossus_common_author_id));
  context->SetIntListCommonAttr(output_global_content_colossus_common_play_time_, std::move(
    global_content_colossus_common_play_time));
  context->SetIntListCommonAttr(output_global_content_colossus_common_video_duration_, std::move(
    global_content_colossus_common_video_duration));
  context->SetIntListCommonAttr(output_global_content_colossus_common_video_channel_, std::move(
    global_content_colossus_common_video_channel));
  context->SetIntListCommonAttr(output_global_content_colossus_common_video_label_, std::move(
    global_content_colossus_common_video_label));
  context->SetIntListCommonAttr(output_global_content_colossus_common_lag_hour_, std::move(
    global_content_colossus_common_lag_hour));
  context->SetIntListCommonAttr(output_global_content_colossus_common_lag_day_, std::move(
    global_content_colossus_common_lag_day));
  context->SetIntListCommonAttr(output_global_content_colossus_common_video_spu_id_, std::move(
    global_content_colossus_common_video_spu_id));
  context->SetIntListCommonAttr(output_global_content_colossus_common_video_cate1_, std::move(
    global_content_colossus_common_video_cate1));
  context->SetIntListCommonAttr(output_global_content_colossus_common_video_cate2_, std::move(
    global_content_colossus_common_video_cate2));
  context->SetIntListCommonAttr(output_global_content_colossus_common_video_cate3_, std::move(
    global_content_colossus_common_video_cate3));
  context->SetIntListCommonAttr(output_global_content_colossus_common_live_live_id_, std::move(
    global_content_colossus_common_live_live_id));
  context->SetIntListCommonAttr(output_global_content_colossus_common_live_auto_play_time_, std::move(
    global_content_colossus_common_live_auto_play_time));
  context->SetIntListCommonAttr(output_global_content_colossus_common_live_hetu_tag_channel_, std::move(
    global_content_colossus_common_live_hetu_tag_channel));
  context->SetIntListCommonAttr(output_global_content_colossus_common_live_cluster_id_, std::move(
    global_content_colossus_common_live_cluster_id));
  context->SetIntListCommonAttr(output_global_content_colossus_common_live_label_, std::move(
    global_content_colossus_common_live_label));
  context->SetIntListCommonAttr(output_global_content_colossus_common_live_audience_count_, std::move(
    global_content_colossus_common_live_audience_count));
  context->SetIntListCommonAttr(output_global_content_colossus_common_live_order_price_, std::move(
    global_content_colossus_common_live_order_price));
  context->SetIntListCommonAttr(output_global_content_colossus_common_live_cart_top2_leaf_cate_, std::move(
    global_content_colossus_common_live_cart_top2_leaf_cate));
  context->SetIntListCommonAttr(output_global_content_colossus_common_live_cart_top3_leaf_cate_, std::move(
    global_content_colossus_common_live_cart_top3_leaf_cate));
  context->SetIntListCommonAttr(output_global_content_colossus_common_live_cart_top4_leaf_cate_, std::move(
    global_content_colossus_common_live_cart_top4_leaf_cate));
  context->SetIntListCommonAttr(output_global_content_colossus_common_live_cart_top1_cate1_, std::move(
    global_content_colossus_common_live_cart_top1_cate1));
  context->SetIntListCommonAttr(output_global_content_colossus_common_live_cart_top1_cate2_, std::move(
    global_content_colossus_common_live_cart_top1_cate2));
  context->SetIntListCommonAttr(output_global_content_colossus_common_live_cart_top1_cate3_, std::move(
    global_content_colossus_common_live_cart_top1_cate3));

  for (size_t i = 0; i < reco_results_num; ++i) {
    if (result_iter == end) {
      CL_LOG(ERROR) << "result_iter out of bounds";
      break;
    }
    uint64 item_key = (*result_iter).item_key;

    std::vector<int64> global_content_colossus_item_video_photo_id;
    std::vector<int64> global_content_colossus_item_author_id;
    std::vector<int64> global_content_colossus_item_play_time;
    std::vector<int64> global_content_colossus_item_video_duration;
    std::vector<int64> global_content_colossus_item_video_channel;
    std::vector<int64> global_content_colossus_item_video_label;
    std::vector<int64> global_content_colossus_item_lag_hour;
    std::vector<int64> global_content_colossus_item_lag_day;
    std::vector<int64> global_content_colossus_item_video_spu_id;
    std::vector<int64> global_content_colossus_item_video_cate1;
    std::vector<int64> global_content_colossus_item_video_cate2;
    std::vector<int64> global_content_colossus_item_video_cate3;
    std::vector<int64> global_content_colossus_item_live_live_id;
    std::vector<int64> global_content_colossus_item_live_auto_play_time;
    std::vector<int64> global_content_colossus_item_live_hetu_tag_channel;
    std::vector<int64> global_content_colossus_item_live_cluster_id;
    std::vector<int64> global_content_colossus_item_live_label;
    std::vector<int64> global_content_colossus_item_live_audience_count;
    std::vector<int64> global_content_colossus_item_live_order_price;
    std::vector<int64> global_content_colossus_item_live_cart_top2_leaf_cate;
    std::vector<int64> global_content_colossus_item_live_cart_top3_leaf_cate;
    std::vector<int64> global_content_colossus_item_live_cart_top4_leaf_cate;
    std::vector<int64> global_content_colossus_item_live_cart_top1_cate1;
    std::vector<int64> global_content_colossus_item_live_cart_top1_cate2;
    std::vector<int64> global_content_colossus_item_live_cart_top1_cate3;

    auto aId_ptr = context->GetIntItemAttr(item_key, input_aId_);
    if (!aId_ptr.has_value()) {
      CL_LOG(ERROR) << "aId is null";
      aId_ptr = absl::make_optional<int64>(-1);
    }
    const auto& aId = *aId_ptr;

    auto sCartItemCate3IdList_ptr = context->GetIntListItemAttr(item_key, input_sCartItemCate3IdList_);
    if (!sCartItemCate3IdList_ptr.has_value()) {
      CL_LOG(ERROR) << "sCartItemCate3IdList is null";
      sCartItemCate3IdList_ptr = absl::make_optional<std::vector<int64>>();
    }
    std::unordered_set<int64> sCartItemCate3IdTop5Set(sCartItemCate3IdList_ptr->begin(),
    sCartItemCate3IdList_ptr->begin() + std::min(5, static_cast<int>(sCartItemCate3IdList_ptr->size())));

    struct ListNode {
      const std::vector<std::vector<int64_t>>* list;
      size_t index;

      ListNode(const std::vector<std::vector<int64_t>>* l, size_t i)
        : list(l), index(i) {}
    };

    auto cmp = [](const ListNode& a, const ListNode& b) {
      return (*a.list)[a.index][1] < (*b.list)[b.index][1];
    };

    std::priority_queue<ListNode, std::vector<ListNode>, decltype(cmp)> max_heap(cmp);

    auto add_to_heap = [&](const auto& map, const auto& key) {
      if (auto it = map.find(key); it != map.end()) {
        const auto& list = it->second;
        if (!list.empty()) {
          max_heap.emplace(&list, 0);
        }
      }
    };

    add_to_heap(aid_index_map, aId);

    for (auto cate3 : sCartItemCate3IdTop5Set) {
      add_to_heap(cate3_index_map, cate3);
    }

    std::unordered_set<uint64_t> seen_elements;
    auto make_key = [](const std::vector<int64_t>& elem) {
      return (static_cast<uint64_t>(elem[0]) << 32) | static_cast<uint32_t>(elem[2]);
    };

    std::vector<std::vector<int64_t>> merge_search_index_list;
    merge_search_index_list.reserve(long_seq_limit);

    while (!max_heap.empty() && merge_search_index_list.size() < long_seq_limit) {
      auto node = max_heap.top();
      max_heap.pop();

      const auto& element = (*node.list)[node.index];
      uint64_t key = make_key(element);

      if (seen_elements.find(key) == seen_elements.end()) {
        merge_search_index_list.push_back(element);
        seen_elements.insert(key);
      }

      if (node.index + 1 < node.list->size()) {
        max_heap.emplace(node.list, node.index + 1);
      }
    }

    for (size_t i = 0; i < merge_search_index_list.size(); ++i) {
      auto cur_index = merge_search_index_list[i][0];
      auto seq_flag = merge_search_index_list[i][2];
      if (seq_flag == 0) {
        global_content_colossus_item_video_photo_id.emplace_back(eshop_video_colossus_photo_id[cur_index]);
        global_content_colossus_item_author_id.emplace_back(eshop_video_colossus_author_id[cur_index]);
        global_content_colossus_item_play_time.emplace_back(eshop_video_colossus_play_time[cur_index]);
        global_content_colossus_item_video_duration.emplace_back(eshop_video_colossus_duration[cur_index]);
        global_content_colossus_item_video_channel.emplace_back(eshop_video_colossus_channel[cur_index]);
        global_content_colossus_item_video_label.emplace_back(eshop_video_colossus_label[cur_index]);
        global_content_colossus_item_lag_hour.emplace_back(std::floor((request_time -
          eshop_video_colossus_timestamp[cur_index]) / (3600)));
        global_content_colossus_item_lag_day.emplace_back(std::floor((request_time -
          eshop_video_colossus_timestamp[cur_index]) / (3600 * 24)));
        global_content_colossus_item_video_spu_id.emplace_back(eshop_video_colossus_spu_id[cur_index]);
        global_content_colossus_item_video_cate1.emplace_back(
          (eshop_video_colossus_category[cur_index] >> 32) & 0xffff);
        global_content_colossus_item_video_cate2.emplace_back(
          (eshop_video_colossus_category[cur_index] >> 16) & 0xffff);
        global_content_colossus_item_video_cate3.emplace_back(
          (eshop_video_colossus_category[cur_index]) & 0xffff);
        global_content_colossus_item_live_live_id.emplace_back(-1);
        global_content_colossus_item_live_auto_play_time.emplace_back(-1);
        global_content_colossus_item_live_hetu_tag_channel.emplace_back(-1);
        global_content_colossus_item_live_cluster_id.emplace_back(-1);
        global_content_colossus_item_live_label.emplace_back(-1);
        global_content_colossus_item_live_audience_count.emplace_back(-1);
        global_content_colossus_item_live_order_price.emplace_back(-1);
        global_content_colossus_item_live_cart_top2_leaf_cate.emplace_back(-1);
        global_content_colossus_item_live_cart_top3_leaf_cate.emplace_back(-1);
        global_content_colossus_item_live_cart_top4_leaf_cate.emplace_back(-1);
        global_content_colossus_item_live_cart_top1_cate1.emplace_back(-1);
        global_content_colossus_item_live_cart_top1_cate2.emplace_back(-1);
        global_content_colossus_item_live_cart_top1_cate3.emplace_back(-1);
      } else {
        global_content_colossus_item_video_photo_id.emplace_back(-1);
        global_content_colossus_item_author_id.emplace_back(live_colossus_author_id[cur_index]);
        global_content_colossus_item_play_time.emplace_back(live_colossus_play_time[cur_index]);
        global_content_colossus_item_video_duration.emplace_back(-1);
        global_content_colossus_item_video_channel.emplace_back(-1);
        global_content_colossus_item_video_label.emplace_back(-1);
        global_content_colossus_item_lag_hour.emplace_back(std::floor((request_time -
          live_colossus_timestamp[cur_index]) / (3600)));
        global_content_colossus_item_lag_day.emplace_back(std::floor((request_time -
          live_colossus_timestamp[cur_index]) / (3600 * 24)));
        global_content_colossus_item_video_spu_id.emplace_back(-1);
        global_content_colossus_item_video_cate1.emplace_back(-1);
        global_content_colossus_item_video_cate2.emplace_back(-1);
        global_content_colossus_item_video_cate3.emplace_back(-1);
        global_content_colossus_item_live_live_id.emplace_back(live_colossus_live_id[cur_index]);
        global_content_colossus_item_live_auto_play_time.emplace_back(
          live_colossus_auto_play_time[cur_index]);
        global_content_colossus_item_live_hetu_tag_channel.emplace_back(
          live_colossus_hetu_tag_channel[cur_index]);
        global_content_colossus_item_live_cluster_id.emplace_back(live_colossus_cluster_id[cur_index]);
        global_content_colossus_item_live_label.emplace_back(live_colossus_label[cur_index]);
        global_content_colossus_item_live_audience_count.emplace_back(
          live_colossus_audience_count[cur_index]);
        global_content_colossus_item_live_order_price.emplace_back(live_colossus_order_price[cur_index]);
        global_content_colossus_item_live_cart_top2_leaf_cate.emplace_back(
          live_colossus_cart_top2_leaf_cate[cur_index]);
        global_content_colossus_item_live_cart_top3_leaf_cate.emplace_back(
          live_colossus_cart_top3_leaf_cate[cur_index]);
        global_content_colossus_item_live_cart_top4_leaf_cate.emplace_back(
          live_colossus_cart_top4_leaf_cate[cur_index]);
        global_content_colossus_item_live_cart_top1_cate1.emplace_back(
          live_colossus_cart_top1_cate1[cur_index]);
        global_content_colossus_item_live_cart_top1_cate2.emplace_back(
          live_colossus_cart_top1_cate2[cur_index]);
        global_content_colossus_item_live_cart_top1_cate3.emplace_back(
          live_colossus_cart_top1_cate3[cur_index]);
      }
    }

    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_video_photo_id_, std::move(
      global_content_colossus_item_video_photo_id));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_author_id_, std::move(
      global_content_colossus_item_author_id));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_play_time_, std::move(
      global_content_colossus_item_play_time));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_video_duration_, std::move(
      global_content_colossus_item_video_duration));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_video_channel_, std::move(
      global_content_colossus_item_video_channel));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_video_label_, std::move(
      global_content_colossus_item_video_label));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_lag_hour_, std::move(
      global_content_colossus_item_lag_hour));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_lag_day_, std::move(
      global_content_colossus_item_lag_day));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_video_spu_id_, std::move(
      global_content_colossus_item_video_spu_id));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_video_cate1_, std::move(
      global_content_colossus_item_video_cate1));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_video_cate2_, std::move(
      global_content_colossus_item_video_cate2));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_video_cate3_, std::move(
      global_content_colossus_item_video_cate3));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_live_live_id_, std::move(
      global_content_colossus_item_live_live_id));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_live_auto_play_time_,
      std::move(global_content_colossus_item_live_auto_play_time));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_live_hetu_tag_channel_,
      std::move(global_content_colossus_item_live_hetu_tag_channel));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_live_cluster_id_,
      std::move(global_content_colossus_item_live_cluster_id));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_live_label_,
      std::move(global_content_colossus_item_live_label));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_live_audience_count_,
      std::move(global_content_colossus_item_live_audience_count));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_live_order_price_,
      std::move(global_content_colossus_item_live_order_price));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_live_cart_top2_leaf_cate_,
      std::move(global_content_colossus_item_live_cart_top2_leaf_cate));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_live_cart_top3_leaf_cate_,
      std::move(global_content_colossus_item_live_cart_top3_leaf_cate));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_live_cart_top4_leaf_cate_,
      std::move(global_content_colossus_item_live_cart_top4_leaf_cate));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_live_cart_top1_cate1_,
      std::move(global_content_colossus_item_live_cart_top1_cate1));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_live_cart_top1_cate2_,
      std::move(global_content_colossus_item_live_cart_top1_cate2));
    context->SetIntListItemAttr(item_key, output_global_content_colossus_item_live_cart_top1_cate3_,
      std::move(global_content_colossus_item_live_cart_top1_cate3));

    result_iter = std::next(result_iter);
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, GlobalContentSeqV2ExtractEnricher,
  GlobalContentSeqV2ExtractEnricher);
}  // namespace platform
}  // namespace ks
