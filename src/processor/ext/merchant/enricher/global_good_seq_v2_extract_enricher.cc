#include "dragon/src/processor/ext/merchant/enricher/global_good_seq_v2_extract_enricher.h"

#include <memory>
#include <string>
#include <utility>
#include <vector>
#include <cmath>
#include <unordered_set>
#include <algorithm>
#include <queue>
#include <functional>
#include <unordered_map>

namespace ks {
namespace platform {

bool GlobalGoodSeqV2ExtractEnricher::InitProcessor() {
  // input common attr
  request_time_ = config()->GetString("request_time", "");
  input_good_click_colossus_item_id_ = config()->GetString("input_good_click_colossus_item_id", "");
  input_good_click_colossus_timestamp_ = config()->GetString("input_good_click_colossus_timestamp", "");
  input_good_click_colossus_real_price_ = config()->GetString("input_good_click_colossus_real_price", "");
  input_good_click_colossus_click_from_ = config()->GetString("input_good_click_colossus_click_from", "");
  input_good_click_colossus_click_flow_type_ = config()->GetString(
    "input_good_click_colossus_click_flow_type", "");
  input_good_click_colossus_label_ = config()->GetString("input_good_click_colossus_label", "");
  input_good_click_colossus_category_ = config()->GetString("input_good_click_colossus_category", "");
  input_good_click_colossus_seller_id_ = config()->GetString("input_good_click_colossus_seller_id", "");
  input_good_click_colossus_real_seller_id_ = config()->GetString(
    "input_good_click_colossus_real_seller_id", "");
  input_good_order_colossus_item_id_ = config()->GetString("input_good_order_colossus_item_id", "");
  input_good_order_colossus_timestamp_ = config()->GetString("input_good_order_colossus_timestamp", "");
  input_good_order_colossus_order_price_ = config()->GetString("input_good_order_colossus_order_price", "");
  input_good_order_colossus_order_status_ = config()->GetString("input_good_order_colossus_order_status", "");
  input_good_order_colossus_item_price_ = config()->GetString("input_good_order_colossus_item_price", "");
  input_good_order_colossus_buy_from_ = config()->GetString("input_good_order_colossus_buy_from", "");
  input_good_order_colossus_buy_flow_type_ = config()->GetString(
    "input_good_order_colossus_buy_flow_type", "");
  input_good_order_colossus_express_cost_ = config()->GetString("input_good_order_colossus_express_cost", "");
  input_good_order_colossus_coupon_platform_amt_ = config()->GetString(
    "input_good_order_colossus_coupon_platform_amt", "");
  input_good_order_colossus_full_reduce_amt_ = config()->GetString(
    "input_good_order_colossus_full_reduce_amt", "");
  input_good_order_colossus_category_ = config()->GetString("input_good_order_colossus_category", "");
  input_good_order_colossus_seller_id_ = config()->GetString("input_good_order_colossus_seller_id", "");
  input_good_order_colossus_real_seller_id_ = config()->GetString(
    "input_good_order_colossus_real_seller_id", "");

  // input item attr
  input_aId_ = config()->GetString("input_aId", "");
  input_sCartItemCate1IdList_ = config()->GetString("input_sCartItemCate1IdList", "");

  // output common attr
  output_global_good_colossus_common_item_id_ = config()->GetString(
    "output_global_good_colossus_common_item_id", "");
  output_global_good_colossus_common_lag_hour_ = config()->GetString(
    "output_global_good_colossus_common_lag_hour", "");
  output_global_good_colossus_common_lag_day_ = config()->GetString(
    "output_global_good_colossus_common_lag_day", "");
  output_global_good_colossus_common_item_price_ = config()->GetString(
    "output_global_good_colossus_common_item_price", "");
  output_global_good_colossus_common_from_live_ = config()->GetString(
    "output_global_good_colossus_common_from_live", "");
  output_global_good_colossus_common_from_photo_ = config()->GetString(
    "output_global_good_colossus_common_from_photo", "");
  output_global_good_colossus_common_flow_type_ = config()->GetString(
    "output_global_good_colossus_common_flow_type", "");
  output_global_good_colossus_common_label_ = config()->GetString(
    "output_global_good_colossus_common_label", "");
  output_global_good_colossus_common_cate1_ = config()->GetString(
    "output_global_good_colossus_common_cate1", "");
  output_global_good_colossus_common_cate2_ = config()->GetString(
    "output_global_good_colossus_common_cate2", "");
  output_global_good_colossus_common_cate3_ = config()->GetString(
    "output_global_good_colossus_common_cate3", "");
  output_global_good_colossus_common_seller_id_ = config()->GetString(
    "output_global_good_colossus_common_seller_id", "");
  output_global_good_colossus_common_real_seller_id_ = config()->GetString(
    "output_global_good_colossus_common_real_seller_id", "");
  output_global_good_colossus_common_order_order_price_ = config()->GetString(
    "output_global_good_colossus_common_order_order_price", "");
  output_global_good_colossus_common_order_order_status_ = config()->GetString(
    "output_global_good_colossus_common_order_order_status", "");
  output_global_good_colossus_common_order_express_cost_ = config()->GetString(
    "output_global_good_colossus_common_order_express_cost", "");
  output_global_good_colossus_common_order_coupon_platform_amt_ = config()->GetString(
    "output_global_good_colossus_common_order_coupon_platform_amt", "");
  output_global_good_colossus_common_order_full_reduce_amt_ = config()->GetString(
    "output_global_good_colossus_common_order_full_reduce_amt", "");

  // output item attr
  output_global_good_colossus_item_item_id_ = config()->GetString(
    "output_global_good_colossus_item_item_id", "");
  output_global_good_colossus_item_lag_hour_ = config()->GetString(
    "output_global_good_colossus_item_lag_hour", "");
  output_global_good_colossus_item_lag_day_ = config()->GetString(
    "output_global_good_colossus_item_lag_day", "");
  output_global_good_colossus_item_item_price_ = config()->GetString(
    "output_global_good_colossus_item_item_price", "");
  output_global_good_colossus_item_from_live_ = config()->GetString(
    "output_global_good_colossus_item_from_live", "");
  output_global_good_colossus_item_from_photo_ = config()->GetString(
    "output_global_good_colossus_item_from_photo", "");
  output_global_good_colossus_item_flow_type_ = config()->GetString(
    "output_global_good_colossus_item_flow_type", "");
  output_global_good_colossus_item_label_ = config()->GetString("output_global_good_colossus_item_label", "");
  output_global_good_colossus_item_cate1_ = config()->GetString("output_global_good_colossus_item_cate1", "");
  output_global_good_colossus_item_cate2_ = config()->GetString("output_global_good_colossus_item_cate2", "");
  output_global_good_colossus_item_cate3_ = config()->GetString("output_global_good_colossus_item_cate3", "");
  output_global_good_colossus_item_seller_id_ = config()->GetString(
    "output_global_good_colossus_item_seller_id", "");
  output_global_good_colossus_item_real_seller_id_ = config()->GetString(
    "output_global_good_colossus_item_real_seller_id", "");
  output_global_good_colossus_item_order_order_price_ = config()->GetString(
    "output_global_good_colossus_item_order_order_price", "");
  output_global_good_colossus_item_order_order_status_ = config()->GetString(
    "output_global_good_colossus_item_order_order_status", "");
  output_global_good_colossus_item_order_express_cost_ = config()->GetString(
    "output_global_good_colossus_item_order_express_cost", "");
  output_global_good_colossus_item_order_coupon_platform_amt_ = config()->GetString(
    "output_global_good_colossus_item_order_coupon_platform_amt", "");
  output_global_good_colossus_item_order_full_reduce_amt_ = config()->GetString(
    "output_global_good_colossus_item_order_full_reduce_amt", "");

  return true;
}

void GlobalGoodSeqV2ExtractEnricher::Enrich(MutableRecoContextInterface *context,
                                                RecoResultConstIter begin, RecoResultConstIter end) {
  if (context == nullptr) {
    CL_LOG(ERROR) << "context is null";
    return;
  }

  auto request_time_ptr = context->GetIntCommonAttr(request_time_);
  if (!request_time_ptr.has_value()) {
    CL_LOG(ERROR) << "request_time is null";
    return;
  }
  const auto& request_time = *request_time_ptr;

  auto good_click_colossus_item_id_ptr = context->GetIntListCommonAttr(input_good_click_colossus_item_id_);
  if (!good_click_colossus_item_id_ptr.has_value()) {
    CL_LOG(ERROR) << "good_click_colossus_item_id is null";
    good_click_colossus_item_id_ptr = absl::make_optional<std::vector<int64>>();
  }
  const auto& good_click_colossus_item_id = *good_click_colossus_item_id_ptr;

  auto good_click_colossus_timestamp_ptr = context->GetIntListCommonAttr(
    input_good_click_colossus_timestamp_);
  if (!good_click_colossus_timestamp_ptr.has_value()) {
    CL_LOG(ERROR) << "good_click_colossus_timestamp is null";
    good_click_colossus_timestamp_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (good_click_colossus_timestamp_ptr->size() != good_click_colossus_item_id_ptr->size()) {
    CL_LOG(ERROR) << "good_click_colossus_timestamp_ptr size not equal";
    return;
  }
  const auto& good_click_colossus_timestamp = *good_click_colossus_timestamp_ptr;

  auto good_click_colossus_real_price_ptr = context->GetIntListCommonAttr(
    input_good_click_colossus_real_price_);
  if (!good_click_colossus_real_price_ptr.has_value()) {
    CL_LOG(ERROR) << "good_click_colossus_real_price is null";
    good_click_colossus_real_price_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (good_click_colossus_real_price_ptr->size() != good_click_colossus_item_id_ptr->size()) {
    CL_LOG(ERROR) << "good_click_colossus_real_price_ptr size not equal";
    return;
  }
  const auto& good_click_colossus_real_price = *good_click_colossus_real_price_ptr;

  auto good_click_colossus_click_from_ptr = context->GetIntListCommonAttr(
    input_good_click_colossus_click_from_);
  if (!good_click_colossus_click_from_ptr.has_value()) {
    CL_LOG(ERROR) << "good_click_colossus_click_from is null";
    good_click_colossus_click_from_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (good_click_colossus_click_from_ptr->size() != good_click_colossus_item_id_ptr->size()) {
    CL_LOG(ERROR) << "good_click_colossus_click_from_ptr size not equal";
    return;
  }
  const auto& good_click_colossus_click_from = *good_click_colossus_click_from_ptr;

  auto good_click_colossus_click_flow_type_ptr = context->GetIntListCommonAttr(
    input_good_click_colossus_click_flow_type_);
  if (!good_click_colossus_click_flow_type_ptr.has_value()) {
    CL_LOG(ERROR) << "good_click_colossus_click_flow_type is null";
    good_click_colossus_click_flow_type_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (good_click_colossus_click_flow_type_ptr->size() != good_click_colossus_item_id_ptr->size()) {
    CL_LOG(ERROR) << "good_click_colossus_click_flow_type_ptr size not equal";
    return;
  }
  const auto& good_click_colossus_click_flow_type = *good_click_colossus_click_flow_type_ptr;

  auto good_click_colossus_label_ptr = context->GetIntListCommonAttr(input_good_click_colossus_label_);
  if (!good_click_colossus_label_ptr.has_value()) {
    CL_LOG(ERROR) << "good_click_colossus_label is null";
    good_click_colossus_label_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (good_click_colossus_label_ptr->size() != good_click_colossus_item_id_ptr->size()) {
    CL_LOG(ERROR) << "good_click_colossus_label_ptr size not equal";
    return;
  }
  const auto& good_click_colossus_label = *good_click_colossus_label_ptr;

  auto good_click_colossus_category_ptr = context->GetIntListCommonAttr(input_good_click_colossus_category_);
  if (!good_click_colossus_category_ptr.has_value()) {
    CL_LOG(ERROR) << "good_click_colossus_category is null";
    good_click_colossus_category_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (good_click_colossus_category_ptr->size() != good_click_colossus_item_id_ptr->size()) {
    CL_LOG(ERROR) << "good_click_colossus_category_ptr size not equal";
    return;
  }
  const auto& good_click_colossus_category = *good_click_colossus_category_ptr;

  auto good_click_colossus_seller_id_ptr = context->GetIntListCommonAttr(
    input_good_click_colossus_seller_id_);
  if (!good_click_colossus_seller_id_ptr.has_value()) {
    CL_LOG(ERROR) << "good_click_colossus_seller_id is null";
    good_click_colossus_seller_id_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (good_click_colossus_seller_id_ptr->size() != good_click_colossus_item_id_ptr->size()) {
    CL_LOG(ERROR) << "good_click_colossus_seller_id_ptr size not equal";
    return;
  }
  const auto& good_click_colossus_seller_id = *good_click_colossus_seller_id_ptr;

  auto good_click_colossus_real_seller_id_ptr = context->GetIntListCommonAttr(
    input_good_click_colossus_real_seller_id_);
  if (!good_click_colossus_real_seller_id_ptr.has_value()) {
    CL_LOG(ERROR) << "good_click_colossus_real_seller_id is null";
    good_click_colossus_real_seller_id_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (good_click_colossus_real_seller_id_ptr->size() != good_click_colossus_item_id_ptr->size()) {
    CL_LOG(ERROR) << "good_click_colossus_real_seller_id_ptr size not equal";
    return;
  }
  const auto& good_click_colossus_real_seller_id = *good_click_colossus_real_seller_id_ptr;

  auto good_order_colossus_item_id_ptr = context->GetIntListCommonAttr(input_good_order_colossus_item_id_);
  if (!good_order_colossus_item_id_ptr.has_value()) {
    CL_LOG(ERROR) << "good_order_colossus_item_id is null";
    good_order_colossus_item_id_ptr = absl::make_optional<std::vector<int64>>();
  }
  const auto& good_order_colossus_item_id = *good_order_colossus_item_id_ptr;

  auto good_order_colossus_timestamp_ptr = context->GetIntListCommonAttr(
    input_good_order_colossus_timestamp_);
  if (!good_order_colossus_timestamp_ptr.has_value()) {
    CL_LOG(ERROR) << "good_order_colossus_timestamp is null";
    good_order_colossus_timestamp_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (good_order_colossus_timestamp_ptr->size() != good_order_colossus_item_id_ptr->size()) {
    CL_LOG(ERROR) << "good_order_colossus_timestamp_ptr size not equal";
    return;
  }
  const auto& good_order_colossus_timestamp = *good_order_colossus_timestamp_ptr;

  auto good_order_colossus_order_price_ptr = context->GetIntListCommonAttr(
    input_good_order_colossus_order_price_);
  if (!good_order_colossus_order_price_ptr.has_value()) {
    CL_LOG(ERROR) << "good_order_colossus_order_price is null";
    good_order_colossus_order_price_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (good_order_colossus_order_price_ptr->size() != good_order_colossus_item_id_ptr->size()) {
    CL_LOG(ERROR) << "good_order_colossus_order_price_ptr size not equal";
    return;
  }
  const auto& good_order_colossus_order_price = *good_order_colossus_order_price_ptr;

  auto good_order_colossus_order_status_ptr = context->GetIntListCommonAttr(
    input_good_order_colossus_order_status_);
  if (!good_order_colossus_order_status_ptr.has_value()) {
    CL_LOG(ERROR) << "good_order_colossus_order_status is null";
    good_order_colossus_order_status_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (good_order_colossus_order_status_ptr->size() != good_order_colossus_item_id_ptr->size()) {
    CL_LOG(ERROR) << "good_order_colossus_order_status_ptr size not equal";
    return;
  }
  const auto& good_order_colossus_order_status = *good_order_colossus_order_status_ptr;

  auto good_order_colossus_item_price_ptr = context->GetIntListCommonAttr(
    input_good_order_colossus_item_price_);
  if (!good_order_colossus_item_price_ptr.has_value()) {
    CL_LOG(ERROR) << "good_order_colossus_item_price is null";
    good_order_colossus_item_price_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (good_order_colossus_item_price_ptr->size() != good_order_colossus_item_id_ptr->size()) {
    CL_LOG(ERROR) << "good_order_colossus_item_price_ptr size not equal";
    return;
  }
  const auto& good_order_colossus_item_price = *good_order_colossus_item_price_ptr;

  auto good_order_colossus_buy_from_ptr = context->GetIntListCommonAttr(input_good_order_colossus_buy_from_);
  if (!good_order_colossus_buy_from_ptr.has_value()) {
    CL_LOG(ERROR) << "good_order_colossus_buy_from is null";
    good_order_colossus_buy_from_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (good_order_colossus_buy_from_ptr->size() != good_order_colossus_item_id_ptr->size()) {
    CL_LOG(ERROR) << "good_order_colossus_buy_from_ptr size not equal";
    return;
  }
  const auto& good_order_colossus_buy_from = *good_order_colossus_buy_from_ptr;

  auto good_order_colossus_buy_flow_type_ptr = context->GetIntListCommonAttr(
    input_good_order_colossus_buy_flow_type_);
  if (!good_order_colossus_buy_flow_type_ptr.has_value()) {
    CL_LOG(ERROR) << "good_order_colossus_buy_flow_type is null";
    good_order_colossus_buy_flow_type_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (good_order_colossus_buy_flow_type_ptr->size() != good_order_colossus_item_id_ptr->size()) {
    CL_LOG(ERROR) << "good_order_colossus_buy_flow_type_ptr size not equal";
    return;
  }
  const auto& good_order_colossus_buy_flow_type = *good_order_colossus_buy_flow_type_ptr;

  auto good_order_colossus_express_cost_ptr = context->GetIntListCommonAttr(
    input_good_order_colossus_express_cost_);
  if (!good_order_colossus_express_cost_ptr.has_value()) {
    CL_LOG(ERROR) << "good_order_colossus_express_cost is null";
    good_order_colossus_express_cost_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (good_order_colossus_express_cost_ptr->size() != good_order_colossus_item_id_ptr->size()) {
    CL_LOG(ERROR) << "good_order_colossus_express_cost_ptr size not equal";
    return;
  }
  const auto& good_order_colossus_express_cost = *good_order_colossus_express_cost_ptr;

  auto good_order_colossus_coupon_platform_amt_ptr = context->GetIntListCommonAttr(
    input_good_order_colossus_coupon_platform_amt_);
  if (!good_order_colossus_coupon_platform_amt_ptr.has_value()) {
    CL_LOG(ERROR) << "good_order_colossus_coupon_platform_amt is null";
    good_order_colossus_coupon_platform_amt_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (good_order_colossus_coupon_platform_amt_ptr->size() != good_order_colossus_item_id_ptr->size()) {
    CL_LOG(ERROR) << "good_order_colossus_coupon_platform_amt_ptr size not equal";
    return;
  }
  const auto& good_order_colossus_coupon_platform_amt = *good_order_colossus_coupon_platform_amt_ptr;

  auto good_order_colossus_full_reduce_amt_ptr = context->GetIntListCommonAttr(
    input_good_order_colossus_full_reduce_amt_);
  if (!good_order_colossus_full_reduce_amt_ptr.has_value()) {
    CL_LOG(ERROR) << "good_order_colossus_full_reduce_amt is null";
    good_order_colossus_full_reduce_amt_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (good_order_colossus_full_reduce_amt_ptr->size() != good_order_colossus_item_id_ptr->size()) {
    CL_LOG(ERROR) << "good_order_colossus_full_reduce_amt_ptr size not equal";
    return;
  }
  const auto& good_order_colossus_full_reduce_amt = *good_order_colossus_full_reduce_amt_ptr;

  auto good_order_colossus_category_ptr = context->GetIntListCommonAttr(input_good_order_colossus_category_);
  if (!good_order_colossus_category_ptr.has_value()) {
    CL_LOG(ERROR) << "good_order_colossus_category is null";
    good_order_colossus_category_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (good_order_colossus_category_ptr->size() != good_order_colossus_item_id_ptr->size()) {
    CL_LOG(ERROR) << "good_order_colossus_category_ptr size not equal";
    return;
  }
  const auto& good_order_colossus_category = *good_order_colossus_category_ptr;

  auto good_order_colossus_seller_id_ptr = context->GetIntListCommonAttr(
    input_good_order_colossus_seller_id_);
  if (!good_order_colossus_seller_id_ptr.has_value()) {
    CL_LOG(ERROR) << "good_order_colossus_seller_id is null";
    good_order_colossus_seller_id_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (good_order_colossus_seller_id_ptr->size() != good_order_colossus_item_id_ptr->size()) {
    CL_LOG(ERROR) << "good_order_colossus_seller_id_ptr size not equal";
    return;
  }
  const auto& good_order_colossus_seller_id = *good_order_colossus_seller_id_ptr;

  auto good_order_colossus_real_seller_id_ptr = context->GetIntListCommonAttr(
    input_good_order_colossus_real_seller_id_);
  if (!good_order_colossus_real_seller_id_ptr.has_value()) {
    CL_LOG(ERROR) << "good_order_colossus_real_seller_id is null";
    good_order_colossus_real_seller_id_ptr = absl::make_optional<std::vector<int64>>();
  }
  if (good_order_colossus_real_seller_id_ptr->size() != good_order_colossus_item_id_ptr->size()) {
    CL_LOG(ERROR) << "good_order_colossus_real_seller_id_ptr size not equal";
    return;
  }
  const auto& good_order_colossus_real_seller_id = *good_order_colossus_real_seller_id_ptr;

  ProcessRecoResults(
    context, begin, end,
    request_time,
    good_click_colossus_item_id,
    good_click_colossus_timestamp,
    good_click_colossus_real_price,
    good_click_colossus_click_from,
    good_click_colossus_click_flow_type,
    good_click_colossus_label,
    good_click_colossus_category,
    good_click_colossus_seller_id,
    good_click_colossus_real_seller_id,
    good_order_colossus_item_id,
    good_order_colossus_timestamp,
    good_order_colossus_order_price,
    good_order_colossus_order_status,
    good_order_colossus_item_price,
    good_order_colossus_buy_from,
    good_order_colossus_buy_flow_type,
    good_order_colossus_express_cost,
    good_order_colossus_coupon_platform_amt,
    good_order_colossus_full_reduce_amt,
    good_order_colossus_category,
    good_order_colossus_seller_id,
    good_order_colossus_real_seller_id);
}

void GlobalGoodSeqV2ExtractEnricher::ProcessRecoResults(
  MutableRecoContextInterface* context,
  RecoResultConstIter begin, RecoResultConstIter end,
  int64 request_time,
  absl::Span<const int64> good_click_colossus_item_id,
  absl::Span<const int64> good_click_colossus_timestamp,
  absl::Span<const int64> good_click_colossus_real_price,
  absl::Span<const int64> good_click_colossus_click_from,
  absl::Span<const int64> good_click_colossus_click_flow_type,
  absl::Span<const int64> good_click_colossus_label,
  absl::Span<const int64> good_click_colossus_category,
  absl::Span<const int64> good_click_colossus_seller_id,
  absl::Span<const int64> good_click_colossus_real_seller_id,
  absl::Span<const int64> good_order_colossus_item_id,
  absl::Span<const int64> good_order_colossus_timestamp,
  absl::Span<const int64> good_order_colossus_order_price,
  absl::Span<const int64> good_order_colossus_order_status,
  absl::Span<const int64> good_order_colossus_item_price,
  absl::Span<const int64> good_order_colossus_buy_from,
  absl::Span<const int64> good_order_colossus_buy_flow_type,
  absl::Span<const int64> good_order_colossus_express_cost,
  absl::Span<const int64> good_order_colossus_coupon_platform_amt,
  absl::Span<const int64> good_order_colossus_full_reduce_amt,
  absl::Span<const int64> good_order_colossus_category,
  absl::Span<const int64> good_order_colossus_seller_id,
  absl::Span<const int64> good_order_colossus_real_seller_id) {
  size_t reco_results_num = std::distance(begin, end);
  auto result_iter = begin;
  int short_seq_limit = 200;
  int long_seq_limit = 100;
  int global_good_short_click_len = 0;
  int global_good_short_order_len = 0;
  int click_index = static_cast<int>(good_click_colossus_timestamp.size()) - 1;
  int order_index = static_cast<int>(good_order_colossus_timestamp.size()) - 1;
  std::vector<int64> global_good_colossus_common_item_id;
  std::vector<int64> global_good_colossus_common_lag_hour;
  std::vector<int64> global_good_colossus_common_lag_day;
  std::vector<int64> global_good_colossus_common_item_price;
  std::vector<int64> global_good_colossus_common_from_live;
  std::vector<int64> global_good_colossus_common_from_photo;
  std::vector<int64> global_good_colossus_common_flow_type;
  std::vector<int64> global_good_colossus_common_label;
  std::vector<int64> global_good_colossus_common_cate1;
  std::vector<int64> global_good_colossus_common_cate2;
  std::vector<int64> global_good_colossus_common_cate3;
  std::vector<int64> global_good_colossus_common_seller_id;
  std::vector<int64> global_good_colossus_common_real_seller_id;
  std::vector<int64> global_good_colossus_common_order_order_price;
  std::vector<int64> global_good_colossus_common_order_order_status;
  std::vector<int64> global_good_colossus_common_order_express_cost;
  std::vector<int64> global_good_colossus_common_order_coupon_platform_amt;
  std::vector<int64> global_good_colossus_common_order_full_reduce_amt;
  std::unordered_map<int64_t, std::vector<std::vector<int64_t>>> aid_index_map;
  std::unordered_map<int64_t, std::vector<std::vector<int64_t>>> cate1_index_map;

  while (click_index >= 0 || order_index >= 0) {
    if (click_index >= 0 && good_click_colossus_timestamp[click_index] > request_time - 120) {
      click_index--;
      continue;
    }
    if (order_index >= 0 && good_order_colossus_timestamp[order_index] > request_time - 120) {
      order_index--;
      continue;
    }
    if (click_index >= 0 && (order_index < 0 || good_click_colossus_timestamp[click_index] >
      good_order_colossus_timestamp[order_index])) {
      if (global_good_short_click_len + global_good_short_order_len < short_seq_limit) {
        global_good_colossus_common_item_id.emplace_back(good_click_colossus_item_id[click_index]);
        global_good_colossus_common_lag_hour.emplace_back((request_time -
          good_click_colossus_timestamp[click_index]) / (3600));
        global_good_colossus_common_lag_day.emplace_back((request_time -
          good_click_colossus_timestamp[click_index]) / (3600 * 24));
        global_good_colossus_common_item_price.emplace_back(good_click_colossus_real_price[click_index]);
        if ((good_click_colossus_click_flow_type[click_index] / 65536) % 256 == 1) {
          global_good_colossus_common_from_live.emplace_back(good_click_colossus_click_from[click_index]);
          global_good_colossus_common_from_photo.emplace_back(-1);
        } else if ((good_click_colossus_click_flow_type[click_index] / 65536) % 256 == 2) {
          global_good_colossus_common_from_live.emplace_back(-1);
          global_good_colossus_common_from_photo.emplace_back(good_click_colossus_click_from[click_index]);
        } else {
          global_good_colossus_common_from_live.emplace_back(-1);
          global_good_colossus_common_from_photo.emplace_back(-1);
        }
        global_good_colossus_common_flow_type.emplace_back(good_click_colossus_click_flow_type[click_index]);
        global_good_colossus_common_label.emplace_back(good_click_colossus_label[click_index]);
        global_good_colossus_common_cate1.emplace_back(
          (good_click_colossus_category[click_index] >> 48) & 0xffff);
        global_good_colossus_common_cate2.emplace_back(
          (good_click_colossus_category[click_index] >> 32) & 0xffff);
        global_good_colossus_common_cate3.emplace_back(
          (good_click_colossus_category[click_index] >> 16) & 0xffff);
        global_good_colossus_common_seller_id.emplace_back(good_click_colossus_seller_id[click_index]);
        global_good_colossus_common_real_seller_id.emplace_back(
          good_click_colossus_real_seller_id[click_index]);
        global_good_colossus_common_order_order_price.emplace_back(-1);
        global_good_colossus_common_order_order_status.emplace_back(-1);
        global_good_colossus_common_order_express_cost.emplace_back(-1);
        global_good_colossus_common_order_coupon_platform_amt.emplace_back(-1);
        global_good_colossus_common_order_full_reduce_amt.emplace_back(-1);
        global_good_short_click_len++;
      } else {
        int64_t timestamp = good_click_colossus_timestamp[click_index];
        int64_t aid1 = good_click_colossus_seller_id[click_index];
        int64_t aid2 = good_click_colossus_real_seller_id[click_index];
        int64_t cate1 = (good_click_colossus_category[click_index] >> 48) & 0xffff;
        auto& aid1_index_list = aid_index_map[aid1];
        if (aid1_index_list.size() < long_seq_limit) {
          aid1_index_list.emplace_back(std::vector<int64_t>{click_index, timestamp, 0});
        }
        auto& aid2_index_list = aid_index_map[aid2];
        if (aid2 != aid1 && aid2_index_list.size() < long_seq_limit) {
          aid2_index_list.emplace_back(std::vector<int64_t>{click_index, timestamp, 0});
        }
        if (cate1 > 0) {
          auto& cate1_index_list = cate1_index_map[cate1];
          if (cate1_index_list.size() < long_seq_limit) {
            cate1_index_list.emplace_back(std::vector<int64_t>{click_index, timestamp, 0});
          }
        }
      }
      click_index--;
    } else {
      if (global_good_short_click_len + global_good_short_order_len < short_seq_limit) {
        global_good_colossus_common_item_id.emplace_back(good_order_colossus_item_id[order_index]);
        global_good_colossus_common_lag_hour.emplace_back((request_time -
          good_order_colossus_timestamp[order_index]) / (3600));
        global_good_colossus_common_lag_day.emplace_back((request_time -
          good_order_colossus_timestamp[order_index]) / (3600 * 24));
        global_good_colossus_common_item_price.emplace_back(good_order_colossus_item_price[order_index]);
        if ((good_order_colossus_buy_flow_type[order_index] / 65536) % 256 == 1) {
          global_good_colossus_common_from_live.emplace_back(good_order_colossus_buy_from[order_index]);
          global_good_colossus_common_from_photo.emplace_back(-1);
        } else if ((good_order_colossus_buy_flow_type[order_index] / 65536) % 256 == 2) {
          global_good_colossus_common_from_live.emplace_back(-1);
          global_good_colossus_common_from_photo.emplace_back(good_order_colossus_buy_from[order_index]);
        } else {
          global_good_colossus_common_from_live.emplace_back(-1);
          global_good_colossus_common_from_photo.emplace_back(-1);
        }
        global_good_colossus_common_flow_type.emplace_back(good_order_colossus_buy_flow_type[order_index]);
        global_good_colossus_common_label.emplace_back(-1);
        global_good_colossus_common_cate1.emplace_back(
          (good_order_colossus_category[order_index] >> 48) & 0xffff);
        global_good_colossus_common_cate2.emplace_back(
          (good_order_colossus_category[order_index] >> 32) & 0xffff);
        global_good_colossus_common_cate3.emplace_back(
          (good_order_colossus_category[order_index] >> 16) & 0xffff);
        global_good_colossus_common_seller_id.emplace_back(good_order_colossus_seller_id[order_index]);
        global_good_colossus_common_real_seller_id.emplace_back(
          good_order_colossus_real_seller_id[order_index]);
        global_good_colossus_common_order_order_price.emplace_back(
          good_order_colossus_order_price[order_index]);
        global_good_colossus_common_order_order_status.emplace_back(
          good_order_colossus_order_status[order_index]);
        global_good_colossus_common_order_express_cost.emplace_back(
          good_order_colossus_express_cost[order_index]);
        global_good_colossus_common_order_coupon_platform_amt.emplace_back(
          good_order_colossus_coupon_platform_amt[order_index]);
        global_good_colossus_common_order_full_reduce_amt.emplace_back(
          good_order_colossus_full_reduce_amt[order_index]);
        global_good_short_order_len++;
      } else {
        int64_t timestamp = good_order_colossus_timestamp[order_index];
        int64_t aid1 = good_order_colossus_seller_id[order_index];
        int64_t aid2 = good_order_colossus_real_seller_id[order_index];
        int64_t cate1 = (good_order_colossus_category[order_index] >> 48) & 0xffff;
        auto& aid1_index_list = aid_index_map[aid1];
        if (aid1_index_list.size() < long_seq_limit) {
          aid1_index_list.emplace_back(std::vector<int64_t>{order_index, timestamp, 1});
        }
        auto& aid2_index_list = aid_index_map[aid2];
        if (aid2 != aid1 && aid2_index_list.size() < long_seq_limit) {
          aid2_index_list.emplace_back(std::vector<int64_t>{order_index, timestamp, 1});
        }
        if (cate1 > 0) {
          auto& cate1_index_list = cate1_index_map[cate1];
          if (cate1_index_list.size() < long_seq_limit) {
            cate1_index_list.emplace_back(std::vector<int64_t>{order_index, timestamp, 1});
          }
        }
      }
      order_index--;
    }
  }

  context->SetIntListCommonAttr(output_global_good_colossus_common_item_id_,
    std::move(global_good_colossus_common_item_id));
  context->SetIntListCommonAttr(output_global_good_colossus_common_lag_hour_,
    std::move(global_good_colossus_common_lag_hour));
  context->SetIntListCommonAttr(output_global_good_colossus_common_lag_day_,
    std::move(global_good_colossus_common_lag_day));
  context->SetIntListCommonAttr(output_global_good_colossus_common_item_price_,
    std::move(global_good_colossus_common_item_price));
  context->SetIntListCommonAttr(output_global_good_colossus_common_from_live_,
    std::move(global_good_colossus_common_from_live));
  context->SetIntListCommonAttr(output_global_good_colossus_common_from_photo_,
    std::move(global_good_colossus_common_from_photo));
  context->SetIntListCommonAttr(output_global_good_colossus_common_flow_type_,
    std::move(global_good_colossus_common_flow_type));
  context->SetIntListCommonAttr(output_global_good_colossus_common_label_,
    std::move(global_good_colossus_common_label));
  context->SetIntListCommonAttr(output_global_good_colossus_common_cate1_,
    std::move(global_good_colossus_common_cate1));
  context->SetIntListCommonAttr(output_global_good_colossus_common_cate2_,
    std::move(global_good_colossus_common_cate2));
  context->SetIntListCommonAttr(output_global_good_colossus_common_cate3_,
    std::move(global_good_colossus_common_cate3));
  context->SetIntListCommonAttr(output_global_good_colossus_common_seller_id_,
    std::move(global_good_colossus_common_seller_id));
  context->SetIntListCommonAttr(output_global_good_colossus_common_real_seller_id_,
    std::move(global_good_colossus_common_real_seller_id));
  context->SetIntListCommonAttr(output_global_good_colossus_common_order_order_price_,
    std::move(global_good_colossus_common_order_order_price));
  context->SetIntListCommonAttr(output_global_good_colossus_common_order_order_status_,
    std::move(global_good_colossus_common_order_order_status));
  context->SetIntListCommonAttr(output_global_good_colossus_common_order_express_cost_,
    std::move(global_good_colossus_common_order_express_cost));
  context->SetIntListCommonAttr(output_global_good_colossus_common_order_coupon_platform_amt_,
    std::move(global_good_colossus_common_order_coupon_platform_amt));
  context->SetIntListCommonAttr(output_global_good_colossus_common_order_full_reduce_amt_,
    std::move(global_good_colossus_common_order_full_reduce_amt));

  for (size_t i = 0; i < reco_results_num; ++i) {
    if (result_iter == end) {
      CL_LOG(ERROR) << "result_iter out of bounds";
      break;
    }
    uint64 item_key = (*result_iter).item_key;

    std::vector<int64> global_good_colossus_item_item_id;
    std::vector<int64> global_good_colossus_item_lag_hour;
    std::vector<int64> global_good_colossus_item_lag_day;
    std::vector<int64> global_good_colossus_item_item_price;
    std::vector<int64> global_good_colossus_item_from_live;
    std::vector<int64> global_good_colossus_item_from_photo;
    std::vector<int64> global_good_colossus_item_flow_type;
    std::vector<int64> global_good_colossus_item_label;
    std::vector<int64> global_good_colossus_item_cate1;
    std::vector<int64> global_good_colossus_item_cate2;
    std::vector<int64> global_good_colossus_item_cate3;
    std::vector<int64> global_good_colossus_item_seller_id;
    std::vector<int64> global_good_colossus_item_real_seller_id;
    std::vector<int64> global_good_colossus_item_order_order_price;
    std::vector<int64> global_good_colossus_item_order_order_status;
    std::vector<int64> global_good_colossus_item_order_express_cost;
    std::vector<int64> global_good_colossus_item_order_coupon_platform_amt;
    std::vector<int64> global_good_colossus_item_order_full_reduce_amt;

    auto aId_ptr = context->GetIntItemAttr(item_key, input_aId_);
    if (!aId_ptr.has_value()) {
      CL_LOG(ERROR) << "aId is null";
      aId_ptr = absl::make_optional<int64>(-1);
    }
    const auto& aId = *aId_ptr;

    auto sCartItemCate1IdList_ptr = context->GetIntListItemAttr(item_key, input_sCartItemCate1IdList_);
    if (!sCartItemCate1IdList_ptr.has_value()) {
      CL_LOG(ERROR) << "sCartItemCate1IdList is null";
      sCartItemCate1IdList_ptr = absl::make_optional<std::vector<int64>>();
    }
    std::unordered_set<int64> sCartItemCate1IdTop5Set(sCartItemCate1IdList_ptr->begin(),
    sCartItemCate1IdList_ptr->begin() + std::min(5, static_cast<int>(sCartItemCate1IdList_ptr->size())));

    struct ListNode {
      const std::vector<std::vector<int64_t>>* list;
      size_t index;

      ListNode(const std::vector<std::vector<int64_t>>* l, size_t i)
        : list(l), index(i) {}
    };

    auto cmp = [](const ListNode& a, const ListNode& b) {
      return (*a.list)[a.index][1] < (*b.list)[b.index][1];
    };

    std::priority_queue<ListNode, std::vector<ListNode>, decltype(cmp)> max_heap(cmp);

    auto add_to_heap = [&](const auto& map, const auto& key) {
      if (auto it = map.find(key); it != map.end()) {
        const auto& list = it->second;
        if (!list.empty()) {
          max_heap.emplace(&list, 0);
        }
      }
    };

    add_to_heap(aid_index_map, aId);

    for (auto cate1 : sCartItemCate1IdTop5Set) {
      add_to_heap(cate1_index_map, cate1);
    }

    std::unordered_set<uint64_t> seen_elements;
    auto make_key = [](const std::vector<int64_t>& elem) {
      return (static_cast<uint64_t>(elem[0]) << 32) | static_cast<uint32_t>(elem[2]);
    };

    std::vector<std::vector<int64_t>> merge_search_index_list;
    merge_search_index_list.reserve(long_seq_limit);

    while (!max_heap.empty() && merge_search_index_list.size() < long_seq_limit) {
      auto node = max_heap.top();
      max_heap.pop();

      const auto& element = (*node.list)[node.index];
      uint64_t key = make_key(element);

      if (seen_elements.find(key) == seen_elements.end()) {
        merge_search_index_list.push_back(element);
        seen_elements.insert(key);
      }

      if (node.index + 1 < node.list->size()) {
        max_heap.emplace(node.list, node.index + 1);
      }
    }

    for (size_t i = 0; i < merge_search_index_list.size(); ++i) {
      auto cur_index = merge_search_index_list[i][0];
      auto seq_flag = merge_search_index_list[i][2];
      if (seq_flag == 0) {
        global_good_colossus_item_item_id.emplace_back(good_click_colossus_item_id[cur_index]);
        global_good_colossus_item_lag_hour.emplace_back((request_time -
          good_click_colossus_timestamp[cur_index]) / (3600));
        global_good_colossus_item_lag_day.emplace_back((request_time -
          good_click_colossus_timestamp[cur_index]) / (3600 * 24));
        global_good_colossus_item_item_price.emplace_back(good_click_colossus_real_price[cur_index]);
        if ((good_click_colossus_click_flow_type[cur_index] / 65536) % 256 == 1) {
          global_good_colossus_item_from_live.emplace_back(good_click_colossus_click_from[cur_index]);
          global_good_colossus_item_from_photo.emplace_back(-1);
        } else if ((good_click_colossus_click_flow_type[cur_index] / 65536) % 256 == 2) {
          global_good_colossus_item_from_live.emplace_back(-1);
          global_good_colossus_item_from_photo.emplace_back(good_click_colossus_click_from[cur_index]);
        } else {
          global_good_colossus_item_from_live.emplace_back(-1);
          global_good_colossus_item_from_photo.emplace_back(-1);
        }
        global_good_colossus_item_flow_type.emplace_back(good_click_colossus_click_flow_type[cur_index]);
        global_good_colossus_item_label.emplace_back(good_click_colossus_label[cur_index]);
        global_good_colossus_item_cate1.emplace_back(
          (good_click_colossus_category[cur_index] >> 48) & 0xffff);
        global_good_colossus_item_cate2.emplace_back(
          (good_click_colossus_category[cur_index] >> 32) & 0xffff);
        global_good_colossus_item_cate3.emplace_back(
          (good_click_colossus_category[cur_index] >> 16) & 0xffff);
        global_good_colossus_item_seller_id.emplace_back(good_click_colossus_seller_id[cur_index]);
        global_good_colossus_item_real_seller_id.emplace_back(good_click_colossus_real_seller_id[cur_index]);
        global_good_colossus_item_order_order_price.emplace_back(-1);
        global_good_colossus_item_order_order_status.emplace_back(-1);
        global_good_colossus_item_order_express_cost.emplace_back(-1);
        global_good_colossus_item_order_coupon_platform_amt.emplace_back(-1);
        global_good_colossus_item_order_full_reduce_amt.emplace_back(-1);
      } else {
        global_good_colossus_item_item_id.emplace_back(good_order_colossus_item_id[cur_index]);
        global_good_colossus_item_lag_hour.emplace_back((request_time -
          good_order_colossus_timestamp[cur_index]) / (3600));
        global_good_colossus_item_lag_day.emplace_back((request_time -
          good_order_colossus_timestamp[cur_index]) / (3600 * 24));
        global_good_colossus_item_item_price.emplace_back(good_order_colossus_item_price[cur_index]);
        if ((good_order_colossus_buy_flow_type[cur_index] / 65536) % 256 == 1) {
          global_good_colossus_item_from_live.emplace_back(good_order_colossus_buy_from[cur_index]);
          global_good_colossus_item_from_photo.emplace_back(-1);
        } else if ((good_order_colossus_buy_flow_type[cur_index] / 65536) % 256 == 2) {
          global_good_colossus_item_from_live.emplace_back(-1);
          global_good_colossus_item_from_photo.emplace_back(good_order_colossus_buy_from[cur_index]);
        } else {
          global_good_colossus_item_from_live.emplace_back(-1);
          global_good_colossus_item_from_photo.emplace_back(-1);
        }
        global_good_colossus_item_flow_type.emplace_back(good_order_colossus_buy_flow_type[cur_index]);
        global_good_colossus_item_label.emplace_back(-1);
        global_good_colossus_item_cate1.emplace_back(
          (good_order_colossus_category[cur_index] >> 48) & 0xffff);
        global_good_colossus_item_cate2.emplace_back(
          (good_order_colossus_category[cur_index] >> 32) & 0xffff);
        global_good_colossus_item_cate3.emplace_back(
          (good_order_colossus_category[cur_index] >> 16) & 0xffff);
        global_good_colossus_item_seller_id.emplace_back(good_order_colossus_seller_id[cur_index]);
        global_good_colossus_item_real_seller_id.emplace_back(good_order_colossus_real_seller_id[cur_index]);
        global_good_colossus_item_order_order_price.emplace_back(good_order_colossus_order_price[cur_index]);
        global_good_colossus_item_order_order_status.emplace_back(
          good_order_colossus_order_status[cur_index]);
        global_good_colossus_item_order_express_cost.emplace_back(
          good_order_colossus_express_cost[cur_index]);
        global_good_colossus_item_order_coupon_platform_amt.emplace_back(
          good_order_colossus_coupon_platform_amt[cur_index]);
        global_good_colossus_item_order_full_reduce_amt.emplace_back(
          good_order_colossus_full_reduce_amt[cur_index]);
      }
    }

    context->SetIntListItemAttr(item_key, output_global_good_colossus_item_item_id_,
      std::move(global_good_colossus_item_item_id));
    context->SetIntListItemAttr(item_key, output_global_good_colossus_item_lag_hour_,
      std::move(global_good_colossus_item_lag_hour));
    context->SetIntListItemAttr(item_key, output_global_good_colossus_item_lag_day_,
      std::move(global_good_colossus_item_lag_day));
    context->SetIntListItemAttr(item_key, output_global_good_colossus_item_item_price_,
      std::move(global_good_colossus_item_item_price));
    context->SetIntListItemAttr(item_key, output_global_good_colossus_item_from_live_,
      std::move(global_good_colossus_item_from_live));
    context->SetIntListItemAttr(item_key, output_global_good_colossus_item_from_photo_,
      std::move(global_good_colossus_item_from_photo));
    context->SetIntListItemAttr(item_key, output_global_good_colossus_item_flow_type_,
      std::move(global_good_colossus_item_flow_type));
    context->SetIntListItemAttr(item_key, output_global_good_colossus_item_label_,
      std::move(global_good_colossus_item_label));
    context->SetIntListItemAttr(item_key, output_global_good_colossus_item_cate1_,
      std::move(global_good_colossus_item_cate1));
    context->SetIntListItemAttr(item_key, output_global_good_colossus_item_cate2_,
      std::move(global_good_colossus_item_cate2));
    context->SetIntListItemAttr(item_key, output_global_good_colossus_item_cate3_,
      std::move(global_good_colossus_item_cate3));
    context->SetIntListItemAttr(item_key, output_global_good_colossus_item_seller_id_,
      std::move(global_good_colossus_item_seller_id));
    context->SetIntListItemAttr(item_key, output_global_good_colossus_item_real_seller_id_,
      std::move(global_good_colossus_item_real_seller_id));
    context->SetIntListItemAttr(item_key, output_global_good_colossus_item_order_order_price_,
      std::move(global_good_colossus_item_order_order_price));
    context->SetIntListItemAttr(item_key, output_global_good_colossus_item_order_order_status_,
      std::move(global_good_colossus_item_order_order_status));
    context->SetIntListItemAttr(item_key, output_global_good_colossus_item_order_express_cost_,
      std::move(global_good_colossus_item_order_express_cost));
    context->SetIntListItemAttr(item_key, output_global_good_colossus_item_order_coupon_platform_amt_,
      std::move(global_good_colossus_item_order_coupon_platform_amt));
    context->SetIntListItemAttr(item_key, output_global_good_colossus_item_order_full_reduce_amt_,
      std::move(global_good_colossus_item_order_full_reduce_amt));

    result_iter = std::next(result_iter);
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, GlobalGoodSeqV2ExtractEnricher,
  GlobalGoodSeqV2ExtractEnricher);
}  // namespace platform
}  // namespace ks
