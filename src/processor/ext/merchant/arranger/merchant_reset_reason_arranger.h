#pragma once

#include <string>

#include "dragon/src/processor/base/common_reco_base_arranger.h"

namespace ks {
namespace platform {

class MerchantResetReasonArranger : public CommonRecoBaseArranger {
 public:
  MerchantResetReasonArranger() {}
  RecoResultIter Arrange(
      MutableRecoContextInterface *context, RecoResultIter begin, RecoResultIter end) override;

 private:
  bool InitProcessor() override {
    reason_attr_ = config()->GetString("reason_attr");
    default_reason_ = config()->GetInt("default_reason", 9999);
    return true;
  }

  std::string reason_attr_;
  int default_reason_ = 0;

  DISALLOW_COPY_AND_ASSIGN(MerchantResetReasonArranger);
};

}  // namespace platform
}  // namespace ks

