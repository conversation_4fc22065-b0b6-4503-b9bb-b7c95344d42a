#include "dragon/src/processor/ext/merchant/arranger/merchant_uni_recall_retrieve_diff.h"

#include <algorithm>
#include <limits>
#include <memory>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <stdexcept>
#include <chrono>
#include <thread>
#include <mutex>
#include <iostream>
#include <functional>
#include <set>

namespace ks {
namespace platform {

std::mutex uni_recall_diff_mtx;
std::atomic_int64_t lastTime_uni_recall_diff(0);

std::string GenerateLogIdDiff() {
    std::lock_guard<std::mutex> lock(uni_recall_diff_mtx);
    auto now = std::chrono::high_resolution_clock::now().time_since_epoch();
    auto nanoseconds = std::chrono::duration_cast<std::chrono::nanoseconds>(now).count();
    while (nanoseconds <= lastTime_uni_recall_diff) {
        std::this_thread::yield();
        now = std::chrono::high_resolution_clock::now().time_since_epoch();
        nanoseconds = std::chrono::duration_cast<std::chrono::nanoseconds>(now).count();
    }
    return std::to_string(nanoseconds);
}

bool MerchantUniRecallRetrieveDiff::InitProcessor() {
  debug_ = config()->GetBoolean("debug", false);
  default_error_tolerance_ = config()->GetFloat("default_error_tolerance", 1e-4);
  auto *reason_pairs = config()->Get("reason_pairs");
  if (reason_pairs && reason_pairs->IsArray() && reason_pairs->array().size() > 0) {
    for (auto *c : reason_pairs->array()) {
      if (c) {
        auto reason_a = c->GetInt("base", 0);
        auto reason_b = c->GetInt("exp", 0);
        if (reason_a > 0 || reason_b > 0) {
          reason_pairs_.emplace_back(reason_a, reason_b);
        }
      }
    }
  }
  if (reason_pairs_.empty()) {
    LOG(ERROR) << "CommonRecoAttrDiffObserver init failed: no reason_pairs in "
                  << "json config, please check!!!!";
  }
  auto *diff_item_attrs = config()->Get("diff_item_attrs");
  if (diff_item_attrs && diff_item_attrs->IsArray() && diff_item_attrs->array().size() > 0) {
    for (auto *c : diff_item_attrs->array()) {
      if (c) {
        diff_item_attrs_.push_back(c->StringValue());
      }
    }
  }
  auto *diff_common_attrs = config()->Get("diff_common_attrs");
  if (diff_common_attrs && diff_common_attrs->IsArray() && diff_common_attrs->array().size() > 0) {
    for (auto *c : diff_common_attrs->array()) {
      if (c) {
        auto base_attr = c->GetString("base_attr");
        auto exp_attr = c->GetString("exp_attr");
        diff_common_attr_pairs_.emplace_back(base_attr, exp_attr);
      }
    }
  }

  if (IsDebug()) {
    log_id_ = GenerateLogIdDiff();
    for (const auto &q : reason_pairs_) {
      LOG(INFO) << "base: " << q.first << ", exp: " << q.second;
    }
  }
  return true;
}

RecoResultIter MerchantUniRecallRetrieveDiff::Arrange(
      MutableRecoContextInterface *context, RecoResultIter begin, RecoResultIter end) {
  size_t total_size = std::distance(begin, end);
  if (total_size <= 0) {
    if (IsDebug()) {
      CL_LOG_EVERY_N(ERROR, 100) << "intermix cancelled: empty item list!";
    }
    return end;
  }

  std::unordered_map<int64, std::vector<RecoResultIter>> result_map;
  for (RecoResultIter it = begin; it != end; ++it) {
    result_map[it->reason].push_back(it);
  }

  int64 common_attr_equal_count = 0;
  int64 common_attr_no_equal_count = 0;
  // common 侧 attr diff 对比
  for (auto iter : diff_common_attr_pairs_) {
    auto base_attr = iter.first;
    auto exp_attr = iter.second;
    if (auto base_val = context->GetIntCommonAttr(base_attr)) {
      if (auto exp_val = context->GetIntCommonAttr(exp_attr)) {
        if (*base_val != *exp_val) {
          common_attr_no_equal_count = 1;
          LOG(ERROR) << "base_attr: " << base_attr << ", exp_attr: " << exp_attr
                    << ", base_val: " << *base_val
                    << ", exp_val: " << *exp_val;
        }
      } else {
        common_attr_no_equal_count = 1;
        LOG(ERROR) << "base_attr: " << base_attr << ", exp_attr: " << exp_attr << ", has type diff or null";
      }
    } else if (auto base_val = context->GetDoubleCommonAttr(base_attr)) {
      if (auto exp_val = context->GetDoubleCommonAttr(exp_attr)) {
        if (std::abs(*base_val - *exp_val) > default_error_tolerance_) {
          common_attr_no_equal_count = 1;
          LOG(ERROR) << "base_attr: " << base_attr << ", exp_attr: " << exp_attr
                    << ", base_val: " << *base_val
                    << ", exp_val: " << *exp_val;
        }
      } else {
        common_attr_no_equal_count = 1;
        LOG(ERROR) << "base_attr: " << base_attr << ", exp_attr: " << exp_attr << ", has type diff or null";
      }
    } else if (auto base_val = context->GetStringCommonAttr(base_attr)) {
      if (auto exp_val = context->GetStringCommonAttr(exp_attr)) {
        if (*base_val != *exp_val) {
          common_attr_no_equal_count = 1;
          LOG(ERROR) << "base_attr: " << base_attr << ", exp_attr: " << exp_attr
                    << ", base_val: " << *base_val
                    << ", exp_val: " << *exp_val;
        }
      } else {
        common_attr_no_equal_count = 1;
        LOG(ERROR) << "base_attr: " << base_attr << ", exp_attr: " << exp_attr << ", has type diff or null";
      }
    } else if (auto base_val = context->GetIntListCommonAttr(base_attr)) {
      if (auto exp_val = context->GetIntListCommonAttr(exp_attr)) {
        auto base_val_list = *base_val;
        auto exp_val_list = *exp_val;
        if (base_val_list.size() != exp_val_list.size()) {
          common_attr_no_equal_count = 1;
          LOG(ERROR) << "base_attr: " << base_attr << ", exp_attr: " << exp_attr
                    << ", base_val_list.size: " << base_val_list.size()
                    << ", exp_val_list.size: " << exp_val_list.size();
        } else {
          for (int i = 0; i < base_val_list.size(); ++i) {
            if (base_val_list[i] != exp_val_list[i]) {
              common_attr_no_equal_count = 1;
              LOG(ERROR) << "base_val_list[" << i << "]: " << base_val_list[i]
                  << ", exp_val_list[" << i << "]: " << exp_val_list[i];
            }
          }
        }
      } else {
        common_attr_no_equal_count = 1;
        LOG(ERROR) << "base_attr: " << base_attr << ", exp_attr: " << exp_attr << ", has type diff or null";
      }
    } else if (auto base_val = context->GetDoubleListCommonAttr(base_attr)) {
      if (auto exp_val = context->GetDoubleListCommonAttr(exp_attr)) {
        auto base_val_list = *base_val;
        auto exp_val_list = *exp_val;
        if (base_val_list.size() != exp_val_list.size()) {
          common_attr_no_equal_count = 1;
          LOG(ERROR) <<"base_val_list.size: " << base_val_list.size()
                    << ", exp_val_list.size: " << exp_val_list.size();
        } else {
          for (int i = 0; i < base_val_list.size(); ++i) {
            if (std::abs(base_val_list[i] - exp_val_list[i]) > default_error_tolerance_) {
              common_attr_no_equal_count = 1;
              LOG(ERROR) << "base_val_list[" << i << "]: " << base_val_list[i]
                  << ", exp_val_list[" << i << "]: " << exp_val_list[i];
            }
          }
        }
      } else {
        common_attr_no_equal_count = 1;
        LOG(ERROR) << "base_attr: " << base_attr << ", exp_attr: " << exp_attr << ", has type diff or null";
      }
    } else if (auto base_val = context->GetStringListCommonAttr(base_attr)) {
      if (auto exp_val = context->GetStringListCommonAttr(exp_attr)) {
        auto base_val_list = *base_val;
        auto exp_val_list = *exp_val;
        if (base_val_list.size() != exp_val_list.size()) {
          common_attr_no_equal_count = 1;
          LOG(ERROR) << "base_attr: " << base_attr << ", exp_attr: " << exp_attr
                    << ", base_val_list.size: " << base_val_list.size()
                    << ", exp_val_list.size: " << exp_val_list.size();
        } else {
          for (int i = 0; i < base_val_list.size(); ++i) {
            if (base_val_list[i] != exp_val_list[i]) {
              common_attr_no_equal_count = 1;
              LOG(ERROR) << "base_val_list[" << i << "]: " << base_val_list[i]
                  << ", exp_val_list[" << i << "]: " << exp_val_list[i];
            }
          }
        }
      } else {
        common_attr_no_equal_count = 1;
        LOG(ERROR) << "base_attr: " << base_attr << ", exp_attr: " << exp_attr << ", has type diff or null";
      }
    }
    if (common_attr_no_equal_count == 0) {
      common_attr_equal_count = 1;
    }
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
      common_attr_equal_count * 1000, kPerfNs,
      "uni_recall_diff.retrieve_diff", GlobalHolder::GetServiceIdentifier(),
      context->GetRequestType(), GetName(), base_attr, exp_attr, "common_attr_equal_count");
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
      common_attr_no_equal_count * 1000, kPerfNs, "uni_recall_diff.retrieve_diff",
      GlobalHolder::GetServiceIdentifier(),
      context->GetRequestType(), GetName(), base_attr, exp_attr, "common_attr_no_equal_count");
    LOG(INFO) << "base_attr: " << base_attr << ", exp_attr: " << exp_attr
              << ", common_attr_equal_count: " << common_attr_equal_count
              << ", common_attr_no_equal_count: " << common_attr_no_equal_count;
  }

  std::unordered_map<std::string, ItemAttr*> diff_item_attrs_accessor;
  std::unordered_map<std::string, int64> item_attr_equal_count;
  std::unordered_map<std::string, int64> item_attr_no_equal_count;
  for (auto diff_attr : diff_item_attrs_) {
     auto *attr_accessor = context->GetItemAttrAccessor(diff_attr);
     diff_item_attrs_accessor[diff_attr] = attr_accessor;
     item_attr_equal_count[diff_attr] = 0;
     item_attr_no_equal_count[diff_attr] = 0;
  }

  for (int i = 0; i < reason_pairs_.size(); i++) {
    auto &q = reason_pairs_[i];
    auto base_reason = q.first;
    auto exp_reason = q.second;
    auto base_results = result_map[base_reason];
    auto exp_results = result_map[exp_reason];
    // if (base_results.empty() || exp_results.empty()) {
    //   continue;
    // }

    // item 覆盖率对比
    std::unordered_set<int64> base_keys;
    std::unordered_map<int64, bool> base_key_once;
    std::unordered_map<int64, RecoResultIter> base_map;
    for (auto base_it : base_results) {
        base_keys.insert(base_it->ItemKey());
        base_map[base_it->ItemKey()] = base_it;
    }

    std::unordered_set<int64> exp_keys;
    std::unordered_map<int64, bool> exp_key_once;
    std::unordered_map<int64, RecoResultIter> exp_map;
    for (auto exp_it : exp_results) {
        exp_keys.insert(exp_it->ItemKey());
        exp_map[exp_it->ItemKey()] = exp_it;
    }

    std::vector<RecoResultIter> base_overlapping_items;
    std::vector<RecoResultIter> exp_overlapping_items;
    for (auto exp_it : exp_results) {
        if (base_keys.find(exp_it->ItemKey()) != base_keys.end()) {
            if (exp_key_once[exp_it->ItemKey()] == false) {
                exp_key_once[exp_it->ItemKey()] = true;
                exp_overlapping_items.push_back(exp_it);
            }
        }
    }
    for (auto base_it : base_results) {
        if (exp_keys.find(base_it->ItemKey()) != exp_keys.end()) {
            if (base_key_once[base_it->ItemKey()] == false) {
              base_key_once[base_it->ItemKey()] = true;
              base_overlapping_items.push_back(base_it);
            }
        }
    }

    double base_item_overlap_ratio = 1000.0 * base_overlapping_items.size()
        / (base_results.size() > 0 ? base_results.size() : 1);
    double exp_item_overlap_ratio = 1000.0 * exp_overlapping_items.size()
        / (exp_results.size() > 0 ? exp_results.size() : 1);

    LOG(INFO) << "base_overlapping_items size:" << base_overlapping_items.size()
              << ", exp_overlapping_items size:" << exp_overlapping_items.size()
              << ", base_results size:" << base_results.size()
              << ", exp_results size:" << exp_results.size()
              << ", base_reason: " << base_reason
              << ", exp_reason: " << exp_reason
              << ", base_item_overlap_ratio: " << base_item_overlap_ratio / 1000
              << ", exp_item_overlap_ratio: " << exp_item_overlap_ratio / 1000;

    base::perfutil::PerfUtilWrapper::IntervalLogStash(
            base_item_overlap_ratio, kPerfNs, "uni_recall_diff.retrieve_diff",
            GlobalHolder::GetServiceIdentifier(), context->GetRequestType(),
            GetName(), std::to_string(base_reason), std::to_string(exp_reason), "base_item_overlap_ratio");
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
            exp_item_overlap_ratio, kPerfNs, "uni_recall_diff.retrieve_diff",
            GlobalHolder::GetServiceIdentifier(), context->GetRequestType(),
            GetName(), std::to_string(base_reason), std::to_string(exp_reason), "exp_item_overlap_ratio");

    // item 侧对比
    if (base_overlapping_items.size() == exp_overlapping_items.size()) {
      for (int i = 0; i < base_overlapping_items.size(); i++) {
        auto base_it = base_overlapping_items[i];
        auto exp_it = exp_overlapping_items[i];
        for (auto attr_accessor : diff_item_attrs_accessor) {
          auto attr_name = attr_accessor.first;
          auto base_attr_name = attr_accessor.first + "_base";
          auto exp_attr_name = attr_accessor.first + "_exp";

          if (auto int_val = base_it->GetIntAttr(attr_accessor.second)) {
            auto base_int_val = *int_val;
            if (auto exp_int_val_opt = exp_it->GetIntAttr(attr_accessor.second)) {
              auto exp_int_val = *exp_int_val_opt;
              if (base_int_val != exp_int_val) {
                LOG(ERROR) << "base_reason: " << base_reason << ", exp_reason: " << exp_reason
                          << ", base_int_val: " << base_int_val
                          << ", exp_int_val: " << exp_int_val;
              } else {
                item_attr_equal_count[attr_name]++;
              }
            } else {
              LOG(ERROR) << "base_reason: " << base_reason << ", exp_reason: " << exp_reason
                        << ", base_int_val: " << base_int_val
                        << ", exp_int_val: " << "null or type error";
            }
          } else if (auto double_val = base_it->GetDoubleAttr(attr_accessor.second)) {
            auto base_double_val = *double_val;
            if (auto exp_double_val_opt = exp_it->GetDoubleAttr(attr_accessor.second)) {
              auto exp_double_val = *exp_double_val_opt;
              if (std::abs(base_double_val - exp_double_val) > default_error_tolerance_) {
                LOG(ERROR) << "base_reason: " << base_reason << ", exp_reason: " << exp_reason
                          << ", base_double_val: " << base_double_val
                          << ", exp_double_val: " << exp_double_val;
              } else {
                item_attr_equal_count[attr_name]++;
              }
            } else {
              LOG(ERROR) << "base_reason: " << base_reason << ", exp_reason: " << exp_reason
                        << ", base_double_val: " << base_double_val
                        << ", exp_double_val: " << "null or type error";
            }
          } else if (auto string_val = base_it->GetStringAttr(attr_accessor.second)) {
            auto base_string_val = *string_val;
            if (auto exp_string_val_opt = exp_it->GetStringAttr(attr_accessor.second)) {
              auto exp_string_val = *exp_string_val_opt;
              if (base_string_val != exp_string_val) {
                LOG(ERROR) << "base_reason: " << base_reason << ", exp_reason: " << exp_reason
                          << ", base_string_val: " << base_string_val
                          << ", exp_string_val: " << exp_string_val;
              } else {
                item_attr_equal_count[attr_name]++;
              }
            } else {
              LOG(ERROR) << "base_reason: " << base_reason << ", exp_reason: " << exp_reason
                        << ", base_string_val: " << base_string_val
                        << ", exp_string_val: " << "null or type error";
            }
          }
        }
      }
    }
    for (auto attr_name : item_attr_equal_count) {
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
              1000.0 * attr_name.second / base_overlapping_items.size(),
              kPerfNs, "uni_recall_diff.retrieve_diff",
              GlobalHolder::GetServiceIdentifier(), context->GetRequestType(),
              GetName(), std::to_string(base_reason),
              std::to_string(exp_reason), attr_name.first);
    }
  }
  return end;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, MerchantUniRecallRetrieveDiff, MerchantUniRecallRetrieveDiff)

}  // namespace platform
}  // namespace ks
