#include "dragon/src/processor/ext/merchant/arranger/merchant_reset_reason_arranger.h"

#include "ks/reco_proto/proto/reco_exptag.pb.h"
#include "ks/reco_proto/proto/reco.pb.h"

namespace ks {
namespace platform {

RecoResultIter MerchantResetReasonArranger::Arrange(
    MutableRecoContextInterface *context, RecoResultIter begin, RecoResultIter end) {
  if (reason_attr_.empty()) {
    CL_LOG(ERROR) << "reason_attr_ empty";
    return end;
  }
  const auto *reason_attr_accessor = context->GetItemAttrAccessor(reason_attr_);
  if (!reason_attr_accessor) {
    CL_LOG(ERROR) << "reason_attr_accessor nullptr";
    return end;
  }
  int default_reason = default_reason_;
  std::for_each(begin, end, [reason_attr_accessor, default_reason, context]
      (CommonRecoResult &result) {
    result.reason = context->GetIntItemAttr(result, reason_attr_accessor).value_or(default_reason);
  });
  return end;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, MerchantResetReasonArranger, MerchantResetReasonArranger);

}  // namespace platform
}  // namespace ks
