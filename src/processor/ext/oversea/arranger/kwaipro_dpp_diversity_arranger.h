#pragma once

#include <algorithm>
#include <cmath>
#include <string>
#include <vector>

#include "Eigen/Dense"
#include "base/hash_function/city.h"
#include "dragon/src/processor/base/common_reco_base_arranger.h"

namespace ks {
namespace platform {
class KwaiProDppDiversityArranger : public CommonRecoBaseArranger {
 public:
  KwaiProDppDiversityArranger() {}

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  bool InitProcessor() override {
    item_theta_attr_name_ = config()->GetString("item_theta_attr");
    item_embedding_attr_name_ = config()->GetString("item_embedding_attr");
    mmu_embedding_attr_name_ = config()->GetString("mmu_embedding_attr");
    ranking_score_attr_name_ = config()->GetString("ranking_score_attr");
    output_score_attr_name_ = config()->GetString("output_score_attr", "");
    return true;
  }

  bool PrepareSimMatrix(MutableRecoContextInterface *context, RecoResultIter begin, RecoResultIter end,
                        int total_size, Eigen::MatrixXf *sim_matrix, const std::string &embedding_attr_name,
                        bool enable_weighted_embedding, Eigen::MatrixXf *lost_embedding_mask);

  bool PrepareKernelMatrix(MutableRecoContextInterface *context, RecoResultIter begin, RecoResultIter end,
                           int total_size, Eigen::MatrixXf *sim_matrix);

  double GetScore(MutableRecoContextInterface *context, RecoResultIter begin, RecoResultIter end,
                  const CommonRecoResult &item);

  bool Dpp(MutableRecoContextInterface *context, const Eigen::MatrixXf &kernel_matrix, int total_size,
           int dpp_max_length, double epsilon, std::vector<size_t> *selected_items,
           std::vector<bool> *selected_items_index);

 private:
  std::string item_theta_attr_name_;
  std::string item_embedding_attr_name_;
  std::string mmu_embedding_attr_name_;
  std::string ranking_score_attr_name_;
  std::string output_score_attr_name_;

  ItemAttr *item_theta_attr_accessor_ = nullptr;
  ItemAttr *item_embedding_attr_accessor_ = nullptr;
  ItemAttr *ranking_score_attr_accessor_ = nullptr;

  DISALLOW_COPY_AND_ASSIGN(KwaiProDppDiversityArranger);
};

}  // namespace platform
}  // namespace ks
