#include "dragon/src/processor/ext/oversea/arranger/kwaipro_dpp_diversity_arranger.h"

namespace ks {
namespace platform {

RecoResultIter KwaiProDppDiversityArranger::Arrange(MutableRecoContextInterface *context,
                                                    RecoResultIter begin, RecoResultIter end) {
  // step1: preCheck
  int64 start_ts = base::GetTimestamp();
  const int total_size = std::distance(begin, end);
  if (total_size <= 0) {
    return end;
  }
  // dpp 要计算的结果个数,一般最小不能小于请求结果数
  int64 limit = GetIntProcessorParameter(context, "limit", 8);
  if (limit <= 0) {
    CL_LOG(INFO) << "Dpp diversity cancelled, invalid limit:" << limit;
    return end;
  }

  if (limit > total_size) {
    limit = total_size;
  }
  // 最小增益阈值
  double epsilon = GetDoubleProcessorParameter(context, "epsilon", 0.000001);
  if (epsilon <= 0) {
    CL_LOG(INFO) << "Dpp diversity cancelled, invalid epsilon:" << epsilon;
    return end;
  }
  // Step2: prepare kernel matrix
  Eigen::MatrixXf kernel_matrix;
  if (!PrepareKernelMatrix(context, begin, end, total_size, &kernel_matrix)) {
    CL_LOG(WARNING) << "Dpp diversity PrepareKernelMatrix failed!";
    return end;
  }
  // Step3: dpp
  std::vector<size_t> selected_items;
  std::vector<bool> selected_items_index(total_size, false);
  if (!Dpp(context, kernel_matrix, total_size, limit, epsilon, &selected_items, &selected_items_index)) {
    CL_LOG(WARNING) << "Dpp diversity core compute logic failed";
    return end;
  }

  // Step4: get final result
  std::vector<CommonRecoResult> final_results;
  // 先取 dpp 结果集
  for (int i = 0; i < selected_items.size(); i++) {
    auto item = begin + selected_items[i];
    final_results.push_back(*item);
  }
  // 取 dpp 结果之外的 item 集合
  for (int index = 0; index < total_size; ++index) {
    if (selected_items_index[index]) {
      continue;
    }
    auto item = (begin + index);
    final_results.push_back(*item);
  }

  if (selected_items.size() < limit) {
    CL_LOG(INFO) << "dpp diversity, select " << selected_items.size() << " less than limit " << limit;
  }

  CL_LOG(INFO) << "dpp diversity finished, selected " << selected_items.size() << " items out of "
               << total_size;

  std::copy(final_results.begin(), final_results.end(), begin);

  // 用来算 dpp es rank
  if (!output_score_attr_name_.empty()) {
    ItemAttr *output_attr_accessor = context->GetItemAttrAccessor(output_score_attr_name_);
    int i = 1;
    for (auto it = begin; it != end; ++it) {
      context->SetDoubleItemAttr(*it, output_attr_accessor, 1.0 / (i++));
    }
  }
  return end;
}

inline double Exp(double x) {
  return std::exp(x);
}

bool KwaiProDppDiversityArranger::PrepareKernelMatrix(MutableRecoContextInterface *context,
                                                      RecoResultIter begin, RecoResultIter end,
                                                      int total_size, Eigen::MatrixXf *kernel_matrix) {
  // 生成基于粗排 embedding 的相似性矩阵
  Eigen::MatrixXf item_embedding_sim_matrix;
  if (!PrepareSimMatrix(context, begin, end, total_size, &item_embedding_sim_matrix,
                        item_embedding_attr_name_, false, nullptr)) {
    CL_LOG(WARNING) << "Dpp diversity " << item_embedding_attr_name_ << " PrepareSimMatrix failed!";
    return false;
  }

  // 相似性矩阵是否引入其他 embedding 做加权平均
  bool enable_weighted_embedding = GetBoolProcessorParameter(context, "enable_weighted_embedding");
  if (enable_weighted_embedding) {
    // 生成基于 mmu embedding 的相似性矩阵
    // beta 属于 [0, 1]
    double beta = GetDoubleProcessorParameter(context, "beta", 0.5);
    if (beta > 1 || beta < 0) {
      CL_LOG(INFO) << "dpp diversity, parameter beta " << beta << " is not valid!";
      return false;
    }

    Eigen::MatrixXf mmu_embedding_sim_matrix, lost_embedding_mask;
    if (!PrepareSimMatrix(context, begin, end, total_size, &mmu_embedding_sim_matrix,
                          mmu_embedding_attr_name_, true, &lost_embedding_mask)) {
      CL_LOG(WARNING) << "Dpp diversity " << mmu_embedding_attr_name_ << " PrepareSimMatrix failed!";
      return false;
    }
    // lost_embedding_mask 为 0 的位置表示没有 embedding
    // mmu_embedding_mask_matrix 为 0 的位置表示两个 item 至少一个没有 embedding, item_embedding_mask_matrix
    // 为 1 的位置表示两个 item 至少一个没有 embedding
    Eigen::MatrixXf mmu_embedding_mask_matrix = lost_embedding_mask.transpose() * lost_embedding_mask;
    Eigen::MatrixXf item_embedding_mask_matrix =
        Eigen::MatrixXf::Ones(total_size, total_size) - mmu_embedding_mask_matrix;
    (*kernel_matrix) =
        Eigen::MatrixXf::Constant(total_size, total_size, 1 - beta)
                .cwiseMax(item_embedding_mask_matrix)
                .array() *
            item_embedding_sim_matrix.array() +
        Eigen::MatrixXf::Constant(total_size, total_size, beta).cwiseMin(mmu_embedding_mask_matrix).array() *
            mmu_embedding_sim_matrix.array();
  } else {
    (*kernel_matrix) = item_embedding_sim_matrix.array();
  }

  bool enable_simscore_scale = GetBoolProcessorParameter(context, "enable_simscore_scale", false);
  std::vector<double> hyper_params;
  if (enable_simscore_scale) {
    hyper_params.clear();
    std::string hyper_params_str = GetStringProcessorParameter(context, "sim_scale_params_str", "1,1,0.8,10");
    std::vector<std::string> hyper_str =
        absl::StrSplit(hyper_params_str, absl::ByAnyChar(","), absl::SkipWhitespace());
    double val;
    for (std::string &elem : hyper_str) {
      if (absl::SimpleAtod(elem, &val)) {
        hyper_params.push_back(val);
      }
    }
    if (hyper_params.size() == 4) {
      (*kernel_matrix) =
          hyper_params[0] /
          (hyper_params[1] + (-(kernel_matrix->array() - hyper_params[2]) * hyper_params[3]).exp());
    }
  }

  // theta 属于 [0, 1)
  double theta = GetDoubleProcessorParameter(context, "theta", 0.0);
  if (theta >= 1 || theta < 0) {
    CL_LOG(INFO) << "dpp diversity, parameter theta " << theta << " is not valid!";
    return false;
  }
  double alpha = 0.5 * theta / (1 - theta);
  double tmp_theta;

  // 生成 ranking_score 矩阵
  Eigen::MatrixXf items_ranking_score;
  items_ranking_score.resize(total_size, total_size);
  items_ranking_score.setZero();
  ranking_score_attr_accessor_ = context->GetItemAttrAccessor(ranking_score_attr_name_);

  // 判断是否要进行 item_theta 设置
  bool allow_item_theta = GetBoolProcessorParameter(context, "allow_item_theta", false);
  item_theta_attr_accessor_ = context->GetItemAttrAccessor(item_theta_attr_name_);

  for (int i = 0; i < total_size; i++) {
    auto item = (begin + i);
    auto ranking_score = item->GetDoubleAttr(ranking_score_attr_accessor_);
    if (allow_item_theta) {
      auto item_theta = item->GetDoubleAttr(item_theta_attr_accessor_);
      if (item_theta) {
        tmp_theta = theta * (*item_theta);
        if (tmp_theta >= 1 || tmp_theta < 0) {
          CL_LOG(INFO) << "dpp diversity, parameter item theta " << tmp_theta << " is not valid!";
          tmp_theta = std::max(tmp_theta, 0.0);
          tmp_theta = std::min(tmp_theta, 0.99);
        }
      } else {
        CL_LOG(INFO) << "Dpp diveristy PrepareKernelMatrix get attr:" << item_theta_attr_name_ << " failed!";
        return false;
      }
      if (tmp_theta == 1) {
        CL_LOG(INFO) << "dpp diversity, parameter item theta transform failed!";
        return false;
      } else {
        alpha = 0.5 * tmp_theta / (1 - tmp_theta);
      }
    }
    if (ranking_score) {
      items_ranking_score(i, i) = Exp((*ranking_score) * alpha);
    } else {
      CL_LOG(INFO) << "Dpp diveristy PrepareKernelMatrix get attr:" << ranking_score_attr_name_ << " failed!";
      return false;
    }
  }

  // 生成 kernel matrix
  (*kernel_matrix) = items_ranking_score * (*kernel_matrix) * items_ranking_score;
  CL_LOG(INFO) << "successfully generate kernel matrix";
  return true;
}

inline size_t argmax(std::vector<float>::iterator begin, std::vector<float>::iterator end) {
  return std::distance(begin, std::max_element(begin, end));
}

bool KwaiProDppDiversityArranger::Dpp(MutableRecoContextInterface *context,
                                      const Eigen::MatrixXf &kernel_matrix, int total_size, int limit,
                                      double epsilon, std::vector<size_t> *selected_items,
                                      std::vector<bool> *selected_items_index) {
  bool allow_less_limit = GetBoolProcessorParameter(context, "allow_results_less_limit", false);

  selected_items->clear();
  Eigen::MatrixXf c;
  c.resize(limit, total_size);
  c.setZero();

  Eigen::VectorXf d = kernel_matrix.diagonal();
  std::vector<float> diag_scores(d.data(), d.data() + d.size());
  // 先取第一个 item
  size_t j = argmax(diag_scores.begin(), diag_scores.end());
  // 结果索引集
  selected_items->push_back(j);
  selected_items_index->at(j) = true;

  float ei = 0.0;
  while (selected_items->size() < limit) {
    int k = selected_items->size() - 1;
    for (size_t i = 0; i < total_size; i++) {
      if (!selected_items_index->at(i)) {
        if (k == 0) {
          ei = kernel_matrix(j, i) / sqrt(diag_scores[j]);
        } else {
          ei = (kernel_matrix(j, i) - c.col(j).head(k).dot(c.col(i).head(k))) / sqrt(diag_scores[j]);
        }
        c(k, i) = ei;
        diag_scores[i] = diag_scores[i] - ei * ei;
      }
    }
    diag_scores[j] = 0;
    j = argmax(diag_scores.begin(), diag_scores.end());
    if (diag_scores[j] < epsilon) {
      if (allow_less_limit) {
        CL_LOG(INFO) << "dpp diversity, diag_scores " << j << " less than epsilon " << epsilon;
        break;
      }
      return false;
    }
    selected_items->push_back(j);
    selected_items_index->at(j) = true;
  }

  CL_LOG(INFO) << "Dpp diversity, select " << selected_items->size() << " items, limit is " << limit;
  return true;
}

bool KwaiProDppDiversityArranger::PrepareSimMatrix(MutableRecoContextInterface *context, RecoResultIter begin,
                                                   RecoResultIter end, int total_size,
                                                   Eigen::MatrixXf *sim_matrix,
                                                   const std::string &embedding_attr_name,
                                                   bool enable_weighted_embedding,
                                                   Eigen::MatrixXf *lost_embedding_mask) {
  bool allow_empty_embedding = GetBoolProcessorParameter(context, "allow_empty_embedding", false);
  item_embedding_attr_accessor_ = context->GetItemAttrAccessor(embedding_attr_name);
  Eigen::MatrixXf items_embedding;
  int embedding_size = 0;
  if (enable_weighted_embedding) {
    (*lost_embedding_mask) = Eigen::MatrixXf::Ones(1, total_size);
    embedding_size = GetIntProcessorParameter(context, "secondary_embed_len", 64);
  }
  for (int i = 0; i < total_size; i++) {
    auto item = (begin + i);
    auto embedding = item->GetDoubleListAttr(item_embedding_attr_accessor_);
    if (i == 0) {
      if (!enable_weighted_embedding) {
        if (!embedding) {
          // 如果是对主 embedding 处理，且第一个 item embedding 为空则不计算
          CL_LOG(WARNING) << "dpp diversity, the first item's embedding of " << embedding_attr_name
                          << " is empty!";
          return false;
        } else {
          embedding_size = embedding->size();
        }
      }
      items_embedding.resize(total_size, embedding_size);
      items_embedding.setZero();
    }
    if (!embedding) {
      if (enable_weighted_embedding) {
        (*lost_embedding_mask)(0, i) = 0;
      }
      // 通常不允许 embedding 为空, 为了兼容个别 item 取不到 embedding 的情况增加容错机制
      if (allow_empty_embedding) {
        items_embedding.row(i).setRandom();
        continue;
      } else {
        CL_LOG(WARNING) << "dpp diversity, get embedding failed!";
        return false;
      }
    }
    // NOTE! 这步看下是否可以通过一行来优化
    if (embedding->size() != embedding_size) {
      CL_LOG(WARNING) << "dpp diversity, get different item embedding size";
      return false;
    }
    for (int j = 0; j < embedding->size(); j++) {
      items_embedding(i, j) = embedding->at(j);
    }
  }
  // 归一化
  items_embedding.rowwise().normalize();
  // 生成相似矩阵
  (*sim_matrix) = items_embedding * items_embedding.transpose();
  // 做变换防止出现极端 case
  (*sim_matrix) = sim_matrix->array() / 2 + 0.5;

  return true;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, KwaiProDppDiversityArranger, KwaiProDppDiversityArranger)
}  // namespace platform
}  // namespace ks
