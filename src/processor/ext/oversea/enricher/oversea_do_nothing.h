#pragma once

#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class OverseaDummyEnricher : public CommonRecoBaseEnricher {
 public:
  OverseaDummyEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override {
    // do nothing
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(OverseaDummyEnricher);
};

}  // namespace platform
}  // namespace ks
