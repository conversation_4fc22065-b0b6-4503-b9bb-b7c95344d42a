#include "dragon/src/processor/ext/uni_predict/embedding_manager/post_fetch_embedding_manager.h"

#include <memory>
#include <string>
#include <unordered_set>
#include <vector>

#include "boost/asio.hpp"
#include "dragon/src/processor/ext/uni_predict/common/functions.h"
#include "dragon/src/processor/ext/uni_predict/embedding_merger/default_attr_based_embedding_merger.h"
#include "dragon/src/processor/ext/uni_predict/embedding_merger/single_space_embedding_merger.h"
#include "folly/futures/Future.h"
#include "teams/reco-arch/uni-predict/src/utils/monitor.h"

namespace ks {
namespace platform {
namespace embedding_manager {

PostFetchEmbeddingManager::PostFetchEmbeddingManager(
    std::vector<EmbeddingConfig> configs, Map<std::string, InputConfig> input_configs,
    std::vector<std::shared_ptr<embedding_fetcher::BaseEmbeddingFetcher>> fetchers,
    const std::string &model_key, bool debug_tensor)
    : BaseEmbeddingManager(configs, input_configs, fetchers, model_key) {
  CHECK_EQ(embedding_configs_.size(), embedding_fetchers_.size())
      << "embedding_config and embedding_fetcher size mismatch";
  debug_tensor_ = debug_tensor;
}

bool PostFetchEmbeddingManager::PreBatchingProcess(MutableRecoContextInterface *context,
                                                   RecoResultConstIter begin, RecoResultConstIter end) {
  // collect signs + fetch + sum_pooling are all executed in PostBatchingProcess
  return true;
}

bool PostFetchEmbeddingManager::PostBatchingProcess(
    MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end,
    const reco_arch::uni_predict::PreallocatedTensors &preallocated) {
  if (preallocated.device.type != reco_arch::uni_predict::DeviceType::CPU) {
    CL_LOG(ERROR) << "now fill input only support CPU pinned memory";
    return false;
  }
  timer_.Start();
  item_slot_signs_.clear();
  common_slot_signs_.clear();
  sign_to_embeddings_.clear();
  item_slot_signs_.resize(embedding_configs_.size());
  common_slot_signs_.resize(embedding_configs_.size());
  sign_to_embeddings_.resize(embedding_fetchers_.size());
  int num_items = std::distance(begin, end);
  using FlatTensor = reco_arch::uni_predict::FlatTensor;

  Map<std::string, FlatTensor> attr_to_tensor;
  for (auto entry : preallocated.tensors) {
    auto it = input_configs_.find(entry.first);
    if (it != input_configs_.end()) {
      auto attr_name = it->second.attr_name;
      if (attr_to_tensor.count(attr_name) != 0) {
        CL_LOG(ERROR) << attr_name << " are used to fill two input tensors";
        continue;
      }
      attr_to_tensor[attr_name] = entry.second;
    }
  }
  std::vector<reco_arch::uni_predict::PreallocatedTensors> tensor_groups(embedding_fetchers_.size());
  std::unordered_set<std::string> find_tensors;
  for (size_t i = 0; i < embedding_fetchers_.size(); ++i) {
    auto &tensor_group = tensor_groups[i];
    tensor_group.device = preallocated.device;
    for (auto &cfg : embedding_configs_[i].slot_configs) {
      auto iter = attr_to_tensor.find(cfg.first);
      if (iter != attr_to_tensor.end()) {
        tensor_group.tensors[cfg.first] = iter->second;
        find_tensors.insert(cfg.first);
      }
    }
  }

  reco_arch::uni_predict::PreallocatedTensors extra_input_tensors;
  for (auto t : preallocated.tensors) {
    if (find_tensors.find(t.first) == find_tensors.end()) {
      extra_input_tensors.tensors[t.first] = t.second;
    }
  }
  extra_input_tensors.device = preallocated.device;
  timer_.AppendCostMs("split_input_tensor");
  auto fetch_func = [this, context, begin, end, num_items](
                        std::shared_ptr<embedding_fetcher::BaseEmbeddingFetcher> fetcher,
                        std::vector<std::pair<const void *, size_t>> *sign_embed,
                        SlotSignPos *common_slot_signs, std::vector<SlotSignPos> *item_slot_signs,
                        EmbeddingConfig *embedding_config, const Map<std::string, InputConfig> *input_configs,
                        const reco_arch::uni_predict::PreallocatedTensors *tensor_group,
                        size_t fetcher_index) -> bool {
    uni_predict_functions::ResetThreadLocalVariable(context);
    serving_base::Timer tt;
    tt.Start();
    CollectSlotSigns(context, begin, end, num_items, item_slot_signs, common_slot_signs, embedding_config);
    auto all_signs = &embedding_config->all_signs;
    sign_embed->resize(all_signs->size(), {nullptr, 0});
    const auto collect_signs_duration = tt.Interval();
    tt.AppendCostMs("collect_signs", collect_signs_duration / 1000.f);
    reco_arch::uni_predict::MonitorManager::GetInstance().ReportCollectSignsTime(model_key_,
                                                                                 collect_signs_duration);
    sign_embed->resize(embedding_config->seq_signs.size(), {nullptr, 0});
    if (fetcher->IsSupportSequenceFetch()) {
      fetcher->FetchEmbedding(context, embedding_config->seq_signs, sign_embed);
    } else {
      fetcher->FetchEmbedding(context, &embedding_config->all_signs, sign_embed);
    }
    base::perfutil::PerfUtilWrapper::IntervalLogStash(all_signs->size(), "online.uni_predict_server",
                                                      "uniq_sign_count", GlobalHolder::GetServiceIdentifier(),
                                                      std::to_string(fetcher_index));
    const auto fetch_duration = tt.Interval();
    tt.AppendCostMs("fetch_embedding", fetch_duration / 1000.f);
    embedding_merger::SingleSpaceEmbeddingMergerParameter param = {
        input_configs,   &embedding_config->slot_configs,
        sign_embed,      common_slot_signs,
        item_slot_signs, processor_name_,
        debug_tensor_};
    embedding_merger::SingleSpaceEmbeddingMerger merger(&param);
    merger.Merge(context, begin, end, num_items, tensor_group);
    const auto sum_pooling_duration = tt.Interval();
    tt.AppendCostMs("merge_embedding", sum_pooling_duration / 1000.f);
    reco_arch::uni_predict::MonitorManager::GetInstance().ReportSumPoolingTime(model_key_,
                                                                               sum_pooling_duration);
    CL_LOG(INFO)  << model_key_ << " fetcher " << fetcher_index << " post process: " << tt.display();
    return true;
  };

  std::vector<folly::Future<bool>> fetch_futs;
  for (size_t i = 0; i < embedding_fetchers_.size(); ++i) {
    auto fetcher = embedding_fetchers_[i];
    auto sign_embed = &sign_to_embeddings_[i];
    auto common_slot_sign = &common_slot_signs_[i];
    auto item_slot_sign = &item_slot_signs_[i];
    auto embedding_config = &embedding_configs_[i];
    auto input_config = &input_configs_;
    auto tensor_group = &tensor_groups[i];
    auto fut = folly::via(EmbeddingManagerGlobalHelper::GetInstance().GetGlobalThreadPool(),
                          std::bind(fetch_func, fetcher, sign_embed, common_slot_sign, item_slot_sign,
                                    embedding_config, input_config, tensor_group, i));
    fetch_futs.emplace_back(std::move(fut));
  }
  timer_.AppendCostMs("submit_task");
  embedding_merger::DefaultAttrBasedEmbeddingMergerParameter param = {&input_configs_};
  embedding_merger::DefaultAttrBasedEmbeddingMerger merger(param);
  merger.Merge(context, begin, end, num_items, &extra_input_tensors);
  timer_.AppendCostMs("fill_extra_input");
  // NOTE(qiuchuyu): 这里无论是 fetch_func 提交异常，还是 fetch_func return
  // false，其实都不会导致后续流程 core. 这里假如没有值，才会设置 ret=false. 其中一个 fetcher 返回 false
  // 并不会导致 ret=false，当做 embedding miss 处理。
  // NOTE (wangshengzhe): 这里使用 collectAll 替换 collect 来处理上面提到的 fetch_func 的异常, collectAll
  // 可以保证任何一个 future 出异常时都不会提前终止, 而是一直 wait 到所有 future 执行结束,
  // 避免提前终止引发其他 future core.
  bool ret = true;
  folly::collectAll(fetch_futs.begin(), fetch_futs.end())
      .thenTry([&ret](auto &&trys) {
        if (!trys.hasValue()) {
          ret = false;
          if (trys.hasException()) {
            const auto &e = trys.exception();
            CL_LOG_ERROR("uni_predict", "embedding_manager_collect_all_error") << e.what();
          } else {
            CL_LOG_ERROR("uni_predict", "embedding_manager_collect_all_error")
                << " collect all fetcher futures failed";
          }
          return;
        }

        const auto &ts = trys.value();
        for (size_t i = 0; i < ts.size(); ++i) {
          if (!ts[i].hasValue()) {
            // fetch task is broken. stop this request.
            ret = false;
            if (ts[i].hasException()) {
              const auto &e = ts[i].exception();
              if (e.class_name() == "folly::QueueFullException") {
                CL_LOG_ERROR("uni_predict", "fetcher_thread_not_enough")
                    << e.what() << ", please reset flags `embedding_fetcher_thread_num`(now "
                    << FLAGS_embedding_fetcher_thread_num << ") and `embedding_fetcher_queue_size`(now "
                    << FLAGS_embedding_fetcher_queue_size << ") to a bigger value.";
              } else {
                CL_LOG_ERROR("uni_predict", "embedding_manager_error") << e.what();
              }
            } else {
              CL_LOG_ERROR("uni_predict", "embedding_manager_error")
                  << " cannot get value from fetcher futures";
            }
            return;
          }
          const auto &res = ts[i].value();
          if (!res) {
            // log warning fetcher failed
            CL_LOG(WARNING) << "embedding fetcher[" << i
                            << "] returns false in this request. Set embeddings as zero in this fetcher";
          }
        }
      })
      .wait();
  timer_.AppendCostMs("wait_task_done");
  CL_LOG(INFO) << "post_fetch_embedding time cost: " << timer_.display();
  return ret;
}

void PostFetchEmbeddingManager::SetProcessorName(const std::string &name) {
  for (size_t i = 0; i < embedding_fetchers_.size(); ++i) {
    embedding_fetchers_[i]->SetProcessorName(name);
  }
  processor_name_ = name;
}

}  // namespace embedding_manager
}  // namespace platform
}  // namespace ks
