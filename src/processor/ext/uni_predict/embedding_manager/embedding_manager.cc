#include "dragon/src/processor/ext/uni_predict/embedding_manager/embedding_manager.h"

#include <string>

#include "boost/asio.hpp"
#include "dragon/src/processor/ext/uni_predict/common/functions.h"
#include "dragon/src/processor/ext/uni_predict/embedding_merger/common_embedding_merger.h"
#include "folly/futures/Future.h"
#include "teams/reco-arch/uni-predict/src/utils/monitor.h"

DEFINE_int64(embedding_fetcher_thread_num, 512, "embedding_fetcher_thread_num");
DEFINE_int64(embedding_fetcher_queue_size, 512, "embedding_fetcher_queue_size");

namespace ks {
namespace platform {
namespace embedding_manager {

void BaseEmbeddingManager::CollectSlotSignsExp(MutableRecoContextInterface *context,
                                               RecoResultConstIter begin, RecoResultConstIter end,
                                               int num_items, std::vector<SlotSignPos> *item_slot_signs,
                                               SlotSignPos *common_slot_signs,
                                               EmbeddingConfig *embedding_config) {
  int last_sign_size = embedding_config->all_signs.size();
  embedding_config->all_signs.clear();
  embedding_config->all_signs.reserve(last_sign_size);
  last_sign_size = embedding_config->seq_signs.size();
  embedding_config->seq_signs.clear();
  embedding_config->seq_signs.reserve(last_sign_size);

  uint32_t pos = 0;
  size_t common_signs_size = 0, item_signs_size = 0;
  // common slot signs
  for (int i = 0; i < embedding_config->common_slots_inputs.size(); ++i) {
    auto slot_id = embedding_config->common_slots_id_inputs[i];
    const auto &parameters_input = embedding_config->common_parameters_inputs[i];
    auto parameters = context->GetIntListCommonAttr(parameters_input);

    // checking
    if (!parameters) {
      CL_LOG(WARNING) << "common parameters attr is missing: " << parameters_input;
      continue;
    }

    // collect slot signs
    std::vector<uint32_t> *vec_ptr;
    int parameters_size = parameters->size();
    if (parameters_size > 0) {
      (*common_slot_signs)[slot_id] = std::vector<uint32_t>();
      vec_ptr = &(*common_slot_signs)[slot_id];
      vec_ptr->reserve(parameters_size);
    }
    const auto &parameters_ref = parameters.value();
    for (int j = 0; j < parameters_size; ++j) {
      auto inserted = embedding_config->all_signs.insert({parameters_ref[j], pos});
      if (inserted.second) {
        embedding_config->seq_signs.emplace_back(parameters_ref[j]);
        ++pos;
      }
      vec_ptr->emplace_back(inserted.first->second);
    }

    common_signs_size = parameters_size;
  }
  // monitor
  for (const auto &sp : *common_slot_signs) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        kPerfBase * sp.second.size(), kPerfNs, "embedding_fetcher.common_sign_num",
        GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), processor_name_,
        std::to_string(sp.first));
  }

  // item slot signs
  if (!embedding_config->slots_inputs.empty()) {
    item_slot_signs->resize(num_items);
    for (size_t item_idx = 0; item_idx < num_items; ++item_idx) {
      auto &this_item_slot_signs = (*item_slot_signs)[item_idx];
      this_item_slot_signs.reserve(embedding_config->slots_id_inputs.size() * LOAD_FACTOR_4_FAST_MAP);
    }
    for (int i = 0; i < embedding_config->slots_inputs.size(); ++i) {
      const auto &parameters_input = embedding_config->parameters_inputs[i];
      int parameters_attr_missing = 0;
      auto slot_id = embedding_config->slots_id_inputs[i];
      ItemAttr *sign_attr_accessor = context->GetItemAttr(parameters_input);
      if (!sign_attr_accessor) {
        CL_LOG_REPEAT(WARNING, context, "parameters_input_empty_" + parameters_input, parameters_attr_missing)
            << "parameters attr is missing: " << parameters_input;
        continue;
      }
      size_t item_idx = 0;
      for (auto iter = begin; iter != end; ++iter, ++item_idx) {
        auto &this_item_slot_signs = (*item_slot_signs)[item_idx];
        auto signs = context->GetIntListItemAttr(*iter, sign_attr_accessor);

        if (signs.has_value()) {
          item_signs_size += signs->size();
          // collect item slot signs to map
          std::vector<uint32_t> *param_vec = nullptr;
          int signs_size = signs->size();
          if (signs_size > 0) {
            this_item_slot_signs[slot_id] = std::vector<uint32_t>();
            param_vec = &(this_item_slot_signs[slot_id]);
            param_vec->reserve(signs_size);
          }
          const auto &signs_ref = signs.value();
          for (int j = 0; j < signs_size; ++j) {
            auto inserted = embedding_config->all_signs.insert({signs_ref[j], pos});
            if (inserted.second) {
              embedding_config->seq_signs.emplace_back(signs_ref[j]);
              ++pos;
            }
            param_vec->emplace_back(inserted.first->second);
          }
        } else {
          // slots or signs is empty
          parameters_attr_missing += (signs.has_value() ? 0 : 1);
        }
      }

      CL_LOG_REPEAT(WARNING, context, "parameters_attr_missing_" + parameters_input, parameters_attr_missing)
          << "parameters attr is missing: " << parameters_input;
    }
  }

  CL_LOG(INFO) << "Fetcher:" << embedding_config->fetcher_index
               << ", signs size, common: " << common_signs_size << ", item: " << item_signs_size
               << ", total: " << common_signs_size + item_signs_size
               << ", unique: " << embedding_config->all_signs.size() << ", unique rate: "
               << embedding_config->all_signs.size() * 1.0 / (common_signs_size + item_signs_size);
}

void BaseEmbeddingManager::CollectSlotSigns(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                            RecoResultConstIter end, int num_items,
                                            std::vector<SlotSignPos> *item_slot_signs,
                                            SlotSignPos *common_slot_signs,
                                            EmbeddingConfig *embedding_config) {
  if (embedding_config->slot_as_attr_name_) {
    CollectSlotSignsExp(context, begin, end, num_items, item_slot_signs, common_slot_signs, embedding_config);
    return;
  }
  int last_sign_size = embedding_config->all_signs.size();
  embedding_config->all_signs.clear();
  embedding_config->all_signs.reserve(last_sign_size);
  last_sign_size = embedding_config->seq_signs.size();
  embedding_config->seq_signs.clear();
  embedding_config->seq_signs.reserve(last_sign_size);

  uint32_t pos = 0;
  size_t common_signs_size = 0, item_signs_size = 0;
  int filter_sign = 0;
  if (embedding_config->common_slots_inputs.size() > 0) {
    auto &common_slot_signs_ref = *common_slot_signs;
    common_slot_signs_ref.reserve(embedding_config->common_slots_id_inputs.size() * LOAD_FACTOR_4_FAST_MAP);
    // common slot signs
    // lixinhua: common 不能通过预先设置的 slot 进行过滤，因为 item 里获取不到的 slot 会到 common 里找
    for (int i = 0; i < embedding_config->common_slots_inputs.size(); ++i) {
      const auto &slots_input = embedding_config->common_slots_inputs[i];
      const auto &parameters_input = embedding_config->common_parameters_inputs[i];

      auto slots = context->GetIntListCommonAttr(slots_input);
      auto parameters = context->GetIntListCommonAttr(parameters_input);

      // checking
      if (!slots) {
        CL_LOG(WARNING) << "common slots attr is missing: " << slots_input;
        continue;
      }
      if (!parameters) {
        CL_LOG(WARNING) << "common parameters attr is missing: " << parameters_input;
        continue;
      }
      if (slots->size() != parameters->size()) {
        CL_LOG_ERROR("mio_embedding",
                     "common_slots_parameters_size_unmatched_" + slots_input + "_" + parameters_input)
            << "size of common slots and common parameters do not match: " << slots_input << " & "
            << parameters_input;
        continue;
      }

      // collect slot signs
      const auto &slots_ref = slots.value();
      const auto &parameters_ref = parameters.value();
      std::vector<uint32_t> *param_vec = nullptr;
      uint64 last_slot = 0;
      for (int j = 0; j < slots->size(); ++j) {
        auto inserted = embedding_config->all_signs.insert({parameters_ref[j], pos});
        if (inserted.second) {
          embedding_config->seq_signs.emplace_back(parameters_ref[j]);
          ++pos;
        }
        auto slot = slots_ref[j];
        if (slot != last_slot || param_vec == nullptr) {
          param_vec = &common_slot_signs_ref[slot];
          if (param_vec->size() == 0) {
            // common 过滤暂未添加
            auto iter_slot_to_expand = embedding_config->slot_to_expand.find(slot);
            if (iter_slot_to_expand != embedding_config->slot_to_expand.end()) {
              param_vec->reserve(iter_slot_to_expand->second);
            }
          }
          last_slot = slot;
        }
        param_vec->emplace_back(inserted.first->second);
      }

      common_signs_size += slots->size();  // lixinhua03 TODO ---->> ?? common_signs_size = slots->size()
    }
    // monitor
    for (const auto &sp : *common_slot_signs) {
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
          kPerfBase * sp.second.size(), kPerfNs, "embedding_fetcher.common_sign_num",
          GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), processor_name_,
          std::to_string(sp.first));
    }
  }

  // item slot signs
  if (!embedding_config->slots_inputs.empty()) {
    item_slot_signs->resize(num_items);
    size_t item_idx = 0;
    for (auto iter = begin; iter != end; ++iter, ++item_idx) {
      auto &this_item_slot_signs = (*item_slot_signs)[item_idx];
      this_item_slot_signs.reserve(embedding_config->slots_id_inputs.size() * LOAD_FACTOR_4_FAST_MAP);
    }
    for (int i = 0; i < embedding_config->slots_inputs.size(); ++i) {
      const auto &slots_input = embedding_config->slots_inputs[i];
      const auto &parameters_input = embedding_config->parameters_inputs[i];
      int slots_attr_missing = 0;
      int parameters_attr_missing = 0;
      int slots_parameters_size_unmatched = 0;
      // NOTE(jiabw): GetItemAttr 在 attr 不存在时不会向 AttrTable 中插入 attr, 这里获取的也是 attr 的
      // accessor
      ItemAttr *slot_attr_accessor = context->GetItemAttr(slots_input);
      ItemAttr *sign_attr_accessor = context->GetItemAttr(parameters_input);
      if (slot_attr_accessor == nullptr) {
        CL_LOG(ERROR) << "`CollectSlotSigns` slots_attr: " << slots_input << " not exist";
        continue;
      }
      if (sign_attr_accessor == nullptr) {
        CL_LOG(ERROR) << "`CollectSlotSigns` signs_attr: " << parameters_input << " not exist";
        continue;
      }
      size_t item_idx = 0;
      for (auto iter = begin; iter != end; ++iter, ++item_idx) {
        auto &this_item_slot_signs = (*item_slot_signs)[item_idx];
        auto slots = context->GetIntListItemAttr(*iter, slot_attr_accessor);
        auto signs = context->GetIntListItemAttr(*iter, sign_attr_accessor);

        if (slots.has_value() && signs.has_value()) {
          if (slots->size() != signs->size()) {
            ++slots_parameters_size_unmatched;
            continue;
          }
          item_signs_size += slots->size();
          // collect item slot signs to map
          std::vector<uint32_t> *param_vec = nullptr;
          uint64 last_slot = 0;  // 节省查找 slot 的时间, 因为相同 slot 在 attr 里基本是连续的
          const auto &slots_ref = slots.value();
          const auto &signs_ref = signs.value();
          for (int j = 0; j < slots->size(); ++j) {
            uint64_t slot = slots_ref[j];
            if (slot != last_slot || param_vec == nullptr) {
              param_vec = &this_item_slot_signs[slot];
              last_slot = slot;
              if (param_vec->size() == 0) {
                auto iter_slot_to_expand = embedding_config->slot_to_expand.find(slot);
                if (iter_slot_to_expand != embedding_config->slot_to_expand.end()) {
                  param_vec->reserve(iter_slot_to_expand->second);
                } else {
                  ++j;
                  for (; j < slots->size(); ++j) {
                    if (slots_ref[j] == slot) {
                      ++filter_sign;
                      continue;
                    }
                    break;
                  }
                  --j;
                  ++filter_sign;
                  continue;
                }
              }
            }
            auto inserted = embedding_config->all_signs.insert({signs_ref[j], pos});
            if (inserted.second) {
              embedding_config->seq_signs.emplace_back(signs_ref[j]);
              ++pos;
            }
            param_vec->emplace_back(inserted.first->second);
          }
        } else {
          // slots or signs is empty
          slots_attr_missing += (slots.has_value() ? 0 : 1);
          parameters_attr_missing += (signs.has_value() ? 0 : 1);
        }
      }

      // monitor
      CL_LOG_REPEAT(WARNING, context, "slots_attr_missing_" + slots_input, slots_attr_missing)
          << "slots attr is missing: " << slots_input;

      CL_LOG_REPEAT(WARNING, context, "parameters_attr_missing_" + parameters_input, parameters_attr_missing)
          << "parameters attr is missing: " << parameters_input;

      CL_LOG_REPEAT(WARNING, context,
                    "slots_parameters_size_unmatched_" + slots_input + "_" + parameters_input,
                    slots_parameters_size_unmatched)
          << "sizes of slots and parameters do not match: " << slots_input << " & " << parameters_input;
    }
  }

  CL_LOG(INFO) << "Fetcher:" << embedding_config->fetcher_index
               << ", signs size, common: " << common_signs_size << ", item: " << item_signs_size
               << ", total: " << common_signs_size + item_signs_size << ", filter_sign: " << filter_sign
               << ", unique: " << embedding_config->all_signs.size() << ", unique rate: "
               << embedding_config->all_signs.size() * 1.0 / (common_signs_size + item_signs_size);
}

ParallelFetchEmbeddingManager::ParallelFetchEmbeddingManager(
    const std::vector<EmbeddingConfig> &configs, const Map<std::string, InputConfig> &input_configs,
    const std::vector<std::shared_ptr<embedding_fetcher::BaseEmbeddingFetcher>> &fetchers,
    const std::string &model_key, bool debug_tensor,
    const reco_arch::uni_predict::BatchingSystemOption &batch_options)
    : BaseEmbeddingManager(configs, input_configs, fetchers, model_key), task_type_(batch_options.task_type) {
  CHECK_EQ(embedding_configs_.size(), embedding_fetchers_.size())
      << "embedding_config and embedding_fetcher size mismatch";
  for (size_t i = 0; i < embedding_fetchers_.size(); ++i) {
    for (auto cfg : embedding_configs_[i].slot_configs) {
      CHECK_EQ(all_slots_configs_.count(cfg.first), 0) << "duplicated slot_config: " + cfg.first;
      all_slots_configs_[cfg.first] = {cfg.second, i};
    }
  }

  // embedding merger
  embedding_merger::EmbeddingMergerParameter merger_param;
  merger_param.debug_tensor = debug_tensor;
  merger_param.input_configs = &input_configs_;
  merger_param.slot_configs = &all_slots_configs_;

  if (task_type_ == reco_arch::uni_predict::BatchTaskType::kBasicBatchingTask ||
      task_type_ == reco_arch::uni_predict::BatchTaskType::kBatchTVMTask) {
    embedding_merger_ = std::make_shared<embedding_merger::CommonEmbeddingMerger>(merger_param);
  } else if (task_type_ == reco_arch::uni_predict::BatchTaskType::kBatchTensorflowTask) {
    embedding_merger_ = std::make_shared<embedding_merger::CommonEmbeddingMerger>(merger_param);
  } else if (task_type_ == reco_arch::uni_predict::BatchTaskType::kBatchGPUMergeTask) {
#ifdef USE_GPU
    gpu_merger_ =
        std::make_shared<embedding_merger::BatchedGPUEmbeddingMergerV2>(merger_param, batch_options);
    embedding_merger::DefaultAttrBasedEmbeddingMergerParameter default_param = {&input_configs_};
    attr_merger_ = std::make_shared<embedding_merger::DefaultAttrBasedEmbeddingMerger>(default_param);
#else
    MISMATCH_DEVICE_TYPE(GPU);
#endif
  } else {
    LOG(FATAL) << "Not implemented now";
  }
}

bool ParallelFetchEmbeddingManager::PreBatchingProcess(
    MutableRecoContextInterface *context, RecoResultConstIter begin,
    RecoResultConstIter end) {  // 1. collect slot_signs, common_slot_signs and all_signs
  item_slot_signs_.clear();
  item_slot_signs_.resize(embedding_configs_.size());
  common_slot_signs_.clear();
  common_slot_signs_.resize(embedding_configs_.size());
  int num_items = std::distance(begin, end);

  serving_base::Timer mgr_timer;
  mgr_timer.Start();

  for (size_t i = 0; i < embedding_configs_.size(); ++i) {
    CollectSlotSigns(context, begin, end, num_items, &item_slot_signs_[i], &common_slot_signs_[i],
                     &embedding_configs_[i]);
  }
  const auto collect_signs_duration = mgr_timer.Interval();
  mgr_timer.AppendCostMs("collect_slot_signs", collect_signs_duration / 1000.f);
  reco_arch::uni_predict::MonitorManager::GetInstance().ReportCollectSignsTime(model_key_,
                                                                               collect_signs_duration);

  sign_to_embeddings_.clear();
  sign_to_embeddings_.resize(embedding_fetchers_.size());
  mgr_timer.AppendCostMs("clear_old_embedding");
  auto fetch_func = [this, context](std::shared_ptr<embedding_fetcher::BaseEmbeddingFetcher> fetcher,
                                    EmbeddingConfig *embedding_config,
                                    std::vector<std::pair<const void *, size_t>> *sign_embed,
                                    Map<uint64_t, uint32_t> *all_signs) -> bool {
    uni_predict_functions::ResetThreadLocalVariable(context);
    sign_embed->resize(embedding_config->seq_signs.size(), {nullptr, 0});
    if (fetcher->IsSupportSequenceFetch()) {
      return fetcher->FetchEmbedding(context, embedding_config->seq_signs, sign_embed);
    } else {
      return fetcher->FetchEmbedding(context, &embedding_config->all_signs, sign_embed);
    }
  };

  std::vector<folly::Future<bool>> fetch_futs;
  for (size_t i = 0; i < embedding_fetchers_.size(); ++i) {
    auto fetcher = embedding_fetchers_[i];
    auto sign_embed = &sign_to_embeddings_[i];
    auto all_signs = &embedding_configs_[i].all_signs;
    auto embedding_config = &embedding_configs_[i];
    auto fut = folly::via(EmbeddingManagerGlobalHelper::GetInstance().GetGlobalThreadPool(),
                          std::bind(fetch_func, fetcher, embedding_config, sign_embed, all_signs));
    fetch_futs.emplace_back(std::move(fut));
    base::perfutil::PerfUtilWrapper::IntervalLogStash(all_signs->size(), "online.uni_predict_server",
                                                      "uniq_sign_count", GlobalHolder::GetServiceIdentifier(),
                                                      std::to_string(i));
  }
  // NOTE(qiuchuyu):
  // 之前这里我们用 folly::collectAll(fetch_futs.begin(), fetch_futs.end()).wait();
  // 有两个问题
  // 1. 出现 fetcher_func 提交异常时没有处理，会导致 sign_embed 是空的
  // 2. 没判断 fetcher_func 的返回值（当然目前看都是 true）
  // 这里我们先修改问题 1，因为问题 2 即使返回 false，也是可以继续做 sum_pooling 的，所以暂时不特殊判断
  // NOTE (wangshengzhe): 这里使用 collectAll 替换 collect 来处理上面提到的 fetch_func 的异常, collectAll
  // 可以保证任何一个 future 出异常时都不会提前终止, 而是一直 wait 到所有 future 执行结束,
  // 避免提前终止引发其他 future core.
  bool is_good_fetches = true;
  folly::collectAll(fetch_futs.begin(), fetch_futs.end())
      .thenTry([&is_good_fetches](auto &&trys) {
        if (!trys.hasValue()) {
          is_good_fetches = false;
          if (trys.hasException()) {
            const auto &e = trys.exception();
            CL_LOG_ERROR("uni_predict", "embedding_manager_collect_all_error") << e.what();
          } else {
            CL_LOG_ERROR("uni_predict", "embedding_manager_collect_all_error")
                << " collect all fetcher futures failed";
          }
          return;
        }
        const auto &ts = trys.value();
        for (size_t i = 0; i < ts.size(); ++i) {
          if (!ts[i].hasValue()) {
            // fetch task is broken. stop this request.
            is_good_fetches = false;
            if (ts[i].hasException()) {
              const auto &e = ts[i].exception();
              if (e.class_name() == "folly::QueueFullException") {
                CL_LOG_ERROR("uni_predict", "fetcher_thread_not_enough")
                    << e.what() << ", please reset flags `embedding_fetcher_thread_num`(now "
                    << FLAGS_embedding_fetcher_thread_num << ") and `embedding_fetcher_queue_size`(now "
                    << FLAGS_embedding_fetcher_queue_size << ") to a bigger value.";
              } else {
                CL_LOG_ERROR("uni_predict", "embedding_manager_error") << e.what();
              }
            } else {
              CL_LOG_ERROR("uni_predict", "embedding_manager_error")
                  << " cannot get value from fetcher futures";
            }
            return;
          }
          const auto &res = ts[i].value();
          if (!res) {
            // log warning fetcher failed
            CL_LOG(WARNING) << "embedding fetcher[" << i
                            << "] returns false in this request. Set embeddings as zero in this fetcher";
          }
        }
      })
      .wait();

  mgr_timer.AppendCostMs("fetch_embedddings");

  CL_LOG(INFO) << "mgr_timer: " << mgr_timer.display();

  return is_good_fetches;
}

bool ParallelFetchEmbeddingManager::PostBatchingProcess(
    MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end,
    const reco_arch::uni_predict::PreallocatedTensors &preallocated) {
  if (preallocated.device.type != reco_arch::uni_predict::DeviceType::CPU) {
    CL_LOG(ERROR) << "now fill input only support CPU pinned memory";
    return false;
  }
  int64 start_ts = base::GetTimestamp();
  int num_items = std::distance(begin, end);
  if (!this->embedding_merger_->Merge(context, begin, end, num_items, &sign_to_embeddings_,
                                      common_slot_signs_, item_slot_signs_, preallocated)) {
    return false;
  }
  int64 duration = base::GetTimestamp() - start_ts;
  reco_arch::uni_predict::MonitorManager::GetInstance().ReportSumPoolingTime(model_key_, duration);
  return true;
}

#ifdef USE_GPU
bool ParallelFetchEmbeddingManager::BatchGPUMergeTaskPostBatching(
    MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end,
    const reco_arch::uni_predict::BatchedGPUMergeOffset &offsets,
    const reco_arch::uni_predict::PreallocatedTensors &predict_tensors,
    const reco_arch::uni_predict::PreallocatedTensors &extra_pinned_buffer,
    const reco_arch::uni_predict::PreallocatedTensors &extra_device_buffer, cudaStream_t stream) {
  // 0. checking
  if (predict_tensors.device.type != reco_arch::uni_predict::DeviceType::GPU) {
    CL_LOG(ERROR) << "now batch GPU merge only support GPU memory";
    return false;
  }

  // 1. gpu set buffer
  if (!gpu_merger_->SetBuffers(extra_pinned_buffer, extra_device_buffer, offsets, stream)) {
    CL_LOG(ERROR) << "set buffer failed";
    return false;
  }

  // 2. split predict_tensors
  // tensors in slot config: gpu merger
  // others: default attr merger

  reco_arch::uni_predict::PreallocatedTensors slot_config_predict_tensors;
  slot_config_predict_tensors.device = predict_tensors.device;
  reco_arch::uni_predict::PreallocatedTensors attr_predict_tensors;
  attr_predict_tensors.device = predict_tensors.device;
  for (const auto &item : predict_tensors.tensors) {
    if (all_slots_configs_.count(item.first)) {
      slot_config_predict_tensors.tensors[item.first] = item.second;
    } else {
      attr_predict_tensors.tensors[item.first] = item.second;
    }
  }

  // 3. attr_merger
  int num_items = std::distance(begin, end);
  if (!attr_merger_->Merge(context, begin, end, num_items, &attr_predict_tensors)) {
    return false;
  }

  // 4. gpu merger
  if (!gpu_merger_->Merge(context, begin, end, sign_to_embeddings_, common_slot_signs_, item_slot_signs_,
                          slot_config_predict_tensors)) {
    return false;
  }
  return true;
}

bool ParallelFetchEmbeddingManager::GetTaskSizes(reco_arch::uni_predict::BatchedGPUMergeOffset *size) {
  size->task_offset = 1;
  size->unique_sign_offset = 0;
  size->embed_element_offset = 0;
  for (size_t i = 0; i < sign_to_embeddings_.size(); ++i) {
    size->unique_sign_offset += sign_to_embeddings_[i].size();
    for (size_t j = 0; j < sign_to_embeddings_[i].size(); ++j) {
      size->embed_element_offset += sign_to_embeddings_[i][j].second / sizeof(int16_t);
    }
  }
  size->non_unique_sign_offset = 0;
  for (size_t i = 0; i < common_slot_signs_.size(); ++i) {
    // common_slot signs
    for (const auto &common : common_slot_signs_[i]) {
      size->non_unique_sign_offset += common.second.size();
    }
    // item_slot signs
    for (const auto &item : item_slot_signs_[i]) {
      for (const auto &item_signs : item) {
        size->non_unique_sign_offset += item_signs.second.size();
      }
    }
  }

  size->item_offset = item_slot_signs_.size() > 0 ? item_slot_signs_[0].size() : 0;

  // debug
  // non-unique sign embeddings
  // size_t non_unique_sign_embedding_element = 0;
  // for (size_t i = 0; i < common_slot_signs_.size(); ++i) {
  //   // common_slot signs
  //   for (const auto &common : common_slot_signs_[i]) {
  //     for (const auto &sign : common.second) {
  //       non_unique_sign_embedding_element += sign_to_embeddings_[i][sign].second / sizeof(int16_t);
  //     }
  //   }
  //   // item_slot signs
  //   for (const auto &item : item_slot_signs_[i]) {
  //     for (const auto &item_signs : item) {
  //       for (const auto &sign : item_signs.second) {
  //         non_unique_sign_embedding_element += sign_to_embeddings_[i][sign].second / sizeof(int16_t);
  //       }
  //     }
  //   }
  // }

  // // tensor size
  // size_t tensor_elements = 0;
  // size_t slot_num = 0;
  // for (const auto &embedding_config : embedding_configs_) {
  //   for (const auto &slot_config_pair : embedding_config.slot_configs) {
  //     const auto &slot_config = slot_config_pair.second;
  //     int bs = slot_config.common ? size->task_offset : size->item_offset;
  //     tensor_elements += slot_config.slots.size() * slot_config.expand * slot_config.dim * bs;
  //     slot_num += bs * slot_config.slots.size();
  //   }
  // }
  CL_LOG(INFO) << "#uniq-sign = " << size->unique_sign_offset
               << ", #non-uniq-sign = " << size->non_unique_sign_offset;
  //              << ", #uniq-sign-emb = " << size->embed_element_offset
  //              << ", #non-uniq-sign-emb = " << non_unique_sign_embedding_element
  //              << ", #tensor_size = " << tensor_elements << ", #slots = " << slot_num;
  return true;
}
#endif

void ParallelFetchEmbeddingManager::SetProcessorName(const std::string &name) {
  for (size_t i = 0; i < embedding_fetchers_.size(); ++i) {
    embedding_fetchers_[i]->SetProcessorName(name);
  }
  if (embedding_merger_) {
    embedding_merger_->SetProcessorName(name);
  }
#ifdef USG_GPU
  if (gpu_merger_) {
    gpu_merger_->SetProcessorName(name);
  }
#endif
  processor_name_ = name;
}

}  // namespace embedding_manager
}  // namespace platform
}  // namespace ks
