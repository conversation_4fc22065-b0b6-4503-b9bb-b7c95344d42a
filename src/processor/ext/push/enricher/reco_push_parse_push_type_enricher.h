#pragma once
#include <array>
#include <memory>
#include <set>
#include <string>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "folly/container/F14Map.h"
#include "folly/container/F14Set.h"
#include "kconf/kconf.h"

namespace ks {
namespace platform {

typedef std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<::Json::Value>>> Kconf;

class PushParsePushTypeEnricher : public CommonRecoBaseEnricher {
 public:
  PushParsePushTypeEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  bool InitProcessor() override {
    black_mix_event_type_ =
        ks::infra::KConf().GetSet("push.platform.blackEventType", std::make_shared<std::set<std::string>>());
    unfollow_people_recall_switch_ = ks::infra::KConf().Get("push.mix.unfollowPeopleRecallSwitch", false);
    social_person_push_bug_fix_switch_ =
        ks::infra::KConf().Get("push.search.socialPersonPushBugFixSwitch", true);

    debug_log_info_flag_ = config()->GetBoolean("debug_log_info_flag", false);
    debug_read_push_type_ = config()->GetString("debug_read_push_type", "");
    debug_reason_to_photo_push_type_ = config()->GetString("debug_reason_to_photo_push_type", "");
    debug_reason_to_live_push_type_ = config()->GetString("debug_reason_to_live_push_type", "");
    debug_reason_to_people_push_type_ = config()->GetString("debug_reason_to_people_push_type", "");
    debug_reason_author_relation_to_push_type_ =
        config()->GetString("debug_reason_author_relation_to_push_type", "");
    debug_reason_people_relation_to_push_type_ =
        config()->GetString("debug_reason_people_relation_to_push_type", "");
    debug_unlogin_reason_to_push_type_ = config()->GetString("debug_unlogin_reason_to_push_type", "");
    debug_unlogin_item_type_to_push_type_ = config()->GetString("debug_unlogin_item_type_to_push_type", "");

    push_type_attr_name_ = config()->GetString("push_type_attr_name", "push_type_attr");
    push_record_event_types_name_ =
        config()->GetString("push_record_event_types_name", "push_record_event_types");
    rerank_leaf_type_attr_name_ = config()->GetString("rerank_leaf_type_attr_name", "rerank_leaf_type_attr");
    kconf_path_ = config()->GetString("kconf_path", "push.leaf.push_type_parse_info_meta");

    ::Json::Value json_value;
    auto config_parser_feature_meta = [this](const kuaishou::config::Value &value, ::Json::Value *json_val) {
      ks::infra::ValueParser<::Json::Value> json_parser;
      return json_parser.Parse(value, json_val);
    };
    auto on_content_change_feature_meta = [this](const ::Json::Value &json_value) {
      auto json_push_type_config = json_value.get("push_type_config", ::Json::Value{});
      int next_idx = (parser_idx_ + 1) % 2;
      // push type 原始信息都没解析出来就返回吧
      if (json_push_type_config.empty()) {
        LOG(ERROR) << "Parse PushType array failed, please check kconf - push_type_config: empty config";
        return;
      }
      if (!parser_array_[next_idx].UpdatePushTypeAttrFromJson(json_push_type_config)) {
        LOG(ERROR) << "Parse PushType array failed, please check kconf - push_type_config";
        return;
      }

      auto code_to_push_type = json_value.get("code_to_push_type", ::Json::Value{});
      parser_array_[next_idx].UpdateCodeToPushTypeFromJson(code_to_push_type);
      parser_idx_ = next_idx;
    };
    ks::infra::KConf().SetWatch<::Json::Value>(kconf_path_, json_value, config_parser_feature_meta,
                                               on_content_change_feature_meta, &parser_idx_);
    if (parser_idx_ == 0) {
      // idx 没变化代表初始化失败
      LOG(ERROR) << "Parse PushType array failed";
      return false;
    }
    return true;
  }

 private:
  void DebugSingleReasonCast();
  void GetPhotoPushTypeByReason(int64, int64, int64, std::string &);
  void UnFollowAuthorPushType(int64, std::string &);
  void GetLivePushTypeByReason(int64, std::string &);
  void GetPeoplePushTypeByReason(int64, int64, const std::string &, std::string &);
  void UnFollowPeoplePushType(int64, std::string &);
  void UnLoginGetPushTypeByReason(int64, int64, std::string &);
  void UnLoginGetPushTypeByItemType(int64, std::string &);

  struct PushTypeParser {
    struct PushTypeNode {
      std::string name = "";
      int code = -100000;
      std::weak_ptr<PushTypeNode> parent;
      std::string parent_name = "";
      std::string ancestor_name = "";
      std::string primary_attribution = "";
      std::string secondary_attribution = "";
      std::vector<std::weak_ptr<PushTypeNode>> children;
      std::string path = "";
      int level = 0;
      std::string leaf_type_attr = "";

      bool CheckIllegal() const {
        return this->name.empty() || this->code == -100000 || this->primary_attribution.empty() ||
               this->secondary_attribution.empty() || leaf_type_attr.empty();
      }
    };

   public:
    bool UpdatePushTypeAttrFromJson(const ::Json::Value &json_value) {
      if (!json_value.isArray()) {
        LOG(ERROR) << "Parse PushType array failed, please check kconf";
        return false;
      }
      node_map_.clear();
      code_map_.clear();
      node_name_list_.clear();
      leaf_type_attr_list_.clear();
      leaf_type_to_push_type_.clear();

      // 第一遍：创建所有节点
      for (const auto &item : json_value) {
        auto node = std::make_shared<PushTypeNode>();
        node->name = item.get("name", "").asString();
        node->code = item.get("code", -100000).asInt();
        node->parent_name = item.get("parent", "").asString();
        node->primary_attribution = item.get("primary_attribution", "").asString();
        node->secondary_attribution = item.get("secondary_attribution", "").asString();
        node->leaf_type_attr = item.get("leaf_type_attr", "null").asString();
        if (node->CheckIllegal()) {
          LOG(ERROR)
              << "Parse PushType array failed, please check kconf - push_type_config: Invalid node data";
          return false;
        }
        node_map_[node->name] = node;
        code_map_[node->code] = node;
        node_name_list_.emplace_back(node->name);
        if (leaf_type_attr_list_.count(node->leaf_type_attr) && node->leaf_type_attr != "null") {
          LOG(ERROR) << "Parse PushType array failed, please check kconf - push_type_config: "
                     << "leaf_type_attr " << node->leaf_type_attr;
        } else {
          leaf_type_attr_list_.insert(node->leaf_type_attr);
          leaf_type_to_push_type_[node->leaf_type_attr] = node->name;
        }
      }
      // 第二遍：建立父子关系并检查循环依赖
      for (const auto &node_name : node_name_list_) {
        const std::string &parent_name = node_map_[node_name]->parent_name;
        if (node_map_.count(parent_name)) {
          if (HasCycle(node_name, parent_name)) {
            LOG(ERROR) << "Parse PushType array failed, please check kconf - push_type_config: "
                       << "Cycle detected in hierarchy involving node: " << node_name;
            return false;
          }
          node_map_[parent_name]->children.push_back(node_map_[node_name]);
          node_map_[node_name]->parent = node_map_[parent_name];
        }
      }
      // 第三遍：建立 path 和 level
      for (const auto &node_name : node_name_list_) {
        BuildPathAndLevel(node_name);
      }
      return true;
    }

    void UpdateCodeToPushTypeFromJson(const ::Json::Value &json_value) {
      auto reason_code_json = json_value.get("reason_code", ::Json::Value{});
      if (!reason_code_json.empty()) {
        reason_name_to_code_.clear();
        std::vector<std::string> keys = reason_code_json.getMemberNames();
        for (auto key : keys) {
          if (!reason_code_json[key].isInt()) {
            LOG(ERROR) << "Parse code_to_push_type.reason_code failed, please check kconf " << key;
            continue;
          }
          reason_name_to_code_[key] = reason_code_json.get(key, 0).asInt();
        }
      }

      auto item_type_code_json = json_value.get("item_type_code", ::Json::Value{});
      if (!item_type_code_json.empty()) {
        item_type_name_to_code_.clear();
        std::vector<std::string> keys = item_type_code_json.getMemberNames();
        for (auto key : keys) {
          if (!item_type_code_json[key].isInt()) {
            LOG(ERROR) << "Parse code_to_push_type.item_type_code failed, please check kconf " << key;
            continue;
          }
          item_type_name_to_code_[key] = item_type_code_json.get(key, 0).asInt();
        }
      }

      auto reason_photo_json = json_value.get("reason_to_photo_push_type", ::Json::Value{});
      ParseReasonToPushTypeFromJsonArray(reason_photo_json, &reason_to_photo_push_type_,
                                         "reason_to_photo_push_type");

      auto reason_live_json = json_value.get("reason_to_live_push_type", ::Json::Value{});
      ParseReasonToPushTypeFromJsonArray(reason_live_json, &reason_to_live_push_type_,
                                         "reason_to_live_push_type");

      auto reason_people_json = json_value.get("reason_to_people_push_type", ::Json::Value{});
      ParseReasonToPushTypeFromJsonArray(reason_people_json, &reason_to_people_push_type_,
                                         "reason_to_people_push_type");

      auto reason_author_relation_json =
          json_value.get("reason_author_relation_to_push_type", ::Json::Value{});
      ParseReasonToPushTypeFromJsonArray(reason_author_relation_json, &reason_author_relation_to_push_type_,
                                         "reason_author_relation_to_push_type");

      auto reason_people_relation_json =
          json_value.get("reason_people_relation_to_push_type", ::Json::Value{});
      ParseReasonToPushTypeFromJsonArray(reason_people_relation_json, &reason_people_relation_to_push_type_,
                                         "reason_people_relation_to_push_type");

      auto unlogin_reason_json = json_value.get("unlogin_reason_to_push_type", ::Json::Value{});
      ParseReasonToPushTypeFromJsonArray(unlogin_reason_json, &unlogin_reason_to_push_type_,
                                         "unlogin_reason_to_push_type");

      auto unlogin_item_type_json = json_value.get("unlogin_item_type_to_push_type", ::Json::Value{});
      ParseReasonToPushTypeFromJsonArray(unlogin_item_type_json, &unlogin_item_type_to_push_type_,
                                         "unlogin_item_type_to_push_type");
    }

    bool IsPhotoItem(const std::string &node_name) {
      if (!node_map_.count(node_name)) {
        return false;
      }
      static folly::F14FastSet<std::string> photo_type_name = {"photoLike", "photoFriendLike"};
      return IsDescendant(node_name, "photo") || IsDescendant(node_name, "hotNews") ||
             photo_type_name.count(node_name);
    }

    folly::F14FastMap<std::string, std::shared_ptr<PushTypeNode>> node_map_;
    folly::F14FastMap<int, std::shared_ptr<PushTypeNode>> code_map_;
    std::vector<std::string> node_name_list_;
    folly::F14FastSet<std::string> leaf_type_attr_list_;

    folly::F14FastMap<std::string, std::string> leaf_type_to_push_type_;

    folly::F14FastMap<std::string, int> reason_name_to_code_;
    folly::F14FastMap<std::string, int> item_type_name_to_code_;

    folly::F14FastMap<int, std::string> reason_to_photo_push_type_;
    folly::F14FastMap<int, std::string> reason_to_live_push_type_;
    folly::F14FastMap<int, std::string> reason_to_people_push_type_;
    folly::F14FastMap<int, std::string> reason_author_relation_to_push_type_;
    folly::F14FastMap<int, std::string> reason_people_relation_to_push_type_;
    folly::F14FastMap<int, std::string> unlogin_reason_to_push_type_;
    folly::F14FastMap<int, std::string> unlogin_item_type_to_push_type_;

   private:
    bool HasCycle(const std::string &start_node, const std::string &parent_name) {
      folly::F14FastSet<std::string> visited;
      std::string current = parent_name;
      while (!current.empty()) {
        if (current == start_node) {
          return true;
        }
        if (visited.count(current)) {
          return true;
        }
        visited.insert(current);
        if (!node_map_.count(current)) {
          break;
        }
        current = node_map_[current]->parent_name;
      }
      return false;
    }

    bool IsDescendant(const std::string &node_name_in, const std::string &ancestor_name) {
      std::string node_name = node_name_in;
      if (!node_map_.count(node_name) || !node_map_.count(ancestor_name)) {
        return false;
      }
      while (!node_name.empty() && node_map_.count(node_name)) {
        if (node_name == ancestor_name) {
          return true;
        }
        node_name = node_map_[node_name]->parent_name;
      }
      return false;
    }

    void BuildPathAndLevel(const std::string &node_name) {
      if (!node_map_[node_name]->path.empty()) return;
      auto parent_shared = node_map_[node_name]->parent.lock();
      if (parent_shared) {
        if (parent_shared->path.empty()) {
          BuildPathAndLevel(node_map_[node_name]->parent_name);
        }
        node_map_[node_name]->path = parent_shared->path + "." + node_name;
        node_map_[node_name]->level = parent_shared->level + 1;
        node_map_[node_name]->ancestor_name = parent_shared->ancestor_name;
      } else {
        node_map_[node_name]->path = node_name;
        node_map_[node_name]->level = 0;
        node_map_[node_name]->ancestor_name = node_name;
      }
    }

    std::shared_ptr<PushTypeNode> GetByCode(int code) {
      if (!code_map_.count(code)) {
        return nullptr;
      }
      return code_map_[code];
    }

    std::shared_ptr<PushTypeNode> GetByName(const std::string &name) {
      if (!node_map_.count(name)) {
        return nullptr;
      }
      return node_map_[name];
    }

    bool ParseReasonToPushTypeFromJsonArray(const ::Json::Value &json_value,
                                            folly::F14FastMap<int, std::string> *reason_to_push_type,

                                            const std::string &key) {
      if (!json_value.isArray()) {
        LOG(ERROR) << "Parse code_to_push_type failed, please check kconf : " << key << " should be an array";
        return false;
      }
      if (json_value.empty()) {
        LOG(INFO) << key << " is empty";
      }
      reason_to_push_type->clear();
      for (auto item : json_value) {
        int reason_code;
        if (item.get("reason_name_or_code", "").isString()) {
          auto reason_name = item.get("reason_name_or_code", "").asString();
          if (reason_name_to_code_.count(reason_name) && key != "unlogin_item_type_to_push_type") {
            reason_code = reason_name_to_code_[reason_name];
          } else if (item_type_name_to_code_.count(reason_name)) {
            reason_code = item_type_name_to_code_[reason_name];
          } else {
            LOG(ERROR) << "Parse code_to_push_type failed, please check kconf : " << reason_name
                       << " not found";
          }
        } else if (item.get("reason_name_or_code", "").isInt()) {
          reason_code = item.get("reason_name_or_code", "").asInt();
        } else {
          LOG(ERROR) << "Parse code_to_push_type failed, please check kconf : reason_name_or_code." << key
                     << " error";
          continue;
        }
        auto push_type = item.get("push_type", "").asString();
        if (!node_map_.count(push_type)) {
          LOG(ERROR) << "Parse code_to_push_type failed, please check kconf : push type not contain "
                     << push_type;
          continue;
        }
        if (reason_to_push_type->count(reason_code) && (*reason_to_push_type)[reason_code] != push_type) {
          LOG(ERROR) << "Parse code_to_push_type failed, please check kconf :" << key << " " << reason_code
                     << " has muti push type !!! " << push_type;
        }
        (*reason_to_push_type)[reason_code] = push_type;
      }
      return true;
    }
  };

  std::array<PushTypeParser, 2> parser_array_;
  int parser_idx_ = 0;

  bool debug_log_info_flag_ = false;
  std::string debug_read_push_type_ = "";
  std::string debug_reason_to_photo_push_type_ = "";
  std::string debug_reason_to_live_push_type_ = "";
  std::string debug_reason_to_people_push_type_ = "";
  std::string debug_reason_author_relation_to_push_type_ = "";
  std::string debug_reason_people_relation_to_push_type_ = "";
  std::string debug_unlogin_reason_to_push_type_ = "";
  std::string debug_unlogin_item_type_to_push_type_ = "";

  std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<std::set<std::string>>>> black_mix_event_type_;
  std::shared_ptr<ks::infra::KsConfig<bool>> unfollow_people_recall_switch_;
  std::shared_ptr<ks::infra::KsConfig<bool>> social_person_push_bug_fix_switch_;

  absl::optional<absl::Span<const int64>> follow_friend_list_;
  absl::optional<absl::Span<const int64>> follow_favorite_list_;
  std::string push_type_attr_name_;
  std::string push_record_event_types_name_;
  std::string rerank_leaf_type_attr_name_;
  std::string kconf_path_;

  DISALLOW_COPY_AND_ASSIGN(PushParsePushTypeEnricher);
};

}  // namespace platform
}  // namespace ks
