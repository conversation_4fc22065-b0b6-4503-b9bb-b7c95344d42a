#pragma once

#include <memory>
#include <string>
#include <map>
#include <set>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>
#include "folly/container/F14Set.h"
#include "kconf/kconf.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "teams/reco-arch/colossusdb/clotho-sdk/cpp/clotho_client_internal.h"
namespace ks {
namespace platform {

class KaiworksOfflineFeatureJoinEnricher : public CommonRecoBaseEnricher {
 public:
  KaiworksOfflineFeatureJoinEnricher() {}
  ~KaiworksOfflineFeatureJoinEnricher() override {
    ks::infra::KConf::RemoveWatch(this);
  }
  bool IsAsync() const override {
    return true;
  }
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    token_ = config()->GetString("token", ks::platform::GlobalHolder::GetServiceIdentifier());
    join_key_ = config()->GetString("join_key", "");
    if (join_key_.empty()) {
      LOG(ERROR) << "KaiworksOfflineFeatureJoinEnricher no join_key";
      return false;
    }
    is_common_ = config()->GetBoolean("is_common", false);
    timeout_ms_ = config()->GetInt("timeout_ms", 200);
    if (timeout_ms_ <= 0) {
      LOG(ERROR) << "KaiworksOfflineFeatureJoinEnricher init failed! timeout_ms must be > 0";
      return false;
    }
    entity_name_ = config()->GetString("entity", "");
    if (entity_name_.empty()) {
      LOG(ERROR) << "KaiworksOfflineFeatureJoinEnricher no entity name";
      return false;
    }
    auto kw_entity_conf = ks::infra::KConf().Get("KAIWorks.featurecenter.kwOfflineEntityConf",
      std::make_shared<::Json::Value>());
    auto entity_conf = kw_entity_conf->Get()->get(entity_name_, "");
    if (entity_conf.empty()) {
      LOG(ERROR) << "KaiworksOfflineFeatureJoinEnricher: no entity meta info, " << entity_name_;
      return false;
    }
    std::string column_conf = entity_conf.get("column_conf", "").asString();
    if (column_conf.empty()) {
      LOG(ERROR) << "KaiworksOfflineFeatureJoinEnricher: no column_conf" << entity_name_;
      return false;
    }
    InitColumns(column_conf);

    table_name_ = entity_conf.get("table_name", "").asString();
    if (table_name_.empty()) {
      LOG(ERROR) << "KaiworksOfflineFeatureJoinEnricher: no clotho table name, entity: " << entity_name_;
      return false;
    }

    ::reco::clotho::sdk::ClothoOptions opt;
    opt.table = table_name_;
    using ClothoClient = ::reco::clotho::sdk::ClothoClientInternal;
    clotho_client_ = ::reco::clotho::sdk::ClothoClientBase::GetInstance<ClothoClient>();
    if (clotho_client_ == nullptr) {
      LOG(ERROR) << "KaiworksOfflineFeatureEntityJoinEnricher Clotho Client nullptr";
      return false;
    }
    if (!clotho_client_->Init(opt)) {
      LOG(ERROR) << "KaiworksOfflineFeatureEntityJoinEnricher Clotho Client init failed! ";
      return false;
    }
    return true;
  }
  void InitColumns(const std::string &column_conf);

  void EnrichCommon(MutableRecoContextInterface *context);
  void EnrichItem(MutableRecoContextInterface *context,
                  RecoResultConstIter begin, RecoResultConstIter end);
  std::shared_ptr<::kuiba::PredictItem> ParseAttr(const std::string &raw_data);


 private:
  bool is_common_ = true;
  int timeout_ms_ = 200;
  std::string token_;
  std::string join_key_;
  std::string entity_name_;
  std::string table_name_;
  folly::F14FastSet<std::string> attr_names_;
  folly::F14FastSet<std::string> column_names_;
  ::reco::clotho::sdk::ClothoClientBase *clotho_client_ = nullptr;
  ::reco::clotho::gateway::TableReadRequest request_;
  ::reco::clotho::gateway::TableReadResponse response_;
  ks::infra::OnContentChange<std::map<std::string, int64_t>> column_watcher_;
  ks::infra::OnContentChange<std::set<std::string>> attr_name_watcher_;
  DISALLOW_COPY_AND_ASSIGN(KaiworksOfflineFeatureJoinEnricher);
};

}  // namespace platform
}  // namespace ks
