#include "dragon/src/processor/ext/offline/enricher/kaiworks_offline_feature_join_enricher.h"

#include <cstddef>
#include <memory>
#include <string>
#include <vector>
#include <utility>

#include "base/time/time.h"
#include "dragon/src/util/logging_util.h"
#include "google/protobuf/map.h"
#include "google/protobuf/map_field.h"
#include "google/protobuf/reflection.h"
#include "google/protobuf/util/message_differencer.h"
#include "ks/reco_proto/sample_log/reco_label_set.pb.h"

namespace ks {
namespace platform {
  static constexpr char kwNameSpace[] = "kw.feature_center";
  static constexpr char joinSuccess[] = "join_success";
  // static constexpr char joinOverWrite[] = "join_overwrite";

  void KaiworksOfflineFeatureJoinEnricher::InitColumns(const std::string &column_conf) {
    auto column_map = std::make_shared<std::map<std::string, std::string>>();
    auto parser = [](const std::string &key, std::string *val) -> bool {
      *val = key;
      return true;
    };
    column_map = ks::infra::KConf().GetMap<std::string, std::string>(
      column_conf,
      std::make_shared<std::map<std::string, std::string>>(), parser)->Get();
      CL_LOG(INFO) << "KaiworksOfflineFeatureJoinEnricher: get column_conf of " << column_conf;

    auto column_conf_on_change = [this] (const std::map<std::string, std::string>& vals) {
      folly::F14FastSet<std::string> column_names;
      for (const auto attr_name : attr_names_) {
        if (!attr_name.empty() && vals.find(attr_name) != vals.end()) {
          column_names.insert(vals.at(attr_name));
          CL_LOG(INFO) << "column conf changed, " << vals.at(attr_name);
        }
      }
      std::swap(column_names_, column_names);
    };
    ks::infra::KConf().SetMapWatch<std::string, std::string>(
      column_conf,
      std::map<std::string, std::string>(),
      parser,
      column_conf_on_change);

    std::string attr_name_kconf = config()->GetString("attr_name_kconf", "");
    if (!attr_name_kconf.empty()) {
      auto attr_name_set = ks::infra::KConf().GetSet(
        attr_name_kconf, std::make_shared<std::set<std::string>>());
      for (const auto attr_name : *attr_name_set.get()->Get()) {
        attr_names_.insert(attr_name);
        if (!attr_name.empty() && column_map->find(attr_name) != column_map->end()) {
          column_names_.insert(column_map->at(attr_name));
        }
      }
      auto attr_name_on_change = [this, column_conf, parser] (const std::set<std::string>& vals) {
        folly::F14FastSet<std::string> attr_names;
        folly::F14FastSet<std::string> column_names;
        auto column_map_conf = ks::infra::KConf().GetMap<std::string, std::string>(
          column_conf,
          std::make_shared<std::map<std::string, std::string>>(), parser)->Get();
        for (const auto attr_name : vals) {
          attr_names.insert(attr_name);
          if (!attr_name.empty() && column_map_conf->find(attr_name) != column_map_conf->end()) {
            column_names.insert(column_map_conf->at(attr_name));
            CL_LOG(INFO) << "column conf changed, " << column_map_conf->at(attr_name);
          }
        }
        std::swap(attr_names_, attr_names);
        std::swap(column_names_, column_names);
      };
      ks::infra::KConf().SetSetWatch<std::string>(attr_name_kconf,
      std::set<std::string>(),
      attr_name_on_change);
    } else {
      auto *attrs = config()->Get("attr_names");
      if (attrs && attrs->IsArray()) {
        for (const auto *attr_json : attrs->array()) {
          if (attr_json == nullptr) continue;
          if (attr_json->IsString()) {
            std::string attr_name = attr_json->StringValue();
            attr_names_.insert(attr_name);
            if (!attr_name.empty() && column_map->find(attr_name) != column_map->end()) {
              column_names_.insert(column_map->at(attr_name));
            }
          } else {
            CL_LOG(INFO) << "value attr init failed! Item of attrs should be a"
                      << " string! But value found: " << attr_json->ToString();
          }
        }
      }
    }
  }

  void KaiworksOfflineFeatureJoinEnricher::Enrich(
    MutableRecoContextInterface *context, RecoResultConstIter begin,
    RecoResultConstIter end) {
    if (attr_names_.empty() || column_names_.empty()) {
      // 未指定要 Join 的 attr 列表时，跳过当前算子
      CL_LOG(INFO) << "attr_names_ or column_names_ empty ";
      return;
    }
      if (is_common_) {
        EnrichCommon(context);
      } else {
        EnrichItem(context, begin, end);
      }
  }

  void KaiworksOfflineFeatureJoinEnricher::EnrichCommon(MutableRecoContextInterface *context) {
    auto whiteList = ks::infra::KConf().GetSet("KAIWorks.featurecenter.kwOfflineFeatureCallServiceWhiteList",
      std::make_shared<std::set<std::string>>());
    if (whiteList->Get()->find(token_) == whiteList->Get()->end()) {
      CL_LOG(WARNING) << "token not in call service white list: " << token_;
      return;
    }

    std::string key;
    auto key_accessor = context->GetCommonAttrAccessor(join_key_);
    if (key_accessor == nullptr) {
      CL_LOG(WARNING) << "join_key not exists: " << join_key_;
      return;
    }
    auto key_type = context->GetCommonAttrType(join_key_);
    if (key_type == AttrType::STRING) {
      auto str_attr = key_accessor->GetStringValue();
      if (str_attr) {
        key = std::string(*str_attr);
      }
    } else if (key_type == AttrType::INT) {
      auto int_val = key_accessor->GetIntValue();
      if (int_val) {
        key = absl::StrFormat("%d", *int_val);
      }
    } else {
      CL_LOG(WARNING) << "unsupported join_key(" << (int)context->GetCommonAttrType(join_key_)
                    << "): " << join_key_;
      return;
    }
    response_.Clear();
    auto pms = std::make_shared<std::promise<void *>>();
    std::future<void *> st_future(pms->get_future());
    auto clotho_callback = [this, pms, context](::reco::clotho::sdk::Status status,
                                                const ::reco::clotho::gateway::TableReadResponse &response) {
      if (status != ::reco::clotho::sdk::Status::STATUS_OK) {
        pms->set_value(nullptr);
        CL_LOG_WARNING_EVERY("clotho", "send_request_fail", 1000)
            << "Clotho read failed, status=" << StatusToString(status);
        return;
      }
      response_ = response;
      // nullptr will be special handle by dragonfly
      pms->set_value(reinterpret_cast<void *>(&response_));
    };

    request_.Clear();
    request_.add_keys(key);
    for (const auto &column : column_names_) {
      auto column_req = request_.add_column_read_reqs();
      column_req->set_column(column);
    }
    request_.set_table(table_name_);
    request_.set_token(token_);
    request_.set_get_pb_result(true);
    request_.set_skip_empty_res(false);

    auto status_async = clotho_client_->AsyncTableRead(
      request_, clotho_callback, timeout_ms_);
    if (status_async != ::reco::clotho::sdk::Status::STATUS_OK) {
      // 这里主要是对 client 创建和参数是否正常进行的检查
      CL_LOG_WARNING_EVERY("clotho", "create_client_failed", 1000)
          << "Clotho client create failed" << RecoUtil::GetRequestInfoForLog(context);
      return;
    }

    RegisterLocalAsyncCallback(context, std::move(st_future), [this, context](void *resp) {
      if (resp == nullptr) return;
      if (response_.rows_size() != 1) {
        CL_LOG(INFO) << "Clotho read failed size = " << response_.rows_size();
        CL_LOG_WARNING_EVERY("clotho", "response fail", 1000)
            << "Clotho read failed size = " << response_.rows_size();
        return;
      }
      if (response_.rows(0).get_empty_row()) {
        CL_LOG(INFO) << "the whole row is empty, key=" << response_.rows(0).key();
        CL_LOG_WARNING_EVERY("clotho", "whole_row_empty", 1000)
            << "the whole row is empty, key=" << response_.rows(0).key();
        return;
      }
      for (int i = 0; i < response_.rows(0).raw_data_size(); i++) {
        const auto &column_value = response_.rows(0).raw_data(i);
        if (column_value.is_null() || column_value.str_value().empty()) continue;
        auto predict_item = ParseAttr(column_value.str_value());
        if (predict_item != nullptr) {
          for (int i = 0; i < predict_item->attr_size(); ++i) {
            auto &attr = predict_item->attr(i);
            auto &name = attr.name();
            if (attr_names_.size() > 0 && attr_names_.count(name) == 0) continue;
            interop::SaveSampleAttrToCommonAttr(context, attr);
            base::perfutil::PerfUtilWrapper::CountLogStash(
              kwNameSpace, entity_name_,
              ::ks::platform::GlobalHolder::GetServiceIdentifier(),
               joinSuccess, attr.name());
          }
        }
      }
    });
  }
  void KaiworksOfflineFeatureJoinEnricher::EnrichItem(
    MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto whiteList = ks::infra::KConf().GetSet("KAIWorks.featurecenter.kwOfflineFeatureCallServiceWhiteList",
      std::make_shared<std::set<std::string>>());
    if (whiteList->Get()->find(token_) == whiteList->Get()->end()) {
      CL_LOG(WARNING) << "token not in call service white list: " << token_;
      return;
    }
    response_.Clear();
    auto pms = std::make_shared<std::promise<void *>>();
    std::future<void *> st_future(pms->get_future());
    auto clotho_callback = [this, pms, context](::reco::clotho::sdk::Status status,
                                                const ::reco::clotho::gateway::TableReadResponse &response) {
      if (status != ::reco::clotho::sdk::Status::STATUS_OK) {
        pms->set_value(nullptr);
        CL_LOG_WARNING_EVERY("clotho", "send_request_fail", 1000)
            << "Clotho read failed, status=" << StatusToString(status);
        return;
      }
      response_ = response;
      // nullptr will be special handle by dragonfly
      pms->set_value(reinterpret_cast<void *>(&response_));
    };

    request_.Clear();
    auto accessor = context->GetItemAttrAccessor(join_key_);
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      std::string key = "-";
      auto key_type = context->GetItemAttrType(join_key_);
      if (key_type == AttrType::STRING) {
        auto str_attr = context->GetStringItemAttr(result.ItemKey(), join_key_);
        if (str_attr) {
          key = std::string(*str_attr);
        }
      } else if (key_type == AttrType::INT) {
        auto int_val = context->GetIntItemAttr(
          result.ItemKey(), join_key_);
        if (int_val) {
          key = absl::StrFormat("%d", *int_val);
        }
      } else {
        CL_LOG(WARNING) << "unsupported join_key: " << join_key_;
      }
      request_.add_keys(key);
    });
    for (const auto &column : column_names_) {
      auto column_req = request_.add_column_read_reqs();
      column_req->set_column(column);
    }
    request_.set_table(table_name_);
    request_.set_token(token_);
    request_.set_get_pb_result(true);
    request_.set_skip_empty_res(false);
    auto status_async = clotho_client_->AsyncTableRead(
      request_, clotho_callback, timeout_ms_);
    if (status_async != ::reco::clotho::sdk::Status::STATUS_OK) {
      // 这里主要是对 client 创建和参数是否正常进行的检查
      CL_LOG_WARNING_EVERY("clotho", "create_client_failed", 1000)
          << "Clotho client create failed" << RecoUtil::GetRequestInfoForLog(context);
      return;
    }

    RegisterLocalAsyncCallback(context, std::move(st_future), [this, context, begin, end](void *resp) {
      if (resp == nullptr) return;
      int item_size = std::distance(begin, end);
      if (response_.rows_size() != item_size) {
        CL_LOG_WARNING_EVERY("clotho", "response fail", 1000)
            << "Clotho read failed size = " << response_.rows_size() << " expect: " << item_size;
        return;
      }
      int index = 0;
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        if (response_.rows(index).get_empty_row()) {
          CL_LOG_WARNING_EVERY("clotho", "whole_row_empty", 1000)
              << "the whole row is empty, key=" << response_.rows(index).key();
          index++;
          return;
        }
        for (int i = 0; i < response_.rows(index).raw_data_size(); i++) {
          const auto &column_value = response_.rows(index).raw_data(i);
          if (column_value.is_null() || column_value.str_value().empty()) {
            continue;
          }
          auto predict_item = ParseAttr(column_value.str_value());
          if (predict_item != nullptr) {
            for (int j = 0; j < predict_item->attr_size(); ++j) {
              auto &attr = predict_item->attr(j);
              auto &name = attr.name();
              if (attr_names_.size() > 0 && attr_names_.count(name) == 0) continue;
              interop::SaveSampleAttrToItemAttr(context, result.item_key, attr);
              base::perfutil::PerfUtilWrapper::CountLogStash(
                kwNameSpace, entity_name_,
                ::ks::platform::GlobalHolder::GetServiceIdentifier(),
                joinSuccess, attr.name());
            }
          }
        }
        index++;
      });
    });
  }

  std::shared_ptr<::kuiba::PredictItem> KaiworksOfflineFeatureJoinEnricher::ParseAttr(
    const std::string &raw_data) {
    auto predict_item = std::make_shared<::kuiba::PredictItem>();
    std::string out;
    bool status = traceback_util::zstd_util_decompress(
      raw_data.data(), raw_data.size(), &out);
    if (status && predict_item->ParseFromString(out)) {
      return predict_item;
    }
    return nullptr;
  }

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, KaiworksOfflineFeatureJoinEnricher, KaiworksOfflineFeatureJoinEnricher)
}  // namespace platform
}  // namespace ks
