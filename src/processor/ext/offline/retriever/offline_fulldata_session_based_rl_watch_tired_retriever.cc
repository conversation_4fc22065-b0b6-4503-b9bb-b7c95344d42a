#include "dragon/src/processor/ext/offline/retriever/offline_fulldata_session_based_rl_watch_tired_retriever.h"

#include <vector>
#include <map>
#include <utility>

#include "ks/reco_proto/proto/session_based_rl.pb.h"

namespace ks {
namespace platform {

void OfflineFulldataSessionBasedRLWatchTiredRetriever::Retrieve(AddibleRecoContextInterface *context) {
  std::vector<CommonRecoRetrieveResult> retrieve_items;

  // part 1 (解析数据至 session_based_rl proto)
  const auto *full_session =
      context->GetProtoMessagePtrCommonAttr<ks::reco::FullLinkSession>(sample_from_attr_);
  if (!full_session) {
    CL_LOG(ERROR) << "OfflineFulldataSessionBasedRLWatchTiredRetriever fail full_session null: "
      << sample_from_attr_;
    return;
  }
  // part 2 (先循环 requests，填充 common_att；再循环 samples，计算 reward，填充至 item_att)
  if (full_session->requests().size() <= 0) {
    CL_LOG(INFO)<< "no request in session!";
    return;
  }

  context->PurposelyResetUserId(full_session->user_id());
  context->PurposelyResetDeviceId(full_session->device_id());

  // set common attr
  context->SetStringCommonAttr("session_id", full_session->session_id());
  context->SetIntCommonAttr("user_id", full_session->user_id());
  context->SetIntCommonAttr("gender", full_session->gender());
  context->SetStringCommonAttr("device_id", full_session->device_id());
  context->SetIntCommonAttr("tab", full_session->tab());
  CL_LOG(INFO) << "httdebug get tab val:" << full_session->tab();
  int session_size = full_session->requests().size();
  double u_act = full_session->requests()[session_size - 1].user_avg_month_duration_level();
  double last_req_time = full_session->requests()[session_size - 1].request_tm();
  std::string last_session_id = full_session->requests()[session_size - 1].session_id();
  // calc reward & set item attr
  double future_reward = 0.0;
  double total_reward = 0.0;
  double future_reward_tmp = 0.0;
  double future_play_addup = 0.0;
  double future_hd_addup = 0.0;
  double future_vv_addup = 0.0;
  double session_hd_sum = 0.0;
  double session_play_sum = 0.0;
  double immediate_play = 0.0;
  double immediate_hd = 0.0;
  bool is_effective_show = false;
  uint64 request_time = 0;
  uint64 later_request_time = 0;
  uint32 sess_max_realshow_index = 1;
  folly::F14FastMap<std::string, double> pxtr_mean_map;
  folly::F14FastMap<std::string, double> pxtr_max_map;
  folly::F14FastMap<std::string, double> pxtr_mean_nst;
  folly::F14FastMap<std::string, double> pxtr_max_nst;
  std::vector<double> likelist(history_num_ + delay_num_, padding_value_);
  std::vector<double> followlist(history_num_ + delay_num_, padding_value_);
  std::vector<double> commentlist(history_num_ + delay_num_, padding_value_);
  std::vector<double> forwardlist(history_num_ + delay_num_, padding_value_);
  std::vector<double> collectlist(history_num_ + delay_num_, padding_value_);
  std::vector<double> downloadlist(history_num_ + delay_num_, padding_value_);
  std::vector<double> profilelist(history_num_ + delay_num_, padding_value_);
  std::vector<double> playdurlist(history_num_ + delay_num_, padding_value_);
  std::vector<double> svdurlist(history_num_ + delay_num_, padding_value_);
  std::vector<int64> hetu1list(history_num_ + delay_num_, padding_value_);
  std::vector<int64> hetu2list(history_num_ + delay_num_, padding_value_);
  std::vector<std::string> pidlist(history_num_ + delay_num_, std::to_string(padding_value_));
  std::vector<double> histotaltime(history_num_ + delay_num_, 0.0);
  std::unordered_map<int, int> real_show_index_to_index;
  // std::unordered_map<int, int> real_show_index_to_validcount;
  std::vector<int> valid_count_vec;
  int all_valid_count = 0;
  // 统计 session 内总 vv 数
  int all_session_vv = 0;
  std::map<int, double> slide_win_wt_table;
  std::map<int, int> hetu1_v1_last_effective_show_cnt;
  for (int i = 0; i < session_size; i++) {
    const auto &request = full_session->requests()[i];
    for (const auto &samp_ds : request.samples()) {
      if (samp_ds.real_show() == 1) {
        slide_win_wt_table[samp_ds.real_show_index()] = samp_ds.play_time_ms() / 1000.0;
        sess_max_realshow_index = std::max(sess_max_realshow_index, samp_ds.real_show_index());
        immediate_play = CalcImmediatePlay(samp_ds);
        immediate_hd = CalcImmediateHd(samp_ds);
        is_effective_show = CalcEffectiveShow(samp_ds.play_time_ms() / 1000.0, samp_ds.duration_ms() / 1000);
        auto item_key = samp_ds.photo_id();
        int watch_tired = 0;
        int hetu1_v1 = -1;
        if (samp_ds.has_hetu_level_info()) {
          const auto hetu_level = samp_ds.hetu_level_info();
          if (hetu_level.hetu_level_one_v1_size() > 0) {
            hetu1_v1 = hetu_level.hetu_level_one_v1()[0];
          }
        }
        bool is_pos = is_effective_show || 1.0 * samp_ds.like() + samp_ds.comment() + samp_ds.forward() +
            samp_ds.collect() + samp_ds.follow() + samp_ds.download() + samp_ds.enter_profile() > 0;
        bool is_chase = hetu1_v1_last_effective_show_cnt.find(hetu1_v1) !=
                hetu1_v1_last_effective_show_cnt.end();
        if (is_pos) {
          if (is_chase) {
            hetu1_v1_last_effective_show_cnt[hetu1_v1]++;
          } else {
            hetu1_v1_last_effective_show_cnt[hetu1_v1] = 1;
          }
        }
        if (!is_pos) {
          if (is_chase) {
            if (hetu1_v1_last_effective_show_cnt[hetu1_v1] >= 2) {
              watch_tired = 1;
            }
          }
        }
        context->SetDoubleItemAttr(item_key, "is_pos", is_pos);
        context->SetDoubleItemAttr(item_key, "is_chase", is_chase);
        context->SetDoubleItemAttr(item_key, "watch_tired", watch_tired);
        session_play_sum += immediate_play;
        session_hd_sum += immediate_hd;
        real_show_index_to_index.emplace(samp_ds.real_show_index(), all_session_vv);
        all_session_vv += 1;
        CalcHisHd(samp_ds, &likelist, &followlist, &commentlist, &forwardlist, &collectlist,
              &downloadlist, &profilelist, &playdurlist, &svdurlist, &histotaltime,
              &hetu1list, &hetu2list, &pidlist);
        int hudong =  (samp_ds.like() + samp_ds.comment() + samp_ds.forward() +
            samp_ds.collect() + samp_ds.follow()) > 0 ? 1 : 0;
        int click = samp_ds.play_time_ms() > 5000? 1 : 0;
        if (hudong * valid_hd_ +  click * valid_click_ > 0) all_valid_count += 1;
        // real_show_index_to_validcount.emplace(samp_ds.real_show_index(), all_valid_count);
        valid_count_vec.push_back(all_valid_count);
      }
    }
  }
  context->SetIntCommonAttr("sess_max_realshow_index", sess_max_realshow_index);
  int page = 1;
  int index = 0;
  int n_size1 = 5;
  int n_size2 = 2;
  std::map<int, std::pair<double, double>> index_to_wt_label;
  for (auto it_outer = slide_win_wt_table.begin(); it_outer != slide_win_wt_table.end(); ++it_outer) {
      int realshow_idx = it_outer->first;
      double sum1 = slide_win_wt_table[realshow_idx];
      double sum2 = slide_win_wt_table[realshow_idx];
      auto it = slide_win_wt_table.upper_bound(realshow_idx);
      for (int i = 0; i < n_size1 && it != slide_win_wt_table.end(); ++i, ++it) {
          if (i < n_size1) sum1 += it->second;
          if (i < n_size2) sum2 += it->second;
      }
      index_to_wt_label[realshow_idx] = {sum1, sum2};
  }

  for (int i = session_size - 1; i >= 0; i--) {
    const auto &request = full_session->requests()[i];
    request_time = request.request_tm();
    std::string request_tm_str = std::to_string(request.request_tm());
    std::string req_uniq_id = full_session->device_id() + request_tm_str;  // 请求唯一标识
    int64 req_uniq_id_hash = base::CityHash64(req_uniq_id.data(), req_uniq_id.size());
    double pxtr_absent_rate = 0;
    double non_pxtr_n = 0.0;
    double request_size = 0.1;
    double server_n = 0.0;
    double real_n = 0.1;

    // 统计 request 内样本 pxtr 缺失率
    for (const auto &samp_ds : request.samples()) {
      if (samp_ds.has_pxtr()) {
        if (1.0 * samp_ds.pxtr().pctr() + samp_ds.pxtr().plvtr() + samp_ds.pxtr().pftr() +
           samp_ds.pxtr().pvtr() + samp_ds.pxtr().pltr() + samp_ds.pxtr().pptr() +
           samp_ds.pxtr().pwtr() + samp_ds.pxtr().pcmtr() == 0.0) {
            non_pxtr_n += 1.0;
        } else {
          // 统计有效 real/server 样本数
          if (samp_ds.real_show() == 0) {
            server_n += 1.0;
          }
          if (samp_ds.real_show() == 1) {
            real_n += 1.0;
          }
        }
      } else {
        non_pxtr_n += 1.0;
      }
      request_size += 1.0;
    }

    pxtr_absent_rate = non_pxtr_n / (request_size + 1e-3);
    uint32 is_bottom = 0;
    // update future reward
    future_reward = gamma_ * future_reward + future_reward_tmp;
    future_reward_tmp = 0.0;
    double filled_samples = request_size - non_pxtr_n;
    // update next state's pxtr
    if (i < session_size - 1) {
      pxtr_mean_nst.insert(std::make_pair("pctr", pxtr_mean_map["pctr"]));
      pxtr_mean_nst.insert(std::make_pair("pvtr", pxtr_mean_map["pvtr"]));
      pxtr_mean_nst.insert(std::make_pair("plvtr", pxtr_mean_map["plvtr"]));
      pxtr_mean_nst.insert(std::make_pair("pltr", pxtr_mean_map["pltr"]));
      pxtr_mean_nst.insert(std::make_pair("pftr", pxtr_mean_map["pftr"]));
      pxtr_mean_nst.insert(std::make_pair("pcmtr", pxtr_mean_map["pcmtr"]));
      pxtr_mean_nst.insert(std::make_pair("pptr", pxtr_mean_map["pptr"]));
      pxtr_mean_nst.insert(std::make_pair("pwtr", pxtr_mean_map["pwtr"]));
      pxtr_max_nst.insert(std::make_pair("pctr", pxtr_max_map["pctr"]));
      pxtr_max_nst.insert(std::make_pair("pvtr", pxtr_max_map["pvtr"]));
      pxtr_max_nst.insert(std::make_pair("plvtr", pxtr_max_map["plvtr"]));
      pxtr_max_nst.insert(std::make_pair("pltr", pxtr_max_map["pltr"]));
      pxtr_max_nst.insert(std::make_pair("pftr", pxtr_max_map["pftr"]));
      pxtr_max_nst.insert(std::make_pair("pcmtr", pxtr_max_map["pcmtr"]));
      pxtr_max_nst.insert(std::make_pair("pptr", pxtr_max_map["pptr"]));
      pxtr_max_nst.insert(std::make_pair("pwtr", pxtr_max_map["pwtr"]));
    } else {
      pxtr_mean_nst.insert(std::make_pair("pctr", 0.0));
      pxtr_mean_nst.insert(std::make_pair("pvtr", 0.0));
      pxtr_mean_nst.insert(std::make_pair("plvtr", 0.0));
      pxtr_mean_nst.insert(std::make_pair("pltr", 0.0));
      pxtr_mean_nst.insert(std::make_pair("pftr", 0.0));
      pxtr_mean_nst.insert(std::make_pair("pcmtr", 0.0));
      pxtr_mean_nst.insert(std::make_pair("pptr", 0.0));
      pxtr_mean_nst.insert(std::make_pair("pwtr", 0.0));
      pxtr_max_nst.insert(std::make_pair("pctr", 0.0));
      pxtr_max_nst.insert(std::make_pair("pvtr", 0.0));
      pxtr_max_nst.insert(std::make_pair("plvtr", 0.0));
      pxtr_max_nst.insert(std::make_pair("pltr", 0.0));
      pxtr_max_nst.insert(std::make_pair("pftr", 0.0));
      pxtr_max_nst.insert(std::make_pair("pcmtr", 0.0));
      pxtr_max_nst.insert(std::make_pair("pptr", 0.0));
      pxtr_max_nst.insert(std::make_pair("pwtr", 0.0));
    }
    pxtr_mean_map.clear();
    pxtr_max_map.clear();
    pxtr_mean_map.insert(std::make_pair("pctr", 0.0));
    pxtr_mean_map.insert(std::make_pair("pvtr", 0.0));
    pxtr_mean_map.insert(std::make_pair("plvtr", 0.0));
    pxtr_mean_map.insert(std::make_pair("pltr", 0.0));
    pxtr_mean_map.insert(std::make_pair("pftr", 0.0));
    pxtr_mean_map.insert(std::make_pair("pcmtr", 0.0));
    pxtr_mean_map.insert(std::make_pair("pptr", 0.0));
    pxtr_mean_map.insert(std::make_pair("pwtr", 0.0));
    pxtr_max_map.insert(std::make_pair("pctr", 0.0));
    pxtr_max_map.insert(std::make_pair("pvtr", 0.0));
    pxtr_max_map.insert(std::make_pair("plvtr", 0.0));
    pxtr_max_map.insert(std::make_pair("pltr", 0.0));
    pxtr_max_map.insert(std::make_pair("pftr", 0.0));
    pxtr_max_map.insert(std::make_pair("pcmtr", 0.0));
    pxtr_max_map.insert(std::make_pair("pptr", 0.0));
    pxtr_max_map.insert(std::make_pair("pwtr", 0.0));
    for (const auto &sample : request.samples()) {
      // if (sample.real_show() == 0) continue;
      is_bottom = sample.is_bottom();
      int rank_index = -1.0;
      rank_index = sample.rank_index();
      double immediate_reward = 0.0;
      int32 hetu1_v1 = -1, hetu2_v1 = -1, hetu3_v1 = -1;
      auto item_key = sample.photo_id();
      context->SetIntItemAttr(item_key, "request_time", request.request_tm());
      retrieve_items.emplace_back(item_key, reason_);
      double hist_play = session_play_sum - future_play_addup;
      double hist_hd = session_hd_sum - future_hd_addup;
      double hist_vv = sess_max_realshow_index - future_vv_addup;
      int64 time_gap = later_request_time - request_time;
      context->SetDoubleItemAttr(item_key, "hist_play_addup", hist_play);
      context->SetDoubleItemAttr(item_key, "hist_hd_addup", hist_hd);
      context->SetDoubleItemAttr(item_key, "hist_play_vv", hist_play / (hist_vv + 0.1));
      context->SetDoubleItemAttr(item_key, "hist_hd_vv", hist_hd / (hist_vv + 0.1));
      context->SetDoubleItemAttr(item_key, "later_req_gap_hour", std::max(time_gap / (3600000.0), -1.0));
      if (sample.real_show() == 2 || sample.real_show() == 0) {
        context->SetDoubleItemAttr(item_key, "immediate_reward", 0.0);
        context->SetDoubleItemAttr(item_key, "future_reward", 0.0);
        context->SetIntItemAttr(item_key, "latter_vv_cnt", 0);
        context->SetIntItemAttr(item_key, "latter_vv_cnt_server", 0);
        total_reward = 0.0;
      } else {
        immediate_play = CalcImmediatePlay(sample);
        immediate_hd = CalcImmediateHd(sample);
        immediate_reward = immediate_play + immediate_hd;
        context->SetDoubleItemAttr(item_key, "immediate_reward", immediate_reward);
        context->SetDoubleItemAttr(item_key, "future_reward", future_reward);
        context->SetIntItemAttr(item_key, "latter_vv_cnt",
                                sess_max_realshow_index - sample.real_show_index());
        context->SetIntItemAttr(item_key, "latter_vv_cnt_server",
                                  sess_max_realshow_index - sample.real_show_index());
        total_reward = immediate_reward + gamma_ * future_reward;
        future_reward_tmp += immediate_reward;
        future_play_addup += immediate_play;
        future_hd_addup += immediate_hd;
        future_vv_addup += 1.0;
      }
      if (sample.real_show() == 1) {
        context->SetIntItemAttr(item_key, "latter_vv_cnt_new",
                                sess_max_realshow_index - sample.real_show_index());
      } else {
        context->SetIntItemAttr(item_key, "latter_vv_cnt_new", 0);
      }
      context->SetDoubleItemAttr(item_key, "user_avg_month_duration_level", u_act);
      context->SetIntItemAttr(item_key, "session_last_req_timestamp", last_req_time);
      context->SetStringItemAttr(item_key, "true_session_id", last_session_id);
      context->SetIntItemAttr(item_key, "session_realshow_index_new", sample.real_show_index());
      context->SetIntItemAttr(item_key, "rank_index", rank_index);
      context->SetIntItemAttr(item_key, "future_req_size", session_size - i - 1);
      context->SetIntItemAttr(item_key, "session_size", session_size);  // 统计过滤过短的 session
      context->SetDoubleItemAttr(item_key, "server_num", server_n);
      context->SetDoubleItemAttr(item_key, "real_num", real_n);
      context->SetIntItemAttr(item_key, "request_uniq_hash", req_uniq_id_hash);
      context->SetIntItemAttr(item_key, "is_degrade", request.is_degrade());
      context->SetIntItemAttr(item_key, "is_bottom", is_bottom);
      context->SetDoubleItemAttr(item_key, "pxtr_absent_rate", pxtr_absent_rate);
      context->SetIntItemAttr(item_key, "real_show", sample.real_show());
      context->SetIntItemAttr(item_key, "photo_id", sample.photo_id());
      context->SetIntItemAttr(item_key, "author_id", sample.author_id());
      context->SetIntItemAttr(item_key, "duration_ms", sample.duration_ms());
      context->SetDoubleItemAttr(item_key, "total_reward", total_reward);
      context->SetIntItemAttr(item_key, "realshow_time", sample.realshow_tm());
      double hudong = 0.0;
      if (1.0 * sample.like() + sample.comment() + sample.forward() +
          sample.collect() + sample.follow() + sample.download() + sample.enter_profile() > 0) {
        hudong = 1.0;
      }
      context->SetDoubleItemAttr(item_key, "hudong", hudong);
      context->SetDoubleItemAttr(item_key, "like", sample.like() * 1.0);
      context->SetDoubleItemAttr(item_key, "comment", sample.comment() * 1.0);
      context->SetDoubleItemAttr(item_key, "forward", sample.forward() * 1.0);
      context->SetDoubleItemAttr(item_key, "collect", sample.collect() * 1.0);
      context->SetDoubleItemAttr(item_key, "follow", sample.follow() * 1.0);
      context->SetDoubleItemAttr(item_key, "download", sample.download() * 1.0);
      context->SetDoubleItemAttr(item_key, "enter_profile", sample.enter_profile() * 1.0);
      context->SetDoubleItemAttr(item_key, "negative", sample.negative() * 1.0);
      context->SetDoubleItemAttr(item_key, "play_time_s", sample.play_time_ms() / 1000.0);
      context->SetDoubleItemAttr(item_key, "comment_stay_time_s", sample.comment_stay_time_ms() / 1000.0);
      context->SetDoubleItemAttr(item_key, "pctr_mean_nst", pxtr_mean_nst["pctr"]);
      context->SetDoubleItemAttr(item_key, "plvtr_mean_nst", pxtr_mean_nst["plvtr"]);
      context->SetDoubleItemAttr(item_key, "pftr_mean_nst", pxtr_mean_nst["pftr"]);
      context->SetDoubleItemAttr(item_key, "pvtr_mean_nst", pxtr_mean_nst["pvtr"]);
      context->SetDoubleItemAttr(item_key, "pltr_mean_nst", pxtr_mean_nst["pltr"]);
      context->SetDoubleItemAttr(item_key, "pptr_mean_nst", pxtr_mean_nst["pptr"]);
      context->SetDoubleItemAttr(item_key, "pwtr_mean_nst", pxtr_mean_nst["pwtr"]);
      context->SetDoubleItemAttr(item_key, "pcmtr_mean_nst", pxtr_mean_nst["pcmtr"]);
      context->SetDoubleItemAttr(item_key, "pctr_max_nst", pxtr_max_nst["pctr"]);
      context->SetDoubleItemAttr(item_key, "plvtr_max_nst", pxtr_max_nst["plvtr"]);
      context->SetDoubleItemAttr(item_key, "pftr_max_nst", pxtr_max_nst["pftr"]);
      context->SetDoubleItemAttr(item_key, "pvtr_max_nst", pxtr_max_nst["pvtr"]);
      context->SetDoubleItemAttr(item_key, "pltr_max_nst", pxtr_max_nst["pltr"]);
      context->SetDoubleItemAttr(item_key, "pptr_max_nst", pxtr_max_nst["pptr"]);
      context->SetDoubleItemAttr(item_key, "pwtr_max_nst", pxtr_max_nst["pwtr"]);
      context->SetDoubleItemAttr(item_key, "pcmtr_max_nst", pxtr_max_nst["pcmtr"]);
      if (sample.has_pxtr()) {
        double pctr = sample.pxtr().pctr();
        double plvtr = sample.pxtr().plvtr();
        double pftr = sample.pxtr().pftr();
        double pvtr = sample.pxtr().pvtr();
        double pltr = sample.pxtr().pltr();
        double pptr = sample.pxtr().pptr();
        double pwtr = sample.pxtr().pwtr();
        double pcmtr = sample.pxtr().pcmtr();
        double psvtr = sample.pxtr().psvtr();
        double pwatch_time = sample.pxtr().pwatch_time();
        double pcmef = sample.pxtr().pcmef();
        double phtr = sample.pxtr().phtr();
        // set 精排 pxtr
        context->SetDoubleItemAttr(item_key, "pctr", pctr);
        context->SetDoubleItemAttr(item_key, "plvtr", plvtr);
        context->SetDoubleItemAttr(item_key, "pftr", pftr);
        context->SetDoubleItemAttr(item_key, "pvtr", pvtr);
        context->SetDoubleItemAttr(item_key, "pltr", pltr);
        context->SetDoubleItemAttr(item_key, "pptr", pptr);
        context->SetDoubleItemAttr(item_key, "pwtr", pwtr);
        context->SetDoubleItemAttr(item_key, "pcmtr", pcmtr);
        context->SetDoubleItemAttr(item_key, "psvtr", psvtr);
        context->SetDoubleItemAttr(item_key, "pwatch_time", pwatch_time);
        context->SetDoubleItemAttr(item_key, "pcmef", pcmef);
        context->SetDoubleItemAttr(item_key, "phtr", phtr);
        // set 粗排 pxtr
        context->SetDoubleItemAttr(item_key, "cascade_pctr", sample.pxtr().cascade_pctr());
        context->SetDoubleItemAttr(item_key, "cascade_plvtr", sample.pxtr().cascade_plvtr());
        context->SetDoubleItemAttr(item_key, "cascade_pcmtr", sample.pxtr().cascade_pcmtr());
        context->SetDoubleItemAttr(item_key, "cascade_pvtr", sample.pxtr().cascade_pvtr());
        context->SetDoubleItemAttr(item_key, "cascade_pltr", sample.pxtr().cascade_pltr());
        context->SetDoubleItemAttr(item_key, "cascade_pwtr", sample.pxtr().cascade_pwtr());
        context->SetDoubleItemAttr(item_key, "cascade_pftr", sample.pxtr().cascade_pftr());
        context->SetDoubleItemAttr(item_key, "cascade_pptr", sample.pxtr().cascade_pptr());
        // set 经验 xtr
        context->SetDoubleItemAttr(item_key, "empirical_ctr", sample.pxtr().empirical_ctr());
        context->SetDoubleItemAttr(item_key, "empirical_ltr", sample.pxtr().empirical_ltr());
        context->SetDoubleItemAttr(item_key, "empirical_wtr", sample.pxtr().empirical_wtr());
        context->SetDoubleItemAttr(item_key, "empirical_ftr", sample.pxtr().empirical_ftr());
        context->SetDoubleItemAttr(item_key, "empirical_ptr", sample.pxtr().empirical_ptr());
        context->SetDoubleItemAttr(item_key, "empirical_cmtr", sample.pxtr().empirical_cmtr());
        context->SetDoubleItemAttr(item_key, "empirical_htr", sample.pxtr().empirical_htr());
        // 统计下一刷的候选集特征
        pxtr_mean_map["pctr"] += pctr / (filled_samples + 1e-8);
        pxtr_mean_map["plvtr"] += plvtr / (filled_samples + 1e-8);
        pxtr_mean_map["pftr"] += pftr / (filled_samples + 1e-8);
        pxtr_mean_map["pvtr"] += pvtr / (filled_samples + 1e-8);
        pxtr_mean_map["pltr"] += pltr / (filled_samples + 1e-8);
        pxtr_mean_map["pptr"] += pptr / (filled_samples + 1e-8);
        pxtr_mean_map["pwtr"] += pwtr / (filled_samples + 1e-8);
        pxtr_mean_map["pcmtr"] += pcmtr / (filled_samples + 1e-8);
        pxtr_max_map["pctr"] = std::max(pctr, pxtr_max_map["pctr"]);
        pxtr_max_map["plvtr"] = std::max(plvtr, pxtr_max_map["plvtr"]);
        pxtr_max_map["pftr"] = std::max(pftr, pxtr_max_map["pftr"]);
        pxtr_max_map["pvtr"] = std::max(pvtr, pxtr_max_map["pvtr"]);
        pxtr_max_map["pltr"] = std::max(pltr, pxtr_max_map["pltr"]);
        pxtr_max_map["pptr"] = std::max(pptr, pxtr_max_map["pptr"]);
        pxtr_max_map["pwtr"] = std::max(pwtr, pxtr_max_map["pwtr"]);
        pxtr_max_map["pcmtr"] = std::max(pcmtr, pxtr_max_map["pcmtr"]);
      } else {
        context->SetDoubleItemAttr(item_key, "pctr", 0.0);
        context->SetDoubleItemAttr(item_key, "plvtr", 0.0);
        context->SetDoubleItemAttr(item_key, "pftr", 0.0);
        context->SetDoubleItemAttr(item_key, "pvtr", 0.0);
        context->SetDoubleItemAttr(item_key, "pltr", 0.0);
        context->SetDoubleItemAttr(item_key, "pptr", 0.0);
        context->SetDoubleItemAttr(item_key, "pwtr", 0.0);
        context->SetDoubleItemAttr(item_key, "pcmtr", 0.0);
        context->SetDoubleItemAttr(item_key, "cascade_pctr", 0.0);
        context->SetDoubleItemAttr(item_key, "cascade_plvtr", 0.0);
        context->SetDoubleItemAttr(item_key, "cascade_pcmtr", 0.0);
        context->SetDoubleItemAttr(item_key, "cascade_pvtr", 0.0);
        context->SetDoubleItemAttr(item_key, "cascade_pltr", 0.0);
        context->SetDoubleItemAttr(item_key, "cascade_pwtr", 0.0);
        context->SetDoubleItemAttr(item_key, "cascade_pftr", 0.0);
        context->SetDoubleItemAttr(item_key, "cascade_pptr", 0.0);
        context->SetDoubleItemAttr(item_key, "empirical_ctr", 0.0);
        context->SetDoubleItemAttr(item_key, "empirical_ltr", 0.0);
        context->SetDoubleItemAttr(item_key, "empirical_wtr", 0.0);
        context->SetDoubleItemAttr(item_key, "empirical_ftr", 0.0);
        context->SetDoubleItemAttr(item_key, "empirical_ptr", 0.0);
        context->SetDoubleItemAttr(item_key, "empirical_cmtr", 0.0);
        context->SetDoubleItemAttr(item_key, "empirical_htr", 0.0);
      }
      if (sample.has_hetu_level_info()) {
        const auto hetu_level = sample.hetu_level_info();
        if (hetu_level.hetu_level_one_v1_size() > 0) {
          hetu1_v1 = hetu_level.hetu_level_one_v1()[0];
          context->SetIntItemAttr(item_key, "hetu1_v1", hetu1_v1);
        } else {
          context->SetIntItemAttr(item_key, "hetu1_v1", -1);
        }
        if (hetu_level.hetu_level_two_v1_size() > 0) {
          hetu2_v1 = hetu_level.hetu_level_two_v1()[0];
          context->SetIntItemAttr(item_key, "hetu2_v1", hetu2_v1);
        } else {
          context->SetIntItemAttr(item_key, "hetu2_v1", -1);
        }
        if (hetu_level.hetu_level_three_v1_size() > 0) {
          hetu3_v1 = hetu_level.hetu_level_three_v1()[0];
          context->SetIntItemAttr(item_key, "hetu3_v1", hetu3_v1);
        } else {
          context->SetIntItemAttr(item_key, "hetu3_v1", -1);
        }
      } else {
          context->SetIntItemAttr(item_key, "hetu1_v1", 0);
          context->SetIntItemAttr(item_key, "hetu2_v1", 0);
          context->SetIntItemAttr(item_key, "hetu3_v1", 0);
      }
      context->SetIntItemAttr(item_key, "hetu_sim_cluster_id", sample.hetu_sim_cluster_id());
      context->SetIntItemAttr(item_key, "author_circle_v2", sample.author_circle_v2());
      if (sample.real_show() == 1) {
        index = real_show_index_to_index.find(sample.real_show_index()) != real_show_index_to_index.end() ?
                real_show_index_to_index[sample.real_show_index()] : -1;
        int end_index = index + history_num_;
        if (index >= 0 && end_index < histotaltime.size()) {
          std::vector<double> tmplikelist(likelist.begin() + index, likelist.begin() + end_index);
          std::vector<double> tmpfollowlist(followlist.begin() + index, followlist.begin() + end_index);
          std::vector<double> tmpcommentlist(commentlist.begin() + index, commentlist.begin() + end_index);
          std::vector<double> tmpforwardlist(forwardlist.begin() + index, forwardlist.begin() + end_index);
          std::vector<double> tmpdownloadlist(downloadlist.begin() + index, downloadlist.begin() + end_index);
          std::vector<double> tmpprofilelist(profilelist.begin() + index, profilelist.begin() + end_index);
          std::vector<double> tmpcollectlist(collectlist.begin() + index, collectlist.begin() + end_index);
          std::vector<double> tmpplaydurlist(playdurlist.begin() + index, playdurlist.begin() + end_index);
          std::vector<double> tmpsvdurlist(svdurlist.begin() + index, svdurlist.begin() + end_index);
          std::vector<int64> tmphetu1list(hetu1list.begin() + index, hetu1list.begin() + end_index);
          std::vector<int64> tmphetu2list(hetu2list.begin() + index, hetu2list.begin() + end_index);
          std::vector<std::string> tmppidlist(pidlist.begin() + index, pidlist.begin() + end_index);
          context->SetListItemAttr(item_key, "his_like", std::move(tmplikelist));
          context->SetListItemAttr(item_key, "his_follow", std::move(tmpfollowlist));
          context->SetListItemAttr(item_key, "his_comment", std::move(tmpcommentlist));
          context->SetListItemAttr(item_key, "his_forward", std::move(tmpforwardlist));
          context->SetListItemAttr(item_key, "his_download", std::move(tmpdownloadlist));
          context->SetListItemAttr(item_key, "his_profile", std::move(tmpprofilelist));
          context->SetListItemAttr(item_key, "his_collect", std::move(tmpcollectlist));
          context->SetListItemAttr(item_key, "his_playdur", std::move(tmpplaydurlist));
          context->SetListItemAttr(item_key, "his_svdur", std::move(tmpsvdurlist));
          context->SetIntListItemAttr(item_key, "his_hetu1", std::move(tmphetu1list));
          context->SetIntListItemAttr(item_key, "his_hetu2", std::move(tmphetu2list));
          context->SetStringListItemAttr(item_key, "his_pid", std::move(tmppidlist));
          context->SetDoubleItemAttr(item_key, "his_alltime",
                                histotaltime[end_index + delay_num_] - sample.play_time_ms());
        }
        int latter_vv_cnt_valid = 0.0;
        if (index >= 0 && index < valid_count_vec.size()) {
          int index_ls = std::min(index + latter_num_, (int)valid_count_vec.size() - 1);
          latter_vv_cnt_valid = valid_count_vec[index_ls] - valid_count_vec[index];
        }
        // real_show_index_to_validcount.find(sample.real_show_index()) !=
        //     real_show_index_to_validcount.end() ?
        //       all_valid_count - real_show_index_to_validcount[sample.real_show_index()] : 0.0;
        context->SetIntItemAttr(item_key, "latter_vv_cnt_valid", latter_vv_cnt_valid);
      }
      context->SetIntItemAttr(item_key, "page", page);
      int is_last_req = 0;
      if (page == session_size) is_last_req = 1;
      context->SetIntItemAttr(item_key, "is_last_page", is_last_req);
      // absl::string_view attr_name, std::vector<T> &&value,
      //                  bool if_not_exist = false, bool check_overwrite = true) {
      if (sample.real_show() == 1) {
        int realshow_idx = sample.real_show_index();
        if (index_to_wt_label.find(realshow_idx) != index_to_wt_label.end()) {
          context->SetDoubleItemAttr(item_key, "total_playtime_win_size_6",
            index_to_wt_label[realshow_idx].first);
          context->SetDoubleItemAttr(item_key, "total_playtime_win_size_3",
            index_to_wt_label[realshow_idx].second);
          if (sess_max_realshow_index - realshow_idx >= n_size1) {
            context->SetIntItemAttr(item_key, "total_playtime_win_size_6_vaild", 1);
          } else {
            context->SetIntItemAttr(item_key, "total_playtime_win_size_6_vaild", 0);
          }
          if (sess_max_realshow_index - realshow_idx >= n_size2) {
            context->SetIntItemAttr(item_key, "total_playtime_win_size_3_vaild", 1);
          } else {
            context->SetIntItemAttr(item_key, "total_playtime_win_size_3_vaild", 0);
          }
        } else {
          context->SetDoubleItemAttr(item_key, "total_playtime_win_size_6", -1.0);
          context->SetDoubleItemAttr(item_key, "total_playtime_win_size_3", -1.0);
          context->SetIntItemAttr(item_key, "total_playtime_win_size_6_vaild", 0);
          context->SetIntItemAttr(item_key, "total_playtime_win_size_3_vaild", 0);
        }
      }
    }
    page += 1;
    later_request_time = request_time;
  }

  context->PurposelyResetRequestTime(request_time);

  // part 3 (AddToRecoResults)
  AddToRecoResults(context, retrieve_items);
}

double OfflineFulldataSessionBasedRLWatchTiredRetriever::CalcImmediatePlay(
    const ks::reco::FullLinkSample &sample) {
  double immediate_play = 0.0;
  immediate_play += play_time_weight_ * sample.play_time_ms() / 1000.0;
  immediate_play += comment_stay_time_weight_ * sample.comment_stay_time_ms() / 1000.0;
  immediate_play += server_show_weight_;
  return immediate_play;
}

double OfflineFulldataSessionBasedRLWatchTiredRetriever::CalcImmediateHd(
    const ks::reco::FullLinkSample &sample) {
  double immediate_hd = 0.0;
  immediate_hd += like_weight_ * sample.like();
  immediate_hd += comment_weight_ * sample.comment();
  immediate_hd += forward_weight_ * sample.forward();
  immediate_hd += collect_weight_ * sample.collect();
  immediate_hd += follow_weight_ * sample.follow();
  immediate_hd += download_weight_ * sample.download();
  immediate_hd += enter_profile_weight_ * sample.enter_profile();
  return immediate_hd;
}

bool OfflineFulldataSessionBasedRLWatchTiredRetriever::CalcEffectiveShow(double play_time, double duration) {
  if (duration < 7) return play_time >= duration;
  if (duration > 36) {
    return play_time >= std::max(18.0, duration * 0.4);
  } else {
    return play_time >= duration * 0.4;
  }
}

void OfflineFulldataSessionBasedRLWatchTiredRetriever::CalcHisHd(
    const ks::reco::FullLinkSample &sample, std::vector<double> *likelist,
    std::vector<double> *followlist, std::vector<double> *commentlist, std::vector<double> *forwardlist,
    std::vector<double> *collectlist, std::vector<double> *downloadlist, std::vector<double> *profilelist,
    std::vector<double> *playdurlist, std::vector<double> *svdurlist, std::vector<double> *histotaltime,
    std::vector<int64> *hetu1list, std::vector<int64> *hetu2list, std::vector<std::string> *pidlist) {
  likelist->emplace_back(sample.like());
  commentlist->emplace_back(sample.comment());
  forwardlist->emplace_back(sample.forward());
  collectlist->emplace_back(sample.collect());
  followlist->emplace_back(sample.follow());
  downloadlist->emplace_back(sample.download());
  profilelist->emplace_back(sample.enter_profile());
  playdurlist->emplace_back(sample.play_time_ms());
  svdurlist->emplace_back(sample.duration_ms());
  pidlist->emplace_back(std::to_string(sample.photo_id()));
  const auto hetu_level = sample.hetu_level_info();
  int32 hetu1_v1 = 0, hetu2_v1 = 0;
  if (hetu_level.hetu_level_one_v1_size() > 0) {
    hetu1_v1 = hetu_level.hetu_level_one_v1()[0];
  }
  if (hetu_level.hetu_level_two_v1_size() > 0) {
    hetu2_v1 = hetu_level.hetu_level_two_v1()[0];
  }
  hetu1list->emplace_back(static_cast<int64>(hetu1_v1));
  hetu2list->emplace_back(static_cast<int64>(hetu2_v1));
  if (histotaltime->size() > 0) {
    histotaltime->emplace_back(histotaltime->at(histotaltime->size() - 1) + sample.play_time_ms());
  } else {
    histotaltime->emplace_back(sample.play_time_ms());
  }
}


typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, OfflineFulldataSessionBasedRLWatchTiredRetriever,
                 OfflineFulldataSessionBasedRLWatchTiredRetriever);

}  // namespace platform
}  // namespace ks
