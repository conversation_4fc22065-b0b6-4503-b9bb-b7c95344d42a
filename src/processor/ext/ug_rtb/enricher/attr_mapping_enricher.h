#pragma once

#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class AttrMappingEnricher : public CommonRecoBaseEnricher {
 public:
  AttrMappingEnricher() = default;

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  bool InitProcessor() override {
    auto *mappings_content = config()->Get("mappings");
    if (!mappings_content || !mappings_content->IsObject()) {
      LOG(ERROR) << "AttrMappingEnricher"
                 << " init failed! Missing \"mappings\" config or it is"
                 << " not an object.";
      return false;
    }
    for (const auto &pr : mappings_content->objects()) {
      const std::string &key = pr.first;
      auto value = pr.second;
      if (!value) {
        LOG(ERROR) << "[AttrMappingEnricher] init failed, null value for key " << key;
        return false;
      }

      std::string val_str;
      if (value->IsString()) {
        val_str = value->StringValue();
      } else if (value->IsInteger()) {
        int64 int_val;
        value->IntValue(&int_val);
        val_str = std::to_string(int_val);
      } else {
        LOG(ERROR) << "[AttrMappingEnricher] unsupported mapping value type for key " << key;
        return false;
      }
      mappings_[key] = std::move(val_str);
    }
    for (const auto &kv : mappings_) {
      reverse_mappings_[kv.second] = kv.first;
    }

    is_common_ = config()->GetBoolean("is_common", true);  // 默认处理 common attr

    // 转换 configs
    const auto *configs = config()->Get("configs");
    if (configs == nullptr || !configs->IsArray() || configs->size() == 0) {
      LOG(ERROR) << "[AttrMappingEnricher] init failed, `configs` should be a non-empty list";
      return false;
    }
    for (const auto *attr : configs->array()) {
      std::string input_attr_name = attr->GetString("input");
      if (input_attr_name.empty()) {
        LOG(ERROR) << "[AttrMappingEnricher] init failed, met empty input attr name";
        return false;
      }
      std::string output_attr_name = attr->GetString("output");
      if (output_attr_name.empty()) {
        LOG(ERROR) << "[AttrMappingEnricher] init failed, met empty ouput attr name";
        return false;
      }
      int mode = attr->GetInt("mode", 1);  // 默认模式 1, 进行 key->value 的转化
      if (mode != 1 && mode != 2) {
        LOG(ERROR) << "[AttrMappingEnricher] init failed, Invalid mode, only 1(key->value) or 2(value->key) "
                      "supported";
        return false;
      }
      bool to_int = attr->GetBoolean("to_int", false);

      configs_.emplace_back(std::move(input_attr_name), std::move(output_attr_name), mode, to_int);
    }

    return true;
  }

 private:
  inline void ProcessCommon(MutableRecoContextInterface *context);
  inline void ProcessItem(MutableRecoContextInterface *context, RecoResultConstIter begin,
                          RecoResultConstIter end);
  inline absl::optional<std::string> Lookup(const std::string &input, int mode);

 private:
  struct MappingConfigs {
    std::string input_attr_name;
    std::string output_attr_name;
    int mode;
    bool to_int;
    MappingConfigs() {}
    MappingConfigs(std::string &&input, std::string &&output, int m, bool toInt)
        : input_attr_name(std::move(input)), output_attr_name(std::move(output)), mode(m), to_int(toInt) {}
  };

  std::unordered_map<std::string, std::string> mappings_;          // key -> value
  std::unordered_map<std::string, std::string> reverse_mappings_;  // value -> key
  std::vector<MappingConfigs> configs_;
  bool is_common_ = true;

  DISALLOW_COPY_AND_ASSIGN(AttrMappingEnricher);
};

}  // namespace platform
}  // namespace ks
