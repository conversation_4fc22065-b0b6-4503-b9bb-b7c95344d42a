#include <cstdint>
#include <string>
#include <utility>

#include "dragon/src/module/future_action_wrapper.h"
#include "dragon/src/processor/ext/ug_rtb/enricher/did_uid_resolver_enricher.h"
#include "ks/reco_proto/proto/idmapping/product_platform.pb.h"

#include "absl/strings/escaping.h"

namespace ks {
namespace platform {

bool DidUidResolverEnricher::InitProcessor() {
  int input_behavior = config()->GetInt("behavior", 0);
  if (!IsValidBehavior(input_behavior)) {
    LOG(ERROR) << "[DidUidResolverEnricher] init failed, invalid behavior: " << input_behavior;
    return false;
  }
  behavior_ = static_cast<IdMappingBehavior>(input_behavior);
  strategy_ = GetMappingStrategy(behavior_);

  input_id_attr_name_ = config()->GetString("input_id_attr_name");
  if (input_id_attr_name_.empty()) {
    LOG(ERROR) << "[DidUidResolverEnricher] init failed, input_id_attr_name can not be empty";
    return false;
  }

  request_src_ = config()->GetString("request_src");
  if (request_src_.empty()) {
    LOG(ERROR) << "[DidUidResolverEnricher] init failed, request_src can not be empty";
    return false;
  }

  output_id_attr_name_ = config()->GetString("output_id_attr_name");
  if (output_id_attr_name_.empty()) {
    LOG(ERROR) << "[DidUidResolverEnricher] init failed, output_id_attr_name can not be empty";
    return false;
  }

  resolve_status_attr_ = config()->GetString("resolve_status_attr");
  if (resolve_status_attr_.empty()) {
    LOG(ERROR) << "[DidUidResolverEnricher] init failed, resolve_status_attr can not be empty";
    return false;
  }

  // 可选参数
  kpn_product_ = config()->GetString("kpn_product", "");

  int product_val = config()->GetInt("product", 0);
  if (!kuaishou::ug::idmapping::Product_IsValid(product_val)) {
    LOG(ERROR) << "[DidUidResolverEnricher] init failed, product must be positive integer, current value: "
               << product_val;
    return false;
  }
  product_ = static_cast<Product>(product_val);

  if (strategy_.requires_activity) {
    output_active_ts_attr_ = config()->GetString("output_active_ts_attr");
    if (output_active_ts_attr_.empty()) {
      LOG(ERROR) << "[DidUidResolverEnricher] init failed, output_active_ts_attr is required for behavior "
                 << behavior_;
      return false;
    }
  }

  timeout_ms_ = config()->GetInt("timeout_ms", timeout_ms_);
  std::string specified_cluster_name = config()->GetString("cluster_name", "");
  if (!specified_cluster_name.empty()) {
    strategy_.cluster_name = specified_cluster_name;
  }

  redis_client_ = GetAsyncRedisClient();
  if (!redis_client_) {
    LOG(ERROR) << "[DidUidResolverEnricher] init failed, redis client init failed, cluster: "
               << strategy_.cluster_name;
    return false;
  }

  LOG(INFO) << "[DidUidResolverEnricher] init success, behavior: " << behavior_
            << ", cluster: " << strategy_.cluster_name << ", input_attr: " << input_id_attr_name_
            << ", output_attr: " << output_id_attr_name_ << ", request_src: " << request_src_;
  return true;
}

void DidUidResolverEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
  // 1. 获取输入 ID
  auto value = context->GetStringCommonAttr(input_id_attr_name_);
  if (!value) {
    CL_LOG(ERROR) << "Missing input_id for attr: " << input_id_attr_name_;
    context->SetStringCommonAttr(resolve_status_attr_, "missing_input_id");
    return;
  }
  std::string input_id(value->data(), value->size());

  // 2. 生成 Redis key 和 field
  std::string redis_key = DeriveKeyFromID(input_id);
  std::string redis_field = DeriveFieldFromProduct();

  CL_LOG(INFO) << "[DidUidResolverEnricher] input_id: " << input_id << ", behavior: " << behavior_
               << ", product: " << product_ << ", redis_key_hex: " << absl::BytesToHexString(redis_key)
               << ", redis_field: " << redis_field;

  // 3. 使用 CommonRecoFutureActionWrapper 封装异步操作
  // redis_value 的格式对齐 kuaishou/promotion/basedata/sdk/basedata_identity_active.proto IdentityActiveInfo
  // 其中的 active_id 经过进制转换压缩，last_active_time 为秒级时间戳
  auto redis_future = CommonRecoFutureActionWrapper<RedisHashGetResult>(
      [this, context, redis_key, redis_field]() -> RedisHashGetResult {
        int64_t start_us = base::GetTimestamp();

        auto response = redis_client_->HashGet(redis_key, redis_field);
        RedisHashGetResult result;
        result.err_code = response.Get(&result.redis_value, timeout_ms_);
        result.err_msg = ks::infra::err2str(result.err_code);

        int64_t async_cost_us = base::GetTimestamp() - start_us;
        CL_PERF_INTERVAL(async_cost_us, kPerfNs, "processor_async_ready",
                         GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName(),
                         GetDownstreamProcessor(), "", GlobalHolder::GetJsonConfigVersion());

        return result;
      });

  // 4. 回调函数将在 下游算子执行前 & future 就绪后 执行
  auto callback = [this, context, input_id, redis_key, redis_field](const RedisHashGetResult &result) {
    ProcessRedisResponse(context, input_id, redis_key, redis_field, result);
  };

  std::string request_info = absl::StrCat("DidUidResolverEnricherAsync: ", input_id, " -> ", redis_field);
  RegisterAsyncCallback(context, std::move(redis_future), std::move(callback), request_info);
}

void DidUidResolverEnricher::ProcessRedisResponse(MutableRecoContextInterface *context,
                                                  const std::string &input_id, const std::string &redis_key,
                                                  const std::string &redis_field,
                                                  const RedisHashGetResult &result) {
  // 1. 检验 Redis 返回值
  if (!result.IsSuccess()) {
    std::string error_log =
        absl::StrCat("Redis HashGet failed, [err_code: ", result.err_code, ", err_str: ", result.err_msg,
                     "], redis_key_hex: ", absl::BytesToHexString(redis_key), ", redis_field: ", redis_field);
    CL_LOG(ERROR) << error_log;
    std::string resolve_status = absl::StrCat("redis_hget_failed_code_", result.err_code);
    context->SetStringCommonAttr(resolve_status_attr_, resolve_status);
    return;
  }

  if (result.redis_value.empty()) {
    CL_LOG(ERROR) << "Redis value is empty, redis_key_hex: " << absl::BytesToHexString(redis_key)
                  << ", redis_field: " << redis_field;
    context->SetStringCommonAttr(resolve_status_attr_, "redis_value_empty");
    return;
  }

  // 2. 反序列化并解析结果
  IdentityActiveInfo raw_identity_active_info;
  if (!raw_identity_active_info.ParseFromString(result.redis_value)) {
    CL_LOG(ERROR) << "Failed to parse IdentityActiveInfo from redis_value, redis_key_hex: "
                  << absl::BytesToHexString(redis_key) << ", redis_field: " << redis_field
                  << ", redis_value_size: " << result.redis_value.size();
    context->SetStringCommonAttr(resolve_status_attr_, "parse_from_redis_failed");
    return;
  }

  // 3. 从被优化的 ID 中还原出原始的 ID，并采用毫秒级别时间戳，逻辑对齐：
  // com.kuaishou.promotion.basedata.component.service.mapping.impl.BaseDataUidDidMappingServiceImpl#getDidIdentityActiveInfoFromOptimization
  IdentityActiveInfo identity_active_info = DecodeIdentityAndMillis(raw_identity_active_info);
  CL_LOG(INFO) << "After decode: identity_active_info.DebugString=" << identity_active_info.DebugString();

  // 4. 选择最近活跃的记录
  const auto &active_infos = identity_active_info.active_info();
  if (active_infos.empty()) {
    CL_LOG(ERROR) << "No active info found, input_id: " << input_id;
    context->SetStringCommonAttr(resolve_status_attr_, "no_active_info");
    return;
  }

  auto most_recent_it = std::max_element(
      active_infos.begin(), active_infos.end(),
      [](const ActiveData &a, const ActiveData &b) { return a.last_active_time() < b.last_active_time(); });

  // 5. 设置输出结果
  std::string output_id = most_recent_it->active_id();
  uint64_t last_active_timestamp_ms = most_recent_it->last_active_time();

  context->SetStringCommonAttr(output_id_attr_name_, output_id);
  if (strategy_.requires_activity && !output_active_ts_attr_.empty()) {
    context->SetIntCommonAttr(output_active_ts_attr_, last_active_timestamp_ms);
  }

  context->SetStringCommonAttr(resolve_status_attr_, "ok");
  CL_LOG(INFO) << "ID conversion successful: " << input_id << " -> " << output_id
               << (strategy_.requires_activity ? absl::StrCat(", active_ts: ", last_active_timestamp_ms)
                                               : "");
}

std::string DidUidResolverEnricher::DeriveKeyFromID(const std::string &input_id) const {
  std::string optimized_id = IdCompressionUtil::CompressTypedIDToBase256(input_id, strategy_.input_id_type);
  return strategy_.key_prefix + optimized_id;
}

std::string DidUidResolverEnricher::DeriveFieldFromProduct() const {
  // 1. 先做快手系判断
  // 用户不传 product 值，默认值则为 UNKNOWN_PRODUCT
  if (kuaishou::ug::idmapping::Product_IsValid(product_) && product_ != Product::UNKNOWN_PRODUCT) {
    return std::to_string(static_cast<int>(product_));
  }

  // 2. 兼容新产品系部分 kpn，节省存储 - 检查 kpn_product 是否可以转换为有效的 Product
  // NOTICE!!! 实际上这里符合业务语义的判断逻辑应该是 if(!kpn_product_.empty())
  // 但为了获取到的 field 完全一样，对齐 java idmapping sdk 判断逻辑
  if (product_ != Product::UNKNOWN_PRODUCT && !kpn_product_.empty()) {
    Product transferred_product = KpnStrToProduct(kpn_product_);
    if (transferred_product != Product::UNKNOWN_PRODUCT) {
      return std::to_string(static_cast<int>(transferred_product));
    }
  }

  // 3. 检查是否为 PromotionKPN
  if (IsPromotionKpnProduct(kpn_product_)) {
    return kpn_product_;
  }

  CL_LOG(ERROR) << "Cannot derive valid redis field, product: " << static_cast<int>(product_)
                << ", kpn_product: " << kpn_product_;
  return "";
}

IdentityActiveInfo DidUidResolverEnricher::DecodeIdentityAndMillis(const IdentityActiveInfo &raw_info) const {
  // 遍历 raw_info 中的 ActiveData 数组，将里边的优化过的 id 还原成原始的 id。
  // 根据 id_type 不同，还原的算法不同。
  IdentityActiveInfo restored_info;

  for (const auto &raw_active_data : raw_info.active_info()) {
    ActiveData *restored_active_data = restored_info.add_active_info();
    // 还原 ID
    std::string original_id =
        IdCompressionUtil::DecompressBase256ToTypedID(raw_active_data.active_id(), strategy_.output_id_type);
    restored_active_data->set_active_id(original_id);
    // 秒级时间戳转毫秒级
    restored_active_data->set_last_active_time(raw_active_data.last_active_time() * kSecondToMillisecond);
  }
  return restored_info;
}

ks::infra::RedisPipelineClient *DidUidResolverEnricher::GetAsyncRedisClient() {
  auto *redis_client =
      ks::infra::RedisProxyClient::GetRedisPipelineClientByKccFromKconf(strategy_.cluster_name, timeout_ms_);
  if (!redis_client) {
    CL_LOG(WARNING) << "GetRedisClientByKccFromKconf failed, trying GetRedisClientByKcc, cluster: "
                    << strategy_.cluster_name;
    redis_client =
        ks::infra::RedisProxyClient::GetRedisPipelineClientByKcc(strategy_.cluster_name, timeout_ms_);
    if (!redis_client) {
      CL_LOG(ERROR) << "GetRedisClientByKcc also failed, cluster: " << strategy_.cluster_name;
    }
  }
  return redis_client;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, DidUidResolverEnricher, DidUidResolverEnricher)

}  // namespace platform
}  // namespace ks
