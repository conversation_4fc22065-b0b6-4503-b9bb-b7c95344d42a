#pragma once

#include <cstdint>
#include <memory>
#include <ostream>
#include <string>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/processor/ext/ug_rtb/util/id_compression_util.h"
#include "dragon/src/processor/ext/ug_rtb/util/product_util.h"
#include "redis_proxy_client/redis_proxy_client.h"

#include "ks/reco_proto/proto/idmapping/identity_active_info.pb.h"

namespace ks {
namespace platform {

using IdentityActiveInfo = kuaishou::ug::idmapping::IdentityActiveInfo;
using ActiveData = kuaishou::ug::idmapping::ActiveData;

inline constexpr int kSecondToMillisecond = 1000;

// 默认集群名, 可由用户传参 `cluster_name` 覆盖
inline constexpr char kUid2DidClusterName[] = "userGrowthBaseDataUidDidMapping";
inline constexpr char kDid2UidClusterName[] = "userGrowthBaseDataDidUidMapping";

inline constexpr char kUidDidMappingPrefix[] = "u:";
inline constexpr char kDidUidMappingPrefix[] = "d:";

enum class IdMappingBehavior : uint8_t {
  MIN_VALUE = 1,

  DID_TO_UID = 1,
  DID_TO_UID_WITH_ACTIVITY = 2,
  UID_TO_DID = 3,
  UID_TO_DID_WITH_ACTIVITY = 4,

  MAX_VALUE = 4,
  COUNT = 4
};

inline std::ostream &operator<<(std::ostream &os, const IdMappingBehavior &behavior) {
  switch (behavior) {
    case IdMappingBehavior::DID_TO_UID:
      return os << "DID_TO_UID";
    case IdMappingBehavior::DID_TO_UID_WITH_ACTIVITY:
      return os << "DID_TO_UID_WITH_ACTIVITY";
    case IdMappingBehavior::UID_TO_DID:
      return os << "UID_TO_DID";
    case IdMappingBehavior::UID_TO_DID_WITH_ACTIVITY:
      return os << "UID_TO_DID_WITH_ACTIVITY";
    default:
      return os << "UNKNOWN_BEHAVIOR";
  }
}

// ID 映射策略配置
struct MappingStrategy {
  IDType input_id_type = IDType::kDeviceId;
  IDType output_id_type = IDType::kUserId;
  std::string cluster_name = kDid2UidClusterName;
  std::string key_prefix = kDidUidMappingPrefix;
  bool requires_activity = false;
};

inline MappingStrategy GetMappingStrategy(IdMappingBehavior behavior) {
  switch (behavior) {
    case IdMappingBehavior::DID_TO_UID:
      return {IDType::kDeviceId, IDType::kUserId, kDid2UidClusterName, kDidUidMappingPrefix, false};
    case IdMappingBehavior::DID_TO_UID_WITH_ACTIVITY:
      return {IDType::kDeviceId, IDType::kUserId, kDid2UidClusterName, kDidUidMappingPrefix, true};
    case IdMappingBehavior::UID_TO_DID:
      return {IDType::kUserId, IDType::kDeviceId, kUid2DidClusterName, kUidDidMappingPrefix, false};
    case IdMappingBehavior::UID_TO_DID_WITH_ACTIVITY:
      return {IDType::kUserId, IDType::kDeviceId, kUid2DidClusterName, kUidDidMappingPrefix, true};
    default:
      return {IDType::kDeviceId, IDType::kUserId, kDid2UidClusterName, kDidUidMappingPrefix, false};
  }
}

struct RedisHashGetResult {
  ks::infra::RedisErrorCode err_code = ks::infra::KS_INF_REDIS_NO_ERROR;
  std::string redis_value;
  std::string err_msg;

  bool IsSuccess() const {
    return err_code == ks::infra::KS_INF_REDIS_NO_ERROR;
  }
};

// DID/UID 互转 Enricher：对齐 Java idmapping SDK，支持 4 种转换策略+活跃时间输出
// 仓库地址: https://git.corp.kuaishou.com/kuaishou-attribution-platform/kuaishou-promotion-basedata
class DidUidResolverEnricher : public CommonRecoBaseEnricher {
 public:
  DidUidResolverEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  bool InitProcessor() override;

  bool IsAsync() const override {
    return true;
  }

 private:
  bool IsValidBehavior(int value) const {
    return value >= static_cast<int>(IdMappingBehavior::MIN_VALUE) &&
           value <= static_cast<int>(IdMappingBehavior::MAX_VALUE);
  }

  // 生成 Redis key，保证与 Java 端字节序列一致性
  // 核心在于：C++ 端必须生成与 Java 端 redisKey.getBytes() 完全相同的字节序列
  // === Java 端实现细节 ===
  // 1. getBytes() 未显示指定字符集，使用平台默认编码(Linux->UTF-8)，存在一定风险
  // 2. 压缩算法：两个 16 进制字符压缩到一个 char(16bit)
  //    - 2bit：大小写标志(00,01,10,11)
  //    - 8bit：两个字符的数值(各 4bit，范围 0~15)
  //    - 总计：2+4+4=10bit 有效位
  //
  // === C++ 适配 ===
  // Redis 接口接收 std::string 参数，需模拟 Java 的 UTF-8 编码输出
  std::string DeriveKeyFromID(const std::string &input_id) const;

  // 生成 Redis field，该方法返回快手系的产品编号或者新产品系的产品名
  std::string DeriveFieldFromProduct() const;

  // ID 编解码
  IdentityActiveInfo DecodeIdentityAndMillis(const IdentityActiveInfo &raw_info) const;

  // 处理 Redis 响应结果
  void ProcessRedisResponse(MutableRecoContextInterface *context, const std::string &input_id,
                            const std::string &redis_key, const std::string &redis_field,
                            const RedisHashGetResult &result);

  // 获取 Redis 客户端
  ks::infra::RedisPipelineClient *GetAsyncRedisClient();

  IdMappingBehavior behavior_ = IdMappingBehavior::DID_TO_UID;
  MappingStrategy strategy_;

  std::string input_id_attr_name_;
  std::string output_id_attr_name_;
  std::string output_active_ts_attr_;
  std::string resolve_status_attr_;
  std::string request_src_;

  Product product_ = Product::UNKNOWN_PRODUCT;
  std::string kpn_product_;

  int32_t timeout_ms_ = 10;
  ks::infra::RedisPipelineClient *redis_client_ = nullptr;

  DISALLOW_COPY_AND_ASSIGN(DidUidResolverEnricher);
};

}  // namespace platform
}  // namespace ks
