#include "dragon/src/processor/ext/ug_rtb/enricher/attr_mapping_enricher.h"

#include <stdint.h>
#include <optional>
#include <string>
#include <unordered_map>

namespace ks {
namespace platform {

void AttrMappingEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
  if (is_common_) {
    ProcessCommon(context);
  } else {
    ProcessItem(context, begin, end);
  }
}

void AttrMappingEnricher::ProcessCommon(MutableRecoContextInterface *context) {
  for (const auto &config : configs_) {
    auto input_attr = context->GetCommonAttr(config.input_attr_name);
    if (!input_attr) {
      return;
    }

    std::string key;
    if (auto val = context->GetStringCommonAttr(config.input_attr_name)) {
      key = std::string(*val);
    } else if (auto val = context->GetIntCommonAttr(config.input_attr_name)) {
      key = std::to_string(*val);
    }
    auto res = Lookup(key, config.mode);
    if (res.has_value()) {
      std::string value_str = res.value();
      if (config.to_int) {
        int64 int_val;
        if (absl::SimpleAtoi(value_str, &int_val)) {
          context->SetIntCommonAttr(config.output_attr_name, int_val);
        }
      } else {
        context->SetStringCommonAttr(config.output_attr_name, value_str);
      }
    }
  }
}

void AttrMappingEnricher::ProcessItem(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                      RecoResultConstIter end) {
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    for (const auto &config : configs_) {
      auto *input_item_attr_accessor = context->GetItemAttrAccessor(config.input_attr_name);
      auto *output_item_attr_accessor = context->GetItemAttrAccessor(config.output_attr_name);

      // 读取
      std::string key;
      if (auto val = context->GetStringItemAttr(result, input_item_attr_accessor)) {
        key = std::string(*val);
      } else if (auto val = context->GetIntItemAttr(result, input_item_attr_accessor)) {
        key = std::to_string(*val);
      }
      auto res = Lookup(key, config.mode);
      if (res.has_value()) {
        std::string value_str = res.value();
        if (config.to_int) {
          int64 int_val;
          if (absl::SimpleAtoi(value_str, &int_val)) {
            context->SetIntItemAttr(result, output_item_attr_accessor, int_val);
          }
        } else {
          context->SetStringItemAttr(result, output_item_attr_accessor, value_str);
        }
      }
    }  // end for configs_
  });

  return;
}

absl::optional<std::string> AttrMappingEnricher::Lookup(const std::string &input, int mode) {
  if (mode == 1) {
    auto it = mappings_.find(input);
    if (it == mappings_.end()) return absl::nullopt;
    return it->second;
  } else if (mode == 2) {
    auto it = reverse_mappings_.find(input);
    if (it == reverse_mappings_.end()) return absl::nullopt;
    return it->second;
  }
  return absl::nullopt;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AttrMappingEnricher, AttrMappingEnricher)

}  // namespace platform
}  // namespace ks
