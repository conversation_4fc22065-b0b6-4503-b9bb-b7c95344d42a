#include "dragon/src/processor/ext/ug_rtb/util/id_compression_util.h"

#include <gtest/gtest.h>
#include <string>
#include <vector>

namespace ks {
namespace platform {

class IdCompressionUtilTest : public ::testing::Test {
 protected:
  void SetUp() override {}
  void TearDown() override {}
};

TEST_F(IdCompressionUtilTest, HexCharToIntValid) {
  EXPECT_EQ(IdCompressionUtil::HexCharToInt('0'), 0);
  EXPECT_EQ(IdCompressionUtil::HexCharToInt('5'), 5);
  EXPECT_EQ(IdCompressionUtil::HexCharToInt('9'), 9);

  EXPECT_EQ(IdCompressionUtil::HexCharToInt('a'), 10);
  EXPECT_EQ(IdCompressionUtil::HexCharToInt('f'), 15);

  EXPECT_EQ(IdCompressionUtil::HexCharToInt('A'), 10);
  EXPECT_EQ(IdCompressionUtil::HexCharToInt('F'), 15);
}

TEST_F(IdCompressionUtilTest, HexCharToIntInvalid) {
  EXPECT_EQ(IdCompressionUtil::HexCharToInt('g'), -1);
  EXPECT_EQ(IdCompressionUtil::HexCharToInt('G'), -1);
  EXPECT_EQ(IdCompressionUtil::HexCharToInt('z'), -1);
  EXPECT_EQ(IdCompressionUtil::HexCharToInt('@'), -1);
  EXPECT_EQ(IdCompressionUtil::HexCharToInt(' '), -1);
}

TEST_F(IdCompressionUtilTest, IsUpperHexAlpha) {
  EXPECT_TRUE(IdCompressionUtil::IsUpperHexAlpha('A'));
  EXPECT_TRUE(IdCompressionUtil::IsUpperHexAlpha('B'));
  EXPECT_TRUE(IdCompressionUtil::IsUpperHexAlpha('F'));

  EXPECT_FALSE(IdCompressionUtil::IsUpperHexAlpha('a'));
  EXPECT_FALSE(IdCompressionUtil::IsUpperHexAlpha('0'));
  EXPECT_FALSE(IdCompressionUtil::IsUpperHexAlpha('9'));
  EXPECT_FALSE(IdCompressionUtil::IsUpperHexAlpha('G'));
  EXPECT_FALSE(IdCompressionUtil::IsUpperHexAlpha('Z'));
}

TEST_F(IdCompressionUtilTest, IntToHexStr) {
  EXPECT_EQ(IdCompressionUtil::IntToHexStr(0, false), "0");
  EXPECT_EQ(IdCompressionUtil::IntToHexStr(10, false), "a");
  EXPECT_EQ(IdCompressionUtil::IntToHexStr(15, false), "f");

  EXPECT_EQ(IdCompressionUtil::IntToHexStr(0, true), "0");
  EXPECT_EQ(IdCompressionUtil::IntToHexStr(10, true), "A");
  EXPECT_EQ(IdCompressionUtil::IntToHexStr(15, true), "F");
}

TEST_F(IdCompressionUtilTest, CaseConversion) {
  std::string test_str = "AbC123dEf";

  EXPECT_EQ(IdCompressionUtil::ToLowerCase(test_str), "abc123def");
  EXPECT_EQ(IdCompressionUtil::ToUpperCase(test_str), "ABC123DEF");

  EXPECT_EQ(IdCompressionUtil::ToLowerCase(""), "");
  EXPECT_EQ(IdCompressionUtil::ToUpperCase(""), "");
}

// 测试压缩/解压缩：CompressHexToBase256/DecompressBase256ToHex
TEST_F(IdCompressionUtilTest, HexCompressionDecompression) {
  std::string hex_str = "1234abcd";
  std::string compressed = IdCompressionUtil::CompressHexToBase256(hex_str);
  std::string decompressed = IdCompressionUtil::DecompressBase256ToHex(compressed);

  EXPECT_EQ(decompressed, hex_str);
}

TEST_F(IdCompressionUtilTest, HexCompressionWithMixedCase) {
  std::string hex_str = "1234abcD12ABCD";
  std::string compressed = IdCompressionUtil::CompressHexToBase256(hex_str);
  std::string decompressed = IdCompressionUtil::DecompressBase256ToHex(compressed);

  EXPECT_EQ(decompressed, hex_str);
}

TEST_F(IdCompressionUtilTest, HexCompressionEmptyString) {
  std::string empty_str = "";
  std::string compressed = IdCompressionUtil::CompressHexToBase256(empty_str);
  EXPECT_EQ(compressed, "");

  std::string decompressed = IdCompressionUtil::DecompressBase256ToHex(compressed);
  EXPECT_EQ(decompressed, "");
}

// 测试编码 TypeSign：CompressTypedIDToBase256/
TEST_F(IdCompressionUtilTest, CompressTypedIDMuid) {
  // 测试 MUID 压缩
  std::string muid = "1234567890abcdef1234567890abcdef";  // 32 位十六进制
  std::string compressed = IdCompressionUtil::CompressTypedIDToBase256(muid, IDType::kMuid);

  EXPECT_FALSE(compressed.empty());
  EXPECT_EQ(compressed[0], kTypeSignMuid);
}

TEST_F(IdCompressionUtilTest, CompressTypedIDAndroidDeviceId) {
  // 测试 Android Device ID 压缩
  std::string android_id = "ANDROID_1234567890abcdef";
  std::string compressed = IdCompressionUtil::CompressTypedIDToBase256(android_id, IDType::kDeviceId);

  EXPECT_FALSE(compressed.empty());
  EXPECT_EQ(compressed[0], kTypeSignDidAndroid);
}

TEST_F(IdCompressionUtilTest, CompressTypedIDiOSDeviceId) {
  // 测试 iOS Device ID 压缩
  std::string ios_id = "12345678-1234-1234-1234-123456789ABC";
  std::string compressed = IdCompressionUtil::CompressTypedIDToBase256(ios_id, IDType::kDeviceId);

  EXPECT_FALSE(compressed.empty());
  EXPECT_EQ(compressed[0], kTypeSignDidIos);
}

TEST_F(IdCompressionUtilTest, CompressTypedIDUserId) {
  // 测试 User ID 压缩
  std::string user_id = "123456789";
  std::string compressed = IdCompressionUtil::CompressTypedIDToBase256(user_id, IDType::kUserId);

  EXPECT_FALSE(compressed.empty());
  EXPECT_EQ(compressed[0], kTypeSignUid);
}

TEST_F(IdCompressionUtilTest, CompressTypedIDGlobalId) {
  // 测试 Global ID 压缩
  std::string global_id = "abcdef123456";
  std::string compressed = IdCompressionUtil::CompressTypedIDToBase256(global_id, IDType::kGlobalId);

  EXPECT_FALSE(compressed.empty());
  EXPECT_EQ(compressed[0], kTypeSignGid);
}

// 测试编码/解码：CompressTypedIDToBase256/DecompressBase256ToTypedID
TEST_F(IdCompressionUtilTest, DecompressBase256ToTypedIDMuid) {
  // 测试 Muid 完整的压缩-解压流程
  std::string original_muid = "1234567890abcdef1234567890abcdef";
  std::string compressed = IdCompressionUtil::CompressTypedIDToBase256(original_muid, IDType::kMuid);
  std::string decompressed = IdCompressionUtil::DecompressBase256ToTypedID(compressed, IDType::kMuid);

  EXPECT_EQ(decompressed, original_muid);
}

TEST_F(IdCompressionUtilTest, DecompressBase256ToTypedIDUid) {
  // 测试 uid 完整的压缩-解压流程
  std::vector<std::string> test_cases = {"791990746", "120514214",  "413080852",  "851906401", "647402741",
                                         "629550429", "2528829908", "3458642356", "2881507257"};
  for (const std::string &original_uid : test_cases) {
    std::string compressed = IdCompressionUtil::CompressTypedIDToBase256(original_uid, IDType::kUserId);
    std::string decompressed = IdCompressionUtil::DecompressBase256ToTypedID(compressed, IDType::kUserId);
    EXPECT_EQ(decompressed, original_uid);
  }
}

TEST_F(IdCompressionUtilTest, DecompressBase256ToTypedIDAndroid) {
  // 测试 Android Device ID 的完整流程
  std::string original_id = "ANDROID_1234567890abcdef";
  std::string compressed = IdCompressionUtil::CompressTypedIDToBase256(original_id, IDType::kDeviceId);
  std::string decompressed = IdCompressionUtil::DecompressBase256ToTypedID(compressed, IDType::kDeviceId);

  EXPECT_EQ(decompressed, original_id);
}

TEST_F(IdCompressionUtilTest, DecompressBase256ToTypedIDIos) {
  // 测试 iOS did 的完整流程
  std::string original_id = "E199C5F4-45E6-4E2E-AB86-7A384D674647";
  std::string compressed = IdCompressionUtil::CompressTypedIDToBase256(original_id, IDType::kDeviceId);
  std::string decompressed = IdCompressionUtil::DecompressBase256ToTypedID(compressed, IDType::kDeviceId);

  EXPECT_EQ(decompressed, original_id);
}

TEST_F(IdCompressionUtilTest, CompressTypedIDInvalidFormat) {
  // 测试无效的 MUID 格式
  std::string invalid_muid = "invalid_muid";
  std::string compressed = IdCompressionUtil::CompressTypedIDToBase256(invalid_muid, IDType::kMuid);

  EXPECT_EQ(compressed[0], kTypeSignError);
  EXPECT_EQ(compressed.substr(1), invalid_muid);
}

TEST_F(IdCompressionUtilTest, CompressTypedIDEmptyString) {
  std::string empty_str = "";
  std::string compressed = IdCompressionUtil::CompressTypedIDToBase256(empty_str, IDType::kMuid);

  EXPECT_EQ(compressed, "");
}

}  // namespace platform
}  // namespace ks
