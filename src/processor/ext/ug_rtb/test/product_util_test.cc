#include "dragon/src/processor/ext/ug_rtb/util/product_util.h"

#include <gtest/gtest.h>
#include <string>

namespace ks {
namespace platform {

class ProductUtilTest : public ::testing::Test {
 protected:
  void SetUp() override {}
  void TearDown() override {}
};

// 测试 KpnStrToProduct 函数
// 有效的 Product 枚举值
TEST_F(ProductUtilTest, KpnStrToProductValidEnum) {
  Product result = KpnStrToProduct("KUAISHOU");
  EXPECT_EQ(result, Product::KUAISHOU);

  result = KpnStrToProduct("KUAISHOU_NEBULA");
  EXPECT_EQ(result, Product::KUAISHOU_NEBULA);

  result = KpnStrToProduct("KWAI");
  EXPECT_EQ(result, Product::KWAI);
}

// 特殊映射
TEST_F(ProductUtilTest, KpnStrToProductSpecialMappings) {
  Product result = KpnStrToProduct("NEBULA");
  EXPECT_EQ(result, Product::KUAISHOU_NEBULA);

  result = KpnStrToProduct("THANOS");
  EXPECT_EQ(result, Product::KUAISHOU_THANOS);

  result = KpnStrToProduct("NEBULA_ANTMAN");
  EXPECT_EQ(result, Product::KUAISHOU_NEBULA_ANTMAN);
}

// 未知产品名称
TEST_F(ProductUtilTest, KpnStrToProductUnknown) {
  Product result = KpnStrToProduct("UNKNOWN_PRODUCT_NAME");
  EXPECT_EQ(result, Product::UNKNOWN_PRODUCT);

  result = KpnStrToProduct("");
  EXPECT_EQ(result, Product::UNKNOWN_PRODUCT);

  result = KpnStrToProduct("INVALID");
  EXPECT_EQ(result, Product::UNKNOWN_PRODUCT);
}

// 大小写敏感性
TEST_F(ProductUtilTest, KpnStrToProductCaseSensitive) {
  Product result = KpnStrToProduct("nebula");  // 小写
  EXPECT_EQ(result, Product::UNKNOWN_PRODUCT);

  result = KpnStrToProduct("Nebula");  // 首字母大写
  EXPECT_EQ(result, Product::UNKNOWN_PRODUCT);
}

// 测试 IsPromotionKpnProduct 函数
// 有效 promotion_kpn 字符串
TEST_F(ProductUtilTest, IsPromotionKpnProductValid) {
  EXPECT_TRUE(IsPromotionKpnProduct("SOGAME"));
  EXPECT_TRUE(IsPromotionKpnProduct("hlg"));
  EXPECT_TRUE(IsPromotionKpnProduct("pearl"));
  EXPECT_TRUE(IsPromotionKpnProduct("KG_APP_NOVEL"));
  EXPECT_TRUE(IsPromotionKpnProduct("XIFAN"));
}

// 非 promotion_kpn_product
TEST_F(ProductUtilTest, IsPromotionKpnProductInvalid) {
  EXPECT_FALSE(IsPromotionKpnProduct("KUAISHOU"));
  EXPECT_FALSE(IsPromotionKpnProduct("KWAI"));
  EXPECT_FALSE(IsPromotionKpnProduct("NEBULA"));
  EXPECT_FALSE(IsPromotionKpnProduct(""));
  EXPECT_FALSE(IsPromotionKpnProduct("UNKNOWN"));
}

// KpnProduct 大小写敏感性
TEST_F(ProductUtilTest, IsPromotionKpnProductCaseSensitive) {
  EXPECT_FALSE(IsPromotionKpnProduct("sogame"));  // 小写
  EXPECT_FALSE(IsPromotionKpnProduct("Sogame"));  // 首字母大写
  EXPECT_FALSE(IsPromotionKpnProduct("HLG"));     // 大写
}

// 边界条件测试
TEST_F(ProductUtilTest, EdgeCases) {
  // 测试空字符串
  Product result = KpnStrToProduct("");
  EXPECT_EQ(result, Product::UNKNOWN_PRODUCT);

  EXPECT_FALSE(IsPromotionKpnProduct(""));

  // 测试包含空格的字符串
  result = KpnStrToProduct(" NEBULA ");
  EXPECT_EQ(result, Product::UNKNOWN_PRODUCT);

  EXPECT_FALSE(IsPromotionKpnProduct(" SOGAME "));
}

}  // namespace platform
}  // namespace ks
