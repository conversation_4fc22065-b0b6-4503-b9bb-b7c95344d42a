#pragma once

#include <string>
#include <unordered_map>
#include <unordered_set>

#include "ks/reco_proto/proto/idmapping/product_platform.pb.h"

namespace ks {
namespace platform {

using Product = kuaishou::ug::idmapping::Product;

inline const std::unordered_map<std::string, Product> &GetSpecialKpnMappings() {
  static const std::unordered_map<std::string, Product> mappings = {
      {"NEBULA", Product::KUAISHOU_NEBULA},
      {"THANOS", Product::KUAISHOU_THANOS},
      {"NEBULA_ANTMAN", Product::KUAISHOU_NEBULA_ANTMAN}};
  return mappings;
}

inline const std::unordered_set<std::string> &GetPromotionKpnSet() {
  static const std::unordered_set<std::string> promotion_set = {"SOGAME", "hlg", "pearl", "KG_APP_NOVEL",
                                                                "XIFAN"};
  return promotion_set;
}

inline Product KpnStrToProduct(const std::string &app_product) {
  Product product = Product::UNKNOWN_PRODUCT;
  // 尝试 app_product 字符串能否正常对应到 Product 枚举
  if (kuaishou::ug::idmapping::Product_Parse(app_product, &product)) {
    return product;
  }

  const auto &mappings = GetSpecialKpnMappings();
  auto it = mappings.find(app_product);
  if (it != mappings.end()) {
    return it->second;
  }

  return product;
}

inline bool IsPromotionKpnProduct(const std::string &product_name) {
  return GetPromotionKpnSet().count(product_name) > 0;
}

}  // namespace platform
}  // namespace ks
