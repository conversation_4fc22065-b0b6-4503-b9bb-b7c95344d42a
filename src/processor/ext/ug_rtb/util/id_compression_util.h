#pragma once

#include <algorithm>
#include <cctype>
#include <cstdint>
#include <iostream>
#include <sstream>
#include <string>

#include "absl/strings/numbers.h"
#include "absl/strings/str_cat.h"
#include "absl/strings/str_format.h"
#include "base/strings/utf_string_conversion_utils.h"
#include "re2/re2.h"

#ifdef UG_RTB_UTIL_TEST
// 简化的日志宏，用于测试环境
class TestLogger {
 public:
  explicit TestLogger(const char *level) {
    buffer_ << "[" << level << "] ";
  }
  ~TestLogger() {
    std::cerr << buffer_.str() << std::endl;
  }
  template <typename T>
  TestLogger &operator<<(const T &msg) {
    buffer_ << msg;
    return *this;
  }

 private:
  std::ostringstream buffer_;
};
#define CL_LOG(level) TestLogger(#level)
#else
// 生产环境使用完整的日志系统
#include "dragon/src/util/logging_util.h"
#endif

namespace ks {
namespace platform {

enum class IDType : uint8_t { kMuid = 0, kDeviceId = 1, kUserId = 2, kGlobalId = 3 };

inline std::ostream &operator<<(std::ostream &os, const IDType &type) {
  switch (type) {
    case IDType::kMuid:
      return os << "MUID(0)";
    case IDType::kDeviceId:
      return os << "DeviceId(1)";
    case IDType::kUserId:
      return os << "UserId(2)";
    case IDType::kGlobalId:
      return os << "GlobalId(3)";
    default:
      return os << "Unknown(" << static_cast<int>(type) << ")";
  }
}

inline constexpr char kTypeSignDefault = '0';
inline constexpr char kTypeSignError = '1';
inline constexpr char kTypeSignMuid = '2';
inline constexpr char kTypeSignDidAndroid = '3';
inline constexpr char kTypeSignDidIos = '4';
inline constexpr char kTypeSignUid = '5';
inline constexpr char kTypeSignGid = '6';

inline constexpr char kCharMin = '\0';
inline constexpr char kCharUpperA = 'A';
inline constexpr char kCharUpperF = 'F';

inline constexpr char kMuidFormat[] = "[0-9a-fA-F]{32}";
inline constexpr char kAndroidFormat[] = "ANDROID_[0-9a-fA-F]{16}";
inline constexpr char kIosFormat[] = "[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}";
inline constexpr char kSplitString[] = "-";
inline constexpr char kAndroidPrefix[] = "ANDROID_";
inline constexpr size_t kAndroidPrefixLength = 8;

class IdCompressionUtil {
 public:
  // 根据 ID 类型获取对应的类型标识符
  static char GetSignForIDType(IDType type, const std::string &original_id) {
    switch (type) {
      // std::regex 性能较差, 使用 google/re2. benchmark: [https://lh3lh3.users.sourceforge.net/reb.shtml,
      // https://github.com/HFTrader/regex-performance, https://github.com/mariomka/regex-benchmark]
      case IDType::kMuid:
        return RE2::FullMatch(original_id, kMuidFormat) ? kTypeSignMuid : kTypeSignError;

      case IDType::kUserId:
        return kTypeSignUid;

      case IDType::kGlobalId:
        return kTypeSignGid;

      case IDType::kDeviceId:
        if (RE2::FullMatch(original_id, kAndroidFormat)) {
          return kTypeSignDidAndroid;
        } else if (RE2::FullMatch(original_id, kIosFormat)) {
          return kTypeSignDidIos;
        }
        return kTypeSignError;

      default:
        return kTypeSignError;
    }
  }

  // 验证标识符与 ID 类型的匹配性
  static bool IsValidSignForType(char sign, IDType type) {
    switch (type) {
      case IDType::kMuid:
        return sign == kTypeSignMuid;
      case IDType::kUserId:
        return sign == kTypeSignUid;
      case IDType::kGlobalId:
        return sign == kTypeSignGid;
      case IDType::kDeviceId:
        return sign == kTypeSignDidAndroid || sign == kTypeSignDidIos;
      default:
        return false;
    }
  }

  // 将数字部分的 16 进制表示转化为 256 进制表示，结合 ID 类型，返回压缩优化后的 id
  static std::string CompressTypedIDToBase256(const std::string &original_id, IDType type) {
    if (original_id.empty()) {
      CL_LOG(WARNING) << "Empty original_id provided for compression";
      return "";
    }

    char sign = GetSignForIDType(type, original_id);
    if (sign == kTypeSignError) {
      CL_LOG(ERROR) << "Invalid format for IDType " << type << ": " << original_id;
      return kTypeSignError + original_id;
    }

    // 根据 ID 类型处理数字部分
    std::string number_part;
    if (sign == kTypeSignDidAndroid) {
      // Android: 移除"ANDROID_"前缀
      number_part = original_id.substr(kAndroidPrefixLength);
    } else if (sign == kTypeSignDidIos) {
      // iOS: 移除连字符并转为小写
      number_part = original_id;
      RE2::GlobalReplace(&number_part, kSplitString, "");
      number_part = ToLowerCase(number_part);
    } else {
      // MUID/UID/GID: 直接使用原始 ID
      number_part = original_id;
    }

    // sign + 压缩数字部分
    return absl::StrFormat("%c%s", sign, CompressHexToBase256(number_part));
  }

  // CompressTypedIDToBase256 的逆操作，256 进制转换为 16 进制，得到原始的 id
  static std::string DecompressBase256ToTypedID(const std::string &system256_str, IDType type) {
    if (system256_str.empty()) {
      CL_LOG(ERROR) << "Empty system256_str provided for decompression";
      return "";
    }

    char sign = system256_str[0];
    if (sign == kTypeSignError) {
      return system256_str.substr(1);
    }

    if (!IsValidSignForType(sign, type)) {
      CL_LOG(ERROR) << "Type and sign mismatch, expected type: " << type << ", actual sign: " << sign;
      return "";
    }

    std::string number_part = DecompressBase256ToHex(system256_str.substr(1));

    // 根据签名字符还原原始格式
    if (sign == kTypeSignMuid || sign == kTypeSignUid || sign == kTypeSignGid) {
      return number_part;
    } else if (sign == kTypeSignDidAndroid) {
      return absl::StrCat(kAndroidPrefix, number_part);
    } else if (sign == kTypeSignDidIos) {
      // IOS: 转大写并添加连字符 (8-4-4-4-12 格式)
      std::string upper_number_part = ToUpperCase(number_part);
      absl::string_view sv(upper_number_part);
      return absl::StrCat(sv.substr(0, 8), "-", sv.substr(8, 4), "-", sv.substr(12, 4), "-", sv.substr(16, 4),
                          "-", sv.substr(20));
    } else {
      CL_LOG(ERROR) << "Unknown sign for restoration: " << sign;
      return "";
    }
  }

  // 算法对齐 idmapping java sdk：16 进制字符串压缩存储，保留大小写信息
  //
  // === 背景 ===
  // JDK8: String 内部使用 UTF-16 char 数组，每个 char 占 2 字节
  // JDK9+: String 优化为 byte 数组+编码标志，Latin-1 字符 1 字节，其他 UTF-16 字符 2 字节
  // ref: https://openjdk.org/jeps/254, https://docs.oracle.com/javase/8/docs/api/java/lang/Character.html
  //
  // === Redis Key 生成流程 ===
  // 1. 压缩：两个 16 进制字符压缩为一个字符
  //        === 压缩算法示例 ===
  //          输入：'A'(大写，值 10) + 'f'(小写，值 15)
  //          Java 压缩：[大小写位:10][A 低 4 位:1010][f 低 4 位:1111] => 0x2AF
  // 2. UTF-8 编码：压缩字符串转字节数组作为 redis key
  static std::string CompressHexToBase256(const std::string &hex_str) {
    if (hex_str.empty()) {
      CL_LOG(WARNING) << "Empty hex string provided for compression";
      return "";
    }

    std::string result;
    result.reserve(hex_str.length());  // 非精确

    size_t index;
    for (index = 1; index < hex_str.length(); index += 2) {
      unsigned char part1 = hex_str[index - 1];
      unsigned char part2 = hex_str[index];

      // 验证字符是否为有效的十六进制字符
      int val1 = HexCharToInt(part1);
      int val2 = HexCharToInt(part2);
      if (val1 == -1 || val2 == -1) {
        CL_LOG(ERROR) << "Invalid hex character: " << part1 << part2;
        return "";
      }

      // 大小写标志：00=都小写/数字，01=第 2 个大写，10=第 1 个大写，11=都大写
      int flag = 2 * IsUpperHexAlpha(part1) + IsUpperHexAlpha(part2);

      // 对齐 Java 的码点计算，codepoint 占用两字节：
      // 高位字节低两位存储 flag，低位字节存储 val1 及 val2
      int codepoint = ((val1 + (flag << 4)) << 4) + val2;

      // 将计算出的码点按 UTF-8 编码
      AppendUtf8(&result, codepoint);
    }

    if (index == hex_str.length()) {
      // NOTICE: 最后一个奇数字符特殊处理(UID 可能为奇数长度, java 算法硬编码 flag 设置成 1 存在风险)
      // 对齐 sdk 逻辑：加一个 0 结尾且大写标签的校验位
      // flag = b'0000,0001'，two_number_parts 为 'xxxx 0000'，xxxx 为最后一个字符的数值
      CL_LOG(ERROR) << "Invalid hex string length: " << hex_str.length();
      int last_number = HexCharToInt(hex_str[index - 1]);
      int flag = 1;
      int codepoint = ((last_number + (flag << 4)) << 4);
      AppendUtf8(&result, codepoint);
    }
    return result;
  }

  // CompressHexToBase256 的逆运算，解码出原 16 进制字符串
  // src 长度预期是偶数，
  // 2 个一组，第一个字符为大小写信息；第二个字符高 4 位为原第一个字符数值，低 4 位为原第二个字符数值
  static std::string DecompressBase256ToHex(const std::string &src) {
    if (src.empty()) {
      return "";
    }

    std::string result;
    result.reserve(src.length());  // 粗略预估

    const char *data = src.c_str();
    int32_t src_len = src.length();
    int32_t i = 0;
    for (; i < src_len; ++i) {
      uint32_t codepoint;
      // i 在 ReadUnicodeCharacter 内部会被更新，指向最后一个字符末尾
      if (!base::ReadUnicodeCharacter(data, src_len, &i, &codepoint)) {
        CL_LOG(ERROR) << "Invalid UTF-8 sequence at position: " << i;
        return "";
      }

      // 处理 Java 为奇数长度字符串生成的特殊校验位
      // 校验位特征：低 4 位为 0，并且 flag=1
      if (i == src_len - 1 && (codepoint & 0x0F) == 0) {
        int temp = codepoint >> 4;
        int flag = (temp >> 4) & 0x03;
        if (flag == 1) {
          int val1 = temp & 0x0F;
          result += IntToHexStr(val1, false);
          break;
        }
      }

      // 码点解析：flag(高 2 位) + val1(中 4 位) + val2(低 4 位)
      int val2 = codepoint & 0x0F;
      int temp = codepoint >> 4;
      int val1 = temp & 0x0F;
      int flag = (temp >> 4) & 0x03;  // flag 占 2bits，always <= 3

      bool is_part1_upper = (flag & 0b10) != 0;
      bool is_part2_upper = (flag & 0b01) != 0;

      result += IntToHexStr(val1, is_part1_upper);
      result += IntToHexStr(val2, is_part2_upper);
    }

    return result;
  }

  static std::string ToLowerCase(const std::string &str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
  }

  static std::string ToUpperCase(const std::string &str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::toupper);
    return result;
  }

  static std::string IntToHexStr(int num, bool is_to_upper) {
    const unsigned int unum = static_cast<unsigned int>(num);
    return is_to_upper ? absl::StrFormat("%X", unum) : absl::StrFormat("%x", unum);
  }

  static int HexCharToInt(unsigned char c) {
    if (c >= '0' && c <= '9') {
      return c - '0';
    } else if (c >= 'a' && c <= 'f') {
      return c - 'a' + 10;
    } else if (c >= 'A' && c <= 'F') {
      return c - 'A' + 10;
    }
    CL_LOG(ERROR) << "Invalid hex character: '" << c << "' (ASCII: " << static_cast<int>(c) << ")";
    return -1;
  }

  static bool IsUpperHexAlpha(unsigned char c) {
    return c >= kCharUpperA && c <= kCharUpperF;
  }

  static void AppendUtf8(std::string *str, int codepoint) {
    if (!base::IsValidCodepoint(codepoint)) {
      CL_LOG(ERROR) << "Invalid codepoint: 0x" << std::hex << codepoint;
      return;
    }
    base::WriteUnicodeCharacter(codepoint, str);
    return;
  }
};

}  // namespace platform
}  // namespace ks
