#pragma once

#include <string>
#include <vector>
#include <set>
#include <unordered_set>
#include <memory>
#include <unordered_map>

#include "kconf/kconf.h"
#include "kenv/kenv.h"
#include "ks/serving_util/infra_redis_client.h"
#include "dragon/src/processor/ext/ks_mix_rank/util/mix_rank_util.h"
#include "dragon/src/processor/ext/ks_mix_rank/base/hot_mix_rank_context.h"
#include "dragon/src/processor/ext/ks_mix_rank/base/mix_rank_base_processor.h"
#include "dragon/src/processor/ext/ks_mix_rank/hot_mix_ranker/hot_mix_rank_item_score.h"

namespace ks {
namespace platform {

class HotMixLiveScoreEnricher : public BaseMixRankProcessor {
 public:
  HotMixLiveScoreEnricher() {}

  /**
   * @brief 返回 kRecoLivestream ，表示处理所有类型的结果
   *
   * @return kRecoLivestream
   */
  std::vector<MixRecoResult::ResultCase> ProcessItemType() {
    return {MixRecoResult::ResultCase::kRecoLivestream};
  }

  // 混排处理 Live
  void MixRank(AddibleRecoContextInterface *context);
  bool GetBadInfoFromMarkcode(AddibleRecoContextInterface *context, const std::string& pid);
  void CalcScore(AddibleRecoContextInterface *context, std::vector<MixRankResult>* items,
    std::unordered_map<uint64, MixRankResult>* pid2result_map, const std::string& case_name);

 private:
  bool InitProcessor() override {
    live_bad_candidates_attr_ = config()->GetString("live_bad_candidates_attr", "");
    std::string redis_conf_str = "reco.author.markcode_filter_redis";
    std::shared_ptr<std::string> default_str = std::make_shared<std::string>("");
    std::string redis_address = *(ks::infra::KConf().Get(redis_conf_str, default_str))->Get();
    // redis_client 初始化
    if (redis_client_ == nullptr) {
      auto *client_mgr = ks::serving_util::InfraReidsClient::Singleton();
      if (client_mgr == nullptr) {
        LOG(WARNING) << redis_address << " : get redis live mgr error";
        return true;
      }
      redis_client_ = client_mgr->GetClientKccFromKconf(redis_address);
      if (!redis_client_) {
        LOG(WARNING) << redis_address << " : get redis live client error";
      }
    }
    return true;
  }

 private:
  ks::infra::RedisClient* redis_client_ = nullptr;
  std::string live_bad_candidates_attr_;
  std::vector<std::string> markcodes_;

 private:
  DISALLOW_COPY_AND_ASSIGN(HotMixLiveScoreEnricher);
};

}  // namespace platform
}  // namespace ks

