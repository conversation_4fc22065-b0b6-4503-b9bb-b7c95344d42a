#pragma once

#include <string>
#include <vector>
#include <set>
#include <unordered_set>
#include <memory>
#include <unordered_map>

#include "kconf/kconf.h"
#include "kenv/kenv.h"
#include "ks/serving_util/infra_redis_client.h"
#include "dragon/src/processor/ext/ks_mix_rank/util/mix_rank_util.h"
#include "dragon/src/processor/ext/ks_mix_rank/base/hot_mix_rank_context.h"
#include "dragon/src/processor/ext/ks_mix_rank/base/mix_rank_base_processor.h"
#include "dragon/src/processor/ext/ks_mix_rank/hot_mix_ranker/hot_mix_rank_item_score.h"

namespace ks {
namespace platform {

class HotMixExploreAdScoreEnricher : public BaseMixRankProcessor {
 public:
  HotMixExploreAdScoreEnricher() {}

  /**
   * @brief 返回 kAdDsp ，表示处理所有类型的结果
   *
   * @return kAdDsp
   */
  std::vector<MixRecoResult::ResultCase> ProcessItemType() {
    return {MixRecoResult::ResultCase::kAdDsp};
  }

  // 混排处理 Live
  void MixRank(AddibleRecoContextInterface *context);
  void CalcScore(AddibleRecoContextInterface *context, std::vector<MixRankResult>* items,
    std::unordered_map<uint64, MixRankResult>* pid2result_map, const std::string& case_name);

 private:
  bool InitProcessor() override {
    std::vector<double> default_cpm_quantiles = {2.58021, 3.64116, 4.48397, 5.25707, 6.31571,
                                            7.86713, 10.3708, 15.0253, 25.8677, 100};
    cpm_quantiles_kconf_ = ks::infra::KConf().GetList("reco.picture.hotMixCpmQuantiles",
        std::make_shared<std::vector<double>>(default_cpm_quantiles));
    live_bad_candidates_attr_ = config()->GetString("live_bad_candidates_attr", "");
    return true;
  }

  void CalcScoreAndPerf(AddibleRecoContextInterface *context,
            HotMixRankContext *hot_mix_rank_context, MixRankResult *mix_rank_explore_ad,
            double boost_weight, double pctr_boost_weight, double pctr_weight, double eco_weight,
            double final_score_bias, double cpm_bonus_weight, const std::string& case_name,
            bool is_satisfied, double eco_bias, double pctr_bias,
            const std::string& low_active_ad_flag);

  std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<std::vector<double>>>> cpm_quantiles_kconf_;
  std::string live_bad_candidates_attr_;

  DISALLOW_COPY_AND_ASSIGN(HotMixExploreAdScoreEnricher);
};

}  // namespace platform
}  // namespace ks

