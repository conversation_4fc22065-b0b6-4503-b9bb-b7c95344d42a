#include <math.h>
#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <set>
#include <algorithm>
#include <sstream>
#include <utility>

#include "kconf/kconf.h"
#include "base/random/true_random.h"
#include "dragon/src/processor/ext/ks_mix_rank/util/mix_rank_util.h"
#include "dragon/src/processor/ext/ks_mix_rank/base/mix_rank_result.h"
#include "dragon/src/processor/ext/ks_mix_rank/base/hot_mix_rank_context.h"
#include "dragon/src/processor/ext/ks_mix_rank/hot_mix_ranker/hot_mix_live_score_enricher.h"

namespace ks {
namespace platform {

void HotMixLiveScoreEnricher::MixRank(AddibleRecoContextInterface *context) {
  MixRankContext &mix_ctx = MixCtx();
  const std::vector<MixRecoResult *> &reco_lives = mix_ctx.RecoLiveResults();
  if (reco_lives.size() < 1) {
    return;
  }

  // 最多插入上限
  int max_insert_num = GetIntProcessorParameter(context,
                  "reco_rpc_reco_live_max_insert_num", 1);
  if (max_insert_num < 1) {
    return;
  }
  // 获取 redis 中劣质内容集合，在解析过程中将其过滤掉
  auto plugin_bad_data_map =
   context->GetPtrCommonAttr<folly::F14FastMap<uint64, double>>(live_bad_candidates_attr_);
  int enable_reco_live_bad_filter = GetIntProcessorParameter(context,
          "explore_mix_enable_reco_live_bad_filter", 0);
  // markcode 慢脚过滤开关
  bool enable_markcode_manjiao = GetBoolProcessorParameter(context,
      "enable_explore_live_markcode_manjiao", false);
  if (enable_markcode_manjiao) {
    if (auto p = context->GetIntListCommonAttr("filter_markcode_manjiao_list")) {
      for (auto value : *p) {
        markcodes_.emplace_back(base::Int64ToString(value));
      }
    }
  }
  // 转成 mix_rank_result
  std::vector<MixRankResult> cands;
  cands.reserve(reco_lives.size());
  HotMixRankContext &hot_mix_rank_context = mix_ctx.GetHotMixRankContext();
  std::unordered_set<uint64> live_ids_set = hot_mix_rank_context.live_ids_set;
  for (auto* it : reco_lives) {
    auto photo_id = it->reco_livestream().livestream_id();
    auto author_id = it->reco_livestream().author_id();
    if (enable_reco_live_bad_filter) {
      if (plugin_bad_data_map && plugin_bad_data_map->find(author_id) != plugin_bad_data_map->end()) {
        base::perfutil::PerfUtilWrapper::CountLogStash(1, "reco.mix", "score.enricher",
        "live_bad_live_filter", "count");
        continue;
      }
    }
    // 从社区提供的数据中待删除的 pid 2lmmr_13124991056
    std::string liveid_key = "2lmmr_" + std::to_string(photo_id);
    if (enable_markcode_manjiao && GetBadInfoFromMarkcode(context, liveid_key)) {
      continue;
    }
    if (live_ids_set.count(photo_id) > 0) {
      continue;
    } else {
      live_ids_set.insert(photo_id);
    }
    // 从 MixRecoResult 当中把分数统一抽取出来
    std::unordered_map<std::string, float> xtr_infos;
    kuiba::PredictItem reco_pred;
    HotMixGetXtrInfo(it, &xtr_infos, MixRecoResult::ResultCase::kRecoLivestream,
        photo_id, author_id, &(hot_mix_rank_context.hetu_infos), &mix_ctx, &reco_pred);
    hot_mix_rank_context.pred_xtr_mp[photo_id] = reco_pred;
    hot_mix_rank_context.xtr_info_mp[photo_id] = xtr_infos;
    cands.emplace_back(it);
  }

  // ab 参数
  std::string pctr_score_name = GetStringProcessorParameter(context,
                  "reco_rpc_reco_live_pctr_score_name", "live_pctr");
  std::string eco_score_name = GetStringProcessorParameter(context,
                  "reco_rpc_reco_live_eco_score_name", "live_plvtr");
  double boost_weight = GetDoubleProcessorParameter(context, "reco_rpc_reco_live_boost_weight", 1.0);
  double pctr_boost_weight =
            GetDoubleProcessorParameter(context, "reco_rpc_reco_live_pctr_boost_weight", 1.0);
  double pctr_weight = GetDoubleProcessorParameter(context, "reco_rpc_reco_live_pctr_weight", 1.0);
  double pctr_bias = GetDoubleProcessorParameter(context, "reco_rpc_reco_live_pctr_bias", 1.0);
  double eco_weight = GetDoubleProcessorParameter(context, "reco_rpc_reco_live_eco_weight", 0.0);
  double eco_bias = GetDoubleProcessorParameter(context, "reco_rpc_reco_live_eco_bias", 0.0);
  bool is_ctr_need_cali = GetBoolProcessorParameter(context,
                            "reco_rpc_reco_live_ctr_need_calibration", false);
  double alpha = GetDoubleProcessorParameter(context, "reco_rpc_reco_live_ctr_neg_sampling_rate", 1.0);
  double final_score_bias = GetDoubleProcessorParameter(context,
                  "reco_rpc_reco_live_final_score_bias", 0.0);
  bool hot_mix_enable_open_zero_live_score = GetBoolProcessorParameter(context,
                  "hot_mix_enable_open_zero_live_score", false);
  bool hot_mix_enable_open_lowactive_live_score = GetBoolProcessorParameter(context,
                  "hot_mix_enable_open_lowactive_live_score", false);
  bool enable_la_live_filter = GetBoolProcessorParameter(context,
                  "enable_la_live_filter", false);
  int lowactive_live_nth_page = GetIntProcessorParameter(context,
                  "lowactive_live_nth_page", 0);
  int zero_user_live_nth_page = GetIntProcessorParameter(context,
                  "zero_user_live_nth_page", 0);
  int lowactive_live_nth_fresh_type = GetIntProcessorParameter(context,
                  "lowactive_live_nth_fresh_type", 0);
  int zero_user_live_nth_fresh_type = GetIntProcessorParameter(context,
                  "zero_user_live_nth_fresh_type", 0);
  int la_live_nth_page = GetIntProcessorParameter(context,
                  "la_live_nth_page", 0);
  bool enable_low_live_filter = GetBoolProcessorParameter(context,
                  "enable_low_live_filter", false);
  int low_live_nth_page = GetIntProcessorParameter(context,
                  "low_live_nth_page", 0);
  bool enable_hot_mix_calc_score_opt = GetBoolProcessorParameter(context,
    "enable_hot_mix_calc_score_opt", false);
  bool is_debug = hot_mix_rank_context.is_debug;
  if (enable_hot_mix_calc_score_opt) {
    CalcScore(context, &cands, &hot_mix_rank_context.pid2result_map, std::string("living"));
  } else {
    for (auto& cand : cands) {
      auto photo_id = cand.ItemId();
      auto author_id = cand.AuthorId();
      auto xtr_infos = hot_mix_rank_context.xtr_info_mp[photo_id];
      // 抽取 pctr
      float pctr_score = 0.0;
      if (xtr_infos.count(pctr_score_name) > 0) {
        pctr_score = xtr_infos[pctr_score_name];
      }
      if (is_ctr_need_cali) {
        pctr_score = alpha * pctr_score / (1 - (1 - alpha) * pctr_score);
      }
      std::pair<int, double> score_pair =
                      hot_mix_rank_context.GetQuantileScore(hot_mix_rank_context.pctr_quantiles,
                      pctr_score * pctr_boost_weight);
      float quantile_pctr_score = score_pair.second;
      cand.SetPctrScore(quantile_pctr_score);

      // 抽取生态分
      float eco_score = 0.0;
      if (xtr_infos.count(eco_score_name) > 0) {
          eco_score = xtr_infos[eco_score_name];
      }
      cand.SetEcologyValue(eco_score);

      // 生成 final score
      float final_score = 0.0;
      if (hot_mix_rank_context.use_power_calc) {
        final_score = (pow(pctr_bias + quantile_pctr_score, pctr_weight) *
          pow(eco_bias + eco_score, eco_weight) + final_score_bias) * boost_weight;
      } else {
        final_score = (pctr_weight * quantile_pctr_score + eco_weight * eco_score
                          + final_score_bias) * boost_weight;
      }
      cand.SetFinalScore(final_score);
      cand.SetPctrWeight(pctr_weight);
      cand.SetEcoWeight(eco_weight);
      cand.SetEcoBias(final_score_bias);

      if (is_debug) {
        LOG(INFO) << "HotMixLiveScoreEnricher: "
            << " reulst_case=" << GetResultCaseName(cand) << ", "
            << " photo_id=" << photo_id << ", "
            << " author_id=" << author_id << ", "
            << " pctr_score=" << pctr_score << ", "
            << " eco_score=" << eco_score << ", "
            << " final_score=" << final_score << ", "
            << " neg_sample_rate=" << alpha << ", "
            << " quantile_pctr_score=" << quantile_pctr_score;
      }
    }
  }
  int inserted_count = 0;  // 记录已插入数量
  for (auto& cand : cands) {
    if (inserted_count < max_insert_num) {
      std::string low_active_live_flag = "0";
      bool is_la_user = hot_mix_rank_context.is_new_low_vv_user || hot_mix_rank_context.is_true_new_user;
      if (enable_la_live_filter && is_la_user && hot_mix_rank_context.nth_page < la_live_nth_page) {
        low_active_live_flag = "1";
      }
      if ((hot_mix_enable_open_lowactive_live_score
          && hot_mix_rank_context.is_low_active_cold_start
          && ((hot_mix_rank_context.nth_page < lowactive_live_nth_page)
              || (hot_mix_rank_context.nth_fresh_type < lowactive_live_nth_fresh_type)))
          || (enable_low_live_filter
            && hot_mix_rank_context.is_low_active_user
            && hot_mix_rank_context.nth_page < low_live_nth_page)
          || (hot_mix_enable_open_zero_live_score
          && hot_mix_rank_context.is_zero_cold_start
          && ((hot_mix_rank_context.nth_page < zero_user_live_nth_page)
              || (hot_mix_rank_context.nth_fresh_type < zero_user_live_nth_fresh_type)))) {
        low_active_live_flag = "1";
      }
      if (low_active_live_flag != "1") {
        hot_mix_rank_context.AddRecoLiveResults(cand);
        hot_mix_rank_context.AddFinalResults(cand);
      }
    }
    inserted_count++;
    hot_mix_rank_context.AddFullMixResults(cand);
  }
}

bool HotMixLiveScoreEnricher::GetBadInfoFromMarkcode(AddibleRecoContextInterface *context,
    const std::string& redis_key) {
  if (redis_client_ == nullptr) {
    return false;
  }
  std::set<std::string> res;
  bool is_valid_val = GetSMembersFromRedis(redis_client_, redis_key, &res, 50);
  if (!is_valid_val || res.empty()) {
    return false;
  }
  //  kconf  的内容
  if (markcodes_.empty()) {
    return false;
  }
  for (const auto& code : res) {
    auto it = std::find(markcodes_.begin(), markcodes_.end(), code);
    if (it != markcodes_.end()) {
      return true;
    }
  }
  return false;
}

void HotMixLiveScoreEnricher::CalcScore(AddibleRecoContextInterface *context,
    std::vector<MixRankResult>* items, std::unordered_map<uint64, MixRankResult>* pid2result_map,
    const std::string& case_name) {
  bool enable_experience = GetBoolProcessorParameter(context,
        "hot_mix_enable_experience_" + case_name, false);
  bool enable_eco = GetBoolProcessorParameter(context,
        "hot_mix_enable_eco_" + case_name, false);
  int experience_score_type = GetIntProcessorParameter(context,
        "hot_mix_experience_score_type_" + case_name, 0);
  int eco_score_type = GetIntProcessorParameter(context,
        "hot_mix_eco_score_type_" + case_name, 0);
  int final_score_type = GetIntProcessorParameter(context,
        "hot_mix_final_score_type_" + case_name, 0);
  double final_adjust_wt = GetDoubleProcessorParameter(context,
        "hot_mix_final_adjust_wt_" + case_name, 1.0);
  double eco_wt = GetDoubleProcessorParameter(context,
        "hot_mix_eco_wt_" + case_name, 1.0);
  double experience_wt = GetDoubleProcessorParameter(context,
        "hot_mix_experience_wt_" + case_name, 1.0);
  std::string eco_params_str = GetStringProcessorParameter(context,
        "hot_mix_eco_params_" + case_name, "");
  std::string experience_params_str = GetStringProcessorParameter(context,
        "hot_mix_experience_params_" + case_name, "");
  bool enable_cali = GetBoolProcessorParameter(context,
        "enable_hot_mix_pxtr_cali", false);
  folly::F14FastMap<std::string, std::pair<double, double> > eco_queue_weights;
  folly::F14FastMap<std::string, std::pair<double, double> > experience_queue_weights;
  if (enable_eco) {
    ParseWeightParams(eco_params_str, &eco_queue_weights);
  }
  if (enable_experience) {
    ParseWeightParams(experience_params_str, &experience_queue_weights);
  }
  for (int i = 0; i < (int)items->size(); ++i) {
    double final_score = 0.0;
    double eco_score = 0.0;
    double experience_score = 0.0;
    MixRankResult& item = items->at(i);
    auto iter = pid2result_map->find(item.ItemId());
    if (iter != pid2result_map->end()) {
      CalcItemFinalScore(&(iter->second), &final_score, &experience_score, &eco_score,
      enable_experience, enable_eco, experience_score_type, eco_score_type,
      final_score_type, final_adjust_wt, experience_wt, eco_wt,
      experience_queue_weights, eco_queue_weights, enable_cali);
    }
    item.SetFinalScore(final_score);
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, HotMixLiveScoreEnricher, HotMixLiveScoreEnricher);

}  // namespace platform
}  // namespace ks

