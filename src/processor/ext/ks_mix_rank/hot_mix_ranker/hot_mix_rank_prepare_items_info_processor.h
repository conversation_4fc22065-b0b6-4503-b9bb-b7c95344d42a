#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <algorithm>
#include <utility>
#include "dragon/src/processor/ext/ks_mix_rank/util/mix_rank_util.h"
#include "dragon/src/processor/ext/ks_mix_rank/base/mix_rank_base_processor.h"

namespace ks {
namespace platform {

class HotMixRankPrepareItemsInfoProcessor : public BaseMixRankProcessor {
 public:
  HotMixRankPrepareItemsInfoProcessor() {}

  std::vector<MixRecoResult::ResultCase> ProcessItemType() {
    return {MixRecoResult::ResultCase::RESULT_NOT_SET};
  }

  void MixRank(AddibleRecoContextInterface *context);
  void FillResult(std::unordered_map<uint64, MixRankResult>* pid2result_map,
    const std::vector<MixRecoResult *> &result_vec, int result_type);
  void PxtrCali(AddibleRecoContextInterface *context,
    std::unordered_map<uint64, MixRankResult>* pid2result_map);
  void InitPrePostMap(AddibleRecoContextInterface *context,
    folly::F14FastMap<std::string, std::pair<std::vector<double>, std::vector<double>>>* score_map);
  bool CalcPrePostScore(const std::string &key,
    const double pre_score, double* post_score,
    const folly::F14FastMap<std::string, std::pair<std::vector<double>, std::vector<double>>>& score_map);
  double CalFittingValue(double x1, double y1, double x2, double y2, double x);

 private:
  bool InitProcessor() override {
    cali_data_ptr_attr_ = config()->GetString("cali_data_ptr_attr", "");
    return true;
  }
  std::string cali_data_ptr_attr_;
  DISALLOW_COPY_AND_ASSIGN(HotMixRankPrepareItemsInfoProcessor);
};

}  // namespace platform
}  // namespace ks
