#pragma once
#include <set>
#include <string>
#include <utility>
#include <unordered_map>
#include <unordered_set>
#include <vector>
#include <map>
#include <memory>

#include "dragon/src/processor/ext/ks_mix_rank/util/mix_rank_util.h"
#include "dragon/src/processor/ext/ks_mix_rank/base/hot_mix_rank_context.h"
#include "dragon/src/processor/ext/ks_mix_rank/base/mix_rank_base_processor.h"

namespace ks {
namespace platform {

class HotMixAuctionSeqEvaluator : public BaseMixRankProcessor {
 public:
  HotMixAuctionSeqEvaluator() {}
  /**
   * @brief 返回 RESULT_NOT_SET ，表示处理所有类型的结果
   *
   * @return RESULT_NOT_SET
   */
  std::vector<MixRecoResult::ResultCase> ProcessItemType() {
    return {MixRecoResult::ResultCase::RESULT_NOT_SET};
  }

  void MixRank(AddibleRecoContextInterface *context);

 private:
  bool InitProcessor() override {
    return true;
  }

 private:
  void Init(AddibleRecoContextInterface *context);
  void CalSeqValue(AddibleRecoContextInterface *context);
  void GenFinalMixRlt(AddibleRecoContextInterface *context);
  void CalDiscountedPhotoValue(AddibleRecoContextInterface *context);
  void CalDiscountedNormalLiveValue(AddibleRecoContextInterface *context);
  void CalDiscountedMerchantLiveValue(AddibleRecoContextInterface *context);
  void CalDiscountedDspValue(AddibleRecoContextInterface *context);
  void CalDiscountedTopFansValue(AddibleRecoContextInterface *context);
  void CalDiscountedAdLiveValue(AddibleRecoContextInterface *context);
  void GenSeqValue(AddibleRecoContextInterface *context);
  void PrintPerf();

 private:
  std::vector<HotCandidateSequence> mix_list_retrieval_;
  std::vector<MixRankResult> origin_normal_live_list_;
  std::vector<MixRankResult> origin_merchant_live_list_;
  std::vector<MixRankResult> origin_dsp_list_;
  std::vector<MixRankResult> origin_top_fans_list_;
  std::vector<MixRankResult> origin_ad_live_list_;
  bool is_debug_ = false;
  int ad_pos_no_0_limit_ = 0;

  DISALLOW_COPY_AND_ASSIGN(HotMixAuctionSeqEvaluator);
};

}  // namespace platform
}  // namespace ks
