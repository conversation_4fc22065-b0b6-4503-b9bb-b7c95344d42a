#pragma once

#include <string>
#include <vector>
#include <set>
#include <unordered_set>
#include <memory>
#include <unordered_map>

#include "kconf/kconf.h"
#include "kenv/kenv.h"
#include "ks/serving_util/infra_redis_client.h"
#include "dragon/src/processor/ext/ks_mix_rank/util/mix_rank_util.h"
#include "dragon/src/processor/ext/ks_mix_rank/base/hot_mix_rank_context.h"
#include "dragon/src/processor/ext/ks_mix_rank/base/mix_rank_base_processor.h"
#include "dragon/src/processor/ext/ks_mix_rank/hot_mix_ranker/hot_mix_rank_item_score.h"

namespace ks {
namespace platform {

class HotMixRecoPhotoScoreEnricher : public BaseMixRankProcessor {
 public:
  HotMixRecoPhotoScoreEnricher() {}

  /**
   * @brief 返回 kRecoPhoto ，表示处理所有类型的结果
   *
   * @return kRecoPhoto
   */
  std::vector<MixRecoResult::ResultCase> ProcessItemType() {
    return {MixRecoResult::ResultCase::kRecoPhoto};
  }

  // 混排处理 Live
  void MixRank(AddibleRecoContextInterface *context);
  void CalcScore(AddibleRecoContextInterface *context, std::vector<MixRankResult>* items,
    std::unordered_map<uint64, MixRankResult>* pid2result_map, const std::string& case_name);

 private:
  void Init(AddibleRecoContextInterface *context);
  void BoostHighEffPhoto(MixRankResult* reco_photo_result,
          HotMixRankContext* hot_context, uint64 photo_id, int idx);
  void UserActiveAdjustScore(AddibleRecoContextInterface *context,
          MixRankResult* reco_photo_result, HotMixRankContext* hot_context);

 private:
  bool enable_high_eff_boost_ = false;
  int high_eff_boost_idx_limit_ = 1;
  double high_eff_boost_threshold_ = 1.0;
  double high_eff_boost_param_ = 1.0;
  DISALLOW_COPY_AND_ASSIGN(HotMixRecoPhotoScoreEnricher);
};

}  // namespace platform
}  // namespace ks

