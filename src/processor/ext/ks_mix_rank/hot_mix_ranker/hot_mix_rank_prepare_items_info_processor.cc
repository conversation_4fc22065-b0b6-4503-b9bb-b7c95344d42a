#include <utility>

#include "dragon/src/processor/ext/ks_mix_rank/base/mix_rank_result.h"
#include "dragon/src/processor/ext/ks_mix_rank/hot_mix_ranker/hot_mix_rank_prepare_items_info_processor.h"

namespace ks {
namespace platform {

void HotMixRankPrepareItemsInfoProcessor::MixRank(AddibleRecoContextInterface *context) {
  MixRankContext &mix_ctx = MixCtx();

  auto& pid2result_map = mix_ctx.GetHotMixRankContext().pid2result_map;
  pid2result_map.clear();

  FillResult(&pid2result_map, mix_ctx.RecoPhotoResults(), 0);
  FillResult(&pid2result_map, mix_ctx.AdDspResults(), 1);
  FillResult(&pid2result_map, mix_ctx.AdFansTopResults(), 1);
  FillResult(&pid2result_map, mix_ctx.RecoLiveResults(), 2);
  FillResult(&pid2result_map, mix_ctx.MerchantExploreLiveResults(), 2);

  if (GetBoolProcessorParameter(context, "enable_hot_mix_pxtr_cali", false)) {
    PxtrCali(context, &pid2result_map);
  }
}

void HotMixRankPrepareItemsInfoProcessor::FillResult(
  std::unordered_map<uint64, MixRankResult>* pid2result_map,
  const std::vector<MixRecoResult *> &result_vec, int result_type) {
  for (MixRecoResult *ele : result_vec) {
    MixRankResult result = MixRankResult(ele);
    uint64 pid = result.ItemId();
    if (pid2result_map->count(pid) > 0) {
      LOG_EVERY_N(ERROR, 1000000) << "Got duplicate pid: " << pid;
      continue;
    }
    auto &hot_pxtr_info = result.GetMutableHotPxtrInfo();
    if (result_type == 0) {
      if (result.GetPtr()) {
        mix::kuaishou::newsmodel::RankResult reco_photo_rank_results =
          GetRecoPhotoRankResult(result.GetPtr()->reco_photo());
        hot_pxtr_info.SetHotRecoPhoto(reco_photo_rank_results);
      }
    } else if (result_type == 1) {
      auto ad_mixed_info = MixRecoUtil::GetAdMixedInfo(ele);
      hot_pxtr_info.SetHotAdPhoto(ad_mixed_info);
    } else if (result_type == 2) {
      ::mix::kuiba::PredictItem pred_item = ele->predict_item();
      hot_pxtr_info.SetHotLive(pred_item);
    }
    pid2result_map->insert(std::make_pair(pid, result));
  }
}

void HotMixRankPrepareItemsInfoProcessor::PxtrCali(AddibleRecoContextInterface *context,
    std::unordered_map<uint64, MixRankResult>* pid2result_map) {
  folly::F14FastMap<std::string, std::pair<std::vector<double>, std::vector<double>>> score_map;
  InitPrePostMap(context, &score_map);
  for (auto& kv : *pid2result_map) {
    auto& item = kv.second;
    auto type = GetItemNameString(GetPermItemType(item));
    auto& pxtr_info = item.GetMutableHotPxtrInfo().GetMutablePxtrInfo();
    for (auto& info : pxtr_info) {
      auto xtr_name = info.first;
      auto pre_score = info.second.first;
      auto post_score = pre_score;
      auto key = "explore_" + type + '_' + xtr_name;
      CalcPrePostScore(key, pre_score, &post_score, score_map);
      info.second.second = post_score;
    }
  }
}

void HotMixRankPrepareItemsInfoProcessor::InitPrePostMap(
    AddibleRecoContextInterface *context,
    folly::F14FastMap<std::string, std::pair<std::vector<double>, std::vector<double>>>* score_map) {
  auto map_ptr = context->GetPtrCommonAttr<folly::F14FastMap<std::string,
    std::vector<double>>>(cali_data_ptr_attr_);
  if (!map_ptr) {
    return;
  }
  for (auto& kv : *map_ptr) {
    auto key = kv.first;
    auto data = kv.second;
    if (data.size() % 2 != 0) {
      continue;
    }
    int sz = data.size() / 2;
    std::vector<double> pre(data.begin(), data.begin() + sz);
    std::vector<double> post(data.begin() + sz, data.end());
    std::sort(pre.begin(), pre.end());
    std::sort(post.begin(), post.end());
    score_map->emplace(key, std::make_pair(pre, post));
  }
}

bool HotMixRankPrepareItemsInfoProcessor::CalcPrePostScore(const std::string &key,
    const double pre_score, double* post_score,
    const folly::F14FastMap<std::string, std::pair<std::vector<double>, std::vector<double>>>& score_map) {
  if (score_map.count(key) == 0) {
    return false;
  }
  auto map_pair = score_map.at(key);
  const auto& pre_list = map_pair.first;
  const auto& post_list = map_pair.second;
  if (pre_list.size() <= 2 || pre_list.size() != post_list.size() || pre_list[0] < 0 || post_list[0] < 0) {
    return false;
  }
  double x1 = 0.0, y1 = 0.0, x2 = 0.0, y2 = 0.0;
  auto iter = std::lower_bound(pre_list.begin(), pre_list.end(), pre_score);
  if (iter == pre_list.end()) {
    x2 = pre_list[pre_list.size() - 1];
    y2 = post_list[post_list.size() - 1];
    x1 = pre_list[pre_list.size() - 2];
    y1 = post_list[post_list.size() - 2];
    *post_score = std::max(CalFittingValue(x1, y1, x2, y2, pre_score), 0.0);
    return true;
  } else {
    if (iter == pre_list.begin()) {
      x2 = pre_list[0];
      y2 = post_list[0];
    } else {
      int idx = iter - pre_list.begin();
      x2 = pre_list[idx];
      y2 = post_list[idx];
      x1 = pre_list[idx - 1];
      y1 = post_list[idx - 1];
    }
    *post_score = std::max(CalFittingValue(x1, y1, x2, y2, pre_score), 0.0);
    return true;
  }
  return false;
}

double HotMixRankPrepareItemsInfoProcessor::CalFittingValue(double x1, double y1,
    double x2, double y2, double x) {
  if (y2 <= y1) return y1;
  if ((x2 - x1) == -1e-20) return y1;
  double weight = (y2 - y1) / (x2 - x1 + 1e-20);
  double bias = y1 - weight * x1;
  double fitting_val = x * weight + bias;
  return fitting_val;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, HotMixRankPrepareItemsInfoProcessor, HotMixRankPrepareItemsInfoProcessor);

}  // namespace platform
}  // namespace ks
