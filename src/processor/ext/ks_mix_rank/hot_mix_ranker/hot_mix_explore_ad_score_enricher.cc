#include <math.h>
#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <set>
#include <algorithm>
#include <sstream>
#include <utility>

#include "kconf/kconf.h"
#include "base/random/true_random.h"
#include "dragon/src/processor/ext/ks_mix_rank/util/mix_rank_util.h"
#include "dragon/src/processor/ext/ks_mix_rank/base/mix_rank_result.h"
#include "dragon/src/processor/ext/ks_mix_rank/base/hot_mix_rank_context.h"
#include "dragon/src/processor/ext/ks_mix_rank/hot_mix_ranker/hot_mix_explore_ad_score_enricher.h"

namespace ks {
namespace platform {

void HotMixExploreAdScoreEnricher::MixRank(AddibleRecoContextInterface *context) {
  MixRankContext &mix_ctx = MixCtx();
  HotMixRankContext &hot_mix_rank_context = mix_ctx.GetHotMixRankContext();
  bool hot_mix_enable_explore_ad = GetBoolProcessorParameter(context,
                  "reco_rpc_hot_mix_enable_explore_ad", false);
  bool hot_mix_enable_open_zero_ad_score = GetBoolProcessorParameter(context,
                  "hot_mix_enable_open_zero_ad_score", false);
  bool hot_mix_enable_open_lowactive_ad_score = GetBoolProcessorParameter(context,
                  "hot_mix_enable_open_lowactive_ad_score", false);
  bool enable_la_ad_filter = GetBoolProcessorParameter(context,
                  "enable_la_ad_filter", false);
  bool enable_low_ad_filter = GetBoolProcessorParameter(context,
                  "enable_low_ad_filter", false);
  int lowactive_ad_nth_page = GetIntProcessorParameter(context,
                  "lowactive_ad_nth_page", 0);
  int zero_user_ad_nth_page = GetIntProcessorParameter(context,
                  "zero_user_ad_nth_page", 0);
  int lowactive_ad_nth_fresh_type = GetIntProcessorParameter(context,
                  "lowactive_ad_nth_fresh_type", 0);
  int zero_user_ad_nth_fresh_type = GetIntProcessorParameter(context,
                  "zero_user_ad_nth_fresh_type", 0);
  int la_ad_nth_page = GetIntProcessorParameter(context,
                  "la_ad_nth_page", 0);

  if (!hot_mix_rank_context.use_auction_seq_mix && !hot_mix_enable_explore_ad) {
    return;
  }
    // 获取 redis 中劣质内容集合，在解析过程中将其过滤掉
  auto plugin_bad_data_map =
   context->GetPtrCommonAttr<folly::F14FastMap<uint64, double>>(live_bad_candidates_attr_);
  int enable_ad_live_bad_filter =
   GetIntProcessorParameter(context, "explore_mix_enable_ad_live_bad_filter", 0);
  int enable_filter_jiankang_hetu =
    GetIntProcessorParameter(context, "explore_mix_enable_filter_jiankang_hetu", 0);
  bool enable_fountain_top_fans =
    hot_mix_rank_context.enable_ad_fountain_top_fans_definition;
  const std::unordered_set<uint64> reco_ids_set = mix_ctx.RecoPhotoIdSets();

  int explore_mix_enable_ad_MachineAuditFail_filter = GetIntProcessorParameter(context,
                  "explore_mix_enable_ad_MachineAuditFail_filter", 0);
  int explore_mix_enable_ad_ManAuditGray_filter = GetIntProcessorParameter(context,
                  "explore_mix_enable_ad_ManAuditGray_filter", 0);
  int explore_mix_enable_ad_ManAuditPoor_filter = GetIntProcessorParameter(context,
                  "explore_mix_enable_ad_ManAuditPoor_filter", 0);
  auto hotMixMachineAuditFail_labels = context->GetIntListCommonAttr("hotMixMachineAuditFail_labels");
  auto hotMixManAuditGray_labels = context->GetIntListCommonAttr("hotMixManAuditGray_labels");
  auto hotMixManAuditPoor_labels = context->GetIntListCommonAttr("hotMixManAuditPoor_labels");

  auto m_ctx = context->GetMutablePtrCommonAttr<MixRankContext>(kMixRankCtxAttr);
  const auto& auditValues = m_ctx->MixRankRequest()->extra_request_info().plugin_photo_cover_audit_values();
  std::unordered_set<int64> photoid_MachineAuditFail;
  std::unordered_set<int64> photoid_ManAuditGray;
  std::unordered_set<int64> photoid_ManAuditPoor;
  for (const auto& kv : auditValues) {
    if (hotMixMachineAuditFail_labels.has_value()) {
      if (std::find(hotMixMachineAuditFail_labels->begin(), hotMixMachineAuditFail_labels->end(), kv.second)
        != hotMixMachineAuditFail_labels->end()) {
        photoid_MachineAuditFail.insert(kv.first);
      }
    }
    if (hotMixManAuditGray_labels.has_value()) {
      if (std::find(hotMixManAuditGray_labels->begin(), hotMixManAuditGray_labels->end(), kv.second)
        != hotMixManAuditGray_labels->end()) {
        photoid_ManAuditGray.insert(kv.first);
      }
    }
    if (hotMixManAuditPoor_labels.has_value()) {
      if (std::find(hotMixManAuditPoor_labels->begin(), hotMixManAuditPoor_labels->end(), kv.second)
        != hotMixManAuditPoor_labels->end()) {
        photoid_ManAuditPoor.insert(kv.first);
      }
    }
  }

  bool enable_hot_mix_calc_score_opt = GetBoolProcessorParameter(context,
    "enable_hot_mix_calc_score_opt", false);
  std::vector<MixRankResult> ad_dsp_filter;
  std::vector<MixRankResult> ad_top_fans_filter;
  std::vector<MixRankResult> ad_live_filter;
  bool enable_ad_live = GetBoolProcessorParameter(context, "enable_hot_mix_ad_live", false);
  for (auto* ad_dsp_item : mix_ctx.AdDspResults()) {
    MixRankResult item = MixRankResult(ad_dsp_item);
    int32 hetu1_id = -1;
    if (ad_dsp_item) {
      hetu1_id =
        ad_dsp_item->ad_dsp().ad_result().ad_deliver_info().first_level_category_id();
    }
    auto photo_id = ad_dsp_item->ad_dsp().ad_result().ad_deliver_info().ad_base_info().photo_id();

    if (explore_mix_enable_ad_MachineAuditFail_filter) {
      if (photoid_MachineAuditFail.find(photo_id) != photoid_MachineAuditFail.end()) {
        continue;
      }
    }

    if (explore_mix_enable_ad_ManAuditGray_filter) {
      if (photoid_ManAuditGray.find(photo_id) != photoid_ManAuditGray.end()) {
        continue;
      }
    }

    if (explore_mix_enable_ad_ManAuditPoor_filter) {
      if (photoid_ManAuditPoor.find(photo_id) != photoid_ManAuditPoor.end()) {
        continue;
      }
    }

    auto author_id = ad_dsp_item->ad_dsp().ad_result().ad_deliver_info().ad_base_info().author_id();
    if (enable_ad_live_bad_filter && MixRecoUtil::IsDspLive(ad_dsp_item) && plugin_bad_data_map
              && plugin_bad_data_map->find(author_id) != plugin_bad_data_map->end()) {
      base::perfutil::PerfUtilWrapper::CountLogStash(1, "reco.mix", "score.enricher",
        "ad_bad_live_filter", "count");
      continue;
    }
    if (reco_ids_set.count(photo_id) > 0) {
      continue;
    }
    // 健康类目进行过滤，请求不为健康类(16)
    if (enable_filter_jiankang_hetu) {
      if (hetu1_id == 16) {
        continue;
      }
    }
    if (enable_ad_live && enable_hot_mix_calc_score_opt && IsLiveItem(item)) {
      ad_live_filter.emplace_back(ad_dsp_item);
    } else if ((!enable_fountain_top_fans && MixRecoUtil::IsFansTop(ad_dsp_item))
        || (enable_fountain_top_fans && MixRecoUtil::IsNormalFansTopOrSoftAds(ad_dsp_item))) {
      ad_top_fans_filter.emplace_back(ad_dsp_item);
    } else {
      ad_dsp_filter.emplace_back(ad_dsp_item);
    }
    base::perfutil::PerfUtilWrapper::CountLogStash(1, "reco.mix", "score.enricher",
        "AdDspResults", "count");
  }

  for (auto* ad_top_fans_item : mix_ctx.AdFansTopResults()) {
    MixRankResult item = MixRankResult(ad_top_fans_item);
    auto photo_id = ad_top_fans_item->ad_fans_top().fans_top_result().fanstop_unit_info().photo_id();
    if (reco_ids_set.count(photo_id) > 0) {
      continue;
    }
    if (explore_mix_enable_ad_MachineAuditFail_filter) {
      if (photoid_MachineAuditFail.find(photo_id) != photoid_MachineAuditFail.end()) {
        continue;
      }
    }
    if (explore_mix_enable_ad_ManAuditGray_filter) {
      if (photoid_ManAuditGray.find(photo_id) != photoid_ManAuditGray.end()) {
        continue;
      }
    }
    if (explore_mix_enable_ad_ManAuditPoor_filter) {
      if (photoid_ManAuditPoor.find(photo_id) != photoid_ManAuditPoor.end()) {
        continue;
      }
    }
    if (enable_ad_live && enable_hot_mix_calc_score_opt && IsLiveItem(item)) {
      ad_live_filter.emplace_back(ad_top_fans_item);
    } else if ((!enable_fountain_top_fans && MixRecoUtil::IsFansTop(ad_top_fans_item))
        || (enable_fountain_top_fans && MixRecoUtil::IsNormalFansTopOrSoftAds(ad_top_fans_item))) {
      ad_top_fans_filter.emplace_back(ad_top_fans_item);
    } else {
      ad_dsp_filter.emplace_back(ad_top_fans_item);
    }
    base::perfutil::PerfUtilWrapper::CountLogStash(1, "reco.mix", "score.enricher",
        "AdFansTopResults", "count");
  }

  std::string low_active_ad_flag = "0";

  bool is_la_user = hot_mix_rank_context.is_new_low_vv_user || hot_mix_rank_context.is_true_new_user;
  if (((enable_la_ad_filter && is_la_user)
      || (enable_low_ad_filter && hot_mix_rank_context.is_low_active_user))
      && hot_mix_rank_context.nth_page < la_ad_nth_page) {
    low_active_ad_flag = "1";
  }

  if ((hot_mix_enable_open_lowactive_ad_score
      && hot_mix_rank_context.is_low_active_cold_start
      && ((hot_mix_rank_context.nth_page < lowactive_ad_nth_page)
      || (hot_mix_rank_context.nth_fresh_type < lowactive_ad_nth_fresh_type)))
      || (hot_mix_enable_open_zero_ad_score
      && hot_mix_rank_context.is_zero_cold_start
      && ((hot_mix_rank_context.nth_page < zero_user_ad_nth_page)
      || (hot_mix_rank_context.nth_fresh_type < zero_user_ad_nth_fresh_type)))) {
    low_active_ad_flag = "1";
  }

  // 硬广
  int ad_dsp_max_insert_num = GetIntProcessorParameter(context,
                  "reco_rpc_explore_ad_dsp_max_insert_num", 2);

  if (ad_dsp_max_insert_num > 0 && ad_dsp_filter.size() > 0) {
    std::string case_name = "ad_dsp";
    if (enable_hot_mix_calc_score_opt) {
      CalcScore(context, &ad_dsp_filter, &hot_mix_rank_context.pid2result_map, std::string("adDsp"));
      std::sort(ad_dsp_filter.begin(), ad_dsp_filter.end(),
          [](const MixRankResult& a, const MixRankResult& b) {
        return a.FinalScore() > b.FinalScore();
      });
      for (int i = 0; i < (int)ad_dsp_filter.size(); ++i) {
        MixRankResult& mix_rank_explore_ad = ad_dsp_filter.at(i);
        hot_mix_rank_context.AddExploreAdDspResults(mix_rank_explore_ad);
        hot_mix_rank_context.AddFinalResults(mix_rank_explore_ad);
      }
    } else {
      // ab 参数
      double boost_weight = GetDoubleProcessorParameter(context,
                      "reco_rpc_explore_ad_dsp_boost_weight", 1.0);
      double pctr_boost_weight = GetDoubleProcessorParameter(context,
            "reco_rpc_explore_ad_dsp_pctr_boost_weight", 1.0);
      double pctr_weight = GetDoubleProcessorParameter(context,
                            "reco_rpc_explore_ad_dsp_pctr_weight", 1.0);
      double pctr_bias = GetDoubleProcessorParameter(context,
                            "reco_rpc_explore_ad_dsp_pctr_bias", 1.0);
      double eco_weight = GetDoubleProcessorParameter(context,
                            "reco_rpc_explore_ad_dsp_eco_weight", 0.0);
      double eco_bias = GetDoubleProcessorParameter(context,
                            "reco_rpc_explore_ad_dsp_eco_bias", 0.0);
      double final_score_bias = GetDoubleProcessorParameter(context,
                      "reco_rpc_explore_ad_dsp_final_score_bias", 0.0);
      double cpm_bonus_weight = GetDoubleProcessorParameter(context,
                      "reco_rpc_explore_ad_dsp_cpm_bonus_weight", 1.0);
      double pos_0_boost_weight = GetDoubleProcessorParameter(context,
                      "reco_rpc_explore_ad_dsp_pos_0_boost_weight", 1.0);
      int ad_pos_no_0_limit =
        GetIntProcessorParameter(context, "reco_rpc_explore_ad_pos_no_0_limit", 2);
      bool has_pos_0 = false;
      for (int i = 0; i < (int)ad_dsp_filter.size(); ++i) {
        MixRankResult& mix_rank_explore_ad = ad_dsp_filter.at(i);
        if (mix_rank_explore_ad.RawAdPos() == 1) {
          boost_weight = boost_weight * pos_0_boost_weight;
          has_pos_0 = true;
        }
        CalcScoreAndPerf(context, &hot_mix_rank_context,
              &mix_rank_explore_ad, boost_weight, pctr_boost_weight,
              pctr_weight, eco_weight, final_score_bias, cpm_bonus_weight, case_name,
              i < ad_dsp_max_insert_num, eco_bias, pctr_bias, low_active_ad_flag);
        // 保存结果
        if (i < ad_dsp_max_insert_num) {
          if (low_active_ad_flag != "1") {
            hot_mix_rank_context.AddExploreAdDspResults(mix_rank_explore_ad);
            hot_mix_rank_context.AddFinalResults(mix_rank_explore_ad);
          }
        }
        hot_mix_rank_context.AddFullMixResults(mix_rank_explore_ad);
      }
      if (!has_pos_0) {
        hot_mix_rank_context.ad_pos_no_0_limit = ad_pos_no_0_limit;
      }
    }
  }
  // 粉条
  int ad_top_fans_max_insert_num = GetIntProcessorParameter(context,
                  "reco_rpc_explore_ad_top_fans_max_insert_num", 2);
  if (ad_top_fans_max_insert_num > 0 && ad_top_fans_filter.size() > 0) {
    std::string case_name = "ad_top_fans";
    if (enable_hot_mix_calc_score_opt) {
      CalcScore(context, &ad_top_fans_filter, &hot_mix_rank_context.pid2result_map, std::string("topFans"));
      std::sort(ad_top_fans_filter.begin(), ad_top_fans_filter.end(),
          [](const MixRankResult& a, const MixRankResult& b) {
        return a.FinalScore() > b.FinalScore();
      });
      for (int i = 0; i < (int)ad_top_fans_filter.size(); ++i) {
        MixRankResult& mix_rank_explore_ad = ad_top_fans_filter.at(i);
        hot_mix_rank_context.AddExploreAdTopFansResults(mix_rank_explore_ad);
        hot_mix_rank_context.AddFinalResults(mix_rank_explore_ad);
      }
    } else {
      // ab 参数
      double boost_weight = GetDoubleProcessorParameter(context,
                      "reco_rpc_explore_ad_top_fans_boost_weight", 1.0);
      double pctr_boost_weight = GetDoubleProcessorParameter(context,
            "reco_rpc_explore_ad_top_fans_pctr_boost_weight", 1.0);
      double pctr_weight = GetDoubleProcessorParameter(context,
                            "reco_rpc_explore_ad_top_fans_pctr_weight", 1.0);
      double pctr_bias = GetDoubleProcessorParameter(context,
                            "reco_rpc_explore_ad_top_fans_pctr_bias", 1.0);
      double eco_weight = GetDoubleProcessorParameter(context,
                            "reco_rpc_explore_ad_top_fans_eco_weight", 0.0);
      double eco_bias = GetDoubleProcessorParameter(context,
                            "reco_rpc_explore_ad_top_fans_eco_bias", 0.0);
      double final_score_bias = GetDoubleProcessorParameter(context,
                      "reco_rpc_explore_ad_top_fans_final_score_bias", 0.0);
      double cpm_bonus_weight = GetDoubleProcessorParameter(context,
                      "reco_rpc_explore_ad_top_fans_cpm_bonus_weight", 1.0);
      double pos_0_boost_weight = GetDoubleProcessorParameter(context,
                      "reco_rpc_explore_ad_top_fans_pos_0_boost_weight", 1.0);
      for (int i = 0; i < (int)ad_top_fans_filter.size(); ++i) {
        MixRankResult& mix_rank_explore_ad = ad_top_fans_filter.at(i);
        if (mix_rank_explore_ad.RawAdPos() == 0) {
          boost_weight = boost_weight * pos_0_boost_weight;
        }
        CalcScoreAndPerf(context, &hot_mix_rank_context,
              &mix_rank_explore_ad, boost_weight, pctr_boost_weight,
              pctr_weight, eco_weight, final_score_bias, cpm_bonus_weight, case_name,
              i < ad_top_fans_max_insert_num, eco_bias, pctr_bias, low_active_ad_flag);
        if (i < ad_top_fans_max_insert_num) {
          if (low_active_ad_flag != "1") {
            hot_mix_rank_context.AddExploreAdTopFansResults(mix_rank_explore_ad);
            hot_mix_rank_context.AddFinalResults(mix_rank_explore_ad);
          }
        }
        hot_mix_rank_context.AddFullMixResults(mix_rank_explore_ad);
      }
    }
  }
  //  商业化直播
  int ad_live_max_insert_num =
    GetIntProcessorParameter(context, "reco_rpc_explore_ad_live_max_insert_num", 0);
  if (enable_ad_live && enable_hot_mix_calc_score_opt &&
      ad_live_filter.size() > 0 && ad_live_max_insert_num > 0) {
    std::string case_name = "ad_living";
    CalcScore(context, &ad_live_filter, &hot_mix_rank_context.pid2result_map, case_name);
    std::sort(ad_live_filter.begin(), ad_live_filter.end(),
        [](const MixRankResult& a, const MixRankResult& b) {
      return a.FinalScore() > b.FinalScore();
    });
    for (int i = 0; i < (int)ad_live_filter.size(); ++i) {
      MixRankResult& mix_rank_explore_ad = ad_live_filter.at(i);
      hot_mix_rank_context.AddExploreAdLiveResults(mix_rank_explore_ad);
      hot_mix_rank_context.AddFinalResults(mix_rank_explore_ad);
    }
  }
}

void HotMixExploreAdScoreEnricher::CalcScoreAndPerf(AddibleRecoContextInterface *context,
            HotMixRankContext *hot_mix_rank_context, MixRankResult *mix_rank_explore_ad,
            double boost_weight, double pctr_boost_weight, double pctr_weight, double eco_weight,
            double final_score_bias, double cpm_bonus_weight, const std::string& case_name,
            bool is_satisfied, double eco_bias, double pctr_bias, const std::string& low_active_ad_flag) {
  MixRankContext &mix_ctx = MixCtx();
  bool is_debug = hot_mix_rank_context->is_debug;
  uint64 photo_id = mix_rank_explore_ad->PhotoId();
  uint64 author_id = mix_rank_explore_ad->AuthorId();
  bool use_power_calc = GetBoolProcessorParameter(context,
                  "reco_rpc_hot_mix_enable_ad_power_calc", false);

  // 从 MixRecoResult 当中把分数统一抽取出来
  std::unordered_map<std::string, float> xtr_infos;
  kuiba::PredictItem reco_pred;
  MixRecoResult *explore_ad = const_cast<MixRecoResult *>(mix_rank_explore_ad->GetPtr());
  HotMixGetXtrInfo(explore_ad, &xtr_infos, mix_rank_explore_ad->RawResultCase(),
      photo_id, author_id, &(hot_mix_rank_context->hetu_infos), &mix_ctx, &reco_pred);
  hot_mix_rank_context->pred_xtr_mp[photo_id] = reco_pred;

  auto ad_mixed_info = MixRecoUtil::GetAdMixedInfo(mix_rank_explore_ad->GetPtr());
  // 抽取 pctr
  float pctr_score = ad_mixed_info->unify_ctr();
  float cali_pctr_score = ad_mixed_info->feed_pctr();
  if (hot_mix_rank_context->use_new_ad_score) {
    pctr_score = cali_pctr_score;
  }
  std::pair<int, double> pctr_score_pair =
                  hot_mix_rank_context->GetQuantileScore(hot_mix_rank_context->pctr_quantiles,
                  pctr_score * pctr_boost_weight);
  float quantile_pctr_score = pctr_score_pair.second;
  mix_rank_explore_ad->SetPctrScore(quantile_pctr_score);
  // 抽取生态分
  float cpm = (ad_mixed_info->cpm() + cpm_bonus_weight * ad_mixed_info->cpm_bonus()) * 1.0 / 1e6;
  float eco_score = cpm;
  float ad_biz_value = ad_mixed_info->ad_biz_value();
  if (hot_mix_rank_context->use_new_ad_score) {
    eco_score = ad_biz_value;
  }
  mix_rank_explore_ad->SetEcologyValue(eco_score);

  // 生成 final score
  float final_score = 0.0;
  if (hot_mix_rank_context->use_power_calc || use_power_calc) {
    final_score = (pow(pctr_bias + quantile_pctr_score, pctr_weight) * pow(eco_bias + eco_score, eco_weight)
                      + final_score_bias) * boost_weight;
  } else {
    final_score = (pctr_weight * quantile_pctr_score + eco_weight * eco_score
                      + final_score_bias) * boost_weight;
  }
  mix_rank_explore_ad->SetFinalScore(final_score);
  mix_rank_explore_ad->SetPctrWeight(pctr_weight);
  mix_rank_explore_ad->SetEcoWeight(eco_weight);
  mix_rank_explore_ad->SetEcoBias(final_score_bias);

  if (is_debug) {
    LOG(INFO) << "HotMixExploreAdScoreEnricher: "
      << " reulst_case=" << case_name << ", "
      << " photo_id=" << photo_id << ", "
      << " author_id=" << author_id << ", "
      << " pctr_score=" << pctr_score << ", "
      << " eco_score=" << eco_score << ", "
      << " final_score=" << final_score << ", "
      << " quantile_pctr_score=" << quantile_pctr_score << ","
      << " cpm=" << ad_mixed_info->cpm() << ","
      << " cpm_bonus=" << ad_mixed_info->cpm_bonus() << ","
      << " position=" << mix_rank_explore_ad->GetPtr()->ad_dsp().pos() - 1;
  }

  // perf
  if (is_satisfied) {
    base::perfutil::PerfUtilWrapper::CountLogStash(1, "reco.mix", "score.enricher",
        case_name, "count", low_active_ad_flag);
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(pctr_score * 1000000),
        "reco.mix", "score.enricher", case_name, "pctr_score", low_active_ad_flag);
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(cali_pctr_score * 1000000),
        "reco.mix", "score.enricher", case_name, "new_pctr_score", low_active_ad_flag);
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(eco_score * 1000000),
        "reco.mix", "score.enricher", case_name, "eco_score", low_active_ad_flag);
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(ad_biz_value * 1000000),
        "reco.mix", "score.enricher", case_name, "new_eco_score", low_active_ad_flag);
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(final_score * 1000000),
        "reco.mix", "score.enricher", case_name, "final_score", low_active_ad_flag);
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(quantile_pctr_score * 1000000),
        "reco.mix", "score.enricher", case_name, "quantile_pctr_score", low_active_ad_flag);
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(ad_mixed_info->cpm()),
        "reco.mix", "score.enricher", case_name, "cpm", low_active_ad_flag);
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(ad_mixed_info->cpm_bonus()),
        "reco.mix", "score.enricher", case_name, "cpm_bonus", low_active_ad_flag);
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(pctr_weight * 1000000),
        "reco.mix", "score.enricher", case_name, "pctr_weight", low_active_ad_flag);
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(eco_weight * 1000000),
        "reco.mix", "score.enricher", case_name, "eco_weight", low_active_ad_flag);
  }
}

void HotMixExploreAdScoreEnricher::CalcScore(AddibleRecoContextInterface *context,
    std::vector<MixRankResult>* items, std::unordered_map<uint64, MixRankResult>* pid2result_map,
    const std::string& case_name) {
  bool enable_experience = GetBoolProcessorParameter(context,
        "hot_mix_enable_experience_" + case_name, false);
  bool enable_eco = GetBoolProcessorParameter(context,
        "hot_mix_enable_eco_" + case_name, false);
  int experience_score_type = GetIntProcessorParameter(context,
        "hot_mix_experience_score_type_" + case_name, 0);
  int eco_score_type = GetIntProcessorParameter(context,
        "hot_mix_eco_score_type_" + case_name, 0);
  int final_score_type = GetIntProcessorParameter(context,
        "hot_mix_final_score_type_" + case_name, 0);
  double final_adjust_wt = GetDoubleProcessorParameter(context,
        "hot_mix_final_adjust_wt_" + case_name, 1.0);
  double eco_wt = GetDoubleProcessorParameter(context,
        "hot_mix_eco_wt_" + case_name, 1.0);
  double experience_wt = GetDoubleProcessorParameter(context,
        "hot_mix_experience_wt_" + case_name, 1.0);
  std::string eco_params_str = GetStringProcessorParameter(context,
        "hot_mix_eco_params_" + case_name, "");
  std::string experience_params_str = GetStringProcessorParameter(context,
        "hot_mix_experience_params_" + case_name, "");
  bool enable_cali = GetBoolProcessorParameter(context,
    "enable_hot_mix_pxtr_cali", false);
  folly::F14FastMap<std::string, std::pair<double, double> > eco_queue_weights;
  folly::F14FastMap<std::string, std::pair<double, double> > experience_queue_weights;
  if (enable_eco) {
    ParseWeightParams(eco_params_str, &eco_queue_weights);
  }
  if (enable_experience) {
    ParseWeightParams(experience_params_str, &experience_queue_weights);
  }
  for (int i = 0; i < (int)items->size(); ++i) {
    double final_score = 0.0;
    double eco_score = 0.0;
    double experience_score = 0.0;
    MixRankResult& item = items->at(i);
    auto iter = pid2result_map->find(item.ItemId());
    if (iter != pid2result_map->end()) {
      CalcItemFinalScore(&(iter->second), &final_score, &experience_score, &eco_score,
      enable_experience, enable_eco, experience_score_type, eco_score_type,
      final_score_type, final_adjust_wt, experience_wt, eco_wt,
      experience_queue_weights, eco_queue_weights, enable_cali);
    }
    item.SetFinalScore(final_score);
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, HotMixExploreAdScoreEnricher, HotMixExploreAdScoreEnricher);

}  // namespace platform
}  // namespace ks
