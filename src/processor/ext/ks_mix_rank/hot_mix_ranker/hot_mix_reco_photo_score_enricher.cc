#include <math.h>
#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <set>
#include <algorithm>
#include <sstream>
#include <utility>

#include "kconf/kconf.h"
#include "base/random/true_random.h"
#include "dragon/src/processor/ext/ks_mix_rank/util/mix_rank_util.h"
#include "dragon/src/processor/ext/ks_mix_rank/base/mix_rank_result.h"
#include "dragon/src/processor/ext/ks_mix_rank/base/hot_mix_rank_context.h"
#include "dragon/src/processor/ext/ks_mix_rank/hot_mix_ranker/hot_mix_reco_photo_score_enricher.h"

namespace ks {
namespace platform {

void HotMixRecoPhotoScoreEnricher::MixRank(AddibleRecoContextInterface *context) {
  Init(context);
  MixRankContext &mix_ctx = MixCtx();
  HotMixRankContext &hot_mix_rank_context = mix_ctx.GetHotMixRankContext();

  // 获取混排重排序结果
  Sequence &final_seq = hot_mix_rank_context.opt_photo_seq;
  std::vector<MixRankResult> &reco_photo_results = final_seq.items;

  // 是否要直接拿 rerank 给的结果, 默认 false
  int replace_flag = GetIntProcessorParameter(context, "reco_rpc_hot_mix_rank_replace_rerank_flag", 0);

  // flag 为 true 或重排序结果为空时，拿 rerank 给的结果
  if (replace_flag > 0 || reco_photo_results.empty()) {
    reco_photo_results.clear();
    const auto& reco_photos = mix_ctx.RecoPhotoResults();
    for (int i = 0; i < reco_photos.size(); i++) {
      MixRankResult rank_result(reco_photos.at(i));
      reco_photo_results.emplace_back(rank_result);
    }
  }

  // 确认是否为空
  if (reco_photo_results.empty()) {
    base::perfutil::PerfUtilWrapper::CountLogStash("reco.mix",
        "reco.photo.seq", "size.zero");
    return;
  }

  double boost_weight = GetDoubleProcessorParameter(context,
                  "reco_rpc_reco_photo_boost_weight", 1.0);
  double pctr_weight = GetDoubleProcessorParameter(context,
                        "reco_rpc_reco_photo_pctr_weight", 1.0);
  double eco_weight = GetDoubleProcessorParameter(context,
                        "reco_rpc_reco_photo_eco_weight", 0.0);
  double final_score_bias = GetDoubleProcessorParameter(context,
                  "reco_rpc_reco_photo_final_score_bias", 0.0);

  bool is_ctr_need_cali = GetBoolProcessorParameter(context,
                          "reco_rpc_reco_photo_ctr_need_calibration", true);
  double alpha = GetDoubleProcessorParameter(context,
                          "reco_rpc_reco_photo_ctr_neg_sampling_rate", 0.1614);
  bool use_pos_score = GetBoolProcessorParameter(context,
                          "reco_rpc_reco_photo_use_pos_score", false);
  std::string eco_score_str = GetStringProcessorParameter(context,
          "reco_rpc_reco_photo_eco_score_str", "1.0,0.95,0.9,0.85,0.8,0.7,0.6,0.5,0.4,0.3");
  const std::string str_split = ",";
  std::vector<std::string> multi_eco_score_strs;
  MixSplitStringWithOptions(eco_score_str, str_split,
      true, true, &multi_eco_score_strs);
  std::vector<double> eco_scores;
  for (auto &score_str : multi_eco_score_strs) {
    double score = 0.75;
    if (!absl::SimpleAtod(score_str, &score)) {
      LOG(WARNING) << "invalid reco_photo eco_score_str, score = " << score_str;
    }
    eco_scores.push_back(score);
  }

  bool is_debug = hot_mix_rank_context.is_debug;
  bool enable_hot_mix_calc_score_opt = GetBoolProcessorParameter(context,
    "enable_hot_mix_calc_score_opt", false);
  if (enable_hot_mix_calc_score_opt) {
    CalcScore(context, &reco_photo_results, &hot_mix_rank_context.pid2result_map, std::string("reco_photo"));
  } else {
    for (int i = 0; i < reco_photo_results.size(); ++i) {
      MixRankResult &reco_photo_result = reco_photo_results.at(i);

      uint64 photo_id = reco_photo_result.PhotoId();
      uint64 author_id = reco_photo_result.AuthorId();

      // 抽取 pctr
      float pctr_score = reco_photo_result.PctrScore();
      if (is_ctr_need_cali) {
        pctr_score = alpha * pctr_score / (1 - (1 - alpha) * pctr_score);
      }
      std::pair<int, double> score_pair = hot_mix_rank_context.GetQuantileScore(
                      hot_mix_rank_context.pctr_quantiles,
                      pctr_score);
      float quantile_pctr_score = score_pair.second;
      reco_photo_result.SetPctrScore(quantile_pctr_score);

      // 抽取生态分
      float eco_score = 0.5;
      if (i < eco_scores.size()) {
        eco_score = static_cast<float>(eco_scores[i]);
      }

      reco_photo_result.SetEcologyValue(eco_score);

      // 生成 final score
      // float final_score = ctr_weight * pctr_score + eco_weight * eco_score;
      float final_score = 0.8;
      if (hot_mix_rank_context.use_auction_seq_mix) {
        final_score = (pctr_weight * quantile_pctr_score + eco_weight * eco_score
                                + final_score_bias) * boost_weight;
      } else {
        final_score = eco_score;
      }
      reco_photo_result.SetFinalScore(final_score);
      reco_photo_result.SetPctrWeight(pctr_weight);
      reco_photo_result.SetEcoWeight(eco_weight);
      BoostHighEffPhoto(&reco_photo_result, &hot_mix_rank_context, photo_id, i);
      UserActiveAdjustScore(context, &reco_photo_result, &hot_mix_rank_context);

      if (is_debug) {
        LOG(INFO) << "HotMixRecoPhotoScoreEnricher: "
            << " reulst_case=" << GetResultCaseName(reco_photo_result) << ", "
            << " photo_id=" << photo_id << ", "
            << " author_id=" << author_id << ", "
            << " pctr_score=" << pctr_score << ", "
            << " eco_score=" << eco_score << ", "
            << " final_score=" << final_score << ", "
            << " neg_sample_rate=" << alpha << ", "
            << " quantile_pctr_score=" << quantile_pctr_score;
      }
      // perf
      base::perfutil::PerfUtilWrapper::CountLogStash(1, "reco.mix", "score.enricher",
          "reco_photo", "count");
      base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(pctr_score * 1000000),
          "reco.mix", "score.enricher", "reco_photo", "pctr_score");
      base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(eco_score * 1000000),
          "reco.mix", "score.enricher", "reco_photo", "eco_score");
      base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(reco_photo_result.FinalScore() * 1000000),
          "reco.mix", "score.enricher", "reco_photo", "final_score");
      base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(quantile_pctr_score * 1000000),
          "reco.mix", "score.enricher", "reco_photo", "quantile_pctr_score");
      base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(pctr_weight * 1000000),
          "reco.mix", "score.enricher", "reco_photo", "pctr_weight");
      base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(eco_weight * 1000000),
          "reco.mix", "score.enricher", "reco_photo", "eco_weight");
    }
  }
  for (const auto& reco_photo_result : reco_photo_results) {
    hot_mix_rank_context.AddRecoPhotoResults(reco_photo_result);
    hot_mix_rank_context.AddFinalResults(reco_photo_result);
    if (hot_mix_rank_context.use_auction_seq_mix) {
      Append(reco_photo_result);
    }
  }
}

void HotMixRecoPhotoScoreEnricher::Init(AddibleRecoContextInterface *context) {
  enable_high_eff_boost_ = GetBoolProcessorParameter(context,
                  "reco_rpc_reco_photo_enable_high_eff_boost", false);
  high_eff_boost_idx_limit_ = GetIntProcessorParameter(context,
                  "reco_rpc_reco_photo_high_eff_boost_idx_limit", 1);
  high_eff_boost_threshold_ = GetDoubleProcessorParameter(context,
                        "reco_rpc_reco_photo_high_eff_boost_threshold", 0.15);
  high_eff_boost_param_ = GetDoubleProcessorParameter(context,
                        "reco_rpc_reco_photo_high_eff_boost_param", 1.0);
}

void HotMixRecoPhotoScoreEnricher::BoostHighEffPhoto(MixRankResult* reco_photo_result,
          HotMixRankContext* hot_context, uint64 photo_id, int idx) {
  if (!enable_high_eff_boost_ || idx > high_eff_boost_idx_limit_) {
    return;
  }
  float pctr = 0.0;
  float peff = 0.0;
  if (hot_context->xtr_info_mp.count(photo_id) > 0) {
    pctr = hot_context->xtr_info_mp[photo_id]["reco_pctr"];
    peff = hot_context->xtr_info_mp[photo_id]["reco_peff"];
  }
  float pctreff = pctr * peff;
  if (pctreff > high_eff_boost_threshold_) {
    reco_photo_result->SetFinalScore(reco_photo_result->FinalScore() * high_eff_boost_param_);
  }
}

void HotMixRecoPhotoScoreEnricher::UserActiveAdjustScore(AddibleRecoContextInterface *context,
          MixRankResult* reco_photo_result, HotMixRankContext* hot_context) {
  bool enable_user_active_pos_adjust = GetBoolProcessorParameter(context,
                                           "reco_rpc_enable_user_active_pos_adjust", false);
  if (enable_user_active_pos_adjust) {
    double low_active_boost_param = GetDoubleProcessorParameter(context,
                                          "reco_rpc_low_active_reco_photo_boost_param", 1.0);
    double high_active_boost_param = GetDoubleProcessorParameter(context,
                                          "reco_rpc_high_active_reco_photo_boost_param", 1.0);
    std::string flag = "mid_act";
    if (hot_context->active_days <= hot_context->low_active_days_thred) {
      reco_photo_result->SetFinalScore(reco_photo_result->FinalScore() * low_active_boost_param);
      flag = "low_act";
    }
    if (hot_context->active_days >= hot_context->high_active_days_thred) {
      reco_photo_result->SetFinalScore(reco_photo_result->FinalScore() * high_active_boost_param);
      flag = "high_act";
    }
    base::perfutil::PerfUtilWrapper::CountLogStash(1, "reco.mix", "score.enricher.user_active",
        "user_active", flag);
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(reco_photo_result->FinalScore() * 1000000),
        "reco.mix", "score.enricher.user_active", "reco_photo", "final_score", flag);
  }
}

void HotMixRecoPhotoScoreEnricher::CalcScore(AddibleRecoContextInterface *context,
    std::vector<MixRankResult>* items, std::unordered_map<uint64, MixRankResult>* pid2result_map,
    const std::string& case_name) {
  bool enable_experience = GetBoolProcessorParameter(context,
        "hot_mix_enable_experience_" + case_name, false);
  bool enable_eco = GetBoolProcessorParameter(context,
        "hot_mix_enable_eco_" + case_name, false);
  int experience_score_type = GetIntProcessorParameter(context,
        "hot_mix_experience_score_type_" + case_name, 0);
  int eco_score_type = GetIntProcessorParameter(context,
        "hot_mix_eco_score_type_" + case_name, 0);
  int final_score_type = GetIntProcessorParameter(context,
        "hot_mix_final_score_type_" + case_name, 0);
  double final_adjust_wt = GetDoubleProcessorParameter(context,
        "hot_mix_final_adjust_wt_" + case_name, 1.0);
  double eco_wt = GetDoubleProcessorParameter(context,
        "hot_mix_eco_wt_" + case_name, 1.0);
  double experience_wt = GetDoubleProcessorParameter(context,
        "hot_mix_experience_wt_" + case_name, 1.0);
  std::string eco_params_str = GetStringProcessorParameter(context,
        "hot_mix_eco_params_" + case_name, "");
  std::string experience_params_str = GetStringProcessorParameter(context,
        "hot_mix_experience_params_" + case_name, "");
  bool enable_cali = GetBoolProcessorParameter(context,
        "enable_hot_mix_pxtr_cali", false);
  folly::F14FastMap<std::string, std::pair<double, double> > eco_queue_weights;
  folly::F14FastMap<std::string, std::pair<double, double> > experience_queue_weights;
  if (enable_eco) {
    ParseWeightParams(eco_params_str, &eco_queue_weights);
  }
  if (enable_experience) {
    ParseWeightParams(experience_params_str, &experience_queue_weights);
  }
  for (int i = 0; i < (int)items->size(); ++i) {
    double final_score = 0.0;
    double eco_score = 0.0;
    double experience_score = 0.0;
    MixRankResult& item = items->at(i);
    auto iter = pid2result_map->find(item.ItemId());
    if (iter != pid2result_map->end()) {
      CalcItemFinalScore(&(iter->second), &final_score, &experience_score, &eco_score,
      enable_experience, enable_eco, experience_score_type, eco_score_type,
      final_score_type, final_adjust_wt, experience_wt, eco_wt,
      experience_queue_weights, eco_queue_weights, enable_cali);
    item.SetFinalScore(final_score);
    }
  }
}
typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, HotMixRecoPhotoScoreEnricher, HotMixRecoPhotoScoreEnricher);

}  // namespace platform
}  // namespace ks
