#include <algorithm>
#include <memory>
#include <ctime>

#include "ks/reco_pub/reco/util/util.h"
#include "dragon/src/processor/ext/ks_mix_rank/load_controller/slide_mix_rank_load_controller_enricher.h"
#include "dragon/src/processor/ext/ks_mix_rank/util/mix_reco_util.h"


namespace ks {
namespace platform {

void SlideMixRankLoadControllerEnricher::Enrich(MutableRecoContextInterface *context,
    RecoResultConstIter begin, RecoResultConstIter end) {
  // 从 KConf 获取 JSON
  Init();
  GetInfoFromKconf(context);
  // 从 CommonAttr 中读取数据并按照格式进行处理
  GetInfoFromCommonAttr(context);
  // 分组聚合计算 vv load err
  ComputeVVLoadErrByGroup();
  // 根据信息进行 pid 控制
  DoPIDControl();
  // 商业化合并双端到单端
  MergeAllPageToSingle();
  // 对输出进行截断或置零或特殊处理
  TruncateOutput();
  // 将信息填充到 CommonAttr
  SetInfoToCommonAttr(context);
  // perf
  PerfLog();
}

void SlideMixRankLoadControllerEnricher::Init() {
  // stid vv 没发生变化则上游不下发 所以不能重制
  // stid_to_VV_map.clear();
  // 保留 timestamp 记录上一次的时间戳
  // timestamp = 0;
  group_to_VV_map.clear();
  total_VV_map.clear();
  // load 信息不清空
  // group_to_load_map.clear();
  stid_to_group_map.clear();
  group_to_range_map.clear();
  // 误差和输出不清空
  // group_pid_info_map.clear();
  group_to_pid_params_map.clear();
  group_to_starting_hour_map.clear();
  group_to_minmax_map.clear();
  group_to_w_load_map.clear();
  group_to_w_b_map.clear();
  w1 = 1;
  w2 = 1;
}

bool SlideMixRankLoadControllerEnricher::GetInfoFromCommonAttr(MutableRecoContextInterface *context) {
  // 填充每个 stid 的曝光
  FillVVInfo(context);
  // 填充 err 信息
  FillErrInfo(context);
  return true;
}

bool SlideMixRankLoadControllerEnricher::GetInfoFromKconf(MutableRecoContextInterface *context) {
  // 获取 kconf 配置并解析
  std::shared_ptr<::Json::Value> json = ks::infra::KConf().Get(
      "reco.mixRank.LoadController", std::make_shared<::Json::Value>())->Get();
  if (!json) return false;
  const ::Json::Value &root = *json;
  if (root["global_switch"].isBool()) {
    global_switch = root["global_switch"].asBool();
    CL_LOG(INFO) << "global_switch=" << global_switch;
  }
  if (root["w1"].isDouble()) {
    w1 = root["w1"].asDouble();
    CL_LOG(INFO) << "w1=" << w1;
  }
  if (root["w2"].isDouble()) {
    w1 = root["w2"].asDouble();
    CL_LOG(INFO) << "w2=" << w2;
  }
  const ::Json::Value &groups = root["groups"];
  if (!groups.isArray()) return false;
  std::string range_str;
  std::string group_str;
  std::string pid_str;
  std::string minmax_str;
  std::vector<std::string> range_str_vec;
  std::vector<std::string> group_str_vec;
  std::vector<std::string> pid_str_vec;
  std::vector<std::string> minmax_str_vec;

  for (const auto &group : groups) {
    if (!group["group_name"].isString()) {
      CL_LOG(INFO) << "load controller parse error, check KConf";
      continue;
    }
    const std::string &group_name = group["group_name"].asString();

    if (!group["starting_hour"].isInt()) {
      CL_LOG(INFO) << "starting_hour parse error, check KConf";
      continue;
    }
    group_to_starting_hour_map[group_name] = group["starting_hour"].asInt();

    const ::Json::Value &stids = group["stids"];
    if (group_name.empty() || !stids.isArray()) {
      continue;
    }

    std::unordered_map<std::string, std::string> page_string;
    page_string["nebula"] = "KUAISHOU_NEBULA";
    page_string["gamora"] = "KUAISHOU";
    page_string["all"] = "ALL";
    for (const auto &kv : page_string) {
      const std::string lowercase_page = kv.first;  // nebula
      const std::string uppercase_page = kv.second;  // KUAISHOU_NEBULA
      for (const auto & page_exp : group[lowercase_page]) {
        if (!page_exp["exp"].isString() || !page_exp["lower_range"].isDouble() ||
            !page_exp["upper_range"].isDouble()) {
          CL_LOG(INFO) << "load controller parse error, check KConf";
          continue;
        }
        for (const auto &stid : stids) {
          if (!stid.isString()) {
            CL_LOG(INFO) << "load controller parse error, check KConf";
            continue;
          }
          stid_to_group_map[page_exp["exp"].asString() + '|' + stid.asString()] = group_name;
        }
        const std::string key = uppercase_page + "|" + page_exp["exp"].asString() + "|" + group_name;
        double k_p = 0, k_i = 0, k_d = 0;
        if (page_exp["k_p"].isDouble()) {
          k_p = page_exp["k_p"].asDouble();
        } else {
          CL_LOG(INFO) << "k_p params parse error, check KConf";
        }
        if (page_exp["k_i"].isDouble()) {
          k_i = page_exp["k_i"].asDouble();
        } else {
          CL_LOG(INFO) << "k_i params parse error, check KConf";
        }
        if (page_exp["k_d"].isDouble()) {
          k_d = page_exp["k_d"].asDouble();
        } else {
          CL_LOG(INFO) << "k_d params parse error, check KConf";
        }
        group_to_pid_params_map[key] = std::vector<double> {k_p, k_i, k_d};
        pid_str_vec.push_back(key + ":" + std::to_string(k_p) + "," + std::to_string(k_i) +
            "," + std::to_string(k_d));

        // parse range
        const double &lower_range = page_exp["lower_range"].asDouble();
        const double &upper_range = page_exp["upper_range"].asDouble();
        group_to_range_map[key] = std::vector<double> {lower_range, upper_range};
        range_str_vec.push_back(key + ":" + std::to_string(lower_range) + "," + std::to_string(upper_range));

        // parse minmax
        const double &min_output = page_exp["min_output"].asDouble();
        const double &max_output = page_exp["max_output"].asDouble();
        group_to_minmax_map[key] = std::vector<double> {min_output, max_output};
        minmax_str_vec.push_back(key + ":" + std::to_string(min_output) + "," + std::to_string(max_output));

        // parse w_load
        const double &w_load = page_exp.get("w_load", 1.0).asDouble();
        group_to_w_load_map[key] = w_load;

        // parse w_b
        const double &w_b = page_exp.get("w_b", 1.0).asDouble();
        group_to_w_b_map[key] = w_b;
      }
    }
  }

  for (const auto &pair : stid_to_group_map) {
    group_str_vec.push_back(pair.first + ":" + pair.second);
  }

  base::FastJoinStrings(range_str_vec, ";", &range_str);
  base::FastJoinStrings(group_str_vec, ";", &group_str);
  base::FastJoinStrings(pid_str_vec, ";", &pid_str);
  base::FastJoinStrings(minmax_str_vec, ";", &minmax_str);

  context->SetStringCommonAttr("group_to_range_map", range_str);
  context->SetStringCommonAttr("stid_to_group_map", group_str);
  context->SetStringCommonAttr("group_to_pid_params_map", pid_str);
  context->SetStringCommonAttr("group_to_minmax_map", minmax_str);

  CL_LOG(INFO) << "kconf result range_str: " + range_str;
  CL_LOG(INFO) << "kconf result group_str: " + group_str;
  CL_LOG(INFO) << "kconf result pid_str: " + pid_str;
  CL_LOG(INFO) << "kconf result minmax_str: " + minmax_str;
  return true;
}

bool SlideMixRankLoadControllerEnricher::SetInfoToCommonAttr(MutableRecoContextInterface *context) {
  // 把 new_pre_err, new_cum_err, new_output 写入 commonAttr
  bool is_success = true;
  std::string new_pre_err_str = "";
  std::string new_cum_err_str = "";
  std::string new_output_str = "";
  std::string output_str = "";
  std::string err_str = "";
  std::vector<std::string> new_pre_err_str_vec;
  std::vector<std::string> new_cum_err_str_vec;
  std::vector<std::string> new_output_str_vec;
  std::vector<std::string> output_str_vec;
  std::vector<std::string> err_str_vec;
  for (const auto &pair : group_pid_info_map) {
    const auto &group = pair.first;
    const auto &type_map = pair.second;

    // 累积 cum_err 这一次的 err 变为下一次的 pre_err
    if (type_map.find("err") == type_map.end() || type_map.find("new_output") == type_map.end()) {
      CL_LOG(INFO) << "\"err\" not found in group_pid_info_map[" + group +"]";
      is_success = false;
      continue;
    }
    double new_cum_err = 0, new_pre_err = 0, cum_err = 0;
    if (type_map.find("cum_err") != type_map.end()) {
      cum_err = type_map.at("cum_err");
    } else {
      CL_LOG(INFO) << group + ", \"cum_err\" Not Found, set to zero.";
    }
    new_cum_err = cum_err + type_map.at("err");
    new_pre_err = type_map.at("err");

    new_pre_err_str_vec.push_back(group + ":" + std::to_string(new_pre_err));
    new_cum_err_str_vec.push_back(group + ":" + std::to_string(new_cum_err));
    new_output_str_vec.push_back(group + ":" + std::to_string(type_map.at("new_output")));
    output_str_vec.push_back(group + ":" + std::to_string(
        type_map.find("output") != type_map.end() ? type_map.at("output") : 0));
    err_str_vec.push_back(group + ":" + std::to_string(type_map.at("err")));
  }
  base::FastJoinStrings(new_pre_err_str_vec, ";", &new_pre_err_str);
  base::FastJoinStrings(new_cum_err_str_vec, ";", &new_cum_err_str);
  base::FastJoinStrings(new_output_str_vec, ";", &new_output_str);
  base::FastJoinStrings(output_str_vec, ";", &output_str);
  base::FastJoinStrings(err_str_vec, ";", &err_str);

  context->SetStringCommonAttr("new_pre_err", new_pre_err_str);
  context->SetStringCommonAttr("new_cum_err", new_cum_err_str);
  context->SetStringCommonAttr("new_output", new_output_str);
  context->SetStringCommonAttr("output", output_str);
  context->SetStringCommonAttr("err", err_str);

  // 数据落盘
  std::string stid_to_VV_map_str, group_to_VV_map_str, total_VV_map_str, group_to_load_map_str;
  SerializeMapToString(stid_to_VV_map, &stid_to_VV_map_str);
  SerializeMapToString(group_to_VV_map, &group_to_VV_map_str);
  SerializeMapToString(total_VV_map, &total_VV_map_str);
  SerializeMapToString(group_to_load_map, &group_to_load_map_str);

  context->SetStringCommonAttr("stid_to_VV_map", stid_to_VV_map_str);
  context->SetStringCommonAttr("group_to_VV_map", group_to_VV_map_str);
  context->SetStringCommonAttr("total_VV_map", total_VV_map_str);
  context->SetStringCommonAttr("group_to_load_map", group_to_load_map_str);
  context->SetIntCommonAttr("timestamp", timestamp);
  return is_success;
}

bool SlideMixRankLoadControllerEnricher::ComputeVVLoadErrByGroup() {
  // 1. 将 stid 粒度 VV 聚合为分组粒度
  // 2. 计算每组的 load
  // 3. 计算每组的 err

  bool is_success = true;

  // 计算总 VV
  // 计算分组 VV
  for (const auto &pair : stid_to_VV_map) {
    const auto &key_string = pair.first;
    const auto &VV = pair.second;
    std::vector<std::string> group_vec;
    MixSplitStringWithOptions(key_string, "|", true, true, &group_vec);
    if (group_vec.size() != 3) {
      is_success = false;
      // todo perf and error log
      break;
    }
    const std::string page = group_vec[0];
    const std::string exp = group_vec[1];
    const std::string stid = group_vec[2];

    const std::string total_key = page + "|" + exp;
    total_VV_map[total_key] += VV;
    // 计算双端总 VV
    const std::string double_page_all_key = "ALL|" + exp;
    total_VV_map[double_page_all_key] += VV;

    if (stid_to_group_map.find(exp + '|' + stid) == stid_to_group_map.end()) {
      // 当前 stid 不在控制范围内
      continue;
    }
    const std::string group_key = page + "|" + exp + "|" + stid_to_group_map[exp + '|' + stid];
    group_to_VV_map[group_key] += VV;

    const std::string double_page_all_group_key = "ALL|" + exp + "|" + stid_to_group_map[exp + '|' + stid];
    group_to_VV_map[double_page_all_group_key] += VV;
  }

  // 计算分组 load and err
  for (const auto &pair : group_to_VV_map) {
    const auto &key_string = pair.first;
    const auto &VV = pair.second;
    std::vector<std::string> group_vec;
    MixSplitStringWithOptions(key_string, "|", true, true, &group_vec);
    if (group_vec.size() != 3) {
      is_success = false;
      CL_LOG(INFO) << "parse group_to_VV_map wrong: group_vec.size() != 3";
      continue;
    }
    const std::string page = group_vec[0];
    const std::string exp = group_vec[1];
    const std::string total_key = page + "|" + exp;
    if (total_VV_map.find(total_key) == total_VV_map.end() || total_VV_map.at(total_key) == 0) {
      is_success = false;
      // todo perf and error log
      CL_LOG(INFO) << "total_VV_map key not found: " + total_key;
      continue;
    }
    double load = 1.0 * VV / total_VV_map.at(total_key);
    group_to_load_map[key_string] = load;
    if (group_to_range_map.find(key_string) == group_to_range_map.end()) {
      // 线上出现了没配置的 exp 组
      // todo perf
      is_success = false;
      CL_LOG(INFO) << "group_to_range_map key not found: " + key_string;
      continue;
    }
    const double &lower_bound = group_to_range_map.at(key_string)[0];
    const double &upper_bound = group_to_range_map.at(key_string)[1];
    if (lower_bound > upper_bound) {
      is_success = false;
      // todo: perf, ERROR log
    }

    if (load < lower_bound) {
      group_pid_info_map[key_string]["err"] = - lower_bound + load;
    } else if (load > upper_bound) {
      group_pid_info_map[key_string]["err"] = - upper_bound + load;
    } else {
      group_pid_info_map[key_string]["err"] = 0;
    }
  }

  return is_success;
}

void SlideMixRankLoadControllerEnricher::DoPIDControl() {
  // 对每一个分组分别用 pid 计算输出值并填充
  for (auto &pair : group_pid_info_map) {
    const auto &group_name = pair.first;
    std::vector<std::string> group_vec;
    MixSplitStringWithOptions(group_name, "|", true, true, &group_vec);
    auto &pid_map = pair.second;
    double err = 0, pre_err = 0, cum_err = 0;
    if (pid_map.find("err") != pid_map.end())
      err = pid_map.at("err");
    if (pid_map.find("pre_err") != pid_map.end())
      pre_err = pid_map.at("pre_err");
    if (pid_map.find("cum_err") != pid_map.end())
      cum_err = pid_map.at("cum_err");

    double k_p = 0, k_i = 0, k_d = 0;
    if (group_to_pid_params_map.find(group_name) != group_to_pid_params_map.end()) {
      const auto &param_vec = group_to_pid_params_map.at(group_name);
      k_p = param_vec[0];
      k_i = param_vec[1];
      k_d = param_vec[2];
    }
    // pid
    double out = k_p * err + k_i * (cum_err + err) + k_d * (err - pre_err);
    pid_map["new_output"] = out;
    // pid_map["output"] = out + (out == 0 ? group_to_w_load_map[group_name]: 1.0) * pid_map["output"];
    // 记录未融合的值
    pid_map["uncombined_output"] = pid_map["output"];
  }
}

void SlideMixRankLoadControllerEnricher::MergeAllPageToSingle() {
  // 对商业化进行融合操作
  for (auto &pair : group_pid_info_map) {
    const std::string group_name = pair.first;
    auto &pid_map = pair.second;
    std::vector<std::string> group_vec;
    MixSplitStringWithOptions(group_name, "|", true, true, &group_vec);
    if (group_vec.size() != 3) {
      CL_LOG(INFO) << "[MergeAllPageToSingle]parse group_pid_info_map group wrong: group_vec.size() != 3";
      continue;
    }
    const std::string page = group_vec[0];
    const std::string exp = group_vec[1];
    const std::string biz = group_vec[2];
    // 如果是商业化业务 将双端输出加到单端输出上
    if ("ad" == biz && "ALL" != page) {
      // 商业化双端排除 ALL
      double combined_new_output = group_pid_info_map[page + "|" + exp + "|" + biz]["new_output"] +
          (page == "KUAISHOU" ? w1 : w2) * group_pid_info_map["ALL|" + exp + "|" + biz]["new_output"];
      pid_map["output"] = combined_new_output +
          (combined_new_output == 0 ? group_to_w_load_map[group_name]: 1.0) * pid_map["output"];
    }
    if ("ad" != biz) {
      // 其他双端
      double out = pid_map["new_output"];
      double w = group_to_w_b_map[group_name];
      if (w == 0) w = 1;

      if (out == 0) {
        // 如果没有本次输出值 使用 w_load 对上次 output 逐渐降低
        pid_map["output"] = pid_map["output"] * group_to_w_load_map[group_name];
      } else {
        // 如果有输出值 使用 w_b 加速
        if (pid_map["output"] < 0) {
          pid_map["output"] /= w;
        }
        pid_map["output"] += out;
        if (pid_map["output"] < 0) {
          pid_map["output"] *= w;
        }
      }
    }
  }
}

void SlideMixRankLoadControllerEnricher::TruncateOutput() {
  // truncate the output
  for (auto &pair : group_pid_info_map) {
    const auto &group_name = pair.first;
    auto &pid_map = pair.second;
    // 总开关
    if (global_switch == false) {
      pid_map["output"] = 0;
    }

    if (group_to_minmax_map.find(group_name) == group_to_minmax_map.end()) {
      CL_LOG(INFO) << "cannot find group_name in group_to_minmax_map:" + group_name;
    } else {
      const auto &vec = group_to_minmax_map.at(group_name);
      if (vec.size() != 2) {
        CL_LOG(INFO) << "TruncateOutput group_to_minmax_map[group_name].size != 2";
      }
      // Do the truncate
      const double &min_output = vec[0];
      const double &max_output = vec[1];
      if (min_output > max_output) {
        CL_LOG(INFO) << "min_output > max_output";
      } else {
        pid_map["output"] = std::max(std::min(pid_map["output"], max_output), min_output);
      }
    }

    // 如果是 x 点前 则不进行输出
    std::vector<std::string> group_vec;
    MixSplitStringWithOptions(group_name, "|", true, true, &group_vec);
    time_t time_value = static_cast<time_t>(timestamp/1000);
    std::tm* time_info = std::localtime(&time_value);
    if (group_vec.size() != 3) {
      CL_LOG(INFO) << "TruncateOutput parse group wrong: group_vec.size() != 3";
      continue;
    }
    const int starting_hour = group_to_starting_hour_map[group_vec[2]];
    if (time_info->tm_hour < starting_hour) {
      pid_map["output"] = 0;
    }
    // 总开关
    if (global_switch == false) {
      pid_map["output"] = 0;
    }
  }
}

void SlideMixRankLoadControllerEnricher::FillVVInfo(MutableRecoContextInterface *context) {
  // 解析 CommonAttr 填充 stid_to_VV_map
  const auto *common_index_doc =
      context->GetProtoMessagePtrCommonAttr<ks::platform::CommonIndexDoc>("input_pb");
  if (common_index_doc == nullptr) {
    LOG(ERROR) << "common_index_doc doesn't exist in CommonAttr.";
    return;
  }
  timestamp = common_index_doc->batch_version_ms();
  // 新的一天 则清空 map
  time_t current_time_value = static_cast<time_t>(timestamp/1000);
  std::tm* current_time = std::localtime(&current_time_value);
  if (current_time->tm_hour == 0 && current_time->tm_min == 1) {
    stid_to_VV_map.clear();
    CL_LOG(INFO) << "Happy New Day! Clear stid_to_VV_map!";
  }

  for (const auto &attr : common_index_doc->attr()) {
    const std::string &group = attr.key();
    const int64 &value = attr.int_value();
    stid_to_VV_map[group] = value;
  }
}

bool SlideMixRankLoadControllerEnricher::FillErrInfo(MutableRecoContextInterface *context) {
  bool is_success = true;
  if (!FillMapByType(context, "pre_err")) {
    is_success = false;
    CL_LOG(INFO) << "fill err unsuccess: pre_err";
  }
  if (!FillMapByType(context, "cum_err")) {
    is_success = false;
    CL_LOG(INFO) << "fill err unsuccess: cum_err";
  }
  if (init_flag_) {
    // 如果首次运行则读 redis 否则直接使用本地变量
    if (!FillMapByType(context, "output")) {
      is_success = false;
      CL_LOG(INFO) << "fill err unsuccess: output";
    }
    init_flag_ = false;
  }
  return is_success;
}

bool SlideMixRankLoadControllerEnricher::FillMapByType(MutableRecoContextInterface *context,
    const std::string &type) {
  const auto &str = context->GetStringCommonAttr(type);
  if (!str) {
    CL_LOG(INFO) << "Unexist common attr: " + type;
    return false;
  }
  bool is_success = true;
  // {group}:{value};
  std::vector<std::string> str_vec;
  MixSplitStringWithOptions(std::string(str.value()), ";", true, true, &str_vec);
  if (str_vec.size() == 0) {
    CL_LOG(INFO) << "parsed str_vec.size() == 0";
    is_success = false;
  }
  for (const auto& group_kv_str : str_vec) {
    std::vector<std::string> group_vec;
    MixSplitStringWithOptions(group_kv_str, ":", true, true, &group_vec);
    if (group_vec.size() != 2) {
      is_success = false;
      CL_LOG(INFO) << "parsed group_vec.size() != 2, the target string" + group_kv_str;
      continue;
    }
    double parsed_double;
    if (!absl::SimpleAtod(group_vec[1], &parsed_double)) {
      is_success = false;
      CL_LOG(INFO) << "parsed double group_vec[1] wrong, with target string" + group_vec[1];
      continue;
    }
    group_pid_info_map[group_vec[0]][type] = parsed_double;
  }
  return is_success;
}

void SlideMixRankLoadControllerEnricher::PerfLog() {
  const std::string kMixPerfNamespace = "common.leaf.mix.service";
  for (const auto &pair : stid_to_VV_map) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(pair.second),
        kMixPerfNamespace, "mix_rank_auction.load_controller.offline" + env_, pair.first, "stid_vv");
  }
  for (const auto &pair : group_to_VV_map) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(pair.second),
        kMixPerfNamespace, "mix_rank_auction.load_controller.offline" + env_, pair.first, "group_vv");
  }
  for (const auto &pair : total_VV_map) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(pair.second),
        kMixPerfNamespace, "mix_rank_auction.load_controller.offline" + env_, pair.first, "total_vv");
  }
  for (const auto &pair : group_to_load_map) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(pair.second * 1000000),
        kMixPerfNamespace, "mix_rank_auction.load_controller.offline" + env_, pair.first, "group_load");
  }
  // err, pre_err, cum_err, output, new_output
  for (const auto &key_map : group_pid_info_map) {
    const auto &group = key_map.first;
    const auto &map = key_map.second;
    for (const auto pair : map) {
      const auto &type = pair.first;
      const auto &value = pair.second;
      base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(value * 1000000),
          kMixPerfNamespace, "mix_rank_auction.load_controller.offline" + env_, group, type);
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, SlideMixRankLoadControllerEnricher, SlideMixRankLoadControllerEnricher);


}  // namespace platform
}  // namespace ks
