#include <unordered_set>
#include "dragon/src/processor/ext/ks_mix_rank/util/mix_rank_util.h"
#include "dragon/src/processor/ext/ks_mix_rank/common_mix_ranker/hot_mix_rank_leaf_show_observer.h"

namespace ks {
namespace platform {

static const int kInfoLogInterval = 1000;

void HotMixRankLeafShowObserver::MixRank(AddibleRecoContextInterface *context) {
  // 发送 sample
  SendSample(context);
  // 发送 kafka topic
  SendMixMetaLog(context);
}


void HotMixRankLeafShowObserver::SendSample(AddibleRecoContextInterface *context) {
  Clear();
  bool enable_send_sample = GetBoolProcessorParameter(context, enable_send_sample_, false);
  if (!enable_send_sample) {
    CL_LOG(INFO) << "MixRank leaf show cancelled, check online env or ab switch, json config:"
                 << base::JsonToString(enable_send_sample_->get());
    return;
  }

  MixRankContext &mix_ctx = MixCtx();
  const MixRankResultList &mix_results = mix_ctx.MixResults();
  MixFeatureCache &fea_cache = mix_ctx.GetMixFeatureCache();
  // MockCachePredictItem(context);

  // clear unnatural live
  mix::kuaishou::ad::AdEnum ad_enum;
  std::set<int64> default_special_exptag = {1256, 1293, 1285, 1299, 1295};
  MixRankResultList &mutable_mix_results = mix_ctx.MixResults();
  int mix_results_size = mutable_mix_results.Size();

  // 1.Generate CommonKey
  std::string common_key;
  if (common_key_choice_ == "request_id") {
    common_key = context->GetRequestId();
  } else if (common_key_choice_ == "device_id") {
    common_key = context->GetDeviceId();
  } else {
    common_key = base::Uint64ToString(context->GetUserId());
  }
  CL_LOG_EVERY_N(INFO, kInfoLogInterval)
      << "HotMixRankLeafShowObserver common_key_choice: " << common_key_choice_ << " key: " << common_key;

  // 2.1 顺序执行配置的 UserExtractor 函数, 抽取 User 侧特征
  for (int extractor_index = 0; extractor_index < user_extractors.size(); extractor_index++) {
    MixRankBaseUserExtractor *extractor = user_extractors[extractor_index];
    extractor->extract(&user_attrs, *context, mix_ctx);

    CL_LOG_EVERY_N(INFO, kInfoLogInterval)
        << "Finish UserExtractor: " << extractor_index << ", common_attrs size to:" << user_attrs.attr_size();
  }

  // 2.2 Merge 已经抽取好的 User 侧特征
  for (auto &name : user_merge_cached_feature_names) {
    kuiba::PredictItem *predict_item = fea_cache.GetUserAttrsByName(name);
    if (predict_item == nullptr) {
      CL_LOG_EVERY_N(ERROR, kInfoLogInterval) << "Cant find name in MixFeatureCache, name:" << name;
      continue;
    }
    for (const auto &it : predict_item->attr()) {
      kuiba::SampleAttr *attr = user_attrs.add_attr();
      *attr = it;  // copy SampleAttr
    }
    CL_LOG_EVERY_N(INFO, kInfoLogInterval)
        << "Finish Merge User Attrs: " << name << ", common_attrs size to:" << user_attrs.attr_size();
  }
  user_attrs.SerializeToString(&user_attrs_string);

  bool enable_mix_trace =
      (ks::infra::KConf().Get("reco.picture.enableFillMixTraceResult", false))->Get();
  HotMixRankContext &hot_mix_context = mix_ctx.GetHotMixRankContext();
  const auto& full_mix_results = hot_mix_context.FullMixResults();
  // 3. Collect item_keys & item_owner_keys
  std::unordered_set<uint64_t> photoid_set;
  for (int item_index = 0; item_index < mix_results.Size(); item_index++) {
    const MixRankResult &item = mix_results.At(item_index);
    item_keys.push_back(base::Uint64ToString(item.PhotoId()));
    item_owner_keys.push_back(base::Uint64ToString(item.AuthorId()));
    photoid_set.insert(item.PhotoId());
  }
  if (enable_mix_trace) {
    for (int item_index = 0; item_index < full_mix_results.size(); item_index++) {
      const MixRankResult &item = full_mix_results[item_index];
      const auto& photo_id = item.PhotoId();
      if (photoid_set.find(photo_id) != photoid_set.end()) {
        continue;
      }
      item_keys.push_back(base::Uint64ToString(photo_id));
      item_owner_keys.push_back(base::Uint64ToString(item.AuthorId()));
    }
  }
  CL_LOG_EVERY_N(INFO, kInfoLogInterval) << "HotMixRankLeafShowObserver item_keys_size: " << item_keys.size()
                                         << " item_owner_keys_size: " << item_owner_keys.size();

  // 抽取 Item 侧特征
  for (int item_index = 0; item_index < mix_results.Size(); item_index++) {
    item_attrs.Clear();
    item_attrs_buffer.clear();
    const MixRankResult &item = mix_results.At(item_index);
    // 4.1 顺序执行配置的 ItemExtractor 函数, 抽取 Item 侧特征
    for (int extractor_index = 0; extractor_index < item_extractors.size(); extractor_index++) {
      MixRankBaseItemExtractor *extractor = item_extractors[extractor_index];
      extractor->extract(&item_attrs, *context, mix_ctx, item_index + 1, item);

      CL_LOG_EVERY_N(INFO, kInfoLogInterval)
          << "Item index: " << item_index << ", Finish ItemExtractor: " << extractor_index
          << ", item_attrs size to:" << item_attrs.attr_size();
    }

    // 4.2 Merge 已经构造好 PredictItem, 适用于在预估等不同阶段抽取特征.统一用 PhotoId() 为 key
    uint64 item_key = item.PhotoId();
    for (auto &name : item_merge_cached_feature_names) {
      folly::F14FastMap<uint64, kuiba::PredictItem *> *map_ptr = fea_cache.GetItemAttrsByName(name);
      if (map_ptr == nullptr) {
        CL_LOG_EVERY_N(ERROR, kInfoLogInterval) << "Cant find name in MixFeatureCache, name:" << name;
        continue;
      }
      if (map_ptr->find(item_key) != map_ptr->end()) {
        kuiba::PredictItem *predict_item = (*map_ptr)[item_key];
        for (const auto &it : predict_item->attr()) {
          kuiba::SampleAttr *attr = item_attrs.add_attr();
          *attr = it;  // copy SampleAttr
        }
      }
      CL_LOG_EVERY_N(INFO, kInfoLogInterval)
          << "Item index: " << item_index << ", Finish Merge Cached Feature: " << name
          << ", item_attrs size to:" << item_attrs.attr_size();
    }

    item_attrs.SerializeToString(&item_attrs_buffer);
    item_attrs_strings.push_back(std::move(item_attrs_buffer));
  }

  if (enable_mix_trace) {
    for (int item_index = 0; item_index < full_mix_results.size(); item_index++) {
      item_attrs.Clear();
      item_attrs_buffer.clear();
      const MixRankResult &item = full_mix_results[item_index];
      const auto& photo_id = item.PhotoId();
      if (photoid_set.find(photo_id) != photoid_set.end()) {
        continue;
      }

      // 4.1 顺序执行配置的 ItemExtractor 函数, 抽取 Item 侧特征
      for (int extractor_index = 0; extractor_index < item_extractors.size(); extractor_index++) {
        MixRankBaseItemExtractor *extractor = item_extractors[extractor_index];
        extractor->extract(&item_attrs, *context, mix_ctx, 0, item);

        CL_LOG_EVERY_N(INFO, kInfoLogInterval)
            << "Full Item index: " << item_index << ", Finish ItemExtractor: " << extractor_index
            << ", item_attrs size to:" << item_attrs.attr_size();
      }

      item_attrs.SerializeToString(&item_attrs_buffer);
      item_attrs_strings.push_back(std::move(item_attrs_buffer));
    }
  }

  // 6.发送 LeafShow 到 btq
  // 确认: 什么场景需要发送 item_extra_attrs_string, 此时是否需要发送 common_attrs?
  if (as_extra_attrs_) {
    ks::action::SampleJoinBTQueueClient::Singleton()->SetAttrs(
        "", biz_name_, common_key, item_keys, user_attrs_string, item_owner_keys, std::vector<std::string>(),
        item_attrs_strings, shard_num_, true, bt_queue_name_);
  } else {
    ks::action::SampleJoinBTQueueClient::Singleton()->SetAttrs(
        "", biz_name_, common_key, item_keys, user_attrs_string, item_owner_keys, item_attrs_strings,
        std::vector<std::string>(), shard_num_, true, bt_queue_name_);
  }

  if (VLOG_IS_ON(100)) {
    LOG(INFO) << "  common_key: " << common_key << ", item_keys: " << base::JoinStrings(item_keys, "|")
              << ", common_attrs: " << user_attrs.Utf8DebugString()
              << ", item_owner_keys: " << base::JoinStrings(item_owner_keys, "|");
    if (item_attrs_strings.size() > 0) {
      LOG(INFO) << " item_attrs_strings_size:" << item_attrs_strings.size();
      kuiba::PredictItem tmp_message;
      tmp_message.ParseFromString(item_attrs_strings[0]);
      LOG(INFO) << " item[0]: " << tmp_message.Utf8DebugString();
    }
  }
}


void HotMixRankLeafShowObserver::SendMixMetaLog(AddibleRecoContextInterface *ctx) {
  if (!enable_send_mix_meta_log_) {
    CL_LOG(INFO) << "MixRank SendMixMetaLog cancelled, check online env or ab switch, json config:"
                 << base::JsonToString(enable_send_sample_->get());
    return;
  }

  MixRankContext &m_ctx = MixCtx();
  HotMixRankContext &hot_mix_rank_context = m_ctx.GetHotMixRankContext();
  const mix::kuaishou::newsmodel::UserInfo *user_info = m_ctx.UserInfo();
  const std::unordered_set<int>& permit_type_list = hot_mix_rank_context.permit_type_list;
  const MixRankResultList &mix_results = m_ctx.MixResults();
  // full_mix_result 包含广告竞价失败的 result
  const std::vector<MixRankResult> &full_mix_results = hot_mix_rank_context.FullMixResults();

  // 用于判断是否是在混排阶段过滤掉的 item
  std::unordered_set<uint64_t> photo_id_set;

  // 先获得只需执行一次的 common attr
  uint64_t llsid = 0;
  base::StringToUint64(ctx->GetRequestId(), &llsid);
  uint64_t timestamp = base::GetTimestamp();
  uint64_t uid = user_info->id();
  std::string device_id = ctx->GetDeviceId();
  std::string session_id = m_ctx.RecoClientRequestInfo()->session_id();
  int page_index = m_ctx.MixRankRequest()->extra_request_info().page();
  int fresh_type = m_ctx.MixRankRequest()->reco_fresh_type().fresh_type();
  int cold_start = m_ctx.RecoClientRequestInfo()->cold_start();
  int pic_num = 0;
  auto& pid2result_map = m_ctx.GetHotMixRankContext().pid2result_map;
  // 获取 item 特征
  for (int idx = 0; idx < mix_results.Size() + full_mix_results.size(); ++idx) {
    // 判断是否为 mix_result
    bool is_mix_result = idx < mix_results.Size();

    // 获取 item
    const MixRankResult &item =
      is_mix_result ? mix_results.At(idx) : full_mix_results[idx - mix_results.Size()];

    uint64_t photo_id = item.PhotoId();
    int position = idx + 1;
    if (is_mix_result) {
      // 避免竞价成功的在 full 重复运算
      photo_id_set.insert(photo_id);
    } else {
      // 略过竞价成功的
      if (photo_id_set.find(photo_id) != photo_id_set.end()) {
        continue;
      }
      // 竞价失败的 position = 0
      position = 0;
    }

    // 从 predict_item 获取 pctr, hetu info
    double pctr = 0.0;
    MixRecoResult::ResultCase result_case = item.RawResultCase();
    int tab = static_cast<int>(result_case);
    std::unordered_map<std::string, int> hetu_map = default_hetu_map_;
    if (hot_mix_rank_context.pred_xtr_mp.count(item.PhotoId()) > 0) {
      // 从 item_feature 获取 attr
      const kuiba::PredictItem &item_feature = hot_mix_rank_context.pred_xtr_mp.at(item.PhotoId());
      for (int feat_idx = 0; feat_idx < item_feature.attr_size(); feat_idx++) {
        auto sample_attr_ptr = item_feature.attr(feat_idx);
        // 获取 hetu 信息
        if (hetu_map.find(sample_attr_ptr.name()) != hetu_map.end()) {
          auto int_list = sample_attr_ptr.int_list_value();
          if (int_list.size() > 0) {
            hetu_map[sample_attr_ptr.name()] = *(int_list.begin());
          }
        }
      }

      // 根据不同业务获取 pctr
      if (result_case == MixRecoResult::ResultCase::kRecoPhoto) {
        mix::kuaishou::newsmodel::RankResult rank_result =
          GetRecoPhotoRankResult(item.GetPtr()->reco_photo());
        pctr = rank_result.pctr();
      } else if (result_case == MixRecoResult::ResultCase::kAdDsp
               || result_case == MixRecoResult::ResultCase::kAdFansTop) {
        auto ad_mixed_info =  MixRecoUtil::GetAdMixedInfo(item.GetPtr());
        pctr = ad_mixed_info->unify_ctr();
      } else if (result_case == MixRecoResult::ResultCase::kRecoLivestream
               || result_case == MixRecoResult::ResultCase::kMerchantExploreLive
               || MixRecoResult::ResultCase::kRelationRecoPhoto) {
        if (hot_mix_rank_context.off_item_feats.count(item.PhotoId()) > 0) {
          // 获取业务 pctr name
          std::string pctr_attr_name = result_case_pctr_name_map_[result_case];
          // 从 off_item_feat 取对应的 pctr
          const kuiba::PredictItem &off_item_feat = hot_mix_rank_context.off_item_feats.at(item.PhotoId());
          for (int feat_idx = 0; feat_idx < off_item_feat.attr_size(); feat_idx++) {
            auto sample_attr_ptr = off_item_feat.attr(feat_idx);
            if (sample_attr_ptr.name() == pctr_attr_name) {
              pctr = sample_attr_ptr.float_value();
              break;
            }
          }
        }
      }
    } else {
      // HotMixRankItemExtractor 的逻辑，我也不知道为啥。
      if (permit_type_list.find(tab) != permit_type_list.end()) {
        tab = 0;
      }
    }

    int traffic_type = item.IsFansTop() == 1 ? 3 : tab;
    std::string content_type = IsLiveItem(item) ? "live" : "photo";
    int cali_pctr = (int)(item.PctrScore() * 1000000);
    int eco_bias = (int)(item.EcoBias() * 1000000);

    // 获取商业化直播 id
    if (result_case == MixRecoResult::ResultCase::kAdDsp && content_type == "live") {
      const DspAdInfo *ad_dsp = item.GetAdDspInfo();
      if (ad_dsp) {
        uint64 live_id = ad_dsp->ad_result().ad_deliver_info().ad_base_info().live_stream_id();
        if (live_id > 0) {
          photo_id = live_id;
        }
      }
    }

    // clear attr
    hot_mix_meta_log_.Clear();
    hot_mix_meta_log_str_.clear();

    // set attr
    hot_mix_meta_log_.set_llsid(llsid);
    hot_mix_meta_log_.set_tab("hot");
    hot_mix_meta_log_.set_timestamp(timestamp);
    hot_mix_meta_log_.set_device_id(device_id);
    hot_mix_meta_log_.set_user_id(uid);
    hot_mix_meta_log_.set_item_id(photo_id);
    hot_mix_meta_log_.set_hetu_level_one(hetu_map["hetu_level_one"]);
    hot_mix_meta_log_.set_hetu_level_two(hetu_map["hetu_level_two"]);
    hot_mix_meta_log_.set_hetu_level_three(hetu_map["hetu_level_three"]);
    hot_mix_meta_log_.set_hetu_level_four(hetu_map["hetu_level_four"]);
    hot_mix_meta_log_.set_hetu_level_five(hetu_map["hetu_level_five"]);
    hot_mix_meta_log_.set_author_id(item.AuthorId());
    hot_mix_meta_log_.set_stid_mix(item.StidMixValue());
    hot_mix_meta_log_.set_traffic_type(traffic_type);
    hot_mix_meta_log_.set_session_id(session_id);
    hot_mix_meta_log_.set_ecology_score(item.FinalScore());
    hot_mix_meta_log_.set_pctr(pctr);
    hot_mix_meta_log_.set_weight_alpha(item.PctrWeight());
    hot_mix_meta_log_.set_weight_belta(item.EcoWeight());
    hot_mix_meta_log_.set_eco_score(item.EcologyValue());
    hot_mix_meta_log_.set_score(item.EcologyValue());
    hot_mix_meta_log_.set_show_pos(position);
    hot_mix_meta_log_.set_is_servershow(1);
    hot_mix_meta_log_.set_show_index(position);
    hot_mix_meta_log_.set_page_index(page_index);
    hot_mix_meta_log_.set_fresh_type(fresh_type);
    hot_mix_meta_log_.set_cold_start(cold_start);
    hot_mix_meta_log_.set_cali_pctr(cali_pctr);
    hot_mix_meta_log_.set_eco_bias(eco_bias);
    hot_mix_meta_log_.set_content_type(content_type);
    auto iter = pid2result_map.find(photo_id);
    if (iter != pid2result_map.end()) {
      hot_mix_meta_log_.set_extra_info(FillExtraInfo(iter->second));
    }

    // 发送 kafka
    hot_mix_meta_log_.SerializeToString(&hot_mix_meta_log_str_);
    RdKafka::ErrorCode err = kafka_producer_->Produce(hot_mix_meta_log_str_);
    if (err != RdKafka::ERR_NO_ERROR) {
      std::string err_msg = RdKafka::err2str(err);
      CL_LOG_ERROR("SendMixMetaLog", "send_kafka_fail:" + err_msg)
        << "failed to send data to kafka topic: " << topic_name_ << ", error: " << err_msg;
      base::perfutil::PerfUtilWrapper::CountLogStash("reco.mix",
        "mix_meta_log", "send_status", "fail");
    } else {
      // log
      CL_LOG_EVERY_N(INFO, kInfoLogInterval)
            << "send_kafka_succeed, uid:" << uid << ", pid: " << item.PhotoId()
            << ", traffic_type:" << traffic_type
            << ", is_cold_start: " << cold_start
            << ", hetu_l1: " << hetu_map["hetu_level_one"]
            << ", hetu_l2: " << hetu_map["hetu_level_two"]
            << ", eco_score: " << item.EcologyValue()
            << ", pctr: " << pctr
            << ", cali_pctr: " << cali_pctr
            << ", stid: " << item.StidMixValue();

      // perf
      std::string result_case_name = GetResultCaseName(item);
      base::perfutil::PerfUtilWrapper::CountLogStash(1, "reco.mix",
        "mix_meta_log", "send_status", "succeed");
      base::perfutil::PerfUtilWrapper::CountLogStash(1, "reco.mix",
        "mix_meta_log", "result_case", result_case_name);
      base::perfutil::PerfUtilWrapper::CountLogStash(1, "reco.mix",
        "mix_meta_log", "show_pos", result_case_name, std::to_string(position));
      base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(pctr * 1000000),
        "reco.mix", "mix_meta_log", "score", result_case_name, "pctr");
      base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(item.PctrScore() * 1000000),
        "reco.mix", "mix_meta_log", "score", result_case_name, "cali_pctr");
      base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(item.EcologyValue() * 1000000),
        "reco.mix", "mix_meta_log", "score", result_case_name, "eco_score");
      base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(item.FinalScore() * 1000000),
        "reco.mix", "mix_meta_log", "score", result_case_name, "final_score");
      for (auto const& it : hetu_map) {
        base::perfutil::PerfUtilWrapper::CountLogStash(1,
        "reco.mix", "mix_meta_log", "hetu", result_case_name, it.first, std::to_string(it.second));
      }
      if (photo_id == 0) {
        base::perfutil::PerfUtilWrapper::CountLogStash(1, "reco.mix",
          "mix_meta_log", "item_id=0");
      }
    }
    pic_num += is_mix_result && result_case == MixRecoResult::ResultCase::kRecoPhoto && IsPicture(item);
  }

  // 图文数量
  base::perfutil::PerfUtilWrapper::CountLogStash(1,
      "reco.mix", "request_photo_pic_num", "output", std::to_string(hot_mix_rank_context.age),
      std::to_string(hot_mix_rank_context.gender), std::to_string(pic_num));
}

// 仅作为例子
void HotMixRankLeafShowObserver::MockCachePredictItem(AddibleRecoContextInterface *context) {
  MixRankContext &mix_ctx = MixCtx();
  MixFeatureCache &fea_cache = mix_ctx.GetMixFeatureCache();
  const MixRankResultList &mix_results = mix_ctx.MixResults();
  auto test_map_ptr = fea_cache.GetOrInsertItemAttrsByName("Test");
  SlideMixRankItemExtractorDemo demo_fun;
  for (int item_index = 0; item_index < mix_results.Size(); item_index++) {
    const MixRankResult &item = mix_results.At(item_index);
    kuiba::PredictItem *predict_item = MixRankPool<kuiba::PredictItem>::Instance().AllocPb();
    demo_fun.extract(predict_item, *context, mix_ctx, item_index, item);
    (*test_map_ptr)[item.PhotoId()] = predict_item;
  }

  SlideMixRankUserExtractorDemo user_demo_fun;
  kuiba::PredictItem *user_attr = MixRankPool<kuiba::PredictItem>::Instance().AllocPb();
  user_demo_fun.extract(user_attr, *context, mix_ctx);
  fea_cache.SetUserAttrs("UserTest", user_attr);
}

std::string HotMixRankLeafShowObserver::FillExtraInfo(const MixRankResult& item) {
  thread_local PlainJsonStringBuilder json_builder;
  json_builder.clear();
  const auto &pxtr_info = item.GetHotPxtrInfo().GetPxtrInfo();
  for (const auto& kv : pxtr_info) {
    json_builder.AddField(kv.first + "_pre", std::to_string(kv.second.first));
    json_builder.AddField(kv.first + "_pos", std::to_string(kv.second.second));
  }
  const auto& item_info = item.GetHotPxtrInfo().GetItemInfo();
    for (auto& kv : item_info) {
    json_builder.AddField(kv.first, std::to_string(kv.second));
  }
  auto result_case = item.RawResultCase();
  if (result_case == MixRecoResult::ResultCase::kAdDsp ||
      result_case == MixRecoResult::ResultCase::kAdFansTop) {
    json_builder.AddField("price_adjust_ratio",
      std::to_string(MixRecoUtil::GetAdMixedInfo(item.GetPtr())->price_adjust_ratio()));
  }
  return json_builder.json_string();
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, HotMixRankLeafShowObserver, HotMixRankLeafShowObserver);

}  // namespace platform
}  // namespace ks
