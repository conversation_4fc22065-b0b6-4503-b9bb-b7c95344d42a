#pragma once
#include <algorithm>
#include <map>
#include <memory>
#include <string>
#include <utility>
#include <vector>
#include "folly/container/F14Map.h"

#include "dragon/src/processor/ext/ks_mix_rank/base/mix_rank_pool.h"
#include "dragon/src/processor/ext/ks_mix_rank/util/mix_reco_util.h"
#include "dragon/src/util/logging_util.h"
#include "ks/reco_proto/mix_rank/mix_rank.pb.h"
#include "ks/reco_proto/proto/eyeshot/mix/eyeshot_fountain_mix_meta_log.pb.h"
#include "learning/kuiba/proto/common_sample_log.pb.h"

namespace ks {
namespace platform {

using MixRecoResult = mix::kuaishou::reco::MixRecoResult;
using AdMixedInfo = mix::kuaishou::ad::AdMixedInfo;
using AdQueueType = mix::kuaishou::ad::AdEnum_AdQueueType;
using RecoPhoto = mix::kuaishou::reco::RecoPhoto;
using RecoLiveStream = mix::kuaishou::reco::RecoLiveStream;
using DspAdInfo = mix::kuaishou::ad::DspAdInfo;
using UserCardInfo = mix::kuaishou::reco::RecoUserCard;
using PhotoCardInfo = mix::kuaishou::reco::RecoPhotoCard;
using MaterialCardInfo = mix::kuaishou::reco::RecoMaterialCard;
using FansTopAdInfo = mix::kuaishou::ad::FansTopAdInfo;
using MixRecoResultSource = mix::kuaishou::reco::MixRecoResultSource;
using AggrCardType = mix::kuaishou::aggrcard::AggrCardType;
using FeedInjectionPriority = mix::kuaishou::reco::FeedInjectionPriority;
using MixFilterReason = ::kuaishou::reco::MixFilterReason;
using MixUnifyBid = ::mix::kuaishou::reco::MixUnifyBid;
using FountainMixFilterReason = mix::kuaishou::reco::FountainMixFilterReason;

enum class FixedItemImportace : int {
  SERVER_INJECTION = 0,
  AD,
  RECO,

  UNKNOWN  //保持最后
};

enum class MixRankApiStatus : int {
  INVALID_INDEX = -2,      // 下标范围错误
  PERMISSION_DENIED = -1,  // 权限错误
  OK = 0,

  UNKNOWN  //保持最后
};

enum class BonusCpmTag : int {
  // 该枚举迁自 ad 代码, 混排 auction 时只会使用到这一个枚举值, 故先只加上这个
  EXT_MERCHNAT_DELETE_BONUS_TAG = 81,  // 商家外投清理 bonus
};

enum SortTag {
  Brand = 0,
  NewGame = 1,
  FIRSTN = 2,
  KPerN = 3,     // 起量工具 N 保 K 策略；heqian, yeziqing
  Retarget = 4,  // 用户在次留等场景下强制 retarget
  MaxTag = 1024
};

enum SampleType {
  UNKNOWN_ITEM_TYPE = 0,
  RECO_PHOTO = 1,
  RECO_LIVE = 2,
  RECO_PHOTO_WITH_LIVE = 3,
  AD_PHOTO = 4,
  AD_LIVE = 5,
  FANSTOP_PHOTO = 6,
  FANSTOP_LIVE = 7
};

// pointwise 模型预估体验分
struct CtrRankPxtr {
  kuaishou::reco::CtrRankPxtr pxtr_;
  folly::F14FastMap<std::string, float> preds_;
  const kuaishou::reco::CtrRankPxtr &Pxtr() const {
    return pxtr_;
  }
  void Set(const folly::F14FastMap<std::string, float> &preds) {
    for (const auto &entry : preds) {
      const std::string &pred_name = entry.first;
      if (pred_name == "pvtr") {
        pxtr_.set_pvtr(entry.second);
      } else if (pred_name == "pevtr") {
        pxtr_.set_pevtr(entry.second);
      } else if (pred_name == "plvtr") {
        pxtr_.set_plvtr(entry.second);
      } else if (pred_name == "psvtr") {
        pxtr_.set_psvtr(entry.second);
      } else if (pred_name == "pnext") {
        pxtr_.set_pnext(entry.second);
      }
      preds_[pred_name] = entry.second;
    }
  }
  float Get(const std::string &pred_name) const {
    auto iter = preds_.find(pred_name);
    if (iter != preds_.end()) {
      return iter->second;
    }
    return 0.0f;
  }
};

// UeScore 模型预估体验分
struct UeScorePxtr {
  folly::F14FastMap<std::string, std::pair<double, double>> photo_preds_;
  folly::F14FastMap<std::string, std::pair<double, double>> live_preds_;
  folly::F14FastMap<std::string, std::pair<double, double>> &GetMutable(bool is_live) {
    return is_live ? live_preds_ : photo_preds_;
  }
  const folly::F14FastMap<std::string, std::pair<double, double>> &Get(bool is_live) const {
    return is_live ? live_preds_ : photo_preds_;
  }
  void SetPhoto(const ::mix::kuaishou::newsmodel::RankResult &rank_result, bool is_nebula) {
    folly::F14FastMap<std::string, double> pred_map;
    pred_map["pltr"] = rank_result.pltr();
    pred_map["pwtr"] = rank_result.pwtr();
    pred_map["pftr"] = rank_result.pftr();
    pred_map["pcmtr"] = rank_result.pcmtr();
    pred_map["phtr"] = rank_result.phtr();
    pred_map["plvtr"] = rank_result.plvtr();
    pred_map["psvr"] = rank_result.psvr();
    pred_map["pcpr"] = rank_result.pcpr();
    pred_map["pvtr"] = rank_result.pvtr();
    SetPhoto(pred_map, is_nebula);
  }
  void SetPhoto(const folly::F14FastMap<std::string, double> &pred_map, bool is_nebula) {
    photo_preds_["pltr"] = std::make_pair(0.0, -1.0);
    photo_preds_["pwtr"] = std::make_pair(0.0, -1.0);
    photo_preds_["pftr"] = std::make_pair(0.0, -1.0);
    photo_preds_["pcmtr"] = std::make_pair(0.0, -1.0);
    photo_preds_["phtr"] = std::make_pair(0.0, -1.0);
    photo_preds_["plvtr"] = std::make_pair(0.0, -1.0);
    photo_preds_["psvr"] = std::make_pair(0.0, -1.0);
    photo_preds_["pcpr"] = std::make_pair(0.0, -1.0);
    photo_preds_["pwatchtime"] = std::make_pair(0.0, -1.0);
    for (const auto &entry : pred_map) {
      const std::string &name = entry.first;
      double pxtr = entry.second;
      if (pxtr <= 0.0 || pxtr >= 1.0) continue;
      if (name == "pvtr") {
        pxtr = (is_nebula ? 3.0 : 1.0) * pxtr / (1 - pxtr);
        photo_preds_["pwatchtime"].first = pxtr;
      } else if (photo_preds_.count(name) && name != "pwatchtime") {
        if (name == "pwtr") {
          pxtr = pxtr / (8 + (1 - 8) * pxtr);
        } else if (name == "pcmtr" || name == "pftr") {
          pxtr = pxtr / (10 + (1 - 10) * pxtr);
        }
        photo_preds_[name].first = pxtr;
      }
    }
  }
  void SetLive(const folly::F14FastMap<std::string, double> &pred_map) {
    folly::F14FastMap<std::string, double> pxtr_map(pred_map);
    live_preds_["ltr"] = std::make_pair(pxtr_map["ltr"], -1.0);
    live_preds_["wtr"] = std::make_pair(pxtr_map["wtr"], -1.0);
    live_preds_["htr"] = std::make_pair(pxtr_map["htr"], -1.0);
    live_preds_["ctr"] = std::make_pair(pxtr_map["ctr"], -1.0);
    live_preds_["etr"] = std::make_pair(pxtr_map["etr"], -1.0);
    live_preds_["lvtr"] = std::make_pair(pxtr_map["lvtr"], -1.0);
    live_preds_["in_etr"] = std::make_pair(pxtr_map["in_etr"], -1.0);
    live_preds_["in_lvtr"] = std::make_pair(pxtr_map["in_lvtr"], -1.0);
  }
};

struct HotPxtrInfo {
  folly::F14FastMap<std::string, std::pair<double, double>> pxtr_info_;
  folly::F14FastMap<std::string, double> item_info_;
  folly::F14FastMap<std::string, std::pair<double, double>> &GetMutablePxtrInfo() {
    return pxtr_info_;
  }
  folly::F14FastMap<std::string, double> &GetMutableItemInfo() {
    return item_info_;
  }
  const folly::F14FastMap<std::string, std::pair<double, double>> &GetPxtrInfo() const {
    return pxtr_info_;
  }
  const folly::F14FastMap<std::string, double> &GetItemInfo() const {
    return item_info_;
  }
  // 商业化暂定这样的传输形式，商业化视频和直播一并传输
  void SetPhoto(const ::mix::kuaishou::ad::AdMixedInfo * ad_mixed_info) {
    // 商业化传的乘了 10 的 6 次方
    pxtr_info_["cpm"] = std::make_pair(
      std::max(static_cast<double>(ad_mixed_info->cpm()) / 1000000.0, 0.0), -1.0);
    pxtr_info_["bonus"] = std::make_pair(
      std::max(static_cast<double>(ad_mixed_info->cpm_bonus()) / 1000000.0, 0.0), -1.0);
    pxtr_info_["gpm"] = std::make_pair(
      std::max(static_cast<double>(ad_mixed_info->gpm()) / 1000000.0, 0.0), -1.0);
    pxtr_info_["ltr"] = std::make_pair(std::max(static_cast<double>(ad_mixed_info->ltr()), 0.0), -1.0);
    pxtr_info_["wtr"] = std::make_pair(std::max(static_cast<double>(ad_mixed_info->wtr()), 0.0), -1.0);
    pxtr_info_["ftr"] = std::make_pair(std::max(static_cast<double>(ad_mixed_info->ftr()), 0.0), -1.0);
    pxtr_info_["cmtr"] = std::make_pair(std::max(static_cast<double>(ad_mixed_info->cmtr()), 0.0), -1.0);
    pxtr_info_["lvtr"] = std::make_pair(std::max(static_cast<double>(ad_mixed_info->lvtr()), 0.0), -1.0);
    pxtr_info_["play7s"] = std::make_pair(std::max(static_cast<double>(ad_mixed_info->play7s()), 0.0), -1.0);
    pxtr_info_["svr"] = std::make_pair(std::max(static_cast<double>(ad_mixed_info->svr()), 0.0), -1.0);
    pxtr_info_["htr"] = std::make_pair(std::max(static_cast<double>(ad_mixed_info->htr()), 0.0), -1.0);
    pxtr_info_["ad_play_time"] = std::make_pair(
      std::max(static_cast<double>(ad_mixed_info->ad_play_time()), 0.0), -1.0);
  }

  void SetLive(const ::mix::kuiba::PredictItem& pred_item) {
    for (int i = 0; i < pred_item.attr_size(); ++i) {
      const ::mix::kuiba::SampleAttr &attr = pred_item.attr(i);
      pxtr_info_[attr.name()] = std::make_pair(std::max(static_cast<double>(attr.float_value()), 0.0), -1.0);
    }
  }

  void SetRecoPhoto(const mix::kuaishou::newsmodel::RankResult& rank_result) {
    // key 与双列样本表 ks_origin_reco_arch_log.joint_reco_log_parse 对应
    pxtr_info_["pctr"] = std::make_pair(std::max(static_cast<double>(rank_result.pctr()), 0.0), -1.0);
    pxtr_info_["pwtr"] = std::make_pair(std::max(static_cast<double>(rank_result.pwtr()), 0.0), -1.0);
    pxtr_info_["pltr"] = std::make_pair(std::max(static_cast<double>(rank_result.pltr()), 0.0), -1.0);
    pxtr_info_["pptr"] = std::make_pair(std::max(static_cast<double>(rank_result.pptr()), 0.0), -1.0);
    pxtr_info_["phtr"] = std::make_pair(std::max(static_cast<double>(rank_result.phtr()), 0.0), -1.0);
    pxtr_info_["pcmtr"] = std::make_pair(std::max(static_cast<double>(rank_result.pcmtr()), 0.0), -1.0);
    pxtr_info_["pvtr"] = std::make_pair(std::max(static_cast<double>(rank_result.pvtr()), 0.0), -1.0);
    pxtr_info_["plvtr"] = std::make_pair(std::max(static_cast<double>(rank_result.plvtr()), 0.0), -1.0);
    pxtr_info_["pcmef"] = std::make_pair(std::max(static_cast<double>(rank_result.pcmef()), 0.0), -1.0);
    pxtr_info_["pepstr"] = std::make_pair(std::max(static_cast<double>(rank_result.pepstr()), 0.0), -1.0);
    pxtr_info_["plstr"] = std::make_pair(std::max(static_cast<double>(rank_result.plstr()), 0.0), -1.0);
    pxtr_info_["pcpr"] = std::make_pair(std::max(static_cast<double>(rank_result.pcpr()), 0.0), -1.0);
    pxtr_info_["pcltr"] = std::make_pair(std::max(static_cast<double>(rank_result.pcltr()), 0.0), -1.0);
    pxtr_info_["pftr"] = std::make_pair(std::max(static_cast<double>(rank_result.pftr()), 0.0), -1.0);
    pxtr_info_["psvtr"] = std::make_pair(std::max(static_cast<double>(rank_result.psvr()), 0.0), -1.0);
    pxtr_info_["pwtd_score"] = std::make_pair(std::max(static_cast<double>(rank_result.pwtd()), 0.0), -1.0);
  }

  void SetHotAdPhoto(const ::mix::kuaishou::ad::AdMixedInfo * ad_mixed_info) {
    // 商业化传的乘了 10 的 6 次方
    pxtr_info_["cpm"] = std::make_pair(
      std::max(static_cast<double>(ad_mixed_info->cpm()) / 1000000.0, 0.0), -1.0);
    pxtr_info_["bonus"] = std::make_pair(
      std::max(static_cast<double>(ad_mixed_info->cpm_bonus()) / 1000000.0, 0.0), -1.0);
    pxtr_info_["gpm"] = std::make_pair(
      std::max(static_cast<double>(ad_mixed_info->gpm()) / 1000000.0, 0.0), -1.0);
    pxtr_info_["ctr"] = std::make_pair(
      std::max(ad_mixed_info->feed_pctr(), 0.0), -1.0);
    pxtr_info_["ltr"] = std::make_pair(std::max(static_cast<double>(ad_mixed_info->ltr()), 0.0), -1.0);
    pxtr_info_["wtr"] = std::make_pair(std::max(static_cast<double>(ad_mixed_info->wtr()), 0.0), -1.0);
    pxtr_info_["ftr"] = std::make_pair(std::max(static_cast<double>(ad_mixed_info->ftr()), 0.0), -1.0);
    pxtr_info_["cmtr"] = std::make_pair(std::max(static_cast<double>(ad_mixed_info->cmtr()), 0.0), -1.0);
    pxtr_info_["lvtr"] = std::make_pair(std::max(static_cast<double>(ad_mixed_info->lvtr()), 0.0), -1.0);
    pxtr_info_["play7s"] = std::make_pair(std::max(static_cast<double>(ad_mixed_info->play7s()), 0.0), -1.0);
    pxtr_info_["svr"] = std::make_pair(std::max(static_cast<double>(ad_mixed_info->svr()), 0.0), -1.0);
    pxtr_info_["htr"] = std::make_pair(std::max(static_cast<double>(ad_mixed_info->htr()), 0.0), -1.0);
    pxtr_info_["ad_play_time"] = std::make_pair(
      std::max(static_cast<double>(ad_mixed_info->ad_play_time()), 0.0), -1.0);
  }

  void SetHotLive(const ::mix::kuiba::PredictItem& pred_item) {
    for (int i = 0; i < pred_item.attr_size(); ++i) {
      const ::mix::kuiba::SampleAttr &attr = pred_item.attr(i);
      pxtr_info_[attr.name()] = std::make_pair(std::max(static_cast<double>(attr.float_value()), 0.0), -1.0);
    }
  }

  void SetHotRecoPhoto(const mix::kuaishou::newsmodel::RankResult& rank_result) {
    // key 与双列样本表 ks_origin_reco_arch_log.joint_reco_log_parse 对应
    pxtr_info_["pctr"] = std::make_pair(std::max(static_cast<double>(rank_result.pctr()), 0.0), -1.0);
    pxtr_info_["pwtr"] = std::make_pair(std::max(static_cast<double>(rank_result.pwtr()), 0.0), -1.0);
    pxtr_info_["pltr"] = std::make_pair(std::max(static_cast<double>(rank_result.pltr()), 0.0), -1.0);
    pxtr_info_["pptr"] = std::make_pair(std::max(static_cast<double>(rank_result.pptr()), 0.0), -1.0);
    pxtr_info_["phtr"] = std::make_pair(std::max(static_cast<double>(rank_result.phtr()), 0.0), -1.0);
    pxtr_info_["pcmtr"] = std::make_pair(std::max(static_cast<double>(rank_result.pcmtr()), 0.0), -1.0);
    pxtr_info_["pvtr"] = std::make_pair(std::max(static_cast<double>(rank_result.pvtr()), 0.0), -1.0);
    pxtr_info_["plvtr"] = std::make_pair(std::max(static_cast<double>(rank_result.plvtr()), 0.0), -1.0);
    pxtr_info_["pcmef"] = std::make_pair(std::max(static_cast<double>(rank_result.pcmef()), 0.0), -1.0);
    pxtr_info_["pepstr"] = std::make_pair(std::max(static_cast<double>(rank_result.pepstr()), 0.0), -1.0);
    pxtr_info_["pftr"] = std::make_pair(std::max(static_cast<double>(rank_result.pftr()), 0.0), -1.0);
    pxtr_info_["pwtd"] =
      std::make_pair(std::max(static_cast<double>(rank_result.pwtd()), 0.0), -1.0);
    pxtr_info_["pwatchtime"] =
      std::make_pair(std::max(static_cast<double>(rank_result.pwatchtime()), 0.0), -1.0);
    pxtr_info_["pfr_score1"] =
      std::make_pair(std::max(static_cast<double>(rank_result.pfr_score1()), 0.0), -1.0);
    pxtr_info_["pfr_score2"] =
      std::make_pair(std::max(static_cast<double>(rank_result.pfr_score2()), 0.0), -1.0);
  }

  void SetItemInfo(const std::string& key, const double& value) {
    item_info_[key] = value;
  }
};

struct IndexPhotoInfo {
  bool is_initial = false;
  int level_hot_online = -1;
  int audit_hot_high_tag_level = -1;
  int topk_audit_level = -1;
  int audit_b_second_tag = -1;
  int plc_business_type = -1;
  int is_living = -1;
  bool IsInitial() const {
    return is_initial;
  }
  int PlcType() const {
    return plc_business_type;
  }
  bool IsHighQuality() const {
    return level_hot_online >= 3 && audit_hot_high_tag_level >= 3 &&
        (topk_audit_level >= 3 || topk_audit_level <= 0);
  }
  bool IsNativeAd() const {
    return audit_b_second_tag == 2096384;
  }
  bool IsPlc() const {
    return plc_business_type > 0;
  }
  bool IsLiving() const {
    return is_living > 0;
  }
  uint64 CoverMask() const {
    uint64 cover_mask = 0;
    cover_mask |= IsHighQuality();
    cover_mask |= IsPlc() << 1;
    cover_mask |= IsLiving() << 2;
    cover_mask |= (level_hot_online >= 0) << 3;
    cover_mask |= (audit_hot_high_tag_level >= 0) << 4;
    cover_mask |= (topk_audit_level >= 0) << 5;
    cover_mask |= (plc_business_type >= 0) << 6;
    cover_mask |= (is_living >= 0) << 7;
    cover_mask |= IsNativeAd() << 8;
    return cover_mask;
  }
};

struct MixRankAuctionInfo {
  int64 price = 0;
  int64 price_undiscount = 0;
  int64 price_feed = 0;
  int64 price_slide = 0;
  int64 next_benifit = 0;
  int64 next_cpm = 0;
  int64 bonus_cpm = 0;
  int64 bonus_cpm_tag = 0;
  int64 auction_bid = 0;
  int64 cpm = 0;
  int64 rank_benifit = 0;
  int64 photo_quality_score = 0;
  SortTag sort_tag = SortTag::MaxTag;
  ::mix::kuaishou::ad::AdEnum_BidType bid_type = ::mix::kuaishou::ad::AdEnum_BidType_UNKNOWN_BID_TYPE;
  ::mix::kuaishou::ad::AdEnum_AdTpAudienceType audience_type =
      ::mix::kuaishou::ad::AdEnum_AdTpAudienceType_UNKNOWN_TP_AUDIENCE_TYPE;
  int64 cpm_thr = 0;
  int64 ad_strategy_tag = 0;
};

enum LowActiveExpVersion : int { EXPV1, EXPV2, BASE };

// MixRecoResult 的包装类，将 RecoPhoto / Ad / Live 等返回结果归一到同一结构体
class MixRankResult {
 public:
  // (NOTICE): 按照约定，冷启动、关系链等插件结果，会在这个构造函数将其 one of 的类型转换成 RecoPhoto
  // 转换后, 其原 one of 类型无意义如 ptr->relation_reco_photo()
  explicit MixRankResult(MixRecoResult *ptr);

 public:
  // (NOTICE): 以下是获取 MixRecoResult 转换后的 one of Message 类型，应该成对使用，避免取到无效的指针
  // (NOTICE): 以下是获取 MixRecoResult 转换后的 one of Message 类型，应该成对使用，避免取到无效的指针
  // HasXxxx 和 GetXxxx 请成对新增
  bool HasRecoPhoto() const {
    return has_reco_photo;
  }
  const RecoPhoto *GetRecoPhoto() const {
    if (!HasRecoPhoto()) {
      return nullptr;
    }
    return ptr_->mutable_reco_photo();
  }

  bool HasLiveStream() const {
    return result_case == MixRecoResult::ResultCase::kRecoLivestream;
  }
  const RecoLiveStream *GetLiveStream() const {
    if (!HasLiveStream()) {
      return nullptr;
    }
    return ptr_->mutable_reco_livestream();
  }

  // (NOTICE): 粉丝头条 V2 也在 DspAdInfo, 业务上可以用 MixRecoUtil::IsDsp / IsFansTop 区分！
  bool HasAdDspInfo() const {
    return result_case == MixRecoResult::ResultCase::kAdDsp;
  }
  const DspAdInfo *GetAdDspInfo() const {
    if (!HasAdDspInfo()) {
      return nullptr;
    }
    return ptr_->mutable_ad_dsp();
  }

  DspAdInfo *GetMutableAdDspInfo() const {
    if (!HasAdDspInfo()) {
      return nullptr;
    }
    return ptr_->mutable_ad_dsp();
  }

  // (NOTICE): 同上，由于 v2，使用时注意这个接口并不代表业务上的粉丝头条。
  bool HasFansTopInfo() const {
    return result_case == MixRecoResult::ResultCase::kAdFansTop;
  }
  bool HasMerchantCardInfo() const {
    return result_case == MixRecoResult::ResultCase::kMerchantCard;
  }
  bool HasNpsSurveyCardInfo() const {
    return result_case == MixRecoResult::ResultCase::kNpsSurveyCard;
  }
  bool HasUgActivityCardInfo() const {
    return result_case == MixRecoResult::ResultCase::kUgActivityCard;
  }
  bool HasRocketLiveStreamInfo() const {
    return result_case == MixRecoResult::ResultCase::kRocketLivestream;
  }
  bool HasMerchantRecoShowcaseInfo() const {
    return result_case == MixRecoResult::ResultCase::kMerchantRecoShowcase;
  }
  bool HasRecoMerchantPhotoInfo() const {
    return result_case == MixRecoResult::ResultCase::kRecoMerchantPhoto;
  }
  bool HasRecoLivePhotoInfo() const {
    return result_case == MixRecoResult::ResultCase::kRecoLivePhoto;
  }
  bool HasRecoHotspotPhotoInfo() const {
    return result_case == MixRecoResult::ResultCase::kRecoHotspotPhoto;
  }
  bool HasRevenueLiveStreamInfo() const {
    return result_case == MixRecoResult::ResultCase::kRevenueLivestream;
  }
  bool HasRelationRecoPhoto() const {
    return result_case == MixRecoResult::ResultCase::kRelationRecoPhoto;
  }
  const FansTopAdInfo *GetFansTopInfo() const {
    if (!HasFansTopInfo()) {
      return nullptr;
    }
    return ptr_->mutable_ad_fans_top();
  }
  bool HasUserCardInfo() const {
    return result_case == MixRecoResult::ResultCase::kRecoUserCard;
  }
  const UserCardInfo *GetUserCard() const {
    if (!HasUserCardInfo()) {
      return nullptr;
    }
    return ptr_->mutable_reco_user_card();
  }

  bool HasPhotoCardInfo() const {
    return result_case == MixRecoResult::ResultCase::kRecoPhotoCard;
  }
  const PhotoCardInfo *GetPhotoCard() const {
    if (!HasPhotoCardInfo()) {
      return nullptr;
    }
    return ptr_->mutable_reco_photo_card();
  }

  bool HasMaterialCardInfo() const {
    return result_case == MixRecoResult::ResultCase::kRecoMagicFaceMaterialCard;
  }
  const MaterialCardInfo *GetMaterialCard() const {
    if (!HasMaterialCardInfo()) {
      return nullptr;
    }
    return ptr_->mutable_reco_magic_face_material_card();
  }

  // 不建议使用此接口!!! 可能会取到无效的 message
  const MixRecoResult *GetPtr() const {
    return ptr_;
  }

  MixRecoResult *GetMutablePtr() {
    return ptr_;
  }

 public:
  // (NOTICE): 按照上边构造函数注释所说，有些插件的结果会转换成 RecoPhoto
  // 这里的 RawResultCase 记录的是原本的 ResultCase 而非转换后的 ResultCase::RecoPhoto
  MixRecoResult::ResultCase RawResultCase() const {
    return result_case;
  }

  bool IsCommercialResult() const {
    return result_case == MixRecoResult::ResultCase::kAdDsp ||
           result_case == MixRecoResult::ResultCase::kAdFansTop;
  }

  bool IsCommercialResultExcludeInnerFansTop() const {
    const ::mix::kuaishou::ad::AdMixedInfo *ad_mixed_info = MixRecoUtil::GetAdMixedInfo(ptr_);
    return ad_mixed_info != nullptr &&
           ad_mixed_info->fans_top_type() != ::mix::kuaishou::ad::AdMixedInfo::INNER_FANSTOP;
  }

  bool IsAdsDsp() const {
    const ::mix::kuaishou::ad::AdMixedInfo *ad_mixed_info = MixRecoUtil::GetAdMixedInfo(ptr_);
    return ad_mixed_info != nullptr &&
           ad_mixed_info->ad_mixed_source_type() == ::mix::kuaishou::ad::AdMixedInfo::AD_DSP;
  }

  bool IsGeneralOuterFansTop() const {
    return MixRecoUtil::IsGeneralOuterFansTop(ptr_);
  }

  bool IsFakeSoftDsp() const {
    return MixRecoUtil::IsFakeSoftDsp(ptr_);
  }

  bool RecoPhotoFromRandom() const {
    return result_case == MixRecoResult::ResultCase::kRandomRecoPhoto;
  }

  bool IsLocalLifePhoto() const {
    return result_case == MixRecoResult::ResultCase::kSildeLocalLifePhoto ||
          (result_case == MixRecoResult::ResultCase::kRandomRecoPhoto &&
              stid_mix_value.substr(0, 2) == "7_");
  }

  bool RecoPhotoFromLeaf() const {
    return result_case == MixRecoResult::ResultCase::kRecoPhoto;
  }

  bool RecoPhotoFromReleation() const {
    return result_case == MixRecoResult::ResultCase::kRelationRecoPhoto;
  }

  bool IsSearchRelatedPhoto() const {
    return result_case == MixRecoResult::ResultCase::kRecoSearchRelatedPhoto;
  }

  bool IsUserInterestExplorePhoto() const {
    return result_case == MixRecoResult::ResultCase::kInterestRecoPhoto;
  }

  bool IsProduceHotPhoto() const {
    return result_case == MixRecoResult::ResultCase::kProduceHotPhoto;
  }

  int GetFanstopType() const {
    if (MixRecoUtil::IsInnerFansTop(ptr_)) return 1;
    if (MixRecoUtil::IsNormalFansTopOrSoftAds(ptr_)) return 2;
    return 0;
  }

  int GetMerchantShowCaseFollow() const {
    if (HasMerchantRecoShowcaseInfo()) {
      return MixRecoUtil::GetMerchantShowCaseFollow(ptr_);
    }
    return -1;
  }

  int GetMerchantShowCaseExpand() const {
    if (HasMerchantRecoShowcaseInfo()) {
      return MixRecoUtil::GetMerchantShowCaseExpand(ptr_);
    }
    return -1;
  }

  int GetMerchantShowCaseHighValueUser() const {
    if (HasMerchantRecoShowcaseInfo()) {
      return MixRecoUtil::GetMerchantShowCaseHighValueUser(ptr_);
    }
    return -1;
  }

  int GetMerchantShowCaseSubFlow() const {
    if (HasMerchantRecoShowcaseInfo()) {
      return MixRecoUtil::GetMerchantShowCaseSubFlow(ptr_);
    }
    return -1;
  }

  std::string GetMerchantLiveBuyerType() const {
    if (HasMerchantRecoShowcaseInfo()) {
      return MixRecoUtil::GetMerchantLiveBuyerType(ptr_);
    }
    return "U0";
  }

  float GetMerchantShowCaseBonus() const {
    if (HasMerchantRecoShowcaseInfo()) {
      return MixRecoUtil::GetMerchantShowCaseBonus(ptr_);
    }
    return 0.0;
  }

  int GetMerchantShowCaseFresh() const {
    if (HasMerchantRecoShowcaseInfo()) {
      return MixRecoUtil::GetMerchantShowCaseFresh(ptr_);
    }
    return -1;
  }

 public:
  // MixRankResult 混排处理过程中的公共属性

  // 约定以 photo_id 字段作为先知上报的 key，各个插件结果都应设置 photo_id 字段
  uint64 PhotoId() const {
    return photo_id;
  }

  // 内部 key，每个输入的结果都有不变唯一的标示
  // 0 - 3 ad 广告类型标示，4 - 15 result_case 标示, 16 - 63 后 48 位 PhotoId
  uint64 MixKey() const {
    uint64 type = static_cast<uint64>(RawResultCase());
    uint64 ad_type = static_cast<uint64>(RaWAdQueueType());
    return ad_type << 60 | type << 48 | item_id;
  }

  // 短视频 photo_id, 直播 livestream_id
  uint64 ItemId() const {
    return item_id;
  }

  // 是否是直播展现形式
  bool IsLiveStyle() const {
    return is_live_style;
  }

  // 是否是强插
  bool IsFeedInject() const {
    return is_feed_inject;
  }

  void SetIsFeedInject(bool is_feed_inject) {
    this->is_feed_inject = is_feed_inject;
  }

  // 是否有广告标
  bool DisableAdMark() const {
    return disable_ad_mark;
  }

  // 是否有 live 头像视频
  bool IsLivingPhoto() const {
    return index_photo_.IsLiving();
  }

  // 是否有 PLC 视频
  bool IsPlcPhoto() const {
    return index_photo_.IsPlc();
  }

  // 是否是优质视频
  bool IsHighQualityPhoto() const {
    return is_high_quality || index_photo_.IsHighQuality();
  }

  void SetHighQualityPhoto(bool is_high_quality) {
    this->is_high_quality = is_high_quality;
  }

  // 是否是社区标准的原生广告
  bool IsNativePhoto() const {
    return index_photo_.IsNativeAd();
  }

  // 视频 PlcType
  int PlcTypeEnum() const {
    return index_photo_.PlcType();
  }

  void SetIndexPhoto(const IndexPhotoInfo &index_photo) {
    index_photo_ = index_photo;
  }

  const IndexPhotoInfo &IndexPhoto() const {
    return index_photo_;
  }

  uint64 AuthorId() const {
    return author_id;
  }

  int32 Reason() const {
    return reason;
  }

  int ItemIndex() const {
    return item_index;
  }

  void SetItemIndex(int index) {
    item_index = index;
  }

  int RawAdPos() const {
    return raw_ad_pos;
  }

  int RawAdSourceType() const {
    return raw_ad_type;
  }

  int RaWAdQueueType() const {
    return raw_ad_queue_type;
  }

  double UserValue() const {
    return user_value;
  }

  void SetUserValue(double user_value) {
    this->user_value = user_value;
  }

  double OrganicScore() const {
    return organic_score;
  }

  void SetOrganicScore(double organic_score) {
    this->organic_score = organic_score;
  }

  double BusinessValue() const {
    return business_value;
  }

  void SetBusinessValue(double business_value) {
    this->business_value = business_value;
  }

  UeScorePxtr &GetMutableUeScorePxtr() {
    return ue_score_pxtr_;
  }

  const UeScorePxtr &GetUeScorePxtr() const {
    return ue_score_pxtr_;
  }

  const CtrRankPxtr &GetCtrRankPxtr() const {
    return ctr_rank_pxtr;
  }

  HotPxtrInfo &GetMutableHotPxtrInfo() {
    return hot_pxtr_info_;
  }

  const HotPxtrInfo &GetHotPxtrInfo() const {
    return hot_pxtr_info_;
  }

  void SetCtrRankPxtr(const folly::F14FastMap<std::string, float> &preds) {
    ctr_rank_pxtr.Set(preds);
  }

  double RecoMixScore() const {
    return reco_mix_score_;
  }

  void SetRecoMixScore(double reco_mix_score) {
    this->reco_mix_score_ = reco_mix_score;
  }

  double PredUeq() const {
    return pred_ueq;
  }

  void SetPredUeq(double pred_ueq) {
    this->pred_ueq = pred_ueq;
  }

  double RawBizValue() const {
    return raw_biz_value;
  }

  void SetRawBizValue(double raw_biz_value) {
    this->raw_biz_value = raw_biz_value;
  }

  double CalibrateRawBizValue() const {
    return calibrate_raw_biz_value;
  }

  float PctrScore() const {
    return pctr_score;
  }

  void SetPctrScore(float pctr_score) {
    this->pctr_score = pctr_score;
  }

  float PctrWeight() const {
    return pctr_weight;
  }

  void SetPctrWeight(float weight) {
    this->pctr_weight = weight;
  }

  float EcoWeight() const {
    return eco_weight;
  }

  void SetEcoWeight(float weight) {
    this->eco_weight = weight;
  }

  float EcoBias() const {
    return eco_bias;
  }

  void SetEcoBias(float bias) {
    this->eco_bias = bias;
  }

  void SetCalibrateRawBizValue(double calibrate_raw_biz_value) {
    this->calibrate_raw_biz_value = calibrate_raw_biz_value;
  }

  float Pvtr() const {
    return pvtr;
  }

  void SetPvtr(float pvtr) {
    this->pvtr = pvtr;
  }

  double Ecpm() const {
    return ecpm;
  }

  void SetEcpm(double ecpm) {
    this->ecpm = ecpm;
  }

  double TargetEcpm() const {
    return target_ecpm;
  }

  void SetTargetEcpm(double target_ecpm) {
    this->target_ecpm = target_ecpm;
  }

  double SctrCorrectRatio() const {
    return sctr_correct_ratio;
  }

  void SetSctrCorrectRatio(double sctr_correct_ratio) {
    this->sctr_correct_ratio = sctr_correct_ratio;
  }

  int PhotoType() const {
    return photo_type;
  }

  void SetPhotoType(int photo_type) {
    this->photo_type = photo_type;
  }

  int PermItemType() const {
    return perm_item_type;
  }

  void SetPermItemType(int perm_item_type) {
    this->perm_item_type = perm_item_type;
  }

  int QueueIndex() const {
    return queue_index;
  }

  void SetQueueIndex(int queue_index) {
    this->queue_index = queue_index;
  }

  int ItemType() const {
    return item_type;
  }

  void SetItemType(int item_type) {
    this->item_type = item_type;
  }

  double BidFrac() const {
    return bid_frac;
  }

  void SetBidFrac(double bid_frac) {
    this->bid_frac = bid_frac;
  }

  double LiftValue() const {
    return lift_value;
  }

  void SetLiftValue(double lift_value) {
    this->lift_value = lift_value;
  }

  int ShowPos() const {
    return show_pos;
  }

  void SetShowPos(int show_pos) {
    this->show_pos = show_pos;
  }

  double FinalScoreIdx() const {
    return final_score_idx;
  }

  void SetFinalScoreIdx(double final_score_idx) {
    this->final_score_idx = final_score_idx;
  }

  double EcologyValue() const {
    return ecology_value;
  }

  void SetEcologyValue(double ecology_value) {
    this->ecology_value = ecology_value;
  }

  // 大混排统计竞价基础函数
  double UniverseBidScore() const {
    return universe_bid_score;
  }

  void SetUniverseBidScore(double universe_bid_score) {
    this->universe_bid_score = universe_bid_score;
  }

  double UniverseEgpmAdMerchantMerge() const {
    return universe_egpm_ad_merchant_merge;
  }

  void SetUniverseEgpmAdMerchantMerge(double universe_egpm_ad_merchant_merge) {
    this->universe_egpm_ad_merchant_merge = universe_egpm_ad_merchant_merge;
  }

  double UniverseBonusAdMerchantMerge() const {
    return universe_bonus_ad_merchant_merge;
  }

  void SetUniverseBonusAdMerchantMerge(double universe_bonus_ad_merchant_merge) {
    this->universe_bonus_ad_merchant_merge = universe_bonus_ad_merchant_merge;
  }

  double UniverseAuctionScore() const {
    return universe_auction_score;
  }

  void SetUniverseAuctionScore(double universe_auction_score) {
    this->universe_auction_score = universe_auction_score;
  }

  void SetCalibGpm(double calib_gpm) {
    this->calib_gpm = calib_gpm;
  }

  void SetCalibCpm(double calib_cpm) {
    this->calib_cpm = calib_cpm;
  }

  void SetCalibGift(double calib_gift) {
    this->calib_gift = calib_gift;
  }

  void SetWholeGpm(double whole_gpm) {
    this->whole_gpm = whole_gpm;
  }

  void SetWholeCpm(double whole_cpm) {
    this->whole_cpm = whole_cpm;
  }

  void SetWholeGift(double whole_gift) {
    this->whole_gift = whole_gift;
  }

  void SetWholeUescore(double whole_uescore) {
    this->whole_uescore = whole_uescore;
  }

  void SetRestrictBonus(double restrict_bonus) {
    this->restrict_bonus = restrict_bonus;
  }

  double GetCalibGpm() const {
    return calib_gpm;
  }

  double GetCalibCpm() const {
    return calib_cpm;
  }

  double GetCalibGift() const {
    return calib_gift;
  }

  double GetWholeGpm() const {
    return whole_gpm;
  }

  double GetWholeCpm() const {
    return whole_cpm;
  }

  double GetWholeGift() const {
    return whole_gift;
  }

  double GetWholeUescore() const {
    return whole_uescore;
  }

  double GetRestrictBonus() const {
    return restrict_bonus;
  }

  double FullEcomScore() const {
    return full_ecom_score;
  }

  void SetFullEcomScore(double full_ecom_score) {
    this->full_ecom_score = full_ecom_score;
  }

  double RawBidScore() const {
    return raw_bid_score;
  }

  void SetRawBidScore(double raw_bid_score) {
    this->raw_bid_score = raw_bid_score;
  }

  double RawBidScoreRerank() const {
    return raw_bid_score_rerank;
  }

  void SetRawBidScoreRerank(double raw_bid_score_rerank) {
    this->raw_bid_score_rerank = raw_bid_score_rerank;
  }

  double UniverseAuctionWeight() {
    return universe_auction_weight;
  }

  void SetUniverseAuctionWeight(double universe_auction_weight) {
    this->universe_auction_weight = universe_auction_weight;
  }

  double UniverseAuctionBias() {
    return universe_auction_bias;
  }

  void SetUniverseAuctionBias(double universe_auction_bias) {
    this->universe_auction_bias = universe_auction_bias;
  }

  double UniverseAlphaWeight() {
    return universe_alpha_weight;
  }

  void SetUniverseAlphaWeight(double universe_alpha_weight) {
    this->universe_alpha_weight = universe_alpha_weight;
  }

  double UniverseGspPrice() {
    return universe_gsp_price;
  }

  void SetUniverseGspPrice(double universe_gsp_price) {
    this->universe_gsp_price = universe_gsp_price;
  }

  double AdGspBid() {
    return ad_gsp_bid;
  }

  void SetAdGspBid(double ad_gsp_bid) {
    this->ad_gsp_bid = ad_gsp_bid;
  }

  double UniverseGfpPrice() {
    return universe_gfp_price;
  }

  void SetUniverseGfpPrice(double universe_gfp_price) {
    this->universe_gfp_price = universe_gfp_price;
  }

  double AdmodelCorrectRatio() const {
    return ad_model_correct_ratio;
  }

  void SetAdmodelCorrectRatio(double ad_model_correct_ratio) {
    this->ad_model_correct_ratio = ad_model_correct_ratio;
  }

  double AdmodelCorrectRatio2() const {
    return ad_model_correct_ratio2;
  }

  void SetAdmodelCorrectRatio2(double ad_model_correct_ratio2) {
    this->ad_model_correct_ratio2 = ad_model_correct_ratio2;
  }

  double ResortRankBenifit() const {
    return resort_rank_benifit;
  }
  void SetResortRankBenifit(double resort_rank_benifit) {
    this->resort_rank_benifit = resort_rank_benifit;
  }

  double UniverseFinalScore() const {
    return universe_final_score;
  }

  double NormalizeUniverseFinalScore() const {
    return normalize_universe_final_score;
  }

  void SetUniverseFinalScore(double universe_final_score) {
    this->universe_final_score = universe_final_score;
  }

  void SetNormalizeUniverseFinalScore(double universe_final_score) {
    this->normalize_universe_final_score = universe_final_score;
  }

  void SetUniverseDspFinalScore(double universe_dsp_final_score) {
    this->universe_dsp_final_score = universe_dsp_final_score;
  }

  double UniverseDspFinalScore() const {
    return universe_dsp_final_score;
  }

  double RecoMixFinalScore() const {
    return reco_mix_final_score;
  }

  void SetRecoMixFinalScore(double reco_mix_final_score) {
    this->reco_mix_final_score = reco_mix_final_score;
  }

  double ResortFinalScore() const {
    return resort_final_score;
  }

  void SetResortFinalScore(double resort_final_score) {
    this->resort_final_score = resort_final_score;
  }

  double FinalScore() const {
    return final_score;
  }

  void SetFinalScore(double final_score) {
    this->final_score = final_score;
  }

  float RerankPvtr() const {
    return rerank_pvtr;
  }

  void SetRerankPvtr(float rerank_pvtr) {
    this->rerank_pvtr = rerank_pvtr;
  }

  float RerankMerchantLiveCtr() const {
    return rerank_merchant_live_ctr;
  }

  void SetRerankMerchantLiveCtr(float rerank_merchant_live_ctr) {
    this->rerank_merchant_live_ctr = rerank_merchant_live_ctr;
  }

  float RerankMerchantLiveCvr() const {
    return rerank_merchant_live_cvr;
  }

  void SetRerankMerchantLiveCvr(float rerank_merchant_live_cvr) {
    this->rerank_merchant_live_cvr = rerank_merchant_live_cvr;
  }

  float RerankMerchantLivePrice() const {
    return rerank_merchant_live_price;
  }

  void SetRerankMerchantLivePrice(float rerank_merchant_live_price) {
    this->rerank_merchant_live_price = rerank_merchant_live_price;
  }

  float RerankMerchantLiveEgpm() const {
    return rerank_merchant_live_egpm;
  }

  void SetRerankMerchantLiveEgpm(float rerank_merchant_live_egpm) {
    this->rerank_merchant_live_egpm = rerank_merchant_live_egpm;
  }

  std::string RerankSeqIdxMerchantLivePos() const {
    return rerank_seq_idx_mer_pos;
  }

  void SetRerankSeqIdxMerchantLivePos(const std::string &seq_idx_mer_pos) {
    this->rerank_seq_idx_mer_pos = seq_idx_mer_pos;
  }

  std::string ScoreFunctionName() const {
    return score_function_name;
  }

  void SetScoreFunctionName(std::string score_func) {
    this->score_function_name = score_func;
  }

  // 短视频直播混排的 origin_score 与 final_score
  double PhotoLiveOriginScore() const {
    return photo_live_origin_score;
  }

  void SetPhotoLiveOriginScore(double photo_live_origin_score) {
    this->photo_live_origin_score = photo_live_origin_score;
  }

  double PhotoLiveFinalScore() const {
    return photo_live_final_score;
  }

  void SetPhotoLiveFinalScore(double photo_live_final_score) {
    this->photo_live_final_score = photo_live_final_score;
  }

  double LtrModelScore() const {
    return ltr_model_score;
  }

  void SetLtrModelScore(double model_score) {
    this->ltr_model_score = model_score;
  }

  double AuctionScore() const {
    return auction_score;
  }

  void SetAuctionScore(double auction_score) {
    this->auction_score = auction_score;
  }

  bool IsMarketingPhoto() const {
    return is_marketing_photo;
  }
  void SetIsMarketingPhoto(bool is_marketing_photo) {
    this->is_marketing_photo = is_marketing_photo;
  }

  double UserExperienceScore() const {
    return user_experience_score;
  }

  void SetUserExperienceScore(double ue_socre) {
    this->user_experience_score = ue_socre;
  }

  double AccumulateUEScore() const {
    return accumulate_ue_score;
  }

  void SetAccumulateUEScore(double accumulate_ue_score) {
    this->accumulate_ue_score = accumulate_ue_score;
  }

  std::string StidMixValue() const {
    return stid_mix_value;
  }

  void SetStidMixValue(std::string stid_mix_value) {
    this->stid_mix_value = stid_mix_value;
  }

  void SetIsHardAd(bool use_new_standard, bool old_standard, bool new_standard) {
    if (use_new_standard) {
      this->is_hard_ad = new_standard;
    } else {
      this->is_hard_ad = old_standard;
    }
  }

  bool IsHardAd() const {
    return this->is_hard_ad;
  }

  void SetIsSoftAd(bool is_soft_ad) { this->is_soft_ad = is_soft_ad; }

  bool IsSoftAd() const { return this->is_soft_ad; }

  void SetIsNoTagAd(bool is_no_tag_ad) { this->is_no_tag_ad = is_no_tag_ad; }

  bool IsNoTagAd() const { return this->is_no_tag_ad; }

  // 具有强位置需求的结果设置此字段，首位设置为 0。在兜底 Processor 会做位置的检查、调整
  void SetFixedPos(int pos, FixedItemImportace item_importance = FixedItemImportace::AD) {
    fixed_pos = pos;
    fixed_importance = item_importance;
  }

  int FixedPos() const {
    return fixed_pos;
  }

  bool IsUnknownResultCase() const {
    return is_unknown_result_case;
  }

  void SetFilterReason(MixFilterReason filter_reason) {
    this->filter_reason = filter_reason;
  }

  MixFilterReason FilterReason() const {
    return filter_reason;
  }

  bool IsFiltered() const {
    return filter_reason != MixFilterReason::DEFAULT_NO_FILTER;
  }

  void SetFountainFilterReason(FountainMixFilterReason filter_reason) {
    this->fountain_filter_reason = filter_reason;
  }

  FountainMixFilterReason FountainFilterReason() const {
    return fountain_filter_reason;
  }

  void SetIsExtendItem(int is_extend_item) {
    this->is_extend_item = is_extend_item;
  }

  int IsExtendItem() const {
    return is_extend_item;
  }

  void SetIsRecallItem(bool flag) {
    is_recall_item = flag;
  }

  int IsRecallItem() const {
    return is_recall_item;
  }

  void SetIsLocalLifeLive(bool flag) {
    is_local_life_live = flag;
  }

  bool IsLocalLifeLive() {
    return is_local_life_live;
  }

  void SetPlcBusinessType(int business_type) {
    plc_business_type = business_type;
  }

  int PlcBusinessType() const {
    return plc_business_type;
  }

  void SetIsNoFirstLive(bool flag) {
    is_no_first_live = flag;
  }

  bool IsNoFirstLive() {
    return is_no_first_live;
  }

  void SetIsAllowFirstLive(bool flag) {
    is_allow_first_live = flag;
  }

  bool IsAllowFirstLive() {
    return is_allow_first_live;
  }

  float MerchantLiveBonus() const {
    return merchant_live_bonus;
  }

  void SetMerchantLiveBonus(float merchant_live_bonus) {
    this->merchant_live_bonus = merchant_live_bonus;
  }

  int64 ItemMappingPsKeyId() const {
    // 获得广告在 ps 打分的实际 id, 目前仅针对 ad_source_type = DSP 类型, 对应打分 id 就是 creative_id
    // 不请求模型的广告, 映射出来的 ps_key 都是 -1, 后面会统一过滤掉
    if (IsAdsDsp() || GetAdDspInfo() != nullptr) {
      return GetAdDspInfo()->ad_result().ad_deliver_info().ad_base_info().creative_id();
    } else {
      return -1;
    }
  }

  void GetUnifyBidFromLeaf(const MixUnifyBid* unify_bid_ptr);

  void WritePublicLiveCpmToProto(double public_live_cpm);

  void WriteAdPosInfoToProto(size_t real_pos);

  void WriteDeliveryCpm(double weight);

  void SetResultSourceForServerInjection(const MixRecoResultSource &source);

  void SetIsLeaderGr(bool is_leader_gr) {
    this->is_leader_gr = is_leader_gr;
  }
  bool IsLeaderGr() const {
    return is_leader_gr;
  }
  void SetIsNewInnerFanstop(bool is_new_inner_fanstop) {
    this->is_new_inner_fanstop = is_new_inner_fanstop;
  }
  bool IsNewInnerFanstop() const {
    return is_new_inner_fanstop;
  }
  // 把当前 MixRank Item 级别信息托管到框架
  void DumpMixRankItemAttrSnapshot(google::protobuf::RepeatedPtrField<kuiba::SampleAttr> *output) const;

  MixRankAuctionInfo *GetAuctionInfo() {
    return &mix_rank_auction_info;
  }
  const MixRankAuctionInfo *GetAuctionInfo() const {
    return &mix_rank_auction_info;
  }
  void SetIsPlatoPhoto(bool flag) {
    this->is_plato_photo = flag;
  }
  bool IsPlatoPhoto() {
    return is_plato_photo;
  }
  bool IsFansTop() const {
    return is_fans_top;
  }
  const bool IsFountainFansTop() const {
    return ptr_ && MixRecoUtil::IsNormalFansTopOrSoftAds(ptr_);
  }
  const bool IsFountainDsp() const {
    return ptr_ && MixRecoUtil::IsDsp(ptr_);
  }
  const bool IsRecoLivestream() const {
    return ptr_ && MixRecoUtil::IsRecoLivestream(ptr_);
  }
  const int FountainResultCase() const {
    int fountain_result_case = result_case;
    if (this->IsFountainDsp()) {
      fountain_result_case = MixRecoResult::ResultCase::kAdDsp;
    } else if (this->IsFountainFansTop()) {
      fountain_result_case = MixRecoResult::ResultCase::kAdFansTop;
    }
    return fountain_result_case;
  }
  bool IsDsp() {
    return is_dsp;
  }
  bool IsAdGD() const {
    return this->is_ad_gd;
  }
  int GetAdGDPos() {
    return this->ad_gd_pos;
  }

  bool IsLeafLive() {
    return this->is_leaf_live;
  }

  bool IsMarketing() const {
    return this->is_marketing;
  }

  struct MixItemContextInfo {
    float pctr;
    float pvtr;
    float plvtr;
    float pltr;
    float pwtr;
    float pepstr;
    float plivingtr;
    int hetu_tag_level_one;
    float unify_ctr;
    float unify_cvr;
    int unify_cvr_start;
    int unify_cvr_end;
    int ocpc_action_type;
    int64 creative_id;
    int64 product_name;
    int64 first_industry_id;
    float unify_cvr_new;
    int unify_ctr_start;
    int unify_ctr_end;
    int ad_pos1;
    int ad_pos2;
    float unify_ctr_new;
    int64 original_price = 0;
    int64 new_price = 0;
  };
  MixItemContextInfo mix_item_context_info;

  struct UnifyBidInfo {
   public:
    // MTB 二期一阶段计算需要，开放 bonus set 方法
    void SetBonus(double score) {
      this->bonus = score;
    }

    // 在混排填充
    void SetUeScore(double score) {
      this->ue_score = score;
    }

    double GetGpm() {
      return this->gpm;
    }

    double GetCpm() {
      return this->cpm;
    }

    double GetGift() {
      return this->gift;
    }

    double GetUeScore() {
      return this->ue_score;
    }

    double GetBonus() {
      return this->bonus;
    }

   private:
    void SetGpm(double score) {
      this->gpm = score;
    }

    void SetCpm(double score) {
      this->cpm = score;
    }

    void SetGift(double score) {
      this->gift = score;
    }

   private:
    double gpm = 0.0;       // 统一打分 : gpm 营收因子
    double cpm = 0.0;       // 统一打分 : cpm 营收因子
    double gift = 0.0;      // 统一打分 : 打赏营收因子
    double ue_score = 0.0;  // 统一打分 : 体验因子
    double bonus = 0.0;     // 统一打分 : 主营收价值 bonus

    friend class MixRankResult;
  };
  UnifyBidInfo& GetUnifyBidInfo() {
    return this->unify_bid_info;
  }

 private:
  bool TransToRecoPhoto(RecoPhoto *reco_photo_ptr);
  bool TransToRecoLive(RecoLiveStream *reco_live_ptr);
  bool IsMarketingPhoto(MixRecoResult *result);
  void ParseStRecoIdAndSetStid(StidContainer stid_container);
  void GetAdGDInfo(const MixRecoResult* reco_ptr, bool* is_ad_gd, int* ad_gd_pos);
  void ProcessPlatoPhoto();
  std::string StidConvertName(std::string raw_stid) const;
  MixRecoResult *ptr_ = nullptr;
  bool has_reco_photo = false;
  bool is_unknown_result_case = false;

  MixRecoResult::ResultCase result_case = MixRecoResult::ResultCase::RESULT_NOT_SET;
  int raw_ad_pos = -1;
  // 插件结果的原始返回序，所有业务通用。从 0 开始
  int item_index = -1;
  AdMixedInfo::AdMixedSourceType raw_ad_type = AdMixedInfo::UNKNOW_SOURCE_TYPE;
  AdQueueType raw_ad_queue_type = AdQueueType::AdEnum_AdQueueType_UNKNOWN_AD_QUEUE_TYPE;
  int fixed_pos = -1;
  FixedItemImportace fixed_importance = FixedItemImportace::UNKNOWN;
  uint64 photo_id = 0;
  uint64 item_id = 0;
  // Gap 框架升级 : 交互样式字段
  bool is_live_style = false;
  bool is_feed_inject = false;
  bool disable_ad_mark = false;
  uint64 author_id = 0;
  int32 reason = 0;
  double universe_dsp_final_score = -1.0;
  double universe_final_score = 0.0;
  double normalize_universe_final_score = 0.0;
  double reco_mix_final_score = 0.0;
  double resort_final_score = 0.0;
  double final_score = 0.0;
  double ecology_value = 0.0;
  double organic_score = 0.0;
  double business_value = 0.0;
  float pctr_score = 0.0;
  float pctr_weight = 0.0;
  float eco_weight = 0.0;
  float eco_bias = 0.0;
  float rerank_pvtr = 0.0;
  float rerank_merchant_live_ctr = 0.0;
  float rerank_merchant_live_cvr = 0.0;
  float rerank_merchant_live_price = 0.0;
  float rerank_merchant_live_egpm = 0.0;
  std::string rerank_seq_idx_mer_pos = "";
  double raw_biz_value = 0.0;
  double calibrate_raw_biz_value = 0.0;
  double user_value = 1.0;
  double ecpm = 0.0;
  double target_ecpm = 0.0;
  int photo_type = 0;
  int item_type = 0;
  int perm_item_type = 0;
  int queue_index = 100;
  double bid_frac = 0.0;
  double lift_value = 0.0;
  int show_pos = 0;
  float pvtr = 0.0;
  double ltr_model_score = 0.0;
  double user_experience_score = 0.0;
  double accumulate_ue_score = 0.0;
  AggrCardType card_type = AggrCardType::UNKNOWN;
  // 混排竞价阶段需要用到的数据统一放到这个临时变量中, 竞价完成后统一填充数据到 pb message 中
  MixRankAuctionInfo mix_rank_auction_info;
  // UeScore 模型预估体验分
  UeScorePxtr ue_score_pxtr_;
  HotPxtrInfo hot_pxtr_info_;
  CtrRankPxtr ctr_rank_pxtr;
  double pred_ueq = 0.0;
  double reco_mix_score_ = 0.0;

  double photo_live_origin_score = -1.0;
  double photo_live_final_score = -1.0;
  int final_score_idx = 0.0;
  double auction_score = 0.0;
  bool is_marketing_photo = false;
  // score_function_name 分数计算逻辑结构题名称
  std::string score_function_name = "";
  double ad_model_correct_ratio = 1.0;
  double ad_model_correct_ratio2 = 1.0;
  double resort_rank_benifit = 0.0;
  double sctr_correct_ratio = 0.0;
  // 大混排竞价分
  double universe_bid_score = 0.0;                // 竞价分(兼容 混排电商和商业化队列合并)
  double universe_egpm_ad_merchant_merge = 0.0;   // 混排电商和商业化队列合并 电商 egpm
  double universe_bonus_ad_merchant_merge = 0.0;  // 混排电商和商业化队列合并 电商 bonus
  double raw_bid_score = 0.0;                     // 原始竞价分
  double raw_bid_score_rerank = 0.0;              // rerank 竞价分
  double universe_auction_score = 0.0;            // 竞价排序分
  double universe_auction_weight = 1.0;           // 竞价权重
  double universe_auction_bias = 0.0;             // 竞价偏置项
  double universe_alpha_weight = 1.0;             // 各个业务的兑换汇率
  double universe_gsp_price = 0.0;                // 二价计费
  double universe_gfp_price = 0.0;                // 一价计费
  double ad_gsp_bid = 0.0;                        // 广告二价成本核算
  double full_ecom_score = 0.0;                   // 泛电商队列排序分
  double calib_gpm = 0.0;                         // 校准后的 gpm
  double calib_cpm = 0.0;                         // 校准后的 cpm
  double calib_gift = 0.0;                       // 校准后的 gift
  double whole_gpm = 0.0;                         // 整体的 gpm
  double whole_cpm = 0.0;                         // 整体的 cpm
  double whole_gift = 0.0;                        // 整体的 gift
  double whole_uescore = 0.0;                     // 整体的 uescore
  double restrict_bonus = 0.0;                    // 约束后的 bonus
  bool is_hard_ad = false;
  bool is_soft_ad = false;
  bool is_no_tag_ad = false;
  // stid 流量标识
  std::string stid_mix_value = "";
  // 政府关系强插的孪生标识
  bool is_leader_gr = false;
  // 过滤原因
  MixFilterReason filter_reason = MixFilterReason::DEFAULT_NO_FILTER;
  // 双列内流混排过滤原因
  FountainMixFilterReason fountain_filter_reason = FountainMixFilterReason::DEFAULT_NO_FILTER;
  // =1 表示是 2 月 8 日以后新增的混排过滤数据
  int is_extend_item = 0;
  // 新内粉标识
  bool is_new_inner_fanstop = false;
  // plato 标识 （result_case 为 RECO_PHOTO)
  bool is_plato_photo = false;
  bool is_dsp = false;
  bool is_fans_top = false;
  // 索引信息
  IndexPhotoInfo index_photo_;
  bool is_ad_gd = false;  // 是否是保量广告
  int ad_gd_pos = -1;  // 保位置广告的位置
  bool is_leaf_live = false;  // 自然直播、直播 1pp、已关
  bool is_marketing = false;  // 是否是营销感内容
  bool is_high_quality = false;  // 是否为优质内容
  bool is_recall_item = false;  // 是否为回捞的内容
  bool is_local_life_live = false;  // 是否是本地生活直播
  bool is_no_first_live = false;  // 是否是不允许出在首位的直播类型
  bool is_allow_first_live = false;  // 是否是允许出在首位的直播类型
  // 电商直播 rerank 存储 bonus
  float merchant_live_bonus = 0.0;
  UnifyBidInfo unify_bid_info;
  int plc_business_type = -1;
};

}  // namespace platform
}  // namespace ks
