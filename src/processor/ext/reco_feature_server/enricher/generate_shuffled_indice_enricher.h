#pragma once

#include <string>
#include <random>
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class GenerateShuffledIndiceEnricher : public CommonRecoBaseEnricher {
 public:
  GenerateShuffledIndiceEnricher() {
    std::random_device rd;
    random_generator_ = std::mt19937(rd());
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    attr_name_ = config()->GetString("attr_name", "");
    if (attr_name_.empty()) {
      LOG(ERROR) << "GenerateShuffledIndiceEnricher"
                 << " init failed! \"attr_name\" cannot be empty!";
      return false;
    }

    return true;
  }

 private:
  std::string attr_name_;
  std::mt19937 random_generator_;

  DISALLOW_COPY_AND_ASSIGN(GenerateShuffledIndiceEnricher);
};

}  // namespace platform
}  // namespace ks
