#include "dragon/src/processor/ext/reco_feature_server/enricher/unpack_colossus_user_author_statistic_attr_enricher.h"
#include <vector>
#include <cmath>

namespace ks {
namespace platform {

static const char kSeparator[] = "_";

bool UnpackColossusUserAuthorStatisticAttrEnricher::InitProcessor() {
  import_item_attr_name_ = config()->GetString("import_item_attr", "");
  if (import_item_attr_name_.empty()) {
    LOG(ERROR) << "UnpackColossusUserAuthorStatisticAttrEnricher"
               << " init failed! Empty import_item_attr";
    return false;
  }
  auto *suffix_config = config()->Get("export_attr_suffix_list");
  if (!suffix_config || !suffix_config->IsArray()) {
    LOG(ERROR) << "UnpackColossusUserAuthorStatisticAttrEnricher"
               << " init failed! Missing export_attr_suffix_list or"
               << " it is not an array";
    return false;
  }
  export_attr_list_.clear();
  for (const auto *suffix_json : suffix_config->array()) {
    if (suffix_json == nullptr) {
      LOG(ERROR) << "UnpackColossusUserAuthorStatisticAttrEnricher"
                 << " init failed! nullptr suffix_json";
      return false;
    }
    auto suffix_str = suffix_json->StringValue("");
    if (suffix_str.empty()) {
      LOG(ERROR) << "UnpackColossusUserAuthorStatisticAttrEnricher"
                 << " init failed! Exist empty-string in export_attr_suffix_list";
      return false;
    }
    auto export_attr_name = import_item_attr_name_ + kSeparator + suffix_str;
    export_attr_list_.push_back(export_attr_name);
  }
  export_attr_count_ = export_attr_list_.size();

  return true;
}

void UnpackColossusUserAuthorStatisticAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                                           RecoResultConstIter begin,
                                                           RecoResultConstIter end) {
  ItemAttr *import_attr_accessor = context->GetItemAttr(import_item_attr_name_);
  if (!import_attr_accessor) {
    for (const auto &attr_name : export_attr_list_) {
      auto attr_accessor = context->GetItemAttrAccessor(attr_name);
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        context->SetIntItemAttr(result, attr_accessor, 0);
      });
    }
    CL_LOG_WARNING_EVERY("null_import_attr", "UnpackColossusUserAuthorStatisticAttrEnricher", 10000)
        << "import_attr is null";
  }

  int64 size_miss_match_count = 0;
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    auto import_values = context->GetIntListItemAttr(result, import_attr_accessor);
    if (!import_values || !import_values.has_value() ||
        (import_values->size() != export_attr_count_)) {
      for (const auto &attr_name : export_attr_list_) {
        auto attr_accessor = context->GetItemAttrAccessor(attr_name);
        context->SetIntItemAttr(result, attr_accessor, 0);
      }
      ++size_miss_match_count;
      return;
    }

    for (int idx = 0; idx < export_attr_count_; ++idx) {
      const auto &attr_name = export_attr_list_[idx];
      auto attr_accessor = context->GetItemAttrAccessor(attr_name);
      // NOTE 当前固定计算方式，如有扩展需求可封装 inline
      auto value = static_cast<int64>(1000 * std::log(1 + 0.1 * (*import_values)[idx]));
      context->SetIntItemAttr(result, attr_accessor, value);
    }
  });

  if (size_miss_match_count) {
    std::string category = "size_miss_match";
    std::string outline = "UnpackColossusUserAuthorStatisticAttrEnricher";
    int freq = 10000;
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        size_miss_match_count, kPerfNs, "warning", ::ks::platform::GlobalHolder::GetServiceIdentifier(),
        ::ks::platform::GlobalHolder::GetCurrentRequestType(), outline, category);
    LOG_IF_EVERY_N(WARNING, LoggingUtil::IsLoggingEnabled(), freq)
        << "EVERY_" << freq << " | "
        << "item import_attr is null or size miss_match";
  }

  return;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, UnpackColossusUserAuthorStatisticAttrEnricher,
                 UnpackColossusUserAuthorStatisticAttrEnricher)
}  // namespace platform
}  // namespace ks
