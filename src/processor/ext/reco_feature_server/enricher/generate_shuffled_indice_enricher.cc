#include "dragon/src/processor/ext/reco_feature_server/enricher/generate_shuffled_indice_enricher.h"
#include <algorithm>
#include <vector>

namespace ks {
namespace platform {

void GenerateShuffledIndiceEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                            RecoResultConstIter end) {
  int num_items = std::distance(begin, end);
  std::vector<int64> indice_data(num_items);
  std::iota(indice_data.begin(), indice_data.end(), 0);
  std::shuffle(indice_data.begin(), indice_data.end(), random_generator_);
  int index = 0;
  auto attr_accessor = context->GetItemAttrAccessor(attr_name_);
  for (auto iter = begin; iter != end && index < num_items; ++iter, ++index) {
    context->SetIntItemAttr(*iter, attr_accessor, indice_data[index]);
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, GenerateShuffledIndiceEnricher, GenerateShuffledIndiceEnricher)

}  // namespace platform
}  // namespace ks
