#include "dragon/src/processor/ext/reco_feature_server/enricher/rounding_higest_digit_search_feature_enricher.h"
#include <vector>
#include <algorithm>

namespace ks {
namespace platform {

static const char kSeparator[] = "_";

bool RoundingHighestDigitSearchFeatureEnricher::InitProcessor() {
  import_item_attr_name_ = config()->GetString("import_item_attr", "");
  if (import_item_attr_name_.empty()) {
    LOG(ERROR) << "RoundingHighestDigitSearchFeatureEnricher"
               << " init failed! Empty import_item_attr";
    return false;
  }
  auto *prefix_config = config()->Get("export_attr_prefix_list");
  if (!prefix_config || !prefix_config->IsArray()) {
    LOG(ERROR) << "RoundingHighestDigitSearchFeatureEnricher"
               << " init failed! Missing export_attr_prefix_list or"
               << " it is not an array";
    return false;
  }
  auto *suffix_config = config()->Get("export_attr_suffix_list");
  if (!suffix_config || !suffix_config->IsArray()) {
    LOG(ERROR) << "RoundingHighestDigitSearchFeatureEnricher"
               << " init failed! Missing export_attr_suffix_list or"
               << " it is not an array";
    return false;
  }
  export_attr_list_.clear();
  for (const auto *suffix_json : suffix_config->array()) {
    if (suffix_json == nullptr) {
      LOG(ERROR) << "RoundingHighestDigitSearchFeatureEnricher"
                 << " init failed! nullptr suffix_json";
      return false;
    }
    auto suffix_str = suffix_json->StringValue("");
    if (suffix_str.empty()) {
      LOG(ERROR) << "RoundingHighestDigitSearchFeatureEnricher"
                 << " init failed! Exist empty-string in export_attr_suffix_list";
      return false;
    }
    for (const auto *prefix_json : prefix_config->array()) {
      if (prefix_json == nullptr) {
        LOG(ERROR) << "RoundingHighestDigitSearchFeatureEnricher"
                   << " init failed! nullptr prefix_json";
        return false;
      }
      auto prefix_str = prefix_json->StringValue("");
      if (prefix_str.empty()) {
        LOG(ERROR) << "RoundingHighestDigitSearchFeatureEnricher"
                   << " init failed! Exist empty-string in export_attr_prefix_list";
        return false;
      }
      auto export_attr_name = prefix_str + kSeparator + suffix_str;
      export_attr_list_.push_back(export_attr_name);
    }
  }
  export_attr_count_ = export_attr_list_.size();

  return true;
}

// 获取 int value 十进制最高位并舍去尾数
inline static int64 GetRoundingHighestDigit(int64 input) {
  if (input <= 0) return 0;
  if (input < 10) return 1;
  int64 pow_weight = 0;
  while (input/10) {
    input = input / 10;
    ++pow_weight;
  }
  return input * std::pow(10, pow_weight);
}

inline static void PerfIntervalAndLogging(int64 interval_value,
                                          const std::string &category,
                                          const std::string &msg) {
  if (interval_value) {
    std::string outline = "RoundingHighestDigitSearchFeatureEnricher";
    int freq = 10000;
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        interval_value, kPerfNs, "warning", ::ks::platform::GlobalHolder::GetServiceIdentifier(),
        ::ks::platform::GlobalHolder::GetCurrentRequestType(), outline, category);
    LOG_IF_EVERY_N(WARNING, LoggingUtil::IsLoggingEnabled(), freq)
        << "EVERY_" << freq << " | "
        << msg;
  }
}

void RoundingHighestDigitSearchFeatureEnricher::Enrich(MutableRecoContextInterface *context,
                                                       RecoResultConstIter begin,
                                                       RecoResultConstIter end) {
  ItemAttr *import_attr_accessor = context->GetItemAttr(import_item_attr_name_);
  if (!import_attr_accessor) {
    for (const auto &attr_name : export_attr_list_) {
      auto attr_accessor = context->GetItemAttrAccessor(attr_name);
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        context->SetIntItemAttr(result, attr_accessor, 0);
      });
    }
    CL_LOG_WARNING_EVERY("null_import_attr", "RoundingHighestDigitSearchFeatureEnricher", 10000)
        << "import_attr is null";
  }

  int64 item_attr_null_count = 0;
  int64 size_miss_match_count = 0;
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    auto import_values = context->GetIntListItemAttr(result, import_attr_accessor);
    if (!import_values || !import_values.has_value()) {
      for (const auto &attr_name : export_attr_list_) {
        auto attr_accessor = context->GetItemAttrAccessor(attr_name);
        context->SetIntItemAttr(result, attr_accessor, 0);
      }
      ++item_attr_null_count;
      return;
    }

    auto import_attr_size = import_values->size();
    auto valid_attr_size = std::min(import_attr_size, export_attr_count_);
    for (int idx = 0; idx < valid_attr_size; ++idx) {
      const auto &attr_name = export_attr_list_[idx];
      auto attr_accessor = context->GetItemAttrAccessor(attr_name);
      auto value = GetRoundingHighestDigit((*import_values)[idx]);
      context->SetIntItemAttr(result, attr_accessor, value);
    }
    for (int idx = valid_attr_size; idx < export_attr_count_; ++idx) {
      const auto &attr_name = export_attr_list_[idx];
      auto attr_accessor = context->GetItemAttrAccessor(attr_name);
      context->SetIntItemAttr(result, attr_accessor, 0);
    }
    if (import_attr_size < export_attr_count_) {
      ++size_miss_match_count;
    }
  });

  PerfIntervalAndLogging(size_miss_match_count, "size_miss_match",
                         "size_miss_match");
  PerfIntervalAndLogging(item_attr_null_count, "item_attr_null_count",
                         "item_attr is null");

  return;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, RoundingHighestDigitSearchFeatureEnricher,
                 RoundingHighestDigitSearchFeatureEnricher)
}  // namespace platform
}  // namespace ks
