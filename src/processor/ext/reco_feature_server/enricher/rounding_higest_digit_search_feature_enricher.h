#pragma once

#include <string>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/core/common_reco_define.h"

namespace ks {
namespace platform {

class RoundingHighestDigitSearchFeatureEnricher : public CommonRecoBaseEnricher {
 public:
  RoundingHighestDigitSearchFeatureEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;

 private:
  std::string import_item_attr_name_;
  size_t export_attr_count_{0};
  std::vector<std::string> export_attr_list_;

  DISALLOW_COPY_AND_ASSIGN(RoundingHighestDigitSearchFeatureEnricher);
};

}  // namespace platform
}  // namespace ks
