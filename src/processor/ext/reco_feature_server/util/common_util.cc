#include "dragon/src/processor/ext/reco_feature_server/util/common_util.h"
#include <algorithm>
#include "third_party/abseil/absl/strings/str_split.h"
#include "third_party/abseil/absl/strings/numbers.h"

namespace ks {
namespace platform {

void SplitStringViewToIntVectorByChar(absl::string_view input_view,
                                      char delimiter,
                                      std::vector<int64>* result) {
  if (!result || delimiter == '\0') return;
  std::vector<absl::string_view> parts = absl::StrSplit(input_view, absl::ByChar(delimiter));
  result->clear();
  result->reserve(parts.size());
  for (auto element : parts) {
    if (element.empty()) {
      continue;
    }
    int64 int_val;
    if (absl::SimpleAtoi(element, &int_val)) {
      result->push_back(int_val);
    }
  }
}

}  // namespace platform
}  // namespace ks
