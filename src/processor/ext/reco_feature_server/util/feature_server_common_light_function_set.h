#pragma once

#include <cmath>
#include <algorithm>
#include <utility>
#include <string>
#include <vector>
#include "dragon/src/module/common_reco_light_function.h"
#include "ks/reco_proto/proto/reco.pb.h"
#include "third_party/abseil/absl/strings/str_split.h"
#include "third_party/abseil/absl/strings/numbers.h"
#include "dragon/src/processor/ext/reco_feature_server/util/common_util.h"

namespace ks {
namespace platform {

class FeatureServerCommonLightFunctionSet : public CommonRecoBaseLightFunctionSet {
 public:
  FeatureServerCommonLightFunctionSet() {
    REGISTER_LIGHT_FUNCTION(GetPhotoUploadCeilingHour);
    REGISTER_LIGHT_FUNCTION(GetPhotoAuthorIdFollowStatus);
    REGISTER_LIGHT_FUNCTION(GetPhotoDurationFloorSecond);
    REGISTER_LIGHT_FUNCTION(EnrichItemHetu);
    REGISTER_LIGHT_FUNCTION(GenBarrierItemNonRemoteFetchDone);
    REGISTER_LIGHT_FUNCTION(EmptyFunction);
    REGISTER_LIGHT_FUNCTION(GetSearchBubbleQuery);
    REGISTER_LIGHT_FUNCTION(SplitSearchBubbleQueryId);
    REGISTER_LIGHT_FUNCTION(TransformSearchBubbleQueryNumericAttr);
    REGISTER_LIGHT_FUNCTION(GetFRWtdBucket);
    REGISTER_LIGHT_FUNCTION(GetFRCascadePxtrIndexOut);
    REGISTER_LIGHT_FUNCTION(PreprocessFRCascadePxtr);
    REGISTER_LIGHT_FUNCTION(GetPhotoDurationCeilingSecond);
    REGISTER_LIGHT_FUNCTION(SetPageItemAttrForMio);
  }

  // 获取 photo 上传时间距离当前的小时数, 向上取整
  static bool GetPhotoUploadCeilingHour(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin,
                                        RecoResultConstIter end) {
    auto upload_time_accessor = context.GetIntItemAttr("upload_time");
    auto request_time = context.GetIntCommonAttr("request_time").value_or(0);
    auto photo_upload_celing_hour_accessor = context.SetIntItemAttr("photo_upload_ceiling_hour");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto upload_time_ptr = upload_time_accessor(result);
      if (upload_time_ptr) {
        uint64 upload_celing_hour = std::ceil((request_time - (*upload_time_ptr)) / 1000.0 / 3600);
        photo_upload_celing_hour_accessor(result, upload_celing_hour);
      }
    });
    return true;
  }

  // 获取 photo author_id 的关注状态: 1 已关, 2 双关
  static bool GetPhotoAuthorIdFollowStatus(const CommonRecoLightFunctionContext &context,
                                           RecoResultConstIter begin,
                                           RecoResultConstIter end) {
    auto author_id_accessor = context.GetIntItemAttr("author_id");
    auto follow_list = context.GetIntListCommonAttr("follow_list");
    auto bid_follow_list = context.GetIntListCommonAttr("bid_follow_list");
    auto follow_status_accessor = context.SetIntItemAttr("follow_status");

    folly::F14FastSet<int64> follow_list_set, bid_follow_list_set;
    if (follow_list) {
      follow_list_set.insert(follow_list->begin(), follow_list->end());
    }
    if (bid_follow_list) {
      bid_follow_list_set.insert(bid_follow_list->begin(), bid_follow_list->end());
    }

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      int follow_status = 0;
      int64 author_id = 0;
      auto author_id_ptr = author_id_accessor(result);
      if (author_id_ptr) {
        author_id = *author_id_ptr;
      }
      if (follow_list_set.count(author_id)) {
        follow_status = 1;
      }
      if (bid_follow_list_set.count(author_id)) {
        follow_status = 2;
      }
      follow_status_accessor(result, follow_status);
    });
    return true;
  }

  // 获取 photo duration seconds, 向下取整
  static bool GetPhotoDurationFloorSecond(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin,
                                          RecoResultConstIter end) {
    auto duration_ms_accessor = context.GetIntItemAttr("duration_ms");
    auto duration_second_accessor = context.SetIntItemAttr("duration_s");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto duration_ms_ptr = duration_ms_accessor(result);
      if (duration_ms_ptr) {
        auto duration_second = static_cast<int64>((*duration_ms_ptr) / 1000);
        duration_second_accessor(result, duration_second);
      }
    });
    return true;
  }

  static bool EnrichItemHetu(const CommonRecoLightFunctionContext &context,
                                RecoResultConstIter begin,
                                RecoResultConstIter end) {
    auto photo_info_accessor = context.GetProtoMessagePtrItemAttr<reco::PhotoInfo>("photo_info_ptr");

    auto hetu_one_setter = context.SetIntItemAttr("hetu_one");
    auto hetu_two_setter = context.SetIntItemAttr("hetu_two");
    auto hetu_one_v2_setter = context.SetIntItemAttr("hetu_one_v2");
    auto hetu_two_v2_setter = context.SetIntItemAttr("hetu_two_v2");

    for (auto iter = begin; iter != end; ++iter) {
      int64 hetu_one_val = -1;
      int64 hetu_two_val = -1;
      int64 hetu_one_val_v2 = 0;
      int64 hetu_two_val_v2 = 0;
      const auto *photo_info = photo_info_accessor(*iter);
      if (nullptr != photo_info) {
        if (photo_info->has_hetu_tag_level_info()) {
          if (photo_info->hetu_tag_level_info().hetu_level_one_size() > 0) {
            hetu_one_val = photo_info->hetu_tag_level_info().hetu_level_one(0);
            hetu_one_val_v2 = photo_info->hetu_tag_level_info().hetu_level_one(0);
          }
          if (photo_info->hetu_tag_level_info().hetu_level_two_size() > 0) {
            hetu_two_val = photo_info->hetu_tag_level_info().hetu_level_two(0);
            hetu_two_val_v2 = photo_info->hetu_tag_level_info().hetu_level_two(0);
          }
        }
      }
      hetu_one_setter(*iter, hetu_one_val);
      hetu_two_setter(*iter, hetu_two_val);
      hetu_one_v2_setter(*iter, hetu_one_val_v2);
      hetu_two_v2_setter(*iter, hetu_two_val_v2);
    }

    return true;
  }

  // 生成 DSL 编排需要的 barrier, 用于 item 特征抽取场景中 remote_fetch 后续处理的编排
  static bool GenBarrierItemNonRemoteFetchDone(const CommonRecoLightFunctionContext &context,
                                               RecoResultConstIter begin,
                                               RecoResultConstIter end) {
    // NOTE import attr 仅用于 DSL 构建，cpp 仅生成对应的 export_common_attr
    context.SetIntCommonAttr("_barrier_item_non_remote_fetch_done", 1);
    return true;
  }

  // 无代码逻辑，用于 DSL 依赖检查
  static bool EmptyFunction(const CommonRecoLightFunctionContext &context,
                            RecoResultConstIter begin,
                            RecoResultConstIter end) {
    return true;
  }

  // 基于 photo_info.search_bubble_query_id 获取 search_bubble_query
  static bool GetSearchBubbleQuery(const CommonRecoLightFunctionContext &context,
                                   RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    auto search_bubble_query_id_accessor = context.GetStringItemAttr("search_bubble_query_id");
    auto search_bubble_query_accessor = context.SetStringItemAttr("search_bubble_query");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto search_bubble_query_id_ptr = search_bubble_query_id_accessor(result);
      std::string search_query = "";
      if (search_bubble_query_id_ptr) {
        size_t pos = search_bubble_query_id_ptr->find_first_of("@#");
        if (pos != absl::string_view::npos) {
          search_query = std::string(search_bubble_query_id_ptr->substr(0, pos));
        }
      }
      search_bubble_query_accessor(result, std::move(search_query));
    });
    return true;
  }

  // 基于 photo_info.search_bubble_query_id, 按照指定字符拆分字符串并输出多个 item attr
  // int/double attr name 添加 _raw 后缀方便二次加工，其他 attr_name 对齐 DDP 配置
  static bool SplitSearchBubbleQueryId(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin,
                                       RecoResultConstIter end) {
    auto search_bubble_query_id_accessor = context.GetStringItemAttr("search_bubble_query_id");
    auto search_bubble_query_accessor = context.SetStringItemAttr("photo_search_bubble_query");
    auto search_bubble_query_top4_pid_accessor =
      context.SetIntListItemAttr("photo_search_bubble_query_top4_pid");
    auto search_bubble_query_term_accessor = context.SetStringListItemAttr("photo_search_bubble_query_term");
    auto search_bubble_query_term_time_accessor =
      context.SetIntListItemAttr("photo_search_bubble_query_dura");
    // int/double attr
    auto search_bubble_query_score_accessor = context.SetDoubleItemAttr("search_bubble_query_score_raw");
    auto search_bubble_query_show_count_accessor = context.SetIntItemAttr("search_bubble_query_show_cnt_raw");
    auto search_bubble_query_click_count_accessor =
      context.SetIntItemAttr("search_bubble_query_click_cnt_raw");
    auto search_bubble_query_ctr_accessor = context.SetDoubleItemAttr("search_bubble_query_ctr_raw");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto search_bubble_query_id_ptr = search_bubble_query_id_accessor(result);
      std::string search_query = "";
      double score = 0.0;
      int64 show_count = 0;
      int64 click_count = 0;
      double ctr = 0.0;
      std::vector<int64> top4_pid{};
      std::vector<std::string> query_term{};
      std::vector<int64> query_term_time{};

      if (search_bubble_query_id_ptr) {
        std::vector<absl::string_view> bubble_query_parts = absl::StrSplit(*search_bubble_query_id_ptr,
                                                                           absl::ByAnyChar("@#"));
        for (int idx = 0; idx < bubble_query_parts.size(); ++idx) {
          if (0 == idx) {
            search_query = std::string(bubble_query_parts[idx]);
          } else if (1 == idx) {
            std::vector<absl::string_view> numeric_parts = absl::StrSplit(bubble_query_parts[idx],
                                                                          absl::ByChar(';'));
            if (numeric_parts.size() == 4) {
              int64 int_val = 0;
              double double_val = 0;
              if (absl::SimpleAtod(numeric_parts[0], &double_val)) {
                score = double_val;
              }
              if (absl::SimpleAtoi(numeric_parts[1], &int_val)) {
                show_count = int_val;
              }
              if (absl::SimpleAtoi(numeric_parts[2], &int_val)) {
                click_count = int_val;
              }
              if (absl::SimpleAtod(numeric_parts[3], &double_val)) {
                ctr = double_val;
              }
            }
          } else if (2 == idx) {
            SplitStringViewToIntVectorByChar(bubble_query_parts[idx], ';', &top4_pid);
          } else if (3 == idx) {
            query_term = absl::StrSplit(bubble_query_parts[idx], absl::ByChar(';'));
          } else if (4 == idx) {
            SplitStringViewToIntVectorByChar(bubble_query_parts[idx], ';', &query_term_time);
          } else {
            break;
          }
        }
      }

      search_bubble_query_accessor(result, std::move(search_query));
      search_bubble_query_score_accessor(result, score);
      search_bubble_query_show_count_accessor(result, show_count);
      search_bubble_query_click_count_accessor(result, click_count);
      search_bubble_query_ctr_accessor(result, ctr);
      search_bubble_query_top4_pid_accessor(result, top4_pid);
      search_bubble_query_term_accessor(result, query_term);
      search_bubble_query_term_time_accessor(result, query_term_time);
    });
    return true;
  }

  // 针对 search_bubble_query 获取的数值型 attr 原始数据进行转换/二次加工
  // export attr_name 对齐 DDP 配置
  static bool TransformSearchBubbleQueryNumericAttr(const CommonRecoLightFunctionContext &context,
                                                    RecoResultConstIter begin,
                                                    RecoResultConstIter end) {
    auto raw_ctr_accessor = context.GetDoubleItemAttr("search_bubble_query_ctr_raw");
    auto raw_score_accessor = context.GetDoubleItemAttr("search_bubble_query_score_raw");
    auto raw_show_count_accessor = context.GetIntItemAttr("search_bubble_query_show_cnt_raw");
    auto raw_click_count_accessor = context.GetIntItemAttr("search_bubble_query_click_cnt_raw");
    auto export_ctr_accessor = context.SetIntItemAttr("photo_search_bubble_query_ctr");
    auto export_score_accessor = context.SetIntItemAttr("photo_search_bubble_query_score");
    auto export_show_count_accessor = context.SetIntItemAttr("photo_search_bubble_query_show_cnt");
    auto export_click_count_accessor = context.SetIntItemAttr("photo_search_bubble_query_click_cnt");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto ptr = raw_ctr_accessor(result);
      if (ptr) {
        export_ctr_accessor(result, static_cast<int64>(100 * (*ptr)));
      }
    });
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto ptr = raw_score_accessor(result);
      if (ptr) {
        export_score_accessor(result, static_cast<int64>(100 * (*ptr)));
      }
    });
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto ptr = raw_show_count_accessor(result);
      if (ptr) {
        export_show_count_accessor(result, std::min(static_cast<int64>((*ptr)/10), 200L));
      }
    });
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto ptr = raw_click_count_accessor(result);
      if (ptr) {
        export_click_count_accessor(result, std::min(static_cast<int64>((*ptr)/10), 200L));
      }
    });
    return true;
  }

  // FullRank 使用。基于 duration_ms 获取分桶数据：photo_fr_wtd_bucket, photo_fr_wtd_bucket_duration
  static bool GetFRWtdBucket(const CommonRecoLightFunctionContext &context,
                             RecoResultConstIter begin,
                             RecoResultConstIter end) {
    auto duration_ms_accessor = context.GetIntItemAttr("duration_ms");
    auto fr_wtd_bucket_accessor = context.SetIntItemAttr("photo_fr_wtd_bucket");
    auto fr_wtd_bucket_duration_accessor = context.SetIntItemAttr("photo_fr_wtd_bucket_duration");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto duration_ms_ptr = duration_ms_accessor(result);
      if (duration_ms_ptr) {
        int64 wtd_bucket = 0;
        int64 duration_second = std::ceil(static_cast<double>(*duration_ms_ptr) / 1000);
        duration_second = std::min(duration_second, 400L);
        int64 small_duration = std::ceil(static_cast<double>(*duration_ms_ptr) / 100);
        if (small_duration == 0) wtd_bucket = 0;
        else if (small_duration <= 87) wtd_bucket = 1;
        else if (small_duration <= 127) wtd_bucket = 2;
        else if (small_duration <= 203) wtd_bucket = 3;
        else if (small_duration <= 388) wtd_bucket = 4;
        else if (small_duration <= 718) wtd_bucket = 5;
        else if (small_duration <= 1182) wtd_bucket = 6;
        else if (small_duration <= 1950) wtd_bucket = 7;
        else
          wtd_bucket = 8;

        int64 wtd_bucket_duration = wtd_bucket << 10 | duration_second;
        fr_wtd_bucket_accessor(result, wtd_bucket);
        fr_wtd_bucket_duration_accessor(result, wtd_bucket_duration);
      }
    });
    return true;
  }

  // FullRank 使用。基于 cascade_pxtr_index 获取分桶数据：fr_cascade_pxtr_index_out
  static bool GetFRCascadePxtrIndexOut(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin,
                                       RecoResultConstIter end) {
    const std::vector<std::string> cascade_pxtr_index_attrs = {"cascade_pctr_index",
                                                               "cascade_plvtr_index",
                                                               "cascade_pvtr_index",
                                                               "cascade_pltr_index",
                                                               "cascade_pwtr_index",
                                                               "cascade_pftr_index"};
    for (const auto& input_attr : cascade_pxtr_index_attrs) {
      auto output_attr = "fr_" + input_attr + "_out";
      auto input_accessor = context.GetIntItemAttr(input_attr);
      auto output_accessor = context.SetIntItemAttr(output_attr);

      std::for_each(begin, end, [=](const CommonRecoResult &result) {
        auto input_attr_ptr = input_accessor(result);
        int64 bucket = 0;
        if (input_attr_ptr) {
          auto index_value = *input_attr_ptr;
          if (index_value <= 0) bucket = 0;
          else if (index_value < 30) bucket = static_cast<int64>(index_value/3) + 1;
          else if (index_value < 300) bucket = 10 + static_cast<int64>((index_value-30)/5) + 1;
          else if (index_value < 600) bucket = 64 + static_cast<int64>((index_value-300)/10) + 1;
          else if (index_value < 1000) bucket = 94 + static_cast<int64>((index_value-600)/50) + 1;
          else
            bucket = 110;
        }
        output_accessor(result, bucket);
      });
    }
    return true;
  }

  // FullRank 使用。预处理 cascade_pxtr 并输出 int attr
  static bool PreprocessFRCascadePxtr(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin,
                                      RecoResultConstIter end) {
    const std::vector<std::string> cascade_pxtr_attrs = {"cascade_pctr",
                                                         "cascade_plvtr",
                                                         "cascade_psvr",
                                                         "cascade_pvtr",
                                                         "cascade_pltr",
                                                         "cascade_pwtr",
                                                         "cascade_pftr",
                                                         "cascade_pptr",
                                                         "cascade_pepstr"};
    for (const auto& input_attr : cascade_pxtr_attrs) {
      auto output_attr = "fr_" + input_attr + "_out";
      auto input_accessor = context.GetDoubleItemAttr(input_attr);
      auto output_accessor = context.SetIntItemAttr(output_attr);

      std::for_each(begin, end, [=](const CommonRecoResult &result) {
        auto input_attr_ptr = input_accessor(result);
        int64 output_value = -1;
        if (input_attr_ptr && std::fabs(*input_attr_ptr) > 1e-6f) {
          output_value = static_cast<int64>((*input_attr_ptr) * 100);
        }
        output_accessor(result, output_value);
      });
    }
    return true;
  }

  // 获取 photo duration seconds, 向上取整
  static bool GetPhotoDurationCeilingSecond(const CommonRecoLightFunctionContext &context,
                                            RecoResultConstIter begin,
                                            RecoResultConstIter end) {
    auto duration_ms_accessor = context.GetIntItemAttr("duration_ms");
    auto duration_second_accessor = context.SetIntItemAttr("duration_ceiling_s");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto duration_ms_ptr = duration_ms_accessor(result);
      if (duration_ms_ptr) {
        auto duration_second = std::ceil((*duration_ms_ptr) / 1000.0);
        duration_second_accessor(result, duration_second);
      }
    });
    return true;
  }

  // mio 抽取算子使用。使用 common_attr page_common 赋值到 item_attr: _page, 算子默认值：1
  static bool SetPageItemAttrForMio(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto page_common = context.GetIntCommonAttr("page_common").value_or(1);
    auto page_item_accessor = context.SetIntItemAttr("_page");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      page_item_accessor(result, page_common);
    });
    return true;
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(FeatureServerCommonLightFunctionSet);
};

}  // namespace platform
}  // namespace ks
