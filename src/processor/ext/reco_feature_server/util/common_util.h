#pragma once

#include <vector>
#include <string>
#include "base/common/basic_types.h"
#include "third_party/abseil/absl/strings/string_view.h"

namespace ks {
namespace platform {

void SplitStringViewToIntVectorByChar(absl::string_view input_view,
                                      char delimiter,
                                      std::vector<int64>* result);

}  // namespace platform
}  // namespace ks
