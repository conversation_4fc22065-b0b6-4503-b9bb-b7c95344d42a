#pragma once

#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/util/task_register.h"
#include "ks/common_reco/ann_retrieve/server/ann_data.h"
#include "teams/reco-arch/colossusdb/partition_server/partition_lib/partition_lib_util.h"

namespace ks {
namespace platform {

class AnnPartitionData : public AnnData<float> {
 public:
  explicit AnnPartitionData(MetaParser *meta_parser) {
    meta_parser_ = meta_parser;
    if (meta_parser_ == nullptr) {
      LOG(INFO) << "default meta parser is null, will use meta parser for table";
    }
  }

  virtual ~AnnPartitionData();

  void SetValid(bool valid) override {
    // do nothing, valid flag is maintained by colossusdb itself
  }

  void SetAnnIndexReady(bool ready) {
    while (!partition_lib_->SetExternalDependencyReady(table_id_, ready)) {
      LOG(ERROR) << "SetExternalDependencyReady failed, this should not happen, retry after 5s...";
      absl::SleepFor(absl::Seconds(5));
    }
    LOG(INFO) << "SetExternalDependencyReady success, table_name: " << table_name_
              << ", table_id: " << table_id_ << ", ready: " << ready;
  }

  bool Valid() const override {
    return key_num() > 0;
  }

  uint64 key_num() const override {
    return partition_lib_->ItemCountRegardlessOfState(table_id_);
  }

  double max_item_num() const override {
    return partition_lib_->MaxItemCount(table_id_);
  }

  bool Get(uint64 key, uint64 *item_flag, std::vector<float> *v) const override {
    // item_flag is ignored
    return GetItem(key, v);
  }

  bool GetItem(uint64 key, std::vector<float> *item) const override;

  int GetItems(const std::vector<uint64> &keys, std::vector<const float*> *items, int *dim) const override;

  bool GetKeys(std::vector<uint64> *keys) const override;

  bool Update(uint64 key, uint64 item_flag, const std::vector<float> &v) override {
    CHECK(false) << "update is done inside colossusdb";
    return false;
  }

 protected:
  bool Initialize() override;

 private:
  int table_id_ = -1;
  std::string table_name_;
  colossusdb::ps::PartitionLib *partition_lib_ = nullptr;

  DISALLOW_COPY_AND_ASSIGN(AnnPartitionData);
};

}  // namespace platform
}  // namespace ks
