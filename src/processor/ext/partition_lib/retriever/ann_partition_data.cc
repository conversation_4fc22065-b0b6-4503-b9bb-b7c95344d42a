#include "dragon/src/processor/ext/partition_lib/retriever/ann_partition_data.h"

namespace ks {
namespace platform {

void StopAnnPartitionLib() {
  colossusdb::ps::StopPartitionLibServing();
}

DRAGON_REGISTER_TASK("wait_for_exit", StopAnnPartitionLib);

bool AnnPartitionData::Initialize() {
  if (!AnnData<float>::Initialize()) {
    LOG(ERROR) << "AnnData Initialize fail";
    return false;
  }

  partition_lib_ = colossusdb::ps::PartitionLibSingleton();
  CHECK(partition_lib_) << "init partition lib fail";

  CHECK(meta_parser_) << "meta parser is null, no default or custom meta parser";

  auto *kv_config = config()->Get("kv");
  CHECK(kv_config) << "kv_config is nullptr";
  table_name_ = kv_config->GetString("table_name", "");
  CHECK(!table_name_.empty()) << "table name is empty";
  table_id_ = partition_lib_->GetTableId(table_name_);

  int retry_times = 0;
  while (table_id_ < 0) {
    LOG(ERROR) << "table_name: " << table_name_
               << " not found in partition lib, wait 10s and retry, retry times: " << retry_times;
    std::this_thread::sleep_for(std::chrono::seconds(10));
    table_id_ = partition_lib_->GetTableId(table_name_);
  }

  // Set meta parser for consumer
  CHECK(partition_lib_->SetAnnMetaParser(table_id_, meta_parser_))
      << "SetAnnMetaParser failed, table_id: " << table_id_;

  while (true) {
    auto partitions = partition_lib_->GetPartitions(table_id_);
    if (partitions.empty()) {
      FB_LOG_EVERY_MS(INFO, 10000) << "No partitions found, waiting...";
      absl::SleepFor(absl::Seconds(3));
    } else {
      bool ready = true;
      for (auto &partition : partitions) {
        auto report_info = partition->GetReportInfo().get();
        if (!report_info.ready_to_promote) {
          FB_LOG_EVERY_MS(INFO, 30000)
              << "table_id: " << report_info.table_id << ", partition_id: " << report_info.partition_id
              << ", not ready to promote, waiting...";
          ready = false;
          absl::SleepFor(absl::Seconds(5));
          break;
        }
      }
      if (ready) {
        LOG(INFO) << "all partitions are ready";
        break;
      }
    }
  }
  return true;
}

AnnPartitionData::~AnnPartitionData() {
  colossusdb::ps::DeletePartitionLibSingleton();
}

bool AnnPartitionData::GetItem(uint64 key, std::vector<float> *item) const {
  auto s = partition_lib_->ReadRowRegardlessOfState(
      table_id_, key, [item](uint64_t key, const char *data, int32_t size) {
        if (!size || size % sizeof(float) != 0) {
          FB_LOG_EVERY_MS(WARNING, 1000) << "invalid data size, key: " << key << ", size: " << size;
          return;
        }
        auto cache = reinterpret_cast<const float *>(data);
        int num = size / sizeof(float);
        item->resize(num);
        memcpy(item->data(), cache, size);
      });

  if (!s.ok()) {
    FB_LOG_EVERY_MS(INFO, 10000) << "read row failed, key: " << key << ", status: " << s;
    return false;
  }
  return true;
}

// WARNING: value in items are not deep copied, please use it carefully
int AnnPartitionData::GetItems(const std::vector<uint64> &keys, std::vector<const float *> *items,
                               int *dim) const {
  if (items == nullptr) {
    FB_LOG_EVERY_MS(WARNING, 1000) << "items is null";
    return 0;
  }
  items->resize(keys.size());
  int success_num = 0;
  *dim = -1;
  partition_lib_->BatchReadRowRegardlessOfState(
      table_id_, keys, [items, dim, &success_num](size_t idx, uint64_t key, const char *data, int32_t size) {
        if (!size || size % sizeof(float) != 0) {
          FB_LOG_EVERY_MS(WARNING, 1000) << "invalid data size, key: " << key << ", size: " << size;
          return;
        }

        int num = size / sizeof(float);
        if (*dim > 0 && *dim != num) {
          FB_LOG_EVERY_MS(WARNING, 1000)
              << "data size mismatch, key: " << key << ", dim: " << num << ", last dim: " << *dim;
          return;
        }
        *dim = size / sizeof(float);
        (*items)[idx] = reinterpret_cast<const float *>(data);
        success_num++;
      });
  return success_num;
}

bool AnnPartitionData::GetKeys(std::vector<uint64> *keys) const {
  if (keys == nullptr) {
    FB_LOG_EVERY_MS(WARNING, 1000) << "keys is null";
    return false;
  }
  auto s = partition_lib_->GetKeysRegardlessOfState(table_id_, keys);
  if (!s.ok()) {
    FB_LOG_EVERY_MS(WARNING, 1000) << "get keys failed, status: " << s;
    return false;
  }
  return true;
}

using JsonFactoryClass = base::JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, annPartitionData, AnnPartitionData, MetaParser *)

}  // namespace platform
}  // namespace ks
