
#include <map>
#include <vector>
#include <string>
#include "dragon/src/processor/ext/livestream/enricher/livestream_ltvn_by_rodis_enricher.h"
// #include "teams/reco-arch/rodis/flat_generated/flat_generated_helper.h"
#include "ks/reco_proto/cofea/rodis/common_item.pb.h"

namespace ks {
namespace platform {

/*
  // 所有label回分n天
  // 样本中主播维度
  reward_amt_n                   // 用户总共打赏金额
  reward_times_n                 // 用户总共打赏次数

  like_amt_n                     // 用户总点赞次数
  comment_amt_n                  // 用户评论次数
  watch_live_time_n              // 用户观看直播时长
  simple_watch_live_time_n       // 用户观看简易直播间时长
  view_amt_n                     // 用户点击次数
  real_show_amt_n                // 用户曝光次数
  action_list_amt_n              // 用户推荐曝光

  // 所有主播维度
  total_reward_amt_n
  total_reward_times_n
  total_new_following_amt      // 新关注人数

  total_like_amt_n
  total_comment_amt_n
  total_watch_live_time_n
  total_simple_watch_live_time_n
  total_view_amt_n
  total_real_show_amt_n
  total_action_list_amt_n

  // 已关注主播维度
  following_total_reward_amt_n
  following_total_reward_times_n

  following_total_like_amt_n
  following_total_comment_amt_n
  following_total_watch_live_time_n
  following_total_simple_watch_live_time_n
  following_total_view_amt_n
  following_total_real_show_amt_n
  following_total_action_list_amt_n

*/

bool LiveStreamLtvnByRodisEnricher::InitProcessor() {
  rodis_resp_attr_ = config()->GetString("rodis_resp_attr", "");

  if (rodis_resp_attr_.empty()) {
    CL_LOG_EVERY_N(ERROR, 100) << "miss rodis_resp_attr_";
    return false;
  }

  author_id_attr_ = config()->GetString("author_id_attr", "author_id");
  count_days_ = config()->GetInt("count_days", 7);
  return true;
}

void LiveStreamLtvnByRodisEnricher::Enrich(MutableRecoContextInterface *context,
                                                             RecoResultConstIter begin,
                                                             RecoResultConstIter end) {
  timer_.Start();
  // get history live info from rodis_resp if return null
  std::string str_count_days = std::to_string(count_days_);
  const auto sim_labels = context->GetStringListCommonAttr(rodis_resp_attr_);
  if (!sim_labels || sim_labels->empty()) {
    for (auto it = begin; it != end; it = std::next(it)) {
      context->SetIntItemAttr(it->item_key, "following_time_" + str_count_days, 0);

      context->SetIntItemAttr(it->item_key, "reward_amt_" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "reward_times" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "like_amt" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "comment_amt" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "watch_live_time" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "simple_watch_live_time" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "view_amt" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "real_show_amt" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "action_list_amt" + str_count_days, 0);

      context->SetIntItemAttr(it->item_key, "view_live_room_amt" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "like_live_room_amt" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "comment_live_room_amt" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "reward_live_room_amt" + str_count_days, 0);

      // 拆分公域、私域： u-a 粒度
      context->SetIntItemAttr(it->item_key, "public_reward_amt_" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "public_reward_times" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "public_like_amt" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "public_comment_amt" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "public_watch_live_time" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "public_simple_watch_live_time" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "public_view_amt" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "public_real_show_amt" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "public_action_list_amt" + str_count_days, 0);

      context->SetIntItemAttr(it->item_key, "public_view_live_room_amt" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "public_like_live_room_amt" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "public_comment_live_room_amt" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "public_reward_live_room_amt" + str_count_days, 0);

      context->SetIntItemAttr(it->item_key, "private_reward_amt_" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "private_reward_times" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "private_like_amt" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "private_comment_amt" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "private_watch_live_time" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "private_simple_watch_live_time" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "private_view_amt" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "private_real_show_amt" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "private_action_list_amt" + str_count_days, 0);

      context->SetIntItemAttr(it->item_key, "private_view_live_room_amt" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "private_like_live_room_amt" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "private_comment_live_room_amt" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "private_reward_live_room_amt" + str_count_days, 0);

      // 主动看播天数、次数
      context->SetIntItemAttr(it->item_key, "private_total_active_play_days_" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "private_total_active_play_ex_days_" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "private_active_play_cnt_" + str_count_days, 0);
      context->SetIntItemAttr(it->item_key, "private_total_active_play_rooms_" + str_count_days, 0);
    }

    context->SetIntCommonAttr("total_reward_amt" + str_count_days, 0);
    context->SetIntCommonAttr("total_reward_times" + str_count_days, 0);
    context->SetIntCommonAttr("following_total_reward_amt" + str_count_days, 0);
    context->SetIntCommonAttr("following_total_reward_times" + str_count_days, 0);

    context->SetIntCommonAttr("total_like_amt" + str_count_days, 0);
    context->SetIntCommonAttr("total_comment_amt" + str_count_days, 0);
    context->SetIntCommonAttr("total_watch_live_time" + str_count_days, 0);
    context->SetIntCommonAttr("total_simple_watch_live_time" + str_count_days, 0);
    context->SetIntCommonAttr("total_view_amt" + str_count_days, 0);
    context->SetIntCommonAttr("total_real_show_amt" + str_count_days, 0);
    context->SetIntCommonAttr("total_action_list_amt" + str_count_days, 0);
    context->SetIntCommonAttr("total_new_following_amt" + str_count_days, 0);
    context->SetIntCommonAttr("total_view_live_room_amt" + str_count_days, 0);
    context->SetIntCommonAttr("total_like_live_room_amt" + str_count_days, 0);
    context->SetIntCommonAttr("total_comment_live_room_amt" + str_count_days, 0);
    context->SetIntCommonAttr("total_reward_live_room_amt" + str_count_days, 0);

    context->SetIntCommonAttr("following_total_like_amt" + str_count_days, 0);
    context->SetIntCommonAttr("following_total_comment_amt" + str_count_days, 0);
    context->SetIntCommonAttr("following_total_watch_live_time" + str_count_days, 0);
    context->SetIntCommonAttr("following_total_simple_watch_live_time" + str_count_days, 0);
    context->SetIntCommonAttr("following_total_view_amt" + str_count_days, 0);
    context->SetIntCommonAttr("following_total_real_show_amt" + str_count_days, 0);
    context->SetIntCommonAttr("following_total_action_list_amt" + str_count_days, 0);

    // 拆分公域、私域: 用户维度
    context->SetIntCommonAttr("public_total_reward_amt" + str_count_days, 0);
    context->SetIntCommonAttr("public_total_reward_times" + str_count_days, 0);
    context->SetIntCommonAttr("public_total_like_amt" + str_count_days, 0);
    context->SetIntCommonAttr("public_total_comment_amt" + str_count_days, 0);
    context->SetIntCommonAttr("public_total_watch_live_time" + str_count_days, 0);
    context->SetIntCommonAttr("public_total_simple_watch_live_time" + str_count_days, 0);
    context->SetIntCommonAttr("public_total_view_amt" + str_count_days, 0);
    context->SetIntCommonAttr("public_total_real_show_amt" + str_count_days, 0);
    context->SetIntCommonAttr("public_total_action_list_amt" + str_count_days, 0);

    context->SetIntCommonAttr("private_total_reward_amt" + str_count_days, 0);
    context->SetIntCommonAttr("private_total_reward_times" + str_count_days, 0);
    context->SetIntCommonAttr("private_total_like_amt" + str_count_days, 0);
    context->SetIntCommonAttr("private_total_comment_amt" + str_count_days, 0);
    context->SetIntCommonAttr("private_total_watch_live_time" + str_count_days, 0);
    context->SetIntCommonAttr("private_total_simple_watch_live_time" + str_count_days, 0);
    context->SetIntCommonAttr("private_total_view_amt" + str_count_days, 0);
    context->SetIntCommonAttr("private_total_real_show_amt" + str_count_days, 0);
    context->SetIntCommonAttr("private_total_action_list_amt" + str_count_days, 0);
    return;
  }
  timer_.AppendCostMs("get_rodis_resp");

  // 请求时间之后 n 天的打赏情况
  int64 start_filter_time = context->GetRequestTime() / 1000;
  int64 end_filter_time_n = start_filter_time + count_days_ * 24 * 60 * 60;

  struct tm st_tm;
  char p_date_request[] = "20250910";
  time_t st_param_time = (time_t)(start_filter_time);
  localtime_r(&st_param_time, &st_tm);
  strftime(p_date_request, sizeof(p_date_request), "%Y%m%d", &st_tm);

  auto author_attr_accessor = context->GetItemAttrAccessor(author_id_attr_);
  std::map<uint32, std::vector<const rodis::LtvLiveGiftItem *>>::iterator iter;
  int64 total_reward_times_n = 0;
  int64 public_total_reward_times_n = 0;
  int64 private_total_reward_times_n = 0;
  int64 total_reward_amt_n = 0;
  int64 public_total_reward_amt_n = 0;
  int64 private_total_reward_amt_n = 0;
  int64 total_like_amt_n = 0;
  int64 public_total_like_amt_n = 0;
  int64 private_total_like_amt_n = 0;
  int64 total_comment_amt_n = 0;
  int64 public_total_comment_amt_n = 0;
  int64 private_total_comment_amt_n = 0;
  int64 total_watch_live_time_n = 0;
  int64 public_total_watch_live_time_n = 0;
  int64 private_total_watch_live_time_n = 0;
  int64 total_simple_watch_live_time_n = 0;
  int64 public_total_simple_watch_live_time_n = 0;
  int64 private_total_simple_watch_live_time_n = 0;
  int64 total_view_amt_n = 0;
  int64 public_total_view_amt_n = 0;
  int64 private_total_view_amt_n = 0;
  int64 total_real_show_amt_n = 0;
  int64 public_total_real_show_amt_n = 0;
  int64 private_total_real_show_amt_n = 0;
  int64 total_action_list_amt_n = 0;
  int64 public_total_action_list_amt_n = 0;
  int64 private_total_action_list_amt_n = 0;
  int64 total_new_following_amt_n = 0;
  int64 total_like_live_room_amt_n = 0;
  int64 total_reward_live_room_amt_n = 0;
  int64 total_view_live_room_amt_n = 0;
  int64 total_comment_live_room_amt_n = 0;

  int64 following_total_reward_times_n = 0;
  int64 following_total_reward_amt_n = 0;

  int64 following_total_like_amt_n = 0;
  int64 following_total_comment_amt_n = 0;
  int64 following_total_watch_live_time_n = 0;
  int64 following_total_simple_watch_live_time_n = 0;
  int64 following_total_view_amt_n = 0;
  int64 following_total_real_show_amt_n = 0;
  int64 following_total_action_list_amt_n = 0;


  std::unordered_set<uint64> total_view_live_rooms;
  std::unordered_set<uint64> total_like_live_rooms;
  std::unordered_set<uint64> total_comment_live_rooms;
  std::unordered_set<uint64> total_reward_live_rooms;
  static std::vector<int64> public_channels = {
      37, 68, 72, 21, 64, 38, 49, 67, 71, 65, 1, 14, 16, 12, 18, 22, 23, 10, 13, 15,
      17, 76, 50, 43, 141
      };
  // 计算总的打赏情况，将每个 author 的打赏情况放到 map 中，便于检索统计每个 author 的打赏情况
  std::map<uint32, std::vector<const rodis::LtvLiveGiftItem *>> reward_info_map;
  // 初始化 map, 只对 request 中的相关 aid 进行插入处理
  for (auto it = begin; it != end; it = std::next(it)) {
    int64 aid = context->GetIntItemAttr(*it, author_attr_accessor).value();
    std::vector<const rodis::LtvLiveGiftItem *> v;
    reward_info_map.insert(std::pair<uint32, std::vector<const rodis::LtvLiveGiftItem *>>(aid, v));
  }


  for (int i = sim_labels->size() - 1; i >= 0; i--) {
    rodis::LtvLiveGiftItem *sim_label = new rodis::LtvLiveGiftItem();

    sim_label->ParseFromString(std::string(sim_labels->at(i)));
    if ((sim_label->timestamp() < start_filter_time || sim_label->timestamp() > end_filter_time_n)) {
        delete sim_label;
        continue;
    }

    int64 channel = sim_label->channel();
    bool is_public =
      std::find(public_channels.begin(), public_channels.end(), channel) != public_channels.end();

    total_view_live_rooms.insert(sim_label->live_id());
    if (sim_label->like_live_count() > 0) {
      total_like_live_rooms.insert(sim_label->live_id());
    }
    if (sim_label->send_comment_success_count() > 0) {
      total_comment_live_rooms.insert(sim_label->live_id());
    }
    if (sim_label->gift_price() > 0) {
      total_reward_live_rooms.insert(sim_label->live_id());
    }

    // LTVn 计算用户维度的特征
    total_reward_amt_n += sim_label->gift_price();
    total_reward_times_n += sim_label->gift_count();
    total_like_amt_n += sim_label->like_live_count();
    total_comment_amt_n += sim_label->send_comment_success_count();
    total_watch_live_time_n += sim_label->watch_live_time();
    total_simple_watch_live_time_n += sim_label->simple_watch_live_time();
    total_view_amt_n += sim_label->view_count();
    total_real_show_amt_n += sim_label->real_show_count();
    total_action_list_amt_n += sim_label->list_count();

    if (is_public) {
      public_total_reward_amt_n += sim_label->gift_price();
      public_total_reward_times_n += sim_label->gift_count();
      public_total_like_amt_n += sim_label->like_live_count();
      public_total_comment_amt_n += sim_label->send_comment_success_count();
      public_total_watch_live_time_n += sim_label->watch_live_time();
      public_total_simple_watch_live_time_n += sim_label->simple_watch_live_time();
      public_total_view_amt_n += sim_label->view_count();
      public_total_real_show_amt_n += sim_label->real_show_count();
      public_total_action_list_amt_n += sim_label->list_count();
    } else {
      private_total_reward_amt_n += sim_label->gift_price();
      private_total_reward_times_n += sim_label->gift_count();
      private_total_like_amt_n += sim_label->like_live_count();
      private_total_comment_amt_n += sim_label->send_comment_success_count();
      private_total_watch_live_time_n += sim_label->watch_live_time();
      private_total_simple_watch_live_time_n += sim_label->simple_watch_live_time();
      private_total_view_amt_n += sim_label->view_count();
      private_total_real_show_amt_n += sim_label->real_show_count();
      private_total_action_list_amt_n += sim_label->list_count();
    }

    uint32 aid = sim_label->author_id();
    iter = reward_info_map.find(aid);
    if (iter != reward_info_map.end()) {
      iter->second.push_back(sim_label);
    } else {
      delete sim_label;
    }
  }

  total_view_live_room_amt_n = total_view_live_rooms.size();
  total_like_live_room_amt_n = total_like_live_rooms.size();
  total_comment_live_room_amt_n = total_comment_live_rooms.size();
  total_reward_live_room_amt_n = total_reward_live_rooms.size();

  std::unordered_set<uint64> view_live_rooms;
  std::unordered_set<uint64> like_live_rooms;
  std::unordered_set<uint64> comment_live_rooms;
  std::unordered_set<uint64> reward_live_rooms;
  std::unordered_set<std::string> private_total_active_play_days;
  std::unordered_set<std::string> private_total_active_play_ex_days;
  std::unordered_set<uint64> private_total_active_play_rooms;
  // 一个样本中，支持包含多个 item，u2a 一对多
  for (auto it = begin; it != end; it = std::next(it)) {
    absl::optional<int64> optional_aid = context->GetIntItemAttr(*it, author_attr_accessor);
    if (!optional_aid.has_value()) {
      CL_LOG_EVERY_N(ERROR, 100) << GetName() << " miss author_id attr!";
      continue;
    }
    int64 aid = optional_aid.value();

    int64 author_followingtime = 0;
    int64 reward_amt_n = 0;
    int64 public_reward_amt_n = 0;
    int64 private_reward_amt_n = 0;
    int64 reward_times_n = 0;
    int64 public_reward_times_n = 0;
    int64 private_reward_times_n = 0;
    int64 like_amt_n = 0;
    int64 public_like_amt_n = 0;
    int64 private_like_amt_n = 0;
    int64 comment_amt_n = 0;
    int64 public_comment_amt_n = 0;
    int64 private_comment_amt_n = 0;
    int64 watch_live_time_n = 0;
    int64 public_watch_live_time_n = 0;
    int64 private_watch_live_time_n = 0;
    int64 simple_watch_live_time_n = 0;
    int64 public_simple_watch_live_time_n = 0;
    int64 private_simple_watch_live_time_n = 0;
    int64 view_amt_n = 0;
    int64 public_view_amt_n = 0;
    int64 private_view_amt_n = 0;
    int64 real_show_amt_n = 0;
    int64 public_real_show_amt_n = 0;
    int64 private_real_show_amt_n = 0;
    int64 action_list_amt_n = 0;
    int64 public_action_list_amt_n = 0;
    int64 private_action_list_amt_n = 0;
    int64 private_active_play_cnt = 0;
    view_live_rooms.clear();
    like_live_rooms.clear();
    comment_live_rooms.clear();
    reward_live_rooms.clear();
    private_total_active_play_days.clear();
    private_total_active_play_ex_days.clear();
    private_total_active_play_rooms.clear();

    iter = reward_info_map.find(aid);
    if (iter != reward_info_map.end()) {
      std::vector<const rodis::LtvLiveGiftItem *> v = iter->second;
      for (std::vector<const rodis::LtvLiveGiftItem *>::iterator vit = v.begin(); vit != v.end(); ++vit) {
        int64 channel = (*vit)->channel();
        struct tm tm;
        char p_date_str[] = "20250910";
        bool is_public_scenario =
          std::find(public_channels.begin(), public_channels.end(), channel) != public_channels.end();

        reward_amt_n += (*vit)->gift_price();
        reward_times_n += (*vit)->gift_count();
        like_amt_n += (*vit)->like_live_count();
        comment_amt_n += (*vit)->send_comment_success_count();
        watch_live_time_n += (*vit)->watch_live_time();
        simple_watch_live_time_n += (*vit)->simple_watch_live_time();
        view_amt_n += (*vit)->view_count();
        real_show_amt_n += (*vit)->real_show_count();
        action_list_amt_n += (*vit)->list_count();

        time_t param_time = (time_t)((*vit)->timestamp());
        localtime_r(&param_time, &tm);
        strftime(p_date_str, sizeof(p_date_str), "%Y%m%d", &tm);
        if ((*vit)->watch_live_time() >= 3) {
          if (!is_public_scenario) {
            private_active_play_cnt += 1;
            private_total_active_play_rooms.insert((*vit)->live_id());
            private_total_active_play_days.insert(p_date_str);
            if (p_date_str != p_date_request) {
              private_total_active_play_ex_days.insert(p_date_str);
            }
          }
        }

        if (is_public_scenario) {
          public_reward_amt_n += (*vit)->gift_price();
          public_reward_times_n += (*vit)->gift_count();
          public_like_amt_n += (*vit)->like_live_count();
          public_comment_amt_n += (*vit)->send_comment_success_count();
          public_watch_live_time_n += (*vit)->watch_live_time();
          public_simple_watch_live_time_n += (*vit)->simple_watch_live_time();
          public_view_amt_n += (*vit)->view_count();
          public_real_show_amt_n += (*vit)->real_show_count();
          public_action_list_amt_n += (*vit)->list_count();
        } else {
          private_reward_amt_n += (*vit)->gift_price();
          private_reward_times_n += (*vit)->gift_count();
          private_like_amt_n += (*vit)->like_live_count();
          private_comment_amt_n += (*vit)->send_comment_success_count();
          private_watch_live_time_n += (*vit)->watch_live_time();
          private_simple_watch_live_time_n += (*vit)->simple_watch_live_time();
          private_view_amt_n += (*vit)->view_count();
          private_real_show_amt_n += (*vit)->real_show_count();
          private_action_list_amt_n += (*vit)->list_count();
        }


        view_live_rooms.insert((*vit)->live_id());
        if ((*vit)->like_live_count() > 0) {
          like_live_rooms.insert((*vit)->live_id());
        }
        if ((*vit)->send_comment_success_count() > 0) {
          comment_live_rooms.insert((*vit)->live_id());
        }
        if ((*vit)->gift_price() > 0) {
          reward_live_rooms.insert((*vit)->live_id());
        }

        author_followingtime = author_followingtime > (*vit)->following_time() ?
                               author_followingtime : (*vit)->following_time();
      }

      // 给老关注主播的打赏
      if (author_followingtime > 0 && author_followingtime <= start_filter_time) {
        following_total_reward_amt_n += reward_amt_n;
        following_total_reward_times_n += reward_times_n;

        following_total_like_amt_n += like_amt_n;
        following_total_comment_amt_n += comment_amt_n;
        following_total_watch_live_time_n += watch_live_time_n;
        following_total_simple_watch_live_time_n += simple_watch_live_time_n;
        following_total_view_amt_n += view_amt_n;
        following_total_real_show_amt_n += real_show_amt_n;
        following_total_action_list_amt_n += action_list_amt_n;
      }
      if (author_followingtime > start_filter_time && author_followingtime < end_filter_time_n) {
        total_new_following_amt_n += 1;
      }
    }
    context->SetIntItemAttr(it->item_key, "count_days", count_days_);
    context->SetIntItemAttr(it->item_key, "reward_amt_" + str_count_days, reward_amt_n);
    context->SetIntItemAttr(it->item_key, "reward_times_" + str_count_days, reward_times_n);
    context->SetIntItemAttr(it->item_key, "like_amt_" + str_count_days, like_amt_n);
    context->SetIntItemAttr(it->item_key, "comment_amt_" + str_count_days, comment_amt_n);
    context->SetIntItemAttr(it->item_key, "watch_live_time_" + str_count_days, watch_live_time_n);
    context->SetIntItemAttr(it->item_key, "simple_watch_live_time_" + str_count_days,
                            simple_watch_live_time_n);
    context->SetIntItemAttr(it->item_key, "view_amt_" + str_count_days, view_amt_n);
    context->SetIntItemAttr(it->item_key, "real_show_amt_" + str_count_days, real_show_amt_n);
    context->SetIntItemAttr(it->item_key, "action_list_amt_" + str_count_days, action_list_amt_n);
    context->SetIntItemAttr(it->item_key, "view_live_room_amt_" + str_count_days, view_live_rooms.size());
    context->SetIntItemAttr(it->item_key, "like_live_room_amt_" + str_count_days, like_live_rooms.size());
    context->SetIntItemAttr(it->item_key, "comment_live_room_amt_" + str_count_days,
                            comment_live_rooms.size());
    context->SetIntItemAttr(it->item_key, "reward_live_room_amt_" + str_count_days, reward_live_rooms.size());

    context->SetIntItemAttr(it->item_key, "following_time", author_followingtime);


    context->SetIntItemAttr(it->item_key, "public_reward_amt_" + str_count_days, public_reward_amt_n);
    context->SetIntItemAttr(it->item_key, "public_reward_times_" + str_count_days, public_reward_times_n);
    context->SetIntItemAttr(it->item_key, "public_like_amt_" + str_count_days, public_like_amt_n);
    context->SetIntItemAttr(it->item_key, "public_comment_amt_" + str_count_days, public_comment_amt_n);
    context->SetIntItemAttr(it->item_key, "public_watch_live_time_" + str_count_days,
                            public_watch_live_time_n);
    context->SetIntItemAttr(it->item_key, "public_simple_watch_live_time_" + str_count_days,
                            public_simple_watch_live_time_n);
    context->SetIntItemAttr(it->item_key, "public_view_amt_" + str_count_days, public_view_amt_n);
    context->SetIntItemAttr(it->item_key, "public_real_show_amt_" + str_count_days,
                            public_real_show_amt_n);
    context->SetIntItemAttr(it->item_key, "public_action_list_amt_" + str_count_days,
                            public_action_list_amt_n);

    context->SetIntItemAttr(it->item_key, "private_reward_amt_" + str_count_days, private_reward_amt_n);
    context->SetIntItemAttr(it->item_key, "private_reward_times_" + str_count_days, private_reward_times_n);
    context->SetIntItemAttr(it->item_key, "private_like_amt_" + str_count_days, private_like_amt_n);
    context->SetIntItemAttr(it->item_key, "private_comment_amt_" + str_count_days, private_comment_amt_n);
    context->SetIntItemAttr(it->item_key, "private_watch_live_time_" + str_count_days,
                            private_watch_live_time_n);
    context->SetIntItemAttr(it->item_key, "private_simple_watch_live_time_" + str_count_days,
                            private_simple_watch_live_time_n);
    context->SetIntItemAttr(it->item_key, "private_view_amt_" + str_count_days, private_view_amt_n);
    context->SetIntItemAttr(it->item_key, "private_real_show_amt_" + str_count_days,
                            private_real_show_amt_n);
    context->SetIntItemAttr(it->item_key, "private_action_list_amt_" + str_count_days,
                            private_action_list_amt_n);
    context->SetIntItemAttr(it->item_key, "private_total_active_play_days_" + str_count_days,
                            private_total_active_play_days.size());
    context->SetIntItemAttr(it->item_key, "private_total_active_play_ex_days_" + str_count_days,
                            private_total_active_play_ex_days.size());
    context->SetIntItemAttr(it->item_key, "private_active_play_cnt_" + str_count_days,
                            private_active_play_cnt);
    context->SetIntItemAttr(it->item_key, "private_total_active_play_rooms_" + str_count_days,
                        private_total_active_play_rooms.size());
    CL_LOG_EVERY_N(INFO, 1000) << GetName()
      << ", view_live_room_amt_n:" << view_live_rooms.size()
      << ", like_live_room_amt_n:" << like_live_rooms.size()
      << ", comment_live_room_amt_n:" << comment_live_rooms.size()
      << ", reward_live_room_amt_n:" << reward_live_rooms.size()
      << ", following_time:" << author_followingtime;
  }

  // Release memory
  for (iter = reward_info_map.begin(); iter != reward_info_map.end(); ++iter) {
      std::vector<const rodis::LtvLiveGiftItem *> v = iter->second;
      for (std::vector<const rodis::LtvLiveGiftItem *>::iterator vit = v.begin(); vit != v.end(); ++vit) {
          delete *vit;
      }
      // Clear the vector
      iter->second.clear();
  }

  // Clear the map
  reward_info_map.clear();


  context->SetIntCommonAttr("total_reward_amt" + str_count_days, total_reward_amt_n);
  context->SetIntCommonAttr("total_reward_times" + str_count_days, total_reward_times_n);
  context->SetIntCommonAttr("following_total_reward_amt" + str_count_days, following_total_reward_amt_n);
  context->SetIntCommonAttr("following_total_reward_times" + str_count_days, following_total_reward_times_n);

  context->SetIntCommonAttr("total_like_amt" + str_count_days, total_like_amt_n);
  context->SetIntCommonAttr("total_comment_amt" + str_count_days, total_comment_amt_n);
  context->SetIntCommonAttr("total_watch_live_time" + str_count_days, total_watch_live_time_n);
  context->SetIntCommonAttr("total_simple_watch_live_time" + str_count_days, total_simple_watch_live_time_n);
  context->SetIntCommonAttr("total_view_amt" + str_count_days, total_view_amt_n);
  context->SetIntCommonAttr("total_real_show_amt" + str_count_days, total_real_show_amt_n);
  context->SetIntCommonAttr("total_action_list_amt" + str_count_days, total_action_list_amt_n);

  context->SetIntCommonAttr("following_total_like_amt" + str_count_days, following_total_like_amt_n);
  context->SetIntCommonAttr("following_total_comment_amt" + str_count_days, following_total_comment_amt_n);
  context->SetIntCommonAttr("following_total_watch_live_time" + str_count_days,
                            following_total_watch_live_time_n);
  context->SetIntCommonAttr("following_total_simple_watch_live_time" + str_count_days,
                            following_total_simple_watch_live_time_n);
  context->SetIntCommonAttr("following_total_view_amt" + str_count_days, following_total_view_amt_n);
  context->SetIntCommonAttr("following_total_real_show_amt" + str_count_days,
                            following_total_real_show_amt_n);
  context->SetIntCommonAttr("following_total_action_list_amt" + str_count_days,
                            following_total_action_list_amt_n);

  context->SetIntCommonAttr("total_new_following_amt" + str_count_days, total_new_following_amt_n);

  context->SetIntCommonAttr("total_view_live_room_amt" + str_count_days, total_view_live_room_amt_n);
  context->SetIntCommonAttr("total_like_live_room_amt" + str_count_days, total_like_live_room_amt_n);
  context->SetIntCommonAttr("total_comment_live_room_amt" + str_count_days, total_comment_live_room_amt_n);
  context->SetIntCommonAttr("total_reward_live_room_amt" + str_count_days, total_reward_live_room_amt_n);

  context->SetIntCommonAttr("public_total_reward_amt" + str_count_days, public_total_reward_amt_n);
  context->SetIntCommonAttr("public_total_reward_times" + str_count_days, public_total_reward_times_n);
  context->SetIntCommonAttr("public_total_like_amt" + str_count_days, public_total_like_amt_n);
  context->SetIntCommonAttr("public_total_comment_amt" + str_count_days, public_total_comment_amt_n);
  context->SetIntCommonAttr("public_total_watch_live_time" + str_count_days,
                            public_total_watch_live_time_n);
  context->SetIntCommonAttr("public_total_simple_watch_live_time" + str_count_days,
                            public_total_simple_watch_live_time_n);
  context->SetIntCommonAttr("public_total_view_amt" + str_count_days, public_total_view_amt_n);
  context->SetIntCommonAttr("public_total_real_show_amt" + str_count_days, public_total_real_show_amt_n);
  context->SetIntCommonAttr("public_total_action_list_amt" + str_count_days,
                            public_total_action_list_amt_n);

  context->SetIntCommonAttr("private_total_reward_amt" + str_count_days, private_total_reward_amt_n);
  context->SetIntCommonAttr("private_total_reward_times" + str_count_days, private_total_reward_times_n);
  context->SetIntCommonAttr("private_total_like_amt" + str_count_days, private_total_like_amt_n);
  context->SetIntCommonAttr("private_total_comment_amt" + str_count_days, private_total_comment_amt_n);
  context->SetIntCommonAttr("private_total_watch_live_time" + str_count_days,
                            private_total_watch_live_time_n);
  context->SetIntCommonAttr("private_total_simple_watch_live_time" + str_count_days,
                            private_total_simple_watch_live_time_n);
  context->SetIntCommonAttr("private_total_view_amt" + str_count_days, private_total_view_amt_n);
  context->SetIntCommonAttr("private_total_real_show_amt" + str_count_days,
                            private_total_real_show_amt_n);
  context->SetIntCommonAttr("private_total_action_list_amt" + str_count_days,
                            private_total_action_list_amt_n);
  // CL_LOG(WARNING) << "------ come on baby total_reward_amt = " << total_reward_amt;
  // CL_LOG(WARNING) << "------ total_reward_times = " <<  total_reward_times;
  // CL_LOG(WARNING) << "------ following_total_reward_amt = " << following_total_reward_amt;
  timer_.AppendCostMs("fill_attr");
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, LiveStreamLtvnByRodisEnricher,
                 LiveStreamLtvnByRodisEnricher);

}  // namespace platform
}  // namespace ks
