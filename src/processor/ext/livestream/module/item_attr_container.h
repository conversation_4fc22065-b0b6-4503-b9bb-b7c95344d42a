#pragma once

#include <cstddef>
#include <string>
#include <vector>

#include "dragon/src/core/common_reco_context_interface.h"
#include "dragon/src/processor/ext/livestream/util/retrieval_filter_util.h"

namespace ks {
namespace platform {
namespace livestream {

class ItemAttrContainer {
 public:
  explicit ItemAttrContainer(const folly::F14FastMap<std::string, const ItemAttr *> &item_accessor_map);
  void ResetItemAttrContainer(ReadableRecoContextInterface *context, const CommonRecoResult &result);

  // item attr getters
  inline absl::optional<int64> GetPhotoId() const {
    return photo_id_;
  }

  inline absl::optional<int64> GetIsShopLive() const {
    return is_shop_live_;
  }

  inline absl::optional<int64> GetaGoodAuthorRangeV4KV() const {
    return aGoodAuthorRangeV4KV_;
  }

  inline absl::optional<int64> GetRecruitPriorityAttr() const {
    return recruit_priority_attr_;
  }

  inline absl::optional<absl::Span<const int64>> GetMmuTagId() const {
    return mmu_tag_id_;
  }

  inline absl::optional<absl::Span<const int64>> GetReasonList() const {
    return reason_list_;
  }

  inline absl::optional<int64> GetIsGameLive() const {
    return is_game_live_;
  }

  inline absl::optional<int64> GetIsAudioLiveRoom() const {
    return is_audio_live_room_;
  }

  inline absl::optional<int64> GetIsKtv() const {
    return is_ktv_;
  }

  inline absl::optional<int64> GetIsTheater() const {
    return is_theater_;
  }

  inline absl::optional<int64> GetIsTeampk() const {
    return is_teampk_;
  }

  inline absl::optional<int64> GetIsVoiceParty() const {
    return is_voice_party_;
  }

  inline absl::optional<int64> GetASingleKillLabel() const {
    return a_single_kill_label_;
  }

  inline absl::optional<int64> GetLMerchantThemisAuthorKV() const {
    return l_merchant_themis_author_kv_;
  }

  inline absl::optional<int64> GetAuthorId() const {
    return author_id_;
  }

  inline absl::optional<double> GetLThompsonNegGamoraV1() const {
    return l_thompson_neg_gamora_v1_;
  }

  inline absl::optional<double> GetLThompsonNegNebulaV1() const {
    return l_thompson_neg_nebula_v1_;
  }

  inline absl::optional<double> GetLThompsonNegGamoraV2() const {
    return l_thompson_neg_gamora_v2_;
  }

  inline absl::optional<double> GetLThompsonNegNebulaV2() const {
    return l_thompson_neg_nebula_v2_;
  }

  inline absl::optional<absl::Span<const int64>>
    GetMmuTagInfoTagListLv2() const {
    return mmu_tag_info_tag_list_lv2_;
  }

  inline absl::optional<absl::Span<const int64>>
    GetLiveMmuSegAttrList() const {
    return l_mmu_seg_attr_list_;
  }

  inline absl::optional<absl::Span<const int64>>
    GetMmuUserHetuTag() const {
    return mmu_user_hetu_tag_;
  }

  inline absl::optional<int64> GetAuthorInfoIsSellerAuthorV2() const {
    return author_info_is_seller_author_v2_;
  }

  inline absl::optional<int64> GetLRiskLevel() const {
    return l_risk_level_;
  }

  inline absl::optional<int64> GetGoodAuthorRangeV4() const {
    return good_author_range_v4_;
  }

  // consume good author v4 @chenliangliang03
  inline absl::optional<int64> GetConsumeGoodAuthorRangeV4() const {
    return consume_good_author_range_v4_;
  }

  inline absl::optional<int64> GetLRingBall() const {
    return l_ring_ball_;
  }

  inline absl::optional<int64> GetLRealShowRecent() const {
    return l_realshow_recent_;
  }

  inline absl::optional<int64> GetLReportRecent() const {
    return l_report_recent_;
  }

  inline absl::optional<int64> GetLNegativeRecent() const {
    return l_negative_recent_;
  }

  inline absl::optional<double> GetLDurPerShow() const {
    return l_dur_per_show_;
  }

  inline absl::optional<int64> GetLShufflePolicy() const {
    return l_shuffle_policy_;
  }

  inline absl::optional<absl::string_view>GetPunishSiriusAll() const {
    return punish_sirius_all_;
  }

  inline absl::optional<int64> GetLRecruitQueryRoomBool() const {
    return l_recruit_query_room_bool_;
  }

  inline absl::optional<int64> GetLLiveHouseIsLiveTag() const {
    return l_live_house_is_live_tag_;
  }

  inline absl::optional<int64> GetLLiveHouseIsUnaLiveTag() const {
    return l_live_house_is_una_live_tag_;
  }

  inline absl::optional<int64> GetLIsLocalLife() const {
    return l_is_local_life_;
  }

  inline absl::optional<std::vector<absl::string_view>>
    GetLPoiGeoHash4List() const {
    return l_poi_geo_hash4_list_;
  }

  inline absl::optional<std::vector<absl::string_view>>
    GetLLiveZeroPunishAbSuffixList() const {
    return l_live_zero_punish_ab_suffix_list_;
  }

  inline absl::optional<int64> GetSlideRealShowCount() const {
    return slide_real_show_count_;
  }

  inline absl::optional<int64> GetSlideRealShowAllDayCount() const {
    return slide_real_show_all_day_count_;
  }

  inline absl::optional<int64> GetSlideInnerRealShowAllDayCount() const {
    return slide_inner_real_show_all_day_count_;
  }

  inline absl::optional<int64> GetSlideOutterRealShowAllDayCount() const {
    return slide_outter_real_show_all_day_count_;
  }

  inline absl::optional<int64> GetExploreInnerRealShowAllDayCount() const {
    return explore_inner_real_show_all_day_count_;
  }

  inline absl::optional<int64> GetExploreOuterRealShowAllDayCount() const {
    return explore_outer_real_show_all_day_count_;
  }

  inline absl::optional<int64> GetLiveTagRealShowAllDayCount() const {
    return live_tag_real_show_all_day_count_;
  }

  inline absl::optional<int64> GetACStartAuthorBucket() const {
    return a_c_start_author_bucket_;
  }

  inline absl::optional<int64> GetACStartExpectTotalShow() const {
    return a_c_start_expect_total_show_;
  }

  inline absl::optional<int64> GetACStartCurrentTotalShow() const {
    return a_c_start_current_total_show_;
  }

  inline absl::optional<int64> GetACStartExpectTotalGift() const {
    return a_c_start_expect_total_gift_;
  }

  inline absl::optional<int64> GetACStartCurrentTotalGift() const {
    return a_c_start_current_total_gift_;
  }

  inline absl::optional<int64> GetACStartExpectTotalShowV1() const {
    return a_c_start_expect_total_show_v1_;
  }

  inline absl::optional<int64> GetACStartExpectTotalShowV2() const {
    return a_c_start_expect_total_show_v2_;
  }

  inline absl::optional<int64> GetACStartExpectTotalShowV3() const {
    return a_c_start_expect_total_show_v3_;
  }

  inline absl::optional<int64> GetACStartExpectTotalShowV4() const {
    return a_c_start_expect_total_show_v4_;
  }

  inline absl::optional<int64> GetACStartExpectTotalGiftV1() const {
    return a_c_start_expect_total_gift_v1_;
  }

  inline absl::optional<int64> GetACStartExpectTotalGiftV2() const {
    return a_c_start_expect_total_gift_v2_;
  }

  inline absl::optional<int64> GetACStartExpectTotalGiftV3() const {
    return a_c_start_expect_total_show_v3_;
  }

  inline absl::optional<int64> GetACStartExpectTotalGiftV4() const {
    return a_c_start_expect_total_show_v4_;
  }

  inline absl::optional<absl::string_view> GetAuthorSupportType() const {
    return author_support_type_;
  }

  inline absl::optional<int64> GetColdStartQueueReasonValue() const {
    return cold_start_queue_reason_value_;
  }

  inline absl::optional<int64> GetAIsHouseLiveKV() const {
    return a_is_house_live_kv_;
  }

  inline absl::optional<int64> GetLLiveHouseIsDreamHouse() const {
    return l_live_house_is_dream_house_;
  }

  inline absl::optional<absl::string_view> GetAIsPrivateUserKV() const {
    return a_is_private_user_kv_;
  }

  inline absl::optional<int64> GetARecruitOrgIdKV() const {
    return a_recruit_org_id_kv_;
  }

  inline absl::optional<int64> GetAViolationPunishRt() const {
    return a_violation_punish_rt_;
  }

  inline absl::optional<int64> GetAViolationPunishOffline() const {
    return a_violation_punish_offline_;
  }

  inline absl::optional<absl::Span<const int64>>
    GetMmuTagInfoTagListLv1() const {
    return mmu_tag_info_tag_list_lv1_;
  }

  inline absl::optional<absl::Span<const int64>>
    GetMmuTagInfoTagListLv3() const {
    return mmu_tag_info_tag_list_lv3_;
  }

  inline absl::optional<double> GetLLatKV() const {
    return l_lat_kv_;
  }

  inline absl::optional<double> GetLLonKV() const {
    return l_lon_kv_;
  }

  inline absl::optional<absl::string_view> GetLLiveLocationCity() const {
    return l_live_location_city_;
  }

  inline absl::optional<absl::string_view> GetLLiveLocationProvince() const {
    return l_live_location_province_;
  }

  inline absl::optional<absl::Span<const int64>> GetGradeLabelForTemp() const {
    return grade_label_for_temp_;
  }

  inline absl::optional<absl::Span<const int64>> GetGradeLabelForAidTemp() const {
    return grade_label_for_aid_temp_;
  }

  inline absl::optional<int64> GetLIsChatGirdWhitelistAid() const {
    return l_is_chat_gird_whitelist_aid_;
  }

  inline absl::optional<double> GetLVoicePartyStatus() const {
    return l_voice_party_status_;
  }

  inline absl::optional<int64> GetLVoicePartyOpenVideoStatus() const {
    return l_voice_party_open_video_status_;
  }

  inline absl::optional<absl::Span<const double>> GetAContentEmbV2() const {
    return a_content_emb_v2_;
  }

  inline absl::optional<int64> GetLHotExplorePunishTag() const {
    return l_hot_explore_punish_tag_;
  }

  inline absl::optional<absl::Span<const int64>> GetLHotSmallBusinessPunishCity() const {
    return l_hot_small_business_punish_city_;
  }

  inline absl::optional<absl::Span<const int64>> GetLHotExplorePunishCity() const {
    return l_hot_explore_punish_city_;
  }

  inline absl::optional<absl::Span<const int64>> GetLHotSlidePunishCity() const {
    return l_hot_slide_punish_city_;
  }

  inline absl::optional<int64> GetLInteractiveGameType() const {
    return l_interactive_game_type_;
  }

  inline absl::optional<absl::Span<const int64>> GetItemOffFilterList() const {
    return item_off_filter_list_;
  }

  inline absl::optional<absl::string_view> GetLlBrandName() const {
    return ll_brand_name_;
  }

  inline absl::optional<int64> GetLlBrandId() const {
    return ll_brand_id_;
  }

  inline absl::optional<int64> GetLlNewBrandId() const {
    return ll_new_brand_id_;
  }

  inline absl::optional<int64> GetLlMerchantId() const {
    return ll_merchant_id_;
  }

  inline absl::optional<int64> GetLlMmu() const {
    return ll_mmu_;
  }

  inline absl::optional<int64> GetLlRestrictAuthor() const {
    return ll_restrict_author_;
  }

  inline absl::optional<int64> GetLlFireworksAuthor() const {
    return ll_fireworks_author_;
  }

  inline absl::optional<double> GetLlGeohashDistance() const {
    return ll_geohash_distance_;
  }

  inline absl::optional<double> GetLlGeohashFilterValue() const {
    return ll_geohash_filter_value_;
  }

  inline absl::optional<int64> GetLlSkipDistanceFilter() const {
    return ll_skip_distance_filter_;
  }

  inline absl::optional<int64> GetIsCnyLivePunish() const {
    return is_cny_live_punish_;
  }

  inline absl::optional<int64> GetIsCnyUserPunish() const {
    return is_cny_user_punish_;
  }

  inline absl::optional<absl::Span<const int64>> GetLlPoiCityIdList() const {
    return ll_poi_city_id_list_;
  }

  inline absl::optional<absl::string_view> GetLlGoodsCate2() const {
    return ll_goods_cate_2_;
  }

  inline absl::optional<int64> GetLlGoodsCate3Id() const {
    return ll_goods_cate_3_id_;
  }

  inline absl::optional<int64> GetLlMarkCode() const {
    return ll_mark_code_;
  }

  inline absl::optional<int64> GetLlSlideShowCnt() const {
    return ll_slide_show_cnt_;
  }

  inline absl::optional<int64> GetIsIgBlackAuthor() const {
    return a_is_ig_black_author_;
  }

  inline absl::optional<double> GetKvaContent2Value() const {
    return a_kva_is_content2_;
  }

  inline absl::optional<double> GetKvaContent7Value() const {
    return a_kva_is_content7_;
  }

  inline absl::optional<int64> GetIsPkScriptAuthor() const {
    return a_is_pk_script_;
  }

  inline absl::optional<double> GetRevenueStage() const {
    return a_revenue_stage_;
  }

  inline absl::optional<double> GetAuthorTail() const {
    return a_author_tail_;
  }

  inline absl::optional<double> GetHighArppuValue() const {
    return bigr_hav_in_14d_;
  }

  inline absl::optional<int64> GetIsGoodAuthor() const {
    return a_is_good_author_;
  }

  inline absl::optional<int64> GetIsCopyrightInfriAuthor() const {
    return a_is_copyright_infri_;
  }

  inline absl::optional<int64> GetIsDigitalAuthor() const {
    return a_is_digital_;
  }

  inline absl::optional<int64> GetIsLongtimeAuthor() const {
    return a_is_longtime_;
  }

  inline absl::optional<int64> GetBucketTotalScore() const {
    return a_bucket_total_score_;
  }

  inline absl::optional<int64> GetIsCopyLive() const {
    return l_is_copy_;
  }

  inline absl::optional<int64> GetIsNewAppVer() const {
    return a_is_new_app_ver_;
  }

  inline absl::optional<int64> GetIsChatAudioExemption() const {
    return a_is_chat_audio_exemption_;
  }

  inline absl::optional<int64> GetLiveBeginShowType() const {
    return l_begin_show_type_;
  }

  inline absl::optional<int64> GetIsRandomBlockedAuthor() const {
    return a_is_random_blocked_;
  }

  inline absl::optional<int64> GetIsLiarAuthor() const {
    return a_is_liar_;
  }

  inline absl::optional<int64> GetIsHrelationAuthor() const {
    return a_is_hrelation_author_;
  }

  inline absl::optional<int64> GetIsLowQualityV1() const {
    return a_is_low_quality_v1_;
  }

  inline absl::optional<int64> GetIsLowQualityV2() const {
    return a_is_low_quality_v2_;
  }

  inline absl::optional<int64> GetIsLowQualityV3() const {
    return a_is_low_quality_v3_;
  }

  inline absl::optional<int64> GetIsLowQualityV4() const {
    return a_is_low_quality_v4_;
  }

  inline absl::optional<int64> GetIsLowQualityV5() const {
    return a_is_low_quality_v5_;
  }

  inline absl::optional<int64> GetIsLowQualityV6() const {
    return a_is_low_quality_v6_;
  }

  inline absl::optional<int64> GetIsLowQualityV7() const {
    return a_is_low_quality_v7_;
  }

  inline absl::optional<int64> GetIsLowQualityV8() const {
    return a_is_low_quality_v8_;
  }

  inline absl::optional<int64> GetIsLowQualityV9() const {
    return a_is_low_quality_v9_;
  }

  inline absl::optional<int64> GetIsLowQualityV10() const {
    return a_is_low_quality_v10_;
  }

  inline absl::optional<int64> GetThresholdListV1LimitNum() const {
    return a_in_threshold_list_v1_limit_num_;
  }

  inline absl::optional<int64> GetThresholdListV2LimitNum() const {
    return a_in_threshold_list_v2_limit_num_;
  }

  inline absl::optional<int64> GetKconfBlacklistExemption() const {
    return a_in_kconf_blacklist_but_exemption_;
  }

  inline absl::optional<int64> GetGlobalGovernAuthorBucket() const {
    return a_global_govern_author_bucket_;
  }

  inline absl::optional<int64> GetAuthorAffairType() const {
    return a_affair_type_;
  }

  inline absl::optional<int64> GetAuthorInMarketWhitelist() const {
    return a_in_market_whitelist_;
  }

  inline absl::optional<int64> GetAuthorStreamHealth() const {
    return a_stream_health_;
  }

  inline absl::optional<int64> GetLIsAigc() const {
    return ll_is_aigc_;
  }

  inline absl::optional<absl::string_view> GetLGoodsSecondCate() const {
    return ll_goods_second_cate_;
  }

  inline absl::optional<absl::string_view> GetLGoodsSecondCateNew() const {
    return ll_goods_second_cate_new_;
  }

  inline absl::optional<int64> GetLFilterUserCityInItemCityList() const {
    return ll_filter_user_city_in_item_city_list_;
  }

  inline absl::optional<int64> GetIsMerchantVirtualHumanLive() const {
    return l_is_merchant_virtual_human_;
  }

  inline absl::optional<int64> GetReplaceAuthorId() const {
    return replace_author_id_;
  }

  inline absl::optional<absl::Span<const int64>> GetKeyList() const {
    return key_list_;
  }

  inline absl::optional<std::vector<absl::string_view>> GetEffectList() const {
    return effect_list_;
  }

  inline absl::optional<int64> GetFollowClick() const {
    return follow_click_;
  }

  inline absl::optional<absl::Span<const int64>> GetGradeLabel() const {
    return grade_label_;
  }

  inline absl::optional<int64> GetSlideAuthorVersion() const {
    return slide_author_version_;
  }

  inline absl::optional<int64> GetHasItemInfo() const {
    return has_item_info_;
  }

  inline absl::optional<int64> GetHasDynamicItemInfo() const {
    return has_dynamic_item_info_;
  }

  inline absl::optional<int64> GetIsCoverMerchantLive() const {
    return is_cover_merchant_live_;
  }

  inline absl::optional<int64> GetIsMerchantLive() const {
    return is_merchant_live_;
  }

  inline absl::optional<double> GetMmuImpreScore() const {
    return mmu_impre_score_;
  }

  inline absl::optional<absl::string_view> GetACategoryType() const {
    return a_category_type_;
  }

  inline absl::optional<int64> GetAuthorGoodAuthorV4Raw() const {
    return a_good_author_v4_raw_;
  }

  inline absl::optional<absl::Span<const int64>>
    GetLiveDistributionMarkCodeList() const {
    return l_distribution_mark_code_list_;
  }

  inline absl::optional<int64> GetAuthorIsCopyGreyExemptionV1() const {
    return a_is_copy_grey_exemption_v1_;
  }

  inline absl::optional<int64> GetAuthorIsCopyGreyExemptionV2() const {
    return a_is_copy_grey_exemption_v2_;
  }

  inline absl::optional<int64> GetIsFollowAuthor() const {
    return a_is_follow_author_;
  }

  inline absl::optional<int64> GetBigVType() const {
    return big_v_type_;
  }

  inline absl::optional<int64> GetExploreAuthorVersion() const {
    return explore_author_version_;
  }

  inline absl::optional<std::vector<absl::string_view>> GetLiveAllTags() const {
    return live_all_tags_;
  }

  inline absl::optional<int64> GetIsSellerAuthor() const {
    return is_seller_author_;
  }

  inline absl::optional<int64> GetColdStartReasonCount() const {
    return cold_start_reason_count_;
  }

  inline absl::optional<double> GetRecruitAuthorMapFilterProb() const {
    return recruit_author_map_filter_prob_;
  }

  inline absl::optional<int64> GetAuthorBatchId() const {
    return author_batch_id_;
  }

  inline absl::optional<int64> GetVoicePartyPlayType() const {
    return voice_party_play_type_;
  }

  inline absl::optional<int64> GetReasonIsOnly1825() const {
    return reason_is_only_1825_;
  }

  inline absl::optional<absl::string_view> GetPkCoreAuthorFlag() const {
    return is_pk_core_author_flag_;
  }

  inline absl::optional<int64> GetIsPkNow() const {
    return is_pk_now_;
  }

  inline absl::optional<int64> GetIsPorn() const {
    return a_is_porn_;
  }

  inline absl::optional<int64> GetIsStory() const {
    return a_is_story_;
  }

  inline absl::optional<std::vector<absl::string_view>> GetlViewTypeNames() const {
    return l_view_type_names_;
  }

  inline absl::optional<int64> GetRevenueAmount60d() const {
    return revenue_amount_60d_;
  }

  inline absl::optional<int64> GetlRecruit5RFrequencyFilter() const {
    return l_recruit_5R_frequency_filter_;
  }

 private:
  static inline const ItemAttr *GetItemAttrAccessor(
      const folly::F14FastMap<std::string, const ItemAttr *> &item_accessor_map,
      const std::string &attr_name) {
    auto iter = item_accessor_map.find(attr_name);
    return iter == item_accessor_map.end() ? nullptr : iter->second;
  }

  // item attr accessors
  const ItemAttr *photo_id_accessor_ = nullptr;
  const ItemAttr *is_shop_live_accessor_ = nullptr;
  const ItemAttr *aGoodAuthorRangeV4KV_accessor_ = nullptr;
  const ItemAttr *aGoodAuthorConsumeV4IntKV_accessor_ = nullptr;
  const ItemAttr *recruit_priority_attr_accessor_ = nullptr;
  const ItemAttr *mmu_tag_id_accessor_ = nullptr;
  const ItemAttr *reason_list_accessor_ = nullptr;
  const ItemAttr *is_game_live_accessor_ = nullptr;
  const ItemAttr *is_audio_live_room_accessor_ = nullptr;
  const ItemAttr *is_ktv_accessor_ = nullptr;
  const ItemAttr *is_theater_accessor_ = nullptr;
  const ItemAttr *is_teampk_accessor_ = nullptr;
  const ItemAttr *is_voice_party_accessor_ = nullptr;
  const ItemAttr *a_single_kill_label_accessor_ = nullptr;
  const ItemAttr *l_merchant_themis_author_kv_accessor_ = nullptr;
  const ItemAttr *author_id_accessor_ = nullptr;
  const ItemAttr *l_thompson_neg_gamora_v1_accessor_ = nullptr;
  const ItemAttr *l_thompson_neg_nebula_v1_accessor_ = nullptr;
  const ItemAttr *l_thompson_neg_gamora_v2_accessor_ = nullptr;
  const ItemAttr *l_thompson_neg_nebula_v2_accessor_ = nullptr;
  const ItemAttr *mmu_tag_info_tag_list_lv2_accessor_ = nullptr;
  const ItemAttr *l_mmu_seg_attr_list_accessor_ = nullptr;
  const ItemAttr *mmu_user_hetu_tag_accessor_ = nullptr;
  const ItemAttr *author_info_is_seller_author_v2_accessor_ = nullptr;
  const ItemAttr *l_risk_level_accessor_ = nullptr;
  const ItemAttr *l_ring_ball_accessor_ = nullptr;
  const ItemAttr *l_realshow_recent_accessor_ = nullptr;
  const ItemAttr *l_report_recent_accessor_ = nullptr;
  const ItemAttr *l_negative_recent_accessor_ = nullptr;
  const ItemAttr *l_dur_per_show_accessor_ = nullptr;
  const ItemAttr *l_shuffle_policy_accessor_ = nullptr;
  const ItemAttr *punish_sirius_all_accessor_ = nullptr;
  const ItemAttr *l_recruit_query_room_bool_accessor_ = nullptr;
  const ItemAttr *l_live_house_is_live_tag_accessor_ = nullptr;
  const ItemAttr *l_live_house_is_una_live_tag_accessor_ = nullptr;
  const ItemAttr *l_is_local_life_accessor_ = nullptr;
  const ItemAttr *l_poi_geo_hash4_list_accessor_ = nullptr;
  const ItemAttr *l_live_zero_punish_ab_suffix_list_accessor_ = nullptr;
  const ItemAttr *slide_real_show_count_accessor_ = nullptr;
  const ItemAttr *slide_real_show_all_day_count_accessor_ = nullptr;
  const ItemAttr *slide_inner_real_show_all_day_count_accessor_ = nullptr;
  const ItemAttr *slide_outter_real_show_all_day_count_accessor_ = nullptr;
  const ItemAttr *explore_inner_real_show_all_day_count_accessor_ = nullptr;
  const ItemAttr *explore_outer_real_show_all_day_count_accessor_ = nullptr;
  const ItemAttr *live_tag_real_show_all_day_count_accessor_ = nullptr;
  const ItemAttr *a_c_start_author_bucket_accessor_ = nullptr;
  const ItemAttr *a_c_start_expect_total_show_accessor_ = nullptr;
  const ItemAttr *a_c_start_current_total_show_accessor_ = nullptr;
  const ItemAttr *a_c_start_expect_total_gift_accessor_ = nullptr;
  const ItemAttr *a_c_start_current_total_gift_accessor_ = nullptr;
  const ItemAttr *a_c_start_expect_total_show_v1_accessor_ = nullptr;
  const ItemAttr *a_c_start_expect_total_show_v2_accessor_ = nullptr;
  const ItemAttr *a_c_start_expect_total_show_v3_accessor_ = nullptr;
  const ItemAttr *a_c_start_expect_total_show_v4_accessor_ = nullptr;
  const ItemAttr *a_c_start_expect_total_gift_v1_accessor_ = nullptr;
  const ItemAttr *a_c_start_expect_total_gift_v2_accessor_ = nullptr;
  const ItemAttr *a_c_start_expect_total_gift_v3_accessor_ = nullptr;
  const ItemAttr *a_c_start_expect_total_gift_v4_accessor_ = nullptr;
  const ItemAttr *author_support_type_accessor_ = nullptr;
  const ItemAttr *cold_start_queue_reason_value_accessor_ = nullptr;
  const ItemAttr *a_is_house_live_kv_accessor_ = nullptr;
  const ItemAttr *l_live_house_is_dream_house_accessor_ = nullptr;
  const ItemAttr *a_is_private_user_kv_accessor_ = nullptr;
  const ItemAttr *a_recruit_org_id_kv_accessor_ = nullptr;
  const ItemAttr *a_violation_punish_rt_accessor_ = nullptr;
  const ItemAttr *a_violation_punish_offline_accessor_ = nullptr;
  const ItemAttr *mmu_tag_info_tag_list_lv1_accessor_ = nullptr;
  const ItemAttr *mmu_tag_info_tag_list_lv3_accessor_ = nullptr;
  const ItemAttr *l_lat_kv_accessor_ = nullptr;
  const ItemAttr *l_lon_kv_accessor_ = nullptr;
  const ItemAttr *l_live_location_city_accessor_ = nullptr;
  const ItemAttr *l_live_location_province_accessor_ = nullptr;
  const ItemAttr *grade_label_for_temp_accessor_ = nullptr;
  const ItemAttr *grade_label_for_aid_temp_accessor_ = nullptr;
  const ItemAttr *l_is_chat_gird_whitelist_aid_accessor_ = nullptr;
  const ItemAttr *l_voice_party_status_accessor_ = nullptr;
  const ItemAttr *l_voice_party_open_video_status_accessor_ = nullptr;
  const ItemAttr *a_content_emb_v2_accessor_ = nullptr;
  const ItemAttr *l_hot_explore_punish_tag_accessor_ = nullptr;
  const ItemAttr *l_hot_small_business_punish_city_accessor_ = nullptr;
  const ItemAttr *l_hot_explore_punish_city_accessor_ = nullptr;
  const ItemAttr *l_hot_slide_punish_city_accessor_ = nullptr;
  const ItemAttr *l_interactive_game_type_accessor_ = nullptr;
  const ItemAttr *item_off_filter_list_accessor_ = nullptr;
  const ItemAttr *ll_brand_name_accessor_ = nullptr;
  const ItemAttr *ll_brand_id_accessor_ = nullptr;
  const ItemAttr *ll_new_brand_id_accessor_ = nullptr;
  const ItemAttr *ll_merchant_id_accessor_ = nullptr;
  const ItemAttr *ll_mmu_accessor_ = nullptr;
  const ItemAttr *ll_restrict_author_accessor_ = nullptr;
  const ItemAttr *ll_fireworks_author_accessor_ = nullptr;
  const ItemAttr *ll_geohash_distance_accessor_ = nullptr;
  const ItemAttr *ll_geohash_filter_value_accessor_ = nullptr;
  const ItemAttr *ll_skip_distance_filter_accessor_ = nullptr;
  const ItemAttr *is_cny_live_punish_accessor_ = nullptr;
  const ItemAttr *is_cny_user_punish_accessor_ = nullptr;
  const ItemAttr *ll_poi_city_id_list_accessor_ = nullptr;
  const ItemAttr *ll_goods_cate_2_accessor_ = nullptr;
  const ItemAttr *ll_goods_cate_3_id_accessor_ = nullptr;
  const ItemAttr *ll_mark_code_accessor_ = nullptr;
  const ItemAttr *ll_slide_show_cnt_accessor_ = nullptr;
  const ItemAttr *a_is_ig_black_accessor_ = nullptr;
  const ItemAttr *a_kva_is_content2_accessor_ = nullptr;
  const ItemAttr *a_kva_is_content7_accessor_ = nullptr;
  const ItemAttr *a_is_pk_script_accessor_ = nullptr;
  const ItemAttr *a_revenue_stage_accessor_ = nullptr;
  const ItemAttr *a_author_tail_accessor_ = nullptr;
  const ItemAttr *bigr_hav_in_14d_accessor_ = nullptr;
  const ItemAttr *a_is_good_author_accessor_ = nullptr;
  const ItemAttr *a_is_copyright_infri_accessor_ = nullptr;
  const ItemAttr *a_is_digital_accessor_ = nullptr;
  const ItemAttr *a_is_longtime_accessor_ = nullptr;
  const ItemAttr *a_bucket_total_score_accessor_ = nullptr;
  const ItemAttr *l_is_copy_accessor_ = nullptr;
  const ItemAttr *a_is_chat_audio_exemption_accessor_ = nullptr;
  const ItemAttr *l_begin_show_type_accessor_ = nullptr;
  const ItemAttr *a_is_new_app_ver_accessor_ = nullptr;
  const ItemAttr *a_is_random_blocked_accessor_ = nullptr;
  const ItemAttr *a_is_liar_accessor_ = nullptr;
  const ItemAttr *a_is_hrelation_author_accessor_ = nullptr;
  const ItemAttr *a_is_low_quality_v1_accessor_ = nullptr;
  const ItemAttr *a_is_low_quality_v2_accessor_ = nullptr;
  const ItemAttr *a_is_low_quality_v3_accessor_ = nullptr;
  const ItemAttr *a_is_low_quality_v4_accessor_ = nullptr;
  const ItemAttr *a_is_low_quality_v5_accessor_ = nullptr;
  const ItemAttr *a_is_low_quality_v6_accessor_ = nullptr;
  const ItemAttr *a_is_low_quality_v7_accessor_ = nullptr;
  const ItemAttr *a_is_low_quality_v8_accessor_ = nullptr;
  const ItemAttr *a_is_low_quality_v9_accessor_ = nullptr;
  const ItemAttr *a_is_low_quality_v10_accessor_ = nullptr;
  const ItemAttr *a_in_threshold_list_v1_limit_num_accessor_ = nullptr;
  const ItemAttr *a_in_threshold_list_v2_limit_num_accessor_ = nullptr;
  const ItemAttr *a_in_kconf_blacklist_but_exemption_accessor_ = nullptr;
  const ItemAttr *a_global_govern_author_bucket_accessor_ = nullptr;
  const ItemAttr *a_affair_type_accessor_ = nullptr;
  const ItemAttr *a_in_market_whitelist_accessor_ = nullptr;
  const ItemAttr *a_stream_health_accessor_ = nullptr;
  const ItemAttr *ll_is_aigc_accessor_ = nullptr;
  const ItemAttr *ll_goods_second_cate_accessor_ = nullptr;
  const ItemAttr *ll_goods_second_cate_new_accessor_ = nullptr;
  const ItemAttr *ll_filter_user_city_in_item_city_list_accessor_ = nullptr;
  const ItemAttr *l_is_merchant_virtual_human_accessor_ = nullptr;
  const ItemAttr *replace_author_id_accessor_ = nullptr;
  const ItemAttr *key_list_accessor_ = nullptr;
  const ItemAttr *effect_list_accessor_ = nullptr;
  const ItemAttr *follow_click_accessor_ = nullptr;
  const ItemAttr *grade_label_accessor_ = nullptr;
  const ItemAttr *slide_author_version_accessor_ = nullptr;
  const ItemAttr *has_item_info_accessor_ = nullptr;
  const ItemAttr *has_dynamic_item_info_accessor_ = nullptr;
  const ItemAttr *is_cover_merchant_live_accessor_ = nullptr;
  const ItemAttr *is_merchant_live_accessor_ = nullptr;
  const ItemAttr *mmu_impre_score_accessor_ = nullptr;
  const ItemAttr *a_category_type_accessor_ = nullptr;
  const ItemAttr *a_good_author_v4_raw_accessor_ = nullptr;
  const ItemAttr *l_distribution_mark_code_list_accessor_ = nullptr;
  const ItemAttr *a_is_copy_grey_exemption_v1_accessor_ = nullptr;
  const ItemAttr *a_is_copy_grey_exemption_v2_accessor_ = nullptr;
  const ItemAttr *a_is_follow_author_accessor_ = nullptr;
  const ItemAttr *big_v_type_accessor_ = nullptr;
  const ItemAttr *explore_author_version_accessor_ = nullptr;
  const ItemAttr *live_all_tags_accessor_ = nullptr;
  const ItemAttr *is_seller_author_accessor_ = nullptr;
  const ItemAttr *cold_start_reason_count_accessor_ = nullptr;
  const ItemAttr *recruit_author_map_filter_prob_accessor_ = nullptr;
  const ItemAttr *author_batch_id_accessor_ = nullptr;
  const ItemAttr *voice_party_play_type_accessor_ = nullptr;
  const ItemAttr *reason_is_only_1825_accessor_ = nullptr;
  const ItemAttr *is_pk_core_author_flag_accessor_ = nullptr;
  const ItemAttr *is_pk_now_accessor_ = nullptr;
  const ItemAttr *a_is_porn_accessor_ = nullptr;
  const ItemAttr *a_is_story_accessor_ = nullptr;
  const ItemAttr *l_view_type_names_accessor_ = nullptr;
  const ItemAttr *l_recruit_5R_frequency_filter_accessor_ = nullptr;
  const ItemAttr *revenue_amount_60d_accessor_ = nullptr;

  // item attr
  absl::optional<int64> photo_id_;
  absl::optional<int64> is_shop_live_;
  absl::optional<int64> aGoodAuthorRangeV4KV_;
  absl::optional<int64> recruit_priority_attr_;
  absl::optional<absl::Span<const int64>> mmu_tag_id_;
  absl::optional<absl::Span<const int64>> reason_list_;
  absl::optional<int64> is_game_live_;
  absl::optional<int64> is_audio_live_room_;
  absl::optional<int64> is_ktv_;
  absl::optional<int64> is_theater_;
  absl::optional<int64> is_teampk_;
  absl::optional<int64> is_voice_party_;
  absl::optional<int64> a_single_kill_label_;
  absl::optional<int64> l_merchant_themis_author_kv_;
  absl::optional<int64> author_id_;
  absl::optional<double> l_thompson_neg_gamora_v1_;
  absl::optional<double> l_thompson_neg_nebula_v1_;
  absl::optional<double> l_thompson_neg_gamora_v2_;
  absl::optional<double> l_thompson_neg_nebula_v2_;
  absl::optional<absl::Span<const int64>> mmu_tag_info_tag_list_lv2_;
  absl::optional<absl::Span<const int64>> l_mmu_seg_attr_list_;
  absl::optional<absl::Span<const int64>> mmu_user_hetu_tag_;
  absl::optional<int64> author_info_is_seller_author_v2_;
  absl::optional<int64> l_risk_level_;
  absl::optional<int64> good_author_range_v4_;
  absl::optional<int64> consume_good_author_range_v4_;
  absl::optional<int64> l_ring_ball_;
  absl::optional<int64> l_realshow_recent_;
  absl::optional<int64> l_report_recent_;
  absl::optional<int64> l_negative_recent_;
  absl::optional<double> l_dur_per_show_;
  absl::optional<int64> l_shuffle_policy_;
  absl::optional<absl::string_view> punish_sirius_all_;
  absl::optional<int64> l_recruit_query_room_bool_;
  absl::optional<int64> l_live_house_is_live_tag_;
  absl::optional<int64> l_live_house_is_una_live_tag_;
  absl::optional<int64> l_is_local_life_;
  absl::optional<std::vector<absl::string_view>> l_poi_geo_hash4_list_;
  absl::optional<std::vector<absl::string_view>> l_live_zero_punish_ab_suffix_list_;
  absl::optional<int64> slide_real_show_count_;
  absl::optional<int64> slide_real_show_all_day_count_;
  absl::optional<int64> slide_inner_real_show_all_day_count_;
  absl::optional<int64> slide_outter_real_show_all_day_count_;
  absl::optional<int64> explore_inner_real_show_all_day_count_;
  absl::optional<int64> explore_outer_real_show_all_day_count_;
  absl::optional<int64> live_tag_real_show_all_day_count_;
  absl::optional<int64> a_c_start_author_bucket_;
  absl::optional<int64> a_c_start_expect_total_show_;
  absl::optional<int64> a_c_start_current_total_show_;
  absl::optional<int64> a_c_start_expect_total_gift_;
  absl::optional<int64> a_c_start_current_total_gift_;
  absl::optional<int64> a_c_start_expect_total_show_v1_;
  absl::optional<int64> a_c_start_expect_total_show_v2_;
  absl::optional<int64> a_c_start_expect_total_show_v3_;
  absl::optional<int64> a_c_start_expect_total_show_v4_;
  absl::optional<int64> a_c_start_expect_total_gift_v1_;
  absl::optional<int64> a_c_start_expect_total_gift_v2_;
  absl::optional<int64> a_c_start_expect_total_gift_v3_;
  absl::optional<int64> a_c_start_expect_total_gift_v4_;
  absl::optional<absl::string_view> author_support_type_;
  absl::optional<int64> cold_start_queue_reason_value_;
  absl::optional<int64> a_is_house_live_kv_;
  absl::optional<int64> l_live_house_is_dream_house_;
  absl::optional<absl::string_view> a_is_private_user_kv_;
  absl::optional<int64> a_recruit_org_id_kv_;
  absl::optional<int64> a_violation_punish_rt_;
  absl::optional<int64> a_violation_punish_offline_;
  absl::optional<absl::Span<const int64>> mmu_tag_info_tag_list_lv1_;
  absl::optional<absl::Span<const int64>> mmu_tag_info_tag_list_lv3_;
  absl::optional<double> l_lat_kv_;
  absl::optional<double> l_lon_kv_;
  absl::optional<absl::string_view> l_live_location_city_;
  absl::optional<absl::string_view> l_live_location_province_;
  absl::optional<absl::Span<const int64>> grade_label_for_temp_;
  absl::optional<absl::Span<const int64>> grade_label_for_aid_temp_;
  absl::optional<int64> l_is_chat_gird_whitelist_aid_;
  absl::optional<double> l_voice_party_status_;
  absl::optional<int64> l_voice_party_open_video_status_;
  absl::optional<absl::Span<const double>> a_content_emb_v2_;
  absl::optional<int64> l_hot_explore_punish_tag_;
  absl::optional<absl::Span<const int64>> l_hot_small_business_punish_city_;
  absl::optional<absl::Span<const int64>> l_hot_explore_punish_city_;
  absl::optional<absl::Span<const int64>> l_hot_slide_punish_city_;
  absl::optional<int64> l_interactive_game_type_;
  absl::optional<absl::Span<const int64>> item_off_filter_list_;
  absl::optional<absl::string_view> ll_brand_name_;
  absl::optional<int64> ll_brand_id_;
  absl::optional<int64> ll_new_brand_id_;
  absl::optional<int64> ll_merchant_id_;
  absl::optional<int64> ll_mmu_;
  absl::optional<int64> ll_restrict_author_;
  absl::optional<int64> ll_fireworks_author_;
  absl::optional<double> ll_geohash_distance_;
  absl::optional<double> ll_geohash_filter_value_;
  absl::optional<int64> ll_skip_distance_filter_;
  absl::optional<int64> is_cny_live_punish_;
  absl::optional<int64> is_cny_user_punish_;
  absl::optional<absl::Span<const int64>> ll_poi_city_id_list_;
  absl::optional<absl::string_view> ll_goods_cate_2_;
  absl::optional<int64> ll_goods_cate_3_id_;
  absl::optional<int64> ll_mark_code_;
  absl::optional<int64> ll_slide_show_cnt_;
  absl::optional<int64> a_is_ig_black_author_;
  absl::optional<double> a_kva_is_content2_;
  absl::optional<double> a_kva_is_content7_;
  absl::optional<int64> a_is_pk_script_;
  absl::optional<double> a_revenue_stage_;
  absl::optional<double> a_author_tail_;
  absl::optional<double> bigr_hav_in_14d_;
  absl::optional<int64> a_is_good_author_;
  absl::optional<int64> a_is_copyright_infri_;
  absl::optional<int64> a_is_digital_;
  absl::optional<int64> a_is_longtime_;
  absl::optional<int64> a_bucket_total_score_;
  absl::optional<int64> l_is_copy_;
  absl::optional<int64> a_is_new_app_ver_;
  absl::optional<int64> a_is_chat_audio_exemption_;
  absl::optional<int64> l_begin_show_type_;
  absl::optional<int64> a_is_random_blocked_;
  absl::optional<int64> a_is_liar_;
  absl::optional<int64> a_is_hrelation_author_;
  absl::optional<int64> a_is_low_quality_v1_;
  absl::optional<int64> a_is_low_quality_v2_;
  absl::optional<int64> a_is_low_quality_v3_;
  absl::optional<int64> a_is_low_quality_v4_;
  absl::optional<int64> a_is_low_quality_v5_;
  absl::optional<int64> a_is_low_quality_v6_;
  absl::optional<int64> a_is_low_quality_v7_;
  absl::optional<int64> a_is_low_quality_v8_;
  absl::optional<int64> a_is_low_quality_v9_;
  absl::optional<int64> a_is_low_quality_v10_;
  absl::optional<int64> a_in_threshold_list_v1_limit_num_;
  absl::optional<int64> a_in_threshold_list_v2_limit_num_;
  absl::optional<int64> a_in_kconf_blacklist_but_exemption_;
  absl::optional<int64> a_global_govern_author_bucket_;
  absl::optional<int64> a_affair_type_;
  absl::optional<int64> a_in_market_whitelist_;
  absl::optional<int64> a_stream_health_;
  absl::optional<int64> ll_is_aigc_;
  absl::optional<absl::string_view> ll_goods_second_cate_;
  absl::optional<absl::string_view> ll_goods_second_cate_new_;
  absl::optional<int64> ll_filter_user_city_in_item_city_list_;
  absl::optional<int64> l_is_merchant_virtual_human_;
  absl::optional<int64> replace_author_id_;
  absl::optional<absl::Span<const int64>> key_list_;
  absl::optional<std::vector<absl::string_view>> effect_list_;
  absl::optional<int64> follow_click_;
  absl::optional<absl::Span<const int64>> grade_label_;
  absl::optional<int64> slide_author_version_;
  absl::optional<int64> has_item_info_;
  absl::optional<int64> has_dynamic_item_info_;
  absl::optional<int64> is_cover_merchant_live_;
  absl::optional<int64> is_merchant_live_;
  absl::optional<double> mmu_impre_score_;
  absl::optional<absl::string_view> a_category_type_;
  absl::optional<int64> a_good_author_v4_raw_;
  absl::optional<absl::Span<const int64>> l_distribution_mark_code_list_;
  absl::optional<int64> a_is_copy_grey_exemption_v1_;
  absl::optional<int64> a_is_copy_grey_exemption_v2_;
  absl::optional<int64> a_is_follow_author_;
  absl::optional<int64> big_v_type_;
  absl::optional<int64> explore_author_version_;
  absl::optional<std::vector<absl::string_view>> live_all_tags_;
  absl::optional<int64> is_seller_author_;
  absl::optional<int64> cold_start_reason_count_;
  absl::optional<double> recruit_author_map_filter_prob_;
  absl::optional<int64> author_batch_id_;
  absl::optional<int64> voice_party_play_type_;
  absl::optional<int64> reason_is_only_1825_;
  absl::optional<absl::string_view> is_pk_core_author_flag_;
  absl::optional<int64> is_pk_now_;
  absl::optional<int64> a_is_porn_;
  absl::optional<int64> a_is_story_;
  absl::optional<std::vector<absl::string_view>> l_view_type_names_;
  absl::optional<int64> revenue_amount_60d_;
  absl::optional<int64> l_recruit_5R_frequency_filter_;
};

}  // namespace livestream
}  // namespace platform
}  // namespace ks
