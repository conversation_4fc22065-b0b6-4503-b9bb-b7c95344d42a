#include "dragon/src/processor/ext/livestream/module/filter/l_view_type_names_filter.h"
#include <string>
#include <vector>

namespace ks {
namespace platform {
namespace livestream {

void LViewTypeNamesFilter::Init(
    ReadableRecoContextInterface *context, const base::J<PERSON> *config) {
    auto block_l_view_type_names_list_str = livestream::GetStringCommonAttr(
        context, config, "block_l_view_type_names_list_str_attr").value_or("");
    block_l_view_type_names_list_.clear();
    std::vector<absl::string_view> block_l_view_type_names_list_str_split = absl::StrSplit(
        block_l_view_type_names_list_str, absl::ByAnyChar(","), absl::SkipEmpty());
    if (!block_l_view_type_names_list_str_split.empty()) {
      for (const auto str : block_l_view_type_names_list_str_split) {
        block_l_view_type_names_list_.emplace(str);
      }
    }
}

bool LViewTypeNamesFilter::ShouldRemove(
    const CommonRecoResult &result) {
  auto l_view_type_names = item_attr_container_->GetlViewTypeNames()
    .value_or(std::vector<absl::string_view>());
  for (const auto &tag : l_view_type_names) {
    if (block_l_view_type_names_list_.find(std::string(tag)) != block_l_view_type_names_list_.end()) {
      return true;
    }
  }
  return false;
}

}  // namespace livestream
}  // namespace platform
}  // namespace ks
