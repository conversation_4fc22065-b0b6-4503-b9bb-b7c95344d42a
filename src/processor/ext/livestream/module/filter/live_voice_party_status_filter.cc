#include "dragon/src/processor/ext/livestream/module/filter/live_voice_party_status_filter.h"

#include <vector>

namespace ks {
namespace platform {
namespace livestream {

void LiveVoicePartyStatusFilter::Init(
    ReadableRecoContextInterface *context, const base::<PERSON><PERSON> *config) {
  enable_chat_gird_whitelist_index_ = livestream::GetIntCommonAttr(
      context, config, "enable_chat_gird_whitelist_index_attr").value_or(0);
  auto except_aid_list = livestream::GetIntListCommonAttr(
      context, config, "except_aid_list_attr").value_or(absl::Span<const int64>());
  except_aid_set_.clear();
  except_aid_set_.insert(except_aid_list.begin(), except_aid_list.end());
  enable_live_voice_party_open_video_ = livestream::GetIntCommonAttr(
      context, config, "enable_live_voice_party_open_video_attr").value_or(0);
  enable_release_audio_live_author_filter_ = livestream::GetIntCommonAttr(
      context, config, "enable_audio_live_room_skip_filter_attr").value_or(0);
  enable_audio_live_gift_author_filter_ = livestream::GetIntCommonAttr(
      context, config, "enable_audio_live_gift_author_filter_attr").value_or(0);
  release_batch_list_.clear();
  voice_party_play_type_list_.clear();
  auto release_batch_list_str = livestream::GetStringCommonAttr(
      context, config, "release_batch_list_attr").value_or("0");
  auto voice_party_play_type_list_str = livestream::GetStringCommonAttr(
      context, config, "voice_party_play_type_list_attr").value_or("");
  // 将 release_batch_list_str 分割成整数列表
  std::vector<absl::string_view> release_batch_list_str_arr =
      absl::StrSplit(release_batch_list_str, ',', absl::SkipWhitespace());
  int value = 0;
  for (const auto &value_str : release_batch_list_str_arr) {
    if (absl::SimpleAtoi(value_str, &value)) {
      release_batch_list_.insert(value);
    }
  }
  // 将 voice_party_play_type_list_str 分割成整数列表
  std::vector<absl::string_view> voice_party_play_type_list_str_arr =
      absl::StrSplit(voice_party_play_type_list_str, ',', absl::SkipWhitespace());
  value = 0;
  for (const auto &value_str : voice_party_play_type_list_str_arr) {
    if (absl::SimpleAtoi(value_str, &value)) {
      voice_party_play_type_list_.insert(value);
    }
  }
}

bool LiveVoicePartyStatusFilter::ShouldRemove(
    const CommonRecoResult &result) {
  int64 need_filter = 1;
  if (enable_chat_gird_whitelist_index_ == 1) {
    need_filter = item_attr_container_->GetLIsChatGirdWhitelistAid().value_or(1);
  } else {
    auto author_id = item_attr_container_->GetAuthorId().value_or(-1);
    if (except_aid_set_.count(author_id)) {
      need_filter = 0;
    }
  }
  auto party_status = item_attr_container_->GetLVoicePartyStatus().value_or(0.0);
  auto video_status = item_attr_container_->GetLVoicePartyOpenVideoStatus().value_or(0);

  auto author_batch_id = item_attr_container_->GetAuthorBatchId().value_or(0);
  auto voice_party_play_type = item_attr_container_->GetVoicePartyPlayType().value_or(0);

  auto revenue_amount_60d = item_attr_container_->GetRevenueAmount60d().value_or(0);
  auto reason_list = item_attr_container_->GetReasonList().value_or(absl::Span<const int64>());

  bool has_reason_1264 = std::find(reason_list.begin(), reason_list.end(), 1264) != reason_list.end();
  bool has_reason_1371 = std::find(reason_list.begin(), reason_list.end(), 1371) != reason_list.end();
  bool has_gift_reasons = has_reason_1264 || has_reason_1371;

  // 判断主播所属 batch 是否处于白名单 batch_list 中，若是，则不需要过滤
  int64 author_batch_filter = 1;
  if (enable_release_audio_live_author_filter_ &&
    voice_party_play_type_list_.find(voice_party_play_type) == voice_party_play_type_list_.end()) {
    // batch_list 不为空且 batch_id 在 list 里，不需要过滤
    if (release_batch_list_.find(author_batch_id) != release_batch_list_.end()) {
      author_batch_filter = 0;
    }
  }

  return (party_status == 1.0 && (need_filter > 0) && (author_batch_filter > 0) &&
      !(enable_live_voice_party_open_video_ == 1 && video_status == 1)) &&
      !(enable_audio_live_gift_author_filter_ && (revenue_amount_60d > 0 || has_gift_reasons));
}

}  // namespace livestream
}  // namespace platform
}  // namespace ks
