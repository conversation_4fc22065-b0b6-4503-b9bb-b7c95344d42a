#include "dragon/src/processor/ext/livestream/module/filter/dual_r1_filter.h"
#include <vector>

namespace ks {
namespace platform {
namespace livestream {

void DualR1Filter::Init(
    ReadableRecoContextInterface *context, const base::<PERSON><PERSON> *config) {
    dual_r1_limit_num_ = livestream::GetIntCommonAttr(
      context, config, "dual_r1_limit_num_attr").value_or(10000000);
    follow_aid_list_.clear();
    auto follow_aid_list = livestream::GetIntListCommonAttr(
        context, config, "follow_aid_list_attr").value_or(absl::Span<const int64>());
    follow_aid_list_.insert(follow_aid_list.begin(), follow_aid_list.end());
}

bool DualR1Filter::ShouldRemove(
    const CommonRecoResult &result) {
  // 已关和确定性 UA 豁免
  auto author_id = item_attr_container_->GetAuthorId().value_or(0);
  bool is_follow_aid = (follow_aid_list_.find(author_id) != follow_aid_list_.end());
  auto a_is_hrelation_author = item_attr_container_->GetIsHrelationAuthor().value_or(0);
  if (is_follow_aid || a_is_hrelation_author > 0) {
    return false;
  }
  // 豁免垂类
  auto recruit_priority_attr = item_attr_container_->GetRecruitPriorityAttr().value_or(0);
  auto is_sign_house_live = item_attr_container_->GetLLiveHouseIsLiveTag().value_or(0);
  auto is_una_house_live = item_attr_container_->GetLLiveHouseIsUnaLiveTag().value_or(0);
  auto l_is_local_life = item_attr_container_->GetLIsLocalLife().value_or(0);
  auto author_info_is_seller_author_v2 = item_attr_container_->GetAuthorInfoIsSellerAuthorV2().value_or(0);
  auto is_shop_live = item_attr_container_->GetIsShopLive().value_or(0);

  bool is_recruit = recruit_priority_attr > 0;
  bool is_house = is_sign_house_live > 0 || is_una_house_live > 0;
  bool is_local = l_is_local_life > 0;
  bool is_merchant = author_info_is_seller_author_v2 > 0 || is_shop_live > 0;
  if (is_recruit || is_house || is_local || is_merchant) {
    return false;
  }

  // 双 R1
  auto revenue_good_author_range_v4 = item_attr_container_->GetGoodAuthorRangeV4().value_or(0);
  auto consume_good_author_range_v4 = item_attr_container_->GetConsumeGoodAuthorRangeV4().value_or(0);
  bool is_dual_r1 = consume_good_author_range_v4 == 1 && revenue_good_author_range_v4 == 1;
  // 盖帽
  int64 slide_inner_all_day_show_num = item_attr_container_->GetSlideInnerRealShowAllDayCount().value_or(0);
  int64 slide_outter_all_day_show_num =
    item_attr_container_->GetSlideOutterRealShowAllDayCount().value_or(0);
  int64 live_show_num = slide_outter_all_day_show_num + slide_inner_all_day_show_num;
  if (is_dual_r1 && live_show_num >= dual_r1_limit_num_) {
    return true;
  }
  return false;
}

}  // namespace livestream
}  // namespace platform
}  // namespace ks
