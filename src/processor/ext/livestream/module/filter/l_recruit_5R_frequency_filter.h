#pragma once

#include <string>
#include "dragon/src/processor/ext/livestream/module/filter/base_filter.h"

namespace ks {
namespace platform {
namespace livestream {

class LRecruit5RFrequencyFilter : public livestream::BaseFilter {
 public:
  LRecruit5RFrequencyFilter() {}
  void Init(
  ReadableRecoContextInterface *context,
    const base::Json *config) override;
  bool ShouldRemove(const CommonRecoResult &result) override;

 private:
  int64 l_recruit_5R_frequency_filter_flag_ = 0;
  DISALLOW_COPY_AND_ASSIGN(LRecruit5RFrequencyFilter);
};

}  // namespace livestream
}  // namespace platform
}  // namespace ks
