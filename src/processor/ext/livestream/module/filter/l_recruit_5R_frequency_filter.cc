#include "dragon/src/processor/ext/livestream/module/filter/l_recruit_5R_frequency_filter.h"
#include <string>
#include <vector>

namespace ks {
namespace platform {
namespace livestream {

void LRecruit5RFrequencyFilter::Init(
    ReadableRecoContextInterface *context, const base::<PERSON><PERSON> *config) {
  l_recruit_5R_frequency_filter_flag_ = livestream::GetIntCommonAttr(
      context, config, "l_recruit_5R_frequency_filter_attr").value_or(0);
}

bool LRecruit5RFrequencyFilter::ShouldR<PERSON>ove(
    const CommonRecoResult &result) {
    if ( 1 == l_recruit_5R_frequency_filter_flag_ ) {
      return true;
    }
  return false;
}

}  // namespace livestream
}  // namespace platform
}  // namespace ks
