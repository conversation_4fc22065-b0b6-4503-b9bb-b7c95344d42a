#pragma once

#include <string>
#include <vector>

#include "dragon/src/processor/ext/livestream/module/filter/base_filter.h"

namespace ks {
namespace platform {
namespace livestream {

class PkValueAuthorFilter : public livestream::BaseFilter {
 public:
  PkValueAuthorFilter() {}
  void Init(
      ReadableRecoContextInterface *context,
      const base::Json *config) override;
  bool ShouldRemove(const CommonRecoResult &result) override;

 private:
  int64 filter_good_author_level_ = 0;
  int64 filter_pk_core_authors_only_ = 0;
  int64 filter_pk_status_only_ = 0;
  int64 enable_partial_filter_ = 0;
  double partial_filter_ratio_ = 1.0;
  int64 filter_author_tail_start_ = -1;
  int64 filter_author_tail_end_ = -1;
  base::PseudoRandom random_;

  DISALLOW_COPY_AND_ASSIGN(PkValueAuthorFilter);
};

}  // namespace livestream
}  // namespace platform
}  // namespace ks
