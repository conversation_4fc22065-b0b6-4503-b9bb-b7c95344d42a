#pragma once

#include "dragon/src/processor/ext/livestream/module/filter/base_filter.h"

namespace ks {
namespace platform {
namespace livestream {

class LiveVoicePartyStatusFilter : public livestream::BaseFilter {
 public:
  LiveVoicePartyStatusFilter() {}
  void Init(
      ReadableRecoContextInterface *context,
      const base::Json *config) override;
  bool ShouldRemove(const CommonRecoResult &result) override;

 private:
  int64 enable_chat_gird_whitelist_index_ = 0;
  int64 enable_release_audio_live_author_filter_ = 0;
  int64 enable_audio_live_gift_author_filter_ = 0;
  folly::F14FastSet<int64> except_aid_set_;
  folly::F14FastSet<int64> release_batch_list_;
  int64 enable_live_voice_party_open_video_ = 0;
  folly::F14FastSet<int64> voice_party_play_type_list_;

  DISALLOW_COPY_AND_ASSIGN(LiveVoicePartyStatusFilter);
};

}  // namespace livestream
}  // namespace platform
}  // namespace ks
