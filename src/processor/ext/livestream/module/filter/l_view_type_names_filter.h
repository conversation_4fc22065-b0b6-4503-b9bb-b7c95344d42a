#pragma once

#include <string>
#include "dragon/src/processor/ext/livestream/module/filter/base_filter.h"

namespace ks {
namespace platform {
namespace livestream {

class LViewTypeNamesFilter : public livestream::BaseFilter {
 public:
  LViewTypeNamesFilter() {}
  void Init(
  ReadableRecoContextInterface *context,
    const base::Json *config) override;
  bool ShouldRemove(const CommonRecoResult &result) override;

 private:
  folly::F14FastSet<std::string> block_l_view_type_names_list_;
  DISALLOW_COPY_AND_ASSIGN(LViewTypeNamesFilter);
};

}  // namespace livestream
}  // namespace platform
}  // namespace ks
