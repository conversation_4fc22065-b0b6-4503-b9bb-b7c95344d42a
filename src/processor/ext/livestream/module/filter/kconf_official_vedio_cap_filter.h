#pragma once

#include "dragon/src/processor/ext/livestream/module/filter/base_filter.h"

namespace ks {
namespace platform {
namespace livestream {


class KconfOfficialVedioCapFilter : public livestream::BaseFilter {
 public:
  KconfOfficialVedioCapFilter() {}
  void Init(
      ReadableRecoContextInterface *context,
      const base::Json *config) override;
  bool ShouldRemove(const CommonRecoResult &result) override;

 private:
  folly::F14FastMap<int64, int64> kconf_official_vedio_cap_num_map_;

  DISALLOW_COPY_AND_ASSIGN(KconfOfficialVedioCapFilter);
};

}  // namespace livestream
}  // namespace platform
}  // namespace ks
