#include "dragon/src/processor/ext/livestream/module/filter/local_life_live_filter.h"

#include <string>
#include <vector>

namespace ks {
namespace platform {
namespace livestream {

void LocalLifeLiveFilter::Init(
    ReadableRecoContextInterface *context, const base::J<PERSON> *config) {
    user_level_ = livestream::GetIntCommonAttr(context, config, "ll_user_level_attr").value_or(0);
    u_level_ = livestream::GetIntCommonAttr(context, config, "ll_u_level_attr").value_or(0);
    user_id_ = livestream::GetIntCommonAttr(context, config, "lll_user_id_attr").value_or(0);
    enable_distance_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_distance_filter_attr").value_or(0);
    enable_real_live_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_real_live_filter_attr").value_or(0);
    enable_mmu_white_list_author_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_mmu_white_list_author_attr").value_or(0);
    enable_brand_browsed_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_brand_browsed_filter_attr").value_or(0);
    enable_brand_browsed_filter_v2_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_brand_browsed_filter_v2_attr").value_or(0);
    enable_brand_paid_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_brand_paid_filter_attr").value_or(0);
    enable_all_brand_browsed_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_ll_all_brand_browsed_filter_attr").value_or(0);
    enable_all_brand_browsed_filter_replace_ = livestream::GetIntCommonAttr(context, config,
        "enable_ll_all_brand_browsed_filter_replace_attr").value_or(0);
    enable_all_brand_browsed_filter_minutes_ = livestream::GetIntCommonAttr(context, config,
        "ebable_lll_real_show_brand_browsed_minutes_gap_filter_attr").value_or(0);
    enable_all_brand_browsed_filter_minutes_replace_ = livestream::GetIntCommonAttr(context, config,
        "ebable_lll_real_show_brand_browsed_minutes_gap_filter_replace_attr").value_or(0);
    enable_all_brand_browsed_filter_day_ = livestream::GetIntCommonAttr(context, config,
        "ebable_lll_real_show_brand_browsed_day_gap_filter_attr").value_or(0);
    enable_all_brand_browsed_filter_day_replace_ = livestream::GetIntCommonAttr(context, config,
        "ebable_lll_real_show_brand_browsed_day_gap_filter_replace_attr").value_or(0);
    enable_new_user_hot_author_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_new_user_hot_author_filter_attr").value_or(0);
    enable_distance_filter_reasons_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_distance_filter_reasons_attr").value_or(0);
    enable_new_user_hot_author_filter_reasons_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_new_user_hot_author_filter_reasons_attr").value_or(0);
    enable_all_local_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_filter_all_local_life_attr").value_or(0);
    enable_all_local_filter_v2_ = livestream::GetIntCommonAttr(context, config,
        "enable_filter_all_local_life_v2_attr").value_or(0);
    enable_lll_isolate_user_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_filter_isolate_user_attr").value_or(0);
    lll_isolated_user_flag_ = livestream::GetIntCommonAttr(context, config,
        "lll_isolated_user_flag_attr").value_or(0);
    enable_lll_low_pay_user_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_low_pay_user_filter_attr").value_or(0);
    lll_low_pay_user_filter_level_ = livestream::GetIntCommonAttr(context, config,
        "lll_low_pay_user_filter_level_attr").value_or(0);
    lll_low_pay_user_flag2_ = livestream::GetIntCommonAttr(context, config,
        "lll_low_pay_user_flag2_attr").value_or(0);
    lll_low_pay_user_flag3_ = livestream::GetIntCommonAttr(context, config,
        "lll_low_pay_user_flag3_attr").value_or(0);
    lll_low_pay_user_flag4_ = livestream::GetIntCommonAttr(context, config,
        "lll_low_pay_user_flag4_attr").value_or(0);
    enable_lll_restricted_live_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_local_life_restricted_live_attr").value_or(0);
    enable_lll_fireworks_live_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_local_life_fireworks_live_attr").value_or(0);
    enable_lll_black_user_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_black_user_filter_attr").value_or(0);
    enable_lll_new_user_black_user_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_new_user_black_user_attr").value_or(0);
    enable_lll_dislike_user_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_dislike_user_filter_attr").value_or(0);
    enable_same_city_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_local_life_live_same_city_filter_attr").value_or(0);
    lll_quanguo_cate_2_filter_enable_ = livestream::GetIntCommonAttr(context, config,
        "lll_quanguo_cate_2_filter_enable_attr").value_or(0);
    is_black_user_ = livestream::GetIntCommonAttr(context, config,
        "lll_black_user_attr").value_or(0);
    enable_local_life_author_black_list_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_local_life_author_black_list_filter_attr").value_or(0);
    lll_black_probability = livestream::GetDoubleCommonAttr(context, config,
        "lll_black_probability_attr").value_or(1.0);
    enable_local_life_mark_author_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_local_life_mark_author_filter_attr").value_or(0);
    enable_local_life_merchant_browsed_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_local_life_merchant_browsed_filter_attr").value_or(0);

    enable_lll_union_paid_merchant_browset_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_union_paid_merchant_browset_attr").value_or(0);
    enable_lll_union_mintues_merchant_server_show_browset_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_union_mintues_merchant_server_show_browset_attr").value_or(0);
    enable_lll_aigc_live_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_aigc_live_filter_attr").value_or(0);
    enable_lll_invalid_send_city_bitmap_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_invalid_send_city_bitmap_filter_attr").value_or(0);
    enable_lll_same_city_filter_use_new_index_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_same_city_filter_use_new_index_attr").value_or(0);
    enable_lll_same_city_filter_use_new_goods_cate_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_same_city_filter_use_new_goods_cate_attr").value_or(0);
    enable_lll_skip_same_city_bitmap_filter_by_reason_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_skip_same_city_bitmap_filter_by_reason_attr").value_or(0);
    enable_local_life_live_geohash_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_local_life_live_geohash_filter_attr").value_or(0);

    enable_lll_union_mintues_cate3_server_show_browset_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_union_mintues_cate3_server_show_browset_attr").value_or(0);
    enable_lll_union_mintues_cate3_real_show_browset_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_union_mintues_cate3_real_show_browset_attr").value_or(0);
    enable_lll_union_mintues_cate3_real_show_browset2_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_union_mintues_cate3_real_show_browset_2_attr").value_or(0);
    enable_lll_union_mintues_cate3_real_show_browset3_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_union_mintues_cate3_real_show_browset_3_attr").value_or(0);

    enable_local_life_new_brand_id_browsed_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_local_life_new_brand_id_browsed_filter_attr").value_or(0);
    enable_local_life_new_brand_id_browsed_filter_new_ = livestream::GetIntCommonAttr(context, config,
        "enable_local_life_new_brand_id_browsed_filter_new_attr").value_or(0);
    enable_local_life_new_brand_id_day_paid_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_local_life_new_brand_id_day_paid_filter_attr").value_or(0);

    enable_lll_inner_new_brand_id_browsed_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_inner_new_brand_id_browsed_filter_attr").value_or(0);

    enable_lll_mix_replace_item_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_lll_mix_replace_item_filter_attr").value_or(0);

    enable_local_life_gray_filter_ = livestream::GetIntCommonAttr(context, config,
        "enable_local_life_gray_filter_attr").value_or(0);
    lll_gray_filter_slide_show_threshold_ = livestream::GetIntCommonAttr(context, config,
        "lll_gray_filter_slide_show_threshold_attr").value_or(0);

    if (enable_lll_fireworks_live_filter_) {
      auto province_list = livestream::GetStringListCommonAttr(context, config,
        "fireworks_filter_province_list").value_or(std::vector<absl::string_view>());
      auto user_province = livestream::GetStringCommonAttr(context, config,
        "user_province").value_or("");
      enable_lll_fireworks_city_filter_ = 0;
      for (const auto &province : province_list) {
        if (std::string(province) == std::string(user_province)) {
          enable_lll_fireworks_city_filter_ = 1;
          break;
        }
      }
    }

    is_dislike_user_ = false;
    if (enable_lll_dislike_user_filter_) {
      auto dislike_user_list = livestream::GetIntListCommonAttr(context, config,
        "lll_dislike_user_list_attr").value_or(absl::Span<const int64>());
      for (const auto &uid : dislike_user_list) {
        if (user_id_ == uid) {
          is_dislike_user_ = true;
          break;
        }
      }
    }

    if (enable_real_live_filter_ && enable_mmu_white_list_author_) {
      white_list_author_id_set_.clear();
      auto mmu_white_author_id_list = livestream::GetIntListCommonAttr(context, config,
            "lll_mmu_white_list_author_list_attr");
      if (mmu_white_author_id_list) {
        white_list_author_id_set_.insert(mmu_white_author_id_list->begin(),
              mmu_white_author_id_list->end());
      }
    }

    if (enable_same_city_filter_) {
      user_city_id_ = livestream::GetIntCommonAttr(context, config, "user_city_id_new_attr").value_or(0);
      if (user_city_id_ == 0) {
        user_city_id_ = livestream::GetIntCommonAttr(context, config, "user_city_id_attr").value_or(0);
      }
      same_city_filter_cate_set_.clear();
      auto same_city_filter_cate_list =
          livestream::GetStringListCommonAttr(context, config, "ll_same_city_filter_cate_2_list_attr");
      if (same_city_filter_cate_list) {
        same_city_filter_cate_set_.insert(same_city_filter_cate_list->begin(),
            same_city_filter_cate_list->end());
      }
    }

    if (enable_brand_browsed_filter_) {
      brand_browse_set_.clear();
      auto brand_browset_list =
            livestream::GetIntListCommonAttr(context, config, "local_life_brand_browset_list_attr");
      if (brand_browset_list) {
        brand_browse_set_.insert(brand_browset_list->begin(), brand_browset_list->end());
      }
    }

    if (enable_brand_browsed_filter_v2_) {
      brand_browse_set_v2_.clear();
      auto brand_browset_list_v2 =
            livestream::GetStringListCommonAttr(context, config, "local_life_brand_browset_list_attr_v2");
      if (brand_browset_list_v2) {
        for (const auto &brand : brand_browset_list_v2.value()) {
          brand_browse_set_v2_.insert(std::string(brand));
        }
      }
    }

    if (enable_brand_paid_filter_) {
      brand_paid_set_.clear();
      auto brand_paid_list =
            livestream::GetStringListCommonAttr(context, config, "paid_brand_name_list_attr");
      if (brand_paid_list) {
        for (const auto &brand : brand_paid_list.value()) {
          brand_paid_set_.insert(std::string(brand));
        }
      }
    }

    if (enable_all_brand_browsed_filter_ || enable_all_brand_browsed_filter_replace_) {
      all_brand_id_browse_set_.clear();
      auto all_brand_id_browse_list =
            livestream::GetIntListCommonAttr(context, config, "lll_real_show_brand_browsed_id_list_attr");
      if (all_brand_id_browse_list) {
        for (const auto &brand_id : all_brand_id_browse_list.value()) {
          all_brand_id_browse_set_.insert(brand_id);
        }
      }
    }


    if (enable_all_brand_browsed_filter_minutes_ || enable_all_brand_browsed_filter_minutes_replace_) {
      all_brand_id_browse_minutes_set_.clear();
      auto all_brand_id_browse_list_minutes =
            livestream::GetIntListCommonAttr(context, config,
            "lll_real_show_brand_browsed_id_list_minutes_gap_attr");
      if (all_brand_id_browse_list_minutes) {
        for (const auto &brand_id : all_brand_id_browse_list_minutes.value()) {
          all_brand_id_browse_minutes_set_.insert(brand_id);
        }
      }
    }

    if (enable_all_brand_browsed_filter_day_ || enable_all_brand_browsed_filter_day_replace_) {
      all_brand_id_browse_day_set_.clear();
      auto all_brand_id_browse_list_day =
            livestream::GetIntListCommonAttr(context, config,
            "lll_day_level_brand_browsed_id_list_attr");
      if (all_brand_id_browse_list_day) {
        for (const auto &brand_id : all_brand_id_browse_list_day.value()) {
          all_brand_id_browse_day_set_.insert(brand_id);
        }
      }
    }




    if (enable_new_user_hot_author_filter_ == 1) {
      user_level_retr_reason_ = livestream::GetIntCommonAttr(context, config,
            "lll_user_level_retr_reason_attr").value_or(0);
      new_user_filter_reason_set_.clear();
      if (enable_new_user_hot_author_filter_reasons_) {
        auto reasons_str = livestream::GetStringCommonAttr(context, config,
              "lll_new_user_hot_author_filter_reasons_list_attr").value_or("");
        std::vector<absl::string_view> reasons_list = absl::StrSplit(reasons_str, ",");
        int64 reason = 0;
        for (auto &reason_str : reasons_list) {
          if (absl::SimpleAtoi(reason_str, &reason)) {
            new_user_filter_reason_set_.insert(reason);
          }
        }
      }
    }

    if (enable_distance_filter_reasons_) {
      distance_filter_reason_set_.clear();
      auto reasons_str = livestream::GetStringCommonAttr(context, config,
            "lll_distance_filter_reasons_list_attr").value_or("");
      std::vector<absl::string_view> reasons_list = absl::StrSplit(reasons_str, ",");
      int64 reason = 0;
      for (auto &reason_str : reasons_list) {
        if (absl::SimpleAtoi(reason_str, &reason)) {
          distance_filter_reason_set_.insert(reason);
        }
      }
    }

    if (enable_local_life_author_black_list_filter_) {
      author_black_set_.clear();
      auto lll_black_list =
            livestream::GetIntListCommonAttr(context, config,
            "lll_black_list_attr");
      if (lll_black_list) {
        for (const auto &author_id : lll_black_list.value()) {
          author_black_set_.insert(author_id);
        }
      }
    }
    random.Reset(base::GetTimestamp());

    if (enable_local_life_mark_author_filter_) {
      mark_code_set_.clear();
      auto lll_mark_code_filter_str =
            livestream::GetStringCommonAttr(context, config,
            "lll_mark_code_filter_str_attr");
      if (lll_mark_code_filter_str) {
        std::vector<absl::string_view> split_strs = absl::StrSplit(
          lll_mark_code_filter_str.value(), absl::ByAnyChar(";"), absl::SkipEmpty());
        for (const auto mark_code_str : split_strs) {
          int64 mark_code;
          if (absl::SimpleAtoi(mark_code_str, &mark_code)) {
            mark_code_set_.insert(mark_code);
          }
        }
      }
    }

    if (enable_lll_union_paid_merchant_browset_) {
      merchant_id_paid_browse_mintues_set_.clear();
      merchant_id_paid_browse_hour_set_.clear();
      merchant_id_paid_browse_day_set_.clear();
      auto merchat_paid_browse_list_minutes =
            livestream::GetIntListCommonAttr(context, config,
            "lll_frequence_control_by_mintues_paid_merchat_id_list_attr");
      auto merchat_paid_browse_list_hour =
            livestream::GetIntListCommonAttr(context, config,
            "lll_frequence_control_by_hour_paid_merchat_id_list_attr");
      auto merchat_paid_browse_list_day =
            livestream::GetIntListCommonAttr(context, config,
            "lll_frequence_control_by_day_paid_merchat_id_list_attr");
      if (merchat_paid_browse_list_minutes) {
        for (const auto &merchant_id : merchat_paid_browse_list_minutes.value()) {
          merchant_id_paid_browse_mintues_set_.insert(merchant_id);
        }
      }
      if (merchat_paid_browse_list_hour) {
        for (const auto &merchant_id : merchat_paid_browse_list_hour.value()) {
          merchant_id_paid_browse_hour_set_.insert(merchant_id);
        }
      }
      if (merchat_paid_browse_list_day) {
        for (const auto &merchant_id : merchat_paid_browse_list_day.value()) {
          merchant_id_paid_browse_day_set_.insert(merchant_id);
        }
      }
    }

    if (enable_lll_mix_replace_item_filter_) {
      mix_replace_live_id_set_.clear();
      auto mix_replace_live_id_list =
            livestream::GetIntListCommonAttr(context, config,
            "lll_mix_replace_live_id_list_attr");
      if (mix_replace_live_id_list) {
        for (const auto &live_id : mix_replace_live_id_list.value()) {
          mix_replace_live_id_set_.insert(live_id);
        }
      }
      mix_replace_live_id_from_redis_set_.clear();
      auto mix_replace_live_id_list_from_redis =
            livestream::GetIntListCommonAttr(context, config,
            "lll_mix_replace_live_id_list_from_redis_attr");
      if (mix_replace_live_id_list_from_redis) {
        for (const auto &live_id : mix_replace_live_id_list_from_redis.value()) {
          mix_replace_live_id_from_redis_set_.insert(live_id);
        }
      }
    }

    if (enable_lll_inner_new_brand_id_browsed_filter_) {
      inner_new_brand_id_browse_mintues_set_.clear();
      auto inner_new_brand_id_browse_mintues_list =
            livestream::GetIntListCommonAttr(context, config,
            "lll_inner_frequence_control_by_mintues_new_brand_id_list_attr");
      if (inner_new_brand_id_browse_mintues_list) {
        for (const auto &new_brand_id : inner_new_brand_id_browse_mintues_list.value()) {
          inner_new_brand_id_browse_mintues_set_.insert(new_brand_id);
        }
      }
    }

    if (enable_local_life_new_brand_id_browsed_filter_
    || enable_local_life_new_brand_id_browsed_filter_new_) {
      new_brand_id_browse_mintues_set_.clear();
      new_brand_id_browse_hour_set_.clear();
      new_brand_id_browse_day_set_.clear();
      new_brand_id_browse_server_show_set_.clear();
      new_brand_id_browse_paid_set_.clear();
      auto new_brand_id_browse_mintues_list =
            livestream::GetIntListCommonAttr(context, config,
            "lll_frequence_control_by_mintues_new_brand_id_list_attr");
      auto new_brand_id_browse_hour_list =
            livestream::GetIntListCommonAttr(context, config,
            "lll_frequence_control_by_hour_new_brand_id_list_attr");
      auto new_brand_id_browse_day_list =
            livestream::GetIntListCommonAttr(context, config,
            "lll_frequence_control_by_day_new_brand_id_list_attr");
      auto new_brand_id_browse_server_show_list =
            livestream::GetIntListCommonAttr(context, config,
            "lll_frequence_control_servershow_by_mintues_new_brand_id_list_attr");
      auto new_brand_id_browse_paid_list =
            livestream::GetIntListCommonAttr(context, config,
            "lll_frequence_control_by_day_paid_new_brand_id_list_attr");

      if (new_brand_id_browse_mintues_list) {
        for (const auto &new_brand_id : new_brand_id_browse_mintues_list.value()) {
          new_brand_id_browse_mintues_set_.insert(new_brand_id);
        }
      }
      if (new_brand_id_browse_hour_list) {
        for (const auto &new_brand_id : new_brand_id_browse_hour_list.value()) {
          new_brand_id_browse_hour_set_.insert(new_brand_id);
        }
      }
      if (new_brand_id_browse_day_list) {
        for (const auto &new_brand_id : new_brand_id_browse_day_list.value()) {
          new_brand_id_browse_day_set_.insert(new_brand_id);
        }
      }
      if (new_brand_id_browse_server_show_list) {
        for (const auto &new_brand_id : new_brand_id_browse_server_show_list.value()) {
          new_brand_id_browse_server_show_set_.insert(new_brand_id);
        }
      }
      if (new_brand_id_browse_paid_list) {
        for (const auto &new_brand_id : new_brand_id_browse_paid_list.value()) {
          new_brand_id_browse_paid_set_.insert(new_brand_id);
        }
      }
    }

    if (enable_lll_union_mintues_merchant_server_show_browset_) {
      merchant_id_server_show_browse_mintues_set_.clear();
      auto merchat_server_show_browse_list_day =
            livestream::GetIntListCommonAttr(context, config,
            "lll_frequence_control_by_mintues_server_show_merchat_id_list_attr");
      if (merchat_server_show_browse_list_day) {
        for (const auto &brand_id : merchat_server_show_browse_list_day.value()) {
          merchant_id_server_show_browse_mintues_set_.insert(brand_id);
        }
      }
    }

    if (enable_lll_union_mintues_cate3_server_show_browset_) {
      cate3_id_server_show_browse_mintues_set_.clear();
      auto cate3_server_show_browse_list_day =
            livestream::GetIntListCommonAttr(context, config,
            "lll_frequence_control_by_mintues_server_show_cate3_id_list_attr");
      if (cate3_server_show_browse_list_day) {
        for (const auto &cate_id : cate3_server_show_browse_list_day.value()) {
          cate3_id_server_show_browse_mintues_set_.insert(cate_id);
        }
      }
    }

    if (enable_lll_union_mintues_cate3_real_show_browset_) {
      cate3_id_real_show_browse_mintues_set_.clear();
      auto cate3_real_show_browse_list_day =
            livestream::GetIntListCommonAttr(context, config,
            "lll_frequence_control_by_mintues_real_show_cate3_id_list_attr");
      if (cate3_real_show_browse_list_day) {
        for (const auto &cate_id : cate3_real_show_browse_list_day.value()) {
          cate3_id_real_show_browse_mintues_set_.insert(cate_id);
        }
      }
    }

    if (enable_lll_union_mintues_cate3_real_show_browset2_) {
      cate3_id_real_show_browse_mintues_set2_.clear();
      auto cate3_real_show_browse_list_day =
            livestream::GetIntListCommonAttr(context, config,
            "lll_frequence_control_by_mintues_real_show_cate3_id_list_2_attr");
      if (cate3_real_show_browse_list_day) {
        for (const auto &cate_id : cate3_real_show_browse_list_day.value()) {
          cate3_id_real_show_browse_mintues_set2_.insert(cate_id);
        }
      }
    }

    if (enable_lll_union_mintues_cate3_real_show_browset3_) {
      cate3_id_real_show_browse_mintues_set3_.clear();
      auto cate3_real_show_browse_list_day =
            livestream::GetIntListCommonAttr(context, config,
            "lll_frequence_control_by_mintues_real_show_cate3_id_list_3_attr");
      if (cate3_real_show_browse_list_day) {
        for (const auto &cate_id : cate3_real_show_browse_list_day.value()) {
          cate3_id_real_show_browse_mintues_set3_.insert(cate_id);
        }
      }
    }

    if (enable_lll_invalid_send_city_bitmap_filter_) {
      if (lll_quanguo_cate_2_filter_enable_) {
        quanguo_cate_2_set_.clear();
        auto quanguo_cate_2_list =
            livestream::GetStringListCommonAttr(context, config, "lll_quanguo_cate_2_list_attr");
        if (quanguo_cate_2_list) {
          for (const auto &cate : quanguo_cate_2_list.value()) {
            quanguo_cate_2_set_.insert(std::string(cate));
          }
        }
      } else {
        same_city_filter_cate_2_set_.clear();
        auto same_city_filter_cate_2_list =
            livestream::GetStringListCommonAttr(context, config, "lll_same_city_filter_cate_2_list_attr");
        if (same_city_filter_cate_2_list) {
          for (const auto &cate : same_city_filter_cate_2_list.value()) {
            same_city_filter_cate_2_set_.insert(std::string(cate));
          }
        }
      }

      // 跳过 bitmap 同城过滤的 reason set
      if (enable_lll_skip_same_city_bitmap_filter_by_reason_) {
        skip_same_city_filter_reason_set_.clear();
        auto reasons_str = livestream::GetStringCommonAttr(context, config,
              "lll_skip_same_city_bitmap_filter_reasons_list_attr").value_or("");
        std::vector<absl::string_view> reasons_list = absl::StrSplit(reasons_str, ",");
        int64 reason = 0;
        for (auto &reason_str : reasons_list) {
          if (absl::SimpleAtoi(reason_str, &reason)) {
            skip_same_city_filter_reason_set_.insert(reason);
          }
        }
      }

      // geohash 过滤, 扩大同城过滤分发范围
      if (enable_local_life_live_geohash_filter_) {
        plain_geohash_set_.clear();
        auto geohash_list = livestream::GetStringListCommonAttr(context, config,
              "plain_geohash_list_attr").value_or(std::vector<absl::string_view>());
        for (auto geohash : geohash_list) {
          plain_geohash_set_.insert(std::string(geohash));
        }
      }
    }

    // 灰度劣质直播间断流
    if (enable_local_life_gray_filter_) {
      lll_gray_filter_mark_code_set_.clear();
      auto lll_gray_filter_mark_code_str =
            livestream::GetStringCommonAttr(context, config,
            "lll_gray_filter_mark_code_str_attr");
      if (lll_gray_filter_mark_code_str) {
        std::vector<absl::string_view> split_strs = absl::StrSplit(
          lll_gray_filter_mark_code_str.value(), absl::ByAnyChar(";"), absl::SkipEmpty());
        for (const auto mark_code_str : split_strs) {
          int64 mark_code;
          if (absl::SimpleAtoi(mark_code_str, &mark_code)) {
            lll_gray_filter_mark_code_set_.insert(mark_code);
          }
        }
      }
    }
}

bool LocalLifeLiveFilter::ShouldRemove(const CommonRecoResult &result) {
  auto is_local_life = item_attr_container_->GetLIsLocalLife().value_or(0);
  if (is_local_life != 1) return false;

  if (enable_all_local_filter_ == 1 || enable_all_local_filter_v2_ == 1) return true;

  if (enable_lll_black_user_filter_ == 1 && is_black_user_ == 1) {
    if (enable_lll_new_user_black_user_filter_ == 0 || u_level_ == 100) return true;
  }

  if (is_dislike_user_) return true;

  if (enable_lll_isolate_user_filter_ == 1 && lll_isolated_user_flag_ == 1) return true;

  // 业务需求-本地负时长用户断流 https://docs.corp.kuaishou.com/k/home/<USER>/fcADhF5bWi39r9nCMZPLlnjmo
  if (enable_lll_low_pay_user_filter_ == 1) {
    if (lll_low_pay_user_filter_level_ == 2 && lll_low_pay_user_flag2_ == 1) {
      return true;
    }
    if (lll_low_pay_user_filter_level_ == 3 && lll_low_pay_user_flag3_ == 1) {
      return true;
    }
    if (lll_low_pay_user_filter_level_ == 4 && lll_low_pay_user_flag4_ == 1) {
      return true;
    }
  }

  auto author_id = item_attr_container_->GetAuthorId().value_or(0);

  auto skip_distance_filter = item_attr_container_->GetLlSkipDistanceFilter().value_or(0);
  if (enable_distance_filter_ && skip_distance_filter == 0) {
    auto filter_thresh = item_attr_container_->GetLlGeohashFilterValue().value_or(0.0);
    // 当 item 只包含指定的 reason, 不包含其他 reason 时, filter 生效
    if (enable_distance_filter_reasons_) {
      auto reason_list = item_attr_container_->GetReasonList().value_or(absl::Span<const int64>());
      bool valid_reason = true;
      if (!reason_list.empty()) {
        for (const auto &reason : reason_list) {
          if (distance_filter_reason_set_.find(reason) == distance_filter_reason_set_.end()) {
            valid_reason = false;
            break;
          }
        }
      }
      if (valid_reason) {
        auto geohash_distance = item_attr_container_->GetLlGeohashDistance();
        // 如果指定了距离过滤生效的 reason, geohash_distance 为空的 item 会被过滤
        if (!geohash_distance.has_value() || geohash_distance.value() == 0.0) return true;
        if (filter_thresh > 0 && geohash_distance.value() > filter_thresh) return true;
      }
    } else {
      auto geohash_distance = item_attr_container_->GetLlGeohashDistance().value_or(0.0);
      if (filter_thresh > 0 && geohash_distance > filter_thresh) return true;
    }
  }
  if (enable_real_live_filter_) {
    auto is_mmu = item_attr_container_->GetLlMmu().value_or(0);
    if (is_mmu < 1) {
      if (enable_mmu_white_list_author_) {
        if (white_list_author_id_set_.find(author_id) == white_list_author_id_set_.end()) {
          return true;
        }
      } else {
        return true;
      }
    }
  }
  if (enable_brand_browsed_filter_) {
    auto brand_name = item_attr_container_->GetLlBrandName().value_or("");
    if (!brand_name.empty()) {
      auto brand_hash = base::CityHash64(brand_name.data(), brand_name.size());
      if (brand_browse_set_.find(brand_hash) != brand_browse_set_.end()) {
        return true;
      }
    }
  }
  if (enable_brand_browsed_filter_v2_) {
    auto brand_name = item_attr_container_->GetLlBrandName().value_or("");
    if (!brand_name.empty()) {
      if (brand_browse_set_v2_.find(std::string(brand_name)) != brand_browse_set_v2_.end()) {
        return true;
      }
    }
  }

  if (enable_brand_paid_filter_) {
    auto brand_name = item_attr_container_->GetLlBrandName().value_or("");
    if (!brand_name.empty()) {
      if (brand_paid_set_.find(std::string(brand_name)) != brand_paid_set_.end()) {
        return true;
      }
    }
  }

  if (enable_all_brand_browsed_filter_ || enable_all_brand_browsed_filter_replace_) {
    auto brand_id = item_attr_container_->GetLlBrandId().value_or(0);
    if (brand_id > 0) {
      if (all_brand_id_browse_set_.find(brand_id) != all_brand_id_browse_set_.end()) {
        return true;
      }
    }
  }

  if (enable_all_brand_browsed_filter_minutes_ || enable_all_brand_browsed_filter_minutes_replace_) {
    auto brand_id = item_attr_container_->GetLlBrandId().value_or(0);
    if (brand_id > 0) {
      if (all_brand_id_browse_minutes_set_.find(brand_id) != all_brand_id_browse_minutes_set_.end()) {
        return true;
      }
    }
  }

  if (enable_all_brand_browsed_filter_day_ || enable_all_brand_browsed_filter_day_replace_) {
    auto brand_id = item_attr_container_->GetLlBrandId().value_or(0);
    if (brand_id > 0) {
      if (all_brand_id_browse_day_set_.find(brand_id) != all_brand_id_browse_day_set_.end()) {
        return true;
      }
    }
  }

  if (enable_local_life_merchant_browsed_filter_) {
    auto merchant_id = item_attr_container_->GetLlMerchantId().value_or(0);
    if (merchant_id > 0) {
      if (all_brand_id_browse_day_set_.find(merchant_id) != all_brand_id_browse_day_set_.end()) {
        return true;
      }
      if (all_brand_id_browse_minutes_set_.find(merchant_id) != all_brand_id_browse_minutes_set_.end()) {
        return true;
      }
      if (all_brand_id_browse_set_.find(merchant_id) != all_brand_id_browse_set_.end()) {
        return true;
      }
    }
  }

  if (enable_lll_union_paid_merchant_browset_) {
    auto merchant_id = item_attr_container_->GetLlMerchantId().value_or(0);
    if (merchant_id > 0) {
      if (merchant_id_paid_browse_mintues_set_.find(merchant_id)
      != merchant_id_paid_browse_mintues_set_.end()) {
        return true;
      }
      if (merchant_id_paid_browse_hour_set_.find(merchant_id) != merchant_id_paid_browse_hour_set_.end()) {
        return true;
      }
      if (merchant_id_paid_browse_day_set_.find(merchant_id) != merchant_id_paid_browse_day_set_.end()) {
        return true;
      }
    }
  }

  if (enable_lll_mix_replace_item_filter_) {
    auto live_id = item_attr_container_->GetPhotoId().value_or(0);
    if (live_id > 0 && mix_replace_live_id_set_.find(live_id) != mix_replace_live_id_set_.end()) {
      return true;
    }
    if (live_id > 0
        && mix_replace_live_id_from_redis_set_.find(live_id) != mix_replace_live_id_from_redis_set_.end()) {
      return true;
    }
  }

  if (enable_lll_inner_new_brand_id_browsed_filter_) {
    auto new_brand_id = item_attr_container_->GetLlNewBrandId().value_or(0);
    if (new_brand_id > 0) {
      if (inner_new_brand_id_browse_mintues_set_.find(new_brand_id)
      != new_brand_id_browse_mintues_set_.end()) {
        return true;
      }
    }
  }

  if (enable_local_life_new_brand_id_browsed_filter_
  || enable_local_life_new_brand_id_browsed_filter_new_) {
    auto new_brand_id = item_attr_container_->GetLlNewBrandId().value_or(0);
    if (new_brand_id > 0) {
      if (new_brand_id_browse_mintues_set_.find(new_brand_id)
      != new_brand_id_browse_mintues_set_.end()) {
        return true;
      }
      if (new_brand_id_browse_hour_set_.find(new_brand_id) != new_brand_id_browse_hour_set_.end()) {
        return true;
      }
      if (new_brand_id_browse_day_set_.find(new_brand_id) != new_brand_id_browse_day_set_.end()) {
        return true;
      }
      if (new_brand_id_browse_server_show_set_.find(new_brand_id)
      != new_brand_id_browse_server_show_set_.end()) {
        return true;
      }
      if (enable_local_life_new_brand_id_day_paid_filter_ &&
          new_brand_id_browse_paid_set_.find(new_brand_id) != new_brand_id_browse_paid_set_.end()) {
        return true;
      }
    }
  }

  if (enable_lll_union_mintues_merchant_server_show_browset_) {
    auto merchant_id = item_attr_container_->GetLlMerchantId().value_or(0);
    if (merchant_id > 0) {
      if (merchant_id_server_show_browse_mintues_set_.find(merchant_id)
      != merchant_id_server_show_browse_mintues_set_.end()) {
        return true;
      }
    }
  }

  if (enable_lll_union_mintues_cate3_server_show_browset_) {
    auto cate_id = item_attr_container_->GetLlGoodsCate3Id().value_or(0);
    if (cate_id > 0) {
      if (cate3_id_server_show_browse_mintues_set_.find(cate_id)
      != cate3_id_server_show_browse_mintues_set_.end()) {
        return true;
      }
    }
  }

  if (enable_lll_union_mintues_cate3_real_show_browset_) {
    auto cate_id = item_attr_container_->GetLlGoodsCate3Id().value_or(0);
    if (cate_id > 0) {
      if (cate3_id_real_show_browse_mintues_set_.find(cate_id)
      != cate3_id_real_show_browse_mintues_set_.end()) {
        return true;
      }
    }
  }

  if (enable_lll_union_mintues_cate3_real_show_browset2_) {
    auto cate_id = item_attr_container_->GetLlGoodsCate3Id().value_or(0);
    if (cate_id > 0) {
      if (cate3_id_real_show_browse_mintues_set2_.find(cate_id)
      != cate3_id_real_show_browse_mintues_set2_.end()) {
        return true;
      }
    }
  }

  if (enable_lll_union_mintues_cate3_real_show_browset3_) {
    auto cate_id = item_attr_container_->GetLlGoodsCate3Id().value_or(0);
    if (cate_id > 0) {
      if (cate3_id_real_show_browse_mintues_set3_.find(cate_id)
      != cate3_id_real_show_browse_mintues_set3_.end()) {
        return true;
      }
    }
  }

  double rand = random.GetDouble();;
  if (enable_local_life_author_black_list_filter_ && rand <= lll_black_probability) {
    if (author_id > 0) {
      if (author_black_set_.find(author_id) != author_black_set_.end()) {
        return true;
      }
    }
  }

  if (enable_local_life_mark_author_filter_) {
    auto mark_code = item_attr_container_->GetLlMarkCode().value_or(0);
    if (mark_code_set_.find(mark_code) != mark_code_set_.end()) {
      return true;
    }
  }


  // 同城过滤, 只对非 geohash 召回的到餐直播间生效
  auto geohash_distance = item_attr_container_->GetLlGeohashDistance();
  if (enable_same_city_filter_ && user_city_id_ > 0 && !geohash_distance.has_value()) {
    auto cate = item_attr_container_->GetLlGoodsCate2().value_or("");
    if (!cate.empty() &&
        same_city_filter_cate_set_.find(cate) != same_city_filter_cate_set_.end()) {
      auto city_list = item_attr_container_->GetLlPoiCityIdList()
                           .value_or(absl::Span<const int64>());
      bool is_same_city = false;
      for (const auto city : city_list) {
        if (city == user_city_id_) {
          is_same_city = true;
          break;
        }
      }
      if (!city_list.empty() && !is_same_city) {
        return true;
      }
    }
  }
  if (enable_lll_restricted_live_filter_) {
    auto restrict_author = item_attr_container_->GetLlRestrictAuthor().value_or(0);
    if (restrict_author == 1 || restrict_author == 2) {
      return true;
    }
  }
  if (enable_lll_fireworks_live_filter_ && enable_lll_fireworks_city_filter_) {
    auto fireworks_author = item_attr_container_->GetLlFireworksAuthor().value_or(0);
    if (fireworks_author == 1) {
      return true;
    }
  }
  if (enable_new_user_hot_author_filter_ && user_level_ == 0 && user_level_retr_reason_ > 0) {
    auto reason_list = item_attr_container_->GetReasonList().value_or(absl::Span<const int64>());
    if (!reason_list.empty()) {
      bool is_user_level_retr = false;
      for (const auto &reason : reason_list) {
        if (reason == user_level_retr_reason_) {
          is_user_level_retr = true;
          break;
        }
      }
      if (is_user_level_retr == false) {
        // 当 item 只包含指定的 reason, 不包含其他 reason 时, filter 生效
        if (enable_new_user_hot_author_filter_reasons_) {
          bool valid_reason = true;
          for (const auto &reason : reason_list) {
            if (new_user_filter_reason_set_.find(reason) == new_user_filter_reason_set_.end()) {
              valid_reason = false;
              break;
            }
          }
          if (valid_reason) return true;
        } else {
          return true;
        }
      }
    }
  }

  if (enable_lll_aigc_live_filter_) {
    auto ll_is_aigc = item_attr_container_->GetLIsAigc().value_or(0);
    if (ll_is_aigc > 0) {
      return true;
    }
  }

  if (enable_lll_invalid_send_city_bitmap_filter_) {
    bool skip_filter = false;
    auto reason_list = item_attr_container_->GetReasonList().value_or(absl::Span<const int64>());
    if (enable_lll_skip_same_city_bitmap_filter_by_reason_ && !reason_list.empty()) {
      for (const auto &reason : reason_list) {
        if (skip_same_city_filter_reason_set_.find(reason)
              != skip_same_city_filter_reason_set_.end()) {
          skip_filter = true;
          break;
        }
      }
    }
    // 与用户在同一 geohash 块中的 poi, 不满足同城条件也不过滤
    bool is_geohash = false;
    if (enable_local_life_live_geohash_filter_) {
      auto item_geohash_list = item_attr_container_->GetLPoiGeoHash4List()
                                         .value_or(std::vector<absl::string_view>());
      for (auto geohash : item_geohash_list) {
        if (plain_geohash_set_.find(std::string(geohash)) != plain_geohash_set_.end()) {
          is_geohash = true;
          break;
        }
      }
    }
    if (!skip_filter && !is_geohash) {
      auto second_cate = item_attr_container_->GetLGoodsSecondCate().value_or("");
      if (enable_lll_same_city_filter_use_new_index_ &&
          enable_lll_same_city_filter_use_new_goods_cate_) {
        second_cate = item_attr_container_->GetLGoodsSecondCateNew().value_or("");
      }
      auto is_valid_send_city = item_attr_container_->GetLFilterUserCityInItemCityList().value_or(0);
      if (!second_cate.empty()) {
        if (lll_quanguo_cate_2_filter_enable_) {
          if ((quanguo_cate_2_set_.find(std::string(second_cate)) ==
                quanguo_cate_2_set_.end()) && !is_valid_send_city) {
            return true;
          }
        } else {
          if ((same_city_filter_cate_2_set_.find(std::string(second_cate)) !=
                same_city_filter_cate_2_set_.end()) && !is_valid_send_city) {
            return true;
          }
        }
      }
    }
  }

  // 灰度劣质直播间 filter: (slide_show_cnt > threshold) and (mark_code in lll_gray_filter_mark_code_set_)
  if (enable_local_life_gray_filter_) {
    auto mark_code = item_attr_container_->GetLlMarkCode().value_or(0);
    auto slide_show_cnt = item_attr_container_->GetLlSlideShowCnt().value_or(0);
    if (slide_show_cnt > lll_gray_filter_slide_show_threshold_ &&
        lll_gray_filter_mark_code_set_.find(mark_code) != lll_gray_filter_mark_code_set_.end()) {
      return true;
    }
  }

  return false;
}

}  // namespace livestream
}  // namespace platform
}  // namespace ks
