#include "dragon/src/processor/ext/livestream/module/filter/pk_value_author_filter.h"

namespace ks {
namespace platform {
namespace livestream {

void PkValueAuthorFilter::Init(
    ReadableRecoContextInterface *context, const base::<PERSON><PERSON> *config) {
  filter_good_author_level_ = livestream::GetIntCommonAttr(
      context, config, "filter_good_author_level_attr").value_or(0);
  filter_pk_core_authors_only_ = livestream::GetIntCommonAttr(
      context, config, "filter_pk_core_authors_only_attr").value_or(0);
  filter_pk_status_only_ = livestream::GetIntCommonAttr(
      context, config, "filter_pk_status_only_attr").value_or(0);
  enable_partial_filter_ = livestream::GetIntCommonAttr(
      context, config, "enable_partial_filter_attr").value_or(0);
  partial_filter_ratio_ = livestream::GetDoubleCommonAttr(
      context, config, "partial_filter_ratio_attr").value_or(1.0);
  auto author_buckets = livestream::GetStringCommonAttr(
      context, config, "filter_pk_author_buckets_attr").value_or("-1:-1");
  std::vector<std::string> tokens;
  base::SplitStringWithOptions(author_buckets.data(), ":", true, true, &tokens);
  filter_author_tail_start_ = -1;
  filter_author_tail_end_ = -1;
  if (tokens.size() == 2) {
    int64 filter_author_tail_start = -1;
    int64 filter_author_tail_end = -1;
    if (absl::SimpleAtoi(tokens[0], &filter_author_tail_start)) {
      filter_author_tail_start_ = filter_author_tail_start;
    }
    if (absl::SimpleAtoi(tokens[1], &filter_author_tail_end)) {
      filter_author_tail_end_ = filter_author_tail_end;
    }
  }
  random_.Reset(base::GetTimestamp());
}

bool PkValueAuthorFilter::ShouldRemove(
    const CommonRecoResult &result) {
  // 人群是否命中
  // 好主播
  bool hitGoodAuthor = false;
  // 如果索引没有该字段，默认为无标主播
  auto aGoodAuthorRangeV4KV = item_attr_container_->GetaGoodAuthorRangeV4KV().value_or(0);
  if (aGoodAuthorRangeV4KV >= filter_good_author_level_) {
    hitGoodAuthor = true;
  }
  // pk 核心主播
  bool hitPkCoreAuthor = false;
  auto isPkCoreAuthor = item_attr_container_->GetPkCoreAuthorFlag().value_or("0");
  if (isPkCoreAuthor == "1") {
    hitPkCoreAuthor = true;
  }

  bool hitAuthor = hitGoodAuthor;
  //  0：不限制是否 pk 核心主播
  //  1: 只命中 pk 核心主播
  //  2：只命中非 pk 核心主播
  if (filter_pk_core_authors_only_ > 0) {
    if (filter_pk_core_authors_only_ == 1) {
      hitAuthor = hitAuthor && hitPkCoreAuthor;
    } else if (filter_pk_core_authors_only_ == 2) {
      hitAuthor = hitAuthor && !hitPkCoreAuthor;
    }
  }

  // 主播尾号符合
  bool hitAuthorTail = false;
  auto author_id = item_attr_container_->GetAuthorId().value_or(-1);
  if (author_id > 0) {
    int64 author_id_tail = author_id % 100;
    if (author_id_tail >= filter_author_tail_start_ &&
        author_id_tail <= filter_author_tail_end_) {
      hitAuthorTail = true;
    }
  }
  hitAuthor = hitAuthor && hitAuthorTail;

  bool hitPkStatus = true;
  auto isPkNow = item_attr_container_->GetIsPkNow().value_or(0);
  //  0：不限制 pk 状态
  //  1: 只命中 pk 状态
  //  2：只命中非 pk 状态
  if (filter_pk_status_only_ > 0) {
    if (filter_pk_status_only_ == 1) {
      hitPkStatus = isPkNow > 0;
    } else if (filter_pk_status_only_ == 2) {
      hitPkStatus = isPkNow == 0;
    }
  }

  bool hitRatio = true;
  double current_random = random_.GetDouble();
  if (enable_partial_filter_) {
      hitRatio = current_random < partial_filter_ratio_;
  }

  bool hitAllCondition = hitAuthor && hitPkStatus && hitRatio;

  /*
  LOG(INFO) << "hitAllCondition " << hitAllCondition
          << ", author_id: " << author_id
          << ", hitAuthor: " << hitAuthor
          << ", hitPkStatus: " << hitPkStatus
          << ", hitRatio: " << hitRatio
          << ", hitGoodAuthor: " << hitGoodAuthor
          << ", filter_good_author_level_: " << filter_good_author_level_
          << ", aGoodAuthorRangeV4KV: " << aGoodAuthorRangeV4KV
          << ", hitPkCoreAuthor: " << hitPkCoreAuthor
          << ", isPkCoreAuthor: " << isPkCoreAuthor
          << ", filter_author_tail_start_: " << filter_author_tail_start_
          << ", filter_author_tail_end_: " << filter_author_tail_end_
          << ", filter_pk_core_authors_only_: " << filter_pk_core_authors_only_
          << ", filter_pk_status_only_: " << filter_pk_status_only_
          << ", isPkNow: " << isPkNow
          << ", enable_partial_filter_: " << enable_partial_filter_
          << ", current_random: " << current_random;
          */

  return hitAllCondition;
}

}  // namespace livestream
}  // namespace platform
}  // namespace ks
