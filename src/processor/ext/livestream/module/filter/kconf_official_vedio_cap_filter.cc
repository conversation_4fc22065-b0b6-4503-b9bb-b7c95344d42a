#include <vector>
#include <string>
#include <sstream>
#include "dragon/src/processor/ext/livestream/module/filter/kconf_official_vedio_cap_filter.h"

namespace ks {
namespace platform {
namespace livestream {

void KconfOfficialVedioCapFilter::Init(
    ReadableRecoContextInterface *context, const base::Json *config) {
    kconf_official_vedio_cap_num_map_.clear();
    auto kconf_official_vedio_cap_num_str_list = livestream::GetStringListCommonAttr(context, config,
        "kconf_official_vedio_cap_num_str_list_attr").value_or(std::vector<absl::string_view>());
    for (const auto& aid_num_str : kconf_official_vedio_cap_num_str_list) {
      std::vector<absl::string_view> aid_num_str_split = absl::StrSplit(
        aid_num_str, absl::ByAnyChar(":"), absl::SkipEmpty());
      if (aid_num_str_split.size() == 2) {
        int64 aid, num;
        if (absl::SimpleAtoi(aid_num_str_split[0], &aid) && absl::SimpleAtoi(aid_num_str_split[1], &num)) {
          kconf_official_vedio_cap_num_map_[aid] = num;
        }
      }
    }
}


bool KconfOfficialVedioCapFilter::ShouldRemove(
    const CommonRecoResult &result) {
  auto author_id = item_attr_container_->GetAuthorId().value_or(0);
  auto tag = kconf_official_vedio_cap_num_map_.find(author_id);
  if (tag != kconf_official_vedio_cap_num_map_.end()) {
    int64 slide_inner_all_day_show_num = item_attr_container_->GetSlideInnerRealShowAllDayCount().value_or(0);
    int64 slide_outter_all_day_show_num =
      item_attr_container_->GetSlideOutterRealShowAllDayCount().value_or(0);
    int64 live_show_num = slide_outter_all_day_show_num + slide_inner_all_day_show_num;
    if (live_show_num >= tag->second) {
      return true;
    } else {
      return false;
    }
  }

  return false;
}

}  // namespace livestream
}  // namespace platform
}  // namespace ks
