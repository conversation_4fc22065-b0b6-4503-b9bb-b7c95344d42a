#pragma once

#include <string>

#include "dragon/src/processor/ext/livestream/module/filter/base_filter.h"

namespace ks {
namespace platform {
namespace livestream {

class LocalLifeLiveFilter : public livestream::BaseFilter {
 public:
  LocalLifeLiveFilter() {}
  void Init(
      ReadableRecoContextInterface *context,
      const base::Json *config) override;
  bool ShouldRemove(const CommonRecoResult &result) override;

 private:
  bool is_dislike_user_ = false;
  int64 enable_distance_filter_ = 0;
  int64 enable_real_live_filter_ = 0;
  int64 enable_mmu_white_list_author_ = 0;
  int64 enable_brand_browsed_filter_ = 0;
  int64 enable_brand_browsed_filter_v2_ = 0;
  int64 enable_brand_paid_filter_ = 0;
  int64 enable_new_user_hot_author_filter_ = 0;
  int64 enable_distance_filter_reasons_ = 0;
  int64 enable_new_user_hot_author_filter_reasons_ = 0;
  int64 enable_all_local_filter_ = 0;
  int64 enable_all_local_filter_v2_ = 0;
  int64 enable_lll_isolate_user_filter_ = 0;
  int64 enable_lll_black_user_filter_ = 0;
  int64 enable_lll_new_user_black_user_filter_ = 0;
  int64 enable_lll_dislike_user_filter_ = 0;
  int64 enable_same_city_filter_ = 0;
  int64 lll_quanguo_cate_2_filter_enable_ = 0;
  int64 is_black_user_ = 0;
  int64 lll_isolated_user_flag_ = 0;
  int64 enable_lll_low_pay_user_filter_ = 0;
  int64 lll_low_pay_user_filter_level_ = 0;
  int64 lll_low_pay_user_flag2_ = 0;
  int64 lll_low_pay_user_flag3_ = 0;
  int64 lll_low_pay_user_flag4_ = 0;
  int64 user_city_id_ = 0;
  int64 user_level_ = 0;
  int64 u_level_ = 0;
  int64 user_id_ = 0;
  int64 user_level_retr_reason_ = 0;
  int64 enable_lll_restricted_live_filter_ = 0;
  int64 enable_lll_fireworks_live_filter_ = 0;
  int64 enable_lll_fireworks_city_filter_ = 0;
  int64 enable_all_brand_browsed_filter_ = 0;
  int64 enable_all_brand_browsed_filter_minutes_ = 0;
  int64 enable_all_brand_browsed_filter_day_ = 0;
  int64 enable_all_brand_browsed_filter_replace_ = 0;
  int64 enable_all_brand_browsed_filter_minutes_replace_ = 0;
  int64 enable_all_brand_browsed_filter_day_replace_ = 0;
  int64 enable_local_life_author_black_list_filter_ = 0;
  double lll_black_probability = 1.0;
  base::PseudoRandom random;
  int64 enable_local_life_mark_author_filter_ = 0;
  int64 enable_local_life_merchant_browsed_filter_ = 0;
  int64 enable_lll_union_paid_merchant_browset_ = 0;
  int64 enable_lll_union_mintues_merchant_server_show_browset_ = 0;
  int64 enable_lll_aigc_live_filter_ = 0;
  int64 enable_lll_invalid_send_city_bitmap_filter_ = 0;
  int64 enable_lll_same_city_filter_use_new_index_ = 0;
  int64 enable_lll_same_city_filter_use_new_goods_cate_ = 0;
  int64 enable_lll_skip_same_city_bitmap_filter_by_reason_ = 0;
  int64 enable_local_life_live_geohash_filter_ = 0;
  int64 enable_lll_union_mintues_cate3_server_show_browset_ = 0;
  int64 enable_lll_union_mintues_cate3_real_show_browset_ = 0;
  int64 enable_lll_union_mintues_cate3_real_show_browset2_ = 0;
  int64 enable_lll_union_mintues_cate3_real_show_browset3_ = 0;
  int64 enable_lll_inner_new_brand_id_browsed_filter_ = 0;
  int64 enable_local_life_new_brand_id_browsed_filter_ = 0;
  int64 enable_local_life_new_brand_id_browsed_filter_new_ = 0;
  int64 enable_local_life_new_brand_id_day_paid_filter_ = 0;
  int64 enable_lll_mix_replace_item_filter_ = 0;
  int64 enable_local_life_gray_filter_ = 0;
  int64 lll_gray_filter_slide_show_threshold_ = 500000;

  folly::F14FastSet<absl::string_view,
      absl::Hash<absl::string_view>> same_city_filter_cate_set_;
  folly::F14FastSet<int64> brand_browse_set_;
  folly::F14FastSet<std::string> brand_browse_set_v2_;
  folly::F14FastSet<std::string> same_city_filter_cate_2_set_;
  folly::F14FastSet<std::string> quanguo_cate_2_set_;
  folly::F14FastSet<std::string> brand_paid_set_;
  folly::F14FastSet<std::string> plain_geohash_set_;
  folly::F14FastSet<int64> white_list_author_id_set_;
  folly::F14FastSet<int64> distance_filter_reason_set_;
  folly::F14FastSet<int64> new_user_filter_reason_set_;
  folly::F14FastSet<int64> skip_same_city_filter_reason_set_;
  folly::F14FastSet<int64> all_brand_id_browse_set_;
  folly::F14FastSet<int64> all_brand_id_browse_minutes_set_;
  folly::F14FastSet<int64> all_brand_id_browse_day_set_;
  folly::F14FastSet<int64> author_black_set_;
  folly::F14FastSet<int64> mark_code_set_;
  folly::F14FastSet<int64> merchant_id_paid_browse_mintues_set_;
  folly::F14FastSet<int64> merchant_id_paid_browse_hour_set_;
  folly::F14FastSet<int64> merchant_id_paid_browse_day_set_;
  folly::F14FastSet<int64> merchant_id_server_show_browse_mintues_set_;
  folly::F14FastSet<int64> cate3_id_server_show_browse_mintues_set_;
  folly::F14FastSet<int64> cate3_id_real_show_browse_mintues_set_;
  folly::F14FastSet<int64> cate3_id_real_show_browse_mintues_set2_;
  folly::F14FastSet<int64> cate3_id_real_show_browse_mintues_set3_;
  folly::F14FastSet<int64> inner_new_brand_id_browse_mintues_set_;
  folly::F14FastSet<int64> new_brand_id_browse_mintues_set_;
  folly::F14FastSet<int64> new_brand_id_browse_hour_set_;
  folly::F14FastSet<int64> new_brand_id_browse_day_set_;
  folly::F14FastSet<int64> new_brand_id_browse_server_show_set_;
  folly::F14FastSet<int64> new_brand_id_browse_paid_set_;
  folly::F14FastSet<int64> mix_replace_live_id_set_;
  folly::F14FastSet<int64> mix_replace_live_id_from_redis_set_;
  folly::F14FastSet<int64> lll_gray_filter_mark_code_set_;

  DISALLOW_COPY_AND_ASSIGN(LocalLifeLiveFilter);
};

}  // namespace livestream
}  // namespace platform
}  // namespace ks
