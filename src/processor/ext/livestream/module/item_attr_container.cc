#include "dragon/src/processor/ext/livestream/module/item_attr_container.h"

namespace ks {
namespace platform {
namespace livestream {

ItemAttrContainer::ItemAttrContainer(const folly::F14FastMap<std::string,
                                     const ItemAttr *> &item_accessor_map) {
  // 获取 item attr accessor
  photo_id_accessor_ = GetItemAttrAccessor(item_accessor_map, "photo_id_attr");
  is_shop_live_accessor_ = GetItemAttrAccessor(item_accessor_map, "is_shop_live_attr");
  aGoodAuthorRangeV4KV_accessor_ = GetItemAttrAccessor(item_accessor_map, "aGoodAuthorRangeV4KV_attr");
  aGoodAuthorConsumeV4IntKV_accessor_ =
             GetItemAttrAccessor(item_accessor_map, "aGoodAuthorConsumeV4IntKV_attr");
  recruit_priority_attr_accessor_ = GetItemAttrAccessor(item_accessor_map, "recruit_priority_attr_attr");
  mmu_tag_id_accessor_ = GetItemAttrAccessor(item_accessor_map, "mmu_tag_id_attr");
  reason_list_accessor_ = GetItemAttrAccessor(item_accessor_map, "reason_list_attr");
  is_game_live_accessor_ = GetItemAttrAccessor(item_accessor_map, "is_game_live_attr");
  is_audio_live_room_accessor_ = GetItemAttrAccessor(item_accessor_map, "is_audio_live_room_attr");
  is_ktv_accessor_ = GetItemAttrAccessor(item_accessor_map, "is_ktv_attr");
  is_theater_accessor_ = GetItemAttrAccessor(item_accessor_map, "is_theater_attr");
  is_teampk_accessor_ = GetItemAttrAccessor(item_accessor_map, "is_teampk_attr");
  is_voice_party_accessor_ = GetItemAttrAccessor(item_accessor_map, "is_voice_party_attr");
  a_single_kill_label_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_single_kill_label_attr");
  l_merchant_themis_author_kv_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_merchant_themis_author_kv_attr");
  author_id_accessor_ = GetItemAttrAccessor(item_accessor_map, "author_id_attr");
  l_thompson_neg_gamora_v1_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_thompson_neg_gamora_v1_attr");
  l_thompson_neg_nebula_v1_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_thompson_neg_nebula_v1_attr");
  l_thompson_neg_gamora_v2_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_thompson_neg_gamora_v2_attr");
  l_thompson_neg_nebula_v2_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_thompson_neg_nebula_v2_attr");
  mmu_tag_info_tag_list_lv2_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "mmu_tag_info_tag_list_lv2_attr");
  l_mmu_seg_attr_list_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_mmu_seg_attr_list_attr");
  mmu_user_hetu_tag_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "mmu_user_hetu_tag_attr");
  l_ring_ball_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_ring_ball_attr");
  l_realshow_recent_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_realshow_recent_attr");
  l_report_recent_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_report_recent_attr");
  l_negative_recent_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_negative_recent_attr");
  l_dur_per_show_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_dur_per_show_attr");
  author_info_is_seller_author_v2_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "author_info_is_seller_author_v2_attr");
  l_risk_level_accessor_ = GetItemAttrAccessor(item_accessor_map, "l_risk_level_attr");
  l_shuffle_policy_accessor_ = GetItemAttrAccessor(item_accessor_map, "l_shuffle_policy_attr");
  punish_sirius_all_accessor_ = GetItemAttrAccessor(item_accessor_map, "punish_sirius_all_attr");
  l_recruit_query_room_bool_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_recruit_query_room_bool_attr");
  l_live_house_is_live_tag_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_live_house_is_live_tag_attr");
  l_live_house_is_una_live_tag_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_live_house_is_una_live_tag_attr");
  l_is_local_life_accessor_ = GetItemAttrAccessor(item_accessor_map, "l_is_local_life_attr");
  l_poi_geo_hash4_list_accessor_ = GetItemAttrAccessor(item_accessor_map, "l_poi_geo_hash4_list_attr");
  l_live_zero_punish_ab_suffix_list_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_live_zero_punish_ab_suffix_list_attr");
  slide_real_show_count_accessor_ = GetItemAttrAccessor(item_accessor_map, "slide_real_show_count_attr");
  slide_real_show_all_day_count_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "slide_real_show_all_day_count_attr");
  slide_inner_real_show_all_day_count_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "slide_inner_real_show_all_day_count_attr");
  slide_outter_real_show_all_day_count_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "slide_outter_real_show_all_day_count_attr");
  explore_inner_real_show_all_day_count_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "explore_inner_real_show_all_day_count_attr");
  explore_outer_real_show_all_day_count_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "explore_outer_real_show_all_day_count_attr");
  live_tag_real_show_all_day_count_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "live_tag_real_show_all_day_count_attr");
  a_c_start_author_bucket_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "a_c_start_author_bucket_attr");
  a_c_start_expect_total_show_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "a_c_start_expect_total_show_attr");
  a_c_start_current_total_show_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "a_c_start_current_total_show_attr");
  a_c_start_expect_total_gift_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "a_c_start_expect_total_gift_attr");
  a_c_start_current_total_gift_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "a_c_start_current_total_gift_attr");
  a_c_start_expect_total_show_v1_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "a_c_start_expect_total_show_v1_attr");
  a_c_start_expect_total_show_v2_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "a_c_start_expect_total_show_v2_attr");
  a_c_start_expect_total_show_v3_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "a_c_start_expect_total_show_v3_attr");
  a_c_start_expect_total_show_v4_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "a_c_start_expect_total_show_v4_attr");
  a_c_start_expect_total_gift_v1_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "a_c_start_expect_total_gift_v1_attr");
  a_c_start_expect_total_gift_v2_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "a_c_start_expect_total_gift_v2_attr");
  a_c_start_expect_total_gift_v3_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "a_c_start_expect_total_gift_v3_attr");
  a_c_start_expect_total_gift_v4_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "a_c_start_expect_total_gift_v4_attr");
  author_support_type_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "author_support_type_attr");
  cold_start_queue_reason_value_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "cold_start_queue_reason_value_attr");
  a_is_house_live_kv_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_house_live_kv_attr");
  l_live_house_is_dream_house_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_live_house_is_dream_house_attr");
  a_is_private_user_kv_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "a_is_private_user_kv_attr");
  a_recruit_org_id_kv_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_recruit_org_id_kv_attr");
  a_violation_punish_rt_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_violation_punish_rt_attr");
  a_violation_punish_offline_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "a_violation_punish_offline_attr");
  mmu_tag_info_tag_list_lv1_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "mmu_tag_info_tag_list_lv1_attr");
  mmu_tag_info_tag_list_lv3_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "mmu_tag_info_tag_list_lv3_attr");
  l_lat_kv_accessor_ = GetItemAttrAccessor(item_accessor_map, "l_lat_kv_attr");
  l_lon_kv_accessor_ = GetItemAttrAccessor(item_accessor_map, "l_lon_kv_attr");
  l_live_location_city_accessor_ = GetItemAttrAccessor(item_accessor_map, "l_live_location_city_attr");
  l_live_location_province_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_live_location_province_attr");
  grade_label_for_temp_accessor_ = GetItemAttrAccessor(item_accessor_map, "grade_label_for_temp_attr");
  grade_label_for_aid_temp_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "grade_label_for_aid_temp_attr");
  l_is_chat_gird_whitelist_aid_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_is_chat_gird_whitelist_aid_attr");
  l_voice_party_status_accessor_ = GetItemAttrAccessor(item_accessor_map, "l_voice_party_status_attr");
  l_voice_party_open_video_status_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_voice_party_open_video_status_attr");
  a_content_emb_v2_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_content_emb_v2_attr");
  l_hot_explore_punish_tag_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_hot_explore_punish_tag_attr");
  l_hot_small_business_punish_city_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_hot_small_business_punish_city_attr");
  l_hot_explore_punish_city_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_hot_explore_punish_city_attr");
  l_hot_slide_punish_city_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_hot_slide_punish_city_attr");
  l_interactive_game_type_accessor_ =
      GetItemAttrAccessor(item_accessor_map, "l_interactive_game_type_attr");
  item_off_filter_list_accessor_ = GetItemAttrAccessor(item_accessor_map, "item_off_filter_list_attr");
  ll_brand_name_accessor_ = GetItemAttrAccessor(item_accessor_map, "ll_brand_name_attr");
  ll_brand_id_accessor_ = GetItemAttrAccessor(item_accessor_map, "ll_brand_id_attr");
  ll_new_brand_id_accessor_ = GetItemAttrAccessor(item_accessor_map, "ll_new_brand_id_attr");
  ll_merchant_id_accessor_ = GetItemAttrAccessor(item_accessor_map, "ll_merchant_id_attr");
  ll_mmu_accessor_ = GetItemAttrAccessor(item_accessor_map, "ll_mmu_attr");
  ll_geohash_distance_accessor_ = GetItemAttrAccessor(item_accessor_map, "ll_geohash_distance_attr");
  ll_geohash_filter_value_accessor_ = GetItemAttrAccessor(item_accessor_map, "ll_geohash_filter_value_attr");
  ll_skip_distance_filter_accessor_ = GetItemAttrAccessor(item_accessor_map, "ll_skip_distance_filter_attr");
  is_cny_live_punish_accessor_ = GetItemAttrAccessor(item_accessor_map, "is_cny_live_punish_attr");
  is_cny_user_punish_accessor_ = GetItemAttrAccessor(item_accessor_map, "is_cny_user_punish_attr");
  ll_restrict_author_accessor_ = GetItemAttrAccessor(item_accessor_map, "lll_restrict_author_attr");
  ll_fireworks_author_accessor_ = GetItemAttrAccessor(item_accessor_map, "lll_fireworks_author_attr");
  ll_poi_city_id_list_accessor_ = GetItemAttrAccessor(item_accessor_map, "ll_poi_city_id_list_attr");
  ll_goods_cate_2_accessor_ =  GetItemAttrAccessor(item_accessor_map, "ll_goods_cate_2_attr");
  ll_goods_cate_3_id_accessor_ =  GetItemAttrAccessor(item_accessor_map, "ll_goods_cate_3_id_attr");
  ll_mark_code_accessor_ =  GetItemAttrAccessor(item_accessor_map, "lll_mark_code_attr");
  ll_slide_show_cnt_accessor_ = GetItemAttrAccessor(item_accessor_map, "lll_slide_show_cnt_attr");
  a_is_ig_black_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_ig_black_author_attr");
  a_kva_is_content2_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_kva_is_content2_attr");
  a_kva_is_content7_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_kva_is_content7_attr");
  a_is_pk_script_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_pk_script_attr");
  a_revenue_stage_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_revenue_stage_attr");
  a_author_tail_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_author_tail_attr");
  bigr_hav_in_14d_accessor_ = GetItemAttrAccessor(item_accessor_map, "bigr_hav_in_14d_attr");
  a_is_good_author_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_good_author_attr");
  a_is_copyright_infri_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "a_is_copyright_infri_attr");
  a_is_digital_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_digital_attr");
  a_is_longtime_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_longtime_attr");
  a_bucket_total_score_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_bucket_total_score_attr");
  l_is_copy_accessor_ = GetItemAttrAccessor(item_accessor_map, "l_is_copy_attr");
  a_is_new_app_ver_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_new_app_ver_attr");
  a_is_chat_audio_exemption_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "a_is_chat_audio_exemption_attr");
  l_begin_show_type_accessor_ = GetItemAttrAccessor(item_accessor_map, "l_begin_show_type_attr");
  a_is_random_blocked_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_random_blocked_attr");
  a_is_liar_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_liar_attr");
  a_is_hrelation_author_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_hrelation_author_attr");
  a_is_low_quality_v1_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_low_quality_v1_attr");
  a_is_low_quality_v2_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_low_quality_v2_attr");
  a_is_low_quality_v3_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_low_quality_v3_attr");
  a_is_low_quality_v4_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_low_quality_v4_attr");
  a_is_low_quality_v5_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_low_quality_v5_attr");
  a_is_low_quality_v6_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_low_quality_v6_attr");
  a_is_low_quality_v7_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_low_quality_v7_attr");
  a_is_low_quality_v8_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_low_quality_v8_attr");
  a_is_low_quality_v9_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_low_quality_v9_attr");
  a_is_low_quality_v10_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_low_quality_v10_attr");
  a_in_threshold_list_v1_limit_num_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "a_in_threshold_list_v1_limit_num_attr");
  a_in_threshold_list_v2_limit_num_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "a_in_threshold_list_v2_limit_num_attr");
  a_in_kconf_blacklist_but_exemption_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "a_in_kconf_blacklist_but_exemption_attr");
  a_global_govern_author_bucket_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "a_global_govern_author_bucket_attr");
  a_affair_type_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_affair_type_attr");
  a_in_market_whitelist_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_in_market_whitelist_attr");
  a_stream_health_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_stream_health_attr");
  ll_is_aigc_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "ll_is_aigc_attr");
  ll_goods_second_cate_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "ll_goods_second_cate_attr");
  ll_goods_second_cate_new_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "ll_goods_second_cate_new_attr");
  ll_filter_user_city_in_item_city_list_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "ll_filter_user_city_in_item_city_list_attr");
  l_is_merchant_virtual_human_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "l_is_merchant_virtual_human_attr");
  replace_author_id_accessor_ = GetItemAttrAccessor(item_accessor_map, "replace_author_id_attr");
  key_list_accessor_ = GetItemAttrAccessor(item_accessor_map, "scene_punish_info_key_list_attr");
  effect_list_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "scene_punish_info_effect_list_attr");
  follow_click_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "count.follow_click_attr");
  grade_label_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "grade_label_attr");
  slide_author_version_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "audit_author_white_info.slide_author_version_attr");
  has_item_info_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "has_item_info_attr");
  has_dynamic_item_info_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "has_dynamic_item_info_attr");
  is_cover_merchant_live_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "is_cover_merchant_live_attr");
  is_merchant_live_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "is_merchant_live_attr");
  mmu_impre_score_accessor_ =
   GetItemAttrAccessor(item_accessor_map, "mmu_info.mmu_impre_score_attr");
  a_category_type_accessor_ =
   GetItemAttrAccessor(item_accessor_map, "a_category_type_attr");
  a_good_author_v4_raw_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_good_author_v4_raw_attr");
  l_distribution_mark_code_list_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "l_distribution_mark_code_list_attr");
  a_is_copy_grey_exemption_v1_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "a_is_copy_grey_exemption_v1_attr");
  a_is_copy_grey_exemption_v2_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "a_is_copy_grey_exemption_v2_attr");
  a_is_follow_author_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_follow_author_attr");
  big_v_type_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "audit_author_big_v_info_big_v_type_attr");
  explore_author_version_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "audit_author_white_info_explore_author_version_attr");
  live_all_tags_accessor_ =
    GetItemAttrAccessor(item_accessor_map, "live_all_tags_attr");
  is_seller_author_accessor_ =
   GetItemAttrAccessor(item_accessor_map, "author_info_is_seller_author_attr");
  cold_start_reason_count_accessor_ =
   GetItemAttrAccessor(item_accessor_map, "cold_start_reason_count_attr");
  recruit_author_map_filter_prob_accessor_ =
   GetItemAttrAccessor(item_accessor_map, "recruit_author_map_filter_prob_attr");
  author_batch_id_accessor_ =
   GetItemAttrAccessor(item_accessor_map, "author_batch_id_attr");
  voice_party_play_type_accessor_ =
   GetItemAttrAccessor(item_accessor_map, "voice_party_play_type_attr");
  reason_is_only_1825_accessor_ = GetItemAttrAccessor(item_accessor_map, "reason_is_only_1825_attr");
  is_pk_core_author_flag_accessor_ = GetItemAttrAccessor(item_accessor_map, "is_pk_core_author_flag_attr");
  is_pk_now_accessor_ = GetItemAttrAccessor(item_accessor_map, "is_pk_now_attr");
  a_is_porn_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_porn_attr");
  a_is_story_accessor_ = GetItemAttrAccessor(item_accessor_map, "a_is_story_attr");
  l_view_type_names_accessor_ = GetItemAttrAccessor(item_accessor_map, "l_view_type_names_attr");
  revenue_amount_60d_accessor_ = GetItemAttrAccessor(item_accessor_map, "revenue_amount_60d_attr");
  l_recruit_5R_frequency_filter_accessor_ =
   GetItemAttrAccessor(item_accessor_map, "l_recruit_5R_frequency_filter_attr");
}

void ItemAttrContainer::ResetItemAttrContainer(ReadableRecoContextInterface *context,
                                               const CommonRecoResult &result) {
  // 获取 item attr
  photo_id_ = context->GetIntItemAttr(result, photo_id_accessor_);
  is_shop_live_ = context->GetIntItemAttr(result, is_shop_live_accessor_);
  aGoodAuthorRangeV4KV_ = context->GetIntItemAttr(result, aGoodAuthorRangeV4KV_accessor_);
  recruit_priority_attr_ = context->GetIntItemAttr(result, recruit_priority_attr_accessor_);
  mmu_tag_id_ = context->GetIntListItemAttr(result, mmu_tag_id_accessor_);
  reason_list_ = context->GetIntListItemAttr(result, reason_list_accessor_);
  is_game_live_ = context->GetIntItemAttr(result, is_game_live_accessor_);
  is_audio_live_room_ = context->GetIntItemAttr(result, is_audio_live_room_accessor_);
  is_ktv_ = context->GetIntItemAttr(result, is_ktv_accessor_);
  is_theater_ = context->GetIntItemAttr(result, is_theater_accessor_);
  is_teampk_ = context->GetIntItemAttr(result, is_teampk_accessor_);
  is_voice_party_ = context->GetIntItemAttr(result, is_voice_party_accessor_);
  a_single_kill_label_ = context->GetIntItemAttr(result, a_single_kill_label_accessor_);
  l_merchant_themis_author_kv_ = context->GetIntItemAttr(result, l_merchant_themis_author_kv_accessor_);
  author_id_ = context->GetIntItemAttr(result, author_id_accessor_);
  l_thompson_neg_gamora_v1_ = context->GetDoubleItemAttr(result, l_thompson_neg_gamora_v1_accessor_);
  l_thompson_neg_nebula_v1_ = context->GetDoubleItemAttr(result, l_thompson_neg_nebula_v1_accessor_);
  l_thompson_neg_gamora_v2_ = context->GetDoubleItemAttr(result, l_thompson_neg_gamora_v2_accessor_);
  l_thompson_neg_nebula_v2_ = context->GetDoubleItemAttr(result, l_thompson_neg_nebula_v2_accessor_);
  mmu_tag_info_tag_list_lv2_ = context->GetIntListItemAttr(result, mmu_tag_info_tag_list_lv2_accessor_);
  l_mmu_seg_attr_list_ = context->GetIntListItemAttr(result, l_mmu_seg_attr_list_accessor_);
  mmu_user_hetu_tag_ = context->GetIntListItemAttr(result, mmu_user_hetu_tag_accessor_);
  l_realshow_recent_ = context->GetIntItemAttr(result, l_realshow_recent_accessor_);
  l_report_recent_ = context->GetIntItemAttr(result, l_report_recent_accessor_);
  l_negative_recent_ = context->GetIntItemAttr(result, l_negative_recent_accessor_);
  l_dur_per_show_ = context->GetDoubleItemAttr(result, l_dur_per_show_accessor_);
  l_ring_ball_ = context->GetIntItemAttr(result, l_ring_ball_accessor_);
  author_info_is_seller_author_v2_ =
      context->GetIntItemAttr(result, author_info_is_seller_author_v2_accessor_);
  l_risk_level_ = context->GetIntItemAttr(result, l_risk_level_accessor_);
  good_author_range_v4_ = context->GetIntItemAttr(result, aGoodAuthorRangeV4KV_accessor_);
  consume_good_author_range_v4_ = context->GetIntItemAttr(result, aGoodAuthorConsumeV4IntKV_accessor_);
  l_shuffle_policy_ = context->GetIntItemAttr(result, l_shuffle_policy_accessor_);
  punish_sirius_all_ = context->GetStringItemAttr(result, punish_sirius_all_accessor_);
  l_recruit_query_room_bool_ =
      context->GetIntItemAttr(result, l_recruit_query_room_bool_accessor_);
  l_live_house_is_live_tag_ =
      context->GetIntItemAttr(result, l_live_house_is_live_tag_accessor_);
  l_live_house_is_una_live_tag_ =
      context->GetIntItemAttr(result, l_live_house_is_una_live_tag_accessor_);
  l_is_local_life_ = context->GetIntItemAttr(result, l_is_local_life_accessor_);
  l_poi_geo_hash4_list_ = context->GetStringListItemAttr(result, l_poi_geo_hash4_list_accessor_);
  l_live_zero_punish_ab_suffix_list_ =
      context->GetStringListItemAttr(result, l_live_zero_punish_ab_suffix_list_accessor_);
  slide_real_show_count_ = context->GetIntItemAttr(result, slide_real_show_count_accessor_);
  slide_real_show_all_day_count_ = context->GetIntItemAttr(result, slide_real_show_all_day_count_accessor_);
  slide_inner_real_show_all_day_count_ =
      context->GetIntItemAttr(result, slide_inner_real_show_all_day_count_accessor_);
  slide_outter_real_show_all_day_count_ =
      context->GetIntItemAttr(result, slide_outter_real_show_all_day_count_accessor_);
  explore_inner_real_show_all_day_count_ =
      context->GetIntItemAttr(result, explore_inner_real_show_all_day_count_accessor_);
  explore_outer_real_show_all_day_count_ =
      context->GetIntItemAttr(result, explore_outer_real_show_all_day_count_accessor_);
  live_tag_real_show_all_day_count_ =
      context->GetIntItemAttr(result, live_tag_real_show_all_day_count_accessor_);
  a_c_start_author_bucket_ = context->GetIntItemAttr(result, a_c_start_author_bucket_accessor_);
  a_c_start_expect_total_show_ = context->GetIntItemAttr(result, a_c_start_expect_total_show_accessor_);
  a_c_start_current_total_show_ = context->GetIntItemAttr(result, a_c_start_current_total_show_accessor_);
  a_c_start_expect_total_gift_ = context->GetIntItemAttr(result, a_c_start_expect_total_gift_accessor_);
  a_c_start_current_total_gift_ = context->GetIntItemAttr(result, a_c_start_current_total_gift_accessor_);
  a_c_start_expect_total_show_v1_ =
      context->GetIntItemAttr(result, a_c_start_expect_total_show_v1_accessor_);
  a_c_start_expect_total_show_v2_ =
      context->GetIntItemAttr(result, a_c_start_expect_total_show_v2_accessor_);
  a_c_start_expect_total_show_v3_ =
      context->GetIntItemAttr(result, a_c_start_expect_total_show_v3_accessor_);
  a_c_start_expect_total_show_v4_ =
      context->GetIntItemAttr(result, a_c_start_expect_total_show_v4_accessor_);
  a_c_start_expect_total_gift_v1_ =
      context->GetIntItemAttr(result, a_c_start_expect_total_gift_v1_accessor_);
  a_c_start_expect_total_gift_v2_ =
      context->GetIntItemAttr(result, a_c_start_expect_total_gift_v2_accessor_);
  a_c_start_expect_total_gift_v3_ =
      context->GetIntItemAttr(result, a_c_start_expect_total_gift_v3_accessor_);
  a_c_start_expect_total_gift_v4_ =
      context->GetIntItemAttr(result, a_c_start_expect_total_gift_v4_accessor_);
  author_support_type_ = context->GetStringItemAttr(result, author_support_type_accessor_);
  cold_start_queue_reason_value_ =
      context->GetIntItemAttr(result, cold_start_queue_reason_value_accessor_);
  a_is_house_live_kv_ = context->GetIntItemAttr(result, a_is_house_live_kv_accessor_);
  l_live_house_is_dream_house_ = context->GetIntItemAttr(result, l_live_house_is_dream_house_accessor_);
  a_is_private_user_kv_ = context->GetStringItemAttr(result, a_is_private_user_kv_accessor_);
  a_recruit_org_id_kv_ = context->GetIntItemAttr(result, a_recruit_org_id_kv_accessor_);
  a_violation_punish_rt_ = context->GetIntItemAttr(result, a_violation_punish_rt_accessor_);
  a_violation_punish_offline_ = context->GetIntItemAttr(result, a_violation_punish_offline_accessor_);
  mmu_tag_info_tag_list_lv1_ =
      context->GetIntListItemAttr(result, mmu_tag_info_tag_list_lv1_accessor_);
  mmu_tag_info_tag_list_lv3_ =
      context->GetIntListItemAttr(result, mmu_tag_info_tag_list_lv3_accessor_);
  l_lat_kv_ = context->GetDoubleItemAttr(result, l_lat_kv_accessor_);
  l_lon_kv_ = context->GetDoubleItemAttr(result, l_lon_kv_accessor_);
  l_live_location_city_ = context->GetStringItemAttr(result, l_live_location_city_accessor_);
  l_live_location_province_ =
      context->GetStringItemAttr(result, l_live_location_province_accessor_);
  grade_label_for_temp_ = context->GetIntListItemAttr(result, grade_label_for_temp_accessor_);
  grade_label_for_aid_temp_ =
      context->GetIntListItemAttr(result, grade_label_for_aid_temp_accessor_);
  l_is_chat_gird_whitelist_aid_ =
      context->GetIntItemAttr(result, l_is_chat_gird_whitelist_aid_accessor_);
  l_voice_party_status_ = context->GetDoubleItemAttr(result, l_voice_party_status_accessor_);
  l_voice_party_open_video_status_ =
      context->GetIntItemAttr(result, l_voice_party_open_video_status_accessor_);
  a_content_emb_v2_ = context->GetDoubleListItemAttr(result, a_content_emb_v2_accessor_);
  l_hot_explore_punish_tag_ = context->GetIntItemAttr(result, l_hot_explore_punish_tag_accessor_);
  l_hot_small_business_punish_city_ =
      context->GetIntListItemAttr(result, l_hot_small_business_punish_city_accessor_);
  l_hot_explore_punish_city_ = context->GetIntListItemAttr(result, l_hot_explore_punish_city_accessor_);
  l_hot_slide_punish_city_ = context->GetIntListItemAttr(result, l_hot_slide_punish_city_accessor_);
  l_interactive_game_type_ = context->GetIntItemAttr(result, l_interactive_game_type_accessor_);
  item_off_filter_list_ = context->GetIntListItemAttr(result,  item_off_filter_list_accessor_);
  ll_brand_name_ = context->GetStringItemAttr(result, ll_brand_name_accessor_);
  ll_brand_id_ = context->GetIntItemAttr(result, ll_brand_id_accessor_);
  ll_new_brand_id_ = context->GetIntItemAttr(result, ll_new_brand_id_accessor_);
  ll_merchant_id_ = context->GetIntItemAttr(result, ll_merchant_id_accessor_);
  ll_mmu_ = context->GetIntItemAttr(result, ll_mmu_accessor_);
  ll_restrict_author_ = context->GetIntItemAttr(result, ll_restrict_author_accessor_);
  ll_fireworks_author_ = context->GetIntItemAttr(result, ll_fireworks_author_accessor_);
  ll_geohash_distance_ = context->GetDoubleItemAttr(result, ll_geohash_distance_accessor_);
  ll_geohash_filter_value_ = context->GetDoubleItemAttr(result, ll_geohash_filter_value_accessor_);
  ll_skip_distance_filter_ = context->GetIntItemAttr(result, ll_skip_distance_filter_accessor_);
  is_cny_live_punish_ = context->GetIntItemAttr(result, is_cny_live_punish_accessor_);
  is_cny_user_punish_ = context->GetIntItemAttr(result, is_cny_user_punish_accessor_);
  ll_poi_city_id_list_ = context->GetIntListItemAttr(result, ll_poi_city_id_list_accessor_);
  ll_goods_cate_2_ = context->GetStringItemAttr(result, ll_goods_cate_2_accessor_);
  ll_goods_cate_3_id_ = context->GetIntItemAttr(result, ll_goods_cate_3_id_accessor_);
  ll_mark_code_ = context->GetIntItemAttr(result, ll_mark_code_accessor_);
  ll_slide_show_cnt_ = context->GetIntItemAttr(result, ll_slide_show_cnt_accessor_);
  a_is_ig_black_author_ = context->GetIntItemAttr(result, a_is_ig_black_accessor_);
  a_kva_is_content2_ = context->GetDoubleItemAttr(result, a_kva_is_content2_accessor_);
  a_kva_is_content7_ = context->GetDoubleItemAttr(result, a_kva_is_content7_accessor_);
  a_is_pk_script_ = context->GetIntItemAttr(result, a_is_pk_script_accessor_);
  a_revenue_stage_ = context->GetDoubleItemAttr(result, a_revenue_stage_accessor_);
  a_author_tail_ = context->GetDoubleItemAttr(result, a_author_tail_accessor_);
  bigr_hav_in_14d_ = context->GetDoubleItemAttr(result, bigr_hav_in_14d_accessor_);
  a_is_good_author_ = context->GetIntItemAttr(result, a_is_good_author_accessor_);
  a_is_copyright_infri_ = context->GetIntItemAttr(result, a_is_copyright_infri_accessor_);
  a_is_digital_ = context->GetIntItemAttr(result, a_is_digital_accessor_);
  a_is_longtime_ = context->GetIntItemAttr(result, a_is_longtime_accessor_);
  l_is_copy_ = context->GetIntItemAttr(result, l_is_copy_accessor_);
  a_is_new_app_ver_ = context->GetIntItemAttr(result, a_is_new_app_ver_accessor_);
  a_is_chat_audio_exemption_ = context->GetIntItemAttr(result, a_is_chat_audio_exemption_accessor_);
  l_begin_show_type_ = context->GetIntItemAttr(result, l_begin_show_type_accessor_);
  a_bucket_total_score_ = context->GetIntItemAttr(result, a_bucket_total_score_accessor_);
  a_is_random_blocked_ = context->GetIntItemAttr(result, a_is_random_blocked_accessor_);
  a_is_liar_ = context->GetIntItemAttr(result, a_is_liar_accessor_);
  a_is_hrelation_author_ = context->GetIntItemAttr(result, a_is_hrelation_author_accessor_);
  a_is_low_quality_v1_ = context->GetIntItemAttr(result, a_is_low_quality_v1_accessor_);
  a_is_low_quality_v2_ = context->GetIntItemAttr(result, a_is_low_quality_v2_accessor_);
  a_is_low_quality_v3_ = context->GetIntItemAttr(result, a_is_low_quality_v3_accessor_);
  a_is_low_quality_v4_ = context->GetIntItemAttr(result, a_is_low_quality_v4_accessor_);
  a_is_low_quality_v5_ = context->GetIntItemAttr(result, a_is_low_quality_v5_accessor_);
  a_is_low_quality_v6_ = context->GetIntItemAttr(result, a_is_low_quality_v6_accessor_);
  a_is_low_quality_v7_ = context->GetIntItemAttr(result, a_is_low_quality_v7_accessor_);
  a_is_low_quality_v8_ = context->GetIntItemAttr(result, a_is_low_quality_v8_accessor_);
  a_is_low_quality_v9_ = context->GetIntItemAttr(result, a_is_low_quality_v9_accessor_);
  a_is_low_quality_v10_ = context->GetIntItemAttr(result, a_is_low_quality_v10_accessor_);
  a_in_threshold_list_v1_limit_num_ =
        context->GetIntItemAttr(result, a_in_threshold_list_v1_limit_num_accessor_);
  a_in_threshold_list_v2_limit_num_ =
        context->GetIntItemAttr(result, a_in_threshold_list_v2_limit_num_accessor_);
  a_in_kconf_blacklist_but_exemption_ =
        context->GetIntItemAttr(result, a_in_kconf_blacklist_but_exemption_accessor_);
  a_global_govern_author_bucket_ =
        context->GetIntItemAttr(result, a_global_govern_author_bucket_accessor_);
  a_affair_type_ = context->GetIntItemAttr(result, a_affair_type_accessor_);
  a_in_market_whitelist_ = context->GetIntItemAttr(result, a_in_market_whitelist_accessor_);
  a_stream_health_ = context->GetIntItemAttr(result, a_stream_health_accessor_);
  ll_is_aigc_ = context->GetIntItemAttr(result, ll_is_aigc_accessor_);
  ll_goods_second_cate_ = context->GetStringItemAttr(result, ll_goods_second_cate_accessor_);
  ll_goods_second_cate_new_ = context->GetStringItemAttr(result, ll_goods_second_cate_new_accessor_);
  ll_filter_user_city_in_item_city_list_ =
        context->GetIntItemAttr(result, ll_filter_user_city_in_item_city_list_accessor_);
  l_is_merchant_virtual_human_ = context->GetIntItemAttr(result, l_is_merchant_virtual_human_accessor_);
  replace_author_id_ = context->GetIntItemAttr(result, replace_author_id_accessor_);
  key_list_ = context->GetIntListItemAttr(result, key_list_accessor_);
  effect_list_ = context->GetStringListItemAttr(result, effect_list_accessor_);
  follow_click_ = context->GetIntItemAttr(result, follow_click_accessor_);
  grade_label_ = context->GetIntListItemAttr(result, grade_label_accessor_);
  slide_author_version_ = context->GetIntItemAttr(result, slide_author_version_accessor_);
  has_item_info_ = context->GetIntItemAttr(result, has_item_info_accessor_);
  has_dynamic_item_info_ = context->GetIntItemAttr(result, has_dynamic_item_info_accessor_);
  is_cover_merchant_live_ = context->GetIntItemAttr(result, is_cover_merchant_live_accessor_);
  is_merchant_live_ = context->GetIntItemAttr(result, is_merchant_live_accessor_);
  mmu_impre_score_ = context->GetDoubleItemAttr(result, mmu_impre_score_accessor_);
  a_category_type_ = context->GetStringItemAttr(result, a_category_type_accessor_);
  a_good_author_v4_raw_ = context->GetIntItemAttr(result, a_good_author_v4_raw_accessor_);
  l_distribution_mark_code_list_ =
      context->GetIntListItemAttr(result, l_distribution_mark_code_list_accessor_);
  a_is_copy_grey_exemption_v1_ = context->GetIntItemAttr(result, a_is_copy_grey_exemption_v1_accessor_);
  a_is_copy_grey_exemption_v2_ = context->GetIntItemAttr(result, a_is_copy_grey_exemption_v2_accessor_);
  a_is_follow_author_ = context->GetIntItemAttr(result, a_is_follow_author_accessor_);
  big_v_type_ = context->GetIntItemAttr(result, big_v_type_accessor_);
  explore_author_version_ = context->GetIntItemAttr(result, explore_author_version_accessor_);
  live_all_tags_ = context->GetStringListItemAttr(result, live_all_tags_accessor_);
  is_seller_author_ = context->GetIntItemAttr(result, is_seller_author_accessor_);
  cold_start_reason_count_ = context->GetIntItemAttr(result, cold_start_reason_count_accessor_);
  recruit_author_map_filter_prob_ =
        context->GetDoubleItemAttr(result, recruit_author_map_filter_prob_accessor_);
  author_batch_id_ = context->GetIntItemAttr(result, author_batch_id_accessor_);
  voice_party_play_type_ = context->GetIntItemAttr(result, voice_party_play_type_accessor_);
  reason_is_only_1825_ = context->GetIntItemAttr(result, reason_is_only_1825_accessor_);
  is_pk_core_author_flag_ = context->GetStringItemAttr(result, is_pk_core_author_flag_accessor_);
  is_pk_now_ = context->GetIntItemAttr(result, is_pk_now_accessor_);
  a_is_porn_ = context->GetIntItemAttr(result, a_is_porn_accessor_);
  a_is_story_ = context->GetIntItemAttr(result, a_is_story_accessor_);
  l_view_type_names_ = context->GetStringListItemAttr(result, l_view_type_names_accessor_);
  revenue_amount_60d_ = context->GetIntItemAttr(result, revenue_amount_60d_accessor_);
  l_recruit_5R_frequency_filter_ = context->GetIntItemAttr(result, l_recruit_5R_frequency_filter_accessor_);
}

}  // namespace livestream
}  // namespace platform
}  // namespace ks
