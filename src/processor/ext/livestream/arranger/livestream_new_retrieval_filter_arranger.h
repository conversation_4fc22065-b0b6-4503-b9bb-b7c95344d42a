#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>

#include "ks/reco_pub/reco/grade_distribute/grade_distribute_matcher.h"
#include "dragon/src/processor/base/common_reco_base_arranger.h"
#include "dragon/src/processor/ext/livestream/module/filter/base_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/demo.h"
#include "dragon/src/processor/ext/livestream/module/filter/is_living_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/merchant_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/recruit_channel_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/recruit_feature_negative_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/channel_reason_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/cleaner_reason_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/game_shop_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/live_channel_chatroom_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/live_author_single_kill_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/themis_shopping_liveroom_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/user_be_blacked_author_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/cny_author_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/printer_author_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/printer_a2a_ann_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/thompson_negative_explore_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/live_blind_date_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/audio_live_room_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/seller_author_v2_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/seller_author_v2_cutype_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/is_shop_live_cutype_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/vertical_cutype_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/movie_cutype_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/recruit_feature_price_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/risk_level_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/risk_shuffle_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/unfollow_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/new_neg_system_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/zero_punish_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/cold_start_request_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/house_live_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/live_questionnaire_feedback_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/explore_seller_author_v2_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/explore_shop_live_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/explore_marketing_live_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/private_user_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/cleaner_live_space_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/live_history_play_a2a_variant_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/reason_whitelist_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/live_hate_list_swing_a2a_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/recruit_deliver_org_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/unify_black_action_author_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/happy_play_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/game_retrieval_random_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/channel_lbs_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/new_grade_label_sys_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/movie_live_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/live_voice_party_status_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/live_consume_explore_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/dynamic_neg_action_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/local_life_live_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/recruit_feature_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/official_risk_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/interaction_game_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/interaction_game_v2_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/movie_live_v2_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/live_gr_pr_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/recruit_gift_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/live_pay_user_kconf_bad_author_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/dislike_current_author_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/live_short_play_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/inner_reason_white_list_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/inner_mmu_tag_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/inner_browsed_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/ring_ball_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/redirect_author_and_mmu_tag_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/kconf_specific_author_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/cny_punish_live_author_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/cny_fireworks_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/movie_prob_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/interact_game_black_author_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/live_beauty_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/pk_script_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/movie_live_v3_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/good_author_pool_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/pk_authors_neg_action_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/fx_author_shield_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/high_arppu_bigr_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/copyright_infringement_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/bekicked_black_author_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/merchant_virtual_human_live_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/movie_browse_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/case_browse_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/live_show_cap_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/blacklist_remove_all_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/case_random_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/ua_exemption_cap_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/threshold_author_list_cap_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/replace_author_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/mmu_seg_list_cap_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/stream_health_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/cap_ratio_random_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/low_value_follow_author_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/category_support_author_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/copy_grey_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/dual_r1_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/only_multi_white_list_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/follow_list_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/only_diversity_tag_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/seller_author_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/browsed_set_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/black_list_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/browsed_author_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/grade_label_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/hate_list_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/item_info_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/punish_live_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/slide_only_multi_white_list_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/user_black_list_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/recruit_live_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/kconf_official_author_cap_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/recruit_author_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/report_author_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/mix_browset_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/low_active_user_bad_author_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/copyright_infringement_v1_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/ia_neg_retr_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/pk_value_author_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/low_ea_recall_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/inner_low_active_user_bad_author_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/badcase_for_bigr_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/explore_screen_author_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/unfollow_author_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/sepcific_markcode_block_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/kconf_official_vedio_cap_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/l_view_type_names_filter.h"
#include "dragon/src/processor/ext/livestream/module/filter/l_recruit_5R_frequency_filter.h"


namespace ks {
namespace platform {

class LiveStreamNewRetrievalFilterArranger : public CommonRecoBaseArranger {
 public:
  LiveStreamNewRetrievalFilterArranger() {}

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  bool InitProcessor() override {
    // 分级分发 kconf 初始化
    if (!ks::reco::grade_distribute::GradeDistributeHelper::GetInstance()->Init(
            "reco.live.onlineLiveGradeLabelRules")) {
      LOG(ERROR) << "LiveStreamRetrievalFilterEnricher init failed! Grade distribute not load!";
      return false;
    }
    debug_mode_ = config()->GetBoolean("debug_mode", false);
    if (debug_mode_) {
      export_item_attr_ = config()->GetString("export_item_attr");
      if (export_item_attr_.empty()) {
        LOG(ERROR) << "LiveStreamNewRetrievalFilterArranger init failed! "
                   << "Missing \"export_item_attr\" config!";
        return false;
      }
    }

    item_attr_map_config_ = config()->Get("item_attr_map");
    if (!item_attr_map_config_ || !item_attr_map_config_->IsObject()) {
      LOG(ERROR) << "LiveStreamNewRetrievalFilterArranger init failed! Missing \"item_attr_map\" config!";
      return false;
    }

    const auto *filter_list_config = config()->Get("filters");
    if (!filter_list_config || !filter_list_config->IsArray()) {
      LOG(ERROR) << "LiveStreamNewRetrievalFilterArranger init failed! Missing \"filters\" config!";
      return false;
    }

    filter_vec_.reserve(filter_list_config->array().size());
    for (const auto *filter_config : filter_list_config->array()) {
      if (!filter_config->IsObject()) {
        continue;
      }

      std::string name = filter_config->GetString("name");
      if (name.empty()) {
        continue;
      }

      auto iter = filter_map_.find(name);
      if (iter == filter_map_.end()) {
        continue;
      }

      filter_vec_.push_back(Filter {
        .name = name,
        .flag = filter_config->GetInt("filter_flag", -1),
        .enable = false,
        .config = filter_config,
        .processor = iter->second,
      });
    }

    return true;
  }

 private:
  void InitTruncationMap(MutableRecoContextInterface *context);
  void Perf(const std::string &request_type);

  folly::F14FastMap<std::string, std::shared_ptr<livestream::BaseFilter>> filter_map_ {
    // 添加 filter
    { "demo", std::shared_ptr<livestream::BaseFilter>(new livestream::DemoFilter()) },
    { "is_living_filter", std::shared_ptr<livestream::BaseFilter>(new livestream::IsLivingFilter()) },
    { "merchant_filter", std::shared_ptr<livestream::BaseFilter>(new livestream::MerchantFilter()) },
    { "recruit_channel_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::RecruitChannelFilter()) },
    { "recruit_feature_negative_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::RecruitFeatureNegativeFilter()) },
    { "channel_reason_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::ChannelReasonFilter()) },
    { "cleaner_reason_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::CleanerReasonFilter()) },
    { "game_shop_filter", std::shared_ptr<livestream::BaseFilter>(new livestream::GameShopFilter()) },
    { "live_channel_chatroom_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::LiveChannelChatroomFilter()) },
    { "live_author_single_kill_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::LiveAuthorSingleKillFilter()) },
    { "themis_shopping_liveroom_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::ThemisShoppingLiveRoomFilter()) },
    { "user_be_blacked_author_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::UserBeBlackedAuthorFilter()) },
    { "cny_author_filter", std::shared_ptr<livestream::BaseFilter>(new livestream::CNYAuthorFilter()) },
    { "printer_author_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::PrinterAuthorFilter()) },
    { "printer_a2a_ann_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::PrinterA2aAnnFilter()) },
    { "thompson_negative_explore_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::ThompsonNegativeExploreFilter()) },
    { "live_blind_date_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::LiveBlindDateFilter()) },
    { "audio_live_room_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::AudioLiveRoomFilter()) },
    { "seller_author_v2_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::SellerAuthorV2Filter()) },
    { "seller_author_v2_cutype_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::SellerAuthorV2CUtypeFilter()) },
    { "is_shop_live_cutype_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::IsShopLiveCUtypeFilter()) },
    { "vertical_cutype_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::VerticalCUtypeFilter()) },
    { "movie_cutype_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::MovieCUtypeFilter()) },
    { "recruit_feature_price_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::RecruitFeaturePriceFilter()) },
    { "risk_level_filter", std::shared_ptr<livestream::BaseFilter>(new livestream::RiskLevelFilter()) },
    { "risk_shuffle_filter", std::shared_ptr<livestream::BaseFilter>(new livestream::RiskShuffleFilter()) },
    { "unfollow_filter", std::shared_ptr<livestream::BaseFilter>(new livestream::UnfollowFilter()) },
    { "new_neg_system_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::NewNegSystemFilter()) },
    { "zero_punish_filter", std::shared_ptr<livestream::BaseFilter>(new livestream::ZeroPunishFilter()) },
    { "cold_start_request_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::ColdStartRequestFilter()) },
    { "house_live_filter", std::shared_ptr<livestream::BaseFilter>(new livestream::HouseLiveFilter()) },
    { "live_questionnaire_feedback_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::LiveQuestionnaireFeedbackFilter()) },
    { "explore_seller_author_v2_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::ExploreSellerAuthorV2Filter()) },
    { "explore_shop_live_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::ExploreShopLiveFilter()) },
    { "explore_marketing_live_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::ExploreMarketingLiveFilter()) },
    { "private_user_filter", std::shared_ptr<livestream::BaseFilter>(new livestream::PrivateUserFilter()) },
    { "cleaner_live_space_filter",
     std::shared_ptr<livestream::BaseFilter>(new livestream::CleanerLiveSpaceFilter()) },
    { "live_history_play_a2a_variant_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::LiveHistoryPlayA2AVariantFilter()) },
    { "reason_whitelist_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::ReasonWhitelistFilter()) },
    { "live_hate_list_swing_a2a_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::LiveHateListSwingA2AFilter()) },
    { "recruit_deliver_org_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::RecruitDeliverOrgFilter()) },
    { "unify_black_action_author_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::UnifyBlackActionAuthorFilter()) },
    { "happy_play_filter", std::shared_ptr<livestream::BaseFilter>(new livestream::HappyPlayFilter()) },
    { "game_retrieval_random_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::GameRetrievalRandomFilter()) },
    { "channel_lbs_filter", std::shared_ptr<livestream::BaseFilter>(new livestream::ChannelLbsFilter()) },
    { "new_grade_label_sys_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::NewGradeLabelSysFilter()) },
    { "movie_live_filter", std::shared_ptr<livestream::BaseFilter>(new livestream::MovieLiveFilter()) },
    { "movie_live_v3_filter", std::shared_ptr<livestream::BaseFilter>(new livestream::MovieLiveV3Filter()) },
    { "live_voice_party_status_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::LiveVoicePartyStatusFilter()) },
    { "live_consume_explore_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::LiveConsumeExploreFilter()) },
    { "dynamic_neg_action_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::DynamicNegActionFilter()) },
    { "local_life_live_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::LocalLifeLiveFilter()) },
    { "recruit_feature_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::RecruitFeatureFilter()) },
    { "official_risk_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::OfficialRiskFilter()) },
    { "interaction_game_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::InteractionGameFilter()) },
    { "interaction_game_v2_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::InteractionGameV2Filter()) },
    { "movie_live_v2_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::MovieLiveV2Filter()) },
    { "live_gr_pr_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::LiveGrPrFilter()) },
    { "recruit_gift_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::RecruitGiftFilter()) },
    { "pay_user_kconf_bad_author_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::LivePayUserKconfBadAuthorFilter()) },
    { "dislike_current_author_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::DislikeCurrentAuthorFilter()) },
    { "live_short_play_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::LiveShortPlayFilter()) },
    { "inner_reason_white_list_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::InnerReasonWhiteListFilter()) },
    { "inner_mmu_tag_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::InnerMmuTagFilter()) },
    { "inner_browsed_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::InnerBrowsedFilter()) },
    { "ring_ball_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::RingBallFilter()) },
    { "redirect_author_and_mmu_tag_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::RedirectAuthorAndMmuTagFilter()) },
    { "kconf_specific_author_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::KconfSpecificAuthorFilter()) },
    { "cny_punish_live_author_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::CnyPunishLiveAuthorFilter()) },
    { "cny_fireworks_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::CNYFireworksFilter()) },
    { "movie_prob_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::MovieProbFilter()) },
    { "interact_game_black_author_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::InteractGameBlackAuthorFilter()) },
    { "live_beauty_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::LiveBeautyFilter()) },
    { "pk_script_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::PKScriptFilter()) },
    { "good_author_pool_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::GoodAuthorPoolFilter()) },
    { "pk_neg_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::PkNegFilter()) },
    { "fx_author_shield_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::FxAuthorShieldFilter()) },
    { "high_arppu_bigr_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::HighArppuBigrFilter()) },
    { "copyright_infringement_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::CopyrightInfringementFilter()) },
    { "bekicked_black_author_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::BeKickedBlackAuthorFilter()) },
    { "merchant_virtual_human_live_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::MerchantVirtualHumanLiveFilter()) },
    { "movie_browse_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::MovieBrowseFilter()) },
    { "case_browse_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::CaseBrowseFilter()) },
    { "live_show_cap_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::LiveShowCapFilter()) },
    { "blacklist_remove_all_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::BlacklistRemoveAllFilter()) },
    { "case_random_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::CaseRandomFilter()) },
    { "ua_exemption_cap_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::UAExemptionCapFilter()) },
    { "threshold_author_list_cap_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::ThresholdAuthorListCapFilter()) },
    { "replace_author_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::ReplaceAuthorFilter()) },
    { "mmu_seg_list_cap_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::MmuSegListCapFilter()) },
    { "stream_health_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::StreamHealthFilter()) },
    { "cap_ratio_random_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::CapRatioRandomFilter()) },
    { "black_list_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::BlackListFilter()) },
    { "browsed_author_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::BrowsedAuthorFilter()) },
    { "grade_label_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::GradeLabelFilter()) },
    { "hate_list_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::HateListFilter()) },
    { "item_info_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::ItemInfoFilter()) },
    { "punish_live_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::PunishLiveFilter()) },
    { "slide_only_multi_white_list_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::SlideOnlyMultiWhiteListFilter()) },
    { "user_black_list_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::UserBlackListFilter()) },
    { "recruit_live_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::RecruitLiveFilter()) },
    { "low_value_follow_author_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::LowValueFollowAuthorFilter()) },
    { "category_support_author_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::CategorySupportAuthorFilter()) },
    { "copy_grey_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::CopyGreyFilter()) },
    { "dual_r1_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::DualR1Filter()) },
    { "kconf_official_author_cap_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::KconfOfficialAuthorCapFilter()) },
    { "only_multi_white_list_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::OnlyMultiWhiteListFilter()) },
    { "follow_list_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::FollowListFilter()) },
    { "only_diversity_tag_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::OnlyDiversityTagFilter()) },
    { "seller_author_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::SellerAuthorFilter()) },
    { "browsed_set_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::BrowsedSetFilter()) },
    { "recruit_author_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::RecruitAuthorFilter()) },
    { "report_author_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::ReportAuthorFilter()) },
    { "mix_browset_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::MixBrowsetFilter()) },
    { "low_active_user_bad_author_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::LowActiveUserBadAuthorFilter()) },
    { "copyright_infringement_v1_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::CopyrightInfringementV1Filter()) },
    { "ia_neg_retr_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::IaNegRetrFilter()) },
    { "pk_value_author_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::PkValueAuthorFilter()) },
    { "low_ea_recall_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::LowEaRecallFilter()) },
    { "inner_low_active_user_bad_author_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::InnerLowActiveUserBadAuthorFilter()) },
    { "badcase_for_bigr_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::BadcaseForBigrFilter()) },
    { "explore_screen_author_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::ExploreScreenAuthorFilter()) },
    { "unfollow_author_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::UnfollowAuthorFilter()) },
    { "sepcific_markcode_block_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::SepcificMarkcodeBlockFilter()) },
    { "kconf_official_vedio_cap_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::KconfOfficialVedioCapFilter()) },
    { "l_view_type_names_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::LViewTypeNamesFilter()) },
    { "l_recruit_5R_frequency_filter",
      std::shared_ptr<livestream::BaseFilter>(new livestream::LRecruit5RFrequencyFilter()) }
  };

  struct Filter {
    std::string name;
    int64 flag;
    bool enable = false;
    const base::Json *config = nullptr;
    std::shared_ptr<livestream::BaseFilter> processor;
  };

  std::vector<Filter> filter_vec_;
  folly::F14FastMap<int, int> truncation_map_;
  std::vector<CommonRecoResult> new_result_vec_;
  folly::F14FastMap<int, int> reason_num_map_;
  folly::F14FastMap<int, folly::F14FastMap<int, int>> filter_num_map_;
  const base::Json *item_attr_map_config_ = nullptr;
  folly::F14FastMap<std::string, const ItemAttr *> item_accessor_map_;
  bool debug_mode_ = false;
  std::string export_item_attr_;
  ItemAttr *export_item_attr_accessor_ = nullptr;
  folly::F14FastSet<int64> common_off_filter_set_;
  folly::F14FastSet<int64> common_on_filter_set_;

  DISALLOW_COPY_AND_ASSIGN(LiveStreamNewRetrievalFilterArranger);
};

}  // namespace platform
}  // namespace ks
