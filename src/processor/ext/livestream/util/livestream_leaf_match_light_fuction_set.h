#pragma once

#include <sys/timeb.h>
#include <algorithm>
#include <map>
#include <unordered_map>
#include <set>
#include <string>
#include <unordered_set>
#include <utility>
#include <vector>
#include <cmath>
#include <functional>
#include <ctime>
#include <random>

#include "dragon/src/module/common_reco_light_function.h"
#include "dragon/src/processor/ext/livestream/util/grade_distribute_matcher_v2.h"
#include "dragon/src/processor/ext/livestream/util/livestream_util.h"
#include "third_party/abseil/absl/strings/numbers.h"
#include "base/time/timestamp.h"
#include "folly/container/F14Map.h"
#include "third_party/nlohmann_json/include/nlohmann/json.hpp"

namespace ks {
namespace platform {
class LiveStreamLeafMatchLightFunctionSet : public CommonRecoBaseLightFunctionSet {
 public:
  LiveStreamLeafMatchLightFunctionSet() {
    REGISTER_LIGHT_FUNCTION(CheckIsCoverRevenueLive);
    REGISTER_LIGHT_FUNCTION(CheckIsRevenue);
    REGISTER_LIGHT_FUNCTION(CalcRevenueEmpScore);
    REGISTER_LIGHT_FUNCTION(CalcRevenueScoreRank);
    REGISTER_LIGHT_FUNCTION(GenerateMatchFlags);
    REGISTER_LIGHT_FUNCTION(GenerateUaOverlapRedisKey);
    REGISTER_LIGHT_FUNCTION(CalcOverlapScore);
    REGISTER_LIGHT_FUNCTION(CalcRevenueAuthorScore);
    REGISTER_LIGHT_FUNCTION(CalcRevenueUserScore);
    REGISTER_LIGHT_FUNCTION(CalcFollowRevenueLive);
    REGISTER_LIGHT_FUNCTION(CalcRecruitPeopleFlag);
    REGISTER_LIGHT_FUNCTION(CalcRecruitMmuFilterFlag);
    REGISTER_LIGHT_FUNCTION(CalcRecruitHoldoutFilterFlag);
    REGISTER_LIGHT_FUNCTION(CalcRecruitDynamicHourClaim);
    REGISTER_LIGHT_FUNCTION(CalcRecruitExposeCountFlag);
    REGISTER_LIGHT_FUNCTION(CalcRecruitKABoostWeight);
    REGISTER_LIGHT_FUNCTION(CalcRecruitPropensityWeight);
    REGISTER_LIGHT_FUNCTION(CalcRecruitMatchDeliverInfoMatch);
    REGISTER_LIGHT_FUNCTION(CalcRecruitLocationMatchFlag);
    REGISTER_LIGHT_FUNCTION(CalcRecruitMultiLocationMatchFlag);
    REGISTER_LIGHT_FUNCTION(CalcRecruitBidScore);
    REGISTER_LIGHT_FUNCTION(FilterGameRandom);
    REGISTER_LIGHT_FUNCTION(CalcUAOverlapTimeScore);
    REGISTER_LIGHT_FUNCTION(FilterAuthorTailList);
    REGISTER_LIGHT_FUNCTION(FilterAuthorWhiteList);
    REGISTER_LIGHT_FUNCTION(OverrideItemOffFilterList);
    REGISTER_LIGHT_FUNCTION(CalcCondSkipNegFilterFlag);
    REGISTER_LIGHT_FUNCTION(CalcNewNegSysFilterFlag);
    REGISTER_LIGHT_FUNCTION(CalcEngagementScore);
    REGISTER_LIGHT_FUNCTION(CertainReasonBoostMc);
    REGISTER_LIGHT_FUNCTION(FilterUnfollow);
    REGISTER_LIGHT_FUNCTION(GenerateItemProvinceKey);
    REGISTER_LIGHT_FUNCTION(CalcRealFilterFlag);
    REGISTER_LIGHT_FUNCTION(CalcExploreForceEmpty);
    REGISTER_LIGHT_FUNCTION(CalcGradeDistributeMatchForTemp);
    REGISTER_LIGHT_FUNCTION(CalcMcRevenueGiftValue);
    REGISTER_LIGHT_FUNCTION(CalcRecheableLiveList);
    REGISTER_LIGHT_FUNCTION(CalcRecheableLiveListNorm);
    REGISTER_LIGHT_FUNCTION(RemoveItemOffFilter);
    REGISTER_LIGHT_FUNCTION(CalcFollowLeafCoolingFilter);
    REGISTER_LIGHT_FUNCTION(CalcMultiWhiteListFilter);
    REGISTER_LIGHT_FUNCTION(CalcMcBPlusScoreBoost);
    REGISTER_LIGHT_FUNCTION(CalcFrBPlusScoreBoost);
    REGISTER_LIGHT_FUNCTION(RevenueUAItemOffFilterList);
    REGISTER_LIGHT_FUNCTION(ResearchRedirectitemOffBrowsetFilter);
    REGISTER_LIGHT_FUNCTION(CalcCStartAuthorFlag);
    REGISTER_LIGHT_FUNCTION(FilterTailNumber);
    REGISTER_LIGHT_FUNCTION(CalcRepeatedShowFilter);
    REGISTER_LIGHT_FUNCTION(CalcExploreRevenueAuthorFilter);
    REGISTER_LIGHT_FUNCTION(CloseLivingRedisKeyConcat);
    REGISTER_LIGHT_FUNCTION(CalcGreenPassLivesInfo);
    REGISTER_LIGHT_FUNCTION(GetHouseLiveFilterTag);
    REGISTER_LIGHT_FUNCTION(CalcHousePeopleFlag);
    REGISTER_LIGHT_FUNCTION(CalcLTReportedTag);
    REGISTER_LIGHT_FUNCTION(CalcHouseTargetCityUserFlag);
    REGISTER_LIGHT_FUNCTION(CalcHouseHighQualityPeopleFlag);
    REGISTER_LIGHT_FUNCTION(CalcHouseUserClusterFilterFlag);
    REGISTER_LIGHT_FUNCTION(CalcMcHouseReasonBoostScore);
    REGISTER_LIGHT_FUNCTION(CalcMcHouseReasonListBoostScore);
    REGISTER_LIGHT_FUNCTION(CalcMcHouseResetModelScore);
    REGISTER_LIGHT_FUNCTION(CalcMcHouseModelScoreFilterFlag);
    REGISTER_LIGHT_FUNCTION(CalcHouseAuthorBoostCoeff);
    REGISTER_LIGHT_FUNCTION(CalcHouseAuthorBoostCoeffV2);
    REGISTER_LIGHT_FUNCTION(CalcHouseCityDegradeCoeff);
    REGISTER_LIGHT_FUNCTION(CalcHouseMcAdditionFlag);
    REGISTER_LIGHT_FUNCTION(CalUniformHouseRankingScore);
    REGISTER_LIGHT_FUNCTION(ResetHouseRankingScore);
    REGISTER_LIGHT_FUNCTION(ResetHouseLiveTag);
    REGISTER_LIGHT_FUNCTION(CalcHouseBidIndex);
    REGISTER_LIGHT_FUNCTION(CalcHouseMcAdditionScore);
    REGISTER_LIGHT_FUNCTION(HouseUserCityMatch);
    REGISTER_LIGHT_FUNCTION(CalcIndustryNormalizedRankScore);
    REGISTER_LIGHT_FUNCTION(HouseMcOutHighQualityAuthorBoost);
    REGISTER_LIGHT_FUNCTION(HouseMcInHighQualityAuthorBoost);
    REGISTER_LIGHT_FUNCTION(CalcLuckGrassAuthorFilter);
    REGISTER_LIGHT_FUNCTION(CalcSkipFilterTag);
    REGISTER_LIGHT_FUNCTION(CalcExploreSellerFilter);
    REGISTER_LIGHT_FUNCTION(CalcMainAccountFavoriteFilter);
    REGISTER_LIGHT_FUNCTION(CalcSellerAuthorFilterForHighPayingUser);
    REGISTER_LIGHT_FUNCTION(CalcProvCode);
    REGISTER_LIGHT_FUNCTION(CalcSameAreaShouldBoost);
    REGISTER_LIGHT_FUNCTION(CalcRedisAidFilter);
    REGISTER_LIGHT_FUNCTION(CalcMatchmakerBoostFirstCoeff);
    REGISTER_LIGHT_FUNCTION(CalcMatchmakerBoostSecondCoeff);
    REGISTER_LIGHT_FUNCTION(CalcAreaFilter);
    REGISTER_LIGHT_FUNCTION(LocalLifeCascadingInferQuotaByReason);
    REGISTER_LIGHT_FUNCTION(GetLocalLiveFilterCoreRevenueTag);
    REGISTER_LIGHT_FUNCTION(ItemAttrStringToDouble);
    REGISTER_LIGHT_FUNCTION(CalcRecruitMcScore);
    REGISTER_LIGHT_FUNCTION(CalcMcRecruitScore);
    REGISTER_LIGHT_FUNCTION(GetLocalLiveMultipyScore);
    REGISTER_LIGHT_FUNCTION(GetLocalLiveMultiplyScoreV2);
    REGISTER_LIGHT_FUNCTION(ItemAttrScoreCompare);
    REGISTER_LIGHT_FUNCTION(LocalLifeGeoHashSendItemAttr);
    REGISTER_LIGHT_FUNCTION(ItemAttrCompareItemAttr);
    REGISTER_LIGHT_FUNCTION(LocalLifeUserAgeSegmentFilterBoost);
    REGISTER_LIGHT_FUNCTION(LocalLifeRealShowFilterDeboost);
    REGISTER_LIGHT_FUNCTION(LocalLifeMixRealShowFilterDeboost);
    REGISTER_LIGHT_FUNCTION(GetLocalLifeUserLevelRankFilterParams);
    REGISTER_LIGHT_FUNCTION(GetNewLocalLifeUserLevelRankFilterParams);
    REGISTER_LIGHT_FUNCTION(GetNewLocalLifeUserLevelRankFilterParamsV2);
    REGISTER_LIGHT_FUNCTION(GetNewLocalLifeUserLevelRankFilterParamsV3);
    REGISTER_LIGHT_FUNCTION(GetLocalLifeUserLevelRankScoreParams);
    REGISTER_LIGHT_FUNCTION(RankScorePostFilter);
    REGISTER_LIGHT_FUNCTION(RankScorePostFilterWithCommonBoost);
    REGISTER_LIGHT_FUNCTION(CalcLocalAreaSkipBlackFilterFlag);
    REGISTER_LIGHT_FUNCTION(CalcHouseExploreScore);
    REGISTER_LIGHT_FUNCTION(CalcRecruitExploreScore);
    REGISTER_LIGHT_FUNCTION(CalcVerticalNormScore);
    REGISTER_LIGHT_FUNCTION(CalcVariantMcPreFilterFlag);
    REGISTER_LIGHT_FUNCTION(BlackAuthorRemoveItemOffFilter);
    REGISTER_LIGHT_FUNCTION(CalcEauthorFilter);
    REGISTER_LIGHT_FUNCTION(ItemAttrCityHash);
    REGISTER_LIGHT_FUNCTION(McMoiveTagDeBoostMc);
    REGISTER_LIGHT_FUNCTION(McSellerTagDeBoostMc);
    REGISTER_LIGHT_FUNCTION(CalcIsClickAuthor);
    REGISTER_LIGHT_FUNCTION(GenRecruitUserIntentionInfo);
    REGISTER_LIGHT_FUNCTION(IsRecruitAuthorHitExp);
    REGISTER_LIGHT_FUNCTION(CalcRecruitColdstartAuthorBoost);
    REGISTER_LIGHT_FUNCTION(CalcNrMmuTagRetrFlag);
    REGISTER_LIGHT_FUNCTION(CalcNrRedirectAuthorList);
    REGISTER_LIGHT_FUNCTION(CalcRecruitHouseUAFlag);
    REGISTER_LIGHT_FUNCTION(CalcGameAuthorQualityScore);
    REGISTER_LIGHT_FUNCTION(CalcUAFollowDay);
    REGISTER_LIGHT_FUNCTION(GetGPSQueryList);
    REGISTER_LIGHT_FUNCTION(GetHighValueFlag);
    REGISTER_LIGHT_FUNCTION(CalcLLMRedisKey);
    REGISTER_LIGHT_FUNCTION(CalcRecruitSameCityBoost);
    REGISTER_LIGHT_FUNCTION(CalcRecruitExptagBoost);
    REGISTER_LIGHT_FUNCTION(CalcRecruitRealBidScore);
    REGISTER_LIGHT_FUNCTION(CalcReplaceAuthorIdAndList);
    REGISTER_LIGHT_FUNCTION(CalcLiveSearchInfo);
    REGISTER_LIGHT_FUNCTION(CalcLiveSearch7dInfo);
    REGISTER_LIGHT_FUNCTION(CalcNoCptList);
  }

  static bool CalcAreaFilter(const CommonRecoLightFunctionContext &context,
                             RecoResultConstIter begin,
                             RecoResultConstIter end) {
    double filter_gift_user_prob = context.GetDoubleCommonAttr("filter_gift_user_prob").value_or(-1.0);
    double filter_high_gift_user_prob = context.GetDoubleCommonAttr(
                                        "filter_high_gift_user_prob").value_or(-1.0);
    auto is_gift_user = context.GetIntCommonAttr("is_gift_user").value_or(0);
    auto is_high_gift_user = context.GetIntCommonAttr("is_high_gift_user").value_or(0);
    auto is_region_user = context.GetIntCommonAttr("is_region_user").value_or(0);
    auto filter_tag_list = context.GetIntListCommonAttr("filter_tag_list");

    auto is_shop_live = context.GetIntItemAttr("is_shop_live");
    auto mmu_tag_v2 = context.GetIntListItemAttr("mmu_tag_info.tag_list_lv2");

    auto is_area_filtered = context.SetIntItemAttr("is_area_filtered");
    auto perf_area_filter = context.SetIntItemAttr("perf_area_filter");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto is_area_filtered_value = 0;
      auto perf_area_filter_value = 0;
      auto is_shop_live_value = is_shop_live(result).value_or(0);
      bool is_tag_filter = contain_tag(mmu_tag_v2(result), filter_tag_list);

      if (is_region_user > 0) {
        if (is_shop_live_value > 0) {
          std::srand(static_cast<unsigned int>(std::time(nullptr)));
          double randf = static_cast<double>(std::rand()) / RAND_MAX;
          if (is_high_gift_user > 0 && filter_high_gift_user_prob >0.0) {
            perf_area_filter_value = 10;
            if (randf > filter_high_gift_user_prob) {
              is_area_filtered_value = 1;
              perf_area_filter_value = 11;
            }
          } else if (is_gift_user > 0 && filter_gift_user_prob >0.0) {
            perf_area_filter_value = 20;
            if (randf > filter_high_gift_user_prob) {
              is_area_filtered_value = 1;
              perf_area_filter_value = 21;
            }
          }
        }
        if (is_tag_filter) {
          std::srand(static_cast<unsigned int>(std::time(nullptr)));
          double randf = static_cast<double>(std::rand()) / RAND_MAX;
          if (is_high_gift_user > 0 && filter_high_gift_user_prob >0.0) {
            perf_area_filter_value = 30;
            if (randf > filter_high_gift_user_prob) {
              is_area_filtered_value = 1;
              perf_area_filter_value = 31;
            }
          } else if (is_gift_user > 0 && filter_gift_user_prob >0.0) {
            perf_area_filter_value = 40;
            if (randf > filter_high_gift_user_prob) {
              is_area_filtered_value = 1;
              perf_area_filter_value = 41;
            }
          }
        }
      }
      is_area_filtered(result, is_area_filtered_value);
      perf_area_filter(result, perf_area_filter_value);
    });
    return true;
  }

  static bool CalcEauthorFilter(const CommonRecoLightFunctionContext &context,
                             RecoResultConstIter begin,
                             RecoResultConstIter end) {
    double filter_gift_user_prob = context.GetDoubleCommonAttr("filter_gift_user_prob").value_or(0.0);
    double filter_high_gift_user_prob = context.GetDoubleCommonAttr(
                                        "filter_high_gift_user_prob").value_or(0.0);
    auto is_gift_user = context.GetIntCommonAttr("is_gift_user").value_or(0);
    auto is_high_gift_user = context.GetIntCommonAttr("is_high_gift_user").value_or(0);

    auto is_shop_live = context.GetIntItemAttr("is_shop_live");

    auto is_filtered_e_author = context.SetIntItemAttr("is_filtered_e_author");
    auto perf_is_shop_live = context.SetIntItemAttr("perf_is_shop_live");
    std::srand(std::time(nullptr));

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto is_filtered_e_author_value = 0;
      auto perf_is_shop_live_value = 0;
      auto is_shop_live_value = is_shop_live(result).value_or(0);
      if (is_shop_live_value > 0) {
        double random_number = static_cast<double>(std::rand()) / RAND_MAX;
        if (is_high_gift_user > 0 && filter_high_gift_user_prob > 0) {
          perf_is_shop_live_value = 1;
          is_filtered_e_author_value = (random_number < filter_high_gift_user_prob) ? 1 : 0;
        } else if (is_gift_user > 0 && filter_gift_user_prob > 0) {
          perf_is_shop_live_value = 2;
          is_filtered_e_author_value = (random_number < filter_gift_user_prob) ? 2 : 0;
        }
      }

      is_filtered_e_author(result, is_filtered_e_author_value);
      perf_is_shop_live(result, perf_is_shop_live_value);
    });
    return true;
  }

  static bool CalcSameAreaShouldBoost(const CommonRecoLightFunctionContext &context,
                             RecoResultConstIter begin,
                             RecoResultConstIter end) {
    const std::unordered_map<std::string, std::vector<std::string>> extendSameAreaStrategy = {
        {"023", {"023", "028", "0851", "0731"}},
        {"028", {"023", "028", "0851", "0731", "0871"}},
        {"025", {"021", "025", "0571"}},
        {"021", {"021", "025", "0571"}},
        {"0571", {"021", "025", "0571"}},
        {"010", {"010", "022", "0311"}},
        {"022", {"010", "022", "0311"}},
        {"0311", {"010", "022", "0311", "0371", "0531", "0351"}},
        {"0351", {"0351", "0311", "029", "0371"}},
        {"024", {"024", "0431", "0470"}},
        {"0431", {"0431", "0470", "024", "0451"}},
        {"0551", {"0551", "025", "0571", "0791", "027", "0371", "0531", "021"}},
        {"0591", {"0591", "0571", "0791", "020"}},
        {"0791", {"0791", "0591", "0571", "0551", "027", "0731", "020"}},
        {"0531", {"010", "022", "0311", "0371", "0551", "025"}},
        {"0371", {"0371", "0531", "0311", "0351", "029", "027", "0551", "025"}},
        {"027", {"027", "023", "0731", "0791", "0551", "0371", "029"}},
        {"0731", {"0731", "023", "0851", "0771", "020", "0791", "027"}},
        {"020", {"020", "0771", "0731", "0791", "0591", "0898"}},
        {"0771", {"0771", "020", "0731", "0851", "0871"}},
        {"0898", {"0771", "0898", "020"}},
        {"0851", {"0851", "023", "028", "0871", "0731", "0771"}},
        {"0871", {"0871", "0851", "0771", "028", "0891"}},
        {"0891", {"0891", "028", "0871", "0971", "0991"}},
        {"029", {"029", "028", "023", "027", "0371", "0351", "0951", "0931"}},
        {"0931", {"0931", "028", "029", "0971", "0951", "0991", "0470"}},
        {"0971", {"0971", "0991", "0891", "028", "0931"}},
        {"0951", {"0951", "0931", "029", "0470"}},
        {"0991", {"0991", "0891", "0971", "0931"}},
        {"0470", {"0931", "0951", "029", "0351", "0311", "024", "0431", "0451"}}
    };

    double same_area_enable = context.GetDoubleCommonAttr("same_area_enable").value_or(0.0);
    double same_area_extend = context.GetDoubleCommonAttr("same_area_extend").value_or(0.0);
    double same_area_pass_tags_all = context.GetDoubleCommonAttr("same_area_pass_tags_all").value_or(0.0);
    auto is_prov_pass = context.GetIntCommonAttr("is_prov_pass").value_or(0);
    double same_area_arppu_threshold = context.GetDoubleCommonAttr("same_area_arppu_threshold").value_or(0.0);
    std::string user_prov_code = std::string(
      context.GetStringCommonAttr("user_prov_code").value_or("unknown"));

    auto pass_mmu_tags_list = context.GetIntListCommonAttr("pass_mmu_tags_list");

    auto author_prov_code = context.GetStringItemAttr("author_prov_code");
    auto is_same_prov = context.GetIntItemAttr("is_same_prov");
    auto author_arppu_14d = context.GetDoubleItemAttr("author_arppu_14d");
    auto mmu_tag_v3 = context.GetIntListItemAttr("mmu_tag_info.tag_list_lv3");
    auto same_area_should_boost = context.SetIntItemAttr("same_area_should_boost");
    auto mmu_tag_should_boost = context.SetIntItemAttr("mmu_tag_should_boost");
    auto is_tag_pass_flag = context.SetIntItemAttr("is_tag_pass_flag");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto same_area_should_boost_value = 0;
      auto mmu_tag_should_boost_value = 0;
      auto is_tag_pass_flag_value = 0;
      std::string author_prov_code_value = std::string(
                author_prov_code(result).value_or("unknown"));
      auto is_same_prov_value = is_same_prov(result).value_or(0);
      auto author_arppu_14d_value = author_arppu_14d(result).value_or(0.0);
      bool is_tag_pass = contain_tag(mmu_tag_v3(result), pass_mmu_tags_list);
      std::vector<std::string> extendCodeList;
      if (same_area_enable > 0 && extendSameAreaStrategy.count(user_prov_code) > 0 && is_prov_pass > 0) {
        if (same_area_extend > 0) {
          extendCodeList = extendSameAreaStrategy.at(user_prov_code);
          if (std::find(extendCodeList.begin(), extendCodeList.end(),
                        author_prov_code_value) != extendCodeList.end()
                        && author_arppu_14d_value >= same_area_arppu_threshold) {
            same_area_should_boost_value = 1;
          }
          if (std::find(extendCodeList.begin(), extendCodeList.end(),
                        author_prov_code_value) != extendCodeList.end()
                        && is_tag_pass && same_area_pass_tags_all > 0) {
            same_area_should_boost_value = 1;
            mmu_tag_should_boost_value = 1;
          }
        } else {
          if (is_same_prov_value > 0 && author_arppu_14d_value >= same_area_arppu_threshold) {
            same_area_should_boost_value = 1;
          }
          if (is_same_prov_value > 0 && is_tag_pass && same_area_pass_tags_all > 0) {
            same_area_should_boost_value = 1;
            mmu_tag_should_boost_value = 1;
          }
        }
      }

      if (same_area_pass_tags_all > 1.0 && is_tag_pass) {
        same_area_should_boost_value = 1;
        mmu_tag_should_boost_value = 1;
      }

      same_area_should_boost(result, same_area_should_boost_value);
      mmu_tag_should_boost(result, mmu_tag_should_boost_value);
      if (is_tag_pass) {
        is_tag_pass_flag_value = 1;
      }
      is_tag_pass_flag(result, is_tag_pass_flag_value);
    });
    return true;
  }

  static bool contain_tag(const absl::optional<absl::Span<const int64>>& span1,
                      const absl::optional<absl::Span<const int64>>& span2) {
    if (!span1.has_value() || !span2.has_value()) {
      return false;
    }
    auto vec1 = std::vector<int64>(span1.value().begin(), span1.value().end());
    auto vec2 = std::vector<int64>(span2.value().begin(), span2.value().end());
    return std::any_of(vec1.begin(), vec1.end(), [&](int64 value) {
      return std::find(vec2.begin(), vec2.end(), value) != vec2.end();
    });
  }

  static bool CheckIsCoverRevenueLive(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto is_cover_revenue_live = context.GetIntCommonAttr("is_cover_revenue_live").value_or(0);
    auto cover_author_id = context.GetIntCommonAttr("cover_author_id").value_or(0);
    auto author_id_list_from_1569 = context.GetIntListCommonAttr("author_id_list_from_1569");
    auto enable_cover_revenue_live = context.GetIntCommonAttr("enable_cover_revenue_live");

    if (enable_cover_revenue_live == 1 && author_id_list_from_1569.has_value()) {
      for (auto authorId : *author_id_list_from_1569) {
        if (authorId == cover_author_id) {
          is_cover_revenue_live = 1;
          break;
        }
      }
    }
    context.SetIntCommonAttr("is_cover_revenue_live", is_cover_revenue_live);
    return true;
  }

  static bool CheckIsRevenue(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                             RecoResultConstIter end) {
    auto revenue_amount_60d = context.GetDoubleItemAttr("revenue_amount_60d");
    auto origin_is_revenue_live = context.GetIntItemAttr("is_revenue_live");
    auto reason_list = context.GetIntListItemAttr("reason_list");

    auto is_revenue_live = context.SetIntItemAttr("is_revenue_live");
    auto is_revenue_dis_live = context.SetIntItemAttr("is_revenue_dis_live");
    auto is_revenue_dis_follow_live = context.SetIntItemAttr("is_revenue_dis_follow_live");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto is_revenue_live_value = origin_is_revenue_live(result).value_or(0);
      auto is_revenue_dis_live_value = 0;
      auto is_revenue_dis_follow_live_value = 0;
      auto revenue_amount_60d_value = revenue_amount_60d(result).value_or(0.0);
      auto reason_list_value = reason_list(result);
      if (revenue_amount_60d_value > 0.0) {
        is_revenue_live_value = 1;
      }

      if (reason_list_value.has_value()) {
        for (auto reason : reason_list_value.value()) {
          if (reason == 1319) {
            is_revenue_dis_live_value = 1;
          }
          if (reason == 1320) {
            is_revenue_dis_follow_live_value = 1;
          }
        }
      }

      is_revenue_live(result, is_revenue_live_value);
      is_revenue_dis_live(result, is_revenue_dis_live_value);
      is_revenue_dis_follow_live(result, is_revenue_dis_follow_live_value);
    });
    return true;
  }

  static bool CalcProvCode(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                             RecoResultConstIter end) {
    const std::unordered_map<std::string, std::string> areaCodes = {
        {"北京", "010"},
        {"上海", "021"},
        {"天津", "022"},
        {"重庆", "023"},
        {"河北", "0311"},
        {"山西", "0351"},
        {"辽宁", "024"},
        {"吉林", "0431"},
        {"黑龙江", "0451"},
        {"江苏", "025"},
        {"浙江", "0571"},
        {"安徽", "0551"},
        {"福建", "0591"},
        {"江西", "0791"},
        {"山东", "0531"},
        {"河南", "0371"},
        {"湖北", "027"},
        {"湖南", "0731"},
        {"广东", "020"},
        {"广西", "0771"},
        {"海南", "0898"},
        {"四川", "028"},
        {"贵州", "0851"},
        {"云南", "0871"},
        {"西藏", "0891"},
        {"陕西", "029"},
        {"甘肃", "0931"},
        {"青海", "0971"},
        {"宁夏", "0951"},
        {"新疆", "0991"},
        {"内蒙古", "0470"}
    };
    std::string user_realtime_province = std::string(
      context.GetStringCommonAttr("user_realtime_province").value_or("unknown"));
    std::string user_prov_code_value("unknown");
    if (areaCodes.count(user_realtime_province) > 0) {
        user_prov_code_value = areaCodes.at(user_realtime_province);
    }
    context.SetStringCommonAttr("user_prov_code", user_prov_code_value);

    auto lLiveLocationProvince = context.GetStringItemAttr("lLiveLocationProvince");
    auto author_prov_code = context.SetStringItemAttr("author_prov_code");
    auto is_same_prov = context.SetIntItemAttr("is_same_prov");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      std::string author_prov_code_value("unknown");
      auto is_same_prov_value = 0;

      std::string lLiveLocationProvince_value = std::string(
        lLiveLocationProvince(result).value_or("unknown"));
      if (areaCodes.count(lLiveLocationProvince_value) > 0) {
        author_prov_code_value = areaCodes.at(lLiveLocationProvince_value);
        if (author_prov_code_value == user_prov_code_value) {
          is_same_prov_value = 1;
        }
      }
      author_prov_code(result, author_prov_code_value);
      is_same_prov(result, is_same_prov_value);
    });
    return true;
  }

  static bool CalcRevenueEmpScore(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto revenue_amount_1d = context.GetDoubleItemAttr("revenue_amount_1d");
    auto revenue_amount_3d = context.GetDoubleItemAttr("revenue_amount_3d");
    auto revenue_amount_7d = context.GetDoubleItemAttr("revenue_amount_7d");
    auto revenue_amount_14d = context.GetDoubleItemAttr("revenue_amount_14d");
    auto revenue_amount_30d = context.GetDoubleItemAttr("revenue_amount_30d");
    auto revenue_amount_60d = context.GetDoubleItemAttr("revenue_amount_60d");

    auto target_revenue_emp_score = context.SetDoubleItemAttr("target_revenue_emp_score");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto target_revenue_emp_score_value = 0.0;
      auto revenue_amount_cliff = 1000.0;
      auto getAttrValue = [&result, revenue_amount_cliff](
                              std::function<absl::optional<double>(const CommonRecoResult &)> attr) {
        auto attr_value = attr(result).value_or(0.0);
        return attr_value > revenue_amount_cliff ? revenue_amount_cliff : attr_value;
      };

      auto revenue_amount_1d_value = getAttrValue(revenue_amount_1d);
      auto revenue_amount_3d_value = getAttrValue(revenue_amount_3d);
      auto revenue_amount_7d_value = getAttrValue(revenue_amount_7d);
      auto revenue_amount_14d_value = getAttrValue(revenue_amount_14d);
      auto revenue_amount_30d_value = getAttrValue(revenue_amount_30d);
      auto revenue_amount_60d_value = getAttrValue(revenue_amount_60d);

      target_revenue_emp_score_value = 0.8 * revenue_amount_1d_value + 0.7 * revenue_amount_3d_value +
                                       0.5 * revenue_amount_7d_value * 0.5 +
                                       100000 * revenue_amount_14d_value + 0.3 * revenue_amount_30d_value +
                                       0.2 * revenue_amount_60d_value;

      target_revenue_emp_score(result, target_revenue_emp_score_value);
    });

    return true;
  }

  static bool CalcRevenueScoreRank(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    auto revenue_emp_score_max_count = context.GetIntCommonAttr("revenue_emp_score_max_count").value_or(0);
    auto revenue_emp_score_threshold =
        context.GetDoubleCommonAttr("revenue_emp_score_threshold").value_or(0.0);

    auto is_shop_live = context.GetIntItemAttr("is_shop_live");
    auto target_revenue_emp_score = context.GetDoubleItemAttr("target_revenue_emp_score");

    auto target_revenue_score_rank = context.SetDoubleItemAttr("target_revenue_score_rank");

    int idx = 0;
    std::for_each(begin, end, [=, &idx](const CommonRecoResult &result) {
      auto target_revenue_score_rank_value = 0.0;

      auto is_shop_live_value = is_shop_live(result).value_or(0);
      auto target_revenue_emp_score_value = target_revenue_emp_score(result).value_or(0.0);

      if (idx < revenue_emp_score_max_count && target_revenue_emp_score_value > revenue_emp_score_threshold &&
          is_shop_live_value == 0) {
        target_revenue_score_rank_value = revenue_emp_score_max_count - idx;
      }
      target_revenue_score_rank(result, target_revenue_score_rank_value);
      idx++;
    });
    return true;
  }

  static bool GenerateMatchFlags(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto ab_mr_revenue_score_flag = context.GetStringCommonAttr("ab_mr_revenue_score_flag").value_or("");
    auto ab_mr_revenue_dis_score_flag =
        context.GetStringCommonAttr("ab_mr_revenue_dis_score_flag").value_or("");
    auto ab_mr_revenue_dis_follow_score_flag =
        context.GetStringCommonAttr("ab_mr_revenue_dis_follow_score_flag").value_or("");

    context.SetStringCommonAttr("match_ab_mr_revenue_score_flag",
                                "match" + std::string(ab_mr_revenue_score_flag));

    context.SetStringCommonAttr("match_ab_mr_revenue_dis_score_flag",
                                "match" + std::string(ab_mr_revenue_dis_score_flag));

    context.SetStringCommonAttr("match_ab_mr_revenue_dis_follow_score_flag",
                                "match" + std::string(ab_mr_revenue_dis_follow_score_flag));

    return true;
  }

  static bool GenerateUaOverlapRedisKey(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    auto u_uid = context.GetIntCommonAttr("u_uid").value_or(0);
    auto author_id = context.GetIntItemAttr("author_id");

    auto ua_overlap_redis_key = context.SetStringItemAttr("ua_overlap_redis_key");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto author_id_value = author_id(result).value_or(0);
      std::string redis_key = "r_o_t_" + std::to_string(u_uid) + "_" + std::to_string(author_id_value);
      ua_overlap_redis_key(result, redis_key);
    });
    return true;
  }

  static bool CalcUAOverlapTimeScore(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    auto user_live_hours = context.GetIntListCommonAttr("reco.live_user_feature.user_live_duration_hour")
                               .value_or(absl::Span<const int64>());
    auto author_live_hours_getter = context.GetIntListItemAttr("lPlayTimeDurationList");
    auto ua_overlap_score_setter = context.SetDoubleItemAttr("ua_overlap_score");
    auto ua_overlap_score_norm_setter = context.SetDoubleItemAttr("ua_overlap_score_norm");

    double user_live_hour_sum = 0.0;
    if (user_live_hours.size() >= 24) {
      for (int i = 0; i < 24; ++i) {
        user_live_hour_sum += user_live_hours[i] * user_live_hours[i];
      }
    }
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto author_live_hours = author_live_hours_getter(result).value_or(absl::Span<const int64>());
      if (user_live_hours.size() >= 24 && author_live_hours.size() >= 24) {
        double score = 0.0;
        double author_score = 0.0;
        for (int i = 0; i < 24; ++i) {
          author_score += author_live_hours[i] * author_live_hours[i];
          score += author_live_hours[i] * user_live_hours[i];
        }
        double ua_overlap_score = score / std::sqrt(std::max(user_live_hour_sum, 1.0));
        ua_overlap_score_setter(result, ua_overlap_score);
        ua_overlap_score_norm_setter(result, ua_overlap_score / std::sqrt(std::max(author_score, 1.0)));
      }
    });

    return true;
  }

  static bool CalcOverlapScore(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                               RecoResultConstIter end) {
    auto ua_overlap_time = context.GetStringItemAttr("ua_overlap_time");
    auto revenue_amount_60d = context.GetIntItemAttr("revenue_amount_60d");
    auto target_revenue_emp_score = context.GetIntItemAttr("target_revenue_emp_score");

    auto opportunity_cost_60d_queue_score = context.SetDoubleItemAttr("opportunity_cost_60d_queue_score");
    auto target_revenue_emp_score_copy = context.SetDoubleItemAttr("target_revenue_emp_score_copy");
    auto ua_overlap_time_recip = context.SetDoubleItemAttr("ua_overlap_time_recip");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto revenue_amount_60d_value = revenue_amount_60d(result).value_or(0.0);
      auto target_revenue_emp_score_value = target_revenue_emp_score(result).value_or(0.0);
      auto ua_overlap_time_string_value = ua_overlap_time(result).value_or("0");
      auto ua_overlap_time_value = 0.0;
      if (!absl::SimpleAtod(ua_overlap_time_string_value, &ua_overlap_time_value)) {
        ua_overlap_time_value = 0.0;
      }

      double opportunity_cost_60d_queue_score_value = 0.0;
      double ua_overlap_time_recip_value = 1 / (ua_overlap_time_value + 1);

      if (ua_overlap_time_value == 0) {
        ua_overlap_time_recip_value = 0;
      } else {
        opportunity_cost_60d_queue_score_value = revenue_amount_60d_value / ua_overlap_time_recip_value;
      }
      opportunity_cost_60d_queue_score(result, opportunity_cost_60d_queue_score_value);
      target_revenue_emp_score_copy(result, target_revenue_emp_score_value);
      ua_overlap_time_recip(result, ua_overlap_time_recip_value);
    });

    return true;
  }

  // 将形如 grade_delta:1.6;prob_min:0.2;score_scale:1.0; 的字符串解析为 map
  static absl::flat_hash_map<std::string, double> ParseWeightParam(absl::string_view param_string) {
    absl::flat_hash_map<std::string, double> param_map;
    std::vector<absl::string_view> params = absl::StrSplit(param_string, ";", absl::SkipWhitespace());
    for (auto token : params) {
      std::vector<absl::string_view> param = absl::StrSplit(token, ":", absl::SkipWhitespace());
      if (param.size() != 2) continue;
      auto key = param[0];
      auto value = 0.0;
      if (absl::SimpleAtod(param[1], &value)) {
        param_map.emplace(key, value);
      }
    }
    return param_map;
  }

  static double GetOrDefault(absl::flat_hash_map<std::string, double> map, std::string key,
                             double defaultValue) {
    auto item = map.find(key);
    if (item == map.end()) {
      return defaultValue;
    }
    return item->second;
  }

  static bool CalcRevenueAuthorScore(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    auto author_revenue_score_param = context.GetStringCommonAttr("author_revenue_score_param").value_or("");
    auto author_revenue_score_type = context.GetIntCommonAttr("author_revenue_score_type").value_or(0);
    auto aAuthorModelV1KV = context.GetStringItemAttr("aAuthorModelV1KV");

    auto author_revenue_score_param_map = ParseWeightParam(author_revenue_score_param);

    auto author_revenue_score = context.SetDoubleItemAttr("author_revenue_score");
    std::for_each(begin, end, [=, &author_revenue_score_param_map](const CommonRecoResult &result) {
      if (author_revenue_score_type != 1 && author_revenue_score_type != 2) {
        author_revenue_score(result, 0.0);
        return;
      }
      auto item_arr = aAuthorModelV1KV(result).value_or("");
      auto item_param_map = ParseWeightParam(item_arr);

      auto mh = GetOrDefault(item_param_map, "mh", 0.0);
      if (mh > 0) {
        author_revenue_score(result, 0.0);
        return;
      }

      auto grade = GetOrDefault(item_param_map, "ga", -1.0);
      auto prob = GetOrDefault(item_param_map, "sa", -1.0);
      if (author_revenue_score_type == 2) {
        grade = GetOrDefault(item_param_map, "gs", -1.0);
        prob = GetOrDefault(item_param_map, "ss", 0.0);
      }

      auto grade_delta = GetOrDefault(author_revenue_score_param_map, "grade_delta", 1.0);
      auto prob_min = GetOrDefault(author_revenue_score_param_map, "prob_min", 0.0);
      auto score_scale = GetOrDefault(author_revenue_score_param_map, "score_scale", 0.0);

      if (grade < 1) {
        author_revenue_score(result, 0.0);
        return;
      }
      auto score = (grade - grade_delta) + (prob - prob_min) / (1.0 - prob_min) * grade_delta;
      score = score < 0 ? 0.0 : score;
      author_revenue_score(result, score * prob_min * score_scale);
    });

    return true;
  }

  static bool CalcRevenueUserScore(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    auto user_revenue_score_param = context.GetStringCommonAttr("user_revenue_score_param").value_or("");
    auto premium_type = context.GetStringCommonAttr("premiumtype").value_or("");

    auto user_revenue_score_param_map = ParseWeightParam(user_revenue_score_param);
    auto premium_type_map = ParseWeightParam(premium_type);

    auto grade = GetOrDefault(premium_type_map, "g", -1);
    auto prob = GetOrDefault(premium_type_map, "s", 0.0);

    auto grade_delta = GetOrDefault(user_revenue_score_param_map, "grade_delta", 1.0);
    auto prob_min = GetOrDefault(user_revenue_score_param_map, "prob_min", 0.0);
    auto score_scale = GetOrDefault(user_revenue_score_param_map, "score_scale", 0.0);

    if (grade < 0) {
      context.SetDoubleCommonAttr("user_revenue_score", 0.0);
      return true;
    }
    auto score = (grade - grade_delta) + (prob - prob_min) / (1.0 - prob_min) * grade_delta;
    score = score < 0 ? 0.0 : score;
    context.SetDoubleCommonAttr("user_revenue_score", score * prob_min / (1.0 - prob_min) * score_scale);
    return true;
  }

  static bool CalcFollowRevenueLive(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto revenue_amount_1d = context.GetDoubleItemAttr("revenue_amount_1d");
    auto revenue_amount_3d = context.GetDoubleItemAttr("revenue_amount_3d");
    auto revenue_amount_7d = context.GetDoubleItemAttr("revenue_amount_7d");
    auto revenue_amount_14d = context.GetDoubleItemAttr("revenue_amount_14d");
    auto revenue_amount_30d = context.GetDoubleItemAttr("revenue_amount_30d");
    auto revenue_amount_60d = context.GetDoubleItemAttr("revenue_amount_60d");

    auto is_revenue_amount_1d_live = context.SetIntItemAttr("is_revenue_amount_1d_live");
    auto is_revenue_amount_3d_live = context.SetIntItemAttr("is_revenue_amount_3d_live");
    auto is_revenue_amount_7d_live = context.SetIntItemAttr("is_revenue_amount_7d_live");
    auto is_revenue_amount_14d_live = context.SetIntItemAttr("is_revenue_amount_14d_live");
    auto is_revenue_amount_30d_live = context.SetIntItemAttr("is_revenue_amount_30d_live");
    auto is_revenue_amount_60d_live = context.SetIntItemAttr("is_revenue_amount_60d_live");
    auto is_revenue_amount_live = context.SetIntItemAttr("is_revenue_amount_live");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto revenue_amount_1d_value = revenue_amount_1d(result).value_or(0.0);
      auto revenue_amount_3d_value = revenue_amount_3d(result).value_or(0.0);
      auto revenue_amount_7d_value = revenue_amount_7d(result).value_or(0.0);
      auto revenue_amount_14d_value = revenue_amount_14d(result).value_or(0.0);
      auto revenue_amount_30d_value = revenue_amount_30d(result).value_or(0.0);
      auto revenue_amount_60d_value = revenue_amount_60d(result).value_or(0.0);

      is_revenue_amount_1d_live(result, revenue_amount_1d_value > 0.0 ? 1 : 0);
      is_revenue_amount_3d_live(result, revenue_amount_3d_value > 0.0 ? 1 : 0);
      is_revenue_amount_7d_live(result, revenue_amount_7d_value > 0.0 ? 1 : 0);
      is_revenue_amount_14d_live(result, revenue_amount_14d_value > 0.0 ? 1 : 0);
      is_revenue_amount_30d_live(result, revenue_amount_30d_value > 0.0 ? 1 : 0);
      is_revenue_amount_60d_live(result, revenue_amount_60d_value > 0.0 ? 1 : 0);
      bool is_revenue_amount_live_value = revenue_amount_1d_value > 0.0 || revenue_amount_3d_value > 0.0 ||
                                          revenue_amount_7d_value > 0.0 || revenue_amount_14d_value > 0.0 ||
                                          revenue_amount_30d_value > 0.0 || revenue_amount_60d_value > 0.0;
      is_revenue_amount_live(result, is_revenue_amount_live_value ? 1 : 0);
    });

    return true;
  }

  static bool CalcRecruitPeopleFlag(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto recruit_people_type = context.GetIntCommonAttr("recruit_people_type").value_or(0);
    auto is_recruit_people_v1 =
        context.GetIntCommonAttr("reco.live_user_feature.is_recruit_people_v1").value_or(0);
    auto is_recruit_people_v2 =
        context.GetIntCommonAttr("reco.live_user_feature.is_recruit_people_v2").value_or(0);
    auto is_recruit_people_v3 =
        context.GetIntCommonAttr("reco.live_user_feature.is_recruit_people_v3").value_or(0);
    auto live_user_common_category_type =
        context.GetStringCommonAttr("reco.live_user_feature.live_user_common_category_type").value_or("0");
    auto recruit_offline_user_info = context.GetStringCommonAttr("recruit_offline_user_info").value_or("0");
    auto enable_recruit_user_new_people_check_type =
        context.GetIntCommonAttr("enable_recruit_user_new_people_check_type").value_or(0);
    if (enable_recruit_user_new_people_check_type == 1) {
      live_user_common_category_type = context.GetStringCommonAttr("recruit_offline_user_info").value_or("0");
    }
    auto recruit_realtime_action_type =
        context.GetStringCommonAttr("recruit_realtime_action_type").value_or("0");
    auto enable_recruit_close_revenue_user =
        context.GetIntCommonAttr("enable_recruit_close_revenue_user").value_or(0);
    auto recruit_close_renenue_user_weight =
        context.GetDoubleCommonAttr("recruit_close_renenue_user_weight").value_or(0.6);
    auto user_revenue_score = context.GetDoubleCommonAttr("user_revenue_score").value_or(0.0);
    auto enable_recruit_close_high_gift_user =
        context.GetIntCommonAttr("enable_recruit_close_high_gift_user").value_or(0);
    auto enable_recruit_close_gift_user =
        context.GetIntCommonAttr("enable_recruit_close_gift_user").value_or(0);
    auto enable_recruit_close_big_r_user =
        context.GetIntCommonAttr("enable_recruit_close_big_r_user").value_or(0);
    auto is_high_gift_user = context.GetIntCommonAttr("is_high_gift_user").value_or(0);
    auto is_gift_user = context.GetIntCommonAttr("is_gift_user").value_or(0);
    auto is_big_r_user = context.GetIntCommonAttr("is_big_r_user").value_or(0);
    auto enable_recruit_filter_hate_user =
        context.GetIntCommonAttr("enable_recruit_filter_hate_user").value_or(0);
    auto enable_recruit_user_type_boost_v1 =
        context.GetIntCommonAttr("enable_recruit_user_type_boost_v1").value_or(0);
    auto enable_recruit_user_type_boost_v1_new =
        context.GetIntCommonAttr("enable_recruit_user_type_boost_v1_new").value_or(0);
    auto recruit_people_type_str = context.GetStringCommonAttr("recruit_people_type_str").value_or("0");
    auto recruit_people_type_str_new =
        context.GetStringCommonAttr("recruit_people_type_str_new").value_or("0");
    auto enable_recruit_real_action_people =
        context.GetIntCommonAttr("enable_recruit_real_action_people").value_or(0);
    auto recruit_real_action_people_type =
        context.GetStringCommonAttr("recruit_real_action_people_type").value_or("d");
    auto enable_recruit_history_deliver_people =
        context.GetIntCommonAttr("enable_recruit_history_deliver_people_new").value_or(0);
    auto enable_recruit_filter_black_uid =
        context.GetIntCommonAttr("enable_recruit_filter_black_uid").value_or(0);
    auto enable_recruit_close_not_target_user =
        context.GetIntCommonAttr("enable_recruit_close_not_target_user").value_or(0);
    auto recruit_people_check_use_rank_value =
        context.GetIntCommonAttr("recruit_people_check_use_rank_value").value_or(50000000);
    auto user_recruit_people_rank =
        context.GetIntCommonAttr("reco.live_user_feature.user_recruit_people_rank").value_or(200000000);
    auto enable_recruit_people_check_v2 =
        context.GetIntCommonAttr("enable_recruit_people_check_v2").value_or(0);
    auto enable_recruit_history_deliver_people_v2 =
        context.GetIntCommonAttr("enable_recruit_history_deliver_people_v2").value_or(0);
    auto enable_recruit_people_type_v2 =
        context.GetIntCommonAttr("enable_recruit_people_type_v2").value_or(0);
    auto enabel_recruit_lead_old_user =
        context.GetIntCommonAttr("enabel_recruit_lead_old_user").value_or(0);
    auto enable_recruit_people_type_v3 =
        context.GetIntCommonAttr("enable_recruit_people_type_v3").value_or(0);
    auto enable_recruit_people_type_v3_2 =
        context.GetIntCommonAttr("enable_recruit_people_type_v3_2").value_or(0);
    auto enable_recruit_close_revenue_user_new =
        context.GetIntCommonAttr("enable_recruit_close_revenue_user_new").value_or(0);
    auto enable_recruit_close_high_gift_user_new =
        context.GetIntCommonAttr("enable_recruit_close_high_gift_user_new").value_or(0);
    auto enable_recruit_close_gift_user_new =
        context.GetIntCommonAttr("enable_recruit_close_gift_user_new").value_or(0);
    auto enable_recruit_close_big_r_user_new =
        context.GetIntCommonAttr("enable_recruit_close_big_r_user_new").value_or(0);
    auto recruit_people_history_deliver_user_symbol =
        context.GetStringCommonAttr("recruit_people_history_deliver_user_symbol").value_or("0");
    auto recruit_people_realtime_deliver_user_symbol =
        context.GetStringCommonAttr("recruit_people_realtime_deliver_user_symbol").value_or("0");
    auto recruit_people_recent_deliver_user_symbol =
        context.GetStringCommonAttr("recruit_people_recent_deliver_user_symbol").value_or("0");
    auto enable_recruit_people_show_deliver_cnt_limt =
        context.GetIntCommonAttr("enable_recruit_people_show_deliver_cnt_limt").value_or(0);
    auto recruit_live_show_cnt_list =
        context.GetIntListCommonAttr("recruit_live_show_cnt_list").value_or(absl::Span<const int64>());
    auto recruit_live_deliver_cnt_list =
        context.GetIntListCommonAttr("recruit_live_deliver_cnt_list").value_or(absl::Span<const int64>());
    auto recruit_user_show_cnt_limit_list =
        context.GetIntListCommonAttr("recruit_user_show_cnt_limit_list").value_or(absl::Span<const int64>());
    auto enable_recruit_content_last_show_time =
        context.GetIntCommonAttr("enable_recruit_content_last_show_time").value_or(0);
    auto enable_recruit_live_last_show_time =
        context.GetIntCommonAttr("enable_recruit_live_last_show_time").value_or(0);
    auto recruit_content_last_show_time_limit_list =
        context.GetIntListCommonAttr("recruit_content_last_show_time_limit_list")
        .value_or(absl::Span<const int64>());
    auto uRecruitLiveExpLastPvTsKV =
        context.GetIntCommonAttr("uRecruitLiveExpLastPvTsKV").value_or(0);
    auto uRecruitPhotoPlayLastPvTsKV =
        context.GetIntCommonAttr("uRecruitPhotoPlayLastPvTsKV").value_or(0);
    auto uRecruit5RTagKv =
        context.GetIntCommonAttr("uRecruit5RTagKv").value_or(0);
    auto enable_recruit_not_recruit_user =
        context.GetIntCommonAttr("enable_recruit_not_recruit_user").value_or(0);
    auto recruit_people_not_recruit_user_symbol_list =
        context.GetStringListCommonAttr("recruit_people_not_recruit_user_symbol_list")
        .value_or(std::vector<absl::string_view>());

    auto is_recruit_people_flag = 0;
    if (recruit_people_type == 1 && is_recruit_people_v1 == 1) {
      is_recruit_people_flag = 1;
    } else if (recruit_people_type == 2 && is_recruit_people_v2 == 1) {
      is_recruit_people_flag = 1;
    } else if (recruit_people_type == 3 && is_recruit_people_v3 == 1) {
      is_recruit_people_flag = 1;
    } else if (recruit_people_type == 4 && (live_user_common_category_type.find("r") != std::string::npos ||
                                            live_user_common_category_type.find("m") != std::string::npos)) {
      is_recruit_people_flag = 1;
    } else if (recruit_people_type == 5 && (live_user_common_category_type.find("m") != std::string::npos)) {
      is_recruit_people_flag = 1;
    } else if (recruit_people_type == 6 &&
               (is_recruit_people_v2 == 1 || live_user_common_category_type.find("r") != std::string::npos)) {
      is_recruit_people_flag = 1;
    } else if (recruit_people_type == 8 &&
               (is_recruit_people_v1 == 1 || live_user_common_category_type.find("r") != std::string::npos ||
                recruit_realtime_action_type.find("d") != std::string::npos)) {
      is_recruit_people_flag = 1;
    } else if (recruit_people_type == 9 &&
               (live_user_common_category_type.find("r") != std::string::npos ||
                live_user_common_category_type.find(recruit_people_type_str) != std::string::npos)) {
      is_recruit_people_flag = 1;
    } else if (recruit_people_type == 10 &&
               live_user_common_category_type.find(recruit_people_type_str) != std::string::npos) {
      is_recruit_people_flag = 1;
    } else if (recruit_people_type == 11 && user_recruit_people_rank <= recruit_people_check_use_rank_value) {
      is_recruit_people_flag = 1;
    } else if (recruit_people_type == 12) {
      is_recruit_people_flag = 1;
    }

    if (enable_recruit_people_check_v2 == 1 &&
        recruit_offline_user_info.find(recruit_people_type_str_new) != std::string::npos) {
      is_recruit_people_flag = 1;
    }

    if (enable_recruit_history_deliver_people == 1 &&
        live_user_common_category_type.find("d") != std::string::npos) {
      is_recruit_people_flag = 1;
    }
    if (enable_recruit_history_deliver_people_v2 == 1 &&
        recruit_offline_user_info.find("d") != std::string::npos) {
      is_recruit_people_flag = 1;
    }
    if (enabel_recruit_lead_old_user == 1 && recruit_offline_user_info.find("o") != std::string::npos) {
      is_recruit_people_flag = 1;
    }
    if (enable_recruit_real_action_people == 1 &&
        recruit_realtime_action_type.find(recruit_real_action_people_type) != std::string::npos) {
      is_recruit_people_flag = 1;
    }

    if (enable_recruit_close_revenue_user == 1 && user_revenue_score > recruit_close_renenue_user_weight &&
        enable_recruit_close_revenue_user_new == 0) {
      is_recruit_people_flag = 0;
    }
    if (enable_recruit_close_high_gift_user == 1 && is_high_gift_user == 1 &&
        enable_recruit_close_high_gift_user_new == 0) {
      is_recruit_people_flag = 0;
    }
    if (enable_recruit_close_gift_user == 1 && is_gift_user == 1 &&
        enable_recruit_close_gift_user_new == 0) {
      is_recruit_people_flag = 0;
    }
    if (enable_recruit_close_big_r_user == 1 && is_big_r_user == 1 &&
        enable_recruit_close_big_r_user_new == 0) {
      is_recruit_people_flag = 0;
    }
    // 开关均关闭，屏蔽代码
    // if (enable_recruit_filter_hate_user == 1 &&
    //     recruit_offline_user_info.find("h") != std::string::npos) {
    //   is_recruit_people_flag = 0;
    // }
    if (enable_recruit_filter_black_uid == 1 &&
        recruit_offline_user_info.find("l") != std::string::npos) {
      is_recruit_people_flag = 0;
    }
    // 开关均关闭，屏蔽代码
    // if (enable_recruit_close_not_target_user == 1 &&
    //     live_user_common_category_type.find("n") != std::string::npos) {
    //   is_recruit_people_flag = 0;
    // }

    auto is_recruit_deliver_old_user = 0;
    auto is_recruit_deliver_new_user = 0;
    auto is_recruit_not_deliver_user = 0;
    auto is_recruit_deliver_recent_user = 0;
    auto is_recruit_deliver_history_user = 0;
    if (live_user_common_category_type.find("d") != std::string::npos) {
      is_recruit_deliver_old_user = 1;
    }
    if (recruit_realtime_action_type.find("d") != std::string::npos &&
        live_user_common_category_type.find("d") == std::string::npos) {
      is_recruit_deliver_new_user = 1;
    }
    if (is_recruit_people_flag == 1 && is_recruit_deliver_old_user != 1 && is_recruit_deliver_new_user != 1) {
      is_recruit_not_deliver_user = 1;
    }
    if (enable_recruit_user_type_boost_v1 == 1) {
      is_recruit_deliver_old_user = 0;
      if (live_user_common_category_type.find("a") != std::string::npos) {
        is_recruit_deliver_recent_user = 1;
      }
      if (live_user_common_category_type.find("d") != std::string::npos &&
          live_user_common_category_type.find("a") == std::string::npos) {
        is_recruit_deliver_history_user = 1;
      }
    }
    if (enable_recruit_people_type_v2 == 1) {
      is_recruit_deliver_old_user = 0;
      is_recruit_deliver_new_user = 0;
      is_recruit_not_deliver_user = 0;
      is_recruit_deliver_recent_user = 0;
      is_recruit_deliver_history_user = 0;
      if (recruit_offline_user_info.find("d") != std::string::npos) {
        is_recruit_deliver_old_user = 1;
      }
      if (recruit_realtime_action_type.find("d") != std::string::npos &&
          recruit_offline_user_info.find("d") == std::string::npos) {
        is_recruit_deliver_new_user = 1;
      }
      if (is_recruit_people_flag == 1 &&
          is_recruit_deliver_old_user != 1 &&
          is_recruit_deliver_new_user != 1) {
        is_recruit_not_deliver_user = 1;
      }
      if (enable_recruit_user_type_boost_v1_new == 1) {
        is_recruit_deliver_old_user = 0;
        if (recruit_offline_user_info.find("a") != std::string::npos) {
          is_recruit_deliver_recent_user = 1;
        }
        if (recruit_offline_user_info.find("d") != std::string::npos &&
            recruit_offline_user_info.find("a") == std::string::npos) {
          is_recruit_deliver_history_user = 1;
        }
      }
    }
    if (enabel_recruit_lead_old_user == 1 && recruit_offline_user_info.find("o") != std::string::npos) {
      is_recruit_deliver_history_user = 1;
    }

    if (enable_recruit_people_show_deliver_cnt_limt == 1) {
      if (recruit_live_show_cnt_list.size() == recruit_user_show_cnt_limit_list.size()
        && recruit_live_show_cnt_list.size() == recruit_live_deliver_cnt_list.size()) {
          for (int i = 0; i < recruit_live_show_cnt_list.size(); i++) {
            if (recruit_user_show_cnt_limit_list[i] > 0 && recruit_live_deliver_cnt_list[i] <= 0
              && recruit_live_show_cnt_list[i] >= recruit_user_show_cnt_limit_list[i]) {
                is_recruit_people_flag = 0;
          }
        }
      }
    }
    if (enable_recruit_people_type_v3 == 1 || enable_recruit_people_type_v3_2 == 1) {
      is_recruit_deliver_old_user = 0;
      is_recruit_deliver_new_user = 0;
      is_recruit_not_deliver_user = 0;
      is_recruit_deliver_recent_user = 0;
      is_recruit_deliver_history_user = 0;
      if (recruit_realtime_action_type.find(recruit_people_realtime_deliver_user_symbol)
          != std::string::npos &&
          recruit_offline_user_info.find(recruit_people_history_deliver_user_symbol) == std::string::npos) {
        is_recruit_deliver_new_user = 1;
        is_recruit_deliver_recent_user = 1;
        is_recruit_people_flag = 1;
      }
      if (recruit_offline_user_info.find(recruit_people_recent_deliver_user_symbol) != std::string::npos) {
        is_recruit_deliver_recent_user = 1;
        is_recruit_people_flag = 1;
      }
      if (recruit_offline_user_info.find(recruit_people_history_deliver_user_symbol) != std::string::npos &&
      is_recruit_deliver_recent_user != 1) {
        is_recruit_deliver_history_user = 1;
      }
      if (is_recruit_people_flag == 1 &&
          is_recruit_deliver_history_user == 0 &&
          is_recruit_deliver_new_user == 0 &&
          is_recruit_deliver_recent_user == 0) {
        is_recruit_not_deliver_user = 1;
      }
    }

    if (enable_recruit_content_last_show_time == 1 || enable_recruit_live_last_show_time == 1) {
      auto last_show_time = uRecruitLiveExpLastPvTsKV;
      auto cur_time = base::GetTimestamp() / 1000;
      if (enable_recruit_content_last_show_time == 1) {
        last_show_time = std::max(uRecruitPhotoPlayLastPvTsKV, last_show_time);
      }
      if (last_show_time > 0 && uRecruit5RTagKv <= 5 &&
          recruit_content_last_show_time_limit_list.size() == 6) {
        if (cur_time - last_show_time < recruit_content_last_show_time_limit_list[uRecruit5RTagKv] * 1000) {
          is_recruit_people_flag = 0;
        }
      }
    }
    if (enable_recruit_not_recruit_user == 1) {
      for (auto smb : recruit_people_not_recruit_user_symbol_list) {
        if (recruit_offline_user_info.find(smb) != std::string::npos) {
          is_recruit_people_flag = 0;
          break;
        }
      }
    }

    context.SetIntCommonAttr("is_recruit_people_flag", is_recruit_people_flag);
    context.SetIntCommonAttr("is_recruit_deliver_old_user", is_recruit_deliver_old_user);
    context.SetIntCommonAttr("is_recruit_deliver_new_user", is_recruit_deliver_new_user);
    context.SetIntCommonAttr("is_recruit_not_deliver_user", is_recruit_not_deliver_user);
    context.SetIntCommonAttr("is_recruit_deliver_recent_user", is_recruit_deliver_recent_user);
    context.SetIntCommonAttr("is_recruit_deliver_history_user", is_recruit_deliver_history_user);

    return true;
  }

  static bool CalcRecruitMcScore(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto is_recruit_deliver_old_user = context.GetIntCommonAttr("is_recruit_deliver_old_user").value_or(0);
    auto is_recruit_deliver_new_user = context.GetIntCommonAttr("is_recruit_deliver_new_user").value_or(0);
    auto is_recruit_deliver_recent_user =
              context.GetIntCommonAttr("is_recruit_deliver_recent_user").value_or(0);
    auto is_recruit_deliver_history_user =
              context.GetIntCommonAttr("is_recruit_deliver_history_user").value_or(0);
    auto is_recruit_people_flag = context.GetIntCommonAttr("is_recruit_people_flag").value_or(0);
    auto enable_recruit_people_mc_score =
              context.GetIntCommonAttr("enable_recruit_people_mc_score").value_or(0);
    auto mc_recruit_model_weight_str =
              context.GetStringCommonAttr("mc_recruit_model_weight").value_or("0,0,0,0");
    auto recruit_final_retr_ens_score = context.GetDoubleItemAttr("recruit_final_retr_ens_score");
    auto pctr_recruit = context.GetDoubleItemAttr("pctr_recruit");
    auto pcvr_recruit = context.GetDoubleItemAttr("pcvr_recruit");
    auto pctcvr_recruit = context.GetDoubleItemAttr("pctcvr_recruit");
    auto mc_final_retr_ens_score = context.SetDoubleItemAttr("mc_final_retr_ens_score");
    auto is_recruit_svip_target_item = context.SetIntItemAttr("is_recruit_svip_target_item");
    std::vector<absl::string_view> mc_recruit_model_weight_list =
                absl::StrSplit(mc_recruit_model_weight_str, ",", absl::SkipWhitespace());
    auto enable_recruit_svip_mc_boost =
              context.GetIntCommonAttr("enable_recruit_svip_mc_boost").value_or(0);
    auto reason_list_getter = context.GetIntListItemAttr("reason_list");

    int is_all_recruit_people = 0;
    if (enable_recruit_people_mc_score > 0 && is_recruit_people_flag > 0) {
        is_all_recruit_people = 1;
    }
    // output item attr
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto is_svip_item = 0;
      if (enable_recruit_svip_mc_boost == 1) {
        auto reason_list = reason_list_getter(result).value_or(absl::Span<const int64>());
        for (auto reason_tag : reason_list) {
          if (reason_tag == 667) {
            is_svip_item = 1;
            break;
          }
        }
      }
      is_recruit_svip_target_item(result, is_svip_item);
      if (is_recruit_deliver_old_user > 0 || is_recruit_deliver_new_user > 0 ||
          is_recruit_deliver_recent_user > 0 || is_recruit_deliver_history_user > 0 ||
          is_all_recruit_people) {
        double bias_weight, pctr_weight, pcvr_weight, pctcvr_weight;
        if (mc_recruit_model_weight_list.size() == 4 &&
            absl::SimpleAtod(mc_recruit_model_weight_list[0], &bias_weight) &&
            absl::SimpleAtod(mc_recruit_model_weight_list[1], &pctr_weight) &&
            absl::SimpleAtod(mc_recruit_model_weight_list[2], &pcvr_weight) &&
            absl::SimpleAtod(mc_recruit_model_weight_list[3], &pctcvr_weight)) {
            auto final_ens_score = bias_weight +
                            pctr_weight * pctr_recruit(result).value_or(0.0) +
                            pcvr_weight * pcvr_recruit(result).value_or(0.0) +
                            pctcvr_weight * pctcvr_recruit(result).value_or(0.0);
            if (is_svip_item == 1) {
              final_ens_score = final_ens_score + 1000.0;
            }
            mc_final_retr_ens_score(result, final_ens_score);
        }
      } else {
        auto retr_ens_score = recruit_final_retr_ens_score(result).value_or(0.0);
        if (is_svip_item == 1) {
          retr_ens_score = retr_ens_score + 1000.0;
        }
        mc_final_retr_ens_score(result, retr_ens_score);
      }
    });
    return true;
  }

static bool CalcMcRecruitScore(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto mc_recruit_svip_bias = context.GetDoubleCommonAttr("mc_recruit_svip_bias").value_or(1000.0);
    auto mc_recruit_svip_bias_2 = context.GetDoubleCommonAttr("mc_recruit_svip_bias_2").value_or(1.0);
    auto enable_recruit_svip_mc_boost =
          context.GetIntCommonAttr("enable_recruit_svip_mc_boost").value_or(0);
    auto enable_mc_recruit_exptag_boost =
          context.GetIntCommonAttr("enable_mc_recruit_exptag_boost").value_or(0);
    auto mc_recruit_exptag_boost_type = context.GetIntCommonAttr("mc_recruit_exptag_boost_type").value_or(0);
    auto mc_recruit_exptag_boost_reason_list =
    context.GetIntListCommonAttr("mc_recruit_exptag_boost_reason_list").value_or(absl::Span<const int64>());
    auto mc_recruit_exptag_boost_weight_list =
          context.GetDoubleListCommonAttr("mc_recruit_exptag_boost_weight_list")
          .value_or(absl::Span<const double>());

    auto recruit_final_retr_ens_score_getter = context.GetDoubleItemAttr("mc_final_retr_ens_score");
    auto reason_list_getter = context.GetIntListItemAttr("reason_list");

    auto mc_final_retr_ens_score_setter = context.SetDoubleItemAttr("mc_final_retr_ens_score");
    auto is_recruit_svip_target_item_setter = context.SetIntItemAttr("is_recruit_svip_target_item");

    // output item attr
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto is_svip_item = 0;
      auto bias = 0.0;
      auto reason_list = reason_list_getter(result).value_or(absl::Span<const int64>());
      if (enable_recruit_svip_mc_boost == 1) {
        for (auto reason_tag : reason_list) {
          if (reason_tag == 667) {
            is_svip_item = 1;
            bias = mc_recruit_svip_bias * mc_recruit_svip_bias_2;
            break;
          }
        }
      }
      double recruit_final_retr_ens_score = recruit_final_retr_ens_score_getter(result).value_or(0.0);
      if (enable_mc_recruit_exptag_boost == 1) {
        if (mc_recruit_exptag_boost_weight_list.size() == mc_recruit_exptag_boost_reason_list.size()) {
          for (auto reason : reason_list) {
              for (int i = 0; i < mc_recruit_exptag_boost_reason_list.size(); ++i) {
                if (mc_recruit_exptag_boost_reason_list[i] == reason) {
                  if (mc_recruit_exptag_boost_type == 1) {
                    recruit_final_retr_ens_score *= mc_recruit_exptag_boost_weight_list[i];
                  } else {
                    recruit_final_retr_ens_score += mc_recruit_exptag_boost_weight_list[i];
                  }
                }
              }
          }
        }
      }
      is_recruit_svip_target_item_setter(result, is_svip_item);
      mc_final_retr_ens_score_setter(result, recruit_final_retr_ens_score + bias);
    });
    return true;
  }

  // 计算房产 EE score
  static bool CalcHouseExploreScore(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto house_explore_alpha = context.GetDoubleItemAttr("house_explore_alpha");
    auto house_explore_beta = context.GetDoubleItemAttr("house_explore_beta");
    auto house_explore_score = context.SetDoubleItemAttr("house_explore_score");
    double ALPHA_MIN = 0.0001;
    double BETA_MIN = 0.0001;
    double ALPHA_MAX = 100;
    double BETA_MAX = 10000;
    std::mt19937 random_engine;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto he_alpha = house_explore_alpha(result).value_or(0.0);
      auto he_beta = house_explore_beta(result).value_or(0.0);
      if (he_alpha <= ALPHA_MIN || he_beta <= BETA_MIN || he_alpha >= ALPHA_MAX || he_beta >= BETA_MAX) {
        house_explore_score(result, 0.0);
      } else {
        boost::random::beta_distribution<> beta_engine(he_alpha, he_beta);
        double explore_prob = beta_engine(random_engine);
        house_explore_score(result, explore_prob);
      }
    });
    return true;
  }

  // 计算招聘 EE score
  static bool CalcRecruitExploreScore(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto recruit_explore_category_list =
        context.GetStringListCommonAttr("recruit_explore_category_list");
    auto recruit_explore_alpha_list = context.GetDoubleListCommonAttr("recruit_explore_alpha_list");
    auto recruit_explore_beta_list = context.GetDoubleListCommonAttr("recruit_explore_beta_list");
    auto recruit_explore_upper_limit = context.GetIntCommonAttr("recruit_explore_upper_limit").value_or(10);
    auto job_categoty_id_accessor = context.GetStringItemAttr("lLiveRecruitExplainJobCategoryIdString");
    auto recruit_explore_score = context.SetDoubleItemAttr("recruit_explore_score");
    double ALPHA_MIN = 0.0001;
    double BETA_MIN = 0.0001;
    double ALPHA_MAX = 100;
    double BETA_MAX = 10000;
    std::vector<int> random_index_list;
    std::mt19937 random_engine;
    folly::F14FastMap<absl::string_view, double, absl::Hash<absl::string_view>> user_alpha_map;
    folly::F14FastMap<absl::string_view, double, absl::Hash<absl::string_view>> user_beta_map;
    for (int i = 0; i < recruit_explore_category_list->size(); ++i) {
      random_index_list.push_back(i);
    }
    std::shuffle(random_index_list.begin(), random_index_list.end(),
                std::default_random_engine(base::GetTimestamp()));
    for (int i = 0; i < random_index_list.size(); ++i) {
      if (i >= recruit_explore_upper_limit) {
        break;
      }
      int index = random_index_list[i];
      auto categoty_id = recruit_explore_category_list->at(index);
      double alpha =  recruit_explore_alpha_list.value()[index];
      double beta = recruit_explore_beta_list.value()[index];
      // CL_LOG_EVERY_N(INFO, 1) << " index: " <<  index << "|| id: " << categoty_id;
      // CL_LOG_EVERY_N(INFO, 1) << " alpha: " <<  alpha << "|| beta: " << beta;
      if (alpha <= ALPHA_MIN || beta <= BETA_MIN || alpha >= ALPHA_MAX || beta >= BETA_MAX) {
        continue;
      }
      user_alpha_map[categoty_id] = alpha;
      user_beta_map[categoty_id] = beta;
    }
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto job_categoty_id = job_categoty_id_accessor(result).value_or("");
      // CL_LOG_EVERY_N(INFO, 1) << "need id: " <<  job_categoty_id;
      if (user_alpha_map.find(job_categoty_id) != user_alpha_map.end()
          && user_beta_map.find(job_categoty_id) != user_beta_map.end()) {
        double alpha = user_alpha_map[job_categoty_id];
        double beta = user_beta_map[job_categoty_id];
        boost::random::beta_distribution<> beta_engine(alpha, beta);
        double explore_prob = beta_engine(random_engine);
        recruit_explore_score(result, explore_prob);
        // CL_LOG_EVERY_N(INFO, 1) << "userzxf alpha: " <<  alpha << "|| beta: " << beta;
        // CL_LOG_EVERY_N(INFO, 1) << "userzxf explore: " <<  explore_prob;
      } else {
        recruit_explore_score(result, 0.0);
      }
    });
    return true;
  }

  static bool CalcRecruitMmuFilterFlag(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto recruit_filter_mmu_type = context.GetIntCommonAttr("recruit_filter_mmu_type").value_or(1);
    auto enable_recruit_live_mmu_tag = context.GetIntCommonAttr("enable_recruit_live_mmu_tag").value_or(0);
    auto recruit_not_mmu_recognize_reduce_weight =
        context.GetDoubleCommonAttr("recruit_not_mmu_recognize_reduce_weight").value_or(0.1);
    auto recruit_priority_attr = context.GetIntItemAttr("recruit_priority_attr");
    auto mmu_tag_id = context.GetIntListItemAttr("mmu_tag_id");
    auto mmu_tag_v4 = context.GetIntListItemAttr("mmu_tag_info.tag_list_lv4");

    auto mmu_recruit_flag = context.SetDoubleItemAttr("mmu_recruit_flag");
    auto recruit_tag_list = context.GetIntListCommonAttr("mmu_recruit_tag_list");

    std::set<int> recruit_filter_tag_list = {2035, 2036, 2037, 2038, 2040, 2146, 2147, 3116, 3117, 3118,
                                             3119, 3120, 3121, 3122, 3124, 3125, 3408, 3409, 3410, 3412};
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      std::set<int> mmu_tag_id_list;

      auto recruit_priority_attr_value = recruit_priority_attr(result).value_or(0);
      auto mmu_tag_id_value = mmu_tag_id(result);
      bool is_recruit_tag = contain_tag(mmu_tag_v4(result), recruit_tag_list);

      auto is_recruit_mmu_filter_value = 1.0;
      if (recruit_priority_attr_value > 0) {
        if (mmu_tag_id_value.has_value()) {
          for (auto mmu_tag : mmu_tag_id_value.value()) {
            mmu_tag_id_list.insert(mmu_tag);
          }
          if (recruit_filter_mmu_type == 1) {
            if (mmu_tag_id_list.find(4513) == mmu_tag_id_list.end() &&
                mmu_tag_id_list.find(4519) == mmu_tag_id_list.end()) {
              is_recruit_mmu_filter_value = recruit_not_mmu_recognize_reduce_weight;
            }
          } else if (recruit_filter_mmu_type == 2) {
            folly::F14FastSet<int> inter_set;
            set_intersection(mmu_tag_id_list.begin(), mmu_tag_id_list.end(), recruit_filter_tag_list.begin(),
                             recruit_filter_tag_list.end(), inserter(inter_set, inter_set.begin()));
            if (inter_set.size() > 0 && mmu_tag_id_list.find(4513) == mmu_tag_id_list.end()) {
              is_recruit_mmu_filter_value = recruit_not_mmu_recognize_reduce_weight;
            }
          }
        } else {
          if (enable_recruit_live_mmu_tag > 0) {
            if (!is_recruit_tag) {
              is_recruit_mmu_filter_value = recruit_not_mmu_recognize_reduce_weight;
            }
          }
        }
      }

      mmu_recruit_flag(result, is_recruit_mmu_filter_value);
    });
    return true;
  }

  static bool CalcRecruitHoldoutFilterFlag(const CommonRecoLightFunctionContext &context,
                                           RecoResultConstIter begin, RecoResultConstIter end) {
    auto live_user_common_category_type =
        context.GetStringCommonAttr("reco.live_user_feature.live_user_common_category_type").value_or("0");
    auto recruit_priority_attr = context.GetIntItemAttr("recruit_priority_attr");

    auto is_recruit_holdout_filter_flag = context.SetIntItemAttr("is_recruit_holdout_filter_flag");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto recruit_priority_attr_value = recruit_priority_attr(result).value_or(0);
      auto is_recruit_holdout_filter_flag_value = 0;
      if (recruit_priority_attr_value > 0 && live_user_common_category_type.find("n") != std::string::npos) {
        is_recruit_holdout_filter_flag_value = 1;
      }

      is_recruit_holdout_filter_flag(result, is_recruit_holdout_filter_flag_value);
    });
    return true;
  }

  static bool CalcRecruitDynamicHourClaim(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto recruit_dynamic_hour_claim_list = context.GetDoubleListCommonAttr("recruit_dynamic_hour_claim_list");
    auto recruit_dynamic_hour_list = context.GetIntListCommonAttr("recruit_dynamic_hour_list");
    auto recruit_dynamic_hour_claim = 1.0;
    if (recruit_dynamic_hour_claim_list.has_value() && recruit_dynamic_hour_list.has_value() &&
        recruit_dynamic_hour_claim_list.value().size() > 0 && recruit_dynamic_hour_list.value().size() > 0 &&
        recruit_dynamic_hour_claim_list.value().size() == recruit_dynamic_hour_list.value().size()) {
      std::map<int, double> map_hour_claim_value;
      for (int i = 0; i < recruit_dynamic_hour_list.value().size(); ++i) {
        map_hour_claim_value[recruit_dynamic_hour_list.value()[i]] =
            recruit_dynamic_hour_claim_list.value()[i];
      }
      base::Time ts = base::Time::FromTimeT(base::GetTimestamp() / 1e6);
      base::Time::Exploded exploded;
      ts.LocalExplode(&exploded);
      if (map_hour_claim_value.count(exploded.hour) > 0) {
        recruit_dynamic_hour_claim = map_hour_claim_value[exploded.hour];
      }
    }

    context.SetDoubleCommonAttr("recruit_dynamic_hour_claim", recruit_dynamic_hour_claim);

    return true;
  }

  static bool CalcRecruitExposeCountFlag(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    auto recruit_limit = context.GetIntCommonAttr("recruit_user_expose_count_limit").value_or(400);
    auto recruit_real_expose_count = context.GetIntCommonAttr("recruit_tag_expose_count").value_or(0);
    auto recruit_user_expose_count_list = context.GetIntListCommonAttr("recruit_user_expose_count_list");
    auto recruit_expose_count_flag = 1;
    auto recruit_expose_count_claim = 1.0;

    if (recruit_user_expose_count_list.has_value() && recruit_user_expose_count_list.value().size() > 0) {
      auto user_expose_count = recruit_user_expose_count_list.value()[0];
      user_expose_count += recruit_real_expose_count;
      if (user_expose_count >= recruit_limit) {
        recruit_expose_count_flag = 0;
      } else {
        if (user_expose_count > 0) {
          recruit_expose_count_claim = std::min(10.0, recruit_limit / static_cast<double>(user_expose_count));
        }
      }
    } else {
      recruit_expose_count_claim = 10.0;
    }

    context.SetIntCommonAttr("recruit_expose_count_flag", recruit_expose_count_flag);
    context.SetDoubleCommonAttr("recruit_expose_count_claim", recruit_expose_count_claim);

    return true;
  }

  static bool CalcRecruitKABoostWeight(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto recruit_author_ka_boost_value =
        context.GetDoubleCommonAttr("recruit_author_ka_boost_value").value_or(1.0);
    auto recruit_author_ka_list = context.GetIntListCommonAttr("recruit_author_ka_list");
    auto enable_recuit_ka_use_city_match =
        context.GetIntCommonAttr("enable_recuit_ka_use_city_match").value_or(0);
    auto recruit_ka_city_list = context.GetStringListCommonAttr("recruit_ka_city_list");
    auto user_city = context.GetStringCommonAttr("user_city").value_or("");
    auto author_id = context.GetIntItemAttr("author_id");

    auto recruit_ka_boost_weight = context.SetDoubleItemAttr("recruit_ka_boost_weight");

    std::set<int> recruit_author_ka_set;
    if (recruit_author_ka_list.has_value()) {
      for (auto ka_author : recruit_author_ka_list.value()) {
        recruit_author_ka_set.insert(ka_author);
      }
    }
    folly::F14FastSet<std::string> recruit_ka_city_set;
    if (recruit_ka_city_list.has_value()) {
      for (auto city : recruit_ka_city_list.value()) {
        recruit_ka_city_set.insert(city.data());
      }
    }
    bool is_city_match = false;
    if (enable_recuit_ka_use_city_match == 0) {
      is_city_match = true;
    } else {
      if (recruit_ka_city_set.count(user_city.data()) > 0) {
        is_city_match = true;
      }
    }

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      double recruit_ka_boost_weight_value = 1.0;
      auto author_id_value = author_id(result).value_or(0);
      if (recruit_author_ka_set.find(author_id_value) != recruit_author_ka_set.end() && is_city_match) {
        recruit_ka_boost_weight_value = recruit_author_ka_boost_value;
      }
      recruit_ka_boost_weight(result, recruit_ka_boost_weight_value);
    });

    return true;
  }

  static bool CalcRecruitPropensityWeight(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto recruit_propensity_expire =
        context.GetDoubleCommonAttr("recruit_propensity_expire").value_or(30.0);
    auto recruit_action_time_list =
        context.GetIntListCommonAttr("uRecruitConsumeActionTimeList");
    auto recruit_action_type_list =
        context.GetIntListCommonAttr("uRecruitConsumeActionTypeList");
    auto recruit_action_time_attenuation =
        context.GetDoubleCommonAttr("recruit_action_time_attenuation").value_or(1.0);
    auto recruit_action_max_boost =
        context.GetDoubleCommonAttr("recruit_action_max_boost").value_or(1.0);
    auto action_type_weight_str =
        context.GetStringCommonAttr("recruit_action_type_weight").value_or("32:1.0");

    if (!recruit_action_time_list.has_value() ||
        !recruit_action_type_list.has_value() ||
        recruit_action_time_list.value().size() != recruit_action_type_list.value().size()) {
      context.SetDoubleCommonAttr("user_recruit_propensity_weight", 1.0);
      return true;
    }

    folly::F14FastMap<int, double> action_type_weight_map;
    if (!livestream::ParseWeightParam(std::string(action_type_weight_str), ";", &action_type_weight_map)) {
      action_type_weight_map[32] = 1.0;
    }

    double propensity_weight = 1.0;
    auto cur_time = base::GetTimestamp() / 1e6;
    for (size_t i = 0; i < recruit_action_time_list.value().size(); ++i) {
      if (recruit_action_type_list.value()[i] == 0) {
        continue;
      }
      int time_diff = (cur_time - recruit_action_time_list.value()[i]) / (24.0 * 3600);
      if (time_diff <= 0 || time_diff > recruit_propensity_expire) {
        continue;
      }
      int action_type = 1;
      double action_factor = 0.0;
      for (int j = 1; j <= 6; ++j) {
        if ((recruit_action_type_list.value()[i] & action_type) == action_type &&
            action_type_weight_map.find(action_type) != action_type_weight_map.end()) {
          action_factor += action_type_weight_map[action_type];
        }
        action_type *= 2;
      }
      propensity_weight += recruit_action_max_boost * action_factor /
          std::pow(1.0 + time_diff, recruit_action_time_attenuation);
    }

    context.SetDoubleCommonAttr("user_recruit_propensity_weight", propensity_weight);
    return true;
  }

  static bool CalcRecruitBidScore(const CommonRecoLightFunctionContext &context,
                                  RecoResultConstIter begin, RecoResultConstIter end) {
    auto recruit_bid_info_list = context.GetDoubleListCommonAttr("recruit_bid_info_list");
    auto recruit_predict_info_list = context.GetDoubleListCommonAttr("recruit_predict_info_list");
    auto recruit_core_people_bid_boost_value =
        context.GetDoubleCommonAttr("recruit_core_people_bid_boost_value").value_or(1.0);
    auto recruit_core_people_bid_deboost_value =
        context.GetDoubleCommonAttr("recruit_core_people_bid_deboost_value").value_or(1.0);
    auto enable_recruit_core_people_bid_boost =
        context.GetIntCommonAttr("enable_recruit_core_people_bid_boost").value_or(0);
    auto enable_recruit_bid_use_unit_model =
        context.GetIntCommonAttr("enable_recruit_bid_use_unit_model").value_or(0);
    auto enable_recruit_bid_use_backlink_model =
        context.GetIntCommonAttr("enable_recruit_bid_use_backlink_model").value_or(0);
    auto recruit_predict_bid_backlink_pxtr_weight =
        context.GetDoubleCommonAttr("recruit_predict_bid_backlink_pxtr_weight").value_or(1.0);
    auto recruit_offline_user_info =
        context.GetStringCommonAttr("recruit_offline_user_info").value_or("");
    auto sim_prtr = context.GetDoubleItemAttr("sim_prtr");
    auto unit_pctr = context.GetDoubleItemAttr("unit_pctr");
    auto pctr_recruit = context.GetDoubleItemAttr("pctr_recruit");
    auto recruit_priority_attr = context.GetIntItemAttr("recruit_priority_attr");
    auto is_house_live = context.GetIntItemAttr("lLiveHouseIsLiveTag");
    auto is_house_live_new_tag = context.GetIntItemAttr("lLiveIsHouseLiveTag");
    auto house_bid_index = context.GetIntItemAttr("house_bid_index");
    auto recruit_backlink_pxtr = context.GetDoubleItemAttr("recruit_backlink_pxtr");

    auto recruit_bid_score = context.SetDoubleItemAttr("recruit_bid_score");
    // 房聘 CPT 策略用户侧
    auto enable_house_live_cpt_boost
            = context.GetIntCommonAttr("enable_house_live_cpt_boost").value_or(0);
    auto house_live_cpt_boost_rate
            = context.GetDoubleCommonAttr("house_live_cpt_boost_rate").value_or(0.0);
    auto enable_recruit_live_cpt_boost
            = context.GetIntCommonAttr("enable_recruit_live_cpt_boost").value_or(0);
    auto recruit_live_cpt_boost_rate
            = context.GetDoubleCommonAttr("recruit_live_cpt_boost_rate").value_or(0.0);
    // 房聘 CPT 策略作者侧
    auto liveIsHouseCptNoAAuthor
            = context.GetIntItemAttr("liveIsHouseCptNoAAuthor");
    auto liveIsRecruitCptNoAAuthor
            = context.GetIntItemAttr("liveIsRecruitCptNoAAuthor");
    auto enable_house_live_cpt_author
            = context.GetIntItemAttr("enable_house_live_cpt_author");
    auto enable_recruit_live_cpt_author
            = context.GetIntItemAttr("enable_recruit_live_cpt_author");

    std::vector<double> recruit_predict_score_vec =
      { 0.00000512, 0.00000962, 0.00001385, 0.00001801, 0.00002226,
        0.00002671, 0.00003147, 0.00003669, 0.00004251, 0.00004915,
        0.00005694, 0.00006638, 0.00007826, 0.00009408, 0.00011664,
        0.00015195, 0.00021481, 0.00034986, 0.00075907, 0.56575370 };
    std::vector<double> recruit_bid_score_vec =
      { 0.00001282, 0.00002083, 0.00002428, 0.00002821, 0.00002972,
        0.00003449, 0.00003307, 0.00003689, 0.00003909, 0.00003773,
        0.00004139, 0.00004825, 0.00004773, 0.00005291, 0.00005987,
        0.00006212, 0.00006159, 0.00006520, 0.00006709, 0.00018739 };
    if (recruit_bid_info_list.has_value() && recruit_bid_info_list.value().size() >= 20) {
      recruit_bid_score_vec.clear();
      for (auto bid_score : recruit_bid_info_list.value()) {
        recruit_bid_score_vec.push_back(bid_score);
      }
    }

    if (recruit_predict_info_list.has_value() && recruit_predict_info_list.value().size() >= 20) {
      recruit_predict_score_vec.clear();
      for (auto predict_score : recruit_predict_info_list.value()) {
        recruit_predict_score_vec.push_back(predict_score);
      }
    }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double recruit_bid_score_value = 0.0;
      auto sim_prtr_value = sim_prtr(result).value_or(0.0);
      auto unit_pctr_value = unit_pctr(result).value_or(0.0);
      auto pctr_recruit_value = pctr_recruit(result).value_or(0.0);
      auto recruit_priority_attr_value = recruit_priority_attr(result).value_or(0);
      auto is_house_live_value = is_house_live(result).value_or(0);
      auto is_house_live_new_tag_value = is_house_live_new_tag(result).value_or(0);
      auto recruit_backlink_pxtr_value = recruit_backlink_pxtr(result).value_or(0.0);
      is_house_live_value = is_house_live_value > 0 ? is_house_live_value : is_house_live_new_tag_value;
      auto house_bid_index_value = house_bid_index(result).value_or(0);
      auto liveIsHouseCptNoAAuthor_value = liveIsHouseCptNoAAuthor(result).value_or(0);
      auto liveIsRecruitCptNoAAuthor_value = liveIsRecruitCptNoAAuthor(result).value_or(0);
      auto enable_house_live_cpt_author_value = enable_house_live_cpt_author(result).value_or(0);
      auto enable_recruit_live_cpt_author_value = enable_recruit_live_cpt_author(result).value_or(0);
      if (recruit_priority_attr_value > 0) {
        double recruit_model_score = sim_prtr_value * pctr_recruit_value;
        if (enable_recruit_bid_use_unit_model == 1) {
          recruit_model_score = unit_pctr_value * pctr_recruit_value;
        }
        if (enable_recruit_bid_use_backlink_model == 1) {
          recruit_model_score = unit_pctr_value * pctr_recruit_value
                              * (1 + recruit_predict_bid_backlink_pxtr_weight * recruit_backlink_pxtr_value);
        }
        int index = 0;
        int vec_size = recruit_predict_score_vec.size();
        if (recruit_model_score > recruit_predict_score_vec[vec_size - 2]) {
          index = vec_size - 1;
        } else {
          for (int i = 0; i < recruit_predict_score_vec.size(); ++i) {
            if (recruit_model_score < recruit_predict_score_vec[i]) {
              index = i;
              break;
            }
          }
        }
        if (index >= recruit_bid_score_vec.size()) {
          index = recruit_bid_score_vec.size() - 1;
        }
        recruit_bid_score_value = recruit_bid_score_vec[index];
        if (enable_recruit_core_people_bid_boost == 1) {
          if (recruit_offline_user_info.find("a") != std::string::npos) {
            recruit_bid_score_value *= recruit_core_people_bid_boost_value;
          } else {
            recruit_bid_score_value *= recruit_core_people_bid_deboost_value;
          }
        }
      } else if (is_house_live_value > 0 && house_bid_index_value < recruit_bid_score_vec.size()) {
        recruit_bid_score_value = recruit_bid_score_vec[house_bid_index_value];
      }
      if (enable_house_live_cpt_boost) {
        if (enable_house_live_cpt_author_value && liveIsHouseCptNoAAuthor_value) {
          recruit_bid_score_value *= (1 + house_live_cpt_boost_rate);
        }
      }
      if (enable_recruit_live_cpt_boost) {
        if (enable_recruit_live_cpt_author_value && liveIsRecruitCptNoAAuthor_value) {
          recruit_bid_score_value *= (1 + recruit_live_cpt_boost_rate);
        }
      }
      recruit_bid_score(result, recruit_bid_score_value);
    });

    return true;
  }

    static bool CalcRecruitRealBidScore(const CommonRecoLightFunctionContext &context,
                                  RecoResultConstIter begin, RecoResultConstIter end) {
    auto recruit_predict_bid_real_score_coeff =
        context.GetDoubleCommonAttr("recruit_predict_bid_real_score_coeff").value_or(0);
    auto recruit_predict_bid_real_score_old_user_coeff =
        context.GetDoubleCommonAttr("recruit_predict_bid_real_score_old_user_coeff").value_or(0);
    auto enable_recruit_bid_use_backlink_model =
        context.GetIntCommonAttr("enable_recruit_bid_use_backlink_model").value_or(0);
    auto recruit_predict_bid_backlink_pxtr_weight =
        context.GetDoubleCommonAttr("recruit_predict_bid_backlink_pxtr_weight").value_or(1.0);
    auto recruit_offline_user_info = context.GetStringCommonAttr("recruit_offline_user_info").value_or("0");
    auto recruit_bid_people_type = context.GetStringCommonAttr("recruit_bid_people_type").value_or("0");
    auto recruit_realtime_action_type =
          context.GetStringCommonAttr("recruit_realtime_action_type").value_or("0");
    auto recruit_bid_people_type_realtime =
          context.GetStringCommonAttr("recruit_bid_people_type_realtime").value_or("0");
    auto enable_recruit_real_bid_house_score =
        context.GetIntCommonAttr("enable_recruit_real_bid_house_score").value_or(0);
    auto recruit_bid_house_max_score =
        context.GetDoubleCommonAttr("recruit_bid_house_max_score").value_or(0.0);
    auto recruit_bid_house_bucket_num = context.GetIntCommonAttr("recruit_bid_house_bucket_num").value_or(0);
    auto uRecruit5RTagKv = context.GetIntCommonAttr("uRecruit5RTagKv").value_or(0);
    auto enable_recruit_real_bid_user_type =
      context.GetIntCommonAttr("enable_recruit_real_bid_user_type").value_or(0);
    auto recruit_real_bid_user_type_list =
      context.GetIntListCommonAttr("recruit_real_bid_user_type_list").value_or(absl::Span<const int64>());
    auto recruit_real_bid_user_type_score_list =
      context.GetDoubleListCommonAttr("recruit_real_bid_user_type_score_list")
      .value_or(absl::Span<const double>());
    auto enable_recruit_live_bid_with_pgvalue5 =
      context.GetIntCommonAttr("enable_recruit_live_bid_with_pgvalue5").value_or(0);
    auto recruit_live_bid_with_pgvalue5_type =
      context.GetIntCommonAttr("recruit_live_bid_with_pgvalue5_type").value_or(0);
    auto enable_recruit_live_recruit_pctr_bid =
      context.GetIntCommonAttr("enable_recruit_live_recruit_pctr_bid").value_or(0);
    auto recruit_bid_use_backlink_model_type =
      context.GetIntCommonAttr("recruit_bid_use_backlink_model_type").value_or(0);
    auto unit_pctr = context.GetDoubleItemAttr("unit_pctr");
    auto pctr_recruit = context.GetDoubleItemAttr("pctr_recruit");
    auto recruit_priority_attr = context.GetIntItemAttr("recruit_priority_attr");
    auto recruit_backlink_pxtr = context.GetDoubleItemAttr("recruit_backlink_pxtr");
    auto is_house_live = context.GetIntItemAttr("lLiveHouseIsLiveTag");
    auto is_house_live_new_tag = context.GetIntItemAttr("lLiveIsHouseLiveTag");
    auto house_bid_index = context.GetIntItemAttr("house_bid_index");
    auto unit_pgvalue5 = context.GetDoubleItemAttr("unit_pgvalue5");
    auto recruit_bid_score = context.SetDoubleItemAttr("recruit_bid_score");
    auto enable_recruit_live_bid_with_recruit_pcvr =
      context.GetIntCommonAttr("enable_recruit_live_bid_with_recruit_pcvr").value_or(0);
    auto recruit_live_bid_with_recruit_pcvr_type =
      context.GetIntCommonAttr("recruit_live_bid_with_recruit_pcvr_type").value_or(0);
    auto pcvr_recruit = context.GetDoubleItemAttr("pcvr_recruit");
    // 房聘 CPT 策略用户侧
    auto enable_house_live_cpt_boost
            = context.GetIntCommonAttr("enable_house_live_cpt_boost").value_or(0);
    auto house_live_cpt_boost_rate
            = context.GetDoubleCommonAttr("house_live_cpt_boost_rate").value_or(0.0);
    auto enable_recruit_live_cpt_boost
            = context.GetIntCommonAttr("enable_recruit_live_cpt_boost").value_or(0);
    auto recruit_live_cpt_boost_rate
            = context.GetDoubleCommonAttr("recruit_live_cpt_boost_rate").value_or(0.0);
    // 房聘 CPT 策略作者侧
    auto liveIsHouseCptNoAAuthor
            = context.GetIntItemAttr("liveIsHouseCptNoAAuthor");
    auto liveIsRecruitCptNoAAuthor
            = context.GetIntItemAttr("liveIsRecruitCptNoAAuthor");
    auto enable_house_live_cpt_author
            = context.GetIntItemAttr("enable_house_live_cpt_author");
    auto enable_recruit_live_cpt_author
            = context.GetIntItemAttr("enable_recruit_live_cpt_author");

    auto house_bid_score_weight
            = context.GetDoubleCommonAttr("house_bid_score_weight").value_or(1.0);

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double recruit_bid_score_value = 0.0;
      auto unit_pctr_value = unit_pctr(result).value_or(0.0);
      auto pctr_recruit_value = pctr_recruit(result).value_or(0.0);
      auto recruit_priority_attr_value = recruit_priority_attr(result).value_or(0);
      auto recruit_backlink_pxtr_value = recruit_backlink_pxtr(result).value_or(0.0);
      auto is_house_live_value = is_house_live(result).value_or(0);
      auto is_house_live_new_tag_value = is_house_live_new_tag(result).value_or(0);
      is_house_live_value = is_house_live_value > 0 ? is_house_live_value : is_house_live_new_tag_value;
      auto house_bid_index_value = house_bid_index(result).value_or(0);
      auto liveIsHouseCptNoAAuthor_value = liveIsHouseCptNoAAuthor(result).value_or(0);
      auto liveIsRecruitCptNoAAuthor_value = liveIsRecruitCptNoAAuthor(result).value_or(0);
      auto enable_house_live_cpt_author_value = enable_house_live_cpt_author(result).value_or(0);
      auto enable_recruit_live_cpt_author_value = enable_recruit_live_cpt_author(result).value_or(0);
      auto unit_pgvalue5_value = unit_pgvalue5(result).value_or(0.0);
      auto pcvr_recruit_value = pcvr_recruit(result).value_or(0.0);
      if (recruit_priority_attr_value > 0) {
        recruit_bid_score_value = unit_pctr_value * pctr_recruit_value;
        if (enable_recruit_live_bid_with_pgvalue5 == 1) {
          if (recruit_live_bid_with_pgvalue5_type == 1) {
            recruit_bid_score_value *= unit_pgvalue5_value;
          } else {
            recruit_bid_score_value = unit_pgvalue5_value * pctr_recruit_value;
          }
        }
        if (enable_recruit_live_recruit_pctr_bid == 1) {
          recruit_bid_score_value = pctr_recruit_value;
        }
        if (enable_recruit_live_bid_with_recruit_pcvr == 1) {
          if (recruit_live_bid_with_recruit_pcvr_type == 1) {
            recruit_bid_score_value *= pcvr_recruit_value;
          } else {
            recruit_bid_score_value = pcvr_recruit_value;
          }
        }
        if (enable_recruit_real_bid_user_type == 1) {
          if (recruit_real_bid_user_type_list.size() == recruit_real_bid_user_type_score_list.size()) {
            for (int i = 0; i < recruit_real_bid_user_type_list.size(); ++i) {
              if (uRecruit5RTagKv == recruit_real_bid_user_type_list[i]) {
                recruit_bid_score_value *= recruit_real_bid_user_type_score_list[i];
                break;
              }
            }
          }
        } else {
          if (recruit_offline_user_info.find(recruit_bid_people_type) != std::string::npos ||
              recruit_realtime_action_type.find(recruit_bid_people_type_realtime) != std::string::npos) {
            recruit_bid_score_value *= recruit_predict_bid_real_score_old_user_coeff;
          } else {
            recruit_bid_score_value *= recruit_predict_bid_real_score_coeff;
          }
        }
        if (enable_recruit_bid_use_backlink_model == 1) {
          if (recruit_bid_use_backlink_model_type == 1) {
            recruit_bid_score_value *=
              (1 + recruit_predict_bid_backlink_pxtr_weight * recruit_backlink_pxtr_value);
          } else {
            recruit_bid_score_value *=  recruit_backlink_pxtr_value;
          }
        }
      }  else if (enable_recruit_real_bid_house_score == 1 && is_house_live_value > 0
                  && house_bid_index_value < recruit_bid_house_bucket_num) {
        base::PseudoRandom random(base::GetTimestamp());
        recruit_bid_score_value = (house_bid_index_value + random.GetDouble()) *
                                   recruit_bid_house_max_score / recruit_bid_house_bucket_num;
        recruit_bid_score_value *= house_bid_score_weight;
      }
      if (enable_house_live_cpt_boost) {
        if (enable_house_live_cpt_author_value && liveIsHouseCptNoAAuthor_value) {
          recruit_bid_score_value *= (1 + house_live_cpt_boost_rate);
        }
      }
      if (enable_recruit_live_cpt_boost) {
        if (enable_recruit_live_cpt_author_value && liveIsRecruitCptNoAAuthor_value) {
          recruit_bid_score_value *= (1 + recruit_live_cpt_boost_rate);
        }
      }
      recruit_bid_score(result, recruit_bid_score_value);
    });
    return true;
  }

  static bool CalcRecruitMatchDeliverInfoMatch(const CommonRecoLightFunctionContext &context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
    auto user_province = context.GetStringCommonAttr("game_user_province").value_or("");
    auto user_recruit_deliver_job_id_list = context.GetIntListCommonAttr("user_recruit_deliver_job_id_list");
    auto user_recruit_deliver_job_province_list =
        context.GetStringListCommonAttr("user_recruit_deliver_job_province_list");
    auto recruit_use_deliver_info_type =
        context.GetIntCommonAttr("recruit_use_deliver_info_type").value_or(0);

    auto live_recruit_job_province_list = context.GetStringListItemAttr("live_recruit_job_province");
    auto live_recruit_job_id_list = context.GetStringListItemAttr("lLiveRecruitJobCategoryIdList");

    auto is_match_deliver_info = context.SetIntItemAttr("is_match_deliver_info");

    folly::F14FastSet<std::string> recruit_deliver_job_set;
    if (user_recruit_deliver_job_id_list.has_value()) {
      for (auto job_id : user_recruit_deliver_job_id_list.value()) {
        recruit_deliver_job_set.insert(base::IntToString(job_id));
      }
    }
    folly::F14FastSet<std::string> recruit_deliver_province_set;
    if (user_recruit_deliver_job_province_list.has_value()) {
      for (auto job_province : user_recruit_deliver_job_province_list.value()) {
        std::string str_job_province = job_province.data();
        recruit_deliver_province_set.insert(str_job_province);
        recruit_deliver_province_set.insert(str_job_province + "省");
        recruit_deliver_province_set.insert(str_job_province + "市");
        recruit_deliver_province_set.insert(str_job_province + "自治区");
        recruit_deliver_province_set.insert(str_job_province + "回族自治区");
        recruit_deliver_province_set.insert(str_job_province + "壮族自治区");
        recruit_deliver_province_set.insert(str_job_province + "维吾尔自治区");
      }
    }

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      int is_match_deliver_info_value = 0;
      bool is_province_match = false;
      bool is_job_match = false;
      auto live_recruit_job_province_list_value = live_recruit_job_province_list(result);
      auto live_recruit_job_id_list_value = live_recruit_job_id_list(result);

      if (recruit_deliver_province_set.size() == 0) {
        is_province_match = true;
      } else {
        if (live_recruit_job_province_list_value.has_value()) {
          for (auto job_province : live_recruit_job_province_list_value.value()) {
            if (recruit_deliver_province_set.count(job_province.data()) > 0) {
              is_province_match = true;
              break;
            }
          }
        }
      }

      if (recruit_deliver_job_set.size() == 0) {
        is_job_match = true;
      } else {
        if (live_recruit_job_id_list_value.has_value()) {
          for (auto job_id : live_recruit_job_id_list_value.value()) {
            if (recruit_deliver_job_set.count(job_id.data()) > 0) {
              is_job_match = true;
              break;
            }
          }
        }
      }

      if (recruit_use_deliver_info_type == 0) {
        if ((recruit_deliver_province_set.size() > 0 && is_province_match == true) ||
            (recruit_deliver_job_set.size() > 0 && is_job_match == true) ||
            (recruit_deliver_job_set.size() == 0 && recruit_deliver_province_set.size() == 0)) {
          is_match_deliver_info_value = 1;
        }
      } else if (recruit_use_deliver_info_type == 1) {
        if (is_job_match == true) {
          is_match_deliver_info_value = 1;
        }
      } else if (recruit_use_deliver_info_type == 2) {
        if (is_province_match == true) {
          is_match_deliver_info_value = 1;
        }
      } else if (recruit_use_deliver_info_type == 3) {
        if (recruit_deliver_province_set.size() > 0 && recruit_deliver_job_set.size() > 0) {
          if (is_job_match == true && is_province_match == true) {
            is_match_deliver_info_value = 1;
          }
        } else if (recruit_deliver_province_set.size() > 0 && is_province_match == true) {
          is_match_deliver_info_value = 1;
        } else if (recruit_deliver_job_set.size() > 0 && is_job_match == true) {
          is_match_deliver_info_value = 1;
        } else if (recruit_deliver_province_set.size() == 0 || recruit_deliver_job_set.size() == 0) {
          is_match_deliver_info_value = 1;
        }
      }
      is_match_deliver_info(result, is_match_deliver_info_value);
    });

    return true;
  }

  static bool CalcRecruitLocationMatchFlag(const CommonRecoLightFunctionContext &context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
    auto user_province = context.GetStringCommonAttr("user_province_name").value_or("");  //  山东
    auto user_city = context.GetStringCommonAttr("user_city_name").value_or("");  //  哈尔滨
    std::string user_province_str = user_province.data();
    std::string user_city_str = user_city.data();

    auto job_province = context.GetStringItemAttr("lLiveRecruitExplainJobProvinceString");  //  山东省
    auto job_city_area = context.GetStringItemAttr("lLiveRecruitExplainJobAddressString");
    //  苏州市  •  虎丘区
    auto author_ip_region = context.GetStringItemAttr("ip_region");  //  中国黑龙江哈尔滨

    auto is_recruit_loacation_match = context.SetIntItemAttr("is_recruit_loacation_match");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      int is_recruit_loacation_match_value = 0;
      auto job_province_value = job_province(result).value_or("unknown");
      auto job_city_area_value = job_city_area(result).value_or("unknown");
      auto author_ip_reagion_value = author_ip_region(result).value_or("unknown");
      std::string author_ip_reagion_str = author_ip_reagion_value.data();
      std::string job_province_str = job_province_value.data();
      std::string job_city_area_str = job_city_area_value.data();
      std::string job_city_str = "unknown";
      std::vector<std::string> city_area;
      base::SplitStringWithOptions(job_city_area_str, "•", true, true, &city_area);
      if (city_area.size() > 0) {
        job_city_str = city_area[0];
      }

      if (job_city_str.find(user_city_str) != std::string::npos) {
        if (author_ip_reagion_str.find(user_city_str) != std::string::npos) {
          is_recruit_loacation_match_value = 8;
        } else if (author_ip_reagion_str.find(user_province_str) != std::string::npos) {
          is_recruit_loacation_match_value = 7;
        } else {
          is_recruit_loacation_match_value = 6;
        }
      } else if (job_province_str.find(user_province_str) != std::string::npos) {
        if (author_ip_reagion_str.find(user_city_str) != std::string::npos) {
          is_recruit_loacation_match_value = 5;
        } else if (author_ip_reagion_str.find(user_province_str) != std::string::npos) {
          is_recruit_loacation_match_value = 4;
        } else {
          is_recruit_loacation_match_value = 3;
        }
      } else {
        if (author_ip_reagion_str.find(user_city_str) != std::string::npos) {
          is_recruit_loacation_match_value = 2;
        } else if (author_ip_reagion_str.find(user_province_str) != std::string::npos) {
          is_recruit_loacation_match_value = 1;
        } else {
          is_recruit_loacation_match_value = 0;
        }
      }
      is_recruit_loacation_match(result, is_recruit_loacation_match_value);
    });

    return true;
  }

  static bool CalcRecruitMultiLocationMatchFlag(const CommonRecoLightFunctionContext &context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
    auto user_province = context.GetStringCommonAttr("user_province_name").value_or("");  //  山东
    auto user_city = context.GetStringCommonAttr("user_city_name").value_or("");  //  哈尔滨
    std::string user_province_str = user_province.data();
    std::string user_city_str = user_city.data();

    auto job_province_list = context.GetStringListItemAttr("live_recruit_job_province");
    auto job_city_area_list = context.GetStringListItemAttr("live_recruit_job_address");
    auto job_category_id_list = context.GetStringListItemAttr("lLiveRecruitJobCategoryIdList");
    //  苏州市  •  虎丘区
    auto author_ip_region = context.GetStringItemAttr("ip_region");  //  中国黑龙江哈尔滨

    auto is_recruit_loacation_match = context.SetIntItemAttr("is_recruit_loacation_match");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      int is_recruit_location_match_value = 0;
      int is_recruit_location_match_final_value = 0;
      auto author_ip_reagion_value = author_ip_region(result).value_or("unknown");
      auto job_category_id_list_value = job_category_id_list(result);
      auto job_province_list_value = job_province_list(result);
      auto job_city_area_list_vlaue = job_city_area_list(result);

      std::string author_ip_reagion_str = author_ip_reagion_value.data();

      std::set<std::string> recruit_category_id_set;
      if (job_category_id_list_value.has_value()) {
        for (auto category_id : job_category_id_list_value.value()) {
          recruit_category_id_set.insert(category_id.data());
        }
      }

      if (job_province_list_value.has_value() && job_city_area_list_vlaue.has_value()
        && job_province_list_value.value().size() == job_city_area_list_vlaue.value().size()) {
        for (int i = 0; i < job_province_list_value.value().size(); ++i) {
          std::string job_province_str = job_province_list_value.value()[i].data();
          std::string job_city_area_str = job_city_area_list_vlaue.value()[i].data();
          std::string job_city_str = "unknown";
          std::vector<std::string> city_area;
          base::SplitStringWithOptions(job_city_area_str, "•", true, true, &city_area);
          if (city_area.size() > 0) {
            job_city_str = city_area[0];
          }
          if (job_city_str.find(user_city_str) != std::string::npos) {
            if (author_ip_reagion_str.find(user_city_str) != std::string::npos) {
              is_recruit_location_match_value = 8;
            } else if (author_ip_reagion_str.find(user_province_str) != std::string::npos) {
              is_recruit_location_match_value = 7;
            } else {
              is_recruit_location_match_value = 6;
            }
          } else if (job_province_str.find(user_province_str) != std::string::npos) {
            if (author_ip_reagion_str.find(user_city_str) != std::string::npos) {
              is_recruit_location_match_value = 5;
            } else if (author_ip_reagion_str.find(user_province_str) != std::string::npos) {
              is_recruit_location_match_value = 4;
            } else {
              is_recruit_location_match_value = 3;
            }
          } else {
            if (author_ip_reagion_str.find(user_city_str) != std::string::npos) {
              is_recruit_location_match_value = 2;
            } else if (author_ip_reagion_str.find(user_province_str) != std::string::npos) {
              is_recruit_location_match_value = 1;
            } else {
              is_recruit_location_match_value = 0;
            }
          }
          if (is_recruit_location_match_value > is_recruit_location_match_final_value) {
            is_recruit_location_match_final_value = is_recruit_location_match_value;
          }
          if (recruit_category_id_set.size() > 5) {
            break;
          }
        }
      }
      is_recruit_loacation_match(result, is_recruit_location_match_final_value);
    });

    return true;
  }

  static bool FilterGameRandom(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                               RecoResultConstIter end) {
    auto mmu_tag_lv1_list = context.GetIntListItemAttr("mmu_tag_info.tag_list_lv1");
    auto mmu_tag_lv2_list = context.GetIntListItemAttr("mmu_tag_info.tag_list_lv2");
    auto mmu_tag_lv3_list = context.GetIntListItemAttr("mmu_tag_info.tag_list_lv3");
    auto is_follow_author = context.GetIntItemAttr("is_follow_author_flag");
    auto filter_mmu_tag_list = context.GetIntListCommonAttr("filter_mmu_tag_list");
    auto game_filter_percent = context.GetDoubleCommonAttr("game_filter_percent");
    const double game_filter_percent_val = game_filter_percent.value_or(0.0);
    auto is_game_random_filter_flag = context.SetIntItemAttr("is_game_random_filter_flag");
    base::PseudoRandom random(base::GetTimestamp());

    if (!filter_mmu_tag_list || filter_mmu_tag_list->empty() || game_filter_percent_val < 1e-9) {
      std::for_each(begin, end,
                    [=](const CommonRecoResult &result) { is_game_random_filter_flag(result, 0); });
      return true;
    }
    folly::F14FastSet<int64> filter_mmu_tag_set;
    for (auto mmu_tag : filter_mmu_tag_list.value()) {
      filter_mmu_tag_set.insert(mmu_tag);
    }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int64 is_recruit_mmu_filter_value = 0;
      if (is_follow_author(result).value_or(0) == 1) {
        is_game_random_filter_flag(result, 0);
        return;
      }
      double rand = random.GetDouble();
      for (auto mmu_tag : mmu_tag_lv1_list(result).value_or(absl::Span<const int64>())) {
        if (filter_mmu_tag_set.count(mmu_tag)) {
          if (rand < game_filter_percent_val) {
            is_recruit_mmu_filter_value = 1;
          }
          is_game_random_filter_flag(result, is_recruit_mmu_filter_value);
          return;
        }
      }

      for (auto mmu_tag : mmu_tag_lv2_list(result).value_or(absl::Span<const int64>())) {
        if (filter_mmu_tag_set.count(mmu_tag)) {
          if (rand < game_filter_percent_val) {
            is_recruit_mmu_filter_value = 1;
          }
          is_game_random_filter_flag(result, is_recruit_mmu_filter_value);
          return;
        }
      }

      for (auto mmu_tag : mmu_tag_lv3_list(result).value_or(absl::Span<const int64>())) {
        if (filter_mmu_tag_set.count(mmu_tag)) {
          if (rand < game_filter_percent_val) {
            is_recruit_mmu_filter_value = 1;
          }
          is_game_random_filter_flag(result, is_recruit_mmu_filter_value);
          return;
        }
      }
      is_game_random_filter_flag(result, is_recruit_mmu_filter_value);
    });

    return true;
  }

  static bool FilterAuthorTailList(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    auto author_tail_mod_value = context.GetIntCommonAttr("author_skip_filter_mod_number").value_or(1);
    auto author_tail_value_list = context.GetIntListCommonAttr("author_skip_filter_tail_number_list");
    auto aid = context.GetIntItemAttr("author_id");
    auto is_tail_author_filter_flag = context.SetIntItemAttr("tail_filter_attr_tag");

    if (!author_tail_value_list || author_tail_value_list->empty()) {
      std::for_each(begin, end,
                    [=](const CommonRecoResult &result) { is_tail_author_filter_flag(result, 0); });
      return true;
    }
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int64 remainder = 0;
      int64 hit = 0;
      auto author_id_value = aid(result).value_or(0);
      remainder = author_id_value % author_tail_mod_value;
      for (auto tailId : *author_tail_value_list) {
        if (remainder == tailId) {
          hit = 1;
          break;
        }
      }
      is_tail_author_filter_flag(result, hit);
    });

    return true;
  }

  static bool FilterAuthorWhiteList(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto author_skip_white_list = context.GetIntListCommonAttr("author_skip_filter_aid_white_list");
    auto aid = context.GetIntItemAttr("author_id");
    auto is_white_list_author_filter_flag = context.SetIntItemAttr("whitelist_filter_attr_tag");

    if (!author_skip_white_list || author_skip_white_list->empty()) {
      std::for_each(begin, end,
                    [=](const CommonRecoResult &result) { is_white_list_author_filter_flag(result, 0); });
      return true;
    }
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int64 hit = 0;
      auto author_id_value = aid(result).value_or(0);
      for (auto id : *author_skip_white_list) {
        if (author_id_value == id) {
          hit = 1;
          break;
        }
      }
      is_white_list_author_filter_flag(result, hit);
    });
    return true;
  }

  static bool OverrideItemOffFilterList(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    auto filter_str_list = context.GetStringCommonAttr("author_skip_filter_list").value_or("");
    auto is_skip_filter_tag = context.GetIntItemAttr("is_skip_filter_tag");
    auto item_off_old_filter_list = context.GetIntListItemAttr("item_off_filter_list");
    auto item_off_new_filter_list = context.SetIntListItemAttr("item_off_filter_list");
    std::vector<absl::string_view> filter_params =
        absl::StrSplit(filter_str_list, ",", absl::SkipWhitespace());
    // if filter_params is empty, return as is
    if (filter_params.empty()) {
      return true;
    }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_off_old_filter_list_value =
          item_off_old_filter_list(result).value_or(absl::Span<const int64>());
      auto skip_filter_tag_value = is_skip_filter_tag(result).value_or(0);
      std::vector<int64> ori_filter_list(item_off_old_filter_list_value.begin(),
                                         item_off_old_filter_list_value.end());
      folly::F14FastSet<int64> cur_filter_set(item_off_old_filter_list_value.begin(),
                                              item_off_old_filter_list_value.end());
      if (skip_filter_tag_value > 0) {
        for (auto new_filter_item : filter_params) {
          int64 val = 0;
          if (absl::SimpleAtoi(new_filter_item, &val) && cur_filter_set.find(val) == cur_filter_set.end()) {
            ori_filter_list.push_back(val);
          }
        }
        // update item item_off_filter_list
        item_off_new_filter_list(result, ori_filter_list);
      }
    });
    return true;
  }

  static bool RevenueUAItemOffFilterList(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    auto revenue_ua_off_filter_reason_list =
        context.GetIntListCommonAttr("revenue_ua_off_filter_reason_list").value_or(absl::Span<const int64>());
    auto revenue_ua_off_filter_list =
        context.GetIntListCommonAttr("revenue_ua_off_filter_list").value_or(absl::Span<const int64>());

    auto reason_list_getter = context.GetIntListItemAttr("reason_list");
    auto item_off_filter_list_getter = context.GetIntListItemAttr("item_off_filter_list");
    auto item_off_filter_list_setter = context.SetIntListItemAttr("item_off_filter_list");

    folly::F14FastSet<int64> off_filter_reason_set(revenue_ua_off_filter_reason_list.begin(),
                                                   revenue_ua_off_filter_reason_list.end());

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto reason_list = reason_list_getter(result).value_or(absl::Span<const int64>());

      bool is_target_item = false;
      for (auto reason_tag : reason_list) {
        if (off_filter_reason_set.find(reason_tag) != off_filter_reason_set.end()) {
          is_target_item = true;
          break;
        }
      }

      if (is_target_item) {
        auto off_filter_list = item_off_filter_list_getter(result).value_or(absl::Span<const int64>());

        std::vector<int64> new_off_filter_list(off_filter_list.begin(), off_filter_list.end());
        new_off_filter_list.insert(new_off_filter_list.end(), revenue_ua_off_filter_list.begin(),
                                   revenue_ua_off_filter_list.end());
        folly::F14FastSet<int64> off_filter_set(new_off_filter_list.begin(), new_off_filter_list.end());
        new_off_filter_list.assign(off_filter_set.begin(), off_filter_set.end());

        item_off_filter_list_setter(result, new_off_filter_list);
      }
    });
    return true;
  }

  static bool ResearchRedirectitemOffBrowsetFilter(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    auto research_redirect_item_skip_filter_reason_list =
        context.GetIntListCommonAttr("research_redirect_item_skip_filter_reason_list");
    auto research_redirect_item_skip_filter_list =
        context.GetIntListCommonAttr("research_redirect_item_skip_filter_list");

    auto reason_list_getter = context.GetIntListItemAttr("reason_list");
    auto item_off_filter_list_getter = context.GetIntListItemAttr("item_off_filter_list");
    auto item_off_filter_list_setter = context.SetIntListItemAttr("item_off_filter_list");

    if (!research_redirect_item_skip_filter_reason_list ||
             research_redirect_item_skip_filter_reason_list->empty() ||
             !research_redirect_item_skip_filter_list ||
             research_redirect_item_skip_filter_list->empty()) {
      return true;
    }

    folly::F14FastSet<int64> off_filter_reason_set(research_redirect_item_skip_filter_reason_list->begin(),
                                                   research_redirect_item_skip_filter_reason_list->end());

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto reason_list = reason_list_getter(result).value_or(absl::Span<const int64>());

      bool is_target_item = false;
      for (auto reason_tag : reason_list) {
        if (off_filter_reason_set.find(reason_tag) != off_filter_reason_set.end()) {
          is_target_item = true;
          break;
        }
      }
      if (is_target_item && research_redirect_item_skip_filter_list->size() == 1) {
        auto off_filter_list = item_off_filter_list_getter(result).value_or(absl::Span<const int64>());
        std::vector<int64> new_off_filter_list(off_filter_list.begin(), off_filter_list.end());
        new_off_filter_list.push_back((*research_redirect_item_skip_filter_list)[0]);
        item_off_filter_list_setter(result, new_off_filter_list);
      }
    });
    return true;
  }
  static bool CalcCondSkipNegFilterFlag(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    auto check_cond_skip_flags = context.GetStringCommonAttr("check_cond_skip_neg_str").value_or("");
    auto item_neg_info_str = context.GetStringItemAttr("punish_sirius_all");
    auto cond_skip_neg_tag = context.SetIntItemAttr("cond_skip_neg_tag");
    auto item_skip_neg_tag = context.GetStringItemAttr("item_skip_neg_tag");

    auto fill_item_set = [](const absl::string_view &ori_str, folly::F14FastSet<int64> *set_ptr) {
      if (set_ptr == nullptr || ori_str.empty()) {
        return;
      }
      std::vector<absl::string_view> vec = absl::StrSplit(ori_str, ",", absl::SkipWhitespace());
      for (auto item : vec) {
        int64 flag = 0;
        if (absl::SimpleAtoi(item, &flag)) {
          set_ptr->insert(flag);
        }
      }
    };
    folly::F14FastSet<int64> cond_flag_set;
    fill_item_set(check_cond_skip_flags, &cond_flag_set);

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_neg_info_str_val = item_neg_info_str(result).value_or("");
      std::vector<absl::string_view> item_neg_info_vec =
          absl::StrSplit(item_neg_info_str_val, ",", absl::SkipWhitespace());
      if (!item_neg_info_vec.empty()) {
        auto item_skip_neg_tag_str = item_skip_neg_tag(result).value_or("");
        folly::F14FastSet<int64> item_cond_flag_set;
        fill_item_set(item_skip_neg_tag_str, &item_cond_flag_set);
        int64 can_be_exempt = 0;
        for (auto neg_info : item_neg_info_vec) {
          std::vector<absl::string_view> info_vec = absl::StrSplit(neg_info, ":", absl::SkipWhitespace());
          int64 tag_id = 0;
          if (info_vec.empty() || info_vec.size() < 3) continue;
          if (absl::SimpleAtoi(info_vec[0], &tag_id) && (cond_flag_set.find(tag_id) != cond_flag_set.end()) ||
              item_cond_flag_set.find(tag_id) != item_cond_flag_set.end()) {
            can_be_exempt = 1;
            break;
          }
        }
        cond_skip_neg_tag(result, can_be_exempt);
      } else {
        cond_skip_neg_tag(result, 0);
      }
    });
    return true;
  }

  static bool CalcNewNegSysFilterFlag(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto scene_effect_str = context.GetStringCommonAttr("new_neg_sys_scene_effect_str").value_or("");
    auto item_neg_info_str = context.GetStringItemAttr("punish_sirius_all");
    auto only_punish_tag_str = context.GetStringCommonAttr("new_neg_sys_only_punish_tag_str").value_or("");
    auto exempt_tag_str = context.GetStringCommonAttr("new_neg_sys_exempt_tag_str").value_or("");
    auto new_neg_sys_filter_info = context.SetIntItemAttr("new_neg_sys_filter_info");

    std::vector<absl::string_view> scene_effect_vec =
        absl::StrSplit(scene_effect_str, ",", absl::SkipWhitespace());
    std::vector<absl::string_view> exempt_tag_vec =
        absl::StrSplit(exempt_tag_str, ",", absl::SkipWhitespace());
    std::vector<absl::string_view> only_punish_tag_vec =
        absl::StrSplit(only_punish_tag_str, ",", absl::SkipWhitespace());

    if (scene_effect_vec.empty()) {
      return true;
    }

    auto fill_item_set = [](const std::vector<absl::string_view> &vec, folly::F14FastSet<int64> *set_ptr) {
      if (set_ptr == nullptr || vec.empty()) {
        return;
      }
      for (auto item : vec) {
        int64 flag = 0;
        if (absl::SimpleAtoi(item, &flag)) {
          set_ptr->insert(flag);
        }
      }
    };
    folly::F14FastSet<int64> scene_effect_set;
    folly::F14FastSet<int64> exempt_tag_set;
    folly::F14FastSet<int64> only_punish_tag_set;
    fill_item_set(scene_effect_vec, &scene_effect_set);
    fill_item_set(exempt_tag_vec, &exempt_tag_set);
    fill_item_set(only_punish_tag_vec, &only_punish_tag_set);

    base::PseudoRandom random(base::GetTimestamp());
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_neg_info_str_val = item_neg_info_str(result).value_or("");
      std::vector<absl::string_view> item_neg_info_vec =
          absl::StrSplit(item_neg_info_str_val, ",", absl::SkipWhitespace());
      bool is_punish = false;
      if (!item_neg_info_vec.empty()) {
        for (auto neg_info : item_neg_info_vec) {
          std::vector<std::string> info_vec = absl::StrSplit(neg_info, ":", absl::SkipWhitespace());
          int64 tag_id = 0;
          int64 scene_id = 0;
          if (info_vec.empty() || info_vec.size() < 3) continue;
          if (!absl::SimpleAtoi(info_vec[0], &tag_id) || !absl::SimpleAtoi(info_vec[1], &scene_id)) continue;
          // 不属于该场景 或者命中豁免
          if (scene_effect_set.find(scene_id) == scene_effect_set.end() ||
              exempt_tag_set.find(tag_id) != exempt_tag_set.end() ||
              (!only_punish_tag_set.empty() && only_punish_tag_set.find(tag_id) == only_punish_tag_set.end()))
            continue;
          std::string data_str = info_vec[2];
          if (data_str.back() == '%') {
            data_str = data_str.substr(0, data_str.size() - 1);
          }
          double raw_data = 0.0;
          if (!absl::SimpleAtod(data_str, &raw_data)) continue;
          if (raw_data < 100.0) {
            raw_data /= 100.0;
          } else {
            raw_data = 1.0;
          }
          double rand = random.GetDouble();
          is_punish = is_punish || (rand > raw_data);
        }
        new_neg_sys_filter_info(result, is_punish ? 1 : 0);
      } else {
        new_neg_sys_filter_info(result, 0);
      }
    });
    return true;
  }

  static bool CertainReasonBoostMc(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    double certain_reason_boost_coeff =
             context.GetDoubleCommonAttr("certain_reason_boost_coeff").value_or(1.0);
    auto enable_inner_reason_boost = context.GetIntCommonAttr("enable_inner_reason_boost").value_or(0);
    auto is_inner_induce_moment = context.GetIntCommonAttr("is_inner_induce_moment").value_or(0);
    double inner_reason_boost_coeff = context.GetDoubleCommonAttr("inner_reason_boost_coeff").value_or(1.0);
    auto enable_click_ua_boost = context.GetIntCommonAttr("enable_click_ua_boost").value_or(0);
    auto is_click_ua_boost_moment = context.GetIntCommonAttr("is_click_ua_boost_moment").value_or(0);
    double click_ua_boost_coeff = context.GetDoubleCommonAttr("click_ua_boost_coeff").value_or(1.0);
    auto is_need_boost_tag = context.GetIntItemAttr("is_need_boost_tag");
    auto inner_boost_flag = context.GetIntItemAttr("inner_boost_flag");
    auto is_click_author = context.GetIntItemAttr("is_click_author");
    auto get_score = context.GetDoubleItemAttr("mc_csqs_exploitation");
    auto set_to_score = context.SetDoubleItemAttr("mc_csqs_exploitation");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double score = get_score(result).value_or(0.0);
      if (is_need_boost_tag(result).value_or(0)) {
        score *= certain_reason_boost_coeff;
      }
      if (enable_inner_reason_boost == 1 && is_inner_induce_moment == 1
      && inner_boost_flag(result).value_or(0) == 1) {
        score *= inner_reason_boost_coeff;
      }
      if (enable_click_ua_boost == 1 && is_click_ua_boost_moment == 1
      && is_click_author(result).value_or(0) == 1) {
        score *= click_ua_boost_coeff;
      }
      set_to_score(result, score);
    });

    return true;
  }

  static bool McMoiveTagDeBoostMc(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    double movie_boost_v1_coeff =
             context.GetDoubleCommonAttr("mc_movie_tag_v1_deboost_coeff").value_or(1.0);
    double movie_boost_v2_coeff =
             context.GetDoubleCommonAttr("mc_movie_tag_v2_deboost_coeff").value_or(1.0);
    double movie_boost_v3_coeff =
             context.GetDoubleCommonAttr("mc_movie_tag_v3_deboost_coeff").value_or(1.0);
    auto is_movie_tag_v1 = context.GetIntItemAttr("is_movie_tag");
    auto is_movie_tag_v2 = context.GetIntItemAttr("is_movie_tag_v2");
    auto is_movie_tag_v3 = context.GetIntItemAttr("is_movie_tag_v3");
    auto get_score = context.GetDoubleItemAttr("mc_csqs_exploitation");
    auto set_to_score = context.SetDoubleItemAttr("mc_csqs_exploitation");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double score = get_score(result).value_or(0.0);
      auto is_movie_tag_v1_val = is_movie_tag_v1(result).value_or(0);
      auto is_movie_tag_v2_val = is_movie_tag_v2(result).value_or(0);
      auto is_movie_tag_v3_val = is_movie_tag_v3(result).value_or(0);
      if (is_movie_tag_v1_val > 0) {
        score *= movie_boost_v1_coeff;
      }
      if (is_movie_tag_v2_val > 0) {
        score *= movie_boost_v2_coeff;
      }
      if (is_movie_tag_v3_val > 0) {
        score *= movie_boost_v3_coeff;
      }
      set_to_score(result, score);
    });
    return true;
  }

  static bool CalcEngagementScore(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto mc_pctr = context.GetDoubleItemAttr("mc_pctr");
    auto mc_pltr = context.GetDoubleItemAttr("mc_pltr");
    auto mc_pwtr = context.GetDoubleItemAttr("mc_pwtr");
    auto mc_plvtr = context.GetDoubleItemAttr("mc_plvtr");
    auto mc_plvtr2 = context.GetDoubleItemAttr("mc_plvtr2");
    auto mc_psvtr = context.GetDoubleItemAttr("mc_psvtr");
    auto mc_pnctr = context.GetDoubleItemAttr("mc_pnctr");
    auto mc_pwatchtime = context.GetDoubleItemAttr("mc_pwatchtime");

    auto mc_engage_weight_str =
        context.GetStringCommonAttr("mc_engage_weight_str")
            .value_or("ctr:1.0,wtr:50.0,lvtr:40.0,lvtr2:20.0,svtr:0.5,watchtime:20.0,svtr2:20.0,nctr:50.0");
    folly::F14FastMap<std::string, double> mc_engage_weight_map;
    if (!livestream::ParseWeightParam(std::string(mc_engage_weight_str), ",", &mc_engage_weight_map)) {
      mc_engage_weight_map["ctr"] = 1.0;
    }
    std::vector<std::string> need_engage_weight_param_names;
    need_engage_weight_param_names = {"ctr",      "lvtr",     "lvtr2",     "wt",        "inwt",  "gtr",
                                      "wtr",      "gv_1",     "gv_2",      "watchtime", "svtr2", "nctr",
                                      "gift_wtr", "gift_gtr", "gift_gv_1", "ltr"};
    for (const std::string &name : need_engage_weight_param_names) {
      if (mc_engage_weight_map.find(name) == mc_engage_weight_map.end()) {
        mc_engage_weight_map[name] = 0.0;
      }
    }

    // 传出字段使用默认的 exploitation 队列分数
    auto mc_csqs_exploitation = context.SetDoubleItemAttr("mc_csqs_exploitation");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double mc_score = 0.0;
      auto mc_pctr_value = mc_pctr(result).value_or(0.0);
      auto mc_pltr_value = mc_pltr(result).value_or(0.0);
      auto mc_pwtr_value = mc_pwtr(result).value_or(0.0);
      auto mc_plvtr_value = mc_plvtr(result).value_or(0.0);
      auto mc_plvtr2_value = mc_plvtr2(result).value_or(0.0);
      auto mc_psvtr_value = mc_psvtr(result).value_or(0.0);
      auto mc_pnctr_value = mc_pnctr(result).value_or(0.0);
      auto mc_pwatchtime_value = mc_pwatchtime(result).value_or(0.0);
      double svtr_discount = 1.0 - 0.9 * std::pow(mc_psvtr_value, 0.5);
      if (abs(svtr_discount) < 1e-8) {
        svtr_discount = 1.0;
      }

      mc_score = mc_pctr_value * svtr_discount *
                     (mc_engage_weight_map.at("ctr") + mc_engage_weight_map.at("wtr") * mc_pwtr_value +
                      mc_engage_weight_map.at("lvtr") * mc_plvtr_value +
                      mc_engage_weight_map.at("lvtr2") * mc_plvtr2_value +
                      mc_engage_weight_map.at("watchtime") * mc_pwatchtime_value +
                      mc_engage_weight_map.at("svtr2") * mc_psvtr_value) +
                 mc_engage_weight_map.at("nctr") * mc_pnctr_value;
      mc_csqs_exploitation(result, mc_score);
    });

    return true;
  }

  static bool FilterUnfollow(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                             RecoResultConstIter end) {
    auto unfollow_aid_list = context.GetIntListCommonAttr("unfollow_aid_list");
    auto unfollow_aid_ts_list = context.GetIntListCommonAttr("unfollow_aid_ts_list");
    auto enable_all_unfollow_filter = context.GetIntCommonAttr("enable_all_unfollow_filter").value_or(0);
    auto filter_unfollow_aid_interval =
        context.GetIntCommonAttr("filter_unfollow_aid_interval").value_or(168);
    auto author_id = context.GetIntItemAttr("author_id");
    auto should_filter_flag = context.SetIntItemAttr("should_filter_flag");

    if (!unfollow_aid_list || unfollow_aid_list->empty() || !unfollow_aid_ts_list ||
        unfollow_aid_ts_list->empty()) {
      std::for_each(begin, end, [=](const CommonRecoResult &result) { should_filter_flag(result, 0); });
      return true;
    }

    if (unfollow_aid_list->size() != unfollow_aid_ts_list->size()) {
      std::for_each(begin, end, [=](const CommonRecoResult &result) { should_filter_flag(result, 0); });
      return true;
    }

    folly::F14FastMap<int64, folly::F14FastSet<int64>> aid_unfollow_ts_map;
    for (int i = 0; i < unfollow_aid_list->size(); i++) {
      aid_unfollow_ts_map[(*unfollow_aid_list)[i]].insert((*unfollow_aid_ts_list)[i]);
    }

    int64 start_ts = base::GetTimestamp() / 1000 - filter_unfollow_aid_interval * 3600 * 1000;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto aid = author_id(result).value_or(-1);
      if (aid_unfollow_ts_map.find(aid) == aid_unfollow_ts_map.end()) {
        should_filter_flag(result, 0);
        return;
      }

      int64 should_filter_flag_value = 0;
      for (const auto &ts : aid_unfollow_ts_map[aid]) {
        if (enable_all_unfollow_filter == 1 || ts >= start_ts) {
          should_filter_flag_value = 1;
          break;
        }
      }
      should_filter_flag(result, should_filter_flag_value);
    });

    return true;
  }

  static bool GenerateItemProvinceKey(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto game_user_province = context.GetStringCommonAttr("game_user_province").value_or("no_user_province");
    auto author_province = context.GetStringItemAttr("lLiveLocationProvince");
    auto item_province_key_setter = context.SetStringItemAttr("item_province_key");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto author_province_value = author_province(result).value_or("author_province");
      std::string item_province_key_value =
          absl::StrFormat("%s.%s", game_user_province, author_province_value);
      item_province_key_setter(result, item_province_key_value);
    });
    return true;
  }

  static bool CalcRealFilterFlag(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto cur_filter_flag = context.GetIntCommonAttr("cur_filter_flag").value_or(-1);
    auto filter_flag_item_attr_name =
        context.GetStringCommonAttr("filter_flag_item_attr_name").value_or("unkown");
    auto skip_filter_item_attr_name =
        context.GetStringCommonAttr("skip_filter_item_attr_name").value_or("unkown");

    auto item_off_filter_list_getter = context.GetIntListItemAttr("item_off_filter_list");
    auto filter_flag_item_attr_getter = context.GetIntItemAttr(filter_flag_item_attr_name);
    auto filter_flag_item_attr_setter = context.SetIntItemAttr(filter_flag_item_attr_name);
    auto skip_filter_item_attr_setter = context.SetIntItemAttr(skip_filter_item_attr_name);

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      int ori_filter_flag = filter_flag_item_attr_getter(result).value_or(0);
      if (ori_filter_flag == 0) {
        filter_flag_item_attr_setter(result, 0);
        skip_filter_item_attr_setter(result, 0);
        return;
      }
      auto item_off_filter_list = item_off_filter_list_getter(result).value_or(absl::Span<const int64>());
      for (auto off_flag : item_off_filter_list) {
        if (cur_filter_flag == off_flag) {
          filter_flag_item_attr_setter(result, 0);
          skip_filter_item_attr_setter(result, 1);
          return;
        }
      }
      filter_flag_item_attr_setter(result, 1);
      skip_filter_item_attr_setter(result, 0);
    });
    return true;
  }

  static bool CalcExploreForceEmpty(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto force_need_leaf_result = context.GetIntCommonAttr("force_need_leaf_result").value_or(0);
    auto top_mc_pctr_after_cs = context.GetDoubleCommonAttr("top_mc_pctr_after_cs").value_or(0.0);
    auto force_empty_rate = context.GetIntCommonAttr("explore_mc_force_empty_rate").value_or(20);
    context.SetIntCommonAttr("explore_enable_force_empty", 0);
    if (force_need_leaf_result == 1) {
      return true;
    }
    ks::infra::RedisClient *static_redis_client_ = nullptr;
    if (static_redis_client_ == nullptr) {
      auto *client_mgr = ks::serving_util::InfraReidsClient::Singleton();
      if (!client_mgr) {
        return true;
      }

      static_redis_client_ = client_mgr->GetClientKccFromKconf("recoExploreLiveAutoPlay");
      if (!static_redis_client_) {
        return true;
      }
    }
    std::vector<std::string> ctr_vec = {std::to_string(top_mc_pctr_after_cs)};
    int64 list_length;
    auto err_code =
        static_redis_client_->ListPushRight("explore_live_mc_store_v2", ctr_vec, &list_length, 50);
    if (err_code) {
      return true;
    }

    if (list_length > 100000) {
      err_code = static_redis_client_->ListTrim("explore_live_mc_store_v2", 0 - 100000, -1, 50);
      if (err_code) {
        return true;
      }
    }

    std::string value_buffer;
    err_code = static_redis_client_->Get("explore_live_mc_read_v2", &value_buffer, 50);
    if (err_code) {
      return true;
    }

    std::vector<std::string> string_percents;
    base::SplitStringWithOptions(value_buffer, ",", true, true, &string_percents);
    if (string_percents.size() != 100) {
      return true;
    }

    double ctr_threhold;
    if (absl::SimpleAtod(string_percents[force_empty_rate], &ctr_threhold)
                         && top_mc_pctr_after_cs <= ctr_threhold) {
      context.SetIntCommonAttr("explore_enable_force_empty", 1);
    }
    return true;
  }

  // 分级分发短时打压功能
  static bool CalcGradeDistributeMatchForTemp(const CommonRecoLightFunctionContext &context,
                                              RecoResultConstIter begin, RecoResultConstIter end) {
    auto use_aid_grade_label = context.GetIntCommonAttr("temp_grade_use_aid_label").value_or(0);
    auto grade_rule_version = static_cast<std::string>(
        context.GetStringCommonAttr("grade_distribute_rule_version").value_or("default"));
    auto grade_label_for_temp = context.GetIntListItemAttr("grade_label_for_temp");
    auto grade_label_for_aid_temp = context.GetIntListItemAttr("grade_label_for_aid_temp");
    auto item_label_version = context.GetIntCommonAttr("grade_distribute_item_label_version").value_or(0);
    auto temp_grade_dis_effect_way = context.GetIntCommonAttr("temp_grade_dis_effect_way").value_or(0);
    auto punish_tag_for_temp_grade_res = context.GetIntItemAttr("punish_tag_for_temp_grade");
    auto punish_tag_for_temp_grade = context.SetIntItemAttr("punish_tag_for_temp_grade");
    auto grade_label_rules_kconf_str =
        static_cast<std::string>(context.GetStringCommonAttr("grade_label_rules_kconf_str")
                                     .value_or("reco.live.onlineLiveGradeLabelRules"));
    auto grade_label_cate_kconf_str =
        static_cast<std::string>(context.GetStringCommonAttr("grade_label_cate_kconf_str")
                                     .value_or("audit.liveStream.auditLiveGradingTagInfo"));

    // 传入 userinfo 输出有问题 先采用这种方式填充
    auto uinfo_gender = context.GetIntCommonAttr("uinfo_gender").value_or(-1);
    auto uinfo_age_seg = context.GetIntCommonAttr("uinfo_age_seg").value_or(-1);
    auto uinfo_city_level = context.GetStringCommonAttr("uinfo_city_level").value_or("");
    auto uinfo_os = context.GetIntCommonAttr("uinfo_os").value_or(-1);
    auto uinfo_price = context.GetIntCommonAttr("uinfo_price").value_or(-1);
    auto uinfo_act30d = context.GetIntCommonAttr("uinfo_act30d").value_or(-1);
    auto uinfo_is_reflux = context.GetIntCommonAttr("uinfo_is_reflux").value_or(0);
    auto uinfo_is_la = context.GetIntCommonAttr("uinfo_is_la").value_or(0);
    auto uinfo_is_hp = context.GetIntCommonAttr("uinfo_is_hp").value_or(0);
    auto uinfo_is_tnu = context.GetIntCommonAttr("uinfo_is_tnu").value_or(0);

    UserLabel cur_user_label;
    cur_user_label.gender = uinfo_gender;
    cur_user_label.age = uinfo_age_seg;
    cur_user_label.city = static_cast<std::string>(uinfo_city_level);
    cur_user_label.os = uinfo_os;
    cur_user_label.price = uinfo_price;
    cur_user_label.act_30d = uinfo_act30d;
    cur_user_label.is_reflux = uinfo_is_reflux;
    cur_user_label.is_la = uinfo_is_la;
    cur_user_label.is_hp = uinfo_is_hp;
    cur_user_label.is_tnu = uinfo_is_tnu;

    GradeDistributeHelperV2::GetInstance()->Init(grade_label_rules_kconf_str, grade_label_cate_kconf_str);
    GradeDistributeMatcherV2 matcher;
    if (grade_rule_version.empty() || !matcher.Init(grade_rule_version, cur_user_label)) {
      CL_LOG_ERROR("light_function", "CalcGradeDistributeMatchForTemp")
          << "Grade distribute matcher error version = " << grade_rule_version;
      return false;
    }

    int64 cur_ts = base::GetTimestamp() / 1000000;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto cur_punish_tag = punish_tag_for_temp_grade_res(result).value_or(0);
      if (cur_punish_tag == 1) return;
      absl::Span<const int64> label_list;
      if (use_aid_grade_label == 0) {
        label_list = grade_label_for_temp(result).value_or(absl::Span<const int64>());
      } else {
        label_list = grade_label_for_aid_temp(result).value_or(absl::Span<const int64>());
      }
      cate_info_t cate_info_vec;
      if (label_list.size() > 2) {
        int i = 0;
        while (i < label_list.size()) {
          int sub_list_size = label_list[i];
          if (sub_list_size > 2 && i + sub_list_size <= label_list.size()) {
            punish_info_t punish_info;
            int64 category = label_list[i + 1];
            int time_len = (sub_list_size - 2) / 2;
            for (int sub_i = 0; sub_i < time_len; ++sub_i) {
              int left_idx = i + 2 + sub_i;
              int right_idx = i + 2 + time_len + sub_i;
              punish_info.emplace_back(label_list[left_idx], label_list[right_idx]);
            }
            cate_info_vec.emplace_back(category, std::move(punish_info));
          }
          i += sub_list_size;
        }
        if (!cate_info_vec.empty()) {
          auto res =
              matcher.ItemShouldFilterForTemp(cate_info_vec, item_label_version, temp_grade_dis_effect_way);
          if (res.first) {
            base::perfutil::PerfUtilWrapper::IntervalLogStash(1, kPerfNs, "livestream.grade_dist_for_temp",
                                                              std::to_string(res.second));
            punish_tag_for_temp_grade(result, 1);
          }
        }
      }
    });

    return true;
  }

  static bool CalcMcRevenueGiftValue(const CommonRecoLightFunctionContext &context,
                                              RecoResultConstIter begin,
                                              RecoResultConstIter end) {
    auto mc_pgtr_list = context.GetDoubleListItemAttr("mc_pgtr_list");
    auto mc_gvalue_label_list_value = context.GetDoubleListCommonAttr("mc_gvalue_label_list");
    auto mc_gtr = context.SetDoubleItemAttr("mc_gtr");
    auto mc_gvalue = context.SetDoubleItemAttr("mc_gvalue");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto mc_pgtr_list_value = mc_pgtr_list(result);
      if (mc_pgtr_list_value.has_value() && mc_gvalue_label_list_value.has_value() &&
          mc_pgtr_list_value.value().size() - 1 == mc_gvalue_label_list_value.value().size()) {
        double sum_gtr = 0.0;
        double gvalue = 0.0;
        double gtr = 0.0;
        std::vector<double> gtr_list(14);
        int index = 0;
        for (auto mc_pgtr : mc_pgtr_list_value.value()) {
          gtr_list[index] = exp(mc_pgtr);
          sum_gtr += gtr_list[index];
          ++index;
        }
        index = 1;
        for (auto mc_gvalue_label : mc_gvalue_label_list_value.value()) {
          gvalue += (gtr_list[index] / sum_gtr) * mc_gvalue_label;
          ++index;
        }
        gtr = 1 - (gtr_list[0] / sum_gtr);
        mc_gtr(result, gtr);
        mc_gvalue(result, gvalue);
      }
    });
    return true;
  }

  static bool CalcGreenPassLivesInfo(const CommonRecoLightFunctionContext &context,
                                              RecoResultConstIter begin,
                                              RecoResultConstIter end) {
    // input commonAttrs
    auto greenPassLiveIdsStr = context.GetStringCommonAttr("green_pass_live_ids_str").value_or("");
    auto greenPassTotalServerShowsStr =
      context.GetStringCommonAttr("green_pass_total_server_shows_str").value_or("");
    auto greenPassBoostCoeffFirstStageStr =
      context.GetStringCommonAttr("green_pass_boost_coeff_first_stage_str").value_or("");
    auto greenPassBoostCoeffFecondStageStr =
      context.GetStringCommonAttr("green_pass_boost_coeff_second_stage_str").value_or("");
    auto greenPassCurServerShow =
      context.GetIntCommonAttr("green_pass_cur_server_show").value_or(0);
    auto enableGreenPassSelectReason =
      context.GetIntCommonAttr("enable_green_pass_select_reason").value_or(0);
    // input itemAttrs
    auto green_pass_enable_reason_count =
      context.GetIntItemAttr("green_pass_enable_reason_count");
    // output itemAttrs
    auto green_pass_boost_coeff_first_stage = context.SetDoubleItemAttr("green_pass_boost_coeff_first_stage");
    auto green_pass_boost_coeff_second_stage =
      context.SetDoubleItemAttr("green_pass_boost_coeff_second_stage");
    // 异常判断
    if (
      greenPassLiveIdsStr == "" || greenPassTotalServerShowsStr == "" ||
      greenPassBoostCoeffFirstStageStr == "" || greenPassBoostCoeffFecondStageStr == ""
    ) {
      return true;
    }
    std::vector<absl::string_view> liveIdsStrVec =
        absl::StrSplit(greenPassLiveIdsStr, ";", absl::SkipWhitespace());
    std::vector<absl::string_view> totalServerShowsStrVec =
        absl::StrSplit(greenPassTotalServerShowsStr, ";", absl::SkipWhitespace());
    std::vector<absl::string_view> firstStageStrVec =
        absl::StrSplit(greenPassBoostCoeffFirstStageStr, ";", absl::SkipWhitespace());
    std::vector<absl::string_view> secondStageStrVec =
        absl::StrSplit(greenPassBoostCoeffFecondStageStr, ";", absl::SkipWhitespace());
    // 异常判断
    if (
      liveIdsStrVec.size() == 0 ||
      liveIdsStrVec.size() != totalServerShowsStrVec.size() ||
      totalServerShowsStrVec.size() != firstStageStrVec.size() ||
      firstStageStrVec.size() != secondStageStrVec.size()
    ) {
      return true;
    }
    // 遍历筛选打标
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto liveId = result.item_key;
      double targetLiveId, totalServerShow;
      double firstCoeff, secondCoeff;
      for (int i = 0; i < liveIdsStrVec.size(); i++) {
        if (
          absl::SimpleAtod(liveIdsStrVec[i], &targetLiveId) &&
          liveId == targetLiveId &&
          absl::SimpleAtod(totalServerShowsStrVec[i], &totalServerShow) &&
          absl::SimpleAtod(firstStageStrVec[i], &firstCoeff) &&
          absl::SimpleAtod(secondStageStrVec[i], &secondCoeff) &&
          totalServerShow - greenPassCurServerShow > 0
        ) {
          base::perfutil::PerfUtilWrapper::IntervalLogStash(1, "livestream.green_pass_lives", "target_id_cnt",
                                                              std::to_string(liveId));
          if (enableGreenPassSelectReason) {
            auto reason_cnt = green_pass_enable_reason_count(result).value_or(0);
            if (reason_cnt > 0) {
              green_pass_boost_coeff_first_stage(result, firstCoeff);
              green_pass_boost_coeff_second_stage(result, secondCoeff);
            }
          } else {
            green_pass_boost_coeff_first_stage(result, firstCoeff);
            green_pass_boost_coeff_second_stage(result, secondCoeff);
          }
        }
      }
    });
    return true;
  }

  static bool CalcRecheableLiveList(const CommonRecoLightFunctionContext &context,
                                              RecoResultConstIter begin,
                                              RecoResultConstIter end) {
    timeb t;
    ftime(&t);
    int64 cur_time = t.time * 1000 + t.millitm;
    auto infoString = context.GetStringCommonAttr("infoString").value_or("");
    auto user_lat = context.GetDoubleCommonAttr("user_lat").value_or(0.);
    auto user_lon = context.GetDoubleCommonAttr("user_lon").value_or(0.);
    int64 max_location_num = context.GetIntCommonAttr("maxmum_location_num_int").value_or(0);
    if (infoString == "" || user_lat <= 0 || user_lon <= 0 || max_location_num <= 0) {
      return true;
    }
    double PI = 3.1415926535897932384626433832795;
    double EARTH_RADIUS = 6378.137;
    // vectors for pass lives
    std::vector<int64> final_pass_lives;
    std::vector<int64> final_pass_start_times;
    std::vector<int64> final_pass_durations;
    std::vector<int64> final_pass_total_vvs;
    // temp vectors for saving storage
    std::vector<absl::string_view> planVec =
        absl::StrSplit(infoString, ";", absl::SkipWhitespace());
    std::vector<int64> live_ids;
    std::vector<int64> limitations;
    std::vector<absl::string_view> locations;
    std::vector<absl::string_view> infoVec;
    std::vector<int64> start_times;
    std::vector<int64> durations;
    std::vector<int64> total_vvs;
    for (const auto &plan : planVec) {
      if (plan == "") {
        continue;
      }
      infoVec =
        absl::StrSplit(plan, ":", absl::SkipWhitespace());
      if (infoVec.size() != 7) {
        CL_LOG_ERROR("light_function", "CalcRecheableLiveList")
          << "Pase string error";
        continue;
      }
      int64 live_id;
      int64 limitation;
      int64 start_time;
      int64 duration;
      int64 total_vv;
      if (absl::SimpleAtoi(infoVec[0], &live_id) && absl::SimpleAtoi(infoVec[2], &limitation) &&
            absl::SimpleAtoi(infoVec[4], &start_time) && absl::SimpleAtoi(infoVec[5], &duration) &&
              absl::SimpleAtoi(infoVec[6], &total_vv)) {
        if (cur_time - start_time > duration) {
          continue;
        }
        if (infoVec[1] == "false" || infoVec[1] == "False") {
          final_pass_lives.push_back(live_id);
          final_pass_start_times.push_back(start_time);
          final_pass_durations.push_back(duration);
          final_pass_total_vvs.push_back(total_vv);
          continue;
        }
        live_ids.push_back(live_id);
        limitations.push_back(limitation);
        locations.push_back(infoVec[3]);
        start_times.push_back(start_time);
        durations.push_back(duration);
        total_vvs.push_back(total_vv);
      }
    }

    if (live_ids.size() != limitations.size() || live_ids.size() != locations.size() ||
          live_ids.size() != start_times.size() || live_ids.size() != durations.size() ||
            live_ids.size() != total_vvs.size()) {
      CL_LOG_ERROR("light_function", "CalcRecheableLiveList")
          << "Pase string error";
      return true;
    }

    std::vector<absl::string_view> location_vec;
    std::vector<absl::string_view> lon_lat;
    double lon, lat, dx, dy, b, lx, ly, s;
    std::random_device rng;
    std::mt19937 urng(rng());
    for (int i = 0; i < live_ids.size(); i++) {
      if (locations[i] == "") {
        continue;
      }
      location_vec = absl::StrSplit(locations[i], ",", absl::SkipWhitespace());
      std::shuffle(location_vec.begin(), location_vec.end(), urng);
      location_vec.resize(max_location_num);
      for (const auto &info : location_vec) {
        if (info == "") {
          continue;
        }
        lon_lat = absl::StrSplit(info, "-", absl::SkipWhitespace());
        if (lon_lat.size() == 2 && absl::SimpleAtod(lon_lat[0], &lon) &&
              absl::SimpleAtod(lon_lat[1], &lat)) {
          dx = user_lon - lon;
          dy = user_lat - lat;
          b = (user_lat + lat) * 0.5;
          lx = (0.05 * b * b * b - 19.16 * b * b + 47.13 * b + 110966) * dx;
          ly = (17 * b + 110352) * dy;
          s = sqrt(lx * lx + ly * ly);
          if (s <= limitations[i]) {
            final_pass_lives.push_back(live_ids[i]);
            final_pass_start_times.push_back(start_times[i]);
            final_pass_durations.push_back(durations[i]);
            final_pass_total_vvs.push_back(total_vvs[i]);
            break;
          }
        }
      }
    }
    context.SetIntListCommonAttr("final_pass_lives_int_list",
                                std::move(final_pass_lives));
    context.SetIntListCommonAttr("boost_start_time_stamp_int_list",
                                std::move(final_pass_start_times));
    context.SetIntListCommonAttr("boost_total_duration_int_list",
                                std::move(final_pass_durations));
    context.SetIntListCommonAttr("plan_total_vv_int_list",
                                std::move(final_pass_total_vvs));
    return true;
  }

  static bool CalcRecheableLiveListNorm(const CommonRecoLightFunctionContext &context,
                                              RecoResultConstIter begin,
                                              RecoResultConstIter end) {
    auto infoString = context.GetStringCommonAttr("infoStringNorm").value_or("");
    auto user_lat = context.GetDoubleCommonAttr("user_lat").value_or(0.);
    auto user_lon = context.GetDoubleCommonAttr("user_lon").value_or(0.);
    int64 max_location_num = context.GetIntCommonAttr("maxmum_location_num_norm_int").value_or(0);
    if (infoString == "" || user_lat <= 0 || user_lon <= 0 || max_location_num <= 0) {
      return true;
    }
    double PI = 3.1415926535897932384626433832795;
    double EARTH_RADIUS = 6378.137;
    // vectors for pass lives
    std::vector<int64> final_pass_lives;
    // temp vectors for saving storage
    std::vector<absl::string_view> planVec =
        absl::StrSplit(infoString, ";", absl::SkipWhitespace());
    std::vector<int64> live_ids;
    std::vector<int64> limitations;
    std::vector<absl::string_view> locations;
    std::vector<absl::string_view> infoVec;
    for (const auto &plan : planVec) {
      if (plan == "") {
        continue;
      }
      infoVec =
        absl::StrSplit(plan, ":", absl::SkipWhitespace());
      if (infoVec.size() != 4) {
        CL_LOG_ERROR("light_function", "CalcRecheableLiveList")
          << "Pase string error";
        continue;
      }

      int64 live_id;
      int64 limitation;
      if (absl::SimpleAtoi(infoVec[0], &live_id) && absl::SimpleAtoi(infoVec[2], &limitation)) {
        if (infoVec[1] == "false" || infoVec[1] == "False") {
          final_pass_lives.push_back(live_id);
          continue;
        }
        live_ids.push_back(live_id);
        limitations.push_back(limitation);
        locations.push_back(infoVec[3]);
      }
    }

    if (live_ids.size() != limitations.size() || live_ids.size() != locations.size()) {
      CL_LOG_ERROR("light_function", "CalcRecheableLiveList")
          << "Pase string error";
      return true;
    }

    std::vector<absl::string_view> location_vec;
    std::vector<absl::string_view> lon_lat;
    double lon, lat, dx, dy, b, lx, ly, s;
    std::random_device rng;
    std::mt19937 urng(rng());
    for (int i = 0; i < live_ids.size(); i++) {
      if (locations[i] == "") {
        continue;
      }
      location_vec = absl::StrSplit(locations[i], ",", absl::SkipWhitespace());
      std::shuffle(location_vec.begin(), location_vec.end(), urng);
      location_vec.resize(max_location_num);
      for (const auto &info : location_vec) {
        if (info == "") {
          continue;
        }
        lon_lat = absl::StrSplit(info, "-", absl::SkipWhitespace());
        if (lon_lat.size() == 2 && absl::SimpleAtod(lon_lat[0], &lon) &&
              absl::SimpleAtod(lon_lat[1], &lat)) {
          dx = user_lon - lon;
          dy = user_lat - lat;
          b = (user_lat + lat) * 0.5;
          lx = (0.05 * b * b * b - 19.16 * b * b + 47.13 * b + 110966) * dx;
          ly = (17 * b + 110352) * dy;
          s = sqrt(lx * lx + ly * ly);
          if (s <= limitations[i]) {
            final_pass_lives.push_back(live_ids[i]);
            break;
          }
        }
      }
    }
    context.SetIntListCommonAttr("norm_final_pass_lives_int_list",
                                std::move(final_pass_lives));
    return true;
  }

  static bool RemoveItemOffFilter(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    auto remove_off_filter_list = context.GetIntListCommonAttr("remove_off_filter_list");
    auto item_off_old_filter_list = context.GetIntListItemAttr("item_off_filter_list");
    auto item_off_new_filter_list = context.SetIntListItemAttr("item_off_filter_list");

    if (!remove_off_filter_list || remove_off_filter_list->empty()) {
      return true;
    }

    folly::F14FastSet<int64> remove_off_filter_set(remove_off_filter_list->begin(),
                                                   remove_off_filter_list->end());
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_off_old_filter_list_value = item_off_old_filter_list(result);
      if (item_off_old_filter_list_value.has_value()) {
        std::vector<int64> new_off_filter_list;
        for (auto filter : item_off_old_filter_list_value.value()) {
          if (remove_off_filter_set.find(filter) == remove_off_filter_set.end()) {
            new_off_filter_list.push_back(filter);
          }
        }
        item_off_new_filter_list(result, new_off_filter_list);
      }
    });
    return true;
  }

  static bool CalcFollowLeafCoolingFilter(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto live_play_ms = context.GetIntListCommonAttr("live_play_ms").value_or(absl::Span<const int64>());
    auto live_play_time = context.GetIntListCommonAttr("live_play_time").value_or(absl::Span<const int64>());
    auto play_author_id = context.GetIntListCommonAttr("play_author_id").value_or(absl::Span<const int64>());
    auto stid_list = context.GetStringListCommonAttr("stid_list").value_or(std::vector<absl::string_view>());
    auto report_list = context.GetIntListCommonAttr("report_list").value_or(absl::Span<const int64>());
    auto offline_short_play_ids_str = context.GetStringCommonAttr("offline_short_play_ids_str").value_or("");
    auto timely_short_play_thred_sec =
      context.GetIntCommonAttr("follow_leaf_short_play_thred_sec").value_or(5);
    auto timely_short_svr_cooling_sec =
      context.GetIntCommonAttr("follow_leaf_svr_cooling_sec").value_or(259200);
    auto twice_cooling_gap_sec =
      context.GetIntCommonAttr("follow_leaf_twice_cooling_gap_sec").value_or(345600);
    auto twice_cooling_sec =
      context.GetIntCommonAttr("follow_leaf_twice_cooling_sec").value_or(2592000);

    auto author_id = context.GetIntItemAttr("author_id");
    auto is_filter_by_cooling_short_play = context.SetIntItemAttr("is_filter_by_cooling_short_play");
    auto is_filter_by_report = context.SetIntItemAttr("is_filter_by_report");

    std::vector<absl::string_view> short_view_ids =
        absl::StrSplit(offline_short_play_ids_str, ",", absl::SkipWhitespace());
    folly::F14FastSet<int64> short_view_sets;
    for (auto id : short_view_ids) {
      int64 id_val = 0;
      if (absl::SimpleAtoi(id, &id_val)) {
        short_view_sets.insert(id_val);
      }
    }
    folly::F14FastSet<int64> report_sets(report_list.begin(), report_list.end());
    folly::F14FastMap<int64, std::vector<int64>> timely_svr_map;
    if (live_play_ms.size() == live_play_time.size() && live_play_ms.size() == play_author_id.size()
        && live_play_ms.size() == stid_list.size()) {
      for (int i = 0; i < live_play_ms.size(); ++i) {
        if (stid_list[i] != "2_7_0_0_0") continue;
        auto play_ts = live_play_ms[i] / 1000;
        auto play_time = live_play_time[i];
        auto play_aid = play_author_id[i];
        if (play_time > timely_short_play_thred_sec * 1000) continue;
        timely_svr_map[play_aid].emplace_back(play_ts);
      }
    }

    int64 cur_ts = base::GetTimestamp() / 1000000;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto aid_val = author_id(result).value_or(0);
      if (aid_val == 0) return;
      if (report_sets.find(aid_val) != report_sets.end()) {
        is_filter_by_report(result, 1);
        return;
      }
      if (short_view_sets.find(aid_val) != short_view_sets.end()) {
        is_filter_by_cooling_short_play(result, 1);
        return;
      }
      auto timely_iter = timely_svr_map.find(aid_val);
      if (timely_iter == timely_svr_map.end()) return;
      auto &timely_info_vec = timely_iter->second;
      std::sort(timely_info_vec.begin(), timely_info_vec.end(), std::greater<>());
      for (int i = 0; i < timely_info_vec.size(); ++i) {
        int64 cur_time_gap = cur_ts - timely_info_vec[i];
        if (cur_time_gap < timely_short_svr_cooling_sec) {
          is_filter_by_cooling_short_play(result, 1);
          return;
        }
        if (i + 1 < timely_info_vec.size()) {
          int64 svr_time_gap = timely_info_vec[i] - timely_info_vec[i + 1];
          if (svr_time_gap < twice_cooling_gap_sec && cur_time_gap < twice_cooling_sec) {
            // 命中 30 天冷静
            is_filter_by_cooling_short_play(result, 1);
            return;
          }
        }
      }
    });
    return true;
  }

  static bool CalcMultiWhiteListFilter(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto white_list_ver = context.GetIntCommonAttr("multi_white_list_version").value_or(1);
    auto big_v_white_list =
      context.GetIntListCommonAttr("multi_big_v_white_list").value_or(absl::Span<const int64>());
    auto slide_author_ver = context.GetIntItemAttr("audit_author_white_info.slide_author_version");
    auto big_v_type = context.GetIntItemAttr("audit_author_big_v_info.big_v_type");
    auto white_list_filter = context.SetIntItemAttr("multi_white_list_filter");

    folly::F14FastSet<int64> white_list_set(big_v_white_list.begin(), big_v_white_list.end());

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto version = slide_author_ver(result).value_or(0);
      if ((version & (static_cast<uint64>(0x1) << (white_list_ver - 1))) > 0) {
        white_list_filter(result, 0);
        return;
      }
      auto type = big_v_type(result).value_or(-1);
      if (type < 0 || white_list_set.find(type) == white_list_set.end()) {
        white_list_filter(result, 1);
      } else {
        white_list_filter(result, 0);
      }
    });
    return true;
  }

  static bool CalcCStartAuthorFlag(const CommonRecoLightFunctionContext &context,
                                           RecoResultConstIter begin, RecoResultConstIter end) {
    auto enable_goodauthor_a2u_only = context.GetIntCommonAttr("enable_goodauthor_a2u_only").value_or(0);
    auto author_buckets =
     context.GetStringCommonAttr("cold_start_queue_author_buckets").value_or("");
    auto author_type_list_exempt =
     context.GetStringCommonAttr("author_type_list_exempt").value_or("");
    auto slide_real_show_count_accessor = context.GetIntItemAttr("slide_real_show_count");
    auto author_bucket_accessor = context.GetIntItemAttr("aCStartAuthorBucket");
    auto score_version =
     context.GetIntCommonAttr("cold_start_queue_author_score_version").value_or(0);
    auto enable_high_pay_total_gift =
     context.GetIntCommonAttr("enable_high_pay_total_gift").value_or(0);
    std::string show_key = "aCStartExpectTotalShow";
    std::string gift_key = "aCStartExpectTotalGift";
    if (score_version > 0) {
      show_key = show_key + "V" + std::to_string(score_version);
      gift_key = gift_key + "V" + std::to_string(score_version);
    }
    auto expect_total_show_accessor =
     context.GetIntItemAttr(show_key);
    auto expect_total_gift_accessor =
     context.GetIntItemAttr(gift_key);
    auto current_total_show_accessor =
     context.GetIntItemAttr("aCStartCurrentTotalShow");
    auto current_total_gift_accessor =
     context.GetIntItemAttr("aCStartCurrentTotalGift");
    auto cstart_author_reason_list_accessor =
     context.GetIntListItemAttr("reason_list");
    auto author_support_type_accessor =
     context.GetStringItemAttr("authorSupportType");
    auto is_cstart_author =
     context.SetIntItemAttr("is_cstart_author");
    auto set_expect_total_show =
     context.SetIntItemAttr("aCStartExpectTotalShow");
    auto set_expect_total_gift =
     context.SetIntItemAttr("aCStartExpectTotalGift");
    auto fill_item_set = [](const absl::string_view &ori_str, folly::F14FastSet<int64> *set_ptr) {
      if (set_ptr == nullptr || ori_str.empty()) {
        return;
      }
      std::vector<absl::string_view> vec = absl::StrSplit(ori_str, ",", absl::SkipWhitespace());
      for (auto item : vec) {
        int64 flag = 0;
        if (absl::SimpleAtoi(item, &flag)) {
          set_ptr->insert(flag);
        }
      }
    };
    folly::F14FastSet<int64> author_bucket_set;
    fill_item_set(author_buckets, &author_bucket_set);
    std::vector<std::string> author_bucket_set_exempt;
    std::string author_type_list_exempt_t = author_type_list_exempt.data();
    base::SplitStringWithOptions(author_type_list_exempt_t, ",", true, true, &author_bucket_set_exempt);

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto slide_real_show_count = slide_real_show_count_accessor(result).value_or(0);
      auto author_bucket = author_bucket_accessor(result).value_or(0);
      auto expect_total_show = expect_total_show_accessor(result).value_or(0);
      auto expect_total_gift = expect_total_gift_accessor(result).value_or(0);
      auto current_total_show = current_total_show_accessor(result).value_or(0);
      auto current_total_gift = current_total_gift_accessor(result).value_or(0);
      auto author_support_type = author_support_type_accessor(result).value_or("");
      auto cstart_author_reason_list =
          cstart_author_reason_list_accessor(result).value_or(absl::Span<const int64>());

      auto flag_value = 1;
      std::vector<int> cstart_author_reason_list_exempt = {1258, 1264, 1569};
      auto it =
          std::find(author_bucket_set_exempt.begin(), author_bucket_set_exempt.end(), author_support_type);
      // 过滤非实验组主播
      if (!author_bucket_set.empty() && author_bucket_set.count(author_bucket) == 0 &&
          it == author_bucket_set_exempt.end()) {
        flag_value = 0;
      } else {
        // 过滤非冷启主播
        if (expect_total_show <= 0 || expect_total_gift <= 0) {
          flag_value = 0;
        } else {
          // 实验组使用的曝光上限 替换 原始上限，兼容后链路
          if (score_version > 0) {
            set_expect_total_show(result, expect_total_show);
            set_expect_total_gift(result, expect_total_gift);
          }
          // 已关 重定向召回豁免
          for (auto item : cstart_author_reason_list) {
            auto it = std::find(cstart_author_reason_list_exempt.begin(),
                                cstart_author_reason_list_exempt.end(), item);
            if (it != cstart_author_reason_list_exempt.end()) {
                  continue;
              }
          }
          // 重点付费用户曝光上限过滤
          if (enable_high_pay_total_gift && current_total_gift > expect_total_gift) {
            flag_value = 0;
          }
          // 单列发现页曝光上限过滤 打赏上限过滤
          if (slide_real_show_count > expect_total_show
          || current_total_show > expect_total_show) {
            flag_value = 0;
          }
        }
      }
      // 实验组只透出 1340 reason 召回结果
      if (enable_goodauthor_a2u_only == 1) {
        auto it = std::find(cstart_author_reason_list.begin(), cstart_author_reason_list.end(), 1340);
        if (it == cstart_author_reason_list.end()) {
          flag_value = 0;
        } else {
          flag_value = 1;
        }
      }
      is_cstart_author(result, flag_value);
    });
    return true;
  }

  static bool CalcMcBPlusScoreBoost(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto get_score = context.GetDoubleItemAttr("mc_csqs_exploitation");
    auto set_score = context.SetDoubleItemAttr("mc_csqs_exploitation");

    auto is_high_gift_user = context.GetIntCommonAttr("is_high_gift_user").value_or(0);
    auto high_gift_author_boost_param_map_str = context.GetStringCommonAttr("high_gift_author_boost_coeff")
      .value_or("1024:2.5,512:2.0,256:1.5");
    folly::F14FastMap<std::string, double> high_gift_author_boost_param_map;
    if (!livestream::ParseWeightParam(std::string(high_gift_author_boost_param_map_str),
     ",", &high_gift_author_boost_param_map)) {
      high_gift_author_boost_param_map["8"] = 1.0;
    }
    auto high_gift_category_type_accessor = context.GetIntItemAttr("author_info.high_gift_category_type");
    if (is_high_gift_user == 1) {
      std::for_each(begin, end, [=](const CommonRecoResult &result) {
        auto high_gift_category_type = high_gift_category_type_accessor(result).value_or(0);
        auto high_gift_author_category_type = high_gift_category_type_accessor(result).value_or(0);
        auto score = get_score(result).value_or(0);
        for (const auto iter : high_gift_author_boost_param_map) {
          if (high_gift_author_category_type & std::stoi(iter.first)) {
            score *=  iter.second;
          }
        }
        set_score(result, score);
      });
    }
    return true;
  }

  static bool CalcFrBPlusScoreBoost(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto get_score = context.GetDoubleItemAttr("time_ens_score");
    auto set_score = context.SetDoubleItemAttr("time_ens_score");

    auto is_high_gift_user = context.GetIntCommonAttr("is_high_gift_user").value_or(0);
    auto high_gift_author_boost_param_map_str = context.GetStringCommonAttr("high_gift_author_boost_coeff")
      .value_or("1024:2.5,512:2.0,256:1.5");
    folly::F14FastMap<std::string, double> high_gift_author_boost_param_map;
    if (!livestream::ParseWeightParam(std::string(high_gift_author_boost_param_map_str),
     ",", &high_gift_author_boost_param_map)) {
      high_gift_author_boost_param_map["8"] = 1.0;
    }
    auto high_gift_category_type_accessor = context.GetIntItemAttr("author_info.high_gift_category_type");
    if (is_high_gift_user == 1) {
      std::for_each(begin, end, [=](const CommonRecoResult &result) {
        auto high_gift_category_type = high_gift_category_type_accessor(result).value_or(0);
        auto high_gift_author_category_type = high_gift_category_type_accessor(result).value_or(0);
        auto score = get_score(result).value_or(0);
        for (const auto iter : high_gift_author_boost_param_map) {
          if (high_gift_author_category_type & std::stoi(iter.first)) {
            score *=  iter.second;
          }
        }
        set_score(result, score);
      });
    }
    return true;
  }

  static bool FilterTailNumber(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                             RecoResultConstIter end) {
    auto filter_tail_number = context.GetIntCommonAttr("filter_tail_number").value_or(-1);
    auto author_id = context.GetIntItemAttr("author_id");
    auto tail_number_filter_flag = context.SetIntItemAttr("tail_number_filter_flag");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto aid = author_id(result).value_or(-1);
      if (aid == -1) {
        tail_number_filter_flag(result, 1);
        return;
      }

      std::string hash_key = std::to_string(aid);
      uint64 hash_val = base::CityHash64(hash_key.data(), hash_key.size());
      if (hash_val % 100 < filter_tail_number) {
        tail_number_filter_flag(result, 1);
        return;
      }
      tail_number_filter_flag(result, 0);
    });

    return true;
  }

  static bool CalcRepeatedShowFilter(const CommonRecoLightFunctionContext &context,
                                     RecoResultConstIter begin, RecoResultConstIter end) {
    auto repeated_play_aids_list =
      context.GetIntListCommonAttr("repeated_play_aids_list").value_or(absl::Span<const int64>());
    auto author_id = context.GetIntItemAttr("author_id");
    auto repeated_show_filter_flag = context.SetIntItemAttr("repeated_show_filter_flag");

    folly::F14FastSet<int64> play_list_set(repeated_play_aids_list.begin(), repeated_play_aids_list.end());

    if (play_list_set.empty()) {
      std::for_each(begin, end, [=](const CommonRecoResult &result) {
        repeated_show_filter_flag(result, 0);
      });
      return true;
    }
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto aid = author_id(result).value_or(-1);
      if (aid < 0 || play_list_set.find(aid) == play_list_set.end()) {
        repeated_show_filter_flag(result, 0);
      } else {
        repeated_show_filter_flag(result, 1);
      }
    });
    return true;
  }

  static bool CalcRedisAidFilter(const CommonRecoLightFunctionContext &context,
                                     RecoResultConstIter begin, RecoResultConstIter end) {
    auto qinggan_aid_list =
      context.GetIntListCommonAttr("qinggan_aid_list").value_or(absl::Span<const int64>());
    auto author_id = context.GetIntItemAttr("author_id");
    auto qinggan_aid_filter_flag = context.SetIntItemAttr("qinggan_aid_filter_flag");

    folly::F14FastSet<int64> play_list_set(qinggan_aid_list.begin(), qinggan_aid_list.end());

    if (play_list_set.empty()) {
      std::for_each(begin, end, [=](const CommonRecoResult &result) {
        qinggan_aid_filter_flag(result, 0);
      });
      return true;
    }
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto aid = author_id(result).value_or(-1);
      if (aid < 0 || play_list_set.find(aid) == play_list_set.end()) {
        qinggan_aid_filter_flag(result, 0);
      } else {
        qinggan_aid_filter_flag(result, 1);
      }
    });
    return true;
  }

  static bool CalcMatchmakerBoostFirstCoeff(const CommonRecoLightFunctionContext &context,
                                     RecoResultConstIter begin, RecoResultConstIter end) {
    auto matchmaker_aid_list =
      context.GetIntListCommonAttr("matchmaker_aid_list").value_or(absl::Span<const int64>());
    auto consume_first_matchmaker_boost_coff =
      context.GetDoubleCommonAttr("consume_first_matchmaker_boost_coff").value_or(1.0);
    auto author_id = context.GetIntItemAttr("author_id");
    auto consume_first_matchmaker_item_boost_coff =
      context.SetDoubleItemAttr("consume_first_matchmaker_item_boost_coff");

    folly::F14FastSet<int64> matchmaker_aid_set(matchmaker_aid_list.begin(), matchmaker_aid_list.end());

    if (matchmaker_aid_set.empty()) {
      std::for_each(begin, end, [=](const CommonRecoResult &result) {
        consume_first_matchmaker_item_boost_coff(result, 1.0);
      });
      return true;
    }
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto aid = author_id(result).value_or(-1);
      if (aid < 0 || matchmaker_aid_set.find(aid) == matchmaker_aid_set.end()) {
        consume_first_matchmaker_item_boost_coff(result, 1.0);
      } else {
        consume_first_matchmaker_item_boost_coff(result, consume_first_matchmaker_boost_coff);
      }
    });
    return true;
  }

  static bool CalcMatchmakerBoostSecondCoeff(const CommonRecoLightFunctionContext &context,
                                     RecoResultConstIter begin, RecoResultConstIter end) {
    auto matchmaker_aid_list =
      context.GetIntListCommonAttr("matchmaker_aid_list").value_or(absl::Span<const int64>());
    auto consume_second_matchmaker_boost_coff =
      context.GetDoubleCommonAttr("consume_second_matchmaker_boost_coff").value_or(1.0);
    auto author_id = context.GetIntItemAttr("author_id");
    auto consume_second_matchmaker_item_boost_coff =
      context.SetDoubleItemAttr("consume_second_matchmaker_item_boost_coff");

    folly::F14FastSet<int64> matchmaker_aid_set(matchmaker_aid_list.begin(), matchmaker_aid_list.end());

    if (matchmaker_aid_set.empty()) {
      std::for_each(begin, end, [=](const CommonRecoResult &result) {
        consume_second_matchmaker_item_boost_coff(result, 1.0);
      });
      return true;
    }
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto aid = author_id(result).value_or(-1);
      if (aid < 0 || matchmaker_aid_set.find(aid) == matchmaker_aid_set.end()) {
        consume_second_matchmaker_item_boost_coff(result, 1.0);
      } else {
        consume_second_matchmaker_item_boost_coff(result, consume_second_matchmaker_boost_coff);
      }
    });
    return true;
  }

  static bool CalcExploreRevenueAuthorFilter(const CommonRecoLightFunctionContext &context,
                                             RecoResultConstIter begin, RecoResultConstIter end) {
    auto explore_revenue_crowd_filter_aids_list =
        context.GetIntListCommonAttr("explore_revenue_crowd_filter_set").value_or(absl::Span<const int64>());
    auto author_id = context.GetIntItemAttr("author_id");
    auto explore_revenue_author_should_filter =
        context.SetIntItemAttr("explore_revenue_author_should_filter");
    folly::F14FastSet<int64> explore_revenue_list_set(explore_revenue_crowd_filter_aids_list.begin(),
                                                      explore_revenue_crowd_filter_aids_list.end());
    if (explore_revenue_list_set.empty()) {
      std::for_each(begin, end, [=](const CommonRecoResult &result) {
        explore_revenue_author_should_filter(result, 0);
      });
      return true;
    }
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto aid = author_id(result).value_or(-1);
      if (aid < 0 || explore_revenue_list_set.find(aid) == explore_revenue_list_set.end()) {
        explore_revenue_author_should_filter(result, 0);
      } else {
        explore_revenue_author_should_filter(result, 1);
      }
    });
    return true;
  }

  static bool CloseLivingRedisKeyConcat(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    auto item_alive_redis_key = context.SetStringItemAttr("item_alive_redis_key");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      item_alive_redis_key(result, "alive_" + std::to_string(result.item_key));
    });
    return true;
  }

  // NOTE(litianshi03) 获取房产直播过滤标记
  static bool GetHouseLiveFilterTag(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto enable_house_author_live_filter =
        context.GetIntCommonAttr("enable_house_author_live_filter").value_or(0);
    auto enable_dream_house_live_filter =
        context.GetIntCommonAttr("enable_dream_house_live_filter").value_or(0);

    auto is_house_author_live_accessor = context.GetIntItemAttr("aIshouseLiveKV");
    auto is_dream_house_live_accessor = context.GetIntItemAttr("lLiveHouseIsDreamHouse");
    auto house_live_user_filter_item_flag_accessor =
        context.SetIntItemAttr("house_live_user_filter_item_flag");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto is_house_author_live = is_house_author_live_accessor(result).value_or(0);
      auto is_dream_house_live = is_dream_house_live_accessor(result).value_or(0);

      int house_filter_flag = 0;
      if (enable_house_author_live_filter == 1 && is_house_author_live == 1) {
        house_filter_flag = 1;
      }
      if (enable_dream_house_live_filter == 1 && is_dream_house_live == 1) {
        house_filter_flag = 1;
      }
      house_live_user_filter_item_flag_accessor(result, house_filter_flag);
    });
    return true;
  }

  // NOTE(gaoyunpeng) 本地生活按照 reason 进行请求粗排模型的 quota 限制
  static bool LocalLifeCascadingInferQuotaByReason(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    // 解析进粗排 quota 参数
    auto reason_quota_param = context.GetStringCommonAttr("lll_cascading_infer_reason_quotas").value_or("");
    auto quota_all = context.GetIntCommonAttr("lll_cascading_infer_quota").value_or(0);
    folly::F14FastMap<int64, int> quota_map;
    if (quota_all <= 0) return true;
    int other_quota = quota_all;
    std::vector<absl::string_view> reason_params = absl::StrSplit(reason_quota_param,
                                                                  ",", absl::SkipWhitespace());
    for (int j = 0; j < reason_params.size(); ++j) {
      auto reason_param_str = reason_params[j];
      std::vector<absl::string_view> param_pair = absl::StrSplit(reason_param_str,
                                                      ":", absl::SkipWhitespace());
      if (param_pair.size() != 2) continue;
      int64 reason;
      double ratio = 0.0;
      if (absl::SimpleAtoi(param_pair[0], &reason) && absl::SimpleAtod(param_pair[1], &ratio)) {
        if (ratio > 0 && ratio <= 1) {
          int quota = quota_all * ratio;
          if (quota > 0) {
            quota_map[reason] = quota;
            other_quota -= quota;
          }
        }
      }
    }
    if (other_quota > 0) quota_map[-1] = other_quota;

    auto reason_list_accessor = context.GetIntListItemAttr("reason_list");
    auto item_tag_accessor = context.SetIntItemAttr("local_life_cascading_tag");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      if (!quota_map.empty()) {
        bool has_reason = false;
        auto reason_list = reason_list_accessor(result).value_or(absl::Span<const int64>());
        for (auto reason : reason_list) {
          if (quota_map.find(reason) != quota_map.end()) {
            has_reason = true;
            auto quota = quota_map[reason];
            if (quota > 0) {
              item_tag_accessor(result, 1);
              quota_map[reason] = quota - 1;
              break;
            }
          }
        }
        // item 包含单独配置的 reason, 即 has_reason == true, 就不再占用 other_reason 的 quota
        if (!has_reason && quota_map.find(-1) != quota_map.end()) {
          auto quota = quota_map[-1];
          if (quota > 0) {
            item_tag_accessor(result, 1);
            quota_map[-1] = quota - 1;
          }
        }
      }
    });
    return true;
  }

  // NOTE(wangxuming) 获取本地生活过滤秀场核心营收用户标记
  static bool GetLocalLiveFilterCoreRevenueTag(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto enable_local_live_single_core_revenue_filter =
        context.GetIntCommonAttr("enable_local_live_single_core_revenue_filter").value_or(0);
    auto local_life_exptag =
        context.GetIntListCommonAttr("local_life_exptag").value_or(absl::Span<const int64>());
    auto local_live_user_filter_item_flag_accessor =
        context.SetIntItemAttr("local_live_user_filter_item_flag");
    auto reason_list_accessor = context.GetIntListItemAttr("reason_list");


    folly::F14FastSet<int64> off_filter_reason_set(local_life_exptag.begin(),
                                                   local_life_exptag.end());

    int debug_fliter_glag = 0;
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto reason_list = reason_list_accessor(result).value_or(absl::Span<const int64>());
      int local_life_filter_flag = 0;

      if (enable_local_live_single_core_revenue_filter != 1) {
        local_live_user_filter_item_flag_accessor(result, local_life_filter_flag);
        return;
      }

      if (reason_list.size() <=0 || local_life_exptag.size() <= 0) {
        local_live_user_filter_item_flag_accessor(result, local_life_filter_flag);
        return;
      }

      for (auto reason_tag : reason_list) {
        if (off_filter_reason_set.find(reason_tag) != off_filter_reason_set.end()) {
          local_life_filter_flag = 1;
          break;
        }
      }
      local_live_user_filter_item_flag_accessor(result, local_life_filter_flag);
    });
    return true;
  }

  // NOTE(wangxuming) 处理 item_attr string to double
  static bool ItemAttrStringToDouble(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto item_str_accessor  = context.GetStringItemAttr("local_life_item_str");
    auto item_double_accessor = context.SetDoubleItemAttr("local_life_item_double");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto item_value = item_str_accessor(result).value_or("0.0");
      std::string item_str = item_value.data();
      double value = 0.0;
      if (absl::SimpleAtod(item_str, &value)) {
        item_double_accessor(result, value);
      } else {
        item_double_accessor(result, 0.0);
      }
    });
    return true;
  }

  // NOTE(wangxuming) 处理 item_attr1 > item_attr2 or item_attr1 > defalut
  static bool ItemAttrScoreCompare(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto item_score_accessor  = context.GetDoubleItemAttr("local_life_item_score");
    auto local_life_author_score_defalut_value =
    context.GetDoubleCommonAttr("local_life_author_score_defalut_value").value_or(30.0);
    auto item_long_accessor = context.SetIntItemAttr("local_life_compare_is_true");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto item_value = item_score_accessor(result).value_or(0.0);
      if (item_value <= local_life_author_score_defalut_value) {
        item_long_accessor(result, 1);
      } else {
        item_long_accessor(result, 0);
      }
    });
    return true;
  }

  // NOTE(wangxuming) 处理 item_attr cityhash
  static bool ItemAttrCityHash(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto item_attr_accessor  = context.GetStringItemAttr("local_life_item_attr");
    auto item_hash_accessor = context.SetIntItemAttr("local_life_item_hash");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto item_value = item_attr_accessor(result).value_or("");
      if (item_value.empty()) {
        return;
      }
      auto item_hash = base::CityHash64(item_value.data(), item_value.size());
      item_hash_accessor(result, item_hash);
    });
    return true;
  }

  // NOTE(gaoyunpeng) 按照 age_segment 配置本地生活流控 filter_ratio
  static bool LocalLifeUserAgeSegmentFilterBoost(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto age_segment = context.GetIntCommonAttr("age_segment").value_or(0);
    auto filter_ratio = context.GetDoubleCommonAttr("local_life_post_filter_ratio").value_or(1.0);
    auto raw_params_str =
          context.GetStringCommonAttr("lll_user_age_filter_boost_ratio").value_or("");
    std::vector<absl::string_view> params =
        absl::StrSplit(raw_params_str.data(), ";", absl::SkipWhitespace());
    for (int i = 0; i < params.size(); ++i) {
      std::vector<absl::string_view> param_kv = absl::StrSplit(params[i], ":", absl::SkipWhitespace());
      if (param_kv.size() != 2) continue;
      int key = -1;
      double value = 1.0;
      if (absl::SimpleAtoi(param_kv[0], &key) && absl::SimpleAtod(param_kv[1], &value)) {
        if (key == age_segment && value > 0) {
          filter_ratio *= value;
          context.SetDoubleCommonAttr("local_life_post_filter_ratio", filter_ratio);
          break;
        }
      }
    }
    return true;
  }

  // NOTE(gaoyunpeng) 本地生活流控 real show deboost
  static bool LocalLifeRealShowFilterDeboost(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto filter_ratio = context.GetDoubleCommonAttr("local_life_post_filter_ratio").value_or(1.0);
    auto ratio1 = context.GetDoubleCommonAttr("lll_real_show_deboost_ratio").value_or(1.0);
    auto ratio2 = context.GetDoubleCommonAttr("lll_real_show_deboost_ratio2").value_or(1.0);
    auto mix_ratio = context.GetDoubleCommonAttr("lll_real_show_deboost_ratio_mix").value_or(1.0);
    auto thresh = context.GetDoubleCommonAttr("lll_real_show_thresh").value_or(1.0);
    auto show_cnt_list = context.GetIntListCommonAttr("local_life_goods_cate2_browset_cnt_list")
                             .value_or(absl::Span<const int64>());

    int show_cnt = 0;
    for (auto cnt : show_cnt_list) {
      show_cnt += cnt;
    }

    if (show_cnt <= 0) return true;
    double boost_ratio = 1.0;
    if (show_cnt < thresh) {
      boost_ratio = livestream::LivePow(show_cnt, mix_ratio) / ratio1;
    } else {
      boost_ratio = livestream::LivePow(show_cnt, mix_ratio) / ratio2;
    }
    boost_ratio = std::max(boost_ratio, 1.0);

    filter_ratio *= boost_ratio;
    context.SetDoubleCommonAttr("local_life_post_filter_ratio", filter_ratio);
    return true;
  }

  // NOTE(gaoyunpeng) 本地生活混排世界流控 real show deboost
  static bool LocalLifeMixRealShowFilterDeboost(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto filter_ratio = context.GetDoubleCommonAttr("local_life_post_filter_ratio").value_or(1.0);
    auto low_ratio = context.GetDoubleCommonAttr("ll_live_mix_real_show_deboost_low_ratio1").value_or(1.0);
    auto high_ratio = context.GetDoubleCommonAttr("ll_live_mix_real_show_deboost_high_ratio1").value_or(1.0);
    auto exponent = context.GetDoubleCommonAttr("ll_live_mix_real_show_deboost_high_exponent1").value_or(1.0);
    auto thresh = context.GetDoubleCommonAttr("ll_live_mix_real_show_deboost_thresh1").value_or(1.0);
    auto show_cnt_list = context.GetIntListCommonAttr("local_life_goods_cate2_browset_cnt_list")
                             .value_or(absl::Span<const int64>());

    int show_cnt = 0;
    for (auto cnt : show_cnt_list) {
      show_cnt += cnt;
    }

    if (show_cnt < 0) return true;
    double boost_ratio = low_ratio;
    if (show_cnt > thresh) {
      boost_ratio = livestream::LivePow(show_cnt, exponent) * high_ratio;
    }

    filter_ratio *= boost_ratio;
    context.SetDoubleCommonAttr("local_life_post_filter_ratio", filter_ratio);
    return true;
  }

  // NOTE(gaoyunpeng) 按照本地生活用户分层配置
  static bool GetLocalLifeUserLevelRankFilterParams(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto user_level_value =
          context.GetIntCommonAttr("uLocalLiveCoreLevel").value_or(0);
    int user_level = 0;
    if (user_level_value == 31) {
      // 90 天内有购买
      user_level = 3;
    } else if (user_level_value == 32 || user_level_value == 33) {
      // 30 天内有提单或商品点击
      user_level = 2;
    } else if (user_level_value == 34) {
      // 最近一周间内时长 > 3s
      user_level = 1;
    }
    // 解析用户分层流控参数, 覆盖原来的流控参数
    auto raw_params_str =
          context.GetStringCommonAttr("lll_user_level_rank_filter_params").value_or("");
    std::vector<std::string> params =
        absl::StrSplit(raw_params_str.data(), ";", absl::SkipWhitespace());
    for (int i = 0; i < params.size(); ++i) {
      std::vector<std::string> param_kv = absl::StrSplit(params[i], "@", absl::SkipWhitespace());
      if (param_kv.size() != 2) continue;
      int key = -1;
      if (absl::SimpleAtoi(param_kv[0], &key)) {
        if (key == user_level && param_kv[1] != "") {
          context.SetStringCommonAttr("local_life_rank_multi_score_params", param_kv[1]);
          break;
        }
      }
    }
    return true;
  }

  // NOTE(linjianyong) 按照本地新用户分层配置
  static bool GetNewLocalLifeUserLevelRankFilterParams(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto user_level_value =
          context.GetIntCommonAttr("uLocalLifeUserLayerU1").value_or(100);
    int user_level = 0;

    if (user_level_value == 100 || user_level_value == 1 || user_level_value == 2) {
      user_level = 1;
    }
    // 解析用户分层流控参数, 覆盖原来的流控参数
    auto raw_params_str =
          context.GetStringCommonAttr("lll_user_level_rank_filter_params").value_or("");
    std::vector<std::string> params =
        absl::StrSplit(raw_params_str.data(), ";", absl::SkipWhitespace());
    for (int i = 0; i < params.size(); ++i) {
      std::vector<std::string> param_kv = absl::StrSplit(params[i], "@", absl::SkipWhitespace());
      if (param_kv.size() != 2) continue;
      int key = -1;
      if (absl::SimpleAtoi(param_kv[0], &key)) {
        if (key == user_level && param_kv[1] != "") {
          context.SetStringCommonAttr("local_life_rank_multi_score_params", param_kv[1]);
          break;
        }
      }
    }
    return true;
  }

  // NOTE(linjianyong) 按照本地新用户分层配置(0-新，1-回，2-活)
  static bool GetNewLocalLifeUserLevelRankFilterParamsV2(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto user_level = context.GetIntCommonAttr("user_level").value_or(100);
    // 解析用户分层流控参数, 覆盖原来的流控参数
    auto raw_params_str = context.GetStringCommonAttr("params").value_or("");
    std::vector<std::string> params =
        absl::StrSplit(raw_params_str.data(), ";", absl::SkipWhitespace());
    for (int i = 0; i < params.size(); ++i) {
      std::vector<std::string> param_kv = absl::StrSplit(params[i], "@", absl::SkipWhitespace());
      if (param_kv.size() != 2) continue;
      int key = -1;
      if (absl::SimpleAtoi(param_kv[0], &key)) {
        if (key == user_level && param_kv[1] != "") {
          context.SetStringCommonAttr("local_life_rank_multi_score_params", param_kv[1]);
          break;
        }
      }
    }
    return true;
  }

  // 处理本地生活栅栏流控参数 pxtr 的后缀:
  // locallife_ctr1:0.03||locallife_ctr_gmv1:0.02 -> locallife_ctr:0.03||locallife_ctr_gmv:0.02
  static std::string LocallifeFilterParamConvert(const std::string &origin_param) {
    std::string ret;
    std::vector<std::string> params = absl::StrSplit(origin_param, ":", absl::SkipWhitespace());
    for (int i = 0; i < params.size()-1; ++i) {
      std::string tmp = params[i];
      if (i <= params.size() - 1) {
        if (tmp[tmp.size()-1] == '1') tmp.erase(tmp.size()-1, 1);
        ret.append(tmp);
        ret.append(":");
      }
    }
    ret.append(params[params.size()-1]);
    return ret;
  }

  // NOTE(gaoyunpeng03) 按照本地新用户分层配置(0-新，1-回，2-活)
  static bool GetNewLocalLifeUserLevelRankFilterParamsV3(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto user_level_value =
          context.GetIntCommonAttr("uLocalLifeUserLayerU1").value_or(100);
    int user_level = 0;
    if (user_level_value == 0) {
      user_level = 1;
    } else if (user_level_value >= 1 && user_level_value <= 6) {
      user_level = 2;
    }
    // 解析用户分层流控参数, 覆盖原来的流控参数
    auto raw_params_str =
          context.GetStringCommonAttr("params").value_or("");
    std::vector<std::string> params =
        absl::StrSplit(raw_params_str.data(), ";", absl::SkipWhitespace());
    for (int i = 0; i < params.size(); ++i) {
      std::vector<std::string> param_kv = absl::StrSplit(params[i], "@", absl::SkipWhitespace());
      if (param_kv.size() != 2) continue;
      int key = -1;
      if (absl::SimpleAtoi(param_kv[0], &key)) {
        if (key == user_level && param_kv[1] != "") {
          std::string param = LocallifeFilterParamConvert(param_kv[1]);
          context.SetStringCommonAttr("local_life_rank_multi_score_params", param);
          break;
        }
      }
    }
    return true;
  }

  // NOTE(gaoyunpeng03) 按照本地新用户分层配置(0-新，1-回，2-活)
  static bool GetLocalLifeUserLevelRankScoreParams(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto user_level_value =
          context.GetIntCommonAttr("uLocalLifeUserLayerU1").value_or(100);
    int user_level = 0;
    if (user_level_value == 0) {
      user_level = 1;
    } else if (user_level_value >= 1 && user_level_value <= 6) {
      user_level = 2;
    }
    // 解析用户分层流控参数, 覆盖原来的流控参数
    auto raw_params_str =
          context.GetStringCommonAttr("lll_user_level_rank_score_params").value_or("");
    std::vector<std::string> params =
        absl::StrSplit(raw_params_str.data(), "|", absl::SkipWhitespace());
    for (int i = 0; i < params.size(); ++i) {
      std::vector<std::string> param_kv = absl::StrSplit(params[i], "@", absl::SkipWhitespace());
      if (param_kv.size() != 2) continue;
      int key = -1;
      if (absl::SimpleAtoi(param_kv[0], &key)) {
        if (key == user_level && param_kv[1] != "") {
          std::string param = LocallifeFilterParamConvert(param_kv[1]);
          context.SetStringCommonAttr("local_life_rank_score_params", param);
          break;
        }
      }
    }
    return true;
  }

  // NOTE(wangxuming) 处理 rank 产出多个分进行按条件过滤
  static bool RankScorePostFilter(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    std::vector<std::vector<std::pair<std::function<absl::optional<double>(
      const CommonRecoResult &)>, double>>> post_filter;
    auto score_filter_params =
    context.GetStringCommonAttr("local_life_rank_multi_score_params").value_or("");
    auto post_filter_ratio = context.GetDoubleCommonAttr("local_life_post_filter_ratio").value_or(1.0);
    auto fr_raw_qs_local_life_accessor  = context.GetDoubleItemAttr("fr_raw_qs_local_life");
    auto item_score_accessor = context.SetDoubleItemAttr("local_life_rank_final_score");
    auto pid_filter_coeff_accessor = context.GetDoubleItemAttr("locallife_pid_filter_coeff");
    auto enable_adjust_factor = context.GetIntCommonAttr("lll_rank_filter_adjust_factor_enable")
                                       .value_or(0);

    // 解析 adjust_factor 参数
    folly::F14FastMap<std::string, double> factor_map;
    if (enable_adjust_factor > 0) {
      auto adjust_factor_str = context.GetStringCommonAttr("lll_rank_filter_adjust_factor").value_or("");
      std::vector<std::string> raw_params =
          absl::StrSplit(adjust_factor_str.data(), ",", absl::SkipWhitespace());
      for (int i = 0; i < raw_params.size(); ++i) {
        auto raw_param = raw_params[i];
        std::vector<std::string> kv = absl::StrSplit(raw_param, ":", absl::SkipWhitespace());
        if (kv.size() != 2) continue;
        auto pxtr_name = kv[0];
        auto ratio = 1.0;
        if (absl::SimpleAtod(kv[1], &ratio)) {
          factor_map[pxtr_name] = ratio;
        }
      }
    }

    // 解析多条件逻辑关系
    std::vector<absl::string_view> or_params =
        absl::StrSplit(score_filter_params.data(), "||", absl::SkipWhitespace());
    for (int i = 0; i < or_params.size(); ++i) {
      auto or_param = or_params[i];
      std::vector<absl::string_view> and_params = absl::StrSplit(or_param, "&&", absl::SkipWhitespace());
      std::vector<std::pair<std::function<absl::optional<double>(
        const CommonRecoResult &)>, double>> and_list;
      for (int j = 0; j < and_params.size(); ++j) {
        auto and_param = and_params[j];
        std::vector<absl::string_view> filter_param = absl::StrSplit(and_param, ":", absl::SkipWhitespace());
        if (filter_param.size() != 2) continue;
        auto key = filter_param[0];
        auto value = 0.0;
        auto tmp_accessor = context.GetDoubleItemAttr(key);
        if (absl::SimpleAtod(filter_param[1], &value)) {
          double adjust_factor = 1.0;
          if (enable_adjust_factor > 0 && factor_map.count((std::string) key)) {
            adjust_factor = factor_map.at((std::string) key);
          }
          and_list.emplace_back(tmp_accessor, value * post_filter_ratio * adjust_factor);
        }
      }
      post_filter.push_back(std::move(and_list));
    }

    // 按逻辑进行判断.
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto pid_filter_coeff = pid_filter_coeff_accessor(result).value_or(1.0);
      auto fr_raw_qs_local_life = fr_raw_qs_local_life_accessor(result).value_or(0.0);
      if (post_filter.size() <= 0) {
        item_score_accessor(result, fr_raw_qs_local_life);
        return;
      }
      bool or_true = false;
      for (auto and_lists : post_filter) {
        if (or_true) {
            break;
        }
        bool and_true = true;
        for (auto and_pair : and_lists) {
          if (!and_true) {
            break;
          }
          auto key_accessor = and_pair.first;
          auto threshold = and_pair.second;
          auto score = key_accessor(result).value_or(0.0);
          if (score < threshold * pid_filter_coeff) {
              and_true = false;
          }
        }
        or_true = or_true || and_true;
      }
      // or_true = true 代表不过滤
      if (or_true) {
        item_score_accessor(result, fr_raw_qs_local_life);
      } else {
        item_score_accessor(result, 0.0);
      }
    });
    return true;
  }

  // NOTE(gaoyunpeng03) 处理 rank 产出多个分进行按条件过滤, 支持 user 粒度的 boost
  static bool RankScorePostFilterWithCommonBoost(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    std::vector<std::vector<std::pair<std::function<absl::optional<double>(
      const CommonRecoResult &)>, double>>> post_filter;
    auto score_filter_params =
    context.GetStringCommonAttr("local_life_rank_multi_score_params").value_or("");
    auto fr_raw_qs_local_life_accessor = context.GetDoubleItemAttr("fr_raw_qs_local_life");
    auto item_score_accessor = context.SetDoubleItemAttr("local_life_rank_final_score");
    auto filter_ratio = context.GetDoubleCommonAttr("local_life_rank_multi_score_params").value_or(1.0);

    // 解析多条件逻辑关系
    std::vector<absl::string_view> or_params =
        absl::StrSplit(score_filter_params.data(), "||", absl::SkipWhitespace());
    for (int i = 0; i < or_params.size(); ++i) {
      auto or_param = or_params[i];
      std::vector<absl::string_view> and_params = absl::StrSplit(or_param, "&&", absl::SkipWhitespace());
      std::vector<std::pair<std::function<absl::optional<double>(
        const CommonRecoResult &)>, double>> and_list;
      for (int j = 0; j < and_params.size(); ++j) {
        auto and_param = and_params[j];
        std::vector<absl::string_view> filter_param = absl::StrSplit(and_param, ":", absl::SkipWhitespace());
        if (filter_param.size() != 2) continue;
        auto key = filter_param[0];
        auto value = 0.0;
        auto tmp_accessor = context.GetDoubleItemAttr(key);
        if (absl::SimpleAtod(filter_param[1], &value)) {
          and_list.emplace_back(tmp_accessor, value);
        }
      }
      post_filter.push_back(std::move(and_list));
    }

    // 按逻辑进行判断.
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto fr_raw_qs_local_life = fr_raw_qs_local_life_accessor(result).value_or(0.0);
      if (post_filter.size() <= 0) {
        item_score_accessor(result, fr_raw_qs_local_life);
        return;
      }
      bool or_true = false;
      for (auto and_lists : post_filter) {
        if (or_true) {
            break;
        }
        bool and_true = true;
        for (auto and_pair : and_lists) {
          if (!and_true) {
            break;
          }
          auto key_accessor = and_pair.first;
          auto threshold = and_pair.second * filter_ratio;
          auto score = key_accessor(result).value_or(0.0);
          if (score < threshold) {
              and_true = false;
          }
        }
        or_true = or_true || and_true;
      }
      // or_true = true 代表不过滤
      if (or_true) {
        item_score_accessor(result, fr_raw_qs_local_life);
      } else {
        item_score_accessor(result, 0.0);
      }
    });
    return true;
  }

  // NOTE(wangxuming) 处理 item_attr1 > item_attr2 or item_attr1 > defalut
  static bool ItemAttrCompareItemAttr(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto item_v1_score_accessor  = context.GetDoubleItemAttr("local_life_item_v1_score");
    auto item_v2_score_accessor  = context.GetDoubleItemAttr("local_life_item_v2_score");
    auto item_long_accessor = context.SetIntItemAttr("local_life_compare_is_true");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto item_v1_value = item_v1_score_accessor(result);
      auto item_v2_value = item_v2_score_accessor(result).value_or(0.0);
      if (!item_v1_value) {
        item_long_accessor(result, 0);
        return;
      }
      if (item_v1_value > item_v2_value) {
        item_long_accessor(result, 1);
      } else {
        item_long_accessor(result, 0);
      }
    });
    return true;
  }

    // NOTE(wangxuming) 获取本地生活融合分计算
  static bool GetLocalLiveMultipyScore(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto local_life_sort_defalut_value =
        context.GetDoubleCommonAttr("local_life_sort_defalut_value").value_or(1.0);
    // pay_order
    auto local_life_sort_weight_pay_order_params =
        context.GetDoubleCommonAttr("local_life_sort_weight_pay_order_params")
        .value_or(1.0);
    auto local_life_sort_const_pay_order_params =
        context.GetDoubleCommonAttr("local_life_sort_const_pay_order_params")
        .value_or(1.0);
    auto local_life_sort_temperature_pay_order_params =
        context.GetDoubleCommonAttr("local_life_sort_temperature_pay_order_params")
        .value_or(1.0);
    // gmv
    auto local_life_sort_weight_gmv_params =
        context.GetDoubleCommonAttr("local_life_sort_weight_gmv_params")
        .value_or(1.0);
    auto local_life_sort_const_gmv_params =
        context.GetDoubleCommonAttr("local_life_sort_const_gmv_params")
        .value_or(1.0);
    auto local_life_sort_temperature_gmv_params =
        context.GetDoubleCommonAttr("local_life_sort_temperature_gmv_params")
        .value_or(1.0);
    // sim_pctr
    auto local_life_sort_weight_sim_pctr_params =
        context.GetDoubleCommonAttr("local_life_sort_weight_sim_pctr_params")
        .value_or(1.0);
    auto local_life_sort_const_sim_pctr_params =
        context.GetDoubleCommonAttr("local_life_sort_const_sim_pctr_params")
        .value_or(1.0);
    auto local_life_sort_temperature_sim_pctr_params =
        context.GetDoubleCommonAttr("local_life_sort_temperature_sim_pctr_params")
        .value_or(1.0);
    // sim_prtr
    auto local_life_sort_weight_sim_prtr_params =
        context.GetDoubleCommonAttr("local_life_sort_weight_sim_prtr_params")
        .value_or(1.0);
    auto local_life_sort_const_sim_prtr_params =
        context.GetDoubleCommonAttr("local_life_sort_const_sim_prtr_params")
        .value_or(1.0);
    auto local_life_sort_temperature_sim_prtr_params =
        context.GetDoubleCommonAttr("local_life_sort_temperature_sim_prtr_params")
        .value_or(1.0);
    // sim_plvtr
    auto local_life_sort_weight_sim_plvtr_params =
        context.GetDoubleCommonAttr("local_life_sort_weight_sim_plvtr_params")
        .value_or(1.0);
    auto local_life_sort_const_sim_plvtr_params =
        context.GetDoubleCommonAttr("local_life_sort_const_sim_plvtr_params")
        .value_or(1.0);
    auto local_life_sort_temperature_sim_plvtr_params =
        context.GetDoubleCommonAttr("local_life_sort_temperature_sim_plvtr_params")
        .value_or(1.0);
     // sim_pin_lvtr
    auto local_life_sort_weight_sim_pin_lvtr_params =
        context.GetDoubleCommonAttr("local_life_sort_weight_sim_pin_lvtr_params")
        .value_or(1.0);
    auto local_life_sort_const_sim_pin_lvtr_params =
        context.GetDoubleCommonAttr("local_life_sort_const_sim_pin_lvtr_params")
        .value_or(1.0);
    auto local_life_sort_temperature_sim_pin_lvtr_params =
        context.GetDoubleCommonAttr("local_life_sort_temperature_sim_pin_lvtr_params")
        .value_or(1.0);

    // sim_wtr
    auto local_life_sort_weight_sim_wtr_params =
        context.GetDoubleCommonAttr("local_life_sort_weight_sim_wtr_params")
        .value_or(1.0);
    auto local_life_sort_const_sim_wtr_params =
        context.GetDoubleCommonAttr("local_life_sort_const_sim_wtr_params")
        .value_or(1.0);
    auto local_life_sort_temperature_sim_wtr_params =
        context.GetDoubleCommonAttr("local_life_sort_temperature_sim_wtr_params")
        .value_or(1.0);

    // sim_cmt
    auto local_life_sort_weight_sim_cmt_params =
        context.GetDoubleCommonAttr("local_life_sort_weight_sim_cmt_params")
        .value_or(1.0);
    auto local_life_sort_const_sim_cmt_params =
        context.GetDoubleCommonAttr("local_life_sort_const_sim_cmt_params")
        .value_or(1.0);
    auto local_life_sort_temperature_sim_cmt_params =
        context.GetDoubleCommonAttr("local_life_sort_temperature_sim_cmt_params")
        .value_or(1.0);

    // sim_submit
    auto local_life_sort_weight_sim_submit_params =
        context.GetDoubleCommonAttr("local_life_sort_weight_sim_submit_params")
        .value_or(1.0);
    auto local_life_sort_const_sim_submit_params =
        context.GetDoubleCommonAttr("local_life_sort_const_sim_submit_params")
        .value_or(1.0);
    auto local_life_sort_temperature_sim_submit_params =
        context.GetDoubleCommonAttr("local_life_sort_temperature_sim_submit_params")
        .value_or(1.0);

    // sim_pay_order_cnt
    auto local_life_sort_weight_sim_pay_order_cnt_params =
        context.GetDoubleCommonAttr("local_life_sort_weight_sim_pay_order_cnt_params")
        .value_or(1.0);
    auto local_life_sort_const_sim_pay_order_cnt_params =
        context.GetDoubleCommonAttr("local_life_sort_const_sim_pay_order_cnt_params")
        .value_or(1.0);
    auto local_life_sort_temperature_sim_pay_order_cnt_params =
        context.GetDoubleCommonAttr("local_life_sort_temperature_sim_pay_order_cnt_params")
        .value_or(1.0);

    // unit_pctr
    auto local_life_sort_weight_unit_pctr_params =
        context.GetDoubleCommonAttr("local_life_sort_weight_unit_pctr_params")
        .value_or(1.0);
    auto local_life_sort_const_unit_pctr_params =
        context.GetDoubleCommonAttr("local_life_sort_const_unit_pctr_params")
        .value_or(1.0);
    auto local_life_sort_temperature_unit_pctr_params =
        context.GetDoubleCommonAttr("local_life_sort_temperature_unit_pctr_params")
        .value_or(1.0);

    // unit_pinlvtr
    auto local_life_sort_weight_unit_pinlvtr_params =
        context.GetDoubleCommonAttr("local_life_sort_weight_unit_pinlvtr_params")
        .value_or(1.0);
    auto local_life_sort_const_unit_pinlvtr_params =
        context.GetDoubleCommonAttr("local_life_sort_const_unit_pinlvtr_params")
        .value_or(1.0);
    auto local_life_sort_temperature_unit_pinlvtr_params =
        context.GetDoubleCommonAttr("local_life_sort_temperature_unit_pinlvtr_params")
        .value_or(1.0);

    // unit_pwtr
    auto local_life_sort_weight_unit_pwtr_params =
        context.GetDoubleCommonAttr("local_life_sort_weight_unit_pwtr_params")
        .value_or(1.0);
    auto local_life_sort_const_unit_pwtr_params =
        context.GetDoubleCommonAttr("local_life_sort_const_unit_pwtr_params")
        .value_or(1.0);
    auto local_life_sort_temperature_unit_pwtr_params =
        context.GetDoubleCommonAttr("local_life_sort_temperature_unit_pwtr_params")
        .value_or(1.0);

    // unit_pinetr
    auto local_life_sort_weight_unit_pinetr_params =
        context.GetDoubleCommonAttr("local_life_sort_weight_unit_pinetr_params")
        .value_or(1.0);
    auto local_life_sort_const_unit_pinetr_params =
        context.GetDoubleCommonAttr("local_life_sort_const_unit_pinetr_params")
        .value_or(1.0);
    auto local_life_sort_temperature_unit_pinetr_params =
        context.GetDoubleCommonAttr("local_life_sort_temperature_unit_pinetr_params")
        .value_or(1.0);

    // unit_pcmtr
    auto local_life_sort_weight_unit_pcmtr_params =
        context.GetDoubleCommonAttr("local_life_sort_weight_unit_pcmtr_params")
        .value_or(1.0);
    auto local_life_sort_const_unit_pcmtr_params =
        context.GetDoubleCommonAttr("local_life_sort_const_unit_pcmtr_params")
        .value_or(1.0);
    auto local_life_sort_temperature_unit_pcmtr_params =
        context.GetDoubleCommonAttr("local_life_sort_temperature_unit_pcmtr_params")
        .value_or(1.0);


    auto local_live_pay_order_score_accessor =
        context.GetDoubleItemAttr("local_live_pay_order_score");
    auto local_live_gmv_score_accessor =
        context.GetDoubleItemAttr("local_live_gmv_score");
    auto local_live_evtr_score_accessor =
        context.GetDoubleItemAttr("local_live_evtr_score");
    auto local_live_ctr_score_accessor =
        context.GetDoubleItemAttr("local_live_ctr_score");
    auto local_live_lvtr_score_accessor =
        context.GetDoubleItemAttr("local_live_lvtr_score");
    auto local_live_in_lvtr_score_accessor =
        context.GetDoubleItemAttr("local_live_in_lvtr_score");
    auto locallife_cmtr_score_accessor =
        context.GetDoubleItemAttr("locallife_cmtr_score");
    auto locallife_ctr_submit_order_score_accessor =
        context.GetDoubleItemAttr("locallife_ctr_submit_order_score");
    auto locallife_ctr_pay_order_cnt_score_accessor =
        context.GetDoubleItemAttr("locallife_ctr_pay_order_cnt_score");
    auto locallife_wtr_score_accessor =
        context.GetDoubleItemAttr("locallife_wtr_score");
    auto locallife_unit_pctr_score_accessor =
        context.GetDoubleItemAttr("unit_pctr");
    auto locallife_unit_pinlvtr_score_accessor =
        context.GetDoubleItemAttr("unit_pinlvtr");
    auto locallife_unit_pwtr_score_accessor =
        context.GetDoubleItemAttr("unit_pwtr");
    auto locallife_unit_pinetr_score_accessor =
        context.GetDoubleItemAttr("unit_pinetr");
    auto locallife_unit_pcmtr_score_accessor =
        context.GetDoubleItemAttr("unit_pcmtr");

    // 获取 item score accessor
    auto final_score_accessor = context.SetDoubleItemAttr("final_score");

    int debug_fliter_glag = 0;
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      double final_score = 1.0;
      auto pay_order_score = local_live_pay_order_score_accessor(result).value_or(0.0);
      auto gmv_score = local_live_gmv_score_accessor(result).value_or(0.0);
      auto evtr_score = local_live_evtr_score_accessor(result).value_or(0.0);
      auto ctr_score = local_live_ctr_score_accessor(result).value_or(0.0);
      auto lvtr_score = local_live_lvtr_score_accessor(result).value_or(0.0);
      auto in_lvtr_score = local_live_in_lvtr_score_accessor(result).value_or(0.0);
      auto cmtr_score = locallife_cmtr_score_accessor(result).value_or(0.0);
      auto ctr_submit_order_score = locallife_ctr_submit_order_score_accessor(result).value_or(0.0);
      auto ctr_pay_order_cnt_score = locallife_ctr_pay_order_cnt_score_accessor(result).value_or(0.0);
      auto wtr_score = locallife_wtr_score_accessor(result).value_or(0.0);
      auto unit_pctr_score = locallife_unit_pctr_score_accessor(result).value_or(0.0);
      auto unit_pinlvtr_score = locallife_unit_pinlvtr_score_accessor(result).value_or(0.0);
      auto unit_pwtr_score = locallife_unit_pwtr_score_accessor(result).value_or(0.0);
      auto unit_pinetr_score = locallife_unit_pinetr_score_accessor(result).value_or(0.0);
      auto unit_pcmtr_score = locallife_unit_pcmtr_score_accessor(result).value_or(0.0);

      // final_score = final_score * pay_orde 相关得分
      final_score *= livestream::LivePow(local_life_sort_const_pay_order_params +
      pay_order_score * local_life_sort_weight_pay_order_params,
      local_life_sort_temperature_pay_order_params);
      // final_score = final_score * gmv 相关得分
      final_score *= livestream::LivePow(local_life_sort_const_gmv_params +
      gmv_score * local_life_sort_weight_gmv_params, local_life_sort_temperature_gmv_params);
      // final_score = final_score * sim_evtr 相关得分
      final_score *= livestream::LivePow(local_life_sort_const_sim_pctr_params +
      evtr_score * local_life_sort_weight_sim_pctr_params, local_life_sort_temperature_sim_pctr_params);
      // final_score = final_score * sim_ctr 相关得分
      final_score *= livestream::LivePow(local_life_sort_const_sim_prtr_params +
      ctr_score * local_life_sort_weight_sim_prtr_params, local_life_sort_temperature_sim_prtr_params);
      // final_score = final_score * sim_lvtr 相关得分
      final_score *= livestream::LivePow(local_life_sort_const_sim_plvtr_params +
      lvtr_score * local_life_sort_weight_sim_plvtr_params, local_life_sort_temperature_sim_plvtr_params);
      // final_score = final_score * sim_in_lvtr 相关得分
      final_score *= livestream::LivePow(local_life_sort_const_sim_pin_lvtr_params +
      in_lvtr_score * local_life_sort_weight_sim_pin_lvtr_params,
      local_life_sort_temperature_sim_pin_lvtr_params);
      // final_score = final_score * sim_cmt 相关得分
      final_score *= livestream::LivePow(local_life_sort_const_sim_cmt_params +
      cmtr_score * local_life_sort_weight_sim_cmt_params,
      local_life_sort_temperature_sim_cmt_params);
      // final_score = final_score * sim_wtr 相关得分
      final_score *= livestream::LivePow(local_life_sort_const_sim_wtr_params +
      wtr_score * local_life_sort_weight_sim_wtr_params,
      local_life_sort_temperature_sim_wtr_params);
      // final_score = final_score * sim_sumbit 相关得分
      final_score *= livestream::LivePow(local_life_sort_const_sim_submit_params +
      ctr_submit_order_score * local_life_sort_weight_sim_submit_params,
      local_life_sort_temperature_sim_submit_params);
      // final_score = final_score * sim_pay_order_cnt 相关得分
      final_score *= livestream::LivePow(local_life_sort_const_sim_pay_order_cnt_params +
      ctr_pay_order_cnt_score * local_life_sort_weight_sim_pay_order_cnt_params,
      local_life_sort_temperature_sim_pay_order_cnt_params);
      // final_score = final_score * unit_pctr 相关得分
      final_score *= livestream::LivePow(local_life_sort_const_unit_pctr_params +
      unit_pctr_score * local_life_sort_weight_unit_pctr_params,
      local_life_sort_temperature_unit_pctr_params);
      // final_score = final_score * unit_pinlvtr 相关得分
      final_score *= livestream::LivePow(local_life_sort_const_unit_pinlvtr_params +
      unit_pinlvtr_score * local_life_sort_weight_unit_pinlvtr_params,
      local_life_sort_temperature_unit_pinlvtr_params);
      // final_score = final_score * unit_pwtr 相关得分
      final_score *= livestream::LivePow(local_life_sort_const_unit_pwtr_params +
      unit_pwtr_score * local_life_sort_weight_unit_pwtr_params,
      local_life_sort_temperature_unit_pwtr_params);
      // final_score = final_score * unit_pinetr 相关得分
      final_score *= livestream::LivePow(local_life_sort_const_unit_pinetr_params +
      unit_pinetr_score * local_life_sort_weight_unit_pinetr_params,
      local_life_sort_temperature_unit_pinetr_params);
      // final_score = final_score * unit_pcmtr 相关得分
      final_score *= livestream::LivePow(local_life_sort_const_unit_pcmtr_params +
      unit_pcmtr_score * local_life_sort_weight_unit_pcmtr_params,
      local_life_sort_temperature_unit_pcmtr_params);

      final_score_accessor(result, final_score * local_life_sort_defalut_value);
    });
    return true;
  }

  // NOTE(gaoyunpeng03) 获取本地生活融合分计算, 支持更灵活的 abtest 参数配置
  static bool GetLocalLiveMultiplyScoreV2(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    // 获取 item score accessor
    auto final_score_accessor = context.SetDoubleItemAttr("final_score");
    auto local_life_sort_defalut_value =
        context.GetDoubleCommonAttr("local_life_sort_defalut_value").value_or(1.0);

    // 解析乘法公式 abtest 参数, "locallife_in_lvtr:100,2.0;sim_pwtr:1.0,0.5;sim_pin_etr:10,0.5"
    auto rank_score_params =
    context.GetStringCommonAttr("local_life_rank_score_params").value_or("");
    std::vector<absl::string_view> raw_params =
        absl::StrSplit(rank_score_params.data(), ";", absl::SkipWhitespace());
    std::vector<std::pair<std::function<absl::optional<double>(
        const CommonRecoResult &)>, std::vector<double>>> pxtr_params_list;
    for (int i = 0; i < raw_params.size(); ++i) {
      auto raw_param = raw_params[i];
      std::vector<absl::string_view> kv = absl::StrSplit(raw_param, ":", absl::SkipWhitespace());
      if (kv.size() != 2) continue;
      auto pxtr_name = kv[0];
      auto pxtr_accessor = context.GetDoubleItemAttr(pxtr_name);
      std::vector<absl::string_view> values = absl::StrSplit(kv[1], ",", absl::SkipWhitespace());
      std::vector<double> params_list;
      if (values.size() != 2) continue;
      for (int j = 0; j < values.size(); ++j) {
        auto value = 0.0;
        if (absl::SimpleAtod(values[j], &value)) {
          params_list.emplace_back(value);
        }
      }
      if (params_list.size() != 2) continue;
      pxtr_params_list.emplace_back(pxtr_accessor, params_list);
    }

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      double final_score = local_life_sort_defalut_value;
      for (auto param_pair : pxtr_params_list) {
        auto pxtr_accessor = param_pair.first;
        auto pxtr_score = pxtr_accessor(result).value_or(0.0);
        auto params = param_pair.second;
        auto weight_param = params[0];
        auto temp_param = params[1];
        final_score *= livestream::LivePow(1 + pxtr_score * weight_param, temp_param);
      }
      final_score_accessor(result, final_score);
    });
    return true;
  }

  // NOTE(wangxuming) 本地生活 geohash 发送直播间距离
  static bool LocalLifeGeoHashSendItemAttr(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    auto need_send_item_accessor = context.GetDoubleItemAttr("need_send_item_attr");
    auto local_life_geohash_retrieval_live_accessor =
    context.GetIntListItemAttr("local_life_geohash_retrieval_live");
    auto local_life_retrieval_result_list_accessor =
    context.SetIntListItemAttr("local_life_retrieval_result_list");
    auto local_life_send_item_attr_list_accessor =
    context.SetDoubleListItemAttr("local_life_send_item_attr_list");

    thread_local folly::F14FastSet<int64> live_set;
    live_set.clear();

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto tmp_item_attr = need_send_item_accessor(result);
      if (!tmp_item_attr) {
        return;
      }
      thread_local std::vector<int64> local_life_retrieval_result_list;
      thread_local std::vector<double> local_life_send_item_attr_list;
      local_life_retrieval_result_list.clear();
      local_life_send_item_attr_list.clear();
      auto live_list = local_life_geohash_retrieval_live_accessor(result);

      int result_size = 0;
      if (live_list) {
        result_size += live_list->size();
      }

      local_life_retrieval_result_list.reserve(result_size);

      if (live_list) {
        for (int i = 0; i < live_list->size(); ++i) {
          int64 live_id = live_list->at(i);
          if (live_set.find(live_id) == live_set.end()) {
            live_set.insert(live_id);
            local_life_retrieval_result_list.emplace_back(live_id);
          }
        }
      }

      local_life_send_item_attr_list.resize(local_life_retrieval_result_list.size(), *tmp_item_attr);

      local_life_retrieval_result_list_accessor(result, local_life_retrieval_result_list);
      local_life_send_item_attr_list_accessor(result, local_life_send_item_attr_list);
    });

    return true;
  }

  static bool CalcLTReportedTag(const CommonRecoLightFunctionContext &context,
                                  RecoResultConstIter begin, RecoResultConstIter end) {
    auto aid = context.GetIntItemAttr("author_id");
    auto lt_reported_aids_list =
        context.GetIntListCommonAttr("lt_reported_aids_list").value_or(absl::Span<const int64>());
    auto output_label_attr = context.SetIntItemAttr("is_lt_reported_tag");
    if (lt_reported_aids_list.empty()) return true;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto aid_value = aid(result).value_or(-1);
      for (auto item : lt_reported_aids_list) {
        if (aid_value == item) {
          output_label_attr(result, 1);
          break;
        }
      }
    });
    return true;
  }

  // NOTE(litianshi03) 获取房产人群包用户
  static bool CalcHousePeopleFlag(const CommonRecoLightFunctionContext &context,
                                  RecoResultConstIter begin, RecoResultConstIter end) {
    auto house_user_group_feasury_key_list =
        context.GetIntListCommonAttr("house_user_group_feasury_key_list")
        .value_or(absl::Span<const int64>());
    auto enable_judge_house_user_by_action =
        context.GetIntCommonAttr("enable_judge_house_user_by_action").value_or(0);
    auto user_house_interact_list =
        context.GetIntListCommonAttr("uHouseInteractObjectIdList")
        .value_or(absl::Span<const int64>());
    folly::F14FastSet<int64> user_key_set;
    for (auto user_key : house_user_group_feasury_key_list) {
      user_key_set.insert(user_key);
    }
    auto user_cluster_list =
        context.GetIntListCommonAttr("uPropertyUserCluster")
        .value_or(absl::Span<const int64>());

    int house_user_flag = 0;
    for (auto user_cluster : user_cluster_list) {
      if (user_key_set.count(user_cluster)) {
        house_user_flag = 1;
        break;
      }
    }
    if (enable_judge_house_user_by_action == 1) {
      house_user_flag = (user_house_interact_list.empty() ? 0 : 1);
    }

    context.SetIntCommonAttr("is_house_people_flag", house_user_flag);
    return true;
  }

  // NOTE(litianshi03) 获取房产高质量用户
  static bool CalcHouseHighQualityPeopleFlag(
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
    auto house_high_quality_feasury_key_list =
        context.GetIntListCommonAttr("house_live_high_quality_feasury_key_list")
        .value_or(absl::Span<const int64>());
    folly::F14FastSet<int64> user_key_set;
    for (auto user_key : house_high_quality_feasury_key_list) {
      user_key_set.insert(user_key);
    }
    auto user_cluster_list =
        context.GetIntListCommonAttr("uPropertyUserCluster")
        .value_or(absl::Span<const int64>());

    int house_user_flag = 0;
    for (auto user_cluster : user_cluster_list) {
      if (user_key_set.count(user_cluster)) {
        house_user_flag = 1;
        break;
      }
    }

    context.SetIntCommonAttr("is_house_high_quality_people_flag", house_user_flag);
    return true;
  }

  // NOTE(litianshi03) 获取房产指定城市用户
  static bool CalcHouseTargetCityUserFlag(
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
    auto house_live_target_city_list =
        context.GetIntListCommonAttr("house_live_target_city_list")
        .value_or(absl::Span<const int64>());
    folly::F14FastSet<int64> target_city_set;
    for (auto city_id : house_live_target_city_list) {
      target_city_set.insert(city_id);
    }
    auto user_city_id =
        context.GetIntCommonAttr("user_city_id")
        .value_or(0);

    int target_city_user_flag = 0;
    if (user_city_id != 0 && target_city_set.count(user_city_id)) {
      target_city_user_flag = 1;
    }

    context.SetIntCommonAttr("is_house_target_city_user_flag", target_city_user_flag);
    return true;
  }

  static bool CalcHouseUserClusterFilterFlag(const CommonRecoLightFunctionContext &context,
                                  RecoResultConstIter begin, RecoResultConstIter end) {
    auto house_user_group_feasury_key_list =
        context.GetIntListCommonAttr("house_user_cluster_list")
        .value_or(absl::Span<const int64>());
    auto user_cluster_list =
        context.GetIntListCommonAttr("uPropertyUserCluster")
        .value_or(absl::Span<const int64>());

    auto is_sign_house_live_accessor =
        context.GetIntItemAttr("lLiveHouseIsLiveTag");
    auto is_una_house_live_accessor =
        context.GetIntItemAttr("lLiveHouseIsUnaLiveTag");
    auto is_house_live_accessor =
        context.GetIntItemAttr("lLiveIsHouseLiveTag");
    auto house_user_cluster_filter_flag_accessor =
        context.SetIntItemAttr("house_user_cluster_filter_flag");

    int house_user_flag = 0;
    for (auto house_user_type : house_user_group_feasury_key_list) {
      if (house_user_flag == 1) {
        break;
      }
      for (auto user_cluster : user_cluster_list) {
        if (house_user_type == user_cluster) {
          house_user_flag = 1;
          break;
        }
      }
    }

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
        auto is_sign_house_live = is_sign_house_live_accessor(result).value_or(0);
        auto is_una_house_live = is_una_house_live_accessor(result).value_or(0);
        auto is_house_live = is_house_live_accessor(result).value_or(0);
        if (house_user_flag == 1 &&
            (is_sign_house_live == 1 || is_una_house_live == 1 || is_house_live == 1)) {
            house_user_cluster_filter_flag_accessor(result, 1);
        } else {
            house_user_cluster_filter_flag_accessor(result, 0);
        }
    });

    return true;
  }

  static bool CalcMcHouseReasonBoostScore(
    const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin,
    RecoResultConstIter end) {
    auto mc_house_boost_reason = context.GetIntCommonAttr("mc_house_boost_reason").value_or(0);
    auto mc_house_boost_coeff = context.GetDoubleCommonAttr("mc_house_boost_coeff").value_or(1.0);

    auto is_house_live_tag_accessor = context.GetIntItemAttr("lLiveHouseIsLiveTag");
    auto is_house_live_new_tag_accessor = context.GetIntItemAttr("lLiveIsHouseLiveTag");
    auto reason_list_accessor = context.GetIntListItemAttr("reason_list");
    auto mc_score_accessor = context.GetDoubleItemAttr("mc_csqs_house");

    auto boost_mc_score_accessor = context.SetDoubleItemAttr("mc_csqs_house");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
        auto is_house_live_tag = is_house_live_tag_accessor(result).value_or(0);
        auto is_house_live_new_tag = is_house_live_new_tag_accessor(result).value_or(0);
        is_house_live_tag = is_house_live_tag > 0 ? is_house_live_tag : is_house_live_new_tag;
        auto reason_list = reason_list_accessor(result).value_or(absl::Span<const int64>());
        auto mc_score = mc_score_accessor(result).value_or(0.0);
        if (is_house_live_tag == 1) {
          double boost = 1.0;
          for (auto reason : reason_list) {
            if (reason == mc_house_boost_reason) {
              boost = mc_house_boost_coeff;
              break;
            }
          }
          boost_mc_score_accessor(result, boost * mc_score);
        } else {
          boost_mc_score_accessor(result, mc_score);
        }
    });

    return true;
  }

  static bool HouseMcOutHighQualityAuthorBoost(
    const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin,
    RecoResultConstIter end) {
    auto house_mc_high_quality_author_boost_param =
        context.GetStringCommonAttr("house_mc_high_quality_author_boost_param").value_or("");
    auto house_mc_high_quality_author_boost_map =
        ParseWeightParam(house_mc_high_quality_author_boost_param);

    auto is_high_quality_house_author_accessor = context.GetIntItemAttr("is_high_quality_house_author");
    auto is_house_live_tag_accessor = context.GetIntItemAttr("lLiveHouseIsLiveTag");
    auto is_house_live_new_tag_accessor = context.GetIntItemAttr("lLiveIsHouseLiveTag");
    auto reason_list_accessor = context.GetIntListItemAttr("reason_list");
    auto mc_score_accessor = context.GetDoubleItemAttr("mc_csqs_house_addition");

    auto boost_mc_score_accessor = context.SetDoubleItemAttr("mc_csqs_house_addition");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
        auto is_high_quality_house_author = is_high_quality_house_author_accessor(result).value_or(0);
        auto is_house_live_tag = is_house_live_tag_accessor(result).value_or(0);
        auto is_house_live_new_tag = is_house_live_new_tag_accessor(result).value_or(0);
        is_house_live_tag = is_house_live_tag > 0 ? is_house_live_tag : is_house_live_new_tag;
        auto reason_list = reason_list_accessor(result).value_or(absl::Span<const int64>());
        auto mc_score = mc_score_accessor(result).value_or(0.0);
        if (is_house_live_tag == 1 && is_high_quality_house_author == 1) {
          double boost = 1.0;
          for (auto reason : reason_list) {
            auto iter = house_mc_high_quality_author_boost_map.find(std::to_string(reason));
            if (iter != house_mc_high_quality_author_boost_map.end()) {
              boost = iter->second;
              // 保证需要提权的 reason 保量分数至少大于 1
               mc_score += 1e-8;
              break;
            }
          }
          boost_mc_score_accessor(result, boost * mc_score);
        } else {
          boost_mc_score_accessor(result, mc_score);
        }
    });

    return true;
  }

  static bool HouseMcInHighQualityAuthorBoost(
    const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin,
    RecoResultConstIter end) {
    auto house_mc_high_quality_author_boost_param =
        context.GetStringCommonAttr("house_mc_high_quality_author_boost_param").value_or("");
    auto house_mc_high_quality_author_boost_map =
        ParseWeightParam(house_mc_high_quality_author_boost_param);

    auto is_high_quality_house_author_accessor = context.GetIntItemAttr("is_high_quality_house_author");
    auto is_house_live_tag_accessor = context.GetIntItemAttr("lLiveHouseIsLiveTag");
    auto is_house_live_new_tag_accessor = context.GetIntItemAttr("lLiveIsHouseLiveTag");
    auto reason_list_accessor = context.GetIntListItemAttr("reason_list");
    auto mc_score_accessor = context.GetDoubleItemAttr("mc_csqs_house");

    auto boost_mc_score_accessor = context.SetDoubleItemAttr("mc_csqs_house");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
        auto is_high_quality_house_author = is_high_quality_house_author_accessor(result).value_or(0);
        auto is_house_live_tag = is_house_live_tag_accessor(result).value_or(0);
        auto is_house_live_new_tag = is_house_live_new_tag_accessor(result).value_or(0);
        is_house_live_tag = is_house_live_tag > 0 ? is_house_live_tag : is_house_live_new_tag;
        auto reason_list = reason_list_accessor(result).value_or(absl::Span<const int64>());
        auto mc_score = mc_score_accessor(result).value_or(0.0);
        if (is_house_live_tag == 1 && is_high_quality_house_author == 1) {
          double boost = 1.0;
          for (auto reason : reason_list) {
            auto iter = house_mc_high_quality_author_boost_map.find(std::to_string(reason));
            if (iter != house_mc_high_quality_author_boost_map.end()) {
              boost = iter->second;
              // 保证需要提权的 reason 保量分数至少大于 0
              mc_score += 1e-8;
              break;
            }
          }
          boost_mc_score_accessor(result, boost * mc_score);
        } else {
          boost_mc_score_accessor(result, mc_score);
        }
    });

    return true;
  }

  static bool CalcMcHouseReasonListBoostScore(
    const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin,
    RecoResultConstIter end) {
    auto mc_live_house_reason_boost_param =
        context.GetStringCommonAttr("mc_live_house_reason_boost_param").value_or("");
    auto mc_live_house_reason_boost_map = ParseWeightParam(mc_live_house_reason_boost_param);

    auto is_house_live_tag_accessor = context.GetIntItemAttr("lLiveHouseIsLiveTag");
    auto is_house_live_new_tag_accessor = context.GetIntItemAttr("lLiveIsHouseLiveTag");
    auto reason_list_accessor = context.GetIntListItemAttr("reason_list");
    auto mc_score_accessor = context.GetDoubleItemAttr("mc_csqs_house_addition");

    auto boost_mc_score_accessor = context.SetDoubleItemAttr("mc_csqs_house_addition");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
        auto is_house_live_tag = is_house_live_tag_accessor(result).value_or(0);
        auto is_house_live_new_tag = is_house_live_new_tag_accessor(result).value_or(0);
        is_house_live_tag = is_house_live_tag > 0 ? is_house_live_tag : is_house_live_new_tag;
        auto reason_list = reason_list_accessor(result).value_or(absl::Span<const int64>());
        auto mc_score = mc_score_accessor(result).value_or(0.0);
        if (is_house_live_tag == 1) {
          double boost = 1.0;
          for (auto reason : reason_list) {
            auto iter = mc_live_house_reason_boost_map.find(std::to_string(reason));
            if (iter != mc_live_house_reason_boost_map.end()) {
              boost = iter->second;
              // 保证需要提权的 reason 保量分数至少大于 1
              mc_score += 1;
              break;
            }
          }
          boost_mc_score_accessor(result, boost * mc_score);
        } else {
          boost_mc_score_accessor(result, mc_score);
        }
    });

    return true;
  }

  static bool CalcMcHouseResetModelScore(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    auto addition_house_mc_filter_model_score_threshold =
      context.GetDoubleCommonAttr("addition_house_mc_filter_model_score_threshold");
    auto addition_mc_house_predict_model_value_accessor =
      context.GetDoubleItemAttr("addition_mc_house_predict_model_value");
    auto reset_addition_mc_house_predict_model_value_accessor =
      context.SetDoubleItemAttr("addition_mc_house_predict_model_value");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto addition_mc_house_predict_model_value =
        addition_mc_house_predict_model_value_accessor(result).value_or(0.0);
      if (addition_mc_house_predict_model_value < addition_house_mc_filter_model_score_threshold) {
        reset_addition_mc_house_predict_model_value_accessor(result, 0.0);
      } else {
        reset_addition_mc_house_predict_model_value_accessor(result, addition_mc_house_predict_model_value);
      }
    });

    return true;
  }

  static bool CalcMcHouseModelScoreFilterFlag(const CommonRecoLightFunctionContext &context,
                                              RecoResultConstIter begin, RecoResultConstIter end) {
    auto addition_house_mc_filter_model_score_threshold =
      context.GetDoubleCommonAttr("addition_house_mc_filter_model_score_threshold");
    auto addition_mc_house_predict_model_value_accessor =
      context.GetDoubleItemAttr("addition_mc_house_predict_model_value");
    auto is_house_live_accessor =
      context.GetIntItemAttr("lLiveHouseIsLiveTag");
    auto is_house_live_new_tag_accessor =
      context.GetIntItemAttr("lLiveIsHouseLiveTag");
    auto addition_mc_house_model_filter_flag_accessor =
      context.SetIntItemAttr("addition_mc_house_model_filter_flag");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto addition_mc_house_predict_model_value =
        addition_mc_house_predict_model_value_accessor(result).value_or(0.0);
      auto is_house_live = is_house_live_accessor(result).value_or(0);
      auto is_house_live_new_tag = is_house_live_new_tag_accessor(result).value_or(0);
      is_house_live = is_house_live > 0 ? is_house_live : is_house_live_new_tag;
      if (is_house_live != 1
          || addition_mc_house_predict_model_value < addition_house_mc_filter_model_score_threshold) {
        addition_mc_house_model_filter_flag_accessor(result, 0);
      } else {
        addition_mc_house_model_filter_flag_accessor(result, 1);
      }
    });

    return true;
  }

  // NOTE(litianshi03) 获取房产作者策略 exptag 提权系数
  static bool CalcHouseAuthorBoostCoeff(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    auto house_author_boost_param =
        context.GetStringCommonAttr("house_author_boost_param").value_or("");
    auto house_author_boost_map = ParseWeightParam(house_author_boost_param);

    auto reason_list_accessor = context.GetIntListItemAttr("reason_list");
    auto house_author_boost_coeff_accessor =
        context.SetDoubleItemAttr("house_author_boost_coeff");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto reason_list = reason_list_accessor(result).value_or(absl::Span<const int64>());
      double coeff_result = 1.0;
      for (auto reason_tag : reason_list) {
        auto iter = house_author_boost_map.find(std::to_string(reason_tag));
        if (iter != house_author_boost_map.end()) {
          coeff_result = iter->second;
          break;
        }
      }
      house_author_boost_coeff_accessor(result, coeff_result);
    });
    return true;
  }

  // NOTE(litianshi03) 计算房产作者策略提权 V2
  static bool CalcHouseAuthorBoostCoeffV2(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto house_author_boost_param_v2 =
        context.GetStringCommonAttr("house_author_boost_param_v2").value_or("");
    auto house_author_boost_map_v2 = ParseWeightParam(house_author_boost_param_v2);
    auto exptag_list = context.GetIntListCommonAttr("house_author_boost_exptag_list_v2")
        .value_or(absl::Span<const int64>());
    folly::F14FastSet<int64> exptag_set;
    for (auto exptag : exptag_list) {
      exptag_set.insert(exptag);
    }
    auto is_house_live_accessor = context.GetIntItemAttr("lLiveHouseIsLiveTag");
    auto is_house_live_new_tag_accessor = context.GetIntItemAttr("lLiveIsHouseLiveTag");
    auto reason_list_accessor = context.GetIntListItemAttr("reason_list");
    auto boost_author_level_accessor = context.GetStringItemAttr("boost_author_level");
    auto score_input_accessor =
        context.GetDoubleItemAttr("fr_raw_qs_recruit");
    auto score_output_accessor =
        context.SetDoubleItemAttr("fr_raw_qs_recruit");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto is_house_live = is_house_live_accessor(result).value_or(0);
      auto is_house_live_new_tag = is_house_live_new_tag_accessor(result).value_or(0);
      is_house_live = is_house_live > 0 ? is_house_live : is_house_live_new_tag;
      auto reason_list = reason_list_accessor(result).value_or(absl::Span<const int64>());
      auto boost_author_level = boost_author_level_accessor(result).value_or("");
      auto score_input = score_input_accessor(result).value_or(0.0);
      double coeff = 1.0;
      double score_result = score_input;
      for (auto reason_tag : reason_list) {
        if (exptag_set.count(reason_tag) > 0 && is_house_live > 0) {
          auto iter = house_author_boost_map_v2.find(boost_author_level);
          if (iter != house_author_boost_map_v2.end()) {
            coeff = iter->second;
            score_result = score_result * coeff;
          }
          break;
        }
      }
      score_output_accessor(result, score_result);
    });
    return true;
  }

  // NOTE(litianshi03) 获取房产城市降权系数
  static bool CalcHouseCityDegradeCoeff(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    auto house_live_city_degrade_conf =
        context.GetStringCommonAttr("house_live_city_degrade_conf").value_or("");
    std::string house_live_city_degrade_conf_str = house_live_city_degrade_conf.data();
    auto house_live_city_degrade_conf_map = ParseWeightParam(house_live_city_degrade_conf_str);

    auto is_house_target_user_flag =
        context.GetIntCommonAttr("is_house_target_user_flag").value_or(0);
    auto user_city_id =
        context.GetIntCommonAttr("user_city_id").value_or(0);
    std::string user_city_id_str = std::to_string(user_city_id);
    auto user_city_name =
        context.GetStringCommonAttr("user_city_name").value_or("");
    std::string user_city_name_str = user_city_name.data();

    auto house_live_tag_accessor = context.GetIntItemAttr("lLiveHouseIsLiveTag");
    auto is_house_live_new_tag_accessor = context.GetIntItemAttr("lLiveIsHouseLiveTag");
    auto house_una_live_tag_accessor = context.GetIntItemAttr("is_una_house_live");
    auto house_building_city_id_accessor = context.GetIntItemAttr("lLiveHouseBuildingCityId");
    auto house_building_city_name_accessor = context.GetStringItemAttr("lLiveHouseBuildingCityName");
    auto ip_region_accessor = context.GetStringItemAttr("ip_region");
    auto house_author_city_id_accessor = context.GetIntItemAttr("lLiveHouseBuildingAuthorCityId");
    auto house_author_city_name_accessor = context.GetStringItemAttr("lLiveHouseBuildingAuthorCityName");

    auto house_live_city_degrade_coeff_accessor =
        context.SetDoubleItemAttr("house_live_city_degrade_coeff");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      double coeff_result = 1.0;
      auto house_live_tag = house_live_tag_accessor(result).value_or(0);
      auto is_house_live_new_tag = is_house_live_new_tag_accessor(result).value_or(0);
      house_live_tag = house_live_tag > 0 ? house_live_tag : is_house_live_new_tag;
      auto house_una_live_tag = house_live_tag_accessor(result).value_or(0);
      // 限定人群包内部生效
      if (is_house_target_user_flag == 1 && (house_live_tag == 1 || house_una_live_tag == 1)) {
        std::string city_id = std::to_string(house_building_city_id_accessor(result).value_or(0));
        auto house_building_city_name = house_building_city_name_accessor(result).value_or("");
        std::string city_name = house_building_city_name.data();
        auto ip_region = ip_region_accessor(result).value_or("");
        std::string ip_region_str = ip_region.data();
        if (city_id == "" || city_id == "0") {
          city_id = std::to_string(house_author_city_id_accessor(result).value_or(0));
        }
        if (city_name == "") {
          if (ip_region_str != "") {
            city_name = ip_region_str;
          } else {
            auto house_author_city_name = house_author_city_name_accessor(result).value_or("");
            std::string house_author_city_name_str = house_author_city_name.data();
            city_name = house_author_city_name_str;
          }
        }

        auto iter = house_live_city_degrade_conf_map.find(city_id);
        if (iter != house_live_city_degrade_conf_map.end()) {
          // 外市降权
          if (city_id != user_city_id_str && city_name.find(user_city_name_str) == std::string::npos) {
            coeff_result = iter->second;
          }
        }
      }
      house_live_city_degrade_coeff_accessor(result, coeff_result);
    });
    return true;
  }

  // NOTE(litianshi03) 获取房产粗排保量额外队列 Flag 获取
  static bool CalcHouseMcAdditionFlag(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto enable_house_live_mc_queue_addition =
        context.GetIntCommonAttr("enable_house_live_mc_queue_addition").value_or(0);
    auto enable_house_holdout_close_mc_queue_addition =
        context.GetIntCommonAttr("enable_house_holdout_close_mc_queue_addition").value_or(0);
    auto is_house_people_flag =
        context.GetIntCommonAttr("is_house_people_flag").value_or(0);
    auto enable_house_live_mc_filter_addition =
        context.GetIntCommonAttr("enable_house_live_mc_filter_addition").value_or(0);
    auto xtr_weight_param =
        context.GetStringCommonAttr("house_live_mc_filter_score_sort_param_addition").value_or("");
    std::string xtr_weight_param_str = xtr_weight_param.data();
    auto xtr_weight_map = ParseWeightParam(xtr_weight_param_str);
    auto house_live_mc_filter_threshold_addition =
        context.GetDoubleCommonAttr("house_live_mc_filter_threshold_addition").value_or(0.0);

    auto house_live_tag_accessor = context.GetIntItemAttr("lLiveHouseIsLiveTag");
    auto is_house_live_new_tag_accessor = context.GetIntItemAttr("lLiveIsHouseLiveTag");
    auto history_lvtr_accessor = context.GetDoubleItemAttr("lLiveHouseAuthorHistoryLvtr");
    auto history_wtr_accessor = context.GetDoubleItemAttr("lLiveHouseAuthorHistoryWtr");
    auto history_cmtr_accessor = context.GetDoubleItemAttr("lLiveHouseAuthorHistoryCmtr");
    auto history_plctr_accessor = context.GetDoubleItemAttr("lLiveHouseAuthorHistoryPlctr");
    auto flag_accessor =
        context.SetIntItemAttr("mc_csqf_house_addition");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      int flag = 0;
      if (enable_house_holdout_close_mc_queue_addition == 1) {
        flag_accessor(result, flag);
        return;
      }
      if (is_house_people_flag != 1) {
        flag_accessor(result, flag);
        return;
      }

      auto house_live_tag = house_live_tag_accessor(result).value_or(0);
      auto is_house_live_new_tag = is_house_live_new_tag_accessor(result).value_or(0);
      house_live_tag = house_live_tag > 0 ? house_live_tag : is_house_live_new_tag;
      if (enable_house_live_mc_queue_addition == 1 && house_live_tag > 0) {
        flag = 1;
        // 历史 xtr 过滤
        if (enable_house_live_mc_filter_addition == 1) {
          auto lvtr_weight = GetOrDefault(xtr_weight_map, "lvtr", 0.0);
          auto wtr_weight = GetOrDefault(xtr_weight_map, "wtr", 0.0);
          auto cmtr_weight = GetOrDefault(xtr_weight_map, "cmtr", 0.0);
          auto plctr_weight = GetOrDefault(xtr_weight_map, "plctr", 0.0);
          auto history_lvtr = history_lvtr_accessor(result).value_or(0.0);
          auto history_wtr = history_wtr_accessor(result).value_or(0.0);
          auto history_cmtr = history_cmtr_accessor(result).value_or(0.0);
          auto history_plctr = history_plctr_accessor(result).value_or(0.0);
          double house_score = lvtr_weight * history_lvtr +
                               wtr_weight * history_wtr +
                               cmtr_weight * history_cmtr +
                               plctr_weight * history_plctr;
          if (house_score < house_live_mc_filter_threshold_addition) {
            flag = 0;
          }
        }
        flag_accessor(result, flag);
        return;
      }
      flag_accessor(result, flag);
    });
    return true;
  }

  static bool CalUniformHouseRankingScore(const CommonRecoLightFunctionContext &context,
                                     RecoResultConstIter begin, RecoResultConstIter end) {
    auto mc_house_predict_model_value_accessor =
        context.GetDoubleItemAttr("mc_house_predict_model_value");
    auto addition_mc_house_predict_model_value_accessor =
        context.GetDoubleItemAttr("addition_mc_house_predict_model_value");
    auto mc_house_plctr_accessor =
        context.GetDoubleItemAttr("mc_house_plctr");

    auto set_score_accessor =
        context.SetDoubleItemAttr("house_ranking_model_score");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double inside_score = mc_house_predict_model_value_accessor(result).value_or(0.0);
      double outside_score = addition_mc_house_predict_model_value_accessor(result).value_or(0.0);
      double mc_house_plctr = mc_house_plctr_accessor(result).value_or(0.0);
      if (mc_house_plctr > 0.0 && (inside_score > 0.0 || outside_score > 0.0)) {
        set_score_accessor(result, std::max(inside_score, outside_score));
      } else {
        set_score_accessor(result, 0.0);
      }
    });
    return true;
  }

  // NOTE(liingtao03) 重置房产精排模型分
  static bool ResetHouseRankingScore(const CommonRecoLightFunctionContext &context,
                                     RecoResultConstIter begin, RecoResultConstIter end) {
    auto top_k =
        context.GetIntCommonAttr("house_ranking_keep_top_k").value_or(1);
    auto non_top_k_default_score =
        context.GetDoubleCommonAttr("house_ranking_non_top_k_default_score").value_or(0.00001);
    std::vector<double> ranking_score_list;
    auto min_house_ranking_model_score =
        context.GetDoubleCommonAttr("min_house_ranking_model_score").value_or(1.0);

    auto get_score_accessor =
        context.GetDoubleItemAttr("house_ranking_model_score");
    auto set_score_accessor =
        context.SetDoubleItemAttr("house_ranking_model_score");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double score = get_score_accessor(result).value_or(0.0);
      auto auto_score = get_score_accessor(result).value_or(0.0);
      if (score > min_house_ranking_model_score) {
        ranking_score_list.push_back(score);
      }
    });
    if (ranking_score_list.size() == 0) {
      return true;
    }

    auto valid_size = ranking_score_list.size();
    std::sort(ranking_score_list.begin(), ranking_score_list.end(), std::greater<double>());
    if (ranking_score_list.size() > top_k) {
      valid_size = top_k;
    }
    double sum_score =
        std::accumulate(ranking_score_list.begin(), ranking_score_list.begin() + valid_size, 0.0);
    int times = static_cast<int>(sum_score / min_house_ranking_model_score * 2.0);
    int diff = times - valid_size;

    auto min_reset_score =
      ranking_score_list.size() > top_k ? ranking_score_list[top_k - 1] : ranking_score_list.back();
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double score = get_score_accessor(result).value_or(0.0);
      if (score < min_reset_score) {
        if (diff == 0 && score > 0.0) {
          set_score_accessor(result, 0.0);
        }
        if (diff > 0) {
          set_score_accessor(result, non_top_k_default_score);
          diff--;
        }
      }
    });
    return true;
  }

  static bool ResetHouseLiveTag(const CommonRecoLightFunctionContext &context,
                                RecoResultConstIter begin, RecoResultConstIter end) {
    auto sign_house_live_accessor =
        context.GetIntItemAttr("lLiveHouseIsLiveTag");
    auto flower_house_live_accessor =
        context.GetIntItemAttr("lLiveHouseIsFlowerLiveTag");
    auto is_house_target_tail_accessor =
        context.GetIntItemAttr("is_house_target_tail");
    auto final_house_live_accessor =
        context.SetIntItemAttr("lLiveIsHouseLiveTag");

    auto tag_decoration_live_accessor =
        context.SetIntItemAttr("lLiveIsDecorationTag");
    // 泛行业 mock 房产开关
    auto enable_industry_mock_house =
        context.GetIntCommonAttr("enable_industry_mock_house").value_or(0);
    // 要对齐的策略号内容，
    auto config_list_industry_mock_house_live =
        context.GetIntListCommonAttr("config_list_industry_mock_house_live")
        .value_or(absl::Span<int64>());

    auto author_industry_type_list_getter =
        context.GetIntListItemAttr("aAuthorIndustryTypeList");

    // 转换 config_list_industry_mock_house_live 为 set 以便快速查找
    folly::F14FastSet<int64> config_industry_mock_house_set(
        config_list_industry_mock_house_live.begin(), config_list_industry_mock_house_live.end());

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto sign_house_live = sign_house_live_accessor(result).value_or(0);
      auto flower_house_live = flower_house_live_accessor(result).value_or(0);
      auto author_industry_type_list =
        author_industry_type_list_getter(result).value_or(absl::Span<const int64>());
      auto is_house_target_tail = is_house_target_tail_accessor(result).value_or(0);
      if (sign_house_live > 0 || flower_house_live > 0) {
        final_house_live_accessor(result, 1);
      } else {
        final_house_live_accessor(result, 0);
        // 新增逻辑：检查 author_industry_type_list 是否包含 config_list_industry_mock_house_live 中的值
        if (enable_industry_mock_house == 1 && !config_industry_mock_house_set.empty()) {
          for (auto industry_type : author_industry_type_list) {
            // 当策略号匹配上了
            if (config_industry_mock_house_set.count(
              industry_type) > 0 && 1 == is_house_target_tail) {
              final_house_live_accessor(result, 1);
              tag_decoration_live_accessor(result, 1);
              break;
            }
          }
        }
      }
    });
    return true;
  }

static double GetVerticalNormScore(
      const absl::Span<const double>& score_bucket, double score, double max_norm_score, double norm_pow) {
    if (score <= 0.0) {
      return 0.0;
    }
    if (score_bucket.size() == 0) {
      return max_norm_score;
    }
    size_t index = 0;
    for (; index < score_bucket.size(); ++index) {
      if (score < score_bucket[index]) {
        break;
      }
    }

    double step = 1.0 / score_bucket.size();

    if (index == 0) {
      return std::pow(score / score_bucket[0] * step, norm_pow) * max_norm_score;
    }
    if (index >= score_bucket.size()) {
      return max_norm_score;
    }
    return std::pow((score - score_bucket[index - 1]) / (score_bucket[index] - score_bucket[index - 1]) * \
           step + index * step, norm_pow) * max_norm_score;
  }

  static bool CalcVerticalNormScore(const CommonRecoLightFunctionContext &context,
                                RecoResultConstIter begin, RecoResultConstIter end) {
    auto house_bucket_score = context.GetDoubleListCommonAttr("house_bucket_score");
    auto recruit_bucket_score = context.GetDoubleListCommonAttr("recruit_bucket_score");
    auto local_bucket_score = context.GetDoubleListCommonAttr("local_bucket_score");

    auto max_norm_score = context.GetDoubleCommonAttr("vertical_max_norm_score").value_or(1.0);
    auto norm_pow = context.GetDoubleCommonAttr("vertical_norm_pow").value_or(1.0);

    auto enable_recurit_close_recruit_uniform_norm =
      context.GetIntCommonAttr("enable_recurit_close_recruit_uniform_norm").value_or(0);
    auto enable_recurit_close_house_uniform_norm =
      context.GetIntCommonAttr("enable_recurit_close_house_uniform_norm").value_or(0);

    auto is_house_live_accessor = context.GetIntItemAttr("lLiveIsHouseLiveTag");
    auto is_recruit_live_accessor = context.GetIntItemAttr("recruit_priority_attr");
    auto is_local_live_accessor = context.GetIntItemAttr("lIsLocalLife");

    auto house_final_score_accessor = context.GetDoubleItemAttr("house_final_score");
    auto recruit_final_score_accessor = context.GetDoubleItemAttr("recruit_final_score");
    auto local_final_score_accessor = context.GetDoubleItemAttr("local_final_score");

    auto house_final_score_setter = context.SetDoubleItemAttr("house_final_score");
    auto recruit_final_score_setter = context.SetDoubleItemAttr("recruit_final_score");
    auto local_final_score_setter = context.SetDoubleItemAttr("local_final_score");

    std::vector<double> tmp_score_bucket =
      { 1.0, 2.0, 3.0, 4.0, 5.0,
        6.0, 7.0, 8.0, 9.0, 10.0,
        11.0, 12.0, 13.0, 14.0, 15.0,
        16.0, 17.0, 18.0, 19.0, 20.0 };
    auto default_score_bucket = absl::Span<const double>(tmp_score_bucket);

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto is_house_live = is_house_live_accessor(result).value_or(0);
      auto is_recruit_live = is_recruit_live_accessor(result).value_or(0);
      auto is_local_live = is_local_live_accessor(result).value_or(0);

      if (enable_recurit_close_house_uniform_norm == 0) {
        if (is_house_live > 0) {
          auto house_final_score = house_final_score_accessor(result).value_or(0.0);
          house_final_score_setter(result,
            GetVerticalNormScore(house_bucket_score.has_value() && house_bucket_score.value().size() >= 20 ?
                  house_bucket_score.value() : default_score_bucket,
                house_final_score, max_norm_score, norm_pow));
        }
      }
      if (enable_recurit_close_recruit_uniform_norm == 0) {
        if (is_recruit_live > 0) {
          auto recruit_final_score = recruit_final_score_accessor(result).value_or(0.0);
          recruit_final_score_setter(result, GetVerticalNormScore(
                recruit_bucket_score.has_value() && recruit_bucket_score.value().size() >= 20 ?
                  recruit_bucket_score.value() : default_score_bucket,
                recruit_final_score, max_norm_score, norm_pow));
        }
      }
      if (is_local_live > 0) {
        auto local_final_score = local_final_score_accessor(result).value_or(0.0);
        local_final_score_setter(result,
            GetVerticalNormScore(local_bucket_score.has_value() && local_bucket_score.value().size() >= 20 ?
                local_bucket_score.value() : default_score_bucket,
              local_final_score, max_norm_score, norm_pow));
      }
    });

    return true;
  }

  static bool CalcHouseBidIndex(const CommonRecoLightFunctionContext &context,
                                RecoResultConstIter begin, RecoResultConstIter end) {
    auto house_predict_info_list = context.GetDoubleListCommonAttr("house_predict_info_list");
    auto enable_house_bid_by_cvr = context.GetIntCommonAttr("enable_house_bid_by_cvr").value_or(0);
    auto enable_house_bid_by_ctr = context.GetIntCommonAttr("enable_house_bid_by_ctr").value_or(0);
    auto mc_house_plctr = context.GetDoubleItemAttr("mc_house_plctr");
    auto mc_house_cvr = context.GetDoubleItemAttr("mc_house_cvr");
    auto unit_pctr = context.GetDoubleItemAttr("unit_pctr");
    auto is_house_live = context.GetIntItemAttr("lLiveHouseIsLiveTag");
    auto is_house_live_new_tag_accessor = context.GetIntItemAttr("lLiveIsHouseLiveTag");
    auto house_bid_index = context.SetIntItemAttr("house_bid_index");

    std::vector<double> house_predict_score_vec =
      { 0.00000512, 0.00000962, 0.00001385, 0.00001801, 0.00002226,
        0.00002671, 0.00003147, 0.00003669, 0.00004251, 0.00004915,
        0.00005694, 0.00006638, 0.00007826, 0.00009408, 0.00011664,
        0.00015195, 0.00021481, 0.00034986, 0.00075907, 0.56575370 };

    if (house_predict_info_list.has_value() && house_predict_info_list.value().size() >= 20) {
      house_predict_score_vec.clear();
      for (auto predict_score : house_predict_info_list.value()) {
        house_predict_score_vec.push_back(predict_score);
      }
    }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int index = 0;
      auto mc_house_plctr_value = mc_house_plctr(result).value_or(0.0);
      auto mc_house_cvr_value = mc_house_cvr(result).value_or(0.0);
      auto unit_pctr_value = unit_pctr(result).value_or(0.0);
      auto mc_house_xtr = enable_house_bid_by_cvr == 1 ? mc_house_cvr_value : mc_house_plctr_value;
      if (enable_house_bid_by_ctr == 1) {
        mc_house_xtr *= unit_pctr_value;
      }
      auto is_house_live_value = is_house_live(result).value_or(0);
      auto is_house_live_new_tag = is_house_live_new_tag_accessor(result).value_or(0);
      is_house_live_value = is_house_live_value > 0 ? is_house_live_value : is_house_live_new_tag;
      if (is_house_live_value > 0) {
        for (int i = 0; i < house_predict_score_vec.size(); ++i) {
          if (mc_house_xtr < house_predict_score_vec[i]) {
            index = i;
            break;
          }
        }
        if (index >= house_predict_score_vec.size()) {
          index = house_predict_score_vec.size() - 1;
        }
      }
      house_bid_index(result, index);
    });

    return true;
  }

  // NOTE(litianshi03) 获取房产粗排保量额外队列 Score 排序
  static bool CalcHouseMcAdditionScore(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto xtr_weight_param =
        context.GetStringCommonAttr("house_live_mc_author_history_score_sort_param_addition").value_or("");
    std::string xtr_weight_param_str = xtr_weight_param.data();
    auto xtr_weight_map = ParseWeightParam(xtr_weight_param_str);

    auto history_lvtr_accessor = context.GetDoubleItemAttr("lLiveHouseAuthorHistoryLvtr");
    auto history_wtr_accessor = context.GetDoubleItemAttr("lLiveHouseAuthorHistoryWtr");
    auto history_cmtr_accessor = context.GetDoubleItemAttr("lLiveHouseAuthorHistoryCmtr");
    auto history_plctr_accessor = context.GetDoubleItemAttr("lLiveHouseAuthorHistoryPlctr");
    auto score_accessor =
        context.SetDoubleItemAttr("mc_csqs_house_addition");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      double score = 0.0;
      auto lvtr_weight = GetOrDefault(xtr_weight_map, "lvtr", 0.0);
      auto wtr_weight = GetOrDefault(xtr_weight_map, "wtr", 0.0);
      auto cmtr_weight = GetOrDefault(xtr_weight_map, "cmtr", 0.0);
      auto plctr_weight = GetOrDefault(xtr_weight_map, "plctr", 0.0);
      auto history_lvtr = history_lvtr_accessor(result).value_or(0.0);
      auto history_wtr = history_wtr_accessor(result).value_or(0.0);
      auto history_cmtr = history_cmtr_accessor(result).value_or(0.0);
      auto history_plctr = history_plctr_accessor(result).value_or(0.0);
      double house_score = lvtr_weight * history_lvtr +
                           wtr_weight * history_wtr +
                           cmtr_weight * history_cmtr +
                           plctr_weight * history_plctr;
      score_accessor(result, house_score);
    });
    return true;
  }

  // NOTE(litianshi03) 房产高质量用户同市召回地域匹配
  static bool HouseUserCityMatch(const CommonRecoLightFunctionContext &context,
                                 RecoResultConstIter begin, RecoResultConstIter end) {
    auto live_user_city_list =
        context.GetIntListCommonAttr("live_user_city_list_int");

    auto building_city_accessor = context.GetStringItemAttr("lLiveHouseBuildingCityId");
    auto author_signed_city_accessor = context.GetStringItemAttr("lLiveHouseBuildingAuthorCityId");
    auto author_city_accessor = context.GetIntItemAttr("aHouseCityIdKV");
    auto is_target_accessor =
        context.SetIntItemAttr("is_house_live_region_match_item");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      int is_target = 0;
      auto building_city = 0;
      auto author_signed_city = 0;
      if (!absl::SimpleAtoi(building_city_accessor(result).value_or("0"), &building_city)) {
        building_city = 0;
      }
      if (!absl::SimpleAtoi(author_signed_city_accessor(result).value_or("0"), &author_signed_city)) {
        author_signed_city = 0;
      }
      auto author_city = author_city_accessor(result).value_or(0);
      for (auto user_city : live_user_city_list.value()) {
        if (building_city > 0) {
          if (building_city == user_city) {
            is_target = 1;
            break;
          }
        } else if (author_city > 0) {
          if (author_city == user_city) {
            is_target = 1;
            break;
          }
        } else if (author_signed_city > 0) {
          if (author_signed_city == user_city) {
            is_target = 1;
            break;
          }
        }
      }
      is_target_accessor(result, is_target);
    });
    return true;
  }

  // NOTE(litianshi03) 招聘房产精排队列分归一化
  static bool CalcIndustryNormalizedRankScore(const CommonRecoLightFunctionContext &context,
                                              RecoResultConstIter begin, RecoResultConstIter end) {
    auto rank_score_conf = context.GetDoubleListCommonAttr("industry_live_rank_top_score_list");
    std::vector<double> rank_score_vec;
    if (rank_score_conf.has_value()) {
      rank_score_vec.clear();
      for (auto score : rank_score_conf.value()) {
        rank_score_vec.push_back(score);
      }
    }
    int rank_score_vec_size = rank_score_vec.size();
    auto is_house_people_flag =
        context.GetIntCommonAttr("is_house_people_flag").value_or(0);
    auto house_live_rank_core_people_weight =
        context.GetDoubleCommonAttr("house_live_rank_core_people_weight").value_or(0.0);
    auto recruit_offline_user_info =
        context.GetStringCommonAttr("recruit_offline_user_info").value_or("");
    auto recruit_live_rank_core_people_weight =
        context.GetDoubleCommonAttr("recruit_live_rank_core_people_weight").value_or(0.0);

    auto score_accessor =
        context.SetDoubleItemAttr("fr_raw_qs_recruit");
    auto recruit_priority_attr =
        context.GetIntItemAttr("recruit_priority_attr");
    auto lLiveHouseIsLiveTag =
        context.GetIntItemAttr("lLiveHouseIsLiveTag");
    auto lLiveHouseIsUnaLiveTag =
        context.GetIntItemAttr("lLiveHouseIsUnaLiveTag");
    auto lLiveIsHouseLiveTag =
        context.GetIntItemAttr("lLiveIsHouseLiveTag");

    int index = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double score_result = 0.0;
      auto recruit_item_flag = recruit_priority_attr(result).value_or(0);
      auto house_live_flag_1 = lLiveHouseIsLiveTag(result).value_or(0);
      auto house_live_flag_2 = lLiveHouseIsUnaLiveTag(result).value_or(0);
      auto house_live_flag_3 = lLiveIsHouseLiveTag(result).value_or(0);
      if (index < rank_score_vec_size) {
        score_result = rank_score_vec[index];

        // 房产核心人群加权
        if (is_house_people_flag == 1) {
          if (house_live_flag_1 == 1 || house_live_flag_2 == 1 || house_live_flag_3 == 1) {
            score_result += house_live_rank_core_people_weight;
          }
        }
        // 快聘核心人群加群
        if (recruit_offline_user_info.find("d") != std::string::npos) {
          if (recruit_item_flag > 0) {
            score_result += recruit_live_rank_core_people_weight;
          }
        }

        ++index;
      }
      score_accessor(result, score_result);
    });
    return true;
  }

  // NOTE(litianshi03) 生成招聘用户意向信息
  static bool GenRecruitUserIntentionInfo(
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
    auto user_category_id_list =
        context.GetIntListCommonAttr("uResumeIntentionJobLevel2CodeList90dList")
        .value_or(absl::Span<const int64>());
    std::vector<std::string> user_category_string_list;
    for (auto category_id : user_category_id_list) {
      user_category_string_list.push_back(std::to_string(category_id));
    }
    context.SetStringListCommonAttr("recruit_user_intention_category_list",
                                    std::move(user_category_string_list));

    auto user_province_id_list =
        context.GetIntListCommonAttr("uResumeUserIntentionWorkProvinceCodeList90dList")
        .value_or(absl::Span<const int64>());
    std::vector<std::string> user_province_string_list;
    for (auto province_id : user_province_id_list) {
      user_province_string_list.push_back(std::to_string(province_id));
    }
    context.SetStringListCommonAttr("recruit_user_intention_province_list",
                                    std::move(user_province_string_list));
    return true;
  }

  // NOTE(litianshi03) 判断招聘 B 端冷启作者是否命中实验组
  static bool IsRecruitAuthorHitExp(
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
    auto author_group_list =
        context.GetIntListCommonAttr("recruit_recruit_coldstart_author_group_list")
        .value_or(absl::Span<const int64>());
    std::set<int> author_group_set;
    for (auto tail : author_group_list) {
      author_group_set.insert((int)tail);
    }

    auto author_id_processor =
        context.GetIntItemAttr("author_id");
    auto is_author_hit_exp_accessor =
        context.SetIntItemAttr("is_author_hit_exp");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      int hit_flag = 0;
      auto author_id = author_id_processor(result).value_or(0);
      if (author_group_set.count(author_id % 10)) {
        hit_flag = 1;
      }
      is_author_hit_exp_accessor(result, hit_flag);
    });
    return true;
  }

  // NOTE(litianshi03) 招聘 B 端冷启作者精排提权
  static bool CalcRecruitColdstartAuthorBoost(
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
    auto author_level_boost_type =
        context.GetIntCommonAttr("author_level_boost_type").value_or(0);
    auto boost_coeff =
        context.GetDoubleCommonAttr("recruit_live_coldstart_author_rank_boost_conf").value_or(0.0);
    auto show_limit =
        context.GetIntCommonAttr("recruit_recruit_coldstart_live_show_limit").value_or(0);
    auto deliver_limit =
        context.GetIntCommonAttr("recruit_recruit_coldstart_live_deliver_limit").value_or(0);

    auto is_hit_exp_processor =
        context.GetIntItemAttr("is_author_hit_exp");
    auto is_coldstart_author_processor =
        context.GetIntItemAttr("aRecruitColdstartAuthor");
    auto show_cnt_processor =
        context.GetIntItemAttr("lRecruitRealtimeShowCntInteger");
    auto deliver_cnt_processor =
        context.GetIntItemAttr("lRecruitRealtimeDeliverCntInteger");

    auto boost_value_processor =
        context.SetDoubleItemAttr("author_level_boost_value");
    auto is_recruit_sabc_item_processor =
        context.SetIntItemAttr("is_recruit_sabc_item");
    auto is_recruit_coldstart_author_item_processor =
        context.SetIntItemAttr("is_recruit_coldstart_author_item");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      double boost_result = 0.0;
      int is_recruit_sabc_item = 0;
      int is_recruit_coldstart_author_item = 0;
      if (author_level_boost_type == 1) {
        boost_result = 0.0;
      } else {
        boost_result = 1.0;
      }
      auto is_hit_exp = is_hit_exp_processor(result).value_or(0);
      auto is_coldstart_author = is_coldstart_author_processor(result).value_or(0);
      auto show_cnt = show_cnt_processor(result).value_or(0);
      auto deliver_cnt = deliver_cnt_processor(result).value_or(0);
      if (is_hit_exp == 1 &&
          is_coldstart_author == 1 &&
          show_cnt < show_limit &&
          deliver_cnt < deliver_limit) {
        boost_result = boost_coeff;
        is_recruit_coldstart_author_item = 1;
      }

      boost_value_processor(result, boost_result);
      is_recruit_sabc_item_processor(result, is_recruit_sabc_item);
      is_recruit_coldstart_author_item_processor(result, is_recruit_coldstart_author_item);
    });
    return true;
  }

  // 魔盒主播过滤
  static bool CalcLuckGrassAuthorFilter(const CommonRecoLightFunctionContext &context,
                                             RecoResultConstIter begin, RecoResultConstIter end) {
    auto luck_grass_redis_value = context.GetStringCommonAttr("luck_grass_redis_value").value_or("0");
    auto author_luck_grass_filter_mode =
        context.GetIntCommonAttr("author_luck_grass_filter_mode").value_or(0);
    auto is_luck_grass_author_v2 = context.GetIntCommonAttr("is_luck_grass_author_v2").value_or(0);
    auto l_open_luck_grass_author = context.GetDoubleItemAttr("lOpenLuckGrassAuthor");
    auto l_open_luck_grass_author_v2 = context.GetIntItemAttr("lOpenLuckGrassAuthorV2");
    auto is_luck_grass_filter_flag = context.SetIntItemAttr("is_luck_grass_filter_flag");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int64 flag;
      if (absl::SimpleAtoi(luck_grass_redis_value, &flag)) {
        if (is_luck_grass_author_v2 > 0) {
          auto is_luck_grass = l_open_luck_grass_author_v2(result).value_or(0);
          if (is_luck_grass > 0 && author_luck_grass_filter_mode == flag) {
            is_luck_grass_filter_flag(result, 1);
          } else {
            is_luck_grass_filter_flag(result, 0);
          }
        } else {
          auto is_luck_grass = l_open_luck_grass_author(result).value_or(0.0);
          if (is_luck_grass >= 0.5 && author_luck_grass_filter_mode == flag) {
            is_luck_grass_filter_flag(result, 1);
          } else {
            is_luck_grass_filter_flag(result, 0);
          }
        }
      }
    });
    return true;
  }

  static bool CalcExploreSellerFilter(const CommonRecoLightFunctionContext &context,
                                              RecoResultConstIter begin,
                                              RecoResultConstIter end) {
    auto isExploreTargetCrowd =
      context.GetIntCommonAttr("is_explore_target_crowd").value_or(0);
    auto isSellerAuthorV2 =
      context.GetIntItemAttr("author_info.is_seller_author_v2");
    auto exploreIsFilteredBySellerAuthorV2 =
      context.SetIntItemAttr("explore_is_filtered_by_seller_author_v2");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      if (isExploreTargetCrowd > 0 && isSellerAuthorV2(result).value_or(0) > 0) {
        exploreIsFilteredBySellerAuthorV2(result, 1);
      } else {
        exploreIsFilteredBySellerAuthorV2(result, 0);
      }
    });
    return true;
  }

  static bool CalcSkipFilterTag(const CommonRecoLightFunctionContext &context,
                                RecoResultConstIter begin, RecoResultConstIter end) {
    auto enable_author_list_skip_filter_index =
      context.GetIntCommonAttr("enable_author_list_skip_filter_index").value_or(0);
    auto enable_author_list_skip_filter_index_v2 =
      context.GetIntCommonAttr("enable_author_list_skip_filter_index_v2").value_or(0);
    auto index_filter_attr_tag_accessor = context.GetIntItemAttr("lSkipFilterAuthorKV");
    auto index_filter_attr_tag2_accessor = context.GetIntItemAttr("lSkipFilterAuthorKV2");
    auto tail_filter_attr_tag_accessor = context.GetIntItemAttr("tail_filter_attr_tag");
    auto whitelist_filter_attr_tag_accessor = context.GetIntItemAttr("whitelist_filter_attr_tag");
    auto is_skip_filter_tag_accessor = context.SetIntItemAttr("is_skip_filter_tag");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto index_filter_attr_tag = index_filter_attr_tag_accessor(result).value_or(0);
      auto index_filter_attr_tag2 = index_filter_attr_tag2_accessor(result).value_or(0);
      auto tail_filter_attr_tag = tail_filter_attr_tag_accessor(result).value_or(0);
      auto whitelist_filter_attr_tag = whitelist_filter_attr_tag_accessor(result).value_or(0);
      int64 is_skip_filter = (enable_author_list_skip_filter_index > 0 && index_filter_attr_tag > 0)
        || (enable_author_list_skip_filter_index_v2 > 0 && index_filter_attr_tag2 > 0)
        || (tail_filter_attr_tag > 0) || (whitelist_filter_attr_tag > 0);
      is_skip_filter_tag_accessor(result, is_skip_filter);
    });
    return true;
  }

  static bool CalcMainAccountFavoriteFilter(const CommonRecoLightFunctionContext &context,
                                RecoResultConstIter begin, RecoResultConstIter end) {
    auto enable_main_account_history =
      context.GetIntCommonAttr("enable_main_account_favorite").value_or(0);
    auto enable_main_account_history_filters =
      context.GetIntCommonAttr("enable_main_account_favorite_filters").value_or(0);
    auto main_account_favorite_authors =
        context.GetIntListCommonAttr("bigr_main_account_favorite_authors")
        .value_or(absl::Span<const int64>());
    auto author_id = context.GetIntItemAttr("author_id");
    auto should_filter_flag  =
        context.SetIntItemAttr("should_filter_flag");
    folly::F14FastSet<int64> main_account_favorite_authors_set(main_account_favorite_authors.begin(),
                                                      main_account_favorite_authors.end());
    if (main_account_favorite_authors_set.empty()) {
      std::for_each(begin, end, [=](const CommonRecoResult &result) {
        should_filter_flag(result, 0);
      });
      return true;
    }
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto aid = author_id(result).value_or(-1);
      if (aid < 0 || main_account_favorite_authors_set.find(aid) == main_account_favorite_authors_set.end()) {
        should_filter_flag(result, 0);
      } else {
        should_filter_flag(result, 1);
      }
    });
    return true;
  }

  static bool CalcSellerAuthorFilterForHighPayingUser(const CommonRecoLightFunctionContext &context,
                                RecoResultConstIter begin, RecoResultConstIter end) {
    auto is_high_gift_user =
      context.GetIntCommonAttr("is_high_gift_user").value_or(0);
    auto is_seller_author_v2 = context.GetIntItemAttr("author_info.is_seller_author_v2");
    auto is_filtered_by_seller_author_v2 = context.SetIntItemAttr("is_filtered_by_seller_author_v2");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto seller_author_tag = is_seller_author_v2(result).value_or(0);
      if (is_high_gift_user > 0 && seller_author_tag > 0) {
        is_filtered_by_seller_author_v2(result, 1);
      } else {
        is_filtered_by_seller_author_v2(result, 0);
      }
    });
    return true;
  }

  static bool CalcLocalAreaSkipBlackFilterFlag(const CommonRecoLightFunctionContext &context,
                                RecoResultConstIter begin, RecoResultConstIter end) {
    auto target_area =
      context.GetStringCommonAttr("local_area_skip_black_filter_loaction").value_or("unknown");
    auto area =
      context.GetStringCommonAttr("user_province").value_or("unknown");
    auto condition_flag =
      context.GetIntCommonAttr("skip_black_filter_condition_switch").value_or(0);
    if (condition_flag == 0) {
      if (target_area == area && target_area != "unknown" && area != "unknown") {
        context.SetIntCommonAttr("local_area_skip_black_tag", 1);
      } else {
        context.SetIntCommonAttr("local_area_skip_black_tag", 0);
      }
    } else {
      if (target_area != area && target_area != "unknown" && area != "unknown") {
        context.SetIntCommonAttr("local_area_skip_black_tag", 1);
      } else {
        context.SetIntCommonAttr("local_area_skip_black_tag", 0);
      }
    }
    return true;
  }

  static bool CalcVariantMcPreFilterFlag(const CommonRecoLightFunctionContext &context,
                              RecoResultConstIter begin, RecoResultConstIter end) {
    auto variant_history_size =
      context.GetIntCommonAttr("variant_history_size").value_or(0);
    auto tag_max_size =
      context.GetIntCommonAttr("tag_max_size").value_or(0);
    auto feasury_play_list =
      context.GetIntListCommonAttr("feasury_play_list").value_or(absl::Span<const int64>());
    auto variant_filter_flag = context.SetIntItemAttr("variant_filter_flag");
    int count = 0;
    int length = 0;
    if (variant_history_size < feasury_play_list.size()) {
      length = variant_history_size;
    } else {
      length = feasury_play_list.size();
    }
    for (int i = 0; i < length; i++) {
      if (feasury_play_list[i] != -1 && feasury_play_list[i] != 0) {
        count++;
      }
    }
    bool flag = (count >= tag_max_size);
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      if (flag) {
        variant_filter_flag(result, 1);
      } else {
        variant_filter_flag(result, 0);
      }
    });
    return true;
  }

  static bool BlackAuthorRemoveItemOffFilter(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    auto target_black_author_type =
          context.GetIntCommonAttr("skip_black_filter_author_type").value_or(0);
    auto b_side_switch =
          context.GetIntCommonAttr("skip_black_filter_for_fixed_black_author").value_or(0);
    auto enable_movie_skip_flag = context.GetIntCommonAttr("enable_movie_skip").value_or(0);
    auto item_off_old_filter_list = context.GetIntListItemAttr("item_off_filter_list");
    auto author_black_type = context.GetIntItemAttr("lBlackSourceStatus");
    auto fixed_black_tag = context.GetIntItemAttr("lRandomFixedBlackSourceStatus");
    auto author_is_movie = context.GetIntItemAttr("is_movie_hetu_tag");
    auto item_off_new_filter_list = context.SetIntListItemAttr("item_off_filter_list");
    auto skip_black_filter_n = context.GetIntCommonAttr("black_author_skip_filter_reason").value_or(53);
    // int64 skip_black_filter_n = 53;

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto old_filter_list_value = item_off_old_filter_list(result).value_or(absl::Span<const int64>());
      auto author_black_type_value = author_black_type(result).value_or(0);
      auto fixed_black_tag_value = fixed_black_tag(result).value_or(0);
      auto author_is_movie_value = author_is_movie(result).value_or(0);
      if (enable_movie_skip_flag == 1 && author_is_movie_value > 0) {
        return;
      }
      if (b_side_switch == 1 && fixed_black_tag_value == 1) {
        std::vector<int64> new_off_filter_list(old_filter_list_value.begin(), old_filter_list_value.end());
        new_off_filter_list.push_back(skip_black_filter_n);
        item_off_new_filter_list(result, new_off_filter_list);
      } else {
        if (author_black_type_value != 0 && target_black_author_type == author_black_type_value) {
          std::vector<int64> new_off_filter_list(old_filter_list_value.begin(), old_filter_list_value.end());
          new_off_filter_list.push_back(skip_black_filter_n);
          item_off_new_filter_list(result, new_off_filter_list);
        }
      }
    });
    return true;
  }

  static bool McSellerTagDeBoostMc(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    double boost_coeff =
             context.GetDoubleCommonAttr("mc_seller_author_deboost_coeff").value_or(1.0);
    auto is_seller_tag_attr = context.GetIntItemAttr("is_seller_tag");
    auto get_score = context.GetDoubleItemAttr("mc_csqs_exploitation");
    auto set_to_score = context.SetDoubleItemAttr("mc_csqs_exploitation");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double score = get_score(result).value_or(0.0);
      auto is_seller_tag_attr_val = is_seller_tag_attr(result).value_or(0);
      if (is_seller_tag_attr_val > 0) {
        score *= boost_coeff;
      }
      set_to_score(result, score);
    });
    return true;
  }

  static bool CalcIsClickAuthor(const CommonRecoLightFunctionContext &context,
                                RecoResultConstIter begin, RecoResultConstIter end) {
    auto live_aid_click_list =
      context.GetIntListCommonAttr("live_aid_click_list").value_or(absl::Span<const int64>());
    auto live_aid_click_ts_list =
      context.GetIntListCommonAttr("live_aid_click_ts_list").value_or(absl::Span<const int64>());
    auto enable_click_author_judge_by_cnt =
          context.GetIntCommonAttr("enable_click_author_judge_by_cnt").value_or(0);
    auto click_author_judge_cnt =
          context.GetIntCommonAttr("click_author_judge_cnt").value_or(15);
    auto enable_click_author_judge_by_interval =
          context.GetIntCommonAttr("enable_click_author_judge_by_interval").value_or(0);
    auto click_author_judge_interval =
          context.GetIntCommonAttr("click_author_judge_interval").value_or(168);
    auto author_id = context.GetIntItemAttr("author_id");
    auto is_click_author = context.SetIntItemAttr("is_click_author");
    folly::F14FastSet<int64> aid_set;
    if (live_aid_click_list.size() == live_aid_click_ts_list.size()) {
      if (enable_click_author_judge_by_cnt == 1) {
        int pos = 0;
        if (live_aid_click_list.size() - click_author_judge_cnt > 0) {
          pos = live_aid_click_list.size() - click_author_judge_cnt;
        }
        for (int i = live_aid_click_list.size() - 1; i >= pos; --i) {
          aid_set.insert(live_aid_click_list[i]);
        }
      }
      if (click_author_judge_interval == 1) {
        int64 start_ts = base::GetTimestamp() / 1000 - click_author_judge_interval * 3600 * 1000;
        for (int i = live_aid_click_list.size() - 1; i >= 0; --i) {
          if (live_aid_click_ts_list[i] >= start_ts) {
            aid_set.insert(live_aid_click_list[i]);
          } else {
            break;
          }
        }
      }
    }
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto author_id_value = author_id(result).value_or(-1);
      if (aid_set.find(author_id_value) != aid_set.end()) {
        is_click_author(result, 1);
      } else {
        is_click_author(result, 0);
      }
    });
    return true;
  }

  static bool CalcNrMmuTagRetrFlag(const CommonRecoLightFunctionContext &context,
                                RecoResultConstIter begin, RecoResultConstIter end) {
    auto is_nr_user = context.GetIntCommonAttr("is_nr_user").value_or(0);
    auto nr_mmu_tag_retr_probability =
      context.GetDoubleCommonAttr("nr_mmu_tag_retr_probability").value_or(1.0);
    std::srand(std::time(nullptr));
    double random_number = static_cast<double>(std::rand()) / RAND_MAX;
    if (is_nr_user == 1 && random_number < nr_mmu_tag_retr_probability) {
      context.SetIntCommonAttr("nr_mmu_tag_retr_flag", 1);
    } else {
      context.SetIntCommonAttr("nr_mmu_tag_retr_flag", 0);
    }
    return true;
  }

  static bool CalcNrRedirectAuthorList(const CommonRecoLightFunctionContext &context,
                                RecoResultConstIter begin, RecoResultConstIter end) {
    auto live_play_aid_list =
      context.GetIntListCommonAttr("live_play_aid_list").value_or(absl::Span<const int64>());
    auto live_play_reason_list =
      context.GetStringListCommonAttr("live_play_reason_list").value_or(std::vector<absl::string_view>());
    auto live_play_time_list =
      context.GetIntListCommonAttr("live_play_time_list").value_or(absl::Span<const int64>());
    auto nr_redirect_retr_view_time_threshold =
          context.GetIntCommonAttr("nr_redirect_retr_view_time_threshold").value_or(0);
    auto action_author_id_list =
      context.GetIntListCommonAttr("action_author_id_list").value_or(absl::Span<const int64>());
    auto enable_nr_redirect_retr_retain_interaction =
          context.GetIntCommonAttr("enable_nr_redirect_retr_retain_interaction").value_or(0);
    auto enable_nr_redirect_retr_skip_living =
          context.GetIntCommonAttr("enable_nr_redirect_retr_skip_living").value_or(0);
    auto enable_nr_redirect_retr_add_time_gap =
          context.GetIntCommonAttr("enable_nr_redirect_retr_add_time_gap").value_or(0);
    auto nr_redirect_retr_time_gap =
          context.GetIntCommonAttr("nr_redirect_retr_time_gap").value_or(86400);
    auto live_play_ms_list =
          context.GetIntListCommonAttr("live_play_ms_list").value_or(absl::Span<const int64>());
    std::vector<int64> author_id_list;
    folly::F14FastSet<std::string> tag_set = {"li0", "scn0", "slv0", "scnns0", "scbf0",
                                              "sff0", "combsearchuser"};
    int64 start_ts = base::GetTimestamp() / 1000 - nr_redirect_retr_time_gap * 1000;
    if (live_play_aid_list.size() == live_play_reason_list.size()
        && live_play_aid_list.size() == live_play_time_list.size()
        && live_play_aid_list.size() == live_play_ms_list.size()) {
      for (int i = 0; i < live_play_aid_list.size(); ++i) {
        if (tag_set.find(std::string(live_play_reason_list[i])) != tag_set.end()) {
          if (enable_nr_redirect_retr_skip_living == 1 && live_play_reason_list[i] == "li0") {
            continue;
          }
          if (enable_nr_redirect_retr_add_time_gap == 1 && live_play_ms_list[i] < start_ts) {
            continue;
          }
          if (live_play_time_list[i] >= nr_redirect_retr_view_time_threshold * 1000
              || (enable_nr_redirect_retr_retain_interaction == 1 && std::count(action_author_id_list.begin(),
              action_author_id_list.end(), live_play_aid_list[i]) > 0)) {
            author_id_list.push_back(live_play_aid_list[i]);
          }
        }
      }
    }
    context.SetIntListCommonAttr("author_id_list", std::move(author_id_list));
    return true;
  }

  // NOTE(wangqian23) prerank 设置是否招聘房产目标 UA
  static bool CalcRecruitHouseUAFlag(const CommonRecoLightFunctionContext &context,
                                  RecoResultConstIter begin, RecoResultConstIter end) {
    auto is_house_people_flag = context.GetIntCommonAttr("is_house_people_flag").value_or(0);
    auto is_recruit_people_flag = context.GetIntCommonAttr("is_recruit_people_flag").value_or(0);

    auto live_house_is_live_tag = context.GetIntItemAttr("lLiveHouseIsLiveTag");
    auto live_house_is_una_live_tag = context.GetIntItemAttr("lLiveHouseIsUnaLiveTag");
    auto live_house_is_flower_live_tag = context.GetIntItemAttr("lLiveHouseIsFlowerLiveTag");
    auto recruit_priority_attr = context.GetIntItemAttr("recruit_priority_attr");

    auto set_is_house_ua = context.SetIntItemAttr("is_house_ua");
    auto set_is_recruit_ua = context.SetIntItemAttr("is_recruit_ua");
    auto set_is_house_live = context.SetIntItemAttr("is_house_live");
    auto set_is_recruit_live = context.SetIntItemAttr("is_recruit_live");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto recruit_priority_attr_value = recruit_priority_attr(result).value_or(0);
      auto live_house_is_live_tag_value = live_house_is_live_tag(result).value_or(0);
      auto live_house_is_una_live_tag_value = live_house_is_una_live_tag(result).value_or(0);
      auto live_house_is_flower_live_tag_value = live_house_is_flower_live_tag(result).value_or(0);
      if (recruit_priority_attr_value > 0) {
        set_is_recruit_live(result, 1);
        if (is_recruit_people_flag > 0) {
          set_is_recruit_ua(result, 1);
        } else {
          set_is_recruit_ua(result, 0);
        }
      } else {
        set_is_recruit_live(result, 0);
        set_is_recruit_ua(result, 0);
      }

      if (live_house_is_live_tag_value > 0 || live_house_is_una_live_tag_value > 0
          || live_house_is_flower_live_tag_value > 0) {
        set_is_house_live(result, 1);
        if (is_house_people_flag > 0) {
          set_is_house_ua(result, 1);
        } else {
          set_is_house_ua(result, 0);
        }
      } else {
        set_is_house_live(result, 0);
        set_is_house_ua(result, 0);
      }
    });
    return true;
  }

  static bool CalcGameAuthorQualityScore(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto live_g2a_retr_ranking_weight_list =
        context.GetDoubleListCommonAttr("live_g2a_retr_ranking_weight_list");
    auto lWilsonCtr10Mins = context.GetDoubleItemAttr("lWilsonCtr10Mins");
    auto lctr10Mins = context.GetDoubleItemAttr("lctr10Mins");
    auto lRealShow10Mins = context.GetIntItemAttr("lRealShow10Mins");
    auto lLike10Mins = context.GetIntItemAttr("lLike10Mins");
    auto lComment10Mins = context.GetIntItemAttr("lComment10Mins");
    auto lGiftCnt10Mins = context.GetIntItemAttr("lGiftCnt10Mins");
    auto lDurPerShow10Mins = context.GetDoubleItemAttr("lDurPerShow10Mins");
    auto set_game_author_quality_score = context.SetDoubleItemAttr("game_author_quality_score");

    // output item attr
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto realshow_cnt = lRealShow10Mins(result).value_or(0);
      realshow_cnt = std::max(static_cast<int>(realshow_cnt), 1);
      auto wtime = lDurPerShow10Mins(result).value_or(0.0);
      wtime = std::max(static_cast<double>(wtime) / 1000.0, 0.0);
      auto ctr = lctr10Mins(result).value_or(0.0);
      ctr = std::max(static_cast<double>(ctr), 0.0);
      auto wilson_ctr = lWilsonCtr10Mins(result).value_or(0.0);
      wilson_ctr = std::max(static_cast<double>(wilson_ctr), 0.0);
      auto gift_cnt = lGiftCnt10Mins(result).value_or(0);
      gift_cnt = std::max(static_cast<int>(gift_cnt), 0);
      auto like_cnt = lLike10Mins(result).value_or(0);
      like_cnt = std::max(static_cast<int>(like_cnt), 0);
      auto cmt_cnt =  lComment10Mins(result).value_or(0);
      cmt_cnt =  std::max(static_cast<int>(cmt_cnt), 0);
      auto gtr = gift_cnt / static_cast<double>(realshow_cnt+1e-9);
      auto ltr = like_cnt / static_cast<double>(realshow_cnt+1e-9);
      auto cmtr = cmt_cnt / static_cast<double>(realshow_cnt+1e-9);
      if (!live_g2a_retr_ranking_weight_list.has_value() ||
      live_g2a_retr_ranking_weight_list.value().size() != 6) {
        auto score = 1.0 * gtr +
          1.0 * ltr +
          1.0 * cmtr +
          1.0 * ctr +
          1.0 * wtime +
          1.0 * wilson_ctr;
          set_game_author_quality_score(result, score);
      } else {
        auto score = live_g2a_retr_ranking_weight_list.value()[0] * gtr +
          live_g2a_retr_ranking_weight_list.value()[1] * ltr +
          live_g2a_retr_ranking_weight_list.value()[2] * cmtr +
          live_g2a_retr_ranking_weight_list.value()[3] * ctr +
          live_g2a_retr_ranking_weight_list.value()[4] * wtime +
          live_g2a_retr_ranking_weight_list.value()[5] * wilson_ctr;
          set_game_author_quality_score(result, score);
      }
    });
    return true;
  }

  static bool CalcUAFollowDay(const CommonRecoLightFunctionContext &context,
                                RecoResultConstIter begin, RecoResultConstIter end) {
    auto follow_aid_list =
      context.GetIntListCommonAttr("follow_aid_list").value_or(absl::Span<const int64>());
    auto follow_time_list =
      context.GetIntListCommonAttr("follow_time_list").value_or(absl::Span<const int64>());
    auto author_id = context.GetIntItemAttr("author_id");
    auto ua_follow_day = context.SetIntItemAttr("ua_follow_day");
    folly::F14FastMap<int64, int64> aid_follow_ts_map;
    int64 cur_time = base::GetTimestamp() / 1000;
    if (follow_aid_list.size() == follow_time_list.size()) {
      for (int64 i = follow_aid_list.size() - 1; i >= 0; --i) {
        aid_follow_ts_map[follow_aid_list[i]] = (cur_time - follow_time_list[i]) / 86400000;
      }
    }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto author_id_value = author_id(result).value_or(-1);
      auto it = aid_follow_ts_map.find(author_id_value);
      if (it != aid_follow_ts_map.end()) {
        ua_follow_day(result, it->second);
      } else {
        ua_follow_day(result, -1);
      }
    });
    return true;
  }

  static bool GetGPSQueryList(const CommonRecoLightFunctionContext &context,
                                RecoResultConstIter begin, RecoResultConstIter end) {
    auto user_lat = context.GetDoubleCommonAttr("user_lat").value_or(0.0);
    auto user_lon = context.GetDoubleCommonAttr("user_lon").value_or(0.0);
    std::vector<std::string> livestream_gps_retriever_gps_query_list;

    std::vector<double> live_search_distance_kms = {0.5, 2.0, 8.0, 16.0, 32.0, 64.0, 100.0, 200.0};
    std::vector<int> indices = {0, -1, 1};
    for (int i = 0; i <= live_search_distance_kms.size() - 1; ++i) {
      std::string prefix = "GPS_L_" + std::to_string(
        static_cast<int>(std::floor(live_search_distance_kms[i] * 10))) + ":";
      double kKmPerDegree = 40000.0 / 360;
      double unit = live_search_distance_kms[i] / kKmPerDegree;
      if (unit == 0) {break;}
      int x = std::floor(user_lat / unit);
      int y = std::floor(user_lon / unit);
      for (int j : indices) {
        for (int k : indices) {
            livestream_gps_retriever_gps_query_list.emplace_back(
              prefix + std::to_string(x + j) + "_" + std::to_string(y + k));
        }
      }
    }
    context.SetStringListCommonAttr(
      "livestream_gps_retriever_gps_query_list", std::move(livestream_gps_retriever_gps_query_list));

    return true;
  }

  static bool GetHighValueFlag(const CommonRecoLightFunctionContext &context,
                                RecoResultConstIter begin, RecoResultConstIter end) {
    auto ltvscore_threshold = context.GetDoubleCommonAttr("mc_revenue_ltv_threshold").value_or(2.0);
    auto arppu_threshold = context.GetDoubleCommonAttr("mc_revenue_arppu_threshold").value_or(2.0);
    auto ltvscore_live_accessor =
        context.GetDoubleItemAttr("aLtv14GiftValueKV");
    auto arppu_live_accessor =
        context.GetDoubleItemAttr("aArppuKV");
    auto high_value_live_accessor =
        context.SetIntItemAttr("mc_csqf_high_gift");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto ltvscore_live = ltvscore_live_accessor(result).value_or(0.0);
      auto arppu_live = arppu_live_accessor(result).value_or(0.0);
      if (ltvscore_live > ltvscore_threshold || arppu_live > arppu_threshold) {
        high_value_live_accessor(result, 1);
      } else {
        high_value_live_accessor(result, 0);
      }
    });
    return true;
  }

  static bool CalcLLMRedisKey(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    // get
    int64_t user_id =
      context.GetIntCommonAttr("uid").value_or(0);
    std::string user_id_str = std::to_string(user_id);
    auto get_author_id = context.GetIntItemAttr("author_id");
    std::string llm_redis_prefix_str = std::string(
      context.GetStringCommonAttr("llm_redis_prefix").value_or("llm"));
    // set
    auto set_llm_redis_key = context.SetStringItemAttr("llm_redis_key");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int64_t author_id = get_author_id(result).value_or(0);
      std::string author_id_str = std::to_string(author_id);
      std::string ua_llm_redis_key = llm_redis_prefix_str + "_" + user_id_str + "_" + author_id_str;
      set_llm_redis_key(result, ua_llm_redis_key);
    });
    return true;
  }

  static bool CalcRecruitSameCityBoost(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    int64_t user_city_id_6 = context.GetIntCommonAttr("user_city_id_6").value_or(0);
    auto recruit_service_industry_same_location_boost =
      context.GetDoubleCommonAttr("recruit_service_industry_same_location_boost").value_or(0.0);
    auto lRecruitEmployCityCodeInteger =
        context.GetIntItemAttr("lRecruitEmployCityCodeInteger");
    auto lRecruitEmployIndustryCategoryString =
        context.GetStringItemAttr("lRecruitEmployIndustryCategoryString");
    auto recruit_same_city_boost =
        context.SetDoubleItemAttr("recruit_same_city_boost");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      double same_city_boost = 1.0;
      std::string job_industry_category =
          std::string(lRecruitEmployIndustryCategoryString(result).value_or("0"));
      int64_t job_city_code = lRecruitEmployCityCodeInteger(result).value_or(0);
      if (job_industry_category == "服务业" && user_city_id_6 == job_city_code) {
        same_city_boost = recruit_service_industry_same_location_boost;
      }
      recruit_same_city_boost(result, same_city_boost);
    });
    return true;
  }

  static bool CalcRecruitExptagBoost(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    auto recruit_exptag_boost_weight_list =
    context.GetDoubleListCommonAttr("recruit_exptag_boost_weight_list").value_or(absl::Span<const double>());
    auto recruit_exptag_boost_reason_list =
      context.GetIntListCommonAttr("recruit_exptag_boost_reason_list").value_or(absl::Span<const int64>());
    auto recruit_exptag_boost_type = context.GetIntCommonAttr("recruit_exptag_boost_type").value_or(0);
    auto reason_list_getter = context.GetIntListItemAttr("reason_list");
    auto exptag_boost_value = context.SetDoubleItemAttr("exptag_boost_value");
    if (recruit_exptag_boost_weight_list.size() == recruit_exptag_boost_reason_list.size()) {
      std::for_each(begin, end, [=](const CommonRecoResult &result) {
        double exptag_boost = 0.0;
        if (recruit_exptag_boost_type == 1) {
          exptag_boost = 1.0;
        }
        auto reason_list = reason_list_getter(result).value_or(absl::Span<const int64>());
        for (auto reason : reason_list) {
          for (int i = 0; i < recruit_exptag_boost_reason_list.size(); ++i) {
            if (recruit_exptag_boost_reason_list[i] == reason) {
              if (recruit_exptag_boost_type == 1) {
                exptag_boost *= recruit_exptag_boost_weight_list[i];
              } else {
                exptag_boost += recruit_exptag_boost_weight_list[i];
              }
            }
          }
        }
        exptag_boost_value(result, exptag_boost);
      });
    } else {
      double exptag_boost = 0.0;
      if (recruit_exptag_boost_type == 1) {
        exptag_boost = 1.0;
      }
      std::for_each(begin, end, [=](const CommonRecoResult &result) {
        exptag_boost_value(result, exptag_boost);
      });
    }
    return true;
  }

  static bool CalcNoCptList(const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
    auto not_cpt_users_list =
      context.GetPtrCommonAttr<const folly::F14FastSet<uint64>>("not_cpt_users_list_str");
    if (not_cpt_users_list) {
      std::vector<int64> user_vec(not_cpt_users_list->begin(), not_cpt_users_list->end());
      context.SetIntListCommonAttr("not_cpt_users_list", std::move(user_vec));
    }
    return true;
  }

  static bool CalcReplaceAuthorIdAndList(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    auto replace_author_id_map =
      context.GetPtrCommonAttr<const folly::F14FastMap<uint64, uint64>>("replace_author_id_map");
    auto enable_live_retr_replace_author_skip_ua =
        context.GetIntCommonAttr("enable_live_retr_replace_author_skip_ua").value_or(0);
    auto author_id_getter = context.GetIntItemAttr("author_id");
    auto replace_author_id_setter = context.SetIntItemAttr("replace_author_id");
    auto user_follow_list_ids = context.GetIntListCommonAttr("user_follow_list_ids");

    folly::F14FastSet<int64> user_follow_aid_set;
    if (user_follow_list_ids.has_value()) {
      user_follow_aid_set.insert(user_follow_list_ids->begin(), user_follow_list_ids->end());
    }
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto author_id = author_id_getter(result).value_or(0);
      if (replace_author_id_map && replace_author_id_map->find(author_id) != replace_author_id_map->end()) {
        if (!(enable_live_retr_replace_author_skip_ua && user_follow_aid_set.count(author_id))) {
          auto replace_author_id = replace_author_id_map->at(author_id);
          replace_author_id_setter(result, replace_author_id);
          context.AppendIntListCommonAttr("replace_author_id_list", replace_author_id);
        }
      }
    });
    return true;
  }

  static bool CalcLiveSearchInfo(const CommonRecoLightFunctionContext &context,
                                 RecoResultConstIter begin, RecoResultConstIter end) {
    auto nearby_search_live_info = context.GetStringCommonAttr("nearby_search_live_info").value_or("");
    if (nearby_search_live_info.empty()) {
        CL_LOG_WARNING("light_function", "CalcLiveSearchInfo")
            << "nearby_search_live_info is empty";
        return false;
    }
    std::string json_string(nearby_search_live_info);
    // 解析 JSON 数据
    nlohmann::json parsed_data;
    try {
        parsed_data = nlohmann::json::parse(json_string);
    } catch (const nlohmann::json::exception &e) {
        CL_LOG_ERROR("light_function", "CalcLiveSearchInfo")
            << "Failed to parse JSON: " << e.what();
        return false;
    }
    std::vector<int64_t> author_ids;
    std::vector<int64_t> scores;
    // 提取 author id
    for (auto &search_term : parsed_data.items()) {
        for (auto &live_data : search_term.value().items()) {
            std::string live_id_author_id = live_data.key();
            size_t pos = live_id_author_id.find('_');
            if (pos != std::string::npos) {
                std::string author_id_str = live_id_author_id.substr(pos + 1);
                int64_t author_id = 0;

                if (!absl::SimpleAtoi(author_id_str, &author_id)) {
                    CL_LOG_WARNING("light_function", "CalcLiveSearchInfo")
                        << "Invalid author_id format: " << live_id_author_id;
                    continue;
                }

                int64_t play_duration = live_data.value().value("play_duration", 0);
                int64_t send_gift_cnt = live_data.value().value("send_gift_cnt", 0);
                int64_t send_gift_amt = live_data.value().value("send_gift_amt", 0);
                int64_t live_like_cnt = live_data.value().value("live_like_cnt", 0);
                int64_t score =
                    send_gift_amt * 1000 + 10 * (live_like_cnt + send_gift_cnt) + play_duration / 3600;

                author_ids.push_back(author_id);
                scores.push_back(score);
            }
        }
    }
    struct ScoreData {
        int64_t author_id;
        int64_t score;
        explicit ScoreData(int64_t author_id, int64_t score = 0)
            : author_id(author_id), score(score) {}
    };
    std::vector<ScoreData> sorted_data;
    for (size_t i = 0; i < author_ids.size(); ++i) {
        sorted_data.push_back(ScoreData(author_ids[i], scores[i]));
    }
    std::sort(sorted_data.begin(), sorted_data.end(), [](const ScoreData &a, const ScoreData &b) {
        return a.score > b.score;
    });
    std::vector<int64_t> top10_author_ids;
    for (size_t i = 0; i < std::min<size_t>(sorted_data.size(), 10); ++i) {
        top10_author_ids.push_back(sorted_data[i].author_id);
    }
    context.SetIntListCommonAttr("live_search_author_list", std::move(top10_author_ids));
    return true;
  }

  static bool CalcLiveSearch7dInfo(const CommonRecoLightFunctionContext &context,
                                 RecoResultConstIter begin, RecoResultConstIter end) {
    // 辅助函数：严谨计算两个 "YYYYMMDD" 格式日期之间的天数差
    auto getDaysDifference = [](const std::string &date1, const std::string &date2) -> int {
      std::tm tm1 = {};
      std::tm tm2 = {};
      int year = 0, month = 0, day = 0;
      // 解析 date1，使用 absl::SimpleAtoi 替代 std::stoi
      if (!absl::SimpleAtoi(date1.substr(0, 4), &year)) return 0;
      if (!absl::SimpleAtoi(date1.substr(4, 2), &month)) return 0;
      if (!absl::SimpleAtoi(date1.substr(6, 2), &day)) return 0;
      tm1.tm_year = year - 1900;  // tm_year 从 1900 开始
      tm1.tm_mon  = month - 1;    // tm_mon 范围 [0, 11]
      tm1.tm_mday = day;
      tm1.tm_hour = 0;
      tm1.tm_min  = 0;
      tm1.tm_sec  = 0;
      // 解析 date2
      if (!absl::SimpleAtoi(date2.substr(0, 4), &year)) return 0;
      if (!absl::SimpleAtoi(date2.substr(4, 2), &month)) return 0;
      if (!absl::SimpleAtoi(date2.substr(6, 2), &day)) return 0;
      tm2.tm_year = year - 1900;
      tm2.tm_mon  = month - 1;
      tm2.tm_mday = day;
      tm2.tm_hour = 0;
      tm2.tm_min  = 0;
      tm2.tm_sec  = 0;
      time_t time1 = std::mktime(&tm1);
      time_t time2 = std::mktime(&tm2);
      if (time1 == -1 || time2 == -1) return 0;
      double seconds_diff = std::difftime(time1, time2);
      int days_diff = static_cast<int>(seconds_diff / (60 * 60 * 24));
      return days_diff;
    };
    // 获取 JSON 数据（7 天内的直播数据）
    auto nearby_search_7d_live_info = context.GetStringCommonAttr("nearby_search_7d_live_info").value_or("");
    // 转换为 std::string 避免 nlohmann::json 调用 input_adapter 时无法匹配 absl::string_view
    std::string json_str(nearby_search_7d_live_info);
    nlohmann::json parsed_data_7d;
    try {
        parsed_data_7d = nlohmann::json::parse(json_str);
    } catch (const nlohmann::json::exception &e) {
        return false;
    }
    // 如果解析后的 JSON 不是对象或者为空（例如为 null），直接返回 false
    if (!parsed_data_7d.is_object() || parsed_data_7d.empty()) {
        return false;
    }
    // 遍历所有外层日期，找出最新日期（假设格式为 "YYYYMMDD"）
    std::string latest_date = "";
    for (auto &date_item : parsed_data_7d.items()) {
        if (date_item.key() > latest_date) {
            latest_date = date_item.key();
        }
    }
    const double decay_base = 0.9;  // 定义衰减基数
    // 使用 map 累计同一作者的加权得分
    std::unordered_map<int64_t, double> author_score_map;
    // 遍历每个日期层的数据
    for (auto &date_item : parsed_data_7d.items()) {
        std::string current_date = date_item.key();
        int daysDiff = 0;
        if (latest_date.size() == 8 && current_date.size() == 8) {
          daysDiff = getDaysDifference(latest_date, current_date);
        } else {
          daysDiff = 0;
        }
        double decay_multiplier = std::pow(decay_base, daysDiff);
        // 遍历该日期下每个搜索词分类
        for (auto &search_term : date_item.value().items()) {
            // 遍历分类下每条直播数据
            for (auto &live_data : search_term.value().items()) {
                std::string live_id_author_id = live_data.key();  // 格式例如 "13378223739_1512061158"
                size_t pos = live_id_author_id.find('_');
                if (pos != std::string::npos) {
                    std::string author_id_str = live_id_author_id.substr(pos + 1);
                    int64_t author_id = 0;
                    if (!base::StringToInt64(author_id_str, &author_id)) {
                        continue;
                    }
                    int64_t play_duration = live_data.value().value("play_duration", 0);
                    int64_t send_gift_cnt = live_data.value().value("send_gift_cnt", 0);
                    int64_t send_gift_amt = live_data.value().value("send_gift_amt", 0);
                    int64_t live_like_cnt = live_data.value().value("live_like_cnt", 0);
                    double score = send_gift_amt * 1000 +
                                   10 * (live_like_cnt + send_gift_cnt) +
                                   play_duration / 3600.0;
                    // 应用时间衰减系数
                    score *= decay_multiplier;
                    // 同一作者得分累计
                    author_score_map[author_id] += score;
                }
            }
        }
    }
    // 将累计得分转换为结构体数组，并按得分降序排序，取 Top10
    struct ScoreData7d {
      int64_t author_id;
      double score;
      ScoreData7d(int64_t id, double s) : author_id(id), score(s) {}
    };
    std::vector<ScoreData7d> sorted_data_7d;
    for (const auto &entry : author_score_map) {
        sorted_data_7d.emplace_back(entry.first, entry.second);
    }
    std::sort(sorted_data_7d.begin(), sorted_data_7d.end(), [](const ScoreData7d &a, const ScoreData7d &b) {
        return a.score > b.score;
    });
    std::vector<int64_t> top10_author_ids_7d;
    for (size_t i = 0; i < std::min(sorted_data_7d.size(), size_t(10)); ++i) {
        top10_author_ids_7d.push_back(sorted_data_7d[i].author_id);
    }
    context.SetIntListCommonAttr("live_search_7d_author_list", std::move(top10_author_ids_7d));
    return true;
  }
};

// NOLINTNEXTLINE
}  // namespace platform
}  // namespace ks
