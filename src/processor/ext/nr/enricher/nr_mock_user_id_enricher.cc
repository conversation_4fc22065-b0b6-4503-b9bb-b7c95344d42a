#include "dragon/src/processor/ext/nr/enricher/nr_mock_user_id_enricher.h"

#include <unordered_map>
#include <unordered_set>

#include "dragon/src/processor/ext/nr/util/user_type.h"
#include "ks/reco_pub/reco/util/config_key.h"

namespace ks {
namespace platform {

bool NrMockUserIdEnricher::InitProcessor() {
  user_info_attr_ = config()->GetString("user_info_attr", "user_info");
  mocked_user_info_str_attr_ = config()->GetString("mocked_user_info_str", "mocked_user_info_str");
  mock_user_id_config_ = config()->Get("mocked_user_id");
  return true;
}

// 不是很耗时就先这样
void NrMockUserIdEnricher::Enrich(MutableRecoContextInterface *context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
  const auto *user_info = context->GetProtoMessagePtrCommonAttr<ks::reco::UserInfo>(user_info_attr_);
  if (user_info == nullptr) {
    CL_LOG_ERROR("NrMockUserIdEnricher, nr_mock_userinfo", "miss_user_info");
    return;
  }
  ks::reco::UserInfo final_user_info;
  final_user_info.CopyFrom(*user_info);
  auto mock_user_id = GetIntProcessorParameter(context, mock_user_id_config_, 0);
  final_user_info.set_id(mock_user_id);
  context->SetStringCommonAttr(mocked_user_info_str_attr_, final_user_info.SerializeAsString());
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, NrMockUserIdEnricher, NrMockUserIdEnricher)

}  // namespace platform
}  // namespace ks
