#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "ks/reco_proto/proto/reco.pb.h"
#include "ks/reco_proto/proto/reco_nr/user_profile_query_service.pb.h"

namespace ks {
namespace platform {

class NrMockUserIdEnricher : public CommonRecoBaseEnricher {
 public:
  NrMockUserIdEnricher() = default;
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;

  // 输入的 user_info (格式为 const ks::reco::UserInfo *)
  std::string user_info_attr_;
  // 待 mock 的 user_id
  const base::Json *mock_user_id_config_ = nullptr;
  // 输出的 user_info (格式为 std::string)
  std::string mocked_user_info_str_attr_;

  DISALLOW_COPY_AND_ASSIGN(NrMockUserIdEnricher);
};

}  // namespace platform
}  // namespace ks
