#pragma once

#include <boost/random.hpp>
#include <algorithm>
#include <sstream>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <map>
#include <utility>
#include <vector>
#include "dcaf/nr_dcaf.h"
#include "dcaf/nr_lambda_solver.h"
#include "dragon/src/module/common_reco_light_function.h"
#include "google/protobuf/util/message_differencer.h"
#include "ks/reco_proto/proto/reco.pb.h"
#include "ks/reco_proto/proto/reco_leaf_info.pb.h"

namespace ks {
namespace platform {

class NrLightFunctionSet : public CommonRecoBaseLightFunctionSet {
 public:
  NrLightFunctionSet() {
    REGISTER_LIGHT_FUNCTION(CompareUserinfoDiff);
    REGISTER_LIGHT_FUNCTION(PrintUserInfo);
    REGISTER_LIGHT_FUNCTION(PrintUserInfoPtr);
    REGISTER_LIGHT_FUNCTION(ParseAbParamIntValue);
    REGISTER_LIGHT_FUNCTION(ParseAbParamDoubleValue);
    REGISTER_LIGHT_FUNCTION(ParseAbParamStringValue);
    REGISTER_LIGHT_FUNCTION(EnrichDayOfWeekAndHourOfDay);
    REGISTER_LIGHT_FUNCTION(EnrichAppList);
    REGISTER_LIGHT_FUNCTION(EnrichGnnRetrTriggerList);
    REGISTER_LIGHT_FUNCTION(EnrichCoOccurInterestedItems);
    REGISTER_LIGHT_FUNCTION(EnrichInTargetTagList);
    REGISTER_LIGHT_FUNCTION(EnrichItemAttrDiffCount);
    REGISTER_LIGHT_FUNCTION(EnrichItemThompsonScore);
    REGISTER_LIGHT_FUNCTION(EnrichItemIsPicture);
    REGISTER_LIGHT_FUNCTION(EnrichItemThompsonScoreV2);
    REGISTER_LIGHT_FUNCTION(EnrichItemFcTowerXtr);
    REGISTER_LIGHT_FUNCTION(EnrichItemFcMcXtr);
    REGISTER_LIGHT_FUNCTION(EnrichLongTermAidTriggerList);
    REGISTER_LIGHT_FUNCTION(EnrichLongTermAidTriggerListV2);
    REGISTER_LIGHT_FUNCTION(EnrichItemIsAuditReviewHigh);
    REGISTER_LIGHT_FUNCTION(EnrichItemIsTeenagerPhoto);
    REGISTER_LIGHT_FUNCTION(EnrichItemIsEmpHtrFilter);
    REGISTER_LIGHT_FUNCTION(EnrichItemThompsonFilter);
    REGISTER_LIGHT_FUNCTION(EnrichItemHotNewAttrs);
    REGISTER_LIGHT_FUNCTION(EnrichItemNrSurveyPosNegInfo);
    REGISTER_LIGHT_FUNCTION(CalclauteRequestValueV0);
    REGISTER_LIGHT_FUNCTION(CalclauteRequestValuePositions);
    REGISTER_LIGHT_FUNCTION(EnrichTargetHetuTagItemThompsonScore);
    REGISTER_LIGHT_FUNCTION(EnrichTargetBadPhotoCustomHetuTagList);
    REGISTER_LIGHT_FUNCTION(CalclauteRequestValuePositionsValueSort);
    REGISTER_LIGHT_FUNCTION(SolveLambda);
    REGISTER_LIGHT_FUNCTION(CalclauteFinalQuotaV2);
    REGISTER_LIGHT_FUNCTION(CombineRankResultAttr);
  }

  static const int AUDIT_HOT_HIGH_REVIEW_HIGH_LEVEL = 4;                // 高热审优质
  static const int TOP_K_AUDIT_LEVEL_PASS = 3;                          // top k 审通过
  static const int AUDIT_COLD_REVIEW_LEVEL_HIGH_QUALITY_FIX = 2002190;  // 新回审优质

  static bool CombineRankResultAttr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    // input
    auto hetu_tag_v1_level_one_list_accessor = context.GetIntListItemAttr("hetu_tag_v1_level_one_list");
    auto hetu_tag_v2_level_two_list_accessor = context.GetIntListItemAttr("hetu_tag_v1_level_two_list");
    auto hetu_tag_v3_level_three_list_accessor = context.GetIntListItemAttr("hetu_tag_v1_level_three_list");
    auto duration_ms_accessor = context.GetIntItemAttr("duration_ms");

#define DECLARE_PXTR(pxtr) auto pxtr##_accessor = context.GetDoubleItemAttr(#pxtr); // NOLINT

    DECLARE_PXTR(fr_ctr);
    DECLARE_PXTR(fr_ltr);
    DECLARE_PXTR(fr_ftr);
    DECLARE_PXTR(fr_wtr);
    DECLARE_PXTR(fr_ptr);
    DECLARE_PXTR(fr_htr);
    DECLARE_PXTR(fr_svtr);
    DECLARE_PXTR(fr_lvtr);
    DECLARE_PXTR(fr_vtr);
    DECLARE_PXTR(fr_cmtr);
    DECLARE_PXTR(fr_evtr);
    DECLARE_PXTR(fr_tag_ctr);
    DECLARE_PXTR(fr_cmef);
    DECLARE_PXTR(fr_fvtr);
    DECLARE_PXTR(fr_epstr);
    DECLARE_PXTR(fr_etcm);
    DECLARE_PXTR(fr_setr);
    DECLARE_PXTR(fr_cltr);
    DECLARE_PXTR(fr_swptr);
    DECLARE_PXTR(fr_swpst);
    DECLARE_PXTR(fr_swppc);
    DECLARE_PXTR(fr_swpac);
    DECLARE_PXTR(fr_lstr);
    DECLARE_PXTR(fr_lsst);
    DECLARE_PXTR(fr_cpr);
    DECLARE_PXTR(fr_effective_watch_live_time);
    DECLARE_PXTR(fr_adaptive_wtd);
    DECLARE_PXTR(fr_click_live);
    DECLARE_PXTR(original_value_score_stage3);

    // output
    auto rank_result_accessor = context.SetStringItemAttr("rank_result_str");

    thread_local ks::reco::RankResult rank_result;

    // calc
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      rank_result.Clear();

      // start
      rank_result.set_exp_tag(result.reason);
      rank_result.mutable_photo_info()->set_photo_id(result.GetId());
      rank_result.set_duration_ms(duration_ms_accessor(result).value_or(0));

      auto hetu = rank_result.mutable_photo_info()->mutable_hetu_tag_level_info();
      auto hetu_tag_v1_level_one_list = hetu_tag_v1_level_one_list_accessor(result);
      auto hetu_tag_v2_level_two_list = hetu_tag_v2_level_two_list_accessor(result);
      auto hetu_tag_v3_level_three_list = hetu_tag_v3_level_three_list_accessor(result);
      if (hetu_tag_v1_level_one_list && hetu_tag_v1_level_one_list->size()) {
        hetu->add_hetu_level_one(hetu_tag_v1_level_one_list->operator[](0));
      }
      if (hetu_tag_v2_level_two_list && hetu_tag_v2_level_two_list->size()) {
        hetu->add_hetu_level_two(hetu_tag_v2_level_two_list->operator[](0));
      }
      if (hetu_tag_v3_level_three_list && hetu_tag_v3_level_three_list->size()) {
        hetu->add_hetu_level_three(hetu_tag_v3_level_three_list->operator[](0));
      }

      // rank_result.set_score(result.score);

#define SET_PXTR(from, to)                                        /*NOLINT*/\
  double to##_tmp_value = from##_accessor(result).value_or(0.0);  /*NOLINT*/\
  if (to##_tmp_value != 0.0) {                                    /*NOLINT*/\
    rank_result.set_##to(to##_tmp_value);                         /*NOLINT*/\
  }

      SET_PXTR(fr_ctr, pctr);
      SET_PXTR(fr_ltr, pltr);
      SET_PXTR(fr_ftr, pftr);
      SET_PXTR(fr_wtr, pwtr);
      SET_PXTR(fr_ptr, pptr);
      SET_PXTR(fr_htr, phtr);
      SET_PXTR(fr_svtr, psvr);
      SET_PXTR(fr_lvtr, plvtr);
      SET_PXTR(fr_vtr, pvtr);
      SET_PXTR(fr_cmtr, pcmtr);
      SET_PXTR(fr_evtr, pevr);
      SET_PXTR(fr_tag_ctr, ptag_ctr);
      SET_PXTR(fr_cmef, pcmef);
      SET_PXTR(fr_fvtr, pfvtr);
      SET_PXTR(fr_epstr, pepstr);
      SET_PXTR(fr_etcm, petcm);
      SET_PXTR(fr_setr, psetr);
      SET_PXTR(fr_cltr, pcltr);
      SET_PXTR(fr_swptr, pswptr);
      SET_PXTR(fr_swpst, pswpst);
      SET_PXTR(fr_swppc, pswppc);
      SET_PXTR(fr_swpac, pswpac);
      SET_PXTR(fr_lstr, plstr);
      SET_PXTR(fr_lsst, plsst);
      SET_PXTR(fr_cpr, pcpr);
      SET_PXTR(fr_effective_watch_live_time, effective_watch_live_time);
      SET_PXTR(fr_adaptive_wtd, pwtd);
      SET_PXTR(fr_click_live, pclick_live_v1);

      double original_value_score_stage3_tmp = original_value_score_stage3_accessor(result).value_or(0.0);
      if (original_value_score_stage3_tmp != 0.0) {
        rank_result.mutable_rerank_score_array()->set_final_ensemble_score(original_value_score_stage3_tmp);
      }

      // output
      rank_result_accessor(result, rank_result.SerializeAsString());
    });
    return true;
  }

  static bool ParseAbParamIntValue(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    // 形如 n_nd:0,0,0;n_tnu:1,1;rd:1,1;
    auto ab_param_config_str = context.GetStringCommonAttr("AB_PARAM_CONFIG_STR").value_or("");
    if (ab_param_config_str.empty()) {
      context.SetIntCommonAttr("AB_PARAM_VALUE", 0);
      return true;
    }

    std::string matched_value_str;
    int get_matched_value_str_result =
        GetAbParamMatchedValueStr(context, ab_param_config_str, ",", &matched_value_str);
    if (get_matched_value_str_result <= 0) {
      context.SetIntCommonAttr("AB_PARAM_VALUE", 0);
      // 正常退出情况下不报错
      return get_matched_value_str_result == 0;
    }
    int value = 0;
    if (!absl::SimpleAtoi(matched_value_str, &value)) {
      CL_LOG_ERROR("fail2parse_ab_param", "invalid_int_value")
          << "fail to parse ab param, invalid int value:" << matched_value_str
          << ", config:" << ab_param_config_str;
      context.SetIntCommonAttr("AB_PARAM_VALUE", 0);
      return false;
    }
    context.SetIntCommonAttr("AB_PARAM_VALUE", value);
    return true;
  }

  static bool ParseAbParamDoubleValue(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    // 形如 n_nd:0.5#0#0;n_tnu:0.1#0.5;rd:0.5#0.5;
    auto ab_param_config_str = context.GetStringCommonAttr("AB_PARAM_CONFIG_STR").value_or("");
    if (ab_param_config_str.empty()) {
      context.SetDoubleCommonAttr("AB_PARAM_VALUE", 0);
      return true;
    }

    std::string matched_value_str;
    int get_matched_value_str_result =
        GetAbParamMatchedValueStr(context, ab_param_config_str, "#", &matched_value_str);
    if (get_matched_value_str_result <= 0) {
      context.SetDoubleCommonAttr("AB_PARAM_VALUE", 0);
      // 正常退出情况下不报错
      return get_matched_value_str_result == 0;
    }

    double value = 0;
    if (!absl::SimpleAtod(matched_value_str, &value)) {
      CL_LOG_ERROR("fail2parse_ab_param", "invalid_double_value")
          << "fail to parse ab param, invalid double value:" << matched_value_str
          << ", config:" << ab_param_config_str;
      context.SetDoubleCommonAttr("AB_PARAM_VALUE", 0);
      return false;
    }
    context.SetDoubleCommonAttr("AB_PARAM_VALUE", value);
    return true;
  }

  static bool ParseAbParamStringValue(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto ab_param_config_str = context.GetStringCommonAttr("AB_PARAM_CONFIG_STR").value_or("");
    if (ab_param_config_str.empty()) {
      context.SetStringCommonAttr("AB_PARAM_VALUE", "");
      return true;
    }

    std::string matched_value_str;
    int get_matched_value_str_result =
        GetAbParamMatchedValueStr(context, ab_param_config_str, ",", &matched_value_str);
    if (get_matched_value_str_result <= 0) {
      context.SetStringCommonAttr("AB_PARAM_VALUE", "");
      // 正常退出情况下不报错
      return get_matched_value_str_result == 0;
    }
    context.SetStringCommonAttr("AB_PARAM_VALUE", matched_value_str);
    return true;
  }

  // -1 表示错误，0 表示正常退出 1 表示返回结果
  static int GetAbParamMatchedValueStr(const CommonRecoLightFunctionContext &context,
                                       absl::string_view config_str, const std::string &delimiter,
                                       std::string *matched_value_str) {
    auto user_type = context.GetStringCommonAttr("AB_PARAM_USER_TYPE").value_or("");
    auto user_type_with_product = context.GetStringCommonAttr("AB_PARAM_USER_TYPE_WITH_PRODUCT").value_or("");
    auto index = context.GetIntCommonAttr("AB_PARAM_INDEX").value_or(0);
    std::vector<std::string> config_str_vec = absl::StrSplit(config_str, ";", absl::SkipWhitespace());
    std::string matched_config_str;
    for (int i = 0; i < config_str_vec.size(); ++i) {
      std::vector<std::string> key_val_vec = absl::StrSplit(config_str_vec[i], ":", absl::SkipWhitespace());
      if (key_val_vec.size() != 2) {
        CL_LOG_ERROR("fail2parse_ab_param", "invalid_config_str_size")
            << "fail to parse ab param, key_val_vec size != 2, config:" << config_str;
        return -1;
      }
      if (user_type == key_val_vec[0] || user_type_with_product == key_val_vec[0]) {
        matched_config_str = key_val_vec[1];
        break;
      }
    }
    if (matched_config_str.empty()) {
      // 没有匹配的用户类型配置，正常退出
      return 0;
    }
    std::vector<std::string> matched_config_str_split = absl::StrSplit(matched_config_str, delimiter);

    if (index < 0 || index >= matched_config_str_split.size()) {
      CL_LOG_ERROR("fail2parse_ab_param", "invalid_index")
          << "fail to parse ab param, config:" << config_str << ", user_type:" << user_type
          << ", index:" << index;
      return -1;
    }
    *matched_value_str = matched_config_str_split[index];
    return 1;
  }

  static bool EnrichDayOfWeekAndHourOfDay(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    base::Time ts = base::Time::FromTimeT(base::GetTimestamp() / 1e6);
    base::Time::Exploded exploded;
    ts.LocalExplode(&exploded);
    int day_of_week = exploded.day_of_week;
    int hour_of_day = exploded.hour;
    context.SetIntCommonAttr("day_of_week", day_of_week);
    context.SetIntCommonAttr("hour_of_day", hour_of_day);
    return true;
  }

  static bool EnrichAppList(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                            RecoResultConstIter end) {
    const auto *user_info = context.GetProtoMessagePtrCommonAttr<ks::reco::UserInfo>("user_info");
    if (user_info != nullptr && user_info->has_apps()) {
      std::vector<std::string> app_list;
      for (const auto &user_app : user_info->apps().app()) {
        std::string name;
        if (user_app.has_app_name()) {
          name = user_app.app_name();
        } else {
          name = user_app.package();
        }
        if (user_app.has_first_installation_timestamp() &&
            user_app.first_installation_timestamp() % 1000 != 0) {
          app_list.emplace_back(name);
        }
      }
      if (app_list.size() > 0) {
        context.SetStringListCommonAttr("app_list", std::move(app_list));
      }
    }
    return true;
  }

  static bool EnrichGnnRetrTriggerList(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    const auto *user_info = context.GetProtoMessagePtrCommonAttr<ks::reco::UserInfo>("user_info");
    if (!user_info) {
      return false;
    }

    int size_limit = context.GetIntCommonAttr("size_limit").value_or(150);
    int short_term_interest_count = context.GetIntCommonAttr("short_term_interest_count").value_or(20);
    int long_term_interest_count = context.GetIntCommonAttr("long_term_interest_count").value_or(30);
    int each_hetu_tag_count = context.GetIntCommonAttr("each_hetu_tag_count").value_or(5);
    int64 playing_time_threshold = context.GetIntCommonAttr("trigger_playing_time_thres").value_or(18000);
    folly::F14FastMap<int32, int32> tag_cnt_map;
    std::vector<std::pair<int32, int>> tag_cnt_vec;
    folly::F14FastMap<int32, std::vector<uint64>> tag_pid_map;
    folly::F14FastSet<uint64> pid_set;
    folly::F14FastSet<uint64> hate_pid_set;
    folly::F14FastSet<uint64> hate_aid_set;
    for (const auto &hate_action : user_info->user_profile_v1().hate_list()) {
      const auto &pid = hate_action.photo_id();
      const auto &aid = hate_action.author_id();
      hate_pid_set.insert(pid);
      hate_aid_set.insert(aid);
    }
    int i = 0;
    for (const auto &action : user_info->user_profile_v1().video_playing_stat()) {
      const auto &pid = action.photo_id();
      const auto &aid = action.author_id();
      if (hate_pid_set.count(pid) > 0 || hate_aid_set.count(aid) > 0) {
        continue;
      }
      if (action.playing_time() > playing_time_threshold && action.playing_time() <= 1000000) {
        if (i++ >= short_term_interest_count) {
          if (action.has_hetu_tag_level_info() && !action.hetu_tag_level_info().hetu_level_two().empty()) {
            int hetu_tag = action.hetu_tag_level_info().hetu_level_two()[0];
            if (hetu_tag > 0) {
              if (tag_cnt_map.count(hetu_tag) == 0) {
                tag_cnt_map[hetu_tag] = 1;
              } else {
                tag_cnt_map[hetu_tag] += 1;
              }
              tag_pid_map[hetu_tag].push_back(pid);
            }
          }
        } else {
          if (pid_set.count(pid) == 0) {
            pid_set.insert(pid);
          }
        }
      }
    }
    for (auto &tag_info : tag_cnt_map) {
      tag_cnt_vec.push_back(tag_info);
    }
    std::sort(tag_cnt_vec.begin(), tag_cnt_vec.end(),
              [](std::pair<int32, int32> &a, std::pair<int32, int32> &b) { return a.second > b.second; });
    for (auto &tag_info : tag_cnt_vec) {
      int cnt = 0;
      for (auto &pid : tag_pid_map[tag_info.first]) {
        if (pid_set.count(pid) == 0) {
          pid_set.insert(pid);
          cnt++;
        }
        if (cnt >= each_hetu_tag_count) {
          break;
        }
      }
      if (pid_set.size() >= (short_term_interest_count + long_term_interest_count)) {
        break;
      }
    }
    std::vector<int64> pid_list;
    folly::F14FastSet<uint64> act_pid_set;
    // follow
    for (const auto &follow_action : user_info->user_profile_v1().follow_list()) {
      const auto &pid = follow_action.photo_id();
      const auto &aid = follow_action.author_id();
      if (hate_pid_set.count(pid) > 0 || hate_aid_set.count(aid) > 0) {
        continue;
      }
      act_pid_set.insert(pid);
    }
    // like
    for (const auto &like_action : user_info->user_profile_v1().like_list()) {
      const auto &pid = like_action.photo_id();
      const auto &aid = like_action.author_id();
      if (hate_pid_set.count(pid) > 0 || hate_aid_set.count(aid) > 0) {
        continue;
      }
      act_pid_set.insert(pid);
    }
    // collect
    for (const auto &collect_action : user_info->user_profile_v1().collect_list()) {
      const auto &pid = collect_action.photo_id();
      const auto &aid = collect_action.author_id();
      if (hate_pid_set.count(pid) > 0 || hate_aid_set.count(aid) > 0) {
        continue;
      }
      act_pid_set.insert(pid);
    }
    // 互动 trigger
    for (auto &pid : act_pid_set) {
      if (pid_list.size() >= size_limit) {
        break;
      }
      pid_list.push_back(pid);
    }
    // 不足的用 long view 补充
    for (auto &pid : pid_set) {
      if (pid_list.size() >= size_limit) {
        break;
      }
      pid_list.push_back(pid);
    }
    context.SetIntListCommonAttr("gnn_trigger_list", std::move(pid_list));
    return true;
  }

  static bool EnrichCoOccurInterestedItems(const CommonRecoLightFunctionContext &context,
                                           RecoResultConstIter begin, RecoResultConstIter end) {
    std::vector<std::string> interested_photo_list;
    auto view_photo_time_dist = context.GetStringListCommonAttr("view_photo_time_dist");
    auto up_v1_vps_pt_list = context.GetIntListCommonAttr("up_v1_vps_pt_list");
    auto up_v1_vps_pid_list = context.GetIntListCommonAttr("up_v1_vps_pid_list");
    if (view_photo_time_dist == absl::nullopt || up_v1_vps_pt_list == absl::nullopt ||
        up_v1_vps_pid_list == absl::nullopt) {
      context.SetStringListCommonAttr("interested_photo_list", std::move(interested_photo_list));
      return true;
    }
    if (up_v1_vps_pt_list->size() != view_photo_time_dist->size() ||
        up_v1_vps_pt_list->size() != up_v1_vps_pid_list->size()) {
      context.SetStringListCommonAttr("interested_photo_list", std::move(interested_photo_list));
      return true;
    }
    folly::F14FastMap<int64, int32> pid_map;
    for (int i = 0; i < view_photo_time_dist->size(); ++i) {
      int rank_index = GetViewPhotoRankIndex(up_v1_vps_pt_list->at(i), view_photo_time_dist->at(i));
      pid_map[up_v1_vps_pid_list->at(i)] = rank_index;
    }
    auto up_v1_like_pid_list = context.GetIntListCommonAttr("up_v1_like_pid_list");
    if (up_v1_like_pid_list) {
      int like_photo_num_limit = context.GetIntCommonAttr("like_photo_num_limit").value_or(0);
      int like_num = 0;
      for (int i = 0; i < up_v1_like_pid_list->size(); ++i) {
        if (like_num >= like_photo_num_limit) {
          break;
        }
        pid_map[up_v1_like_pid_list->at(i)] = 10;
        like_num++;
      }
    }

    auto up_v1_pe_pid_list = context.GetIntListCommonAttr("up_v1_pe_pid_list");
    if (up_v1_pe_pid_list) {
      int pe_photo_num_limit = context.GetIntCommonAttr("pe_photo_num_limit").value_or(0);
      int pe_num = 0;
      for (int i = 0; i < up_v1_pe_pid_list->size(); ++i) {
        if (pe_num >= pe_photo_num_limit) {
          break;
        }
        pid_map[up_v1_pe_pid_list->at(i)] = 11;
        pe_num++;
      }
    }

    auto up_v1_follow_pid_list = context.GetIntListCommonAttr("up_v1_follow_pid_list");
    if (up_v1_follow_pid_list) {
      int follow_photo_num_limit = context.GetIntCommonAttr("follow_photo_num_limit").value_or(0);
      int follow_num = 0;
      for (int i = 0; i < up_v1_follow_pid_list->size(); ++i) {
        if (follow_num >= follow_photo_num_limit) {
          break;
        }
        pid_map[up_v1_follow_pid_list->at(i)] = 12;
        follow_num++;
      }
    }

    std::vector<std::pair<int64, int32>> interested_photo_id_score_vec;
    for (auto it = pid_map.begin(); it != pid_map.end(); ++it) {
      interested_photo_id_score_vec.emplace_back(std::make_pair(it->first, it->second));
    }
    std::sort(interested_photo_id_score_vec.begin(), interested_photo_id_score_vec.end(),
              [](const std::pair<int64, int32> &x, const std::pair<int64, int32> &y) -> int {
                return x.second > y.second;
              });
    auto interested_photo_size = context.GetIntCommonAttr("interested_photo_size");
    auto trigger_photo_score_thres = context.GetIntCommonAttr("trigger_photo_score_thres");

    for (auto it = interested_photo_id_score_vec.begin(); it != interested_photo_id_score_vec.end(); ++it) {
      if (interested_photo_list.size() < interested_photo_size && it->second >= trigger_photo_score_thres) {
        interested_photo_list.emplace_back(base::Int64ToString(it->first));
      }
    }
    context.SetStringListCommonAttr("interested_photo_list", std::move(interested_photo_list));
    return true;
  }

  static int GetViewPhotoRankIndex(int play_time, absl::string_view view_photo_str) {
    std::vector<std::string> split_list = absl::StrSplit(view_photo_str, ":", absl::SkipWhitespace());
    if (split_list.size() != 9) {
      return -1;
    }
    int count = 0;
    for (int i = 0; i < split_list.size(); ++i) {
      int value = 0;
      if (!absl::SimpleAtoi(split_list[i], &value)) {
        continue;
      }
      if (play_time > value) {
        ++count;
      }
    }
    return count;
  }

  static bool EnrichInTargetTagList(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto tag_list = context.GetIntListCommonAttr("tag_list");
    if (!tag_list || tag_list->empty()) {
      return true;
    }
    folly::F14FastSet<int64> tag_set(tag_list->begin(), tag_list->end());

    auto item_tag_list_accessor = context.GetIntListItemAttr("item_tag_list");
    auto in_target_tag_list_setter = context.SetIntItemAttr("in_target_tag_list");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto hetu_tag_list = item_tag_list_accessor(result);
      int in_target_tag_list = 0;
      if (hetu_tag_list) {
        for (auto tag : *hetu_tag_list) {
          if (tag_set.count(tag) > 0) {
            in_target_tag_list = 1;
            break;
          }
        }
      }
      in_target_tag_list_setter(result, in_target_tag_list);
    });

    return true;
  }

  static bool EnrichItemAttrDiffCount(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto item_attr_accessor1 = context.GetIntItemAttr("item_attr1");
    auto item_attr_accessor2 = context.GetIntItemAttr("item_attr2");
    int diff_count = 0;

    std::for_each(begin, end, [=, &diff_count](const CommonRecoResult &result) {
      auto value1 = item_attr_accessor1(result);
      auto value2 = item_attr_accessor2(result);
      if (value1 && !value2) {
        diff_count++;
      }
      if (!value1 && value2) {
        diff_count++;
      }
      if (value1 && value2 && value1.value() != value2.value()) {
        diff_count++;
      }
    });
    context.SetIntCommonAttr("attr_diff_count", diff_count);
    return true;
  }

  static bool EnrichItemThompsonScore(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    // common input
    auto is_gamora_user = context.GetIntCommonAttr("is_gamora_user").value_or(0);
    auto audit_hot_weight_list_reset = context.GetDoubleListCommonAttr("audit_hot_weight_list_reset");
    // item input
    auto thanos_stats_positive_accessor = context.GetDoubleItemAttr("thanos_stats_positive");
    auto nebula_stats_positive_accessor = context.GetDoubleItemAttr("nebula_stats_positive");
    auto thanos_stats_negative_accessor = context.GetDoubleItemAttr("thanos_stats_negative");
    auto nebula_stats_negative_accessor = context.GetDoubleItemAttr("nebula_stats_negative");
    auto audit_hot_high_tag_level_accessor = context.GetIntItemAttr("audit_hot_high_tag_level");
    // item output
    auto thompson_score_accessor = context.SetDoubleItemAttr("thompson_score");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double thompson_score = 0.0f;
      int64 postive_cnt = 0, negtive_cnt = 0;
      if (is_gamora_user) {
        negtive_cnt = thanos_stats_negative_accessor(result).value_or(0);
        postive_cnt = thanos_stats_positive_accessor(result).value_or(0);
      } else {
        negtive_cnt = nebula_stats_negative_accessor(result).value_or(0);
        postive_cnt = nebula_stats_positive_accessor(result).value_or(0);
      }
      if (0 != negtive_cnt) {
        auto audit_hot_high_tag_level = audit_hot_high_tag_level_accessor(result);
        double audit_hot_weight = 1.0;
        if (audit_hot_high_tag_level && audit_hot_weight_list_reset && *audit_hot_high_tag_level > 0 &&
            audit_hot_weight_list_reset->size() >= *audit_hot_high_tag_level) {
          audit_hot_weight = (*audit_hot_weight_list_reset)[*audit_hot_high_tag_level - 1];
        }
        boost::random::beta_distribution<> distribution(std::max(1L, negtive_cnt), std::max(1L, postive_cnt));
        thread_local boost::random::mt19937 rng(base::GetTimestamp());
        thompson_score = distribution(rng) * audit_hot_weight;
        // thompson_score = std::max(1L, negtive_cnt) * std::max(1L, postive_cnt) * audit_hot_weight;
        // LOG(INFO)<< negtive_cnt << " "<< postive_cnt <<" "<< audit_hot_weight;
      }
      thompson_score_accessor(result, thompson_score);
    });
    return true;
  }

  static bool EnrichItemIsPicture(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    // input
    auto upload_type_accessor = context.GetIntItemAttr("upload_type");
    auto duration_ms_accessor = context.GetIntItemAttr("duration_ms");
    // output
    auto is_picture_accessor = context.SetIntItemAttr("is_picture");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int64 is_picture = 0;
      if (duration_ms_accessor(result).value_or(0) <= 100) {
        is_picture = 1;
      }
      auto upload_type = upload_type_accessor(result).value_or(0);
      if (upload_type == 7 || upload_type == 10 || upload_type == 11 || upload_type == 70) {
        is_picture = 1;
      }
      is_picture_accessor(result, is_picture);
    });
    return true;
  }

  // 内部函数
  static void PrintUserInfoProtoBuf(const CommonRecoLightFunctionContext &context,
                                    const ::ks::reco::UserInfo *a) {
    auto llsid = context.GetStringCommonAttr("llsid").value_or("");
    auto request_type = context.GetStringCommonAttr("request_type").value_or("");
    auto enable_remove_tnu_leaf = context.GetIntCommonAttr("enable_remove_tnu_leaf").value_or(0);
    if (a == nullptr) {
      LOG(INFO) << "llsid:" << llsid << ",request_type:" << request_type
                << ",enable_remove_tnu_leaf:" << enable_remove_tnu_leaf << ",userinfo:nullptr";
    } else {
      LOG(INFO) << "llsid:" << llsid << ",request_type:" << request_type
                << ",enable_remove_tnu_leaf:" << enable_remove_tnu_leaf
                << ",follow_list:" << a->user_profile_v1().follow_list_size()
                << ",br:" << a->browsed_photo_ids_size() << ",isnr:" << a->is_nr_user()
                << ",edge_reco_bit:" << a->edge_reco_bit();
    }
  }

  // 对外提供 打印 UserInfo 指针能力
  static bool PrintUserInfoPtr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                               RecoResultConstIter end) {
    PrintUserInfoProtoBuf(context, google::protobuf::down_cast<const ::ks::reco::UserInfo *>(
                                       context.GetPtrCommonAttr<::google::protobuf::Message>("user_info")));
    return true;
  }

  static bool PrintUserInfo(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                            RecoResultConstIter end) {
    // input
    auto local_user_info = context.GetStringCommonAttr("user_info_str").value_or("");
    thread_local ::ks::reco::UserInfo local_user_info_pb;
    local_user_info_pb.Clear();
    local_user_info_pb.ParseFromArray(local_user_info.data(), local_user_info.size());
    PrintUserInfoProtoBuf(context, &local_user_info_pb);
    return true;
  }

  static bool CompareUserinfoDiff(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    // input
    auto local_user_info = context.GetStringCommonAttr("userinfo_str").value_or("");
    auto remote_user_info = context.GetStringCommonAttr("remote_userinfo_str").value_or("");
    auto llsid = context.GetStringCommonAttr("llsid").value_or("");
    auto request_type = context.GetStringCommonAttr("request_type").value_or("");
    if (local_user_info.empty()) {
      LOG(INFO) << "request:" << llsid << ",request_type:" << request_type << ",get local userinfo error";
      return false;
    }
    if (remote_user_info.empty()) {
      LOG(INFO) << "request:" << llsid << ",request_type:" << request_type << ",get remote userinfo error";
      return false;
    }
    LOG(INFO) << "request:" << llsid << ",request_type:" << request_type
              << ", local size:" << local_user_info.size() << ", remote size:" << remote_user_info.size();
    thread_local ::ks::reco::UserInfo local_user_info_pb, remote_user_info_pb;
    local_user_info_pb.Clear();
    remote_user_info_pb.Clear();

    local_user_info_pb.ParseFromArray(local_user_info.data(), local_user_info.size());
    remote_user_info_pb.ParseFromArray(remote_user_info.data(), remote_user_info.size());

    ::google::protobuf::util::MessageDifferencer diff;
    diff.set_message_field_comparison(::google::protobuf::util::MessageDifferencer::EQUIVALENT);
    std::string diff_output;
    diff.ReportDifferencesToString(&diff_output);
    diff.IgnoreField(::ks::reco::UserInfo::descriptor()->FindFieldByName("browsed_photo_ids"));
    diff.Compare(local_user_info_pb, remote_user_info_pb);
    LOG(INFO) << "request:" << llsid << ",request_type:" << request_type
              << ", br:" << local_user_info_pb.browsed_photo_ids_size() << " vs "
              << remote_user_info_pb.browsed_photo_ids_size() << " minus:"
              << (int)local_user_info_pb.browsed_photo_ids_size() -
                     remote_user_info_pb.browsed_photo_ids_size()
              << ",diff:" << diff_output;
    if (diff_output.size() > 1000) {
      local_user_info_pb.clear_browsed_photo_ids();
      local_user_info_pb.mutable_user_profile_v1()->clear_real_show_list();
      local_user_info_pb.mutable_user_profile_v1()->clear_click_list();
      local_user_info_pb.mutable_user_profile_v1()->clear_video_playing_stat();
      remote_user_info_pb.clear_browsed_photo_ids();
      remote_user_info_pb.mutable_user_profile_v1()->clear_real_show_list();
      remote_user_info_pb.mutable_user_profile_v1()->clear_click_list();
      remote_user_info_pb.mutable_user_profile_v1()->clear_video_playing_stat();
      LOG(INFO) << "request:" << llsid << "fl:" << local_user_info_pb.user_profile_v1().follow_list_size()
                << ",br:" << local_user_info_pb.browsed_photo_ids_size() << ","
                << local_user_info_pb.is_nr_user() << ",local userinfo:" << local_user_info_pb.DebugString();
      LOG(INFO) << "request:" << llsid << "fl:" << remote_user_info_pb.user_profile_v1().follow_list_size()
                << ",br:" << remote_user_info_pb.browsed_photo_ids_size() << ","
                << remote_user_info_pb.is_nr_user()
                << ",remote userinfo:" << remote_user_info_pb.DebugString();
      // std::cerr << "request:" << llsid << ",local userinfo:" << local_user_info_pb.DebugString();
      // std::cerr << "request:" << llsid << ",remote userinfo:" << remote_user_info_pb.DebugString();
    }
    return true;
  }

  static bool EnrichItemThompsonScoreV2(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    // common input
    auto nr_t_negative_thompson_filter_threshold =
        context.GetDoubleCommonAttr("nr_t_negative_thompson_filter_threshold").value_or(0.0);
    auto nr_t_negative_way_ver = context.GetIntCommonAttr("nr_t_negative_way_ver").value_or(0);
    auto audit_hot_weight_list_accessor = context.GetDoubleListCommonAttr("audit_hot_weight_list");
    // item input
    auto like_count_accessor = context.GetIntItemAttr("like_count");
    auto unlike_count_accessor = context.GetIntItemAttr("unlike_count");
    auto show_count_accessor = context.GetIntItemAttr("show_count");
    auto report_count_accessor = context.GetIntItemAttr("report_count");
    auto audit_hot_high_tag_level_accessor = context.GetIntItemAttr("audit_hot_high_tag_level");

    // item output
    auto is_negative_thompson_filter_acessor = context.SetIntItemAttr("is_negative_thompson_filter");
    auto audit_hot_weight_accessor = context.SetDoubleItemAttr("audit_hot_weight");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int64 is_negative_thompson_filter = 0;
      auto like_count = like_count_accessor(result).value_or(0);
      auto unlike_count = unlike_count_accessor(result).value_or(0);
      auto show_count = show_count_accessor(result).value_or(0);
      auto report_count = report_count_accessor(result).value_or(0);
      double thompson_score = 0.0f;
      int64 postive_cnt = 0, negtive_cnt = 0;
      auto audit_hot_high_tag_level = audit_hot_high_tag_level_accessor(result).value_or(0);

      double audit_hot_weight = 1.0;
      if (audit_hot_high_tag_level > 0 && audit_hot_weight_list_accessor &&
          audit_hot_weight_list_accessor->size() >= audit_hot_high_tag_level) {
        audit_hot_weight = (*audit_hot_weight_list_accessor)[audit_hot_high_tag_level - 1];
      }

      negtive_cnt = unlike_count + report_count;

      if (nr_t_negative_way_ver == 0) {
        postive_cnt = like_count;
      } else {
        postive_cnt = show_count;
      }

      boost::random::beta_distribution<> distribution(std::max(1L, negtive_cnt), std::max(1L, postive_cnt));
      thread_local boost::random::mt19937 rng(base::GetTimestamp());
      thompson_score = distribution(rng) * audit_hot_weight;

      if (thompson_score > nr_t_negative_thompson_filter_threshold) {
        is_negative_thompson_filter = 1;
      }
      is_negative_thompson_filter_acessor(result, is_negative_thompson_filter);
      audit_hot_weight_accessor(result, audit_hot_weight);
    });
    return true;
  }

  static bool EnrichItemFcTowerXtr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    // common input
    auto mc_pxtr_value_accessor = context.GetDoubleListCommonAttr("mc_pxtr_value");
    auto mc_pxtr_label_accessor = context.GetStringListCommonAttr("mc_pxtr_label");
    // item input
    // auto seq_lf_accessor = context.GetIntItemAttr("seq_lf");
    // item output
    std::vector<std::string> nr_fc_mc_res = {
        "nr_fc_mc_ctr",      "nr_fc_mc_lvtr",      "nr_fc_mc_svr",      "nr_fc_mc_pc",  "nr_fc_mc_vtr",
        "nr_fc_mc_ltr",      "nr_fc_mc_wtr",       "nr_fc_mc_ftr",      "nr_fc_mc_eps", "nr_fc_mc_ces",
        "nr_fc_mc_flexevtr", "nr_fc_mc_pair_evtr", "nr_fc_mc_pair_lvtr"};
    std::vector<std::string> nr_fc_mc_name = {"ctr", "lvr", "svr", "pc",      "vtr",       "ltr",      "wtr",
                                              "ftr", "eps", "ces", "u2a_ctr", "pair_evtr", "pair_lvtr"};
    std::unordered_map<std::string, std::function<void(const CommonRecoResult &, const double)>> nr_fc_mc_map;
    std::vector<std::function<void(const CommonRecoResult &, const double)>> nr_fc_mc_accessor;
    for (auto res_str : nr_fc_mc_res) {
      nr_fc_mc_accessor.emplace_back(context.SetDoubleItemAttr(res_str));
    }
    int seq = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      for (int i = 0; i < nr_fc_mc_name.size(); i++) {
        nr_fc_mc_accessor[i](result, 0.0);
        nr_fc_mc_map[nr_fc_mc_name[i]] = nr_fc_mc_accessor[i];
      }
      if (mc_pxtr_label_accessor && mc_pxtr_label_accessor->size() > 0 && mc_pxtr_value_accessor &&
          mc_pxtr_value_accessor->size() > 0) {
        int64 fc_mc_label_num = mc_pxtr_label_accessor->size();
        // int64 seq_lf = seq_lf_accessor(result).value_or(0);
        if ((seq + 1) * fc_mc_label_num <= mc_pxtr_value_accessor->size()) {
          for (int i = 0; i < fc_mc_label_num; i++) {
            auto it = nr_fc_mc_map.find((*mc_pxtr_label_accessor)[i].data());
            if (it != nr_fc_mc_map.end()) {
              it->second(result, (*mc_pxtr_value_accessor)[seq * fc_mc_label_num + i]);
            }
          }
        }
      }
      ++seq;
    });
    return true;
  }

  static bool EnrichItemFcMcXtr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
    // item input
    std::vector<std::string> fc_mc_list = {
        "nr_fc_mc_ctr",      "nr_fc_mc_lvtr",      "nr_fc_mc_svr",      "nr_fc_mc_pc",  "nr_fc_mc_vtr",
        "nr_fc_mc_ltr",      "nr_fc_mc_wtr",       "nr_fc_mc_ftr",      "nr_fc_mc_eps", "nr_fc_mc_ces",
        "nr_fc_mc_flexevtr", "nr_fc_mc_pair_evtr", "nr_fc_mc_pair_lvtr"};
    std::vector<std::string> pure_mc_list = {
        "mc_ctr", "mc_lvtr",  "mc_svtr",  "mc_pc",      "mc_vtr",       "mc_ltr",      "mc_wtr",
        "mc_ftr", "mc_epstr", "mc_ecstr", "mc_u2a_ctr", "mc_pair_evtr", "mc_pair_lvtr"};
    std::vector<std::string> es_mc_list = {
        "es_mc_ctr",     "es_mc_lvtr",      "es_mc_svtr",     "es_mc_pc",    "es_mc_vtr",
        "es_mc_ltr",     "es_mc_wtr",       "es_mc_ftr",      "es_mc_epstr", "es_mc_ecstr",
        "es_mc_u2a_ctr", "es_mc_pair_evtr", "es_mc_pair_lvtr"};
    int64 len = std::min(fc_mc_list.size(), std::min(pure_mc_list.size(), es_mc_list.size()));
    std::vector<std::function<absl::optional<double>(const CommonRecoResult &)>> fc_mc_accessor_list,
        pure_mc_accessor_list;
    for (int i = 0; i < len; i++) {
      fc_mc_accessor_list.emplace_back(context.GetDoubleItemAttr(fc_mc_list[i]));
      pure_mc_accessor_list.emplace_back(context.GetDoubleItemAttr(pure_mc_list[i]));
    }

    // item output
    std::vector<std::function<void(const CommonRecoResult &, const double)>> es_mc_accessor_list,
        pure_mc_accessor_out_list;
    for (int i = 0; i < len; i++) {
      es_mc_accessor_list.emplace_back(context.SetDoubleItemAttr(es_mc_list[i]));
      pure_mc_accessor_out_list.emplace_back(context.SetDoubleItemAttr(pure_mc_list[i]));
    }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      for (int i = 0; i < len; i++) {
        auto pure_mc_value = pure_mc_accessor_list[i](result).value_or(0.0);
        es_mc_accessor_list[i](result, pure_mc_value);

        auto fc_mc_value = fc_mc_accessor_list[i](result).value_or(0.0);
        if (fc_mc_value > 0) {
          pure_mc_accessor_out_list[i](result, fc_mc_value);
          es_mc_accessor_list[i](result, fc_mc_value);
        }
      }
    });
    return true;
  }

  static bool EnrichLongTermAidTriggerList(const CommonRecoLightFunctionContext &context,
                                           RecoResultConstIter begin, RecoResultConstIter end) {
    // 各类行为在 trigger 选取时的权重 0:like,1:follow,2:forward,collect:3,profile:4,long_view:5
    const auto trigger_weight_list = context.GetIntListCommonAttr("trigger_weight_list");
    // 获取 colossus 长期行为列表
    const auto play_time_list = context.GetIntListCommonAttr("colossus_play_time_list");
    const auto duration_list = context.GetIntListCommonAttr("colossus_duration_list");
    const auto label_list = context.GetIntListCommonAttr("colossus_label_list");
    const auto profile_stay_time_list = context.GetIntListCommonAttr("colossus_profile_stay_time_list");
    const auto profile_feed_mode_stay_time_list =
        context.GetIntListCommonAttr("colossus_profile_feed_mode_stay_time_list");
    const auto aid_list = context.GetIntListCommonAttr("colossus_aid_list");
    // 最相关的 topk 个 trigger, 尾部 shuffle 的 trigger
    const auto topk_trigger_num = context.GetIntCommonAttr("topk_trigger_num").value_or(50);
    const auto variant_trigger_num = context.GetIntCommonAttr("variant_trigger_num").value_or(50);
    const auto variant_max_shuffle_size = context.GetIntCommonAttr("variant_max_shuffle_size").value_or(150);
    int effective_profile_stay_time_threshold_sec = 5;
    int ineffective_view_threshold = 3;
    int effective_view_threshold = 7;
    int long_view_threshold = 18;
    int very_long_view_threshold = 36;
    int negative_punish_score = 10000;

    if (!play_time_list || !duration_list || !label_list || !profile_stay_time_list ||
        !profile_feed_mode_stay_time_list || !aid_list || aid_list->size() == 0 ||
        play_time_list->size() != aid_list->size() || duration_list->size() != aid_list->size() ||
        label_list->size() != aid_list->size() || profile_stay_time_list->size() != aid_list->size() ||
        profile_feed_mode_stay_time_list->size() != aid_list->size() || topk_trigger_num <= 0 ||
        variant_trigger_num <= 0 || trigger_weight_list->size() != 6) {
      CL_LOG_EVERY_N(WARNING, 1000) << "EnrichLongTermAidTriggerList input common_attr check failed";
      return false;
    }
    folly::F14FastMap<int64, int64> aid_score_map;
    for (int i = 0; i < aid_list->size(); ++i) {
      int cur_trigger_score = 0;
      auto aid = (*aid_list)[i];
      auto play_time_sec = (*play_time_list)[i];
      auto duration_sec = (*duration_list)[i];
      auto label = (*label_list)[i];
      int like = label & 0x01;
      int follow = label & (1 << 1);
      int forward = label & (1 << 2);
      int report = label & (1 << 3);
      int negative = label & (1 << 13);
      int collect = label & (1 << 19);
      auto profile_stay_time = (*profile_stay_time_list)[i];
      auto profile_feed_mode_stay_time = (*profile_feed_mode_stay_time_list)[i];
      if (report > 0 || negative > 0) {
        cur_trigger_score -= negative_punish_score;
      }
      if (like > 0) {
        cur_trigger_score += (*trigger_weight_list)[0];
      }
      if (follow > 0) {
        cur_trigger_score += (*trigger_weight_list)[1];
      }
      if (forward > 0) {
        cur_trigger_score += (*trigger_weight_list)[2];
      }
      if (collect > 0) {
        cur_trigger_score += (*trigger_weight_list)[3];
      }
      if ((profile_stay_time + profile_feed_mode_stay_time) >= effective_profile_stay_time_threshold_sec) {
        cur_trigger_score += (*trigger_weight_list)[4];
      }
      bool is_long_view = false;
      if (duration_sec < ineffective_view_threshold) {
        is_long_view = play_time_sec >= long_view_threshold;
      } else if (duration_sec > very_long_view_threshold) {
        is_long_view = play_time_sec >= very_long_view_threshold;
      } else {
        is_long_view = play_time_sec * 1000 >= floor((duration_sec * 1000 * 28 + 180000) / 33.0);
      }
      if (is_long_view) {
        cur_trigger_score += (*trigger_weight_list)[5];
      }
      if (aid_score_map.count(aid) > 0) {
        aid_score_map[aid] += cur_trigger_score;
      } else {
        aid_score_map.insert(std::make_pair(aid, cur_trigger_score));
      }
    }
    std::vector<std::pair<int64, int64>> valid_trigger_list;
    for (auto &trigger_info : aid_score_map) {
      if (trigger_info.second > 0) {
        valid_trigger_list.push_back(trigger_info);
      }
    }
    std::sort(valid_trigger_list.begin(), valid_trigger_list.end(),
              [](const std::pair<int64, int64> &x, const std::pair<int64, int64> &y) -> int {
                return x.second > y.second;
              });
    std::vector<int64> topk_trigger_aid;
    std::vector<int64> variant_trigger_aid;
    for (int i = 0; i < valid_trigger_list.size(); i++) {
      if (i < topk_trigger_num) {
        topk_trigger_aid.push_back(valid_trigger_list[i].first);
      } else {
        if (variant_trigger_aid.size() < variant_max_shuffle_size) {
          variant_trigger_aid.push_back(valid_trigger_list[i].first);
        } else {
          break;
        }
      }
    }
    std::random_device rd;
    std::mt19937 g(rd());
    std::shuffle(variant_trigger_aid.begin(), variant_trigger_aid.end(), g);
    if (variant_trigger_aid.size() > variant_trigger_num) {
      variant_trigger_aid.resize(variant_trigger_num);
    }
    context.SetIntListCommonAttr("topk_trigger_aid", std::move(topk_trigger_aid));
    context.SetIntListCommonAttr("variant_trigger_aid", std::move(variant_trigger_aid));
    return true;
  }

  static bool EnrichLongTermAidTriggerListV2(const CommonRecoLightFunctionContext &context,
                                             RecoResultConstIter begin, RecoResultConstIter end) {
    // 各类行为在 trigger 选取时的权重 0:like,1:follow,2:forward,collect:3,profile:4,long_view:5
    const auto trigger_weight_list = context.GetIntListCommonAttr("trigger_weight_list");
    // 获取 colossus 长期行为列表
    const auto play_time_list = context.GetIntListCommonAttr("colossus_play_time_list");
    const auto duration_list = context.GetIntListCommonAttr("colossus_duration_list");
    const auto label_list = context.GetIntListCommonAttr("colossus_label_list");
    const auto profile_stay_time_list = context.GetIntListCommonAttr("colossus_profile_stay_time_list");
    const auto profile_feed_mode_stay_time_list =
        context.GetIntListCommonAttr("colossus_profile_feed_mode_stay_time_list");
    const auto aid_list = context.GetIntListCommonAttr("colossus_aid_list");
    // 最相关的 topk 个 aid trigger
    const auto topk_trigger_num = context.GetIntCommonAttr("topk_trigger_num").value_or(80);
    // 短期 aid 列表
    const auto short_term_aids = context.GetIntListCommonAttr("short_term_aids");
    folly::F14FastSet<int64> short_term_aid_set;
    if (short_term_aids && short_term_aids->size() > 0) {
      short_term_aid_set.insert(short_term_aids->begin(), short_term_aids->end());
    }
    int effective_profile_stay_time_threshold_sec = 5;
    int ineffective_view_threshold = 3;
    int effective_view_threshold = 7;
    int long_view_threshold = 18;
    int very_long_view_threshold = 36;
    int negative_punish_score = 10000;
    if (!play_time_list || !duration_list || !label_list || !profile_stay_time_list ||
        !profile_feed_mode_stay_time_list || !aid_list || aid_list->size() == 0 ||
        play_time_list->size() != aid_list->size() || duration_list->size() != aid_list->size() ||
        label_list->size() != aid_list->size() || profile_stay_time_list->size() != aid_list->size() ||
        profile_feed_mode_stay_time_list->size() != aid_list->size() || topk_trigger_num <= 0 ||
        trigger_weight_list->size() != 6) {
      CL_LOG_EVERY_N(WARNING, 1000) << "EnrichLongTermAidTriggerListV2 input common_attr check failed";
      return false;
    }
    folly::F14FastMap<int64, int64> aid_score_map;
    for (int i = 0; i < aid_list->size(); ++i) {
      auto aid = (*aid_list)[i];
      if (short_term_aid_set.count(aid)) {
        continue;
      }
      int cur_trigger_score = 0;
      auto play_time_sec = (*play_time_list)[i];
      auto duration_sec = (*duration_list)[i];
      auto label = (*label_list)[i];
      int like = label & 0x01;
      int follow = label & (1 << 1);
      int forward = label & (1 << 2);
      int report = label & (1 << 3);
      int negative = label & (1 << 13);
      int collect = label & (1 << 19);
      auto profile_stay_time = (*profile_stay_time_list)[i];
      auto profile_feed_mode_stay_time = (*profile_feed_mode_stay_time_list)[i];
      if (report > 0 || negative > 0) {
        cur_trigger_score -= negative_punish_score;
      }
      if (like > 0) {
        cur_trigger_score += (*trigger_weight_list)[0];
      }
      if (follow > 0) {
        cur_trigger_score += (*trigger_weight_list)[1];
      }
      if (forward > 0) {
        cur_trigger_score += (*trigger_weight_list)[2];
      }
      if (collect > 0) {
        cur_trigger_score += (*trigger_weight_list)[3];
      }
      if ((profile_stay_time + profile_feed_mode_stay_time) >= effective_profile_stay_time_threshold_sec) {
        cur_trigger_score += (*trigger_weight_list)[4];
      }
      bool is_long_view = false;
      if (duration_sec < ineffective_view_threshold) {
        is_long_view = play_time_sec >= long_view_threshold;
      } else if (duration_sec > very_long_view_threshold) {
        is_long_view = play_time_sec >= very_long_view_threshold;
      } else {
        is_long_view = play_time_sec * 1000 >= floor((duration_sec * 1000 * 28 + 180000) / 33.0);
      }
      if (is_long_view) {
        cur_trigger_score += (*trigger_weight_list)[5];
      }
      if (aid_score_map.count(aid) > 0) {
        aid_score_map[aid] += cur_trigger_score;
      } else {
        aid_score_map.insert(std::make_pair(aid, cur_trigger_score));
      }
    }

    std::vector<std::pair<int64, int64>> valid_trigger_list;
    for (auto &trigger_info : aid_score_map) {
      if (trigger_info.second > 0) {
        valid_trigger_list.push_back(trigger_info);
      }
    }
    std::sort(valid_trigger_list.begin(), valid_trigger_list.end(),
              [](const std::pair<int64, int64> &x, const std::pair<int64, int64> &y) -> int {
                return x.second > y.second;
              });
    std::vector<int64> topk_trigger_aid;
    for (auto &valid_trigger : valid_trigger_list) {
      if (topk_trigger_aid.size() < topk_trigger_num) {
        topk_trigger_aid.push_back(valid_trigger.first);
      } else {
        break;
      }
    }
    context.SetIntListCommonAttr("topk_trigger_aid", std::move(topk_trigger_aid));
    return true;
  }

  static bool EnrichItemIsAuditReviewHigh(const CommonRecoLightFunctionContext &context,
                                  RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    // input
    auto topk_audit_level_accessor = context.GetIntItemAttr("topk_audit_level");
    auto audit_hot_high_tag_level_accessor = context.GetIntItemAttr("audit_hot_high_tag_level");
    auto audit_cold_review_level_accessor = context.GetIntItemAttr("audit_cold_review_level");
    // output
    auto is_audit_review_high_accessor = context.SetIntItemAttr("is_audit_review_high");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int is_audit_review_high = 0;
      auto audit_hot_high_tag_level = audit_hot_high_tag_level_accessor(result).value_or(0);
      auto topk_audit_level = topk_audit_level_accessor(result).value_or(0);
      auto audit_cold_review_level = audit_cold_review_level_accessor(result).value_or(0);
      if (topk_audit_level == TOP_K_AUDIT_LEVEL_PASS ||
          audit_hot_high_tag_level == AUDIT_HOT_HIGH_REVIEW_HIGH_LEVEL ||
          audit_cold_review_level == AUDIT_COLD_REVIEW_LEVEL_HIGH_QUALITY_FIX) {
        is_audit_review_high = 1;
      }
      is_audit_review_high_accessor(result, is_audit_review_high);
    });
    return true;
  }

  static bool EnrichItemIsTeenagerPhoto(const CommonRecoLightFunctionContext &context,
                                  RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    // input
    auto data_set_tags_bit_accessor = context.GetIntItemAttr("data_set_tags_bit");
    // output
    auto is_teenager_photo_accessor = context.SetIntItemAttr("is_teenager_photo");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int64 data_set_tags_bit = data_set_tags_bit_accessor(result).value_or(0);
      int is_teenager_photo = 0;
      if ((((data_set_tags_bit) >> 7) & 1) == 1) {
        is_teenager_photo = 1;
      }
      is_teenager_photo_accessor(result, is_teenager_photo);
    });
    return true;
  }

  static bool EnrichItemIsEmpHtrFilter(const CommonRecoLightFunctionContext &context,
                                  RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    // input
    auto nr_retr_emphtr_filter_threshold = context.GetDoubleCommonAttr("nr_retr_emphtr_filter_threshold");
    auto audit_weight_list = context.GetDoubleListCommonAttr("audit_weight_list");
    auto is_gamora_user = context.GetIntCommonAttr("is_gamora_user");

    auto nebula_stats_negative_count_accessor = context.GetIntItemAttr("nebula_stats_negative_count");
    auto nebula_stats_report_count_accessor = context.GetIntItemAttr("nebula_stats_report_count");
    auto nebula_stats_real_show_count_accessor = context.GetIntItemAttr("nebula_stats_real_show_count");
    auto thanos_stats_negative_count_accessor = context.GetIntItemAttr("thanos_stats_negative_count");
    auto thanos_stats_report_count_accessor = context.GetIntItemAttr("thanos_stats_report_count");
    auto thanos_stats_real_show_count_accessor = context.GetIntItemAttr("thanos_stats_real_show_count");

    // output
    auto is_emp_htr_filter_accessor = context.SetIntItemAttr("is_emp_htr_filter");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int negative = 0;
      double emp_htr = 0.0;
      int is_emp_htr_filter = 0;
      int64 nebula_stats_negative_count = nebula_stats_negative_count_accessor(result).value_or(0);
      int64 nebula_stats_report_count = nebula_stats_report_count_accessor(result).value_or(0);
      int64 nebula_stats_real_show_count = nebula_stats_real_show_count_accessor(result).value_or(0);
      int64 thanos_stats_negative_count = thanos_stats_negative_count_accessor(result).value_or(0);
      int64 thanos_stats_report_count = thanos_stats_report_count_accessor(result).value_or(0);
      int64 thanos_stats_real_show_count = thanos_stats_real_show_count_accessor(result).value_or(0);
      if (thanos_stats_real_show_count == 0) {
        thanos_stats_real_show_count = thanos_stats_real_show_count + 1;
      }
      if (nebula_stats_real_show_count == 0) {
        nebula_stats_real_show_count = nebula_stats_real_show_count + 1;
      }
      if (is_gamora_user == 1) {
        negative = thanos_stats_report_count + thanos_stats_negative_count;
        emp_htr = (negative * 1.0) / thanos_stats_real_show_count;

      } else {
          negative = nebula_stats_report_count + nebula_stats_negative_count;
          emp_htr = (negative * 1.0) / nebula_stats_real_show_count;
      }
      if (emp_htr >  nr_retr_emphtr_filter_threshold) {
        is_emp_htr_filter = 1;
      }
      is_emp_htr_filter_accessor(result, is_emp_htr_filter);
    });
    return true;
  }

  static bool EnrichItemThompsonFilter(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    // common input
    auto filter_threshold = context.GetDoubleCommonAttr("filter_threshold").value_or(0.0);
    auto filter_vv_limit = context.GetDoubleCommonAttr("filter_vv_limit").value_or(0.0);
    auto user_type_str = context.GetStringCommonAttr("user_type_str").value_or("");
    auto day_age_limit = context.GetIntCommonAttr("day_age_limit").value_or(0);
    // item input
    auto photo_negative_count_accessor = context.GetIntItemAttr("photo_negative_count");
    auto photo_positive_count_accessor = context.GetIntItemAttr("photo_positive_count");
    auto photo_upload_time_ms_accessor = context.GetIntItemAttr("upload_time");
    auto gamora_nd_show_count_accessor = context.GetIntItemAttr("nr_gamora_nd_stat_real_show_count");
    auto gamora_rd_show_count_accessor = context.GetIntItemAttr("nr_gamora_reflux_stat_real_show_count");
    auto gamora_tnu_show_count_accessor = context.GetIntItemAttr("nr_gamora_tnu_stat_real_show_count");
    auto nebula_nd_show_count_accessor = context.GetIntItemAttr("nr_nebula_nd_stat_real_show_count");
    auto nebula_rd_show_count_accessor = context.GetIntItemAttr("nr_nebula_reflux_stat_real_show_count");
    auto nebula_tnu_show_count_accessor = context.GetIntItemAttr("nr_nebula_tnu_stat_real_show_count");

    // item output
    auto is_negative_thompson_filter_acessor = context.SetIntItemAttr("is_thompson_filter");
    auto thompson_neg_score_accessor = context.SetDoubleItemAttr("thompson_neg_score");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto photo_negative_count = photo_negative_count_accessor(result).value_or(0);
      auto photo_positive_count = photo_positive_count_accessor(result).value_or(0);
      auto photo_upload_time_ms = photo_upload_time_ms_accessor(result).value_or(0);
      auto gamora_nd_show_count = gamora_nd_show_count_accessor(result).value_or(0);
      auto gamora_rd_show_count = gamora_rd_show_count_accessor(result).value_or(0);
      auto gamora_tnu_show_count = gamora_tnu_show_count_accessor(result).value_or(0);
      auto nebula_nd_show_count = nebula_nd_show_count_accessor(result).value_or(0);
      auto nebula_rd_show_count = nebula_rd_show_count_accessor(result).value_or(0);
      auto nebula_tnu_show_count = nebula_tnu_show_count_accessor(result).value_or(0);
      int show_count = 0;
      if (user_type_str == "gamora_nd") {
        show_count = gamora_nd_show_count;
      } else if (user_type_str == "gamora_rd") {
        show_count = gamora_rd_show_count;
      } else if (user_type_str == "gamora_tnu") {
        show_count = gamora_tnu_show_count;
      } else if (user_type_str == "nebula_nd") {
        show_count = nebula_nd_show_count;
      } else if (user_type_str == "nebula_rd") {
        show_count = nebula_rd_show_count;
      } else if (user_type_str == "nebula_tnu") {
        show_count = nebula_tnu_show_count;
      } else {
        CL_LOG_ERROR("EnrichItemThompsonFilter", "invalid_user_type_str")
          << "user_type_str=" << user_type_str;
      }
      int photo_age_day = (base::GetTimestamp() / 1000 - photo_upload_time_ms) / (1000 * 3600 * 24);
      int64 is_negative_thompson_filter = 0;
      double thompson_neg_score = 0.0f;
      if (show_count >= filter_vv_limit && photo_age_day > day_age_limit
          && (photo_negative_count + photo_positive_count) > 1) {
        boost::random::beta_distribution<> distribution(std::max(1L, photo_negative_count),
                    std::max(1L, photo_positive_count));
        thread_local boost::random::mt19937 rng(base::GetTimestamp());
        thompson_neg_score = distribution(rng);
        if (thompson_neg_score > filter_threshold) {
          is_negative_thompson_filter = 1;
        }
      }
      is_negative_thompson_filter_acessor(result, is_negative_thompson_filter);
      thompson_neg_score_accessor(result, thompson_neg_score);
    });
    return true;
  }

  static bool EnrichItemHotNewAttrs(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    // input
    auto upload_time_ms_accessor = context.GetIntItemAttr("upload_time");
    auto audit_hot_high_tag_level_accessor = context.GetIntItemAttr("audit_hot_high_tag_level");
    auto photo_dynamic_xtrs_str_accessor = context.GetStringItemAttr("photo_dynamic_xtrs_str");
    // output
    auto photo_age_segment_accessor = context.SetIntItemAttr("photo_age_segment");
    auto photo_age_hour_accessor = context.SetIntItemAttr("photo_age_hour");
    auto is_audit_good_accessor = context.SetIntItemAttr("is_audit_good");
    auto is_new_audit_good_accessor = context.SetIntItemAttr("is_new_audit_good");
    auto dynamic_evtr_diff_accessor = context.SetDoubleItemAttr("dynamic_evtr_diff");
    auto dynamic_lvtr_diff_accessor = context.SetDoubleItemAttr("dynamic_lvtr_diff");
    auto dynamic_evtr_accessor = context.SetDoubleItemAttr("dynamic_evtr");
    auto dynamic_lvtr_accessor = context.SetDoubleItemAttr("dynamic_lvtr");

    std::vector<int64> photo_age_segment_list = {0, 3, 8, 31, 61, 91, 181, 1<<30};

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto upload_time_ms = upload_time_ms_accessor(result).value_or(0);
      int audit_hot_high_tag_level = audit_hot_high_tag_level_accessor(result).value_or(0);
      auto photo_dynamic_xtrs_str = photo_dynamic_xtrs_str_accessor(result).value_or("");

      int photo_age_hour = (base::GetTimestamp() / 1000 - upload_time_ms) / (1000 * 3600);
      int photo_age_day = photo_age_hour / 24;
      int photo_age_segment = 10;
      int is_audit_good = 0;
      int is_new_audit_good = 0;
      double dynamic_evtr_diff = 0.0;
      double dynamic_lvtr_diff = 0.0;
      double dynamic_evtr = 0.0;
      double dynamic_lvtr = 0.0;
      for (int i = 1; i < photo_age_segment_list.size(); i++) {
        if (photo_age_day < photo_age_segment_list[i] && photo_age_day >= photo_age_segment_list[i-1]) {
          photo_age_segment = i - 1;
          break;
          }
      }
      if (audit_hot_high_tag_level == 4) {
        is_audit_good = 1;
      }
      if (photo_age_segment == 0 && audit_hot_high_tag_level == 4) {
        is_new_audit_good = 1;
      }

      std::vector<absl::string_view> photo_dynamic_xtrs_str_list;
      std::vector<double> photo_dynamic_xtrs_list;
      photo_dynamic_xtrs_str_list = absl::StrSplit(photo_dynamic_xtrs_str,
                                                    absl::ByAnyChar(","),
                                                    absl::SkipWhitespace());
      if (photo_dynamic_xtrs_str_list.size() == 10) {
        for (auto xtr_str : photo_dynamic_xtrs_str_list) {
          double xtr = 0.0;
          if (absl::SimpleAtod(xtr_str, &xtr)) {
            photo_dynamic_xtrs_list.push_back(xtr);
          }
        }
        if (photo_dynamic_xtrs_list.size() == 10) {
          dynamic_evtr_diff = photo_dynamic_xtrs_list[0];
          dynamic_lvtr_diff = photo_dynamic_xtrs_list[6];
          dynamic_evtr = photo_dynamic_xtrs_list[1];
          dynamic_lvtr = photo_dynamic_xtrs_list[7];
        }
      }

      photo_age_segment_accessor(result, photo_age_segment);
      photo_age_hour_accessor(result, photo_age_hour);
      is_audit_good_accessor(result, is_audit_good);
      is_new_audit_good_accessor(result, is_new_audit_good);
      dynamic_evtr_diff_accessor(result, dynamic_evtr_diff);
      dynamic_lvtr_diff_accessor(result, dynamic_lvtr_diff);
      dynamic_evtr_accessor(result, dynamic_evtr);
      dynamic_lvtr_accessor(result, dynamic_lvtr);
    });

    return true;
  }

  static bool EnrichItemNrSurveyPosNegInfo(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    // common input
    auto pid_str_list =
      context.GetStringListCommonAttr("pid_str_list").value_or(std::vector<absl::string_view>());
    auto pos_neg_info_str_list =
      context.GetStringListCommonAttr("pos_neg_info_str_list").value_or(std::vector<absl::string_view>());
    // item input
    auto photo_id_accessor = context.GetIntItemAttr("photo_id");

    folly::F14FastMap<int64, absl::string_view> pid_info_map;
    if (pid_str_list.size() > 0 && pid_str_list.size() == pos_neg_info_str_list.size()) {
      for (int i = 0; i < pid_str_list.size(); ++i) {
        int64 pid = 0;
        if (absl::SimpleAtoi(pid_str_list[i], &pid)) {
          pid_info_map[pid] = pos_neg_info_str_list[i];
        }
      }
    }
    // item output
    auto pos_cnt_str_acessor = context.SetStringItemAttr("pos_cnt_str");
    auto neg_cnt_accessor = context.SetStringItemAttr("neg_cnt_str");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto photo_id = photo_id_accessor(result).value_or(0);
      std::string pos_cnt_str = "";
      std::string neg_cnt_str = "";
      if (pid_info_map.count(photo_id) > 0) {
        std::vector<std::string> info_str_list =
            absl::StrSplit(pid_info_map[photo_id], ",", absl::SkipWhitespace());
        if (info_str_list.size() == 2) {
          pos_cnt_str = info_str_list[0];
          neg_cnt_str = info_str_list[1];
        }
      }
      pos_cnt_str_acessor(result, pos_cnt_str);
      neg_cnt_accessor(result, neg_cnt_str);
    });
    return true;
  }


  static bool CalclauteRequestValueV0(const CommonRecoLightFunctionContext &context,
                                   RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    auto mc_vtr2_accessor = context.GetDoubleItemAttr("mc_vtr2");
    auto index = context.GetIntCommonAttr("nr_t_dynamic_request_value_top_k").value_or(0);
    double sum = 0;
    double sum_mc_vtr2 = 0;
    int i = 0;
    std::unordered_set<int> indexs{20, 50, 100, 150, 200, 250, 250, 300};
    std::map<int, int> index_request_values;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto mc_vtr2 = mc_vtr2_accessor(result).value_or(0);
      if (i < index) {
        sum_mc_vtr2 += mc_vtr2;
      }
      sum += mc_vtr2;
      if (indexs.find(i+1) != indexs.end()) {
          index_request_values.insert(std::pair<int, int>(i+1, sum));
      }
      ++i;
    });
    std::ostringstream oss;
    for (auto it = index_request_values.begin(); it != index_request_values.end(); ++it) {
        oss << it->first << ":" << static_cast<int>(std::ceil(it->second));
        if (std::next(it) != index_request_values.end()) {
            oss << ",";
        }
    }
    std::string request_values_str = oss.str();
    context.SetDoubleCommonAttr("request_value_v0", sum_mc_vtr2);
    context.SetStringCommonAttr("request_values", request_values_str);
    return true;
  }
  static std::unordered_set<int> GetDefaultQuotaScope(const CommonRecoLightFunctionContext &context) {
    std::vector<int64_t> kDefaultIndexesVector = {400, 450, 500, 550, 600, 650, 700,
                                                  750, 800, 850, 900, 950, 1000};
    absl::Span<const int64_t> default_indexes = absl::MakeSpan(kDefaultIndexesVector);
    auto index_span = context.GetIntListCommonAttr("dcaf_quota_list").value_or(default_indexes);
    std::unordered_set<int> indexs(index_span.begin(), index_span.end());
    return indexs;
  }
  static bool CalclauteRequestValuePositions(const CommonRecoLightFunctionContext &context,
                                   RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    double request_value_v0 = context.GetDoubleCommonAttr("request_value_v0").value_or(0);;
    auto variant_es_score_accessor = context.GetDoubleItemAttr("variant_es_score");
    double sum = 0;
    int i = 0;
    std::unordered_set<int> indexs = GetDefaultQuotaScope(context);
    std::map<int, double> index_request_values;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto variant_es_score = variant_es_score_accessor(result).value_or(0);
      sum += variant_es_score;
      if (indexs.find(i+1) != indexs.end()) {
          index_request_values.insert(std::pair<int, double>(i+1, sum));
      }
      ++i;
    });
    std::ostringstream oss;
    if (sum != 0) {
      for (auto it = index_request_values.begin(); it != index_request_values.end(); ++it) {
        oss << it->first << ":" << static_cast<double>(std::ceil((it->second/sum) * request_value_v0));
        if (std::next(it) != index_request_values.end()) {
            oss << ",";
        }
      }
    }
    std::string request_value_positions_str = oss.str();
    context.SetStringCommonAttr("request_value_positions", request_value_positions_str);
    return true;
  }
  static bool CalclauteRequestValuePositionsValueSort(const CommonRecoLightFunctionContext &context,
                                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto pvtr_accessor = context.GetDoubleItemAttr("pvtr");
    auto value_sort_accessor = context.GetDoubleItemAttr("value_sort");
    auto top_k_number = context.GetIntCommonAttr("nr_t_dynamic_request_value_top_k").value_or(0);

    std::unordered_set<int> indexs = GetDefaultQuotaScope(context);

    std::vector<double> pvtr_vector;
    std::vector<double> value_sort_vector;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double pvtr = pvtr_accessor(result).value_or(0);
      double value_sort = value_sort_accessor(result).value_or(0);
      pvtr_vector.push_back(pvtr);
      value_sort_vector.push_back(value_sort);
    });
    ks::platform::NrDCAF nr_dcaf(value_sort_vector, indexs);
    nr_dcaf.CalculatePositionValueByValueSort(pvtr_vector, top_k_number);
    std::string value_level_string = nr_dcaf.GenerateValueLevelString();
    std::string quota_top_k_value_string = nr_dcaf.GenerateTopkValueString();
    context.SetStringCommonAttr("request_value_topk_value_0", quota_top_k_value_string);
    context.SetStringCommonAttr("request_value_value_sort_positions", value_level_string);
    return true;
  }

  static bool EnrichTargetBadPhotoCustomHetuTagList(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    // common input
    auto left_thres_list = context.GetDoubleListCommonAttr("low_resolution_punish_left_thres_list");
    auto right_thres_list = context.GetDoubleListCommonAttr("low_resolution_punish_right_thres_list");
    // item input
    auto photo_vision_feature_accessor = context.GetDoubleListItemAttr("photo_vision_feature");
    auto hetu_tag_v2_level_one_accessor = context.GetIntItemAttr("hetu_tag_v2_level_one");
    auto sirius_distribution_mark_cod_list_accessor =
        context.GetIntListItemAttr("sirius_distribution_mark_cod_list");
    // item output
    auto is_low_resolution_accessor = context.SetIntItemAttr("is_low_resolution");
    auto is_one_cut_video_accessor = context.SetIntItemAttr("is_one_cut_video");
    auto hetu_tag_list_added_accessor = context.SetIntListItemAttr("hetu_tag_list_added");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      std::vector<int64> add_hetu_tags;
      auto photo_vision_feature = photo_vision_feature_accessor(result);
      auto hetu_tag_v2_level_one = hetu_tag_v2_level_one_accessor(result);
      auto sirius_distribution_mark_cod_list = sirius_distribution_mark_cod_list_accessor(result);
      std::unordered_set<int> mark_cod_set;
      if (sirius_distribution_mark_cod_list != absl::nullopt &&
          sirius_distribution_mark_cod_list.has_value() &&
          sirius_distribution_mark_cod_list->size() > 0) {
        mark_cod_set.insert(sirius_distribution_mark_cod_list->begin(),
              sirius_distribution_mark_cod_list->end());
      }
      // 特定劣质视频判断逻辑
      bool is_low_resolution = false;
      if (photo_vision_feature != absl::nullopt && photo_vision_feature.has_value() &&
          left_thres_list != absl::nullopt && left_thres_list.has_value() &&
          right_thres_list != absl::nullopt && right_thres_list.has_value() &&
          photo_vision_feature->size() == left_thres_list->size() &&
        photo_vision_feature->size() == right_thres_list->size()) {
        for (int i = 0; i < photo_vision_feature->size(); i++) {
          if (left_thres_list->at(i) <= photo_vision_feature->at(i) &&
            photo_vision_feature->at(i) <= right_thres_list->at(i)) {
            is_low_resolution = true;
            break;
          }
        }
      }

      bool is_one_cut_video = false;
      if (hetu_tag_v2_level_one == 39 && mark_cod_set.find(2306435) != mark_cod_set.end()) {
        is_one_cut_video = true;
      }

      // 填充自定义 hetu_tag, 与 kconf 配置保持一致
      if (is_low_resolution) {
        add_hetu_tags.push_back(20250801);
      }
      if (is_one_cut_video) {
        add_hetu_tags.push_back(20250802);
      }
      // 设置输出的 item attr
      is_low_resolution_accessor(result, is_low_resolution);
      is_one_cut_video_accessor(result, is_one_cut_video);
      hetu_tag_list_added_accessor(result, add_hetu_tags);
    });
    return true;
  }

  static bool EnrichTargetHetuTagItemThompsonScore(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    // common input
    auto tag_list = context.GetIntListCommonAttr("target_hetu_tag_list");
    auto threshold_list = context.GetDoubleListCommonAttr("target_filter_threshold_list");
    auto vv_limit_list = context.GetIntListCommonAttr("target_hetu_tag_filter_vv_limit_list");
    int64 vps_action_cnt = context.GetIntCommonAttr("vps_action_cnt").value_or(0);
    int64 report_limit =
      context.GetIntCommonAttr("nr_retr_target_bad_photo_thompson_neg_filter_report_limit").value_or(0);
    int64 report_weight =
      context.GetIntCommonAttr("nr_retr_target_bad_photo_thompson_neg_filter_report_score_punish_weight")
      .value_or(10);
    folly::F14FastMap<int64, double> hetu_tag_threshold_map;
    folly::F14FastMap<int64, int64> hetu_tag_vv_limit_map;
    if (tag_list != absl::nullopt && threshold_list != absl::nullopt && vv_limit_list != absl::nullopt &&
        tag_list.has_value() && threshold_list.has_value() && vv_limit_list.has_value() &&
        tag_list->size() > 0 && tag_list->size() == threshold_list->size() &&
        tag_list->size() == vv_limit_list->size()) {
      for (int i = 0; i < tag_list->size(); ++i) {
        hetu_tag_threshold_map[tag_list->at(i)] = threshold_list->at(i);
        hetu_tag_vv_limit_map[tag_list->at(i)] = vv_limit_list->at(i);
      }
    } else {
      CL_LOG_ERROR("EnrichTargetHetuTagItemThompsonScore", "invalid_filter_threshold_info");
    }
    // item input
    auto hetu_tag_list_accessor = context.GetIntListItemAttr("hetu_tag_list");
    auto hetu_tag_list_added_accessor = context.GetIntListItemAttr("hetu_tag_list_added");
    auto thanos_stats_like_count_accessor = context.GetIntItemAttr("thanos_stats_like_count");
    auto nebula_stats_like_count_accessor = context.GetIntItemAttr("nebula_stats_like_count");
    auto thanos_stats_follow_count_accessor = context.GetIntItemAttr("thanos_stats_follow_count");
    auto nebula_stats_follow_count_accessor = context.GetIntItemAttr("nebula_stats_follow_count");
    auto thanos_stats_negative_count_accessor = context.GetIntItemAttr("thanos_stats_negative_count");
    auto nebula_stats_negative_count_accessor = context.GetIntItemAttr("nebula_stats_negative_count");
    auto thanos_stats_report_count_accessor = context.GetIntItemAttr("thanos_stats_report_count");
    auto nebula_stats_report_count_accessor = context.GetIntItemAttr("nebula_stats_report_count");

    // item output
    auto is_filter_accessor = context.SetIntItemAttr("is_target_hetu_tag_photo_filter");
    auto neg_score_accessor = context.SetDoubleItemAttr("target_photo_tag_neg_score");
    auto filter_threshold_accessor = context.SetDoubleItemAttr("target_photo_tag_filter_threshold");
    auto target_filter_hetu_tag_accessor = context.SetIntItemAttr("target_filter_hetu_tag");
    auto target_hetu_tag_filter_vv_limit_accessor = context.SetIntItemAttr("target_hetu_tag_filter_vv_limit");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int64 pos_count = thanos_stats_like_count_accessor(result).value_or(0) +
                      nebula_stats_like_count_accessor(result).value_or(0) +
                      thanos_stats_follow_count_accessor(result).value_or(0) +
                      nebula_stats_follow_count_accessor(result).value_or(0);
      int64 report_count = thanos_stats_report_count_accessor(result).value_or(0) +
                         nebula_stats_report_count_accessor(result).value_or(0);
      int64 neg_count = thanos_stats_negative_count_accessor(result).value_or(0) +
                      nebula_stats_negative_count_accessor(result).value_or(0) +
                      report_weight * report_count;
      auto hetu_tag_list = hetu_tag_list_accessor(result);
      auto hetu_tag_list_added = hetu_tag_list_added_accessor(result);
      std::vector<int64> merged_item_hetu_tags;
      if (hetu_tag_list != absl::nullopt && hetu_tag_list.has_value() &&
          hetu_tag_list->size() > 0) {
        merged_item_hetu_tags.insert(merged_item_hetu_tags.end(),
          hetu_tag_list->begin(), hetu_tag_list->end());
      }
      if (hetu_tag_list_added != absl::nullopt && hetu_tag_list_added.has_value() &&
          hetu_tag_list_added->size() > 0) {
        merged_item_hetu_tags.insert(merged_item_hetu_tags.end(),
          hetu_tag_list_added->begin(), hetu_tag_list_added->end());
      }
      // 初始化，默认不过滤
      int is_filter = 0;
      double neg_score = 0.0;
      double filter_threshold = 1.0;
      int target_filter_hetu_tag = 0;
      int target_hetu_tag_filter_vv_limit = 0;
      if (merged_item_hetu_tags.size() > 0) {
        for (auto tag : merged_item_hetu_tags) {
          if (hetu_tag_threshold_map.count(tag) > 0 && hetu_tag_vv_limit_map.count(tag) > 0 &&
              vps_action_cnt < hetu_tag_vv_limit_map[tag]) {
            filter_threshold = hetu_tag_threshold_map[tag];
            target_filter_hetu_tag = tag;
            target_hetu_tag_filter_vv_limit = hetu_tag_vv_limit_map[tag];
            if (report_count > report_limit) {
              filter_threshold = 0.0;
              neg_score = 1.0;
              is_filter = 1;
            } else {
              boost::random::beta_distribution<> distribution(std::max(1L, neg_count),
                    std::max(1L, pos_count));
              thread_local boost::random::mt19937 rng(base::GetTimestamp());
              neg_score = distribution(rng);
              if (neg_score > filter_threshold) {
                is_filter = 1;
              }
            }
            if (is_filter == 1) {
              break;
            }
          }
        }
      }
      is_filter_accessor(result, is_filter);
      neg_score_accessor(result, neg_score);
      filter_threshold_accessor(result, filter_threshold);
      target_filter_hetu_tag_accessor(result, target_filter_hetu_tag);
      target_hetu_tag_filter_vv_limit_accessor(result, target_hetu_tag_filter_vv_limit);
    });
    return true;
  }

  static bool SolveLambda(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                          RecoResultConstIter end) {
    auto raw_datas = context.GetStringListCommonAttr("raw_position_value_data");
    auto cost_ratios = context.GetDoubleListCommonAttr("cost_ratios");
    std::vector<std::string> raw_data_vector;
    if (!cost_ratios.has_value() || cost_ratios->empty()) {
      return false;
    }
    if (raw_datas.has_value()) {
        for (const auto &data : *raw_datas) {
            raw_data_vector.emplace_back(data.data(), data.size());
        }
    } else {
        return false;
    }
    // 求 cost_ratio 的均值
    double sum = std::accumulate(cost_ratios.value().begin(), cost_ratios.value().end(), 0.0);
    double cost_ratio_mean = sum / static_cast<double>(cost_ratios->size());
    // 求 lambda
    ks::platform::LambdaSolver lambda_solver;
    auto all_req = lambda_solver.ParseContext(raw_data_vector);
    auto max_lambda = lambda_solver.GetMaxLambdaThreshold(all_req);
    double final_lambda = lambda_solver.SolveLambda(all_req, cost_ratio_mean, max_lambda);
    context.SetDoubleCommonAttr("final_lambda", final_lambda);
    LOG(INFO) << "cost_ratio_mean: " << cost_ratio_mean << ", max lambda: " << max_lambda
              << ", final lamdba: " << final_lambda;
    return true;
  }
  static bool CalclauteFinalQuotaV2(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    double lambda = context.GetDoubleCommonAttr("lambda").value_or(-1);
    double request_value_v0 = context.GetDoubleCommonAttr("request_value_v0").value_or(0);
    auto top_k_number = context.GetIntCommonAttr("nr_t_dynamic_request_value_top_k").value_or(0);
    int initial_quota = context.GetIntCommonAttr("nr_t_initial_quota").value_or(401);
    absl::string_view mode = context.GetStringCommonAttr("dcaf_calc_mode").value_or("mode_pvtr");
    std::string mode_str(mode.data(), mode.size());
    auto pvtr_accessor = context.GetDoubleItemAttr("pvtr");
    auto variant_es_score_accessor = context.GetDoubleItemAttr("variant_es_score");

    std::unordered_set<int> indexs = GetDefaultQuotaScope(context);

    if (lambda == -1) {
      context.SetIntCommonAttr("final_quota", 402);
      return false;
    }
    std::vector<double> es_score_vector;
    std::vector<double> pvtr_vector;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto variant_es_score = variant_es_score_accessor(result).value_or(0);
      auto pvtr = pvtr_accessor(result).value_or(0);
      es_score_vector.emplace_back(variant_es_score);
      pvtr_vector.emplace_back(pvtr);
    });
    int final_quota = initial_quota;
    if (mode_str == "sort_value") {
        ks::platform::NrDCAF nr_dcaf(es_score_vector, indexs);
        nr_dcaf.CalculatePositionValueByValueSort(pvtr_vector, top_k_number);
        final_quota = nr_dcaf.CalclauteFinalQuota(lambda, initial_quota);
    } else {
        ks::platform::NrDCAF nr_dcaf(es_score_vector, request_value_v0, indexs);
        nr_dcaf.CalculatePositionValue();
        final_quota = nr_dcaf.CalclauteFinalQuota(lambda, initial_quota);
    }
    context.SetIntCommonAttr("final_quota", final_quota);
    return true;
  }
};
}  // namespace platform
}  // namespace ks
