#pragma once

#include <algorithm>
#include <deque>
#include <string>
#include <vector>

#include "Eigen/Dense"
#include "base/hash_function/city.h"
#include "dragon/src/processor/base/common_reco_base_arranger.h"

namespace ks {
namespace platform {
namespace nr {

class NrSsdVariantArranger : public CommonRecoBaseArranger {
 public:
  NrSsdVariantArranger() {}

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  bool InitProcessor() override {
    item_embedding_attr_name_ = config()->GetString("item_embedding_attr", "");
    if (item_embedding_attr_name_.empty()) {
      LOG(ERROR) << "NrSsdVariantArranger init failed, item_embedding_attr empty.";
      return false;
    }

    ranking_score_attr_name_ = config()->GetString("ranking_score_attr", "");
    if (ranking_score_attr_name_.empty()) {
      LOG(ERROR) << "NrSsdVariantArranger init failed, ranking score empty.";
      return false;
    }

    cross_screen_items_from_attr_ = config()->GetString("cross_screen_items_from_attr", "");
    cross_screen_item_embedding_attr_name_ = config()->GetString("cross_screen_item_embedding_attr", "");
    return true;
  }

  bool PrepareItemsEmbedding(MutableRecoContextInterface *context,
                             RecoResultIter begin, RecoResultIter end,
                             std::vector<Eigen::VectorXf> *items_embedding,
                             Eigen::MatrixXf *items_emb_mat,
                             int total_size);

  bool PrepareCrossScreenItemsEmbedding(MutableRecoContextInterface *context,
                                        std::vector<Eigen::VectorXf> *negative_items_embedding);

  bool PrepareCrossScreenItemsEmbeddingV2(MutableRecoContextInterface *context,
                                        std::vector<Eigen::VectorXf> *negative_items_embedding,
                                        int embedding_dim);

  bool CrossScreenVariant(std::vector<Eigen::VectorXf> *items_embedding,
                          const std::vector<Eigen::VectorXf> &negative_items_embedding);

  void Ssd(std::vector<Eigen::VectorXf> *items_embedding,
           const Eigen::VectorXf &ranking_scores,
           float theta, int total_size, int limit,
           std::vector<bool> *selected_items_index,
           std::vector<int> *selected_items,
           bool stable_ssd,
           bool enable_simscore_scale,
           std::vector<float> *hyper_params);

  void SsdOpt(Eigen::MatrixXf *items_embedding,
              const Eigen::VectorXf &ranking_scores,
              float theta, int limit, bool stable_ssd,
              std::vector<int> *selected_items,
              std::vector<bool> *selected_items_index,
              bool enable_simscore_scale,
              std::vector<float> *hyper_params);


 private:
  // candidate items embedding attr
  std::string item_embedding_attr_name_;
  ItemAttr *item_embedding_attr_accessor_ = nullptr;

  // candidate items ranking score
  std::string ranking_score_attr_name_;
  ItemAttr *ranking_score_attr_accessor_ = nullptr;

  // cross screen items, common attr, optional
  std::string cross_screen_items_from_attr_;

  // cross screen items embedding attr, item attr, optional
  std::string cross_screen_item_embedding_attr_name_;
  ItemAttr *cross_screen_item_embedding_attr_accessor_ = nullptr;


  DISALLOW_COPY_AND_ASSIGN(NrSsdVariantArranger);
};

}  // namespace nr
}  // namespace platform
}  // namespace ks
