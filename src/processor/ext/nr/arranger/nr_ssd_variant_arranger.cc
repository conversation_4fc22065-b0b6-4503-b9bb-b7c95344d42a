#include "dragon/src/processor/ext/nr/arranger/nr_ssd_variant_arranger.h"

namespace ks {
namespace platform {
namespace nr {

RecoResultIter NrSsdVariantArranger::Arrange(MutableRecoContextInterface *context,
                                                    RecoResultIter begin, RecoResultIter end) {
  // step 1: preCheck
  int64 start_ts = base::GetTimestamp();
  const int total_size = std::distance(begin, end);
  if (total_size <= 0) {
    return end;
  }

  // ssd 要计算的结果个数,一般最小不能小于请求结果数
  int64 limit = GetIntProcessorParameter(context, "limit", 8);
  if (limit <= 0) {
    CL_LOG(WARNING) << "Ssd variant cancelled, invalid limit:" << limit;
    return end;
  }

  if (limit > total_size) {
    limit = total_size;
  }

  // theta 属于 (0, 1)
  double theta = GetDoubleProcessorParameter(context, "theta", 0.0);
  if (theta > 1 || theta < 0) {
    CL_LOG(WARNING) << "Ssd variant cancelled, parameter theta " << theta << " is invalid!";
    return end;
  }

  // load sim_scale_params_str
  bool enable_simscore_scale = GetBoolProcessorParameter(context, "enable_simscore_scale", false);
  std::string hyper_params_str = GetStringProcessorParameter(context, "sim_scale_params_str", "1,1,0.8,10");
  std::vector<float> hyper_params;
  if (enable_simscore_scale) {
    hyper_params.clear();
    std::vector<std::string> hyper_str =
        absl::StrSplit(hyper_params_str, absl::ByAnyChar(","), absl::SkipWhitespace());
    float hyper_param_val;
    for (std::string &elem : hyper_str) {
      if (absl::SimpleAtof(elem, &hyper_param_val)) {
        hyper_params.push_back(hyper_param_val);
      }
    }
  }
  CL_LOG(INFO) << "Ssd variant step1, get limit:" << limit << " & theta:" << theta << " & simscore_scale:"
               << enable_simscore_scale <<" & sim_scale_params_str:" << hyper_params_str <<  " success.";

  // load rank_score_norm_params_str
  bool enable_rank_score_norm = GetBoolProcessorParameter(context, "enable_rank_score_norm", false);
  std::string norm_params = GetStringProcessorParameter(context, "rank_score_norm_params_str", "1,1,0.2,10");
  std::vector<float> rank_score_norm_params;
  if (enable_rank_score_norm) {
    rank_score_norm_params.clear();
    std::vector<std::string> rank_score_str =
        absl::StrSplit(norm_params, absl::ByAnyChar(","), absl::SkipWhitespace());
    float rank_score_norm_val;
    for (std::string &elem : rank_score_str) {
      if (absl::SimpleAtof(elem, &rank_score_norm_val)) {
        rank_score_norm_params.push_back(rank_score_norm_val);
      }
    }
  }
  CL_LOG(INFO) << "Ssd variant step1, get limit:" << limit << " & theta:"
              << theta << " & enable_rank_score_norm:"
              << enable_rank_score_norm <<" & rank_score_norm_params_str:"
              << norm_params <<  " success.";

  // step 2: 生成 ranking_scores 向量
  Eigen::VectorXf ranking_scores;
  ranking_scores.resize(total_size);
  ranking_score_attr_accessor_ = context->GetItemAttrAccessor(ranking_score_attr_name_);
  for (int i = 0; i < total_size; i++) {
    auto item = (begin + i);
    auto ranking_score = item->GetDoubleAttr(ranking_score_attr_accessor_);
    if (ranking_score) {
      ranking_scores(i) = *ranking_score;
    } else {
      CL_LOG(WARNING) << "Ssd variant, get ranking_score attr failed!";
      return end;
    }
  }
  CL_LOG(INFO) << "Ssd variant step2, get ranking_score success.";
  // 这里使用 l2-norm, 论文里是减均值除方差
  bool l2_norm = GetBoolProcessorParameter(context, "l2_norm_ranking_score", true);
  if (enable_rank_score_norm) {
    // 使用 scale-norm
    if (rank_score_norm_params.size() == 4) {
      for (int i = 0; i < total_size; i++) {
        ranking_scores[i] = rank_score_norm_params[0] / (rank_score_norm_params[1] +
                    std::exp(-(ranking_scores[i] / (ranking_scores[0] + 1e-8) -
                    rank_score_norm_params[2]) * rank_score_norm_params[3]));
      }
    }
    CL_LOG(INFO) << "Ssd variant, rank_score_scale norm ranking_score success.";
  } else if (l2_norm) {
    // 默认使用 l2-norm
    ranking_scores.normalize();
    CL_LOG(INFO) << "Ssd variant, l2 norm ranking_score success.";
  }

  // Step 3: prepare items embedding
  std::vector<Eigen::VectorXf> items_embedding;
  Eigen::MatrixXf items_emb_mat;
  if (!PrepareItemsEmbedding(context, begin, end, &items_embedding, &items_emb_mat, total_size)) {
    CL_LOG(WARNING) << "Ssd variant, PrepareItemsEmbedding failed!";
    return end;
  }
  CL_LOG(INFO) << "Ssd variant step3, get items embedding success.";

  // cross screen variant if needed
  bool cross_screen_variant = GetBoolProcessorParameter(context, "cross_screen_variant", false);
  bool cross_screen_variant_v2 = GetBoolProcessorParameter(context, "cross_screen_variant_v2", false);
  int embedding_dim = GetIntProcessorParameter(context, "embedding_dim", 64);

  if (cross_screen_variant_v2) {
    if (!cross_screen_items_from_attr_.empty() && !cross_screen_item_embedding_attr_name_.empty()) {
      std::vector<Eigen::VectorXf> cross_screen_items_embedding;
      if (!PrepareCrossScreenItemsEmbeddingV2(context, &cross_screen_items_embedding, embedding_dim)) {
        return end;
      }
      if (!CrossScreenVariant(&items_embedding, cross_screen_items_embedding)) {
        return end;
      }
    } else {
      CL_LOG(WARNING) << "Ssd variant v2, cross_screen_items_from_attr_=" << cross_screen_items_from_attr_
                << ",cross_screen_item_embedding_attr_name_=" << cross_screen_item_embedding_attr_name_;
    }
  } else if (cross_screen_variant) {
    if (!cross_screen_items_from_attr_.empty() && !cross_screen_item_embedding_attr_name_.empty()) {
      std::vector<Eigen::VectorXf> cross_screen_items_embedding;
      if (!PrepareCrossScreenItemsEmbedding(context, &cross_screen_items_embedding)) {
        return end;
      }
      if (!CrossScreenVariant(&items_embedding, cross_screen_items_embedding)) {
        return end;
      }
    } else {
      CL_LOG(WARNING) << "Ssd variant, cross_screen_items_from_attr_=" << cross_screen_items_from_attr_
                << ",cross_screen_item_embedding_attr_name_=" << cross_screen_item_embedding_attr_name_;
    }
  }

  // Step 4: Ssd
  std::vector<bool> selected_items_index(total_size, false);
  std::vector<int> selected_items;
  bool stable_ssd = GetBoolProcessorParameter(context, "stable_ssd", true);
  bool optimized_ssd = GetBoolProcessorParameter(context, "optimized_ssd", false);
  if (optimized_ssd) {
    SsdOpt(&items_emb_mat, ranking_scores, theta, limit,
           stable_ssd, &selected_items, &selected_items_index,
           enable_simscore_scale, &hyper_params);
  } else {
    Ssd(&items_embedding, ranking_scores, theta, total_size, limit,
        &selected_items_index, &selected_items, stable_ssd,
        enable_simscore_scale, &hyper_params);
  }
  CL_LOG(INFO) << "Ssd variant step3, ssd variant success.";

  // Step 5: get final result
  std::vector<CommonRecoResult> final_results;
  // 先取 ssd 结果集
  for (int i = 0; i < selected_items.size(); i++) {
    auto item = begin + selected_items[i];
    final_results.push_back(*item);
  }
  // 取 Ssd 结果之外的 item 集合
  for (int index = 0; index < total_size; ++index) {
    if (selected_items_index[index]) {
      continue;
    }
    auto item = (begin + index);
    final_results.push_back(*item);
  }

  std::copy(final_results.begin(), final_results.end(), begin);
  int64 end_ts = base::GetTimestamp();
  CL_LOG(INFO) << "Ssd variant finished, cost " << (end_ts - start_ts) / 1000.0 << "ms.";
  return end;
}

bool NrSsdVariantArranger::PrepareItemsEmbedding(MutableRecoContextInterface *context,
                                                      RecoResultIter begin, RecoResultIter end,
                                                      std::vector<Eigen::VectorXf> *items_embedding,
                                                      Eigen::MatrixXf *items_emb_mat,
                                                      int total_size) {
  int64 start_ts = base::GetTimestamp();
  bool allow_empty_embedding = GetBoolProcessorParameter(context, "allow_empty_embedding", false);
  item_embedding_attr_accessor_ = context->GetItemAttrAccessor(item_embedding_attr_name_);
  srand((unsigned)time(NULL));
  int embedding_size = 0;
  // 初始化 item embedding 矩阵
  absl::optional<absl::Span<const double>> item_embedding;
  for (int i = 0; i < total_size; i++) {
    auto item = (begin + i);
    item_embedding = item->GetDoubleListAttr(item_embedding_attr_accessor_);
    if (i == 0) {
      // 第一个 item embedding 为空则不计算
      if (!item_embedding) {
        CL_LOG(WARNING) << "Ssd variant, the first item's embedding is empty!";
        return false;
      }
      embedding_size = item_embedding->size();
      items_emb_mat->resize(total_size, embedding_size + 1);
    }
    if (!item_embedding) {
      // 通常不允许 embedding 为空, 为了兼容个别 item 取不到 embedding 的情况增加容错机制
      if (allow_empty_embedding) {
        Eigen::VectorXf embedding;
        embedding.resize(embedding_size);
        embedding.setRandom();
        items_embedding->push_back(embedding);
        continue;
      } else {
        CL_LOG(WARNING) << "Ssd variant, get item_embedding failed!";
        return false;
      }
    }
    // NOTE! 这步看下是否可以通过一行来优化
    if (item_embedding->size() != embedding_size) {
      CL_LOG(WARNING) << "Ssd variant, get different item embedding size";
      return false;
    }
    Eigen::VectorXf embedding;
    embedding.resize(embedding_size);
    for (int j = 0; j < item_embedding->size(); j++) {
      embedding(j) = item_embedding->at(j);
    }
    // 归一化 & 解决余弦距离和体积不匹配问题
    embedding.normalize();
    embedding.conservativeResize(embedding_size + 1);
    embedding(embedding_size) = 1.0;
    items_embedding->push_back(embedding);
    items_emb_mat->row(i) = embedding;
  }
  int64 end_ts = base::GetTimestamp();
  CL_LOG(INFO) << "Ssd variant, PrepareItemsEmbedding cost " << (end_ts - start_ts) / 1000.0 << "ms.";
  return true;
}

bool NrSsdVariantArranger::PrepareCrossScreenItemsEmbedding(
                                    MutableRecoContextInterface *context,
                                    std::vector<Eigen::VectorXf> *cross_screen_items_embedding) {
  int64 start_ts = base::GetTimestamp();
  auto cross_screen_items = context->GetIntListCommonAttr(cross_screen_items_from_attr_);
  cross_screen_item_embedding_attr_accessor_ =
      context->GetItemAttrAccessor(cross_screen_item_embedding_attr_name_);
  int cross_screen_size = 0;
  if (cross_screen_items) {
    cross_screen_size = cross_screen_items->size();
  } else {
    CL_LOG(WARNING) << "Ssd variant, cross_screen_items is null!";
    return false;
  }
  if (cross_screen_size == 0) return false;
  int embedding_size = 0;
  if (cross_screen_items) {
    int i = 0;
    absl::optional<absl::Span<const double>> item_embedding;
    for (auto item_key : *cross_screen_items) {
      auto item = context->NewCommonRecoResult(item_key, -1);
      item_embedding = item.GetDoubleListAttr(cross_screen_item_embedding_attr_accessor_);
      if (!item_embedding) {
        CL_LOG(WARNING) << "Ssd variant, i=" << i << " item_key=" << item_key
                        << " get embedding failed!";
        return false;
      }
      if (i == 0) {
        embedding_size = item_embedding->size();
        CL_LOG(INFO) << "Ssd variant, item_embedding_size=" << embedding_size;
      }
      if (item_embedding->size() != embedding_size) {
        CL_LOG(WARNING) << "Ssd variant, get different item embedding size";
        return false;
      }
      Eigen::VectorXf embedding;
      embedding.resize(embedding_size);
      // NOTE! 这步看下是否可以通过一行来优化
      for (int j = 0; j < item_embedding->size(); j++) {
        embedding(j) = item_embedding->at(j);
      }
      embedding.normalize();
      embedding.conservativeResize(embedding_size + 1);
      embedding(embedding_size) = 1.0;
      cross_screen_items_embedding->push_back(embedding);
      i++;
    }
  }
  int64 end_ts = base::GetTimestamp();
  CL_LOG(INFO) << "Ssd variant, cross screen items_size=" << cross_screen_items_embedding->size()
               << ", embedding_size=" << embedding_size
               << ", cost " << (end_ts - start_ts) / 1000.0 << "ms.";
  return true;
}

bool NrSsdVariantArranger::PrepareCrossScreenItemsEmbeddingV2(
                                    MutableRecoContextInterface *context,
                                    std::vector<Eigen::VectorXf> *cross_screen_items_embedding,
                                    int embedding_dim) {
  int64 start_ts = base::GetTimestamp();
  auto cross_screen_items = context->GetIntListCommonAttr(cross_screen_items_from_attr_);
  if (cross_screen_items && cross_screen_items->size() > 0) {
    absl::optional<absl::Span<const double>> item_embedding;
    for (auto item_key : *cross_screen_items) {
      item_embedding = context->GetDoubleListItemAttr(
        item_key, cross_screen_item_embedding_attr_name_);
      if (!item_embedding || item_embedding->size() != embedding_dim) continue;
      Eigen::VectorXf embedding;
      embedding.resize(embedding_dim);
      for (int j = 0; j < item_embedding->size(); j++) {
        embedding(j) = item_embedding->at(j);
      }
      for (int i = 0; i < cross_screen_items_embedding->size(); i++) {
        auto &i_emb = cross_screen_items_embedding->at(i);
        float i_emb_dot = i_emb.dot(i_emb);
        float projection = abs(i_emb_dot) > 0.000001 ? embedding.dot(i_emb) / i_emb_dot : 0;
        embedding = embedding - projection * i_emb;
      }
      embedding.normalize();
      embedding.conservativeResize(embedding_dim + 1);
      embedding(embedding_dim) = 1.0;
      cross_screen_items_embedding->push_back(embedding);
    }
  } else {
    CL_LOG(WARNING) << "Ssd variant, cross_screen_items is null!";
    return false;
  }
  int64 end_ts = base::GetTimestamp();
  CL_LOG(INFO) << "Ssd variant v2, cross screen items_size=" << cross_screen_items_embedding->size()
               << ", embedding_dim=" << embedding_dim
               << ", cost " << (end_ts - start_ts) / 1000.0 << "ms.";
  return true;
}

bool NrSsdVariantArranger::CrossScreenVariant(
                                    std::vector<Eigen::VectorXf> *items_embedding,
                                    const std::vector<Eigen::VectorXf> &cross_screen_items_embedding) {
  int64 start_ts = base::GetTimestamp();
  for (int i = 0; i < cross_screen_items_embedding.size(); i++) {
    auto &i_emb = cross_screen_items_embedding.at(i);
    float i_emb_dot = i_emb.dot(i_emb);
    for (int j = 0; j < items_embedding->size(); j++) {
      Eigen::VectorXf &j_emb = items_embedding->at(j);
      // embedding size 匹配
      if (i_emb.size() != j_emb.size()) {
        CL_LOG(WARNING) << "Ssd variant, embedding size != candidate item embedding size";
        return false;
      }
      float projection = abs(i_emb_dot) > 0.000001 ? j_emb.dot(i_emb) / i_emb_dot : 0;
      items_embedding->at(j) = j_emb - projection * i_emb;
    }
  }
  int64 end_ts = base::GetTimestamp();
  CL_LOG(INFO) << "Cross screen variant cost " << (end_ts - start_ts) / 1000.0 << "ms.";
  return true;
}

void NrSsdVariantArranger::SsdOpt(Eigen::MatrixXf *items_embedding,
                                       const Eigen::VectorXf &ranking_scores,
                                       float theta, int limit, bool stable_ssd,
                                       std::vector<int> *selected_items,
                                       std::vector<bool> *selected_items_index,
                                       bool enable_simscore_scale, std::vector<float> *hyper_params) {
  int64 start_ts = base::GetTimestamp();
  int it;
  ranking_scores.maxCoeff(&it);
  float V = 1.0;
  if (it < selected_items_index->size() && it < items_embedding->rows()) {
    V = theta * items_embedding->row(it).norm();
    selected_items_index->at(it) = true;
    selected_items->push_back(it);
  } else {
    CL_LOG(WARNING) << "Ssd variant, first item it >= items_embedding.rows()!";
    return;
  }
  Eigen::VectorXf it_emb;
  while (selected_items->size() < limit) {
    it_emb = items_embedding->row(it);
    float it_emb_dot = it_emb.dot(it_emb);
    float max_score = -1.0;
    float cur_score = -1.0;
    Eigen::VectorXf j_emb;
    for (int j = 0; j < items_embedding->rows(); j++) {
      if (selected_items_index->at(j)) continue;
      j_emb = items_embedding->row(j);
      float projection = abs(it_emb_dot) > 0.000001 ? j_emb.dot(it_emb) / it_emb_dot : 0;
      if (enable_simscore_scale) {
        if (hyper_params->size() == 4 && projection > 0 && projection < hyper_params->at(2)) {
          projection = hyper_params->at(0) / (hyper_params->at(1) +
                        std::exp(-(projection - hyper_params->at(2)) * hyper_params->at(3)));
        }
      }
      items_embedding->row(j) = j_emb - projection * it_emb;
      float j_emb_normed = items_embedding->row(j).norm();
      if (stable_ssd) {
        cur_score = (1 - theta) * ranking_scores[j] + theta * j_emb_normed;
      } else {
        cur_score = (1 - theta) * ranking_scores[j] + theta * j_emb_normed * V;
      }
      if (max_score < cur_score) {
        max_score = cur_score;
        it = j;
      }
    }
    if (it < selected_items_index->size() && it < items_embedding->rows()) {
      V = items_embedding->row(it).norm() * V;
      selected_items_index->at(it) = true;
      selected_items->push_back(it);
    } else {
      CL_LOG(WARNING) << "Ssd variant, it >= selected_items_index.size() or it >= items_embedding.rows()!";
      return;
    }
    CL_LOG(INFO) << "Ssd variant, select " << selected_items->size() << "th item, index=" << it;
  }
  int64 end_ts = base::GetTimestamp();
  CL_LOG(INFO) << "Ssd variant, SsdOpt cost " << (end_ts - start_ts) / 1000.0 << "ms.";
}

inline size_t argmax(const Eigen::VectorXf &ranking_scores,
                     const std::vector<Eigen::VectorXf> &items_embedding,
                     const std::vector<bool> &selected_items_index,
                     const float &V, int total_size, float theta) {
  float max_score = -1.0;
  size_t it = 0;
  for (size_t i = 0; i < total_size; i++) {
    if (selected_items_index[i]) continue;
    float cur_score = (1 - theta) * ranking_scores[i] + items_embedding.at(i).norm() * V;
    if (max_score < cur_score) {
      max_score = cur_score;
      it = i;
    }
  }
  return it;
}

inline size_t stable_argmax(const Eigen::VectorXf &ranking_scores,
                            const std::vector<Eigen::VectorXf> &items_embedding,
                            const std::vector<bool> &selected_items_index,
                            float theta, int total_size) {
  float max_score = -1.0;
  size_t candidate_index = 0;
  for (size_t j = 0; j < total_size; j++) {
    if (selected_items_index[j]) continue;
    auto &j_emb = items_embedding.at(j);
    float cur_score = (1 - theta) * ranking_scores[j] + theta * j_emb.norm();
    // CL_LOG(INFO) << "Ssd variant, select item index j=" << j << ", theta=" << theta
    //              << ", ranking_score[" << j << "]=" << ranking_scores[j]
    //              << ", j_emb.norm=" << j_emb.norm()
    //              << ", max_score=" << max_score << ", cur_score=" << cur_score;
    if (max_score < cur_score) {
      max_score = cur_score;
      candidate_index = j;
    }
  }
  return candidate_index;
}

void NrSsdVariantArranger::Ssd(std::vector<Eigen::VectorXf> *items_embedding,
                                    const Eigen::VectorXf &ranking_scores,
                                    float theta, int total_size, int limit,
                                    std::vector<bool> *selected_items_index,
                                    std::vector<int> *selected_items, bool stable_ssd,
                                    bool enable_simscore_scale, std::vector<float> *hyper_params) {
  int64 start_ts = base::GetTimestamp();
  int it;
  ranking_scores.maxCoeff(&it);
  float V = 1.0;
  if (it < selected_items_index->size() && it < items_embedding->size()) {
    V = theta * items_embedding->at(it).norm();
    selected_items_index->at(it) = true;
    selected_items->push_back(it);
  } else {
    CL_LOG(WARNING) << "Ssd variant, first item it >= items_embedding.size()!";
    return;
  }
  while (selected_items->size() < limit && it < items_embedding->size()) {
    Eigen::VectorXf &it_emb = items_embedding->at(it);
    float it_emb_dot = it_emb.dot(it_emb);
    for (int j = 0; j < total_size; j++) {
      if (selected_items_index->at(j)) continue;
      Eigen::VectorXf &j_emb = items_embedding->at(j);
      float projection = abs(it_emb_dot) > 0.000001 ? j_emb.dot(it_emb) / it_emb_dot : 0;
      if (enable_simscore_scale) {
        if (hyper_params->size() == 4 && projection > 0 && projection < hyper_params->at(2)) {
          projection = hyper_params->at(0) / (hyper_params->at(1) +
                        std::exp(-(projection - hyper_params->at(2)) * hyper_params->at(3)));
        }
      }
      items_embedding->at(j) = j_emb - projection * it_emb;
    }
    if (stable_ssd) {
      it = stable_argmax(ranking_scores, *items_embedding, *selected_items_index, theta, total_size);
    } else {
      it = argmax(ranking_scores, *items_embedding, *selected_items_index, V, total_size, theta);
    }
    CL_LOG(INFO) << "Ssd variant, select ssd " << selected_items->size() << "th item, index=" << it;
    if (it < selected_items_index->size() && it < items_embedding->size()) {
      selected_items_index->at(it) = true;
      selected_items->push_back(it);
      V = items_embedding->at(it).norm() * V;
    } else {
      CL_LOG(WARNING) << "Ssd variant, it >= selected_items_index.size() or it >= items_embedding.size()!";
      return;
    }
  }
  int64 end_ts = base::GetTimestamp();
  CL_LOG(INFO) << "Ssd variant, SSD cost " << (end_ts - start_ts) / 1000.0 << "ms.";
}


typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, NrSsdVariantArranger, NrSsdVariantArranger)
}  // namespace nr
}  // namespace platform
}  // namespace ks
