#include "dragon/src/processor/ext/gsu/retriever/common_eshop_random_sleep_retriever.h"
#include "base/common/sleep.h"

namespace ks {
namespace platform {

void CommonEshopRandomSleepRetriever::Observe(ReadableRecoContextInterface *context,
        RecoResultConstIter begin, RecoResultConstIter end) {
  int max_sleep_ms = GetIntProcessorParameter(context, "sleep_ms", 0);
  if (max_sleep_ms > 0) {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    std::uniform_int_distribution<int> dis(0, max_sleep_ms);
    int random_sleep_ms = dis(gen);
    CL_LOG(INFO) << "Randomly sleeping for " << random_sleep_ms << " ms (max=" << max_sleep_ms << ")";
    base::SleepForMilliseconds(random_sleep_ms);
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonEshopRandomSleepRetriever, CommonEshopRandomSleepRetriever)

}  // namespace platform
}  // namespace ks
