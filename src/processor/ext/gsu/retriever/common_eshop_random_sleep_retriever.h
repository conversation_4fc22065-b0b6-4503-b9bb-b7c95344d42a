#pragma once

#include "dragon/src/processor/base/common_reco_base_observer.h"

namespace ks {
namespace platform {

class CommonEshopRandomSleepRetriever : public CommonRecoBaseObserver {
 public:
  CommonEshopRandomSleepRetriever() {}

  void Observe(ReadableRecoContextInterface *context, RecoResultConstIter begin,
               RecoResultConstIter end) override;

 private:
  DISALLOW_COPY_AND_ASSIGN(CommonEshopRandomSleepRetriever);
};

}  // namespace platform
}  // namespace ks
