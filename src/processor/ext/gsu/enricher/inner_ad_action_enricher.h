#pragma once

#include <map>
#include <memory>
#include <string>
#include <utility>
#include <vector>
#include "base/container/btree_set.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/processor/base/common_reco_base_processor.h"

namespace ks {
namespace platform {

class InnerAdActionEnricher : public CommonRecoBaseEnricher {
 public:
  InnerAdActionEnricher() {}
  ~InnerAdActionEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;

 private:
  int filt_type_ = 0;
  int filt_mask_ = 0;
  int list_limit_size_ = 1000;
  int action_threshold_ = 0;

  // 输入属性配置
  std::string request_time_attr_;
  std::string timestamp_list_attr_;
  std::string photo_id_list_attr_;
  std::string item_id_list_attr_;
  std::string channel_list_attr_;
  std::string author_id_list_attr_;
  std::string spu_id_list_attr_;
  std::string category_level1_list_attr_;
  std::string category_level2_list_attr_;
  std::string category_level3_list_attr_;
  std::string category_level4_list_attr_;
  std::string account_id_list_attr_;
  std::string label_list_attr_;

  // 输出属性配置
  std::string resp_timestamp_list_attr_;
  std::string resp_photo_id_list_attr_;
  std::string resp_item_id_list_attr_;
  std::string resp_channel_list_attr_;
  std::string resp_author_id_list_attr_;
  std::string resp_spu_id_list_attr_;
  std::string resp_category_level1_list_attr_;
  std::string resp_category_level2_list_attr_;
  std::string resp_category_level3_list_attr_;
  std::string resp_category_level4_list_attr_;
  std::string resp_account_id_list_attr_;

  DISALLOW_COPY_AND_ASSIGN(InnerAdActionEnricher);
};

}  // namespace platform
}  // namespace ks
