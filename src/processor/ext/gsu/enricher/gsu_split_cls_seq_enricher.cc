#include "dragon/src/processor/ext/gsu/enricher/gsu_split_cls_seq_enricher.h"

#include <algorithm>
#include <numeric>
#include <unordered_set>
#include <memory>

namespace ks {
namespace platform {

void GsuSplitClsSeqEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto colossus_photo_ids = context->GetIntListCommonAttr(colossus_photo_id_);
    auto colossus_author_ids = context->GetIntListCommonAttr(colossus_author_id_);

    auto colossus_play_times = context->GetIntListCommonAttr(colossus_play_time_);
    auto colossus_durations = context->GetIntListCommonAttr(colossus_duration_);

    auto colossus_timestamps = context->GetIntListCommonAttr(colossus_timestamp_);
    auto colossus_labels = context->GetIntListCommonAttr(colossus_label_);
    auto colossus_real_show_indexs = context->GetIntListCommonAttr(colossus_real_show_index_);
    auto colossus_tags = context->GetIntListCommonAttr(colossus_tag_);
    auto colossus_channels = context->GetIntListCommonAttr(colossus_channel_);

    if (!colossus_photo_ids) {
        LOG(ERROR) << "GsuSplitClsSeqEnricher has colossus_photo_ids nullptr";
        return;
    }
    if (!colossus_author_ids) {
        LOG(ERROR) << "GsuSplitClsSeqEnricher has colossus_author_ids nullptr";
        return;
    }
    if (!colossus_play_times) {
        LOG(ERROR) << "GsuSplitClsSeqEnricher has colossus_play_times nullptr";
        return;
    }
    if (!colossus_durations) {
        LOG(ERROR) << "GsuSplitClsSeqEnricher has colossus_durations nullptr";
        return;
    }

    int cls_photo_id_len = colossus_photo_ids->size();
    int cls_author_id_len = colossus_author_ids->size();
    int cls_play_time_len = colossus_play_times->size();
    int cls_duration_len = colossus_durations->size();
    int valid_len = cls_photo_id_len;
    valid_len = std::min(valid_len, cls_author_id_len);
    valid_len = std::min(valid_len, cls_play_time_len);
    valid_len = std::min(valid_len, cls_duration_len);

    std::vector<int64_t> photo_ids_vec(valid_len, -1);
    std::vector<int64_t> author_ids_vec(valid_len, -1);
    std::vector<int64_t> play_times_vec(valid_len, -1);
    std::vector<int64_t> durations_vec(valid_len, -1);

    std::vector<int64_t> timestamps_vec(valid_len, -1);
    std::vector<int64_t> labels_vec(valid_len, -1);
    std::vector<int64_t> rsis_vec(valid_len, -1);
    std::vector<int64_t> tags_vec(valid_len, -1);
    std::vector<int64_t> channels_vec(valid_len, -1);

    for (int i = 0; i < valid_len; ++i) {
        photo_ids_vec[i] = (*colossus_photo_ids)[i];
        author_ids_vec[i] = (*colossus_author_ids)[i];
        play_times_vec[i] = (*colossus_play_times)[i];
        durations_vec[i] = (*colossus_durations)[i];

        if (colossus_timestamps && colossus_timestamps->size() > i) {
            timestamps_vec[i] = (*colossus_timestamps)[i];
        }
        if (colossus_labels && colossus_labels->size() > i) {
            labels_vec[i] = (*colossus_labels)[i];
        }
        if (colossus_real_show_indexs && colossus_real_show_indexs->size() > i) {
            rsis_vec[i] = (*colossus_real_show_indexs)[i];
        }
        if (colossus_tags && colossus_tags->size() > i) {
            tags_vec[i] = (*colossus_tags)[i];
        }
        if (colossus_channels && colossus_channels->size() > i) {
            channels_vec[i] = (*colossus_channels)[i];
        }
    }
    if (sub_seq_max_len_ >= valid_len) {
        context->SetIntListCommonAttr(output_photo_id_, std::move(photo_ids_vec));
        context->SetIntListCommonAttr(output_author_id_, std::move(author_ids_vec));

        context->SetIntListCommonAttr(output_play_time_, std::move(play_times_vec));
        context->SetIntListCommonAttr(output_duration_, std::move(durations_vec));

        context->SetIntListCommonAttr(output_timestamp_, std::move(timestamps_vec));
        context->SetIntListCommonAttr(output_label_, std::move(labels_vec));
        context->SetIntListCommonAttr(output_real_show_index_, std::move(rsis_vec));
        context->SetIntListCommonAttr(output_tag_, std::move(tags_vec));
        context->SetIntListCommonAttr(output_channel_, std::move(channels_vec));
        return;
    }
    std::vector<int64_t> photo_ids_sub_seq;
    std::vector<int64_t> author_ids_sub_seq;
    std::vector<int64_t> play_times_sub_seq;
    std::vector<int64_t> durations_sub_seq;

    std::vector<int64_t> timestamps_sub_seq;
    std::vector<int64_t> labels_sub_seq;
    std::vector<int64_t> rsis_sub_seq;
    std::vector<int64_t> tags_sub_seq;
    std::vector<int64_t> channels_sub_seq;
    bool is_sucess = ProcessPhotoSequences(
        photo_ids_vec, author_ids_vec, play_times_vec, \
        durations_vec, timestamps_vec, labels_vec, \
        rsis_vec, tags_vec, channels_vec, \
        duration_bucket_num_, sub_seq_max_len_, \
        &photo_ids_sub_seq, &author_ids_sub_seq, &play_times_sub_seq, \
        &durations_sub_seq, &timestamps_sub_seq, &labels_sub_seq, \
        &rsis_sub_seq, &tags_sub_seq, &channels_sub_seq);
    if (!is_sucess) {
        LOG(ERROR) << "GsuSplitClsSeqEnricher split failed";
        return;
    }
    context->SetIntListCommonAttr(output_photo_id_, std::move(photo_ids_sub_seq));
    context->SetIntListCommonAttr(output_author_id_, std::move(author_ids_sub_seq));

    context->SetIntListCommonAttr(output_play_time_, std::move(play_times_sub_seq));
    context->SetIntListCommonAttr(output_duration_, std::move(durations_sub_seq));

    context->SetIntListCommonAttr(output_timestamp_, std::move(timestamps_sub_seq));
    context->SetIntListCommonAttr(output_label_, std::move(labels_sub_seq));
    context->SetIntListCommonAttr(output_real_show_index_, std::move(rsis_sub_seq));
    context->SetIntListCommonAttr(output_tag_, std::move(tags_sub_seq));
    context->SetIntListCommonAttr(output_channel_, std::move(channels_sub_seq));
    return;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, GsuSplitClsSeqEnricher, GsuSplitClsSeqEnricher)
}  // namespace platform
}  // namespace ks
