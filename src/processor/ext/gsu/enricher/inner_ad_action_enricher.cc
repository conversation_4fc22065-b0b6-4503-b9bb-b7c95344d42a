#include "dragon/src/processor/ext/gsu/enricher/inner_ad_action_enricher.h"

#include <memory>
#include <string>
#include <utility>
#include <vector>
#include <algorithm>

namespace ks {
namespace platform {

bool InnerAdActionEnricher::InitProcessor() {
  // 输入属性配置
  request_time_attr_ = config()->GetString("request_time_attr", "");
  timestamp_list_attr_ = config()->GetString("timestamp_list_attr", "");
  photo_id_list_attr_ = config()->GetString("photo_id_list_attr", "");
  item_id_list_attr_ = config()->GetString("item_id_list_attr", "");
  channel_list_attr_ = config()->GetString("channel_list_attr", "");
  author_id_list_attr_ = config()->GetString("author_id_list_attr", "");
  spu_id_list_attr_ = config()->GetString("spu_id_list_attr", "");
  category_level1_list_attr_ = config()->GetString("category_level1_list_attr", "");
  category_level2_list_attr_ = config()->GetString("category_level2_list_attr", "");
  category_level3_list_attr_ = config()->GetString("category_level3_list_attr", "");
  category_level4_list_attr_ = config()->GetString("category_level4_list_attr", "");
  account_id_list_attr_ = config()->GetString("account_id_list_attr", "");
  label_list_attr_ = config()->GetString("label_list_attr", "");

  // 输出属性配置
  resp_timestamp_list_attr_ = config()->GetString("resp_timestamp_list_attr", "");
  resp_photo_id_list_attr_ = config()->GetString("resp_photo_id_list_attr", "");
  resp_item_id_list_attr_ = config()->GetString("resp_item_id_list_attr", "");
  resp_channel_list_attr_ = config()->GetString("resp_channel_list_attr", "");
  resp_author_id_list_attr_ = config()->GetString("resp_author_id_list_attr", "");
  resp_spu_id_list_attr_ = config()->GetString("resp_spu_id_list_attr", "");
  resp_category_level1_list_attr_ = config()->GetString("resp_category_level1_list_attr", "");
  resp_category_level2_list_attr_ = config()->GetString("resp_category_level2_list_attr", "");
  resp_category_level3_list_attr_ = config()->GetString("resp_category_level3_list_attr", "");
  resp_category_level4_list_attr_ = config()->GetString("resp_category_level4_list_attr", "");
  resp_account_id_list_attr_ = config()->GetString("resp_account_id_list_attr", "");

  // 过滤参数
  filt_type_ = config()->GetInt("filt_type", 0);
  filt_mask_ = config()->GetInt("filt_mask", 0);
  if (filt_type_ < 0 || filt_type_ > 19) {
    CL_LOG(ERROR) << "invalid filt_type: " << filt_type_;
    return false;
  }
  if (filt_mask_ == 0) {
    filt_mask_ = 1 << filt_type_;
  }
  list_limit_size_ = config()->GetInt("list_limit_size", 1000);
  action_threshold_ = config()->GetInt("action_threshold", 0);

  return true;
}

void InnerAdActionEnricher::Enrich(MutableRecoContextInterface *context,
                                 RecoResultConstIter begin, RecoResultConstIter end) {
  if (context == nullptr) {
    CL_LOG(ERROR) << "context is null";
    return;
  }

  // 获取请求时间
  auto request_time = context->GetIntCommonAttr(request_time_attr_);
  if (!request_time) {
    CL_LOG(ERROR) << "miss attr:" << request_time_attr_;
    return;
  }

  // 获取输入序列属性
  auto timestamp_list = context->GetIntListCommonAttr(timestamp_list_attr_);
  if (!timestamp_list) {
    CL_LOG(ERROR) << "miss attr:" << timestamp_list_attr_;
    timestamp_list = absl::make_optional<std::vector<int64>>();
  }

  auto photo_id_list = context->GetIntListCommonAttr(photo_id_list_attr_);
  if (!photo_id_list) {
    CL_LOG(ERROR) << "miss attr:" << photo_id_list_attr_;
    photo_id_list = absl::make_optional<std::vector<int64>>();
  }

  auto item_id_list = context->GetIntListCommonAttr(item_id_list_attr_);
  if (!item_id_list) {
    CL_LOG(ERROR) << "miss attr:" << item_id_list_attr_;
    item_id_list = absl::make_optional<std::vector<int64>>();
  }

  auto channel_list = context->GetIntListCommonAttr(channel_list_attr_);
  if (!channel_list) {
    CL_LOG(ERROR) << "miss attr:" << channel_list_attr_;
    channel_list = absl::make_optional<std::vector<int64>>();
  }

  auto author_id_list = context->GetIntListCommonAttr(author_id_list_attr_);
  if (!author_id_list) {
    CL_LOG(ERROR) << "miss attr:" << author_id_list_attr_;
    author_id_list = absl::make_optional<std::vector<int64>>();
  }

  auto spu_id_list = context->GetIntListCommonAttr(spu_id_list_attr_);
  if (!spu_id_list) {
    CL_LOG(ERROR) << "miss attr:" << spu_id_list_attr_;
    spu_id_list = absl::make_optional<std::vector<int64>>();
  }

  auto category_level1_list = context->GetIntListCommonAttr(category_level1_list_attr_);
  if (!category_level1_list) {
    CL_LOG(ERROR) << "miss attr:" << category_level1_list_attr_;
    category_level1_list = absl::make_optional<std::vector<int64>>();
  }

  auto category_level2_list = context->GetIntListCommonAttr(category_level2_list_attr_);
  if (!category_level2_list) {
    CL_LOG(ERROR) << "miss attr:" << category_level2_list_attr_;
    category_level2_list = absl::make_optional<std::vector<int64>>();
  }

  auto category_level3_list = context->GetIntListCommonAttr(category_level3_list_attr_);
  if (!category_level3_list) {
    CL_LOG(ERROR) << "miss attr:" << category_level3_list_attr_;
    category_level3_list = absl::make_optional<std::vector<int64>>();
  }

  auto category_level4_list = context->GetIntListCommonAttr(category_level4_list_attr_);
  if (!category_level4_list) {
    CL_LOG(ERROR) << "miss attr:" << category_level4_list_attr_;
    category_level4_list = absl::make_optional<std::vector<int64>>();
  }

  auto account_id_list = context->GetIntListCommonAttr(account_id_list_attr_);
  if (!account_id_list) {
    CL_LOG(ERROR) << "miss attr:" << account_id_list_attr_;
    account_id_list = absl::make_optional<std::vector<int64>>();
  }

  auto label_list = context->GetIntListCommonAttr(label_list_attr_);
  if (!label_list) {
    CL_LOG(ERROR) << "miss attr:" << label_list_attr_;
    label_list = absl::make_optional<std::vector<int64>>();
  }

  if (photo_id_list->size() < 1) {
    CL_LOG(ERROR) << "size of photo_id_list is 0: " << photo_id_list_attr_;
    return;
  }

  CL_LOG(INFO) << "debug_info: action_threshold: " << action_threshold_
               << ", filt_type: " << filt_type_ << ", filt_mask: " << filt_mask_;

  // 初始化输出序列
  std::vector<int64> resp_timestamp_list;
  std::vector<int64> resp_photo_id_list;
  std::vector<int64> resp_item_id_list;
  std::vector<int64> resp_channel_list;
  std::vector<int64> resp_author_id_list;
  std::vector<int64> resp_spu_id_list;
  std::vector<int64> resp_category_level1_list;
  std::vector<int64> resp_category_level2_list;
  std::vector<int64> resp_category_level3_list;
  std::vector<int64> resp_category_level4_list;
  std::vector<int64> resp_account_id_list;

  int num = std::min(list_limit_size_, (int)photo_id_list->size());

  // 预分配内存
  resp_timestamp_list.reserve(num);
  resp_photo_id_list.reserve(num);
  resp_item_id_list.reserve(num);
  resp_channel_list.reserve(num);
  resp_author_id_list.reserve(num);
  resp_spu_id_list.reserve(num);
  resp_category_level1_list.reserve(num);
  resp_category_level2_list.reserve(num);
  resp_category_level3_list.reserve(num);
  resp_category_level4_list.reserve(num);
  resp_account_id_list.reserve(num);

  int count = 0;
  int len = photo_id_list->size();

  // 逆序遍历，从最新的行为开始
  for (int64 index = photo_id_list->size() - 1; index >= 0; index--) {
    // 基于 filt_mask 和 label 位进行过滤
    if ((len == label_list->size()) && ((label_list->at(index) & filt_mask_) != 0)) {
      // 将每个字段提取到输出序列
      if (len == timestamp_list->size()) {
        resp_timestamp_list.emplace_back(timestamp_list->at(index));
      }

      if (len == photo_id_list->size()) {
        resp_photo_id_list.emplace_back(photo_id_list->at(index));
      }

      if (len == item_id_list->size()) {
        resp_item_id_list.emplace_back(item_id_list->at(index));
      }

      if (len == channel_list->size()) {
        resp_channel_list.emplace_back(channel_list->at(index));
      }

      if (len == author_id_list->size()) {
        resp_author_id_list.emplace_back(author_id_list->at(index));
      }

      if (len == spu_id_list->size()) {
        resp_spu_id_list.emplace_back(spu_id_list->at(index));
      }

      if (len == category_level1_list->size()) {
        resp_category_level1_list.emplace_back(category_level1_list->at(index));
      }

      if (len == category_level2_list->size()) {
        resp_category_level2_list.emplace_back(category_level2_list->at(index));
      }

      if (len == category_level3_list->size()) {
        resp_category_level3_list.emplace_back(category_level3_list->at(index));
      }

      if (len == category_level4_list->size()) {
        resp_category_level4_list.emplace_back(category_level4_list->at(index));
      }

      if (len == account_id_list->size()) {
        resp_account_id_list.emplace_back(account_id_list->at(index));
      }

      count += 1;
      CL_LOG(INFO) << "debug_info: count: " << count;
      if (count >= list_limit_size_) {
        break;
      }
    }
  }

  CL_LOG(INFO) << "debug_info: resp_photo_id_list size: " << resp_photo_id_list.size();

  // 设置输出属性
  if (!resp_timestamp_list_attr_.empty()) {
    context->SetIntListCommonAttr(resp_timestamp_list_attr_, std::move(resp_timestamp_list));
  }

  if (!resp_photo_id_list_attr_.empty()) {
    context->SetIntListCommonAttr(resp_photo_id_list_attr_, std::move(resp_photo_id_list));
  }

  if (!resp_item_id_list_attr_.empty()) {
    context->SetIntListCommonAttr(resp_item_id_list_attr_, std::move(resp_item_id_list));
  }

  if (!resp_channel_list_attr_.empty()) {
    context->SetIntListCommonAttr(resp_channel_list_attr_, std::move(resp_channel_list));
  }

  if (!resp_author_id_list_attr_.empty()) {
    context->SetIntListCommonAttr(resp_author_id_list_attr_, std::move(resp_author_id_list));
  }

  if (!resp_spu_id_list_attr_.empty()) {
    context->SetIntListCommonAttr(resp_spu_id_list_attr_, std::move(resp_spu_id_list));
  }

  if (!resp_category_level1_list_attr_.empty()) {
    context->SetIntListCommonAttr(resp_category_level1_list_attr_, std::move(resp_category_level1_list));
  }

  if (!resp_category_level2_list_attr_.empty()) {
    context->SetIntListCommonAttr(resp_category_level2_list_attr_, std::move(resp_category_level2_list));
  }

  if (!resp_category_level3_list_attr_.empty()) {
    context->SetIntListCommonAttr(resp_category_level3_list_attr_, std::move(resp_category_level3_list));
  }

  if (!resp_category_level4_list_attr_.empty()) {
    context->SetIntListCommonAttr(resp_category_level4_list_attr_, std::move(resp_category_level4_list));
  }

  if (!resp_account_id_list_attr_.empty()) {
    context->SetIntListCommonAttr(resp_account_id_list_attr_, std::move(resp_account_id_list));
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, InnerAdActionEnricher, InnerAdActionEnricher);

}  // namespace platform
}  // namespace ks
