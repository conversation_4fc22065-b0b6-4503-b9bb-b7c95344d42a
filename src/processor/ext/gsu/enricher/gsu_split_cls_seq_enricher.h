#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>
#include <unordered_set>
#include <unordered_map>
#include <algorithm>
#include <cmath>

#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {
class GsuSplitClsSeqEnricher : public CommonRecoBaseEnricher {
 public:
    GsuSplitClsSeqEnricher() {}

    void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                RecoResultConstIter end) override;

 private:
    bool InitProcessor() override {
        colossus_play_time_ = config()->GetString("colossus_play_time", "");
        colossus_duration_ = config()->GetString("colossus_duration", "");
        if (colossus_play_time_.empty() || colossus_duration_.empty()) {
            LOG(ERROR) << "colossus_play_time or colossus_duration is empty";
            return false;
        }

        colossus_photo_id_ = config()->GetString("colossus_photo_id", "");
        colossus_author_id_ = config()->GetString("colossus_author_id", "");
        colossus_timestamp_ = config()->GetString("colossus_timestamp", "");
        colossus_label_ = config()->GetString("colossus_label", "");
        colossus_real_show_index_ = config()->GetString("colossus_real_show_index", "");
        colossus_tag_ = config()->GetString("colossus_tag", "");
        colossus_channel_ = config()->GetString("colossus_channel", "");

        output_play_time_ = config()->GetString("output_play_time", "");
        output_duration_ = config()->GetString("output_duration", "");

        output_photo_id_ = config()->GetString("output_photo_id", "");
        output_author_id_ = config()->GetString("output_author_id", "");
        output_timestamp_ = config()->GetString("output_timestamp", "");
        output_label_ = config()->GetString("output_label", "");
        output_real_show_index_ = config()->GetString("output_real_show_index", "");
        output_tag_ = config()->GetString("output_tag", "");
        output_channel_ = config()->GetString("output_channel", "");

        duration_bucket_num_ = config()->GetInt("duration_bucket_num", 0);
        if (duration_bucket_num_ <= 0) {
            LOG(ERROR) << "duration_bucket_num or playtime_bucket_num is 0";
            return false;
        }
        sub_seq_max_len_ = config()->GetInt("sub_seq_max_len", 0);
        if (sub_seq_max_len_ <= 0) {
            LOG(ERROR) << "sub_seq_max_len is 0";
            return false;
        }

        return true;
    }

    bool ProcessPhotoSequences(
        const std::vector<int64_t>& photo_ids_vec,
        const std::vector<int64_t>& author_ids_vec,
        const std::vector<int64_t>& play_times_vec,
        const std::vector<int64_t>& durations_vec,
        const std::vector<int64_t>& timestamps_vec,
        const std::vector<int64_t>& labels_vec,
        const std::vector<int64_t>& rsis_vec,
        const std::vector<int64_t>& tags_vec,
        const std::vector<int64_t>& channels_vec,
        int64_t duration_bucket_num,
        int64_t sub_seq_max_len,
        std::vector<int64_t>* photo_ids_sub_seq,
        std::vector<int64_t>* author_ids_sub_seq,
        std::vector<int64_t>* play_times_sub_seq,
        std::vector<int64_t>* durations_sub_seq,
        std::vector<int64_t>* timestamps_sub_seq,
        std::vector<int64_t>* labels_sub_seq,
        std::vector<int64_t>* rsis_sub_seq,
        std::vector<int64_t>* tags_sub_seq,
        std::vector<int64_t>* channels_sub_seq) {
        if (photo_ids_sub_seq == nullptr || author_ids_sub_seq == nullptr || \
            play_times_sub_seq == nullptr || durations_sub_seq == nullptr || \
            timestamps_sub_seq == nullptr || labels_sub_seq == nullptr || \
            rsis_sub_seq == nullptr || tags_sub_seq == nullptr || \
            channels_sub_seq == nullptr) {
            return false;
        }
        size_t n = photo_ids_vec.size();
        if (n == 0 || duration_bucket_num <= 0 || sub_seq_max_len <= 0) {
            return false;
        }
        if (author_ids_vec.size() != n || play_times_vec.size() != n || \
            durations_vec.size() != n || timestamps_vec.size() != n || \
            labels_vec.size() != n || rsis_vec.size() != n || \
            tags_vec.size() != n || channels_vec.size() != n) {
            return false;
        }
        std::vector<size_t> indices(n);
        for (size_t i = 0; i < n; ++i) {
            indices[i] = i;
        }
        std::sort(indices.begin(), indices.end(), [&](size_t a, size_t b) {
            return durations_vec[a] < durations_vec[b];
        });
        size_t bucket_size = n / duration_bucket_num;
        size_t remainder = n % duration_bucket_num;
        std::vector<std::vector<size_t>> buckets(duration_bucket_num);
        size_t current = 0;
        for (int64_t i = 0; i < duration_bucket_num; ++i) {
            size_t this_bucket_size = bucket_size + (i < remainder ? 1 : 0);
            buckets[i].resize(this_bucket_size);
            for (size_t j = 0; j < this_bucket_size; ++j) {
                if (current >= n) {
                    return false;
                }
                buckets[i][j] = indices[current++];
            }
        }
        for (auto& bucket : buckets) {
            std::sort(bucket.begin(), bucket.end(), [&](size_t a, size_t b) {
                return play_times_vec[a] > play_times_vec[b];
            });
        }
        size_t total_selected = 0;
        std::vector<size_t> bucket_indices(duration_bucket_num, 0);
        std::vector<size_t> bucket_counts(duration_bucket_num);
        for (int64_t i = 0; i < duration_bucket_num; ++i) {
            bucket_counts[i] = buckets[i].size();
        }
        while (total_selected < sub_seq_max_len) {
            bool selected = false;
            for (int64_t i = 0; i < duration_bucket_num; ++i) {
                if (bucket_indices[i] < bucket_counts[i]) {
                    size_t idx = buckets[i][bucket_indices[i]++];
                    if (idx >= n) {
                        return false;
                    }
                    photo_ids_sub_seq->push_back(photo_ids_vec[idx]);
                    author_ids_sub_seq->push_back(author_ids_vec[idx]);
                    play_times_sub_seq->push_back(play_times_vec[idx]);
                    durations_sub_seq->push_back(durations_vec[idx]);
                    timestamps_sub_seq->push_back(timestamps_vec[idx]);
                    labels_sub_seq->push_back(labels_vec[idx]);
                    rsis_sub_seq->push_back(rsis_vec[idx]);
                    tags_sub_seq->push_back(tags_vec[idx]);
                    channels_sub_seq->push_back(channels_vec[idx]);
                    total_selected++;
                    selected = true;
                    if (total_selected >= sub_seq_max_len) {
                        break;
                    }
                }
            }
            if (!selected) {
                break;
            }
        }
        return true;
    }

 private:
    std::string colossus_photo_id_ = "";
    std::string colossus_author_id_ = "";
    std::string colossus_play_time_ = "";
    std::string colossus_duration_ = "";
    std::string colossus_timestamp_ = "";
    std::string colossus_label_ = "";
    std::string colossus_real_show_index_ = "";
    std::string colossus_tag_ = "";
    std::string colossus_channel_ = "";

    std::string output_photo_id_ = "";
    std::string output_author_id_ = "";

    std::string output_play_time_ = "";
    std::string output_duration_ = "";

    std::string output_timestamp_ = "";
    std::string output_label_ = "";
    std::string output_real_show_index_ = "";
    std::string output_tag_ = "";
    std::string output_channel_ = "";

    int duration_bucket_num_ = 0;
    int sub_seq_max_len_ = 0;

    DISALLOW_COPY_AND_ASSIGN(GsuSplitClsSeqEnricher);
};

}  // namespace platform
}  // namespace ks
