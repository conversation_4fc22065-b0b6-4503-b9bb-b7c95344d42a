#include "dragon/src/processor/ext/ug_feature/enricher/ug_feature_output_features_enricher.h"

#include <algorithm>
#include <unordered_set>
#include "base/strings/string_split.h"

namespace ks {
namespace platform {

void UgFeatureOutputFeatureEnricher::Enrich(MutableRecoContextInterface *context,
                RecoResultConstIter begin, RecoResultConstIter end) {
    auto exclude_features_set = exclude_features_set_config_->Get();
    if (!exclude_features_set) {
      CL_LOG_ERROR_EVERY("build_output_feature", "build_output_feature_failed", 1000)
            << "exclude_features_config is not a object! kconf: "
            << "userGrowth.matrix.ugFeatureExcludeFeatures";
      return;
    }
    auto clotho_req_type_biz_config = clotho_req_type_biz_config_->Get();
    if (!clotho_req_type_biz_config || !clotho_req_type_biz_config->isObject()) {
      CL_LOG_ERROR_EVERY("build_output_feature", "build_clotho_req_type_failed", 1000)
            << "clotho request type to biz config is not a object! kconf: "
            << "userGrowth.matrix.ugFeatureClothoReqTypeConfig";
      return;
    }
    std::string req_type = context->GetRequest()->request_type();
    std::transform(req_type.begin(), req_type.end(), req_type.begin(), ::toupper);
    auto biz_name_by_req_type = clotho_req_type_biz_config->get(req_type, "").asString();
    auto feature_meta_config = feature_meta_config_->Get();
    if (!feature_meta_config || !feature_meta_config->isObject()) {
      CL_LOG_ERROR_EVERY("build_output_feature", "build_feature_meta_config", 1000)
            << "feature meta config is not a object! kconf: "
            << "userGrowth.matrix.growthFeatureMetaConfig";
      return;
    }

    auto clotho_column_name = biz_clotho_column_list_[biz_clotho_idx_][biz_name_by_req_type];
    if (clotho_column_name.empty()) {
      CL_LOG_ERROR_EVERY("build_output_feature", "feature_meta_config_mismatch", 1000)
            << "clotho column name not found in feature_meta_config! biz name: " << biz_name_by_req_type
            << "kconf: userGrowth.matrix.growthFeatureMetaConfig";
      return;
    }
    // kkv: <biz_name, <storage, features>>
    std::unordered_map<std::string,
      std::unordered_map<std::string, std::vector<std::string>>> biz_storage_feature_map;
    // clotho 要读的列 <biz, cloumns>
    std::unordered_map<std::string, std::set<std::string>> biz_clotho_columns;
    // feasury 要读的特征 <biz, feasury_attr_list>
    std::unordered_map<std::string, std::vector<std::string>> biz_feasury_attrs;
    std::set<std::string> biz_feasury_attr_set;
    GetFeasuryAttr(context, &biz_feasury_attr_set);
    // LOG(ERROR) << "biz_feasury_attr_set size: " << biz_feasury_attr_set.size();
    for (const auto &feature_name : context->GetRequest()->return_common_attrs()) {
      if (!feature_info_list_[feature_info_idx_].count(feature_name)) {
        if (exclude_features_set->find(feature_name) == exclude_features_set->end()) {
          CL_LOG_ERROR_EVERY("build_output_feature", "require_feature_not_found", 1000)
                << "require feature not found in config! feature: " << feature_name
                << " kconf: userGrowth.matrix.ugFeatureRouterInfo";
        }
        continue;
      }
      auto source = feature_info_list_[feature_info_idx_][feature_name].source;
      if (source == "feasury") {
        // LOG(ERROR) << "into feasury";
        if (!biz_feasury_attr_set.empty()) {
          if (biz_feasury_attr_set.find(feature_name) == biz_feasury_attr_set.end()) {
            CL_LOG_ERROR_EVERY("build_output_feature", "cannot_find_attr_in_feasury_config", 1000)
                    << "cannot find attr in feasury config";
            continue;
          } else {
            if (biz_feasury_attrs.find(biz_name_) == biz_feasury_attrs.end()) {
              biz_feasury_attrs.emplace(biz_name_, std::vector<std::string>{});
            }
            // LOG(ERROR) << "biz_feasury_attrs insert " << biz_name_ << ", " << feature_name;
            biz_feasury_attrs[biz_name_].emplace_back(feature_name);
          }
        }
      } else {
        std::string biz_name = "";
        if (!feature_info_list_[feature_info_idx_][feature_name].biz_list_set.count(biz_name_by_req_type)) {
          CL_LOG_ERROR_EVERY("build_output_feature", "feature_biz_list_mismatch", 1000)
                << "require type to biz not found in biz_list! feature: " << feature_name
                << "kconf: userGrowth.matrix.ugFeatureRouterInfo";
          continue;
        }
        biz_name = biz_name_by_req_type;
        if (!source.empty()) {
          if (biz_storage_feature_map.find(biz_name) == biz_storage_feature_map.end()) {
            biz_storage_feature_map.emplace(biz_name, std::unordered_map<std::string,
            std::vector<std::string>>{});
          }
          if (biz_storage_feature_map[biz_name].find(source) ==
              biz_storage_feature_map[biz_name].end()) {
            biz_storage_feature_map[biz_name].emplace(source, std::vector<std::string>{});
          }
          biz_storage_feature_map[biz_name][source].emplace_back(feature_name);
          if (source == "clotho" && !clotho_column_name.empty()) {
            if (biz_clotho_columns.find(biz_name) == biz_clotho_columns.end()) {
              biz_clotho_columns.emplace(biz_name, std::set<std::string>{});
            }
            biz_clotho_columns[biz_name].insert(clotho_column_name);
          }
        } else {
            CL_LOG_ERROR_EVERY("build_output_feature", "biz_name_or_source_null", 1000)
                << "do not have biz_name or source! kconf: "
                << "push.debug.clothoKeyDebug";
        }
      }
    }
    if (!SetOutputAttr(context, &biz_storage_feature_map, &biz_clotho_columns, &biz_feasury_attrs)) {
        CL_LOG_ERROR_EVERY("build_output_feature", "feature_set_attr_failed", 1000)
                << "feature set attr failed!";
    }
}

bool UgFeatureOutputFeatureEnricher::SetOutputAttr(
            MutableRecoContextInterface *context,
            std::unordered_map<std::string, std::unordered_map<std::string,
            std::vector<std::string>>>* biz_storage_feature_map,
            std::unordered_map<std::string, std::set<std::string>>* biz_clotho_columns,
            std::unordered_map<std::string, std::vector<std::string>>* biz_feasury_attrs) {
  auto biz_source_info = biz_source_config_->Get();
  if (!biz_source_info || !biz_source_info->isObject()) {
      CL_LOG_ERROR_EVERY("build_output_feature", "feature_set_attr_failed", 1000)
                << "feature set attr failed!";
      return false;
    }
  auto biz_name_list_config = biz_source_info->get("biz_name", ::Json::Value{});
  if (!biz_name_list_config.isArray() || biz_name_list_config.empty()) {
    CL_LOG_ERROR_EVERY("build_output_feature", "biz_name_config_not_array", 1000)
                << " output biz_name config is not array!";
    return false;
  }
  auto source_list_config = biz_source_info->get("source", ::Json::Value{});
  if (!source_list_config.isArray() || source_list_config.empty()) {
    CL_LOG_ERROR_EVERY("build_output_feature", "source_config_not_array", 1000)
                << "output source config is not array!";
    return false;
  }
  std::string output_features_name;
  std::string biz_name;
  std::string output_columns_name;
  std::string source;
  std::string output_feasury_name;
  for (auto biz_name_obj : biz_name_list_config) {
    biz_name = biz_name_obj.asString();
    output_columns_name = absl::StrCat(biz_name, "_clotho_columns");
    output_feasury_name = absl::StrCat(biz_name, "_feasury");
    // LOG(ERROR) << "name: " << biz_name << ", " << output_columns_name << ", " << output_feasury_name;
    for (auto source_obj : source_list_config) {
      source = source_obj.asString();
      if (biz_storage_feature_map->find(biz_name)
          != biz_storage_feature_map->end()
          && (*biz_storage_feature_map)[biz_name].find(source)
          != (*biz_storage_feature_map)[biz_name].end()) {
        output_features_name = absl::StrCat(biz_name, "_", source);
        // 执行加到 context 里面
        context->SetStringListCommonAttr(output_features_name,
            std::move((*biz_storage_feature_map)[biz_name][source]));
      } else {
        // CL_LOG_ERROR_EVERY("build_output_feature", "not_found_in_biz_storage_feature_map", 1000)
        //         << "biz_name: " << biz_name << " source: " << source
        //         << " not in biz_storage_feature_map";
      }
    }
    if (biz_clotho_columns->find(biz_name)
        != biz_clotho_columns->end()) {
      std::vector<std::string> tmpVector((*biz_clotho_columns)[biz_name].begin(),
        (*biz_clotho_columns)[biz_name].end());
      context->SetStringListCommonAttr(output_columns_name, std::move(tmpVector));
    } else {
      // CL_LOG_ERROR_EVERY("build_output_feature", "not_found_in_biz_clotho_columns", 1000)
      //         << "biz_name: " << biz_name << " not in biz_clotho_columns";
    }
    if (biz_feasury_attrs->find(biz_name)
        != biz_feasury_attrs->end()) {
      // LOG(ERROR) << "biz_feasury_attrs insert: " << output_feasury_name << ", " << biz_name
      //             << ", " << (*biz_feasury_attrs)[biz_name].size();
      context->SetStringListCommonAttr(output_feasury_name, std::move((*biz_feasury_attrs)[biz_name]));
    } else {
      // CL_LOG_ERROR_EVERY("build_output_feature", "not_found_in_biz_clotho_columns", 1000)
      //         << "biz_name: " << biz_name << " not in biz_clotho_columns";
    }
  }
  return true;
}

void UgFeatureOutputFeatureEnricher::GetFeasuryAttr(MutableRecoContextInterface *context,
                                                    std::set<std::string>* biz_feasury_attr_set) {
  auto feasury_req_type_biz_config = feasury_req_type_biz_config_->Get();
  if (!feasury_req_type_biz_config || !feasury_req_type_biz_config->isObject()) {
    CL_LOG_ERROR_EVERY("build_output_feature", "build_feasury_req_type_failed", 1000)
          << "feasury request type to biz config is not a object! kconf: "
          << "userGrowth.matrix.ugFeatureFeasuryReqTypeConfig";
    return;
  }
  std::string req_type = context->GetRequest()->request_type();
  std::transform(req_type.begin(), req_type.end(), req_type.begin(), ::toupper);
  auto req_type_config = feasury_req_type_biz_config->get(req_type, ::Json::Value{});
  auto feasury_biz = req_type_config.get("feasury_biz", "").asString();
  biz_name_ = req_type_config.get("biz", "").asString();
  if (!req_type_config.isObject() || feasury_biz.empty() || biz_name_.empty()) {
    CL_LOG_ERROR_EVERY("build_output_feature", "cannot_find_req_type_in_config", 1000)
                      << "cannot find feasury config";
    return;
  }
  std::string full_path = feasury_prefix_ + feasury_biz;
  if (feasury_biz_config_map_.find(feasury_biz) == feasury_biz_config_map_.end()) {
    Kconf kconf = ks::infra::KConf().Get(full_path, std::make_shared<Json::Value>());
    feasury_biz_config_map_.emplace(feasury_biz, kconf);
  }
  auto feasury_config = feasury_biz_config_map_[feasury_biz]->Get();
  if (feasury_config) {
    if (!FromJson(feasury_config, biz_feasury_attr_set)) {
      CL_LOG_ERROR_EVERY("build_output_feature", "cannot_parse_feasury_config", 1000)
                        << "cannot parse feasury config";
    }
  } else {
    CL_LOG_ERROR_EVERY("build_output_feature", "cannot_find_feasury_config", 1000)
                      << "cannot find feasury config";
  }
  // LOG(ERROR) << "biz_feasury_attr_set size: " << biz_feasury_attr_set->size();
  return;
}
bool UgFeatureOutputFeatureEnricher::FromJson(std::shared_ptr<Json::Value> json,
              std::set<std::string>* biz_feasury_attr_set) {
  if (!json->isMember("attrs")) {
    return false;
  }
  for (auto &attr_json : (*json)["attrs"]) {
    if (!attr_json.isString()) {
      return false;
    }
    // LOG(ERROR) << "include attr: " << attr_json.asString();
    biz_feasury_attr_set->insert(attr_json.asString());
  }
  return true;
}
typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, UgFeatureOutputFeatureEnricher, UgFeatureOutputFeatureEnricher)

}  // namespace platform
}  // namespace ks
