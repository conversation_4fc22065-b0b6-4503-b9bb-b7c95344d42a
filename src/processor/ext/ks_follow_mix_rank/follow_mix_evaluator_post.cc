#include "dragon/src/processor/ext/ks_follow_mix_rank/follow_mix_evaluator_post.h"

#include <algorithm>
#include <functional>

namespace ks {
namespace platform {

void FollowMixEvaluatorPostProcessor::FollowMixRank(AddibleRecoContextInterface *context) {
  /* 
    在前序阶段 evaluator 选出最优序列后，进行后处理工作
  */
  FollowMixRankContext *m_ctx = context->GetMutablePtrCommonAttr<FollowMixRankContext>(kMixRankCtxAttr);

  {
    FollowMixRankTimer timer("MixRankEvaluatorPostGetAbParams", context);
    hp_.GetScoreCommonHyperParams(context);
    if (m_ctx->is_single_nebula()) {
      //极速版超参数
      hp_.GetScoreNebulaHyperParams(context);
    } else if (m_ctx->is_main_inner()) {
      hp_.GetScoreMainInnerHyperParams(context);
    } else {
      hp_.GetScoreHyperParams(context);
      hp_.GetScoreMainHyperParams(context);
    }
    timer.Interval();
  }
  {
    FollowMixRankTimer timer("MixRankAppend", context);
    // evaluavtor 后剩余的结果拼到混排结果
    AppendRemainResults(context);
    if (hp_.enable_merchant_boost_in_mix_server_for_11_) {
      MerchantBoostFor11(context);
    }
    if (hp_.enable_boost_live_by_r_type_) {
      LiveBoostByRType(context);
    }
    timer.Interval();
  }
  {
    FollowMixRankTimer timer("MixRankReplaceWithTopGiftResults", context);
    // 用自定义的 gift 结果替换混排中的直播结果
    ReplaceWithTopGiftResults(context);
    timer.Interval();
  }
  {
    FollowMixRankTimer timer("MixRankAdsCostCalc", context);
    // 混排后根据位置变化调整计费
    AdsCostCalc(context);
    timer.Interval();
  }
  // ！！！ 从这里不要再改混排结果了 只能做一些日志或者填充信息的工作
  {
    FollowMixRankTimer timer("MixRanktEnrichMixResultInfos", context);
    // 混排后补充信息 回传 rpc 或者落盘日志
    EnrichMixResultInfos(context);
    timer.Interval();
  }
  {
    FollowMixRankTimer timer("MixRankUpdateHistoryList", context);
    UpdateHistoryList(context);
    timer.Interval();
  }
  {
    FollowMixRankTimer timer("MixRankLogMixResults", context);
    // 日志和 perf
    LogMixResults(context);
    timer.Interval();
  }
  // 混排结果 发送先知
  FollowMixRankLog::LogMixRankResult(m_ctx, ::ks::reco::FollowDebugStage::fMixSortMixResults,
    "FollowMixEvaluatorPostProcessor", m_ctx->MixResults());
  // todo 打点 和 debug
  LOG_EVERY_N(INFO, 1000) << "uid:" << std::to_string(m_ctx->user_id_) << ", did:" << m_ctx->device_id_
            << ", llsid:" << m_ctx->llsid_ << ", " << ", live_cand_list=" << m_ctx->LiveCandResults().Debug()
            << ", photo_cand_list=" << m_ctx->PhotoCandResults().Debug()
            << ", live_es_list=" << m_ctx->LiveResults().Debug()
            << ", photo_es_list=" << m_ctx->PhotoResults().Debug()
            << ", critic_list=" << m_ctx->CriticResults().Debug()
            << ", mix_list=" << m_ctx->MixResults().Debug();
}
void FollowMixEvaluatorPostProcessor::LogMixResults(AddibleRecoContextInterface *context) {
  FollowMixRankContext *m_ctx = context->GetMutablePtrCommonAttr<FollowMixRankContext>(kMixRankCtxAttr);

  // 打印最后混排产出的直播 item
  std::vector<std::shared_ptr<FollowMixRankResult>> final_live_items;
  for (size_t i = 0; i < m_ctx->MixResults().Size() && i < m_ctx->mix_page_size_; ++i) {
    auto item = m_ctx->MixResults().At(i);
    if (item && item->is_live_) {
      final_live_items.push_back(item);
    }
  }

  int live_cnt = 0;
  int photo_cnt = 0;
  int merchant_cnt = 0;
  int natural_cnt = 0;
  int ad_cnt = 0;
  int gift_cnt = 0;
  int boost_cnt = 0;
  int local_life_cnt = 0;
  int merchant_by_mix_cnt = 0;
  int ad_by_mix_cnt = 0;
  int natural_by_mix_cnt = 0;
  int gift_by_mix_cnt = 0;
  int boost_by_mix_cnt = 0;
  int log_feed_count = 0;
  int store_wide_by_mix_cnt = 0;
  int local_life_by_mix_cnt = 0;
  int merchant_photo_cart_cnt = 0;
  int merchant_photo_live_head_cnt = 0;
  int recruit_photo_cnt = 0;
  int recruit_live_cnt = 0;
  int ad_live_cnt = 0;
  int64 follow_watch_live_times_28d = 0;
  int64 follow_photo_play_time_28d = 0;
  m_ctx->follow_attrs_.GetSampleAttr("follow_watch_live_times_28d", &follow_watch_live_times_28d);
  m_ctx->follow_attrs_.GetSampleAttr("follow_photo_play_time_28d", &follow_photo_play_time_28d);
  int64 max_log_feed_count = context->GetIntCommonAttr("mixRankMaxLogCount").value_or(500);
  std::string perf_prefix = m_ctx->is_single_nebula() ?
    "nebula." : (m_ctx->is_main_inner() ? "mainInner." : "main.");
  int last_ad_pos = -1;
  int top1_live_cnt = 0, top2_live_cnt = 0, top4_live_cnt = 0, in_page_live_cnt = 0;
  auto ptr = context->GetPtrCommonAttr<folly::F14FastMap<uint64, std::string>>("gift_author_r_type");
  int high_ua_count = 0, boost_high_ua_count = 0;
  for (size_t i = 0; i < m_ctx->MixResults().Size(); i++) {
    auto item_ptr = m_ctx->MixResults().At(i);
    if (i < m_ctx->mix_page_size_) {
      if (item_ptr->is_live_) {
        if (0 == i) {
          top1_live_cnt++;
        }
        if (i <= 1) {
          top2_live_cnt++;
        }
        if (i <= 3) {
          top4_live_cnt++;
        }
        in_page_live_cnt++;
      }
      if (item_ptr->is_live_ && item_ptr->is_high_value_ua_) high_ua_count++;
      if (item_ptr->is_live_ && item_ptr->is_boost_ua_) boost_high_ua_count++;
      if (item_ptr->is_live_) live_cnt++;
      if (!item_ptr->is_live_) photo_cnt++;
      if (item_ptr->is_natural_) natural_cnt++;
      if (item_ptr->is_ad_) ad_cnt++;
      if (item_ptr->is_merchant_) merchant_cnt++;
      if (item_ptr->is_gift_) gift_cnt++;
      if (item_ptr->is_boost_) boost_cnt++;
      if (item_ptr->is_local_life_) local_life_cnt++;
      if (item_ptr->is_photo_with_cart_) merchant_photo_cart_cnt++;
      if (item_ptr != nullptr && item_ptr->is_recruit_photo_) recruit_photo_cnt++;
      if (item_ptr != nullptr && item_ptr->is_recruit_live_) recruit_live_cnt++;
      if (item_ptr->is_merchant_photo_live_head_) merchant_photo_live_head_cnt++;
      if (item_ptr->type == FollowMixRankResult::AD_DSP) {
        ad_by_mix_cnt++;
        if (item_ptr->is_live_) ad_live_cnt++;
        FollowMixPerf(context, 1e+4*item_ptr->ad_cpm, "mix_sort.page_ad_cpm");
        FollowMixPerf(context, 1e+4*item_ptr->ad_gpm, "mix_sort.page_ad_gpm");
        FollowMixPerf(context, 1e+4*item_ptr->ad_rank_benefit, "mix_sort.page_ad_rank_benefit");
        FollowMixPerf(context, 1, "mix_sort.page_ad_w_user_level", std::to_string(item_ptr->ad_w_user_level));
        FollowMixPerf(context, 1e+4*item_ptr->bonus_cpm_, "mix_sort.page_ad_bonus");
        if (last_ad_pos >= 0) {
          if (hp_.enable_adaptive_ad_gap_detail_) {
            FollowMixPerf(context, 1, "mix_sort.ad_gap_adaptive-" + std::to_string(i - last_ad_pos));
          } else {
            FollowMixPerf(context, i - last_ad_pos, "mix_sort.ad_gap");
          }
        }
        last_ad_pos = i;
      }
      if (item_ptr->type == FollowMixRankResult::NATRUAL) natural_by_mix_cnt++;
      if (item_ptr->type == FollowMixRankResult::MERCHANT) {
        merchant_by_mix_cnt++;
        FollowMixPerf(context, 1e+4*item_ptr->merchant_cvr_, "mix_sort.page_mer_cvr");
        FollowMixPerf(context, 1e+4*item_ptr->merchant_price_, "mix_sort.page_mer_price");
        FollowMixPerf(context, 1e+4*item_ptr->merchant_bonus_, "mix_sort.page_mer_bonus");
        FollowMixPerf(context, 1e+4*item_ptr->merchant_cpm_, "mix_sort.page_mer_cpm");
      }
      if (item_ptr->type == FollowMixRankResult::GIFT) {
        gift_by_mix_cnt++;
        FollowMixPerf(context, 1e+4*item_ptr->gift_cpm_, "mix_sort.page_gift_cpm");
        FollowMixPerf(context, 1e+4*item_ptr->gift_cpm_cali_, "mix_sort.page_gift_cpm_cali");
        FollowMixPerf(context, 1e+4*item_ptr->gift_bonus_, "mix_sort.page_gift_bonus");
        FollowMixPerf(context, 1e+4*item_ptr->gift_score_, "mix_sort.page_gift_score");
      }
      if (item_ptr->type == FollowMixRankResult::BOOST) {
        boost_by_mix_cnt++;
      }
      if (item_ptr->type == FollowMixRankResult::STORE_WIDE) {
        store_wide_by_mix_cnt++;
      }
      if (item_ptr->type == FollowMixRankResult::LOCAL_LIFE) {
        local_life_by_mix_cnt++;
        FollowMixPerf(context, 1e+4*item_ptr->local_life_cpm_, "mix_sort.page_local_life_cpm");
        // FollowMixPerf(context, 1e+4*item_ptr->gift_cpm_cali_, "mix_sort.page_gift_cpm_cali");
        FollowMixPerf(context, 1e+4*item_ptr->local_life_score_, "mix_sort.page_local_life_score");
      }
      if (item_ptr->is_live_) {
        if (ptr) {
          std::string r_type = "R-Unknow";
          auto author_id = item_ptr->author_id_;
          auto iter_r_type = ptr->find(author_id);
          if (iter_r_type != ptr->end()) {
            r_type = iter_r_type->second;
          }
          FollowMixPerf(context, 1, "mix_sort.r_type_in_page_count-"
            + hp_.live_boost_tmp_exp_tag_ + "-" + r_type);
        }
        FollowMixPerf(context, 1e+4*item_ptr->ctr_score_, "mix_sort.page_live_ctr");
      } else {
        FollowMixPerf(context, 1e+4*item_ptr->ctr_score_, "mix_sort.page_photo_ctr");
        FollowMixPerf(context, item_ptr->duration_ms_, "mix_sort.duration_ms");
      }
      if (item_ptr->is_high_merchant_ && item_ptr->merchant_index_ < 2 && item_ptr->merchant_index_ >= 0) {
        FollowMixPerf(context, 1, perf_prefix + "mix_sort.high_merchant_top2_win");
      }
      if (item_ptr->is_high_gift_ && item_ptr->gift_index_ < 2 && item_ptr->gift_index_ >= 0) {
        FollowMixPerf(context, 1, perf_prefix + "mix_sort.high_gift_top2_win");
      }
      if (item_ptr->is_high_ad_ && item_ptr->ad_index_ < 2 && item_ptr->ad_index_ >= 0) {
        FollowMixPerf(context, 1, perf_prefix + "mix_sort.high_ad_top2_win");
      }
      if (item_ptr->is_ad_) {
        if (item_ptr->ad_weight_ > 0) {
          FollowMixPerf(context, item_ptr->ad_weight_ * 1000, "mix_sort.top_page_result.ad_weight");
        } else {
          FollowMixPerf(context, 1, "mix_sort.top_page_result.ad_weight_zero");
        }
      }
      if (item_ptr->is_gift_) {
        if (item_ptr->gift_weight_ > 0) {
          FollowMixPerf(context, item_ptr->gift_weight_ * 1000, "mix_sort.top_page_result.gift_weight");
        } else {
          FollowMixPerf(context, 1, "mix_sort.top_page_result.gift_weight_zero");
        }
      }
      if (item_ptr->is_merchant_) {
        if (item_ptr->merchant_weight_ > 0) {
          FollowMixPerf(context, item_ptr->merchant_weight_ * 1000,
            "mix_sort.top_page_result.merchant_weight");
        } else {
          FollowMixPerf(context, 1, "mix_sort.top_page_result.merchant_weight_zero");
        }
      }
      if (item_ptr->is_local_life_) {
        if (item_ptr->local_life_weight_ > 0) {
          FollowMixPerf(context, item_ptr->local_life_weight_ * 1000,
            "mix_sort.top_page_result.local_life_weight");
        } else {
          FollowMixPerf(context, 1, "mix_sort.top_page_result.local_life_weight_zero");
        }
      }
      MixResultToFeedLog(context, item_ptr, i);
    } else {
      if (log_feed_count < max_log_feed_count) {
        if (item_ptr->is_live_) {
          MixResultToFeedLog(context, item_ptr, i);
          log_feed_count++;
        } else {
          if (hp_.enable_photo_send_mix_log_) {
            MixResultToFeedLog(context, item_ptr, i);
            log_feed_count++;
          }
        }
      }
      if (item_ptr->is_high_merchant_ && item_ptr->merchant_index_ < 2 && item_ptr->merchant_index_ >= 0) {
        FollowMixPerf(context, 1, perf_prefix + "mix_sort.high_merchant_top2_lose");
        if (i > 0 && hp_.enable_print_log_) {
          LOG(INFO) << perf_prefix << "user: " << m_ctx->user_id_
                    << ", llsid: " << m_ctx->llsid_
                    << ",merchant_lose_pre_info-" << m_ctx->MixResults().At(i-1)->ToString()
                    << ",merchant_lose_item_info-" << item_ptr->ToString();
        }
      }
      if (item_ptr->is_high_gift_ && item_ptr->gift_index_ < 2 && item_ptr->gift_index_ >= 0) {
        FollowMixPerf(context, 1, perf_prefix + "mix_sort.high_gift_top2_lose");
        if (i > 0 && hp_.enable_print_log_) {
          LOG(INFO) << perf_prefix << "user: " << m_ctx->user_id_
                    << ", llsid: " << m_ctx->llsid_
                    << ",gift_lose_pre_info-" << m_ctx->MixResults().At(i-1)->ToString()
                    << ",gift_lose_item_info-" << item_ptr->ToString();
        }
      }
      if (item_ptr->is_high_ad_ && item_ptr->ad_index_ < 2 && item_ptr->ad_index_ >= 0) {
        FollowMixPerf(context, 1, perf_prefix + "mix_sort.high_ad_top2_lose");
        if (i > 0 && hp_.enable_print_log_) {
          LOG(INFO) << perf_prefix << "user: " << m_ctx->user_id_
                    << ", llsid: " << m_ctx->llsid_
                    << ",ad_lose_pre_info-" << m_ctx->MixResults().At(i-1)->ToString()
                    << ",ad_lose_item_info-" << item_ptr->ToString();
        }
      }
    }
  }
  // 高价值/确定性 UA 监控
  bool enable_follow_mix_certain_ua_log =
    context->GetIntCommonAttr("enable_follow_mix_certain_ua_log").value_or(0);
  if (!m_ctx->is_single_nebula() && !m_ctx->is_main_inner() && enable_follow_mix_certain_ua_log) {
    CountMixUaPerf(context, m_ctx->MixResults(), "mixFinal", 12);
    CountMixUaPerf(context, m_ctx->MixResults(), "mixFinal", 4);
  }
  FollowMixPerf(context, high_ua_count, hp_.high_ua_perf_tag_ + ".mix_high_ua_count");
  FollowMixPerf(context, high_ua_count, hp_.high_ua_perf_tag_ + ".mix_boost_ua_");
  FollowMixPerf(context, live_cnt, "mix_sort.live_cnt");
  FollowMixPerf(context, photo_cnt, "mix_sort.photo_cnt");
  FollowMixPerf(context, ad_cnt, "mix_sort.ad_cnt");
  FollowMixPerf(context, merchant_cnt, "mix_sort.merchant_cnt");
  FollowMixPerf(context, natural_cnt, "mix_sort.natural_cnt");
  FollowMixPerf(context, gift_cnt, "mix_sort.gift_cnt");
  FollowMixPerf(context, local_life_cnt, "mix_sort.local_life_cnt" + hp_.local_life_bucket_);
  FollowMixPerf(context, ad_by_mix_cnt, "mix_sort.ad_by_mixsort_cnt");
  FollowMixPerf(context, ad_live_cnt, "mix_sort.ad_live_by_mixsort_cnt");
  FollowMixPerf(context, natural_by_mix_cnt, "mix_sort.natural_by_mixsort_cnt");
  FollowMixPerf(context, merchant_by_mix_cnt, "mix_sort.merchant_by_mixsort_cnt");
  FollowMixPerf(context, gift_by_mix_cnt, "mix_sort.gift_by_mixsort_cnt");
  FollowMixPerf(context, boost_by_mix_cnt, "mix_sort.boost_by_mix_cnt");
  FollowMixPerf(context, store_wide_by_mix_cnt, "mix_sort.store_wide_by_mix_cnt");
  FollowMixPerf(context, local_life_by_mix_cnt, "mix_sort.local_life_by_mixsort_cnt"
                + hp_.local_life_bucket_);
  FollowMixPerf(context, merchant_photo_cart_cnt, "mix_sort_new.merchant_cart_photo_cnt");
  FollowMixPerf(context, recruit_photo_cnt, "mix_sort_new.recruit_photo_cnt");
  FollowMixPerf(context, recruit_live_cnt, "mix_sort_new.recruit_live_cnt");
  FollowMixPerf(context, merchant_photo_live_head_cnt, "mix_sort_new.merchant_live_head_photo_cnt");
  FollowMixPerf(context, log_feed_count + m_ctx->mix_page_size_, "mix_sort.log_mix_feed_cnt");
  FollowMixPerf(context, top1_live_cnt, "mix_sort.top1_live_cnt_" + m_ctx->user_type_);
  FollowMixPerf(context, top2_live_cnt, "mix_sort.top2_live_cnt_" + m_ctx->user_type_);
  FollowMixPerf(context, top4_live_cnt, "mix_sort.top4_live_cnt_" + m_ctx->user_type_);
  FollowMixPerf(context, in_page_live_cnt, "mix_sort.in_page_live_cnt_" + m_ctx->user_type_);

  // 直播偏好人群监控
  if (follow_watch_live_times_28d > 5000) {
    if (follow_photo_play_time_28d > 10000) {
      FollowMixPerf(context, top1_live_cnt, "mix_sort.top1_live_cnt_double_prefer");
      FollowMixPerf(context, top4_live_cnt, "mix_sort.top4_live_cnt_double_prefer");
      FollowMixPerf(context, in_page_live_cnt, "mix_sort.in_page_live_cnt_double_prefer");
    } else {
      FollowMixPerf(context, top1_live_cnt, "mix_sort.top1_live_cnt_live_prefer");
      FollowMixPerf(context, top4_live_cnt, "mix_sort.top4_live_cnt_live_prefer");
      FollowMixPerf(context, in_page_live_cnt, "mix_sort.in_page_live_cnt_live_prefer");
    }
  } else {
    if (follow_photo_play_time_28d > 10000) {
      FollowMixPerf(context, top1_live_cnt, "mix_sort.top1_live_cnt_photo_prefer");
      FollowMixPerf(context, top4_live_cnt, "mix_sort.top4_live_cnt_photo_prefer");
      FollowMixPerf(context, in_page_live_cnt, "mix_sort.in_page_live_cnt_photo_prefer");
    } else {
      FollowMixPerf(context, top1_live_cnt, "mix_sort.top1_live_cnt_double_low");
      FollowMixPerf(context, top4_live_cnt, "mix_sort.top4_live_cnt_double_low");
      FollowMixPerf(context, in_page_live_cnt, "mix_sort.in_page_live_cnt_double_low");
    }
  }
  // 直播供给人群监控
  int live_candi_num = m_ctx->LiveResults().Size();
  FollowMixPerf(context, top1_live_cnt, "mix_sort.top1_live_cnt_livecandi" + std::to_string(live_candi_num));
  FollowMixPerf(context, top4_live_cnt, "mix_sort.top4_live_cnt_livecandi" + std::to_string(live_candi_num));
  FollowMixPerf(context, in_page_live_cnt,
    "mix_sort.in_page_live_cnt_livecandi" + std::to_string(live_candi_num));

  LOG(INFO) << "user: " << m_ctx->user_id_
            << ", llsid: " << m_ctx->llsid_
            << ", live_cnt: " << live_cnt
            << ", photo_cnt: " << photo_cnt
            << ", ad_cnt: " << ad_cnt
            << ", merchant_cnt: " << merchant_cnt
            << ", natural_cnt: " << natural_cnt
            << ", gift_cnt: " << gift_cnt
            << ", local_life_cnt: " << local_life_cnt
            << ", ad_by_mix_cnt: " << ad_by_mix_cnt
            << ", natural_by_mix_cnt: " << natural_by_mix_cnt
            << ", merchant_by_mix_cnt: " << merchant_by_mix_cnt
            << ", gift_by_mix_cnt: " << gift_by_mix_cnt
            << ", store_wide_by_mix_cnt: " << store_wide_by_mix_cnt
            << ", local_life_by_mix_cnt: " << local_life_by_mix_cnt
            << ", merchant_cart_cnt: " << merchant_photo_cart_cnt
            << ", recruit_photo_cnt: " << recruit_photo_cnt
            << ", recruit_live_cnt: " << recruit_live_cnt
            << ", merchant_photo_live_head_cnt: " << merchant_photo_live_head_cnt
            << ", mix_results_size: " << m_ctx->MixResults().Size();
  // todo 监控 xtr
  // todo 监控 在自然序上的变化
}
// uplift 打点函数
void FollowMixEvaluatorPostProcessor::UpdateUpLiftTags(::kuaishou::reco::FollowFeed* feed,
  std::shared_ptr<FollowMixRankResult> item) {
  if (feed == nullptr) {
    return;
  }
  feed->add_uplift_tags(item->is_generate_sequence ? 0: 10000);
  feed->add_uplift_tags(item->is_generate_sequence_uplift ? 1: 10000);
  feed->add_uplift_tags(item->is_greedy_sequence ? 2: 10000);
  feed->add_uplift_tags(item->is_greedy_sequence_uplift ? 3: 10000);
  feed->add_uplift_tags(item->is_generate_sequence_uplift_2 ? 4: 10000);
  feed->add_uplift_tags(item->is_greedy_sequence_uplift_2 ? 5: 10000);
  feed->add_uplift_tags(item->is_new_mix_rank ? 6: 10000);
  feed->add_uplift_tags(item->is_new_mix_rank_uplift ? 7: 10000);
  feed->add_uplift_tags(item->is_new_mix_rank_uplift_2 ? 8: 10000);
  feed->add_uplift_tags(item->mix_score_no_ad_int);
  feed->add_uplift_tags(item->next_feed_score_int);
  feed->add_uplift_tags(item->is_photo_link ? 1: 0);
  feed->add_uplift_tags(item->inner_advertiser_l_level);
  feed->add_uplift_tags(item->uplift_lt_eq);
}
// 商业化校准所需字段
void FollowMixEvaluatorPostProcessor::UpdateAdCalibrationIndex(::kuaishou::reco::FollowFeed* feed,
  std::shared_ptr<FollowMixRankResult> item) {
  if (feed == nullptr) {
    return;
  }
  feed->add_ad_calibration(item->ad_cpm);
  feed->add_ad_calibration(-1.0);
  feed->add_ad_calibration(item->auction_bid_init);
  feed->add_ad_calibration(-1.0);
  feed->add_ad_calibration(item->price_init);
  feed->add_ad_calibration(item->price_dis);
  feed->add_ad_calibration(item->price_dis_feed);
  feed->add_ad_calibration(item->price_dis_slide);
  feed->add_ad_calibration(item->price_sctr_ratio);
  feed->add_ad_calibration(item->price_adjust_ratio);
  feed->add_ad_calibration(item->ad_cpm_init);
  feed->add_ad_calibration(item->origin_price);
}
// 只对 page size 和 剩余的直播落盘日志 为了控制落盘的数据量
void FollowMixEvaluatorPostProcessor::MixResultToFeedLog(AddibleRecoContextInterface *context,
  std::shared_ptr<FollowMixRankResult> item, int index) {
  FollowMixRankContext *m_ctx = context->GetMutablePtrCommonAttr<FollowMixRankContext>(kMixRankCtxAttr);
  const kuaishou::reco::FollowMixRankRequest *raw_request = m_ctx->FollowMixRankRequest();
  ks::reco::RealTimeFollowRecoUserInfo& user_info =
    *((const_cast<kuaishou::reco::FollowMixRankRequest*>(raw_request))->mutable_follow_user_info());
  auto feed = m_ctx->follow_mix_log_.add_list();
  feed->set_in_page(index < m_ctx->mix_page_size_);
  feed->set_feed_id(item->id_);
  feed->set_reason(item->reason_);
  feed->set_filter_reason(kuaishou::reco::FollowFeed_FilterReason_KEEP_RESERVED);
  feed->set_position_in_nature(item->natural_index_);
  feed->set_author_id(item->author_id_);
  feed->set_ecpm(item->ad_cpm * 1e+6);  // 日志 pb 里定义为了整数 所以转换为原始的 ecpm 落盘
  feed->set_ad_gpm(item->ad_gpm * 1e+6);  // 同上
  feed->set_ad_rank_benefit(item->ad_rank_benefit * 1e+6);  // 同上
  feed->set_user_id(m_ctx->user_id_);
  feed->set_product_type(user_info.follow_page_product_type());
  if (item->with_leaf_ltr_score_) {
    feed->set_ext_info("with_ltr");
  } else {
    feed->set_ext_info("without_ltr");
  }
  uint64 llsid = 0;
  base::StringToUint64(m_ctx->llsid_, &llsid);
  feed->set_llsid(llsid);
  if (item->is_natural_) {
    feed->add_ecology_types(kuaishou::reco::FollowMixCandidateList_QueueName_NATURAL);
  }
  if (item->is_ad_) {
    feed->add_ecology_types(kuaishou::reco::FollowMixCandidateList_QueueName_AD_DSP);
  }
  if (item->is_merchant_) {
    feed->add_ecology_types(kuaishou::reco::FollowMixCandidateList_QueueName_MERCHANT);
  }
  if (item->is_gift_) {
    feed->add_ecology_types(kuaishou::reco::FollowMixCandidateList_QueueName_GIFT);
  }
  if (item->is_boost_) {
    feed->add_ecology_types(kuaishou::reco::FollowMixCandidateList_QueueName_BOOST);
  }
  if (item->is_store_wide_) {
    feed->add_ecology_types(kuaishou::reco::FollowMixCandidateList_QueueName_STORE_WIDE);
    if (hp_.color_ab_disable_store_wide_) {
      feed->set_ext_info("color");
    }
    if (hp_.color_ab_disable_store_wide_photo_ && !item->is_live_) {
      feed->set_ext_info("color");
    }
    if (hp_.color_ab_disable_store_wide_live_ && !item->is_live_) {
      feed->set_ext_info("color");
    }
  }
  if (item->is_merchant_photo_cart_) {
    feed->add_ecology_types(kuaishou::reco::FollowMixCandidateList_QueueName_MERCHANT_CART);
  }
  if (item->is_high_value_ua_ || item->is_boost_ua_ || item->is_boost_medium_) {
    std::string item_ext;
    ItemExtToJsonString(item, &item_ext);
    feed->set_ext_info(item_ext);
  }
  if (item->is_local_life_) {
    feed->add_ecology_types(kuaishou::reco::FollowMixCandidateList_QueueName_LOCAL_LIFE);
  }
  if (item->type == FollowMixRankResult::AD_DSP) {
    feed->set_mix_ecology_type(kuaishou::reco::FollowMixCandidateList_QueueName_AD_DSP);
  }
  if (item->type == FollowMixRankResult::NATRUAL) {
    feed->set_mix_ecology_type(kuaishou::reco::FollowMixCandidateList_QueueName_NATURAL);
  }
  if (item->type == FollowMixRankResult::MERCHANT) {
    feed->set_mix_ecology_type(kuaishou::reco::FollowMixCandidateList_QueueName_MERCHANT);
  }
  if (item->type == FollowMixRankResult::UNKNOWN) {
    feed->set_mix_ecology_type(kuaishou::reco::FollowMixCandidateList_QueueName_UNKNOWN);
  }
  if (item->type == FollowMixRankResult::GIFT) {
    feed->set_mix_ecology_type(kuaishou::reco::FollowMixCandidateList_QueueName_GIFT);
  }
  if (item->type == FollowMixRankResult::BOOST) {
    feed->set_mix_ecology_type(kuaishou::reco::FollowMixCandidateList_QueueName_BOOST);
  }
  if (item->type == FollowMixRankResult::STORE_WIDE) {
    if (hp_.color_ab_disable_store_wide_) {
      feed->set_mix_ecology_type(kuaishou::reco::FollowMixCandidateList_QueueName_NATURAL);
    } else if (hp_.color_ab_disable_store_wide_photo_ && !item->is_live_) {
      feed->set_mix_ecology_type(kuaishou::reco::FollowMixCandidateList_QueueName_NATURAL);
    } else if (hp_.color_ab_disable_store_wide_live_ && item->is_live_) {
      feed->set_mix_ecology_type(kuaishou::reco::FollowMixCandidateList_QueueName_NATURAL);
    } else {
      feed->set_mix_ecology_type(kuaishou::reco::FollowMixCandidateList_QueueName_STORE_WIDE);
      feed->set_is_whole_store_roi_ad(true);
      feed->set_is_whole_store_roi_ad_boost(item->is_boost_store_wide_);
    }
  }
  if (item->type == FollowMixRankResult::LOCAL_LIFE) {
    feed->set_mix_ecology_type(kuaishou::reco::FollowMixCandidateList_QueueName_LOCAL_LIFE);
    feed->set_is_local_life_whole_store_roi_ad_boost(item->is_boost_local_life_);
  }
  if (item->type == FollowMixRankResult::MERCHANT_CART) {
    feed->set_mix_ecology_type(kuaishou::reco::FollowMixCandidateList_QueueName_MERCHANT_CART);
  }
  if (item->type == FollowMixRankResult::RECRUIT_PHOTO) {
    feed->set_mix_ecology_type(kuaishou::reco::FollowMixCandidateList_QueueName_RECRUIT_PHOTO);
  }
  if (item->type == FollowMixRankResult::RECRUIT_LIVE) {
    feed->set_mix_ecology_type(kuaishou::reco::FollowMixCandidateList_QueueName_RECRUIT_LIVE);
  }
  feed->set_ad_boost_cpm_score(item->ad_boost_cpm_score_);
  feed->set_ad_auto_roi_score(item->ad_auto_roi_score_);
  feed->set_ad_roi_ratio_score(item->ad_roi_ratio_score_);
  feed->set_ad_trans_info(item->ad_trans_info_);
  feed->set_ad_gpm_score(item->ad_gpm_score_);
  feed->set_ad_creative_id(item->ad_creative_id_);
  feed->set_store_wide_cpm(item->store_wide_cpm_);
  feed->set_mix_index(index);
  feed->set_natural_score(item->natural_score_);
  feed->set_ad_score(item->ad_score_);
  feed->set_merchant_score(item->merchant_score_);
  feed->set_gift_score(item->gift_score_);
  feed->set_ctr(item->ctr_score_);
  feed->set_cvr(item->merchant_cvr_);
  feed->set_price(item->merchant_price_);
  feed->set_device_id(m_ctx->device_id_);
  feed->set_mix_exp_tag_id(GetExpTag(context));
  std::string exp_atg_candidate = std::string(context->GetStringCommonAttr(
                                  "follow_mix_exp_tag_candidate").value_or("default"));
  feed->set_exp_tag_candidate(exp_atg_candidate);
  for (int idx = 0; idx < item->mix_select_reason_.size(); idx++) {
    feed->add_mix_sort_reasons(item->mix_select_reason_[idx]);
  }
  feed->set_position_in_ad(item->ad_index_);
  feed->set_position_in_merchant(item->merchant_index_);
  feed->set_position_in_gift(item->gift_index_);
  feed->set_is_live(item->is_live_);
  feed->set_gtr(item->gtr_score_);
  feed->set_gift_price(item->gift_price_value_);
  feed->set_boost_score(item->boost_score_);
  feed->set_boost_reason(item->boost_type_);
  feed->set_gift_cpm(item->gift_cpm_);
  feed->set_gift_sequence_value(item->gift_sequence_value_);
  feed->set_merchant_cpm(item->merchant_cpm_);
  feed->set_is_live_bar_request(m_ctx->is_live_bar_request());
  feed->set_ltr_score(item->ltr_score_);
  feed->set_mix_score(item->mix_score_);
  feed->set_is_photo_with_cart(item->is_photo_with_cart_);
  feed->set_merchant_cart_score(item->merchant_photo_cart_score_);
  feed->set_merchant_cart_origin_gpm(item->merchant_photo_cart_score_);
  feed->set_merchant_live_head_origin_gpm(item->merchant_photo_live_head_score_);
  feed->set_is_merchant_live_head(item->is_merchant_photo_live_head_);
  feed->set_merchant_cart_ctr(item->merchant_cart_ctr_);
  feed->set_merchant_cart_cvr(item->merchant_cart_cvr_);
  feed->set_recruit_photo_score(item->recruit_photo_gpm_);
  feed->set_recruit_live_score(item->recruit_live_gpm_);
  feed->set_store_wide_score(item->store_wide_score_);
  feed->set_local_life_cpm(item->local_life_cpm_);
  feed->set_local_life_score(item->local_life_score_);
  feed->set_natural_weight(item->natural_weight_);
  feed->set_ad_weight(item->ad_weight_);
  feed->set_merchant_weight(item->merchant_weight_);
  feed->set_merchant_cart_weight(item->merchant_cart_weight_);
  feed->set_gift_weight(item->gift_weight_);
  feed->set_boost_weight(item->boost_weight_);
  feed->set_store_wide_weight(item->store_wide_weight_);
  feed->set_local_life_weight(item->local_life_weight_);
  feed->set_is_simple_live_entry(m_ctx->is_simple_live_entry());
  feed->set_cursor(m_ctx->cursor());
  feed->set_request_time(base::GetTimestamp());
  std::string sir_exp_flag = "merchant_live_" + hp_.merchant_live_sir_exp_tag_ + ",gift_live_" +
    hp_.gift_live_sir_exp_tag_ + ",ad_" + hp_.ad_sir_exp_tag_;
  feed->set_sir_exp_flag(sir_exp_flag);
  feed->set_gift_cpm_cali(item->gift_cpm_cali_);
  feed->set_merchant_cpm_cali(item->merchant_cpm_cali_);
  feed->set_ad_cpm_cali(item->ad_cpm_cali_);
  feed->set_ad_bonus(item->ad_bonus_);
  feed->set_gift_bonus(item->gift_bonus_);
  feed->set_gift_bonus_weight(hp_.gift_bonus_weight_);
  feed->set_merchant_bonus(item->merchant_bonus_);
  if (item->is_photo_with_cart_) {
    feed->set_merchant_bonus(item->merchant_photo_cart_bonus_);
  }
  feed->set_merchant_bonus_weight(hp_.merchant_bonus_weight_);
  feed->set_ue_score(item->ue_score_);
  feed->set_photo_store_wide_type(item->photo_store_wide_type_);
  feed->set_pxtr_detail(item->pxtr_detail_);
  feed->set_position_in_live(item->position_in_live_);
  feed->set_position_in_photo(item->position_in_photo_);
  feed->set_seq_ue_score(item->seq_ue_score_);
  UpdateUpLiftTags(feed, item);
  UpdateAdCalibrationIndex(feed, item);
  // debug critic 模型的输出值 每次只在第一个 item 上打印所有 code 以及对应 score
  if (0 == index && m_ctx->code_ue_score_.size() > 0) {
    for (int i  = 0; i < m_ctx->code_ue_score_.size(); i++) {
      feed->add_natural_feed_ids(m_ctx->code_ue_score_[i].first);
      feed->add_ad_feed_ids(m_ctx->code_ue_score_[i].second * 100000);
    }
  }
  std::string l2r_detail;
  L2RPxtrToJsonString(item, &l2r_detail);
  feed->set_l2r_detail(l2r_detail);
  std::string mix_pxtr_detail;
  MixPxtrToJsonString(m_ctx, item, &mix_pxtr_detail);
  feed->set_mix_pxtr_detail(mix_pxtr_detail);
  std::string merchant_mix_scores;
  MerchantMixScoresToJsonString(m_ctx, item, &merchant_mix_scores);
  feed->set_mix_merchant_scores(merchant_mix_scores);
  std::string user_type;
  UserTypeToJsonString(context, item, &user_type);
  feed->set_user_type(user_type);
  feed->set_merchant_exp_tag(hp_.merchant_pcoc_exp_tag_);
  feed->set_gift_ue_bonus(item->gift_ue_bonus_);
  feed->set_merchant_ue_bonus(item->merchant_ue_bonus_);
  if (index == 0) {
    feed->set_mix_score_stat(item->mix_score_stat);
  }
  if (!IsLiveItemWriteLog_ && item->is_live_) {
    feed->set_mix_score_stat(item->mix_score_stat);
    IsLiveItemWriteLog_ = true;
  }
  if (!IsPhotoItemWriteLog_ && !item->is_live_) {
    feed->set_mix_score_stat(item->mix_score_stat);
    IsPhotoItemWriteLog_ = true;
  }
  if (hp_.enable_stat_ad_input_ind_) {
    if (index == 0) {
      feed->set_mix_input_stat(item->mix_input_stat_);
    }
    if (!IsLiveItemWriteLogInput_ && item->is_live_) {
      feed->set_mix_input_stat(item->mix_input_stat_);
      IsLiveItemWriteLogInput_ = true;
    }
    if (!IsPhotoItemWriteLogInput_ && !item->is_live_) {
      feed->set_mix_input_stat(item->mix_input_stat_);
      IsPhotoItemWriteLogInput_ = true;
    }
  }
}
void FollowMixEvaluatorPostProcessor::ItemExtToJsonString(std::shared_ptr<FollowMixRankResult> item,
  std::string* res) {
  base::Json ext_info(base::StringToJson("{}"));
  ext_info.set("is_high_value_ua", item->is_high_value_ua_);
  ext_info.set("is_boost_ua", item->is_boost_ua_);
  ext_info.set("is_boost_medium", item->is_boost_medium_);
  ext_info.set("origin_ue_score", item->origin_ue_score_);
  ext_info.set("origin_gift_score", item->origin_gift_score_);
  ext_info.set("origin_merchant_score", item->origin_merchant_score_);
  ext_info.set("origin_mix_pctr", item->origin_mix_pctr_);
  ext_info.set("origin_mix_pwt", item->origin_mix_pwt_);
  *res = base::JsonToString(ext_info.get());
}
void FollowMixEvaluatorPostProcessor::L2RPxtrToJsonString(std::shared_ptr<FollowMixRankResult> item,
  std::string* res) {
  base::Json l2r_pxtr(base::StringToJson("{}"));
  l2r_pxtr.set("wtd_score", item->l2r_wtd_score_);
  l2r_pxtr.set("wtd_ori_score", item->l2r_wtd_ori_score_);
  l2r_pxtr.set("pctr_score", item->l2r_pctr_score_);
  l2r_pxtr.set("pltr_score", item->l2r_pltr_score_);
  l2r_pxtr.set("plvtr_score", item->l2r_plvtr_score_);
  l2r_pxtr.set("psvtr_score", item->l2r_psvtr_score_);
  *res = base::JsonToString(l2r_pxtr.get());
}
void FollowMixEvaluatorPostProcessor::MixPxtrToJsonString(FollowMixRankContext *m_ctx,
    std::shared_ptr<FollowMixRankResult> item, std::string* res) {
  base::Json mix_pxtr(base::StringToJson("{}"));
  auto point_iter = m_ctx->point_pxtr_.find(item->id_);
  double mix_pctr = 0.0, mix_plvtr = 0.0, mix_pwt = 0.0, mix_plivepair = 0.0,
    mix_pwtd = 0.0, mix_pfinish = 0.0, mix_pltr = 0.0, mix_pcmtr = 0.0, wtype = 0.0,
    mix_uescore = 0.0, mix_merchant_score = 0.0, mix_final_score = 0.0;
  if (point_iter != m_ctx->point_pxtr_.end()) {
    mix_pctr = point_iter->second.pctr;
    mix_plvtr = point_iter->second.plvtr;
    mix_pwt = point_iter->second.pwt;
    mix_pwtd = point_iter->second.pwtd;
    mix_pfinish = point_iter->second.pfinish;
    mix_pltr = point_iter->second.pltr;
    mix_pcmtr = point_iter->second.pcmtr;
    mix_plivepair = point_iter->second.plivepair;
    mix_uescore = point_iter->second.mix_uescore;
    mix_merchant_score = point_iter->second.mix_merchant_score;
    mix_final_score = point_iter->second.mix_final_score;
    wtype = point_iter->second.wtype;
  }
  mix_pxtr.set("mix_pctr", mix_pctr);
  mix_pxtr.set("mix_plvtr", mix_plvtr);
  mix_pxtr.set("mix_pwt", mix_pwt);
  mix_pxtr.set("mix_pwtd", mix_pwtd);
  mix_pxtr.set("mix_pfinish", mix_pfinish);
  mix_pxtr.set("mix_pltr", mix_pltr);
  mix_pxtr.set("mix_pcmtr", mix_pcmtr);
  // mix_pxtr.set("mix_plivepair", mix_plivepair);
  mix_pxtr.set("mix_uescore", mix_uescore);
  mix_pxtr.set("mix_merchant_score", mix_merchant_score);
  mix_pxtr.set("mix_final_score", mix_final_score);
  mix_pxtr.set("wtype", wtype);
  *res = base::JsonToString(mix_pxtr.get());
}
void FollowMixEvaluatorPostProcessor::MerchantMixScoresToJsonString(FollowMixRankContext *m_ctx,
  std::shared_ptr<FollowMixRankResult> item, std::string* res) {
base::Json merchant_mix_scores(base::StringToJson("{}"));
merchant_mix_scores.set("m_live", m_ctx->mix_merchant_live_score_map_.count(item->id_)
                                      ? std::round(m_ctx->mix_merchant_live_score_map_[item->id_] * 1e6) / 1e6
                                      : 0.0);
merchant_mix_scores.set("m_live_ctr",
                        m_ctx->mix_merchant_live_ctr_map_.count(item->id_)
                            ? std::round(m_ctx->mix_merchant_live_ctr_map_[item->id_] * 1e6) / 1e6
                            : 0.0);
merchant_mix_scores.set("m_live_cvr",
                        m_ctx->mix_merchant_live_cvr_map_.count(item->id_)
                            ? std::round(m_ctx->mix_merchant_live_cvr_map_[item->id_] * 1e6) / 1e6
                            : 0.0);
merchant_mix_scores.set("m_live_price",
                        m_ctx->mix_merchant_live_price_map_.count(item->id_)
                            ? std::round(m_ctx->mix_merchant_live_price_map_[item->id_] * 1e6) / 1e6
                            : 0.0);
merchant_mix_scores.set("m_cart", m_ctx->mix_merchant_cart_score_map_.count(item->id_)
                                      ? std::round(m_ctx->mix_merchant_cart_score_map_[item->id_] * 1e6) / 1e6
                                      : 0.0);
merchant_mix_scores.set("m_cart_cvr",
                        m_ctx->mix_merchant_cart_cvr_map_.count(item->id_)
                            ? std::round(m_ctx->mix_merchant_cart_cvr_map_[item->id_] * 1e6) / 1e6
                            : 0.0);
merchant_mix_scores.set("m_price",
                        m_ctx->mix_merchant_cart_price_map_.count(item->id_)
                            ? std::round(m_ctx->mix_merchant_cart_price_map_[item->id_] * 1e6) / 1e6
                            : 0.0);
merchant_mix_scores.set("m_livehead",
                        m_ctx->mix_merchant_livehead_score_map_.count(item->id_)
                            ? std::round(m_ctx->mix_merchant_livehead_score_map_[item->id_] * 1e6) / 1e6
                            : 0.0);
merchant_mix_scores.set("m_livehead_cvr",
                        m_ctx->mix_merchant_livehead_cvr_map_.count(item->id_)
                            ? std::round(m_ctx->mix_merchant_livehead_cvr_map_[item->id_] * 1e6) / 1e6
                            : 0.0);
merchant_mix_scores.set("m_livehead_price",
                        m_ctx->mix_merchant_livehead_price_map_.count(item->id_)
                            ? std::round(m_ctx->mix_merchant_livehead_price_map_[item->id_] * 1e6) / 1e6
                            : 0.0);
merchant_mix_scores.set("m_inner",
                        m_ctx->mix_merchant_inner_score_map_.count(item->id_)
                            ? std::round(m_ctx->mix_merchant_inner_score_map_[item->id_] * 1e6) / 1e6
                            : 0.0);
merchant_mix_scores.set("m_inner_cvr",
                        m_ctx->mix_merchant_inner_cvr_map_.count(item->id_)
                            ? std::round(m_ctx->mix_merchant_inner_cvr_map_[item->id_] * 1e6) / 1e6
                            : 0.0);
merchant_mix_scores.set("m_inner_price",
                        m_ctx->mix_merchant_inner_price_map_.count(item->id_)
                            ? std::round(m_ctx->mix_merchant_inner_price_map_[item->id_] * 1e6) / 1e6
                            : 0.0);
merchant_mix_scores.set("m_pctr", m_ctx->mix_merchant_pctr_score_map_.count(item->id_)
                                      ? std::round(m_ctx->mix_merchant_pctr_score_map_[item->id_] * 1e6) / 1e6
                                      : 0.0);
merchant_mix_scores.set("m_plvtr",
                        m_ctx->mix_merchant_plvtr_score_map_.count(item->id_)
                            ? std::round(m_ctx->mix_merchant_plvtr_score_map_[item->id_] * 1e6) / 1e6
                            : 0.0);
merchant_mix_scores.set("m_pwt", m_ctx->mix_merchant_pwt_score_map_.count(item->id_)
                                     ? std::round(m_ctx->mix_merchant_pwt_score_map_[item->id_] * 1e6) / 1e6
                                     : 0.0);
*res = base::JsonToString(merchant_mix_scores.get());
}
void FollowMixEvaluatorPostProcessor::UserTypeToJsonString(AddibleRecoContextInterface *context,
  std::shared_ptr<FollowMixRankResult> item, std::string* res) {
  FollowMixRankContext *m_ctx = context->GetMutablePtrCommonAttr<FollowMixRankContext>(kMixRankCtxAttr);
  base::Json type(base::StringToJson("{}"));
  type.set("user_type", m_ctx->user_type_);
  std::string merchant_user_type = "U0-null";
  m_ctx->follow_attrs_.GetSampleAttr("uBuyerEffectiveType", &merchant_user_type);
  type.set("buyer_type", merchant_user_type);
  int64 pay_value = 0;
  if (m_ctx->is_single_nebula()) {
    m_ctx->follow_attrs_.GetSampleAttr("uUserClassLivePayingTypeNebula", &pay_value);
  } else {
    m_ctx->follow_attrs_.GetSampleAttr("uUserClassLivePayingTypeMainApp", &pay_value);
  }
  type.set("pay_degree", std::to_string(pay_value));
  int64 is_big_g = 0, is_big_r = 0;
  m_ctx->follow_attrs_.GetSampleAttr("uIsBigRForFollowKV", &is_big_r);
  m_ctx->follow_attrs_.GetSampleAttr("uIsBigGForFollowKV", &is_big_g);
  if (is_big_g > 0) {
    type.set("bigG", 1);
  }
  if (is_big_r > 0) {
    type.set("bigR", 1);
  }
  auto ptr = context->GetPtrCommonAttr<folly::F14FastMap<uint64, std::string>>("gift_author_r_type");
  if (ptr) {
    auto author_id = item->author_id_;
    auto iter_r_type = ptr->find(author_id);
    if (iter_r_type != ptr->end()) {
      type.set("rType", iter_r_type->second);
    } else {
      type.set("rType", "R-Unknow");
    }
  }

  int64 follow_watch_live_times_28d = 0;
  int64 follow_photo_play_time_28d = 0;
  m_ctx->follow_attrs_.GetSampleAttr("follow_watch_live_times_28d", &follow_watch_live_times_28d);
  m_ctx->follow_attrs_.GetSampleAttr("follow_photo_play_time_28d", &follow_photo_play_time_28d);
  type.set("follow_watch_live_times_28d", follow_watch_live_times_28d);
  type.set("follow_photo_play_time_28d", follow_photo_play_time_28d);

  *res = base::JsonToString(type.get());
}
void FollowMixEvaluatorPostProcessor::UpdateHistoryList(AddibleRecoContextInterface *context) {
  FollowMixRankContext *m_ctx = context->GetMutablePtrCommonAttr<FollowMixRankContext>(kMixRankCtxAttr);
  auto& history_list = m_ctx->mix_rank_history_list_;
  auto slate = history_list.add_mix_rank_history_list();
  slate->set_user_id(m_ctx->user_id_);
  slate->set_llsid(m_ctx->llsid_);
  std::chrono::system_clock::time_point now = std::chrono::system_clock::now();
  std::chrono::milliseconds ms =
      std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch());
  slate->set_create_time(ms.count());
  slate->set_product_type(m_ctx->GetFollowpageProduct());
  slate->set_is_live_bar_request(m_ctx->is_live_bar_request());
  for (int i = 0; i < m_ctx->mix_page_size_ && i < m_ctx->MixResults().Size(); ++i) {
      auto item = slate->add_mix_feeds();
      auto res = m_ctx->MixResults().At(i);
      item->set_id(res->id_);
      item->set_author_id(res->author_id_);
      if (res->type == FollowMixRankResult::AD_DSP) {
        item->set_queue_name(kuaishou::reco::FollowMixCandidateList_QueueName_AD_DSP);
      } else if (res->type == FollowMixRankResult::MERCHANT) {
        item->set_queue_name(kuaishou::reco::FollowMixCandidateList_QueueName_MERCHANT);
      } else if (res->type == FollowMixRankResult::NATRUAL) {
        item->set_queue_name(kuaishou::reco::FollowMixCandidateList_QueueName_NATURAL);
      } else if (res->type == FollowMixRankResult::GIFT) {
        item->set_queue_name(kuaishou::reco::FollowMixCandidateList_QueueName_GIFT);
      } else if (res->type == FollowMixRankResult::BOOST) {
        item->set_queue_name(kuaishou::reco::FollowMixCandidateList_QueueName_BOOST);
      } else if (res->type == FollowMixRankResult::STORE_WIDE) {
        item->set_queue_name(kuaishou::reco::FollowMixCandidateList_QueueName_STORE_WIDE);
      } else if (res->type == FollowMixRankResult::LOCAL_LIFE) {
        item->set_queue_name(kuaishou::reco::FollowMixCandidateList_QueueName_LOCAL_LIFE);
      } else if (res->type == FollowMixRankResult::MERCHANT_CART) {
        item->set_queue_name(kuaishou::reco::FollowMixCandidateList_QueueName_MERCHANT_CART);
      } else if (res->type == FollowMixRankResult::RECRUIT_PHOTO) {
        item->set_queue_name(kuaishou::reco::FollowMixCandidateList_QueueName_RECRUIT_PHOTO);
      } else if (res->type == FollowMixRankResult::RECRUIT_LIVE) {
        item->set_queue_name(kuaishou::reco::FollowMixCandidateList_QueueName_RECRUIT_LIVE);
      } else {
        item->set_queue_name(kuaishou::reco::FollowMixCandidateList_QueueName_UNKNOWN);
      }
      if (res->is_live_) {
        item->set_type(kuaishou::reco::RecoEnum_ItemType_ITEM_TYPE_LIVESTREAM);
      } else {
        item->set_type(kuaishou::reco::RecoEnum_ItemType_ITEM_TYPE_PHOTO);
      }
      item->set_natural_index(res->natural_index_);
      item->set_mix_index(i);
      item->set_natural_score(res->natural_score_);
      item->set_merchant_score(res->merchant_score_);
      item->set_ad_score(res->ad_score_);
      item->set_gift_score(res->gift_score_);
      item->set_boost_score(res->boost_score_);
      item->set_local_life_score(res->local_life_score_);
  }
  FollowMixPerf(context, history_list.mix_rank_history_list_size(), "mix_sort.history.updated_history_size");
}
void FollowMixEvaluatorPostProcessor::EnrichMixResultInfos(AddibleRecoContextInterface *context) {
  FollowMixRankContext *m_ctx = context->GetMutablePtrCommonAttr<FollowMixRankContext>(kMixRankCtxAttr);
  for (size_t i = 0; i < m_ctx->MixResults().Size(); i++) {
    auto iter = m_ctx->MixResults().At(i);
    if (iter->type == FollowMixRankResult::AD_DSP) {
      iter->reason_ = iter->ad_reason_;
      kuiba::SampleAttr* ad_natural_pos_attr = iter->mix_result_attrs_.add_item_attr();
      ad_natural_pos_attr->set_name("ad_natural_pos");
      ad_natural_pos_attr->set_int_value(iter->natural_index_);
      ad_natural_pos_attr->set_type(kuiba::CommonSampleEnum::INT_ATTR);
      kuiba::SampleAttr* fix_pos_attr = iter->mix_result_attrs_.add_item_attr();
      fix_pos_attr->set_name("ad_fix_pos");
      fix_pos_attr->set_int_value(i);
      fix_pos_attr->set_type(kuiba::CommonSampleEnum::INT_ATTR);
      kuiba::SampleAttr* dsp_attr = iter->mix_result_attrs_.add_item_attr();
      dsp_attr->set_name("adDsp");
      std::string value;
      iter->dsp_info_.SerializeToString(&value);
      dsp_attr->set_string_value(value);
      dsp_attr->set_type(kuiba::CommonSampleEnum::STRING_ATTR);
    }
    if (iter->type == FollowMixRankResult::MERCHANT) {
      iter->reason_ = iter->merchant_reason_;
    }
    if (iter->type == FollowMixRankResult::GIFT) {
      iter->reason_ = iter->gift_reason_;
    }
    if (iter->type == FollowMixRankResult::BOOST) {
      iter->reason_ = iter->boost_reason_;
    }
    if (iter->type == FollowMixRankResult::LOCAL_LIFE) {
      iter->reason_ = iter->local_life_reason_;
    }
    kuiba::SampleAttr* type_attr = iter->mix_result_attrs_.add_item_attr();
    type_attr->set_name("ecologyType");
    type_attr->set_int_value(iter->type);
    type_attr->set_type(kuiba::CommonSampleEnum::INT_ATTR);
    kuiba::SampleAttr* item_attr = iter->mix_result_attrs_.add_item_attr();
    item_attr->set_name("NaturalScore");
    item_attr->set_float_value(iter->NaturalScore());
    item_attr->set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
    item_attr = iter->mix_result_attrs_.add_item_attr();
    item_attr->set_name("AdScore");
    item_attr->set_float_value(iter->AdScore());
    item_attr->set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
    item_attr = iter->mix_result_attrs_.add_item_attr();
    item_attr->set_name("MerchantScore");
    item_attr->set_float_value(iter->MerchantScore());
    item_attr->set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
    item_attr = iter->mix_result_attrs_.add_item_attr();
    item_attr->set_name("GiftScore");
    item_attr->set_float_value(iter->GiftScore());
    item_attr->set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
    item_attr = iter->mix_result_attrs_.add_item_attr();
    item_attr->set_name("BoostScore");
    item_attr->set_float_value(iter->BoostScore());
    item_attr->set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
    item_attr = iter->mix_result_attrs_.add_item_attr();
    item_attr->set_name("isBoostStoreWide");
    item_attr->set_int_value(iter->is_boost_store_wide_ ? 1 : 0);
    item_attr->set_type(kuiba::CommonSampleEnum::INT_ATTR);
    item_attr = iter->mix_result_attrs_.add_item_attr();
    item_attr->set_name("LocalLifeScore");
    item_attr->set_float_value(iter->LocalLifeScore());
    item_attr->set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
    item_attr = iter->mix_result_attrs_.add_item_attr();
    item_attr->set_name("MerchantCartScore");
    item_attr->set_float_value(iter->MerchantPhotoCartScore());
    item_attr->set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
    item_attr = iter->mix_result_attrs_.add_item_attr();
    item_attr->set_name("RecruitPhotoScore");
    item_attr->set_float_value(iter->RecruitPhotoScore());
    item_attr->set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
    item_attr = iter->mix_result_attrs_.add_item_attr();
    item_attr->set_name("RecruitLiveScore");
    item_attr->set_float_value(iter->RecruitLiveScore());
    item_attr->set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
    item_attr = iter->mix_result_attrs_.add_item_attr();
    item_attr->set_name("isBoostLocalLife");
    item_attr->set_int_value(iter->is_boost_local_life_ ? 1 : 0);
    item_attr->set_type(kuiba::CommonSampleEnum::INT_ATTR);
    auto point_iter = m_ctx->point_pxtr_.find(iter->id_);
    double mix_pctr = 0.0, mix_plvtr = 0.0, mix_pwt = 0.0,
      mix_pwtd = 0.0, mix_pfinish = 0.0, mix_pltr = 0.0, mix_pcmtr = 0.0,
      mix_uescore = 0.0, mix_final_score = 0.0;
    if (point_iter != m_ctx->point_pxtr_.end()) {
      mix_pctr = point_iter->second.pctr;
      mix_plvtr = point_iter->second.plvtr;
      mix_pwt = point_iter->second.pwt;
      mix_pwtd = point_iter->second.pwtd;
      mix_pfinish = point_iter->second.pfinish;
      mix_pltr = point_iter->second.pltr;
      mix_pcmtr = point_iter->second.pcmtr;
      mix_uescore = point_iter->second.mix_uescore;
      mix_final_score = point_iter->second.mix_final_score;
    }
    item_attr = iter->mix_result_attrs_.add_item_attr();
    item_attr->set_name("mix_pctr");
    item_attr->set_float_value(mix_pctr);
    item_attr->set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
    item_attr = iter->mix_result_attrs_.add_item_attr();
    item_attr->set_name("mix_plvtr");
    item_attr->set_float_value(mix_plvtr);
    item_attr->set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
    item_attr = iter->mix_result_attrs_.add_item_attr();
    item_attr->set_name("mix_pwt");
    item_attr->set_float_value(mix_pwt);
    item_attr->set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
    item_attr = iter->mix_result_attrs_.add_item_attr();
    item_attr->set_name("mix_pwtd");
    item_attr->set_float_value(mix_pwtd);
    item_attr->set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
    item_attr = iter->mix_result_attrs_.add_item_attr();
    item_attr->set_name("mix_pfinish");
    item_attr->set_float_value(mix_pfinish);
    item_attr->set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
    item_attr = iter->mix_result_attrs_.add_item_attr();
    item_attr->set_name("mix_pltr");
    item_attr->set_float_value(mix_pltr);
    item_attr->set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
    item_attr = iter->mix_result_attrs_.add_item_attr();
    item_attr->set_name("mix_pcmtr");
    item_attr->set_float_value(mix_pcmtr);
    item_attr->set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
    item_attr = iter->mix_result_attrs_.add_item_attr();
    item_attr->set_name("mix_uescore");
    item_attr->set_float_value(mix_uescore);
    item_attr->set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
    item_attr = iter->mix_result_attrs_.add_item_attr();
    item_attr->set_name("mix_final_score");
    item_attr->set_float_value(mix_final_score);
    item_attr->set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
    item_attr = iter->mix_result_attrs_.add_item_attr();
    item_attr->set_name("merchant_mix_scores");
    std::string mix_merchant_scores;
    MerchantMixScoresToJsonString(m_ctx, iter, &mix_merchant_scores);
    item_attr->set_string_value(mix_merchant_scores);
    item_attr->set_type(kuiba::CommonSampleEnum::STRING_ATTR);
    // 电商策略增量流量打标回传, stid  f 字段 uplift_pv_flag
    if (hp_.cs_enable_stid_merchant_uplift_pv_ && iter->merchant_uplift_pv_flag > 0) {
      kuiba::SampleAttr* merchant_strategy_attr = iter->f_stid_attrs_.add_item_attr();
      merchant_strategy_attr->set_name("uplift_pv_flag");
      merchant_strategy_attr->set_int_value(iter->merchant_uplift_pv_flag);
      merchant_strategy_attr->set_type(kuiba::CommonSampleEnum::INT_ATTR);
    }
  }
}
void FollowMixEvaluatorPostProcessor::AppendRemainResults(AddibleRecoContextInterface *context) {
  FollowMixRankContext *m_ctx = context->GetMutablePtrCommonAttr<FollowMixRankContext>(kMixRankCtxAttr);
  folly::F14FastSet<int> already_ids;
  m_ctx->Results().Clear();
  // 先取 critic 的结果
  for (size_t i = 0; i < m_ctx->CriticResults().Size(); ++i) {
    auto item = m_ctx->CriticResults().At(i);
    int64 item_id = item->Id();
    if (already_ids.count(item_id) <= 0) {
      already_ids.insert(item_id);
      m_ctx->Results().Append(item);
    }
  }
  // 再从 mix results 取其他结果 按照原来的序依次填充
  for (size_t i = 0; i < m_ctx->MixResults().Size(); ++i) {
    auto item = m_ctx->MixResults().At(i);
    int64 item_id = item->Id();
    if (already_ids.count(item_id) <= 0) {
      already_ids.insert(item_id);
      m_ctx->Results().Append(item);
    }
  }
  // 将序列写入到 m_ctx->MixResults()
  m_ctx->MixResults().Clear();
  for (size_t i = 0; i < m_ctx->Results().Size(); ++i) {
    m_ctx->MixResults().Append(m_ctx->Results().At(i));
  }
}
void FollowMixEvaluatorPostProcessor::MerchantBoostFor11(AddibleRecoContextInterface *context) {
  FollowMixRankContext *m_ctx = context->GetMutablePtrCommonAttr<FollowMixRankContext>(kMixRankCtxAttr);
  FollowMixPerf(context, 1, "mix_sort.MerchantBoostFor11.enable_merchant_boost");
  for (size_t i = 0; i < m_ctx->MixResults().Size(); ++i) {
    auto item = m_ctx->MixResults().At(i);
    item->all_feed_score_ = 1 / pow(1.1, i + 1);
    if (item->is_live_) {
      item->all_feed_score_ *= hp_.merchnat_boost_value_;
    }
  }
  m_ctx->MixResults().AllFeedScoreSort();
}
void FollowMixEvaluatorPostProcessor::GetTypeWeightMapByAbStrParam(const std::string &param,
  folly::F14FastMap<std::string, double>* weight_map) {
  if (param.size() <= 0) return;
  std::vector<std::string> param_type;
  base::SplitStringWithOptions(param, ";", true, true, &param_type);
  for (const auto &type_value_str : param_type) {
    std::vector<std::string> type_value;
    base::SplitStringWithOptions(type_value_str, ":", true, true, &type_value);
    if (type_value.size() != 2) continue;
    double value = 0.0;
    if (!absl::SimpleAtod(type_value[1], &value)) continue;
    weight_map->insert({type_value[0], value});
  }
}
void FollowMixEvaluatorPostProcessor::LiveBoostByRType(AddibleRecoContextInterface *context) {
  FollowMixRankContext *m_ctx = context->GetMutablePtrCommonAttr<FollowMixRankContext>(kMixRankCtxAttr);
  FollowMixPerf(context, 1, "mix_sort.LiveBoostByRType.enable_live_boost");
  auto ptr = context->GetPtrCommonAttr<folly::F14FastMap<uint64, std::string>>("gift_author_r_type");
  if (ptr == nullptr || ptr->size() == 0) {
    FollowMixPerf(context, 1, "mix_sort.LiveBoostByRType.r_type_empty");
    return;
  }
  folly::F14FastMap<std::string, double> live_author_r_type_weight;
  GetTypeWeightMapByAbStrParam(hp_.live_author_r_type_boost_param_, &live_author_r_type_weight);
  folly::F14FastMap<std::string, int> boost_count_map;
  for (size_t i = 0; i < m_ctx->MixResults().Size(); ++i) {
    auto item = m_ctx->MixResults().At(i);
    item->all_feed_score_ = 1 / pow(1.1, i + 1);
    auto author_id = item->author_id_;
    auto iter_r_type = ptr->find(author_id);
    std::string r_type = "R-Unknow";
    if (iter_r_type != ptr->end()) {
      r_type = iter_r_type->second;
    }
    double boost_weight = 1.0;
    auto weight_iter = live_author_r_type_weight.find(r_type);
    if (weight_iter != live_author_r_type_weight.end()) {
      boost_weight = weight_iter->second;
    }
    if (item->is_live_) {
      item->all_feed_score_ *= boost_weight;
      if (boost_weight > 1.0) {
        boost_count_map[r_type] += 1;
      }
    }
  }
  for (auto map_iter : boost_count_map) {
    FollowMixPerf(context, map_iter.second, "mix_sort.LiveBoostByRType.boost_type_" + map_iter.first);
  }
  m_ctx->MixResults().AllFeedScoreSort();
}
void FollowMixEvaluatorPostProcessor::AdsCostCalc(AddibleRecoContextInterface *context) {
  FollowMixRankContext *m_ctx = context->GetMutablePtrCommonAttr<FollowMixRankContext>(kMixRankCtxAttr);
  auto position_discount_map =
      ks::infra::KConf().GetMap<std::string, double>("reco.follow.mixRankAdPriceRatioMap",
      std::make_shared<std::map<std::string, double>>(),
      [](const std::string &key, std::string *val) -> bool {
        *val = key;
        return true;
      })->Get();
  auto uplift_discount_map =
      ks::infra::KConf().GetMap<std::string, double>("reco.follow.mixUpliftRankAdPriceRatioMap",
      std::make_shared<std::map<std::string, double>>(),
      [](const std::string &key, std::string *val) -> bool {
        *val = key;
        return true;
      })->Get();
  auto mix_uplift_white_author_set_ptr =
      ks::infra::KConf().GetSet<int64_t>("reco.follow.mixUpliftWhiteAuthorSet",
      std::make_shared<std::set<int64_t>>())->Get();
  double follow_mix_uplift_weight = hp_.ad_price_uplift_weight;
  double discount = 1.0;
  double total_price = 0.0;
  int enable_white_author_uplift_weight =
    context->GetIntCommonAttr("enable_white_author_uplift_weight").value_or(0);
  for (size_t idx = 0; idx < m_ctx->MixResults().Size(); idx++) {
    if (idx < m_ctx->mix_page_size_) {
      auto item_ptr = m_ctx->MixResults().At(idx);
      if (item_ptr->type == FollowMixRankResult::AD_DSP) {
        mix::kuaishou::ad::DspAdInfo& dsp_ad_info = item_ptr->dsp_info_;
        auto* ad_result = dsp_ad_info.mutable_ad_result();
        mix::kuaishou::ad::AdDeliverInfo* ad_deliver_info = ad_result->mutable_ad_deliver_info();
        int64 origin_price = ad_deliver_info->price();
        auto position_it = position_discount_map->find(std::to_string(idx));
        if (position_it == position_discount_map->end()) {
          FollowMixPerf(context, 1, "mix_sort.ad_cost_calc.position_discount_map_notfind_skipdiscount");
          continue;
        }
        //  直接打折计费
        discount = std::min(1.0, std::max(position_it->second, 0.1));
        double discount_ori = 0.0;
        auto discount_it = uplift_discount_map->find(std::to_string(item_ptr->natural_index_));
        if (discount_it == uplift_discount_map->end()) {
          FollowMixPerf(context, 1, "mix_sort.ad_cost_calc.uplift_discount_map_notfind");
        } else {
          discount_ori = discount_it->second;
        }
        // 白名单客户完全按 uplift 计费
        if (enable_white_author_uplift_weight &&
          mix_uplift_white_author_set_ptr->count(ad_deliver_info->ad_base_info().author_id()) > 0) {
          follow_mix_uplift_weight = 1.0;
        }
        double price_ratio = discount - discount_ori;
        double price = follow_mix_uplift_weight * ad_deliver_info->price() * price_ratio
          + (1 - follow_mix_uplift_weight) * ad_deliver_info->price();
        double price_feed = follow_mix_uplift_weight * ad_deliver_info->price_feed() * price_ratio
          + (1 - follow_mix_uplift_weight) * ad_deliver_info->price_feed();
        double price_slide = follow_mix_uplift_weight * ad_deliver_info->price_slide() * price_ratio
          + (1 - follow_mix_uplift_weight) * ad_deliver_info->price_slide();
        // 计费保护
        price = std::min(1.0 * ad_deliver_info->price(), std::max(price, 0.1 * ad_deliver_info->price()));
        price_feed = std::min(1.0 * ad_deliver_info->price_feed(),
                              std::max(price_feed, 0.1 * ad_deliver_info->price_feed()));
        price_slide = std::min(1.0 * ad_deliver_info->price_slide(),
                               std::max(price_slide, 0.1 * ad_deliver_info->price_slide()));
        ad_deliver_info->set_price(price);
        ad_deliver_info->set_price_feed(price_feed);
        ad_deliver_info->set_price_slide(price_slide);
        item_ptr->price_dis = price;
        item_ptr->price_dis_feed = price_feed;
        item_ptr->price_dis_slide = price_slide;
        if (idx < 12) {
          total_price += ad_deliver_info->price();
        }
        if (origin_price > price) {
          FollowMixPerf(context, 1000 * (origin_price - price),
            "mix_sort.ad_cost_calc.discount_price_idx-" + std::to_string(idx));
        } else {
          FollowMixPerf(context, 1000 * (price - origin_price), "mix_sort.ad_cost_calc.uplift_price_wrong");
        }
        LOG_EVERY_N(INFO, 1000) << "ad_cost_calculate, mix_uplift: "
                                  << "creative_id: " << ad_deliver_info->ad_base_info().creative_id()
                                  << " author_id: " << ad_deliver_info->ad_base_info().author_id()
                                  << " auction_bid: " << ad_deliver_info->ad_base_info().auction_bid()
                                  << " origin_price: " << origin_price
                                  << " price_ratio: " << price_ratio
                                  << " discount_uplift: " << (discount - discount_ori)
                                  << " weight: " << follow_mix_uplift_weight
                                  << " new_price: " << price;
      }
    }
  }
}
void FollowMixEvaluatorPostProcessor::Clear() {
  IsPhotoItemWriteLogInput_ = false;
  IsLiveItemWriteLogInput_ = false;
  hp_.Clear();
}
typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, FollowMixEvaluatorPostProcessor, FollowMixEvaluatorPostProcessor);
void FollowMixEvaluatorPostProcessor::ReplaceWithTopGiftResults(AddibleRecoContextInterface *context) {
  FollowMixRankContext *m_ctx = context->GetMutablePtrCommonAttr<FollowMixRankContext>(kMixRankCtxAttr);

  // 如果功能关闭，不执行替换
  if (!hp_.enable_random_support_live_tagging_) {
    return;
  }

  // 找到符合条件的直播 item 位置
  std::vector<std::pair<size_t, std::shared_ptr<FollowMixRankResult>>> live_positions;

  for (size_t i = 0; i < m_ctx->MixResults().Size(); ++i) {
    auto item = m_ctx->MixResults().At(i);
    if (!item->is_live_) continue;

    int64_t is_cover_merchant_live = item->is_pro_live_author_or_big_v_;
    int64_t is_merchant_live = item->is_pro_live_author_;

    // 根据 AB 参数选择替换口径
    bool should_replace = hp_.enable_merchant_live_filter_ ?
        (is_cover_merchant_live != 1) : (is_merchant_live != 1);

    if (should_replace) {
      live_positions.push_back(std::make_pair(i, item));
    }
  }

  if (live_positions.empty()) {
    return;
  }

  std::vector<std::shared_ptr<FollowMixRankResult>> replacement_items = m_ctx->top_gift_results_;

  // 执行替换
  size_t replacement_count = std::min(live_positions.size(), replacement_items.size());
  for (size_t i = 0; i < replacement_count; ++i) {
    size_t pos = live_positions[i].first;
    auto new_item = replacement_items[i];
    m_ctx->MixResults().Replace(pos, new_item);
  }
}
}  // namespace platform
}  // namespace ks
