#pragma once

#include <string>
#include <map>
#include <memory>
#include <utility>
#include <vector>
#include "ks/reco_proto/proto/reco.pb.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "teams/reco-arch/colossus/flat_generated/flat_generated_helper.h"
#include "teams/reco-arch/colossus/item/common_item_types.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"

namespace ks {
namespace platform {
class ExploreLifeClusterInterestEnricher : public CommonRecoBaseEnricher {
 public:
  ExploreLifeClusterInterestEnricher() {}

  bool IsAsync() const override {
    return false;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  void SetClusterInterestInfoFromColossus(MutableRecoContextInterface *context);
  void SetClusterInterest(MutableRecoContextInterface *context,
    RecoResultConstIter begin,
    RecoResultConstIter end);

 private:
  bool InitProcessor() override;
  static const int LIFE_CHANNEL_ = 122;
  static const int FOUNTAIN_CHANNEL_ = 125;
  std::string colossus_resp_attr_;
  std::string cluster_id_attr_;
  // std::string save_user_cluster_eff_interest_list_;
  // std::string save_user_cluster_interest_score_list_;
  // std::string save_user_cluster_interest_vv_list_;
  // std::string save_user_cluster_interest_vv_ratio_list_;
  // std::string save_user_cluster_shortterm_interest_list_;
  std::string save_eff_interest_num_to_common_attr_;
  // std::string save_score_to_attr_;
  std::string save_cluster_interest_vv_to_attr_;
  std::string save_is_eff_interest_to_attr_;
  std::string save_is_longterm_interest_to_attr_;
  std::string save_cluster_interest_score_to_attr_;
  std::string save_lt_interest_score_to_attr_;
  folly::F14FastMap<uint64, int> life_user_his_p2c632_map_;
  std::vector<int64>  lt_cluster_vv_list_, st_cluster_vv_list_;
  std::vector<double>  lt_cluster_score_list_, st_cluster_score_list_;
  // std::vector<int64>  lt_eff_interest_cid_list_;
  // folly::F14FastMap<uint64, double> longtime_cluster_interest_score_list;

  DISALLOW_COPY_AND_ASSIGN(ExploreLifeClusterInterestEnricher);
};

}  // namespace platform
}  // namespace ks
