#include <utility>
#include <map>
#include <memory>
#include <algorithm>
#include "dragon/src/processor/ext/explore_life/enricher/explore_life_cluster_interest_enricher.h"
#include "dragon/src/processor/ext/explore/util/explore_util.h"
#include "teams/reco-arch/colossus/flat_generated/flat_generated_helper.h"
#include "teams/reco-arch/colossus/item/common_item_types.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"
namespace ks {
namespace platform {

bool ExploreLifeClusterInterestEnricher::InitProcessor() {
  colossus_resp_attr_ = config()->GetString("colossus_resp_attr", "");
  if (colossus_resp_attr_.empty()) {
    LOG(ERROR) << "Miss colossus_resp_attr";
    return false;
  }
  cluster_id_attr_ = config()->GetString("hetu_sim_cluster_id", "");
  // save_user_cluster_eff_interest_list_ = config()->GetString("save_user_cluster_eff_interest_list", "");
  // save_user_cluster_interest_score_list_ =
    // config()->GetString("save_user_cluster_interest_score_list", "");
  // save_user_cluster_interest_vv_list_ = config()->GetString("save_user_cluster_interest_vv_list", "");
  // save_user_cluster_interest_vv_ratio_list_ =
  //   config()->GetString("save_user_cluster_interest_vv_ratio_list", "");
  // save_user_cluster_shortterm_interest_list_ =
  //   config()->GetString("save_user_cluster_shortterm_interest_list", "");
  save_eff_interest_num_to_common_attr_ =
    config()->GetString("save_eff_interest_num_to_common_attr", "");
  // save_score_to_attr_ = config()->GetString("save_score_to_attr", "");
  save_cluster_interest_vv_to_attr_ = config()->GetString("save_cluster_interest_vv_to_attr", "");
  save_is_eff_interest_to_attr_ = config()->GetString("save_is_eff_interest_to_attr", "");
  save_is_longterm_interest_to_attr_ = config()->GetString("save_is_longterm_interest_to_attr", "");
  save_cluster_interest_score_to_attr_ = config()->GetString("save_cluster_interest_score_to_attr", "");
  save_lt_interest_score_to_attr_ = config()->GetString("save_lt_interest_score_to_attr", "");
  return true;
}

void ExploreLifeClusterInterestEnricher::Enrich(
    MutableRecoContextInterface *context,
    RecoResultConstIter begin,
    RecoResultConstIter end) {
  SetClusterInterestInfoFromColossus(context);
  SetClusterInterest(context, begin, end);
}

void ExploreLifeClusterInterestEnricher::SetClusterInterestInfoFromColossus
  (MutableRecoContextInterface *context) {
  const auto* items_ptr = context->GetPtrCommonAttr<std::pair<const void*, int>>(colossus_resp_attr_);
  if (!items_ptr || !items_ptr->first || items_ptr->second == 0) {
    CL_LOG(WARNING) << "colossus items empty";
    return;
  }
  auto remap_cluster_id_632_list = context->GetIntListCommonAttr("remap_cluster_id_632_list");
  auto colossus_cluster_id_list = context->GetIntListCommonAttr("colossus_cluster_id_list");
  auto colossus_photo_id_list = context->GetIntListCommonAttr("colossus_photo_id_list");
  int shortterm_seconds = GetIntProcessorParameter(context, "user_colossus_min_sec_ago", 1209600);  // 二周
  int longterm_seconds = GetIntProcessorParameter(context, "user_colossus_min_sec_ago", 7776000);  // 三个月

  if (!colossus_photo_id_list || !colossus_cluster_id_list || !remap_cluster_id_632_list ||
    colossus_photo_id_list->size() != colossus_cluster_id_list->size() ||
    remap_cluster_id_632_list->size() != 1000) {
    return;
  }
  life_user_his_p2c632_map_.clear();
  for (int i = 0; i < colossus_cluster_id_list->size(); i++) {
    int pid = colossus_photo_id_list->at(i);
    int cluster_id = colossus_cluster_id_list->at(i);
    int cluster632_id = -1;
    if (cluster_id >= 0 && cluster_id < remap_cluster_id_632_list->size()) {
      cluster632_id = remap_cluster_id_632_list->at(cluster_id);
    }
    if (!life_user_his_p2c632_map_.count(pid) && cluster632_id > 0) {
      life_user_his_p2c632_map_[pid] = cluster632_id;
    }
  }

  // int like_count = 0;
  // int follow_count = 0;
  // int forward_count = 0;
  // int  = 0;
  // int interact_count = 0;
  // int click_count = items_ptr->second;
  // int long_video_click_count = 0;
  // int long_video_watch_time_sum = 0;
  // double long_video_finish_rate_sum = 0;
  // int fountain_watch_time_sum = 0;
  // int short_view_count = 0;
  // int effective_view_count = 0;
  // int long_view_count = 0;
  // int finish_view_count = 0;
  // int colossus_item_count = 0;

  uint64 now_time_sec =  base::GetTimestamp() / base::Time::kMicrosecondsPerSecond;

  int colossus_list_len = colossus_cluster_id_list->size();
  int temp_count = 0;
  // std::vector<int64>  lt_cluster_score_list_, lt_cluster_vv_list_,
    // st_cluster_score_list_, st_cluster_vv_list_;
  // std::vector<int64>  lt_eff_interest_cid_list;
  // folly::F14FastMap<uint64, double> longtime_cluster_interest_score_list;
  lt_cluster_score_list_.clear();
  lt_cluster_vv_list_.clear();
  st_cluster_score_list_.clear();
  st_cluster_vv_list_.clear();
  for (int i=0; i < 1000; i++) {
    lt_cluster_score_list_.emplace_back(0.0);
    lt_cluster_vv_list_.emplace_back(0);
    st_cluster_score_list_.emplace_back(0.0);
    st_cluster_vv_list_.emplace_back(0);
  }

  for (int i = items_ptr->second - 1; i >= 0 && temp_count < colossus_list_len; --i) {
    // 这里的 colossus::SimItemV2T 根据自己的情况替换成合适的 struct
    // 详细定义见头文件：teams/reco-arch/colossus/item/common_item_types.h
    temp_count+=1;
    const auto *item_ptr = reinterpret_cast<const colossus::SimItemV2T *>(items_ptr->first) + i;
    int pid = item_ptr->photo_id;
    if ( !life_user_his_p2c632_map_.count(pid) ) {
      continue;
    }
    int cluster_id = life_user_his_p2c632_map_[pid];  // 632
    uint16_t label = item_ptr->label;
    uint32_t channel = item_ptr->channel;
    bool like = label & 0x01;
    bool follow = label & (1 << 1);
    bool forward = label & (1 << 2);
    // bool hate = label & (1 << 3);
    bool comment = label & (1 << 4);
    // bool has_entered_profile = label & (1 << 6);
    int duration = item_ptr->duration;
    int play_time = item_ptr->play_time;

    if (channel != FOUNTAIN_CHANNEL_ && channel != LIFE_CHANNEL_) {
      continue;
    }

    if (!(cluster_id >= 0 && cluster_id <= lt_cluster_score_list_.size())) {
      continue;
    }

    double interest_score = 0.0;
    double pos_act_num = (double)(like + follow + forward + comment);
    bool effi_view = explore::IsEffectiveView(duration, play_time) ? 1.0 : 0.0;
    double neg_act_num = (double)(!effi_view);
    double is_long_view = explore::IsLongView(duration, play_time) ? 1.0 : 0.0;
    double view_time_mins = play_time / 60.0;
    interest_score = 7 * pos_act_num + is_long_view * view_time_mins - 0.2 * neg_act_num;
    // if (now_time_sec - item_ptr->timestamp < shortterm_seconds) {
    // }

    if (now_time_sec - item_ptr->timestamp < longterm_seconds) {
      lt_cluster_score_list_[cluster_id] += interest_score;
      lt_cluster_vv_list_[cluster_id] += 1;
      // if (longtime_cluster_interest_score_list.count(cluster_id) <= 0) {
      //   longtime_cluster_interest_score_list[cluster_id] = interest_score;
      // } else {
      //   longtime_cluster_interest_score_list[cluster_id] += interest_score;
      // }
    }
    if (now_time_sec - item_ptr->timestamp < shortterm_seconds) {
      st_cluster_score_list_[cluster_id] += interest_score;
      st_cluster_vv_list_[cluster_id] += 1;
    }
  }

  int eff_interest_num = 0;
  for (int i = 0; i < lt_cluster_score_list_.size(); i++) {
    if (st_cluster_score_list_[i] >= 0.3) {
      eff_interest_num += 1;
    }
  }
  context->SetIntCommonAttr(save_eff_interest_num_to_common_attr_, eff_interest_num);
}


void ExploreLifeClusterInterestEnricher::SetClusterInterest(MutableRecoContextInterface *context,
    RecoResultConstIter begin,
    RecoResultConstIter end) {
  auto *cluster_id_accessor = context->GetItemAttrAccessor(cluster_id_attr_);
  for (auto iter = begin; iter != end; ++iter) {
    const CommonRecoResult& result = *iter;
    int64 cluster_id = -1;
    int64 cluster632_id = -1;
    if (auto cluster_id_ptr = context->GetIntItemAttr(*iter, cluster_id_accessor)) {
      cluster_id = *cluster_id_ptr;
    }
    auto remap_cluster_id_632_list = context->GetIntListCommonAttr("remap_cluster_id_632_list");
    if ( remap_cluster_id_632_list->size() == 1000 && cluster_id < 1000 && cluster_id >= 0 ) {
      cluster632_id = remap_cluster_id_632_list->at(cluster_id);
    }

    auto saved_interest_score_accessor =
      context->GetItemAttrAccessor(save_cluster_interest_score_to_attr_);
    auto saved_lt_interest_score_accessor =
      context->GetItemAttrAccessor(save_lt_interest_score_to_attr_);
    auto saved_interest_vv_accessor = context->GetItemAttrAccessor(save_cluster_interest_vv_to_attr_);
    auto saved_is_eff_interest_accessor = context->GetItemAttrAccessor(save_is_eff_interest_to_attr_);
    auto saved_is_longterm_interest_accessor =
      context->GetItemAttrAccessor(save_is_longterm_interest_to_attr_);
    int is_eff_interest = 0;
    int is_longterm_interest = 0;
    if (cluster632_id >= 0 && cluster632_id < st_cluster_score_list_.size()) {
      double interest_score = st_cluster_score_list_[cluster632_id];
      double lt_interest_score = lt_cluster_score_list_[cluster632_id];
      context->SetDoubleItemAttr(result, saved_interest_score_accessor, std::min(interest_score, 8.0));
      context->SetDoubleItemAttr(result, saved_lt_interest_score_accessor, lt_interest_score);
      context->SetIntItemAttr(result, saved_interest_vv_accessor, st_cluster_vv_list_[cluster632_id]);
      if (interest_score >= 0.3) {
        is_eff_interest = 1;
      }
      if (lt_cluster_score_list_[cluster632_id] >= 0.3) {
        is_longterm_interest = 1;
      }
    } else {
      context->SetDoubleItemAttr(result, saved_interest_score_accessor, 0.0);
      context->SetDoubleItemAttr(result, saved_lt_interest_score_accessor, 0.0);
      context->SetIntItemAttr(result, saved_interest_vv_accessor, 0);
    }
    context->SetIntItemAttr(result, saved_is_eff_interest_accessor, is_eff_interest);
    context->SetIntItemAttr(result, saved_is_longterm_interest_accessor, is_longterm_interest);

    // auto saved_attr_accessor = context->GetItemAttrAccessor(save_score_to_attr_);
    // context->SetDoubleItemAttr(result, saved_attr_accessor, 1);
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, ExploreLifeClusterInterestEnricher, ExploreLifeClusterInterestEnricher)

}  // namespace platform
}  // namespace ks
