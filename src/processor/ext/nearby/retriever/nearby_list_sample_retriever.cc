#include "dragon/src/processor/ext/nearby/retriever/nearby_list_sample_retriever.h"

#include <algorithm>
#include <memory>
#include <set>
#include <string>
#include <utility>
#include <vector>

#include "ks/reco_pub/reco/util/util.h"
#include "ks/serving_util/global_gflags.h"
#include "serving_base/util/scope_exit.h"

namespace ks {
namespace platform {

void NearbyListSampleRetriever::Clear() {
  list_samples_item_key_.clear();
  list_samples_item_type_.clear();
  list_samples_item_reason_.clear();
  origin_rank_result_.clear();
  pre_photo_count_ = 0.0;
  pre_live_count_ = 0.0;
}

void NearbyListSampleRetriever::Retrieve(AddibleRecoContextInterface *context) {
  Clear();
  Init(context);
  ListGenerator(context);
  // BeamSearchGenerator(context, 12);
  std::vector<CommonRecoRetrieveResult> retrieve_items;
  int enable_filter_invalid_list_samples = GetIntCommonAttrFromContext(context,
            "enable_filter_invalid_list_samples", 0);
  int nearby_page_size = GetIntCommonAttrFromContext(context,
            "nearby_reco_user_info.nearby_page_size", 0);
  int enable_expand_mixrank_page_size = nearby_page_size > 10 ? 1 : 0;
  if (list_samples_item_key_.size() > 0 && list_samples_item_key_.size() == list_samples_item_type_.size()
      && list_samples_item_key_.size() == list_samples_item_reason_.size()) {
    for (int i = 0; i < list_samples_item_key_.size(); i++) {
      double live_rate = 0.0;
      for (auto &type : list_samples_item_type_[i]) {
        if (type == 2) {
          live_rate += 0.1;
        }
      }
      if (!enable_filter_invalid_list_samples || (enable_filter_invalid_list_samples > 0
            && live_rate >= min_live_rate_ && live_rate <= max_live_rate_)) {
        int64 item_key = RecoUtil::ResetItemType(retrieval_item_type_, 10000 + i);
        retrieve_items.emplace_back(item_key, list_samples_item_reason_[i], 0.0);
      }
    }
    AddToRecoResults(context, retrieve_items);
  }
  AddListAttrs(context);
  context->SetIntCommonAttr("list_mixrank_start_index", start_index_);
  context->SetIntCommonAttr("enable_expand_mixrank_page_size", enable_expand_mixrank_page_size);
}

bool NearbyListSampleRetriever::IsTypeAvalid(int window_size, int window_num,
                std::vector<int64> item_type_sample, int item_type) {
  int size = item_type_sample.size();
  if (window_size > size) {
    return true;
  }
  int live_num = 0;
  for (int i = 0; i < window_size - 1; i++) {
    if (item_type_sample[size - 1 - i] == 2) {
      live_num++;
    }
  }
  if (item_type == 2) {
    return live_num < window_num;
  }
  return live_num >= window_num;;
}

void NearbyListSampleRetriever::Init(AddibleRecoContextInterface *context) {
  int live_max_window_size = GetIntCommonAttrFromContext(context, "live_max_window_size", 4);
  int live_max_window_num = GetIntCommonAttrFromContext(context, "live_max_window_num", 2);
  int live_min_window_size = GetIntCommonAttrFromContext(context, "live_min_window_size", 4);
  int live_min_window_num = GetIntCommonAttrFromContext(context, "live_min_window_num", 1);
  max_live_rate_ = live_max_window_num / (live_max_window_size * 1.0 + 0.000001);
  min_live_rate_ = live_min_window_num / (live_min_window_size * 1.0 + 0.000001);
  context->SetDoubleCommonAttr("max_live_rate", max_live_rate_);
  context->SetDoubleCommonAttr("min_live_rate", min_live_rate_);
}

void NearbyListSampleRetriever::ListGenerator(AddibleRecoContextInterface *context) {
  const auto &current_results = context->GetCommonRecoResults();
    // 选 50 个参与重排, 30 个视频. 20 个直播
  std::vector<int64> live_item_keys;
  std::vector<int64> photo_item_keys;
  int photo_count = 0;
  int live_count = 0;
  double top_live_count = 0.0;
  double weight_top_live_count = 0.0;
  double weight_top_photo_count = 0.0;
  double index = 1.0;
  static const double score_smooth = 3.0;
  std::vector<int64> top_item_ids;
  std::vector<int64> top_item_types;
  int tar_idx = 0;
  for (const auto &result : current_results) {
    NearbyRankResult rank_result;
    if (tar_idx < start_index_) {
      tar_idx++;
      if (result.GetType() == ks::reco::RecoEnum::ITEM_TYPE_PHOTO) {
        pre_photo_count_ += 1.0;
      } else {
        pre_live_count_ += 1.0;
      }
      continue;
    }
    tar_idx++;
    if (photo_count >= 30 && live_count >= 20) {
      break;
    }
    if (photo_count >= 30 && result.GetType() == ks::reco::RecoEnum::ITEM_TYPE_PHOTO) {
      continue;
    }
    if (live_count >= 20 && result.GetType() == ks::reco::RecoEnum::ITEM_TYPE_LIVESTREAM) {
      continue;
    }
    rank_result.item_key = result.item_key;
    rank_result.item_type = result.GetType();
    double score = GetDoubleItemAttrFromContext(context,
                result.item_key, "ensemble_shift_score", 0.0);
    rank_result.raw_score = score;
    rank_result.final_score = score;
    origin_rank_result_.push_back(rank_result);
    index += 1.0;
    if (index < 11.5) {
      top_item_ids.push_back(result.item_key);
    }
    if (result.GetType() == ks::reco::RecoEnum::ITEM_TYPE_PHOTO) {
      photo_item_keys.push_back(result.item_key);
      photo_count++;
      if (index < 11.5) {
        top_item_types.push_back(1);
        weight_top_photo_count += 1.0 / index;
      }
    } else {
      live_item_keys.push_back(result.item_key);
      live_count++;
      if (index < 11.5) {
        top_live_count += 1.0;
        weight_top_live_count += 1.0 / index;
        top_item_types.push_back(2);
      }
    }
  }
  int hard_live_max_window_size = GetIntCommonAttrFromContext(context, "hard_live_max_window_size", 4);
  int hard_live_max_window_num = GetIntCommonAttrFromContext(context, "hard_live_max_window_num", 3);
  int hard_live_min_window_size = GetIntCommonAttrFromContext(context, "hard_live_min_window_size", 8);
  int hard_live_min_window_num = GetIntCommonAttrFromContext(context, "hard_live_min_window_num", 1);
  int nearby_max_live_noshow_session_cnt = GetIntCommonAttrFromContext(context,
                                  "nearby_max_live_noshow_session_cnt", 0);
  if (hard_live_min_window_num == 0 && nearby_max_live_noshow_session_cnt > 0) {
    int live_noshow_session_cnt =  GetIntCommonAttrFromContext(context,
              "nearby_reco_user_info.live_noshow_session_cnt", 0);
    if (live_noshow_session_cnt > nearby_max_live_noshow_session_cnt) {
      hard_live_min_window_num = 1;
    }
  }
  std::vector<std::vector<int64>> item_type_samples;

  // 根据打散条件全量枚举前 10 的 list sample
  for (int i = 0; i < 10; i++) {
    std::vector<std::vector<int64>> item_type_samples_copy = item_type_samples;
    std::vector<std::vector<int64>> item_type_samples_tmp;
    if (item_type_samples_copy.size() == 0) {
      item_type_samples.push_back({1});
      item_type_samples.push_back({2});
    } else {
      base::PseudoRandom random(base::GetTimestamp());
      int now_size = 0;
      int copy_size = item_type_samples_copy.size();
      int index = 0;
      for (const auto &item_type_sample : item_type_samples_copy) {
        std::vector<int64> item_type_sample_tmp = item_type_sample;
        if (IsTypeAvalid(hard_live_max_window_size, hard_live_max_window_num, item_type_sample_tmp, 2)) {
          item_type_sample_tmp.push_back(1);
          item_type_samples_tmp.push_back(item_type_sample_tmp);
        }
        item_type_sample_tmp = item_type_sample;
        if (IsTypeAvalid(hard_live_min_window_size, hard_live_min_window_num, item_type_sample_tmp, 1)) {
          item_type_sample_tmp.push_back(2);
          item_type_samples_tmp.push_back(item_type_sample_tmp);
        }
      }
      item_type_samples = item_type_samples_tmp;
    }
  }
  int photo_size = photo_item_keys.size();
  int live_size = live_item_keys.size();
  int list_index = 0;
  int item_type_samples_size = item_type_samples.size();
  for (const auto &item_type_sample : item_type_samples) {
    std::vector<int64> list_sample_item_key;
    std::vector<int64> list_sample_item_type;
    int i = 0;
    int j = 0;
    bool succ_flag = true;
    for (const auto &item_type : item_type_sample) {
      if (item_type == ks::reco::RecoEnum::ITEM_TYPE_PHOTO) {
        if (i < photo_size) {
          list_sample_item_key.push_back(photo_item_keys[i]);
          list_sample_item_type.push_back(1);
          i++;
        } else {
          succ_flag = false;  // item 数不够,生成 list 失败
          break;
        }
      } else {
        if (j < live_size) {
          list_sample_item_key.push_back(live_item_keys[j]);
          list_sample_item_type.push_back(2);
          j++;
        } else {
          succ_flag = false;  // item 数不够,生成 list 失败
          break;
        }
      }
    }
    list_index++;
    if (succ_flag) {
      list_samples_item_key_.push_back(list_sample_item_key);
      list_samples_item_type_.push_back(list_sample_item_type);
      int reason = 901;
      list_samples_item_reason_.push_back(reason);
    }
  }
}
void NearbyListSampleRetriever::AddListAttrs(AddibleRecoContextInterface *context) {
  static const std::vector<std::string> doubleAttrs = {"fr_pctr", "fr_pltr", "fr_pwtr", "fr_pftr",
    "norm_ctr", "norm_gtr", "gbdt_rerank_score", "norm_vtr",
      "fr_plvtr", "fr_pnvtr", "fr_pwtd", "fr_pcmtr"};
  static const std::vector<std::string> post_click_Attrs_pre = {"fr_pltr", "fr_pwtr", "fr_plvtr",
      "fr_pnvtr", "fr_pwtd", "fr_pcmtr"};
  static const std::vector<std::string> post_click_Attrs_post = {"ctltr", "ctwtr", "ctlvtr",
      "ctvtr", "ctwtd", "ctcmtr"};
  static const std::vector<std::string> intAttrs = {"pId", "aId", "pProvinceId", "aFollowed",
      "pCityId", "pReason", "item_type"};
  int enable_list_wise_evaluator_process = GetIntCommonAttrFromContext(context,
            "enable_list_wise_evaluator_process", 0);
  if (list_samples_item_key_.size() > 0 && list_samples_item_key_.size() == list_samples_item_type_.size()) {
    for (int i = 0; i < list_samples_item_key_.size(); i++) {
      int64 item_key = RecoUtil::ResetItemType(retrieval_item_type_, 10000 + i);
      auto list_sample_item_keys = list_samples_item_key_[i];
      auto list_samples_item_types = list_samples_item_type_[i];
      double mix_rank_score = 0.0;
      if (list_sample_item_keys.size() > 0 &&
          list_samples_item_types.size() == list_sample_item_keys.size()) {
        double live_num = 0.0;
        for (int j = 0; j < list_sample_item_keys.size(); j++) {
          double value = GetDoubleItemAttrFromContext(context,
                list_sample_item_keys[j], "ensemble_shift_score", 0.0);
          mix_rank_score += value * std::pow(0.8, j);
          if (list_samples_item_types[j] == 2) {
            live_num += 1.0;
          }
        }
        if (enable_list_wise_evaluator_process > 0) {
          std::vector<double> pctr_list;
          for (auto &attr : doubleAttrs) {
            std::vector<double> double_attr_list;
            for (int j = 0; j < list_sample_item_keys.size(); j++) {
              double value = GetDoubleItemAttrFromContext(context, list_sample_item_keys[j], attr, 0.0);
              if (attr == "fr_pnvtr" && list_samples_item_types[j] == 2) {  //  直播 vtr 反解
                value = value / (1.0 - std::max(value, 0.000001));
              }
              if (attr == "fr_pctr") {
                pctr_list.push_back(value);
              }
              double_attr_list.push_back(value);
            }
            std::string set_attr_name = attr + "_list";
            context->SetDoubleListItemAttr(item_key, set_attr_name, std::move(double_attr_list));
          }
          for (int k = 0 ; k < post_click_Attrs_pre.size(); k++) {
            std::string attr_name = post_click_Attrs_pre[k] + "_list";
            std::vector<double> double_attr_list;
            auto list_attr = context->GetDoubleListItemAttr(item_key, attr_name);
            if (list_attr && list_attr->size() == pctr_list.size()) {
              for (int j = 0; j < list_attr->size(); ++j) {
                double_attr_list.push_back(list_attr->at(j) * pctr_list[j]);
              }
            }
            if (k < post_click_Attrs_post.size()) {
              std::string set_attr_name = post_click_Attrs_post[k] + "_list";
              context->SetDoubleListItemAttr(item_key, set_attr_name, std::move(double_attr_list));
            }
          }
          for (auto &attr : intAttrs) {
            std::vector<int64> int_attr_list;
            for (int j = 0; j < list_sample_item_keys.size(); j++) {
              int64 value = GetIntItemAttrFromContext(context, list_sample_item_keys[j], attr, 0);
              int_attr_list.push_back(value);
            }
            std::string set_attr_name = attr + "_list";
            context->SetIntListItemAttr(item_key, set_attr_name, std::move(int_attr_list));
          }
        }
        double sample_live_rate = (live_num + pre_live_count_) / (10.0 + pre_live_count_ + pre_photo_count_);
        double max_decay_rate = sample_live_rate > max_live_rate_ ?
            max_live_rate_ / (sample_live_rate + 0.00001) : 1.0;
        double min_decay_rate = sample_live_rate < min_live_rate_ ?
            sample_live_rate / (min_live_rate_ + 0.00001) : 1.0;
        context->SetDoubleItemAttr(item_key, "final_list_mixrank_score", mix_rank_score);
        context->SetDoubleItemAttr(item_key, "mixrank_max_decay_rate", max_decay_rate);
        context->SetDoubleItemAttr(item_key, "mixrank_min_decay_rate", min_decay_rate);
        context->SetIntListItemAttr(item_key,
            "list_sample_item_keys", std::vector<int64>(list_sample_item_keys));
        context->SetIntListItemAttr(item_key,
            "list_sample_item_types", std::vector<int64>(list_samples_item_type_[i]));
      }
    }
  }
}

void NearbyListSampleRetriever::BeamSearchGenerator(AddibleRecoContextInterface *context, int search_num) {
  std::vector<NearbyRankResultList> list_samples;
  int final_search_num = search_num > origin_rank_result_.size() ? origin_rank_result_.size() : search_num;
  int step_search_num = final_search_num < 2 ? final_search_num : 2;
  for (int i = 0; i < final_search_num; i++) {
    NearbyRankResultList result_list;
    result_list.rank_result_list.push_back(origin_rank_result_[i]);
    result_list.item_key_set.insert(origin_rank_result_[i].item_key);
    result_list.list_score = origin_rank_result_[i].final_score;
    list_samples.push_back(result_list);
  }
  double photo_decay_exponent = std::min(2.0 * min_live_rate_ * min_live_rate_
        / std::max(1.0 - min_live_rate_, 0.00001) * GetDoubleCommonAttrFromContext(context,
        "nearby_rerank_bs_photo_decay_rate", 1.25), 1.0);
  double live_decay_exponent = std::min(2.0 * (1.0 - max_live_rate_) * (1.0 - max_live_rate_)
        / std::max(max_live_rate_, 0.0001) * GetDoubleCommonAttrFromContext(context,
        "nearby_rerank_bs_live_decay_rate", 1.4), 1.0);
  double photo_decay_rate = std::pow(150.0, photo_decay_exponent);
  double live_decay_rate = std::pow(150.0, live_decay_exponent);
  //  循环 9 轮
  auto cmp = [](NearbyRankResult &l, NearbyRankResult &r) {
    return l.final_score > r.final_score;
  };
  auto list_cmp = [](NearbyRankResultList &l, NearbyRankResultList &r) {
    return l.list_score > r.list_score;
  };
  for (int i = 0; i < 9; i++) {
    std::vector<NearbyRankResultList> list_samples_tmp = list_samples;
    std::vector<NearbyRankResultList> list_samples_empty;
    for (auto &result_list : list_samples_tmp) {
      double photo_decay_coeff = 1.0;
      double live_decay_coeff = 1.0;
      int size = result_list.rank_result_list.size();
      if (result_list.rank_result_list[size - 1].item_type == 1) {
        for (int j = 0; j < size; j++) {
          if (result_list.rank_result_list[size - 1 - j].item_type == 1) {
            photo_decay_coeff = photo_decay_coeff / std::pow(photo_decay_rate, j + 1.0);
            if (1.0 / max_live_rate_ > j + 2.0) {  // 对于直播 load 低的情况, 增加 photo 的连出机会
              photo_decay_coeff = 10.0;
            }
          } else {
            break;
          }
        }
      } else {
        for (int j = 0; j < size; j++) {
          if (result_list.rank_result_list[size - 1 - j].item_type == 2) {
            live_decay_coeff = live_decay_coeff / std::pow(live_decay_rate, j + 1.0);
          } else {
            break;
          }
        }
      }
      //  对后续序列重新打分后 sort
      std::vector<NearbyRankResult> origin_rank_result_tmp = origin_rank_result_;
      for (int j = 0; j < origin_rank_result_tmp.size(); j++) {
        if (result_list.item_key_set.count(origin_rank_result_tmp[j].item_key)) {
          origin_rank_result_tmp[j].final_score = 0.0;
        } else if (origin_rank_result_tmp[j].item_type == 1) {
          origin_rank_result_tmp[j].final_score = origin_rank_result_tmp[j].raw_score * photo_decay_coeff;
        } else {
          origin_rank_result_tmp[j].final_score = origin_rank_result_tmp[j].raw_score * live_decay_coeff;
        }
      }
      std::sort(origin_rank_result_tmp.begin(), origin_rank_result_tmp.end(), cmp);
      for (int j = 0; j < step_search_num; j++) {
        NearbyRankResultList result_list_tmp = result_list;
        result_list_tmp.rank_result_list.push_back(origin_rank_result_tmp[j]);
        result_list_tmp.list_score = result_list_tmp.list_score + origin_rank_result_tmp[j].final_score;
        result_list_tmp.item_key_set.insert(origin_rank_result_tmp[j].item_key);
        list_samples_empty.push_back(result_list_tmp);
      }
    }
    std::sort(list_samples_empty.begin(), list_samples_empty.end(), list_cmp);
    list_samples.clear();
    for (int j = 0; j < final_search_num && j < list_samples_empty.size(); j++) {
      list_samples.push_back(list_samples_empty[j]);
    }
  }
  //  add to result
  for (auto &list_sample : list_samples) {
    std::vector<int64> list_sample_item_key;
    std::vector<int64> list_sample_item_type;
    for (auto &rank_result : list_sample.rank_result_list) {
      list_sample_item_key.push_back(rank_result.item_key);
      list_sample_item_type.push_back(rank_result.item_type);
    }
    list_samples_item_key_.push_back(list_sample_item_key);
    list_samples_item_type_.push_back(list_sample_item_type);
    list_samples_item_reason_.push_back(902);
  }
}
typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, NearbyListSampleRetriever, NearbyListSampleRetriever);

}  // namespace platform
}  // namespace ks
