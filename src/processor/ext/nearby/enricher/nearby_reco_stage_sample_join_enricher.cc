#include "dragon/src/processor/ext/nearby/enricher/nearby_reco_stage_sample_join_enricher.h"

#include <algorithm>
#include <cmath>
#include <unordered_map>
#include "dragon/src/processor/ext/nearby/util/nearby_dragon_util.h"
#include "ks/action/attr_name.h"
#include "serving_base/util/math.h"
#include "serving_base/utility/timer.h"

namespace ks {
namespace platform {
bool NearbyRecoStageSampleJoinEnricher::InitProcessor() {
  // kafka_topic_
  kafka_topic_ = config()->GetString("kafka_topic");
  if (kafka_topic_.empty()) {
    LOG(ERROR) << "NearbyRecoStageSampleJoinEnricher init failed! kafka_topic_ is empty";
    return false;
  }
  if (!ks::action::SampleJoinClient::Singleton()->RegisterKafkaTopic(kafka_topic_, "")) {
    LOG(ERROR) << "NearbyRecoStageSampleJoin<PERSON>nricher init failed! RegisterKafkaTopic config fail, topic:"
      << kafka_topic_;
    return false;
  }

  // item_attr_name_map_
  auto default_item_attr_name_map = std::make_shared<std::map<std::string, std::string>>();
  ks::infra::KeyParser<std::string> string_key_parser = [](const std::string& key, std::string* val) -> bool {
    *val = key;
    return true;
  };
  item_attr_name_map_ = ks::infra::KConf().GetMap(
    "reco.nearby.nearbyRecoStageSampleJoinItemAttrNameMap",
    default_item_attr_name_map,
    string_key_parser);
  if (!item_attr_name_map_) {
    LOG(ERROR) << "NearbyRecoStageSampleJoinEnricher init failed! item_attr_name_map_ == null";
    return false;
  }

  // req_sample_rate_
  auto default_req_sample_rate = std::make_shared<std::map<std::string, double>>();
  req_sample_rate_ = ks::infra::KConf().GetMap(
    "reco.nearby.nearbyRecoStageSampleJoinReqSampleRate",
    default_req_sample_rate,
    string_key_parser);
  if (!req_sample_rate_) {
    LOG(ERROR) << "NearbyRecoStageSampleJoinEnricher init failed! req_sample_rate_ == null";
    return false;
  }
  std::shared_ptr<std::map<std::string, double>> req_sample_rate = req_sample_rate_->Get();
  for (auto it = req_sample_rate->begin(); it != req_sample_rate->end(); it++) {
    LOG(INFO) << "NearbyRecoStageSampleJoinEnricher req_sample_rate_ key = " << it->first
              << "value = " << it->second;
  }

  LOG(INFO) << "NearbyRecoStageSampleJoinEnricher InitProcessor Success kafka_topic_ =" << kafka_topic_;
  return true;
}

void NearbyRecoStageSampleJoinEnricher::Enrich(MutableRecoContextInterface *context,
                                                          RecoResultConstIter begin,
                                                            RecoResultConstIter end) {
  bizname_ = GetActionLogBizName2(context);
  base::perfutil::PerfUtilWrapper::CountLogStash(1,
    "reco.nearby.NearbyRecoStageSampleJoinEnricher", "Enrich", bizname_);
  if (context->IsDebugRequest()) {
    CL_LOG(INFO) << "leaf show cancelled: this is a debug request.";
    return;
  }
  std::vector<CommonRecoResult> photos;
  std::vector<CommonRecoResult> lives;
  for (auto it = begin; it != end; ++it) {
    if (ks::reco::RecoEnum::ITEM_TYPE_PHOTO == Util::GetType(it->item_key)) {
      photos.emplace_back(*it);
    } else {
      lives.emplace_back(*it);
    }
  }

  // build item attrs
  int top_sample_count = 8;
  int tail_sample_count = 0;
  std::vector<std::string> item_attr_pb_strings;
  std::vector<std::string> item_keys;
  std::vector<std::string> item_owner_keys;
  std::vector<ks::reco::SampleJoinApiRequestEnum::ItemType> item_types;
  base::PseudoRandom random(base::GetTimestamp());
  double photo_req_sample_rate = GetReqSampleRate(bizname_, false);
  double photo_random = random.GetDouble();
  if (photo_random < photo_req_sample_rate) {
    std::vector<std::pair<int, CommonRecoResult>> photo_result;
    TopTailSample(photos, top_sample_count, tail_sample_count, &photo_result);
    BuildItemAttrs(context, photo_result, false, &item_attr_pb_strings,
      &item_keys, &item_owner_keys, &item_types);
  }
  // double live_req_sample_rate = GetReqSampleRate(bizname_, true);
  // double live_random = random.GetDouble();
  // if (live_random < live_req_sample_rate) {
  //   std::vector <std::pair<int, CommonRecoResult>> live_result;
  //   TopTailSample(lives, top_sample_count, tail_sample_count, &live_result);
  //   BuildItemAttrs(context, live_result, true, &item_attr_pb_strings,
  //     &item_keys, &item_owner_keys, &item_types);
  // }
  if (item_keys.size() > 0) {
    // build user attrs
    thread_local kuiba::PredictItem user_attrs;
    user_attrs.Clear();
    BuildUserAttrs(context, &user_attrs);

    // send log
    SendMessage(context, user_attrs, item_keys, item_owner_keys,
      item_attr_pb_strings, item_types);
  }
  CL_LOG_EVERY_N(INFO, 5000) << "NearbyRecoStageSampleJoinEnricher Enrich Success"
                             << " kafka_topic_ = " <<  kafka_topic_
                             << " photo_random = " << photo_random;
}

void NearbyRecoStageSampleJoinEnricher::TopTailSample(const std::vector<CommonRecoResult> &pool,
                                                    int top_sample_count, int tail_sample_count,
                                         std::vector<std::pair<int, CommonRecoResult>> * result) {
  if (top_sample_count > pool.size()) {
    top_sample_count = pool.size();
    tail_sample_count = 0;
  } else if (top_sample_count + tail_sample_count > pool.size()) {
    tail_sample_count = pool.size() - top_sample_count;
  }

  std::vector<int> sample_indices(pool.size() - top_sample_count);
  std::iota(sample_indices.begin(), sample_indices.end(), top_sample_count);

  base::PseudoRandom random(base::GetTimestamp());
  for (int i = 0; i < tail_sample_count; ++i) {
    std::swap(sample_indices[i], sample_indices[random.GetInt(i, sample_indices.size() - 1)]);
  }
  std::sort(sample_indices.begin(), sample_indices.begin() + tail_sample_count);

  for (int i = 0; i < top_sample_count; i++) {
    result->emplace_back(std::make_pair(i, pool[i]));
  }

  for (int i = 0; i < tail_sample_count; i++) {
    int indice = sample_indices[i];
    result->emplace_back(std::make_pair(indice, pool[indice]));
  }
}

void NearbyRecoStageSampleJoinEnricher::BuildItemAttrs(MutableRecoContextInterface *context,
                                 const std::vector<std::pair<int, CommonRecoResult>> &items,
                                                                               bool is_live,
                                            std::vector<std::string> * item_attr_pb_strings,
                                                       std::vector<std::string> * item_keys,
                                                 std::vector<std::string> * item_owner_keys,
                      std::vector<ks::reco::SampleJoinApiRequestEnum::ItemType> * item_types) {
  std::shared_ptr<std::map<std::string, std::string>> item_attr_names = item_attr_name_map_->Get();
  std::vector<std::pair<std::string, ItemAttr *>> attr_accessors;
  for (auto it = item_attr_names->begin(); it != item_attr_names->end(); it++) {
    attr_accessors.push_back(std::make_pair(it->second, context->GetItemAttrAccessor(it->first)));
  }
  for (int i = 0; i < items.size(); ++i) {
    thread_local kuiba::PredictItem item_attr;
    item_attr.Clear();
    for (int k = 0; k < attr_accessors.size(); ++k) {
      if (!interop::BuildSampleAttrFromItemAttr(items[i].second, attr_accessors[k].second,
        item_attr.mutable_attr(), attr_accessors[k].first)) {
        CL_LOG_EVERY_N(INFO, 5000) << "BuildItemAttrs cannot find item attr: " << attr_accessors[k].first;
      }
    }

    // rank index of item
    auto *attr = item_attr.add_attr();
    attr->set_name("item_rank");
    attr->set_type(kuiba::CommonSampleEnum::INT_ATTR);
    attr->set_int_value(items[i].first);

    // default label
    attr = item_attr.add_attr();
    attr->set_name("negative_show");
    attr->set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
    attr->set_float_value(1.0);

    // is_live
    attr = item_attr.add_attr();
    attr->set_name("is_live");
    attr->set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
    if (is_live) {
      attr->set_float_value(1.0);
    } else {
      attr->set_float_value(0.0);
    }

    // pId
    attr = item_attr.add_attr();
    attr->set_name(ks::action::AttrName::PID);
    attr->set_type(kuiba::CommonSampleEnum::INT_ATTR);
    attr->set_int_value(items[i].second.GetId());

    CL_LOG_EVERY_N(INFO, 5000) << "BuildItemAttrs item_attr size = " << item_attr.attr_size();

    // serialization
    item_attr_pb_strings->emplace_back(item_attr.SerializeAsString());

    // item_keys
    item_keys->push_back(base::Uint64ToString(items[i].second.GetId()));

    if (is_live) {
      item_types->emplace_back(ks::reco::SampleJoinApiRequestEnum::ITEM_TYPE_LIVESTREAM);
    } else {
      item_types->emplace_back(ks::reco::SampleJoinApiRequestEnum::ITEM_TYPE_PHOTO);
    }
  }
}


void NearbyRecoStageSampleJoinEnricher::BuildUserAttrs(MutableRecoContextInterface *context,
                                                       kuiba::PredictItem * user_attrs) {
    for (const auto& name :  GetStringListProcessorParameter(context, "slide_mc_clm_data")) {
        if (!interop::BuildSampleAttrFromCommonAttr(context, name, user_attrs->mutable_attr())) {
            CL_LOG_EVERY_N(ERROR, 500) << "BuildUserAttrs failed to get user attr: " << name;
    }
  }
}

void NearbyRecoStageSampleJoinEnricher::SendMessage(MutableRecoContextInterface *context,
                                                    const kuiba::PredictItem &user_attrs,
                                               const std::vector<std::string> &item_keys,
                                         const std::vector<std::string> &item_owner_keys,
                                    const std::vector<std::string> &item_attr_pb_strings,
             const std::vector<ks::reco::SampleJoinApiRequestEnum::ItemType> &item_types) {
  std::string common_key = GetCommonKey(context);
  int shard_num = 1;
  std::string bt_queue_name;
  bool send_one_request = true;
  ks::action::ProducerType producer_type = ks::action::ProducerType::KAFKA;
  ks::action::SampleJoinClient::Singleton()->SetAttr(
    bizname_, common_key, item_keys, user_attrs.SerializeAsString(), item_owner_keys,
    std::vector<std::string>(), item_attr_pb_strings, item_types, shard_num, bt_queue_name,
    context->GetRequestId(), base::GetTimestamp() / 1000, send_one_request, producer_type,
    kafka_topic_);
  base::perfutil::PerfUtilWrapper::CountLogStash(1,
    "reco.nearby.NearbyRecoStageSampleJoinEnricher", "SendMessage", bizname_);
  base::perfutil::PerfUtilWrapper::IntervalLogStash(item_keys.size(),
    "reco.nearby.NearbyRecoStageSampleJoinEnricher", "item_keys_size", bizname_);
  CL_LOG_EVERY_N(INFO, 5000) << "NearbyRecoStageSampleJoinEnricher SendMessage Success kafka_topic_ ="
            << kafka_topic_ <<" item_keys_size=" << item_keys.size()
            << " item_owner_keys="<< item_owner_keys.size()
            << " item_attr_pb_strings=" << item_attr_pb_strings.size()
            << " bizname=" << bizname_
            << " common_key=" << common_key
            << " RequestId=" << context->GetRequestId()
            << " item_owner_keys="<< item_owner_keys.size()
            << " item_attr_pb_strings=" << item_attr_pb_strings.size()
            << " RequestId=" << context->GetRequestId();
}


std::string NearbyRecoStageSampleJoinEnricher::GetActionLogBizName(ReadableRecoContextInterface *context) {
  // 新架构采用 request type 区分主站内外流,极速版
  static const std::unordered_map<std::string, std::string> request_type_to_biz = {
    {"official_nearby_main_feed_v1", "nearby"},
    {"official_nearby_slide_feed_photo", "nearby_in"},
    {"official_nearby_slide_feed_live", "nearby_in"},
    {"official_nearby_nebula_feed_v1", "bl_nearby"}
  };
  std::string request_type = context->GetRequestType();
  auto iFind = request_type_to_biz.find(request_type);
  if (iFind != request_type_to_biz.end()) {
    return iFind->second;
  }
  return request_type;
}

std::string NearbyRecoStageSampleJoinEnricher::GetActionLogBizName2(ReadableRecoContextInterface *context) {
  static const int64 U_PRODUCT_NEBULA = 1;
  static const int64 U_FEED_TYPE_SLIDE = 1;
  int64 uProductType = context->GetIntCommonAttr("uProductType").value_or(0);
  int64 uFeedType = context->GetIntCommonAttr("uFeedType").value_or(0);
  if (U_PRODUCT_NEBULA == uProductType) {
    if (U_FEED_TYPE_SLIDE == uFeedType) {
      return "bl_nearby_in";
    } else {
      return "bl_nearby";
    }
  } else {
    if (U_FEED_TYPE_SLIDE == uFeedType) {
      return "nearby_in";
    } else {
      return "nearby";
    }
  }
}

std::string NearbyRecoStageSampleJoinEnricher::GetCommonKey(ReadableRecoContextInterface *context) {
  // 通常是 user_id/device_id/request_id/llsid, 这里我们取 device_id
  return context->GetRequestId();
}

double NearbyRecoStageSampleJoinEnricher::GetReqSampleRate(const std::string &biz, bool is_live) {
  std::string key = biz;
  if (is_live) {
    key += ".live";
  } else {
    key += ".photo";
  }
  std::shared_ptr<std::map<std::string, double>> req_sample_rate = req_sample_rate_->Get();
  auto itFind = req_sample_rate->find(key);
  if (itFind != req_sample_rate->end()) {
    return itFind->second;
  }
  return 0.0;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, NearbyRecoStageSampleJoinEnricher, NearbyRecoStageSampleJoinEnricher)
}  // namespace platform
}  // namespace ks

