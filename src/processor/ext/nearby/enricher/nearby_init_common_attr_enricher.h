#pragma once

#include <string>
#include <vector>
#include <utility>
#include <unordered_set>
#include <unordered_map>
#include "folly/container/F14Map.h"

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "ks/reco_proto/proto/realtime_reco.pb.h"
#include "ks/reco_pub/reco/util/config_key.h"
#include "ks/reco_pub/reco/util/redis_index.h"

namespace ks {
namespace platform {

class NearbyInitCommonAttrEnricher : public CommonRecoBaseEnricher {
 public:
  NearbyInitCommonAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct RetrievalTriggerInfo {
    std::string name;
    std::vector<int64> trigger_list;
  };

 private:
  bool InitProcessor() override {
    i2i_source_list_.clear();
    // pure_i2i_source_list_.clear();
    const auto *i2i_source_list_config = config()->Get("i2i_source_list");
    // const auto *pure_i2i_source_list_config = config()->Get("pure_i2i_source_list");
    // if (pure_i2i_source_list_config && pure_i2i_source_list_config->IsArray()) {
    //   for (const auto *a : pure_i2i_source_list_config->array()) {
    //     pure_i2i_source_list_config.insert(a->StringValue());
    //   }
    // } else { // 默认召回源
    //   pure_i2i_source_list_ = {"NEGATIVE_SAMPLING_I2I_ACTION_LIST"};
    // }
    if (i2i_source_list_config && i2i_source_list_config->IsArray()) {
      for (const auto *a : i2i_source_list_config->array()) {
        i2i_source_list_.push_back(a->StringValue());
      }
    } else {  // 默认召回源
      i2i_source_list_ = {"NEGATIVE_SAMPLING_I2I_ACTION_LIST", "RECO_EMB_I2I_ACTION_LIST",
                      "SWING_PHOTO_I2I_ACTION_LIST"};
    }
    return true;
  }
  void EnrichFromClientDirectInfo(MutableRecoContextInterface *context);
  void EnrichCommonInfo(MutableRecoContextInterface *context, RecoResultConstIter begin,
        RecoResultConstIter end);
  void EnrichSimActionList(MutableRecoContextInterface *context, RecoResultConstIter begin,
        RecoResultConstIter end);
  void EnrichLiveSimActionList(MutableRecoContextInterface *context, RecoResultConstIter begin,
        RecoResultConstIter end);
  void EnrichTrigger(MutableRecoContextInterface *context, RecoResultConstIter begin,
        RecoResultConstIter end);
  void EnrichActionList(MutableRecoContextInterface *context, RecoResultConstIter begin,
        RecoResultConstIter end);
  void EnrichUserInfo(MutableRecoContextInterface *context, RecoResultConstIter begin,
        RecoResultConstIter end);
  void AddRetrievalTriggerInfo(std::vector<RetrievalTriggerInfo> *retrieval_trigger_info_list,
        std::string source);
  int64 IsGrUser(int64 user_id, int64 user_city_id);
  void TriggerSelectByRetrievalTypeReplenish(RetrievalTriggerInfo *trigger_info, uint64 photo_id);
  void FillUserLongTermHetuFeature(MutableRecoContextInterface *context,
      const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >& hetu_id,
      const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >& hetu_score,
      const std::string &name,
      int hetu_id_topn,
      int legal_threshold);
  void SortMapByValue(const std::unordered_map<int64, int>& mmap, std::vector<std::pair<int64, int>>* output);
  bool LoadHetuCateMap();
  bool IsHhLongViewV5(int64 playingTime, int64 duration);
  void AddSimActionToMap(int sim_tag, int64 sim_pid, int64 sim_aid, bool hate_label, bool report_label,
      int nearby_each_tag_max_sim_count);
  void EnrichSimTagActionList(MutableRecoContextInterface *context);
  void EnrichAuthorCoss(MutableRecoContextInterface *context);
  void EnrichStrongInterestTriggerWithTimeSeries(MutableRecoContextInterface *context);
  void EnrichNearbyActionTriggerList(MutableRecoContextInterface *context, RecoResultConstIter begin,
        RecoResultConstIter end);
  void EnrichNearbyActionTriggerList2(MutableRecoContextInterface *context, RecoResultConstIter begin,
        RecoResultConstIter end);
  void TriggerAssign(MutableRecoContextInterface *context,
                      const std::unordered_set<uint64> &action_photo_set,
                      const std::unordered_set<uint64> &all_action_photo_set,
                      const std::unordered_set<uint64> &strong_interest_set,
                      const std::unordered_set<uint64> &all_strong_interest_set,
                      std::vector<uint64> * pResult);
  int64 GetTabId(int64 uProductType, int64 uFeedType);
  void AddSingleFeature(MutableRecoContextInterface *context, const ks::reco::UserInfo &reco_user_info,
                        const ks::reco::LastActionInfo &last_action_info);

 private:
  std::vector<uint64> nearby_all_photo_action_ids_;
  std::vector<std::string> i2i_source_list_;
  // std::vector<std::string> pure_i2i_source_list_;
  std::mt19937 random_engine_;
  int same_province_trigger_priority_ = 0;
  int same_pid_filter_list_num = 0;
  int strong_interest_max_cnt_ = 10;
  int enable_nearby_recall_trigger_photo_supplement_ = 0;
  int nearby_return_user_shift_days_ = 0;
  std::unordered_set<uint64> action_photo_set_;
  std::unordered_set<uint64> strong_interest_set_;
  std::unordered_set<uint64> all_action_photo_set_;
  std::unordered_set<uint64> all_strong_interest_set_;
  std::unordered_map<uint64, uint32> action_photo_province_map_;
  folly::F14FastMap<int, std::vector<int64>> sim_tag_pid_map_;
  folly::F14FastMap<int, std::vector<int64>> sim_tag_aid_map_;
  folly::F14FastMap<int, std::vector<int64>> sim_tag_tag_map_;
  folly::F14FastMap<int, std::vector<int>> hetu_cate_map_;  // 河图回溯
  std::unordered_set<int64> sim_tag_set_;
  int new_weekly_zero_shot_user_ = 1;
  int new_daily_zero_shot_user_ = 1;
  int new_session_zero_shot_user_ = 1;
  ks::infra::RedisClient* redis_client_ = nullptr;

  DISALLOW_COPY_AND_ASSIGN(NearbyInitCommonAttrEnricher);
};

}  // namespace platform
}  // namespace ks

