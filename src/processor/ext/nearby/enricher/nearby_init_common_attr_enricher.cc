#include "dragon/src/processor/ext/nearby/enricher/nearby_init_common_attr_enricher.h"
#include <algorithm>
#include <limits>
#include <numeric>
#include <sstream>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>
#include "dragon/src/processor/ext/nearby/util/nearby_dragon_util.h"
#include "ks/realtime_reco/index/common_util.h"
#include "ks/reco_proto/proto/realtime_reco.pb.h"
#include "ks/util/location.h"
#include "serving_base/region/region_dict.h"

namespace ks {
namespace platform {

namespace {

std::string GetBizName(MutableRecoContextInterface *context) {
  static const int64 U_PRODUCT_NEBULA = 1;
  static const int64 U_FEED_TYPE_SLIDE = 1;
  std::string bizName("unknown");
  int64 uProductType = GetIntCommonAttrFromContext(context, "uProductType", 0);
  int64 uFeedType = GetIntCommonAttrFromContext(context, "uFeedType", 0);
  if (U_PRODUCT_NEBULA == uProductType) {
    if (U_FEED_TYPE_SLIDE == uFeedType) {
      bizName = "bl_nearby_in";
    } else {
      bizName = "bl_nearby";
    }
  } else {
    if (U_FEED_TYPE_SLIDE == uFeedType) {
      bizName = "nearby_in";
    } else {
      bizName = "nearby";
    }
  }
  return bizName;
}
}  // namespace

void NearbyInitCommonAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                                     RecoResultConstIter begin,
                                                     RecoResultConstIter end) {
  nearby_all_photo_action_ids_.clear();
  action_photo_set_.clear();
  strong_interest_set_.clear();
  all_action_photo_set_.clear();
  all_strong_interest_set_.clear();
  action_photo_province_map_.clear();
  hetu_cate_map_.clear();
  new_weekly_zero_shot_user_ = 1;
  new_daily_zero_shot_user_ = 1;
  new_session_zero_shot_user_ = 1;
  same_province_trigger_priority_ = GetIntCommonAttrFromContext(context,
                                  "same_province_trigger_priority", 0);
  strong_interest_max_cnt_ = GetIntCommonAttrFromContext(context,
                                  "ab_strong_interest_max_cnt", 10);
  same_pid_filter_list_num =
      GetIntCommonAttrFromContext(context, "same_pid_filter_list_num_aa", 0);  // aa 推全, ab 默认值 5000
  enable_nearby_recall_trigger_photo_supplement_ = GetIntCommonAttrFromContext(context,
    "enable_nearby_recall_trigger_photo_supplement", 0);
  nearby_return_user_shift_days_ = GetIntCommonAttrFromContext(context, "nearby_return_user_shift_days", 0);

  EnrichFromClientDirectInfo(context);
  EnrichCommonInfo(context, begin, end);
  EnrichSimActionList(context, begin, end);
  EnrichTrigger(context, begin, end);
  EnrichActionList(context, begin, end);
  EnrichUserInfo(context, begin, end);
  EnrichLiveSimActionList(context, begin, end);
  int enable_nearby_use_all_colossus_action_v2 = GetIntCommonAttrFromContext(context,
                                  "enable_nearby_use_all_colossus_action_v2", 0);
  if (enable_nearby_use_all_colossus_action_v2 > 0) {
    // EnrichNearbyActionTriggerList(context, begin, end);
    EnrichNearbyActionTriggerList2(context, begin, end);
  }
  std::string uBuyerEffectiveType = GetStringCommonAttrFromContext(context,
                                  "uBuyerEffectiveType", "");
  if (uBuyerEffectiveType == "U4" || uBuyerEffectiveType == "U4+") {
    context->SetIntCommonAttr("is_merchant_core_user", 1);
  }
  context->SetIntCommonAttr("new_weekly_zero_shot_user", new_weekly_zero_shot_user_);
  context->SetIntCommonAttr("new_daily_zero_shot_user", new_daily_zero_shot_user_);
  context->SetIntCommonAttr("new_session_zero_shot_user", new_session_zero_shot_user_);
}

void NearbyInitCommonAttrEnricher::EnrichFromClientDirectInfo(MutableRecoContextInterface *context) {
  int64 start_ts = base::GetTimestamp();
  std::string bizName = GetBizName(context);
  std::string client_direct_info
    = GetStringCommonAttrFromContext(context, "nearby_reco_user_info.client_direct_info", "");
  if (client_direct_info.length() > 0) {
    json_t * json_value = base::StringToJson(client_direct_info);
    base::Json parameter_json(json_value);
    if (parameter_json.IsObject() && !parameter_json.objects().empty()) {
      int64 out_request_offset = parameter_json.GetInt("out-request-offset", 0);
      int64 out_request_index = parameter_json.GetInt("out-request-index", 0);
      int64 slide_request_offset = parameter_json.GetInt("slide-request-offset", 0);
      int64 slide_request_index = parameter_json.GetInt("slide-request-index", 0);
      context->SetIntCommonAttr("out_request_offset", out_request_offset);
      context->SetIntCommonAttr("out_request_index", out_request_index);
      context->SetIntCommonAttr("slide_request_offset", slide_request_offset);
      context->SetIntCommonAttr("slide_request_index", slide_request_index);
    }
  }
  base::perfutil::PerfUtilWrapper::IntervalLogStash(base::GetTimestamp() - start_ts,
    "reco.nearby.NearbyInitCommonAttrEnricher", bizName,
    "EnrichFromClientDirectInfo", "total duration");
}

void NearbyInitCommonAttrEnricher::EnrichCommonInfo(MutableRecoContextInterface *context,
                                                     RecoResultConstIter begin,
                                                     RecoResultConstIter end) {
  // 1. 获取当天 0 时刻时间戳
  std::time_t now = std::time(nullptr);
  std::tm *localTime = std::localtime(&now);
  localTime->tm_hour = 0;
  localTime->tm_min = 0;
  localTime->tm_sec = 0;
  std::time_t midnight = std::mktime(localTime);
  std::chrono::system_clock::time_point midnightTimePoint =
      std::chrono::system_clock::from_time_t(midnight);
  uint64 midNightTimestampInMicro =
      std::chrono::duration_cast<std::chrono::microseconds>(midnightTimePoint.time_since_epoch()).count();
  context->SetIntCommonAttr("midNightTimestampInMicro", midNightTimestampInMicro);
  // 2.
}
void NearbyInitCommonAttrEnricher::EnrichUserInfo(MutableRecoContextInterface *context,
                                                     RecoResultConstIter begin,
                                                     RecoResultConstIter end) {
  int64 user_id = GetIntCommonAttrFromContext(context, "user_id", 0);
  int64 user_city_id = GetIntCommonAttrFromContext(context, "user_city_id", 0);
  int64 new_re_shift_days =
      GetIntCommonAttrFromContext(context, "nearby_reco_user_info.new_re_shift_days", -1);
  int enable_nearby_today_first_visit_undertake =
      GetIntCommonAttrFromContext(context, "enable_nearby_today_first_visit_undertake", 0);
  int nearby_today_first_visit = GetIntCommonAttrFromContext(context, "uNearbyTodayFirstVisit", 0);
  int64 is_app_nr_user = GetIntCommonAttrFromContext(context, "nearby_reco_user_info.is_app_nr_user", 0);
  base::perfutil::PerfUtilWrapper::IntervalLogStash(is_app_nr_user * 10000, "reco.nearby.NearbyNRUser",
                                                    "reco_nearby", "is_app_nr_user", "avg");
  int enable_mix_feed_live_degrade = GetIntCommonAttrFromContext(context, "enable_mix_feed_live_degrade", 0);
  context->SetIntCommonAttr("enable_mix_feed_live_degrade", enable_mix_feed_live_degrade);
  std::string redis_name = "nearbyLiveOpt";
  if (nullptr == redis_client_) {
    auto *client_mgr = ks::serving_util::InfraReidsClient::Singleton();
    if (client_mgr != nullptr) {
      redis_client_ = client_mgr->GetClientKccFromKconf(redis_name);
      if (!redis_client_) {
        CL_LOG(WARNING) << redis_name << ": client is null";
      }
    }
  }
  if (redis_client_) {
    // 构建 redis key
    // redis 里面只写入了大盘 30d 新回用户
    std::string redis_key = "nearby_new_reflux_30d_" + context->GetDeviceId();
    std::string redis_vaule = "";
    auto status = redis_client_->Get(redis_key, &redis_vaule);
    if (status == 0 && redis_vaule.size() > 0) {
      is_app_nr_user = 1;
    }
  }
  int64 is_neaby_nr_user = 0;
  if (user_id <= 0 || is_app_nr_user > 0) {
    if (enable_nearby_today_first_visit_undertake > 0) {
      if (nearby_today_first_visit > 0) {
        is_neaby_nr_user = 1;
      }
    } else {
      is_neaby_nr_user = 1;
    }
  }
  context->SetIntCommonAttr("is_nearby_nr_user", is_neaby_nr_user);
  base::perfutil::PerfUtilWrapper::IntervalLogStash(is_neaby_nr_user * 10000, "reco.nearby.NearbyNRUser",
                                                    "reco_nearby", "is_nearby_nr_user", "avg");

  if (user_city_id <= 0) {
    double user_lat = GetDoubleCommonAttrFromContext(context, "nearby_reco_user_info.lat", 0.0);
    double user_lon = GetDoubleCommonAttrFromContext(context, "nearby_reco_user_info.lon", 0.0);
    std::string ip_region = GetStringCommonAttrFromContext(context, "nearby_reco_user_info.ip_region", "");
    uint32 user_region_id = ks::reco::GetRegionId(ip_region, user_lat, user_lon);
    user_city_id = base::RegionDict::LogicCityId(user_region_id);
  }
  int64 is_gr_user = IsGrUser(user_id, user_city_id);
  context->SetIntCommonAttr("is_gr_user", is_gr_user);

  auto nearby_scene_reco_str = context->GetStringCommonAttr("nearby_reco_user_info.nearby_scene_reco");
  ks::reco::NearbySceneReco nearby_scene_reco;
  if (nearby_scene_reco_str) {
    nearby_scene_reco.ParseFromArray(nearby_scene_reco_str->data(), nearby_scene_reco_str->size());
  }
  int64 is_nearby_migrate_user = (nearby_scene_reco.scene_type() ==
    ks::reco::NearbySceneReco_SceneType::NearbySceneReco_SceneType_MIGRATE) ? 1 : 0;
  context->SetIntCommonAttr("is_nearby_migrate_user", is_nearby_migrate_user);
  int user_active_level = GetIntCommonAttrFromContext(context,
                              "nearby_reco_user_info.nearby_user_active_level", 0);
  int is_unengaged_user = user_active_level <= 1 ? 1 : 0;
  context->SetIntCommonAttr("is_unengaged_user", is_unengaged_user);
  context->SetIntCommonAttr("uUserActiveLevel", user_active_level);

  std::string location_name = ks::reco::GetCityName(user_city_id);
    // 城市扩大范围实验
  auto sparse_diff_location_map =
          *ks::reco::RecoConfigKey::reco_nearby_sparseDiffLocationDistanceMap()->Get();
  double live_unrestraint_max_distance_km = 50.0;
  auto sparse_iter = sparse_diff_location_map.find(location_name);
  if (sparse_iter != sparse_diff_location_map.end()) {
    live_unrestraint_max_distance_km = std::max(
        live_unrestraint_max_distance_km, sparse_iter->second);
  }
  // last feed info
  std::vector<int64> last_session_feed_list;
  std::vector<int64> last_session_photo_list;
  std::vector<int64> last_session_live_list;
  auto last_session_feed_infos_str =
    context->GetStringCommonAttr("nearby_reco_user_info.last_session_feed_infos");
  ks::reco::LastSessionFeedInfos last_session_feed_infos;
  if (last_session_feed_infos_str) {
    last_session_feed_infos.ParseFromArray(last_session_feed_infos_str->data(),
                                  last_session_feed_infos_str->size());
    int size = last_session_feed_infos.last_session_feeds().size();
    if (size > 0) {
      std::unordered_set<int64> browsed_set;
      const CommonRecoRequest *common_request = context->GetRequest();
      if (common_request) {
        for (int i = 0; i < common_request->browse_set().browsed_ids_size(); ++i) {
          browsed_set.insert(common_request->browse_set().browsed_ids(i));
        }
      }
      for (int i = 0; i < size; ++i) {
        auto item = last_session_feed_infos.last_session_feeds(i);
        if (!browsed_set.count(item.item_id())) {
          continue;
        }
        if (item.feed_type() == 1) {
          last_session_photo_list.push_back(item.item_key());
        } else {
          last_session_live_list.push_back(item.item_key());
        }
        last_session_feed_list.push_back(item.item_key());
      }
    }
  }
  context->SetIntListCommonAttr("last_session_feed_list", std::move(last_session_feed_list));
  context->SetIntListCommonAttr("last_session_photo_list", std::move(last_session_photo_list));
  context->SetIntListCommonAttr("last_session_live_list", std::move(last_session_live_list));
  // session info
  int32 source_feed_type = GetIntCommonAttrFromContext(context, "uInnerSourceFeedType", 0);
  auto nearby_session_info_str =
    context->GetStringCommonAttr("nearby_reco_user_info.nearby_session_info");
  if (source_feed_type > 0) {
    nearby_session_info_str = context->GetStringCommonAttr("nearby_reco_user_info.nearby_in_session_info");
  }
  ks::reco::NearbySessionInfo nearby_session_info;
  if (nearby_session_info_str) {
    nearby_session_info.ParseFromArray(nearby_session_info_str->data(),
                                  nearby_session_info_str->size());
    SetUserDiversityParamByKconf(nearby_session_info, context);
    SetUserHetuPerferParam(nearby_session_info, context);
  }
  auto nearby_user_uni_count_info_str = context->GetStringCommonAttr(
        "nearby_reco_user_info.nearby_user_uni_count_info");
  if (nearby_user_uni_count_info_str) {
    ks::reco::NearbyUserUniCountInfo nearby_user_uni_count_info;
    nearby_user_uni_count_info
      .ParseFromArray(nearby_user_uni_count_info_str->data(), nearby_user_uni_count_info_str->size());
    int nearby_share_suc = nearby_user_uni_count_info.nearby_photo_uni_feature_35days().share_suc();
    int nearby_share_outsite = nearby_user_uni_count_info.nearby_photo_uni_feature_35days().share_outsite();
    int app_share_suc = nearby_user_uni_count_info.app_photo_uni_feature_35days().share_suc();
    int app_share_outsite = nearby_user_uni_count_info.app_photo_uni_feature_35days().share_outsite();
    context->SetIntCommonAttr("nearby_share_suc_cnt_35d", nearby_share_suc);
    context->SetIntCommonAttr("nearby_share_outsite_cnt_35d", nearby_share_outsite);
    context->SetIntCommonAttr("app_share_suc_cnt_35d", app_share_suc);
    context->SetIntCommonAttr("app_share_outsite_cnt_35d", app_share_outsite);
  }
  if (GetIntCommonAttrFromContext(context, "page_offset", 0) > 0
      && GetIntCommonAttrFromContext(context, "uInnerSourceFeedType", 0) == 1
      && GetIntCommonAttrFromContext(context, "enbale_slide_realtime_tag_alter", 0) > 0) {
    auto slide_one_streaming_info_str =
      context->GetStringCommonAttr("nearby_reco_user_info.slide_one_streaming_info");
    ks::reco::SlideOneStreamingInfo slide_one_streaming_info;
    if (slide_one_streaming_info_str) {
      slide_one_streaming_info.ParseFromArray(slide_one_streaming_info_str->data(),
                                    slide_one_streaming_info_str->size());
    }
    int64 skip_slide_same_tag_filter = slide_one_streaming_info.is_tag_constraint() ? 1 : 0;
    context->SetIntCommonAttr("skip_slide_same_tag_filter", skip_slide_same_tag_filter);
    int size = slide_one_streaming_info.nearby_slide_feed_infos().size();
    int add_num = 0;
    std::vector<int64> nearby_slide_last_action_pids;
    std::vector<int64> nearby_slide_last_action_aids;
    std::vector<int64> nearby_slide_last_action_islv;
    std::vector<int64> nearby_slide_last_action_issv;
    std::vector<int64> nearby_slide_last_action_islike;
    std::vector<int64> nearby_slide_last_action_isfollow;
    std::vector<int64> nearby_slide_last_action_ht2;
    for (int i = size - 1; i >= 0; i--) {
      if (add_num >= 8) {
        break;
      }
      auto& nearby_slide_feed_info = slide_one_streaming_info.nearby_slide_feed_infos(i);
      if (nearby_slide_feed_info.feed_type() == 2) {
        continue;
      }
      nearby_slide_last_action_pids.push_back(nearby_slide_feed_info.feed_id());
      nearby_slide_last_action_aids.push_back(nearby_slide_feed_info.author_id());
      nearby_slide_last_action_ht2.push_back(nearby_slide_feed_info.hetu_tag_two());
      nearby_slide_last_action_islv.push_back(nearby_slide_feed_info.is_long_view() ? 1 : 2);
      nearby_slide_last_action_issv.push_back(nearby_slide_feed_info.is_short_view() ? 1 : 2);
      nearby_slide_last_action_islike.push_back(nearby_slide_feed_info.is_like() ? 1 : 2);
      nearby_slide_last_action_isfollow.push_back(nearby_slide_feed_info.is_follow() ? 1 : 2);
      add_num++;
    }
    context->SetIntListCommonAttr("nearby_slide_last_action_pids", std::move(nearby_slide_last_action_pids));
    context->SetIntListCommonAttr("nearby_slide_last_action_aids", std::move(nearby_slide_last_action_aids));
    context->SetIntListCommonAttr("nearby_slide_last_action_ht2", std::move(nearby_slide_last_action_ht2));
    context->SetIntListCommonAttr("nearby_slide_last_action_islv", std::move(nearby_slide_last_action_islv));
    context->SetIntListCommonAttr("nearby_slide_last_action_issv", std::move(nearby_slide_last_action_issv));
    context->SetIntListCommonAttr("nearby_slide_last_action_islike",
        std::move(nearby_slide_last_action_islike));
    context->SetIntListCommonAttr("nearby_slide_last_action_isfollow",
        std::move(nearby_slide_last_action_isfollow));
  }
  int nearby_slide_user_type = GetIntCommonAttrFromContext(context, "nearby_slide_user_type", 0);
  if (GetIntCommonAttrFromContext(context, "enable_severe_slide_user_skip_slide_same_tag_filter", 0) > 0
  && nearby_slide_user_type > 2 && GetIntCommonAttrFromContext(context, "uInnerSourceFeedType", 0) == 1) {
    context->SetIntCommonAttr("skip_slide_same_tag_filter", 1);
  }
}

void NearbyInitCommonAttrEnricher::EnrichActionList(MutableRecoContextInterface *context,
                                                    RecoResultConstIter begin, RecoResultConstIter end) {
  int64 start_ts = base::GetTimestamp();
  auto reco_user_info_ptr =
      context->GetProtoMessagePtrCommonAttr<ks::reco::UserInfo>(
        "nearby_reco_user_info.user_info_proto");
  if (reco_user_info_ptr == nullptr) {
    return;
  }
  const ks::reco::UserInfo& reco_user_info = *reco_user_info_ptr;
  if (reco_user_info.has_user_profile_v1()) {
    std::vector<int64> same_pid_realshow_filter_pids;
    std::vector<int64> same_pid_play_filter_pids;
    std::vector<int64> same_pid_like_filter_pids;
    std::vector<int64> same_pid_follow_filter_pids;
    std::vector<int64> same_pid_comment_filter_pids;
    auto enable_same_pids_real_show_list_filter =
        GetIntCommonAttrFromContext(context, "enable_same_pids_real_show_list_filter", 0);
    auto enable_same_pids_play_list_filter =
        GetIntCommonAttrFromContext(context, "enable_same_pids_play_list_filter", 0);
    auto enable_same_pids_like_list_filter =
        GetIntCommonAttrFromContext(context, "enable_same_pids_like_list_filter", 0);
    auto enable_same_pids_follow_list_filter =
        GetIntCommonAttrFromContext(context, "enable_same_pids_follow_list_filter", 0);
    auto enable_same_pids_comment_list_filter =
        GetIntCommonAttrFromContext(context, "enable_same_pids_comment_list_filter", 0);
    auto enable_same_pids_filter_page_type_controlled =
        GetIntCommonAttrFromContext(context, "enable_same_pids_filter_page_type_controlled", 0);
    auto enable_same_pids_filter_monitor =
        GetIntCommonAttrFromContext(context, "enable_same_pids_filter_monitor", 0);

    const ks::reco::UserProfileV1& user_photo_profile = reco_user_info.user_profile_v1();
    if (same_pid_filter_list_num > 0) {
      std::unordered_set<int> scene_page = {2, 9, 14, 25, 56};  // 默认仅对同城内容过滤
      if (enable_same_pids_filter_page_type_controlled > 0) {   // 过滤页面可选
        auto optional_page_type = context->GetIntListCommonAttr("nearby_same_pids_filter_page_type_list");
        if (optional_page_type) {
          scene_page.clear();
          for (auto &item : *optional_page_type) {
            scene_page.insert(item);
          }
        }
      }
      if (enable_same_pids_real_show_list_filter > 0) {
        auto rs_list_size = user_photo_profile.real_show_list().size();
        for (int i = 0; i < rs_list_size && i < same_pid_filter_list_num; i++) {
          auto real_show_item = user_photo_profile.real_show_list(i);
          int page_type = real_show_item.page_type();
          if (scene_page.count(page_type)) {
            same_pid_realshow_filter_pids.push_back(real_show_item.photo_id());
          }
        }
      }
      if (enable_same_pids_play_list_filter > 0) {
        for (int i = 0; i < user_photo_profile.video_playing_stat_size() && i < same_pid_filter_list_num;
             i++) {
          auto play_item = user_photo_profile.video_playing_stat(i);
          int page_type = play_item.page();
          if (scene_page.count(page_type)) {
            same_pid_play_filter_pids.push_back(play_item.photo_id());
          }
        }
      }

      for (int i = 0; i < user_photo_profile.like_list_size() && i < same_pid_filter_list_num; i++) {
        auto like_item = user_photo_profile.like_list(i);
        int page_type = like_item.page_type();
        if (scene_page.count(page_type)) {
          same_pid_like_filter_pids.push_back(like_item.photo_id());
        }
      }

      for (int i = 0; i < user_photo_profile.follow_list_size() && i < same_pid_filter_list_num; i++) {
        auto follow_item = user_photo_profile.follow_list(i);
        int page_type = follow_item.page_type();
        if (scene_page.count(page_type)) {
          same_pid_follow_filter_pids.push_back(follow_item.photo_id());
        }
      }

      for (int i = 0; i < user_photo_profile.comment_list_size() && i < same_pid_filter_list_num; i++) {
        auto comment_item = user_photo_profile.comment_list(i);
        int page_type = comment_item.page_type();
        if (scene_page.count(page_type)) {
          same_pid_comment_filter_pids.push_back(comment_item.photo_id());
        }
      }
    }
    base::perfutil::PerfUtilWrapper::IntervalLogStash(same_pid_realshow_filter_pids.size(),
                                                      "reco.nearby.NearbyInitSamePidsSize", "reco_nearby",
                                                      "realshow");
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        same_pid_play_filter_pids.size(), "reco.nearby.NearbyInitSamePidsSize", "reco_nearby", "play");
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        same_pid_like_filter_pids.size(), "reco.nearby.NearbyInitSamePidsSize", "reco_nearby", "like");
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        same_pid_follow_filter_pids.size(), "reco.nearby.NearbyInitSamePidsSize", "reco_nearby", "follow");
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        same_pid_comment_filter_pids.size(), "reco.nearby.NearbyInitSamePidsSize", "reco_nearby", "comment");
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        context->GetBrowseSetSize(), "reco.nearby.NearbyInitSamePidsSize", "reco_nearby", "browseSize");
    // 判定是否有 pid 在 action list 而不在 browset 中
    // realshow list
    int inRealShowNotInBrowseSet =
        std::count_if(same_pid_realshow_filter_pids.begin(), same_pid_realshow_filter_pids.end(),
                      [&context](int64_t pid) { return !context->InBrowseSet(pid); });
    int inPlayNotInBrowseSet =
        std::count_if(same_pid_play_filter_pids.begin(), same_pid_play_filter_pids.end(),
                      [&context](int64_t pid) { return !context->InBrowseSet(pid); });
    int inLikeNotInBrowseSet =
        std::count_if(same_pid_like_filter_pids.begin(), same_pid_like_filter_pids.end(),
                      [&context](int64_t pid) { return !context->InBrowseSet(pid); });
    int inFollowNotInBrowseSet =
        std::count_if(same_pid_follow_filter_pids.begin(), same_pid_follow_filter_pids.end(),
                      [&context](int64_t pid) { return !context->InBrowseSet(pid); });
    int inCommentNotInBrowseSet =
        std::count_if(same_pid_comment_filter_pids.begin(), same_pid_comment_filter_pids.end(),
                      [&context](int64_t pid) { return !context->InBrowseSet(pid); });
    if (enable_same_pids_filter_monitor > 0) {
      if (inRealShowNotInBrowseSet > 0) {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(inRealShowNotInBrowseSet,
                                                          "reco.nearby.NearbyInitSamePidsMonitor",
                                                          "reco_nearby", "inRealShowNotInBrowseSet", "size");
        base::perfutil::PerfUtilWrapper::IntervalLogStash(same_pid_realshow_filter_pids.size(),
                                                          "reco.nearby.NearbyInitSamePidsMonitor",
                                                          "reco_nearby", "realShowListSize", "size");
        base::perfutil::PerfUtilWrapper::IntervalLogStash(context->GetBrowseSetSize(),
                                                          "reco.nearby.NearbyInitSamePidsMonitor",
                                                          "reco_nearby", "realShowBrowseSet", "size");
      }
      if (inPlayNotInBrowseSet > 0) {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(inPlayNotInBrowseSet,
                                                          "reco.nearby.NearbyInitSamePidsMonitor",
                                                          "reco_nearby", "inPlayNotInBrowseSet", "size");
        base::perfutil::PerfUtilWrapper::IntervalLogStash(same_pid_play_filter_pids.size(),
                                                          "reco.nearby.NearbyInitSamePidsMonitor",
                                                          "reco_nearby", "playListSize", "size");
        base::perfutil::PerfUtilWrapper::IntervalLogStash(context->GetBrowseSetSize(),
                                                          "reco.nearby.NearbyInitSamePidsMonitor",
                                                          "reco_nearby", "playBrowseSet", "size");
      }
      if (inLikeNotInBrowseSet > 0) {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(inLikeNotInBrowseSet,
                                                          "reco.nearby.NearbyInitSamePidsMonitor",
                                                          "reco_nearby", "inLikeNotInBrowseSet", "size");
        base::perfutil::PerfUtilWrapper::IntervalLogStash(same_pid_like_filter_pids.size(),
                                                          "reco.nearby.NearbyInitSamePidsMonitor",
                                                          "reco_nearby", "likeListSize", "size");
        base::perfutil::PerfUtilWrapper::IntervalLogStash(context->GetBrowseSetSize(),
                                                          "reco.nearby.NearbyInitSamePidsMonitor",
                                                          "reco_nearby", "likeBrowseSet", "size");
      }
      if (inFollowNotInBrowseSet > 0) {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(inFollowNotInBrowseSet,
                                                          "reco.nearby.NearbyInitSamePidsMonitor",
                                                          "reco_nearby", "inFollowNotInBrowseSet", "size");
        base::perfutil::PerfUtilWrapper::IntervalLogStash(same_pid_follow_filter_pids.size(),
                                                          "reco.nearby.NearbyInitSamePidsMonitor",
                                                          "reco_nearby", "followListSize", "size");
        base::perfutil::PerfUtilWrapper::IntervalLogStash(context->GetBrowseSetSize(),
                                                          "reco.nearby.NearbyInitSamePidsMonitor",
                                                          "reco_nearby", "followBrowseSet", "size");
      }
      if (inCommentNotInBrowseSet > 0) {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(inCommentNotInBrowseSet,
                                                          "reco.nearby.NearbyInitSamePidsMonitor",
                                                          "reco_nearby", "inCommentNotInBrowseSet", "size");
        base::perfutil::PerfUtilWrapper::IntervalLogStash(same_pid_comment_filter_pids.size(),
                                                          "reco.nearby.NearbyInitSamePidsMonitor",
                                                          "reco_nearby", "commentListSize", "size");
        base::perfutil::PerfUtilWrapper::IntervalLogStash(context->GetBrowseSetSize(),
                                                          "reco.nearby.NearbyInitSamePidsMonitor",
                                                          "reco_nearby", "commentBrowseSet", "size");
      }
    }
    context->SetIntListCommonAttr("same_pid_realshow_filter_pids", std::move(same_pid_realshow_filter_pids));
    context->SetIntListCommonAttr("same_pid_play_filter_pids", std::move(same_pid_play_filter_pids));
    context->SetIntListCommonAttr("same_pid_like_filter_pids", std::move(same_pid_like_filter_pids));
    context->SetIntListCommonAttr("same_pid_follow_filter_pids", std::move(same_pid_follow_filter_pids));
    context->SetIntListCommonAttr("same_pid_comment_filter_pids", std::move(same_pid_comment_filter_pids));
    base::perfutil::PerfUtilWrapper::IntervalLogStash(base::GetTimestamp() - start_ts,
                                                      "reco.nearby.nearbyInitCommonArrtEnricher",
                                                      "reco_nearby", "EnrichActionList.duration", "total");
  }
  int today_no_play = 1;  // 今日是否同城零播
  int today_no_play_v2 = -1;  // 今日是否同城零播
  int today_real_show = 0;  //  今日曝光个数
  static const std::unordered_set<int> nearby_page = {2, 9, 14, 25, 56};
    // NEARBY_PAGE=2;NEARBY_SLIDE_PAGE=9;UNLOGIN_NEARBY_PAGE=14;BL_NEARBY_PAGE=25;NEARBY_INDISE_PAGE=56;
  uint64 midnightTimestampInMicroseconds =
    GetIntCommonAttrFromContext(context, "midNightTimestampInMicro", 0);
  uint64 today_real_show_bound =
    GetIntCommonAttrFromContext(context, "nearby_today_real_show_bound", 0);
  if (reco_user_info.has_user_profile_v1()) {
    today_no_play_v2 = 1;
    int64 cur_ts = base::GetTimestamp();
    std::string client_direct_info
      = GetStringCommonAttrFromContext(context, "nearby_reco_user_info.client_direct_info", "");
    if (client_direct_info != "") {
      json_t *json_value = base::StringToJson(client_direct_info);
      base::Json parameter_json(json_value);
      if (parameter_json.IsObject() && !parameter_json.objects().empty()) {
        uint64 app_ts = parameter_json.GetInt("ts", 0) * 1000;       // ms -> us
        uint64 nearby_cnt = parameter_json.GetInt("nearby_cnt", 0);  // 当天访问同城次数
        if (app_ts > 0 && ((cur_ts - app_ts) < 5 * 60 * 1000 * 1000)) {
          // midnightTimestampInMicroseconds = app_ts * 1000;
          base::perfutil::PerfUtilWrapper::CountLogStash(1, "reco.nearby", "nearby_today_no_play_flag1.count",
            "up_ts");
          context->SetIntCommonAttr("uNearbyEnterAppTs", app_ts);
        }
        if (nearby_cnt > 0) {
          context->SetIntCommonAttr("uNearbyTodayEnterCnt", nearby_cnt);
        }
        uint64 nearby_ts = parameter_json.GetInt("ts", 0) * 1000;
        if ((nearby_ts <= 0 || (cur_ts - nearby_ts) < 2 * 1000 * 1000) && nearby_cnt <= 1) {
          context->SetIntCommonAttr("uNearbyTodayFirstVisit", 1);
        } else {
          context->SetIntCommonAttr("uNearbyTodayFirstVisit", 0);
        }
      }
    }
    std::string server_direct_info
      = GetStringCommonAttrFromContext(context, "nearby_reco_user_info.server_direct_info", "");
    bool enable_trial_version = false;
    if (server_direct_info != "") {
      json_t *json_value = base::StringToJson(server_direct_info);
      base::Json parameter_json(json_value);
      if (parameter_json.IsObject() && !parameter_json.objects().empty()) {
        enable_trial_version = parameter_json.GetBoolean("enableTrialVersion", false);
      }
    }
    if (enable_trial_version) {
      context->SetIntCommonAttr("isTrialVersion", 1);
    } else {
      context->SetIntCommonAttr("isTrialVersion", 0);
    }
    ks::reco::UserProfileV1 user_photo_profile = reco_user_info.user_profile_v1();
    for (int i = 0; i < user_photo_profile.real_show_list_size() && i < 1000; i++) {
      auto real_show_list = user_photo_profile.real_show_list(i);
      int page_type = real_show_list.page_type();
      uint64 time_ms = real_show_list.time_ms();
      if (time_ms * 1000 < midnightTimestampInMicroseconds) {
        break;
      }
      if (nearby_page.count(page_type)) {
        ++today_real_show;
      }
    }
    for (int i = 0; i < user_photo_profile.video_playing_stat_size() && i < 200; i++) {
      auto video_playing_stat = user_photo_profile.video_playing_stat(i);
      int page_type = video_playing_stat.page();
      uint64 time_ms = video_playing_stat.client_timestamp();
      if (time_ms * 1000 < midnightTimestampInMicroseconds) {
        break;
      }
      if (nearby_page.count(page_type)) {
        today_no_play = 0;
        today_no_play_v2 = 0;
        break;
      }
    }
    if (today_no_play > 0) {
      for (int i = 0; i < user_photo_profile.click_list_size() && i < 200; i++) {
        auto click_list = user_photo_profile.click_list(i);
        int page_type = click_list.page_type();
        uint64 time_ms = click_list.time_ms();
        if (time_ms * 1000 < midnightTimestampInMicroseconds) {
          break;
        }
        if (nearby_page.count(page_type)) {
          today_no_play_v2 = 0;
          base::perfutil::PerfUtilWrapper::CountLogStash(1, "reco.nearby", "nearby_today_no_play_flag.count",
            "photo_click");
          break;
        }
      }
    }
  }
  if (today_no_play > 0) {  // 视频零播, 处理直播
    if (reco_user_info.has_live_profile_v1()) {
      int live_index = 0;
      for (const auto &item : reco_user_info.live_profile_v1().live_realshow_list()) {
        if ((item.time_ms() * 1000) < midnightTimestampInMicroseconds) {
          break;
        }
        if (nearby_page.count(item.page_type())) {
          ++today_real_show;
        }
      }
      for (const auto &item : reco_user_info.live_profile_v1().live_play_list()) {
        if (item.client_timestamp() * 1000 < midnightTimestampInMicroseconds) {
          break;
        }
        if (nearby_page.count(item.page_type())) {
          today_no_play = 0;
          today_no_play_v2 = 0;
          break;
        }
        if (live_index > 200) {
          break;
        }
        live_index++;
      }
      for (const auto &item : reco_user_info.live_profile_v1().live_click_list()) {
        if ((item.time_ms() * 1000) < midnightTimestampInMicroseconds) {
          break;
        }
        if (nearby_page.count(item.page_type())) {
          today_no_play_v2 = 0;
          break;
        }
      }
    }
  }
  if (today_no_play_v2 > 0 && today_real_show < today_real_show_bound) {
    // 今日 relashow 量不够，先不判定
    today_no_play_v2 = 0;
    base::perfutil::PerfUtilWrapper::CountLogStash(1, "reco.nearby", "nearby_today_no_play_flag.count",
      "today_real_show." + std::to_string(today_real_show_bound));
  }
  context->SetIntCommonAttr("uNearbyTodayNoPlay", today_no_play);
  context->SetIntCommonAttr("nearby_today_no_play_flag", today_no_play_v2);

  base::perfutil::PerfUtilWrapper::CountLogStash(1, "reco.nearby", "nearby_today_no_play_flag.count",
    "user_info_falg." + std::to_string(today_no_play_v2));
  base::perfutil::PerfUtilWrapper::IntervalLogStash(
      today_no_play * 1000000, "reco.nearby.NearbyInitCommonAttrEnricher", "no_play", "today_no_play", "avg");

  auto colossus_photo_id = context->GetIntListCommonAttr("colossus_photo_id");
  auto colossus_author_id = context->GetIntListCommonAttr("colossus_author_id");
  auto colossus_timestamp = context->GetIntListCommonAttr("colossus_timestamp");
  auto colossus_label = context->GetIntListCommonAttr("colossus_label");
  auto colossus_play_time = context->GetIntListCommonAttr("colossus_play_time");

  if (!colossus_photo_id || !colossus_author_id || !colossus_timestamp || !colossus_label ||
      !colossus_play_time) {
    return;
  }

  bool size_match = colossus_photo_id->size() == colossus_timestamp->size() &&
                    colossus_photo_id->size() == colossus_author_id->size() &&
                    colossus_photo_id->size() == colossus_label->size() &&
                    colossus_photo_id->size() == colossus_play_time->size();
  if (!size_match) {
    return;
  }
  std::unordered_set<int64> positive_aid_list_recent_7d_set;
  std::unordered_set<int64> interaction_aid_list_recent_7d_set;
  uint64 week_expired_ms = base::GetTimestamp() - base::Time::kMicrosecondsPerDay * 7;
  for (int i = colossus_photo_id->size() - 1; i >= 0; i--) {
    if (colossus_timestamp->at(i) * base::Time::kMicrosecondsPerSecond >= week_expired_ms) {
      uint32 item_label = colossus_label->at(i);
      bool like_label = item_label & 0x01;
      bool follow_label = item_label & (1 << 1);
      bool comment_label = item_label & (1 << 4);
      uint32 play_time_seconds = colossus_play_time->at(i);
      int64 author_id = colossus_author_id->at(i);
      if (like_label || follow_label || comment_label || play_time_seconds > 7) {
        if (positive_aid_list_recent_7d_set.size() < 1000) {
          positive_aid_list_recent_7d_set.insert(author_id);
        }
      }
      if (like_label || follow_label || comment_label) {
        if (interaction_aid_list_recent_7d_set.size() < 1000) {
          interaction_aid_list_recent_7d_set.insert(author_id);
        }
      }
    } else {
      break;
    }
  }
  std::vector<int64> positive_aid_list_recent_7d;
  std::vector<int64> interaction_aid_list_recent_7d;
  positive_aid_list_recent_7d.assign(positive_aid_list_recent_7d_set.begin(),
                                     positive_aid_list_recent_7d_set.end());
  interaction_aid_list_recent_7d.assign(interaction_aid_list_recent_7d_set.begin(),
                                        interaction_aid_list_recent_7d_set.end());

  base::perfutil::PerfUtilWrapper::IntervalLogStash(positive_aid_list_recent_7d.size() * 1000000,
                                                    "reco.nearby.NearbyInitCommonAttrEnricher", "pos_aid",
                                                    "length", "avg");
  base::perfutil::PerfUtilWrapper::IntervalLogStash(interaction_aid_list_recent_7d.size() * 1000000,
                                                    "reco.nearby.NearbyInitCommonAttrEnricher",
                                                    "interaction_aid", "length", "avg");
  context->SetIntListCommonAttr("positive_aid_list_recent_7d", std::move(positive_aid_list_recent_7d));
  context->SetIntListCommonAttr("interaction_aid_list_recent_7d", std::move(interaction_aid_list_recent_7d));
}
void NearbyInitCommonAttrEnricher::EnrichTrigger(MutableRecoContextInterface *context,
                                                     RecoResultConstIter begin,
                                                     RecoResultConstIter end) {
  // add last_action_info
  auto last_action_info_str = context->GetStringCommonAttr(
    "nearby_reco_user_info.last_action_info");

  ks::reco::LastActionInfo last_action_info;
  if (last_action_info_str) {
    last_action_info.ParseFromArray(last_action_info_str->data(), last_action_info_str->size());
  }
  std::unordered_set<int64> last_action_photo_set;
  std::unordered_set<int64> last_action_live_set;

  for (auto &feed : last_action_info.last_clicked_feed()) {
    feed.feed_type() == 1 ? last_action_photo_set.insert(feed.feed_id())
                : last_action_live_set.insert(feed.feed_id());
  }
  for (auto &feed : last_action_info.last_liked_feed()) {
    feed.feed_type() == 1 ? last_action_photo_set.insert(feed.feed_id())
                : last_action_live_set.insert(feed.feed_id());
  }
  for (auto &feed : last_action_info.last_followed_feed()) {
    feed.feed_type() == 1 ? last_action_photo_set.insert(feed.feed_id())
                : last_action_live_set.insert(feed.feed_id());
  }
  for (auto &feed : last_action_info.last_long_view_feed()) {
    feed.feed_type() == 1 ? last_action_photo_set.insert(feed.feed_id())
                : last_action_live_set.insert(feed.feed_id());
  }
  std::vector<int64> last_action_photo_list;
  last_action_photo_list.assign(last_action_photo_set.begin(), last_action_photo_set.end());
  std::vector<int64> last_action_live_list;
  last_action_live_list.assign(last_action_live_set.begin(), last_action_live_set.end());
  context->SetIntListCommonAttr("last_action_positive_photo_ids", std::move(last_action_photo_list));
  context->SetIntListCommonAttr("last_action_positive_live_ids", std::move(last_action_live_list));
  // add nearby_longterm_photo_action_ids_offline
  std::unordered_set<int64> longterm_photo_set;
  std::vector<int64> nearby_longterm_photo_action_ids_offline;
  auto sim_photo_ids = context->GetIntListCommonAttr("nearby_reco_user_info.sim_photo_ids");
  auto history_mining_photo_ids =
        context->GetIntListCommonAttr("nearby_reco_user_info.history_mining_photo_ids");
  if (sim_photo_ids) {
    for (auto &item : *sim_photo_ids) {
      if (longterm_photo_set.count(item)) {
        continue;
      }
      nearby_longterm_photo_action_ids_offline.push_back(item);
      longterm_photo_set.insert(item);
    }
  }
  if (history_mining_photo_ids) {
    for (auto &item : *history_mining_photo_ids) {
      if (longterm_photo_set.count(item)) {
        continue;
      }
      nearby_longterm_photo_action_ids_offline.push_back(item);
      longterm_photo_set.insert(item);
    }
  }
  context->SetIntListCommonAttr("uNearbyLongTermActionPidOffline",
                                std::move(nearby_longterm_photo_action_ids_offline));
  // add action list
  static const std::unordered_set<std::string> si_action_set = {"uLikeListNear", "uLongPlayListNear",
      "uLikeListNearInside", "uLikeListBlNear", "last_feed_action_list"};
  static const std::vector<std::string> valid_nearby_photo_action_set = {
      "uClickListNear", "uLikeListNear", "uLongPlayListNear", "uClickListBlNear", "uLikeListNearInside",
      "uLongPlayListNearInside", "uNearbyLongTermActionPidOffline", "uLikeListBlNear",
      "uEffectPlayListNear", "uEffectPlayListNearInside", "last_feed_action_list"
  };
  for (auto &attr_name : valid_nearby_photo_action_set) {
    auto action_list = context->GetIntListCommonAttr(attr_name);
    if (action_list) {
      for (auto &item : *action_list) {
        action_photo_set_.insert(item);
      }
      if (si_action_set.count(attr_name) > 0) {
        for (auto &item : *action_list) {
          strong_interest_set_.insert(item);
        }
      }
    }
  }
  auto reco_user_info_ptr =
      context->GetProtoMessagePtrCommonAttr<ks::reco::UserInfo>(
        "nearby_reco_user_info.user_info_proto");
  if (reco_user_info_ptr == nullptr) {
    return;
  }
  const ks::reco::UserInfo& reco_user_info = *reco_user_info_ptr;

  int daily_zero_shot_user = (last_action_photo_list.size() == 0);
  int weekly_zero_shot_user = daily_zero_shot_user;
  int monthly_follow_user = 0;
  int daily_follow_user = 0;
  int monthly_like_user = 0;
  int daily_like_user = 0;
  int monthly_comment_user = 0;
  int daily_comment_user = 0;
  std::vector<int64> nearby_same_province_action_pids;
  std::vector<int64> nearby_same_province_strong_action_pids;

  int negative_feedback_user = 0;  // 负反馈敏感用户
  auto nearby_is_low_active_10d =
      GetIntCommonAttrFromContext(context, "nearby_reco_user_info.nearby_is_low_active_10d", 0);
  auto nearby_is_low_consume_10d =
      GetIntCommonAttrFromContext(context, "nearby_reco_user_info.nearby_is_low_consume_10d", 0);
  if (nearby_is_low_consume_10d == 1 || nearby_is_low_active_10d == 1) {
    negative_feedback_user = 1;
  }
  double last_negative_feedback_gap_hours =
      GetDoubleCommonAttrFromContext(context, "last_negative_feedback_gap_hours", 1.0);
  uint64 negative_feedback_expired_ms =
      (base::GetTimestamp() - last_negative_feedback_gap_hours * base::Time::kMicrosecondsPerHour) / 1000;
  double negative_filter_threshold =
      GetDoubleCommonAttrFromContext(context, "negative_filter_threshold", 0.5);

  if (reco_user_info.has_user_profile_v1()) {
    // 三个月过期时间
    uint64 expired_ms = base::GetTimestamp() - base::Time::kMicrosecondsPerDay * 90;
    uint64 expired_ms_one_day = base::GetTimestamp() - base::Time::kMicrosecondsPerDay;
    uint64 expired_ms_seesion = base::GetTimestamp() - base::Time::kMicrosecondsPerMinute * 20;
    uint64 expired_ms_one_week = base::GetTimestamp() - base::Time::kMicrosecondsPerDay * 7;
    uint64 expired_ms_one_month = base::GetTimestamp() - base::Time::kMicrosecondsPerDay * 30;
    uint64 ms_per_day = base::Time::kMicrosecondsPerDay;
    ks::reco::UserProfileV1 user_photo_profile = reco_user_info.user_profile_v1();
    // 设置 nearby combo slide 相关 action list
    std::vector<int64> nearby_like_pids;
    std::vector<int64> nearby_like_aids;
    std::vector<int64> user_like_pids;
    std::vector<int64> user_like_aids;
    std::vector<int64> nearby_longview_pids;
    std::vector<int64> nearby_longview_aids;
    std::vector<int64> user_longview_pids;
    std::vector<int64> user_longview_aids;
    std::vector<int64> user_follow_aids;
    static const std::unordered_set<int> nearby_page = {2, 9, 14, 25, 56};
    int num = 0;
    int num_all = 0;

    // 大盘 7 天 大盘 30 天 同城 7 天 同城 30 天
    std::vector<int64> real_show_count_list(4);
    std::vector<int64> view_count_list(4);
    std::vector<int64> like_count_list(4);
    std::vector<int64> comment_count_list(4);
    std::vector<int64> follow_count_list(4);
    std::vector<int64> forward_count_list(4);
    std::unordered_set<int64> nearby_recent_weak_visit_cnt;
    std::unordered_set<int64> nearby_recent_month_visit_cnt;
    std::vector<int64> nearby_session_click_list;
    std::vector<int64> nearby_recent_live_play_list;
    std::vector<int64> all_recent_live_play_list;
    int live_play_cnt_30d = 0;
    for (const auto &item : reco_user_info.live_profile_v1().live_play_list()) {
      if (item.client_timestamp() * 1000 >= expired_ms_one_month) {
        if (nearby_page.count(item.page_type())) {
          live_play_cnt_30d++;
          nearby_recent_live_play_list.push_back(item.anchor_id());
        }
        all_recent_live_play_list.push_back(item.anchor_id());
      }
    }

    for (int i = 0; i < user_photo_profile.real_show_list().size() && i < 200; i++) {
      auto real_show_item = user_photo_profile.real_show_list(i);
      int page_type = real_show_item.page_type();
      uint64 time_ms = real_show_item.time_ms();

      if (nearby_page.count(page_type)) {
        if (time_ms * 1000 > expired_ms_one_week) {
          real_show_count_list[2]++;
          nearby_recent_weak_visit_cnt.insert((time_ms * 1000 - expired_ms_one_week)/ms_per_day);
        }
        if (time_ms * 1000 > expired_ms_one_month) {
          real_show_count_list[3]++;
          nearby_recent_month_visit_cnt.insert((time_ms * 1000 - expired_ms_one_week)/ms_per_day);
        }
      }
      if (time_ms * 1000 > expired_ms_one_week) {
        real_show_count_list[0]++;
      }
      if (time_ms * 1000 > expired_ms_one_month) {
        real_show_count_list[1]++;
      }
    }

    for (int i = 0; i < user_photo_profile.video_playing_stat_size() && i < 200; i++) {
      auto video_playing_stat = user_photo_profile.video_playing_stat(i);
      int page_type = video_playing_stat.page();
      uint64 time_ms = video_playing_stat.client_timestamp();

      if (nearby_page.count(page_type)) {
        if (time_ms * 1000 > expired_ms_one_week) {
          view_count_list[2]++;
        }
        if (time_ms * 1000 > expired_ms_one_month) {
          view_count_list[3]++;
        }
      }
      if (time_ms * 1000 > expired_ms_one_week) {
        view_count_list[0]++;
      }
      if (time_ms * 1000 > expired_ms_one_month) {
        view_count_list[1]++;
      }

      if (IsHhLongViewV5(video_playing_stat.playing_time(),
              video_playing_stat.video_duration())) {
        if (num_all < 50) {
          user_longview_pids.push_back(video_playing_stat.photo_id());
          user_longview_aids.push_back(video_playing_stat.author_id());
          num_all++;
        }
        if (nearby_page.count(page_type) && num < 50) {
          nearby_longview_pids.push_back(video_playing_stat.photo_id());
          nearby_longview_aids.push_back(video_playing_stat.photo_id());
          num++;
        }
      }
    }
    num = 0;
    num_all = 0;
    for (int i = 0; i < user_photo_profile.like_list_size() && i < 100; i++) {
      auto like_item = user_photo_profile.like_list(i);
      if (num_all < 50) {
        user_like_pids.push_back(like_item.photo_id());
        user_like_aids.push_back(like_item.author_id());
        num_all++;
      }
      auto page_type = like_item.page_type();
      if ((like_item.page_type() == ks::reco::RecoEnumSummary::NEARBY_PAGE
          || like_item.page_type() == ks::reco::RecoEnumSummary::BL_NEARBY_PAGE) && num < 50) {
        nearby_like_pids.push_back(like_item.photo_id());
        nearby_like_aids.push_back(like_item.author_id());
        num++;
      }
    }
    for (int i = 0; i < user_photo_profile.follow_list_size() && i < 50; i++) {
      user_follow_aids.push_back(user_photo_profile.follow_list(i).author_id());
    }
    context->SetIntListCommonAttr("ucLikeNearbyAids", std::move(nearby_like_aids));
    context->SetIntListCommonAttr("ucLikeNearbyPids", std::move(nearby_like_pids));
    context->SetIntListCommonAttr("ucLongViewNearbyAids", std::move(nearby_longview_aids));
    context->SetIntListCommonAttr("ucLongViewNearbyPids", std::move(nearby_longview_pids));
    context->SetIntListCommonAttr("ucLikeAids", std::move(user_like_aids));
    context->SetIntListCommonAttr("ucLikePids", std::move(user_like_pids));
    context->SetIntListCommonAttr("ucLongViewAids", std::move(user_longview_aids));
    context->SetIntListCommonAttr("ucLongViewPids", std::move(user_longview_pids));
    context->SetIntListCommonAttr("ucFollowAids", std::move(user_follow_aids));
    context->SetIntListCommonAttr("nearby_recent_live_play_list", std::move(nearby_recent_live_play_list));
    context->SetIntListCommonAttr("all_recent_live_play_list", std::move(all_recent_live_play_list));

    if (user_photo_profile.hate_list_size() > 0 &&
        user_photo_profile.hate_list(0).time_ms() > negative_feedback_expired_ms) {
      negative_feedback_user = 1;
    }
    if (user_photo_profile.report_list_size() > 0 &&
        user_photo_profile.report_list(0).time_ms() > negative_feedback_expired_ms) {
      negative_feedback_user = 1;
    }

    for (int i = 0; i < user_photo_profile.click_list_size(); i++) {
      auto page_type = user_photo_profile.click_list(i).page_type();
      uint64 photo_id = user_photo_profile.click_list(i).photo_id();
      uint64 time_ms = user_photo_profile.click_list(i).time_ms();
      if (page_type == ks::reco::RecoEnumSummary::NEARBY_PAGE
          || page_type == ks::reco::RecoEnumSummary::BL_NEARBY_PAGE) {
        if (time_ms * 1000 > expired_ms_one_day) {
          new_daily_zero_shot_user_ = 0;
          new_weekly_zero_shot_user_ = 0;
        }
        if (time_ms * 1000 > expired_ms_seesion) {
          nearby_session_click_list.push_back(photo_id);
          new_session_zero_shot_user_ = 0;
        }
        if (time_ms * 1000 > expired_ms_one_week) {
          new_weekly_zero_shot_user_ = 0;
        }
        if (time_ms * 1000 < expired_ms || action_photo_set_.count(photo_id)) {
          continue;
        }
        if (time_ms * 1000 > expired_ms_one_day) {
          daily_zero_shot_user = 0;
          weekly_zero_shot_user = 0;
        }
        if (time_ms * 1000 > expired_ms_one_week) {
          weekly_zero_shot_user = 0;
        }
        action_photo_set_.insert(photo_id);
      } else {
        // none nearby
        if (enable_nearby_recall_trigger_photo_supplement_) {
          if (time_ms * 1000 < expired_ms) {
            continue;
          }
          all_action_photo_set_.insert(photo_id);
        }
      }
    }
    context->SetIntListCommonAttr("nearby_session_click_list", std::move(nearby_session_click_list));
    for (int i = 0; i < user_photo_profile.like_list_size(); i++) {
      auto page_type = user_photo_profile.like_list(i).page_type();
      uint64 time_ms = user_photo_profile.like_list(i).time_ms();
      if (nearby_page.count(page_type)) {
        if (time_ms * 1000 > expired_ms_one_day) {
           daily_like_user = 1;
        }
        if (time_ms * 1000 > expired_ms_one_week) {
           like_count_list[2]++;
        }
        if (time_ms * 1000 > expired_ms_one_month) {
          monthly_like_user = 1;
          like_count_list[3]++;
        }
      }
      if (time_ms * 1000 > expired_ms_one_week) {
           like_count_list[0]++;
      }
      if (time_ms * 1000 > expired_ms_one_month) {
          like_count_list[1]++;
      }
      uint64 photo_id = user_photo_profile.like_list(i).photo_id();
      if (page_type == ks::reco::RecoEnumSummary::NEARBY_PAGE
          || page_type == ks::reco::RecoEnumSummary::BL_NEARBY_PAGE) {
        if (time_ms * 1000 < expired_ms || action_photo_set_.count(photo_id)) {
          continue;
        }
        action_photo_set_.insert(photo_id);
        strong_interest_set_.insert(photo_id);
      } else {
        // none nearby
        if (enable_nearby_recall_trigger_photo_supplement_) {
          if (time_ms * 1000 < expired_ms) {
            continue;
          }
          all_action_photo_set_.insert(photo_id);
          all_strong_interest_set_.insert(photo_id);
        }
      }
    }
    for (int i = 0; i < user_photo_profile.follow_list_size(); i++) {
      auto page_type = user_photo_profile.follow_list(i).page_type();
      uint64 time_ms = user_photo_profile.follow_list(i).time_ms();
      if (nearby_page.count(page_type)) {
        if (time_ms * 1000 > expired_ms_one_day) {
           daily_follow_user = 1;
        }
        if (time_ms * 1000 > expired_ms_one_week) {
          follow_count_list[2]++;
        }
        if (time_ms * 1000 > expired_ms_one_month) {
          monthly_follow_user = 1;
          follow_count_list[3]++;
        }
      }
      if (time_ms * 1000 > expired_ms_one_week) {
        follow_count_list[0]++;
      }
      if (time_ms * 1000 > expired_ms_one_month) {
        follow_count_list[1]++;
      }
      uint64 photo_id = user_photo_profile.follow_list(i).photo_id();
      if (page_type == ks::reco::RecoEnumSummary::NEARBY_PAGE
          || page_type == ks::reco::RecoEnumSummary::BL_NEARBY_PAGE) {
        if (time_ms * 1000 < expired_ms || action_photo_set_.count(photo_id)) {
          continue;
        }
        action_photo_set_.insert(photo_id);
        strong_interest_set_.insert(photo_id);
      } else {
        // none nearby
        if (enable_nearby_recall_trigger_photo_supplement_) {
          if (time_ms * 1000 < expired_ms) {
            continue;
          }
          all_action_photo_set_.insert(photo_id);
          all_strong_interest_set_.insert(photo_id);
        }
      }
    }
    for (int i = 0; i < user_photo_profile.comment_list_size(); i++) {
      auto page_type = user_photo_profile.comment_list(i).page_type();
      uint64 time_ms = user_photo_profile.comment_list(i).time_ms();
      if (nearby_page.count(page_type)) {
        if (time_ms * 1000 > expired_ms_one_day) {
          daily_comment_user = 1;
        }
        if (time_ms * 1000 > expired_ms_one_week) {
          comment_count_list[2]++;
        }
        if (time_ms * 1000 > expired_ms_one_month) {
          monthly_comment_user = 1;
          comment_count_list[3]++;
        }
      }
      if (time_ms * 1000 > expired_ms_one_week) {
        comment_count_list[0]++;
      }
      if (time_ms * 1000 > expired_ms_one_month) {
        comment_count_list[1]++;
      }
    }
    int64 interaction_list_nearby_30d = follow_count_list[3] + comment_count_list[3] + forward_count_list[3];
    int64 interaction_list_30d = follow_count_list[1] + comment_count_list[1] + forward_count_list[1];
    context->SetIntCommonAttr("forward_list_30d", follow_count_list[1]);  // 大盘 30 天分享
    context->SetIntCommonAttr("interaction_list_30d", interaction_list_30d);  // 大盘 30 天互动
    context->SetIntCommonAttr("view_list_nearby_7d", view_count_list[2]);  // 同城 7 天 vv
    int64 nearby_all_vv_30d = view_count_list[3] + live_play_cnt_30d;
    context->SetIntCommonAttr("view_list_nearby_30d", nearby_all_vv_30d);  // 同城 30 天 vv
    context->SetIntCommonAttr("live_view_list_nearby_30d", live_play_cnt_30d);  // 同城 30 天直播 vv
    context->SetIntCommonAttr("forward_list_nearby_30d", follow_count_list[3]);  // 同城 30 天分享
    context->SetIntCommonAttr("interaction_list_nearby_30d", interaction_list_nearby_30d);  // 同城 30 天互动
    context->SetIntCommonAttr("visit_cnt_nearby_7d", nearby_recent_weak_visit_cnt.size());  // 同城 7 天访问
    context->SetIntCommonAttr("visit_cnt_nearby_30d", nearby_recent_month_visit_cnt.size());
    context->SetIntCommonAttr("real_show_nearby_7d", real_show_count_list[2]);  // 同城 7 天曝光
    context->SetIntCommonAttr("real_show_nearby_30d", real_show_count_list[3]);  // 同城 30 天曝光
    // context->SetIntListCommonAttr("real_show_count_list", std::move(real_show_count_list));
    // context->SetIntListCommonAttr("view_count_list", std::move(view_count_list));
    // context->SetIntListCommonAttr("like_count_list", std::move(like_count_list));
    // context->SetIntListCommonAttr("comment_count_list", std::move(comment_count_list));
    // context->SetIntListCommonAttr("follow_count_list", std::move(follow_count_list));
    // context->SetIntListCommonAttr("forward_count_list", std::move(forward_count_list));
  }

  int enable_negative_filter = 0;
  base::PseudoRandom random(base::GetTimestamp());
  double rand = random.GetDouble();
  if (negative_feedback_user == 1) {  // 近期有负反馈 || 低活低消 过滤
    enable_negative_filter = 1;
  } else if (rand <= negative_filter_threshold) {  // 一定概率过滤
    enable_negative_filter = 1;
  }

  context->SetIntCommonAttr("enable_negative_filter", enable_negative_filter);
  context->SetIntCommonAttr("negative_feedback_user", negative_feedback_user);

  context->SetIntCommonAttr("daily_zero_shot_user", daily_zero_shot_user);
  context->SetIntCommonAttr("weekly_zero_shot_user", weekly_zero_shot_user);
  context->SetIntCommonAttr("potential_comment_user", monthly_comment_user - daily_comment_user);
  context->SetIntCommonAttr("potential_follow_user", monthly_follow_user - daily_follow_user);
  context->SetIntCommonAttr("potential_like_user", monthly_like_user - daily_like_user);
  const auto hetu_id_1 = reco_user_info.user_interest_profile().hetu_level_one_long_term_id();
  const auto hetu_score_1 = reco_user_info.user_interest_profile().hetu_level_one_long_term_score();
  FillUserLongTermHetuFeature(context, hetu_id_1, hetu_score_1, "1", 5, 50);
  const auto hetu_id_2 = reco_user_info.user_interest_profile().hetu_level_two_long_term_id();
  const auto hetu_score_2 = reco_user_info.user_interest_profile().hetu_level_two_long_term_score();
  FillUserLongTermHetuFeature(context, hetu_id_2, hetu_score_2, "2", 10, 50);
  const auto hetu_id_3 = reco_user_info.user_interest_profile().hetu_level_three_long_term_id();
  const auto hetu_score_3 = reco_user_info.user_interest_profile().hetu_level_three_long_term_score();
  FillUserLongTermHetuFeature(context, hetu_id_3, hetu_score_3, "3", 15, 50);

  TriggerAssign(context, action_photo_set_, all_action_photo_set_,
    strong_interest_set_, all_strong_interest_set_, &nearby_all_photo_action_ids_);

  std::shuffle(nearby_all_photo_action_ids_.begin(), nearby_all_photo_action_ids_.end(), random_engine_);
  std::vector<RetrievalTriggerInfo> retrieval_trigger_info_list;

  for (auto source : i2i_source_list_) {
    AddRetrievalTriggerInfo(&retrieval_trigger_info_list, source);
  }
  int sp_count = 0;
  int normal_count = 0;
  int i2i_source_size = i2i_source_list_.size();
  if (i2i_source_size == 0) {
    return;
  }
  uint32 user_province_id = GetIntCommonAttrFromContext(context, "user_province_id", 0);
  for (auto photo_id : nearby_all_photo_action_ids_) {
    if (same_province_trigger_priority_) {   // 过滤非同省的 trigger
      auto it = action_photo_province_map_.find(photo_id);
      if (it != action_photo_province_map_.end()) {
        if (user_province_id > 0 && it->second > 0 && user_province_id != it->second) {
          continue;
        }
        nearby_same_province_action_pids.push_back(photo_id);
      }
    }
    // 特殊 trigger 分配给所有召回源
    if (strong_interest_set_.count(photo_id) && sp_count < strong_interest_max_cnt_) {
      for (int i = 0; i < i2i_source_size; i++) {
        auto &trigger_info = retrieval_trigger_info_list[i];
        TriggerSelectByRetrievalTypeReplenish(&trigger_info, photo_id);
      }
      ++sp_count;
      continue;
    }
    auto &trigger_info = retrieval_trigger_info_list[normal_count % i2i_source_size];
    TriggerSelectByRetrievalTypeReplenish(&trigger_info, photo_id);
    ++normal_count;
  }
  for (auto trigger_info : retrieval_trigger_info_list) {
    context->SetIntListCommonAttr(trigger_info.name, std::move(trigger_info.trigger_list));
  }
  nearby_same_province_strong_action_pids.assign(strong_interest_set_.begin(), strong_interest_set_.end());
  context->SetIntListCommonAttr("nearby_same_province_action_pids",
    std::move(nearby_same_province_action_pids));
  context->SetIntListCommonAttr("nearby_same_province_strong_action_pids",
    std::move(nearby_same_province_strong_action_pids));
  // swing online 单独处理一下
  auto swing_action_list = context->GetIntListCommonAttr("SWING_PHOTO_I2I_ACTION_LIST");
  std::vector<int64> swing_online_source_list;
  std::vector<int64> photo_list;
  if (swing_action_list) {
    for (auto &item : *swing_action_list) {
      photo_list.push_back(item);
    }
  }
  int64 source_id = GetIntCommonAttrFromContext(context, "uInnerSourceFeedId", 0);
  if (source_id > 0) {
    swing_online_source_list.push_back(source_id);
  }
  std::shuffle(photo_list.begin(), photo_list.end(), random_engine_);
  int added_src_action_list_num = 0;
  int max_nearby_swing_online_trigger_count =
      GetIntCommonAttrFromContext(context, "max_nearby_swing_online_trigger_count", 10);
  for (auto pid : photo_list) {
    if (added_src_action_list_num >= max_nearby_swing_online_trigger_count) {
      break;
    }
    if (source_id != pid) {
      swing_online_source_list.push_back(pid);
      ++added_src_action_list_num;
    }
  }
  context->SetIntListCommonAttr("SWING_ONLINE_I2I_ACTION_LIST",
                                std::move(swing_online_source_list));

  AddSingleFeature(context, reco_user_info, last_action_info);

  nearby_same_province_action_pids.clear();
  nearby_same_province_strong_action_pids.clear();
}

void NearbyInitCommonAttrEnricher::AddSingleFeature(MutableRecoContextInterface *context,
                                                    const ks::reco::UserInfo &reco_user_info,
                                                    const ks::reco::LastActionInfo &last_action_info) {
  // 后面是直播的用户偏好, 写在这里主要是为了复用了一下之前解析的 proto
  auto nearby_user_feature_ptr =
      context->GetProtoMessagePtrCommonAttr<ks::reco::NearbyUserFeature>(
        "nearby_reco_user_info.nearby_user_feature_proto");
  if (nearby_user_feature_ptr == nullptr) {
    LOG(ERROR) << "nearby_user_feature_ptr is null";
    return;
  }
  const ks::reco::NearbyUserFeature& nearby_user_feature = *nearby_user_feature_ptr;

  auto nearby_user_app_action_info_ptr =
      context->GetProtoMessagePtrCommonAttr<ks::reco::NearbyUserAppActionInfo>(
        "nearby_reco_user_info.nearby_user_app_action_info_proto");
  if (nearby_user_app_action_info_ptr == nullptr) {
    LOG(ERROR) << "nearby_user_app_action_info_ptr is null";
    return;
  }
  const ks::reco::NearbyUserAppActionInfo& nearby_user_app_action_info =
    *nearby_user_app_action_info_ptr;

  std::string config_key = GetStringCommonAttrFromContext(context, "nearby_live_limit_config_key",
                            "diversity_live_limit_config_key_0");
  int user_rely_live_level = GetUserWatchLiveLevel(nearby_user_app_action_info, last_action_info,
                        nearby_user_feature, config_key);
  context->SetIntCommonAttr("user_rely_live_level", user_rely_live_level);

  // 兼容单列数据流特征
  if (reco_user_info.has_basic_info()) {
    context->SetIntCommonAttr("uBasicGender", reco_user_info.basic_info().gender());
    context->SetIntCommonAttr("uBasicAge", reco_user_info.basic_info().age_segment());
  }
  context->SetStringCommonAttr("uVisitMod", reco_user_info.visit_mod());
  context->SetIntCommonAttr("uTrueNewUser", reco_user_info.true_new_user());
  context->SetIntCommonAttr("uFollowCount", reco_user_info.follow_count());
  context->SetIntCommonAttr("uFansCount", reco_user_info.fans_count());
  int64 uProductType = GetIntCommonAttrFromContext(context, "uProductType", 0);
  int64 uFeedType = GetIntCommonAttrFromContext(context, "uFeedType", 0);
  int64 tabId = GetTabId(uProductType, uFeedType);
  int64 hour = (base::GetTimestamp() / 3600000000) % 24;
  int64 weekday = (base::GetTimestamp() / 3600000000 / 24) % 7;
  context->SetIntCommonAttr("TabId", tabId);
  context->SetIntCommonAttr("hour", hour);
  context->SetIntCommonAttr("weekday", weekday);
}

int64 NearbyInitCommonAttrEnricher::GetTabId(int64 uProductType, int64 uFeedType) {
  int64 tabId = 3;
  if (uProductType == 0) {
    if (uFeedType == 0L) {
      tabId = 3;
    } else {
      tabId = 4;
    }
  } else {
    if (uFeedType == 0) {
      tabId = 5;
    } else {
      tabId = 6;
    }
  }
  return tabId;
}

void NearbyInitCommonAttrEnricher::TriggerAssign(MutableRecoContextInterface * context,
                                                 const std::unordered_set<uint64> &action_photo_set,
                                                 const std::unordered_set<uint64> &all_action_photo_set,
                                                 const std::unordered_set<uint64> &strong_interest_set,
                                                 const std::unordered_set<uint64> &all_strong_interest_set,
                                                 std::vector<uint64> * pResult) {
  int64 start_ts = base::GetTimestamp();
  static const int64 U_PRODUCT_NEBULA = 1;
  static const int64 U_FEED_TYPE_SLIDE = 1;
  std::string bizName("unknown");
  int64 uProductType = context->GetIntCommonAttr("uProductType").value_or(0);
  int64 uFeedType = context->GetIntCommonAttr("uFeedType").value_or(0);
  if (U_PRODUCT_NEBULA == uProductType) {
    if (U_FEED_TYPE_SLIDE == uFeedType) {
      bizName = "bl_nearby_in";
    } else {
      bizName = "bl_nearby";
    }
  } else {
    if (U_FEED_TYPE_SLIDE == uFeedType) {
      bizName = "nearby_in";
    } else {
      bizName = "nearby";
    }
  }

  int enable_nearby_recall_trigger_photo_supplement = GetIntCommonAttrFromContext(context,
    "enable_nearby_recall_trigger_photo_supplement", 0);
  int nearby_recall_trigger_photo_supplement_low_bound = GetIntCommonAttrFromContext(context,
    "nearby_recall_trigger_photo_supplement_low_bound", 5);
  if (enable_nearby_recall_trigger_photo_supplement) {
    if (action_photo_set_.size() < nearby_recall_trigger_photo_supplement_low_bound) {
      all_action_photo_set_.insert(action_photo_set_.begin(), action_photo_set_.end());
      pResult->assign(all_action_photo_set_.begin(), all_action_photo_set_.end());
      base::perfutil::PerfUtilWrapper::IntervalLogStash(all_strong_interest_set.size(),
        "reco.nearby.NearbyInitCommonAttrEnricher", bizName, "TriggerAssign.hit", "size");
    } else {
      pResult->assign(action_photo_set_.begin(), action_photo_set_.end());
      base::perfutil::PerfUtilWrapper::IntervalLogStash(action_photo_set_.size(),
        "reco.nearby.NearbyInitCommonAttrEnricher", bizName, "TriggerAssign.miss", "size");
    }
  } else {
    pResult->assign(action_photo_set_.begin(), action_photo_set_.end());
  }

  base::perfutil::PerfUtilWrapper::IntervalLogStash(action_photo_set.size(),
    "reco.nearby.NearbyInitCommonAttrEnricher", bizName, "TriggerAssign.action_photo_set", "size");
  base::perfutil::PerfUtilWrapper::IntervalLogStash(strong_interest_set.size(),
    "reco.nearby.NearbyInitCommonAttrEnricher", bizName, "TriggerAssign.strong_interest_set", "size");
  base::perfutil::PerfUtilWrapper::IntervalLogStash(all_action_photo_set.size(),
    "reco.nearby.NearbyInitCommonAttrEnricher", bizName, "TriggerAssign.all_action_photo_set", "size");
  base::perfutil::PerfUtilWrapper::IntervalLogStash(all_strong_interest_set.size(),
    "reco.nearby.NearbyInitCommonAttrEnricher", bizName, "TriggerAssign.all_strong_interest_set", "size");
  base::perfutil::PerfUtilWrapper::IntervalLogStash(base::GetTimestamp() - start_ts,
    "reco.nearby.NearbyInitCommonAttrEnricher", bizName, "TriggerAssign.duration", "total");
}

void NearbyInitCommonAttrEnricher::AddRetrievalTriggerInfo(
    std::vector<RetrievalTriggerInfo> *retrieval_trigger_info_list, std::string source) {
  retrieval_trigger_info_list->emplace_back();
  retrieval_trigger_info_list->back().name = source;
  std::vector<int64> list1;
  retrieval_trigger_info_list->back().trigger_list = list1;
}
void NearbyInitCommonAttrEnricher::TriggerSelectByRetrievalTypeReplenish(RetrievalTriggerInfo *trigger_info,
                                                                         uint64 photo_id) {
  trigger_info->trigger_list.push_back(photo_id);
}

void NearbyInitCommonAttrEnricher::SortMapByValue(const std::unordered_map<int64, int>& mmap,
                      std::vector<std::pair<int64, int>>* output) {
  output->clear();
  for (auto iter : mmap) {
    output->push_back(std::make_pair(iter.first, iter.second));
  }
  std::sort(output->begin(), output->end(), [](std::pair<int64, int>& a, std::pair<int64, int>& b) {
    return a.second > b.second;
  });
  return;
}

bool NearbyInitCommonAttrEnricher::IsHhLongViewV5(int64 playingTime, int64 duration) {
  // 跟 C++ 对齐，这里转换成整数秒，再进行判断
  int64 durationSecond = duration / 1000;
  int64 playingTimeSecond = playingTime / 1000;
  if (durationSecond < 10) {
    return playingTimeSecond >= 10;
  } else if (durationSecond < 58) {
    return playingTimeSecond >= durationSecond + 1;
  } else {
    return playingTimeSecond >= 58;
  }
}

void NearbyInitCommonAttrEnricher::EnrichLiveSimActionList(MutableRecoContextInterface *context,
                                                     RecoResultConstIter begin,
                                                     RecoResultConstIter end) {
  static const int64 page_mask = 0x3f;
  static const int64 page_move = 10;
  auto live_colossus_live_id = context->GetIntListCommonAttr("live_colossus_live_id");
  auto live_colossus_author_id = context->GetIntListCommonAttr("live_colossus_author_id");
  auto live_colossus_page_frame_cluster_id
    = context->GetIntListCommonAttr("live_colossus_page_frame_cluster_id");
  auto live_colossus_timestamp = context->GetIntListCommonAttr("live_colossus_timestamp");
  auto live_colossus_play_time = context->GetIntListCommonAttr("live_colossus_play_time");
  static const std::unordered_set<uint16> nearby_page = {2, 9, 14, 25, 56};
  if (!live_colossus_live_id || !live_colossus_author_id || !live_colossus_page_frame_cluster_id ||
      !live_colossus_timestamp || !live_colossus_play_time) {
    CL_LOG(WARNING) << "lve colossus is empty";
    return;
  }
  if (live_colossus_live_id->size() == 0) {
    CL_LOG(WARNING) << "live colossus size = 0";
    return;
  }
    bool size_macth = live_colossus_live_id->size() == live_colossus_author_id->size()
    && live_colossus_live_id->size() == live_colossus_page_frame_cluster_id->size()
    && live_colossus_live_id->size() == live_colossus_timestamp->size()
    && live_colossus_live_id->size() == live_colossus_play_time->size();
  if (!size_macth) {
     CL_LOG(WARNING) << GetName() << " colossus size is not match";
    return;
  }
  int max_nearby_sim_count = 100;
  int colossus_nearby_live_counts = 0;
  int colossus_all_live_counts = 0;
  uint64 expired_ms = base::GetTimestamp() - base::Time::kMicrosecondsPerDay * 90;
  std::vector<int64> colossus_nearby_live_pos_aids;
  std::vector<int64> colossus_all_live_pos_aids;
  std::unordered_set<int64> colossus_nearby_live_pos_aids_set;
  std::unordered_set<int64> colossus_all_live_pos_aids_set;
  for (int i = live_colossus_live_id->size() - 1; i >= 0; i--) {
    if (colossus_nearby_live_counts >= max_nearby_sim_count
      || live_colossus_timestamp->at(i) * base::Time::kMicrosecondsPerSecond < expired_ms) {
      break;
    }
    if (live_colossus_live_id->at(i) <= 0 || live_colossus_play_time->at(i) < 20) {
      continue;
    }
    uint16 page_type = (live_colossus_page_frame_cluster_id->at(i) >> page_move) & page_mask;
    int64 author_id = live_colossus_author_id->at(i);
    if (nearby_page.count(page_type)) {
      if (!colossus_nearby_live_pos_aids_set.count(author_id)) {
        colossus_nearby_live_pos_aids_set.insert(author_id);
        colossus_nearby_live_pos_aids.push_back(author_id);
        colossus_nearby_live_counts++;
      }
    }
    if (!colossus_all_live_pos_aids_set.count(author_id)
      && colossus_all_live_counts < max_nearby_sim_count) {
      colossus_all_live_pos_aids_set.insert(author_id);
      colossus_all_live_pos_aids.push_back(author_id);
      colossus_all_live_counts++;
    }
  }
  context->SetIntListCommonAttr("colossus_all_live_pos_aids", std::move(colossus_all_live_pos_aids));
  context->SetIntListCommonAttr("colossus_nearby_live_pos_aids", std::move(colossus_nearby_live_pos_aids));
}
void NearbyInitCommonAttrEnricher::EnrichSimActionList(MutableRecoContextInterface *context,
                                                     RecoResultConstIter begin,
                                                     RecoResultConstIter end) {
  // 同城的 channel type, 详情见 https://git.corp.kuaishou.com/reco-cpp/reco_proto/-/blob/master/ad/ad_proto/kuaishou/reco/platform/log/user_action_log.proto
  static const std::unordered_set<int> NEARBY_CHANNEL = {2, 11, 19, 26, 35, 39, 62, 83, 84};
  static const std::unordered_set<int> NEARBY_OUT_CHANNEL = {2, 39};
  // 不适合在同城引入的公域行为的类目, 具体看这个 doc : https://docs.corp.kuaishou.com/s/home/<USER>
  static const std::unordered_set<int> NEARBY_INVALID_TAG = {3, 6, 16, 38, 40, 41, 44, 47, 56, 64, 68,
    111, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 276, 277, 430, 431, 432, 563, 564, 565,
    581, 632, 584, 583, 630, 631, 180, 234, 123, 243, 244, 288, 289, 179, 232, 349, 410, 411, 412, 578, 589,
    596, 316, 329, 397, 398, 50241, 50252, 50242, 50244, 1286, 2210, 2226, 2233, 1294, 2216, 2231, 1300, 1301,
    2211, 2215, 2236, 2239, 1308, 2222, 1310, 1312, 2212, 1315, 1316, 1319, 1320, 1322, 2221, 1330, 1331,
    2217, 2227, 2232, 1337, 1338, 1339, 1340, 2228, 2237, 1341, 1342, 1344, 1409, 2225, 2220, 2333, 2213,
    2219, 2229, 2230, 1321, 1323, 1324, 2209, 2214, 2223, 2224, 2234, 2235, 2238, 1048, 1049, 1050, 1046,
    1047, 2319, 2320, 2321, 2322, 2323, 2324, 2325, 2326, 2328, 2329, 2330, 2331, 2242, 1350, 1351, 1352,
    1353, 1354, 1355, 1356, 1357, 1358, 1359, 1360, 1361, 1362, 1363, 1364, 1365, 1366, 1367, 1368, 1369,
    1370, 1371, 1372, 1373, 1374, 1375, 1376, 1377, 1378, 1379, 1380, 1381, 1382, 1383, 1384, 1385, 1386,
    1387, 1388, 1389, 1390, 1391, 1392, 1393, 1394, 1395, 1396, 1397, 1398, 1399, 1400, 1401, 1402, 1403,
    1404, 1405, 1406, 1407, 1408, 1161, 1162, 1163, 1454, 1455, 1164, 1165, 1166, 1167, 1524, 1988, 2298,
    2299, 1818, 1821, 1843, 1844};
  auto colossus_photo_id = context->GetIntListCommonAttr("colossus_photo_id");
  auto colossus_author_id = context->GetIntListCommonAttr("colossus_author_id");
  auto colossus_channel = context->GetIntListCommonAttr("colossus_channel");
  auto colossus_timestamp = context->GetIntListCommonAttr("colossus_timestamp");
  auto colossus_tag = context->GetIntListCommonAttr("colossus_tag");
  auto colossus_label = context->GetIntListCommonAttr("colossus_label");
  auto colossus_play_time = context->GetIntListCommonAttr("colossus_play_time");
  auto colossus_duration = context->GetIntListCommonAttr("colossus_duration");
  auto colossus_latitude = context->GetDoubleListCommonAttr("colossus_latitude");
  auto colossus_longitude = context->GetDoubleListCommonAttr("colossus_longitude");

  int user_city_id = GetIntCommonAttrFromContext(context, "user_city_id", 0);
  int enable_author_colossus = GetIntCommonAttrFromContext(context, "enable_author_colossus", 0);
  int colossus_write_maxsize = GetIntCommonAttrFromContext(context, "colossus_write_maxsize", 100);

  int colossus_max_day = GetIntCommonAttrFromContext(context, "colossus_max_day", 1);
  int colossus_interest_variant_is_on =
      GetIntCommonAttrFromContext(context, "colossus_interest_variant_is_on", 0);
  int user_province_id = GetIntCommonAttrFromContext(context, "user_province_id", 0);
  if (!colossus_photo_id || !colossus_channel || !colossus_tag || !colossus_timestamp || !colossus_label ||
      !colossus_play_time || !colossus_duration || !colossus_author_id || !colossus_latitude ||
      !colossus_longitude) {
    CL_LOG(WARNING) << GetName() << " colossus is empty";
    return;
  }
  if (colossus_photo_id->size() == 0) {
     CL_LOG(WARNING) << GetName() << " colossus size = 0";
    return;
  }
  if (GetIntCommonAttrFromContext(context, "enable_slide_sim_action_list", 0)
        && GetIntCommonAttrFromContext(context, "uFeedType", 0) == 1) {
    return;
  }
  bool size_macth = colossus_photo_id->size() == colossus_channel->size() && colossus_photo_id->size()
    == colossus_timestamp->size() && colossus_photo_id->size() == colossus_tag->size()
    && colossus_photo_id->size() == colossus_label->size()
    && colossus_photo_id->size() == colossus_play_time->size()
    && colossus_photo_id->size() == colossus_duration->size()
    && colossus_photo_id->size() == colossus_author_id->size()
    && colossus_photo_id->size() == colossus_latitude->size()
    && colossus_photo_id->size() == colossus_longitude->size();
  if (!size_macth) {
     CL_LOG(WARNING) << GetName() << " colossus size is not match";
    return;
  }
  bool LoadHetuCateMapOK = LoadHetuCateMap();
  // 反向遍历
  uint64 expired_ms = base::GetTimestamp() - base::Time::kMicrosecondsPerDay * 90;
  uint64 current_expired_ms = base::GetTimestamp() - base::Time::kMicrosecondsPerDay * colossus_max_day;
  uint64 month_expired_ms = base::GetTimestamp() - base::Time::kMicrosecondsPerDay * 30;
  uint64 week_expired_ms = base::GetTimestamp() - base::Time::kMicrosecondsPerDay * 7;
  uint64 day_expired_ms = base::GetTimestamp() - base::Time::kMicrosecondsPerDay * 1;
  std::vector<int64> whole_ks_action_photo_ids;
  std::vector<int64> sim_nearby_action_photo_ids;
  std::vector<int64> sim_nearby_action_author_ids;
  std::vector<int64> sim_nearby_action_tag_ids;
  std::vector<int64> sim_nearby_action_play_time;
  std::vector<double> sim_nearby_action_latitude;
  std::vector<double> sim_nearby_action_longitude;
  std::unordered_set<int64> hate_hetu_bottom_tags;
  std::unordered_set<int64> report_hetu_bottom_tags;
  std::vector<int64> colossus_report_photo_ids;
  std::vector<int64> colossus_hate_photo_ids;
  std::vector<int64> short_term_report_photo_ids;
  std::vector<int64> short_term_hate_photo_ids;
  std::vector<int64> nearby_recent_play_photo_ids;
  std::vector<int64> nearby_recent_play_author_ids;
  std::vector<int64> nearby_recent_play_actions;
  std::vector<int64> nearby_recent_play_durations;

  std::vector<int64> nearby_recent_pid_64;
  std::vector<int64> nearby_recent_aid_64;
  std::vector<int64> nearby_recent_watchtime_64;
  std::vector<int64> nearby_recent_duration_64;
  std::vector<int64> nearby_recent_action_64;
  std::vector<int64> nearby_recent_timestamp_64;

  std::vector<int64> nearby_click_pid_256;
  std::vector<int64> nearby_click_aid_256;
  std::vector<int64> nearby_click_watchtime_256;
  std::vector<int64> nearby_click_duration_256;
  std::vector<int64> nearby_click_action_256;
  std::vector<int64> nearby_click_timestamp_256;
  std::vector<int64> nearby_satis_pid_256;
  std::vector<int64> nearby_satis_aid_256;
  std::vector<int64> nearby_satis_watchtime_256;
  std::vector<int64> nearby_satis_duration_256;
  std::vector<int64> nearby_satis_action_256;
  std::vector<int64> nearby_satis_timestamp_256;

  std::vector<int64> nearby_sati_photo_ids;
  std::vector<int64> nearby_eff_click_photo_ids;
  std::vector<int64> nearby_same_city_click_photo_ids;
  std::vector<int64> nearby_like_pids_v2;  // 互动行为过滤需求
  std::unordered_map<int64, int64> nearby_same_city_eff_click_hetu1_map;
  std::unordered_map<int64, int64> nearby_same_city_eff_click_hetu2_map;
  int nearby_counts = 0;
  int whole_count = 0;
  int hate_count = 0;
  int report_count = 0;
  int whole_nearby_counts = 0;
  int nearby_recent_add_counts = 0;
  int nearby_sati_photo_add_counts = 0;
  int nearby_eff_click_photo_add_counts = 0;
  int nearby_same_city_click_photo_add_counts = 0;
  double user_lat = GetDoubleCommonAttrFromContext(context,
                                  "nearby_reco_user_info.lat", 0.0);
  double user_lon = GetDoubleCommonAttrFromContext(context,
                                  "nearby_reco_user_info.lon", 0.0);
  if (enable_author_colossus) {
    // add last_action_info
    EnrichAuthorCoss(context);
  }
  // add last_action_info
  auto nearby_last_action_info_str = context->GetStringCommonAttr(
    "nearby_reco_user_info.last_action_info");
  ks::reco::LastActionInfo nearby_last_action_info;
  if (nearby_last_action_info_str) {
    nearby_last_action_info.ParseFromArray(nearby_last_action_info_str->data(),
            nearby_last_action_info_str->size());
    for (auto& feed : nearby_last_action_info.last_negative_feed()) {
      if (feed.feed_type() == 1) {
        int64 photo_id = feed.feed_id();
        short_term_hate_photo_ids.push_back(photo_id);
        colossus_hate_photo_ids.push_back(photo_id);
      }
    }
  }
  int64 uFeedType = GetIntCommonAttrFromContext(context, "uFeedType", 0);
  folly::F14FastMap<uint32, std::vector<int64>> colossus_hetu_cate_map;
  folly::F14FastMap<uint32, double> colossus_hetu_weight;
  int max_nearby_sim_count = GetIntCommonAttrFromContext(context, "max_nearby_sim_count", 300);
  int nearby_each_tag_max_sim_count = GetIntCommonAttrFromContext(context,
      "nearby_each_tag_max_sim_count", 50);
  bool is_monthly_nearby_interact_user = false;
  bool is_daily_nearby_interact_user = false;
  for (int i = colossus_photo_id->size() - 1; i >= 0; i--) {
    uint32 item_label = colossus_label->at(i);
    bool like_label = item_label & 0x01;
    bool follow_label = item_label & (1 << 1);
    bool comment_label = item_label & (1 << 4);
    if (like_label)
        nearby_like_pids_v2.push_back(colossus_photo_id->at(i));
  }
  int monthly_all_follow_count = 0;
  int monthly_nearby_follow_count = 0;
  for (int i = colossus_photo_id->size() - 1; i >= 0; i--) {
    if (nearby_counts >= max_nearby_sim_count) {
      break;
    }
    if (colossus_photo_id->at(i) <= 0) {
      continue;
    }
    uint32 item_label = colossus_label->at(i);
    bool like_label = item_label & 0x01;
    bool follow_label = item_label & (1 << 1);
    bool comment_label = item_label & (1 << 4);
    bool report_label = item_label & (1 << 3);
    bool hate_label = item_label & (1 << 13);
    int tag = colossus_tag->at(i);
    if (!is_monthly_nearby_interact_user
        && colossus_timestamp->at(i) * base::Time::kMicrosecondsPerSecond >= month_expired_ms
        && NEARBY_CHANNEL.count(colossus_channel->at(i)) && (like_label || follow_label || comment_label)) {
          is_monthly_nearby_interact_user = true;
    }
    if (colossus_timestamp->at(i) * base::Time::kMicrosecondsPerSecond >= month_expired_ms && follow_label) {
      monthly_all_follow_count++;
      if (NEARBY_CHANNEL.count(colossus_channel->at(i))) {
        monthly_nearby_follow_count++;
      }
    }
    if (new_weekly_zero_shot_user_ > 0
        && colossus_timestamp->at(i) * base::Time::kMicrosecondsPerSecond >= week_expired_ms
        && NEARBY_CHANNEL.count(colossus_channel->at(i))) {
        new_weekly_zero_shot_user_ = 0;
    }
    if (new_daily_zero_shot_user_ > 0
        && colossus_timestamp->at(i) * base::Time::kMicrosecondsPerSecond >= day_expired_ms
        && NEARBY_CHANNEL.count(colossus_channel->at(i))) {
        new_daily_zero_shot_user_ = 0;
    }
    if (!is_daily_nearby_interact_user
        && colossus_timestamp->at(i) * base::Time::kMicrosecondsPerSecond >= day_expired_ms
        && NEARBY_CHANNEL.count(colossus_channel->at(i)) && (like_label || follow_label || comment_label)) {
          is_daily_nearby_interact_user = true;
    }
    if (colossus_timestamp->at(i) * base::Time::kMicrosecondsPerSecond >= month_expired_ms
        && NEARBY_OUT_CHANNEL.count(colossus_channel->at(i)) && colossus_play_time->at(i) >= 3) {
        if (nearby_eff_click_photo_add_counts < 100) {
          nearby_eff_click_photo_ids.push_back(colossus_photo_id->at(i));
          nearby_eff_click_photo_add_counts++;
        }
        if (uFeedType == 0 && nearby_same_city_click_photo_add_counts < 300
              && colossus_latitude->at(i) > 0.1) {
          uint32 item_region_id =
            ks::reco::GetRegionId("", colossus_latitude->at(i), colossus_longitude->at(i));
          uint32 item_city_id = base::RegionDict::LogicCityId(item_region_id);
          if (user_city_id == item_city_id || ks::location_util::GetDistance(user_lat, user_lon,
            colossus_latitude->at(i), colossus_longitude->at(i)) < 50.0) {
            nearby_same_city_click_photo_add_counts++;
            nearby_same_city_click_photo_ids.push_back(colossus_photo_id->at(i));
            if (LoadHetuCateMapOK && hetu_cate_map_.find(tag) != hetu_cate_map_.end()) {
              auto parents = hetu_cate_map_[tag];
              if (parents.size() > 1) {
                int item_hetu1 = parents[0];
                int item_hetu2 = parents[1];
                if (item_hetu1 > 0) {
                  if (nearby_same_city_eff_click_hetu1_map.count(item_hetu1)) {
                    nearby_same_city_eff_click_hetu1_map[item_hetu1] += 1;
                  } else {
                    nearby_same_city_eff_click_hetu1_map.insert(std::make_pair(item_hetu1, 1));
                  }
                }
                if (item_hetu2 > 0) {
                  if (nearby_same_city_eff_click_hetu2_map.count(item_hetu2)) {
                    nearby_same_city_eff_click_hetu2_map[item_hetu2] += 1;
                  } else {
                    nearby_same_city_eff_click_hetu2_map.insert(std::make_pair(item_hetu2, 1));
                  }
                }
              }
            }
          }
        }
    }
    if (NEARBY_OUT_CHANNEL.count(colossus_channel->at(i))) {
        if (nearby_click_pid_256.size() < 256) {
          nearby_click_pid_256.push_back(colossus_photo_id->at(i));
          nearby_click_aid_256.push_back(colossus_author_id->at(i));
          nearby_click_duration_256.push_back(colossus_duration->at(i));
          nearby_click_watchtime_256.push_back(colossus_play_time->at(i));
          nearby_click_action_256.push_back(colossus_label->at(i));
          nearby_click_timestamp_256.push_back(
            (base::GetTimestamp() - colossus_timestamp->at(i) *  base::Time::kMicrosecondsPerSecond)
            / base::Time::kMicrosecondsPerDay);
        }
    }

    if (NEARBY_CHANNEL.count(colossus_channel->at(i))) {
      if (nearby_recent_pid_64.size() < 64) {
          nearby_recent_pid_64.push_back(colossus_photo_id->at(i));
          nearby_recent_aid_64.push_back(colossus_author_id->at(i));
          nearby_recent_action_64.push_back(colossus_label->at(i));
          nearby_recent_duration_64.push_back(colossus_duration->at(i));
          nearby_recent_watchtime_64.push_back(colossus_play_time->at(i));
          nearby_recent_timestamp_64.push_back(
            (base::GetTimestamp() - colossus_timestamp->at(i) *  base::Time::kMicrosecondsPerSecond)
            / base::Time::kMicrosecondsPerDay);
      }
      if ((colossus_play_time->at(i) >= 18 ||
            like_label ||
            follow_label ||
            comment_label) && nearby_satis_pid_256.size() < 256) {
          nearby_satis_pid_256.push_back(colossus_photo_id->at(i));
          nearby_satis_aid_256.push_back(colossus_author_id->at(i));
          nearby_satis_duration_256.push_back(colossus_duration->at(i));
          nearby_satis_watchtime_256.push_back(colossus_play_time->at(i));
          nearby_satis_action_256.push_back(colossus_label->at(i));
          nearby_satis_timestamp_256.push_back(
            (base::GetTimestamp() - colossus_timestamp->at(i) * base::Time::kMicrosecondsPerSecond)
            / base::Time::kMicrosecondsPerDay);
      }
      if (nearby_recent_add_counts < 30) {
        int64 action_type = 1;
        if (like_label) {
          action_type += 1;
        }
        if (follow_label) {
          action_type += 2;
        }
        if (comment_label) {
          action_type += 4;
        }
        nearby_recent_play_photo_ids.push_back(colossus_photo_id->at(i));
        nearby_recent_play_author_ids.push_back(colossus_author_id->at(i));
        nearby_recent_play_actions.push_back(action_type);
        nearby_recent_play_durations.push_back(colossus_play_time->at(i));
      }
      nearby_recent_add_counts++;
      if (nearby_sati_photo_add_counts < 100
        && (like_label || follow_label || comment_label || colossus_play_time->at(i) >= 16)) {
        nearby_sati_photo_ids.push_back(colossus_photo_id->at(i));
        nearby_sati_photo_add_counts++;
      }
    }
    AddSimActionToMap(colossus_tag->at(i), colossus_photo_id->at(i), colossus_author_id->at(i),
        hate_label, report_label, nearby_each_tag_max_sim_count);
    if (colossus_timestamp->at(i) * base::Time::kMicrosecondsPerSecond >= current_expired_ms) {
      if (report_label && short_term_report_photo_ids.size() < colossus_write_maxsize) {
        short_term_report_photo_ids.push_back(colossus_photo_id->at(i));
      }
      if (hate_label && short_term_hate_photo_ids.size() < colossus_write_maxsize) {
        short_term_hate_photo_ids.push_back(colossus_photo_id->at(i));
      }
    }
    if (report_label && colossus_report_photo_ids.size() < colossus_write_maxsize) {
      colossus_report_photo_ids.push_back(colossus_photo_id->at(i));
    }
    if (hate_label && colossus_hate_photo_ids.size() < colossus_write_maxsize) {
      colossus_hate_photo_ids.push_back(colossus_photo_id->at(i));
    }
    if (hate_label && tag > 0) {
      hate_hetu_bottom_tags.insert(tag);
      ++hate_count;
    }
    if (report_label && tag > 0) {
      report_hetu_bottom_tags.insert(tag);
      ++report_count;
    }
    if (!like_label && !follow_label && !comment_label) {
      if (colossus_play_time->at(i) <= 5 || (colossus_play_time->at(i) <= 12
          && colossus_play_time->at(i) < colossus_duration->at(i))) {
        continue;
      }
    }
    if (!NEARBY_CHANNEL.count(colossus_channel->at(i))) {
      if (whole_count < 30 && (!NEARBY_INVALID_TAG.count(tag))) {
        whole_ks_action_photo_ids.push_back(colossus_photo_id->at(i));
        ++whole_count;
      }
      if (same_province_trigger_priority_ && colossus_latitude->at(i) > 0.1
          && whole_nearby_counts < 100 && (!NEARBY_INVALID_TAG.count(tag))) {
        uint32 item_province_id = 0;
        uint32 item_region_id =
          ks::reco::GetRegionId("", colossus_latitude->at(i), colossus_longitude->at(i));
        item_province_id = base::RegionDict::LogicProvinceId(item_region_id);
        if (item_province_id == user_province_id) {
          sim_nearby_action_photo_ids.push_back(colossus_photo_id->at(i));
          action_photo_set_.insert(colossus_photo_id->at(i));
          whole_nearby_counts++;
          sim_nearby_action_author_ids.push_back(colossus_author_id->at(i));
          sim_nearby_action_tag_ids.push_back(colossus_tag->at(i));
          sim_nearby_action_play_time.push_back(colossus_play_time->at(i));
          sim_nearby_action_latitude.push_back(colossus_latitude->at(i));
          sim_nearby_action_longitude.push_back(colossus_longitude->at(i));
          if (nearby_sati_photo_add_counts < 100
            && (like_label || follow_label || comment_label || colossus_play_time->at(i) >= 16)) {
            nearby_sati_photo_ids.push_back(colossus_photo_id->at(i));
            nearby_sati_photo_add_counts++;
          }
        } else {
          if (enable_nearby_recall_trigger_photo_supplement_) {
            all_action_photo_set_.insert(colossus_photo_id->at(i));
          }
        }
      }
      continue;
    }
    if (colossus_timestamp->at(i) * base::Time::kMicrosecondsPerSecond < expired_ms) {
      break;
    }
    sim_nearby_action_photo_ids.push_back(colossus_photo_id->at(i));
    sim_nearby_action_author_ids.push_back(colossus_author_id->at(i));
    sim_nearby_action_tag_ids.push_back(colossus_tag->at(i));
    sim_nearby_action_play_time.push_back(colossus_play_time->at(i));
    sim_nearby_action_latitude.push_back(colossus_latitude->at(i));
    sim_nearby_action_longitude.push_back(colossus_longitude->at(i));
    action_photo_set_.insert(colossus_photo_id->at(i));
    if (same_province_trigger_priority_) {
      uint32 item_province_id = 0;
      if (colossus_latitude->at(i) > 0.1) {
        uint32 item_region_id = ks::reco::GetRegionId("", colossus_latitude->at(i),
          colossus_longitude->at(i));
        item_province_id = base::RegionDict::LogicProvinceId(item_region_id);
      }
      action_photo_province_map_.insert({colossus_photo_id->at(i), item_province_id});
    }
    ++nearby_counts;
  }
  EnrichSimTagActionList(context);
  bool is_potential_nearby_interact_user =
    is_monthly_nearby_interact_user && (!is_daily_nearby_interact_user);
  context->SetIntCommonAttr("is_potential_nearby_interact_user", is_potential_nearby_interact_user ? 1 : 0);
  context->SetIntCommonAttr("is_daily_nearby_interact_user", is_daily_nearby_interact_user ? 1 : 0);
  context->SetIntCommonAttr("is_monthly_nearby_interact_user", is_monthly_nearby_interact_user ? 1 : 0);
  context->SetIntCommonAttr("monthly_all_follow_count", monthly_all_follow_count);
  context->SetIntCommonAttr("monthly_nearby_follow_count", monthly_nearby_follow_count);
  context->SetIntListCommonAttr("whole_ks_action_photo_ids", std::move(whole_ks_action_photo_ids));
  context->SetIntListCommonAttr("sim_nearby_action_photo_ids", std::move(sim_nearby_action_photo_ids));
  context->SetIntListCommonAttr("sim_nearby_action_author_ids", std::move(sim_nearby_action_author_ids));
  context->SetIntListCommonAttr("sim_nearby_action_tag_ids", std::move(sim_nearby_action_tag_ids));
  context->SetIntListCommonAttr("sim_nearby_action_play_time", std::move(sim_nearby_action_play_time));
  context->SetDoubleListCommonAttr("sim_nearby_action_latitude", std::move(sim_nearby_action_latitude));
  context->SetDoubleListCommonAttr("sim_nearby_action_longitude", std::move(sim_nearby_action_longitude));
  std::vector<int64> report_hetu_bottom_tag_list(report_hetu_bottom_tags.begin(),
    report_hetu_bottom_tags.end());
  std::vector<int64> hate_hetu_bottom_tag_list(hate_hetu_bottom_tags.begin(), hate_hetu_bottom_tags.end());
  context->SetIntListCommonAttr("report_hetu_bottom_tags", std::move(report_hetu_bottom_tag_list));
  context->SetIntListCommonAttr("hate_hetu_bottom_tags", std::move(hate_hetu_bottom_tag_list));
  context->SetIntListCommonAttr("colossus_report_photo_ids", std::move(colossus_report_photo_ids));
  context->SetIntListCommonAttr("colossus_hate_photo_ids", std::move(colossus_hate_photo_ids));
  context->SetIntListCommonAttr("short_term_report_photo_ids", std::move(short_term_report_photo_ids));
  context->SetIntListCommonAttr("short_term_hate_photo_ids", std::move(short_term_hate_photo_ids));
  context->SetIntListCommonAttr("nearby_recent_play_photo_ids", std::move(nearby_recent_play_photo_ids));
  context->SetIntListCommonAttr("nearby_recent_play_author_ids", std::move(nearby_recent_play_author_ids));
  context->SetIntListCommonAttr("nearby_recent_play_actions", std::move(nearby_recent_play_actions));
  context->SetIntListCommonAttr("nearby_recent_play_durations", std::move(nearby_recent_play_durations));
  context->SetIntCommonAttr("nearby_recent_play_counts", nearby_recent_add_counts);

  std::vector<int64> nearby_recent_mask_64(nearby_recent_pid_64.size(), 1);
  nearby_recent_mask_64.resize(64);
  nearby_recent_pid_64.resize(64);
  nearby_recent_aid_64.resize(64);
  nearby_recent_watchtime_64.resize(64);
  nearby_recent_duration_64.resize(64);
  nearby_recent_action_64.resize(64);
  nearby_recent_timestamp_64.resize(64);
  context->SetIntListCommonAttr("nearby_recent_pid_64", std::move(nearby_recent_pid_64));
  context->SetIntListCommonAttr("nearby_recent_aid_64", std::move(nearby_recent_aid_64));
  context->SetIntListCommonAttr("nearby_recent_watchtime_64", std::move(nearby_recent_watchtime_64));
  context->SetIntListCommonAttr("nearby_recent_duration_64", std::move(nearby_recent_duration_64));
  context->SetIntListCommonAttr("nearby_recent_action_64", std::move(nearby_recent_action_64));
  context->SetIntListCommonAttr("nearby_recent_timestamp_64", std::move(nearby_recent_timestamp_64));
  context->SetIntListCommonAttr("nearby_recent_mask_64", std::move(nearby_recent_mask_64));

  std::vector<int64> nearby_click_mask_256(nearby_click_pid_256.size(), 1);
  nearby_click_mask_256.resize(256);
  nearby_click_pid_256.resize(256);
  nearby_click_aid_256.resize(256);
  nearby_click_watchtime_256.resize(256);
  nearby_click_duration_256.resize(256);
  nearby_click_action_256.resize(256);
  nearby_click_timestamp_256.resize(256);
  std::vector<int64> nearby_satis_mask_256(nearby_satis_pid_256.size(), 1);
  nearby_satis_mask_256.resize(256);
  nearby_satis_pid_256.resize(256);
  nearby_satis_aid_256.resize(256);
  nearby_satis_watchtime_256.resize(256);
  nearby_satis_duration_256.resize(256);
  nearby_satis_action_256.resize(256);
  nearby_satis_timestamp_256.resize(256);
  context->SetIntListCommonAttr("nearby_click_pid_256", std::move(nearby_click_pid_256));
  context->SetIntListCommonAttr("nearby_click_aid_256", std::move(nearby_click_aid_256));
  context->SetIntListCommonAttr("nearby_click_watchtime_256", std::move(nearby_click_watchtime_256));
  context->SetIntListCommonAttr("nearby_click_duration_256", std::move(nearby_click_duration_256));
  context->SetIntListCommonAttr("nearby_click_action_256", std::move(nearby_click_action_256));
  context->SetIntListCommonAttr("nearby_click_timestamp_256", std::move(nearby_click_timestamp_256));
  context->SetIntListCommonAttr("nearby_click_mask_256", std::move(nearby_click_mask_256));
  context->SetIntListCommonAttr("nearby_satis_pid_256", std::move(nearby_satis_pid_256));
  context->SetIntListCommonAttr("nearby_satis_aid_256", std::move(nearby_satis_aid_256));
  context->SetIntListCommonAttr("nearby_satis_watchtime_256", std::move(nearby_satis_watchtime_256));
  context->SetIntListCommonAttr("nearby_satis_duration_256", std::move(nearby_satis_duration_256));
  context->SetIntListCommonAttr("nearby_satis_action_256", std::move(nearby_satis_action_256));
  context->SetIntListCommonAttr("nearby_satis_timestamp_256", std::move(nearby_satis_timestamp_256));
  context->SetIntListCommonAttr("nearby_satis_mask_256", std::move(nearby_satis_mask_256));


  context->SetIntListCommonAttr("nearby_like_pids_v2", std::move(nearby_like_pids_v2));
  context->SetIntListCommonAttr("nearby_sati_photo_ids", std::move(nearby_sati_photo_ids));
  context->SetIntListCommonAttr("nearby_eff_click_photo_ids", std::move(nearby_eff_click_photo_ids));
  context->SetIntListCommonAttr("nearby_same_city_click_photo_ids",
        std::move(nearby_same_city_click_photo_ids));
  std::vector<int64> nearby_same_city_eff_click_tag_hetu1_map_key;
  std::vector<int64> nearby_same_city_eff_click_tag_hetu1_map_value;
  std::vector<int64> nearby_same_city_eff_click_tag_hetu2_map_key;
  std::vector<int64> nearby_same_city_eff_click_tag_hetu2_map_value;
  for (const auto& pair : nearby_same_city_eff_click_hetu1_map) {
    nearby_same_city_eff_click_tag_hetu1_map_key.push_back(pair.first);
    nearby_same_city_eff_click_tag_hetu1_map_value.push_back(pair.second);
  }
  for (const auto& pair : nearby_same_city_eff_click_hetu2_map) {
    nearby_same_city_eff_click_tag_hetu2_map_key.push_back(pair.first);
    nearby_same_city_eff_click_tag_hetu2_map_value.push_back(pair.second);
  }
  context->SetIntListCommonAttr("nearby_same_city_eff_click_tag_hetu1_map_key",
      std::move(nearby_same_city_eff_click_tag_hetu1_map_key));
  context->SetIntListCommonAttr("nearby_same_city_eff_click_tag_hetu1_map_value",
      std::move(nearby_same_city_eff_click_tag_hetu1_map_value));
  context->SetIntListCommonAttr("nearby_same_city_eff_click_tag_hetu2_map_key",
      std::move(nearby_same_city_eff_click_tag_hetu2_map_key));
  context->SetIntListCommonAttr("nearby_same_city_eff_click_tag_hetu2_map_value",
      std::move(nearby_same_city_eff_click_tag_hetu2_map_value));
}

void NearbyInitCommonAttrEnricher::EnrichNearbyActionTriggerList(MutableRecoContextInterface *context,
                                                     RecoResultConstIter begin,
                                                     RecoResultConstIter end) {
  int nb_all_action_ids_size =
      GetIntCommonAttrFromContext(context, "nb_all_action_ids_size", 2000);
  int nb_all_same_province_action_ids_size =
        GetIntCommonAttrFromContext(context, "nb_all_same_province_action_ids_size", 1000);
  auto colossus_photo_id = context->GetIntListCommonAttr("colossus_photo_id");
  auto colossus_author_id = context->GetIntListCommonAttr("colossus_author_id");
  auto colossus_channel = context->GetIntListCommonAttr("colossus_channel");
  auto colossus_timestamp = context->GetIntListCommonAttr("colossus_timestamp");
  auto colossus_tag = context->GetIntListCommonAttr("colossus_tag");
  auto colossus_label = context->GetIntListCommonAttr("colossus_label");
  auto colossus_play_time = context->GetIntListCommonAttr("colossus_play_time");
  auto colossus_duration = context->GetIntListCommonAttr("colossus_duration");
  auto colossus_latitude = context->GetDoubleListCommonAttr("colossus_latitude");
  auto colossus_longitude = context->GetDoubleListCommonAttr("colossus_longitude");
  if (!colossus_photo_id || !colossus_channel || !colossus_tag || !colossus_timestamp || !colossus_label ||
      !colossus_play_time || !colossus_duration || !colossus_author_id || !colossus_latitude ||
      !colossus_longitude) {
    CL_LOG(WARNING) << GetName() << " colossus is empty";
    return;
  }
  if (colossus_photo_id->size() == 0) {
     CL_LOG(WARNING) << GetName() << " colossus size = 0";
    return;
  }
  bool size_macth = colossus_photo_id->size() == colossus_channel->size() && colossus_photo_id->size()
    == colossus_timestamp->size() && colossus_photo_id->size() == colossus_tag->size()
    && colossus_photo_id->size() == colossus_label->size()
    && colossus_photo_id->size() == colossus_play_time->size()
    && colossus_photo_id->size() == colossus_duration->size()
    && colossus_photo_id->size() == colossus_author_id->size()
    && colossus_photo_id->size() == colossus_latitude->size()
    && colossus_photo_id->size() == colossus_longitude->size();
  if (!size_macth) {
     CL_LOG(WARNING) << GetName() << " colossus size is not match";
    return;
  }
  std::vector<int64> nb_all_action_ids;
  std::vector<int64> nb_all_same_province_action_ids;
  std::vector<int64> switch_nearby_other_page_pos_ids;
  std::vector<int64> switch_nearby_other_page_neg_ids;
  static const std::unordered_set<int> NEARBY_CHANNEL = {2, 11, 19, 26, 35, 39, 62, 83, 84};
  int find_nearby = 0;
  int64 max_find_other_page_count = GetIntCommonAttrFromContext(context, "max_find_other_page_count", 6);
  int64 switch_tab_feature_max_count =
      GetIntCommonAttrFromContext(context, "switch_tab_feature_max_count", 50);
  int cur_find_other_page_count = 0;
  int user_province_id = GetIntCommonAttrFromContext(context, "user_province_id", 0);

  double switch_tab_play_rate = GetDoubleCommonAttrFromContext(context, "switch_tab_play_rate", 0.7);
  double switch_tab_long_play_thres_second =
      GetDoubleCommonAttrFromContext(context, "switch_tab_long_play_thres_second", 7);

  for (int i = colossus_photo_id->size() - 1; i >= 0; i--) {  // todo
    if (colossus_photo_id->at(i) <= 0) {
      continue;
    }
    uint32 photo_id = colossus_photo_id->at(i);
    uint32 item_label = colossus_label->at(i);
    uint32 channel = colossus_channel->at(i);
    uint32 play_time = colossus_play_time->at(i);
    uint32 duration = colossus_duration->at(i);
    bool like_label = item_label & 0x01;
    bool follow_label = item_label & (1 << 1);
    bool comment_label = item_label & (1 << 4);
    bool report_label = item_label & (1 << 3);
    bool hate_label = item_label & (1 << 13);

    if (i > 0 && find_nearby == 1) {
      if (NEARBY_CHANNEL.count(channel) > 0 &&
          NEARBY_CHANNEL.count(colossus_channel->at(i - 1)) == 0) {  // 当前是同城的，前一个不是
        find_nearby = 0;
      }
    }

    if (find_nearby == 0) {  // 非同城页，存下来。
      if (NEARBY_CHANNEL.count(channel) == 0 && cur_find_other_page_count < max_find_other_page_count) {
        if (report_label || hate_label || (play_time < 3 && duration > 3)) {
          if (switch_nearby_other_page_neg_ids.size() < switch_tab_feature_max_count) {
            switch_nearby_other_page_neg_ids.push_back(photo_id);
          }
        } else if (like_label || follow_label || comment_label ||
                   play_time >= duration * switch_tab_play_rate ||
                   play_time >= switch_tab_long_play_thres_second) {
          if (switch_nearby_other_page_pos_ids.size() < switch_tab_feature_max_count) {
            switch_nearby_other_page_pos_ids.push_back(photo_id);
          }
        }
        cur_find_other_page_count++;
      } else {  // reset
        find_nearby = 1;
        if (i > 0) {
          if (NEARBY_CHANNEL.count(channel) > 0 &&
              NEARBY_CHANNEL.count(colossus_channel->at(i - 1)) == 0) {  // 当前是同城的，前一个不是
            find_nearby = 0;
          }
        }
        cur_find_other_page_count = 0;
      }
    }
    // if (hate_label || report_label) {
    //   continue;
    // }
    // if (nb_all_action_ids.size() >= nb_all_action_ids_size) {
    //   continue;
    // } else {
    //   nb_all_action_ids.push_back(colossus_photo_id->at(i));
    // }
    // if (nb_all_same_province_action_ids.size() >= nb_all_same_province_action_ids_size) {
    //   continue;
    // } else {
    //   uint32 item_region_id = ks::reco::GetRegionId("", colossus_latitude->at(i),
    //   colossus_longitude->at(i)); uint32 item_province_id =
    //   base::RegionDict::LogicProvinceId(item_region_id); if (item_province_id == user_province_id) {
    //     nb_all_same_province_action_ids.push_back(colossus_photo_id->at(i));
    //   }
    // }
  }

  context->SetIntListCommonAttr("switch_nearby_other_page_pos_ids",
                                std::move(switch_nearby_other_page_pos_ids));
  context->SetIntListCommonAttr("switch_nearby_other_page_neg_ids",
                                std::move(switch_nearby_other_page_neg_ids));
  // context->SetIntListCommonAttr("NB_ALL_ACTION_IDS", std::move(nb_all_action_ids));
  // context->SetIntListCommonAttr("NB_ALL_SAME_PROVINCE_ACTION_IDS",
  //   std::move(nb_all_same_province_action_ids));
  // nb_all_action_ids.clear();
  // nb_all_same_province_action_ids.clear();
}


void NearbyInitCommonAttrEnricher::EnrichNearbyActionTriggerList2(MutableRecoContextInterface *context,
                                                     RecoResultConstIter begin,
                                                     RecoResultConstIter end) {
  auto colossus_photo_id = context->GetIntListCommonAttr("colossus_photo_id");
  auto colossus_author_id = context->GetIntListCommonAttr("colossus_author_id");
  auto colossus_channel = context->GetIntListCommonAttr("colossus_channel");
  auto colossus_timestamp = context->GetIntListCommonAttr("colossus_timestamp");
  auto colossus_tag = context->GetIntListCommonAttr("colossus_tag");
  auto colossus_label = context->GetIntListCommonAttr("colossus_label");
  auto colossus_play_time = context->GetIntListCommonAttr("colossus_play_time");
  auto colossus_duration = context->GetIntListCommonAttr("colossus_duration");
  auto colossus_latitude = context->GetDoubleListCommonAttr("colossus_latitude");
  auto colossus_longitude = context->GetDoubleListCommonAttr("colossus_longitude");
  if (!colossus_photo_id || !colossus_channel || !colossus_tag || !colossus_timestamp || !colossus_label ||
      !colossus_play_time || !colossus_duration || !colossus_author_id || !colossus_latitude ||
      !colossus_longitude) {
    CL_LOG(WARNING) << GetName() << " colossus is empty";
    return;
  }
  if (colossus_photo_id->size() == 0) {
     CL_LOG(WARNING) << GetName() << " colossus size = 0";
    return;
  }
  bool size_macth = colossus_photo_id->size() == colossus_channel->size() && colossus_photo_id->size()
    == colossus_timestamp->size() && colossus_photo_id->size() == colossus_tag->size()
    && colossus_photo_id->size() == colossus_label->size()
    && colossus_photo_id->size() == colossus_play_time->size()
    && colossus_photo_id->size() == colossus_duration->size()
    && colossus_photo_id->size() == colossus_author_id->size()
    && colossus_photo_id->size() == colossus_latitude->size()
    && colossus_photo_id->size() == colossus_longitude->size();
  if (!size_macth) {
     CL_LOG(WARNING) << GetName() << " colossus size is not match";
    return;
  }
  std::vector<int64> pos_pids;
  std::vector<int64> blank_index;
  std::vector<int64> neg_pids;
  // 给了新的命名
  std::vector<int64> switch_nearby_pub_last_pos_ids;
  std::vector<int64> switch_nearby_pub_last_blank_pos_ids;
  std::vector<int64> switch_nearby_pub_last_blank_pos_aids;
  std::vector<int64> switch_nearby_pub_last_blank_pos_tag;
  std::vector<int64> switch_nearby_pub_last_blank_pos_play_time;
  std::vector<int64> switch_nearby_pub_last_blank_pos_duration;
  std::vector<double> switch_nearby_pub_last_blank_pos_latitude;
  std::vector<double> switch_nearby_pub_last_blank_pos_longitude;
  std::vector<int64> switch_nearby_pub_last_blank_pos_label;
  std::vector<int64> switch_nearby_pub_last_neg_ids;
  std::vector<int64> switch_nearby_pub_last_blank_neg_ids;
  std::vector<int64> switch_nearby_pub_last_blank_neg_aids;
  std::vector<int64> switch_nearby_pub_last_blank_neg_tag;
  std::vector<int64> switch_nearby_pub_last_blank_neg_play_time;
  std::vector<int64> switch_nearby_pub_last_blank_neg_duration;
  std::vector<double> switch_nearby_pub_last_blank_neg_latitude;
  std::vector<double> switch_nearby_pub_last_blank_neg_longitude;
  std::vector<int64> switch_nearby_pub_last_blank_neg_label;
  // 当日零播
  int nearby_today_no_play_flag =
    GetIntCommonAttrFromContext(context, "nearby_today_no_play_flag", -1);
  uint64 midnightTimestampInMicro
    = GetIntCommonAttrFromContext(context, "midNightTimestampInMicro", 0);
  static const std::unordered_set<int> NEARBY_CHANNEL = {2, 11, 19, 26, 35, 39, 62, 83, 84};
  bool nearby_long_stay = true;
  int nearby_counts = 0;
  double switch_tab_play_rate = GetDoubleCommonAttrFromContext(context, "switch_tab_play_rate", 0.7);
  double switch_tab_long_play_thres_second =
      GetDoubleCommonAttrFromContext(context, "switch_tab_long_play_thres_second", 7);
  // for (int i = colossus_photo_id->size() - 1; i >= 0 && i > colossus_photo_id->size() - 6; i--) {
  //   int64_t channel = colossus_channel->at(i);
  //   if (NEARBY_CHANNEL.count(channel) == 0) {
  //     nearby_long_stay = false;
  //     break;
  //   }
  // }
  nearby_long_stay = false;
  static const std::unordered_set<int> HOT_CHANNEL = {37, 38};
  if (nearby_long_stay == false) {
    for (auto i = colossus_photo_id->size() - 1; i >= 0 && i > colossus_photo_id->size() - 101; i--) {
      int64_t channel = colossus_channel->at(i);
      int64_t photo_id = colossus_photo_id->at(i);
      int64_t item_label = colossus_label->at(i);
      int64_t play_time = colossus_play_time->at(i);
      int64_t duration = colossus_duration->at(i);
      int64_t timestamp = colossus_timestamp->at(i);
      bool like_label = item_label & 0x01;
      bool follow_label = item_label & (1 << 1);
      bool comment_label = item_label & (1 << 4);
      if (nearby_today_no_play_flag != 0 && NEARBY_CHANNEL.count(channel) > 0
          && midnightTimestampInMicro > timestamp * 1000 * 1000) {
        // 当日的播放
        nearby_today_no_play_flag = 0;
      }
      if (NEARBY_CHANNEL.count(channel) > 0) {
        nearby_counts++;
        if (nearby_counts >= 2 && pos_pids.size() >= 10) {
          break;
        }
      }
      // 非同城
      if (NEARBY_CHANNEL.count(channel) == 0) {
        if (like_label || follow_label || comment_label ||
            play_time >= duration * switch_tab_play_rate ||
            play_time >= switch_tab_long_play_thres_second) {
          pos_pids.push_back(photo_id);
        } else {
          neg_pids.push_back(photo_id);
        }
      }
    }
    for (auto i = colossus_photo_id->size() - 1; i >= 0 && i > colossus_photo_id->size() - 501; i--) {
      int64_t channel = colossus_channel->at(i);
      if (NEARBY_CHANNEL.count(channel) == 0 && HOT_CHANNEL.count(channel) > 0) {
        blank_index.push_back(i);
      }
    }
  }
  if (nearby_today_no_play_flag != 0) {
    for (auto i = colossus_photo_id->size() - 1; i > 0 && i <= colossus_photo_id->size() - 1; i--) {
      int64_t timestamp = colossus_timestamp->at(i);
      int64_t channel = colossus_channel->at(i);
      if (midnightTimestampInMicro > timestamp * 1000 * 1000) {
        // 当日的播放
        if (NEARBY_CHANNEL.count(channel) > 0) {
          nearby_today_no_play_flag = 0;
        }
      } else {
        break;
      }
    }
  }
  base::perfutil::PerfUtilWrapper::CountLogStash(1, "reco.nearby", "nearby_today_no_play_flag.count",
    "colossus_falg." + std::to_string(nearby_today_no_play_flag));
  context->SetIntCommonAttr("nearby_today_no_play_flag", nearby_today_no_play_flag);
  int64_t pid = 0;
  std::reverse(pos_pids.begin(), pos_pids.end());
  for (int j = 0; j < 10; j ++) {
    if (pos_pids.size() > 0) {
      pid = pos_pids.back();
      pos_pids.pop_back();
      switch_nearby_pub_last_pos_ids.push_back(pid);
    } else {
      break;
    }
  }
  bool exist_flag = true;
  std::reverse(blank_index.begin(), blank_index.end());
  for (int j = 0; j < 10; j ++) {
    if (blank_index.size() > 0) {
      int64_t start_ts = base::GetTimestamp();
      int64_t index = blank_index.back();
      if (j == 0 && start_ts - colossus_timestamp->at(index) * 1000000 > 10 * 60 * 1000000) {
        exist_flag = false;
        break;
      }
      blank_index.pop_back();
      switch_nearby_pub_last_blank_pos_ids.push_back(colossus_photo_id->at(index));
      switch_nearby_pub_last_blank_pos_aids.push_back(colossus_author_id->at(index));
      switch_nearby_pub_last_blank_pos_tag.push_back(colossus_tag->at(index));
      switch_nearby_pub_last_blank_pos_play_time.push_back(colossus_play_time->at(index));
      switch_nearby_pub_last_blank_pos_duration.push_back(colossus_duration->at(index));
      switch_nearby_pub_last_blank_pos_latitude.push_back(colossus_latitude->at(index));
      switch_nearby_pub_last_blank_pos_longitude.push_back(colossus_longitude->at(index));
      switch_nearby_pub_last_blank_pos_label.push_back(colossus_label->at(index));
    } else {
      break;
    }
  }
  std::reverse(blank_index.begin(), blank_index.end());
  for (int j = 0; j < 10; j ++) {
    if (blank_index.size() > 0 && exist_flag) {
      int64_t index = blank_index.back();
      blank_index.pop_back();
      switch_nearby_pub_last_blank_neg_ids.push_back(colossus_photo_id->at(index));
      switch_nearby_pub_last_blank_neg_aids.push_back(colossus_author_id->at(index));
      switch_nearby_pub_last_blank_neg_tag.push_back(colossus_tag->at(index));
      switch_nearby_pub_last_blank_neg_play_time.push_back(colossus_play_time->at(index));
      switch_nearby_pub_last_blank_neg_duration.push_back(colossus_duration->at(index));
      switch_nearby_pub_last_blank_neg_latitude.push_back(colossus_latitude->at(index));
      switch_nearby_pub_last_blank_neg_longitude.push_back(colossus_longitude->at(index));
      switch_nearby_pub_last_blank_neg_label.push_back(colossus_label->at(index));
    } else {
      break;
    }
  }
  for (int j = 0; j < 10; j ++) {
    if (neg_pids.size() > 0) {
      pid = neg_pids.back();
      neg_pids.pop_back();
      switch_nearby_pub_last_neg_ids.push_back(pid);
    } else {
      break;
    }
  }
  context->SetIntListCommonAttr("switch_nearby_pub_last_pos_ids",
                                std::move(switch_nearby_pub_last_pos_ids));
  context->SetIntListCommonAttr("switch_nearby_pub_last_neg_ids",
                                std::move(switch_nearby_pub_last_neg_ids));
  context->SetIntListCommonAttr("switch_nearby_pub_last_blank_pos_ids",
                                std::move(switch_nearby_pub_last_blank_pos_ids));
  context->SetIntListCommonAttr("switch_nearby_pub_last_blank_pos_aids",
                                  std::move(switch_nearby_pub_last_blank_pos_aids));
  context->SetIntListCommonAttr("switch_nearby_pub_last_blank_pos_tag",
                                  std::move(switch_nearby_pub_last_blank_pos_tag));
  context->SetIntListCommonAttr("switch_nearby_pub_last_blank_pos_play_time",
                                  std::move(switch_nearby_pub_last_blank_pos_play_time));
  context->SetIntListCommonAttr("switch_nearby_pub_last_blank_pos_duration",
                                  std::move(switch_nearby_pub_last_blank_pos_duration));
  context->SetDoubleListCommonAttr("switch_nearby_pub_last_blank_pos_latitude",
                                  std::move(switch_nearby_pub_last_blank_pos_latitude));
  context->SetDoubleListCommonAttr("switch_nearby_pub_last_blank_pos_longitude",
                                  std::move(switch_nearby_pub_last_blank_pos_longitude));
  context->SetIntListCommonAttr("switch_nearby_pub_last_blank_pos_label",
                                  std::move(switch_nearby_pub_last_blank_pos_label));
  context->SetIntListCommonAttr("switch_nearby_pub_last_blank_neg_ids",
                                std::move(switch_nearby_pub_last_blank_neg_ids));
  context->SetIntListCommonAttr("switch_nearby_pub_last_blank_neg_aids",
                                  std::move(switch_nearby_pub_last_blank_neg_aids));
  context->SetIntListCommonAttr("switch_nearby_pub_last_blank_neg_tag",
                                  std::move(switch_nearby_pub_last_blank_neg_tag));
  context->SetIntListCommonAttr("switch_nearby_pub_last_blank_neg_play_time",
                                  std::move(switch_nearby_pub_last_blank_neg_play_time));
  context->SetIntListCommonAttr("switch_nearby_pub_last_blank_neg_duration",
                                  std::move(switch_nearby_pub_last_blank_neg_duration));
  context->SetDoubleListCommonAttr("switch_nearby_pub_last_blank_neg_latitude",
                                  std::move(switch_nearby_pub_last_blank_neg_latitude));
  context->SetDoubleListCommonAttr("switch_nearby_pub_last_blank_neg_longitude",
                                  std::move(switch_nearby_pub_last_blank_neg_longitude));
  context->SetIntListCommonAttr("switch_nearby_pub_last_blank_neg_label",
                                    std::move(switch_nearby_pub_last_blank_neg_label));
}
void NearbyInitCommonAttrEnricher::AddSimActionToMap(int sim_tag, int64 sim_pid, int64 sim_aid,
        bool hate_label, bool report_label, int nearby_each_tag_max_sim_count) {
    if (report_label || hate_label) {
      return;;
    }
    if (sim_tag_pid_map_.find(sim_tag) == sim_tag_pid_map_.end() &&
            sim_tag_aid_map_.find(sim_tag) == sim_tag_aid_map_.end()) {
      sim_tag_pid_map_.insert(std::make_pair(sim_tag, std::vector<int64>()));
      sim_tag_aid_map_.insert(std::make_pair(sim_tag, std::vector<int64>()));
      sim_tag_tag_map_.insert(std::make_pair(sim_tag, std::vector<int64>()));
      sim_tag_set_.insert(sim_tag);
    }
    if (sim_tag_pid_map_.at(sim_tag).size() <= nearby_each_tag_max_sim_count &&
                sim_tag_aid_map_.at(sim_tag).size() <= nearby_each_tag_max_sim_count) {
      sim_tag_pid_map_.at(sim_tag).push_back(sim_pid);
      sim_tag_aid_map_.at(sim_tag).push_back(sim_aid);
      sim_tag_tag_map_.at(sim_tag).push_back((int64)sim_tag);
    }
  }
void NearbyInitCommonAttrEnricher::EnrichAuthorCoss(MutableRecoContextInterface *context) {
    auto last_action_info_str = context->GetStringCommonAttr("nearby_reco_user_info.last_action_info");
    int implicit_neg_use_realshow_not_click =
      GetIntCommonAttrFromContext(context, "implicit_neg_use_realshow_not_click", 0);
    int colossus_write_maxsize = GetIntCommonAttrFromContext(context, "colossus_write_maxsize", 100);
    int explicit_neg_without_pos = GetIntCommonAttrFromContext(context, "explicit_neg_without_pos", 0);
    double colossus_complete_play_thres = GetDoubleCommonAttrFromContext(context,
                                  "colossus_complete_play_thres", 0.7);
    int colossus_load_photo_maxn = GetIntCommonAttrFromContext(context, "colossus_load_photo_maxn", 1000);
    double colossus_pos_author_thres =
      GetDoubleCommonAttrFromContext(context, "colossus_pos_author_thres", 0.3);
    double colossus_implicit_neg_author_thres =
        GetDoubleCommonAttrFromContext(context, "colossus_implicit_neg_author_thres", 1.0);
    int use_comment_stay_time_for_pos =
        GetIntCommonAttrFromContext(context, "use_comment_stay_time_for_pos", 0);
    ks::reco::LastActionInfo last_action_info;
    std::unordered_map<int64, int> pos_author_ids;
    std::unordered_map<int64, int> implicit_neg_author_ids;
    std::unordered_map<int64, int> explicit_neg_author_ids;
    auto colossus_photo_id = context->GetIntListCommonAttr("colossus_photo_id");
    auto colossus_author_id = context->GetIntListCommonAttr("colossus_author_id");
    auto colossus_channel = context->GetIntListCommonAttr("colossus_channel");
    auto colossus_timestamp = context->GetIntListCommonAttr("colossus_timestamp");
    auto colossus_tag = context->GetIntListCommonAttr("colossus_tag");
    auto colossus_label = context->GetIntListCommonAttr("colossus_label");
    auto colossus_play_time = context->GetIntListCommonAttr("colossus_play_time");
    auto colossus_duration = context->GetIntListCommonAttr("colossus_duration");
    auto colossus_latitude = context->GetDoubleListCommonAttr("colossus_latitude");
    auto colossus_longitude = context->GetDoubleListCommonAttr("colossus_longitude");
    if (!colossus_photo_id || !colossus_channel || !colossus_tag || !colossus_timestamp || !colossus_label ||
        !colossus_play_time || !colossus_duration || !colossus_author_id || !colossus_latitude ||
        !colossus_longitude) {
      CL_LOG(WARNING) << GetName() << " colossus is empty";
      return;
    }
    if (last_action_info_str) {
      last_action_info.ParseFromArray(last_action_info_str->data(), last_action_info_str->size());
    }
    for (auto& feed : last_action_info.last_liked_feed()) {
      if (feed.feed_type() == 1) {
        int64 author_id = feed.author_id();
        if (pos_author_ids.find(author_id) == pos_author_ids.end()) {
          pos_author_ids.insert({author_id, 1});
        } else {
          pos_author_ids[author_id]++;
        }
      }
    }
    for (auto& feed : last_action_info.last_followed_feed()) {
      if (feed.feed_type() == 1) {
        int64 author_id = feed.author_id();
        if (pos_author_ids.find(author_id) == pos_author_ids.end()) {
          pos_author_ids.insert({author_id, 1});
        } else {
          pos_author_ids[author_id]++;
        }
      }
    }
    for (auto& feed : last_action_info.last_long_view_feed()) {
      if (feed.feed_type() == 1) {
        int64 author_id = feed.author_id();
        if (pos_author_ids.find(author_id) == pos_author_ids.end()) {
          pos_author_ids.insert({author_id, 1});
        } else {
          pos_author_ids[author_id]++;
        }
      }
    }
    if (implicit_neg_use_realshow_not_click > 0) {
      std::unordered_set<int64> clicked_author;
      for (auto &feed : last_action_info.last_clicked_feed()) {
        if (feed.feed_type() == 1) {
          int64 author_id = feed.author_id();
          clicked_author.insert(author_id);
        }
      }
      for (auto &feed : last_action_info.last_real_show_feed()) {
        if (feed.feed_type() == 1) {
          int64 author_id = feed.author_id();
          if (clicked_author.count(author_id) > 0) {
            continue;
          }
          if (implicit_neg_author_ids.find(author_id) == implicit_neg_author_ids.end()) {
            implicit_neg_author_ids.insert({author_id, 1});
          } else {
            implicit_neg_author_ids[author_id]++;
          }
        }
      }
    } else {
      for (auto &feed : last_action_info.last_short_view_feed()) {
        if (feed.feed_type() == 1) {
          int64 author_id = feed.author_id();
          if (implicit_neg_author_ids.find(author_id) == implicit_neg_author_ids.end()) {
            implicit_neg_author_ids.insert({author_id, 1});
          } else {
            implicit_neg_author_ids[author_id]++;
          }
        }
      }
    }
    for (auto& feed : last_action_info.last_negative_feed()) {
      if (feed.feed_type() == 1) {
        int64 author_id = feed.author_id();
        if (explicit_neg_author_ids.find(author_id) == explicit_neg_author_ids.end()) {
          explicit_neg_author_ids.insert({author_id, 1});
        } else {
          explicit_neg_author_ids[author_id]++;
        }
      }
    }
    int minn = colossus_photo_id->size() - colossus_load_photo_maxn;
    for (int i = colossus_photo_id->size() - 1; i >= 0; i--) {
      if (i < minn) {
        break;
      }
      uint32 item_label = colossus_label->at(i);
      bool like_label = item_label & 0x01;
      bool follow_label = item_label & (1 << 1);
      bool comment_label = item_label & (1 << 4);
      bool report_label = item_label & (1 << 3);
      bool hate_label = item_label & (1 << 13);
      bool has_comment_stay_time_value = item_label & (1 << 8);
      int64 author_id = colossus_author_id->at(i);
      if (hate_label && author_id > 0) {
        if (explicit_neg_author_ids.find(author_id) == explicit_neg_author_ids.end()) {
          explicit_neg_author_ids.insert({author_id, 1});
        } else {
          explicit_neg_author_ids[author_id]++;
        }
      }
      if (report_label && author_id > 0) {
        if (explicit_neg_author_ids.find(author_id) == explicit_neg_author_ids.end()) {
          explicit_neg_author_ids.insert({author_id, 1});
        } else {
          explicit_neg_author_ids[author_id]++;
        }
      }
      if (implicit_neg_use_realshow_not_click == 0) {
        if (!like_label && !follow_label && !comment_label) {
          if (colossus_play_time->at(i) <= 5 ||
              (colossus_play_time->at(i) <= 12 && colossus_play_time->at(i) < colossus_duration->at(i))) {
            if (implicit_neg_author_ids.find(author_id) == implicit_neg_author_ids.end()) {
              implicit_neg_author_ids.insert({author_id, 1});
            } else {
              implicit_neg_author_ids[author_id]++;
            }
            continue;
          }
        }
      }
      if (like_label || follow_label || comment_label ||
          (use_comment_stay_time_for_pos == 1 && has_comment_stay_time_value) ||
          colossus_play_time->at(i) >= colossus_duration->at(i) * colossus_complete_play_thres) {
        if (pos_author_ids.find(author_id) == pos_author_ids.end()) {
          pos_author_ids.insert({author_id, 1});
        } else {
          pos_author_ids[author_id]++;
        }
      }
    }
    std::vector<std::pair<int64, int>> pos_author_ids_pair;
    SortMapByValue(pos_author_ids, &pos_author_ids_pair);
    std::vector<std::pair<int64, int>> implicit_neg_author_ids_pair;
    SortMapByValue(implicit_neg_author_ids, &implicit_neg_author_ids_pair);
    std::vector<std::pair<int64, int>> explicit_neg_author_ids_pair;
    SortMapByValue(explicit_neg_author_ids, &explicit_neg_author_ids_pair);
    std::stringstream ss;
    std::vector<int64> colossus_pos_author_ids;
    int add_size = 0;
    for (int i = 0; i < pos_author_ids_pair.size() * colossus_pos_author_thres; ++i) {
      if (add_size > colossus_write_maxsize) {
        break;
      }
      if (pos_author_ids_pair[i].second > 1) {
        colossus_pos_author_ids.push_back(pos_author_ids_pair[i].first);
        add_size++;
        ss << pos_author_ids_pair[i].first << ":" << pos_author_ids_pair[i].second << ",";
      }
    }
    std::string colossus_pos_author_kv = ss.str();
    colossus_pos_author_kv = colossus_pos_author_kv.substr(0, colossus_pos_author_kv.size()-1);
    ss.clear();
    add_size = 0;
    for (int i = 0; i < implicit_neg_author_ids_pair.size() * colossus_implicit_neg_author_thres; ++i) {
      if (add_size > colossus_write_maxsize) {
        break;
      }
      add_size++;
      ss << implicit_neg_author_ids_pair[i].first << ":" << implicit_neg_author_ids_pair[i].second << ",";
    }
    std::string colossus_implicit_neg_author_kv = ss.str();
    colossus_implicit_neg_author_kv =
        colossus_implicit_neg_author_kv.substr(0, colossus_implicit_neg_author_kv.size() - 1);
    ss.clear();
    add_size = 0;
    for (int i = 0; i < explicit_neg_author_ids_pair.size(); ++i) {
      if (add_size > colossus_write_maxsize) {
        break;
      }
      if (explicit_neg_author_ids_pair[i].second > 1) {
        if (explicit_neg_without_pos == 1 &&
            pos_author_ids.find(explicit_neg_author_ids_pair[i].first) != pos_author_ids.end()) {
          continue;
        }
        add_size++;
        ss << explicit_neg_author_ids_pair[i].first << ":" << explicit_neg_author_ids_pair[i].second << ",";
      }
    }
    std::string colossus_explicit_neg_author_kv = ss.str();
    colossus_explicit_neg_author_kv =
        colossus_explicit_neg_author_kv.substr(0, colossus_explicit_neg_author_kv.size() - 1);
    context->SetIntListCommonAttr("colossus_pos_author_ids", std::move(colossus_pos_author_ids));
    context->SetStringCommonAttr("colossus_pos_author_kv", colossus_pos_author_kv);
    context->SetStringCommonAttr("colossus_implicit_neg_author_kv", colossus_implicit_neg_author_kv);
    context->SetStringCommonAttr("colossus_explicit_neg_author_kv", colossus_explicit_neg_author_kv);
}

void NearbyInitCommonAttrEnricher::EnrichSimTagActionList(MutableRecoContextInterface *context) {
  int nearby_sim_max_tag_num = GetIntCommonAttrFromContext(context,
      "nearby_sim_max_tag_num", 5);
  int sim_tag_num_index = 0;
  for (auto tag : sim_tag_set_) {
    if (sim_tag_num_index >= nearby_sim_max_tag_num) {
      break;
    }
    if (sim_tag_pid_map_.find(tag) != sim_tag_pid_map_.end() &&
            sim_tag_aid_map_.find(tag) != sim_tag_aid_map_.end()) {
      std::string pid_name = "sim_action_photo_ids_tag" + std::to_string(sim_tag_num_index);
      std::string aid_name = "sim_action_author_ids_tag" + std::to_string(sim_tag_num_index);
      std::string tag_name = "sim_action_tag_ids_tag" + std::to_string(sim_tag_num_index);
      context->SetIntListCommonAttr(pid_name, std::move(sim_tag_pid_map_.at(tag)));
      context->SetIntListCommonAttr(aid_name, std::move(sim_tag_aid_map_.at(tag)));
      context->SetIntListCommonAttr(tag_name, std::move(sim_tag_tag_map_.at(tag)));
      sim_tag_num_index++;
    }
  }
  sim_tag_pid_map_.clear();
  sim_tag_aid_map_.clear();
  sim_tag_tag_map_.clear();
  sim_tag_set_.clear();
}
void NearbyInitCommonAttrEnricher::EnrichStrongInterestTriggerWithTimeSeries(
  MutableRecoContextInterface *context) {
  static const std::vector<std::string> strong_action_name_vec = {"uLikeListNear", "uLongPlayListNear",
      "uLikeListNearInside", "uLikeListBlNear", "last_feed_action_list"};
  int max_len = 0;
  for (auto &action_name : strong_action_name_vec) {
    auto action_list = context->GetIntListCommonAttr(action_name);
    int action_list_size = 0;
    if (action_list) {
      action_list_size = action_list->size();
    }
    if (max_len < action_list_size) {
      max_len = action_list_size;
    }
  }
  std::vector<int64> strong_interest_snake_merge_vec;
  std::unordered_set<int64> strong_interest_snake_merge_set;
  int val_idx = 0;
  while (val_idx < max_len) {
    for (auto &action_name : strong_action_name_vec) {
      auto action_list = context->GetIntListCommonAttr(action_name);
      if (action_list && val_idx < action_list->size()) {
        auto &item = action_list->at(val_idx);
        if (strong_interest_snake_merge_set.count(item) > 0) {
          continue;
        }
        strong_interest_snake_merge_vec.push_back(item);
        strong_interest_snake_merge_set.insert(item);
      }
    }
    ++val_idx;
  }
  context->SetIntListCommonAttr("strong_interest_snake_merge_list",
  std::move(strong_interest_snake_merge_vec));
}
void NearbyInitCommonAttrEnricher::FillUserLongTermHetuFeature(MutableRecoContextInterface *context,
      const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >& hetu_id,
      const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >& hetu_score,
      const std::string &name,
      int hetu_id_topn,
      int legal_threshold) {
  if (hetu_id.size() == hetu_score.size()) {
    std::vector<std::pair<int, int> > hetu_id2scores;
    for (int i = 0; i < hetu_id.size(); ++i) {
      if (hetu_score[i] >= 0) {
        hetu_id2scores.emplace_back(std::make_pair(hetu_id[i], hetu_score[i]));
      }
    }
    std::sort(hetu_id2scores.begin(), hetu_id2scores.end(),
                 [](const auto &p1, const auto &p2) { return p1.second > p2.second; });

    std::vector<int64> long_term_hetu_topn;
    for (int i = 0; i < hetu_id2scores.size(); ++i) {
      if (long_term_hetu_topn.size() >= hetu_id_topn) {
        break;
      }
      long_term_hetu_topn.emplace_back((int64) hetu_id2scores[i].first);
    }
    std::string key_name = "uLongTermHetuLevel" + name + "topN";
    context->SetIntListCommonAttr(key_name, std::move(long_term_hetu_topn));
  }
}

int64 NearbyInitCommonAttrEnricher::IsGrUser(int64 user_id, int64 user_city_id) {
  auto black_list = ks::reco::RecoConfigKey::reco_nearby_nearbyGrBlackList()->Get();
  if (black_list->count(user_id) > 0) {
    return 0;
  }
  auto white_list = ks::reco::RecoConfigKey::reco_nearby_nearbyGrWhiteList()->Get();
  if (white_list->count(user_id) > 0) {
    return 1;
  }
  auto gr_city_ids = ks::reco::RecoConfigKey::reco_nearby_nearbyGrCityIdSet()->Get();

  return gr_city_ids->count(user_city_id) > 0 ? 1 : 0;
}

bool NearbyInitCommonAttrEnricher::LoadHetuCateMap() {
  auto cate_map = ks::reco::RecoConfigKey::reco_model_HetuCateV2TraceV1()->Get();
  if (!cate_map->isObject()) {
    return false;
  }

  ::Json::Value nullValue;
  std::vector<int> counter(5, 0);
  for (const auto &key : cate_map->getMemberNames()) {
    int tag = 0;
    if (absl::SimpleAtoi(key, &tag)) {
      std::vector<int> v(5, -1);
      hetu_cate_map_.emplace(tag, std::move(v));
      if (hetu_cate_map_.count(tag) > 0) {
        auto &parent_tags = hetu_cate_map_.at(tag);
        const auto &parents = cate_map->get(key, nullValue);
        if (!parents.isObject() || parents.isNull()) continue;
        auto set_hetu_level = [&parents, &parent_tags, &counter, &nullValue](int level) mutable {
          const auto &c = parents.get(std::to_string(level), nullValue);
          if (!c.isNull() && c.isString()) {
            int ele = 0;
            if (absl::SimpleAtoi(c.asString(), &ele)) {
              parent_tags[level - 1] = ele;
            }
            counter[level - 1]++;
          }
        };
        set_hetu_level(1);
        set_hetu_level(2);
        set_hetu_level(3);
      }
    }
  }
  return true;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, NearbyInitCommonAttrEnricher,
                 NearbyInitCommonAttrEnricher)
}  // namespace platform
}  // namespace ks

