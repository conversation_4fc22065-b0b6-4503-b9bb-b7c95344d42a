#pragma once

#include <math.h>
#include <algorithm>
#include <cmath>
#include <cstdlib>
#include <ctime>
#include <limits>
#include <memory>
#include <random>
#include <set>
#include <sstream>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>
#include <functional>
#include <map>
#include "dragon/src/module/common_reco_light_function.h"
#include "dragon/src/processor/ext/nearby/util/nearby_dragon_util.h"
#include "dragon/src/processor/ext/nearby/util/json_helper.h"
#include "folly/container/F14Set.h"
#include "ks/realtime_reco/index/cdoc_convertor.h"
#include "ks/reco/bt_embedding_server/client/bt_embedding_client.h"
#include "ks/reco_proto/proto/mmu_user_category.pb.h"
#include "ks/reco_proto/proto/mmu_user_tag.pb.h"
#include "ks/reco_pub/reco/util/config_key.h"
#include "ks/reco_pub/reco/util/util.h"

#define SLIDE_MC_PXTR_MAX_SIZE 3000
#define SLIDE_MC_PXTR_MAX_SIZE_V2 2000
#define PRERANK_PXTR_MAX_SIZE 6000


bool check_valid(const std::vector<int64>& data) {
    int64 sum = 0;
    for (const auto& d : data) sum += d;
    return std::abs(sum) > 0;
}

bool check_valid(const std::vector<double>& data) {
    double sum = 0.0;
    for (const auto& d : data) sum += d;
    return std::abs(sum) > 1e-7;
}

std::vector<int64> sample_data(int64 M, int64 N) {
  std::vector<int64> result;
  result.reserve(N);

  if (N * 4 < M) {
    std::mt19937 rng(std::chrono::system_clock::now().time_since_epoch().count());
    std::uniform_int_distribution<int64> dist(0, M - 1);
    std::vector<bool> selected(M, false);

    while (result.size() < N) {
      int64 num = dist(rng);
      if (!selected[num]) {
        selected[num] = true;
        result.push_back(num);
      }
    }
  } else {
    std::vector<int64> elements(M);
    for (int64 i = 0; i < M; ++i) {
      elements[i] = i;
    }

    std::mt19937 rng(std::chrono::system_clock::now().time_since_epoch().count());

    for (int64 i = 0; i < N; ++i) {
      std::uniform_int_distribution<int64> dist(i, M - 1);
      int64 j = dist(rng);
      std::swap(elements[i], elements[j]);
    }

    result.assign(elements.begin(), elements.begin() + N);
  }

  return result;
}

template <typename T>
int64 topn_pnr_pair(const std::vector<T> &data, int64 topN) {
  std::vector<T> pos(data.begin(), data.begin() + topN);
  std::vector<T> neg(data.begin() + topN, data.end());
  std::sort(pos.begin(), pos.end());
  std::sort(neg.begin(), neg.end());

  int64 pnr_pair = 0;
  for (int l = 0, r = 0; l < pos.size() && r < neg.size();) {
    if (pos[l] < neg[r]) {
      pnr_pair += neg.size() - r;
      l++;
    } else {
      r++;
    }
  }

  return pnr_pair;
}
template<typename T>
int64 merge_sort(int l, int r, std::vector<T>* record, std::vector<T>* tmp) {
  if (l >= r - 1) return 0;

  int m = (l + r) / 2;
  // fmt::print("==== l:{} r:{} m:{} ====\n", l, r, m);
  int left = merge_sort(l, m, record, tmp);
  int right = merge_sort(m, r, record, tmp);
  int mid = 0;

  for (int i = l; i < r; ++i) {
    tmp->at(i) = record->at(i);
  }
  // fmt::print("before - record:{}\n", record);

  for (int i = l, j = m, k = l; k < r; ++k) {
    if (i == m) {
      record->at(k) = tmp->at(j++);
    } else if (j == r || tmp->at(i) >= tmp->at(j)) {
      record->at(k) = tmp->at(i++);
    } else {
      // fmt::print("----mid:{} r:{} j:{}---\n", mid, r, j);
      record->at(k) = tmp->at(j++);
      mid += m - i;
    }
  }
  // fmt::print("after - record:{}\n", record);
  // fmt::print("l:{} r:{} m:{} left:{} right:{} mid:{}\n", l, r, m, left, right, mid);

  return left + right + mid;
}
template<typename T>
int64 merge_sort(const std::vector<T>& data) {
    std::vector<T> tmp_data1(data), tmp_data2(data);
    return merge_sort(0, tmp_data1.size(), &tmp_data1, &tmp_data2);
}

static void container_print(std::string name, std::vector<double> data) {
  std::string data_str;
  double mean = 0.0;
  LOG(INFO) << "name:" << data.size();
  for (auto &d : data) {
    data_str += std::to_string(d) += ", ";
    mean += d;
  }
  LOG(INFO) << "\n" + name + ":"
            << "\n\tmean:" << mean << "\n\tdata:" << data_str;
}
static void container_print(std::string name, std::vector<int64> data) {
  std::string data_str;
  std::unordered_set<int64> data_set;
  for (auto &d : data) {
    data_str += std::to_string(d) += ", ";
    data_set.insert(d);
  }
  LOG(INFO) << "\n" + name + ":"
            << "\n\tdistinct_num:" << data_set.size() << "\n\tdata:" << data_str;
}
static void container_print(std::string name, std::vector<std::string> data) {
  std::string data_str;
  std::unordered_set<std::string> data_set;
  for (auto &d : data) {
    data_str += d += ", ";
    data_set.insert(d);
  }
  LOG(INFO) << "\n" + name + ":"
            << "\n\tdistinct_num:" << data_set.size() << "\n\tdata:" << data_str;
}
template<typename T>
std::vector<int64> argsort(const std::vector<T>& data) {
    std::vector<int64> indices(data.size());
    std::iota(indices.begin(), indices.end(), 0);

    std::sort(indices.begin(), indices.end(),
              [&data](int64 i, int64 j) {
                  return data[i] > data[j];
              });
    return indices;
}

namespace ks {
namespace platform {
// 为防止后面文件过大,该类按功能用于粗排截断之前的 enrich 操作
class NearbyLightFunctionSet : public CommonRecoBaseLightFunctionSet {
 public:
  NearbyLightFunctionSet() {
    REGISTER_LIGHT_FUNCTION(CalIsReview);
    REGISTER_LIGHT_FUNCTION(CalSafeLevelFilter);
    REGISTER_LIGHT_FUNCTION(CalMaxPhotoDistance);
    REGISTER_LIGHT_FUNCTION(CalNebulaPhotoMcPxtr);
    REGISTER_LIGHT_FUNCTION(CalMainPhotoMcPxtr);
    REGISTER_LIGHT_FUNCTION(CalNebulaLiveMcPxtr);
    REGISTER_LIGHT_FUNCTION(CalMainLiveMcPxtr);
    REGISTER_LIGHT_FUNCTION(CalNebulaPhotoMcEnsembleScore);
    REGISTER_LIGHT_FUNCTION(CalMainPhotoMcEnsembleScore);
    REGISTER_LIGHT_FUNCTION(CalNebulaLiveMcEnsembleScore);
    REGISTER_LIGHT_FUNCTION(CalIsSameCity);
    REGISTER_LIGHT_FUNCTION(CalIsSameCityRaw);
    REGISTER_LIGHT_FUNCTION(CalIsSameCitySlide);
    REGISTER_LIGHT_FUNCTION(CalIsFansValid);
    REGISTER_LIGHT_FUNCTION(CalIsHousePlcPhotoValid);
    REGISTER_LIGHT_FUNCTION(CalSupportShowCnt);
    REGISTER_LIGHT_FUNCTION(CalPhotoNearbyFeelingCoff);
    REGISTER_LIGHT_FUNCTION(CalPhotoExptagCoff);
    REGISTER_LIGHT_FUNCTION(CalPhotoNearbyFeelingCoffChange);
    REGISTER_LIGHT_FUNCTION(CalPhotoNegativeMmuSimScore);
    REGISTER_LIGHT_FUNCTION(CalNearbyRecallI2IScore);
    REGISTER_LIGHT_FUNCTION(CalcHouseCityDegradeCoeff);
    REGISTER_LIGHT_FUNCTION(CalUserInRemitCity);
    REGISTER_LIGHT_FUNCTION(CalItemSatisfyRegionRemit);
    REGISTER_LIGHT_FUNCTION(GenHouseCityFlag);
    REGISTER_LIGHT_FUNCTION(ResetHouseCityCtr);
    REGISTER_LIGHT_FUNCTION(CalUserItemDistanceScore);
    REGISTER_LIGHT_FUNCTION(SetPhotoPrerankClmScore);
    REGISTER_LIGHT_FUNCTION(SetPhotoClmScore);
    REGISTER_LIGHT_FUNCTION(SetPhotoClmMcLtrScore);
    REGISTER_LIGHT_FUNCTION(SetPhotoClmMcLtrScoreV2);
    REGISTER_LIGHT_FUNCTION(CalSlidePhotoMcF1Attr);
    REGISTER_LIGHT_FUNCTION(CalcHouseTargetCityBoost);
    REGISTER_LIGHT_FUNCTION(CalcHouseReasonBoost);
    REGISTER_LIGHT_FUNCTION(CalcNearMultiUserEmbedding);
    REGISTER_LIGHT_FUNCTION(CalMainPhotoMcDurationInverse);
    REGISTER_LIGHT_FUNCTION(CalInteractScoreOfAuthor);
    REGISTER_LIGHT_FUNCTION(CalSlideDistanceFilterAttr);
    REGISTER_LIGHT_FUNCTION(CalcHouseRerankAgeCutoff);
    REGISTER_LIGHT_FUNCTION(GenHouseFlag);
    REGISTER_LIGHT_FUNCTION(CalLiveFollowTime);
    REGISTER_LIGHT_FUNCTION(CalLiveFollowTimeOnly);
    REGISTER_LIGHT_FUNCTION(CalMainPhotoMcRank);
    REGISTER_LIGHT_FUNCTION(CalNearbyAttrEnricher);
    REGISTER_LIGHT_FUNCTION(CalMainPhotoCascadeGoalRank);
    REGISTER_LIGHT_FUNCTION(CalcAucOfCotrain);
    REGISTER_LIGHT_FUNCTION(CalcPhotoRandomScore);
    REGISTER_LIGHT_FUNCTION(GenHouseSameProvinceFlag);
    REGISTER_LIGHT_FUNCTION(CalPhotoPoiDiffEnricher);
    REGISTER_LIGHT_FUNCTION(CalNearbyRecallRankXtrEnricher);
    REGISTER_LIGHT_FUNCTION(EnrichPhotoMcContextAttr);
    REGISTER_LIGHT_FUNCTION(CalcHouseSupportAuthorDiversityBoost);
    REGISTER_LIGHT_FUNCTION(CalcTenMainTagPcocPrefix);
    REGISTER_LIGHT_FUNCTION(CalcTenMainTagPcocPrefixV2);
    REGISTER_LIGHT_FUNCTION(CalcUAPctrPcocPrefix);
    REGISTER_LIGHT_FUNCTION(CalcRecentInteractPhotoId);
    REGISTER_LIGHT_FUNCTION(CalcRecentEffectiveViewPhotoId);
    REGISTER_LIGHT_FUNCTION(CalcTenMainTagPcocAttrEnricher);
    REGISTER_LIGHT_FUNCTION(CalcTenMainTagPcocAttrEnricherV2);
    REGISTER_LIGHT_FUNCTION(CalcUAPctrPcocAttrEnricher);
    REGISTER_LIGHT_FUNCTION(CalcUAPxtrPcocPrefix);
    REGISTER_LIGHT_FUNCTION(CalcUAPxtrPcocAttrEnricher);
    REGISTER_LIGHT_FUNCTION(CalcHouseSupportAuthorSkip);
    REGISTER_LIGHT_FUNCTION(GenHouseAuthorLevelRedisKey);
    REGISTER_LIGHT_FUNCTION(CalPushText);
    REGISTER_LIGHT_FUNCTION(GenHouseAffairsType);
    REGISTER_LIGHT_FUNCTION(CalcHouseRerankModelCutoff);
    REGISTER_LIGHT_FUNCTION(CalItemReasonListToString);
    REGISTER_LIGHT_FUNCTION(CalcIsRelationPhoto);
    REGISTER_LIGHT_FUNCTION(CalcHighReportFilter);
    REGISTER_LIGHT_FUNCTION(CalcHighPublishLabel);
    REGISTER_LIGHT_FUNCTION(CalcHighForwardAttr);
    REGISTER_LIGHT_FUNCTION(CalMmuEmbeddingScore);
    REGISTER_LIGHT_FUNCTION(CalcHetuDebiasReflect);
    REGISTER_LIGHT_FUNCTION(EnrichLiveAuthorPlayTime);
    REGISTER_LIGHT_FUNCTION(CalcPoiHighReportFilter);
    REGISTER_LIGHT_FUNCTION(CalNtmInterestId);
    REGISTER_LIGHT_FUNCTION(CalGlobalGraphInterests);
    REGISTER_LIGHT_FUNCTION(CalGlobalGraphInterestsV2);
    REGISTER_LIGHT_FUNCTION(CalGlobalGraphU2UInterests);
    REGISTER_LIGHT_FUNCTION(CalGlobalNegativeSamplingDnnU2UInterests);
    REGISTER_LIGHT_FUNCTION(CalcTriggerHardSearch);
    REGISTER_LIGHT_FUNCTION(DispersePreRankPxtr);
    REGISTER_LIGHT_FUNCTION(CalFilterScoreFromContentId);
    REGISTER_LIGHT_FUNCTION(CalPhotoKeyFromPhotoId);
    REGISTER_LIGHT_FUNCTION(CalUserHateContentIds);
    REGISTER_LIGHT_FUNCTION(CalUserReportContentIds);
    REGISTER_LIGHT_FUNCTION(CalcAllCount);
    REGISTER_LIGHT_FUNCTION(CalWtrDebiasCascadeScore);
    REGISTER_LIGHT_FUNCTION(CalFollowBoostCascadeScore);
    REGISTER_LIGHT_FUNCTION(EnrichPhotoMcSameCitySupplyFactor);
    REGISTER_LIGHT_FUNCTION(EnrichSlidePhotoMcSameCitySupplyFactor);
    REGISTER_LIGHT_FUNCTION(CalcIsSimilarPhoto);
    REGISTER_LIGHT_FUNCTION(CalcWhetherMmuIdentifyRegion);
    REGISTER_LIGHT_FUNCTION(CalcLowShowPhotoFilter);
    REGISTER_LIGHT_FUNCTION(CalFilterScoreFromHateCluster1k);
    REGISTER_LIGHT_FUNCTION(CalFilterScoreFromInstantHateCluster1k);
    REGISTER_LIGHT_FUNCTION(CalMainPhotoFrDurationInverse);
    REGISTER_LIGHT_FUNCTION(CalInterestExploreScore);
    REGISTER_LIGHT_FUNCTION(CalcCancelSimilar);
    REGISTER_LIGHT_FUNCTION(CalculateLocalPhotoFilterFlag);
    REGISTER_LIGHT_FUNCTION(CalDiversifySendMessageFlag);
    REGISTER_LIGHT_FUNCTION(CalcRequestDegradeLevel);
    REGISTER_LIGHT_FUNCTION(CalcRequestDegradeLimit);
    REGISTER_LIGHT_FUNCTION(CalcRegulationPhotoQualityFilterKey);
    REGISTER_LIGHT_FUNCTION(CalcNearbyUserAoiTrace);
    REGISTER_LIGHT_FUNCTION(CalcNearbyAoiArriverList);
    REGISTER_LIGHT_FUNCTION(CalcSkitTag);
    REGISTER_LIGHT_FUNCTION(ParseUserSkitFeatures);
    REGISTER_LIGHT_FUNCTION(ParseAuthorOpenReason);
    REGISTER_LIGHT_FUNCTION(CalUploadHour);
    REGISTER_LIGHT_FUNCTION(CalcUserProvCode);
    REGISTER_LIGHT_FUNCTION(CalcSimScoreCrossCandidates);
    REGISTER_LIGHT_FUNCTION(CalClusterValidClickScore);
    REGISTER_LIGHT_FUNCTION(CalAuthorValidClickScore);
    REGISTER_LIGHT_FUNCTION(CalcIsInDegradeHour);
    REGISTER_LIGHT_FUNCTION(CalcHetuCtrBoostType);
    REGISTER_LIGHT_FUNCTION(CalcCreadTimeDiff);
    REGISTER_LIGHT_FUNCTION(CalcLiveIdReportRate);
    REGISTER_LIGHT_FUNCTION(NearbyFilterFunction);
    REGISTER_LIGHT_FUNCTION(CalcItemIsFollowed);
    REGISTER_LIGHT_FUNCTION(CalcIsWholeStationColdStartFilter);
    REGISTER_LIGHT_FUNCTION(CalcIsWholeStationColdStartFilterV2);
    REGISTER_LIGHT_FUNCTION(SetSlideMcClmScore);
    REGISTER_LIGHT_FUNCTION(SetSlideMcClmScoreV2);
    REGISTER_LIGHT_FUNCTION(NearbyMcBaoLiang);
    REGISTER_LIGHT_FUNCTION(CalClickFollowItemEmb);
    REGISTER_LIGHT_FUNCTION(CalSimItemWithEffectEmb);
    REGISTER_LIGHT_FUNCTION(CalNearbyLowActivePropensityScore);
    REGISTER_LIGHT_FUNCTION(CalSimAuthorEmb);
    REGISTER_LIGHT_FUNCTION(GenDynamicNegActionFilterRedisKeys);
    REGISTER_LIGHT_FUNCTION(GenNearbyUserNegativeEmbPunishScore);
    REGISTER_LIGHT_FUNCTION(GenNearbyUserNegativeEmbPunishThres);
    REGISTER_LIGHT_FUNCTION(CalcIsPosAuthor);
    REGISTER_LIGHT_FUNCTION(SlideMcConsisAuc);
    REGISTER_LIGHT_FUNCTION(SlidePrerankConsisAuc);
    REGISTER_LIGHT_FUNCTION(PadSlideMcPxtr);
    REGISTER_LIGHT_FUNCTION(PadPrerankPxtr);
    REGISTER_LIGHT_FUNCTION(PackPrerankPxtr);
    REGISTER_LIGHT_FUNCTION(PackSlideMcClmInputV2);
    REGISTER_LIGHT_FUNCTION(PadSlideMcClmInputV2);
    REGISTER_LIGHT_FUNCTION(CalcIsLtLiveDegradeUser);
    REGISTER_LIGHT_FUNCTION(ParseUserIntention);
    REGISTER_LIGHT_FUNCTION(EnrichIsPhotoSameCityForCityPreference);
    REGISTER_LIGHT_FUNCTION(CalcIsNeededSocialPhoto);
    REGISTER_LIGHT_FUNCTION(SlidePrerankConsisDuration);
    REGISTER_LIGHT_FUNCTION(CalcRecentMonthInteractPhotoIdV2);
    REGISTER_LIGHT_FUNCTION(CalcItemIntentionDetection);
    REGISTER_LIGHT_FUNCTION(SetPrClmScoreV1);
    REGISTER_LIGHT_FUNCTION(PrerankClmAuc);
    REGISTER_LIGHT_FUNCTION(CalcReversePairs);
    REGISTER_LIGHT_FUNCTION(CalcTopItemBeforeDiversify);
    REGISTER_LIGHT_FUNCTION(CalcTopItemDiffAfterDiversity);
    REGISTER_LIGHT_FUNCTION(SetNewPymkWithoutFillback);
  }

static bool SetPrClmScoreV1(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
  auto pr_clm_emb = context.GetDoubleListCommonAttr("pr_clm_emb_v1");
  auto set_clm_score = context.SetDoubleItemAttr("pr_clm_score");

  int rank = 1;
  int is_valid = 0;
  if (pr_clm_emb) {
    int size1 = pr_clm_emb->size();
    if (size1 == 6000) {
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        if (rank <= size1) {
          set_clm_score(result, pr_clm_emb->at(size1- rank));
          rank++;
        } else {
          set_clm_score(result, 0.0);
        }
      });
      is_valid = 1;
    } else {
      LOG(ERROR) << "size mismatch 6000 - pr_clm_emb:" << size1;
    }
  } else {
    if (!pr_clm_emb) {
      LOG(ERROR) << "pr_clm_emb is null!";
    }
  }

  context.SetIntCommonAttr("set_prerank_clm_score_v1_valid", is_valid);
  return true;
}

static bool CmpReversePairs(std::pair<double, int64> x, std::pair<double, int64> y) {
  return x.first > y.first;
}

static int64 merge_sort(int q[], int temp[], int l, int r) {
    if (l >= r) return 0;
    int mid = (l + r) >> 1;
    int64 ans = merge_sort(q, temp, l, mid) + merge_sort(q, temp, mid + 1, r);
    int i = l, j = mid + 1, k = l;
    while (i <= mid && j <= r) {
        if (q[i] <= q[j]) {
          temp[k++] = q[i++];
        } else {
            temp[k++] = q[j++];
            ans += mid - i + 1;
        }
    }
    while (i <= mid) temp[k++] = q[i++];
    while (j <= r) temp[k++] = q[j++];
    for (int h = l; h <= r; h++) q[h] = temp[h];
    return ans;
}

static bool CalcReversePairs(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                          RecoResultConstIter end) {
  auto org_sort_basis = context.GetDoubleItemAttr("org_sort_basis");
  auto new_sort_basis = context.GetDoubleItemAttr("new_sort_basis");

  std::vector<double> org_sort_basis_vec, new_sort_basis_vec;
  std::vector<int64> photo_id_list;

  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    int64 photo_id = result.GetId();
    double org_sort_basis_value = org_sort_basis(result).value_or(0.0);
    double new_sort_basis_value = new_sort_basis(result).value_or(0.0);
    photo_id_list.push_back(photo_id);
    org_sort_basis_vec.push_back(org_sort_basis_value);
    new_sort_basis_vec.push_back(new_sort_basis_value);
  });

  int n = (int)photo_id_list.size();
  std::pair<double, int64> org_sort[n], final_sort[n];
  std::map<int64, int> mp;
  int q[n], temp[n];

  for (int i = 0; i < n; i++) {
    if (i < org_sort_basis_vec.size() && i < new_sort_basis_vec.size() && i < photo_id_list.size()) {
      org_sort[i] = {org_sort_basis_vec[i], photo_id_list[i]};
      final_sort[i] = {new_sort_basis_vec[i], photo_id_list[i]};
    }
  }

  std::sort(org_sort, org_sort + n, CmpReversePairs);
  for (int i = 0; i < n; i++) {
      mp[org_sort[i].second] = i;
  }

  std::sort(final_sort, final_sort + n, CmpReversePairs);
  for (int i = 0; i < n; i++) {
      q[i] = mp[final_sort[i].second];
  }

  int64 inv_count = merge_sort(q, temp, 0, n - 1);
  context.SetIntCommonAttr("NearbyReversePairs", inv_count);
  return true;
}

static bool PrerankClmAuc(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                          RecoResultConstIter end) {
  auto clm_score_acc = context.GetDoubleItemAttr("pr_clm_score");
  auto pr_acc = context.GetDoubleItemAttr("prerank_score");
  int64 topN = context.GetIntCommonAttr("topN").value_or(0);

  std::vector<double> clm_score, pr_score;
  int64 total_size = 0;
  for (auto result = begin; result != end; ++result) {
    clm_score.push_back(clm_score_acc(*result).value_or(0));
    pr_score.push_back(pr_acc(*result).value_or(0));
    total_size++;
  }

  if (topN > 0 && total_size > topN) {
    int64 total_pair = topN * (total_size - topN);
    if (check_valid(pr_score)) {
      int64 pr_pnr = topn_pnr_pair(pr_score, topN);
      context.SetDoubleCommonAttr("pr_es_topN_auc", (double)(total_pair - pr_pnr) / total_pair);
    }
    if (check_valid(clm_score)) {
      int64 clm_score_pnr = topn_pnr_pair(clm_score, topN);
      context.SetDoubleCommonAttr("pr_clm_topN_auc", (double)(total_pair - clm_score_pnr) / total_pair);
    }
  }

  return true;
}

static bool CalcIsLtLiveDegradeUser(const CommonRecoLightFunctionContext &context,
  RecoResultConstIter begin, RecoResultConstIter end) {
    auto user_type = context.GetIntCommonAttr("uLiveUserInnerPlayLifecycleTypeKV").value_or(-1);
    auto degrade_user_type_list = context.GetIntListCommonAttr("nearby_special_user_type_for_LT_list");
    auto is_LT_live_degrade_user = 0;

    if (degrade_user_type_list && degrade_user_type_list->size()) {
      for (int i = 0; i < degrade_user_type_list->size(); i++) {
        if (user_type == degrade_user_type_list->at(i)) {
          is_LT_live_degrade_user = 1;
          break;
        }
      }
    }
    context.SetIntCommonAttr("is_LT_live_degrade_user", is_LT_live_degrade_user);
    return true;
  }

static bool SlidePrerankConsisDuration(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
  auto duration_acc = context.GetIntItemAttr("item_info.duration_ms");
  std::vector<int64> duration;
  int64 total_size = 0;
  for (auto result = begin; result != end; ++result) {
    duration.push_back(duration_acc(*result).value_or(0.f));
    ++total_size;
  }

  if (total_size >= 10) {
    int64 duration_sum = 0;
    for (int i = 0; i < 10; ++i) {
      duration_sum += duration[i];
    }
    context.SetDoubleCommonAttr("duration_top10_avg", (double)duration_sum / 10);
  }
  if (total_size >= 100) {
    int64 duration_sum = 0;
    for (int i = 0; i < 100; ++i) {
      duration_sum += duration[i];
    }
    context.SetDoubleCommonAttr("duration_top100_avg", (double)duration_sum / 100);
  }
  if (total_size >= 1000) {
    int64 duration_sum = 0;
    for (int i = 0; i < 1000; ++i) {
      duration_sum += duration[i];
    }
    context.SetDoubleCommonAttr("duration_top1000_avg", (double)duration_sum / 1000);
  }
  return true;
}

static bool SlidePrerankConsisAuc(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
  auto prerank_score_acc = context.GetDoubleItemAttr("prerank_score");
  auto prerank_cascade_score_acc = context.GetDoubleItemAttr("prerank_cascade_score");

  std::vector<double> prerank_score, prerank_cascade_score;
  int64 total_size = 0;
  for (auto result = begin; result != end; ++result) {
    prerank_score.push_back(prerank_score_acc(*result).value_or(0.f));
    prerank_cascade_score.push_back(prerank_cascade_score_acc(*result).value_or(0.f));
    total_size++;
  }
  // container_print("prerank_score", prerank_score);
  // container_print("prerank_cascade_score", prerank_cascade_score);

  /* topN - auc */
  int64 topN = 200;
  if (total_size > topN) {
    int64 total_pair = topN * (total_size - topN);
    int64 prerank_topn_pnr_pair = topn_pnr_pair(prerank_score, topN);
    int64 prerank_cascade_topn_pnr_pair = topn_pnr_pair(prerank_cascade_score, topN);
    context.SetDoubleCommonAttr("prerank_topn_auc",
                                (double)(total_pair - prerank_topn_pnr_pair) / total_pair);
    context.SetDoubleCommonAttr("prerank_cascade_topn_auc",
                                (double)(total_pair - prerank_cascade_topn_pnr_pair) / total_pair);
  }

  return true;
}

static bool SlideMcConsisAuc(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto cas_score_acc = context.GetDoubleItemAttr("cascade_ensemble_score");
    auto cas_clm_score_acc = context.GetDoubleItemAttr("cascade_clm_score");

    std::vector<double> cas_score, cas_clm_score;
    int64 total_size = 0;
    for (auto result = begin; result != end; ++result) {
        cas_score.push_back(cas_score_acc(*result).value_or(0.f));
        cas_clm_score.push_back(cas_clm_score_acc(*result).value_or(0.f));
        total_size++;
    }

    /* topN - auc */
    int64 topN = 8;
    if (total_size > topN) {
        int64 total_pair = topN * (total_size - topN);
        int64 cas_topn_pnr_pair = topn_pnr_pair(cas_score, topN);
        int64 cas_clm_topn_pnr_pair = topn_pnr_pair(cas_clm_score, topN);
        context.SetDoubleCommonAttr("cas_topn_es_fr_auc",
            (double)(total_pair - cas_topn_pnr_pair) / total_pair);
        context.SetDoubleCommonAttr("cas_topn_clm_fr_auc",
            (double)(total_pair - cas_clm_topn_pnr_pair) / total_pair);
    }

    topN = 10;
    if (total_size <= topN) return true;

    const auto &es_score_index = argsort(cas_score);
    const auto &clm_score_index = argsort(cas_clm_score);
    for (auto topM : std::vector<int64>{10, 50, 100, 200}) {
      topM = std::min(topM, total_size);

      int64 es_score_hitrate = std::count_if(es_score_index.begin(), es_score_index.begin() + topM,
                                             [&topN](const auto &index) { return index < topN; });
      context.SetDoubleCommonAttr(
          "mc_es_score_hitrate_topN@" + std::to_string(topN) + "_topM@" + std::to_string(topM),
          (double)(es_score_hitrate) / topN);

      int64 clm_score_hitrate = std::count_if(clm_score_index.begin(), clm_score_index.begin() + topM,
                                              [&topN](const auto &index) { return index < topN; });
      context.SetDoubleCommonAttr(
          "mc_clm_score_hitrate_topN@" + std::to_string(topN) + "_topM@" + std::to_string(topM),
          (double)(clm_score_hitrate) / topN);
    }

    return true;
}

static bool NearbyMcBaoLiang(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                             RecoResultConstIter end) {
  // 优质内容保量
  int64 enable_youzhineirong_baoliang = context.GetIntCommonAttr("enable_youzhineirong_baoliang").value_or(0);
  auto guangan_getter = context.GetIntItemAttr("item_info.level_hot_online");
  auto fengmian_getter = context.GetIntItemAttr("item_info.audit_hot_cover_level");
  auto youzhi_setter = context.SetIntItemAttr("youzhineirong_baoliang");
  // 距离保量
  int64 enable_juli_baoliang = context.GetIntCommonAttr("enable_juli_baoliang").value_or(0);
  auto distance_getter = context.GetDoubleItemAttr("pLeafDistance");
  double distance_threshold = context.GetIntCommonAttr("distance_threshold").value_or(0.0);
  auto distance_setter = context.SetIntItemAttr("distance_baoliang");

  std::for_each(begin, end, [&](const auto &result) {
    // 优质内容保量
    int64 guangan = guangan_getter(result).value_or(0);
    int64 fengmian = fengmian_getter(result).value_or(0);
    if (enable_youzhineirong_baoliang == 1 && guangan != 0 && fengmian != 0 && guangan != 1 &&
        fengmian == 2023740) {
      youzhi_setter(result, 1);
    } else {
      youzhi_setter(result, 0);
    }

    // 距离保量
    double distance = distance_getter(result).value_or(0.0);
    if (enable_juli_baoliang == 1 && distance_threshold != 0.0 && distance < distance_threshold) {
      distance_setter(result, 1);
    } else {
      distance_setter(result, 0);
    }
  });

  return true;
}

static bool PackSlideMcClmInputV2(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                            RecoResultConstIter end) {
  int64 max_pid_size =
      std::min(context.GetIntCommonAttr("photo_mc_size").value_or(0), (int64)SLIDE_MC_PXTR_MAX_SIZE_V2);
  auto pack_int_data = [&](std::string &&field, std::string &&name) {
    auto data_ptr = context.GetIntItemAttr(field);
    std::vector<int64> data(max_pid_size);
    for (int i = 0; i < max_pid_size; ++i) {
      data[i] = data_ptr(*(begin + i)).value_or(0);
    }
    context.SetIntListCommonAttr(name, std::move(data));
  };
  auto pack_int_data_plus1 = [&](std::string &&field, std::string &&name) {
    auto data_ptr = context.GetIntItemAttr(field);
    std::vector<int64> data(max_pid_size);
    for (int i = 0; i < max_pid_size; ++i) {
      data[i] = data_ptr(*(begin + i)).value_or(0) + 1;
    }
    context.SetIntListCommonAttr(name, std::move(data));
  };


  auto pack_double_data = [&](std::string &&field, std::string &&name) {
    auto data_ptr = context.GetDoubleItemAttr(field);
    std::vector<double> data(max_pid_size);
    for (int i = 0; i < max_pid_size; ++i) {
      data[i] = data_ptr(*(begin + i)).value_or(0.0);
    }
    context.SetDoubleListCommonAttr(name, std::move(data));
  };
  auto pack_mmu_v4_dis_data = [&](std::string &&field) {
    auto data_ptr = context.GetDoubleListItemAttr(field);
    std::vector<int64> data1(max_pid_size);
    std::vector<int64> data2(max_pid_size);
    std::vector<int64> data3(max_pid_size);
    std::vector<int64> data4(max_pid_size);
    for (int i = 0; i < max_pid_size; ++i) {
      auto data = data_ptr(*(begin + i));
      if (data && data->size() == 8) {
        data1[i] = data->at(0) * 512 + data->at(1) + 1;
        data2[i] = data->at(2) * 512 + data->at(3) + 1;
        data3[i] = data->at(4) * 512 + data->at(5) + 1;
        data4[i] = data->at(6) * 512 + data->at(7) + 1;
      }
    }
    context.SetIntListCommonAttr("photo_mc_mmu_v4_emb1_v2", std::move(data1));
    context.SetIntListCommonAttr("photo_mc_mmu_v4_emb2_v2", std::move(data2));
    context.SetIntListCommonAttr("photo_mc_mmu_v4_emb3_v2", std::move(data3));
    context.SetIntListCommonAttr("photo_mc_mmu_v4_emb4_v2", std::move(data4));
  };

  pack_int_data("wtd_bucket", "photo_mc_wtd_bucket_v2");
  pack_int_data("item_id", "photo_mc_pid_v2");
  pack_int_data("item_info.author_id", "photo_mc_aid_v2");
  pack_int_data_plus1("item_info.duration_ms", "photo_mc_durationms_v2");
  pack_double_data("cascade_plvtr", "photo_mc_lvtr_v2");
  pack_double_data("cascade_pfvtr", "photo_mc_fvtr_v2");
  pack_double_data("cascade_pvtr", "photo_mc_vtr_v2");
  pack_double_data("cascade_wtd_score", "photo_mc_wtd_score_v2");
  pack_double_data("cascade_pltr", "photo_mc_ltr_v2");
  pack_double_data("cascade_pcmtr", "photo_mc_cmtr_v2");
  pack_double_data("cascade_pwtr", "photo_mc_wtr_v2");
  pack_mmu_v4_dis_data("item_info.mmu_v4_discrete_emb");

  return true;
}

static bool PadSlideMcClmInputV2(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                            RecoResultConstIter end) {
  int64 pid_size = context.GetIntCommonAttr("photo_mc_size").value_or(0);
  auto pad_double_fn = [&](std::string &&field) {
    auto data_ptr = context.GetDoubleListCommonAttr(field);
    if (data_ptr) {
        std::vector<double> data(SLIDE_MC_PXTR_MAX_SIZE_V2, 0.0);
        for (int i = 0; i < std::min(SLIDE_MC_PXTR_MAX_SIZE_V2, (int)data_ptr->size()); ++i) {
          data[i] = data_ptr->at(i);
        }
        // container_print(field, data);
        context.SetDoubleListCommonAttr(field, std::move(data));
    }
  };

  auto pad_int_fn = [&](std::string &&field) {
    auto data_ptr = context.GetIntListCommonAttr(field);
    if (data_ptr) {
        std::vector<int64> data(SLIDE_MC_PXTR_MAX_SIZE_V2, 0);
        for (int i = 0; i < std::min(SLIDE_MC_PXTR_MAX_SIZE_V2, (int)data_ptr->size()); ++i) {
          data[i] = data_ptr->at(i);
        }
        // container_print(field, data);
        context.SetIntListCommonAttr(field, std::move(data));
    }
  };
  auto dis_pxtr_fn = [&](std::string &&field, std::string &&name, int64 bucket) {
    auto data_ptr = context.GetDoubleListCommonAttr(field);
    if (data_ptr) {
      std::vector<int64> data(SLIDE_MC_PXTR_MAX_SIZE_V2, 0);
      for (int i = 0; i < std::min(SLIDE_MC_PXTR_MAX_SIZE_V2, (int)pid_size); ++i) {
        data[i] = std::floor(bucket * data_ptr->at(i));
      }
      context.SetIntListCommonAttr(name, std::move(data));
    }
    return true;
  };
  auto dis_score_fn = [&](std::string &&field, std::string &&name, double min, double max, int64 bucket) {
    auto data_ptr = context.GetDoubleListCommonAttr(field);
    if (data_ptr) {
      std::vector<int64> data(SLIDE_MC_PXTR_MAX_SIZE_V2, 0);
      for (int i = 0; i < std::min(SLIDE_MC_PXTR_MAX_SIZE_V2, (int)pid_size); ++i) {
        data[i] = std::floor(std::max(std::min(data_ptr->at(i), max), min) / (max - min + 1e-7) * bucket);
      }
      context.SetIntListCommonAttr(name, std::move(data));
    }
    return true;
  };
  auto rank_pxtr_fn = [&](std::string &&field, std::string &&name) {
    auto data_ptr = context.GetDoubleListCommonAttr(field);
    if (data_ptr) {
        std::vector<int64> indices(data_ptr->size());
        std::iota(indices.begin(), indices.end(), 0);
        std::sort(indices.begin(), indices.end(),
                  [data_ptr](int64 i, int64 j) { return data_ptr->at(i) > data_ptr->at(j); });
        for (int i = 0; i < indices.size(); ++i) { indices[i] += 1; }
        indices.resize(SLIDE_MC_PXTR_MAX_SIZE_V2);
        context.SetIntListCommonAttr(name, std::move(indices));
    }
    return true;
  };

  if (pid_size == 0) return true;

  dis_pxtr_fn("photo_mc_lvtr_v2", "photo_mc_lvtr_v2_dis", 1e+4);
  dis_pxtr_fn("photo_mc_fvtr_v2", "photo_mc_fvtr_v2_dis", 1e+4);
  dis_pxtr_fn("photo_mc_vtr_v2", "photo_mc_vtr_v2_dis", 1e+4);
  dis_pxtr_fn("photo_mc_ltr_v2", "photo_mc_ltr_v2_dis", 1e+4);
  dis_pxtr_fn("photo_mc_cmtr_v2", "photo_mc_cmtr_v2_dis", 1e+5);
  dis_pxtr_fn("photo_mc_wtr_v2", "photo_mc_wtr_v2_dis", 1e+5);
  dis_score_fn("photo_mc_wtd_score_v2", "photo_mc_wtd_score_v2_dis", 0, 1000, 1e+5);

  rank_pxtr_fn("photo_mc_lvtr_v2", "photo_mc_lvtr_v2_rank");
  rank_pxtr_fn("photo_mc_fvtr_v2", "photo_mc_fvtr_v2_rank");
  rank_pxtr_fn("photo_mc_vtr_v2", "photo_mc_vtr_v2_rank");
  rank_pxtr_fn("photo_mc_ltr_v2", "photo_mc_ltr_v2_rank");
  rank_pxtr_fn("photo_mc_cmtr_v2", "photo_mc_cmtr_v2_rank");
  rank_pxtr_fn("photo_mc_wtr_v2", "photo_mc_wtr_v2_rank");
  rank_pxtr_fn("photo_mc_wtd_score_v2", "photo_mc_wtd_score_v2_rank");

  pad_double_fn("photo_mc_lvtr_v2");
  pad_double_fn("photo_mc_fvtr_v2");
  pad_double_fn("photo_mc_vtr_v2");
  pad_double_fn("photo_mc_wtd_score_v2");
  pad_double_fn("photo_mc_ltr_v2");
  pad_double_fn("photo_mc_cmtr_v2");
  pad_double_fn("photo_mc_wtr_v2");
  pad_int_fn("photo_mc_mmu_v4_emb1_v2");
  pad_int_fn("photo_mc_mmu_v4_emb2_v2");
  pad_int_fn("photo_mc_mmu_v4_emb3_v2");
  pad_int_fn("photo_mc_mmu_v4_emb4_v2");
  pad_int_fn("photo_mc_pid_v2");
  pad_int_fn("photo_mc_aid_v2");
  pad_int_fn("photo_mc_wtd_bucket_v2");
  pad_int_fn("photo_mc_durationms_v2");

  std::vector<int64> photo_mc_mask(pid_size, 1);
  photo_mc_mask.resize(SLIDE_MC_PXTR_MAX_SIZE_V2);
  context.SetIntListCommonAttr("photo_mc_mask_v2", std::move(photo_mc_mask));

  return true;
}

static bool PackPrerankPxtr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                            RecoResultConstIter end) {
  int64 size =
      std::min(context.GetIntCommonAttr("prerank_pid_size").value_or(0), (int64)PRERANK_PXTR_MAX_SIZE);
  auto pack_int_data = [&](
                           std::string &&field, std::string &&name,
                           std::function<int64(int64)> &&processor = [](const int64 &p) { return p; }) {
    auto data_ptr = context.GetIntItemAttr(field);
    std::vector<int64> data(size);
    for (int i = 0; i < size; ++i) {
      data[i] = processor(data_ptr(*(begin + i)).value_or(0));
    }
    context.SetIntListCommonAttr(name, std::move(data));
  };
  auto pack_double_data = [&](
                              std::string &&field, std::string &&name,
                              std::function<double(double)> &&processor = [](const double &p) { return p; }) {
    auto data_ptr = context.GetDoubleItemAttr(field);
    std::vector<double> data(size);
    for (int i = 0; i < size; ++i) {
      data[i] = processor(data_ptr(*(begin + i)).value_or(0));
    }
    context.SetDoubleListCommonAttr(name, std::move(data));
  };
  auto pack_wt_data = [&](
                          std::string &&field, std::string &&name,
                          std::function<double(double, int64)> &&processor =
                              [](const double &p, const int64 &d) { return p * d; }) {
    auto data_ptr = context.GetDoubleItemAttr(field);
    auto durationms_ptr = context.GetIntItemAttr("item_info.duration_ms");
    std::vector<double> data(size);
    for (int i = 0; i < size; ++i) {
      data[i] = processor(data_ptr(*(begin + i)).value_or(0.0), durationms_ptr(*(begin + i)).value_or(0));
    }
    context.SetDoubleListCommonAttr(name, std::move(data));
  };

  pack_int_data("item_id", "photo_prerank_pid");
  pack_int_data("item_info.author_id", "photo_prerank_aid");
  pack_int_data("item_info.duration_ms", "photo_prerank_durationms", [](const auto &d) { return d + 2; });
  pack_double_data("prerank_ctr", "photo_prerank_ctr");
  pack_double_data("prerank_wtr", "photo_prerank_wtr");
  pack_double_data("prerank_ltr", "photo_prerank_ltr");
  pack_double_data("prerank_cmtr", "photo_prerank_cmtr");
  pack_double_data("prerank_lvtr", "photo_prerank_lvtr");
  pack_double_data("prerank_vtr", "photo_prerank_vtr",
                   [](const double &p) { return p / std::max(1 - p, 1e-7); });
  pack_wt_data("prerank_fvtr", "photo_prerank_fvtr",
               [](const double &p, const int64 &d) { return std::max(d, (int64)1) * p / 1e+3; });
  pack_wt_data("prerank_wtd", "photo_prerank_wtd", [](const double &p, const int64 &d) {
    auto score = p > 0.5 ? p : 0.5;
    score = -log(std::max(1.0 / score - 1.0, 0.000000001));
    score = score * pow(std::max(d, (int64)1), score - 0.05);
    return score;
  });

  return true;
}
static bool PadPrerankPxtr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                           RecoResultConstIter end) {
  int64 pid_size =
      std::min(context.GetIntCommonAttr("prerank_pid_size").value_or(0), (int64)(PRERANK_PXTR_MAX_SIZE));
  auto pad_double_fn = [&](std::string &&field) {
    auto data_ptr = context.GetDoubleListCommonAttr(field);
    if (data_ptr) {
      std::vector<double> data(pid_size, 0.0);
      for (int i = 0; i < data.size(); ++i) {
        data[i] = data_ptr->at(i);
      }
      data.resize(PRERANK_PXTR_MAX_SIZE);
      // container_print(field, data);
      context.SetDoubleListCommonAttr(field, std::move(data));
    }
  };

  auto pad_int_fn = [&](std::string &&field) {
    auto data_ptr = context.GetIntListCommonAttr(field);
    if (data_ptr) {
      std::vector<int64> data(pid_size, 0);
      for (int i = 0; i < data.size(); ++i) {
        data[i] = data_ptr->at(i);
      }
      // container_print(field, data);
      data.resize(PRERANK_PXTR_MAX_SIZE);
      context.SetIntListCommonAttr(field, std::move(data));
    }
  };
  auto dis_pxtr_fn = [&](std::string &&field, std::string &&name, int64 bucket, double min = 0,
                         double max = 1) {
    auto data_ptr = context.GetDoubleListCommonAttr(field);
    if (data_ptr) {
      std::vector<int64> data(pid_size, 0);
      for (int i = 0; i < data.size(); ++i) {
        data[i] = std::floor(std::max(std::min(data_ptr->at(i), max), min) / (max - min) * bucket);
      }
      data.resize(PRERANK_PXTR_MAX_SIZE);
      context.SetIntListCommonAttr(name, std::move(data));
    }
    return true;
  };
  auto rank_pxtr_fn = [&](std::string &&field, std::string &&name) {
    auto data_ptr = context.GetDoubleListCommonAttr(field);
    if (data_ptr) {
      std::vector<int64> indices(pid_size);
      std::iota(indices.begin(), indices.end(), 0);
      std::sort(indices.begin(), indices.end(),
                [data_ptr](int64 i, int64 j) { return data_ptr->at(i) > data_ptr->at(j); });
      for (int i = 0; i < indices.size(); ++i) {
        indices[i] += 2;
      }
      indices.resize(PRERANK_PXTR_MAX_SIZE);
      context.SetIntListCommonAttr(name, std::move(indices));
    }
    return true;
  };

  if (pid_size == 0) return true;

  dis_pxtr_fn("photo_prerank_ctr", "photo_prerank_ctr_dis", 1e+5);
  dis_pxtr_fn("photo_prerank_wtr", "photo_prerank_wtr_dis", 1e+5);
  dis_pxtr_fn("photo_prerank_ltr", "photo_prerank_ltr_dis", 1e+5);
  dis_pxtr_fn("photo_prerank_cmtr", "photo_prerank_cmtr_dis", 1e+5);
  dis_pxtr_fn("photo_prerank_vtr", "photo_prerank_vtr_dis", 1e+5, 0, 1200);
  dis_pxtr_fn("photo_prerank_lvtr", "photo_prerank_lvtr_dis", 1e+5);
  dis_pxtr_fn("photo_prerank_fvtr", "photo_prerank_fvtr_dis", 1e+5, 0, 2000);
  dis_pxtr_fn("photo_prerank_wtd", "photo_prerank_wtd_dis", 1e+5, 0, 2000);
  rank_pxtr_fn("photo_prerank_ctr", "photo_prerank_ctr_rank");
  rank_pxtr_fn("photo_prerank_wtr", "photo_prerank_wtr_rank");
  rank_pxtr_fn("photo_prerank_ltr", "photo_prerank_ltr_rank");
  rank_pxtr_fn("photo_prerank_cmtr", "photo_prerank_cmtr_rank");
  rank_pxtr_fn("photo_prerank_vtr", "photo_prerank_vtr_rank");
  rank_pxtr_fn("photo_prerank_lvtr", "photo_prerank_lvtr_rank");
  rank_pxtr_fn("photo_prerank_fvtr", "photo_prerank_fvtr_rank");
  rank_pxtr_fn("photo_prerank_wtd", "photo_prerank_wtd_rank");
  pad_int_fn("photo_prerank_pid");
  pad_int_fn("photo_prerank_aid");
  pad_int_fn("photo_prerank_durationms");
  pad_double_fn("photo_prerank_ctr");
  pad_double_fn("photo_prerank_wtr");
  pad_double_fn("photo_prerank_ltr");
  pad_double_fn("photo_prerank_cmtr");
  pad_double_fn("photo_prerank_vtr");
  pad_double_fn("photo_prerank_lvtr");
  pad_double_fn("photo_prerank_fvtr");
  pad_double_fn("photo_prerank_wtd");

  std::vector<int64> photo_prerank_mask(pid_size, 1);
  photo_prerank_mask.resize(PRERANK_PXTR_MAX_SIZE);
  context.SetIntListCommonAttr("photo_prerank_mask", std::move(photo_prerank_mask));

  return true;
}

static bool PadSlideMcPxtr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
  size_t pid_size = context.GetIntListCommonAttr("photo_mc_pid").value_or(std::vector<int64>{}).size();

  auto pad_double_fn = [&](std::string &&field) {
    auto data_ptr = context.GetDoubleListCommonAttr(field);
    if (data_ptr) {
      if (pid_size == data_ptr->size()) {
        std::vector<double> data(SLIDE_MC_PXTR_MAX_SIZE, 0.0);
        for (int i = 0; i < std::min(SLIDE_MC_PXTR_MAX_SIZE, (int)data_ptr->size()); ++i) {
          data[i] = data_ptr->at(i);
        }
        // container_print(field, data);
        context.SetDoubleListCommonAttr(field, std::move(data));
      } else {
        LOG(ERROR) << field + "size:" << data_ptr->size() << " pid_size:" << pid_size;
        return false;
      }
    }
    return true;
  };
  auto pad_string_fn = [&](std::string &&field) {
    auto data_ptr = context.GetStringListCommonAttr(field);
    if (data_ptr) {
      if (pid_size == data_ptr->size()) {
        std::vector<std::string> data(SLIDE_MC_PXTR_MAX_SIZE, "unknown");
        for (int i = 0; i < std::min(SLIDE_MC_PXTR_MAX_SIZE, (int)data_ptr->size()); ++i)
          data[i] = std::move(std::string(data_ptr->at(i)));
        // container_print(field, data);
        context.SetStringListCommonAttr(field, std::move(data));
      } else {
        LOG(ERROR) << field + "size:" << data_ptr->size() << " pid_size:" << pid_size;
        return false;
      }
    }
    return true;
  };
  auto pad_int_fn = [&](std::string &&field) {
    auto data_ptr = context.GetIntListCommonAttr(field);
    if (data_ptr) {
      if (pid_size == data_ptr->size()) {
        std::vector<int64> data(SLIDE_MC_PXTR_MAX_SIZE, 0);
        for (int i = 0; i < std::min(SLIDE_MC_PXTR_MAX_SIZE, (int)data_ptr->size()); ++i) {
          data[i] = data_ptr->at(i);
        }
        // container_print(field, data);
        context.SetIntListCommonAttr(field, std::move(data));
      } else {
        LOG(ERROR) << field + "size:" << data_ptr->size() << " pid_size:" << pid_size;
        return false;
      }
    }
    return true;
  };
  auto pad_int_with_new_name_fn = [&](std::string &&field, std::string &&name) {
    auto data_ptr = context.GetIntListCommonAttr(field);
    if (data_ptr) {
      if (pid_size == data_ptr->size()) {
        std::vector<int64> data(SLIDE_MC_PXTR_MAX_SIZE, 0);
        for (int i = 0; i < std::min(SLIDE_MC_PXTR_MAX_SIZE, (int)data_ptr->size()); ++i) {
          data[i] = data_ptr->at(i);
        }
        // container_print(field, data);
        context.SetIntListCommonAttr(name, std::move(data));
      } else {
        LOG(ERROR) << field + "size:" << data_ptr->size() << " pid_size:" << pid_size;
        return false;
      }
    }
    return true;
  };
  auto pad_int_add_one_with_new_name_fn = [&](std::string &&field, std::string &&name) {
    auto data_ptr = context.GetIntListCommonAttr(field);
    if (data_ptr) {
      if (pid_size == data_ptr->size()) {
        std::vector<int64> data(SLIDE_MC_PXTR_MAX_SIZE, 0);
        for (int i = 0; i < std::min(SLIDE_MC_PXTR_MAX_SIZE, (int)data_ptr->size()); ++i) {
          data[i] = data_ptr->at(i) + 1;
        }
        // container_print(field, data);
        context.SetIntListCommonAttr(name, std::move(data));
      } else {
        LOG(ERROR) << field + "size:" << data_ptr->size() << " pid_size:" << pid_size;
        return false;
      }
    }
    return true;
  };
  auto pad_string_with_new_name_fn = [&](std::string &&field, std::string &&name) {
    auto data_ptr = context.GetStringListCommonAttr(field);
    if (data_ptr) {
      if (pid_size == data_ptr->size()) {
        std::vector<std::string> data(SLIDE_MC_PXTR_MAX_SIZE, "unknown");
        for (int i = 0; i < std::min(SLIDE_MC_PXTR_MAX_SIZE, (int)data_ptr->size()); ++i)
          data[i] = std::move(std::string(data_ptr->at(i)));
        // container_print(field, data);
        context.SetStringListCommonAttr(name, std::move(data));
      } else {
        LOG(ERROR) << field + "size:" << data_ptr->size() << " pid_size:" << pid_size;
        return false;
      }
    }
    return true;
  };

  if (pid_size != 0 &&
      // V1
      pad_int_fn("photo_mc_pid") && pad_int_fn("photo_mc_aid") &&
      pad_int_add_one_with_new_name_fn("photo_mc_duration_ms", "phoot_mc_duration_ms") &&
      // pltr
      pad_double_fn("photo_mc_pltr") && pad_int_fn("photo_mc_pltr_dis") && pad_int_fn("photo_mc_pltr_rank") &&
      // pwtr
      pad_double_fn("photo_mc_pwtr") && pad_int_fn("photo_mc_pwtr_dis") && pad_int_fn("photo_mc_pwtr_rank") &&
      // pcmtr
      pad_double_fn("photo_mc_pcmtr") && pad_int_fn("photo_mc_pcmtr_dis") &&
      pad_int_fn("photo_mc_pcmtr_rank") &&
      // plvtr
      pad_double_fn("photo_mc_plvtr") && pad_int_fn("photo_mc_plvtr_dis") &&
      pad_int_fn("photo_mc_plvtr_rank") &&
      // pfvtr
      pad_double_fn("photo_mc_pfvtr") && pad_int_fn("photo_mc_pfvtr_dis") &&
      pad_int_fn("photo_mc_pfvtr_rank") &&
      // pwtd
      pad_double_fn("photo_mc_pwtd") && pad_int_fn("photo_mc_pwtd_dis") && pad_int_fn("photo_mc_pwtd_rank") &&
      // pvtr
      pad_double_fn("photo_mc_pvtr") && pad_int_fn("photo_mc_pvtr_dis") && pad_int_fn("photo_mc_pvtr_rank") &&
      // es
      pad_int_fn("photo_mc_ensemble_score_rank")
      ) {
    std::vector<int64> photo_mc_mask(pid_size, 1);
    photo_mc_mask.resize(SLIDE_MC_PXTR_MAX_SIZE);
    context.SetIntListCommonAttr("photo_mc_mask", std::move(photo_mc_mask));

    context.SetIntCommonAttr("slide_mc_clm_input_valid", 1);
  } else {
    context.SetIntCommonAttr("slide_mc_clm_input_valid", 0);
  }

  return true;
}

static bool SetSlideMcClmScoreV2(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                               RecoResultConstIter end) {
  auto slide_mc_clm_emb_v2 =
      context.GetDoubleListCommonAttr("slide_mc_clm_emb_v2");
  auto set_photo_clm_score = context.SetDoubleItemAttr("cascade_clm_score");
  int rank = 1;
  int is_valid = 0;
  if (slide_mc_clm_emb_v2) {
    int size1 = slide_mc_clm_emb_v2->size();
    if (size1 == SLIDE_MC_PXTR_MAX_SIZE_V2) {
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        if (rank <= size1) {
          set_photo_clm_score(result, slide_mc_clm_emb_v2->at(size1 - rank));
          rank++;
        } else {
          set_photo_clm_score(result, 0.0);
        }
      });
      is_valid = 1;
    }
  }

  context.SetIntCommonAttr("set_slide_mc_clm_score_valid_v2", is_valid);
  return true;
}


static bool SetSlideMcClmScore(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                               RecoResultConstIter end) {
  auto slide_photo_mc_clm_pred_embedding =
      context.GetDoubleListCommonAttr("slide_photo_mc_clm_pred_embedding");
  auto photo_mc_pid_list = context.GetIntListCommonAttr("photo_mc_pid");
  auto set_photo_clm_score = context.SetDoubleItemAttr("cascade_clm_score");
  int rank = 1;
  int is_valid = 0;
  if (slide_photo_mc_clm_pred_embedding && photo_mc_pid_list) {
    int size1 = slide_photo_mc_clm_pred_embedding->size();
    int size2 = photo_mc_pid_list->size();
    if (size2 == size1) {
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        if (rank <= size2) {
          set_photo_clm_score(result, slide_photo_mc_clm_pred_embedding->at(size2 - rank));
          rank++;
        } else {
          set_photo_clm_score(result, 0.0);
        }
      });
      is_valid = 1;
    }
  }

  context.SetIntCommonAttr("set_slide_mc_clm_score_valid", is_valid);
  return true;
}


static bool NearbyFilterFunction(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
  auto filter_tag_setter = context.SetIntItemAttr("filter_tag");

  // llm 异地过滤需求 - https://docs.corp.kuaishou.com/d/home/<USER>
  int64 user_province_id = context.GetIntCommonAttr("user_province_code").value_or(0);
  auto item_mmu_llm_province_id_ptr = context.GetIntItemAttr("item_info.mmu_llm_province_id");

  // 行为过滤需求 - https://docs.corp.kuaishou.com/d/home/<USER>
  const auto &like_list = context.GetIntListCommonAttr("nearby_like_pids_v2").value_or(std::vector<int64>{});
  const auto &like_set = std::unordered_set<int64>(like_list.begin(), like_list.end());

  // 相关性过滤 - https://docs.corp.kuaishou.com/d/home/<USER>
  const auto &enable_hetu_info_v2_filter = context.GetIntCommonAttr("enable_hetu_info_v2_filter").value_or(0);
  auto hetu_tag_v2_list_ptr = context.GetIntListItemAttr("item_info.hetu_tag_level_info_v2.hetu_tag");
  std::unordered_set<int64> filter_set = {4009040, 4009018, 4009021, 4009055, 4009038, 4009056, 4009058};

  // 一次循环全部过滤掉
  std::for_each(begin, end, [&](const auto &result) {
    const auto &photo_id = result.GetId();
    const auto &photo_province_id = item_mmu_llm_province_id_ptr(result).value_or(0);
    const auto &hetu_tag_v2_list = hetu_tag_v2_list_ptr(result).value_or(std::vector<int64>{});
    if (
        // aa - 异地过滤
        (user_province_id != 0 && photo_province_id != 0 && user_province_id * 10000 != photo_province_id) ||
        // aa - 行为过滤
        (like_set.count(photo_id)) ||
        // ab - 相关性过滤
        (enable_hetu_info_v2_filter == 1 &&
         std::any_of(hetu_tag_v2_list.begin(), hetu_tag_v2_list.end(),
                     [&](const auto &d) { return filter_set.count(d); }))) {
      /* dryrun */
      // std::string like_set_str;
      // for (const auto& d : like_set) like_set_str += std::to_string(d) + "|";
      // std::string hetu_tag_v2_list_str, filter_set_str;
      // for (const auto& d : filter_set) filter_set_str += std::to_string(d) += "|";
      // for (const auto& d : hetu_tag_v2_list) hetu_tag_v2_list_str += std::to_string(d) += "|";
      // LOG(INFO) << "\n-------- province_filter -----------:"
      //           << "\nuser_province_id:" << user_province_id
      //           << "\nphoto_province_id:" << photo_province_id
      //           << "\n-------- action_filter -----------:"
      //           << "\nphoto_id:" << photo_id
      //           << "\nlike_set:" << like_set_str
      //           << "\n-------- hetu_tag_v2_filter -----------:"
      //           << "\nhetu_tag_v2_list:" << hetu_tag_v2_list_str
      //           << "\nfilter_set:" << filter_set_str;

      filter_tag_setter(result, 1);
    } else {
      filter_tag_setter(result, 0);
    }
  });

  return true;
}

  static bool CalNearbyRecallI2IScore(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto nearby_show_accessor = context.GetIntItemAttr("nearby_show");
    auto nearby_click_accessor = context.GetIntItemAttr("nearby_click");
    auto nearby_i2i_score_set = context.SetDoubleItemAttr("nearby_i2i_score");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      uint64 nearby_show = nearby_show_accessor(result).value_or(1);
      uint64 nearby_click = nearby_click_accessor(result).value_or(0);
      double willson_score = WillsonScore(nearby_show, nearby_click);
      nearby_i2i_score_set(result, willson_score * result.score);
    });
    return true;
  }

  static bool CalcRecentMonthInteractPhotoIdV2(
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
        static const std::unordered_set<int> NEARBY_CHANNEL = {2, 11, 19, 26, 35, 39, 62, 83, 84};
        auto colossus_photo_id = context.GetIntListCommonAttr("colossus_photo_id");
        auto colossus_play_time = context.GetIntListCommonAttr("colossus_play_time");
        auto colossus_duration = context.GetIntListCommonAttr("colossus_duration");
        auto colossus_channel = context.GetIntListCommonAttr("colossus_channel");
        auto colossus_label = context.GetIntListCommonAttr("colossus_label");
        auto colossus_timestamp = context.GetIntListCommonAttr("colossus_timestamp");
        uint64 month_expired_ms = base::GetTimestamp() - base::Time::kMicrosecondsPerDay * 90;
        if (!colossus_photo_id || !colossus_play_time || !colossus_duration || !colossus_channel
            || !colossus_timestamp || !colossus_label || colossus_photo_id->size() == 0) {
              return true;
        }
        bool size_match = colossus_photo_id->size() == colossus_play_time->size()
            && colossus_photo_id->size() == colossus_duration->size()
            && colossus_photo_id->size() == colossus_channel->size()
            && colossus_photo_id->size() == colossus_timestamp->size()
            && colossus_photo_id->size() == colossus_label->size();
        if (!size_match) {
          return true;
        }
        std::vector<int64> click_photo_id_list;
        std::vector<int64> follow_photo_id_list;
        std::vector<int64> like_photo_id_list;
        std::vector<int64> commit_photo_id_list;
        std::vector<int64> longview_photo_id_list;
        std::vector<int64> effective_view_photo_id_list;
        for (int i = colossus_photo_id->size() - 1; i >= 0; i--) {
          if (colossus_photo_id->at(i) <= 0) {
            continue;
          }
          if (colossus_timestamp->at(i) * base::Time::kMicrosecondsPerSecond >= month_expired_ms
              && NEARBY_CHANNEL.count(colossus_channel->at(i))) {
              uint32 item_label = colossus_label->at(i);
              uint32 play_time = colossus_play_time->at(i);
              uint32 duration = colossus_duration->at(i);
              bool like_label = item_label & 0x01;
              bool follow_label = item_label & (1 << 1);
              bool comment_label = item_label & (1 << 4);
              click_photo_id_list.push_back(colossus_photo_id->at(i));
              if (follow_label) {
                follow_photo_id_list.push_back(colossus_photo_id->at(i));
              }
              if (like_label) {
                like_photo_id_list.push_back(colossus_photo_id->at(i));
              }
              if (comment_label) {
                commit_photo_id_list.push_back(colossus_photo_id->at(i));
              }
              if (play_time >= 18) {
                longview_photo_id_list.push_back(colossus_photo_id->at(i));
              }
              if (play_time >=7 || (duration >= 3 && play_time >= duration)) {
                effective_view_photo_id_list.push_back(colossus_photo_id->at(i));
              }
            }
        }
        context.SetIntListCommonAttr("nearby_latest_click_pid_list", std::move(click_photo_id_list));
        context.SetIntListCommonAttr("nearby_latest_follow_pid_list", std::move(follow_photo_id_list));
        context.SetIntListCommonAttr("nearby_latest_like_pid_list", std::move(like_photo_id_list));
        context.SetIntListCommonAttr("nearby_latest_comment_pid_list", std::move(commit_photo_id_list));
        context.SetIntListCommonAttr("nearby_latest_longview_pid_list", std::move(longview_photo_id_list));
        context.SetIntListCommonAttr("nearby_latest_effectiveview_pid_list",
                                      std::move(effective_view_photo_id_list));
        return true;
      }

  static bool CalNebulaLiveMcEnsembleScore(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    double live_mc_pwtr_ceiling_min =
        context.GetDoubleCommonAttr("nebula_live_mc_pwtr_ceiling_min").value_or(1.0);
    double live_mc_pwtr_ceiling_boost_coeff =
        context.GetDoubleCommonAttr("nebula_live_mc_pwtr_ceiling_boost_coeff").value_or(1.0);

    auto cascade_ensemble_score_raw_accessor = context.GetDoubleItemAttr("cascade_ensemble_score_raw");
    auto cascade_ctr_score_accessor = context.GetDoubleItemAttr("cascade_ctr_score");
    auto cascade_wtr_score_accessor = context.GetDoubleItemAttr("cascade_wtr_score");

    auto cascade_ensemble_score_set = context.SetDoubleItemAttr("cascade_ensemble_score");
    auto live_mc_boost_coeff_accessor = context.GetDoubleItemAttr("live_mc_boost_coeff");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      double cascade_ensemble_score = cascade_ensemble_score_raw_accessor(result).value_or(0.0);
      double cascade_ctr_score = cascade_ctr_score_accessor(result).value_or(0.0);
      double cascade_wtr_score = cascade_wtr_score_accessor(result).value_or(0.0);
      double cascade_wtr_ctr_score = cascade_ctr_score * cascade_wtr_score;
      if (live_mc_pwtr_ceiling_boost_coeff > 1.0 && cascade_wtr_ctr_score > live_mc_pwtr_ceiling_min) {
        cascade_ensemble_score = cascade_ensemble_score * live_mc_pwtr_ceiling_boost_coeff;
      }
      double live_mc_boost_coeff = live_mc_boost_coeff_accessor(result).value_or(1.0);
      cascade_ensemble_score = cascade_ensemble_score * live_mc_boost_coeff;
      cascade_ensemble_score_set(result, cascade_ensemble_score);
    });
    return true;
  }

  static bool CalSupportShowCnt(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    static std::unordered_map<std::string, double> house_author_valid_show_config = {
        {"dis_dur_0", 0.65},
        {"dis_dur_1", 0.73},
        {"dis_dur_2", 0.8},
        {"dis_dur_3", 0.86},
        {"dis_dur_4", 0.92},
        {"dis_dur_5", 0.97},
        {"dis_dur_6", 0.98},
        {"dis_dur_7", 1.0}
    };
    auto support_show_cnt_set = context.SetDoubleItemAttr("support_show_cnt");

    auto count_extend_type = context.GetIntListItemAttr("item_dynamic_info.count_extend.type");
    auto count_extend_server_show_ts = context.GetIntListItemAttr(
      "item_dynamic_info.count_extend.server_show_ts");
    auto count_extend_value = context.GetIntListItemAttr("item_dynamic_info.count_extend.value");
    auto pReason_accessor = context.GetIntItemAttr("pReason");
    int64 current_ts = std::floor(base::GetTimestamp() / 1000);  // 观察时间点
    // auto house_author_valid_show_config = cdf();
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      int pReason = pReason_accessor(result).value_or(0);
      if (pReason != 721) {
        support_show_cnt_set(result, 0);
        return;
      }
      auto type = count_extend_type(result);
      auto server_show_ts = count_extend_server_show_ts(result);
      auto value = count_extend_value(result);
      if (!server_show_ts || !value
        || server_show_ts->size() != value->size() || server_show_ts->size() == 0) {
        support_show_cnt_set(result, 0);
        return;
      }
      std::vector<std::pair<int64, int64>> observed_counts;
      for (int i = 0; i < server_show_ts->size(); i++) {
        int64 ts = server_show_ts->at(i);
        int64 va = value->at(i);
        observed_counts.push_back(std::make_pair(ts, va));
      }
      int64 real_count = 0L;
      for (int i = 0; i < observed_counts.size(); i++) {
        int64 server_show_timestamp = observed_counts[i].first;  // 下发时间点
        int64 observed_count = observed_counts[i].second;
        // 观察时间与下发时间点的间隔时间
        int64 duration = (current_ts - server_show_timestamp) / 1000;
        int64 duration_dis = duration / 600;  // 0-max
        auto str_duration = "dis_dur_" + std::to_string(duration_dis);
        if (duration > 2000) {
          // 间隔时间越长，观察值越接近真实值，超过阈值后等于真实值
          real_count += observed_count;
        } else {
          // 间隔时间较短，通过 cdf 函数估计真实值
          real_count += (int64)(observed_count / house_author_valid_show_config[str_duration]);
        }
      }
      support_show_cnt_set(result, real_count);
    });
    return true;
  }


  static bool CalMainPhotoMcEnsembleScore(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    double nearby_feeling_photo_boost_coeff =
        context.GetDoubleCommonAttr("main_nearby_feeling_photo_boost_coeff").value_or(1.0);
    double nearby_local_life_photo_boost_coeff =
        context.GetDoubleCommonAttr("main_nearby_local_life_photo_boost_coeff").value_or(1.0);
    double main_nearby_rank_i2i_padding =
        context.GetDoubleCommonAttr("main_nearby_rank_i2i_padding").value_or(0.75);
    double main_nearby_rank_i2i_coeff =
        context.GetDoubleCommonAttr("main_nearby_rank_i2i_coeff").value_or(5.0);
    int enable_photo_rank_i2i = context.GetIntCommonAttr("enable_photo_rank_i2i").value_or(0);
    bool cancel_main_photo_mc_local_life_boost =
        context.GetIntCommonAttr("cancel_main_photo_mc_local_life_boost").value_or(0) == 1;
    // double nebula_photo_mc_pwtr_ceiling_min =
    //     context.GetDoubleCommonAttr("nebula_photo_mc_pwtr_ceiling_min").value_or(1.0);
    // double nebula_photo_mc_pwtr_ceiling_boost_coeff =
    //     context.GetDoubleCommonAttr("nebula_photo_mc_pwtr_ceiling_boost_coeff").value_or(1.0);
    auto cascade_ensemble_score_raw_accessor = context.GetDoubleItemAttr("cascade_ensemble_score_raw");
    auto extra_hetutag_accessor = context.GetIntListItemAttr("item_info.extra_hetutag");
    auto local_life_type_accessor = context.GetIntItemAttr("item_info.local_life_type");
    auto photo_mc_boost_coeff_accessor = context.GetDoubleItemAttr("photo_mc_boost_coeff");
    auto pReason_accessor = context.GetIntItemAttr("pReason");
    auto nearby_i2i_score_accessor = context.GetDoubleItemAttr("nearby_i2i_score");
    // auto cascade_ctr_score_accessor = context.GetDoubleItemAttr("cascade_ctr_score");
    // auto cascade_wtr_score_accessor = context.GetDoubleItemAttr("cascade_wtr_score");
    auto cascade_ensemble_score_set = context.SetDoubleItemAttr("cascade_ensemble_score");
    auto is_nearby_feeling_photo_set = context.SetIntItemAttr("is_nearby_feeling_photo");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      double cascade_ensemble_score_raw = cascade_ensemble_score_raw_accessor(result).value_or(0.0);
      double photo_mc_boost_coeff = photo_mc_boost_coeff_accessor(result).value_or(1.0);
      int local_life_type = local_life_type_accessor(result).value_or(0);
      int pReason = pReason_accessor(result).value_or(86);
      int is_nearby_feeling_photo = 0;
      double nearby_i2i_score = nearby_i2i_score_accessor(result).value_or(0.05);
      auto extra_hetutag = extra_hetutag_accessor(result);
      // double cascade_ctr_score = cascade_ctr_score_accessor(result).value_or(0.0);
      // double cascade_wtr_score = cascade_wtr_score_accessor(result).value_or(0.0);
      // double cascade_wtr_ctr_score = cascade_ctr_score * cascade_wtr_score;
      if ((extra_hetutag && extra_hetutag->size() > 0)
            || local_life_type > 0 || pReason == 210) {
        is_nearby_feeling_photo = 1;
      }
      double cascade_ensemble_score = cascade_ensemble_score_raw * photo_mc_boost_coeff;
      if (!cancel_main_photo_mc_local_life_boost && local_life_type > 0) {
        cascade_ensemble_score = cascade_ensemble_score * nearby_local_life_photo_boost_coeff;
      }
      if (pReason == 173 && enable_photo_rank_i2i > 0) {
        cascade_ensemble_score *= (nearby_i2i_score * main_nearby_rank_i2i_coeff
                                   + main_nearby_rank_i2i_padding);
      }
      // if (nebula_photo_mc_pwtr_ceiling_boost_coeff > 1.0
      //       && cascade_wtr_ctr_score > nebula_photo_mc_pwtr_ceiling_min) {
      //   cascade_ensemble_score = cascade_ensemble_score * nebula_photo_mc_pwtr_ceiling_boost_coeff;
      // }
      cascade_ensemble_score_set(result, cascade_ensemble_score);
      is_nearby_feeling_photo_set(result, is_nearby_feeling_photo);
    });
    return true;
  }
  static bool CalcItemIntentionDetection(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
        auto uNearbyIntentionList = context.GetStringListCommonAttr("uNearbyIntentionList");
        bool is_needed_social_photo = false;
        bool is_needed_local_life_photo = false;
        bool is_needed_hot_news_photo = false;
        bool is_needed_region_character_photo = false;
        if (uNearbyIntentionList && uNearbyIntentionList->size() > 0) {
          for (auto intention_idx : *uNearbyIntentionList) {
            std::string intention_idx_str = std::string(intention_idx);
            if (intention_idx_str == "1") {
              is_needed_social_photo = true;
            }
            if (intention_idx_str == "2") {
              is_needed_local_life_photo = true;
            }
            if (intention_idx_str == "3") {
              is_needed_hot_news_photo = true;
            }
            if (intention_idx_str == "4") {
              is_needed_region_character_photo = true;
            }
          }

          if (is_needed_social_photo) {
            auto get_hetu_two_accessor =
                context.GetIntListItemAttr("item_info.hetu_tag_level_info_v3.hetu_level_two");
            auto get_hetu_tag_accessor =
                context.GetIntListItemAttr("item_info.hetu_tag_level_info.hetu_tag");
            auto set_is_needed_social_photo =
                context.SetIntItemAttr("is_social_intention_needed_photo");
            std::for_each(begin, end, [=](const CommonRecoResult &result) {
              auto hetu_two = get_hetu_two_accessor(result);
              auto hetu_tag = get_hetu_tag_accessor(result);

              bool is_social_photo = false;
              if (hetu_two && hetu_two->size() > 0) {
                for (int64 hetu_two_item : *hetu_two) {
                  if (hetu_two_item == 673 || hetu_two_item == 672
                      || hetu_two_item == 670 || hetu_two_item == 744) {
                    is_social_photo = true;
                    break;
                  }
                }
              }

              if (hetu_tag && hetu_tag->size() > 0) {
                for (int64 hetu_tag_item : *hetu_tag) {
                  if (hetu_tag_item == 58611 || hetu_tag_item == 59549 || hetu_tag_item == 58615) {
                    is_social_photo = true;
                    break;
                  }
                }
              }

              if (is_social_photo) {
                set_is_needed_social_photo(result, 1);
              }
            });
          }
          if (is_needed_local_life_photo) {
            auto get_hetu_two_accessor =
                context.GetIntListItemAttr("item_info.hetu_tag_level_info_v3.hetu_level_two");
            auto get_hetu_tag_accessor =
                context.GetIntListItemAttr("item_info.hetu_tag_level_info.hetu_tag");
            auto set_is_needed_local_life_photo =
                context.SetIntItemAttr("is_local_life_intention_needed_photo");
            std::for_each(begin, end, [=](const CommonRecoResult &result) {
              auto hetu_two = get_hetu_two_accessor(result);
              auto hetu_tag = get_hetu_tag_accessor(result);

              bool is_local_life_photo = false;
              if (hetu_two && hetu_two->size() > 0) {
                for (int64 hetu_two_item : *hetu_two) {
                  if (hetu_two_item == 717 || hetu_two_item == 561 || hetu_two_item == 154
                      || hetu_two_item == 714 || hetu_two_item == 716 || hetu_two_item == 201
                      || hetu_two_item == 374) {
                    is_local_life_photo = true;
                    break;
                  }
                }
              }

              if (is_local_life_photo) {
                set_is_needed_local_life_photo(result, 1);
              }
            });
          }
          if (is_needed_hot_news_photo) {
            auto get_hetu_two_accessor =
                context.GetIntListItemAttr("item_info.hetu_tag_level_info_v3.hetu_level_two");
            auto get_hetu_tag_accessor =
                context.GetIntListItemAttr("item_info.hetu_tag_level_info.hetu_tag");
            auto set_is_needed_hot_news_photo =
                context.SetIntItemAttr("is_hot_news_intention_needed_photo");
            std::for_each(begin, end, [=](const CommonRecoResult &result) {
              auto hetu_two = get_hetu_two_accessor(result);
              auto hetu_tag = get_hetu_tag_accessor(result);

              bool is_hot_news_photo = false;
              if (hetu_two && hetu_two->size() > 0) {
                for (int64 hetu_two_item : *hetu_two) {
                  if (hetu_two_item == 223 || hetu_two_item == 160) {
                    is_hot_news_photo = true;
                    break;
                  }
                }
              }

              if (is_hot_news_photo) {
                set_is_needed_hot_news_photo(result, 1);
              }
            });
          }
          if (is_needed_region_character_photo) {
            auto get_hetu_two_accessor =
                context.GetIntListItemAttr("item_info.hetu_tag_level_info_v3.hetu_level_two");
            auto get_hetu_tag_accessor =
                context.GetIntListItemAttr("item_info.hetu_tag_level_info.hetu_tag");
            auto set_is_needed_region_character_photo =
                context.SetIntItemAttr("is_region_character_intention_needed_photo");
            std::for_each(begin, end, [=](const CommonRecoResult &result) {
              auto hetu_two = get_hetu_two_accessor(result);
              auto hetu_tag = get_hetu_tag_accessor(result);

              bool is_region_character_photo = false;
              if (hetu_two && hetu_two->size() > 0) {
                for (int64 hetu_two_item : *hetu_two) {
                  if (hetu_two_item == 147 || hetu_two_item == 733 || hetu_two_item  == 153
                      || hetu_two_item == 330 || hetu_two_item == 292 || hetu_two_item == 729) {
                    is_region_character_photo = true;
                    break;
                  }
                }
              }

              if (is_region_character_photo) {
                set_is_needed_region_character_photo(result, 1);
              }
            });
          }
        }
        return true;
      }

  static bool CalcIsNeededSocialPhoto(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
        auto uNearbyIntentionList = context.GetStringListCommonAttr("uNearbyIntentionList");
        bool is_needed_social_photo = false;
        if (uNearbyIntentionList && uNearbyIntentionList->size() > 0) {
          for (auto intention_idx : *uNearbyIntentionList) {
            std::string intention_idx_str = std::string(intention_idx);
            if (intention_idx_str == "1") {
              is_needed_social_photo = true;
              break;
            }
          }

          if (is_needed_social_photo) {
            auto get_hetu_two_accessor =
                context.GetIntListItemAttr("item_info.hetu_tag_level_info_v3.hetu_level_two");
            auto get_hetu_tag_accessor =
                context.GetIntListItemAttr("item_info.hetu_tag_level_info.hetu_tag");
            auto set_is_needed_social_photo =
                context.SetIntItemAttr("is_social_intention_needed_photo");
            std::for_each(begin, end, [=](const CommonRecoResult &result) {
              auto hetu_two = get_hetu_two_accessor(result);
              auto hetu_tag = get_hetu_tag_accessor(result);

              bool is_social_photo = false;
              if (hetu_two && hetu_two->size() > 0) {
                for (int64 hetu_two_item : *hetu_two) {
                  if (hetu_two_item == 673 || hetu_two_item == 672
                      || hetu_two_item == 670 || hetu_two_item == 744) {
                    is_social_photo = true;
                    break;
                  }
                }
              }

              if (hetu_tag && hetu_tag->size() > 0) {
                for (int64 hetu_tag_item : *hetu_tag) {
                  if (hetu_tag_item == 58611 || hetu_tag_item == 59549 || hetu_tag_item == 58615) {
                    is_social_photo = true;
                    break;
                  }
                }
              }

              if (is_social_photo) {
                set_is_needed_social_photo(result, 1);
              }
            });
          }
        }
        return true;
      }
  static bool CalNebulaPhotoMcEnsembleScore(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    double nearby_feeling_photo_boost_coeff =
        context.GetDoubleCommonAttr("nearby_feeling_photo_boost_coeff").value_or(1.0);
    double nearby_local_life_photo_boost_coeff =
        context.GetDoubleCommonAttr("nearby_local_life_photo_boost_coeff").value_or(1.0);
    double nebula_photo_mc_pwtr_ceiling_min =
        context.GetDoubleCommonAttr("nebula_photo_mc_pwtr_ceiling_min").value_or(1.0);
    double nebula_photo_mc_pwtr_ceiling_boost_coeff =
        context.GetDoubleCommonAttr("nebula_photo_mc_pwtr_ceiling_boost_coeff").value_or(1.0);
    auto cascade_ensemble_score_raw_accessor = context.GetDoubleItemAttr("cascade_ensemble_score_raw");
    auto extra_hetutag_accessor = context.GetIntListItemAttr("item_info.extra_hetutag");
    auto local_life_type_accessor = context.GetIntItemAttr("item_info.local_life_type");
    auto photo_mc_boost_coeff_accessor = context.GetDoubleItemAttr("photo_mc_boost_coeff");
    auto pReason_accessor = context.GetIntItemAttr("pReason");
    auto cascade_ctr_score_accessor = context.GetDoubleItemAttr("cascade_ctr_score");
    auto cascade_wtr_score_accessor = context.GetDoubleItemAttr("cascade_wtr_score");
    auto cascade_ensemble_score_set = context.SetDoubleItemAttr("cascade_ensemble_score");
    auto is_nearby_feeling_photo_set = context.SetIntItemAttr("is_nearby_feeling_photo");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      double cascade_ensemble_score_raw = cascade_ensemble_score_raw_accessor(result).value_or(0.0);
      double photo_mc_boost_coeff = photo_mc_boost_coeff_accessor(result).value_or(1.0);
      int local_life_type = local_life_type_accessor(result).value_or(0);
      int pReason = pReason_accessor(result).value_or(86);
      int is_nearby_feeling_photo = 0;
      auto extra_hetutag = extra_hetutag_accessor(result);
      double cascade_ctr_score = cascade_ctr_score_accessor(result).value_or(0.0);
      double cascade_wtr_score = cascade_wtr_score_accessor(result).value_or(0.0);
      double cascade_wtr_ctr_score = cascade_ctr_score * cascade_wtr_score;
      if ((extra_hetutag && extra_hetutag->size() > 0)
            || local_life_type > 0 || pReason == 210) {
        is_nearby_feeling_photo = 1;
      }
      double cascade_ensemble_score = cascade_ensemble_score_raw * photo_mc_boost_coeff;
      if (local_life_type > 0) {
        cascade_ensemble_score = cascade_ensemble_score * nearby_local_life_photo_boost_coeff;
      }
      if (nebula_photo_mc_pwtr_ceiling_boost_coeff > 1.0
            && cascade_wtr_ctr_score > nebula_photo_mc_pwtr_ceiling_min) {
        cascade_ensemble_score = cascade_ensemble_score * nebula_photo_mc_pwtr_ceiling_boost_coeff;
      }
      cascade_ensemble_score_set(result, cascade_ensemble_score);
      is_nearby_feeling_photo_set(result, is_nearby_feeling_photo);
    });
    return true;
  }

  static bool CalMainPhotoMcPxtr(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    double low_photo_pctr_bound =
            context.GetDoubleCommonAttr("low_photo_pctr_bound").value_or(0.0);

    auto cascade_pctr_accessor = context.GetDoubleItemAttr("cascade_pctr");
    auto cascade_plvtr_accessor = context.GetDoubleItemAttr("cascade_plvtr");
    auto cascade_pfvtr_accessor = context.GetDoubleItemAttr("cascade_pfvtr");
    auto cascade_pcmtr_accessor = context.GetDoubleItemAttr("cascade_pcmtr");
    auto cascade_pwtr_accessor = context.GetDoubleItemAttr("cascade_pwtr");
    auto cascade_pltr_accessor = context.GetDoubleItemAttr("cascade_pltr");
    auto cascade_wtd_score_accessor = context.GetDoubleItemAttr("cascade_wtd_score");
    auto photo_exp_boost_score_accessor = context.GetDoubleItemAttr("photo_exp_boost_score");
    auto ctr_accessor = context.GetDoubleItemAttr("ctr");

    auto cascade_ctr_score_set = context.SetDoubleItemAttr("cascade_ctr_score");
    auto cascade_lvtr_score_set = context.SetDoubleItemAttr("cascade_lvtr_score");
    auto cascade_fvtr_score_set = context.SetDoubleItemAttr("cascade_fvtr_score");
    auto cascade_cmtr_score_set = context.SetDoubleItemAttr("cascade_cmtr_score");
    auto cascade_wtr_score_set = context.SetDoubleItemAttr("cascade_wtr_score");

    auto cascade_ltr_score_set = context.SetDoubleItemAttr("cascade_ltr_score");
    auto cascade_wtr_score2_set = context.SetDoubleItemAttr("cascade_wtr_score2");
    auto cascade_rmtr_score_set = context.SetDoubleItemAttr("cascade_rmtr_score");
    auto cascade_wtd_score2_set = context.SetDoubleItemAttr("cascade_wtd_score2");
    auto photo_exp_boost_score2_set = context.SetDoubleItemAttr("photo_exp_boost_score2");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto cascade_ctr_score = cascade_pctr_accessor(result).value_or(0.0);
      double boost = 0.0;
      if (cascade_ctr_score > low_photo_pctr_bound) {
          boost = 100.0;
      }
      double cascade_lvtr_score = cascade_plvtr_accessor(result).value_or(0.0) + boost;
      double cascade_fvtr_score = cascade_pfvtr_accessor(result).value_or(0.0) + boost;
      double cascade_cmtr_score = cascade_pcmtr_accessor(result).value_or(0.0) + boost;
      double cascade_wtr_score2 = cascade_pwtr_accessor(result).value_or(0.0) + boost;
      double cascade_rmtr_score = ctr_accessor(result).value_or(0.0);
      double cascade_ltr_score = cascade_plvtr_accessor(result).value_or(0.0);
      double cascade_wtr_score = cascade_pfvtr_accessor(result).value_or(0.0);
      double cascade_wtd_score2 = cascade_wtd_score_accessor(result).value_or(0.0) + boost;
      double photo_exp_boost_score2 = cascade_ctr_score;
      if (photo_exp_boost_score2 > 0.5) {
        photo_exp_boost_score2 += boost;
      }
      cascade_ctr_score_set(result, cascade_ctr_score);
      cascade_lvtr_score_set(result, cascade_lvtr_score);
      cascade_fvtr_score_set(result, cascade_fvtr_score);
      cascade_cmtr_score_set(result, cascade_cmtr_score);
      cascade_wtr_score_set(result, cascade_wtr_score);

      cascade_ltr_score_set(result, cascade_ltr_score);
      cascade_wtr_score2_set(result, cascade_wtr_score2);
      cascade_rmtr_score_set(result, cascade_rmtr_score);
      cascade_wtd_score2_set(result, cascade_wtd_score2);
      photo_exp_boost_score2_set(result, photo_exp_boost_score2);
    });
    return true;
  }
  static bool CalNebulaLiveMcPxtr(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto cascade_pctr_accessor = context.GetDoubleItemAttr("cascade_pctr");
    auto cascade_plvtr_accessor = context.GetDoubleItemAttr("cascade_plvtr");
    auto cascade_pwtr_accessor = context.GetDoubleItemAttr("cascade_pwtr");
    auto cascade_pltr_accessor = context.GetDoubleItemAttr("cascade_pltr");

    auto cascade_ctr_score_set = context.SetDoubleItemAttr("cascade_ctr_score");
    auto cascade_slvtr_score_set = context.SetDoubleItemAttr("cascade_slvtr_score");
    auto cascade_wtr_score_set = context.SetDoubleItemAttr("cascade_wtr_score");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto cascade_ctr_score = cascade_pctr_accessor(result).value_or(0.0);
      auto cascade_pltr = cascade_pltr_accessor(result).value_or(0.0);
      double cascade_slvtr_score = cascade_ctr_score * cascade_plvtr_accessor(result).value_or(0.0);
      double cascade_ltr_score = cascade_pltr / (1 - cascade_pltr) * (1 - cascade_ctr_score);
      double cascade_wtr_score = cascade_pwtr_accessor(result).value_or(0.0);

      cascade_ctr_score_set(result, cascade_ctr_score);
      cascade_slvtr_score_set(result, cascade_slvtr_score);
      cascade_wtr_score_set(result, cascade_wtr_score);
    });
    return true;
  }

  static bool CalcItemIsFollowed(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
        std::unordered_set<int64> follow_set = {};
        auto user_follow_list_v1 = context.GetIntListCommonAttr("user_follow_author_list");
        auto user_follow_list_v2 = context.GetIntListCommonAttr("uFollowList");

        if (user_follow_list_v1) {
          for (auto ele : *user_follow_list_v1) {
            follow_set.insert(ele);
          }
        }
        if (user_follow_list_v2) {
          for (auto ele : *user_follow_list_v2) {
            follow_set.insert(ele);
          }
        }
        auto set_is_followed_photo = context.SetIntItemAttr("is_followed_photo");
        auto get_photo_author_processor = context.GetIntItemAttr("item_info.author_id");
        std::for_each(begin, end, [=](const CommonRecoResult &result) {
          auto author_id = get_photo_author_processor(result).value_or(0);
          if (author_id > 0 && follow_set.count(author_id)) {
            set_is_followed_photo(result, 1);
          } else {
            set_is_followed_photo(result, 0);
          }
        });
        return true;
      }

      static void SplitStringToStringIntMap(const std::string &config,
                                            folly::F14FastMap<std::string, int> *map) {
        std::vector<std::string> items;
        base::SplitStringWithOptions(config, ";", true, true, &items);
        for (auto &str : items) {
          if (str.size() == 0) {
            continue;
          }
          std::vector<std::string> pair;
          base::SplitStringWithOptions(str, ",", true, true, &pair);
          if (pair.size() != 2) {
            continue;
          }
          int val = 0;
          if (absl::SimpleAtoi(pair[1], &val)) {
            (*map)[pair[0]] = val;
          }
        }
      }
      static void SplitStringToIntVec(const std::string &str, std::vector<int> *intvec, char delimiter) {
        std::stringstream ss(str);
        std::string item;
        while (std::getline(ss, item, delimiter)) {
          int value;
          if (absl::SimpleAtoi(item, &value)) {
            intvec->push_back(value);
          }
        }
      }
      static bool CalcIsWholeStationColdStartFilter(const CommonRecoLightFunctionContext &context,
                                                    RecoResultConstIter begin, RecoResultConstIter end) {
        int64 enable_nearby_whole_station_coldstart_filter =
            context.GetIntCommonAttr("enable_nearby_whole_station_coldstart_filter").value_or(0);
        std::string nearby_whole_station_coldstart_filter_str_rules =
            context.GetStringCommonAttr("nearby_whole_station_coldstart_filter_str_rules")
                .value_or("")
                .data();
        auto photo_cold_start_garantee_accessor =
            context.GetIntItemAttr("item_info.photo_cold_start_garantee");
        auto nebula_stats_real_show_count_accessor =
            context.GetIntItemAttr("item_info.nebula_stats_real_show_count");
        auto thanos_stats_real_show_count_accessor =
            context.GetIntItemAttr("item_info.thanos_stats_real_show_count");

        auto is_whole_station_coldstart_filt_set = context.SetIntItemAttr("is_whole_station_coldstart_filt");

        if (enable_nearby_whole_station_coldstart_filter > 0) {
          folly::F14FastMap<std::string, int> coldstart_filter_rules_map;
          SplitStringToStringIntMap(nearby_whole_station_coldstart_filter_str_rules,
                                    &coldstart_filter_rules_map);
          std::vector<std::string> mix_keys;
          std::vector<int> shield_vv_threshold;
          int quality_score_threshold = 0;
          int shield_rate = 0;
          // 解析 ab 参数
          for (auto it = coldstart_filter_rules_map.begin(); it != coldstart_filter_rules_map.end(); it++) {
            shield_rate = it->second;
            // 0:-1_0:-1_0_0_0 会导致视频全屏蔽，禁止配置
            if (shield_rate <= 0 || it->first == "0:-1_0:-1_0_0_0_0") {
              continue;
            }
            base::SplitString(it->first, "_", &mix_keys);
            if (mix_keys.size() < 6) {
              continue;
            }
            // 第一维是 vv 屏蔽，格式为 min_vv,max_vv，max_vv = -1 表示无上界
            SplitStringToIntVec(mix_keys[0], &shield_vv_threshold, ':');
            if ((shield_vv_threshold.size() < 2) ||
                (shield_vv_threshold[1] != -1 && shield_vv_threshold[0] > shield_vv_threshold[1])) {
              continue;
            }
            // 第四维是 quality_score 屏蔽，<=该 score 阈值的视频全屏蔽
            if (!absl::SimpleAtoi(mix_keys[3], &quality_score_threshold)) {
              continue;
            }
          }
          // 判断是否需要过滤
          std::for_each(begin, end, [=](const CommonRecoResult &result) {
            int64 slide_show_cnt = nebula_stats_real_show_count_accessor(result).value_or(0) +
                                   thanos_stats_real_show_count_accessor(result).value_or(0);
            int64 cold_start_garantee = photo_cold_start_garantee_accessor(result).value_or(0);
            bool is_vv_shield = false;
            bool is_quality_shield = false;
            int quality_score_bucket = 0;
            if (cold_start_garantee > 0) {
              quality_score_bucket = (cold_start_garantee >> 1) & 7;
            }
            if ((slide_show_cnt >= shield_vv_threshold.at(0)) &&
                (shield_vv_threshold.at(1) == -1 || slide_show_cnt < shield_vv_threshold.at(1))) {
              is_vv_shield = true;
            }
            if (quality_score_bucket > 0 && quality_score_bucket <= quality_score_threshold) {
              is_quality_shield = true;
            }
            if (is_vv_shield && is_quality_shield) {
              is_whole_station_coldstart_filt_set(result, 1);
            } else {
              is_whole_station_coldstart_filt_set(result, 0);
            }
          });
        }
        return true;
      }

      // 数值区间结构
      struct ValueRange {
        int lower = -1;
        int upper = -1;

        bool check(int value) const {
          return (lower == -1 || value >= lower) &&
                  (upper == -1 || value <= upper);
        }
      };

      // 比特检查配置
      struct BitCondition {
        int start = 0;
        int length = 1;
        enum CompareOp { LT, EQ, GT, LTE, GTE } op = EQ;
        int value = 0;

        bool check(uint32_t target) const {
            const uint32_t mask = (1 << length) - 1;
            const int extracted = (target >> start) & mask;
            switch (op) {
                case LT:  return extracted < value;
                case EQ:  return extracted == value;
                case GT:  return extracted > value;
                case LTE: return extracted <= value;
                case GTE: return extracted >= value;
            }
            return false;
        }
      };
      // 过滤规则结构
      struct FilterRule {
        bool enable_vv_check = false;
        ValueRange vv_range;

        bool enable_fans_check = false;
        ValueRange fans_range;

        bool enable_tail_group_check = false;
        std::string tail_key;
        folly::F14FastSet<int> tail_groups;

        bool enable_upload_time_check = false;
        int64 upload_time_threshold = 0;

        bool enable_shield_bit_check = false;
        BitCondition bit_condition;
      };

      static void ParseVcsValueFilterRule(const std::string&
       filter_rule_kconf_key, std::vector<FilterRule>* filter_rules) {
        // 解析 kconf 获取屏蔽规则
        auto default_val = std::make_shared<::Json::Value>();
        std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<::Json::Value>>> json_conf;
        json_conf = ks::infra::KConf().Get(filter_rule_kconf_key, default_val);
        std::shared_ptr<::Json::Value> filter_rule_config = json_conf->Get();
        if (!json_conf || !filter_rule_config || !filter_rule_config->isObject()) {
          LOG_EVERY_N(ERROR, 10000) << "ValuablePhotoOpenFilterProcessor:"
                                      << "Failed to retrieve KConf value from path:"
                                      << filter_rule_kconf_key;
          return;
        }
        if ((*filter_rule_config).isMember("rules") && (*filter_rule_config)["rules"].isArray()) {
          for (auto &rule_js : (*filter_rule_config)["rules"]) {
            if (!rule_js.isNull() && rule_js.isObject()) {
              FilterRule rule;
              // 解析 vv 范围规则
              rule.enable_vv_check = JsonHelper::SafeGetBoolValue(rule_js, "enable_vv_check");
              if (rule.enable_vv_check) {
                std::string vv_range_str = JsonHelper::SafeGetString(rule_js, "vv_range");
                std::vector<int> vv_range_list;
                SplitStringToIntVec(vv_range_str, &vv_range_list, ',');
                if (vv_range_list.size() == 2 &&
                    ((vv_range_list[1] == -1) || (vv_range_list[0] < vv_range_list[1]))) {
                  rule.vv_range.lower = vv_range_list[0];
                  rule.vv_range.upper = vv_range_list[1];
                }
              }
              // 解析粉段范围规则
              rule.enable_fans_check = JsonHelper::SafeGetBoolValue(rule_js, "enable_fans_check");
              if (rule.enable_fans_check) {
                std::string fans_range_str = JsonHelper::SafeGetString(rule_js, "fans_range");
                std::vector<int> fans_range_list;
                SplitStringToIntVec(fans_range_str, &fans_range_list, ',');
                if (fans_range_list.size() == 2 &&
                    ((fans_range_list[1] == -1) || (fans_range_list[0] < fans_range_list[1]))) {
                  rule.fans_range.lower = fans_range_list[0];
                  rule.fans_range.upper = fans_range_list[1];
                }
              }
              // 解析尾号规则
              rule.enable_tail_group_check = JsonHelper::SafeGetBoolValue(rule_js, "enable_tail_group_check");
              if (rule.enable_tail_group_check) {
                rule.tail_key = JsonHelper::SafeGetString(rule_js, "tail_key");
                std::string tail_group_str = JsonHelper::SafeGetString(rule_js, "tail_groups");
                ks::reco::StringToIntegerSet(tail_group_str, &rule.tail_groups);
              }
              // 解析上传时间规则
              rule.enable_upload_time_check =
               JsonHelper::SafeGetBoolValue(rule_js, "enable_upload_time_check");
              if (rule.enable_upload_time_check) {
                rule.upload_time_threshold = JsonHelper::SafeGetInt64Value(rule_js, "upload_time_threshold");
              }
              // 解析 bit 位规则
              rule.enable_shield_bit_check = JsonHelper::SafeGetBoolValue(rule_js, "enable_shield_bit_check");
              if (rule.enable_shield_bit_check) {
                rule.bit_condition.start = JsonHelper::SafeGetIntValue(rule_js, "bit_start");
                rule.bit_condition.length = JsonHelper::SafeGetIntValue(rule_js, "bit_length");
                static std::map<std::string, BitCondition::CompareOp> op_map = {
                    {"LT", BitCondition::LT}, {"LTE", BitCondition::LTE},
                    {"EQ", BitCondition::EQ}, {"GT", BitCondition::GT},
                    {"GTE", BitCondition::GTE}
                };
                std::string compare_op = JsonHelper::SafeGetString(rule_js, "compare_op");
                if (op_map.find(compare_op) == op_map.end()) {
                  LOG_EVERY_N(ERROR, 1000) << "ValuablePhotoOpenFilterProcessor:"
                  << "Invalid compare_op value: " << compare_op;
                  continue;
                }
                rule.bit_condition.op = op_map.at(compare_op);
                rule.bit_condition.value = JsonHelper::SafeGetIntValue(rule_js, "compare_value");
              }
              if (filter_rules != nullptr) {
                filter_rules->emplace_back(rule);
              }
            }
          }
        }
      }

      static bool VcsShieldCheckVV(const int64 show_cnt, const FilterRule& rule) {
        if (!rule.enable_vv_check) return true;
        return rule.vv_range.check(show_cnt);
      }

      static bool VcsShieldCheckFans(const int64 fans_cnt, const FilterRule& rule) {
        if (!rule.enable_fans_check) return true;
        return rule.fans_range.check(fans_cnt);
      }

      static bool VcsShieldCheckTailGroup(const int64 author_id,
       const folly::F14FastMap<std::string, int> &author_tails, const FilterRule& rule) {
        if (!rule.enable_tail_group_check) return true;
        int author_tail = -1;
        auto iter = author_tails.find(rule.tail_key);
        if (iter != author_tails.end()) {
          author_tail = iter->second;
        }
        if (rule.tail_groups.count(author_tail) > 0) {
          return true;
        }
        return false;
      }

      static bool VcsShieldCheckUploadTime(const int64 timestamp, const FilterRule& rule) {
        if (!rule.enable_upload_time_check) return true;
        const auto upload_time = timestamp / 1000;  // 输入是微妙, 使用毫秒
        if (upload_time >= rule.upload_time_threshold) {
          return true;
        }
        return false;
      }

      static bool VcsShieldCheckBitShield(const int64 coldstart_guarantee_value, const FilterRule& rule) {
        if (!rule.enable_shield_bit_check) return true;
        if (coldstart_guarantee_value > 0) {
          return rule.bit_condition.check(coldstart_guarantee_value);
        }
        return false;
      }

      static bool CalcIsWholeStationColdStartFilterV2(const CommonRecoLightFunctionContext &context,
                                                    RecoResultConstIter begin, RecoResultConstIter end) {
        std::string nearby_vcs_shield_exp_rule_kconf_key =
            context.GetStringCommonAttr("nearby_vcs_shield_exp_rule_kconf_key")
                .value_or("")
                .data();
        auto photo_cold_start_garantee_accessor =
            context.GetIntItemAttr("item_info.photo_cold_start_garantee");
        auto nebula_stats_real_show_count_accessor =
            context.GetIntItemAttr("item_info.nebula_stats_real_show_count");
        auto thanos_stats_real_show_count_accessor =
            context.GetIntItemAttr("item_info.thanos_stats_real_show_count");
        auto author_id_accessor =
            context.GetIntItemAttr("item_info.author_id");
        auto author_fans_count_accessor =
            context.GetIntItemAttr("item_info.fans_count");
        auto timestamp_accessor =
            context.GetIntItemAttr("item_info.timestamp");
        auto video_cold_start_author_tails_json_accessor =
         context.GetStringItemAttr("item_info.video_cold_start_author_tails_json");

        // context.GetProtoMessagePtrItemAttr<std::map<std::string, int>>(absl::string_view attr_name)

        auto is_whole_station_coldstart_filt_v2_set =
         context.SetIntItemAttr("is_whole_station_coldstart_filt_v2");

        std::vector<FilterRule> filter_rules;
        ParseVcsValueFilterRule(nearby_vcs_shield_exp_rule_kconf_key, &filter_rules);

        // 判断是否需要过滤
        std::for_each(begin, end, [=](const CommonRecoResult &result) {
          int64 slide_show_cnt = nebula_stats_real_show_count_accessor(result).value_or(0) +
                                  thanos_stats_real_show_count_accessor(result).value_or(0);
          int64 cold_start_garantee = photo_cold_start_garantee_accessor(result).value_or(0);
          int64 author_id = author_id_accessor(result).value_or(0);
          int64 author_fans_count = author_fans_count_accessor(result).value_or(0);
          int64 timestamp = timestamp_accessor(result).value_or(0);
          std::string author_tails_json =
           video_cold_start_author_tails_json_accessor(result).value_or("").data();

          // R"({
          //   "young_ugc_hash_author_tail": 3,
          //   "ugc_hash_author_tail": 3,
          //   "rl_rerank_author_tail_key": 5,
          //   "produce_author_hashtag_tail_v1": 4,
          //   "ann_author_exp_tail_v2key": 2,
          //   "hq_pool_new_author_tail": 4,
          //   "category_filter_author_tail_key": 5,
          //   "ann_author_exp_tail_v1key": 1,
          //   "explore_hash_author_tail_key": 0,
          //   "climb_retrieval_author_tail": 0,
          //   "ann_author_exp_tail_v0key": 9,
          //   "explore_author_exp_tail": 8,
          //   "ann_author_exp_tail_v3key": 9,
          //   "follow_hash_author_tail_key": -1,
          //   "vcs_author_tail_162": -1,
          //   "timeliness_tail": 0,
          //   "galaxyAuthorExpGroupInteger": 8,
          //   "vcs_author_tail_pr_mc_20250305": 5,
          //   "climb_hash_author_tail_key": 4
          // })";

          base::Json author_tails_json_obj(base::StringToJson(author_tails_json));
          folly::F14FastMap<std::string, int> author_tails;
          auto it = author_tails_json_obj.object_begin();
          while (it != author_tails_json_obj.object_end()) {
            const std::string& key = it->first;
            if (it->second->IsInteger()) {
              int64 default_value = 0;
              int value = static_cast<int>(it->second->IntValue(default_value));
              author_tails[key] = value;
            } else {
              LOG_EVERY_N(INFO, 10000) << "json parse not int";
            }
            it++;
          }

          bool filter_flag = false;

          for (const auto& rule : filter_rules) {
            if (!VcsShieldCheckVV(slide_show_cnt, rule)) continue;
            if (!VcsShieldCheckFans(author_fans_count, rule)) continue;
            if (!VcsShieldCheckTailGroup(author_id, author_tails, rule)) continue;
            if (!VcsShieldCheckUploadTime(timestamp, rule)) continue;
            if (!VcsShieldCheckBitShield(cold_start_garantee, rule)) continue;
            LOG_EVERY_N(INFO, 100000) << "NearbyVcsValueShiledExpRefactor:"
              << "Success Filter photo:"
              << " photo_id=" << result.GetId()
              << " author_id=" << author_id
              << " show_cnt=" << slide_show_cnt
              << " shield ruls================"
              << " vv_lower=" << rule.vv_range.lower
              << " vv_upper=" << rule.vv_range.upper
              << " tail_key=" << rule.tail_key
              << " upload_time_threshold=" << rule.upload_time_threshold
              << " bit_start=" << rule.bit_condition.start
              << " bit_length=" << rule.bit_condition.length;
            filter_flag = true;
            break;
          }
          if (filter_flag) {
            is_whole_station_coldstart_filt_v2_set(result, 1);
          } else {
            is_whole_station_coldstart_filt_v2_set(result, 0);
          }
        });
        return true;
      }

  static bool CalMainLiveMcPxtr(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto cascade_pctr_accessor = context.GetDoubleItemAttr("cascade_pctr");
    auto cascade_plvtr_accessor = context.GetDoubleItemAttr("cascade_plvtr");
    auto cascade_pwtr_accessor = context.GetDoubleItemAttr("cascade_pwtr");
    auto cascade_pltr_accessor = context.GetDoubleItemAttr("cascade_pltr");
    auto cascade_pcmtr_accessor = context.GetDoubleItemAttr("cascade_pcmtr");
    auto cascade_psvtr_accessor = context.GetDoubleItemAttr("cascade_psvtr");
    auto is_followed_accessor = context.GetIntItemAttr("is_followed");

    auto cascade_ctr_score_set = context.SetDoubleItemAttr("cascade_ctr_score");
    auto cascade_slvtr_score_set = context.SetDoubleItemAttr("cascade_slvtr_score");
    auto cascade_psltr_score_set = context.SetDoubleItemAttr("cascade_psltr_score");
    auto cascade_pscmtr_score_set = context.SetDoubleItemAttr("cascade_pscmtr_score");
    auto cascade_pswtr_score_set = context.SetDoubleItemAttr("cascade_pswtr_score");
    auto cascade_pssvtr_score_set = context.SetDoubleItemAttr("cascade_pssvtr_score");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto cascade_ctr_score = cascade_pctr_accessor(result).value_or(0.0);
      double cascade_slvtr_score = cascade_ctr_score * cascade_plvtr_accessor(result).value_or(0.0);
      double cascade_psltr_score = cascade_ctr_score * cascade_pltr_accessor(result).value_or(0.0);
      double cascade_pscmtr_score = cascade_ctr_score * cascade_pcmtr_accessor(result).value_or(0.0);
      double cascade_pswtr_score = cascade_ctr_score * cascade_pwtr_accessor(result).value_or(0.0);
      double cascade_pssvtr_score = cascade_ctr_score * cascade_psvtr_accessor(result).value_or(0.0);
      if (is_followed_accessor(result).value_or(1) == 1) {
        cascade_pswtr_score = 0.0;
      }

      cascade_ctr_score_set(result, cascade_ctr_score);
      cascade_slvtr_score_set(result, cascade_slvtr_score);
      cascade_psltr_score_set(result, cascade_psltr_score);
      cascade_pscmtr_score_set(result, cascade_pscmtr_score);
      cascade_pswtr_score_set(result, cascade_pswtr_score);
      cascade_pssvtr_score_set(result, cascade_pssvtr_score);
    });
    return true;
  }

  static bool CalNebulaPhotoMcPxtr(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    double low_photo_pctr_bound =
            context.GetDoubleCommonAttr("low_photo_pctr_bound").value_or(0.0);

    auto cascade_pctr_accessor = context.GetDoubleItemAttr("cascade_pctr");
    auto cascade_plvtr_accessor = context.GetDoubleItemAttr("cascade_plvtr");
    auto cascade_pfvtr_accessor = context.GetDoubleItemAttr("cascade_pfvtr");
    auto cascade_pcmtr_accessor = context.GetDoubleItemAttr("cascade_pcmtr");
    auto cascade_pwtr_accessor = context.GetDoubleItemAttr("cascade_pwtr");
    auto cascade_pltr_accessor = context.GetDoubleItemAttr("cascade_pltr");
    auto cascade_wtd_score_accessor = context.GetDoubleItemAttr("cascade_wtd_score");
    auto cascade_psvr_accessor = context.GetDoubleItemAttr("cascade_psvr");
    auto cascade_phtr_accessor = context.GetDoubleItemAttr("cascade_phtr");
    auto photo_exp_boost_score_accessor = context.GetDoubleItemAttr("photo_exp_boost_score");
    auto ctr_accessor = context.GetDoubleItemAttr("ctr");

    auto cascade_ctr_score_set = context.SetDoubleItemAttr("cascade_ctr_score");
    auto cascade_lvtr_score_set = context.SetDoubleItemAttr("cascade_lvtr_score");
    auto cascade_fvtr_score_set = context.SetDoubleItemAttr("cascade_fvtr_score");
    auto cascade_cmtr_score_set = context.SetDoubleItemAttr("cascade_cmtr_score");
    auto cascade_wtr_score_set = context.SetDoubleItemAttr("cascade_wtr_score");

    auto cascade_ltr_score_set = context.SetDoubleItemAttr("cascade_ltr_score");
    auto cascade_wtr_score2_set = context.SetDoubleItemAttr("cascade_wtr_score2");
    auto cascade_rmtr_score_set = context.SetDoubleItemAttr("cascade_rmtr_score");
    auto cascade_wtd_score2_set = context.SetDoubleItemAttr("cascade_wtd_score2");
    auto photo_exp_boost_score2_set = context.SetDoubleItemAttr("photo_exp_boost_score2");
    auto cascade_svr_score_set = context.SetDoubleItemAttr("cascade_svr_score");
    auto cascade_htr_score_set = context.SetDoubleItemAttr("cascade_htr_score");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto cascade_ctr_score = cascade_pctr_accessor(result).value_or(0.0);
      double boost = 0.0;
      if (cascade_ctr_score > low_photo_pctr_bound) {
          boost = 100.0;
      }
      double cascade_lvtr_score = cascade_plvtr_accessor(result).value_or(0.0) + boost;
      double cascade_fvtr_score = cascade_pfvtr_accessor(result).value_or(0.0) + boost;
      double cascade_cmtr_score = cascade_pcmtr_accessor(result).value_or(0.0) + boost;
      double cascade_wtr_score2 = cascade_pwtr_accessor(result).value_or(0.0) + boost;
      double cascade_rmtr_score = ctr_accessor(result).value_or(0.0);
      double cascade_ltr_score = cascade_plvtr_accessor(result).value_or(0.0);
      double cascade_wtr_score = cascade_pfvtr_accessor(result).value_or(0.0);
      double cascade_wtd_score2 = cascade_wtd_score_accessor(result).value_or(0.0) + boost;
      double cascade_svr_score = cascade_psvr_accessor(result).value_or(0.0);
      double cascade_htr_score = cascade_phtr_accessor(result).value_or(0.0);
      double photo_exp_boost_score2 = cascade_ctr_score;
      if (photo_exp_boost_score2 > 0.5) {
        photo_exp_boost_score2 += boost;
      }
      cascade_ctr_score_set(result, cascade_ctr_score);
      cascade_lvtr_score_set(result, cascade_lvtr_score);
      cascade_fvtr_score_set(result, cascade_fvtr_score);
      cascade_cmtr_score_set(result, cascade_cmtr_score);
      cascade_wtr_score_set(result, cascade_wtr_score);

      cascade_ltr_score_set(result, cascade_ltr_score);
      cascade_wtr_score2_set(result, cascade_wtr_score2);
      cascade_rmtr_score_set(result, cascade_rmtr_score);
      cascade_wtd_score2_set(result, cascade_wtd_score2);
      photo_exp_boost_score2_set(result, photo_exp_boost_score2);
      cascade_svr_score_set(result, cascade_svr_score);
      cascade_htr_score_set(result, cascade_htr_score);
    });
    return true;
  }

  static bool CalIsReview(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto reviewed_dynamic_accessor = context.GetIntItemAttr("item_dynamic_info.reviewed");
    auto reviewed_accessor = context.GetIntItemAttr("item_info.reviewed");
    auto is_cover_reviewed_accessor = context.GetIntItemAttr("item_dynamic_info.is_cover_reviewed");
    auto data_set_tag_bit_accessor = context.GetIntItemAttr("item_info.data_set_tag_bit");
    auto live_is_reviewed = context.SetIntItemAttr("live_is_reviewed");
    auto photo_is_reviewed = context.SetIntItemAttr("photo_is_reviewed");
    auto photo_is_retr_v3 = context.SetIntItemAttr("photo_is_retr_v3");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto reviewed_dynamic = reviewed_dynamic_accessor(result).value_or(0);
      auto reviewed = reviewed_accessor(result).value_or(0);
      auto is_cover_reviewed = is_cover_reviewed_accessor(result).value_or(0);
      auto data_set_tag_bit = data_set_tag_bit_accessor(result).value_or(0);
      int live_is_reviewed_value = reviewed_dynamic + reviewed + is_cover_reviewed;
      int photo_is_reviewed_value = reviewed_dynamic + reviewed;
      live_is_reviewed(result, live_is_reviewed_value);
      photo_is_reviewed(result, photo_is_reviewed_value);
      photo_is_retr_v3(result, data_set_tag_bit & 1);
    });
    return true;
  }

  static bool CalUploadHour(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto timestamp_accessor = context.GetIntItemAttr("item_info.timestamp");
    auto pLeafAgeHour = context.SetIntItemAttr("pLeafAgeHour");
    auto pUploadTimeGapHour = context.SetIntItemAttr("pUploadTimeGapHour");
    int64 current_ts = base::GetTimestamp();
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto timestamp = timestamp_accessor(result).value_or(0);
      int64 hour = (current_ts - timestamp)  / 3600 / 1000000;
      pLeafAgeHour(result, hour);
      pUploadTimeGapHour(result, hour);
    });
    return true;
  }

  static bool CalMaxPhotoDistance(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    double last_feed_photo_max_distance_km =
            context.GetDoubleCommonAttr("last_feed_photo_max_distance_km").value_or(500.0);
    double nebula_user_active_photo_distance_threshold =
            context.GetDoubleCommonAttr("nebula_user_active_photo_distance_threshold").value_or(100.0);
    int enable_nebula_low_active_photo_distance_expand =
            context.GetIntCommonAttr("enable_nebula_low_active_photo_distance_expand").value_or(0);
    int is_high_quality_hit_user = context.GetIntCommonAttr("is_high_quality_hit_user").value_or(0);
    int nebula_photo_distance_user_active_threshold =
        context.GetIntCommonAttr("nebula_photo_distance_user_active_threshold").value_or(0);
    int user_app_active_level = context.GetIntCommonAttr("user_app_active_level").value_or(0);
    int enable_nebula_low_active_photo_distance_offset =
        context.GetIntCommonAttr("enable_nebula_low_active_photo_distance_offset").value_or(0);
    int nebula_photo_distance_user_active_offset =
        context.GetIntCommonAttr("nebula_photo_distance_user_active_offset").value_or(1);
    int nearby_page_offset =
        context.GetIntCommonAttr("nearby_reco_user_info.nearby_page_offset").value_or(1);
    int enable_nearby_distance_map_filter =
            context.GetIntCommonAttr("enable_nearby_distance_map_filter").value_or(0);
    double user_city_distance_limit_km =
            context.GetDoubleCommonAttr("user_city_distance_limit_km").value_or(200.0);
    int64 enable_nearby_dynamic_distance_filter =
        context.GetIntCommonAttr("enable_nearby_dynamic_distance_filter").value_or(0);
    int64 enable_nearby_dynamic_distance_map_filter =
        context.GetIntCommonAttr("enable_nearby_dynamic_distance_map_filter").value_or(0);
    double nearby_distance_filter_base_km =
        context.GetDoubleCommonAttr("nearby_distance_filter_base_km").value_or(50.0);
    double nearby_distance_filter_delta_km =
        context.GetDoubleCommonAttr("nearby_distance_filter_delta_km").value_or(600.0);
    double nearby_distance_map_filter_base_km =
        context.GetDoubleCommonAttr("nearby_distance_map_filter_base_km").value_or(50.0);

    auto pReason_accessor = context.GetIntItemAttr("pReason");
    auto isPublishRelatioSource_accessor = context.GetIntItemAttr("isPublishRelatioSource");

    auto max_photo_distance_set = context.SetDoubleItemAttr("max_photo_distance");

    base::PseudoRandom local_random(base::GetTimestamp());
    double rand_val = local_random.GetDouble();

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      double max_photo_distance = 100.0;
      auto pReason = pReason_accessor(result).value_or(86);
      auto isPublishRelatioSource = isPublishRelatioSource_accessor(result).value_or(0);
      if (pReason == 86 && is_high_quality_hit_user == 0) {
          max_photo_distance = 20.0;
      } else if (pReason == 149) {
        max_photo_distance = last_feed_photo_max_distance_km;
      }
      if (enable_nearby_dynamic_distance_filter > 0) {
        max_photo_distance = rand_val * rand_val * nearby_distance_filter_delta_km +
          nearby_distance_filter_base_km;
      } else if (enable_nearby_dynamic_distance_map_filter > 0) {
        max_photo_distance = rand_val * rand_val * user_city_distance_limit_km +
          nearby_distance_map_filter_base_km;
      } else {
        max_photo_distance = rand_val * rand_val * 600.0 + max_photo_distance / 2.0;
      }
      if (enable_nearby_distance_map_filter > 0) {
        max_photo_distance = user_city_distance_limit_km;
      }
      if (isPublishRelatioSource > 0) {
         max_photo_distance = 1000.0;
      }
      int enable_distance_expand = enable_nebula_low_active_photo_distance_expand;
      if (enable_nebula_low_active_photo_distance_offset > 0 && enable_distance_expand > 0) {
        if (nebula_photo_distance_user_active_offset > nearby_page_offset) {
          enable_distance_expand = 0;
        }
      }
      if (enable_distance_expand > 0 && user_app_active_level > 0
            && user_app_active_level <= nebula_photo_distance_user_active_threshold
            && nebula_user_active_photo_distance_threshold > max_photo_distance) {
        max_photo_distance = nebula_user_active_photo_distance_threshold;
      }
      max_photo_distance_set(result, max_photo_distance);
    });
    return true;
  }

  static bool CalSafeLevelFilter(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    int live_user_min_item_safety_level =
        context.GetIntCommonAttr("live_user_min_item_safety_level").value_or(4);
    int photo_user_min_item_safety_level =
        context.GetIntCommonAttr("photo_user_min_item_safety_level").value_or(4);
    int u_risk_level = context.GetIntCommonAttr("u_risk_level").value_or(0);
    int enable_live_skip_risk =
        context.GetIntCommonAttr("enable_nearby_live_skip_risk_filter").value_or(0);
    int is_skip_risk_province = context.GetIntCommonAttr("isSkipRiskProv").value_or(0);

    auto item_safe_level_accessor = context.GetIntItemAttr("item_info.safe_level");
    auto i_risk_level_accessor = context.GetIntItemAttr("item_dynamic_info.risk_level");
    auto pLeafDistance_accessor = context.GetDoubleItemAttr("pLeafDistance");

    auto filter_by_safe_level_set = context.SetIntItemAttr("filter_by_safe_level");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      int filter_by_safe_level = 0;
      int item_safe_level = item_safe_level_accessor(result).value_or(0);
      int i_risk_level = i_risk_level_accessor(result).value_or(2);
      if (u_risk_level > i_risk_level) {
        filter_by_safe_level = 1;
      }
      if (result.GetType() == ks::reco::RecoEnum::ITEM_TYPE_PHOTO) {
        if (photo_user_min_item_safety_level > item_safe_level) {
          filter_by_safe_level = 1;
        }
      } else {
        if (live_user_min_item_safety_level > item_safe_level) {
          filter_by_safe_level = 1;
        }
        if (enable_live_skip_risk && is_skip_risk_province && u_risk_level == 3) {
          filter_by_safe_level = 0;
        }
        double pLeafDistance = pLeafDistance_accessor(result).value_or(9999.0);
        if (pLeafDistance < 10.0) {
           filter_by_safe_level = 0;
        }
      }
      filter_by_safe_level_set(result, filter_by_safe_level);
    });
    return true;
  }

  static bool CalIsSameCityRaw(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    int64 user_city = context.GetIntCommonAttr("user_city_id").value_or(0);
    auto item_city_accessor = context.GetIntItemAttr("item_info.city_id");
    auto is_same_city_set = context.SetIntItemAttr("is_same_city_raw");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      int is_same_city_raw = 0;
      int64 item_city = item_city_accessor(result).value_or(0);
      if (user_city > 0 && user_city == item_city) {
        is_same_city_raw = 1;
      }
      is_same_city_set(result, is_same_city_raw);
    });
    return true;
  }


  static bool CalIsSameCitySlide(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto is_same_city_raw_accessor = context.GetIntItemAttr("is_same_city_raw");
    auto pLeafDistance_accessor = context.GetDoubleItemAttr("pLeafDistance");

    auto is_same_city_gen_set = context.SetIntItemAttr("is_same_city_gen");
    auto contras_is_same_city_gen_set = context.SetIntItemAttr("contras_is_same_city_gen");
    auto contrasive_distance_set = context.SetDoubleItemAttr("contrasive_distance");
    base::PseudoRandom local_random(base::GetTimestamp());
    double rand_val = local_random.GetDouble();
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      int64 is_same_city_gen = is_same_city_raw_accessor(result).value_or(0);
      double distance = pLeafDistance_accessor(result).value_or(1000.0);
      if (is_same_city_gen < 1) {
        if (distance < 50.0) {
          is_same_city_gen = 1;
        }
      }
      double contrasive_distance = distance;
      if (distance < 30.0) {
        contrasive_distance += (100 + 100.0 * rand_val);
      } else if (distance > 100.0) {
        contrasive_distance = 50 * rand_val;
      } else {
        if (is_same_city_gen) {
          contrasive_distance = contrasive_distance * (1 + rand_val);
        } else {
          contrasive_distance = contrasive_distance * (1 - rand_val);
        }
      }
      is_same_city_gen_set(result, is_same_city_gen);
      contras_is_same_city_gen_set(result, 1 - is_same_city_gen);
      contrasive_distance_set(result, contrasive_distance);
    });
    return true;
  }


  static bool CalIsSameCity(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    int user_adcode = context.GetIntCommonAttr("user_adcode").value_or(0);

    auto is_house_photo_accessor = context.GetIntItemAttr("item_info.is_house_photo");
    auto item_adcode_accessor = context.GetIntItemAttr("item_info.item_geo_info.adcode");
    auto house_source_city_accessor =
          context.GetIntItemAttr("item_info.house_photo_hetu_info.house_source_city_id");
    auto pLeafDistance_accessor = context.GetDoubleItemAttr("pLeafDistance");

    auto item_city_code_set = context.SetIntItemAttr("item_city_code");
    auto is_same_city_set = context.SetIntItemAttr("is_same_city");
    auto is_same_city_gen_set = context.SetIntItemAttr("is_same_city_gen");
    auto contras_is_same_city_gen_set = context.SetIntItemAttr("contras_is_same_city_gen");
    auto is_same_prov_set = context.SetIntItemAttr("is_same_prov");
    auto is_house_photo_set = context.SetIntItemAttr("is_house_photo");
    auto contrasive_distance_set = context.SetDoubleItemAttr("contrasive_distance");
    base::PseudoRandom local_random(base::GetTimestamp());
    double rand_val = local_random.GetDouble();
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      int item_city_code = 0;
      int item_prov_code = 0;
      int is_same_city = 0;
      int is_same_prov = 0;
      int is_same_city_gen = 0;
      int is_house_photo = is_house_photo_accessor(result).value_or(0);
      int item_adcode = item_adcode_accessor(result).value_or(0);
      int house_source_city = house_source_city_accessor(result).value_or(0);
      if (house_source_city > 0) {
        item_city_code = house_source_city / 100;
      } else if (item_adcode > 0) {
        item_city_code = item_adcode / 100;
      }
      item_prov_code = item_city_code / 100;
      if (user_adcode > 0 && user_adcode / 100 == item_city_code) {
        is_same_city = 1;
        is_same_city_gen = 1;
      }
      if (user_adcode > 0 && user_adcode / 10000 == item_prov_code) {
        is_same_prov = 1;
      }
      double distance = pLeafDistance_accessor(result).value_or(1000.0);
      if (is_same_city_gen < 1) {
        if (distance < 50.0) {
          is_same_city_gen = 1;
        }
      }
      double contrasive_distance = distance;
      if (distance < 30.0) {
        contrasive_distance += (100 + 100.0 * rand_val);
      } else if (distance > 100.0) {
        contrasive_distance = 50 * rand_val;
      } else {
        if (is_same_city_gen) {
          contrasive_distance = contrasive_distance * (1 + rand_val);
        } else {
          contrasive_distance = contrasive_distance * (1 - rand_val);
        }
      }
      item_city_code_set(result, item_city_code);
      is_same_city_set(result, is_same_city);
      is_same_prov_set(result, is_same_prov);
      is_same_city_gen_set(result, is_same_city_gen);
      contras_is_same_city_gen_set(result, 1 - is_same_city_gen);
      contrasive_distance_set(result, contrasive_distance);
      is_house_photo_set(result, is_house_photo);
    });
    return true;
  }

  // 异地 poi 分发过滤
  static bool CalPhotoPoiDiffEnricher(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    int64 user_province_id = context.GetIntCommonAttr("user_province_id").value_or(0);
    auto filter_by_poi_diff_set = context.SetIntItemAttr("filter_by_poi_diff");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto item_province_id_accessor = context.GetIntItemAttr("item_province_id");
      auto item_poi_province_id_accessor = context.GetIntItemAttr("item_info.poi_info.province_id");
      int64 item_poi_province_id = item_poi_province_id_accessor(result).value_or(0);
      int64 item_province_id = item_province_id_accessor(result).value_or(0);
      int filter_by_poi_diff = 0;
      // 如果视频有挂载 poi 先判断与该视频的 province id 是否相同 不同再与 user 当前位置进行判断
      if (item_poi_province_id > 0 && item_province_id > 0 && user_province_id > 0
          && item_poi_province_id != item_province_id
          && item_poi_province_id != user_province_id) {
        filter_by_poi_diff = 1;
      }
      filter_by_poi_diff_set(result, filter_by_poi_diff);
    });
    return true;
  }

  static bool CalIsHousePlcPhotoValid(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    int enable_filter_nonplc_house_photo =
            context.GetIntCommonAttr("enable_filter_nonplc_house_photo").value_or(0);
    if (enable_filter_nonplc_house_photo == 0) {
      return true;
    }
    auto pReason_accessor = context.GetIntItemAttr("pReason");
    auto isHousePlcPhoto_accessor = context.GetIntItemAttr("item_info.is_house_plc_photo");

    auto filter_nonplc_house_photo_set = context.SetIntItemAttr("filter_nonplc_house_photo");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      int filter_nonplc_house_photo = 0;
      int pReason = pReason_accessor(result).value_or(0);
      int isHousePlcPhoto = isHousePlcPhoto_accessor(result).value_or(0);
      if (enable_filter_nonplc_house_photo && (pReason == 670 || pReason == 722)
              && isHousePlcPhoto == 0) {
          filter_nonplc_house_photo = 1;
      }
      filter_nonplc_house_photo_set(result, filter_nonplc_house_photo);
    });
    return true;
  }

  // 将形如 grade_delta:1.6;prob_min:0.2;score_scale:1.0; 的字符串解析为 map
  static absl::flat_hash_map<std::string, double> ParseWeightParam(absl::string_view param_string) {
    absl::flat_hash_map<std::string, double> param_map;
    std::vector<absl::string_view> params = absl::StrSplit(param_string, ";", absl::SkipWhitespace());
    for (auto token : params) {
      std::vector<absl::string_view> param = absl::StrSplit(token, ":", absl::SkipWhitespace());
      if (param.size() != 2) continue;
      auto key = param[0];
      auto value = 0.0;
      if (absl::SimpleAtod(param[1], &value)) {
        param_map.emplace(key, value);
      }
    }
    return param_map;
  }

  // NOTE(litianshi03) 获取房产城市降权系数
  static bool CalcHouseCityDegradeCoeff(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    auto house_live_city_degrade_conf =
        context.GetStringCommonAttr("nearby_house_live_city_degrade_conf").value_or("");
    std::string house_live_city_degrade_conf_str = house_live_city_degrade_conf.data();
    auto house_live_city_degrade_conf_map = ParseWeightParam(house_live_city_degrade_conf_str);

    auto user_city_id =
        context.GetIntCommonAttr("house_user_city_id").value_or(0);
    std::string user_city_id_str = std::to_string(user_city_id);
    auto author_city_id_accessor = context.GetIntItemAttr("house_author_city_id");

    auto house_live_city_degrade_coeff_accessor =
        context.SetDoubleItemAttr("nearby_house_live_city_degrade_coeff");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      double coeff_result = 1.0;
      auto author_city_id = author_city_id_accessor(result).value_or(0);
      std::string author_city_id_str = std::to_string(author_city_id);
      // 外市降权
      auto iter = house_live_city_degrade_conf_map.find(author_city_id_str);
      if (iter != house_live_city_degrade_conf_map.end() && user_city_id_str != author_city_id_str) {
        coeff_result = iter->second;
      }
      house_live_city_degrade_coeff_accessor(result, coeff_result);
    });
    return true;
  }

  // NOTE(litianshi03) 房产召回过滤生成指定城市过滤标标记
  static bool GenHouseCityFlag(const CommonRecoLightFunctionContext &context,
                               RecoResultConstIter begin, RecoResultConstIter end) {
    auto skip_city_list =
        context.GetIntListCommonAttr("skip_city_list");
    auto is_skip_city_accessor =
        context.SetIntItemAttr("is_skip_city");
    auto item_city_id_accessor = context.GetIntItemAttr("item_info.city_id");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      int skip_flag = 0;
      auto item_city_id = item_city_id_accessor(result).value_or(0);
      if (skip_city_list && skip_city_list->size() > 0) {
        for (int64 skip_city_id : *skip_city_list) {
          if (skip_city_id == item_city_id) {
            skip_flag = 1;
            break;
          }
        }
      }
      is_skip_city_accessor(result, skip_flag);
    });
    return true;
  }

  // NOTE(litianshi03) 获取房产指定城市 boost
  static bool CalcHouseTargetCityBoost(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    auto house_live_city_boost_conf =
        context.GetStringCommonAttr("nearby_house_live_target_city_boost_conf").value_or("");
    std::string house_live_city_boost_conf_str = house_live_city_boost_conf.data();
    auto house_live_city_boost_conf_map = ParseWeightParam(house_live_city_boost_conf_str);

    auto user_city_id =
        context.GetIntCommonAttr("house_user_city_id").value_or(0);
    std::string user_city_id_str = std::to_string(user_city_id);
    auto author_city_id_accessor = context.GetIntItemAttr("house_author_city_id");
    auto hs_score_input_accessor = context.GetDoubleItemAttr("hs_score");

    auto hs_score_output_accessor =
        context.SetDoubleItemAttr("hs_score");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      double coeff = 1.0;
      double hs_score = hs_score_input_accessor(result).value_or(0.0);
      auto author_city_id = author_city_id_accessor(result).value_or(0);
      std::string author_city_id_str = std::to_string(author_city_id);
      // 指定用户同市提权
      auto iter = house_live_city_boost_conf_map.find(author_city_id_str);
      if (iter != house_live_city_boost_conf_map.end() && user_city_id_str == author_city_id_str) {
        coeff = iter->second;
        hs_score = hs_score * coeff;
      }
      hs_score_output_accessor(result, hs_score);
    });
    return true;
  }

  static bool CalcHouseReasonBoost(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    auto house_user_group_feasury_key_list =
        context.GetIntListCommonAttr("house_user_group_list")
        .value_or(absl::Span<const int64>());
    auto user_cluster_list =
        context.GetIntListCommonAttr("uPropertyUserCluster")
        .value_or(absl::Span<const int64>());
    auto is_boost_for_target_user =
        context.GetIntCommonAttr("is_boost_for_target_user")
        .value_or(0);
    auto house_reason_boost_conf =
        context.GetStringCommonAttr("house_reason_boost_conf").value_or("");
    std::string house_reason_boost_conf_str = house_reason_boost_conf.data();
    auto house_reason_boost_conf_map = ParseWeightParam(house_reason_boost_conf_str);

    auto reason_accessor = context.GetIntItemAttr("reason");
    auto hs_score_input_accessor = context.GetDoubleItemAttr("hs_score");

    auto hs_score_output_accessor =
        context.SetDoubleItemAttr("hs_score");

    int is_target_user = 0;
    for (auto house_user_type : house_user_group_feasury_key_list) {
      if (is_target_user == 1) {
        break;
      }
      for (auto user_cluster : user_cluster_list) {
        if (house_user_type == user_cluster) {
          is_target_user = 1;
          break;
        }
      }
    }

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      double coeff = 1.0;
      double hs_score = hs_score_input_accessor(result).value_or(0.0);
      auto reason = std::to_string(reason_accessor(result).value_or(0));
      auto iter = house_reason_boost_conf_map.find(reason);
      if ((!is_boost_for_target_user || is_target_user) && iter != house_reason_boost_conf_map.end()) {
        coeff = iter->second;
        hs_score = hs_score * coeff;
      }
      hs_score_output_accessor(result, hs_score);
    });

    return true;
  }

  // NOTE(litianshi03) 房产 rerank 阶段重置指定城市统计 ctr
  static bool ResetHouseCityCtr(const CommonRecoLightFunctionContext &context,
                                RecoResultConstIter begin, RecoResultConstIter end) {
    auto skip_city_list =
        context.GetIntListCommonAttr("skip_city_list");
    auto ele_pctr_output_accessor =
        context.SetDoubleItemAttr("ele_pctr");
    auto emp_ctr_output_accessor =
        context.SetDoubleItemAttr("emp_ctr");
    auto ele_pctr_input_accessor =
        context.GetDoubleItemAttr("ele_pctr");
    auto emp_ctr_input_accessor =
        context.GetDoubleItemAttr("emp_ctr");
    auto item_city_id_accessor = context.GetDoubleItemAttr("item_info.city_id");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto item_city_id = item_city_id_accessor(result).value_or(0);
      auto ele_pctr = ele_pctr_input_accessor(result).value_or(0.0);
      auto emp_ctr = emp_ctr_input_accessor(result).value_or(0.0);
      if (skip_city_list && skip_city_list->size() > 0) {
        for (int64 skip_city_id : *skip_city_list) {
          if (skip_city_id == item_city_id) {
            ele_pctr = 1.0;
            emp_ctr = 1.0;
            break;
          }
        }
      }
      ele_pctr_output_accessor(result, ele_pctr);
      emp_ctr_output_accessor(result, emp_ctr);
    });
    return true;
  }

  // NOTE(litianshi03) 房产 rerank 阶段按用户年龄设置 fr_rank 模型分截断阈值
  static bool CalcHouseRerankAgeCutoff(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto age_cutoff_conf =
        context.GetStringCommonAttr("nearby_house_live_rerank_age_cutoff_param");
    std::string age_cutoff_conf_str = "";
    if (age_cutoff_conf) {
      age_cutoff_conf_str = std::string(*age_cutoff_conf);
    }
    auto age_cutoff_map = ParseWeightParam(age_cutoff_conf_str);
    auto user_age =
        context.GetIntCommonAttr("uAgeSeg").value_or(0);
    double user_age_threshold = 0.0;
    auto iter = age_cutoff_map.find(std::to_string(user_age));
    if (iter != age_cutoff_map.end()) {
      user_age_threshold = iter->second;
    }
    auto age_cutoff_skip_city_list =
        context.GetIntListCommonAttr("age_cutoff_skip_city_list");
    std::set<int64> age_cutoff_skip_city_set;
    if (age_cutoff_skip_city_list && age_cutoff_skip_city_list->size() > 0) {
      for (int64 skip_city_id : *age_cutoff_skip_city_list) {
        age_cutoff_skip_city_set.insert(skip_city_id);
      }
    }

    auto model_score_accessor =
        context.GetDoubleItemAttr("fullrank_ori_ctr");
    auto author_city_id_accessor =
        context.GetIntItemAttr("house_author_city_id");
    auto age_cutoff_flag_accessor =
        context.SetIntItemAttr("filter_by_age_cutoff");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      int age_cutoff_flag = 0;
      auto model_score = model_score_accessor(result).value_or(0.0);
      auto author_city_id = author_city_id_accessor(result).value_or(0);
      if (!age_cutoff_skip_city_set.count(author_city_id) &&
          model_score < user_age_threshold) {
        age_cutoff_flag = 1;
      }

      age_cutoff_flag_accessor(result, age_cutoff_flag);
    });
    return true;
  }

  // NOTE(litianshi03) 房产 rerank 阶段按房产模型分截断阈值
  static bool CalcHouseRerankModelCutoff(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    double model_cutoff_param =
        context.GetDoubleCommonAttr("nearby_house_live_rerank_model_cutoff_param").value_or(0.0);
    double model_itr_cutoff_param =
        context.GetDoubleCommonAttr("nearby_house_live_rerank_model_itr_cutoff_param").value_or(0.0);
    double model_evtr_cutoff_param =
        context.GetDoubleCommonAttr("nearby_house_live_rerank_model_evtr_cutoff_param").value_or(0.0);
    double flower_model_cutoff_param =
        context.GetDoubleCommonAttr("nearby_house_live_flower_rerank_model_cutoff_param").value_or(0.0);
    double flower_model_itr_cutoff_param =
        context.GetDoubleCommonAttr("nearby_house_live_flower_rerank_model_itr_cutoff_param").value_or(0.0);
    double flower_model_evtr_cutoff_param =
        context.GetDoubleCommonAttr("nearby_house_live_flower_rerank_model_evtr_cutoff_param").value_or(0.0);
    auto model_cutoff_skip_city_list =
        context.GetIntListCommonAttr("model_cutoff_skip_city_list");
    std::set<int64> model_cutoff_skip_city_set;
    if (model_cutoff_skip_city_list && model_cutoff_skip_city_list->size() > 0) {
      for (int64 skip_city_id : *model_cutoff_skip_city_list) {
        model_cutoff_skip_city_set.insert(skip_city_id);
      }
    }

    auto model_score_accessor =
        context.GetDoubleItemAttr("house_model_plctr");
    auto model_itr_score_accessor =
        context.GetDoubleItemAttr("house_model_itr");
    auto model_evtr_score_accessor =
        context.GetDoubleItemAttr("house_model_etr");
    auto author_city_id_accessor =
        context.GetIntItemAttr("house_author_city_id");
    auto is_flower_live_accessor =
        context.GetIntItemAttr("item_info.house_live_info.is_flower_house_live");
    auto model_cutoff_flag_accessor =
        context.SetIntItemAttr("filter_by_model_cutoff");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      int model_cutoff_flag = 0;
      auto model_score = model_score_accessor(result).value_or(0.0);
      auto model_itr_score = model_itr_score_accessor(result).value_or(0.0);
      auto model_evtr_score = model_evtr_score_accessor(result).value_or(0.0);
      auto author_city_id = author_city_id_accessor(result).value_or(0);
      auto is_flower_live = is_flower_live_accessor(result).value_or(0);
      if (!model_cutoff_skip_city_set.count(author_city_id)) {
        if (is_flower_live == 1) {
          if (model_score < flower_model_cutoff_param ||
              model_itr_score < flower_model_itr_cutoff_param ||
              model_evtr_score < flower_model_evtr_cutoff_param) {
            model_cutoff_flag = 1;
          }
        } else {
          if (model_score < model_cutoff_param ||
              model_itr_score < model_itr_cutoff_param ||
              model_evtr_score < model_evtr_cutoff_param) {
            model_cutoff_flag = 1;
          }
        }
      }

      model_cutoff_flag_accessor(result, model_cutoff_flag);
    });
    return true;
  }

  // NOTE(litianshi03) 房产生成直播标记
  static bool GenHouseFlag(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    auto house_live_flag_accessor =
        context.SetIntItemAttr("lLiveHouseIsLiveTag");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      house_live_flag_accessor(result, 1);
    });
    return true;
  }

  // NOTE(litianshi03) 判断房产同省人群
  static bool GenHouseSameProvinceFlag(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    int house_user_province_id =
        context.GetIntCommonAttr("house_user_province_id").value_or(0);
    std::string house_user_province_id_str = std::to_string(house_user_province_id);

    auto author_province_id_processor =
        context.GetIntItemAttr("house_author_province_id");
    auto building_province_id_processor =
        context.GetStringItemAttr("house_building_province_id");
    auto author_signed_province_id_processor =
        context.GetStringItemAttr("house_author_signed_province_id");
    auto is_same_province_item_accessor =
        context.SetIntItemAttr("is_same_province_item");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      int is_same_province_item = 0;
      auto author_province_id = author_province_id_processor(result).value_or(0);
      std::string author_province_id_str = std::to_string(author_province_id);
      auto building_province_id = building_province_id_processor(result).value_or("");
      std::string building_province_id_str = building_province_id.data();
      auto author_signed_province_id = author_signed_province_id_processor(result).value_or("");
      std::string author_signed_province_id_str = author_signed_province_id.data();

      std::string item_province_id_str = "";
      // 房源地 > 开播地 > 签约地
      if (building_province_id_str != "" && building_province_id_str != "0") {
        item_province_id_str = building_province_id_str;
      } else if (author_province_id_str != "" && author_province_id_str != "0") {
        item_province_id_str = author_province_id_str;
      } else if (author_signed_province_id_str != "" && author_signed_province_id_str != "0") {
        item_province_id_str = author_signed_province_id_str;
      }
      if (item_province_id_str != "" &&
          item_province_id_str != "0" &&
          item_province_id_str == house_user_province_id_str) {
        is_same_province_item = 1;
      }
      is_same_province_item_accessor(result, is_same_province_item);
    });
    return true;
  }
  static bool CalcTopItemBeforeDiversify(
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
        std::vector<int64> top_item_pids;
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
            int64 photo_id = result.GetId();
            if (top_item_pids.size() < 6) {
              top_item_pids.push_back(photo_id);
            }
          });
        context.SetIntListCommonAttr("top_item_pids_before_diversity", std::move(top_item_pids));
        return true;
      }
  static bool SetNewPymkWithoutFillback(
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
        auto set_publish_relatio_source_accessor = context.SetIntItemAttr("isPublishRelatioSource");
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
            set_publish_relatio_source_accessor(result, 0);
          });
        return true;
      }
  static bool CalcTopItemDiffAfterDiversity(
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
        auto top_pids_org = context.GetIntListCommonAttr("top_item_pids_before_diversity");

        auto is_social_photo_accessor = context.GetIntItemAttr("is_social_intention_needed_photo");
        auto is_local_life_photo_accessor = context.GetIntItemAttr("is_local_life_intention_needed_photo");
        auto is_hot_news_photo_accessor = context.GetIntItemAttr("is_hot_news_intention_needed_photo");
        auto is_region_photo_accessor = context.GetIntItemAttr("is_region_character_intention_needed_photo");

        auto set_social_insert_photo_accessor = context.SetIntItemAttr("intention_social_insert_pid");
        auto set_local_life_insert_photo_accessor = context.SetIntItemAttr("intention_local_life_insert_pid");
        auto set_hot_news_insert_photo_accessor = context.SetIntItemAttr("intention_hot_news_insert_pid");
        auto set_region_insert_photo_accessor = context.SetIntItemAttr("intention_region_insert_pid");

        int cur_idx = 0;
        if (!top_pids_org) {
          return true;
        }
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
            int64 photo_id = result.GetId();
            if (cur_idx < top_pids_org->size()
                && std::find(top_pids_org->begin(), top_pids_org->end(), photo_id) == top_pids_org->end()) {
              if (is_social_photo_accessor(result).value_or(0) > 0) {
                set_social_insert_photo_accessor(result, 1);
              }
              if (is_local_life_photo_accessor(result).value_or(0) > 0) {
                set_local_life_insert_photo_accessor(result, 1);
              }
              if (is_hot_news_photo_accessor(result).value_or(0) > 0) {
                set_hot_news_insert_photo_accessor(result, 1);
              }
              if (is_region_photo_accessor(result).value_or(0) > 0) {
                set_region_insert_photo_accessor(result, 1);
              }
            }
            cur_idx++;
          });
        return true;
      }
  static bool CalcRecentEffectiveViewPhotoId(
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
        struct PhotoData {
          int64 duration = 0;
          int64 play_time = 0;
          int64 photo_id = -1;
          PhotoData(int64 dur, int64 p_time, int64 pid):
            duration(dur), play_time(p_time), photo_id(pid) {}
          PhotoData(): duration(0), play_time(0), photo_id(-1) {}
        };
        static const std::unordered_set<int> NEARBY_CHANNEL = {2, 11, 19, 26, 35, 39, 62, 83, 84};
        auto colossus_photo_id = context.GetIntListCommonAttr("colossus_photo_id");
        auto colossus_play_time = context.GetIntListCommonAttr("colossus_play_time");
        auto colossus_duration = context.GetIntListCommonAttr("colossus_duration");
        auto colossus_channel = context.GetIntListCommonAttr("colossus_channel");
        auto colossus_label = context.GetIntListCommonAttr("colossus_label");
        auto colossus_timestamp = context.GetIntListCommonAttr("colossus_timestamp");
        auto enable_filter_short_view = context.GetIntCommonAttr("enable_filter_short_view").value_or(0);
        uint64 month_expired_ms = base::GetTimestamp() - base::Time::kMicrosecondsPerDay * 90;
        if (!colossus_photo_id || !colossus_play_time || !colossus_duration || !colossus_channel
            || !colossus_timestamp || !colossus_label || colossus_photo_id->size() == 0) {
              return true;
        }
        bool size_match = colossus_photo_id->size() == colossus_play_time->size()
            && colossus_photo_id->size() == colossus_duration->size()
            && colossus_photo_id->size() == colossus_channel->size()
            && colossus_photo_id->size() == colossus_timestamp->size()
            && colossus_photo_id->size() == colossus_label->size();
        if (!size_match) {
          return true;
        }
        std::vector<int64> recent_photo_id_list;
        std::vector<int64> recent_play_time_list;
        std::vector<int64> recent_photo_duration_list;
        for (int i = colossus_photo_id->size() - 1; i >= 0; i--) {
          if (colossus_photo_id->at(i) <= 0) {
            continue;
          }
          if (colossus_timestamp->at(i) * base::Time::kMicrosecondsPerSecond >= month_expired_ms
              && NEARBY_CHANNEL.count(colossus_channel->at(i))) {
              uint32 item_label = colossus_label->at(i);
              bool report_label = item_label & (1 << 3);
              bool hate_label = item_label & (1 << 13);
              if (hate_label || report_label) {
                continue;
              }
              if (enable_filter_short_view == 0) {
                recent_photo_id_list.push_back(colossus_photo_id->at(i));
                recent_play_time_list.push_back(colossus_play_time->at(i));
                recent_photo_duration_list.push_back(colossus_duration->at(i));
              } else if (colossus_play_time->at(i) > std::min(3, (int)colossus_duration->at(i))) {
                recent_photo_id_list.push_back(colossus_photo_id->at(i));
                recent_play_time_list.push_back(colossus_play_time->at(i));
                recent_photo_duration_list.push_back(colossus_duration->at(i));
              }
            }
        }
        int total_photo_size = recent_photo_duration_list.size();
        std::vector<PhotoData> recent_photo_data;
        // 将数据组合成结构体
        for (int i = 0; i < total_photo_size; i++) {
          recent_photo_data.push_back(
            {recent_photo_duration_list[i], recent_play_time_list[i], recent_photo_id_list[i]});
        }
        // 按照 duration 进行排序
        std::sort(recent_photo_data.begin(), recent_photo_data.end(),
          [](const PhotoData& a, const PhotoData& b) {return a.duration < b.duration;});

        std::vector<int64> bucket_top_10_play_time_pids;
        std::vector<int64> bucket_top_3_play_time_pids;
        std::vector<int64> bucket_top_5_play_time_pids;
        int bucketSize = total_photo_size / 10;  // 每个桶的大小

        // 分桶并选择 top 10
        for (int i = 0; i < 10; i++) {
          int start = i * bucketSize;
          int end = (i == 9) ? total_photo_size : start + bucketSize;  // 最后一个桶可能会有多余的元素

          // 对每个桶内部根据 play_time 进行排序
          std::sort(recent_photo_data.begin() + start, recent_photo_data.begin() + end,
            [](const PhotoData& a, const PhotoData& b) {return a.play_time > b.play_time;});

          // 选择前 10 个 photo_id
          for (int j = start; j < start + std::min(10, bucketSize) && j < end; j++) {
            bucket_top_10_play_time_pids.push_back(recent_photo_data[j].photo_id);
          }
          for (int j = start; j < start + std::min(3, bucketSize) && j < end; j++) {
            bucket_top_3_play_time_pids.push_back(recent_photo_data[j].photo_id);
          }
          for (int j = start; j < start + std::min(5, bucketSize) && j < end; j++) {
            bucket_top_5_play_time_pids.push_back(recent_photo_data[j].photo_id);
          }
        }
        context.SetIntListCommonAttr("dur_bucket_top10_pid_list", std::move(bucket_top_10_play_time_pids));
        context.SetIntListCommonAttr("dur_bucket_top3_pid_list", std::move(bucket_top_3_play_time_pids));
        context.SetIntListCommonAttr("dur_bucket_top5_pid_list", std::move(bucket_top_5_play_time_pids));
        return true;
      }
  static bool CalcRecentInteractPhotoId(
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
        static const std::unordered_set<int> NEARBY_CHANNEL = {2, 11, 19, 26, 35, 39, 62, 83, 84};
        auto colossus_photo_id = context.GetIntListCommonAttr("colossus_photo_id");
        auto colossus_channel = context.GetIntListCommonAttr("colossus_channel");
        auto colossus_label = context.GetIntListCommonAttr("colossus_label");
        auto colossus_timestamp = context.GetIntListCommonAttr("colossus_timestamp");
        uint64 month_expired_ms = base::GetTimestamp() - base::Time::kMicrosecondsPerDay * 90;
        if (!colossus_photo_id || !colossus_channel || !colossus_label
            || !colossus_timestamp || colossus_photo_id->size() == 0) {
          return true;
        }
        bool size_match = colossus_photo_id->size() == colossus_channel->size()
            && colossus_photo_id->size() == colossus_label->size()
            && colossus_photo_id->size() == colossus_timestamp->size();
        if (!size_match) {
          return true;
        }
        std::vector<int64> nearby_recent_interact_photo_ids;
        int nearby_interact_pid_count = 0;
        for (int i = colossus_photo_id->size() - 1; i >= 0; i--) {
          if (colossus_photo_id->at(i) <= 0) {
            continue;
          }
          uint32 item_label = colossus_label->at(i);
          bool like_label = item_label & 0x01;
          bool follow_label = item_label & (1 << 1);
          bool comment_label = item_label & (1 << 4);
          if (nearby_interact_pid_count < 100
              && colossus_timestamp->at(i) * base::Time::kMicrosecondsPerSecond >= month_expired_ms
              && NEARBY_CHANNEL.count(colossus_channel->at(i))
              && (like_label || follow_label || comment_label)) {
                nearby_recent_interact_photo_ids.push_back(colossus_photo_id->at(i));
                nearby_interact_pid_count++;
              }
        }
        context.SetIntListCommonAttr("nearby_recent_interact_pid_list",
                                      std::move(nearby_recent_interact_photo_ids));
        return true;
      }

  static bool CalcUAPctrPcocPrefix(
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
        auto redis_prefix = context.GetStringCommonAttr("ua_pctr_debias_prefix");
        int64 user_code = context.GetIntCommonAttr("uTagValueLevelNumKV").value_or(0);
        int64 user_gender = context.GetIntCommonAttr("uGender").value_or(668);
        int64 user_age_seg = context.GetIntCommonAttr("uAgeSeg").value_or(0);
        std::string redis_prefix_str = "";
        if (redis_prefix) {
          redis_prefix_str = std::string(*redis_prefix);
        }
        if (user_gender == 668 || user_age_seg == 0) {
          return true;
        }
        std::string user_age_str;
        if (user_age_seg > 0) {
          user_age_str = std::to_string(user_age_seg);
        }
        std::string user_gender_str = (user_gender == 666) ? "M" : "F";
        std::vector<std::string> age_segment_candidates =
          {"AGE_0_12", "AGE_12_17", "AGE_18_23", "AGE_24_30", "AGE_31_40", "AGE_41_49", "AGE_50_INFI"};
        std::vector<std::string> gender_candidates = {"M", "F"};
        std::vector<std::string> result;
        if (redis_prefix_str.size() > 0 && user_code != 0L) {
          for (int author_idx = 1; author_idx <= 12; author_idx++) {
            for (const std::string& author_age : age_segment_candidates) {
              for (const std::string& author_gender : gender_candidates) {
                std::string combined_redis_key =
                  redis_prefix_str
                  + "_" + std::to_string(user_code) + "_" + user_age_str + "_" + user_gender_str
                  + "_" + std::to_string(author_idx) + "_" + author_age + "_" + author_gender;
                result.push_back(combined_redis_key);
              }
            }
          }
        }
        context.SetStringListCommonAttr("ua_pctr_debias_redis_key", std::move(result));
        std::string ua_pctr_debias_redis_key_prefix =
          redis_prefix_str + "_" + std::to_string(user_code) + "_" + user_age_str + "_" + user_gender_str;
        context.SetStringCommonAttr("ua_pctr_debias_redis_key_prefix", ua_pctr_debias_redis_key_prefix);

        return true;
      }
  static bool CalcUAPxtrPcocPrefix(
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
        auto redis_prefix_list = context.GetStringListCommonAttr("ua_debias_pxtr_prefix");
        std::vector<std::string> redis_prefix_str_vec;
        if (redis_prefix_list && redis_prefix_list->size() > 0) {
          for (auto redis_prefix : *redis_prefix_list) {
            std::string redis_prefix_str = std::string(redis_prefix);
            redis_prefix_str_vec.push_back(redis_prefix_str);
          }
        }

        int64 user_gender = context.GetIntCommonAttr("uGender").value_or(668);
        int64 user_age_seg = context.GetIntCommonAttr("uAgeSeg").value_or(0);
        if (user_gender == 668 || user_age_seg == 0) {
          return true;
        }
        std::string user_age_str;
        if (user_age_seg > 0) {
          user_age_str = std::to_string(user_age_seg);
        }
        std::string user_gender_str = (user_gender == 666) ? "M" : "F";
        std::vector<std::string> age_segment_candidates =
          {"AGE_0_12", "AGE_12_17", "AGE_18_23", "AGE_24_30", "AGE_31_40", "AGE_41_49", "AGE_50_INFI"};
        std::vector<std::string> gender_candidates = {"M", "F"};

        std::vector<std::string> result;
        if (redis_prefix_str_vec.size() > 0) {
          for (std::string redis_prefix_str : redis_prefix_str_vec) {
            for (const std::string& author_age : age_segment_candidates) {
              for (const std::string& author_gender : gender_candidates) {
                std::string combined_redis_key =
                  redis_prefix_str + "_" + user_gender_str + "_" + user_age_str + "_"
                  + author_gender + "_" + author_age;
                result.push_back(combined_redis_key);
              }
            }
          }
        }

        context.SetStringListCommonAttr("ua_debias_pxtr_pcoc_redis_key", std::move(result));

        return true;
      }
  static bool CalcTenMainTagPcocPrefixV2(
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
        auto redis_prefix_list = context.GetStringListCommonAttr("ten_main_tag_pcoc_prefix");
        int64 user_code = context.GetIntCommonAttr("uTagValueLevelNumKV").value_or(0);

        std::vector<std::string> redis_prefix_str_vec;
        if (redis_prefix_list && redis_prefix_list->size() > 0) {
          for (auto redis_prefix : *redis_prefix_list) {
            std::string redis_prefix_str = std::string(redis_prefix);
            redis_prefix_str_vec.push_back(redis_prefix_str);
          }
        }

        std::vector<std::string> result;
        if (redis_prefix_str_vec.size() > 0 && user_code != 0L) {
          for (std::string redis_prefix_str : redis_prefix_str_vec) {
            for (int idx = 1; idx <= 12; idx++) {
              std::string combined_redis_key =
                redis_prefix_str + "_" + std::to_string(user_code) + "_" + std::to_string(idx);
              result.push_back(combined_redis_key);
            }
          }
        }

        context.SetStringListCommonAttr("ten_main_tag_pcoc_redis_key", std::move(result));

        return true;
      }

  static bool CalcTenMainTagPcocPrefix(
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
        auto redis_prefix = context.GetStringCommonAttr("ten_main_tag_pcoc_prefix");
        int64 user_code = context.GetIntCommonAttr("uTagValueLevelNumKV").value_or(0);
        std::string redis_prefix_str = "";
        if (redis_prefix) {
          redis_prefix_str = std::string(*redis_prefix);
        }

        std::vector<std::string> result;
        if (redis_prefix_str.size() > 0 && user_code != 0L) {
          for (int idx = 1; idx <= 12; idx++) {
            std::string combined_redis_key =
              redis_prefix_str + "_" + std::to_string(user_code) + "_" + std::to_string(idx);
            result.push_back(combined_redis_key);
          }
        }

        context.SetStringListCommonAttr("ten_main_tag_pcoc_redis_key", std::move(result));

        return true;
      }

  static bool CalcTenMainTagPcocAttrEnricherV2(
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
        auto ten_main_tag_pcoc_redis_value =
          context.GetDoubleListCommonAttr("ten_main_tag_pcoc_redis_value");
        if (ten_main_tag_pcoc_redis_value && ten_main_tag_pcoc_redis_value->size() == 36) {
          auto item_tag_accessor =
            context.GetIntItemAttr("item_info.nearby_author_ten_main_group_type");
          auto item_pwtr_pcoc_accessor = context.SetDoubleItemAttr("ten_main_group_pwtr_pcoc");
          auto item_pltr_pcoc_accessor = context.SetDoubleItemAttr("ten_main_group_pltr_pcoc");
          auto item_pcmtr_pcoc_accessor = context.SetDoubleItemAttr("ten_main_group_pcmtr_pcoc");
          std::for_each(begin, end, [=](const CommonRecoResult &result) {
            auto item_tag = item_tag_accessor(result).value_or(0);
            if (item_tag > 0 && item_tag - 1 + 24 < 36) {
              item_pwtr_pcoc_accessor(result, ten_main_tag_pcoc_redis_value->at(item_tag - 1));
              item_pltr_pcoc_accessor(result, ten_main_tag_pcoc_redis_value->at(item_tag - 1 + 12));
              item_pcmtr_pcoc_accessor(result, ten_main_tag_pcoc_redis_value->at(item_tag - 1 + 24));
            }
          });
        }
        return true;
      }

  static bool CalcTenMainTagPcocAttrEnricher(
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
        auto ten_main_tag_pcoc_redis_value =
          context.GetDoubleListCommonAttr("ten_main_tag_pcoc_redis_value");
        if (ten_main_tag_pcoc_redis_value && ten_main_tag_pcoc_redis_value->size() == 12) {
          auto item_tag_accessor =
            context.GetIntItemAttr("item_info.nearby_author_ten_main_group_type");
          auto item_pwtr_pcoc_accessor = context.SetDoubleItemAttr("ten_main_group_pwtr_pcoc");
          std::for_each(begin, end, [=](const CommonRecoResult &result) {
            auto item_tag = item_tag_accessor(result).value_or(0);
            if (item_tag > 0 && item_tag - 1 < ten_main_tag_pcoc_redis_value->size()) {
              item_pwtr_pcoc_accessor(result, ten_main_tag_pcoc_redis_value->at(item_tag - 1));
            }
          });
        }
        return true;
    }
  static double PcocValue(double value) {
    return value > 0.0 ? value : 1.0;
  }
  static bool CalcUAPxtrPcocAttrEnricher(
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
        auto ua_debias_pxtr_pcoc_redis_value =
          context.GetDoubleListCommonAttr("ua_debias_pxtr_pcoc_redis_value");

        auto author_age_accessor =
          context.GetStringItemAttr("item_info.author_age_segment");
        auto author_gender_accessor =
          context.GetIntItemAttr("item_info.gender");
        auto item_pwtr_pcoc_accessor = context.SetDoubleItemAttr("ua_debias_pwtr_pcoc");
        auto item_pltr_pcoc_accessor = context.SetDoubleItemAttr("ua_debias_pltr_pcoc");
        auto item_pcmtr_pcoc_accessor = context.SetDoubleItemAttr("ua_debias_pcmtr_pcoc");
        auto item_plvtr_pcoc_accessor = context.SetDoubleItemAttr("ua_debias_plvtr_pcoc");
        auto item_pvtr_pcoc_accessor = context.SetDoubleItemAttr("ua_debias_pvtr_pcoc");

        if (ua_debias_pxtr_pcoc_redis_value && ua_debias_pxtr_pcoc_redis_value->size() == 70) {
          std::for_each(begin, end, [&](const CommonRecoResult &result) {
            // redis value in order pwtr, pltr, pcmtr, plvtr, pvtr
            std::string author_age = author_age_accessor(result).value_or("").data();
            // author_gender 0 - male; 1 - female;
            int64 author_gender = author_gender_accessor(result).value_or(-1);
            int age_idx = -1;
            if (author_age.find("AGE_0_12") != std::string::npos) age_idx = 0;
            else if (author_age.find("AGE_12_17") != std::string::npos) age_idx = 1;
            else if (author_age.find("AGE_18_23") != std::string::npos) age_idx = 2;
            else if (author_age.find("AGE_24_30") != std::string::npos) age_idx = 3;
            else if (author_age.find("AGE_31_40") != std::string::npos) age_idx = 4;
            else if (author_age.find("AGE_41_49") != std::string::npos) age_idx = 5;
            else if (author_age.find("AGE_50_INFI") != std::string::npos) age_idx = 6;

            if (age_idx >= 0 && author_gender >= 0 && author_gender < 2) {
              int pwtr_idx = age_idx * 2 + author_gender;
              int pltr_idx = 14 + age_idx * 2 + author_gender;
              int pcmtr_idx = 28 + age_idx * 2 + author_gender;
              int plvtr_idx = 42 + age_idx * 2 + author_gender;
              int pvtr_idx = 56 + age_idx * 2 + author_gender;
              if (pvtr_idx < 70) {
                item_pwtr_pcoc_accessor(result, PcocValue(ua_debias_pxtr_pcoc_redis_value->at(pwtr_idx)));
                item_pltr_pcoc_accessor(result, PcocValue(ua_debias_pxtr_pcoc_redis_value->at(pltr_idx)));
                item_pcmtr_pcoc_accessor(result, PcocValue(ua_debias_pxtr_pcoc_redis_value->at(pcmtr_idx)));
                item_plvtr_pcoc_accessor(result, PcocValue(ua_debias_pxtr_pcoc_redis_value->at(plvtr_idx)));
                item_pvtr_pcoc_accessor(result, PcocValue(ua_debias_pxtr_pcoc_redis_value->at(pvtr_idx)));
              }
            }
          });
        }
        return true;
      }
  static bool CalcUAPctrPcocAttrEnricher(
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
        auto ua_pctr_debias_redis_value =
          context.GetDoubleListCommonAttr("ua_pctr_debias_redis_value");
        auto ua_pctr_debias_redis_key =
          context.GetStringListCommonAttr("ua_pctr_debias_redis_key");
        auto ua_pctr_debias_redis_key_prefix =
          context.GetStringCommonAttr("ua_pctr_debias_redis_key_prefix");
        auto author_tag_accessor =
          context.GetIntItemAttr("item_info.nearby_author_ten_main_group_type");
        auto author_age_accessor =
          context.GetStringItemAttr("item_info.author_age_segment");
        auto author_gender_accessor =
          context.GetIntItemAttr("item_info.gender");
        auto item_pctr_pcoc_accessor = context.SetDoubleItemAttr("ua_debias_pctr_pcoc");

        std::string redis_key_prefix_str = "";
        if (ua_pctr_debias_redis_key_prefix) {
          redis_key_prefix_str = std::string(*ua_pctr_debias_redis_key_prefix);
        }
        if (ua_pctr_debias_redis_value && ua_pctr_debias_redis_key
            && ua_pctr_debias_redis_value->size() == ua_pctr_debias_redis_key->size()) {
          std::for_each(begin, end, [=](const CommonRecoResult &result) {
            auto author_tag = author_tag_accessor(result).value_or(0);
            auto author_age = author_age_accessor(result).value_or("");
            auto author_gender = author_gender_accessor(result).value_or(-1);

            std::string author_age_str = "";
            author_age_str = author_age.data();

            std::string author_gender_str = "";
            if (author_gender >= 0) {
              author_gender_str = (author_gender == 0) ? 'M' : 'F';
            }
            std::string item_redis_prefix =
              redis_key_prefix_str + "_" + std::to_string(author_tag) + "_" +
              author_age_str + "_" + author_gender_str;

            int key_idx = -1;
            int ite_idx = 0;
            for (auto redis_key : *ua_pctr_debias_redis_key) {
              std::string str_redis_key = std::string(redis_key);
              if (str_redis_key == item_redis_prefix) {
                key_idx = ite_idx;
                break;
              }
              ite_idx++;
            }
            if (key_idx >= 0 && key_idx < ua_pctr_debias_redis_value->size()) {
              double pctr_pcoc = ua_pctr_debias_redis_value->at(key_idx);
              item_pctr_pcoc_accessor(result, pctr_pcoc);
            }
          });
        }
        return true;
      }
  // NOTE(litianshi03) 房产作者扶持按作者分层打散
  static bool CalcHouseSupportAuthorDiversityBoost(
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
    auto support_author_reason_input =
        context.GetStringCommonAttr("house_support_author_reason");
    std::string support_author_reason_str = "";
    if (support_author_reason_input) {
      support_author_reason_str = std::string(*support_author_reason_input);
    }
    int support_author_reason;
    if (!absl::SimpleAtoi(support_author_reason_str, &support_author_reason)) {
      support_author_reason = -1;
    }
    auto random_conf =
        context.GetStringCommonAttr("nearby_house_support_author_random_conf");
    std::string random_conf_str = "";
    if (random_conf) {
      random_conf_str = std::string(*random_conf);
    }
    auto random_conf_map = ParseWeightParam(random_conf_str);

    std::set<std::string> support_author_level_set;
    auto support_author_level_list = context.GetStringListCommonAttr("support_author_level_list")
        .value_or(std::vector<absl::string_view>());
    auto item_reason_list = context.GetIntListCommonAttr("item_reason_list")
        .value_or(absl::Span<const int64>());

    // 确定本次请求内作者分层类型
    if (item_reason_list.size() == support_author_level_list.size()) {
      for (int i = 0; i < item_reason_list.size(); ++i) {
        auto it = random_conf_map.find(support_author_level_list[i]);
        if (item_reason_list[i] == support_author_reason &&
            it != random_conf_map.end()) {
          support_author_level_set.insert(it->first);
        }
      }
    }
    double sum_weight = 0.0;
    std::string target_level = "";
    for (auto level : support_author_level_set) {
      sum_weight += random_conf_map[level];
    }
    if (sum_weight > 0) {
      base::PseudoRandom local_random(base::GetTimestamp());
      double rand_val = local_random.GetDouble();
      double total_scope = 0.0;
      for (auto level : support_author_level_set) {
        total_scope += random_conf_map[level] / sum_weight;
        if (rand_val < total_scope) {
          target_level = level;
          break;
        }
      }
    }

    auto level_boost_conf =
        context.GetStringCommonAttr("nearby_house_support_author_level_boost_conf");
    std::string level_boost_conf_str = "";
    if (level_boost_conf) {
      level_boost_conf_str = std::string(*level_boost_conf);
    }
    auto level_boost_map = ParseWeightParam(level_boost_conf_str);

    auto reason_accessor =
        context.GetIntItemAttr("reason");
    auto author_level_accessor =
        context.GetStringItemAttr("house_support_author_level");
    auto hs_score_input_accessor = context.GetDoubleItemAttr("hs_score");
    auto hs_score_output_accessor =
        context.SetDoubleItemAttr("hs_score");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto item_reason = reason_accessor(result).value_or(0);
      auto author_level = author_level_accessor(result).value_or("");
      std::string author_level_str = "";
      author_level_str = author_level.data();
      double hs_score = hs_score_input_accessor(result).value_or(0.0);
      // 非选中 level 的清零
      if (item_reason == support_author_reason &&
          target_level != "" &&
          author_level_str != target_level) {
        hs_score = 0.0;
      }
      // 选中 level 对应提权
      if (item_reason == support_author_reason &&
          target_level != "" &&
          author_level_str == target_level) {
        double weight = 1.0;
        auto it = level_boost_map.find(target_level);
        if (it != level_boost_map.end()) {
          weight = it->second;
        }
        hs_score = weight * hs_score;
      }
      hs_score_output_accessor(result, hs_score);
    });
    return true;
  }

  // NOTE(litianshi03) 房产筛选扶持主播
  static bool CalcHouseSupportAuthorSkip(
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
    int64 total_show =
        context.GetIntCommonAttr("house_support_total_show").value_or(0);
    int64 total_show_limit =
        context.GetIntCommonAttr("nearby_house_live_support_author_total_show_limit")
            .value_or(0);
    int64 author_single_show_limit =
        context.GetIntCommonAttr("nearby_house_live_support_author_single_show_limit")
            .value_or(0);

    auto single_show_accessor =
        context.GetIntItemAttr("item_info.house_live_info.support_author_show");
    auto nice_house_author_filter_accessor =
        context.SetIntItemAttr("nice_house_author_filter");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto single_show = single_show_accessor(result).value_or(0);
      int nice_house_author_filter = 1;
      if (total_show < total_show_limit && single_show < author_single_show_limit) {
        nice_house_author_filter = 0;
      }
      nice_house_author_filter_accessor(result, nice_house_author_filter);
    });
    return true;
  }

  // NOTE(litianshi03) 生成打散后房产 affair_type
  static bool GenHouseAffairsType(
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
    int64 support_reason =
        context.GetIntCommonAttr("house_support_author_reason").value_or(721);
    int64 support_affairs_type =
        context.GetIntCommonAttr("house_support_author_affairs_type").value_or(21);
    auto normal_affairs_type_list = context.GetIntListCommonAttr("house_normal_affairs_type_list");
    int normal_affairs_type_list_size = 0;
    if (normal_affairs_type_list != absl::nullopt) {
      normal_affairs_type_list_size = normal_affairs_type_list->size();
    }
    int index = 0;

    auto reason_accessor =
        context.GetIntItemAttr("pReason");
    auto affairs_type_accessor =
        context.SetIntItemAttr("affairs_type");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int64 affairs_type_result = 0;
      int64 reason = reason_accessor(result).value_or(0);
      if (reason == support_reason) {
        affairs_type_result = support_affairs_type;
      } else if (normal_affairs_type_list_size > 0) {
        affairs_type_result = normal_affairs_type_list->at(index % normal_affairs_type_list_size);
        index++;
      }
      affairs_type_accessor(result, affairs_type_result);
    });
    return true;
  }

  // NOTE(litianshi03) 房产生成请求主播 level 请求 redis key
  static bool GenHouseAuthorLevelRedisKey(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto author_id_accessor =
        context.GetIntItemAttr("item_info.author_id");
    auto redis_key_accessor =
        context.SetStringItemAttr("support_author_redis_key");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto author_id = author_id_accessor(result).value_or(0);
      std::string author_id_str = std::to_string(author_id);
      std::string redis_key = "b_aid_" + author_id_str;
      redis_key_accessor(result, redis_key);
    });
    return true;
  }

  static bool CalIsFansValid(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
        int fans_a_threshold_ =
            context.GetIntCommonAttr("nearby_photo_filter_fans_a_threshold").value_or(10000);
        int fans_b_threshold_ =
            context.GetIntCommonAttr("nearby_photo_filter_fans_b_threshold").value_or(100000);
        int fans_c_threshold_ =
            context.GetIntCommonAttr("nearby_photo_filter_fans_c_threshold").value_or(100000);
        double fans_less_a_rate_ =
            context.GetDoubleCommonAttr("nearby_photo_filter_fans_less_a_rate").value_or(0.2);
        double fans_less_b_rate_ =
            context.GetDoubleCommonAttr("nearby_photo_filter_fans_less_b_rate").value_or(0.2);
        double fans_less_c_rate_ =
            context.GetDoubleCommonAttr("nearby_photo_filter_fans_less_c_rate").value_or(0.2);
        double fans_over_c_rate_ =
            context.GetDoubleCommonAttr("nearby_photo_filter_fans_over_c_rate").value_or(0.2);
        int enable_nearby_photo_filter_fans_ =
            context.GetIntCommonAttr("enable_nearby_photo_filter_fans").value_or(0);
        if (enable_nearby_photo_filter_fans_ < 1) {
          return true;
        }
        auto str_attr = context.GetStringCommonAttr("nearby_photo_filter_fans_scope");
        std::string filter_fans_scope = "1,2,3,4";
        if (str_attr) {
          filter_fans_scope = std::string(*str_attr);
        }
        std::unordered_set<int> scope_set;
        std::vector<std::string> scope_list_str;
        base::SplitStringWithOptions(filter_fans_scope, ",", true, true, &scope_list_str);
        for (auto id_str : scope_list_str) {
          int scope_id = 0;
          if (!base::StringToInt(id_str, &scope_id)) {
            continue;
          }
          scope_set.insert(scope_id);
        }
        auto author_fans_count_accessor = context.GetIntItemAttr("item_info.fans_count");
        auto is_filter_fans_invalid = context.SetIntItemAttr("filter_fans_invalid");
        std::for_each(begin, end, [=](const CommonRecoResult &result) {
          int filter_fans_invalid = 0;
          base::PseudoRandom local_random(base::GetTimestamp());
          double rand_val = local_random.GetDouble();
          int author_fans_count = author_fans_count_accessor(result).value_or(0);
          if (enable_nearby_photo_filter_fans_ > 0) {
            if (author_fans_count < fans_a_threshold_ && scope_set.count(1) > 0) {
              filter_fans_invalid = rand_val > fans_less_a_rate_ ? 0 : 1;
            }
            if (author_fans_count >= fans_a_threshold_ &&
                    author_fans_count < fans_b_threshold_ && scope_set.count(2) > 0) {
              filter_fans_invalid = rand_val > fans_less_b_rate_ ? 0 : 1;
            }
            if (author_fans_count >= fans_b_threshold_ &&
                    author_fans_count < fans_c_threshold_ && scope_set.count(3) > 0) {
              filter_fans_invalid = rand_val > fans_less_c_rate_ ? 0 : 1;
            }
            if (author_fans_count >= fans_c_threshold_ && scope_set.count(4) > 0) {
              filter_fans_invalid = rand_val > fans_over_c_rate_ ? 0 : 1;
            }
          }
          is_filter_fans_invalid(result, filter_fans_invalid);
        });
        return true;
      }
  static bool CalPhotoNearbyFeelingCoff(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
        auto get_second_category_id_list = context.GetIntListItemAttr("nf_second_category_id_list");
        auto second_category_weight_str = context.GetStringCommonAttr("nf_second_category_weight_str");
        auto set_photo_nearby_feeling_coff = context.SetDoubleItemAttr("photo_nearby_feeling_coff");
        int enable_nearby_feeling_category_opt =
            context.GetIntCommonAttr("enable_nearby_feeling_category_opt").value_or(0);
        std::unordered_map<int64, double> nearby_feeling_weight_map;
        if (second_category_weight_str && !second_category_weight_str->empty()) {
          nearby_feeling_weight_map = StringToWeightMap(std::string(*second_category_weight_str));
        }
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          double photo_nearby_feeling_coff = 1.0;
          double max_coff = 1.0;
          double min_coff = 1.0;
          auto second_category_id_list = get_second_category_id_list(result);
          if (second_category_id_list) {
            for (int64 second_category_id : *second_category_id_list) {
              if (second_category_id > 0 && nearby_feeling_weight_map.count(second_category_id) > 0) {
                double temp_coff = nearby_feeling_weight_map[second_category_id];
                min_coff = std::min(min_coff, temp_coff);
                max_coff = std::max(max_coff, temp_coff);
              }
            }
          }
          if (enable_nearby_feeling_category_opt > 0) {
            photo_nearby_feeling_coff = min_coff < 1.0 ? min_coff : 1.0;
            photo_nearby_feeling_coff = max_coff > 1.0 ? max_coff : photo_nearby_feeling_coff;
          }
          set_photo_nearby_feeling_coff(result, photo_nearby_feeling_coff);
        });
        return true;
      }
    static bool CalPhotoNearbyFeelingCoffChange(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
        auto origin_photo_nearby_feeling_coff_accessor =
            context.GetDoubleItemAttr("photo_nearby_feeling_coff");
        auto photo_leaf_age_hour_accessor = context.GetIntItemAttr("pLeafAgeHour");
        auto nearby_realshow_accessor = context.GetIntItemAttr("nearby_realshow");
        auto set_photo_nearby_feeling_coff_change = context.SetDoubleItemAttr("photo_nearby_feeling_coff");
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          double origin_photo_nearby_feeling_coff =
              origin_photo_nearby_feeling_coff_accessor(result).value_or(1.0);
          int photo_leaf_age_hour = photo_leaf_age_hour_accessor(result).value_or(0);
          int nearby_realshow = nearby_realshow_accessor(result).value_or(0);
          double photo_nearby_feeling_coff = origin_photo_nearby_feeling_coff;
          if (photo_leaf_age_hour > 24 * 7 || nearby_realshow > 1000) {
            photo_nearby_feeling_coff = 1.0;
          }
          set_photo_nearby_feeling_coff_change(result, photo_nearby_feeling_coff);
        });
        return true;
      }
  static bool CalPhotoExptagCoff(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
        const ::Json::Value &config = *ks::reco::RecoConfigKey::reco_nearby_nearbyExptagBoostConfig()->Get();
        auto config_key_str = context.GetStringCommonAttr("nearby_coldstart_exptag_boost_key");
        std::string config_key = std::string(*config_key_str);
        int is_user_in_nf_composite_control_province =
            context.GetIntCommonAttr("is_user_in_nf_composite_control_province").value_or(0);
        auto pReason_accessor = context.GetIntItemAttr("pReason");
        auto set_photo_coldstart_exptag_coff = context.SetDoubleItemAttr("photo_coldstart_exptag_coff");
        const ::Json::Value &boost_config = config[config_key];
        if (!boost_config.isObject() || !config.isMember(config_key)) {
          return true;
        }
        std::string weight_str = KconfGetString(boost_config, "coldstart_exptag_weight", "");
        std::unordered_map<int64, double> exp_tag_boost_map_ = StringToWeightMap(weight_str);
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          int pReason = pReason_accessor(result).value_or(0);
          double photo_coldstart_exptag_coff = 1.0;
          if (exp_tag_boost_map_.count(pReason) > 0 && is_user_in_nf_composite_control_province < 1) {
            photo_coldstart_exptag_coff = photo_coldstart_exptag_coff * exp_tag_boost_map_[pReason];
          }
          set_photo_coldstart_exptag_coff(result, photo_coldstart_exptag_coff);
        });
        return true;
      }
  static bool CalPhotoNegativeMmuSimScore(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
        auto all_negative_photo_id_list = context.GetIntListCommonAttr("all_negative_photo_id_list");
        auto mc_photo_id_list = context.GetIntListCommonAttr("mc_photo_id_list");
        if (!all_negative_photo_id_list || all_negative_photo_id_list->size() == 0 ||
              !mc_photo_id_list || mc_photo_id_list->size() == 0) {
            return true;
        }
        int photo_negative_id_max_num =
            context.GetIntCommonAttr("photo_negative_id_max_num").value_or(10);
        int enable_nearby_photo_mmu_sim_filter_norm =
            context.GetIntCommonAttr("enable_nearby_photo_mmu_sim_filter_norm").value_or(0);
        auto set_photo_negative_mmu_sim_score = context.SetDoubleItemAttr("photo_negative_mmu_sim_score");
        auto item_id_accessor = context.GetIntItemAttr("item_id");
        std::unordered_map<int64, float> mmu_negative_scores_map;
        std::string emb_service_ = "grpc_NearbyAblertHetuMmuEmb";
        const auto &emb_client_ =
            ks::serving_util::KessGrpcClient::Singleton()->GetClient2(emb_service_, "PRODUCTION", 1);
        ks::reco::bt_embd_s::BatchEmbeddingsRequest emb_request_;
        ks::reco::bt_embd_s::BatchEmbeddingsResponse emb_response_;
        ks::kess::rpc::BatchWaiter batch_waiter_;
        std::vector<ks::reco::bt_embd_s::Embedding> request_photo_embeddings_;
        std::unordered_set<uint64> negative_photos_set_;
        std::unordered_set<uint64> request_photos_set_;
        std::unordered_set<uint64> prerank_photos_set_;
        bool flag = true;
        for (int64 mc_photo_id : *mc_photo_id_list) {
          request_photos_set_.insert((uint64)mc_photo_id);
          prerank_photos_set_.insert((uint64)mc_photo_id);
        }
        if (all_negative_photo_id_list->size() < 1) {
          flag = false;
        }
        int neg_count = 0;
        for (int64 neg_photo_id : *all_negative_photo_id_list) {
          if (neg_count < photo_negative_id_max_num) {
            request_photos_set_.insert((uint64)neg_photo_id);
            negative_photos_set_.insert((uint64)neg_photo_id);
            neg_count++;
          }
        }
        if (flag) {
          for (auto id : request_photos_set_) {
            int slot = 0;
            std::string format = "kuiba";
            uint64 sign = GenSignForId(id, slot, format);
            emb_request_.add_signs(sign);
            if (negative_photos_set_.count(id) > 0) {
              emb_request_.add_anchor_signs(sign);
              emb_request_.add_anchor_weights(1.0);
            }
          }
          emb_request_.set_anchor_type(3);
          emb_request_.set_is_need_norm(1);
          auto callback = [&](const ::grpc::Status &status,
              ks::reco::bt_embd_s::BatchEmbeddingsResponse *response) {
                if (status.ok()) {
                  for (const auto &item : response->items()) {
                    ks::reco::bt_embd_s::Embedding emb;
                    emb.sign = item.first;
                    emb.value = item.second;
                    emb.slot = 0;
                    request_photo_embeddings_.push_back(emb);
                  }
                }
              };
          ks::kess::rpc::grpc::Options options;
          int emb_timeout_ = 50;
          options.SetTimeout(std::chrono::milliseconds(emb_timeout_));
          std::string shard_name = "s" + std::to_string(0);
          const auto &future = emb_client_->ByShard(shard_name)
                              ->SelectOne()
                              ->Stub<ks::reco::bt_embd_s::kess::BtEmbeddingService::Stub>()
                              ->AsyncGetBatchEmbeddings(options, emb_request_, &emb_response_);
          batch_waiter_.Add(future, callback);
          batch_waiter_.Wait();
        }
        std::string elem_format = "float";
        if (request_photo_embeddings_.size() > 0) {
          if (elem_format != "int16" && elem_format != "float") {
            flag = false;
          }
        }
        if (flag) {
          for (int i = 0; i < request_photo_embeddings_.size(); ++i) {
            const auto &embedding = request_photo_embeddings_[i];
            const auto &sign = embedding.sign;
            const auto &value = embedding.value;
            uint64 photo_id = (sign >> 60) == 0 ? (sign & ((1L << 48) - 1)) : (sign & ((1L << 60) - 1));
            if (elem_format == "int16") {
              const int16 *weights = reinterpret_cast<const int16 *>(value.data());
              int dim = value.size() / sizeof(int16);
              if (prerank_photos_set_.count(photo_id) > 0 && dim > 0) {
                mmu_negative_scores_map[photo_id] = weights[0];
              }
            } else if (elem_format == "float") {
              const float *weights = reinterpret_cast<const float *>(value.data());
              int dim = value.size() / sizeof(float);
              if (prerank_photos_set_.count(photo_id) > 0 && dim > 0) {
                mmu_negative_scores_map[photo_id] = weights[0];
              }
            }
          }
          if (enable_nearby_photo_mmu_sim_filter_norm > 0) {
            float max_negative_score = 0.0;
            float min_negative_score = 1.0;
            float photo_mmu_negative_score = 0.0;
            for (auto &item : mmu_negative_scores_map) {
              photo_mmu_negative_score = item.second;
              if (max_negative_score < photo_mmu_negative_score) {
                max_negative_score = photo_mmu_negative_score;
              }
              if (min_negative_score > photo_mmu_negative_score) {
                min_negative_score = photo_mmu_negative_score;
              }
            }
            for (auto &item : mmu_negative_scores_map) {
              float score = item.second;
              float origin_score = item.second;
              if (max_negative_score - min_negative_score <= 1e-6) {
                continue;
              }
              score = (item.second - min_negative_score) / (max_negative_score - min_negative_score);
              mmu_negative_scores_map[item.first] = score;
            }
          }
        }
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          double photo_negative_mmu_sim_score = 0.0;
          int64 photo_id = item_id_accessor(result).value_or(0);;
          if (mmu_negative_scores_map.count(photo_id) > 0 && photo_id > 0) {
            photo_negative_mmu_sim_score = mmu_negative_scores_map[photo_id];
          }
          set_photo_negative_mmu_sim_score(result, photo_negative_mmu_sim_score);
        });
        mmu_negative_scores_map.clear();
        negative_photos_set_.clear();
        request_photos_set_.clear();
        prerank_photos_set_.clear();
        request_photo_embeddings_.clear();
        emb_request_.Clear();
        emb_response_.Clear();
        batch_waiter_.Clear();
        return true;
      }
  static bool CalUserInRemitCity(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
        auto region_remit_city_list = context.GetIntListCommonAttr("region_remit_city_list");
        int user_city_id = context.GetIntCommonAttr("user_city_id").value_or(0);
        int is_user_in_remit_city = 0;
        if (!region_remit_city_list || region_remit_city_list->size() == 0) {
            return true;
        }
        for (int city_id : *region_remit_city_list) {
          if (user_city_id == city_id) {
            is_user_in_remit_city = 1;
          }
        }
        context.SetIntCommonAttr("is_user_in_remit_city", is_user_in_remit_city);
        return true;
      }
  static bool CalItemSatisfyRegionRemit(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
        int user_province_id = context.GetIntCommonAttr("user_province_id").value_or(0);
        auto biz_name_accessor = context.GetIntItemAttr("item_info.nearby_feeling_new.biz_name");
        auto pReason_accessor = context.GetIntItemAttr("pReason");
        auto item_province_id_accessor = context.GetIntItemAttr("item_province_id");
        auto set_photo_region_remit_filter = context.SetIntItemAttr("photo_region_remit_filter");
        auto set_photo_is_need_mock = context.SetIntItemAttr("is_need_mock");
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          int photo_region_remit_filter = 0;
          int pReason = pReason_accessor(result).value_or(0);
          int biz_name = biz_name_accessor(result).value_or(0);
          int item_province_id = item_province_id_accessor(result).value_or(0);
          int is_need_mock = 0;
          if (pReason == 622) {
            if (user_province_id != item_province_id && item_province_id > 0) {
              is_need_mock = 1;
              if (biz_name > 0) {
                photo_region_remit_filter = 1;
              }
            }
          }
          set_photo_region_remit_filter(result, photo_region_remit_filter);
          set_photo_is_need_mock(result, is_need_mock);
        });
        return true;
      }
  static bool CalUserItemDistanceScore(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
        auto pLeafDistance_accessor = context.GetDoubleItemAttr("pLeafDistance");
        auto author_fans_count_accessor = context.GetIntItemAttr("item_info.fans_count");
        auto set_photo_distance_score = context.SetDoubleItemAttr("distance_score");
        double kDefaultMaxDistanceKm = 500.0;
        double kDefaultLargeDistanceKm = 20.0;
        double kDistanceScoreFansCountBound = 10000;
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          double photo_distance_score = 0.0;
          double pLeafDistance = pLeafDistance_accessor(result).value_or(0.0);
          int fans_count = author_fans_count_accessor(result).value_or(0);
          if (fans_count > kDistanceScoreFansCountBound) {
            photo_distance_score = 1.0 / std::max(pLeafDistance, kDefaultLargeDistanceKm);
          } else {
            photo_distance_score = 1.0 / std::max(0.001, pLeafDistance);
          }
          set_photo_distance_score(result, photo_distance_score);
        });
        return true;
      }
  static void GetDispersePxtrList(const CommonRecoLightFunctionContext &context,
                        std::string key, int mod, double max_value,  std::vector<int64> item_id_list) {
      struct PlayItem {
        int64 photo_id = -1;
        double pxtr_score = 0.0;
        PlayItem(int64 pid, double pxtr)
            : photo_id(pid), pxtr_score(pxtr) {}
        PlayItem() : photo_id(-1), pxtr_score(0.0) {}
      };
      auto pctr_list = context.GetDoubleListCommonAttr(key);
      int item_id_list_size = item_id_list.size();

      std::vector<int64> disperse_pctr_list;
      std::vector<int64> rank_pctr_list;
      std::vector<PlayItem> play_item_list;
      if (pctr_list) {
        if (item_id_list_size != pctr_list->size()) {
          LOG(ERROR) << "GetDispersePxtrList not match key : " << key;
          return;
        }
        for (int i = 0; i < item_id_list_size; i++) {
          int64 v1 = std::floor(std::min(pctr_list->at(i), max_value) / max_value * mod);
          int64 d_value = v1 % mod;
          disperse_pctr_list.push_back(d_value);
          play_item_list.emplace_back(item_id_list.at(i), pctr_list->at(i));
        }
        std::stable_sort(play_item_list.begin(), play_item_list.end(),
          [] (const PlayItem& a, const PlayItem& b) -> bool {
            return a.pxtr_score > b.pxtr_score;
          });
        folly::F14FastMap<int64, int64> item_rank_map;
        for (int i = 0; i < item_id_list_size; i++) {
          item_rank_map[play_item_list.at(i).photo_id] = i + 1;
        }
        for (int i = 0; i < item_id_list_size; i++) {
            auto iter = item_rank_map.find(item_id_list.at(i));
            if (iter != item_rank_map.end()) {
              rank_pctr_list.push_back(iter->second);
            } else {
              rank_pctr_list.push_back(3000);
            }
        }
      }
      std::string save_key = key + "_dis";
      std::string save_key_rank = key + "_rank";
      context.SetIntListCommonAttr(save_key, std::move(disperse_pctr_list));
      context.SetIntListCommonAttr(save_key_rank, std::move(rank_pctr_list));
  }

static void GetDisperseMcPxtrList(const CommonRecoLightFunctionContext &context, std::string key, int mod,
                                  double max_value, std::vector<int64> item_id_list) {
  struct PlayItem {
    int64 photo_id = -1;
    double pxtr_score = 0.0;
    PlayItem(int64 pid, double pxtr) : photo_id(pid), pxtr_score(pxtr) {}
    PlayItem() : photo_id(-1), pxtr_score(0.0) {}
  };
  auto pctr_list = context.GetDoubleListCommonAttr(key);
  int item_id_list_size = item_id_list.size();

  std::vector<int64> disperse_pctr_list(0);
  std::vector<int64> rank_pctr_list(0);
  std::vector<PlayItem> play_item_list;
  if (pctr_list) {
    if (item_id_list_size != pctr_list->size()) {
      LOG(ERROR) << "GetDispersePxtrList not match key : " << key;
      return;
    }
    for (int i = 0; i < item_id_list_size; i++) {
      int64 v1 = std::floor(std::min(pctr_list->at(i), max_value) / max_value * mod);
      int64 d_value = v1 % mod + 1;
      disperse_pctr_list.push_back(d_value);
      play_item_list.emplace_back(item_id_list.at(i), pctr_list->at(i));
    }
    std::stable_sort(
        play_item_list.begin(), play_item_list.end(),
        [](const PlayItem &a, const PlayItem &b) -> bool { return a.pxtr_score > b.pxtr_score; });
    folly::F14FastMap<int64, int64> item_rank_map;
    for (int i = 0; i < item_id_list_size; i++) {
      item_rank_map[play_item_list.at(i).photo_id] = i + 1;
    }
    for (int i = 0; i < item_id_list_size; i++) {
      auto iter = item_rank_map.find(item_id_list.at(i));
      if (iter != item_rank_map.end()) {
        rank_pctr_list.push_back(iter->second + 1);
      } else {
        rank_pctr_list.push_back(3001);
      }
    }
    if (pctr_list->size() != disperse_pctr_list.size()) {
      LOG(ERROR) << key << " size:" << pctr_list->size() << " " << key + "_dis"
                 << " size:" << disperse_pctr_list.size() << " play_item_list size:" << play_item_list.size();
    }
    std::string save_key = key + "_dis";
    std::string save_key_rank = key + "_rank";
    context.SetIntListCommonAttr(save_key, std::move(disperse_pctr_list));
    context.SetIntListCommonAttr(save_key_rank, std::move(rank_pctr_list));
  }
}

  static void GetPreRankDispersePxtrList(const CommonRecoLightFunctionContext &context,
                                         const std::string &key, const int mod, const double max_value,
                                         const std::vector<int64> &item_id_list) {
    struct PlayItem {
      int64 photo_id = -1;
      double pxtr_score = 0.0;
      PlayItem(int64 pid, double pxtr) : photo_id(pid), pxtr_score(pxtr) {}
      PlayItem() : photo_id(-1), pxtr_score(0.0) {}
    };
    auto pctr_list = context.GetDoubleListCommonAttr(key);
    int item_id_list_size = item_id_list.size();

    std::vector<int64> disperse_pctr_list;
    std::vector<int64> rank_pctr_list;
    std::vector<PlayItem> play_item_list;
    if (pctr_list) {
      if (item_id_list_size != pctr_list->size()) {
        LOG(ERROR) << "GetPreRankDispersePxtrList not match key : " << key;
        return;
      }
      for (int i = 0; i < item_id_list_size; i++) {
        int64 v1 = std::floor(std::min(pctr_list->at(i), max_value) / max_value * mod);
        int64 d_value = v1 % mod;
        disperse_pctr_list.push_back(d_value);
        play_item_list.emplace_back(item_id_list.at(i), pctr_list->at(i));
      }
      std::stable_sort(
          play_item_list.begin(), play_item_list.end(),
          [](const PlayItem &a, const PlayItem &b) -> bool { return a.pxtr_score > b.pxtr_score; });
      folly::F14FastMap<int64, int64> item_rank_map;
      for (int i = 0; i < item_id_list_size; i++) {
        item_rank_map[play_item_list.at(i).photo_id] = i + 1;
      }
      for (int i = 0; i < item_id_list_size; i++) {
        auto iter = item_rank_map.find(item_id_list.at(i));
        if (iter != item_rank_map.end()) {
          rank_pctr_list.push_back(iter->second);
        } else {
          rank_pctr_list.push_back(8000);
        }
      }
    }
    std::string save_key = key + "_dis";
    std::string save_key_rank = key + "_rank";
    context.SetIntListCommonAttr(save_key, std::move(disperse_pctr_list));
    context.SetIntListCommonAttr(save_key_rank, std::move(rank_pctr_list));
  }
  static bool DispersePreRankPxtr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto item_id_list_ptr = context.GetIntListCommonAttr("photo_prerank_item_id_list");
    if (item_id_list_ptr) {
      std::vector<int64> item_id_list;
      for (auto &item_id : *item_id_list_ptr) {
        item_id_list.push_back(item_id);
      }
      GetPreRankDispersePxtrList(context, "photo_prerank_ctr_list", 10000, 1.0, item_id_list);
      GetPreRankDispersePxtrList(context, "photo_prerank_lvtr_list", 10000, 1.0, item_id_list);
      GetPreRankDispersePxtrList(context, "photo_prerank_fvtr_list", 10000, 1.0, item_id_list);
      GetPreRankDispersePxtrList(context, "photo_prerank_wtd_list", 10000, 1.0, item_id_list);
      GetPreRankDispersePxtrList(context, "photo_prerank_vtr_list", 10000, 1.0, item_id_list);
      GetPreRankDispersePxtrList(context, "photo_prerank_ltr_list", 10000, 1.0, item_id_list);
      GetPreRankDispersePxtrList(context, "photo_prerank_wtr_list", 10000, 1.0, item_id_list);
      GetPreRankDispersePxtrList(context, "photo_prerank_cmtr_list", 10000, 1.0, item_id_list);
      std::vector<int64> photo_prerank_duration_s_list;
      auto duration_ms_list_ptr = context.GetIntListCommonAttr("photo_prerank_duration_ms_list");
      if (duration_ms_list_ptr) {
        for (auto &duration_ms : *duration_ms_list_ptr) {
          int64 duration_second = duration_ms / 1000;
          photo_prerank_duration_s_list.push_back(duration_second);
        }
        context.SetIntListCommonAttr("photo_prerank_duration_s_list",
                                     std::move(photo_prerank_duration_s_list));
      }
    } else {
      LOG(ERROR) << "DispersePreRankPxtr Failed item_id_list is null ";
    }
    return true;
  }
  static bool SetPhotoPrerankClmScore(
    const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
      auto photo_clm_pred_embedding = context.GetDoubleListCommonAttr("prerank_clm_user_top_layer");
      auto photo_cascade_item_id_list = context.GetIntListCommonAttr("photo_prerank_item_id_list");
      auto set_photo_prerank_clm_score = context.SetDoubleItemAttr("photo_prerank_clm_score");
      if (photo_clm_pred_embedding && photo_cascade_item_id_list) {
        int size1 = photo_clm_pred_embedding->size();
        int size2 = photo_cascade_item_id_list->size();
        int temp = std::min(size1, size2);
        int rank = 0;
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          if (rank < temp) {
            set_photo_prerank_clm_score(result, photo_clm_pred_embedding->at(rank));
            rank++;
          } else {
            set_photo_prerank_clm_score(result, 0.0);
          }
        });
      }
      return true;
  }
  static bool SetPhotoClmScore(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
        auto photo_clm_pred_embedding = context.GetDoubleListCommonAttr("photo_clm_pred_embedding");
        auto photo_cascade_item_id_list = context.GetIntListCommonAttr("photo_cascade_item_id_list");
        auto set_photo_clm_score = context.SetDoubleItemAttr("photo_clm_score");
        if (photo_clm_pred_embedding && photo_cascade_item_id_list) {
          int size1 = photo_clm_pred_embedding->size();
          int size2 = photo_cascade_item_id_list->size();
          if (size2 <= size1) {
            int rank = 1;
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              if (rank <= size2) {
                set_photo_clm_score(result, photo_clm_pred_embedding->at(size2 - rank));
                rank++;
              } else {
                set_photo_clm_score(result, 0.0);
              }
            });
          }
        }
        return true;
  }
  static bool SetPhotoClmMcLtrScore(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
        auto photo_clm_pred_embedding = context.GetDoubleListCommonAttr("photo_mc_clm_ltr_embedding");
        auto photo_cascade_item_id_list = context.GetIntListCommonAttr("photo_cascade_item_id_list");
        auto set_photo_clm_score = context.SetDoubleItemAttr("photo_mc_clm_ltr_score");
        if (photo_clm_pred_embedding && photo_cascade_item_id_list) {
          int size1 = photo_clm_pred_embedding->size();
          int size2 = photo_cascade_item_id_list->size();
          if (size2 <= size1) {
            int rank = 1;
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              if (rank <= size2) {
                set_photo_clm_score(result, photo_clm_pred_embedding->at(size2 - rank));
                rank++;
              } else {
                set_photo_clm_score(result, 0.0);
              }
            });
          }
        }
        return true;
  }
  static bool SetPhotoClmMcLtrScoreV2(
    const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
      auto photo_clm_pred_embedding = context.GetDoubleListCommonAttr("photo_mc_clm_ltr_embedding");
      auto photo_cascade_item_id_list = context.GetIntListCommonAttr("photo_cascade_item_id_list");
      auto set_photo_cascade_score = context.SetDoubleItemAttr("photo_mc_clm_cascade_score");
      auto set_photo_click_score = context.SetDoubleItemAttr("photo_mc_clm_click_score");
      auto set_photo_interaction_score = context.SetDoubleItemAttr("photo_mc_clm_interaction_score");
      if (photo_clm_pred_embedding && photo_cascade_item_id_list) {
        int size1 = photo_clm_pred_embedding->size();
        int size2 = photo_cascade_item_id_list->size();
        if (size1 == 9000 && size2 <= 3000) {
          int rank = 1;
          std::for_each(begin, end, [&](const CommonRecoResult &result) {
            if (rank <= size2) {
              set_photo_cascade_score(result, photo_clm_pred_embedding->at(size2 - rank));
              set_photo_click_score(result, photo_clm_pred_embedding->at(3000 + size2 - rank));
              set_photo_interaction_score(result, photo_clm_pred_embedding->at(6000 + size2 - rank));
              rank++;
            } else {
              set_photo_cascade_score(result, 0.0);
              set_photo_click_score(result, 0.0);
              set_photo_interaction_score(result, 0.0);
            }
          });
        } else {
          LOG(ERROR) << "Invalid photo_clm_score, SetPhotoClmMcLtrScoreV2";
        }
      } else {
          LOG(ERROR) << "Invalid photo_clm_pred_embedding, SetPhotoClmMcLtrScoreV2";
      }
      return true;
  }
  static bool CalSlidePhotoMcF1Attr(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
        auto duration_ms_accessor = context.GetIntItemAttr("item_info.duration_ms");
        auto social_moment_value = context.GetIntListItemAttr("item_info.social_moment_value");
        auto cascade_pwtd_accessor = context.GetDoubleItemAttr("cascade_pwtd");
        auto set_cascade_wtd_score = context.SetDoubleItemAttr("cascade_wtd_score");
        auto set_social_type_size = context.SetIntItemAttr("social_type_size");
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          double duration_ms = duration_ms_accessor(result).value_or(0);
          double mc_pwtd = cascade_pwtd_accessor(result).value_or(0.0);
          auto social_type_list = social_moment_value(result);
          double score = mc_pwtd > 0.5 ? mc_pwtd : 0.5;
          int social_type_size = 0;
          score = -log(std::max(1.0 / score - 1.0, 0.000000001));
          // score = -log(1.0 / score - 1.0);
          score = score * pow(std::max(duration_ms, 1.0), score - 0.05);
          set_cascade_wtd_score(result, score);
          if (social_type_list) {
            social_type_size = social_type_list->size();
          }
          set_social_type_size(result, social_type_size);
        });
        return true;
  }
  static bool CalcNearMultiUserEmbedding(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
        auto albert_rr_user_embedding = context.GetDoubleListCommonAttr("albert_rr_user_embedding");
        auto region_remit_provinces_str = context.GetStringCommonAttr("region_remit_provinces_str");
        int user_province_index = context.GetIntCommonAttr("user_province_index").value_or(0);
        // temp_embedding
        std::vector<int> index_list;
        index_list.push_back(user_province_index);
        if (region_remit_provinces_str && !region_remit_provinces_str->empty()) {
          std::vector<absl::string_view> indexs = absl::StrSplit(std::string(*region_remit_provinces_str),
                            ",", absl::SkipWhitespace());
          for (auto idx_str : indexs) {
            int value = 0;
            if (absl::SimpleAtoi(idx_str, &value)) {
              index_list.push_back(value);
            }
          }
        }
        std::vector<double> temp_embedding(32, 0.0);
        std::vector<double> temp_result_embedding(128, 0.0);
        std::vector<double> near_rr_user_embedding;
        for (int index : index_list) {
          if (index > 0 && index < 32) {
            temp_embedding[index] = 1.0;
          }
        }
        if (albert_rr_user_embedding) {
          // normalize
          double vector_sum = 0.0;
          for (auto value : *albert_rr_user_embedding) {
            vector_sum += value * value;
          }
          double vector_std = std::sqrt(vector_sum);
          if (vector_std <= 0.0) {
            vector_std = 0.000001;
          }
          for (auto value : *albert_rr_user_embedding) {
            double vector_i = value / vector_std;
            near_rr_user_embedding.push_back(vector_i);
          }
        } else {
          near_rr_user_embedding.swap(temp_result_embedding);
        }
        for (auto value : temp_embedding) {
          near_rr_user_embedding.push_back(value);
        }
        context.SetDoubleListCommonAttr("near_rr_user_embedding", std::move(near_rr_user_embedding));
        return true;
  }
  static bool CalMainPhotoMcDurationInverse(const CommonRecoLightFunctionContext &context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
        auto duration_ms_accessor = context.GetIntItemAttr("item_info.duration_ms");
        auto duration_bucket_split_str = context.GetStringCommonAttr("duration_bucket_split_str");
        auto set_wtd_bucket = context.SetIntItemAttr("wtd_bucket");
        std::string duration_bucket_split = std::string(*duration_bucket_split_str);
        std::vector<std::string> tmp_duration_bucket_vec = absl::StrSplit(duration_bucket_split, ",");
        std::vector<int> duration_bucket_vec;
        std::for_each(tmp_duration_bucket_vec.begin(), tmp_duration_bucket_vec.end(),
                      [&](const std::string &str_duration) {
                        int int_duration;
                        if (absl::SimpleAtoi(str_duration, &int_duration)) {
                          duration_bucket_vec.push_back(int_duration);
                        }
                      });
        // calc wtd inverse duration
        auto wtd_table_list = context.GetStringListCommonAttr("wtd_table");
        std::vector<std::vector<double>> watch_time_list;
        auto cascade_pwtd_accessor = context.GetDoubleItemAttr("cascade_pwtd");
        auto set_wtd_duration = context.SetDoubleItemAttr("wtd_duration");
        int64 duration_inverse_smooth_is_on =
            context.GetIntCommonAttr("calc_duration_inverse_with_add_one_smooth_is_on").value_or(0);
        if (wtd_table_list && wtd_table_list->size() > 0) {
          for (auto duration_bucket : *wtd_table_list) {
            std::string str_duration_bucket = std::string(duration_bucket);
            std::vector<std::string> tmp_watch_time_list;
            std::vector<double> double_tmp_watch_time_list;
            base::SplitStringWithOptions(str_duration_bucket, ",", true, true, &tmp_watch_time_list);
            std::for_each(tmp_watch_time_list.begin(), tmp_watch_time_list.end(),
                          [&](const std::string &str_time) {
                            double d_time;
                            bool is_sucess = absl::SimpleAtod(str_time, &d_time);
                            if (is_sucess) {
                              double_tmp_watch_time_list.push_back(d_time);
                            }
                          });
            watch_time_list.push_back(double_tmp_watch_time_list);
          }
        } else {
          return true;
        }
        int watch_time_point_num = -1;
        if (watch_time_list.size() > 0) {
          watch_time_point_num = watch_time_list[0].size();
        } else {
          return true;
        }
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          // calc wtd bucket
          int duration_ms = duration_ms_accessor(result).value_or(0);
          int bucket_num = duration_bucket_vec.size();
          int wtd_bucket = bucket_num + 1;
          if (duration_ms != 0) {
            int index = 1;
            for (; index < bucket_num; index++) {
              if (duration_ms <= duration_bucket_vec[index]) {
                break;
              }
            }
            wtd_bucket = index;
          }
          set_wtd_bucket(result, wtd_bucket);
          double cascade_pwtd = cascade_pwtd_accessor(result).value_or(0.0);
          // 反 sigmoid 解出 wtd_logit
          cascade_pwtd = cascade_pwtd > 0.5 ? cascade_pwtd : 0.5;
          cascade_pwtd = -log(std::max(1.0 / cascade_pwtd - 1.0, 0.000000001));
          double watch_duration = -1;
          int wtd_val_base = static_cast<int>(cascade_pwtd / (1.0 / watch_time_point_num)) + 1;
          double wtd_val_residual = fmod(cascade_pwtd, 1.0 / watch_time_point_num);
          auto &cur_bucket = watch_time_list[wtd_bucket - 1];
          if (duration_inverse_smooth_is_on == 1) {
            if (wtd_val_base < watch_time_point_num) {  // 有更高的上界
              watch_duration = cur_bucket[wtd_val_base - 1] +
                               wtd_val_residual * (cur_bucket[wtd_val_base] - cur_bucket[wtd_val_base - 1]);
            } else {  // 边界条件处理
              watch_duration =
                  cur_bucket[wtd_val_base - 2] +
                  wtd_val_residual * (cur_bucket[wtd_val_base - 1] - cur_bucket[wtd_val_base - 2]);
            }
          } else {
            if (wtd_val_base == 1) {  // 边界条件处理
              watch_duration = 0 + wtd_val_residual * (cur_bucket[wtd_val_base - 1] - 0);
            } else {
              watch_duration =
                  cur_bucket[wtd_val_base - 2] +
                  wtd_val_residual * (cur_bucket[wtd_val_base - 1] - cur_bucket[wtd_val_base - 2]);
            }
          }
          if (cascade_pwtd > 0.98) {
            watch_duration = cur_bucket.back();
          }
          set_wtd_duration(result, watch_duration);
        });
        return true;
  }
  static bool CalMainPhotoFrDurationInverse(const CommonRecoLightFunctionContext &context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
        auto duration_ms_accessor = context.GetIntItemAttr("item_info.duration_ms");
        auto duration_bucket_split_str = context.GetStringCommonAttr("duration_bucket_split_str");
        auto set_wtd_bucket = context.SetIntItemAttr("fr_wtd_bucket");
        std::string duration_bucket_split = "";
        if (duration_bucket_split_str.has_value()) {
          duration_bucket_split = std::string(*duration_bucket_split_str);
        }
        std::vector<std::string> tmp_duration_bucket_vec = absl::StrSplit(duration_bucket_split, ",");
        std::vector<int> duration_bucket_vec;
        std::for_each(tmp_duration_bucket_vec.begin(), tmp_duration_bucket_vec.end(),
                      [&](const std::string &str_duration) {
                        int int_duration;
                        if (absl::SimpleAtoi(str_duration, &int_duration)) {
                          duration_bucket_vec.push_back(int_duration);
                        }
                      });
        // calc wtd inverse duration
        auto wtd_table_list = context.GetStringListCommonAttr("wtd_table");
        std::vector<std::vector<double>> watch_time_list;
        auto fr_pwtd_accessor = context.GetDoubleItemAttr("fr_wtd");
        auto set_wtd_duration = context.SetDoubleItemAttr("fr_wtd_reverse_dur");
        int64 duration_inverse_smooth_is_on =
            context.GetIntCommonAttr("calc_duration_inverse_with_add_one_smooth_is_on").value_or(0);
        if (wtd_table_list && wtd_table_list->size() > 0) {
          for (auto duration_bucket : *wtd_table_list) {
            std::string str_duration_bucket = std::string(duration_bucket);
            std::vector<std::string> tmp_watch_time_list;
            std::vector<double> double_tmp_watch_time_list;
            base::SplitStringWithOptions(str_duration_bucket, ",", true, true, &tmp_watch_time_list);
            std::for_each(tmp_watch_time_list.begin(), tmp_watch_time_list.end(),
                          [&](const std::string &str_time) {
                            double d_time;
                            bool is_sucess = absl::SimpleAtod(str_time, &d_time);
                            if (is_sucess) {
                              double_tmp_watch_time_list.push_back(d_time);
                            }
                          });
            watch_time_list.push_back(double_tmp_watch_time_list);
          }
        } else {
          return true;
        }
        int watch_time_point_num = -1;
        if (watch_time_list.size() > 0) {
          watch_time_point_num = watch_time_list[0].size();
        } else {
          return true;
        }
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          // calc wtd bucket
          int64 duration_ms = duration_ms_accessor(result).value_or(0);
          int bucket_num = duration_bucket_vec.size();
          int wtd_bucket = bucket_num + 1;
          if (duration_ms != 0) {
            int index = 1;
            for (; index < bucket_num; index++) {
              if (duration_ms <= duration_bucket_vec[index]) {
                break;
              }
            }
            wtd_bucket = index;
          }
          set_wtd_bucket(result, wtd_bucket);
          double cascade_pwtd = fr_pwtd_accessor(result).value_or(0.0);
          double watch_duration = -1;
          int wtd_val_base = static_cast<int>(cascade_pwtd / (1.0 / watch_time_point_num)) + 1;
          double wtd_val_residual = fmod(cascade_pwtd, 1.0 / watch_time_point_num);
          auto &cur_bucket = watch_time_list[wtd_bucket - 1];
          if (duration_inverse_smooth_is_on == 1) {
            if (wtd_val_base < watch_time_point_num) {  // 有更高的上界
              watch_duration = cur_bucket[wtd_val_base - 1] +
                               wtd_val_residual * (cur_bucket[wtd_val_base] - cur_bucket[wtd_val_base - 1]);
            } else {  // 边界条件处理
              watch_duration =
                  cur_bucket[wtd_val_base - 2] +
                  wtd_val_residual * (cur_bucket[wtd_val_base - 1] - cur_bucket[wtd_val_base - 2]);
            }
          } else {
            if (wtd_val_base == 1) {  // 边界条件处理
              watch_duration = 0 + wtd_val_residual * (cur_bucket[wtd_val_base - 1] - 0);
            } else {
              watch_duration =
                  cur_bucket[wtd_val_base - 2] +
                  wtd_val_residual * (cur_bucket[wtd_val_base - 1] - cur_bucket[wtd_val_base - 2]);
            }
          }
          if (cascade_pwtd > 0.98) {
            watch_duration = cur_bucket.back();
          }
          set_wtd_duration(result, watch_duration);
        });
        return true;
  }
  static bool CalLiveFollowTime(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
        auto follow_author_list = context.GetIntListCommonAttr("live_follow_author_list");
        auto follow_time_list = context.GetIntListCommonAttr("live_follow_time_list");
        double follow_day1_coeff = context.GetDoubleCommonAttr("live_cascade_fusion_follow_day1_coeff").\
                value_or(1.0);
        double follow_day7_coeff = context.GetDoubleCommonAttr("live_cascade_fusion_follow_day7_coeff").\
                value_or(1.0);
        double follow_day30_coeff = context.GetDoubleCommonAttr("live_cascade_fusion_follow_day30_coeff").\
                value_or(1.0);
        double follow_day100_coeff = context.GetDoubleCommonAttr("live_cascade_fusion_follow_day100_coeff").\
                value_or(1.0);
        auto author_id_accessor = context.GetIntItemAttr("item_info.author_id");
        int64 current_ts = base::GetTimestamp();
        auto follow_day_score_set = context.SetDoubleItemAttr("follow_day_score");
        std::unordered_map<int64, double> follow_aid_time_map;
        if (follow_author_list && follow_time_list
            && follow_author_list->size() == follow_time_list->size()) {
          for (int i = 0; i < follow_author_list->size(); i++) {
            int follow_day = std::floor((current_ts - follow_time_list->at(i)) / (24 * 60 * 60 * 1000));
            double follow_day_score = 1.0;
            if (follow_day < 1) {
              follow_day_score = follow_day1_coeff;
            } else if (follow_day < 7) {
              follow_day_score = follow_day7_coeff;
            } else if (follow_day < 30) {
              follow_day_score = follow_day30_coeff;
            } else {
              follow_day_score = follow_day100_coeff;
            }
            follow_aid_time_map[follow_author_list->at(i)] = follow_day_score;
          }
        }
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          auto author_id = author_id_accessor(result).value_or(-1);
          if (follow_aid_time_map.find(author_id) != follow_aid_time_map.end()) {
            follow_day_score_set(result, follow_aid_time_map[author_id]);
          } else {
            follow_day_score_set(result, 1.0);
          }
        });
        return true;
  }
  static bool CalLiveFollowTimeOnly(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
        auto follow_author_list = context.GetIntListCommonAttr("live_follow_author_list");
        auto follow_time_list = context.GetIntListCommonAttr("live_follow_time_list");
        auto author_id_accessor = context.GetIntItemAttr("item_info.author_id");
        int64 current_ts = std::floor(base::GetTimestamp() / 1000);
        auto follow_day_desc_set = context.SetIntItemAttr("follow_day_desc");
        std::unordered_map<int64, int> follow_aid_time_map;
        if (follow_author_list && follow_time_list
            && follow_author_list->size() == follow_time_list->size()) {
          for (int i = 0; i < follow_author_list->size(); i++) {
            int follow_day = std::floor((current_ts - follow_time_list->at(i)) / (86400 * 1000));
            int follow_day_desc = 0;
            if (follow_day < 1) {
              follow_day_desc = 1;
            } else if (follow_day < 7) {
              follow_day_desc = 7;
            } else if (follow_day < 30) {
              follow_day_desc = 30;
            } else {
              follow_day_desc = 100;
            }
            follow_aid_time_map[follow_author_list->at(i)] = follow_day_desc;
          }
        }
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          int64 author_id = author_id_accessor(result).value_or(-1);
          if (follow_aid_time_map.find(author_id) != follow_aid_time_map.end()) {
            follow_day_desc_set(result, follow_aid_time_map[author_id]);
          } else {
            follow_day_desc_set(result, 0);
          }
        });
        return true;
  }
  static bool CalMainPhotoMcRank(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
        auto cas_rank_set = context.SetIntItemAttr("cas_rank");
        int rank = 1;
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          cas_rank_set(result, rank);
          rank++;
        });
        return true;
  }

  static bool CalMainPhotoCascadeGoalRank(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
        std::vector<std::pair<double, int64>> cotrain_vec;
        std::vector<std::pair<double, int64>> show_vec;
        std::vector<std::pair<int64, int64>> cotrain_reflect;
        std::vector<std::pair<int64, int64>> show_reflect;
        auto cascade_r_cotrain_accessor = context.GetDoubleItemAttr("cascade_r_cotrain");
        auto cascade_r_show_accessor = context.GetDoubleItemAttr("cascade_r_show");
        auto cotrain_rank_set = context.SetIntItemAttr("cotrain_rank");
        auto show_rank_set = context.SetIntItemAttr("show_rank");
        int64 index = 1;
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          double cas_cotrain = cascade_r_cotrain_accessor(result).value_or(0.0);
          double cas_show = cascade_r_show_accessor(result).value_or(0.0);
          cotrain_vec.push_back(std::make_pair(cas_cotrain, index));
          show_vec.push_back(std::make_pair(cas_show, index));
          index++;
        });
        std::sort(
            cotrain_vec.begin(), cotrain_vec.end(),
            [](const std::pair<double, int64> &a, std::pair<double, int64> &b) { return a.first > b.first; });
        std::sort(
            show_vec.begin(), show_vec.end(),
            [](const std::pair<double, int64> &a, std::pair<double, int64> &b) { return a.first > b.first; });
        for (int64 i = 0; i < cotrain_vec.size(); i++) {
          cotrain_reflect.push_back(std::make_pair(cotrain_vec[i].second, i + 1));
          show_reflect.push_back(std::make_pair(show_vec[i].second, i + 1));
        }
        std::sort(
            cotrain_reflect.begin(), cotrain_reflect.end(),
            [](const std::pair<int64, int64> &a, std::pair<int64, int64> &b) { return a.first < b.first; });
        std::sort(
            show_reflect.begin(), show_reflect.end(),
            [](const std::pair<int64, int64> &a, std::pair<int64, int64> &b) { return a.first < b.first; });
        index = 0;
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          cotrain_rank_set(result, cotrain_reflect[index].second);
          show_rank_set(result, show_reflect[index].second);
          index++;
        });
        return true;
  }

  static bool EnrichPhotoMcContextAttr(const CommonRecoLightFunctionContext &context,
                  RecoResultConstIter begin, RecoResultConstIter end) {
    struct ContextAttrInfo {
        int64 photo_id = -1;
        int64 author_id = -1;
        int64 hetu_level_one = 0;
        int64 duration_ms = 0;
        ContextAttrInfo(int64 pid, int64 aid, int64 tag, int64 dura)
            : photo_id(pid), author_id(aid), hetu_level_one(tag), duration_ms(dura) {}
        ContextAttrInfo() : photo_id(-1), author_id(-1), hetu_level_one(0), duration_ms(0){}
    };
    std::vector<ContextAttrInfo> context_attr_infos;
    auto hetu_level_one_accessor =
              context.GetIntItemAttr("hetu_level_one");
    auto duration_ms_accessor =
              context.GetIntItemAttr("item_info.duration_ms");
    auto author_id_accessor =
              context.GetIntItemAttr("item_info.author_id");
    for (auto result = begin; result != end; ++result) {
      int64 photo_id = result->GetId();
      int64 hetu_level_one = hetu_level_one_accessor(*result).value_or(0);
      int64 author_id = author_id_accessor(*result).value_or(0);
      int64 duration_ms = duration_ms_accessor(*result).value_or(0);
      context_attr_infos.emplace_back(photo_id, author_id, hetu_level_one, duration_ms);
    }
    std::shuffle(context_attr_infos.begin(), context_attr_infos.end(),
         std::default_random_engine(base::GetTimestamp()));
    std::vector<int64> mc_photo_context_pids;
    std::vector<int64> mc_photo_context_aids;
    std::vector<int64> mc_photo_context_tags;
    std::vector<int64> mc_photo_context_duration_ms;
    for (int i = 0; i < context_attr_infos.size() && i < 100; i++) {
      mc_photo_context_pids.push_back(context_attr_infos[i].photo_id);
      mc_photo_context_aids.push_back(context_attr_infos[i].author_id);
      mc_photo_context_tags.push_back(context_attr_infos[i].hetu_level_one);
      mc_photo_context_duration_ms.push_back(context_attr_infos[i].duration_ms);
    }
    context.SetIntListCommonAttr("mc_photo_context_pids", std::move(mc_photo_context_pids));
    context.SetIntListCommonAttr("mc_photo_context_aids", std::move(mc_photo_context_aids));
    context.SetIntListCommonAttr("mc_photo_context_tags", std::move(mc_photo_context_tags));
    context.SetIntListCommonAttr("mc_photo_context_duration_ms", std::move(mc_photo_context_duration_ms));
    return true;
  }
  static bool CalInteractScoreOfAuthor(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);

  static bool CalSlideDistanceFilterAttr(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);

  static bool CalNearbyAttrEnricher(
    const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
      auto dedup_user_follow_author_list = context.GetIntListCommonAttr("dedup_user_follow_author_list");
      std::unordered_set<int64> author_set_;
      if (dedup_user_follow_author_list) {
        for (int64 author_id : *dedup_user_follow_author_list) {
          if (author_id > 0) {
            author_set_.insert(author_id);
          }
        }
      }
      auto get_hetu_level_one_list =
          context.GetIntListItemAttr("item_info.hetu_tag_level_info.hetu_level_one");
      auto author_id_accessor = context.GetIntItemAttr("item_info.author_id");
      auto set_hetu_level_one = context.SetIntItemAttr("pHetuV1LevelOne");
      auto set_is_followed_author = context.SetIntItemAttr("pIsFollowedAuthor");
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        int hetu_level_one = 0;
        auto hetu_level_one_list = get_hetu_level_one_list(result);
        int is_followed_author = 0;
        if (hetu_level_one_list) {
          for (int tag : *hetu_level_one_list) {
            hetu_level_one = tag;
            break;
          }
        }
        uint64 aid = author_id_accessor(result).value_or(0);
        if (author_set_.count(aid) > 0) {
          is_followed_author = 1;
        }
        set_hetu_level_one(result, hetu_level_one);
        set_is_followed_author(result, is_followed_author);
      });
      return true;
    }
  static bool CalcAucOfCotrain(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
      auto cotrain_rank_accessor = context.GetIntItemAttr("cotrain_rank");
      int index = 0;
      int sample_pos_num = 0;
      int sample_neg_num = 0;
      std::vector<int> sample_pos_rank;
      std::vector<int> sample_neg_rank;
      bool flag = false;
      std::random_device rd;                                 // 用于获取种子
      std::mt19937 gen(rd());                                // 使用 Mersenne Twister 算法生成器
      std::uniform_real_distribution<double> dis(0.0, 1.0);  // 生成 0 到 1 之间的随机数
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        double rand_value = dis(gen);
        int cotrain_rank = cotrain_rank_accessor(result).value_or(10000);
        if (cotrain_rank == 10000) {
          flag = true;
        }
        if (index <= 20 && rand_value <= 0.6) {
          sample_pos_num += 1;
          sample_pos_rank.push_back(cotrain_rank);
        } else if (index <= 100 && rand_value <= 0.1) {
          sample_neg_num += 1;
          sample_neg_rank.push_back(cotrain_rank);
        } else if (index <= 200 && rand_value <= 0.3) {
          sample_neg_num += 1;
          sample_neg_rank.push_back(cotrain_rank);
        } else if (index <= 300 && rand_value <= 0.5) {
          sample_neg_num += 1;
          sample_neg_rank.push_back(cotrain_rank);
        } else if (index > 300 && rand_value <= 0.8) {
          sample_neg_num += 1;
          sample_neg_rank.push_back(cotrain_rank);
        }
        index++;
      });
      int rank_correct = 0;
      for (int &pos_rank : sample_pos_rank) {
          for (int &neg_rank : sample_neg_rank) {
            if (pos_rank < neg_rank) {
              rank_correct += 1;
            }
          }
      }
      double cotrain_auc = 0.5;
      if (!flag && sample_pos_num > 0 && sample_neg_num > 0) {
          cotrain_auc = rank_correct / static_cast<double>(sample_pos_num * sample_neg_num);
      }
      if (flag) {
          LOG(ERROR) << "cotrain rank is not exists";
      }
      context.SetIntCommonAttr("uauc_cotrain", static_cast<int>(cotrain_auc * 1000000));
      context.SetIntCommonAttr("sample_cotrain_pos", static_cast<int>(sample_pos_num));
      context.SetIntCommonAttr("sample_cotrain_neg", static_cast<int>(sample_neg_num));
      return true;
    }

    static bool CalcPhotoRandomScore(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
      auto cascade_random_score_set = context.SetDoubleItemAttr("cascade_random_score");
      std::random_device rd;                                 // 用于获取种子
      std::mt19937 gen(rd());                                // 使用 Mersenne Twister 算法生成器
      std::uniform_real_distribution<double> dis(0.0, 1.0);  // 生成 0 到 1 之间的随机数
      std::for_each(begin, end,
                    [&](const CommonRecoResult &result) { cascade_random_score_set(result, dis(gen)); });
      return true;
    }

    static bool CalNearbyRecallRankXtrEnricher(
    const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
      auto recall_pctr_accessor = context.GetDoubleItemAttr("recall_pctr");
      auto item_id_accessor = context.GetIntItemAttr("item_id");
      auto set_rank_pctr = context.SetDoubleItemAttr("recall_rank_pctr");
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        double rank_pctr = 0.0;
        double recall_pctr = recall_pctr_accessor(result).value_or(0.0);
        int64 item_id = item_id_accessor(result).value_or(1000000);
        if (recall_pctr > 0.0) {
          rank_pctr = recall_pctr;
        } else {
          rank_pctr = 1.0 / (1000000.0 + 1.0 / item_id);
        }
        set_rank_pctr(result, rank_pctr);
      });
      return true;
    }

    static std::string CalPushMainTitleText(absl::optional<absl::Span<const int64>> social_moment_value,
                                            const std::unordered_set<int64> &purpose_friends_long) {
      std::vector<int64> social_moment;
      if (social_moment_value && social_moment_value->size() > 0) {
        for (int i = 0; i < social_moment_value->size(); ++i) {
          if (social_moment_value->at(i) >= 3000 && social_moment_value->at(i) < 4000) {  // 跳过拓展人脉
            continue;
          }
          social_moment.push_back(social_moment_value->at(i));
        }
      }
      std::string result;

      std::sort(social_moment.begin(), social_moment.end());  // 从小到大
      if (social_moment.size() > 0) {
        int64 check = social_moment[0];
        for (auto &each : social_moment) {
          if (purpose_friends_long.find(each) != purpose_friends_long.end()) {
            check = each;
            break;
          }
        }
        if (check == 1000) {
          if (purpose_friends_long.size() > 0) {
            result = "想处对象";
          } else {
            result = "想交个朋友";
          }
        } else if (check / 100 == 20) {
          result = "想找搭子";
        } else if (check == 4000) {
          result = "想互关互赞";
        }
      } else {
        result = "想交个朋友";
      }
      return result;
    }

    static bool CalPushText(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                            RecoResultConstIter end) {
      auto nearby_social_push_id = context.GetIntCommonAttr("nearby_social_push_id").value_or(0);
      std::string purpose_friend = std::string(context.GetStringCommonAttr("purpose_friend").value_or(""));
      auto item_id_accessor = context.GetIntItemAttr("item_id");
      auto social_moment_value_accessor = context.GetIntListItemAttr("item_info.social_moment_value");
      auto nearby_like_accessor = context.GetIntItemAttr("nearby_like");
      auto author_id_accessor = context.GetIntItemAttr("item_info.author_id");
      auto set_push_id = context.SetIntItemAttr("push_id");
      auto set_main_title_text = context.SetStringItemAttr("main_title_text");
      auto set_subtitle_text = context.SetStringItemAttr("subtitle_text");

      std::vector<std::string> purpose_friends;
      if (purpose_friend != "") {
        base::SplitStringWithOptions(purpose_friend, ",", true, true, &purpose_friends);
      }
      std::unordered_set<int64> purpose_friends_long;
      int64 value = 0;
      for (auto &each : purpose_friends) {
        if (base::StringToInt64(each, &value)) {
          purpose_friends_long.insert(value);
        }
      }
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto author_id = author_id_accessor(result).value_or(0);
        if (nearby_social_push_id == 23597831) {  // 文案 a
          std::string main_title_text = "";
          std::string subtitle_text = "ta离你很近，去打个招呼吧～";
          auto social_moment_value = social_moment_value_accessor(result);
          main_title_text += CalPushMainTitleText(social_moment_value, purpose_friends_long);

          set_push_id(result, nearby_social_push_id);
          set_main_title_text(result, main_title_text);
          set_subtitle_text(result, subtitle_text);
        } else if (nearby_social_push_id == 23598050) {  // 文案 b
          std::string main_title_text = "";
          std::string subtitle_text = "";
          auto social_moment_value = social_moment_value_accessor(result);
          main_title_text += CalPushMainTitleText(social_moment_value, purpose_friends_long);
          auto nearby_like = nearby_like_accessor(result).value_or(0);
          if (nearby_like >= 100) {
            subtitle_text += "超100人赞过，";
          } else if (nearby_like >= 1) {
            subtitle_text += "有" + base::IntToString(static_cast<int>(nearby_like)) + "人赞过，";
          }
          subtitle_text += "去瞅瞅~";
          set_push_id(result, nearby_social_push_id);
          set_main_title_text(result, main_title_text);
          set_subtitle_text(result, subtitle_text);
        }
      });
      return true;
    }
    static bool CalItemReasonListToString(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
      auto item_reason_list_accessor = context.GetIntListItemAttr("item_reason_list");
      auto set_item_reason_list_str = context.SetStringItemAttr("item_reason_list_str");
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        std::string str_reason = "";
        auto item_reason_list = item_reason_list_accessor(result);
        if (item_reason_list && item_reason_list->size() > 0) {
          for (int i = 0; i < item_reason_list->size(); i++) {
            str_reason.append("_" + std::to_string(item_reason_list->at(i)));
          }
          str_reason.append("_");
        }
        set_item_reason_list_str(result, str_reason);
      });
      return true;
    }

    static bool CalcIsRelationPhoto(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
      auto pReason_accessor = context.GetIntItemAttr("pReason");
      auto set_is_publish_relation = context.SetIntItemAttr("is_publish_relation_photo");
      auto set_is_relation = context.SetIntItemAttr("is_relation_photo");
      static const std::unordered_set<int> relation_item_set = {13,  22,  120, 17,  24,  39,  25,  110, 125,
                                                                145, 157, 148, 314, 312, 313, 123, 124};
      static const std::unordered_set<int> publish_relation_set = {13, 24, 39, 25, 110, 314, 312, 313, 123};

      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        int is_relation = 0;
        int is_publish_relation = 0;
        int reason = pReason_accessor(result).value_or(0);
        if (reason) {
          if (relation_item_set.count(reason)) {
            is_relation = 1;
            if (publish_relation_set.count(reason)) {
              is_publish_relation = 1;
            }
          } else if (reason >= 1000 && reason <= 2000) {
            is_relation = 1;
            if (reason < 1100) {
              is_publish_relation = 1;
            }
          }
        } else {
          is_relation = 1;
        }
        set_is_publish_relation(result, is_publish_relation);
        set_is_relation(result, is_relation);
      });
      return true;
    }
    static bool CalcHighReportFilter(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
      auto hot_click_accessor = context.GetIntItemAttr("hot_click");
      auto nearby_click_accessor = context.GetIntItemAttr("nearby_click");
      auto nearby_report_accessor = context.GetIntItemAttr("nearby_report");
      auto report_count_accessor = context.GetIntItemAttr("report_count");
      auto hot_report_accessor = context.GetIntItemAttr("hot_report");
      auto author_id_accessor = context.GetIntItemAttr("item_info.author_id");
      auto publish_ts_accessor = context.GetIntItemAttr("item_info.timestamp");
      auto photo_audit_report_value_accessor = context.GetIntItemAttr("item_info.photo_audit_report_value");
      int64 skip_nearby_filter_day_avg = context.GetIntCommonAttr("skip_nearby_filter_day_avg").value_or(0);
      double nearby_guanfan_report_num_thres =
          context.GetDoubleCommonAttr("nearby_guanfan_report_num_thres").value_or(4.0);
      double nearby_guanfan_report_rate_thres =
          context.GetDoubleCommonAttr("nearby_guanfan_report_rate_thres").value_or(4.0);
      double nearby_no_guanfan_report_num_thres =
          context.GetDoubleCommonAttr("nearby_no_guanfan_report_num_thres").value_or(4.0);
      double nearby_no_guanfan_report_rate_thres =
          context.GetDoubleCommonAttr("nearby_no_guanfan_report_rate_thres").value_or(4.0);

      auto set_high_report_filter = context.SetIntItemAttr("high_report_filter");
      std::unordered_set<int64> report_author_ids;
      for (int i = 0; i < 10; ++i) {
        std::string bad_author_str =
            std::string(context.GetStringCommonAttr("bad_author_value_" + base::IntToString(i)).value_or(""));
        std::vector<std::string> temp;
        base::SplitStringWithOptions(bad_author_str, ",", true, true, &temp);
        int64 temp_id;
        for (auto &id : temp) {
          if (base::StringToInt64(id, &temp_id)) {
            report_author_ids.insert(temp_id);
          }
        }
      }
      int64 current_ts = base::GetTimestamp();
      std::for_each(begin, end, [=](const CommonRecoResult &result) {
        int hot_click = hot_click_accessor(result).value_or(0);
        int nearby_click = nearby_click_accessor(result).value_or(0);
        int hot_report = hot_report_accessor(result).value_or(0);
        int nearby_report = nearby_report_accessor(result).value_or(0);
        int photo_audit_report_value = photo_audit_report_value_accessor(result).value_or(0);
        int64 author_id = author_id_accessor(result).value_or(0);
        int64 publish_ts = publish_ts_accessor(result).value_or(0);
        int days = static_cast<int>((current_ts - publish_ts) / base::Time::kMicrosecondsPerDay) + 1;
        int all_report = hot_report + nearby_report;
        int all_click = hot_click + nearby_click;
        double all_report_rate = 0;
        if (all_report < all_click && all_click > 0) {
          all_report_rate = all_report / (all_click + 0.0001);
        }
        int high_report_filter = 0;
        if (skip_nearby_filter_day_avg) {
          days = 1;
        }
        if (nearby_report > nearby_no_guanfan_report_num_thres && photo_audit_report_value == 0) {
          high_report_filter = 1;
        }
        // if (report_author_ids.find(author_id) != report_author_ids.end()) {
        //   if (all_report >= nearby_guanfan_report_num_thres * days &&
        //       all_report_rate >= nearby_guanfan_report_rate_thres) {
        //     high_report_filter = 1;
        //   }
        // } else {
        //   if (all_report >= nearby_no_guanfan_report_num_thres * days &&
        //       all_report_rate >= nearby_no_guanfan_report_rate_thres) {
        //     high_report_filter = 1;
        //   }
        // }
        set_high_report_filter(result, high_report_filter);
      });
      return true;
    }

    static bool CalcPoiHighReportFilter(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
      auto nearby_report_accessor = context.GetIntItemAttr("nearby_report");
      auto photo_audit_report_value_accessor = context.GetIntItemAttr("item_info.photo_audit_report_value");
      double nearby_poi_high_report_num_thres =
          context.GetDoubleCommonAttr("nearby_poi_high_report_num_thres").value_or(4.0);
      auto set_high_report_filter = context.SetIntItemAttr("high_report_filter");
      std::for_each(begin, end, [=](const CommonRecoResult &result) {
        int64 nearby_report = nearby_report_accessor(result).value_or(0);
        int64 photo_audit_report_value = photo_audit_report_value_accessor(result).value_or(0);
        int high_report_filter = 0;
        if (nearby_report > nearby_poi_high_report_num_thres && photo_audit_report_value == 0) {
          high_report_filter = 1;
        }
        set_high_report_filter(result, high_report_filter);
      });
      return true;
    }

    static bool CalcHighPublishLabel(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
      std::unordered_set<int64> high_publish_100_aids;
      std::unordered_set<int64> high_publish_50_aids;
      std::vector<int> threshold = {50, 100};
      for (auto j : threshold) {
        for (int i = 0; i < 10; ++i) {
          std::string bad_author_str = std::string(
              context.GetStringCommonAttr("wphp_" + base::IntToString(j) + "_" + base::IntToString(i))
                  .value_or(""));
          std::vector<std::string> temp;
          base::SplitStringWithOptions(bad_author_str, ",", true, true, &temp);
          int64 temp_id;
          for (auto &id : temp) {
            if (base::StringToInt64(id, &temp_id)) {
              if (j == 50) {
                high_publish_50_aids.insert(temp_id);
              } else if (j == 100) {
                high_publish_100_aids.insert(temp_id);
              }
            }
          }
        }
      }
      auto author_id_accessor = context.GetIntItemAttr("item_info.author_id");
      auto set_high_publish_100_label = context.SetIntItemAttr("high_publish_100_label");
      auto set_high_publish_50_label = context.SetIntItemAttr("high_publish_50_label");

      std::for_each(begin, end, [=](const CommonRecoResult &result) {
        int64 author_id = author_id_accessor(result).value_or(0);
        int high_publish_100_label = 0;
        int high_publish_50_label = 0;
        if (high_publish_100_aids.count(author_id)) {
          high_publish_100_label = 1;
        }
        if (high_publish_50_aids.count(author_id)) {
          high_publish_50_label = 1;
        }
        set_high_publish_100_label(result, high_publish_100_label);
        set_high_publish_50_label(result, high_publish_50_label);
      });
      return true;
    }

    static bool CalcHighForwardAttr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
      auto hetu_level_one_accessor =
          context.GetIntListItemAttr("item_info.hetu_tag_level_info_v3.hetu_level_one");
      auto hetu_level_two_accessor =
          context.GetIntListItemAttr("item_info.hetu_tag_level_info_v3.hetu_level_two");
      auto user_forward_hetu_list =
          std::string(context.GetStringCommonAttr("user_forward_hetu_list").value_or(""));
      auto set_hit_high_forward_hetu_one = context.SetIntItemAttr("hit_high_forward_hetu_one");
      auto set_hit_high_forward_hetu_two = context.SetIntItemAttr("hit_high_forward_hetu_two");
      if (user_forward_hetu_list == "") {
        context.SetIntCommonAttr("is_high_forward_user", 0);
      } else {
        context.SetIntCommonAttr("is_high_forward_user", 1);
      }
      std::unordered_set<int64> high_forward_heto_ones;
      std::unordered_set<int64> high_forward_heto_twos;
      if (user_forward_hetu_list != "_") {
        std::vector<std::string> temp;
        base::SplitStringWithOptions(user_forward_hetu_list, "_", true, true, &temp);
        if (temp.size() == 2) {
          std::vector<std::string> tt;
          base::SplitStringWithOptions(temp[0], ",", true, true, &tt);
          for (auto &x : tt) {
            int64 t_int = 0;
            if (base::StringToInt64(x, &t_int)) {
              high_forward_heto_ones.insert(t_int);
            }
          }
          tt.clear();
          base::SplitStringWithOptions(temp[1], ",", true, true, &tt);
          for (auto &x : tt) {
            int64 t_int = 0;
            if (base::StringToInt64(x, &t_int)) {
              high_forward_heto_twos.insert(t_int);
            }
          }
        }
      }

      std::for_each(begin, end, [=](const CommonRecoResult &result) {
        auto hetu_level_one = hetu_level_one_accessor(result);
        auto hetu_level_two = hetu_level_two_accessor(result);
        int hit_high_forward_hetu_one = 0;
        int hit_high_forward_hetu_two = 0;

        if (hetu_level_one && hetu_level_one->size() > 0) {
          for (auto x : *hetu_level_one) {
            if (high_forward_heto_ones.count(x) > 0) {
              hit_high_forward_hetu_one = 1;
              break;
            }
          }
        }
        if (hetu_level_two && hetu_level_two->size() > 0) {
          for (auto x : *hetu_level_two) {
            if (high_forward_heto_twos.count(x) > 0 && x != -124) {
              hit_high_forward_hetu_two = 1;
              break;
            }
          }
        }
        set_hit_high_forward_hetu_one(result, hit_high_forward_hetu_one);
        set_hit_high_forward_hetu_two(result, hit_high_forward_hetu_two);
      });
      return true;
    }

  static bool CalMmuEmbeddingScore(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);

  static bool CalcCancelSimilar(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
      std::string user_click_effective_view_concat_click_cnt =
          std::string(context.GetStringCommonAttr("user_click_effective_view_concat_click_cnt").value_or(""));
      auto uInnerSourceHetuTag = context.GetIntCommonAttr("uInnerSourceHetuTag").value_or(0);
      auto uProductType = context.GetIntCommonAttr("uProductType").value_or(0);
      double cancel_threshold = context.GetDoubleCommonAttr("nearby_cancel_similar_threshold").value_or(0.5);

      // 默认值设置为 1
      int cancel_slide_same_tag_filter = 1;

      int flag = 0;
      if (!user_click_effective_view_concat_click_cnt.empty() && uInnerSourceHetuTag > 0) {
        flag = 1;
        std::vector<std::string> temp;
        base::SplitStringWithOptions(user_click_effective_view_concat_click_cnt, ",", true, true, &temp);

        if (temp.size() == 2) {
          int64 effective_cnt = 0;
          int64 click_cnt = 0;
          base::StringToInt64(temp[0], &effective_cnt);
          base::StringToInt64(temp[1], &click_cnt);

          int64 alpha = 1 + effective_cnt;
          int64 beta = 1 + click_cnt - effective_cnt;
          std::mt19937 random_engine(base::GetTimestamp());
          boost::random::beta_distribution<> beta_dist(alpha, beta);
          double effective_view_rate = beta_dist(random_engine);
          base::perfutil::PerfUtilWrapper::IntervalLogStash(
              effective_view_rate * 1000000, "reco.nearby.NearbyLightFunctionSet", "reco_nearby",
              "CalcCancelSimilar", "effective_view_rate");

          if (effective_view_rate > cancel_threshold) {
            cancel_slide_same_tag_filter = 0;
          }
        }
      }
      if (uProductType == 1) {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(cancel_slide_same_tag_filter * 1000000,
                                                          "reco.nearby.NearbyLightFunctionSet", "reco_nearby",
                                                          "CalcCancelSimilar", "nb_cancel_similar");
      } else {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(cancel_slide_same_tag_filter * 1000000,
                                                          "reco.nearby.NearbyLightFunctionSet", "reco_nearby",
                                                          "CalcCancelSimilar", "main_cancel_similar");
      }
      base::perfutil::PerfUtilWrapper::IntervalLogStash(flag * 1000000, "reco.nearby.NearbyLightFunctionSet",
                                                        "reco_nearby", "CalcCancelSimilar", "read_sucess");
      context.SetIntCommonAttr("cancel_slide_same_tag_filter", cancel_slide_same_tag_filter);
      return true;
  }
  static bool CalcCreadTimeDiff(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
      auto duration_ms_accessor = context.GetIntItemAttr("pVideoDurationMs");
      auto cread_table_list = context.GetStringListCommonAttr("cread_time_table");
      auto set_td1 = context.SetDoubleListItemAttr("or_time_diff_1");
      auto set_td2 = context.SetDoubleListItemAttr("or_time_diff_2");
      auto set_td3 = context.SetDoubleListItemAttr("or_time_diff_3");
      auto set_td4 = context.SetDoubleListItemAttr("or_time_diff_4");
      auto set_td5 = context.SetDoubleListItemAttr("or_time_diff_5");
      auto set_td6 = context.SetDoubleListItemAttr("or_time_diff_6");
      auto set_td7 = context.SetDoubleListItemAttr("or_time_diff_7");
      auto set_td8 = context.SetDoubleListItemAttr("or_time_diff_8");
      auto set_td9 = context.SetDoubleListItemAttr("or_time_diff_9");
      auto set_td10 = context.SetDoubleListItemAttr("or_time_diff_10");
      auto set_cread_bucket = context.SetIntItemAttr("cread_bucket");

      std::vector<std::vector<double>> watch_time_list;
      if (cread_table_list && cread_table_list->size() > 0) {
        for (auto duration_bucket : *cread_table_list) {
          std::string str_duration_bucket = std::string(duration_bucket);
          std::vector<std::string> tmp_watch_time_list;
          std::vector<double> d_tmp_watch_time_list;
          int64 index = 0;
          base::SplitStringWithOptions(str_duration_bucket, ",", true, true, &tmp_watch_time_list);
          std::for_each(tmp_watch_time_list.begin(), tmp_watch_time_list.end(),
                        [&](const std::string &str_time) {
                          double d_time;
                          bool is_sucess = absl::SimpleAtod(str_time, &d_time);
                          if (is_sucess && index % 2 == 1) {
                            d_tmp_watch_time_list.push_back(d_time);
                          }
                          index++;
                        });
          watch_time_list.push_back(d_tmp_watch_time_list);
        }
      } else {
        return true;
      }
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        int64 duration = duration_ms_accessor(result).value_or(0);
        int bucket = 0;
        if (duration == 0) {
          bucket = 0;
        } else if (duration <= 11423) {
          bucket = 1;
        } else if (duration <= 17162) {
          bucket = 2;
        } else if (duration <= 26969) {
          bucket = 3;
        } else if (duration <= 37532) {
          bucket = 4;
        } else if (duration <= 50724) {
          bucket = 5;
        } else if (duration <= 62512) {
          bucket = 6;
        } else if (duration <= 94662) {
          bucket = 7;
        } else if (duration <= 142909) {
          bucket = 8;
        } else if (duration <= 242136) {
          bucket = 9;
        } else if (duration <= 99999999) {
          bucket = 10;
        }
        if (watch_time_list.size() == 11 && bucket < watch_time_list.size()) {
          std::vector<std::vector<double>> time_diff_list;
          std::vector<double> bucket_time_list = watch_time_list.at(bucket);
          if (bucket_time_list.size() > 0) {
            time_diff_list.push_back({bucket_time_list.at(0) / 3000.0});
          }
          for (int i = 1; i < bucket_time_list.size(); i++) {
            time_diff_list.push_back({(bucket_time_list.at(i) - bucket_time_list.at(i - 1)) / 3000.0});
          }
          if (time_diff_list.size() == 10) {
            set_td1(result, time_diff_list.at(0));
            set_td2(result, time_diff_list.at(1));
            set_td3(result, time_diff_list.at(2));
            set_td4(result, time_diff_list.at(3));
            set_td5(result, time_diff_list.at(4));
            set_td6(result, time_diff_list.at(5));
            set_td7(result, time_diff_list.at(6));
            set_td8(result, time_diff_list.at(7));
            set_td9(result, time_diff_list.at(8));
            set_td10(result, time_diff_list.at(9));
            set_cread_bucket(result, bucket);
          }
        }
      });
      return true;
  }

  static bool CalcLiveIdReportRate(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
      auto live_id_report_cnt_set = context.SetIntItemAttr("live_id_report_cnt");
      auto live_id_report_rate_set = context.SetDoubleItemAttr("live_id_report_rate");
      auto nearby_realshow_accessor = context.GetIntItemAttr("nearby_realshow");
      auto hot_realshow_accessor = context.GetIntItemAttr("hot_realshow");
      auto nearby_report_accessor = context.GetIntItemAttr("nearby_report");
      auto hot_report_accessor = context.GetIntItemAttr("hot_report");
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        int64 nearby_realshow = nearby_realshow_accessor(result).value_or(0);
        int64 hot_realshow = hot_realshow_accessor(result).value_or(0);
        int64 nearby_report = nearby_report_accessor(result).value_or(0);
        int64 hot_report = hot_report_accessor(result).value_or(0);
        int64 total_report = nearby_report + hot_report;
        double report_rate = total_report / (nearby_realshow + hot_realshow + 1.0);
        live_id_report_cnt_set(result, total_report);
        live_id_report_rate_set(result, report_rate);
      });
      return true;
  }

  static bool CalcIsPosAuthor(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                              RecoResultConstIter end) {
      auto author_id_accessor = context.GetIntItemAttr("item_info.author_id");
      auto pos_author_list =
          context.GetIntListCommonAttr("positive_aid_list_recent_7d").value_or(absl::Span<const int64>());
      auto inter_author_list =
          context.GetIntListCommonAttr("interaction_aid_list_recent_7d").value_or(absl::Span<const int64>());
      auto is_pos_author_set = context.SetIntItemAttr("is_pos_author");
      auto is_interact_author_set = context.SetIntItemAttr("is_interact_author");
      if (!pos_author_list.empty()) {
        folly::F14FastSet<int64> pos_author_set(pos_author_list.begin(), pos_author_list.end());
        folly::F14FastSet<int64> inter_author_set;
        if (!inter_author_list.empty()) {
          inter_author_set = folly::F14FastSet<int64>(inter_author_list.begin(), inter_author_list.end());
        }
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          int64 author_id = author_id_accessor(result).value_or(0);
          uint32 is_pos_author = 0;
          uint32 is_interact_author = 0;
          if (pos_author_set.find(author_id) != pos_author_set.end()) {
            is_pos_author = 1;
          }
          if (inter_author_set.find(author_id) != inter_author_set.end()) {
            is_interact_author = 1;
          }
          is_pos_author_set(result, is_pos_author);
          is_interact_author_set(result, is_interact_author);
        });
      }
      return true;
  }
  static bool EnrichLiveAuthorPlayTime(const CommonRecoLightFunctionContext &context,
                    RecoResultConstIter begin, RecoResultConstIter end) {
      auto author_id_accessor = context.GetIntItemAttr("item_info.author_id");
      auto live_colossus_author_id =
          context.GetIntListCommonAttr("live_colossus_author_id");
      auto live_colossus_play_time =
          context.GetIntListCommonAttr("live_colossus_play_time");
      auto live_colossus_timestamp =
          context.GetIntListCommonAttr("live_colossus_timestamp");
      if (!live_colossus_author_id || !live_colossus_play_time || !live_colossus_timestamp) {
        return true;
      }
      if (live_colossus_author_id->size() == 0) {
        return true;
      }
      bool size_match = live_colossus_author_id->size() == live_colossus_play_time->size()
        && live_colossus_author_id->size() == live_colossus_timestamp->size();
      if (!size_match) {
        return true;
      }
      uint64 expired_ms = base::GetTimestamp() - base::Time::kMicrosecondsPerDay * 90;
      std::unordered_map<int64, int64> author_play_live_time_map;
      for (int i = live_colossus_author_id->size() - 1; i >= 0; i--) {
        if (live_colossus_timestamp->at(i) * base::Time::kMicrosecondsPerSecond < expired_ms) {
          break;
        }
        int64 author_id = live_colossus_author_id->at(i);
        if (author_play_live_time_map.count(author_id) > 0) {
          author_play_live_time_map[author_id] += live_colossus_play_time->at(i);
        } else {
          author_play_live_time_map[author_id] = live_colossus_play_time->at(i);
        }
      }
      // 处理每个 item
      auto live_author_play_time_set = context.SetIntItemAttr("live_author_play_time");
      std::for_each(begin, end, [=](const CommonRecoResult &result) {
        int64 author_id = author_id_accessor(result).value_or(0);
        if (author_play_live_time_map.count(author_id)) {
          live_author_play_time_set(result, author_play_live_time_map.at(author_id));
        }
      });
      return true;
  }
  static bool CalcHetuDebiasReflect(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
      int64 start_ts = base::GetTimestamp();
      auto cluster_avg_emp_ctr_set = context.SetDoubleItemAttr("cluster_avg_emp_ctr");
      auto cluster_avg_emp_ltr_set = context.SetDoubleItemAttr("cluster_avg_emp_ltr");
      auto cluster_avg_emp_wtr_set = context.SetDoubleItemAttr("cluster_avg_emp_wtr");
      auto cluster_avg_emp_cmtr_set = context.SetDoubleItemAttr("cluster_avg_emp_cmtr");
      auto cluster_avg_emp_lvtr_set = context.SetDoubleItemAttr("cluster_avg_emp_lvtr");
      auto cluster_avg_emp_htr_set = context.SetDoubleItemAttr("cluster_avg_emp_htr");
      auto cluster_avg_fr_ctr_set = context.SetDoubleItemAttr("cluster_avg_fr_ctr");
      auto cluster_avg_fr_ltr_set = context.SetDoubleItemAttr("cluster_avg_fr_ltr");
      auto cluster_avg_fr_wtr_set = context.SetDoubleItemAttr("cluster_avg_fr_wtr");
      auto cluster_avg_fr_cmtr_set = context.SetDoubleItemAttr("cluster_avg_fr_cmtr");
      auto cluster_avg_fr_lvtr_set = context.SetDoubleItemAttr("cluster_avg_fr_lvtr");
      auto cluster_avg_fr_htr_set = context.SetDoubleItemAttr("cluster_avg_fr_htr");
      auto cluster_avg_mc_ctr_set = context.SetDoubleItemAttr("cluster_avg_mc_ctr");
      auto cluster_avg_mc_ltr_set = context.SetDoubleItemAttr("cluster_avg_mc_ltr");
      auto cluster_avg_mc_wtr_set = context.SetDoubleItemAttr("cluster_avg_mc_wtr");
      auto cluster_avg_mc_cmtr_set = context.SetDoubleItemAttr("cluster_avg_mc_cmtr");
      auto cluster_avg_mc_lvtr_set = context.SetDoubleItemAttr("cluster_avg_mc_lvtr");
      auto hetu_cluster_reflect_double_list =
          context.GetDoubleListCommonAttr("hetu_cluster_reflect_double_list");
      int64 nearby_hetu_cluster_list_size =
          context.GetIntCommonAttr("nearby_hetu_cluster_list_size").value_or(-1);
      auto hetu_sim_cluster_accessor = context.GetIntItemAttr("item_dynamic_info.hetu_sim_cluster_id");
      base::perfutil::PerfUtilWrapper::IntervalLogStash(base::GetTimestamp() - start_ts,
                                                        "reco.nearby.NearbyLightFunctionSet", "reco_nearby",
                                                        "CalcHetuDebiasReflect.duration", "first");
      if (hetu_cluster_reflect_double_list &&
          hetu_cluster_reflect_double_list->size() % nearby_hetu_cluster_list_size == 0) {  // redis 格式无误
        std::unordered_map<int, std::vector<double>> hetu_debias_map;
        int hetu_double_list_size = hetu_cluster_reflect_double_list->size();
        int count = 0;
        while (true) {
          if (count % nearby_hetu_cluster_list_size == 0 &&
              (count + nearby_hetu_cluster_list_size) <= hetu_double_list_size) {
            std::vector<double> emp_vector;
            for (int i = 17; i < nearby_hetu_cluster_list_size; i++) {
              if (i == 32 || i == 33) {
                continue;
              }
              emp_vector.push_back(hetu_cluster_reflect_double_list->at(count + i));
            }
            hetu_debias_map[static_cast<int>(hetu_cluster_reflect_double_list->at(count))] =
                std::move(emp_vector);
          }
          // 遍历完毕
          if ((count + nearby_hetu_cluster_list_size) > hetu_double_list_size) {
            break;
          }
          count += nearby_hetu_cluster_list_size;
        }
        base::perfutil::PerfUtilWrapper::IntervalLogStash(base::GetTimestamp() - start_ts,
                                                          "reco.nearby.NearbyLightFunctionSet", "reco_nearby",
                                                          "CalcHetuDebiasReflect.duration", "second");
        // 处理每个 item
        std::for_each(begin, end, [=](const CommonRecoResult &result) {
          int64 hetu_level_cluster = hetu_sim_cluster_accessor(result).value_or(0);
          if (hetu_level_cluster && hetu_debias_map.count(hetu_level_cluster)) {
            cluster_avg_emp_ctr_set(result, hetu_debias_map.at(hetu_level_cluster)[0]);
            cluster_avg_emp_ltr_set(result, hetu_debias_map.at(hetu_level_cluster)[1]);
            cluster_avg_emp_wtr_set(result, hetu_debias_map.at(hetu_level_cluster)[2]);
            cluster_avg_emp_cmtr_set(result, hetu_debias_map.at(hetu_level_cluster)[3]);
            cluster_avg_emp_lvtr_set(result, hetu_debias_map.at(hetu_level_cluster)[4]);
            cluster_avg_fr_ctr_set(result, hetu_debias_map.at(hetu_level_cluster)[5]);
            cluster_avg_fr_ltr_set(result, hetu_debias_map.at(hetu_level_cluster)[6]);
            cluster_avg_fr_wtr_set(result, hetu_debias_map.at(hetu_level_cluster)[7]);
            cluster_avg_fr_cmtr_set(result, hetu_debias_map.at(hetu_level_cluster)[8]);
            cluster_avg_fr_lvtr_set(result, hetu_debias_map.at(hetu_level_cluster)[9]);
            cluster_avg_mc_ctr_set(result, hetu_debias_map.at(hetu_level_cluster)[10]);
            cluster_avg_mc_ltr_set(result, hetu_debias_map.at(hetu_level_cluster)[11]);
            cluster_avg_mc_wtr_set(result, hetu_debias_map.at(hetu_level_cluster)[12]);
            cluster_avg_mc_cmtr_set(result, hetu_debias_map.at(hetu_level_cluster)[13]);
            cluster_avg_mc_lvtr_set(result, hetu_debias_map.at(hetu_level_cluster)[14]);
            cluster_avg_emp_htr_set(result, hetu_debias_map.at(hetu_level_cluster)[15]);
            cluster_avg_fr_htr_set(result, hetu_debias_map.at(hetu_level_cluster)[16]);
          }
        });
        base::perfutil::PerfUtilWrapper::IntervalLogStash(base::GetTimestamp() - start_ts,
                                                          "reco.nearby.NearbyLightFunctionSet", "reco_nearby",
                                                          "CalcHetuDebiasReflect.duration", "third");
      } else {
        LOG(ERROR) << "nearby hetu debias redis read error, hetu_double_list_size:"
                   << std::to_string(hetu_cluster_reflect_double_list->size())
                   << ", nearby_hetu_cluster_list_size:" << std::to_string(nearby_hetu_cluster_list_size);
      }
      return true;
  }
static bool CalcSimScoreCrossCandidates(const CommonRecoLightFunctionContext &context,
  RecoResultConstIter begin, RecoResultConstIter end) {
  // 1. 获取所有 mmu embed 的 avg pooling
  std::vector<float> avg_mmu_embed(64, 0.0f);
  int item_num = 0;
  auto mmu_embed_accessor = context.GetDoubleListItemAttr("nearby_photo_mmu_embed");
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    auto single_mmu_embed = mmu_embed_accessor(result);
    if (single_mmu_embed.has_value() && single_mmu_embed->size() == 64) {
      for (int i = 0; i < 64; i++) {
        avg_mmu_embed[i] += (*single_mmu_embed)[i];
      }
      item_num += 1;
    }
  });
  for (int i = 0; i < 64; i++) {
    avg_mmu_embed[i] /= std::max(1, item_num);
  }
  // 2. 对每个 item 计算其和 avg mmu embed 的 e^{-cosine_sim(avg mmu embed, mmu embed)}
  auto set_mmu_sim_score = context.SetDoubleItemAttr("mmu_sim_score_across_candidates");
  // 2.1 计算 avg mmu embed 的范数，避免重复计算
  float avg_mmu_embed_norm = 0.0f;
  for (int i = 0; i < 64; i++) {
    avg_mmu_embed_norm += avg_mmu_embed[i] * avg_mmu_embed[i];
  }
  avg_mmu_embed_norm = std::max(std::sqrt(avg_mmu_embed_norm), 0.00001f);
  // 2.2 计算每一个 mmu embed 和 avg embed 的相似分数
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    auto single_mmu_embed = mmu_embed_accessor(result);
    if (single_mmu_embed.has_value() && single_mmu_embed->size() == 64) {
      // 计算当前 mmu embed 的范数
      float single_mmu_embed_norm = 0.0f;
      for (int i = 0; i < 64; i++) {
        single_mmu_embed_norm += single_mmu_embed->at(i) * single_mmu_embed->at(i);
      }
      single_mmu_embed_norm = std::max(std::sqrt(single_mmu_embed_norm), 0.00001f);
      // 计算点积
      float dot_product = 0.0f;
      for (int i = 0; i < 64; i++) {
        dot_product += avg_mmu_embed[i] * single_mmu_embed->at(i);
      }
      // 计算 cosine similarity
      float cosine_sim_score = dot_product / (avg_mmu_embed_norm * single_mmu_embed_norm);
      // 计算 e^{-cos_sim}
      float mmu_sim_score = std::exp(-cosine_sim_score);
      set_mmu_sim_score(result, mmu_sim_score);
    }
  });
  return true;
}
static bool CalcUserProvCode(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                             RecoResultConstIter end) {
  int64 user_adcode = context.GetIntCommonAttr("user_adcode").value_or(0);
  int64 prov_code = (user_adcode / 10000) * 10000;
  std::string user_prov_code = "province_" + std::to_string(prov_code);
  context.SetStringCommonAttr("user_prov_code", user_prov_code);
  return true;
}

  static bool CalcTriggerHardSearch(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
      auto colossus_photo_id = context.GetIntListCommonAttr("colossus_photo_id");
      auto colossus_author_id = context.GetIntListCommonAttr("colossus_author_id");
      auto colossus_tag = context.GetIntListCommonAttr("colossus_tag");
      auto colossus_play_time = context.GetIntListCommonAttr("colossus_play_time");
      auto colossus_duration = context.GetIntListCommonAttr("colossus_duration");
      auto colossus_timestamp = context.GetIntListCommonAttr("colossus_timestamp");
      auto trigger_hard_search_num = context.GetIntCommonAttr("trigger_hard_search_num").value_or(1000);

      if (!colossus_photo_id || !colossus_author_id || !colossus_tag || !colossus_play_time ||
          !colossus_duration || !colossus_timestamp) {
        return true;
      }
      if (colossus_photo_id.has_value() && colossus_author_id.has_value() && colossus_tag.has_value() &&
          colossus_play_time.has_value() && colossus_duration.has_value() && colossus_timestamp.has_value()) {
        bool size_macth = colossus_photo_id->size() == colossus_tag->size() &&
                          colossus_photo_id->size() == colossus_play_time->size() &&
                          colossus_photo_id->size() == colossus_duration->size() &&
                          colossus_photo_id->size() == colossus_author_id->size() &&
                          colossus_photo_id->size() == colossus_timestamp->size();
        if (!size_macth) {
          return true;
        }
        auto trigger_hetu_level_one = context.GetIntListCommonAttr("trigger_hetu_level_one");
        auto trigger_hetu_level_two = context.GetIntListCommonAttr("trigger_hetu_level_two");
        auto trigger_hetu_level_three = context.GetIntListCommonAttr("trigger_hetu_level_three");
        int64 current_s = base::GetTimestamp() / 1000000;  // s 为单位的时间戳

        // 依次遍历 3 2 1 level hetu
        int trigger_hetu = 0;
        if (trigger_hetu_level_three && trigger_hetu_level_three->size() > 0) {
          trigger_hetu = trigger_hetu_level_three->at(0);
        } else if (trigger_hetu_level_two && trigger_hetu_level_two->size() > 0) {
          trigger_hetu = trigger_hetu_level_two->at(0);
        } else if (trigger_hetu_level_one && trigger_hetu_level_one->size() > 0) {
          trigger_hetu = trigger_hetu_level_one->at(0);
        }
        // 目前传入参数的序列，最近的行为在最后面；取 tag 和 trigger_hetu 相同的最近 50 个有效播放行为，如果
        // trigger_hetu = 0，则直接取最近的 50 个有效播放行为
        std::vector<int64> hard_search_pids;
        std::vector<int64> hard_search_aids;
        std::vector<int64> hard_search_tags;
        std::vector<int64> hard_search_play_times;
        std::vector<int64> hard_search_durations;
        std::vector<int64> hard_search_day_gaps;

        const absl::Span<const int64_t> &photo_ids = colossus_photo_id.value();
        const absl::Span<const int64_t> &author_ids = colossus_author_id.value();
        const absl::Span<const int64_t> &tags = colossus_tag.value();
        const absl::Span<const int64_t> &play_times = colossus_play_time.value();
        const absl::Span<const int64_t> &durations = colossus_duration.value();
        const absl::Span<const int64_t> &timestamps = colossus_timestamp.value();
        int64 action_size = photo_ids.size();
        int64 visit_size = action_size > trigger_hard_search_num ? trigger_hard_search_num : action_size;
        auto add_if_matches = [&](int64 index) {
          if (trigger_hetu <= 0 || (tags[index] == trigger_hetu)) {
            hard_search_pids.push_back(photo_ids[index]);
            hard_search_aids.push_back(author_ids[index]);
            hard_search_tags.push_back(tags[index]);
            hard_search_play_times.push_back(play_times[index]);
            hard_search_durations.push_back(durations[index]);
            hard_search_day_gaps.push_back((current_s - timestamps[index]) / 86400);
          }
        };
        for (int i = 0; i < visit_size; ++i) {
          if (hard_search_pids.size() < 50) {
            add_if_matches(action_size - 1 - i);  // 从最后面向前取
          } else {
            break;
          }
        }

        base::perfutil::PerfUtilWrapper::IntervalLogStash(hard_search_day_gaps.size() * 1000000,
                                                          "reco.nearby.NearbyLightFunctionSet", "reco_nearby",
                                                          "CalcTriggerHardSearch", "length");
        context.SetIntListCommonAttr("trigger_hard_search_pids", std::move(hard_search_pids));
        context.SetIntListCommonAttr("trigger_hard_search_aids", std::move(hard_search_aids));
        context.SetIntListCommonAttr("trigger_hard_search_tags", std::move(hard_search_tags));
        context.SetIntListCommonAttr("trigger_hard_search_play_times", std::move(hard_search_play_times));
        context.SetIntListCommonAttr("trigger_hard_search_durations", std::move(hard_search_durations));
        context.SetIntListCommonAttr("trigger_hard_search_days_gap", std::move(hard_search_day_gaps));
        context.SetIntCommonAttr("trigger_hetu", trigger_hetu);
      }
      int where_get_colossus = (colossus_photo_id && colossus_photo_id->size() > 0) ? 1000000 : 0;
      base::perfutil::PerfUtilWrapper::IntervalLogStash(where_get_colossus,
                                                        "reco.nearby.NearbyLightFunctionSet", "reco_nearby",
                                                        "CalcTriggerHardSearch", "whether_get_colossus");
      return true;
  }

  static bool CalcAllCount(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
      auto hot_click_accessor = context.GetIntItemAttr("hot_click");
      auto nearby_click_accessor = context.GetIntItemAttr("nearby_click");
      auto nearby_realshow_accessor = context.GetIntItemAttr("nearby_realshow");
      auto hot_realshow_accessor = context.GetIntItemAttr("hot_realshow");
      auto set_all_click = context.SetIntItemAttr("all_click");
      auto set_all_realshow = context.SetIntItemAttr("all_realshow");
      std::for_each(begin, end, [=](const CommonRecoResult &result) {
        int64 hot_click = hot_click_accessor(result).value_or(0);
        int64 nearby_click = nearby_click_accessor(result).value_or(0);
        int64 nearby_realshow = nearby_realshow_accessor(result).value_or(0);
        int64 hot_realshow = hot_realshow_accessor(result).value_or(0);
        set_all_click(result, hot_click + nearby_click);
        set_all_realshow(result, hot_realshow + nearby_realshow);
      });
      return true;
    }

  static bool GenDynamicNegActionFilterRedisKeys(const CommonRecoLightFunctionContext &context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
    auto dislike_author_list =
        context.GetIntListCommonAttr("user_hate_live_author_list").value_or(absl::Span<const int64>());
    auto report_author_list =
        context.GetIntListCommonAttr("user_report_author_list").value_or(absl::Span<const int64>());
    auto dislike_author_list_tms =
        context.GetIntListCommonAttr("user_hate_live_time_ms_list").value_or(absl::Span<const int64>());
    auto report_author_list_tms =
        context.GetIntListCommonAttr("user_report_time_ms_list").value_or(absl::Span<const int64>());
    bool enable_dynamic_emb_punish_report_list =
        context.GetIntCommonAttr("nearby_enable_dynamic_emb_punish_report_list").value_or(0) == 1;
    bool enable_dynamic_emb_punish_time_limit_dislike =
        context.GetIntCommonAttr("nearby_enable_punish_time_limit_dislike").value_or(0) == 1;
    auto dynamic_emb_punish_time_limit_dislike =
        context.GetIntCommonAttr("nearby_punish_time_limit_dislike").value_or(86400000);
    bool enable_dynamic_emb_punish_time_limit_report =
        context.GetIntCommonAttr("nearby_enable_punish_time_limit_report").value_or(0) == 1;
    auto dynamic_emb_punish_time_limit_report =
        context.GetIntCommonAttr("nearby_punish_time_limit_report").value_or(86400000);
    auto negative_emb_prefix =
        context.GetStringCommonAttr("negative_emb_prefix").value_or("hetu_aid_v2_7d_");
    auto follow_aid_list =
        context.GetIntListCommonAttr("live_follow_author_list").value_or(absl::Span<const int64>());
    auto gift_aid_list =
        context.GetIntListCommonAttr("uLiveGiftAuthorIdForFilterList").value_or(absl::Span<const int64>());
    bool enable_dynamic_neg_action_filter_trigger_skip_follow =
        context.GetIntCommonAttr("nearby_enable_dynamic_neg_action_filter_skip_follow").value_or(0) == 1;
    bool enable_follow_skip_generalize =
        context.GetIntCommonAttr("nearby_enable_follow_skip_generalize").value_or(0) == 1;
    auto gift_aid_low_dislike_threshold =
        context.GetIntCommonAttr("nearby_gift_aid_low_dislike_threshold").value_or(2);
    bool enable_dynamic_neg_action_filter_skip_gift_aid_low_dislike =
        context.GetIntCommonAttr("nearby_enable_dynamic_neg_action_filter_skip_gift_aid_low_dislike").
        value_or(0) == 1;

    folly::F14FastSet<int64> follow_aid_set(follow_aid_list.begin(), follow_aid_list.end());
    folly::F14FastSet<int64> gift_aid_set(gift_aid_list.begin(), gift_aid_list.end());

    if (dislike_author_list.empty() && report_author_list.empty()) return true;

    int reserve_size = dislike_author_list.size() + report_author_list.size();
    std::vector<std::string> keys;
    keys.reserve(reserve_size);
    auto now_ms = base::GetTimestamp() / 1000;
    folly::F14FastMap<int64, int> dislike_cnt_map;
    std::vector<int64> skip_gift_aid_list;
    std::vector<int64> effective_dislike_aid_list;
    std::vector<int64> self_filter_aid_list;

    if (!dislike_author_list.empty() && dislike_author_list.size() == dislike_author_list_tms.size()) {
      for (int i = 0; i < dislike_author_list.size(); ++i) {
        if (now_ms - dislike_author_list_tms[i] <= dynamic_emb_punish_time_limit_dislike) {
          dislike_cnt_map[dislike_author_list[i]]+=1;
          effective_dislike_aid_list.emplace_back(dislike_author_list[i]);
        }
      }
    }
    for (const auto &pair : dislike_cnt_map) {
      if (pair.second < gift_aid_low_dislike_threshold &&
          gift_aid_set.find(pair.first) != gift_aid_set.end()) {
        skip_gift_aid_list.emplace_back(pair.first);
      }
    }
    folly::F14FastSet<int64> skip_gift_aid_set(skip_gift_aid_list.begin(), skip_gift_aid_list.end());

    // Process dislike_author_list
    if (!dislike_author_list.empty()) {
        for (int i = 0; i < dislike_author_list.size(); i++) {
            auto item = dislike_author_list.at(i);
            if (enable_dynamic_neg_action_filter_trigger_skip_follow &&
                follow_aid_set.find(item) != follow_aid_set.end()) {
                continue;
            }
            if (enable_dynamic_neg_action_filter_skip_gift_aid_low_dislike &&
                skip_gift_aid_set.find(item) != skip_gift_aid_set.end()) {
                continue;
            }
            std::string temp_key = negative_emb_prefix.data() + std::to_string(item);
            int64 temp_item = item;
            if (enable_dynamic_emb_punish_time_limit_dislike) {
                if (!dislike_author_list_tms.empty()) {
                    if (i < dislike_author_list_tms.size()) {
                        auto time = dislike_author_list_tms.at(i);
                        int64 gap_time = now_ms - time;
                        if (gap_time <= dynamic_emb_punish_time_limit_dislike * 1000) {
                            if (!enable_follow_skip_generalize ||
                                follow_aid_set.find(temp_item) == follow_aid_set.end()) {
                                keys.emplace_back(temp_key);
                            }
                            self_filter_aid_list.emplace_back(temp_item);
                        }
                    }
                }
            } else {
                if (!enable_follow_skip_generalize ||
                    follow_aid_set.find(temp_item) == follow_aid_set.end()) {
                    keys.emplace_back(temp_key);
                }
                self_filter_aid_list.emplace_back(temp_item);
            }
        }
    }

    // Process report_author_list
    if (enable_dynamic_emb_punish_report_list) {
        if (!report_author_list.empty()) {
          for (int i = 0; i < report_author_list.size(); i++) {
              auto item = report_author_list.at(i);
              if (enable_dynamic_neg_action_filter_trigger_skip_follow &&
                  follow_aid_set.find(item) != follow_aid_set.end()) {
                  continue;
              }
              std::string temp_key = negative_emb_prefix.data() + std::to_string(item);
              int64 temp_item = item;
              if (enable_dynamic_emb_punish_time_limit_report) {
                  if (!report_author_list_tms.empty()) {
                      if (i < report_author_list_tms.size()) {
                          auto time = report_author_list_tms.at(i);
                          int64 gap_time = now_ms - time;
                          if (gap_time <= dynamic_emb_punish_time_limit_report * 1000) {
                              if (!enable_follow_skip_generalize ||
                                  follow_aid_set.find(temp_item) == follow_aid_set.end()) {
                                  keys.emplace_back(temp_key);
                              }
                              self_filter_aid_list.emplace_back(temp_item);
                          }
                      }
                  }
              } else {
                  if (!enable_follow_skip_generalize ||
                      follow_aid_set.find(temp_item) == follow_aid_set.end()) {
                      keys.emplace_back(temp_key);
                  }
                  self_filter_aid_list.emplace_back(temp_item);
              }
          }
      }
    }

    if (!keys.empty()) {
        context.SetStringListCommonAttr("neg_redis_keys", std::move(keys));
    }

    bool is_skip_gift_aid_list_empty = skip_gift_aid_list.empty();
    context.SetIntListCommonAttr("skip_gift_aid_list", std::move(skip_gift_aid_list));
    context.SetIntListCommonAttr("effective_dislike_aid_list", std::move(effective_dislike_aid_list));
    context.SetIntListCommonAttr("self_filter_aid_list", std::move(self_filter_aid_list));
    context.SetIntCommonAttr("skip_gift_aid_list_empty", is_skip_gift_aid_list_empty ? 1 : 0);

    return true;
  }

  static bool GenNearbyUserNegativeEmbPunishScore(const CommonRecoLightFunctionContext &context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
    auto neg_emb_str_list =
        context.GetStringListCommonAttr("neg_emb_str_list").value_or(std::vector<absl::string_view>());
    auto item_content_emb = context.GetDoubleListItemAttr("item_info.nearby_live_author_k7_emb");
    auto punish_coeff_gamma = context.GetDoubleCommonAttr("neg_emb_punish_coeff_gamma").value_or(1.0);
    bool enable_neg_emb_cosine_calv2_fix =
        context.GetIntCommonAttr("enable_neg_emb_cosine_calv2_fix").value_or(0) == 1;
    auto punish_score = context.SetDoubleItemAttr("neg_emb_punish_score");
    if (neg_emb_str_list.empty()) return true;

    std::vector<std::vector<double>> neg_emb_vec;
    std::vector<double> neg_sum_vec;
    neg_emb_vec.reserve(neg_emb_str_list.size());
    neg_sum_vec.reserve(neg_emb_str_list.size());
    for (auto str : neg_emb_str_list) {
      std::vector<absl::string_view> str_vec = absl::StrSplit(str, ",", absl::SkipWhitespace());
      if (str_vec.size() != 64) continue;
      std::vector<double> sub_arr;
      sub_arr.reserve(64);
      double val = 0.0;
      double sum_val = 0.0;
      for (auto e : str_vec) {
        if (absl::SimpleAtod(e, &val)) {
          sub_arr.emplace_back(val);
          sum_val += val * val;
        } else {
          sub_arr.emplace_back(0);
        }
      }
      neg_emb_vec.emplace_back(std::move(sub_arr));
      neg_sum_vec.emplace_back(std::sqrt(sum_val));
    }
    if (neg_emb_vec.empty()) return true;

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_emb = item_content_emb(result).value_or(absl::Span<const double>());
      if (item_emb.empty() || item_emb.size() != 64) return;
      double sum_item_val = 0.0;
      double punish_coeff = 1.0;
      double anti_cos = 1.0;
      for (int i = 0; i < neg_emb_vec.size(); ++i) {
        double sum_emb = 0.0;
        for (int j = 0; j < item_emb.size(); ++j) {
          if (i == 0) sum_item_val += item_emb[j] * item_emb[j];
          sum_emb += neg_emb_vec[i][j] * item_emb[j];
        }
        if (enable_neg_emb_cosine_calv2_fix) {
          double cos_val = (1 + (sum_emb / (neg_sum_vec[i] * std::sqrt(sum_item_val)))) / 2;
          double anti_cos_val = 1 - cos_val;
          if (anti_cos_val < anti_cos) {
            anti_cos = anti_cos_val;
          }
        } else {
          double cos_val = std::abs(sum_emb) / (neg_sum_vec[i] * std::sqrt(sum_item_val));
          punish_coeff *= std::pow(1 - cos_val, punish_coeff_gamma);
        }
      }
      if (enable_neg_emb_cosine_calv2_fix) {
        anti_cos = anti_cos * punish_coeff_gamma;
        punish_score(result, anti_cos);
      } else {
        punish_score(result, punish_coeff);
      }
    });

    return true;
  }

  static bool GenNearbyUserNegativeEmbPunishThres(const CommonRecoLightFunctionContext &context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
    auto fixed_threhold =
        context.GetDoubleCommonAttr("nearby_dynamic_neg_emb_punish_threshold").value_or(1.0);
    auto fixed_threhold_coff =
        context.GetDoubleCommonAttr("nearby_dynamic_neg_emb_punish_threshold_coff").value_or(1.0);
    auto fixed_threhold_bound =
        context.GetDoubleCommonAttr("nearby_dynamic_neg_emb_punish_threshold_bound").value_or(1.0);
    auto neg_emb_str_list =
        context.GetStringListCommonAttr("neg_emb_str_list").value_or(std::vector<absl::string_view>());
    if (neg_emb_str_list.empty()) {
      context.SetDoubleCommonAttr("dynamic_neg_emb_punish_threshold", fixed_threhold);
      return true;
    } else {
      int size = neg_emb_str_list.size();
      fixed_threhold = fixed_threhold * std::pow(fixed_threhold_coff, size);
      if (fixed_threhold >= fixed_threhold_bound) {
        fixed_threhold = fixed_threhold_bound;
      }
      context.SetDoubleCommonAttr("dynamic_neg_emb_punish_threshold", fixed_threhold);
    }
    return true;
  }

  static bool CalcSkitTag(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                           RecoResultConstIter end) {
      auto tag_list_lv2_attr = context.GetIntListItemAttr(
        "item_dynamic_info.live_hetu_tag_info.hetu_level_two");
      auto tag_list_lv3_attr = context.GetIntListItemAttr(
        "item_dynamic_info.live_hetu_tag_info.hetu_level_three");

      auto target_tag_lv2_v1_str = context.GetStringCommonAttr("mmu_tag_lv2_v1").value_or("2036");
      auto output_label_lv2_v1_attr = context.SetIntItemAttr("is_movie_tag");
      folly::F14FastSet<int64> target_tag_lv2_v1_set;

      auto target_tag_lv2_v2_str = context.GetStringCommonAttr("mmu_tag_lv2_v2").value_or("2038");
      auto output_label_lv2_v2_attr = context.SetIntItemAttr("is_skit_tag");
      folly::F14FastSet<int64> target_tag_lv2_v2_set;

      auto target_tag_lv2_v3_str = context.GetStringCommonAttr("mmu_tag_lv2_v3").value_or("2114");
      auto output_label_lv2_v3_attr = context.SetIntItemAttr("is_silent_tag");
      folly::F14FastSet<int64> target_tag_lv2_v3_set;

      auto target_tag_lv3_str = context.GetStringCommonAttr("mmu_tag_lv3_v1").value_or("3161");
      auto output_label_lv3_attr = context.SetIntItemAttr("is_fengshui_tag");
      folly::F14FastSet<int64> target_tag_lv3_set;

      auto fill_item_set = [](const absl::string_view str, folly::F14FastSet<int64> *set_ptr) {
        if (set_ptr == nullptr || str.empty()) {
          return;
        }
        std::vector<absl::string_view> str_vec = absl::StrSplit(str, ",", absl::SkipWhitespace());
        for (auto item : str_vec) {
          int64 flag = 0;
          if (absl::SimpleAtoi(item, &flag)) {
            set_ptr->insert(flag);
          }
        }
      };
      fill_item_set(target_tag_lv2_v1_str, &target_tag_lv2_v1_set);
      fill_item_set(target_tag_lv2_v2_str, &target_tag_lv2_v2_set);
      fill_item_set(target_tag_lv2_v3_str, &target_tag_lv2_v3_set);
      fill_item_set(target_tag_lv3_str, &target_tag_lv3_set);

      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        int output_v1 = 0;
        int output_v2 = 0;
        int output_v3 = 0;
        auto tag_lv2_list = tag_list_lv2_attr(result).value_or(absl::Span<const int64>());
        if (!tag_lv2_list.empty()) {
          for (const int64 tag : tag_lv2_list) {
            if (target_tag_lv2_v1_set.find(tag) != target_tag_lv2_v1_set.end()) {
              output_v1 = 1;
            }
            if (target_tag_lv2_v2_set.find(tag) != target_tag_lv2_v2_set.end()) {
              output_v2 = 1;
            }
            if (target_tag_lv2_v3_set.find(tag) != target_tag_lv2_v3_set.end()) {
              output_v3 = 1;
            }
          }
        }
        output_label_lv2_v1_attr(result, output_v1);
        output_label_lv2_v2_attr(result, output_v2);
        output_label_lv2_v3_attr(result, output_v3);
      });

      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        int output_lv3 = 0;
        auto tag_lv3_list = tag_list_lv3_attr(result).value_or(absl::Span<const int64>());
        if (!tag_lv3_list.empty()) {
          for (const int64 tag : tag_lv3_list) {
            if (target_tag_lv3_set.find(tag) != target_tag_lv3_set.end()) {
              output_lv3 = 1;
            }
          }
        }
        output_label_lv3_attr(result, output_lv3);
      });

      return true;
  }

  static bool ParseUserSkitFeatures(const CommonRecoLightFunctionContext &context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
    auto user_skit_features_str = context.GetStringCommonAttr("nearby_user_skit_str").value_or("");
    int valid_skit_user = 0;
    int is_target_openreason_user = 0;
    if (user_skit_features_str != "") {
      std::vector<absl::string_view> params = \
          absl::StrSplit(user_skit_features_str, ";", absl::SkipWhitespace());
      for (auto token : params) {
        std::vector<absl::string_view> param = absl::StrSplit(token, ":", absl::SkipWhitespace());
        if (param.size() != 2) continue;
        auto name = param[0];
        auto value = param[1];
        if (name == "V1") {
          if (!absl::SimpleAtoi(value, &valid_skit_user)) {
            valid_skit_user = 0;
          }
        } else if (name == "V2") {
          if (!absl::SimpleAtoi(value, &is_target_openreason_user)) {
            is_target_openreason_user = 0;
          }
        }
      }
    }
    context.SetIntCommonAttr("valid_skit_user", valid_skit_user);
    context.SetIntCommonAttr("is_target_openreason_user", is_target_openreason_user);
    return true;
  }

  static bool ParseAuthorOpenReason(const CommonRecoLightFunctionContext &context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
    auto author_openreason_skit_str = \
        context.GetStringCommonAttr("author_openreason_skit_str").value_or("");
    auto set_is_openreason_skit_author = context.SetIntItemAttr("is_openreason_skit_author");
    auto get_author_id = context.GetIntItemAttr("author_id");
    if (author_openreason_skit_str != "") {
      std::vector<absl::string_view> openreason_movie_authorls = \
          absl::StrSplit(author_openreason_skit_str, ",", absl::SkipWhitespace());
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        int is_openreason_skit_author = 0;
        auto aid = get_author_id(result).value_or(0);
        if (aid != 0 && std::find(openreason_movie_authorls.begin(), openreason_movie_authorls.end(), \
            std::to_string(aid)) != openreason_movie_authorls.end()) {
              is_openreason_skit_author = 1;
            }
        set_is_openreason_skit_author(result, is_openreason_skit_author);
      });
    } else {
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        set_is_openreason_skit_author(result, 0);
      });
    }
    return true;
  }

  static bool CalNtmInterestId(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalGlobalGraphInterests(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalGlobalGraphInterestsV2(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalClickFollowItemEmb(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalSimItemWithEffectEmb(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalSimAuthorEmb(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalGlobalGraphU2UInterests(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalGlobalNegativeSamplingDnnU2UInterests(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalFilterScoreFromContentId(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalPhotoKeyFromPhotoId(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalUserHateContentIds(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalUserReportContentIds(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalFilterScoreFromHateCluster1k(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalClusterValidClickScore(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalAuthorValidClickScore(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalNearbyLowActivePropensityScore(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalFilterScoreFromInstantHateCluster1k(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalcNearbyUserAoiTrace(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalcNearbyAoiArriverList(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool EnrichIsPhotoSameCityForCityPreference(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);

  static bool CalWtrDebiasCascadeScore(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    std::vector<double> cascade_pwtr_values;
    auto cascade_pwtr_accessor = context.GetDoubleItemAttr("cascade_pwtr");
    auto is_followed_accessor = context.GetIntItemAttr("is_followed");
    auto uProductType = context.GetIntCommonAttr("uProductType").value_or(0);
    double is_followed_total = 0.0;
    int result_total = 0;
    std::string quantile_str;
    if (uProductType == 1) {
      quantile_str = "nearby_nebula_photo_cascade_wtr_replace_quantile";
    } else {
      quantile_str = "nearby_main_photo_cascade_wtr_replace_quantile";
    }
    auto quantile = context.GetDoubleCommonAttr(quantile_str).value_or(0.05);
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double cascade_pwtr = cascade_pwtr_accessor(result).value_or(0.0);
      is_followed_total += is_followed_accessor(result).value_or(0.0);
      result_total += 1;
      cascade_pwtr_values.push_back(cascade_pwtr);
    });

    std::sort(cascade_pwtr_values.begin(), cascade_pwtr_values.end(), std::greater<double>());
    size_t top5_percent_index = cascade_pwtr_values.size() * quantile;
    double top5_percentile = 0.0;
    if (top5_percent_index >= 0 && top5_percent_index < cascade_pwtr_values.size()) {
      top5_percentile = cascade_pwtr_values[top5_percent_index];
    }
    auto set_cascade_pwtr = context.SetDoubleItemAttr("cascade_pwtr");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      if (is_followed_accessor(result).value_or(0.0) > 0.1) {
        set_cascade_pwtr(result, top5_percentile);
      }
    });
    if (uProductType == 1) {
      base::perfutil::PerfUtilWrapper::IntervalLogStash(50000,
      "reco.nearby", "wtr_debias", "nebula", "cascade", "follow",
      std::to_string(static_cast<int>(is_followed_total)));
      base::perfutil::PerfUtilWrapper::IntervalLogStash(50000,
      "reco.nearby", "wtr_debias", "nebula", "cascade", "total",
      std::to_string(result_total));
    } else if (uProductType == 0) {
      base::perfutil::PerfUtilWrapper::IntervalLogStash(50000,
      "reco.nearby", "wtr_debias", "main", "cascade", "follow",
      std::to_string(static_cast<int>(is_followed_total)));
      base::perfutil::PerfUtilWrapper::IntervalLogStash(50000,
      "reco.nearby", "wtr_debias", "main", "cascade", "total",
      std::to_string(result_total));
    }
    return true;
  }

  static bool CalFollowBoostCascadeScore(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto uProductType = context.GetIntCommonAttr("uProductType").value_or(0);
    auto is_followed_accessor = context.GetIntItemAttr("is_followed");
    auto es_score_accessor = context.GetDoubleItemAttr("cascade_ensemble_score_raw");
    auto set_es_score = context.SetDoubleItemAttr("cascade_ensemble_score_raw");
    std::string coeff_str;
    if (uProductType == 1) {
      coeff_str = "nearby_nebula_cascade_follow_boost_coeff";
    } else {
      coeff_str = "nearby_main_cascade_follow_boost_coeff";
    }
    double coeff = context.GetDoubleCommonAttr(coeff_str).value_or(1.0);
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto is_followed = is_followed_accessor(result).value_or(0.0);
      if (is_followed > 0.1) {
        double score = es_score_accessor(result).value_or(0.0);
        double score_boost = score * coeff;
        set_es_score(result, score_boost);
      }
    });
    return true;
  }

  static bool EnrichPhotoMcSameCitySupplyFactor(const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
    int item_num = std::distance(begin, end);
    if (item_num <= 0) {
      return false;
    }
    auto cascade_pctr_get = context.GetDoubleItemAttr("cascade_pctr");
    auto is_same_city_get = context.GetIntItemAttr("is_same_city");

    std::vector<double> raw_same_city_scores;
    std::vector<double> raw_no_same_city_scores;
    const int64 supply_capacity = 300;
    double min_ue_score = std::numeric_limits<double>::max();
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double cascade_pctr = cascade_pctr_get(result).value_or(0.0);
      if (cascade_pctr <= 0.0) {
        return;
      }
      // same city factor
      int64 is_same_city = is_same_city_get(result).value_or(0L);
      if (is_same_city > 0L) {
        raw_same_city_scores.push_back(cascade_pctr);
      } else {
        raw_no_same_city_scores.push_back(cascade_pctr);
      }
    });
    std::sort(raw_same_city_scores.rbegin(), raw_same_city_scores.rend());
    std::sort(raw_no_same_city_scores.rbegin(), raw_no_same_city_scores.rend());
    int raw_same_city_scores_size = raw_same_city_scores.size();
    int raw_no_same_city_scores_size = raw_no_same_city_scores.size();
    double same_city_supply_factor = 0.0;
    if (raw_same_city_scores_size > 0) {
      min_ue_score = raw_same_city_scores[raw_same_city_scores_size - 1];
    }
    if (raw_no_same_city_scores_size > 0) {
      min_ue_score =
        std::min(raw_no_same_city_scores[raw_no_same_city_scores_size - 1], min_ue_score);
    }
    for (int i = 0; i < supply_capacity; i++) {
      double same_city_score =
        i < raw_same_city_scores_size - 1 ? raw_same_city_scores[i] : min_ue_score;
      double no_same_city_score =
        i < raw_no_same_city_scores_size - 1 ? raw_no_same_city_scores[i] : min_ue_score;
      same_city_supply_factor += same_city_score / no_same_city_score;
    }
    same_city_supply_factor = same_city_supply_factor / 300.0;
    context.SetDoubleCommonAttr("mc_same_city_photo_supply_factor", same_city_supply_factor);
    return true;
  }

  static bool EnrichSlidePhotoMcSameCitySupplyFactor(const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin, RecoResultConstIter end) {
    int item_num = std::distance(begin, end);
    if (item_num <= 0) {
      return false;
    }
    auto cascade_pctr_get = context.GetDoubleItemAttr("cascade_plvtr");
    auto is_same_city_get = context.GetIntItemAttr("is_same_city_gen");

    std::vector<double> raw_same_city_scores;
    std::vector<double> raw_no_same_city_scores;
    const int64 supply_capacity = 200;
    double min_ue_score = std::numeric_limits<double>::max();
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double cascade_pctr = cascade_pctr_get(result).value_or(0.0);
      if (cascade_pctr <= 0.0) {
        return;
      }
      // same city factor
      int64 is_same_city = is_same_city_get(result).value_or(0L);
      if (is_same_city > 0L) {
        raw_same_city_scores.push_back(cascade_pctr);
      } else {
        raw_no_same_city_scores.push_back(cascade_pctr);
      }
    });
    std::sort(raw_same_city_scores.rbegin(), raw_same_city_scores.rend());
    std::sort(raw_no_same_city_scores.rbegin(), raw_no_same_city_scores.rend());
    int raw_same_city_scores_size = raw_same_city_scores.size();
    int raw_no_same_city_scores_size = raw_no_same_city_scores.size();
    double same_city_supply_factor = 0.0;
    if (raw_same_city_scores_size > 0) {
      min_ue_score = raw_same_city_scores[raw_same_city_scores_size - 1];
    }
    if (raw_no_same_city_scores_size > 0) {
      min_ue_score =
        std::min(raw_no_same_city_scores[raw_no_same_city_scores_size - 1], min_ue_score);
    }
    for (int i = 0; i < supply_capacity; i++) {
      double same_city_score =
        i < raw_same_city_scores_size - 1 ? raw_same_city_scores[i] : min_ue_score;
      double no_same_city_score =
        i < raw_no_same_city_scores_size - 1 ? raw_no_same_city_scores[i] : min_ue_score;
      same_city_supply_factor += same_city_score / no_same_city_score;
    }
    same_city_supply_factor = same_city_supply_factor / 200.0;
    context.SetDoubleCommonAttr("mc_same_city_photo_supply_factor", same_city_supply_factor);
    return true;
  }

  static bool CalcIsSimilarPhoto(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto item_content_ids_accessor =
        context.GetIntListItemAttr("item_info.duplicate_content_id_list.content_ids");
    auto is_filter_by_content_ids_set = context.SetIntItemAttr("is_filter_by_content_ids");
    std::unordered_set<int64> exist_cids_set;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_cids = item_content_ids_accessor(result);
      bool flag = false;
      if (item_cids.has_value()) {
        for (int i = 0; i < item_cids->size(); i++) {
          if (exist_cids_set.count(item_cids->at(i)) > 0) {
            flag = true;
            break;
          }
        }
        if (!flag) {
          for (int i = 0; i < item_cids->size(); i++) {
            exist_cids_set.insert(item_cids->at(i));
          }
          is_filter_by_content_ids_set(result, 0);
        } else {
          is_filter_by_content_ids_set(result, 1);
        }
      } else {
        is_filter_by_content_ids_set(result, 0);
      }
    });
    return true;
  }
  static bool CalcLowShowPhotoFilter(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto nearby_user_active_level = context.GetIntCommonAttr("nearby_user_active_level").value_or(0);
    auto enable_nearby_low_show_photo_filter_all =
          context.GetIntCommonAttr("enable_nearby_low_show_photo_filter_all").value_or(0);
    auto enable_nearby_low_show_photo_filter_la =
          context.GetIntCommonAttr("enable_nearby_low_show_photo_filter_la").value_or(0);
    auto offset = context.GetIntCommonAttr("offset").value_or(0);
    bool enable_la =  nearby_user_active_level <= 1 && enable_nearby_low_show_photo_filter_la;
    bool enable_all =  offset == 0 && enable_nearby_low_show_photo_filter_all;
    bool enable = enable_la || enable_all;
    auto low_show_bound = context.GetIntCommonAttr("nearby_low_show_bound").value_or(0);
    auto realshow_accessor = context.GetIntItemAttr("nearby_realshow");
    auto timestamp_accessor = context.GetIntItemAttr("item_info.timestamp");
    auto isPublishRelatioSource_accessor = context.GetIntItemAttr("isPublishRelatioSource");
    auto is_photo_low_show_filter = context.SetIntItemAttr("is_photo_low_show_filter");
    int64 current_ts = base::GetTimestamp();
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto realshow = realshow_accessor(result).value_or(0);
      auto photo_age = current_ts - timestamp_accessor(result).value_or(0);
      int64 isPublishRelatioSource = isPublishRelatioSource_accessor(result).value_or(0);
      if (photo_age > 3 * base::Time::kMicrosecondsPerDay && realshow < low_show_bound
        && isPublishRelatioSource == 0 && enable) {
        is_photo_low_show_filter(result, 1);
      } else {
        is_photo_low_show_filter(result, 0);
      }
    });
    return true;
  }
  static bool CalcWhetherMmuIdentifyRegion(const CommonRecoLightFunctionContext &context,
                                           RecoResultConstIter begin, RecoResultConstIter end) {
    auto mmu_prov_accessor = context.GetIntItemAttr("item_info.city_id_8_province");
    auto mmu_city_accessor = context.GetIntItemAttr("item_info.city_id_8_city");
    auto user_prov = context.GetIntCommonAttr("user_province_id").value_or(0);
    auto user_city = context.GetIntCommonAttr("user_city_id").value_or(0);
    auto is_mmu_identify_prov_set = context.SetIntItemAttr("is_mmu_identify_prov");
    auto is_mmu_identify_city_set = context.SetIntItemAttr("is_mmu_identify_city");
    auto has_mmu_city_info_set = context.SetIntItemAttr("has_mmu_city_v1_info");
    int has_mmu_info_count = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto mmu_prov = mmu_prov_accessor(result).value_or(0);
      auto mmu_city = mmu_city_accessor(result).value_or(0);
      has_mmu_city_info_set(result, mmu_prov > 0 && mmu_city > 0 ? 1 : 0);
      if (mmu_prov > 0 && mmu_city > 0) {
        has_mmu_info_count += 1;
      }
      if (mmu_prov > 0 && mmu_city > 0 && user_prov > 0 && user_city > 0) {
        is_mmu_identify_prov_set(result, mmu_prov == user_prov ? 1 : 0);
        is_mmu_identify_city_set(result, mmu_city == user_city ? 1 : 0);
      } else {
        is_mmu_identify_prov_set(result, 2);  // 这部分说明没有 mmu 标识 或者 用户地理位置信息
        is_mmu_identify_city_set(result, 2);
      }
    });
    int size = std::distance(begin, end);
    if (size > 0) {
      base::perfutil::PerfUtilWrapper::IntervalLogStash(has_mmu_info_count / size * 10000000,
                                                        "reco.nearby.NearbyLightFunctionSet", "reco_nearby",
                                                        "CalcWhetherMmuIdentifyRegion", "has_mmu_info_count");
    }
    return true;
  }
  static bool CalInterestExploreScore(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);

     static bool CalcHetuCtrBoostType(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end) {
      auto up_ctr_hetu_list = context.GetIntListCommonAttr("session_up_ctr_hetu_list");
      auto down_ctr_hetu_list = context.GetIntListCommonAttr("session_down_ctr_hetu_list");
      auto hetu_level_one_accessor = context.GetIntItemAttr("hetu_level_one");
      auto set_hetu_boost_type_accessor = context.SetIntItemAttr("hetu_boost_type");
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        int64 item_hetu = hetu_level_one_accessor(result).value_or(0);
        if (up_ctr_hetu_list.has_value() && std::find(
              up_ctr_hetu_list->begin(), up_ctr_hetu_list->end(), item_hetu) != up_ctr_hetu_list->end()) {
          set_hetu_boost_type_accessor(result, 1);
        } else if (down_ctr_hetu_list.has_value() && std::find(
            down_ctr_hetu_list->begin(), down_ctr_hetu_list->end(), item_hetu) != down_ctr_hetu_list->end()) {
          set_hetu_boost_type_accessor(result, -1);
        } else {
          set_hetu_boost_type_accessor(result, 0);
        }
      });
      return true;
    }
  static bool ParseUserIntention(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto uProductType = context.GetIntCommonAttr("uProductType").value_or(0);
    std::string nebulaIntenion = context.GetStringCommonAttr("uNearbyNebulaIntentionKV").value_or("").data();
    std::string mainIntention = context.GetStringCommonAttr("uNearbyMainIntentionKV").value_or("").data();
    if (uProductType == 1) {
      // 极速版
      int user_intention_type = 0;  // 无社交需求
      if (nebulaIntenion == "1") {
        user_intention_type = 1;  // 仅社交需求
      } else if (nebulaIntenion.find("1") != std::string::npos) {
        user_intention_type = 2;  // 包含社交需求
      }
      context.SetIntCommonAttr("user_intention_type", user_intention_type);
    } else {
      // 主站
      int user_intention_type = 0;  // 无社交需求
      if (mainIntention == "1") {
        user_intention_type = 1;  // 仅社交需求
      } else if (mainIntention.find("1") != std::string::npos) {
        user_intention_type = 2;  // 包含社交需求
      }
      context.SetIntCommonAttr("user_intention_type", user_intention_type);
    }
    return true;
  }
  static bool CalcIsInDegradeHour(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end) {
      auto hour = context.GetIntCommonAttr("uHourOfDay").value_or(0);
      auto degrade_hour_list = context.GetIntListCommonAttr("nearby_degrade_hour_list");
      auto enable_nearby_degrade_autoadjust = 0;

      if (degrade_hour_list && degrade_hour_list->size()) {
        for (int i = 0; i < degrade_hour_list->size(); i++) {
          if (hour == degrade_hour_list->at(i)) {
            enable_nearby_degrade_autoadjust = 1;
            break;
          }
        }
      }
      context.SetIntCommonAttr("enable_nearby_degrade_autoadjust", enable_nearby_degrade_autoadjust);

      return true;
    }

  static bool CalcRequestDegradeLevel(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end) {
      auto context_click = context.GetDoubleListCommonAttr("context_click");
      auto delta1 = -1.0;
      auto delta2 = -1.0;
      auto delta3 = -1.0;
      if (context_click && context_click->size() == 3) {
        delta1 = 4 * context_click->at(0) - 1;
        delta2 = 4 * context_click->at(1) - 1;
        delta3 = 4 * context_click->at(2) - 1;
      }
      auto hour = context.GetIntCommonAttr("uHourOfDay").value_or(0);
      auto degrade_hour_list = context.GetIntListCommonAttr("nearby_degrade_hour_list");
      auto nearby_user_degrade_request_value_level3 =
      context.GetDoubleCommonAttr("nearby_user_degrade_request_value_level3").value_or(0.0);
      auto nearby_user_degrade_request_value_level2 =
      context.GetDoubleCommonAttr("nearby_user_degrade_request_value_level2").value_or(0.0);
      auto nearby_user_degrade_request_value_level1 =
      context.GetDoubleCommonAttr("nearby_user_degrade_request_value_level1").value_or(0.0);
      auto nearby_random_sample_for_uplift =
      context.GetIntCommonAttr("nearby_random_sample_for_uplift").value_or(0);

      auto degrade_level = 0;

      if (nearby_random_sample_for_uplift) {
        std::mt19937 random_engine(base::GetTimestamp());
        std::uniform_int_distribution<> dis(0, 3);
        degrade_level = dis(random_engine);
      } else {
        if (degrade_hour_list && degrade_hour_list->size()) {
          for (int i = 0; i < degrade_hour_list->size(); i++) {
            if (hour == degrade_hour_list->at(i)) {
              if (delta1 > nearby_user_degrade_request_value_level1) {
                degrade_level = 1;
              }
              if (delta2 > nearby_user_degrade_request_value_level2) {
                degrade_level = 2;
              }
              if (delta3 > nearby_user_degrade_request_value_level3) {
                degrade_level = 3;
              }
              break;
            }
          }
        }
      }
      context.SetIntCommonAttr("nearby_request_degrade_level", degrade_level);

      return true;
    }

  static bool CalcRequestDegradeLimit(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end) {
      auto nearby_request_degrade_level =
      context.GetIntCommonAttr("nearby_request_degrade_level").value_or(0);
      auto nearby_user_degrede_quota2 =
      context.GetDoubleCommonAttr("nearby_user_degrede_quota2").value_or(0.0);
      auto nearby_user_degrede_quota1 =
      context.GetDoubleCommonAttr("nearby_user_degrede_quota1").value_or(0.0);
      auto item_num = context.GetIntCommonAttr("item_num").value_or(0);
      auto lowbound = context.GetIntCommonAttr("lowbound").value_or(0);
      auto inf = context.GetIntCommonAttr("inf").value_or(0);
      auto truncate_quota = item_num;

      if (nearby_request_degrade_level == 3) {
        truncate_quota = inf;
      } else if (nearby_request_degrade_level == 2) {
        if (nearby_user_degrede_quota2 * item_num > lowbound) {
          truncate_quota = static_cast<int>(nearby_user_degrede_quota2 * item_num);
        } else {
          truncate_quota = lowbound;
        }
      } else if (nearby_request_degrade_level == 1) {
        if (nearby_user_degrede_quota1 * item_num > lowbound) {
          truncate_quota = static_cast<int>(nearby_user_degrede_quota1 * item_num);
        } else {
          truncate_quota = lowbound;
        }
      }

      context.SetIntCommonAttr("truncate_quota", truncate_quota);

      return true;
    }

  static bool CalcRegulationPhotoQualityFilterKey(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end) {
      auto photo_quality_score_accessor = context.GetDoubleItemAttr("ue_score");
      auto realshow_accessor = context.GetIntItemAttr("item_dynamic_info.nearby_real_show_count");
      std::string regulation_filiter_keys =
        context.GetStringCommonAttr("nearby_regulation_filiter_keys").value_or("").data();
      int64 regulation_base_realshow_count =
        context.GetIntCommonAttr("nearby_regulation_base_realshow_count").value_or(0);
      double nearby_regulation_max_cut_ratio =
        context.GetDoubleCommonAttr("nearby_regulation_max_cut_ratio").value_or(0.0);
      auto regulation_photo_quality_filter_set = context.SetIntItemAttr("regulation_photo_quality_filter");

      std::vector<int> realshow_thres;
      std::vector<double> quality_score_thres;
      std::vector<std::string> regulation_filiter_list;
      std::vector<std::string> temp;
      base::SplitStringWithOptions(regulation_filiter_keys, ",", true, true, &regulation_filiter_list);
      for (auto str : regulation_filiter_list) {
        temp.clear();
        base::SplitStringWithOptions(str, ":", true, true, &temp);
        if (temp.size() != 2) {
          continue;
        }
        int realshow = 0;
        double thres = 0.0;
        if (absl::SimpleAtoi(temp[0], &realshow) && absl::SimpleAtod(temp[1], &thres)) {
          realshow_thres.push_back(realshow);
          quality_score_thres.push_back(thres);
        }
      }
      if (realshow_thres.size() != quality_score_thres.size()) {
        return true;
      }

      int low_realshow_count = 0;
      int low_realshow_filter_count = 0;
      int other_realshow_count = 0;
      int other_realshow_filter_count = 0;
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto photo_quality_score = photo_quality_score_accessor(result).value_or(0.0);
        auto realshow = realshow_accessor(result).value_or(0);
        if (realshow <= regulation_base_realshow_count) {
          low_realshow_count++;
          return;
        } else {
          other_realshow_count++;
          bool hit_vv_count = false;
          for (int i = 0; i < realshow_thres.size(); ++i) {
            if (realshow <= realshow_thres[i]) {
              hit_vv_count = true;
              if (photo_quality_score <= quality_score_thres[i]) {
                other_realshow_filter_count++;
                regulation_photo_quality_filter_set(result, 1);
              } else {
                regulation_photo_quality_filter_set(result, 0);
              }
              break;
            }
          }
          if (hit_vv_count == false) {
            if (photo_quality_score <= nearby_regulation_max_cut_ratio) {
              other_realshow_filter_count++;
              regulation_photo_quality_filter_set(result, 1);
            } else {
              regulation_photo_quality_filter_set(result, 0);
            }
          }
        }
      });
      double rate = other_realshow_filter_count * 1.0 / (other_realshow_count + 1);
      base::PseudoRandom local_random(base::GetTimestamp());

      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto photo_quality_score = photo_quality_score_accessor(result).value_or(0.0);
        auto realshow = realshow_accessor(result).value_or(0);
        if (realshow <= regulation_base_realshow_count){
          if (local_random.GetDouble() <= rate) {
            low_realshow_filter_count++;
            regulation_photo_quality_filter_set(result, 1);
          } else {
            regulation_photo_quality_filter_set(result, 0);
          }
        }
      });

      std::string bizName("unknown");
      static const int64 U_PRODUCT_NEBULA = 1;
      static const int64 U_FEED_TYPE_SLIDE = 1;
      int64 uProductType = context.GetIntCommonAttr("uProductType").value_or(0);
      int64 uFeedType = context.GetIntCommonAttr("uFeedType").value_or(0);
      if (U_PRODUCT_NEBULA == uProductType) {
        if (U_FEED_TYPE_SLIDE == uFeedType) {
          bizName = "bl_nearby_in";
        } else {
          bizName = "bl_nearby";
        }
      } else {
        if (U_FEED_TYPE_SLIDE == uFeedType) {
          bizName = "nearby_in";
        } else {
          bizName = "nearby";
        }
      }
      base::perfutil::PerfUtilWrapper::IntervalLogStash(low_realshow_filter_count, "reco.nearby",
            bizName, "low_realshow_regulation_filter_count");
      base::perfutil::PerfUtilWrapper::IntervalLogStash(low_realshow_count, "reco.nearby",
            bizName, "low_realshow_regulation_all_count");
      base::perfutil::PerfUtilWrapper::IntervalLogStash(other_realshow_filter_count, "reco.nearby",
            bizName, "high_realshow_regulation_filter_count");
      base::perfutil::PerfUtilWrapper::IntervalLogStash(other_realshow_count, "reco.nearby",
            bizName, "high_realshow_regulation_all_count");
      return true;
    }
  static bool CalculateLocalPhotoFilterFlag(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end) {
      auto index_accessor = context.GetIntItemAttr("item_info.data_set_tag_bit");
      auto set_flag_accessor = context.SetIntItemAttr("local_photo_filter_flag");
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto local_life_photo_index = index_accessor(result).value_or(0);
        int64 mask = 1 << 1;
        if ((local_life_photo_index & mask) != 0) {
          set_flag_accessor(result, 1);
        } else {
          set_flag_accessor(result, 0);
        }
      });
      return true;
    }
  static bool CalDiversifySendMessageFlag(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end) {
      auto index_accessor = context.GetDoubleItemAttr("is_send_message_function");
      auto set_flag_accessor = context.SetIntItemAttr("diversify_send_message_flag");
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto attr_value = index_accessor(result).value_or(0.0);
        if (attr_value > 0.0) {
          set_flag_accessor(result, 1);
        }
      });
      return true;
    }

 private:
  DISALLOW_COPY_AND_ASSIGN(NearbyLightFunctionSet);
};

}  // namespace platform
}  // namespace ks
