#pragma once

#include <math.h>
#include <algorithm>
#include <cmath>
#include <ctime>
#include <limits>
#include <map>
#include <queue>
#include <random>
#include <sstream>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <memory>
#include <set>
#include <utility>
#include <vector>
#include <functional>
#include "dragon/src/module/common_reco_light_function.h"
#include "ks/realtime_reco/util/nearby_util.h"
#include "serving_base/region/region_dict.h"
#include "ks/realtime_reco/index/common_util.h"
#include "ks/reco_pub/reco/util/util.h"
#include "folly/container/F14Set.h"
#include "ks/realtime_reco/index/cdoc_convertor.h"
#include "ks/reco_proto/proto/mmu_user_category.pb.h"
#include "ks/reco_proto/proto/mmu_user_tag.pb.h"
#include "ks/base/abtest/abtest_instance.h"

namespace ks {
namespace platform {
// 为防止后面文件过大,该类按功能用于粗排截断之后的 enrich 操作
class NearbyLightFrFunctionSet : public CommonRecoBaseLightFunctionSet {
 public:
  NearbyLightFrFunctionSet() {
    REGISTER_LIGHT_FUNCTION(ReasonToExpTag);
    REGISTER_LIGHT_FUNCTION(EnrichSpecialRegionItem);
    REGISTER_LIGHT_FUNCTION(CalDiversifyItemType);
    REGISTER_LIGHT_FUNCTION(EnrichPhotoStrideVariantAttr);
    REGISTER_LIGHT_FUNCTION(CalBaseDiversifyItemType);
    REGISTER_LIGHT_FUNCTION(CalMainPhotoFrPxtr);
    REGISTER_LIGHT_FUNCTION(EnrichPhotoRelationDebiasPxtr);
    REGISTER_LIGHT_FUNCTION(AlterESWeight);
    REGISTER_LIGHT_FUNCTION(CalcCascadeAuc);
    REGISTER_LIGHT_FUNCTION(CalItemLivePreferenceMixScore);
    REGISTER_LIGHT_FUNCTION(CalItemLiveVtrPreferenceMixScore);
    REGISTER_LIGHT_FUNCTION(EnrichPhotoYoungBeautyTag);
    REGISTER_LIGHT_FUNCTION(CalcF1Auc);
    REGISTER_LIGHT_FUNCTION(CalcMcToFrAuc);
    REGISTER_LIGHT_FUNCTION(CalcLiveMcToFrAuc);
    REGISTER_LIGHT_FUNCTION(CalcFrToFinalAuc);
    REGISTER_LIGHT_FUNCTION(DisperseFrPxtr);
    REGISTER_LIGHT_FUNCTION(SetMixrankClmScore);
    REGISTER_LIGHT_FUNCTION(SetMixrankClmScoreV2);
    REGISTER_LIGHT_FUNCTION(SetItemWiseLtrScore);
    REGISTER_LIGHT_FUNCTION(SetMainPhotoItemWiseLtrScore);
    REGISTER_LIGHT_FUNCTION(CalItemDebiasScore);
    REGISTER_LIGHT_FUNCTION(EnrichPhotoFrContextAttr);
    REGISTER_LIGHT_FUNCTION(CalcMcFrConsistency);
    REGISTER_LIGHT_FUNCTION(CalcLiveMcFrConsistency);
    REGISTER_LIGHT_FUNCTION(CalPhotoReportValid);
    REGISTER_LIGHT_FUNCTION(CalcCascadeTaskConsistency);
    REGISTER_LIGHT_FUNCTION(DisperseSlideFrPxtr);
    REGISTER_LIGHT_FUNCTION(DisperseSlideRerankPxtr);
    REGISTER_LIGHT_FUNCTION(SetPhotoFrClmScore);
    REGISTER_LIGHT_FUNCTION(SetPhotoRctrScore);
    REGISTER_LIGHT_FUNCTION(CalSuperiorityScore);
    REGISTER_LIGHT_FUNCTION(SlideDiversityAttrEnrich);
    REGISTER_LIGHT_FUNCTION(CalcMcRerankConsistency);
    REGISTER_LIGHT_FUNCTION(FillSlideTopListSampleAttr);
    REGISTER_LIGHT_FUNCTION(CalHetuShowCntDebiasQueue);
    REGISTER_LIGHT_FUNCTION(SetPhotoFrLtrClmScore);
    REGISTER_LIGHT_FUNCTION(SetPhotoSlideRerankContextScore);
    REGISTER_LIGHT_FUNCTION(PerfPhotoFrEsScore);
    REGISTER_LIGHT_FUNCTION(CalMmuEmbeddingScore);
    REGISTER_LIGHT_FUNCTION(SetListWiseRerankScore);
    REGISTER_LIGHT_FUNCTION(FillTopListSampleAttr);
    REGISTER_LIGHT_FUNCTION(SetPhotoIsBeauty);
    REGISTER_LIGHT_FUNCTION(SetPhotoExpectPxtr);
    REGISTER_LIGHT_FUNCTION(CalAuthorUnClickScore);
    REGISTER_LIGHT_FUNCTION(CalClusterUnClickScore);
    REGISTER_LIGHT_FUNCTION(CalPhotoMigrateBoostScore);
    REGISTER_LIGHT_FUNCTION(CalSafeDiversityValue);
    REGISTER_LIGHT_FUNCTION(CalWtrDebiasFullRankScore);
    REGISTER_LIGHT_FUNCTION(CalMixInsideRankScore);
    REGISTER_LIGHT_FUNCTION(CalFollowBoostFullRankScore);
    REGISTER_LIGHT_FUNCTION(GetMcPhotoFrEsScore);
    REGISTER_LIGHT_FUNCTION(CalPubLastEmbAvg);
    REGISTER_LIGHT_FUNCTION(CalLiveMmuEmbScore);
    REGISTER_LIGHT_FUNCTION(SetLiveSearchTopAuthor);
    REGISTER_LIGHT_FUNCTION(SetLiveSearchTopAuthor7Day);
    REGISTER_LIGHT_FUNCTION(SetLiveAudBoardTarget);
    REGISTER_LIGHT_FUNCTION(SetMixRankListAttr);
    REGISTER_LIGHT_FUNCTION(CalLiveMmuEmbScoreV2);
    REGISTER_LIGHT_FUNCTION(PerfDiversityFlags);
    REGISTER_LIGHT_FUNCTION(SetLiveUnitPxtr);
    REGISTER_LIGHT_FUNCTION(EnrichPhotoNearFieldSupplyFactor);
    REGISTER_LIGHT_FUNCTION(EnrichPhotoPxtrCompetitiveDegree);
    REGISTER_LIGHT_FUNCTION(EnrichUserFollowPreferenceFactor);
    REGISTER_LIGHT_FUNCTION(FilterOtherSendMessageFromTopK);
    REGISTER_LIGHT_FUNCTION(CalNearFieldPhotoSupplyFactor);
    REGISTER_LIGHT_FUNCTION(EnrichPhotoConvolutionPxtr);
  }

struct GreaterBySecondType {
  bool operator()(const std::pair<CommonRecoResult, double> &a,
                  const std::pair<CommonRecoResult, double> &b) {
    return a.second > b.second;
  }
};
using NearbyPriorityQueue = std::priority_queue<std::pair<CommonRecoResult, double>,
  std::vector<std::pair<CommonRecoResult, double>>,
  GreaterBySecondType>;


static bool GetMcPhotoFrEsScore(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                RecoResultConstIter end) {
  auto photo_fr_es = context.GetDoubleListCommonAttr("photo_fr_es");
  auto photo_fr_id = context.GetIntListCommonAttr("photo_fr_id");
  int64 sample_valid = 0;
  int64 load_valid = 0;
  std::vector<double> photo_mc_fr_es_score;
  std::vector<int64> photo_mc_fr_es_rank;
  // 粗排的特征 - 检查长度是否相等
  auto photo_mc_id = context.GetIntListCommonAttr("photo_mc_id");
  auto photo_mc_duration_ms = context.GetIntListCommonAttr("photo_mc_duration_ms");
  auto photo_mc_user_age_segment = context.GetStringListCommonAttr("photo_mc_user_age_segment");
  auto photo_mc_gender = context.GetIntListCommonAttr("photo_mc_gender");
  auto photo_mc_hetu_level_one = context.GetIntListCommonAttr("photo_mc_hetu_level_one");
  auto photo_mc_hetu_level_two = context.GetIntListCommonAttr("photo_mc_hetu_level_two");
  auto photo_mc_author_id = context.GetIntListCommonAttr("photo_mc_author_id");
  auto photo_mc_plvtr = context.GetDoubleListCommonAttr("photo_mc_plvtr");
  auto photo_mc_plvtr_dis = context.GetIntListCommonAttr("photo_mc_plvtr_dis");
  auto photo_mc_plvtr_rank = context.GetIntListCommonAttr("photo_mc_plvtr_rank");
  auto photo_mc_pfvtr = context.GetDoubleListCommonAttr("photo_mc_pfvtr");
  auto photo_mc_pfvtr_dis = context.GetIntListCommonAttr("photo_mc_pfvtr_dis");
  auto photo_mc_pfvtr_rank = context.GetIntListCommonAttr("photo_mc_pfvtr_rank");
  auto photo_mc_pvtr = context.GetDoubleListCommonAttr("photo_mc_pvtr");
  auto photo_mc_pvtr_dis = context.GetIntListCommonAttr("photo_mc_pvtr_dis");
  auto photo_mc_pvtr_rank = context.GetIntListCommonAttr("photo_mc_pvtr_rank");
  auto photo_mc_pwtd = context.GetDoubleListCommonAttr("photo_mc_pwtd");
  auto photo_mc_pwtd_dis = context.GetIntListCommonAttr("photo_mc_pwtd_dis");
  auto photo_mc_pwtd_rank = context.GetIntListCommonAttr("photo_mc_pwtd_rank");
  auto photo_mc_pcmtr = context.GetDoubleListCommonAttr("photo_mc_pcmtr");
  auto photo_mc_pcmtr_dis = context.GetIntListCommonAttr("photo_mc_pcmtr_dis");
  auto photo_mc_pcmtr_rank = context.GetIntListCommonAttr("photo_mc_pcmtr_rank");
  auto photo_mc_pltr = context.GetDoubleListCommonAttr("photo_mc_pltr");
  auto photo_mc_pltr_dis = context.GetIntListCommonAttr("photo_mc_pltr_dis");
  auto photo_mc_pltr_rank = context.GetIntListCommonAttr("photo_mc_pltr_rank");
  auto photo_mc_pwtr = context.GetDoubleListCommonAttr("photo_mc_pwtr");
  auto photo_mc_pwtr_dis = context.GetIntListCommonAttr("photo_mc_pwtr_dis");
  auto photo_mc_pwtr_rank = context.GetIntListCommonAttr("photo_mc_pwtr_rank");
  auto photo_mc_es = context.GetDoubleListCommonAttr("photo_mc_es");
  auto photo_mc_es_dis = context.GetIntListCommonAttr("photo_mc_es_dis");
  auto photo_mc_es_rank = context.GetIntListCommonAttr("photo_mc_es_rank");

  if (!photo_fr_es) {
    LOG(ERROR) << "photo_fr_es is null";
  } else if (!photo_fr_id) {
    LOG(ERROR) << "photo_fr_id is null";
  } else if (!photo_mc_id) {
    LOG(ERROR) << "photo_mc_id is null";
  } else if (photo_mc_duration_ms && photo_mc_duration_ms->size() != 0 &&
             photo_mc_duration_ms->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_duration_ms size:" << photo_mc_duration_ms->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_user_age_segment && photo_mc_user_age_segment->size() != 0 &&
             photo_mc_user_age_segment->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_user_age_segment size:" << photo_mc_user_age_segment->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_gender && photo_mc_gender->size() != 0 &&
             photo_mc_gender->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_gender size:" << photo_mc_gender->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_hetu_level_one && photo_mc_hetu_level_one->size() != 0 &&
             photo_mc_hetu_level_one->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_hetu_level_one size:" << photo_mc_hetu_level_one->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_hetu_level_two && photo_mc_hetu_level_two->size() != 0 &&
             photo_mc_hetu_level_two->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_hetu_level_two size:" << photo_mc_hetu_level_two->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_author_id && photo_mc_author_id->size() != 0 &&
             photo_mc_author_id->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_author_id size:" << photo_mc_author_id->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_plvtr && photo_mc_plvtr->size() != 0 && photo_mc_plvtr->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_plvtr size:" << photo_mc_plvtr->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_plvtr_dis && photo_mc_plvtr_dis->size() != 0 &&
             photo_mc_plvtr_dis->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_plvtr_dis size:" << photo_mc_plvtr_dis->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_plvtr_rank && photo_mc_plvtr_rank->size() != 0 &&
             photo_mc_plvtr_rank->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_plvtr_rank size:" << photo_mc_plvtr_rank->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();

  } else if (photo_mc_pfvtr && photo_mc_pfvtr->size() != 0 && photo_mc_pfvtr->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_pfvtr size:" << photo_mc_pfvtr->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_pfvtr_dis && photo_mc_pfvtr_dis->size() != 0 &&
             photo_mc_pfvtr_dis->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_pfvtr_dis size:" << photo_mc_pfvtr_dis->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_pfvtr_rank && photo_mc_pfvtr_rank->size() != 0 &&
             photo_mc_pfvtr_rank->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_pfvtr_rank size:" << photo_mc_pfvtr_rank->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();

  } else if (photo_mc_pwtd && photo_mc_pwtd->size() != 0 && photo_mc_pwtd->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_pwtd size:" << photo_mc_pwtd->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_pwtd_dis && photo_mc_pwtd_dis->size() != 0 &&
             photo_mc_pwtd_dis->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_pwtd_dis size:" << photo_mc_pwtd_dis->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_pwtd_rank && photo_mc_pwtd_rank->size() != 0 &&
             photo_mc_pwtd_rank->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_pwtd_rank size:" << photo_mc_pwtd_rank->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_pvtr && photo_mc_pvtr->size() != 0 && photo_mc_pvtr->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_pvtr size:" << photo_mc_pvtr->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_pvtr_dis && photo_mc_pvtr_dis->size() != 0 &&
             photo_mc_pvtr_dis->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_pvtr_dis size:" << photo_mc_pvtr_dis->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_pvtr_rank && photo_mc_pvtr_rank->size() != 0 &&
             photo_mc_pvtr_rank->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_pvtr_rank size:" << photo_mc_pvtr_rank->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_pltr && photo_mc_pltr->size() != 0 && photo_mc_pltr->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_pltr size:" << photo_mc_pltr->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_pltr_dis && photo_mc_pltr_dis->size() != 0 &&
             photo_mc_pltr_dis->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_pltr_dis size:" << photo_mc_pltr_dis->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_pltr_rank && photo_mc_pltr_rank->size() != 0 &&
             photo_mc_pltr_rank->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_pltr_rank size:" << photo_mc_pltr_rank->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_pcmtr && photo_mc_pcmtr->size() != 0 && photo_mc_pcmtr->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_pcmtr size:" << photo_mc_pcmtr->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_pcmtr_dis && photo_mc_pcmtr_dis->size() != 0 &&
             photo_mc_pcmtr_dis->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_pcmtr_dis size:" << photo_mc_pcmtr_dis->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_pcmtr_rank && photo_mc_pcmtr_rank->size() != 0 &&
             photo_mc_pcmtr_rank->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_pcmtr_rank size:" << photo_mc_pcmtr_rank->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_pwtr && photo_mc_pwtr->size() != 0 && photo_mc_pwtr->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_pwtr size:" << photo_mc_pwtr->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_pwtr_dis && photo_mc_pwtr_dis->size() != 0 &&
             photo_mc_pwtr_dis->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_pwtr_dis size:" << photo_mc_pwtr_dis->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_pwtr_rank && photo_mc_pwtr_rank->size() != 0 &&
             photo_mc_pwtr_rank->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_pwtr_rank size:" << photo_mc_pwtr_rank->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_es && photo_mc_es->size() != 0 && photo_mc_es->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_es size:" << photo_mc_es->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_es_dis && photo_mc_es_dis->size() != 0 &&
             photo_mc_es_dis->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_es_dis size:" << photo_mc_es_dis->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else if (photo_mc_es_rank && photo_mc_es_rank->size() != 0 &&
             photo_mc_es_rank->size() != photo_mc_id->size()) {
    LOG(ERROR) << "photo_mc_es_rank size:" << photo_mc_es_rank->size()
               << " mismatch photo_mc_id size:" << photo_mc_id->size();
  } else {
    sample_valid = 1;
    load_valid = photo_mc_id->size() > 280 ? 1 : 0;

    int64 fr_size = photo_fr_es->size();
    int64 fr_pid = 0;
    double fr_es = 0.0;
    std::unordered_map<int64, double> fr_id_es_map;
    for (int i = 0; i < fr_size; ++i) {
      fr_pid = photo_fr_id->at(i);
      fr_es = photo_fr_es->at(i);
      fr_id_es_map[fr_pid] = fr_es;
    }

    int64 mc_size = photo_mc_id->size();
    int64 mc_pid = 0;
    for (int i = 0; i < mc_size; ++i) {
      mc_pid = photo_mc_id->at(i);
      auto iter = fr_id_es_map.find(mc_pid);
      if (iter != fr_id_es_map.end())
        photo_mc_fr_es_score.push_back(iter->second);
      else
        photo_mc_fr_es_score.push_back(0);
    }

    photo_mc_fr_es_rank.resize(mc_size, 0);
    for (int i = 1; i < mc_size; i++) {
      for (int j = 0; j < i; j++) {
        if (photo_mc_fr_es_score[j] > photo_mc_fr_es_score[i])
          photo_mc_fr_es_rank[i]++;
        else
          photo_mc_fr_es_rank[j]++;
      }
    }

    context.SetDoubleListCommonAttr("photo_mc_fr_es_score", std::move(photo_mc_fr_es_score));
    context.SetIntListCommonAttr("photo_mc_fr_es_rank", std::move(photo_mc_fr_es_rank));
  }
  context.SetIntCommonAttr("sample_valid", sample_valid);
  context.SetIntCommonAttr("load_valid", load_valid);

  return true;
}
  static bool PerfPhotoFrEsScore(
    const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
      int channel_score_rank_bound =
        context.GetIntCommonAttr("photo_fr_channel_score_rank_bound").value_or(30);
      std::string channel_score_rank_exp = std::string(
        context.GetStringCommonAttr("photo_fr_channel_score_rank_exp_name").value_or("default"));
      auto channel_score_rank_list = context.GetStringListCommonAttr("channel_score_rank_list");
      static const int64 U_PRODUCT_NEBULA = 1;
      static const int64 U_FEED_TYPE_SLIDE = 1;
      std::string bizName("unknown");
      int64 uProductType = context.GetIntCommonAttr("uProductType").value_or(0);
      int64 uFeedType = context.GetIntCommonAttr("uFeedType").value_or(0);
      if (U_PRODUCT_NEBULA == uProductType) {
        if (U_FEED_TYPE_SLIDE == uFeedType) {
          bizName = "bl_nearby_in";
        } else {
          bizName = "bl_nearby";
        }
      } else {
        if (U_FEED_TYPE_SLIDE == uFeedType) {
          bizName = "nearby_in";
        } else {
          bizName = "nearby";
        }
      }
      for (int j = 0; j < channel_score_rank_list->size(); j++) {
        std::string cur_channel_name = std::string(channel_score_rank_list->at(j));
        auto score_rank_result = context.GetDoubleListCommonAttr(cur_channel_name + "_list");
        if (score_rank_result) {
          int length = score_rank_result->size();
          int top_overlap = 0;
          int bottom_overlap = 0;
          for (int i = 0; i < std::min(channel_score_rank_bound, length); i++) {
            if (score_rank_result->at(i) < std::min(channel_score_rank_bound, length)) {
              top_overlap += 1;
            }
          }
          for (int i = length-1; i >= std::max(0, length-channel_score_rank_bound - 1); i--) {
            if (score_rank_result->at(i) >= std::max(0, length-channel_score_rank_bound - 1)) {
              bottom_overlap += 1;
            }
          }
          base::perfutil::PerfUtilWrapper::IntervalLogStash(top_overlap, "reco.nearby",
            bizName, "channel_score_rank", bizName + ".top_overlap",
            cur_channel_name + "_" + channel_score_rank_exp);
          base::perfutil::PerfUtilWrapper::IntervalLogStash(bottom_overlap, "reco.nearby",
            bizName, "channel_score_rank", bizName + ".bottom_overlap",
            cur_channel_name + "_" + channel_score_rank_exp);
        }
      }
      return true;
    }

  static bool CalDiversifyItemType(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    std::unordered_set<int64> last_session_photo_set;
    std::unordered_set<int64> last_session_live_set;
    auto last_session_photo_list = context.GetIntListCommonAttr("last_session_photo_list");
    auto last_session_live_list = context.GetIntListCommonAttr("last_session_live_list");
    if (last_session_photo_list) {
      for (auto &item_key : *last_session_photo_list) {
        last_session_photo_set.insert(item_key);
      }
    }
    if (last_session_live_list) {
      for (auto &item_key : *last_session_live_list) {
        last_session_live_set.insert(item_key);
      }
    }
    auto is_photo_set = context.SetIntItemAttr("is_photo");
    auto is_live_set = context.SetIntItemAttr("is_live");
    int live_count = 0;
    int photo_count = 0;
    std::for_each(begin, end, [=, &live_count, &photo_count](const CommonRecoResult &result) {
      if (last_session_photo_set.count(result.item_key)) {
        is_photo_set(result, 1);
      }
      if (last_session_live_set.count(result.item_key)) {
        is_live_set(result, 1);
      }
    });
    return true;
  }

  static bool ReasonToExpTag(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    static const std::unordered_map<int, int> exptag_map =
        { {13, 90}, {22, 92}, {17, 91}, {24, 94}, {25, 93}, {39, 95},
            {110, 907}, {148, 79}, {124, 279}, {123, 278} };
    auto pReason_accessor = context.GetIntItemAttr("pReason");
    auto item_reason_list_accessor = context.GetIntListItemAttr("item_reason_list");

    auto exp_tag_set = context.SetIntItemAttr("exp_tag");
    auto deduplicate_reason_list_set = context.SetIntListItemAttr("deduplicate_reason_list");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      int exp_tag = pReason_accessor(result).value_or(86);
      auto iter = exptag_map.find(exp_tag);
      if (iter != exptag_map.end()) {
          exp_tag = iter->second;
      }
      exp_tag_set(result, exp_tag);
      auto item_reason_list = item_reason_list_accessor(result);
      std::unordered_set<int64> exptag_set;
      if (item_reason_list && item_reason_list->size() > 0) {
        for (int i = 0; i < item_reason_list->size(); i++) {
          exptag_set.insert(item_reason_list->at(i));
        }
      }
      std::vector<int64> deduplicate_reason_list(exptag_set.begin(), exptag_set.end());
      deduplicate_reason_list_set(result, deduplicate_reason_list);
    });
    return true;
  }

  static bool EnrichSpecialRegionItem(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto is_travel_region_item_accessor = context.GetIntItemAttr("is_travel_region_item");
    auto is_special_region_item_accessor = context.GetIntItemAttr("is_special_region_item");
    auto is_travel_tag_accessor = context.GetIntItemAttr("is_travel_tag");
    auto is_special_travel_region_item_set = context.SetIntItemAttr("is_special_travel_region_item");

    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      int is_travel_region_item =
        is_travel_region_item_accessor(result).value_or(0) * is_travel_tag_accessor(result).value_or(0);
      int is_special_region_item = is_special_region_item_accessor(result).value_or(0);
      if (is_travel_region_item + is_special_region_item > 0) {
        is_special_travel_region_item_set(result, 1);
      }
    });
    return true;
  }

  static bool EnrichPhotoStrideVariantAttr(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    static const std::unordered_set<uint64> municipality_set = {16842752, 17367040, 18219008, 16908288};
    int64 user_city_id = context.GetIntCommonAttr("user_city_id").value_or(0);
    int64 id_user_province = context.GetIntCommonAttr("user_province_id").value_or(0);
    auto item_city_id_accessor = context.GetIntItemAttr("item_city_id");
    auto distance_km_accessor = context.GetDoubleItemAttr("pLeafDistance");

    auto is_photo_distance_city_diversity_set = context.SetIntItemAttr("is_photo_distance_city_diversity");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      int64 item_city_id = item_city_id_accessor(result).value_or(0);
      bool is_same_city = item_city_id > 0 && item_city_id == user_city_id;
      bool is_municipality = municipality_set.find(id_user_province) != municipality_set.end();
      double distance_km = distance_km_accessor(result).value_or(0.0);
      bool is_photo_distance_city_diversity = (!is_municipality) && (distance_km > 50.0) && (!is_same_city);
      if (is_photo_distance_city_diversity > 0) {
        is_photo_distance_city_diversity_set(result, 1);  // 短视频远距离非同城打散
      }
    });
    return true;
  }

  static bool CalBaseDiversifyItemType(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto is_photo_set = context.SetIntItemAttr("is_photo");
    auto is_live_set = context.SetIntItemAttr("is_live");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      if (result.GetType() == ks::reco::RecoEnum::ITEM_TYPE_PHOTO) {
        is_photo_set(result, 1);
      } else {
        is_live_set(result, 1);
      }
    });
    return true;
  }

  static bool EnrichPhotoRelationDebiasPxtr(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    static const std::unordered_set<int> relation_source_set =
        {90, 91, 92, 93, 94, 95, 907, 153, 287, 79, 314, 312, 313, 277, 278};
    double nvtr_relation_debias_coeff =
            context.GetDoubleCommonAttr("photo_nvtr_relation_debias_coeff").value_or(1.0);
    double lvtr_relation_debias_coeff =
            context.GetDoubleCommonAttr("photo_lvtr_relation_debias_coeff").value_or(1.0);

    auto fullrank_relation_debias_nvtr_set = context.SetDoubleItemAttr("fullrank_relation_debias_nvtr");
    auto fullrank_relation_debias_lvtr_set = context.SetDoubleItemAttr("fullrank_relation_debias_lvtr");

    auto fullrank_ori_nvtr_accessor = context.GetDoubleItemAttr("fullrank_ori_nvtr");
    auto fullrank_ori_lvtr_accessor = context.GetDoubleItemAttr("fullrank_ori_lvtr");
    auto exp_tag_accessor = context.GetIntItemAttr("exp_tag");
    std::for_each(begin, end, [=, &nvtr_relation_debias_coeff,
           &lvtr_relation_debias_coeff](const CommonRecoResult &result) {
      int exp_tag = exp_tag_accessor(result).value_or(0);
      double fullrank_relation_debias_nvtr = fullrank_ori_nvtr_accessor(result).value_or(0.0);
      double fullrank_relation_debias_lvtr = fullrank_ori_lvtr_accessor(result).value_or(0.0);
      if (relation_source_set.count(exp_tag) || (exp_tag >= 1000 && exp_tag <= 2000)) {
        fullrank_relation_debias_nvtr = fullrank_relation_debias_nvtr * nvtr_relation_debias_coeff;
        fullrank_relation_debias_lvtr = fullrank_relation_debias_nvtr * lvtr_relation_debias_coeff;
      }
      fullrank_relation_debias_nvtr_set(result, fullrank_relation_debias_nvtr);
      fullrank_relation_debias_lvtr_set(result, fullrank_relation_debias_lvtr);
    });
    return true;
  }
  static bool AlterESWeight(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    std::string user_play_posture =
              std::string(context.GetStringCommonAttr("user_play_posture").value_or(""));
    if (context.GetIntCommonAttr("enable_user_play_posture_alter_es_weight").value_or(0) > 0) {
      double photo_main_fr_weight_fullrank_ori_nvtr =
              context.GetDoubleCommonAttr("photo_main_fr_weight_fullrank_ori_nvtr").value_or(0.0);
      double user_play_posture_nvtr_boost_coeff =
              context.GetDoubleCommonAttr("user_play_posture_nvtr_boost_coeff").value_or(1.0);
      double user_play_posture_nvtr_descend_coeff =
              context.GetDoubleCommonAttr("user_play_posture_nvtr_descend_coeff").value_or(1.0);
      if ("AWAY_FROM_HAND" == user_play_posture) {
        photo_main_fr_weight_fullrank_ori_nvtr *= user_play_posture_nvtr_boost_coeff;
      } else if ("WALKING" == user_play_posture) {
        photo_main_fr_weight_fullrank_ori_nvtr *= user_play_posture_nvtr_descend_coeff;
      }
      context.SetDoubleCommonAttr("photo_main_fr_weight_fullrank_ori_nvtr",
                                  photo_main_fr_weight_fullrank_ori_nvtr);
    }
    if (context.GetIntCommonAttr("enable_user_play_posture_alter_live_weight").value_or(0) > 0) {
      double nearby_main_live_boost_weight =
              context.GetDoubleCommonAttr("nearby_main_live_boost_weight").value_or(1.0);
      double user_play_posture_live_boost_coeff =
              context.GetDoubleCommonAttr("user_play_posture_live_boost_coeff").value_or(1.0);
      double user_play_posture_live_descend_coeff =
              context.GetDoubleCommonAttr("user_play_posture_live_descend_coeff").value_or(1.0);
      if ("AWAY_FROM_HAND" == user_play_posture) {
        nearby_main_live_boost_weight *= user_play_posture_live_boost_coeff;
      } else if ("LAYING" == user_play_posture) {
        nearby_main_live_boost_weight *= user_play_posture_live_descend_coeff;
      }
      context.SetDoubleCommonAttr("nearby_main_live_boost_weight",
                                  nearby_main_live_boost_weight);
    }
    if (context.GetIntCommonAttr("enable_zero_shot_alter_es_weight").value_or(0) > 0) {
      int daily_zero_shot_user =
              context.GetIntCommonAttr("daily_zero_shot_user").value_or(0);
      int weekly_zero_shot_user =
              context.GetIntCommonAttr("weekly_zero_shot_user").value_or(0);
      double photo_main_fr_weight_fullrank_ori_ctr =
              context.GetDoubleCommonAttr("photo_main_fr_weight_fullrank_ori_ctr").value_or(0.0);
      double weekly_zero_shot_user_ctr_boost_coeff =
              context.GetDoubleCommonAttr("weekly_zero_shot_user_ctr_boost_coeff").value_or(1.0);
      double daily_zero_shot_user_ctr_boost_coeff =
              context.GetDoubleCommonAttr("daily_zero_shot_user_ctr_boost_coeff").value_or(1.0);
      if (weekly_zero_shot_user > 0) {
        photo_main_fr_weight_fullrank_ori_ctr *= weekly_zero_shot_user_ctr_boost_coeff;
      } else if (daily_zero_shot_user > 0) {
        weekly_zero_shot_user_ctr_boost_coeff *= daily_zero_shot_user_ctr_boost_coeff;
      }
      context.SetDoubleCommonAttr("photo_main_fr_weight_fullrank_ori_ctr",
                                  photo_main_fr_weight_fullrank_ori_ctr);
    }
    return true;
  }
  static bool CalMainPhotoFrPxtr(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto fr_ctr_list = context.GetIntListCommonAttr("fr_ctr_list");
    std::vector<double> fr_ctr_list_sorted;
    double fr_ctr_quantile = 0.005;
    if (fr_ctr_list) {
      for (auto &fr_ctr : *fr_ctr_list) {
        fr_ctr_list_sorted.push_back(fr_ctr);
      }
      std::sort(fr_ctr_list_sorted.begin(), fr_ctr_list_sorted.end());
      int index = floor(fr_ctr_list_sorted.size() * 0.7);
      fr_ctr_quantile = fr_ctr_list_sorted.at(index);
      context.SetDoubleListCommonAttr("fr_ctr_list_sorted",
                            std::move(fr_ctr_list_sorted));
    }
    context.SetDoubleCommonAttr("fr_ctr_quantile", fr_ctr_quantile);

    auto fullrank_ori_ctr_accessor = context.GetDoubleItemAttr("fullrank_ori_ctr");
    auto fullrank_ori_lvtr_accessor = context.GetDoubleItemAttr("fullrank_ori_lvtr");
    auto fullrank_ori_nvtr_accessor = context.GetDoubleItemAttr("fullrank_ori_nvtr");
    auto fullrank_ori_wtd_accessor = context.GetDoubleItemAttr("fullrank_ori_wtd");
    auto fullrank_ori_cmtr_accessor = context.GetDoubleItemAttr("fullrank_ori_cmtr");

    auto fr_quantile_lvtr_set = context.SetDoubleItemAttr("fr_quantile_lvtr");
    auto fr_quantile_cmtr_set = context.SetDoubleItemAttr("fr_quantile_cmtr");
    auto fr_nvtr_score_set = context.SetDoubleItemAttr("fr_nvtr_score");
    auto fr_wtd_score_set = context.SetDoubleItemAttr("fr_wtd_score");

    std::for_each(begin, end, [=, &fr_ctr_quantile](const CommonRecoResult &result) {
      auto ctr = fullrank_ori_ctr_accessor(result).value_or(0.0);
      auto fr_quantile_lvtr = fullrank_ori_lvtr_accessor(result).value_or(0.0);
      auto fr_nvtr_score = fullrank_ori_nvtr_accessor(result).value_or(0.0);
      auto fr_wtd_score = fullrank_ori_wtd_accessor(result).value_or(0.0);
      auto fr_quantile_cmtr = fullrank_ori_cmtr_accessor(result).value_or(0.0);
      fr_wtd_score = std::min(100.0, std::max(fr_wtd_score, 0.00001));
      fr_wtd_score = fr_wtd_score * ctr;
      fr_nvtr_score = std::min(100.0, std::max(fr_nvtr_score, 0.00001));
      fr_nvtr_score = fr_nvtr_score * ctr;
      if (ctr > fr_ctr_quantile) {
        fr_quantile_lvtr = 10.0 + fr_quantile_lvtr;
        fr_quantile_cmtr = 10.0 + fr_quantile_cmtr;
      }
      fr_quantile_lvtr_set(result, fr_quantile_lvtr);
      fr_quantile_cmtr_set(result, fr_quantile_cmtr);
      fr_nvtr_score_set(result, fr_nvtr_score);
      fr_wtd_score_set(result, fr_wtd_score);
    });
    return true;
  }
  // 暴力计算每个通道分对最终排序分的 AUC, 由于复杂度为 k * n * n, 暂时只在测试环境使用
  static bool CalcF1Auc(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto final_ensemble_score_accessor = context.GetDoubleItemAttr("final_ensemble_score");
    auto f1_ctr_score_accessor = context.GetDoubleItemAttr("f1_ctr_score");
    auto f1_wtr_score_accessor = context.GetDoubleItemAttr("f1_wtr_score");
    auto f1_gbdt_score_accessor = context.GetDoubleItemAttr("f1_gbdt_score");
    auto f1_wtd_score_accessor = context.GetDoubleItemAttr("f1_wtd_score");
    auto f1_lvtr_score_accessor = context.GetDoubleItemAttr("f1_lvtr_score");
    auto f1_cmtr_score_accessor = context.GetDoubleItemAttr("f1_cmtr_score");
    auto f1_ltr_score_accessor = context.GetDoubleItemAttr("f1_ltr_score");
    auto f1_nvtr_score_accessor = context.GetDoubleItemAttr("f1_nvtr_score");
    double all_pair = 0.0;
    double pos_pair_ctr = 0.0;
    double pos_pair_wtr = 0.0;
    double pos_pair_gbdt = 0.0;
    double pos_pair_wtd = 0.0;
    double pos_pair_lvtr = 0.0;
    double pos_pair_cmtr = 0.0;
    double pos_pair_ltr = 0.0;
    double pos_pair_nvtr = 0.0;
    int max_len = context.GetIntCommonAttr("main_photo_frf1_auc_limit_num").value_or(100);
    int index1 = 0;
    for (auto result = begin; result != end; ++result) {
      if (index1 >= max_len) {
        break;
      }
      auto final_ensemble_score = final_ensemble_score_accessor(*result).value_or(0.0);
      auto f1_ctr_score = f1_ctr_score_accessor(*result).value_or(0.0);
      auto f1_wtr_score = f1_wtr_score_accessor(*result).value_or(0.0);
      auto f1_gbdt_score = f1_gbdt_score_accessor(*result).value_or(0.0);
      auto f1_wtd_score = f1_wtd_score_accessor(*result).value_or(0.0);
      auto f1_lvtr_score = f1_lvtr_score_accessor(*result).value_or(0.0);
      auto f1_cmtr_score = f1_cmtr_score_accessor(*result).value_or(0.0);
      auto f1_ltr_score = f1_ltr_score_accessor(*result).value_or(0.0);
      auto f1_nvtr_score = f1_nvtr_score_accessor(*result).value_or(0.0);
      index1++;
      int index2 = index1;
      for (auto result2 = begin; result2 != end; ++result2) {
        if (index2 >= max_len) {
          break;
        }
        auto final_ensemble_score2 = final_ensemble_score_accessor(*result2).value_or(0.0);
        auto f1_ctr_score2 = f1_ctr_score_accessor(*result2).value_or(0.0);
        auto f1_wtr_score2 = f1_wtr_score_accessor(*result2).value_or(0.0);
        auto f1_gbdt_score2 = f1_gbdt_score_accessor(*result2).value_or(0.0);
        auto f1_wtd_score2 = f1_wtd_score_accessor(*result2).value_or(0.0);
        auto f1_lvtr_score2 = f1_lvtr_score_accessor(*result2).value_or(0.0);
        auto f1_cmtr_score2 = f1_cmtr_score_accessor(*result2).value_or(0.0);
        auto f1_ltr_score2 = f1_ltr_score_accessor(*result2).value_or(0.0);
        auto f1_nvtr_score2 = f1_nvtr_score_accessor(*result2).value_or(0.0);
        bool cmp = (final_ensemble_score2 > final_ensemble_score);
        if (cmp == (f1_ctr_score2 > f1_ctr_score)) {
          pos_pair_ctr = pos_pair_ctr + 1.0;
        }
        if (cmp == (f1_wtr_score2 > f1_wtr_score)) {
          pos_pair_wtr = pos_pair_wtr + 1.0;
        }
        if (cmp == (f1_gbdt_score2 > f1_gbdt_score)) {
          pos_pair_gbdt = pos_pair_gbdt + 1.0;
        }
        if (cmp == (f1_wtd_score2 > f1_wtd_score)) {
          pos_pair_wtd = pos_pair_wtd + 1.0;
        }
        if (cmp == (f1_lvtr_score2 > f1_lvtr_score)) {
          pos_pair_lvtr = pos_pair_lvtr + 1.0;
        }
        if (cmp == (f1_cmtr_score2 > f1_cmtr_score)) {
          pos_pair_cmtr = pos_pair_cmtr + 1.0;
        }
        if (cmp == (f1_ltr_score2 > f1_ltr_score)) {
          pos_pair_ltr = pos_pair_ltr + 1.0;
        }
        if (cmp == (f1_nvtr_score2 > f1_nvtr_score)) {
          pos_pair_nvtr = pos_pair_nvtr + 1.0;
        }
        all_pair = all_pair + 1.0;
      }
    }
    double photo_frf1_ctr_auc = pos_pair_ctr / (all_pair + 0.0001);
    double photo_frf1_wtr_auc = pos_pair_wtr / (all_pair + 0.0001);
    double photo_frf1_gbdt_auc = pos_pair_gbdt / (all_pair + 0.0001);
    double photo_frf1_wtd_auc = pos_pair_wtd / (all_pair + 0.0001);
    double photo_frf1_lvtr_auc = pos_pair_lvtr / (all_pair + 0.0001);
    double photo_frf1_cmtr_auc = pos_pair_cmtr / (all_pair + 0.0001);
    double photo_frf1_ltr_auc = pos_pair_ltr / (all_pair + 0.0001);
    double photo_frf1_nvtr_auc = pos_pair_nvtr / (all_pair + 0.0001);
    context.SetDoubleCommonAttr("photo_frf1_ctr_auc", photo_frf1_ctr_auc);
    context.SetDoubleCommonAttr("photo_frf1_wtr_auc", photo_frf1_wtr_auc);
    context.SetDoubleCommonAttr("photo_frf1_gbdt_auc", photo_frf1_gbdt_auc);
    context.SetDoubleCommonAttr("photo_frf1_wtd_auc", photo_frf1_wtd_auc);
    context.SetDoubleCommonAttr("photo_frf1_lvtr_auc", photo_frf1_lvtr_auc);
    context.SetDoubleCommonAttr("photo_frf1_cmtr_auc", photo_frf1_cmtr_auc);
    context.SetDoubleCommonAttr("photo_frf1_ltr_auc", photo_frf1_ltr_auc);
    context.SetDoubleCommonAttr("photo_frf1_nvtr_auc", photo_frf1_nvtr_auc);
    return true;
  }
  // 暴力计算粗排排序分对精排排序分的 AUC
  static bool CalcMcToFrAuc(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto final_ensemble_score_accessor = context.GetDoubleItemAttr("final_ensemble_score");
    auto cascade_ensemble_score_accessor = context.GetDoubleItemAttr("cascade_ensemble_score");
    double all_pair = 0.0;
    double pos_pair = 0.0;
    int64 max_len = context.GetIntCommonAttr("photo_mc_to_fr_auc_limit_num").value_or(100);
    int index1 = 0;
    if (begin + 1 >= end) {
      return false;
    }
    for (auto result = begin; result != end - 1; ++result) {
      if (index1 >= max_len) {
        break;
      }
      auto final_ensemble_score = final_ensemble_score_accessor(*result).value_or(0.0);
      auto cascade_ensemble_score = cascade_ensemble_score_accessor(*result).value_or(0.0);
      index1++;
      int index2 = index1;
      for (auto result2 = begin + index1; result2 != end; ++result2) {
        if (index2 >= max_len) {
          break;
        }
        auto final_ensemble_score2 = final_ensemble_score_accessor(*result2).value_or(0.0);
        auto cascade_ensemble_score2 = cascade_ensemble_score_accessor(*result2).value_or(0.0);
        bool cmp = (final_ensemble_score2 > final_ensemble_score);
        if (cmp == (cascade_ensemble_score2 > cascade_ensemble_score)) {
          pos_pair += 1.0;
        }
        index2++;
        all_pair = all_pair + 1.0;
      }
    }
    double photo_mc_to_fr_auc = pos_pair/ (all_pair + 0.0001);
    context.SetDoubleCommonAttr("photo_mc_to_fr_auc", photo_mc_to_fr_auc);

    struct PlayItem {
      int64 photo_id = -1;
      double mc_score = 0.0;
      double fr_score = 0.0;
      PlayItem(int64 pid, double mc, double fr)
          : photo_id(pid), mc_score(mc), fr_score(fr) {}
      PlayItem() : photo_id(-1), mc_score(0.0), fr_score(0.0) {}
    };
    std::vector<PlayItem> play_item_list;
    for (auto result = begin; result != end; ++result) {
      auto final_ensemble_score = final_ensemble_score_accessor(*result).value_or(0.0);
      auto cascade_ensemble_score = cascade_ensemble_score_accessor(*result).value_or(0.0);
      play_item_list.emplace_back(result->GetId(), cascade_ensemble_score, final_ensemble_score);
    }
    if (play_item_list.size() < 10) {
      return false;
    }
    std::stable_sort(play_item_list.begin(), play_item_list.end(),
      [] (const PlayItem& a, const PlayItem& b) -> bool {
        return a.fr_score > b.fr_score;
      });

    std::unordered_set<int64> fr_top10_item;
    for (int i = 0; i < play_item_list.size() && i < 10; i++) {
      fr_top10_item.insert(play_item_list[i].photo_id);
    }
    std::stable_sort(play_item_list.begin(), play_item_list.end(),
      [] (const PlayItem& a, const PlayItem& b) -> bool {
        return a.mc_score > b.mc_score;
      });
    double mc_top10_hit_num = 0.0;
    double mc_top50_hit_num = 0.0;
    double mc_top100_hit_num = 0.0;
    for (int i = 0; i < play_item_list.size(); i++) {
      if (fr_top10_item.count(play_item_list[i].photo_id) > 0) {
        if (i < 10) {
          mc_top10_hit_num += 1.0;
          mc_top50_hit_num += 1.0;
          mc_top100_hit_num += 1.0;
        } else if (i < 50) {
          mc_top50_hit_num += 1.0;
          mc_top100_hit_num += 1.0;
        } else if (i < 100) {
           mc_top100_hit_num += 1.0;
        }
      }
    }
    context.SetDoubleCommonAttr("mc_top10_hit_rate", mc_top10_hit_num / 10.0);
    context.SetDoubleCommonAttr("mc_top50_hit_rate", mc_top50_hit_num / 10.0);
    context.SetDoubleCommonAttr("mc_top100_hit_rate", mc_top100_hit_num / 10.0);

    return true;
  }

  static bool CalcLiveMcToFrAuc(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto final_ensemble_score_accessor = context.GetDoubleItemAttr("final_ensemble_score");
    auto cascade_ensemble_score_accessor = context.GetDoubleItemAttr("cascade_ensemble_score");
    double all_pair = 0.0;
    double pos_pair = 0.0;
    int64 max_len = context.GetIntCommonAttr("live_mc_to_fr_auc_limit_num").value_or(100);
    int index1 = 0;
    if (begin + 1 >= end) {
      return false;
    }
    for (auto result = begin; result != end - 1; ++result) {
      if (index1 >= max_len) {
        break;
      }
      auto final_ensemble_score = final_ensemble_score_accessor(*result).value_or(0.0);
      auto cascade_ensemble_score = cascade_ensemble_score_accessor(*result).value_or(0.0);
      index1++;
      int index2 = index1;
      for (auto result2 = begin + index1; result2 != end; ++result2) {
        if (index2 >= max_len) {
          break;
        }
        auto final_ensemble_score2 = final_ensemble_score_accessor(*result2).value_or(0.0);
        auto cascade_ensemble_score2 = cascade_ensemble_score_accessor(*result2).value_or(0.0);
        bool cmp = (final_ensemble_score2 > final_ensemble_score);
        if (cmp == (cascade_ensemble_score2 > cascade_ensemble_score)) {
          pos_pair += 1.0;
        }
        index2++;
        all_pair = all_pair + 1.0;
      }
    }
    double photo_mc_to_fr_auc = pos_pair/ (all_pair + 0.0001);
    context.SetDoubleCommonAttr("live_mc_to_fr_auc", photo_mc_to_fr_auc);

    struct PlayItem {
      int64 photo_id = -1;
      double mc_score = 0.0;
      double fr_score = 0.0;
      PlayItem(int64 pid, double mc, double fr)
          : photo_id(pid), mc_score(mc), fr_score(fr) {}
      PlayItem() : photo_id(-1), mc_score(0.0), fr_score(0.0) {}
    };
    std::vector<PlayItem> play_item_list;
    for (auto result = begin; result != end; ++result) {
      auto final_ensemble_score = final_ensemble_score_accessor(*result).value_or(0.0);
      auto cascade_ensemble_score = cascade_ensemble_score_accessor(*result).value_or(0.0);
      play_item_list.emplace_back(result->GetId(), cascade_ensemble_score, final_ensemble_score);
    }
    if (play_item_list.size() < 10) {
      return false;
    }
    std::stable_sort(play_item_list.begin(), play_item_list.end(),
      [] (const PlayItem& a, const PlayItem& b) -> bool {
        return a.fr_score > b.fr_score;
      });

    std::unordered_set<int64> fr_top10_item;
    for (int i = 0; i < play_item_list.size() && i < 10; i++) {
      fr_top10_item.insert(play_item_list[i].photo_id);
    }
    std::stable_sort(play_item_list.begin(), play_item_list.end(),
      [] (const PlayItem& a, const PlayItem& b) -> bool {
        return a.mc_score > b.mc_score;
      });
    double mc_top10_hit_num = 0.0;
    double mc_top50_hit_num = 0.0;
    double mc_top100_hit_num = 0.0;
    for (int i = 0; i < play_item_list.size(); i++) {
      if (fr_top10_item.count(play_item_list[i].photo_id) > 0) {
        if (i < 10) {
          mc_top10_hit_num += 1.0;
          mc_top50_hit_num += 1.0;
          mc_top100_hit_num += 1.0;
        } else if (i < 50) {
          mc_top50_hit_num += 1.0;
          mc_top100_hit_num += 1.0;
        } else if (i < 100) {
           mc_top100_hit_num += 1.0;
        }
      }
    }
    context.SetDoubleCommonAttr("live_mc_top10_hit_rate", mc_top10_hit_num / 10.0);
    context.SetDoubleCommonAttr("live_mc_top50_hit_rate", mc_top50_hit_num / 10.0);
    context.SetDoubleCommonAttr("live_mc_top100_hit_rate", mc_top100_hit_num / 10.0);

    return true;
  }

  // 暴力计算精排排序分对最终排序分的 AUC
  static bool CalcFrToFinalAuc(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto final_ensemble_score_accessor = context.GetDoubleItemAttr("final_ensemble_score");
    double all_pair = 0.0;
    double pos_pair = 0.0;
    int64 max_len = context.GetIntCommonAttr("photo_fr_to_final_auc_limit_num").value_or(10);
    int index1 = 0;
    if (begin + 1 >= end) {
      return false;
    }
    for (auto result = begin; result != end - 1; ++result) {
      if (index1 >= max_len) {
        break;
      }
      auto final_ensemble_score = final_ensemble_score_accessor(*result).value_or(0.0);
      index1++;
      int index2 = index1;
      for (auto result2 = begin + index1; result2 != end; ++result2) {
        if (index2 >= max_len) {
          break;
        }
        auto final_ensemble_score2 = final_ensemble_score_accessor(*result2).value_or(0.0);
        if (final_ensemble_score > final_ensemble_score2) {
          pos_pair += 1.0;
        }
        index2++;
        all_pair = all_pair + 1.0;
      }
    }
    double photo_fr_to_final_auc = pos_pair/ (all_pair + 0.0001);
    context.SetDoubleCommonAttr("photo_fr_to_final_auc", photo_fr_to_final_auc);
    return true;
  }

  static bool CalcCascadeAuc(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto photo_cascade_item_id_list = context.GetIntListCommonAttr("photo_cascade_item_id_list");
    auto cascade_ensemble_score_list = context.GetDoubleListCommonAttr("photo_clm_pred_embedding");
    auto photo_top_item_id_list = context.GetIntListCommonAttr("photo_top_item_id_list");
    auto photo_top_cascade_ensemble_score_list =
          context.GetDoubleListCommonAttr("photo_top_clm_score_list");
    if (photo_cascade_item_id_list && cascade_ensemble_score_list
        && photo_top_item_id_list && photo_top_cascade_ensemble_score_list) {
      std::unordered_set<uint64> photo_top_item_id_set;
      for (auto &id : *photo_top_item_id_list) {
        photo_top_item_id_set.insert(id);
      }
      if (photo_top_item_id_list->size() != photo_top_cascade_ensemble_score_list->size()) {
        LOG(ERROR) << "CalcCascadeAuc failed, photo_top_item_id_list is not match";
        return false;
      }
      if (photo_cascade_item_id_list->size() > cascade_ensemble_score_list->size()) {
        LOG(ERROR) << "CalcCascadeAuc failed, photo_cascade_item_id_list is not match";
        return false;
      }
      int size1 = photo_top_item_id_list->size();
      int size2 = photo_cascade_item_id_list->size();
      double pos_pair = 0.0;
      double all_pair = 0.0;
      for (int i = 0; i < size1; i++) {
        uint64 photo_id1 = photo_top_item_id_list->at(i);
        double score1 = photo_top_cascade_ensemble_score_list->at(i);
        for (int j = 0; j < 3000; j++) {
          uint64 photo_id2 = j < size2 ? photo_cascade_item_id_list->at(j) : 0;
          if (!photo_top_item_id_set.count(photo_id2)) {
            double score2 = j < size2 ? cascade_ensemble_score_list->at(size2 - j - 1)
                      : cascade_ensemble_score_list->at(j);
            all_pair += 1.0;
            if (score1 >= score2) {
              pos_pair += 1.0;
            }
          }
        }
      }
      double auc = pos_pair / (0.0001 + all_pair);
      context.SetDoubleCommonAttr("photo_cascade_auc", auc);
    } else {
      LOG(ERROR) << "CalcCascadeAuc failed, list is null";
    }
    return true;
  }
  static bool CalItemLivePreferenceMixScore(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
        double live_preference_weight =
              context.GetDoubleCommonAttr("user_live_preference_weight").value_or(1.0);
        double degrade_rate =
              context.GetDoubleCommonAttr("user_live_preference_degrade_rate").value_or(1.0);
        double shift_value =
              context.GetDoubleCommonAttr("user_live_preference_shift_value").value_or(4.0);
        int live_preference_weight_max_num =
              context.GetIntCommonAttr("live_preference_weight_max_num").value_or(10);
        int enable_smooth =
              context.GetIntCommonAttr("enable_live_preference_mix_score_smooth").value_or(0);
        int enable_trans_score =
              context.GetIntCommonAttr("enable_live_preference_trans_score").value_or(0);
        int enable_count_stat =
              context.GetIntCommonAttr("enable_live_preference_count_stat").value_or(0);
        auto gbdt_mix_score_final_accessor = context.GetDoubleItemAttr("gbdt_mix_score_final");
        auto item_id_accessor = context.GetIntItemAttr("item_id");
        auto live_preference_mix_score_set = context.SetDoubleItemAttr("live_preference_mix_score");
        if (live_preference_weight < 0.0001) {
          live_preference_weight = 1.0;
        }
        std::unordered_map<int64, double> score_map;
        // 10, 20, 50, 100, 200
        std::vector<double> topk_score {0.0, 0.0, 0.0, 0.0};
        int photo_top_num = 0;
        int live_top_num = 0;
        int photo_top_sum = 0.0;
        int live_top_sum = 0.0;
        int idx = 0;
        int item_num = std::distance(begin, end);
        auto score_count_calc = [](int photo_top_num, int live_top_num) {
          photo_top_num = photo_top_num > 0 ? photo_top_num : 1;
          live_top_num = live_top_num > 0 ? live_top_num : 1;
          return live_top_num / photo_top_num * 1.0;
        };
        auto score_calc = [](int photo_top_num, int live_top_num,
            double photo_top_sum, double live_top_sum) {
          photo_top_num = photo_top_num > 0 ? photo_top_num : 1;
          live_top_num = live_top_num > 0 ? live_top_num : 1;
          double photo_avg = photo_top_sum / photo_top_num * 1.0;
          double live_avg = live_top_sum / live_top_num * 1.0;
          return photo_avg > 0 ? live_avg / photo_avg : 5.0;
        };
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          int64 item_id = item_id_accessor(result).value_or(0);
          double gbdt_mix_score_final = gbdt_mix_score_final_accessor(result).value_or(0.0);
          double trans_score = 1.0 / (1.0 + std::exp(- gbdt_mix_score_final + shift_value));
          double smooth_score = 1.0 - idx / live_preference_weight_max_num;
          double temp_score = gbdt_mix_score_final;
          if (enable_count_stat > 0) {
            if (result.GetType() == ks::reco::RecoEnum::ITEM_TYPE_PHOTO) {
              photo_top_num++;
            } else {
              live_top_num++;
            }
            if (idx == 9) {
              topk_score.insert(topk_score.begin() + 0, score_count_calc(photo_top_num, live_top_num));
            }
            if (idx == 19) {
              topk_score.insert(topk_score.begin() + 1, score_count_calc(photo_top_num, live_top_num));
            }
            if (idx == 49) {
              topk_score.insert(topk_score.begin() + 2, score_count_calc(photo_top_num, live_top_num));
            }
            if (idx == 99) {
              topk_score.insert(topk_score.begin() + 3, score_count_calc(photo_top_num, live_top_num));
            }
            temp_score = gbdt_mix_score_final;
          } else {
            if (enable_smooth > 0) {
              temp_score = smooth_score;
            }
            if (enable_trans_score > 0) {
              temp_score = trans_score;
            }
            if (result.GetType() == ks::reco::RecoEnum::ITEM_TYPE_PHOTO) {
              photo_top_num++;
              photo_top_sum = photo_top_sum + temp_score;
            } else {
              live_top_num++;
              live_top_sum = live_top_sum + temp_score;
            }
            if (idx == 9) {
              topk_score.insert(topk_score.begin() + 0, score_calc(photo_top_num,
                      live_top_num, photo_top_sum, live_top_sum));
            }
            if (idx == 19) {
              topk_score.insert(topk_score.begin() + 1, score_calc(photo_top_num,
                      live_top_num, photo_top_sum, live_top_sum));
            }
            if (idx == 49) {
              topk_score.insert(topk_score.begin() + 2, score_calc(photo_top_num,
                      live_top_num, photo_top_sum, live_top_sum));
            }
            if (idx == 99) {
              topk_score.insert(topk_score.begin() + 3, score_calc(photo_top_num,
                      live_top_num, photo_top_sum, live_top_sum));
            }
          }
          score_map.insert(std::make_pair(item_id, temp_score));
          idx++;
        });
        double batch_weight = 1.0;
        for (int i = 0; i < topk_score.size() && topk_score[i] > 0.0; i++) {
          if (i == 0) {
            batch_weight = batch_weight * topk_score[i] / 0.25;
          }
          if (i == 1) {
            batch_weight = batch_weight * topk_score[i] / 0.30;
          }
          if (i == 2) {
            batch_weight = batch_weight * topk_score[i] / 0.35;
          }
          if (i == 3) {
            batch_weight = batch_weight * topk_score[i] / 0.40;
          }
        }
        double final_weight = degrade_rate * live_preference_weight / batch_weight;
        idx = 0;
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          double mix_score = gbdt_mix_score_final_accessor(result).value_or(0.0);
          int64 item_id = item_id_accessor(result).value_or(0);
          if (score_map.count(item_id) > 0) {
            mix_score = score_map[item_id];
          }
          if (idx < live_preference_weight_max_num) {
            if (result.GetType() == ks::reco::RecoEnum::ITEM_TYPE_LIVESTREAM) {
              mix_score = mix_score * final_weight + 1000.0;
            } else {
              mix_score = mix_score + 1000.0;
            }
          }
          live_preference_mix_score_set(result, mix_score);
          idx++;
        });
        score_map.clear();
        return true;
      }
  static bool CalItemLiveVtrPreferenceMixScore(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
        double live_vtr_preference_weight =
              context.GetDoubleCommonAttr("user_live_vtr_preference_weight").value_or(1.0);
        double vtr_degrade_rate =
              context.GetDoubleCommonAttr("user_live_vtr_preference_degrade_rate").value_or(1.0);
        double vtr_degrade_rate_new =
              context.GetDoubleCommonAttr("user_live_vtr_preference_degrade_rate_new").value_or(1.0);
        int enable_smooth =
              context.GetIntCommonAttr("enable_live_vtr_preference_mix_score_smooth").value_or(0);
        int enable_live_prefer =
              context.GetIntCommonAttr("enable_live_prefer_level").value_or(0);
        int live_prefer_thres =
              context.GetIntCommonAttr("live_prefer_level_thres").value_or(0);
        int user_prefer_level =
              context.GetIntCommonAttr("user_rely_live_level_new").value_or(0);
        int is_live_prefer_hour =
              context.GetIntCommonAttr("isLivePreferHour").value_or(0);
        int enable_live_prefer_pHour_threshold =
              context.GetIntCommonAttr("enable_live_prefer_pHour_threshold").value_or(0);
        int enable_live_prefer_valid_pages =
              context.GetIntCommonAttr("enable_live_prefer_valid_pages").value_or(0);
        int page_offset =
              context.GetIntCommonAttr("nearby_page_offset").value_or(0);
        int nearby_live_prefer_page_threshold =
              context.GetIntCommonAttr("nearby_live_prefer_page_threshold").value_or(-1);
        auto gbdt_mix_score_final_accessor = context.GetDoubleItemAttr("gbdt_mix_score_final");
        auto live_vtr_preference_mix_score_set = context.SetDoubleItemAttr("live_vtr_preference_mix_score");
        if (live_vtr_preference_weight < 0.0001) {
          live_vtr_preference_weight = 1.0;
        }
        if ((enable_live_prefer && user_prefer_level >= live_prefer_thres) &&
            (enable_live_prefer_pHour_threshold && is_live_prefer_hour) &&
            (enable_live_prefer_valid_pages && page_offset > nearby_live_prefer_page_threshold)) {
          vtr_degrade_rate = vtr_degrade_rate_new;
        }
        int idx = 0;
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          double gbdt_mix_score_final = gbdt_mix_score_final_accessor(result).value_or(0.0);
          double live_vtr_preference_mix_score = gbdt_mix_score_final;
          if (enable_smooth > 0) {
            live_vtr_preference_mix_score = 1.0 - idx / 1000.0;
          }
          if (result.GetType() == ks::reco::RecoEnum::ITEM_TYPE_LIVESTREAM) {
            live_vtr_preference_mix_score = live_vtr_preference_mix_score *
                    vtr_degrade_rate * live_vtr_preference_weight;
          }
          idx++;
          live_vtr_preference_mix_score_set(result, live_vtr_preference_mix_score);
        });
        return true;
      }
  static bool EnrichPhotoYoungBeautyTag(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    //新小姐姐 tag 判断
    int64 enable_enrich_young_beauty_tag =
              context.GetIntCommonAttr("enable_enrich_young_beauty_tag").value_or(0);
    if (enable_enrich_young_beauty_tag == 0) {
      return true;
    }
    auto hetu_level_one_accessor =
              context.GetIntItemAttr("hetu_level_one");
    auto hetu_level_two_accessor =
              context.GetIntItemAttr("hetu_level_two");
    auto author_gender_accessor =
              context.GetIntItemAttr("item_info.gender");
    auto is_young_beauty_photo_tag_set = context.SetDoubleItemAttr("is_young_beauty_photo");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      int64 hetu_level_one = hetu_level_one_accessor(result).value_or(0);
      int64 hetu_level_two = hetu_level_two_accessor(result).value_or(0);
      int is_young_beauty_photo = 0;
      //条件一
      //一级河图类目舞蹈：舞蹈 (1) 、 颜值 (8)
      //二级河图类目：美女 (134) 、 潮流 (736) 、 二次元真人秀 (195)
      if (hetu_level_one == 1 || hetu_level_one == 8 || hetu_level_two == 134 ||
      hetu_level_two == 736 || hetu_level_two == 195) {
        is_young_beauty_photo = 1;
      }
      //条件二
      //作者为女性或男性 且（ 无河图类目 或 二级类目为随手拍 (46) ， 唱歌 (109）)
      int author_gender = author_gender_accessor(result).value_or(-1);
      if (author_gender > 0) {
        if (hetu_level_one == 0 || hetu_level_two == 146 || hetu_level_two == 109) {
          is_young_beauty_photo = 1;
        }
      }
      is_young_beauty_photo_tag_set(result, is_young_beauty_photo);
    });
    return true;
  }
  static bool DisperseFrPxtr(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto photo_id_list_ptr = context.GetIntListCommonAttr("photo_fr_item_id_list");
    auto live_id_list_ptr = context.GetIntListCommonAttr("live_fr_item_id_list");
    if (photo_id_list_ptr && live_id_list_ptr) {
      std::vector<int64> photo_id_list;
      for (auto &photo_id : *photo_id_list_ptr) {
        photo_id_list.push_back(photo_id);
      }
      std::vector<int64> live_id_list;
      for (auto &live_id : *live_id_list_ptr) {
        live_id_list.push_back(live_id);
      }
      GetProcessPxtrList(context, "fr_pctr_list", 10000, 1.0, photo_id_list, live_id_list);
      GetProcessPxtrList(context, "fr_pltr_list", 100000, 1.0, photo_id_list, live_id_list);
      GetProcessPxtrList(context, "fr_pwtr_list", 100000, 1.0, photo_id_list, live_id_list);
      GetProcessPxtrList(context, "fr_pcmtr_list", 100000, 1.0, photo_id_list, live_id_list);
      GetProcessPxtrList(context, "fr_vtr_list", 100000, 300.0, photo_id_list, live_id_list, true);
      GetProcessPxtrList(context, "fr_pwtd_score_list", 100000, 300.0, photo_id_list, live_id_list);
      GetProcessPxtrList(context, "fr_plvtr_list", 10000, 1.0, photo_id_list, live_id_list);
      GetProcessPxtrList(context, "fr_pfvtr_list", 10000, 1.0, photo_id_list, live_id_list);
      GetItemPxtrList(context, "photo_fr_plvtr_p80_list", 10000, 1.0, photo_id_list);
      GetItemPxtrList(context, "photo_fr_pevtr_p60_list", 10000, 1.0, photo_id_list);
      GetItemPxtrList(context, "photo_fr_uptr_list", 10000, 1.0, photo_id_list);
      GetItemPxtrList(context, "photo_fr_contrasive_distance_list", 500, 500.0, photo_id_list);
      GetItemPxtrList(context, "photo_fr_pLeafDistance_list", 500, 500.0, photo_id_list);
      ProcessBoolList(context, "photo_fr_contras_is_same_city_gen_list");
      ProcessBoolList(context, "photo_fr_is_same_city_gen_list");
      MergeIntList(context, "fr_is_followed_list");
      std::vector<int64> fr_item_type_list;
      for (int i = 0; i < 500; i++) {
        fr_item_type_list.push_back(1);
      }
      for (int i = 0; i < 500; i++) {
        fr_item_type_list.push_back(2);
      }
      context.SetIntListCommonAttr("fr_item_type_list", std::move(fr_item_type_list));
    } else {
      LOG(ERROR) << "GetDispersePxtrList Failed item_id_list is null ";
    }
    return true;
  }
  static void ExpandIntAttrList(const CommonRecoLightFunctionContext &context,
                      const std::string& key, int expand_num, int photo_id_list_size) {
    std::vector<int64> expand_attr_list;
    auto expand_attr_list_ptr = context.GetIntListCommonAttr(key);
    if (expand_attr_list_ptr && photo_id_list_size == expand_attr_list_ptr->size()) {
      for (int i = 0; i < expand_num; i++) {
        if (i >= photo_id_list_size) {  // 补齐到 expand_num
          expand_attr_list.push_back(0);
        } else {
          expand_attr_list.push_back(expand_attr_list_ptr->at(i));
        }
      }
      context.SetIntListCommonAttr(key, std::move(expand_attr_list));
    }
  }
  static void ExpandStringAttrList(const CommonRecoLightFunctionContext &context,
                      const std::string& key, int expand_num, int photo_id_list_size) {
    std::vector<std::string> expand_attr_list;
    auto expand_attr_list_ptr = context.GetStringListCommonAttr(key);
    if (expand_attr_list_ptr && photo_id_list_size == expand_attr_list_ptr->size()) {
      for (int i = 0; i < expand_num; i++) {
        if (i >= photo_id_list_size) {  // 补齐到 expand_num
          expand_attr_list.push_back("unknown");
        } else {
          expand_attr_list.push_back(std::string(expand_attr_list_ptr->at(i)));
        }
      }
      context.SetStringListCommonAttr(key, std::move(expand_attr_list));
    }
  }
  static bool DisperseSlideFrPxtr(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto photo_id_list_ptr = context.GetIntListCommonAttr("photo_fr_item_id_list");
    std::vector<int64> item_lable_mask_list;
    if (photo_id_list_ptr) {
      std::vector<int64> photo_id_list;
      for (auto &photo_id : *photo_id_list_ptr) {
        photo_id_list.push_back(photo_id);
      }
      int photo_id_list_size = photo_id_list.size();
      std::vector<int64> photo_fr_duration_second_list;
      auto duration_ms_list_ptr = context.GetIntListCommonAttr("photo_fr_duration_ms_list");
      if (duration_ms_list_ptr && photo_id_list_size == duration_ms_list_ptr->size()) {
        for (int i = 0; i < 500; i++) {
          if (i >= photo_id_list_size) {  // 补齐到 500
            photo_fr_duration_second_list.push_back(0);
            item_lable_mask_list.push_back(0);
          } else {
            int64 duration_second = (duration_ms_list_ptr->at(i)) / 1000;
            photo_fr_duration_second_list.push_back(duration_second);
            item_lable_mask_list.push_back(1);
          }
        }
        context.SetIntListCommonAttr("photo_fr_duration_second_list",
                      std::move(photo_fr_duration_second_list));
      }
      GetSlidePxtrList(context, "photo_fr_pltr_list", 100000, 1.0, photo_id_list, 500);
      GetSlidePxtrList(context, "photo_fr_pwtr_list", 1000000, 1.0, photo_id_list, 500);
      GetSlidePxtrList(context, "photo_fr_pcmtr_list", 100000, 1.0, photo_id_list, 500);
      GetSlidePxtrList(context, "photo_fr_vtr_list", 100000, 300.0, photo_id_list, 500);
      GetSlidePxtrList(context, "photo_fr_pwtd_score_list", 100000, 300.0, photo_id_list, 500);
      GetSlidePxtrList(context, "photo_fr_plvtr_list", 10000, 1.0, photo_id_list, 500);
      GetSlidePxtrList(context, "photo_fr_pfvtr_list", 10000, 1.0, photo_id_list, 500);
      GetSlidePxtrList(context, "photo_fr_uptr_list", 10000, 1.0, photo_id_list, 500);

      ExpandIntAttrList(context, "photo_reason_list", 500, photo_id_list_size);
      ExpandIntAttrList(context, "photo_fr_is_followed_list", 500, photo_id_list_size);
      ExpandIntAttrList(context, "photo_hetu_level_one_list", 500, photo_id_list_size);
      ExpandIntAttrList(context, "photo_hetu_level_two_list", 500, photo_id_list_size);
      ExpandStringAttrList(context, "photo_user_age_segment_list", 500, photo_id_list_size);
      ExpandIntAttrList(context, "photo_gender_list", 500, photo_id_list_size);


    } else {
      LOG(ERROR) << "DisperseSlideFrPxtr Failed item_id_list is null ";
    }
    context.SetIntListCommonAttr("item_lable_mask_list",
                      std::move(item_lable_mask_list));
    return true;
  }

    static bool DisperseSlideRerankPxtr(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto photo_id_list_ptr = context.GetIntListCommonAttr("photo_rerank_item_id_list");
    if (photo_id_list_ptr) {
      std::vector<int64> photo_id_list;
      for (auto &photo_id : *photo_id_list_ptr) {
        photo_id_list.push_back(photo_id);
      }
      int photo_id_list_size = photo_id_list.size();
      std::vector<int64> photo_fr_duration_second_list;
      auto duration_ms_list_ptr = context.GetIntListCommonAttr("photo_rerank_duration_ms_list");
      if (duration_ms_list_ptr && photo_id_list_size == duration_ms_list_ptr->size()) {
        for (int i = 0; i < 30; i++) {
          if (i >= photo_id_list_size) {  // 补齐到 30
            photo_fr_duration_second_list.push_back(0);
          } else {
            int64 duration_second = (duration_ms_list_ptr->at(i)) / 1000;
            photo_fr_duration_second_list.push_back(duration_second);
          }
        }
        context.SetIntListCommonAttr("photo_rerank_duration_second_list",
                      std::move(photo_fr_duration_second_list));
      }
      GetSlidePxtrList(context, "photo_rerank_pltr_list", 100000, 1.0, photo_id_list, 30);
      GetSlidePxtrList(context, "photo_rerank_pwtr_list", 1000000, 1.0, photo_id_list, 30);
      GetSlidePxtrList(context, "photo_rerank_pcmtr_list", 100000, 1.0, photo_id_list, 30);
      GetSlidePxtrList(context, "photo_rerank_vtr_list", 100000, 300.0, photo_id_list, 30);
      GetSlidePxtrList(context, "photo_rerank_pwtd_score_list", 100000, 500.0, photo_id_list, 30);
      GetSlidePxtrList(context, "photo_rerank_plvtr_list", 10000, 1.0, photo_id_list, 30);
      GetSlidePxtrList(context, "photo_rerank_pfvtr_list", 10000, 1.0, photo_id_list, 30);
      GetSlidePxtrList(context, "photo_rerank_uptr_list", 10000, 1.0, photo_id_list, 30);
    } else {
      LOG(ERROR) << "DisperseSlideRerankPxtr Failed item_id_list is null ";
    }

    return true;
  }
  static void ProcessBoolList(const CommonRecoLightFunctionContext &context,
                        const std::string& key) {
    auto photo_value_list = context.GetIntListCommonAttr(key);
    std::vector<int64> photo_value_list_post;
    if (photo_value_list) {
      int photo_value_list_size = photo_value_list->size();
      for (int i = 0; i < 500; i++) {
        if (i >= photo_value_list_size) {  // 补齐到 500
          photo_value_list_post.push_back(-1);
        } else {
          photo_value_list_post.push_back(photo_value_list->at(i) + 1);
        }
      }
    }
    context.SetIntListCommonAttr(key, std::move(photo_value_list_post));
  }

  static void MergeIntList(const CommonRecoLightFunctionContext &context,
                        std::string key) {
    std::string photo_key = "photo_" + key;
    auto photo_value_list = context.GetIntListCommonAttr(photo_key);
    std::vector<int64> photo_value_list_post;
    std::vector<int64> photo_value_list_post_v2;
    if (photo_value_list) {
      int photo_value_list_size = photo_value_list->size();
      for (int i = 0; i < 500; i++) {
        if (i >= photo_value_list_size) {  // 补齐到 500
          photo_value_list_post.push_back(0);
          photo_value_list_post_v2.push_back(-1);
        } else {
          photo_value_list_post.push_back(photo_value_list->at(i));
          photo_value_list_post_v2.push_back(photo_value_list->at(i) + 1);
        }
      }
    }
    std::string live_key = "live_" + key;
    auto live_value_list = context.GetIntListCommonAttr(live_key);
    std::vector<int64> live_value_list_post;
    std::vector<int64> live_value_list_post_v2;
    if (live_value_list) {
      int live_value_list_size = live_value_list->size();
      for (int i = 0; i < 500; i++) {
        if (i >= live_value_list_size) {  // 补齐到 500
          live_value_list_post.push_back(0);
          live_value_list_post_v2.push_back(-1);
        } else {
          live_value_list_post.push_back(live_value_list->at(i));
          live_value_list_post_v2.push_back(live_value_list->at(i) + 1);
        }
      }
    }
    std::vector<int64> photo_value_list_post_v2_tmp = photo_value_list_post_v2;
    std::move(live_value_list_post.begin(),
          live_value_list_post.end(), std::back_inserter(photo_value_list_post));
    std::move(live_value_list_post_v2.begin(),
          live_value_list_post_v2.end(), std::back_inserter(photo_value_list_post_v2));
    context.SetIntListCommonAttr(key, std::move(photo_value_list_post));
    context.SetIntListCommonAttr(key + "_v2", std::move(photo_value_list_post_v2));
    context.SetIntListCommonAttr(key + "_v2_photo", std::move(photo_value_list_post_v2_tmp));
  }
  static void GetSlidePxtrList(const CommonRecoLightFunctionContext &context,
                        std::string key, int mod, double max_value,
                        std::vector<int64> photo_id_list, int max_size) {
      struct PlayItem {
        int64 photo_id = -1;
        double pxtr_score = 0.0;
        PlayItem(int64 pid, double pxtr)
            : photo_id(pid), pxtr_score(pxtr) {}
        PlayItem() : photo_id(-1), pxtr_score(0.0) {}
      };
      auto photo_pctr_list = context.GetDoubleListCommonAttr(key);
      int photo_id_list_size = photo_id_list.size();

      std::vector<int64> photo_disperse_pctr_list;
      std::vector<int64> photo_rank_pctr_list;
      std::vector<double> photo_ori_pctr_list;
      std::vector<PlayItem> photo_play_item_list;
      if (photo_pctr_list) {
        if (photo_id_list_size != photo_pctr_list->size()) {
          LOG(ERROR) << "GetSlidePxtrList not match key : " << key;
          return;
        }
        for (int i = 0; i < max_size; i++) {
          if (i >= photo_id_list_size) {  // 补齐到 max_size
            photo_disperse_pctr_list.push_back(0);
            photo_ori_pctr_list.push_back(0.0);
          } else {
            int64 v1 = std::floor(std::min(photo_pctr_list->at(i), max_value) / max_value * mod);
            int64 d_value = v1 % mod;
            photo_disperse_pctr_list.push_back(d_value);
            photo_play_item_list.emplace_back(photo_id_list.at(i), photo_pctr_list->at(i));
            photo_ori_pctr_list.push_back(photo_pctr_list->at(i));
          }
        }
        std::stable_sort(photo_play_item_list.begin(), photo_play_item_list.end(),
          [] (const PlayItem& a, const PlayItem& b) -> bool {
            return a.pxtr_score > b.pxtr_score;
          });
        folly::F14FastMap<int64, int64> item_rank_map;
        for (int i = 0; i < photo_play_item_list.size(); i++) {
          item_rank_map[photo_play_item_list.at(i).photo_id] = i + 1;
        }
        for (int i = 0; i < max_size; i++) {
          if (i >= photo_id_list_size) {  // 补齐到 max_size
            photo_rank_pctr_list.push_back(max_size);
          } else {
            auto iter = item_rank_map.find(photo_id_list.at(i));
            if (iter != item_rank_map.end()) {
              photo_rank_pctr_list.push_back(iter->second);
            } else {
              photo_rank_pctr_list.push_back(max_size);
            }
          }
        }
      } else {  // 如果没有这个 xtr 则用默认值补齐到 max_size
        for (int i = 0; i < max_size; i++) {
          photo_rank_pctr_list.push_back(max_size);
          photo_disperse_pctr_list.push_back(0);
        }
      }
      std::string save_key = key + "_dis";
      std::string save_key_rank = key + "_rank";
      context.SetIntListCommonAttr(save_key, std::move(photo_disperse_pctr_list));
      context.SetIntListCommonAttr(save_key_rank, std::move(photo_rank_pctr_list));
      context.SetDoubleListCommonAttr(key, std::move(photo_ori_pctr_list));
  }
  static void GetItemPxtrList(const CommonRecoLightFunctionContext &context,
                        const std::string &key, int mod, double max_value,
                        std::vector<int64> item_id_list) {
      struct PlayItem {
        int64 photo_id = -1;
        double pxtr_score = 0.0;
        PlayItem(int64 pid, double pxtr)
            : photo_id(pid), pxtr_score(pxtr) {}
        PlayItem() : photo_id(-1), pxtr_score(0.0) {}
      };
      auto photo_pctr_list = context.GetDoubleListCommonAttr(key);
      int photo_id_list_size = item_id_list.size();

      std::vector<int64> photo_disperse_pctr_list_v2;
      std::vector<int64> photo_rank_pctr_list;
      std::vector<PlayItem> photo_play_item_list;
      std::vector<double> photo_pctr_list_ori;
      if (photo_pctr_list) {
        if (photo_id_list_size != photo_pctr_list->size()) {
          LOG(ERROR) << "GetDispersePxtrList not match key : " << key;
          return;
        }
        for (int i = 0; i < 500; i++) {
          if (i >= photo_id_list_size) {  // 补齐到 500
            photo_disperse_pctr_list_v2.push_back(-2);
            photo_pctr_list_ori.push_back(0.0);
          } else {
            int64 v1 = std::floor(std::min(photo_pctr_list->at(i), max_value) / max_value * mod);
            int64 d_value = v1 % mod;
            photo_disperse_pctr_list_v2.push_back(d_value == 0 ? -1 : d_value);
            photo_play_item_list.emplace_back(item_id_list.at(i), photo_pctr_list->at(i));
            photo_pctr_list_ori.push_back(photo_pctr_list->at(i));
          }
        }
        std::stable_sort(photo_play_item_list.begin(), photo_play_item_list.end(),
          [] (const PlayItem& a, const PlayItem& b) -> bool {
            return a.pxtr_score > b.pxtr_score;
          });
        folly::F14FastMap<int64, int64> item_rank_map;
        for (int i = 0; i < photo_play_item_list.size(); i++) {
          item_rank_map[photo_play_item_list.at(i).photo_id] = i + 1;
        }
        for (int i = 0; i < 500; i++) {
          if (i >= photo_id_list_size) {  // 补齐到 500
            photo_rank_pctr_list.push_back(500);
          } else {
            auto iter = item_rank_map.find(item_id_list.at(i));
            if (iter != item_rank_map.end()) {
              photo_rank_pctr_list.push_back(iter->second);
            } else {
              photo_rank_pctr_list.push_back(500);
            }
          }
        }
      } else {  // 如果没有这个 xtr 则用默认值补齐到 500
        for (int i = 0; i < 500; i++) {
          photo_rank_pctr_list.push_back(500);
          photo_disperse_pctr_list_v2.push_back(-2);
          photo_pctr_list_ori.push_back(0.0);
        }
      }
      std::string save_key_v2 = key + "_dis_v2";
      std::string save_key_rank = key + "_rank";
      std::string save_key_ori = key + "_ori";
      context.SetIntListCommonAttr(save_key_v2, std::move(photo_disperse_pctr_list_v2));
      context.SetIntListCommonAttr(save_key_rank, std::move(photo_rank_pctr_list));
      context.SetDoubleListCommonAttr(save_key_ori, std::move(photo_pctr_list_ori));
  }
  static void GetProcessPxtrList(const CommonRecoLightFunctionContext &context,
                        std::string key, int mod, double max_value,
                        std::vector<int64> photo_id_list, std::vector<int64> live_id_list,
                        bool need_inverse = false) {
      struct PlayItem {
        int64 photo_id = -1;
        double pxtr_score = 0.0;
        PlayItem(int64 pid, double pxtr)
            : photo_id(pid), pxtr_score(pxtr) {}
        PlayItem() : photo_id(-1), pxtr_score(0.0) {}
      };
      std::string photo_key = "photo_" + key;
      auto photo_pctr_list = context.GetDoubleListCommonAttr(photo_key);
      int photo_id_list_size = photo_id_list.size();

      std::vector<int64> photo_disperse_pctr_list;
      std::vector<int64> photo_disperse_pctr_list_v2;
      std::vector<int64> photo_rank_pctr_list;
      std::vector<PlayItem> photo_play_item_list;
      std::vector<double> photo_pctr_list_ori;
      if (photo_pctr_list) {
        if (photo_id_list_size != photo_pctr_list->size()) {
          LOG(ERROR) << "GetDispersePxtrList not match key : " << key;
          return;
        }
        for (int i = 0; i < 500; i++) {
          if (i >= photo_id_list_size) {  // 补齐到 500
            photo_disperse_pctr_list.push_back(0);
            photo_disperse_pctr_list_v2.push_back(-2);
            photo_pctr_list_ori.push_back(0.0);
          } else {
            int64 v1 = std::floor(std::min(photo_pctr_list->at(i), max_value) / max_value * mod);
            int64 d_value = v1 % mod;
            photo_disperse_pctr_list.push_back(d_value);
            photo_disperse_pctr_list_v2.push_back(d_value == 0 ? -1 : d_value);
            photo_play_item_list.emplace_back(photo_id_list.at(i), photo_pctr_list->at(i));
            photo_pctr_list_ori.push_back(photo_pctr_list->at(i));
          }
        }
        std::stable_sort(photo_play_item_list.begin(), photo_play_item_list.end(),
          [] (const PlayItem& a, const PlayItem& b) -> bool {
            return a.pxtr_score > b.pxtr_score;
          });
        folly::F14FastMap<int64, int64> item_rank_map;
        for (int i = 0; i < photo_play_item_list.size(); i++) {
          item_rank_map[photo_play_item_list.at(i).photo_id] = i + 1;
        }
        for (int i = 0; i < 500; i++) {
          if (i >= photo_id_list_size) {  // 补齐到 500
            photo_rank_pctr_list.push_back(500);
          } else {
            auto iter = item_rank_map.find(photo_id_list.at(i));
            if (iter != item_rank_map.end()) {
              photo_rank_pctr_list.push_back(iter->second);
            } else {
              photo_rank_pctr_list.push_back(500);
            }
          }
        }
      } else {  // 如果没有这个 xtr 则用默认值补齐到 500
        for (int i = 0; i < 500; i++) {
          photo_rank_pctr_list.push_back(500);
          photo_disperse_pctr_list.push_back(0);
          photo_pctr_list_ori.push_back(0.0);
        }
      }

      std::string live_key = "live_" + key;
      auto live_pctr_list = context.GetDoubleListCommonAttr(live_key);
      int live_id_list_size = live_id_list.size();

      std::vector<int64> live_disperse_pctr_list;
      std::vector<int64> live_disperse_pctr_list_v2;
      std::vector<int64> live_rank_pctr_list;
      std::vector<PlayItem> live_play_item_list;
      std::vector<double> live_pctr_list_ori;
      if (live_pctr_list) {
        if (live_id_list_size != live_pctr_list->size()) {
          LOG(ERROR) << "GetDispersePxtrList not match key : " << key;
          return;
        }
        for (int i = 0; i < 500; i++) {
          if (i >= live_id_list_size) {  // 补齐到 500
            live_disperse_pctr_list.push_back(0);
            live_disperse_pctr_list_v2.push_back(-2);
            live_pctr_list_ori.push_back(0.0);
          } else {
            double value = live_pctr_list->at(i);
            if (need_inverse) {
              value = value / std::max(1.0 - value, 0.0001);
            }
            int64 v1 = std::floor(std::min(value, max_value) / max_value * mod);
            int64 d_value = v1 % mod;
            live_disperse_pctr_list.push_back(d_value);
            live_disperse_pctr_list_v2.push_back(d_value == 0 ? -1 : d_value);
            live_play_item_list.emplace_back(live_id_list.at(i), value);
            live_pctr_list_ori.push_back(value);
          }
        }
        std::stable_sort(live_play_item_list.begin(), live_play_item_list.end(),
          [] (const PlayItem& a, const PlayItem& b) -> bool {
            return a.pxtr_score > b.pxtr_score;
          });
        folly::F14FastMap<int64, int64> item_rank_map;
        for (int i = 0; i < live_play_item_list.size(); i++) {
          item_rank_map[live_play_item_list.at(i).photo_id] = i + 1;
        }
        for (int i = 0; i < 500; i++) {
          if (i >= live_id_list_size) {  // 补齐到 500
            live_rank_pctr_list.push_back(500);
          } else {
            auto iter = item_rank_map.find(live_id_list.at(i));
            if (iter != item_rank_map.end()) {
              live_rank_pctr_list.push_back(iter->second);
            } else {
              live_rank_pctr_list.push_back(500);
            }
          }
        }
      } else {  // 如果没有这个 xtr 则用默认值补齐到 500
        for (int i = 0; i < 500; i++) {
          live_rank_pctr_list.push_back(500);
          live_disperse_pctr_list.push_back(0);
          live_pctr_list_ori.push_back(0.0);
        }
      }
      std::string save_key = key + "_dis";
      std::string save_key_v2 = key + "_dis_v2";
      std::string save_key_rank = key + "_rank";
      std::string save_key_ori = key + "_ori";
      std::string photo_save_key_ori = key + "_ori_photo";
      std::string photo_save_key_v2 = key + "_dis_v2_photo";
      std::string photo_save_key_rank = key + "_rank_photo";

      std::vector<int64> photo_disperse_pctr_list_v2_tmp = photo_disperse_pctr_list_v2;
      std::vector<int64> photo_rank_pctr_list_tmp = photo_rank_pctr_list;
      std::vector<double> photo_pctr_list_ori_tmp = photo_pctr_list_ori;
      std::move(live_disperse_pctr_list.begin(),
          live_disperse_pctr_list.end(), std::back_inserter(photo_disperse_pctr_list));
      std::move(live_disperse_pctr_list_v2.begin(),
          live_disperse_pctr_list_v2.end(), std::back_inserter(photo_disperse_pctr_list_v2));
      std::move(live_rank_pctr_list.begin(),
          live_rank_pctr_list.end(), std::back_inserter(photo_rank_pctr_list));
      std::move(live_pctr_list_ori.begin(),
          live_pctr_list_ori.end(), std::back_inserter(photo_pctr_list_ori));
      context.SetIntListCommonAttr(save_key, std::move(photo_disperse_pctr_list));
      context.SetIntListCommonAttr(save_key_v2, std::move(photo_disperse_pctr_list_v2));
      context.SetIntListCommonAttr(save_key_rank, std::move(photo_rank_pctr_list));
      context.SetDoubleListCommonAttr(save_key_ori, std::move(photo_pctr_list_ori));
      context.SetIntListCommonAttr(photo_save_key_v2, std::move(photo_disperse_pctr_list_v2_tmp));
      context.SetIntListCommonAttr(photo_save_key_rank, std::move(photo_rank_pctr_list_tmp));
      context.SetDoubleListCommonAttr(photo_save_key_ori, std::move(photo_pctr_list_ori_tmp));
  }
  static bool CalPhotoMigrateBoostScore(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto hetu_level_one_accessor =
        context.GetIntListItemAttr("item_info.hetu_tag_level_info_v2.hetu_level_one");
    auto set_migrate_boost_score = context.SetDoubleItemAttr("photo_user_new_migrate_boost_score");
    static const std::unordered_set<int64> migrate_boost_tag_set = {10, 11, 12, 13};
    static const std::unordered_set<int64> migrate_decay_tag_set =
      {29, 21, 22, 38, 32, 27, 35, 28, 6, 18, 23, 9, 24, 39, 3};
    double user_new_migrate_boost_coeff =
      context.GetDoubleCommonAttr("user_new_migrate_boost_coeff").value_or(1.0);
    double user_new_migrate_decay_coeff =
      context.GetDoubleCommonAttr("user_new_migrate_decay_coeff").value_or(1.0);
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double photo_user_new_migrate_boost_score = 1.0;
      auto hetu_level_ones = hetu_level_one_accessor(result);
      if (hetu_level_ones) {
        for (auto& tag : *hetu_level_ones) {
          if (migrate_boost_tag_set.count(tag)) {
            photo_user_new_migrate_boost_score = user_new_migrate_boost_coeff;
            break;
          } else if (migrate_decay_tag_set.count(tag)) {
            photo_user_new_migrate_boost_score = user_new_migrate_decay_coeff;
            break;
          }
        }
      }
      set_migrate_boost_score(result, photo_user_new_migrate_boost_score);
    });
    return true;
  }
  static bool SetPhotoExpectPxtr(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
      auto default_val = std::make_shared<std::map<std::string, std::string>>();
      auto parser = [](const std::string &key, std::string *val) -> bool {
        *val = key;
        return true;
      };
      auto nearbyCtrShapeMap = *ks::infra::KConf().GetMap<std::string, std::string>(
        "reco.nearbyRec.nearbyCtrShapeConfig", default_val, parser)->Get();
      bool getMapSucc = true;
      std::vector<double> pctr_lst;
      std::vector<double> pltr_lst;
      std::vector<double> pcmtr_lst;
      std::vector<double> pwtd_lst;
      for (int i = 0; i < 500; i++) {
        std::string key = base::IntToString(i);
        if (nearbyCtrShapeMap.count(key)) {
          std::string value = nearbyCtrShapeMap.at(key);
          std::vector<absl::string_view> param = absl::StrSplit(value, ",", absl::SkipWhitespace());
          if (param.size() != 4) {
            getMapSucc = false;
            break;
          }
          auto pctr = 0.0;
          auto pltr = 0.0;
          auto pcmtr = 0.0;
          auto pwtd = 0.0;
          if (absl::SimpleAtod(param[0], &pctr)) {
            pctr_lst.push_back(pctr);
          } else {
            getMapSucc = false;
            break;
          }
          if (absl::SimpleAtod(param[1], &pltr)) {
            pltr_lst.push_back(pltr);
          } else {
            getMapSucc = false;
            break;
          }
          if (absl::SimpleAtod(param[2], &pcmtr)) {
            pcmtr_lst.push_back(pcmtr);
          } else {
            getMapSucc = false;
            break;
          }
          if (absl::SimpleAtod(param[3], &pwtd)) {
            pwtd_lst.push_back(pwtd);
          } else {
            getMapSucc = false;
            break;
          }
        } else {
          getMapSucc = false;
          break;
        }
      }
      if (!getMapSucc) {
        context.SetIntCommonAttr("SetPhotoExpectPxtrSucc", 0);
        return true;
      }
      auto fullrank_ori_ctr_accessor = context.GetDoubleItemAttr("fullrank_ori_ctr");
      auto set_expect_pltr = context.SetDoubleItemAttr("expect_pltr");
      auto set_expect_pcmtr = context.SetDoubleItemAttr("expect_pcmtr");
      auto set_expect_pwtd = context.SetDoubleItemAttr("expect_pwtd");
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        double pctr = fullrank_ori_ctr_accessor(result).value_or(0.0);
        int bucket_index =
            std::lower_bound(pctr_lst.begin(), pctr_lst.end(), pctr) -
            pctr_lst.begin();
        if (bucket_index >= 0 && bucket_index < pltr_lst.size()) {
          set_expect_pltr(result, pltr_lst[bucket_index]);
        }
        if (bucket_index >= 0 && bucket_index < pcmtr_lst.size()) {
          set_expect_pcmtr(result, pcmtr_lst[bucket_index]);
        }
        if (bucket_index >= 0 && bucket_index < pwtd_lst.size()) {
          set_expect_pwtd(result, pwtd_lst[bucket_index]);
        }
      });
      context.SetIntCommonAttr("SetPhotoExpectPxtrSucc", 1);
      return true;
  }

  static bool SetPhotoIsBeauty(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto hetu_tags_accessor =
        context.GetIntListItemAttr("item_info.hetu_tag_level_info_v2.hetu_tag");
    auto hetu_level_two_accessor =
        context.GetIntListItemAttr("item_info.hetu_tag_level_info_v2.hetu_level_two");
    auto hetu_sim_cluster_id_accessor =
              context.GetIntItemAttr("item_dynamic_info.hetu_sim_cluster_id");
    auto item_hetu_one_accessor =
              context.GetIntItemAttr("hetu_level_one");
    auto gender_accessor =
              context.GetIntItemAttr("item_info.gender");

    auto set_is_beauty_v2 = context.SetIntItemAttr("is_beauty_v2");
    auto set_is_photo_beauty_tag = context.SetIntItemAttr("is_photo_beauty_tag");
    static const std::unordered_set<int32> strong_beauty_hetu_level_one = {1, 8};
    static const std::unordered_set<int32> weak_beauty_hetu_level_one = {34, 4, 5};
    static const std::unordered_set<int64> photo_beauty_tag_set = {58611, 59549, 58615};
    auto photo_beauty_cluster_id_set = *ks::infra::KConf().GetSet(
      "reco.nearby.photo_beauty_cluster_id_set", std::make_shared<std::set<int64>>())->Get();
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int64 is_photo_beauty_tag = 0;
      auto hetu_tags = hetu_tags_accessor(result);
      if (hetu_tags) {
        for (auto& tag : *hetu_tags) {
          if (photo_beauty_tag_set.count(tag)) {
            is_photo_beauty_tag = 1;
            break;
          }
        }
      }
      int64 is_photo_beauty_v2 = is_photo_beauty_tag;
      if (is_photo_beauty_v2 == 0 && photo_beauty_cluster_id_set.count(
              hetu_sim_cluster_id_accessor(result).value_or(0))) {
        is_photo_beauty_v2 = 1;
      }
      if (is_photo_beauty_v2 == 0) {
        auto hetu_level_twos = hetu_level_two_accessor(result);
        if (hetu_level_twos) {
          for (auto& tag : *hetu_level_twos) {
            if (tag == 134) {
              is_photo_beauty_tag = 1;
              is_photo_beauty_v2 = 1;
              break;
            }
          }
        }
      }
      if (is_photo_beauty_v2 > 0) {
        set_is_beauty_v2(result, 1);
        is_photo_beauty_tag = 1;
      }
      int32 item_hetu_one = item_hetu_one_accessor(result).value_or(0);
      if (strong_beauty_hetu_level_one.count(item_hetu_one)
          || (weak_beauty_hetu_level_one.count(item_hetu_one) && gender_accessor(result).value_or(-1) == 1)) {
        set_is_beauty_v2(result, 1);
      }
      set_is_photo_beauty_tag(result, is_photo_beauty_tag);
    });
    return true;
  }

  static bool SetMainPhotoItemWiseLtrScore(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto photo_fr_iw_pred_embedding = context.GetDoubleListCommonAttr("photo_fr_iw_pred_embedding");
    auto photo_fr_item_id_list = context.GetIntListCommonAttr("photo_fr_item_id_list");
    auto set_iw_pctr_alpha = context.SetDoubleItemAttr("iw_pctr_alpha");
    auto set_iw_pctr_beta = context.SetDoubleItemAttr("iw_pctr_beta");
    auto set_iw_lvtrp80_alpha = context.SetDoubleItemAttr("iw_lvtrp80_alpha");
    auto set_iw_lvtrp80_beta = context.SetDoubleItemAttr("iw_lvtrp80_beta");
    auto set_iw_evtrp60_alpha = context.SetDoubleItemAttr("iw_evtrp60_alpha");
    auto set_iw_evtrp60_beta = context.SetDoubleItemAttr("iw_evtrp60_beta");
    auto set_iw_pwtr_alpha = context.SetDoubleItemAttr("iw_pwtr_alpha");
    auto set_iw_pwtr_beta = context.SetDoubleItemAttr("iw_pwtr_beta");
    auto set_iw_pcmtr_alpha = context.SetDoubleItemAttr("iw_pcmtr_alpha");
    auto set_iw_pcmtr_beta = context.SetDoubleItemAttr("iw_pcmtr_beta");
    auto set_iw_pwtd_alpha = context.SetDoubleItemAttr("iw_pwtd_alpha");
    auto set_iw_pwtd_beta = context.SetDoubleItemAttr("iw_pwtd_beta");
    auto set_iw_pfvtr_alpha = context.SetDoubleItemAttr("iw_pfvtr_alpha");
    auto set_iw_pfvtr_beta = context.SetDoubleItemAttr("iw_pfvtr_beta");
    auto set_iw_pvtr_alpha = context.SetDoubleItemAttr("iw_pvtr_alpha");
    auto set_iw_pvtr_beta = context.SetDoubleItemAttr("iw_pvtr_beta");
    auto set_iw_lvtr_alpha = context.SetDoubleItemAttr("iw_lvtr_alpha");
    auto set_iw_lvtr_beta = context.SetDoubleItemAttr("iw_lvtr_beta");
    auto set_iw_pltr_alpha = context.SetDoubleItemAttr("iw_pltr_alpha");
    auto set_iw_pltr_beta = context.SetDoubleItemAttr("iw_pltr_beta");
    auto set_iw_uptr_alpha = context.SetDoubleItemAttr("iw_uptr_alpha");
    auto set_iw_uptr_beta = context.SetDoubleItemAttr("iw_uptr_beta");

    if (photo_fr_iw_pred_embedding && photo_fr_item_id_list) {
      int pred_size = photo_fr_iw_pred_embedding->size();
      int size_photo = photo_fr_item_id_list->size();
      if (pred_size == 11000 && size_photo <= 500) {
        int rank_photo = 1;
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          if (rank_photo <= size_photo) {
            set_iw_pctr_alpha(result, photo_fr_iw_pred_embedding->at(5500 - (11 * rank_photo)));
            set_iw_lvtrp80_alpha(result, photo_fr_iw_pred_embedding->at(5500 - (11 * rank_photo - 1)));
            set_iw_evtrp60_alpha(result, photo_fr_iw_pred_embedding->at(5500 - (11 * rank_photo - 2)));
            set_iw_pwtr_alpha(result, photo_fr_iw_pred_embedding->at(5500 - (11 * rank_photo - 3)));
            set_iw_pcmtr_alpha(result, photo_fr_iw_pred_embedding->at(5500 - (11 * rank_photo - 4)));
            set_iw_pwtd_alpha(result, photo_fr_iw_pred_embedding->at(5500 - (11 * rank_photo - 5)));
            set_iw_pfvtr_alpha(result, photo_fr_iw_pred_embedding->at(5500 - (11 * rank_photo - 6)));
            set_iw_pvtr_alpha(result, photo_fr_iw_pred_embedding->at(5500 - (11 * rank_photo - 7)));
            set_iw_lvtr_alpha(result, photo_fr_iw_pred_embedding->at(5500 - (11 * rank_photo - 8)));
            set_iw_pltr_alpha(result, photo_fr_iw_pred_embedding->at(5500 - (11 * rank_photo - 9)));
            set_iw_uptr_alpha(result, photo_fr_iw_pred_embedding->at(5500 - (11 * rank_photo - 10)));
            set_iw_pctr_beta(result, photo_fr_iw_pred_embedding->at(11000 - (11 * rank_photo)));
            set_iw_lvtrp80_beta(result, photo_fr_iw_pred_embedding->at(11000 - (11 * rank_photo - 1)));
            set_iw_evtrp60_beta(result, photo_fr_iw_pred_embedding->at(11000 - (11 * rank_photo - 2)));
            set_iw_pwtr_beta(result, photo_fr_iw_pred_embedding->at(11000 - (11 * rank_photo - 3)));
            set_iw_pcmtr_beta(result, photo_fr_iw_pred_embedding->at(11000 - (11 * rank_photo - 4)));
            set_iw_pwtd_beta(result, photo_fr_iw_pred_embedding->at(11000 - (11 * rank_photo - 5)));
            set_iw_pfvtr_beta(result, photo_fr_iw_pred_embedding->at(11000 - (11 * rank_photo - 6)));
            set_iw_pvtr_beta(result, photo_fr_iw_pred_embedding->at(11000 - (11 * rank_photo - 7)));
            set_iw_lvtr_beta(result, photo_fr_iw_pred_embedding->at(11000 - (11 * rank_photo - 8)));
            set_iw_pltr_beta(result, photo_fr_iw_pred_embedding->at(11000 - (11 * rank_photo - 9)));
            set_iw_uptr_beta(result, photo_fr_iw_pred_embedding->at(11000 - (11 * rank_photo - 10)));
            rank_photo++;
          } else {
            set_iw_pctr_alpha(result, 1);
            set_iw_lvtrp80_alpha(result, 1);
            set_iw_evtrp60_alpha(result, 1);
            set_iw_pwtr_alpha(result, 1);
            set_iw_pcmtr_alpha(result, 1);
            set_iw_pwtd_alpha(result, 1);
            set_iw_pfvtr_alpha(result, 1);
            set_iw_pvtr_alpha(result, 1);
            set_iw_lvtr_alpha(result, 1);
            set_iw_pltr_alpha(result, 1);
            set_iw_uptr_alpha(result, 1);
            set_iw_pctr_beta(result, 0);
            set_iw_lvtrp80_beta(result, 0);
            set_iw_evtrp60_beta(result, 0);
            set_iw_pwtr_beta(result, 0);
            set_iw_pcmtr_beta(result, 0);
            set_iw_pwtd_beta(result, 0);
            set_iw_pfvtr_beta(result, 0);
            set_iw_pvtr_beta(result, 0);
            set_iw_lvtr_beta(result, 0);
            set_iw_pltr_beta(result, 0);
            set_iw_uptr_beta(result, 0);          }
        });
        context.SetIntCommonAttr("SetItemWiseLtrScoreSucc", 1);
      } else {
        LOG(ERROR) << "SetItemWiseLtrScore Failed, pred_size : "  << pred_size
          << ", size_photo : " << size_photo;
        context.SetIntCommonAttr("SetItemWiseLtrScoreSucc", 0);
      }
    } else {
      LOG(ERROR) << "SetItemWiseLtrScoreSucc Failed, attr is null";
      context.SetIntCommonAttr("SetItemWiseLtrScoreSucc", 0);
    }
    return true;
  }
  static bool SetItemWiseLtrScore(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto photo_fr_iw_pred_embedding = context.GetDoubleListCommonAttr("photo_fr_iw_pred_embedding");
    auto photo_fr_item_id_list = context.GetIntListCommonAttr("photo_fr_item_id_list");
    auto set_iw_pwtr_alpha = context.SetDoubleItemAttr("iw_pwtr_alpha");
    auto set_iw_pwtr_beta = context.SetDoubleItemAttr("iw_pwtr_beta");
    auto set_iw_pcmtr_alpha = context.SetDoubleItemAttr("iw_pcmtr_alpha");
    auto set_iw_pcmtr_beta = context.SetDoubleItemAttr("iw_pcmtr_beta");
    auto set_iw_pwtd_alpha = context.SetDoubleItemAttr("iw_pwtd_alpha");
    auto set_iw_pwtd_beta = context.SetDoubleItemAttr("iw_pwtd_beta");
    auto set_iw_pfvtr_alpha = context.SetDoubleItemAttr("iw_pfvtr_alpha");
    auto set_iw_pfvtr_beta = context.SetDoubleItemAttr("iw_pfvtr_beta");
    auto set_iw_pvtr_alpha = context.SetDoubleItemAttr("iw_pvtr_alpha");
    auto set_iw_pvtr_beta = context.SetDoubleItemAttr("iw_pvtr_beta");
    auto set_iw_lvtr_alpha = context.SetDoubleItemAttr("iw_lvtr_alpha");
    auto set_iw_lvtr_beta = context.SetDoubleItemAttr("iw_lvtr_beta");
    auto set_iw_pltr_alpha = context.SetDoubleItemAttr("iw_pltr_alpha");
    auto set_iw_pltr_beta = context.SetDoubleItemAttr("iw_pltr_beta");
    auto set_iw_uptr_alpha = context.SetDoubleItemAttr("iw_uptr_alpha");
    auto set_iw_uptr_beta = context.SetDoubleItemAttr("iw_uptr_beta");

    if (photo_fr_iw_pred_embedding && photo_fr_item_id_list) {
      int pred_size = photo_fr_iw_pred_embedding->size();
      int size_photo = photo_fr_item_id_list->size();
      if (pred_size == 8000 && size_photo <= 500) {
        int rank_photo = 1;
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          if (rank_photo <= size_photo) {
            set_iw_pwtr_alpha(result, photo_fr_iw_pred_embedding->at(4000 - (8 * rank_photo)));
            set_iw_pcmtr_alpha(result, photo_fr_iw_pred_embedding->at(4000 - (8 * rank_photo - 1)));
            set_iw_pwtd_alpha(result, photo_fr_iw_pred_embedding->at(4000 - (8 * rank_photo - 2)));
            set_iw_pfvtr_alpha(result, photo_fr_iw_pred_embedding->at(4000 - (8 * rank_photo - 3)));
            set_iw_pvtr_alpha(result, photo_fr_iw_pred_embedding->at(4000 - (8 * rank_photo - 4)));
            set_iw_lvtr_alpha(result, photo_fr_iw_pred_embedding->at(4000 - (8 * rank_photo - 5)));
            set_iw_pltr_alpha(result, photo_fr_iw_pred_embedding->at(4000 - (8 * rank_photo - 6)));
            set_iw_uptr_alpha(result, photo_fr_iw_pred_embedding->at(4000 - (8 * rank_photo - 7)));
            set_iw_pwtr_beta(result, photo_fr_iw_pred_embedding->at(8000 - (8 * rank_photo)));
            set_iw_pcmtr_beta(result, photo_fr_iw_pred_embedding->at(8000 - (8 * rank_photo - 1)));
            set_iw_pwtd_beta(result, photo_fr_iw_pred_embedding->at(8000 - (8 * rank_photo - 2)));
            set_iw_pfvtr_beta(result, photo_fr_iw_pred_embedding->at(8000 - (8 * rank_photo - 3)));
            set_iw_pvtr_beta(result, photo_fr_iw_pred_embedding->at(8000 - (8 * rank_photo - 4)));
            set_iw_lvtr_beta(result, photo_fr_iw_pred_embedding->at(8000 - (8 * rank_photo - 5)));
            set_iw_pltr_beta(result, photo_fr_iw_pred_embedding->at(8000 - (8 * rank_photo - 6)));
            set_iw_uptr_beta(result, photo_fr_iw_pred_embedding->at(8000 - (8 * rank_photo - 7)));
            rank_photo++;
          } else {
            set_iw_pwtr_alpha(result, 1);
            set_iw_pcmtr_alpha(result, 1);
            set_iw_pwtd_alpha(result, 1);
            set_iw_pfvtr_alpha(result, 1);
            set_iw_pvtr_alpha(result, 1);
            set_iw_lvtr_alpha(result, 1);
            set_iw_pltr_alpha(result, 1);
            set_iw_uptr_alpha(result, 1);
            set_iw_pwtr_beta(result, 0);
            set_iw_pcmtr_beta(result, 0);
            set_iw_pwtd_beta(result, 0);
            set_iw_pfvtr_beta(result, 0);
            set_iw_pvtr_beta(result, 0);
            set_iw_lvtr_beta(result, 0);
            set_iw_pltr_beta(result, 0);
            set_iw_uptr_beta(result, 0);          }
        });
        context.SetIntCommonAttr("SetItemWiseLtrScoreSucc", 1);
      } else {
        LOG(ERROR) << "SetItemWiseLtrScore Failed, pred_size : "  << pred_size
          << ", size_photo : " << size_photo;
        context.SetIntCommonAttr("SetItemWiseLtrScoreSucc", 0);
      }
    } else {
      LOG(ERROR) << "SetItemWiseLtrScoreSucc Failed, attr is null";
      context.SetIntCommonAttr("SetItemWiseLtrScoreSucc", 0);
    }
    return true;
  }

  static bool SetMixrankClmScore(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto mix_rank_clm_pred_embedding = context.GetDoubleListCommonAttr("mix_rank_clm_pred_embedding");
    auto live_fr_item_id_list = context.GetIntListCommonAttr("live_fr_item_id_list");
    auto photo_fr_item_id_list = context.GetIntListCommonAttr("photo_fr_item_id_list");
    auto set_mix_rank_clm_score = context.SetDoubleItemAttr("gbdt_mix_score");
    // 设置 CLM score, 由于 kuiba 的 list 特征是反的, 所以这里写的比较奇怪
    if (mix_rank_clm_pred_embedding && live_fr_item_id_list && photo_fr_item_id_list) {
      int size_all = mix_rank_clm_pred_embedding->size();
      int size_photo = photo_fr_item_id_list->size();
      int size_live = live_fr_item_id_list->size();
      if (size_all == 1000 && size_photo <= 500 && size_live <= 500) {
        int rank_photo = 1;
        int rank_live = 1;
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          if (result.GetType() == ks::reco::RecoEnum::ITEM_TYPE_PHOTO) {
            if (rank_photo <= size_photo) {
              set_mix_rank_clm_score(result, mix_rank_clm_pred_embedding->at(1000 - rank_photo));
              rank_photo++;
            } else {
              set_mix_rank_clm_score(result, 0.0);
            }
          } else {
            if (rank_live <= size_live) {
              set_mix_rank_clm_score(result, mix_rank_clm_pred_embedding->at(500 - rank_live));
              rank_live++;
            } else {
              set_mix_rank_clm_score(result, 0.0);
            }
          }
        });
        context.SetIntCommonAttr("SetMixrankClmScoreSucc", 1);
      } else {
        LOG(ERROR) << "SetMixrankClmScore Failed, size_all : "  << size_all
          << ", size_photo : " << size_photo
          << ", size_live : " << size_live;
        context.SetIntCommonAttr("SetMixrankClmScoreSucc", 0);
      }
    } else {
      LOG(ERROR) << "SetMixrankClmScore Failed, attr is null";
      context.SetIntCommonAttr("SetMixrankClmScoreSucc", 0);
    }
    return true;
  }

  static bool SetMixrankClmScoreV2(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto mix_rank_clm_pred_embedding = context.GetDoubleListCommonAttr("mix_rank_clm_pred_embedding");
    auto live_fr_item_id_list = context.GetIntListCommonAttr("live_fr_item_id_list");
    auto photo_fr_item_id_list = context.GetIntListCommonAttr("photo_fr_item_id_list");
    auto set_mix_rank_clm_score = context.SetDoubleItemAttr("gbdt_mix_score");
    auto set_gbdt_sort_score = context.SetDoubleItemAttr("gbdt_sort_score");
    // 设置 CLM score, 由于 kuiba 的 list 特征是反的, 所以这里写的比较奇怪
    if (mix_rank_clm_pred_embedding && live_fr_item_id_list && photo_fr_item_id_list) {
      int size_all = mix_rank_clm_pred_embedding->size();
      int size_photo = photo_fr_item_id_list->size();
      int size_live = live_fr_item_id_list->size();
      if (size_all >= 2000 && size_photo <= 500 && size_live <= 500) {
        int rank_photo = 1;
        int rank_live = 1;
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          if (result.GetType() == ks::reco::RecoEnum::ITEM_TYPE_PHOTO) {
            if (rank_photo <= size_photo) {
              set_mix_rank_clm_score(result, mix_rank_clm_pred_embedding->at(1000 - rank_photo));
              set_gbdt_sort_score(result, mix_rank_clm_pred_embedding->at(2000 - rank_photo));
              rank_photo++;
            } else {
              set_mix_rank_clm_score(result, 0.0);
              set_gbdt_sort_score(result, 0.0);
            }
          } else {
            if (rank_live <= size_live) {
              set_mix_rank_clm_score(result, mix_rank_clm_pred_embedding->at(500 - rank_live));
              set_gbdt_sort_score(result, mix_rank_clm_pred_embedding->at(1500 - rank_live));
              rank_live++;
            } else {
              set_mix_rank_clm_score(result, 0.0);
              set_gbdt_sort_score(result, 0.0);
            }
          }
        });
        if (size_all >= 2001) {
          context.SetDoubleCommonAttr("user_mixrank_live_preference", mix_rank_clm_pred_embedding->at(2000));
        }
        context.SetIntCommonAttr("SetMixrankClmScoreSucc", 1);
      } else {
        LOG(ERROR) << "SetMixrankClmScore Failed, size_all : "  << size_all
          << ", size_photo : " << size_photo
          << ", size_live : " << size_live;
        context.SetIntCommonAttr("SetMixrankClmScoreSucc", 0);
      }
    } else {
      LOG(ERROR) << "SetMixrankClmScore Failed, attr is null";
      context.SetIntCommonAttr("SetMixrankClmScoreSucc", 0);
    }
    return true;
  }

  static bool SetPhotoFrLtrClmScore(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto photo_fr_clm_pred_embedding = context.GetDoubleListCommonAttr("photo_fr_clm_pred_embedding");
    auto photo_fr_item_id_list = context.GetIntListCommonAttr("photo_fr_item_id_list");
    auto enable_photo_clm_realshow_score = context.GetIntCommonAttr("enable_photo_clm_realshow_score");
    auto set_mix_rank_clm_score = context.SetDoubleItemAttr("gbdt_mix_score");
    auto set_clm_effect_view_score = context.SetDoubleItemAttr("clm_effect_view_score");
    auto set_clm_interation_score = context.SetDoubleItemAttr("clm_interation_score");
    auto set_clm_realshow_score = context.SetDoubleItemAttr("clm_realshow_score");
    int emb_len = enable_photo_clm_realshow_score > 0 ? 2000 : 1500;
    // 设置 CLM score, 由于 kuiba 的 list 特征是反的, 所以这里写的比较奇怪
    if (photo_fr_clm_pred_embedding && photo_fr_item_id_list) {
      int size_all = photo_fr_clm_pred_embedding->size();
      int size_photo = photo_fr_item_id_list->size();
      if (size_all == emb_len && size_photo <= 500) {
        int rank_photo = 1;
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          if (rank_photo <= size_photo) {
            set_mix_rank_clm_score(result, photo_fr_clm_pred_embedding->at(500 - rank_photo));
            set_clm_effect_view_score(result, photo_fr_clm_pred_embedding->at(1000 - rank_photo));
            set_clm_interation_score(result, photo_fr_clm_pred_embedding->at(1500 - rank_photo));
            if (enable_photo_clm_realshow_score > 0) {
              set_clm_realshow_score(result, photo_fr_clm_pred_embedding->at(2000 - rank_photo));
            }
            rank_photo++;
          } else {
            set_mix_rank_clm_score(result, 0.0);
            set_clm_effect_view_score(result, 0.0);
            set_clm_interation_score(result, 0.0);
            set_clm_realshow_score(result, 0.0);
          }
        });
        context.SetIntCommonAttr("SetPhotoFrClmScoreSucc", 1);
      } else {
        LOG(ERROR) << "SetPhotoFrLtrClmScore Failed, size_all : "  << size_all
          << ", size_photo : " << size_photo;
        context.SetIntCommonAttr("SetPhotoFrClmScoreSucc", 0);
      }
    } else {
      LOG(ERROR) << "SetPhotoFrLtrClmScore Failed, attr is null";
      context.SetIntCommonAttr("SetPhotoFrClmScoreSucc", 0);
    }
    return true;
  }

  static bool SetPhotoSlideRerankContextScore(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto photo_rerank_context_pred_embedding =
        context.GetDoubleListCommonAttr("photo_rerank_context_pred_embedding");
    auto photo_rerank_item_id_list   = context.GetIntListCommonAttr("photo_rerank_item_id_list");
    auto enable_photo_clm_realshow_score = context.GetIntCommonAttr("enable_photo_clm_realshow_score");
    auto set_rerank_watch_time_score = context.SetDoubleItemAttr("rerank_watch_time_score");
    auto set_rerank_effect_view_score = context.SetDoubleItemAttr("rerank_effect_view_score");
    auto set_rerank_interation_score = context.SetDoubleItemAttr("rerank_interation_score");
    auto set_rerank_realshow_score = context.SetDoubleItemAttr("rerank_realshow_score");
    int emb_len = 120;
    // 设置 CLM score, 由于 kuiba 的 list 特征是反的, 所以这里写的比较奇怪
    if (photo_rerank_context_pred_embedding && photo_rerank_item_id_list) {
      int size_all = photo_rerank_context_pred_embedding->size();
      int size_photo = photo_rerank_item_id_list->size();
      if (size_all == emb_len && size_photo <= 30) {
        int rank_photo = 1;
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          if (rank_photo <= size_photo) {
            set_rerank_watch_time_score(result, photo_rerank_context_pred_embedding->at(30 - rank_photo));
            set_rerank_effect_view_score(result, photo_rerank_context_pred_embedding->at(60 - rank_photo));
            set_rerank_interation_score(result, photo_rerank_context_pred_embedding->at(90 - rank_photo));
            set_rerank_realshow_score(result, photo_rerank_context_pred_embedding->at(120 - rank_photo));
            rank_photo++;
          } else {
            set_rerank_watch_time_score(result, 0.0);
            set_rerank_effect_view_score(result, 0.0);
            set_rerank_interation_score(result, 0.0);
            set_rerank_realshow_score(result, 0.0);
          }
        });
        context.SetIntCommonAttr("SetPhotoSlideRerankContextScoreSucc", 1);
      } else {
        LOG(ERROR) << "SetPhotoSlideRerankContextScore Failed, size_all : "  << size_all
          << ", size_photo : " << size_photo;
        context.SetIntCommonAttr("SetPhotoSlideRerankContextScoreSucc", 0);
      }
    } else {
      LOG(ERROR) << "SetPhotoSlideRerankContextScore Failed, attr is null";
      context.SetIntCommonAttr("SetPhotoSlideRerankContextScoreSucc", 0);
    }
    return true;
  }
  static int GetDurationLevel(int duration_ms) {
    if (duration_ms == 0) {
      return 0;
    } else if (duration_ms <= 6963) {
      return 1;
    } else if (duration_ms <= 10457) {
      return 2;
    } else if (duration_ms <= 11952) {
      return 3;
    } else if (duration_ms <= 15430) {
      return 4;
    } else if (duration_ms <= 21720) {
      return 5;
    } else if (duration_ms <= 31443) {
      return 6;
    } else if (duration_ms <= 46100) {
      return 7;
    } else if (duration_ms <= 64604) {
      return 8;
    } else if (duration_ms <= 110370) {
      return 9;
    } else {
      return 10;
    }
  }

  static bool EnrichPhotoFrContextAttr(const CommonRecoLightFunctionContext &context,
                  RecoResultConstIter begin, RecoResultConstIter end) {
    struct ContextAttrInfo {
        int64 photo_id = -1;
        int64 author_id = -1;
        int64 hetu_level_one = 0;
        int64 duration_ms = 0;
        ContextAttrInfo(int64 pid, int64 aid, int64 tag, int64 dura)
            : photo_id(pid), author_id(aid), hetu_level_one(tag), duration_ms(dura) {}
        ContextAttrInfo() : photo_id(-1), author_id(-1), hetu_level_one(0), duration_ms(0){}
    };
    std::vector<ContextAttrInfo> context_attr_infos;
    auto hetu_level_one_accessor =
              context.GetIntItemAttr("hetu_level_one");
    auto duration_ms_accessor =
              context.GetIntItemAttr("item_info.duration_ms");
    auto author_id_accessor =
              context.GetIntItemAttr("item_info.author_id");
    for (auto result = begin; result != end; ++result) {
      int64 photo_id = result->GetId();
      int64 hetu_level_one = hetu_level_one_accessor(*result).value_or(0);
      int64 author_id = author_id_accessor(*result).value_or(0);
      int64 duration_ms = duration_ms_accessor(*result).value_or(0);
      context_attr_infos.emplace_back(photo_id, author_id, hetu_level_one, duration_ms);
    }
    std::random_shuffle(context_attr_infos.begin(), context_attr_infos.end());
    std::vector<int64> fr_photo_context_pids;
    std::vector<int64> fr_photo_context_aids;
    std::vector<int64> fr_photo_context_tags;
    std::vector<int64> fr_photo_context_duration_ms;
    for (int i = 0; i < context_attr_infos.size() && i < 100; i++) {
      fr_photo_context_pids.push_back(context_attr_infos[i].photo_id);
      fr_photo_context_aids.push_back(context_attr_infos[i].author_id);
      fr_photo_context_tags.push_back(context_attr_infos[i].hetu_level_one);
      fr_photo_context_duration_ms.push_back(context_attr_infos[i].duration_ms);
    }
    context.SetIntListCommonAttr("fr_photo_context_pids", std::move(fr_photo_context_pids));
    context.SetIntListCommonAttr("fr_photo_context_aids", std::move(fr_photo_context_aids));
    context.SetIntListCommonAttr("fr_photo_context_tags", std::move(fr_photo_context_tags));
    context.SetIntListCommonAttr("fr_photo_context_duration_ms", std::move(fr_photo_context_duration_ms));
    // item attr
    auto social_type_size_before = context.GetIntItemAttr("social_type_size");
    auto is_author_social_card_holdout_accessor = context.GetIntItemAttr("is_author_social_card_holdout");
    auto set_social_type_size = context.SetIntItemAttr("social_type_size");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      // 1. social card
      int social_type_size = social_type_size_before(result).value_or(0);
      int is_author_social_card_holdout = is_author_social_card_holdout_accessor(result).value_or(0);
      if (is_author_social_card_holdout > 0) {
        social_type_size = 0;
      }
      set_social_type_size(result, social_type_size);
      // 2.
    });
    return true;
  }
  static bool CalItemDebiasScore(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    std::string debias_prefix = std::string(
        context.GetStringCommonAttr("debias_prefix").value_or("main_fr_debias_"));
    std::string debias_channel_type = std::string(
        context.GetStringCommonAttr("debias_channel_type").value_or("0"));
    std::unordered_map<std::string, std::vector<double>> main_fr_debias;
    std::vector<std::string> xtrs{"lvtr", "ltr", "wtr", "vtr"};
    for (int i = 0; i <= 39; ++i) {
      for (auto &xtr : xtrs) {
        double temp = context
                          .GetDoubleCommonAttr(debias_prefix + debias_channel_type + "_" +
                                               base::IntToString(i) + "_" + xtr)
                          .value_or(1.0);
        main_fr_debias[xtr].push_back(temp);
      }
    }
    auto hetu_level_ones_accessor =
        context.GetIntListItemAttr("item_info.hetu_tag_level_info_v3.hetu_level_one");
    auto fullrank_ori_lvtr_accessor = context.GetDoubleItemAttr("fullrank_ori_lvtr");
    auto fullrank_ori_ltr_accessor = context.GetDoubleItemAttr("fullrank_ori_ltr");
    auto fullrank_ori_wtr_accessor = context.GetDoubleItemAttr("fullrank_ori_wtr");
    auto fullrank_ori_vtr_accessor = context.GetDoubleItemAttr("fullrank_ori_nvtr");

    auto fr_debias_plvtr = context.SetDoubleItemAttr("fr_debias_plvtr");
    auto fr_debias_pltr = context.SetDoubleItemAttr("fr_debias_pltr");
    auto fr_debias_pwtr = context.SetDoubleItemAttr("fr_debias_pwtr");
    auto fr_debias_pvtr = context.SetDoubleItemAttr("fr_debias_pvtr");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto hetu_list = hetu_level_ones_accessor(result);
      int64 hetu_level_one = 0;
      if (hetu_list && hetu_list->size() > 0) {
        hetu_level_one = hetu_list->at(0);
      }
      double fr_plvtr = fullrank_ori_lvtr_accessor(result).value_or(0.0);
      double fr_pltr = fullrank_ori_ltr_accessor(result).value_or(0.0);
      double fr_pwtr = fullrank_ori_wtr_accessor(result).value_or(0.0);
      double fr_pvtr = fullrank_ori_vtr_accessor(result).value_or(0.0);
      double debias_plvtr = 0.0;
      double debias_pltr = 0.0;
      double debias_pwtr = 0.0;
      double debias_pvtr = 0.0;
      if (main_fr_debias.find("lvtr") != main_fr_debias.end() &&
          hetu_level_one <= main_fr_debias.at("lvtr").size() &&
          main_fr_debias.at("lvtr")[hetu_level_one] > 0) {
        debias_plvtr = fr_plvtr / (main_fr_debias.at("lvtr")[hetu_level_one] + 1e-6);
      }
      if (main_fr_debias.find("ltr") != main_fr_debias.end() &&
          hetu_level_one <= main_fr_debias.at("ltr").size() &&
          main_fr_debias.at("ltr")[hetu_level_one] > 0) {
        debias_pltr = fr_pltr / (main_fr_debias.at("ltr")[hetu_level_one] + 1e-6);
      }
      if (main_fr_debias.find("wtr") != main_fr_debias.end() &&
          hetu_level_one <= main_fr_debias.at("wtr").size() &&
          main_fr_debias.at("wtr")[hetu_level_one] > 0) {
        debias_pwtr = fr_pwtr / (main_fr_debias.at("wtr")[hetu_level_one] + 1e-6);
      }
      if (main_fr_debias.find("vtr") != main_fr_debias.end() &&
          hetu_level_one <= main_fr_debias.at("vtr").size() &&
          main_fr_debias.at("vtr")[hetu_level_one] > 0) {
        debias_pvtr = fr_pvtr / (main_fr_debias.at("vtr")[hetu_level_one] + 1e-6);
      }
      fr_debias_plvtr(result, debias_plvtr);
      fr_debias_pltr(result, debias_pltr);
      fr_debias_pwtr(result, debias_pwtr);
      fr_debias_pvtr(result, debias_pvtr);
    });
    return true;
  }

  static bool CalcMcFrConsistency(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto cas_rank_accessor = context.GetIntItemAttr("cas_rank");
    int64 photo_fullrank_cnt = context.GetIntCommonAttr("photo_fullrank_cnt").value_or(-2);
    int consist_top10 = 0;
    int consist_top20 = 0;
    int consist_top50 = 0;
    int consist_top100 = 0;
    double sum_square = 0;
    int index = 1;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int cas_rank = cas_rank_accessor(result).value_or(10000);
      sum_square += std::pow(cas_rank - index, 2);
      if (index <= 100 && cas_rank <= 100) {
        consist_top100++;
      }
      if (index <= 50 && cas_rank <= 50) {
        consist_top50++;
      }
      if (index <= 20 && cas_rank <= 20) {
        consist_top20++;
      }
      if (index <= 10 && cas_rank <= 10) {
        consist_top10++;
      }
      index++;
    });
    double spearman = 0.0;
    if (photo_fullrank_cnt > 1) {  // photo_fullrank_cnt 为 0 或 1 时，分母会变成 0
      spearman = 1 - (6 * sum_square / (photo_fullrank_cnt * (std::pow(photo_fullrank_cnt, 2) - 1)));
    }
    context.SetIntCommonAttr("consistency_spearman", std::floor(10000 * spearman));
    context.SetIntCommonAttr("consistency_top10", consist_top10);
    context.SetIntCommonAttr("consistency_top20", consist_top20);
    context.SetIntCommonAttr("consistency_top50", consist_top50);
    context.SetIntCommonAttr("consistency_top100", consist_top100);
    return true;
  }

  static bool CalcLiveMcFrConsistency(const CommonRecoLightFunctionContext &context,
                                  RecoResultConstIter begin, RecoResultConstIter end) {
    auto cas_rank_accessor = context.GetIntItemAttr("cas_rank");
    int64 live_fullrank_cnt = context.GetIntCommonAttr("live_fullrank_cnt").value_or(-2);
    int consist_top10 = 0;
    int consist_top20 = 0;
    int consist_top50 = 0;
    int consist_top100 = 0;
    double sum_square = 0;
    int index = 1;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int cas_rank = cas_rank_accessor(result).value_or(10000);
      sum_square += std::pow(cas_rank - index, 2);
      if (index <= 100 && cas_rank <= 100) {
        consist_top100++;
      }
      if (index <= 50 && cas_rank <= 50) {
        consist_top50++;
      }
      if (index <= 20 && cas_rank <= 20) {
        consist_top20++;
      }
      if (index <= 10 && cas_rank <= 10) {
        consist_top10++;
      }
      index++;
    });
    double spearman = 0.0;
    if (live_fullrank_cnt > 1) {  // live_fullrank_cnt 为 0 或 1 时，分母会变成 0
      spearman = 1 - (6 * sum_square / (live_fullrank_cnt * (std::pow(live_fullrank_cnt, 2) - 1)));
    }
    context.SetIntCommonAttr("live_consistency_spearman", std::floor(10000 * spearman));
    context.SetIntCommonAttr("live_consistency_top10", consist_top10);
    context.SetIntCommonAttr("live_consistency_top20", consist_top20);
    context.SetIntCommonAttr("live_consistency_top50", consist_top50);
    context.SetIntCommonAttr("live_consistency_top100", consist_top100);
    return true;
  }

  static bool CalcMcRerankConsistency(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto cas_rank_accessor = context.GetIntItemAttr("cas_rank");
    int64 photo_rerank_cnt = context.GetIntCommonAttr("photo_rerank_cnt").value_or(-2);
    int consist_top10 = 0;
    int consist_top20 = 0;
    int consist_top50 = 0;
    int consist_top100 = 0;
    double sum_square = 0;
    int index = 1;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int cas_rank = cas_rank_accessor(result).value_or(10000);
      sum_square += std::pow(cas_rank - index, 2);
      if (index <= 100 && cas_rank <= 100) {
        consist_top100++;
      }
      if (index <= 50 && cas_rank <= 50) {
        consist_top50++;
      }
      if (index <= 20 && cas_rank <= 20) {
        consist_top20++;
      }
      if (index <= 10 && cas_rank <= 10) {
        consist_top10++;
      }
      index++;
    });
    double spearman = 0.0;
    if (photo_rerank_cnt > 1) {  // photo_rerank_cnt 为 0 或 1 时，分母会变成 0
      spearman = 1 - (6 * sum_square / (photo_rerank_cnt * (std::pow(photo_rerank_cnt, 2) - 1)));
    }
    context.SetIntCommonAttr("mc_rerank_consist_spearman", std::floor(10000 * spearman));
    context.SetIntCommonAttr("mc_rerank_consist_top10", consist_top10);
    context.SetIntCommonAttr("mc_rerank_consist_top20", consist_top20);
    context.SetIntCommonAttr("mc_rerank_consist_top50", consist_top50);
    context.SetIntCommonAttr("mc_rerank_consist_top100", consist_top100);
    return true;
  }

  static bool FillTopListSampleAttr(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto context_list_score_accessor = context.GetDoubleItemAttr("final_list_rerank_score");
    double max_score = 0.00000001;
    std::vector<int64> top_list_keys;
    std::vector<int64> top_list_types;
    auto list_sample_item_keys_accessor = context.GetIntListItemAttr("list_sample_item_keys");
    auto list_sample_item_types_accessor = context.GetIntListItemAttr("list_sample_item_types");
    int reason = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double score = context_list_score_accessor(result).value_or(0.0);
      if (score > max_score) {
        max_score = score;
        auto list_sample_item_keys = list_sample_item_keys_accessor(result);
        auto list_sample_item_types = list_sample_item_types_accessor(result);
        if (list_sample_item_keys && list_sample_item_types && list_sample_item_keys->size() > 0) {
          top_list_keys.clear();
          top_list_types.clear();
          reason = result.reason;
          for (int i = 0; i < list_sample_item_keys->size(); i++) {
            top_list_keys.push_back(list_sample_item_keys->at(i));
          }
          for (int i = 0; i < list_sample_item_types->size(); i++) {
            top_list_types.push_back(list_sample_item_types->at(i));
          }
        }
      }
    });
    if (top_list_keys.size() > 0) {
      context.SetIntListCommonAttr("top_list_sample_keys", std::move(top_list_keys));
      context.SetIntListCommonAttr("top_list_sample_types", std::move(top_list_types));
      context.SetIntCommonAttr("FillTopListSampleAttr_succ", 1);
    } else {
      context.SetIntCommonAttr("FillTopListSampleAttr_succ", 0);
    }
    return true;
  }

  static bool SetListWiseRerankScore(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto top_list_sample_keys = context.GetIntListCommonAttr("top_list_sample_keys");
    auto set_listwise_rerank_score = context.SetDoubleItemAttr("listwise_rerank_score");
    int64 list_mixrank_start_index = context.GetIntCommonAttr("list_mixrank_start_index").value_or(0);
    if (top_list_sample_keys && top_list_sample_keys->size() > 0) {
      int size = top_list_sample_keys->size();
      std::unordered_map<int64, double> top_list_sample_score_map;
      for (int i = 0; i < size; i++) {
        top_list_sample_score_map.insert({top_list_sample_keys->at(i), (size - i) * 100.0});
      }
      double index = 1.0;
      int pass_num = 0;
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        double score = 10.0 / index;
        if (index < list_mixrank_start_index + 0.5) {
          score += 100000.0;
          pass_num++;
        } else if (top_list_sample_score_map.find(result.item_key) != top_list_sample_score_map.end()) {
          score += top_list_sample_score_map.at(result.item_key);
        }
        set_listwise_rerank_score(result, score);
        index += 1.0;
      });
    }
    return true;
  }
  static bool FillSlideTopListSampleAttr(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto context_list_score_accessor = context.GetDoubleItemAttr("final_list_rerank_score");
    double max_score = 0.00000001;
    // double min_score = 10000.0;
    std::vector<int64> top_list_keys;
    std::vector<int64> top_list_ranks;
    auto list_sample_item_keys_accessor = context.GetIntListItemAttr("list_sample_item_keys");
    auto list_sample_item_ranks_accessor = context.GetIntListItemAttr("list_sample_item_ranks");
    int slide_rerank_pick_reason = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double score = context_list_score_accessor(result).value_or(0.0);
      // if (score < min_score) {
      //   min_score = score;
      // }
      if (score > max_score) {
        max_score = score;
        auto list_sample_item_keys = list_sample_item_keys_accessor(result);
        auto list_sample_item_ranks = list_sample_item_ranks_accessor(result);
        if (list_sample_item_keys && list_sample_item_ranks && list_sample_item_keys->size() > 0) {
          top_list_keys.clear();
          top_list_ranks.clear();
          slide_rerank_pick_reason = result.reason;
          for (int i = 0; i < list_sample_item_keys->size(); i++) {
            top_list_keys.push_back(list_sample_item_keys->at(i));
          }
          for (int i = 0; i < list_sample_item_ranks->size(); i++) {
            top_list_ranks.push_back(list_sample_item_ranks->at(i));
          }
        }
      }
    });
    if (top_list_keys.size() > 0) {
      // std::string str_top_list = "";
      // for (auto rank : top_list_ranks) {
      //   str_top_list += base::Int64ToString(rank);
      //   str_top_list += ",";
      // }
      context.SetIntListCommonAttr("top_list_sample_keys", std::move(top_list_keys));
      context.SetIntListCommonAttr("top_list_sample_ranks", std::move(top_list_ranks));
      // LOG(ERROR)<<"top_list_ranks : "<<str_top_list
      // <<", max_score : "<<max_score
      // <<", min_score : "<<min_score;
      context.SetIntCommonAttr("FillSlideTopListSampleAttr_succ", 1);
    } else {
      context.SetIntCommonAttr("FillSlideTopListSampleAttr_succ", 0);
    }
    context.SetIntCommonAttr("slide_rerank_pick_reason", slide_rerank_pick_reason);
    return true;
  }
  static bool CalPhotoReportValid(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto report_count_accessor = context.GetIntItemAttr("report_count");
    auto safe_level_accessor = context.GetIntItemAttr("item_info.safe_level");

    auto negative_count_accessor = context.GetIntItemAttr("negative_count");
    auto realshow_count_accessor = context.GetIntItemAttr("realshow_count");

    int64 photo_negative_filter_num_bound =
          context.GetIntCommonAttr("photo_negative_filter_num_bound").value_or(1000);
    double negative_rate_bound =
        context.GetDoubleCommonAttr("nearby_photo_negative_filter_rate_bound").value_or(0.001);

    int64 photo_report_filter_num_bound =
          context.GetIntCommonAttr("photo_report_filter_num_bound").value_or(100);
    int64 user_app_negative_num =
          context.GetIntCommonAttr("user_app_negative_num").value_or(0);
    int64 nearby_photo_green_safe_level_bound =
          context.GetIntCommonAttr("nearby_photo_green_safe_level_bound").value_or(5);
    double report_rate_bound =
        context.GetDoubleCommonAttr("nearby_photo_report_filter_rate_bound").value_or(0.00001);
    auto report_hetu_bottom_tags = context.GetIntListCommonAttr("report_hetu_bottom_tags");
    if (user_app_negative_num > 0 ||
        (report_hetu_bottom_tags && report_hetu_bottom_tags->size() > 0)) {
      photo_report_filter_num_bound =
          context.GetIntCommonAttr("sensitive_photo_report_filter_num_bound").value_or(5);
      nearby_photo_green_safe_level_bound =
          context.GetIntCommonAttr("sensitive_nearby_photo_green_safe_level_bound").value_or(5);
      report_rate_bound =
          context.GetDoubleCommonAttr("nearby_sensitive_photo_report_filter_rate_bound").value_or(0.000001);
    }
    auto is_photo_report_invalid_set = context.SetIntItemAttr("is_photo_report_invalid");
    auto is_photo_negative_invalid_set = context.SetIntItemAttr("is_photo_negative_invalid");
    auto report_rate_set = context.SetDoubleItemAttr("report_rate");
    auto negative_rate_set = context.SetDoubleItemAttr("negative_rate");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int report_count = report_count_accessor(result).value_or(0);
      int safe_level = safe_level_accessor(result).value_or(0);
      int64 realshow_count = realshow_count_accessor(result).value_or(0);
      int64 negative_count = negative_count_accessor(result).value_or(0);
      double report_rate = (report_count + 0.05) / (realshow_count + 10000.0);
      int is_photo_report_invalid = 0;
      int is_photo_negative_invalid = 0;
      if (safe_level < nearby_photo_green_safe_level_bound &&
          (report_rate > report_rate_bound || report_count > photo_report_filter_num_bound)) {
        is_photo_report_invalid = 1;
      }
      is_photo_report_invalid_set(result, is_photo_report_invalid);
      report_rate_set(result, report_rate);
      double negative_rate = (negative_count + 0.1) / (realshow_count + 1000.0);
      if (safe_level < nearby_photo_green_safe_level_bound &&
          (negative_rate > negative_rate_bound
          || negative_count > photo_negative_filter_num_bound)) {
        is_photo_negative_invalid = 1;
      }
      is_photo_negative_invalid_set(result, is_photo_negative_invalid);
      negative_rate_set(result, negative_rate);
    });
    return true;
  }

static bool SetPhotoRctrScore(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                              RecoResultConstIter end) {
  auto photo_fr_rctr_pred_embedding = context.GetDoubleListCommonAttr("photo_fr_rctr_pred_embedding");
  auto today_photo_list = context.GetIntListCommonAttr("today_photo_list");
  auto set_rctr_score = context.SetDoubleItemAttr("rctr_score");
  auto set_att_rctr_score = context.SetDoubleItemAttr("att_rctr_score");

  if (photo_fr_rctr_pred_embedding && today_photo_list) {
    int size_all = photo_fr_rctr_pred_embedding->size();
    int size_photo = today_photo_list->size();
    if (size_all == 1000 && size_photo <= 500) {
      int rank_photo = 1;
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        if (rank_photo < size_photo) {
            set_rctr_score(result, photo_fr_rctr_pred_embedding->at(size_photo - rank_photo));
            set_att_rctr_score(result, photo_fr_rctr_pred_embedding->at(500 + size_photo - rank_photo));
        } else {
            set_rctr_score(result, 0.0);
            set_att_rctr_score(result, 0.0);
        }
        rank_photo++;
      });

      context.SetIntCommonAttr("SetPhotoRctrScoreSucc", 1);
    } else {
      LOG(ERROR) << "SetPhotoRctrScore Failed, size_all : " << size_all << ", size_photo : " << size_photo;
      context.SetIntCommonAttr("SetPhotoRctrScoreSucc", 0);
    }
  } else {
    if (!photo_fr_rctr_pred_embedding)
      LOG(ERROR) << "SetPhotoRctrScore Failed, photo_fr_rctr_pred_embedding is null";
    if (!today_photo_list) LOG(ERROR) << "SetPhotoRctrScore Failed, today_photo_list is null";
    context.SetIntCommonAttr("SetPhotoRctrScoreSucc", 0);
  }
  return true;
}

  static bool SetPhotoFrClmScore(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    auto photo_fr_clm_pred_embedding = context.GetDoubleListCommonAttr("photo_fr_clm_pred_embedding");
    auto photo_fr_item_id_list = context.GetIntListCommonAttr("photo_fr_item_id_list");
    auto set_mix_rank_clm_score = context.SetDoubleItemAttr("gbdt_mix_score");
    // 设置 CLM score, 由于 kuiba 的 list 特征是反的, 所以这里写的比较奇怪
    if (photo_fr_clm_pred_embedding && photo_fr_item_id_list) {
      int size_all = photo_fr_clm_pred_embedding->size();
      int size_photo = photo_fr_item_id_list->size();
      if (size_all == 500 && size_photo <= 500) {
        int rank_photo = 1;
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          if (rank_photo <= size_photo) {
            set_mix_rank_clm_score(result, photo_fr_clm_pred_embedding->at(500 - rank_photo));
            rank_photo++;
          } else {
            set_mix_rank_clm_score(result, 0.0);
          }
        });
        context.SetIntCommonAttr("SetPhotoFrClmScoreSucc", 1);
      } else {
        LOG(ERROR) << "SetPhotoFrClmScore Failed, size_all : "  << size_all
          << ", size_photo : " << size_photo;
        context.SetIntCommonAttr("SetPhotoFrClmScoreSucc", 0);
      }
    } else {
      LOG(ERROR) << "SetPhotoFrClmScore Failed, attr is null";
      context.SetIntCommonAttr("SetPhotoFrClmScoreSucc", 0);
    }
    return true;
  }
  static bool CalcCascadeTaskConsistency(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    auto cotrain_rank_accessor = context.GetIntItemAttr("cotrain_rank");
    auto show_rank_accessor = context.GetIntItemAttr("show_rank");
    int64 photo_fullrank_cnt = context.GetIntCommonAttr("photo_fullrank_cnt").value_or(-2);
    int cotrain_top10 = 0;
    int cotrain_top20 = 0;
    int cotrain_top50 = 0;
    int cotrain_top100 = 0;
    int show_top10 = 0;
    int show_top20 = 0;
    int show_top50 = 0;
    int show_top100 = 0;
    double cotrain_sum_square = 0;
    double show_sum_square = 0;
    int index = 1;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int cotrain_rank = cotrain_rank_accessor(result).value_or(10000);
      int show_rank = show_rank_accessor(result).value_or(10000);
      cotrain_sum_square += std::pow(cotrain_rank - index, 2);
      show_sum_square += std::pow(show_rank - index, 2);
      if (index <= 100) {
        if (cotrain_rank <= 100) {
          cotrain_top100++;
        }
        if (show_rank <= 100) {
          show_top100++;
        }
      }
      if (index <= 50) {
        if (cotrain_rank <= 50) {
          cotrain_top50++;
        }
        if (show_rank <= 50) {
          show_top50++;
        }
      }
      if (index <= 20) {
        if (cotrain_rank <= 20) {
          cotrain_top20++;
        }
        if (show_rank <= 20) {
          show_top20++;
        }
      }
      if (index <= 10) {
        if (cotrain_rank <= 10) {
          cotrain_top10++;
        }
        if (show_rank <= 10) {
          show_top10++;
        }
      }
      index++;
    });
    double cotrain_spearman = 0.0;
    double show_spearman = 0.0;
    if (photo_fullrank_cnt > 1) {  // photo_fullrank_cnt 为 0 或 1 时，分母会变成 0
      cotrain_spearman =
          1 - (6 * cotrain_sum_square / (photo_fullrank_cnt * (std::pow(photo_fullrank_cnt, 2) - 1)));
      show_spearman =
          1 - (6 * show_sum_square / (photo_fullrank_cnt * (std::pow(photo_fullrank_cnt, 2) - 1)));
    }
    context.SetIntCommonAttr("cotrain_spearman", std::floor(10000 * cotrain_spearman));
    context.SetIntCommonAttr("cotrain_top10", cotrain_top10);
    context.SetIntCommonAttr("cotrain_top20", cotrain_top20);
    context.SetIntCommonAttr("cotrain_top50", cotrain_top50);
    context.SetIntCommonAttr("cotrain_top100", cotrain_top100);
    context.SetIntCommonAttr("show_spearman", std::floor(10000 * show_spearman));
    context.SetIntCommonAttr("show_top10", show_top10);
    context.SetIntCommonAttr("show_top20", show_top20);
    context.SetIntCommonAttr("show_top50", show_top50);
    context.SetIntCommonAttr("show_top100", show_top100);
    return true;
  }
  static bool SlideDiversityAttrEnrich(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    // 内流拆分主站极速版做参数 enrich
    // common attr
    int64 product_type = context.GetIntCommonAttr("uProductType").value_or(0);
    std::string fr_clm_kess_name = "grpc_nearbySlidePhotoFrClmInferServer";
    int64 enable_slide_photo_fr_clm_process = 0;
    int64 enable_photo_clm_realshow_score = 0;
    int64 enable_slide_photo_fr_clm_ltr_process = 0;
    double photo_slide_mix_rerank_boost_coeff = 1.0;

    auto enable_Trial_Version_author_diversity =
      context.GetIntCommonAttr("enable_Trial_Version_author_diversity").value_or(0);
    auto isTrialVersion =
      context.GetIntCommonAttr("isTrialVersion").value_or(0);
    auto trial_diversity_aid_window_size =
      context.GetIntCommonAttr("no_first_diversity_author_id_window_size").value_or(0);
    auto trial_diversity_author_id_max_num =
      context.GetIntCommonAttr("no_first_diversity_author_id_max_num").value_or(0);
    if (enable_Trial_Version_author_diversity > 0 && isTrialVersion > 0) {
      trial_diversity_aid_window_size =
        context.GetIntCommonAttr("trial_diversity_author_id_window_size").value_or(0);
      trial_diversity_author_id_max_num =
        context.GetIntCommonAttr("trial_diversity_author_id_max_num").value_or(0);
    }
    context.SetIntCommonAttr("no_first_diversity_author_id_window_size", trial_diversity_aid_window_size);
    context.SetIntCommonAttr("no_first_diversity_author_id_max_num", trial_diversity_author_id_max_num);


    if (product_type == 1) {
      fr_clm_kess_name = std::string(
      context.GetStringCommonAttr("nearby_slide_fr_clm_kess_names_nebula")
      .value_or("grpc_nearbyBlSlidePhotoFrClmInferServer"));
      enable_slide_photo_fr_clm_process =
        context.GetIntCommonAttr("enable_slide_photo_fr_clm_process_nebula").value_or(0);
      enable_slide_photo_fr_clm_ltr_process =
        context.GetIntCommonAttr("enable_slide_photo_fr_clm_ltr_process_nebula").value_or(0);
      enable_photo_clm_realshow_score =
        context.GetIntCommonAttr("enable_photo_clm_realshow_score_nebula").value_or(0);
      photo_slide_mix_rerank_boost_coeff =
        context.GetDoubleCommonAttr("nebula_photo_slide_mix_rerank_boost_coeff").value_or(1.0);
    } else {
      fr_clm_kess_name = std::string(
        context.GetStringCommonAttr("nearby_slide_fr_clm_kess_name_main")
        .value_or("grpc_nearbySlidePhotoFrClmInferServer"));
      enable_slide_photo_fr_clm_process =
        context.GetIntCommonAttr("enable_slide_photo_fr_clm_process_main").value_or(0);
      enable_slide_photo_fr_clm_ltr_process =
        context.GetIntCommonAttr("enable_slide_photo_fr_clm_ltr_process_main").value_or(0);
      enable_photo_clm_realshow_score =
        context.GetIntCommonAttr("enable_photo_clm_realshow_score_main").value_or(0);
      photo_slide_mix_rerank_boost_coeff =
        context.GetDoubleCommonAttr("main_photo_slide_mix_rerank_boost_coeff").value_or(1.0);
    }
    context.SetIntCommonAttr("enable_slide_photo_fr_clm_process", enable_slide_photo_fr_clm_process);
    context.SetStringCommonAttr("nearby_slide_fr_clm_kess_name", fr_clm_kess_name);
    context.SetIntCommonAttr("enable_photo_clm_realshow_score", enable_photo_clm_realshow_score);
    context.SetIntCommonAttr("enable_slide_photo_fr_clm_ltr_process", enable_slide_photo_fr_clm_ltr_process);
    context.SetDoubleCommonAttr("photo_slide_mix_rerank_boost_coeff", photo_slide_mix_rerank_boost_coeff);
    // item attr
    auto social_type_size_before = context.GetIntItemAttr("social_type_size");
    auto is_author_social_card_holdout_accessor = context.GetIntItemAttr("is_author_social_card_holdout");
    auto set_social_type_size = context.SetIntItemAttr("social_type_size");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      // 1. social card
      int social_type_size = social_type_size_before(result).value_or(0);
      int is_author_social_card_holdout = is_author_social_card_holdout_accessor(result).value_or(0);
      if (is_author_social_card_holdout > 0) {
        social_type_size = 0;
      }
      set_social_type_size(result, social_type_size);
      // 2.
    });
    return true;
  }

  static bool CalSuperiorityScore(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);

  static bool CalHetuShowCntDebiasQueue(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    std::unordered_map<int64, double> show_cnt_debias;
    for (int i = 0; i <= 39; ++i) {
      double temp = context.GetDoubleCommonAttr("show_cnt_debias_" + base::IntToString(i)).value_or(1.0);
      show_cnt_debias.insert({i, temp});
    }
    auto hetu_level_ones_accessor = context.GetIntItemAttr("hetu_level_one");

    auto hetu_debias_value = context.SetDoubleItemAttr("hetu_debias_value");
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      auto hetu_level_one = hetu_level_ones_accessor(result).value_or(0);
      double value = 1.0;
      if (show_cnt_debias.find(hetu_level_one) != show_cnt_debias.end() &&
          show_cnt_debias.at(hetu_level_one) > 0) {
        value = show_cnt_debias.at(hetu_level_one);
      }
      hetu_debias_value(result, value);
    });
    return true;
  }
  static bool FilterOtherSendMessageFromTopK(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
      auto send_message_accessor = context.GetDoubleItemAttr("is_send_message_function");
      auto send_message_setter = context.SetDoubleItemAttr("is_send_message_function");
      bool flag = false;
      int index = 0;
      if (!send_message_accessor) {
        LOG(ERROR) << "send_message is null";
      } else {
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          double send_message = send_message_accessor(result).value_or(-1.0);
          if (flag == true && index < 10) {
            send_message_setter(result, 0.0);
          }
          if (flag == false && send_message == 1.0) {
            flag = true;
          }
          index++;
        });
      }
      return true;
  }
  static bool CalSafeDiversityValue(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto data_set_tags_accessor = context.GetIntListItemAttr("item_info.data_set_tags");
    auto set_nearby_outslide_safe_diversity_value = context.SetIntItemAttr
            ("nearby_outslide_safe_diversity_value");
    auto set_nearby_inslide_safe_diversity_value = context.SetIntItemAttr
            ("nearby_inslide_safe_diversity_value");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto data_set_tags = data_set_tags_accessor(result);
        if (data_set_tags && data_set_tags->size() > 0) {
          for (int i = 0; i < data_set_tags->size(); i++) {
            if (data_set_tags->at(i) == 69) {
              set_nearby_outslide_safe_diversity_value(result, 1);
            }
            if (data_set_tags->at(i) == 68) {
              set_nearby_inslide_safe_diversity_value(result, 1);
            }
          }
        }
    });
    return true;
  }

static bool CalMixInsideRankScore(const CommonRecoLightFunctionContext &context,
                                  RecoResultConstIter begin, RecoResultConstIter end) {
    auto enable_live_filter = context.GetIntCommonAttr("enable_live_filter_for_low_consume").value_or(0);
    double computed_live_consume_score_bound = 0.0;
    auto live_consume_score_accessor = context.GetDoubleItemAttr("live_consume_score");
    if (enable_live_filter > 0) {
        auto live_consume_score_bound_quantile =
            context.GetDoubleCommonAttr("live_consume_score_bound_quantile").value_or(0.05);
        std::vector<double> live_consume_scores;
        for (auto it = begin; it != end; ++it) {
            const CommonRecoResult &result = *it;
            if (result.GetType() != ks::reco::RecoEnum::ITEM_TYPE_PHOTO) {
                double score = live_consume_score_accessor(result).value_or(0.0);
                live_consume_scores.push_back(score);
            }
        }
        if (!live_consume_scores.empty()) {
            std::sort(live_consume_scores.begin(), live_consume_scores.end());
            int index = floor(live_consume_scores.size() * live_consume_score_bound_quantile);
            computed_live_consume_score_bound = live_consume_scores.at(index);
        }
        context.SetDoubleCommonAttr("live_consume_score_bound", computed_live_consume_score_bound);
    }
    auto ensemble_shift_score_accessor = context.GetDoubleItemAttr("ensemble_shift_score");
    auto nearby_mix_score_range_end = context.GetIntCommonAttr("nearby_mix_score_range_end").value_or(10);
    auto set_item_mix_rank_final_score = context.SetDoubleItemAttr("item_mix_rank_final_score");
    auto set_nearby_inside_rank_final_score = context.SetDoubleItemAttr("nearby_inside_rank_final_score");

    double live_idx = 1.0;
    double photo_idx = 1.0;
    bool filter_fail = false;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
        double ensemble_shift_score = ensemble_shift_score_accessor(result).value_or(0.0);
        if (result.GetType() == ks::reco::RecoEnum::ITEM_TYPE_PHOTO) {
            if (photo_idx < nearby_mix_score_range_end) {
                ensemble_shift_score += 100000.0;
            }
            set_nearby_inside_rank_final_score(result, 100.0 / (1.0 + photo_idx));
            photo_idx += 1.0;
        } else {
            double live_consume_score = live_consume_score_accessor(result).value_or(0.0);
            if (live_idx < nearby_mix_score_range_end) {
                if (enable_live_filter == 0 || live_consume_score >= computed_live_consume_score_bound) {
                    ensemble_shift_score += 100000.0;
                } else {
                    filter_fail = true;
                }
            }
            set_nearby_inside_rank_final_score(result, 100.0 / (1.0 + live_idx));
            live_idx += 1.0;
        }
        set_item_mix_rank_final_score(result, ensemble_shift_score);
    });
    context.SetIntCommonAttr("SetLiveFilterSucc", filter_fail ? 1 : 0);
    return true;
}
  static bool CalWtrDebiasFullRankScore(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    std::vector<double> fullrank_ori_wtr_values;
    auto fullrank_ori_wtr_accessor = context.GetDoubleItemAttr("fullrank_ori_wtr");
    auto is_followed_accessor = context.GetIntItemAttr("is_followed");
    auto uProductType = context.GetIntCommonAttr("uProductType").value_or(0);
    double is_followed_total = 0.0;
    int result_total = 0;
    std::string quantile_str;
    if (uProductType == 1) {
      quantile_str = "nearby_nebula_photo_fr_wtr_replace_quantile";
    } else {
      quantile_str = "nearby_main_photo_fr_wtr_replace_quantile";
    }
    auto quantile = context.GetDoubleCommonAttr(quantile_str).value_or(0.05);
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double  fullrank_ori_wtr = fullrank_ori_wtr_accessor(result).value_or(0.0);
      is_followed_total += is_followed_accessor(result).value_or(0.0);
      result_total += 1;
      fullrank_ori_wtr_values.push_back(fullrank_ori_wtr);
    });

    std::sort(fullrank_ori_wtr_values.begin(), fullrank_ori_wtr_values.end(), std::greater<double>());
    size_t top5_percent_index_fullrank = fullrank_ori_wtr_values.size() * quantile;
    double top5_percentile_fullrank = 0.0;
    if (top5_percent_index_fullrank >= 0 && top5_percent_index_fullrank < fullrank_ori_wtr_values.size()) {
      top5_percentile_fullrank = fullrank_ori_wtr_values[top5_percent_index_fullrank];
    }
    auto set_fullrank_ori_wtr = context.SetDoubleItemAttr("fullrank_ori_wtr");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      if (is_followed_accessor(result).value_or(0.0) > 0.1) {
        set_fullrank_ori_wtr(result, top5_percentile_fullrank);
      }
    });
    if (uProductType == 1) {
      base::perfutil::PerfUtilWrapper::IntervalLogStash(50000,
      "reco.nearby", "wtr_debias", "nebula", "fullrank", "follow",
      std::to_string(static_cast<int>(is_followed_total)));
      base::perfutil::PerfUtilWrapper::IntervalLogStash(50000,
      "reco.nearby", "wtr_debias", "nebula", "fullrank", "total",
      std::to_string(result_total));
    } else if (uProductType == 0) {
      base::perfutil::PerfUtilWrapper::IntervalLogStash(50000,
      "reco.nearby", "wtr_debias", "main", "fullrank", "follow",
      std::to_string(static_cast<int>(is_followed_total)));
      base::perfutil::PerfUtilWrapper::IntervalLogStash(50000,
      "reco.nearby", "wtr_debias", "main", "fullrank", "total",
      std::to_string(result_total));
    }
    return true;
  }

  static bool CalFollowBoostFullRankScore(const CommonRecoLightFunctionContext &context,
                                      RecoResultConstIter begin, RecoResultConstIter end) {
    auto uProductType = context.GetIntCommonAttr("uProductType").value_or(0);
    auto is_followed_accessor = context.GetIntItemAttr("is_followed");
    std::string coeff_str;
    std::string es_str;
    if (uProductType == 1) {
      coeff_str = "nearby_nebula_fr_follow_boost_coeff";
      es_str =  "fullrank_ensemble_score";
    } else {
      coeff_str = "nearby_main_fr_follow_boost_coeff";
      es_str = "final_ensemble_score";
    }
    auto es_score_accessor = context.GetDoubleItemAttr(es_str);
    auto set_es_score = context.SetDoubleItemAttr(es_str);
    double coeff = context.GetDoubleCommonAttr(coeff_str).value_or(1.0);
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      double is_followed = is_followed_accessor(result).value_or(0.0);
      if (is_followed > 0.1) {
        double score = es_score_accessor(result).value_or(0.0);
        double score_boost = score * coeff;
        set_es_score(result, score_boost);
      }
    });
    return true;
  }

static bool SetLiveSearchTopAuthor(const CommonRecoLightFunctionContext &context,
                                   RecoResultConstIter begin, RecoResultConstIter end) {
    auto item_author_id_accessor = context.GetIntItemAttr("item_info.author_id");
    auto search_author_top10_list = context.GetIntListCommonAttr("live_search_author_list");
    if (!search_author_top10_list.has_value()) {
        return false;
    }
    std::unordered_set<int64_t> search_author_top10_set(
        search_author_top10_list->begin(), search_author_top10_list->end());
    auto is_search_top_author_set = context.SetIntItemAttr("is_search_top_author");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
        int64_t cur_author_id = item_author_id_accessor(result).value_or(0);
        int isSearchTopAuthor = search_author_top10_set.count(cur_author_id) > 0 ? 1 : 0;
        is_search_top_author_set(result, isSearchTopAuthor);
    });
    return true;
}

static bool SetLiveSearchTopAuthor7Day(const CommonRecoLightFunctionContext &context,
                                   RecoResultConstIter begin, RecoResultConstIter end) {
    auto item_author_id_accessor = context.GetIntItemAttr("item_info.author_id");
    auto search_author_top10_list = context.GetIntListCommonAttr("live_search_author_list_7d");
    if (!search_author_top10_list.has_value()) {
        return false;
    }
    std::unordered_set<int64_t> search_author_top10_set(
        search_author_top10_list->begin(), search_author_top10_list->end());
    auto is_search_top_author_set = context.SetIntItemAttr("is_search_top_author_7d");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
        int64_t cur_author_id = item_author_id_accessor(result).value_or(0);
        int isSearchTopAuthor = search_author_top10_set.count(cur_author_id) > 0 ? 1 : 0;
        is_search_top_author_set(result, isSearchTopAuthor);
    });
    return true;
}

static bool SetLiveAudBoardTarget(const CommonRecoLightFunctionContext &context,
                                  RecoResultConstIter begin, RecoResultConstIter end) {
    auto aud_board_coins_accessor =
          context.GetIntListItemAttr("item_info.online_audience_board_ks_coins");
    auto user_max_gift_amt_opt = context.GetIntCommonAttr("isAudBoardUser");
    if (!user_max_gift_amt_opt.has_value()) {
        return false;
    }
    int64_t user_max_gift_amt = user_max_gift_amt_opt.value();
    auto is_aud_board_target_set = context.SetIntItemAttr("is_aud_board_target");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto aud_board_coins_opt = aud_board_coins_accessor(result);
        if (!aud_board_coins_opt.has_value()) {
            is_aud_board_target_set(result, 0);
            return;
        }
        const auto &aud_board_coins = aud_board_coins_opt.value();
        if (aud_board_coins.size() < 3) {
            is_aud_board_target_set(result, 0);
            return;
        }
        if (user_max_gift_amt < aud_board_coins[0] &&
              user_max_gift_amt > aud_board_coins[2]) {
            is_aud_board_target_set(result, 2);
        } else if (user_max_gift_amt > aud_board_coins[2]) {
            is_aud_board_target_set(result, 1);
        } else {
            is_aud_board_target_set(result, 0);
        }
    });
    return true;
}


static bool SetMixRankListAttr(const CommonRecoLightFunctionContext &context,
                                  RecoResultConstIter begin, RecoResultConstIter end) {
    static const std::vector<std::string> doubleAttrs = {"fr_pctr", "fr_pltr", "fr_pwtr",
      "fr_pftr", "norm_ctr", "norm_gtr", "gbdt_rerank_score", "norm_vtr", "fr_plvtr", "fr_pnvtr",
      "fr_pwtd", "fr_pcmtr", "ctltr", "ctwtr", "ctlvtr", "ctvtr", "ctwtd", "ctcmtr"};
    static const std::vector<std::string> intAttrs = {"pId", "aId", "pProvinceId", "aFollowed",
        "pCityId", "pReason", "item_type"};
    for (auto &attr : doubleAttrs) {
      std::string attr_name = attr + "_list";
      auto double_attr_accessor = context.GetDoubleListItemAttr(attr_name);
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto double_attr_opt = double_attr_accessor(result);
        if (!double_attr_opt.has_value()) {
            return;
        }
        const auto &double_attr = double_attr_opt.value();
        int size = double_attr.size();
        for (int i = 0; i < size; i++) {
          std::string set_attr_name = attr + "_idx" + base::IntToString(i + 1);
          auto double_target_set = context.SetDoubleItemAttr(set_attr_name);
          double_target_set(result, double_attr[i]);
        }
      });
    }
    for (auto &attr : intAttrs) {
      std::string attr_name = attr + "_list";
      auto int_attr_accessor = context.GetIntListItemAttr(attr_name);
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto int_attr_opt = int_attr_accessor(result);
        if (!int_attr_opt.has_value()) {
            return;
        }
        const auto &int_attr = int_attr_opt.value();
        int size = int_attr.size();
        for (int i = 0; i < size; i++) {
          std::string set_attr_name = attr + "_idx" + base::IntToString(i + 1);
          auto int_target_set = context.SetIntItemAttr(set_attr_name);
          int_target_set(result, int_attr[i]);
        }
      });
    }
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      for (int i = 0; i < 10; i++) {
        std::string set_attr_name = "pRankIndex_idx" + base::IntToString(i + 1);
        auto int_target_set = context.SetIntItemAttr(set_attr_name);
        int_target_set(result, i + 1);
      }
    });
    return true;
}



static bool PerfDiversityFlags(const CommonRecoLightFunctionContext &context,
                                  RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto nearby_outslide_safe_diversity_value_accessor =
          context.GetIntItemAttr("nearby_outslide_safe_diversity_value");
    auto nearby_inslide_safe_diversity_value_accessor =
          context.GetIntItemAttr("nearby_inslide_safe_diversity_value");
    std::vector<int64> outside_flags;
    std::vector<int64> inside_flags;
    int loop1_count = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto nearby_outslide_safe_diversity_value =
        nearby_outslide_safe_diversity_value_accessor(result).value_or(0);
        auto nearby_inslide_safe_diversity_value =
        nearby_inslide_safe_diversity_value_accessor(result).value_or(0);
        if (outside_flags.size() < 20) {
          outside_flags.push_back(nearby_outslide_safe_diversity_value);
          inside_flags.push_back(nearby_inslide_safe_diversity_value);
        }
        loop1_count++;
    });
    static const int64 U_PRODUCT_NEBULA = 1;
    static const int64 U_FEED_TYPE_SLIDE = 1;
    std::string bizName("unknown");
    int64 uProductType = context.GetIntCommonAttr("uProductType").value_or(0);
    int64 uFeedType = context.GetIntCommonAttr("uFeedType").value_or(0);
    if (U_PRODUCT_NEBULA == uProductType) {
      if (U_FEED_TYPE_SLIDE == uFeedType) {
        bizName = "bl_nearby_in";
      } else {
        bizName = "bl_nearby";
      }
    } else {
      if (U_FEED_TYPE_SLIDE == uFeedType) {
        bizName = "nearby_in";
      } else {
        bizName = "nearby";
      }
    }
    int outside_failed_count = 0;
    int outside_succ1_count = 0;
    int outside_succ0_count = 0;
    int loop2_count = 0;
    for (int i = 0; i <= outside_flags.size() - 10; ++i) {
      int temp = 0;
      for (int j = 0; j < 10; j++) {
        loop2_count++;
        if (i + j < outside_flags.size()) {
          temp += outside_flags.at(i + j);
        }
      }
      if (temp > 1) {
        outside_failed_count++;
      } else if (temp == 1) {
        outside_succ1_count++;
      } else {
        outside_succ0_count++;
      }
    }

    base::perfutil::PerfUtilWrapper::IntervalLogStash(outside_failed_count, "reco.nearby",
            "checkdiversityweichengnian", bizName, "outside", "failed");
    base::perfutil::PerfUtilWrapper::IntervalLogStash(outside_succ1_count, "reco.nearby",
            "checkdiversityweichengnian", bizName, "outside", "success1");
    base::perfutil::PerfUtilWrapper::IntervalLogStash(outside_succ0_count, "reco.nearby",
            "checkdiversityweichengnian", bizName, "outside", "success0");
    int inside_failed_count = 0;
    int inside_succ0_count = 0;
    int inside_succ1_count = 0;
    int loop3_count = 0;
    for (int i = 0; i <= inside_flags.size() - 10; ++i) {
      int temp = 0;
      for (int j = 0; j < 10; j++) {
        loop3_count++;
        if (i + j < inside_flags.size()) {
          temp += inside_flags.at(i + j);
        }
      }
      if (temp > 1) {
        inside_failed_count++;
      } else if (temp == 1) {
        inside_succ1_count++;
      } else {
        inside_succ0_count++;
      }
    }
    LOG_EVERY_N(INFO, 100000) << "wupeng loop1_count " << loop1_count
      << " " << outside_flags.size() << " " << inside_flags.size()
      << "wupeng loop2:" << loop2_count << ",loop3:" << loop3_count;
    // LOG(INFO) << "wupeng loop2:" << loop2_count << ",loop3:" << loop3_count;
    base::perfutil::PerfUtilWrapper::IntervalLogStash(inside_failed_count, "reco.nearby",
            "checkdiversityweichengnian", bizName, "inside", "failed");
    base::perfutil::PerfUtilWrapper::IntervalLogStash(inside_succ1_count, "reco.nearby",
            "checkdiversityweichengnian", bizName, "inside", "success1");
    base::perfutil::PerfUtilWrapper::IntervalLogStash(inside_succ0_count, "reco.nearby",
            "checkdiversityweichengnian", bizName, "inside", "success0");
    return true;
}

static bool SetLiveUnitPxtr(const CommonRecoLightFunctionContext &context,
                            RecoResultConstIter begin, RecoResultConstIter end) {
    auto splitString = [&](const std::string &str, char delimiter) -> std::vector<std::string> {
        std::vector<std::string> tokens;
        std::istringstream iss(str);
        std::string token;
        while (std::getline(iss, token, delimiter)) {
            tokens.push_back(token);
        }
        return tokens;
    };
    auto from_attrs_opt = context.GetStringCommonAttr("nearby_unit_replace_from_attrs");
    auto to_attrs_opt = context.GetStringCommonAttr("nearby_unit_replace_to_attrs");
    if (!from_attrs_opt.has_value() || !to_attrs_opt.has_value()) {
        return false;
    }
    std::vector<std::string> from_attrs = splitString(std::string(from_attrs_opt.value()), ',');
    std::vector<std::string> to_attrs = splitString(std::string(to_attrs_opt.value()), ',');
    if (from_attrs.size() != to_attrs.size()) {
        return false;
    }
    std::vector<std::function<absl::optional<double>(const CommonRecoResult&)>> from_accessors;
    std::vector<std::function<void(CommonRecoResult&, double)>> to_setters;
    for (size_t i = 0; i < from_attrs.size(); ++i) {
        auto from_accessor = context.GetDoubleItemAttr(from_attrs[i]);
        auto to_setter = context.SetDoubleItemAttr(to_attrs[i]);
        from_accessors.push_back(from_accessor);
        to_setters.push_back(to_setter);
    }
    std::for_each(begin, end, [&](const CommonRecoResult &result_const) {
        CommonRecoResult &result = const_cast<CommonRecoResult&>(result_const);
        for (size_t i = 0; i < from_accessors.size(); ++i) {
            auto maybe_value = from_accessors[i](result);
            if (maybe_value.has_value()) {
                double value = maybe_value.value();
                to_setters[i](result, value);
            }
        }
    });
    return true;
}

  static bool CalPubLastEmbAvg(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalMmuEmbeddingScore(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalAuthorUnClickScore(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalClusterUnClickScore(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalLiveMmuEmbScore(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalLiveMmuEmbScoreV2(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool EnrichPhotoNearFieldSupplyFactor(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool EnrichPhotoPxtrCompetitiveDegree(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool EnrichUserFollowPreferenceFactor(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool CalNearFieldPhotoSupplyFactor(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);
  static bool EnrichPhotoConvolutionPxtr(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end);

 private:
  DISALLOW_COPY_AND_ASSIGN(NearbyLightFrFunctionSet);
};

}  // namespace platform
}  // namespace ks
