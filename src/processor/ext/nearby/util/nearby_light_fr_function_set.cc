#include "dragon/src/processor/ext/nearby/util/nearby_light_fr_function_set.h"

#include <memory>
#include <map>
#include "Eigen/Dense"
#include "serving_base/util/factory.h"

namespace ks {
namespace platform {

namespace {
std::string GetBizName(const CommonRecoLightFunctionContext &context) {
  static const int64 U_PRODUCT_NEBULA = 1;
  static const int64 U_FEED_TYPE_SLIDE = 1;
  std::string bizName("unknown");
  int64 uProductType = context.GetIntCommonAttr("uProductType").value_or(0);
  int64 uFeedType = context.GetIntCommonAttr("uFeedType").value_or(0);
  if (U_PRODUCT_NEBULA == uProductType) {
    if (U_FEED_TYPE_SLIDE == uFeedType) {
      bizName = "bl_nearby_in";
    } else {
      bizName = "bl_nearby";
    }
  } else {
    if (U_FEED_TYPE_SLIDE == uFeedType) {
      bizName = "nearby_in";
    } else {
      bizName = "nearby";
    }
  }
  return bizName;
}

double MyClampDouble(double v, double lo, double hi) {
  if (v < lo) {
    return lo;
  }
  if (v > hi) {
    return hi;
  }
  return v;
}

void MinHeapPush(const CommonRecoResult &result, double score, int64 capacity,
  ks::platform::NearbyLightFrFunctionSet::NearbyPriorityQueue * heap) {
  if (heap->size() < capacity) {
    heap->push(std::make_pair(result, score));
  } else {
    if (score > heap->top().second) {
      heap->pop();
      heap->push(std::make_pair(result, score));
    }
  }
}

void MinHeap2OrderedVec(ks::platform::NearbyLightFrFunctionSet::NearbyPriorityQueue * minHeap,
  std::vector<std::pair<CommonRecoResult, double>> * vec, bool descending) {
  while (minHeap->size() > 0) {
    vec->push_back(minHeap->top());
    minHeap->pop();
  }
  if (descending) {
    std::reverse(vec->begin(), vec->end());
  }
}

double CalCompetitiveDegree2(const std::vector<std::pair<CommonRecoResult, double>> &arr,
  int64 supply_capacity) {
  double competitive_degree = 0.0;
  if (supply_capacity <= 1) {
    return competitive_degree;
  }

  for (int i = 1; i < supply_capacity; i++) {
    if (i < arr.size()) {
      double left = arr[i - 1].second;
      double right = arr[i].second;
      if (left < 1e-9) {
        left = 1e-9;
      }
      competitive_degree += right / left;
    } else {
      competitive_degree += 0.0;
    }
  }
  competitive_degree = competitive_degree / (supply_capacity - 1);
  return competitive_degree;
}

double CalCompetitiveDegree(ks::platform::NearbyLightFrFunctionSet::NearbyPriorityQueue * minHeap,
  int64 supply_capacity) {
  std::vector<std::pair<CommonRecoResult, double>> arr;
  MinHeap2OrderedVec(minHeap, &arr, true);
  return CalCompetitiveDegree2(arr, supply_capacity);
}

double CalCompetitiveScore2(
  const std::vector<std::pair<CommonRecoResult, double>> &left,
  const std::vector<std::pair<CommonRecoResult, double>> &right,
  int64 supply_capacity, double padding_score) {
  double competitive_score = 0.0;
  for (int i = 0; i < supply_capacity; i++) {
    if (i < left.size() && i < right.size()) {
      double ue_score_sc = left[i].second;
      double ue_score_nsc = right[i].second;
      if (ue_score_nsc < 1e-9) {
        ue_score_nsc = 1e-9;
      }
      double ratio = ue_score_sc / ue_score_nsc;
      competitive_score += ratio;

    } else if (i < left.size() && i >= right.size()) {
      double ue_score_sc = left[i].second;
      double ue_score_nsc = padding_score;
      if (ue_score_nsc < 1e-9) {
        ue_score_nsc = 1e-9;
      }
      double ratio = ue_score_sc / ue_score_nsc;;
      competitive_score += ratio;

    } else if (i >= left.size() && i < right.size()) {
      double ue_score_sc = padding_score;
      double ue_score_nsc = right[i].second;
      if (ue_score_nsc < 1e-9) {
        ue_score_nsc = 1e-9;
      }
      double ratio = ue_score_sc / ue_score_nsc;
      competitive_score += ratio;

    } else {
      double ratio = 1.0;
      competitive_score += ratio;
    }
  }
  competitive_score /= supply_capacity;
  return competitive_score;
}

double CalCompetitiveScore(
  ks::platform::NearbyLightFrFunctionSet::NearbyPriorityQueue * minHeapLeft,
  ks::platform::NearbyLightFrFunctionSet::NearbyPriorityQueue * minHeapRight,
  int64 supply_capacity, double padding_score) {
  std::vector<std::pair<CommonRecoResult, double>> left;
  MinHeap2OrderedVec(minHeapLeft, &left, true);
  std::vector<std::pair<CommonRecoResult, double>> right;
  MinHeap2OrderedVec(minHeapRight, &right, true);
  return CalCompetitiveScore2(left, right, supply_capacity, padding_score);
}

bool ArithmeticMean(const std::vector<int> &channels,
  const std::vector<double> &scores, double def, double * result) {
  if (channels.size() == 0) {
    *result = def;
    return false;
  }
  for (int i = 0; i < channels.size(); i++) {
    int index = channels[i];
    if (index >= 0 && index < scores.size()) {
      *result += scores[index];
    } else {
      *result = def;
      return false;
    }
  }
  *result /= channels.size();
  return true;
}

bool Maximum(const std::vector<int> &channels,
  const std::vector<double> &scores, double def, double * result) {
  if (channels.size() == 0) {
    *result = def;
    return false;
  }

  *result = std::numeric_limits<double>::min();
  for (int i = 0; i < channels.size(); i++) {
    int index = channels[i];
    if (index >= 0 && index < scores.size()) {
      *result = std::max(*result, scores[index]);
    } else {
      *result = def;
      return false;
    }
  }
  return true;
}

bool FuseScore(int fuseType, const std::vector<int> &channels,
          const std::vector<double> &scores, double def, double * result) {
  static const int MAX_FUSE = 1;
  if (MAX_FUSE == fuseType) {
    return Maximum(channels, scores, def, result);
  }
  // default
  return ArithmeticMean(channels, scores, def, result);
}

double MinMaxNormalize(double score, int norm_version, double max_score,
                                                    double min_score) {
  double numerator = 0.0;
  double denominator = 0.0;
  double eps = 0.0000001;
  if (norm_version == 1) {
    numerator = score - min_score;
  } else if (norm_version == 2) {
    numerator = max_score - score;
  } else {
    return score;
  }
  denominator = max_score - min_score;
  double scaled_score = score;
  if (denominator > eps) {
    scaled_score = numerator / denominator;
  } else {
    scaled_score = 0.0;
  }
  return scaled_score;
}

void GetSuperiorityInfo(
  std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<std::map<int, std::string>>>> kconf,
  std::unordered_map<int, std::vector<int>> * info) {
  if (kconf == nullptr) {
    return;
  }
  std::shared_ptr<std::map<int, std::string>> real_conf = kconf->Get();
  if (real_conf != nullptr) {
    for (auto it = real_conf->begin(); it != real_conf->end(); it++) {
      std::vector<std::string> channel_str;
      base::SplitString(it->second, ",", &channel_str);
      std::vector<int> channels;
      for (int i = 0 ; i < channel_str.size(); i++) {
        int channel = 0;
        if (base::StringToInt(channel_str[i], &channel)) {
          channels.push_back(channel);
        }
      }
      if (channels.size() > 0) {
        info->emplace(it->first, channels);
      }
    }
  }
}

std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<std::map<int, std::string>>>> SuperiorityInfoKConfig(
  int64 uProductType, int64 superiorityType) {
  static std::shared_ptr<std::map<int, std::string>> empty_superiority_info =
    std::make_shared<std::map<int, std::string>>();
  static ks::infra::KeyParser<int> string_key_parser = [](const std::string& key, int * val) -> bool {
    return absl::SimpleAtoi(key, val);
  };
  static const int64 U_PRODUCT_NEBULA = 1;
  static const int64 DURATION_SUPERIORITY = 0;
  static const int64 HETU_ONE_SUPERIORITY = 1;
  static const int64 CITY_SUPERIORITY = 2;

  std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<std::map<int, std::string>>>> ans = nullptr;
  if (DURATION_SUPERIORITY == superiorityType) {
    if (U_PRODUCT_NEBULA == uProductType) {
      ans = ks::infra::KConf().GetMap(
        "reco.nearby.nearbyPhotoDurationSuperiorityInfoForNebuLa",
        empty_superiority_info,
        string_key_parser);
    } else {
      ans = ks::infra::KConf().GetMap(
        "reco.nearby.nearbyPhotoDurationSuperiorityInfo",
        empty_superiority_info,
        string_key_parser);
    }
  } else if (HETU_ONE_SUPERIORITY == superiorityType) {
    if (U_PRODUCT_NEBULA == uProductType) {
      ans = ks::infra::KConf().GetMap(
        "reco.nearby.nearbyHetuOneSuperiorityInfoForNebula",
        empty_superiority_info,
        string_key_parser);
    } else {
      ans = ks::infra::KConf().GetMap(
        "reco.nearby.nearbyHetuOneSuperiorityInfo",
        empty_superiority_info,
        string_key_parser);
    }
  } else if (CITY_SUPERIORITY == superiorityType) {
    if (U_PRODUCT_NEBULA == uProductType) {
      ans = ks::infra::KConf().GetMap(
        "reco.nearby.nearbyCitySuperiorityInfoForNebula",
        empty_superiority_info,
        string_key_parser);
    } else {
      ans = ks::infra::KConf().GetMap(
        "reco.nearby.nearbyCitySuperiorityInfo",
        empty_superiority_info,
        string_key_parser);
    }
  }
  return ans;
}

int64 BuildSuperiorityChannel(const std::vector<int> &channels) {
  int64 flag = 0;
  for (int i = 0; i < channels.size(); i++) {
    int index = channels[i];
    if (index >= 0 && index < 64) {
      flag += 1 << index;
    }
  }
  return flag;
}

double CalWilsonLB(double nd, double n, double z) {
  double p = nd * 1. / n * 1.;
  double low = (p + (std::pow(z, 2) / (2. * n))
         - ((z / (2. * n)) * std::sqrt(4. * n * (1. - p) * p + std::pow(z, 2)))) / (1. + std::pow(z, 2) / n);
  return low;
}


}  // namespace


// static
bool NearbyLightFrFunctionSet::CalSuperiorityScore(const CommonRecoLightFunctionContext &context,
  RecoResultConstIter begin, RecoResultConstIter end) {
  int64 start_ts = base::GetTimestamp();
  int item_num = std::distance(begin, end);
  if (item_num <= 0) {
    return false;
  }
  static const int64 U_PRODUCT_NEBULA = 1;
  static const int64 U_FEED_TYPE_SLIDE = 1;
  std::string bizName("unknown");
  int64 uProductType = context.GetIntCommonAttr("uProductType").value_or(0);
  int64 uFeedType = context.GetIntCommonAttr("uFeedType").value_or(0);
  if (U_PRODUCT_NEBULA == uProductType) {
    if (U_FEED_TYPE_SLIDE == uFeedType) {
      bizName = "bl_nearby_in";
    } else {
      bizName = "bl_nearby";
    }
  } else {
    if (U_FEED_TYPE_SLIDE == uFeedType) {
      bizName = "nearby_in";
    } else {
      bizName = "nearby";
    }
  }

  int64 user_adcode = context.GetIntCommonAttr("user_adcode").value_or(0);
  base::perfutil::PerfUtilWrapper::CountLogStash(1,
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalSuperiorityScore.user_adcode",
    user_adcode == 0 ? "0" : "1");
  int64 user_city_adcode = std::floor(user_adcode / 100);

  int64 user_city_id = context.GetIntCommonAttr("user_city_id").value_or(0);
  base::perfutil::PerfUtilWrapper::CountLogStash(1,
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalSuperiorityScore.user_city_id",
    user_city_id == 0 ? "0" : "1");

  // static
  static std::vector<double> duration_segments =
    {0, 10000, 20000, 30000, 40000, 50000, 60000, 90000, 180000};
  static const int CTR = 0;
  static const int WTD = 1;
  static const int VTR = 2;
  static const int LTR = 3;
  static const int WTR = 4;
  static const int COMMENTTR = 5;
  static const int CHANNEL_NUM = 6;

  // item attr getter
  int64 user_gender = context.GetIntCommonAttr("uGender").value_or(0);
  base::perfutil::PerfUtilWrapper::CountLogStash(1,
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalSuperiorityScore.uGender",
    base::IntToString(user_gender));
  int64 user_age_segment = context.GetIntCommonAttr("uAgeSeg").value_or(0);
  base::perfutil::PerfUtilWrapper::CountLogStash(1,
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalSuperiorityScore.uAgeSeg",
    base::IntToString(user_age_segment));

  auto hetu_level_one_getter =
    context.GetIntItemAttr("hetu_level_one");
  auto duration_ms_getter =
    context.GetIntItemAttr("item_info.duration_ms");
  auto author_age_getter =
    context.GetStringItemAttr("item_info.user_age_segment");

  // item attr setter
  auto duration_superiority_channel_setter =
    context.SetIntItemAttr("duration_superiority_channel");
  auto hetu_one_superiority_channel_setter =
    context.SetIntItemAttr("hetu_one_superiority_channel");
  auto city_superiority_channel_setter =
    context.SetIntItemAttr("city_superiority_channel");

  static const int64 DURATION_SUPERIORITY = 0;
  static const int64 HETU_ONE_SUPERIORITY = 1;
  static const int64 CITY_SUPERIORITY = 2;
  std::unordered_map<int, std::vector<int>> hetu_one_superiority_info;
  GetSuperiorityInfo(SuperiorityInfoKConfig(uProductType, HETU_ONE_SUPERIORITY),
    &hetu_one_superiority_info);

  std::unordered_map<int, std::vector<int>> duration_superiority_info;
  GetSuperiorityInfo(SuperiorityInfoKConfig(uProductType, DURATION_SUPERIORITY),
    &duration_superiority_info);

  std::unordered_map<int, std::vector<int>> city_superiority_info;
  GetSuperiorityInfo(SuperiorityInfoKConfig(uProductType, CITY_SUPERIORITY),
    &city_superiority_info);

  std::unordered_map<int64, int> hetu_one_map;
  std::unordered_map<int, int> duration_map;
  std::unordered_map<std::string, int> age_map;

  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    // duration
    int64 duration_ms = duration_ms_getter(result).value_or(0);
    auto lb = std::lower_bound(duration_segments.begin(), duration_segments.end(),
                               duration_ms);
    int duration_bucket_id = std::distance(duration_segments.begin(), lb);
    if (duration_map.count(duration_bucket_id) > 0) {
      duration_map[duration_bucket_id] += 1;
    } else {
      duration_map[duration_bucket_id] = 1;
    }
    if (duration_superiority_info.count(duration_bucket_id) > 0) {
      int64 duration_superiority_channel =
        BuildSuperiorityChannel(duration_superiority_info[duration_bucket_id]);
      duration_superiority_channel_setter(result, duration_superiority_channel);
    }

    // hetu one
    int64 hetu_level_one = hetu_level_one_getter(result).value_or(0);
    if (hetu_one_map.count(hetu_level_one) > 0) {
      hetu_one_map[hetu_level_one] += 1;
    } else {
      hetu_one_map[hetu_level_one] = 1;
    }
    if (hetu_one_superiority_info.count(hetu_level_one) > 0) {
      int64 hetu_one_superiority_channel =
        BuildSuperiorityChannel(hetu_one_superiority_info[hetu_level_one]);
      hetu_one_superiority_channel_setter(result, hetu_one_superiority_channel);
    }

    // city
    if (city_superiority_info.count(user_city_adcode) > 0) {
      int64 city_superiority_channel =
        BuildSuperiorityChannel(city_superiority_info[user_city_adcode]);
      city_superiority_channel_setter(result, city_superiority_channel);
    }

    // age gender
    std::string author_age(author_age_getter(result).value_or(""));
    if (age_map.count(author_age) > 0) {
      age_map[author_age] += 1;
    } else {
      age_map[author_age] = 1;
    }
  });

  // perf
  for (auto& it : hetu_one_map) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(it.second,
      "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalSuperiorityScore.hetuOne",
      base::IntToString(it.first));
  }
  for (auto& it : duration_map) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(it.second,
      "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalSuperiorityScore.durationBucket",
      base::IntToString(it.first));
  }
  for (auto& it : age_map) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(it.second,
      "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalSuperiorityScore.ageSegment",
      it.first);
  }
  base::perfutil::PerfUtilWrapper::IntervalLogStash(base::GetTimestamp() - start_ts,
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalSuperiorityScore.duration", "total");
  return true;
}

// zmx
// static
bool NearbyLightFrFunctionSet::CalMmuEmbeddingScore(const CommonRecoLightFunctionContext &context,
  RecoResultConstIter begin, RecoResultConstIter end) {
  // LOG(ERROR) << "zmx_debug: " << "into cal_mmu_emb";
  int64 start_ts = base::GetTimestamp();
  int item_num = std::distance(begin, end);
  if (item_num <= 0) {
    return false;
  }
  static const int64 U_PRODUCT_NEBULA = 1;
  static const int64 U_FEED_TYPE_SLIDE = 1;
  std::string bizName("unknown");
  int64 uProductType = context.GetIntCommonAttr("uProductType").value_or(0);
  int64 uFeedType = context.GetIntCommonAttr("uFeedType").value_or(0);
  if (U_PRODUCT_NEBULA == uProductType) {
    if (U_FEED_TYPE_SLIDE == uFeedType) {
      bizName = "bl_nearby_in";
    } else {
      bizName = "bl_nearby";
    }
  } else {
    if (U_FEED_TYPE_SLIDE == uFeedType) {
      bizName = "nearby_in";
    } else {
      bizName = "nearby";
    }
  }

  auto item_mmu_emb_getter =
    context.GetDoubleListItemAttr("photo_mmu_emb");
  int fill_num = 0;
  std::vector<double> item_mmu_embedding;
  std::vector<CommonRecoResult> fr_items;
  int emb_dim_size = 64;
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    fr_items.push_back(result);
    // item mmu emb
    auto photo_mmu_emb = item_mmu_emb_getter(result);
    if (photo_mmu_emb && photo_mmu_emb->size() == emb_dim_size) {
      for (auto &item_e : *photo_mmu_emb) {
        item_mmu_embedding.push_back(item_e);
      }
      fill_num++;
    } else {
      for (int i = 0; i < emb_dim_size; i++) {
        item_mmu_embedding.push_back(0.0);
      }
    }
  });
  item_num = fr_items.size();
  base::perfutil::PerfUtilWrapper::IntervalLogStash(((1.0 * fill_num) / item_num) * 1000,
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.itemMmuEmb", "fillRatio");
  Eigen::MatrixXd fr_embed_matrix = Eigen::MatrixXd::Zero(item_num, emb_dim_size);
  for (int i = 0; i < item_num; ++i) {
    for (int j = 0; j < emb_dim_size; ++j) {
      fr_embed_matrix(i, j) = item_mmu_embedding[i * emb_dim_size + j];
    }
  }

  // mmu short play score
  // not click score
  int not_click_fill_num = 0;
  int not_click_emb_fill_num = 0;
  double not_click_emb_fill_rate = 0.0;
  std::vector<double> not_click_mmu_embeddings;
  auto user_nearby_not_click_pids =
    context.GetIntListCommonAttr("user_nearby_not_click_pids");
  if (user_nearby_not_click_pids && user_nearby_not_click_pids->size() > 0) {
    not_click_fill_num = user_nearby_not_click_pids->size();
    auto user_nearby_not_click_pid_embs =
      context.GetDoubleListCommonAttr("user_nearby_not_click_pid_embs");
    if (user_nearby_not_click_pid_embs && user_nearby_not_click_pid_embs->size() > 0) {
      not_click_emb_fill_num = user_nearby_not_click_pid_embs->size();
      not_click_emb_fill_rate = 1.0 * not_click_emb_fill_num / not_click_fill_num;
      for (auto double_val : *user_nearby_not_click_pid_embs) {
        not_click_mmu_embeddings.push_back(double_val);
      }
    }
  }
  if (not_click_fill_num > 0) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(not_click_fill_num,
      "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmuEmb", "not_click_fill");
  } else {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(1,
      "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmuEmb", "not_click_empty");
  }
  if (not_click_emb_fill_num > 0) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(not_click_emb_fill_num,
      "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmuEmb", "not_click_emb_fill");
    base::perfutil::PerfUtilWrapper::IntervalLogStash(not_click_emb_fill_rate * 1000,
      "reco.nearby.NearbyLightFrFunctionSet", bizName,
      "CalMmuEmbeddingScore.mmuEmb", "not_click_emb_fill_rate");
  } else {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(1,
      "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmuEmb", "not_click_emb_empty");
  }
  if (not_click_fill_num > 0 && not_click_emb_fill_num > 0) {
    if (not_click_fill_num * emb_dim_size == not_click_mmu_embeddings.size()) {
      Eigen::MatrixXd not_click_emb_matrix = Eigen::MatrixXd::Zero(not_click_fill_num, emb_dim_size);
      for (int i = 0; i < not_click_fill_num; ++i) {
        for (int j = 0; j < emb_dim_size; ++j) {
          not_click_emb_matrix(i, j) = not_click_mmu_embeddings[i * emb_dim_size + j];
        }
      }
      Eigen::MatrixXd not_click_score_matrix = Eigen::MatrixXd::Zero(item_num, 1);
      not_click_score_matrix = fr_embed_matrix * (not_click_emb_matrix.transpose().rowwise().sum());
      int64 mmu_not_click_score_reduce_method =
        context.GetIntCommonAttr("mmu_not_click_score_reduce_method").value_or(0);
      if (mmu_not_click_score_reduce_method == 1) {
        // mean
        not_click_score_matrix = not_click_score_matrix / (1.0 * not_click_fill_num);
      }
      auto mmu_not_click_score_setter =
        context.SetDoubleItemAttr("mmu_not_click_score");
      for (int i = 0; i < fr_items.size(); i++) {
        mmu_not_click_score_setter(fr_items[i], not_click_score_matrix(i, 0));
      }
      int col_index = 0, row_index = 0;
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
        not_click_score_matrix.minCoeff(&row_index, &col_index) * 1e6,
        "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmu_not_click_score", "min");
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
        not_click_score_matrix.maxCoeff(&row_index, &col_index) * 1e6,
        "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmu_not_click_score", "max");
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
        not_click_score_matrix.mean() * 1e6,
        "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmu_not_click_score", "avg");
    } else {
      base::perfutil::PerfUtilWrapper::IntervalLogStash(1,
        "reco.nearby.NearbyLightFrFunctionSet", bizName,
        "CalMmuEmbeddingScore.mmu_not_click_score", "emb_wrong_format");
    }
  }

  // click score
  int click_fill_num = 0;
  int click_emb_fill_num = 0;
  double click_emb_fill_rate = 0.0;
  std::vector<double> click_mmu_embeddings;
  auto user_nearby_click_pids =
    context.GetIntListCommonAttr("uClickListNear");
  if (user_nearby_click_pids && user_nearby_click_pids->size() > 0) {
    click_fill_num = user_nearby_click_pids->size();
    auto user_nearby_click_pid_embs =
      context.GetDoubleListCommonAttr("uClickListNearMmuEmbs");
    if (user_nearby_click_pid_embs && user_nearby_click_pid_embs->size() > 0) {
      click_emb_fill_num = user_nearby_click_pid_embs->size();
      click_emb_fill_rate = 1.0 * click_emb_fill_num / click_fill_num;
      for (auto double_val : *user_nearby_click_pid_embs) {
        click_mmu_embeddings.push_back(double_val);
      }
    }
  }
  if (click_fill_num > 0) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(click_fill_num,
      "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmuEmb", "click_fill");
  } else {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(1,
      "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmuEmb", "click_empty");
  }
  if (click_emb_fill_num > 0) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(click_emb_fill_num,
      "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmuEmb", "click_emb_fill");
    base::perfutil::PerfUtilWrapper::IntervalLogStash(click_emb_fill_rate * 1000,
      "reco.nearby.NearbyLightFrFunctionSet", bizName,
      "CalMmuEmbeddingScore.mmuEmb", "click_emb_fill_rate");
  } else {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(1,
      "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmuEmb", "click_emb_empty");
  }
  if (click_fill_num > 0 && click_emb_fill_num > 0) {
    if (click_fill_num * emb_dim_size == click_mmu_embeddings.size()) {
      Eigen::MatrixXd click_emb_matrix = Eigen::MatrixXd::Zero(click_fill_num, emb_dim_size);
      for (int i = 0; i < click_fill_num; ++i) {
        for (int j = 0; j < emb_dim_size; ++j) {
          click_emb_matrix(i, j) = click_mmu_embeddings[i * emb_dim_size + j];
        }
      }
      Eigen::MatrixXd click_score_matrix = Eigen::MatrixXd::Zero(item_num, 1);
      click_score_matrix = fr_embed_matrix * (click_emb_matrix.transpose().rowwise().sum());
      click_score_matrix = click_score_matrix / (1.0 * click_fill_num);
      auto mmu_click_score_setter =
        context.SetDoubleItemAttr("mmu_click_score");
      for (int i = 0; i < fr_items.size(); i++) {
        mmu_click_score_setter(fr_items[i], click_score_matrix(i, 0));
      }
      int col_index = 0, row_index = 0;
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
        click_score_matrix.minCoeff(&row_index, &col_index) * 1e6,
        "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmu_click_score", "min");
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
        click_score_matrix.maxCoeff(&row_index, &col_index) * 1e6,
        "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmu_click_score", "max");
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
        click_score_matrix.mean() * 1e6,
        "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmu_click_score", "avg");
    } else {
      base::perfutil::PerfUtilWrapper::IntervalLogStash(1,
        "reco.nearby.NearbyLightFrFunctionSet", bizName,
        "CalMmuEmbeddingScore.mmu_click_score", "emb_wrong_format");
    }
  }

  // hate score
  int hate_fill_num = 0;
  int hate_emb_fill_num = 0;
  double hate_emb_fill_rate = 0.0;
  std::vector<double> hate_mmu_embeddings;
  auto user_nearby_hate_pids =
    context.GetIntListCommonAttr("user_hate_photo_list");
  if (user_nearby_hate_pids && user_nearby_hate_pids->size() > 0) {
    hate_fill_num = user_nearby_hate_pids->size();
    auto user_nearby_hate_pid_embs =
      context.GetDoubleListCommonAttr("user_hate_photo_list_emb");
    if (user_nearby_hate_pid_embs && user_nearby_hate_pid_embs->size() > 0) {
      hate_emb_fill_num = user_nearby_hate_pid_embs->size();
      hate_emb_fill_rate = 1.0 * hate_emb_fill_num / hate_fill_num;
      for (auto double_val : *user_nearby_hate_pid_embs) {
        hate_mmu_embeddings.push_back(double_val);
      }
    }
  }
  if (hate_fill_num > 0) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(hate_fill_num,
      "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmuEmb", "hate_fill");
  } else {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(1,
      "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmuEmb", "hate_empty");
  }
  if (hate_emb_fill_num > 0) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(hate_emb_fill_num,
      "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmuEmb", "hate_emb_fill");
    base::perfutil::PerfUtilWrapper::IntervalLogStash(hate_emb_fill_rate * 1000,
      "reco.nearby.NearbyLightFrFunctionSet", bizName,
      "CalMmuEmbeddingScore.mmuEmb", "hate_emb_fill_rate");
  } else {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(1,
      "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmuEmb", "hate_emb_empty");
  }
  if (hate_fill_num > 0 && hate_emb_fill_num > 0) {
    if (hate_fill_num * emb_dim_size == hate_mmu_embeddings.size()) {
      Eigen::MatrixXd hate_emb_matrix = Eigen::MatrixXd::Zero(hate_fill_num, emb_dim_size);
      for (int i = 0; i < hate_fill_num; ++i) {
        for (int j = 0; j < emb_dim_size; ++j) {
          hate_emb_matrix(i, j) = hate_mmu_embeddings[i * emb_dim_size + j];
        }
      }
      Eigen::MatrixXd hate_score_matrix = Eigen::MatrixXd::Zero(item_num, 1);
      hate_score_matrix = fr_embed_matrix * (hate_emb_matrix.transpose().rowwise().sum());
      hate_score_matrix = hate_score_matrix / (1.0 * hate_fill_num);
      auto mmu_hate_score_setter =
        context.SetDoubleItemAttr("mmu_hate_score");
      for (int i = 0; i < fr_items.size(); i++) {
        mmu_hate_score_setter(fr_items[i], hate_score_matrix(i, 0));
      }
      int col_index = 0, row_index = 0;
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
        hate_score_matrix.minCoeff(&row_index, &col_index) * 1e6,
        "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmu_hate_score", "min");
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
        hate_score_matrix.maxCoeff(&row_index, &col_index) * 1e6,
        "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmu_hate_score", "max");
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
        hate_score_matrix.mean() * 1e6,
        "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmu_hate_score", "avg");
    } else {
      base::perfutil::PerfUtilWrapper::IntervalLogStash(1,
        "reco.nearby.NearbyLightFrFunctionSet", bizName,
        "CalMmuEmbeddingScore.mmu_hate_score", "emb_wrong_format");
    }
  }

  // undertake inner score
  int public_fill_num = 0;
  int public_emb_fill_num = 0;
  double public_emb_fill_rate = 0.0;
  std::vector<double> public_mmu_embeddings;
  auto user_public_postive_pids =
    context.GetIntListCommonAttr("user_public_postive_pids_filtered");
  if (user_public_postive_pids && user_public_postive_pids->size() > 0) {
    public_fill_num = user_public_postive_pids->size();
    auto user_public_postive_pid_embs =
      context.GetDoubleListCommonAttr("user_public_postive_pids_emb");
    if (user_public_postive_pid_embs && user_public_postive_pid_embs->size() > 0) {
      public_emb_fill_num = user_public_postive_pid_embs->size();
      public_emb_fill_rate = 1.0 * public_emb_fill_num / public_fill_num;
      for (auto double_val : *user_public_postive_pid_embs) {
        public_mmu_embeddings.push_back(double_val);
      }
    }
  }
  if (public_fill_num > 0) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(public_fill_num,
      "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmuEmb", "public_postive_fill");
  } else {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(1,
      "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmuEmb", "public_postive_empty");
  }
  if (public_emb_fill_num > 0) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(public_emb_fill_num,
      "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmuEmb",
      "public_postive_emb_fill");
    base::perfutil::PerfUtilWrapper::IntervalLogStash(public_emb_fill_rate * 1000,
      "reco.nearby.NearbyLightFrFunctionSet", bizName,
      "CalMmuEmbeddingScore.mmuEmb", "public_postive_emb_fill_rate");
  } else {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(1,
      "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmuEmb",
      "public_postive_emb_empty");
  }
  if (public_fill_num > 0 && public_emb_fill_num > 0) {
    if (public_fill_num * emb_dim_size == public_mmu_embeddings.size()) {
      Eigen::MatrixXd public_emb_matrix = Eigen::MatrixXd::Zero(public_fill_num, emb_dim_size);
      for (int i = 0; i < public_fill_num; ++i) {
        for (int j = 0; j < emb_dim_size; ++j) {
          public_emb_matrix(i, j) = public_mmu_embeddings[i * emb_dim_size + j];
        }
      }
      // Eigen::MatrixXd first_emb = public_emb_matrix.row(0);
      Eigen::MatrixXd public_score_matrix = Eigen::MatrixXd::Zero(item_num, 1);
      public_score_matrix = fr_embed_matrix * (public_emb_matrix.transpose().rowwise().sum());
      public_score_matrix = public_score_matrix / (1.0 * public_fill_num);
      auto mmu_public_score_setter =
        context.SetDoubleItemAttr("mmu_undertake_inner_score");
      for (int i = 0; i < fr_items.size(); i++) {
        mmu_public_score_setter(fr_items[i], public_score_matrix(i, 0));
      }
      int col_index = 0, row_index = 0;
      // Eigen::MatrixXd max_mmu_score_matrix = Eigen::MatrixXd::Zero(item_num, 1);
      // max_mmu_score_matrix = fr_embed_matrix * (first_emb.transpose());
      // max_mmu_score_matrix = max_mmu_score_matrix * (1.0 * public_fill_num);
      // auto max_mmu_score_setter = context.SetDoubleItemAttr("max_mmu_postive_score");
      // auto max_mmu_pid_setter = context.SetIntItemAttr("max_mmu_postive_pid");
      // for (int i = 0; i < fr_items.size(); i++) {
      //   max_mmu_score_setter(fr_items[i], max_mmu_score_matrix(i, 0));
      //   max_mmu_pid_setter(fr_items[i], user_public_postive_pids->at(0));
      // }
      // base::perfutil::PerfUtilWrapper::IntervalLogStash(
      //   50000, "reco.nearby.NearbyLightFrFunctionSet", bizName,
      //   "CalMmuEmbeddingScore.mmu_max_score", "z_mx",
      //   std::to_string(static_cast<double>(max_mmu_score_matrix.maxCoeff(&row_index, &col_index))));
      // LOG(ERROR) << "z_mx debug: "
      //<< static_cast<double>(max_mmu_score_matrix.maxCoeff(&row_index, &col_index));
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
        public_score_matrix.minCoeff(&row_index, &col_index) * 1e6,
        "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.public_postive_score", "min");
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
        public_score_matrix.maxCoeff(&row_index, &col_index) * 1e6,
        "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.public_postive_score", "max");
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
        public_score_matrix.mean() * 1e6,
        "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.public_postive_score", "avg");
    } else {
      base::perfutil::PerfUtilWrapper::IntervalLogStash(1,
        "reco.nearby.NearbyLightFrFunctionSet", bizName,
        "CalMmuEmbeddingScore.public_postive_score", "emb_wrong_format");
    }
  }
  // zmx daza score
  int public_10_fill_num = 0;
  int public_10_emb_fill_num = 0;
  double public_10_emb_fill_rate = 0.0;
  std::vector<double> public_10_mmu_embeddings;
  // LOG(ERROR) << "zmx_debug_" << "public_fill_num"
  //   << public_fill_num;
  auto user_public_postive_near_10_pids =
    context.GetIntListCommonAttr("user_public_postive_near_10_pids");
  if (user_public_postive_near_10_pids
          && user_public_postive_near_10_pids->size() > 0) {
    public_10_fill_num = user_public_postive_near_10_pids->size();
    for (auto pid : *user_public_postive_near_10_pids) {
      auto it = std::find(user_public_postive_pids->begin(),
                  user_public_postive_pids->end(), pid);
      if (it != user_public_postive_pids->end()) {
        int index = std::distance(user_public_postive_pids->begin(), it);
        if (index < public_mmu_embeddings.size()) {
          // public_10_mmu_embeddings.push_back(public_mmu_embeddings[index]);
          for (int j = 0; j < emb_dim_size; ++j) {
            public_10_mmu_embeddings.push_back(
              public_mmu_embeddings[index * emb_dim_size + j]);
          }
          public_10_emb_fill_num += 1;
        }
      }
    }
  }
  if (public_10_fill_num > 0) {
    public_10_emb_fill_rate = 1.0 * public_10_emb_fill_num / public_10_fill_num;
  }
  // if (user_public_postive_pids->size() ==
  //     user_public_postive_near_10_pids->size()) {
  //   LOG(ERROR) << "zmx_debug_" << "xiangdeng:"
  //     << user_public_postive_pids->size() << ":"
  //     << user_public_postive_near_10_pids->size();
  // } else {
  //   LOG(ERROR) << "zmx_debug_" << "budeng:"
  //     << user_public_postive_pids->size() << ":"
  //     << user_public_postive_near_10_pids->size();
  // }
  // LOG(ERROR) << "zmx_debug_" << "public_10_emb_fill_rate: "
  //   << public_10_emb_fill_rate;
  // LOG(ERROR) << "zmx_debug_" << "public_10_emb_fill_num: "
  //   << public_10_emb_fill_num;
  // LOG(ERROR) << "zmx_debug_" << "public_10_fill_num: "
  //   << public_10_fill_num;
  if (public_10_fill_num > 0) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(public_10_fill_num,
      "reco.nearby.NearbyLightFrFunctionSet", bizName,
      "CalMmuEmbeddingScore.mmuEmb", "public_10_postive_fill");
  } else {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(1,
      "reco.nearby.NearbyLightFrFunctionSet", bizName,
      "CalMmuEmbeddingScore.mmuEmb", "public_10_postive_empty");
  }
  if (public_10_emb_fill_num > 0) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(public_10_emb_fill_num,
      "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmuEmb",
      "public_10_postive_emb_fill");
    base::perfutil::PerfUtilWrapper::IntervalLogStash(public_10_emb_fill_rate * 1000,
      "reco.nearby.NearbyLightFrFunctionSet", bizName,
      "CalMmuEmbeddingScore.mmuEmb", "public_10_postive_emb_fill_rate",
      std::to_string(public_10_emb_fill_rate));
  } else {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(1,
      "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.mmuEmb",
      "public_10_postive_emb_empty");
  }
  if (public_10_fill_num > 0 && public_10_emb_fill_num > 0) {
    if (public_10_fill_num * emb_dim_size == public_10_mmu_embeddings.size()) {
      Eigen::MatrixXd public_10_emb_matrix = Eigen::MatrixXd::Zero(public_10_fill_num, emb_dim_size);
      for (int i = 0; i < public_10_fill_num; ++i) {
        for (int j = 0; j < emb_dim_size; ++j) {
          public_10_emb_matrix(i, j) = public_10_mmu_embeddings[i * emb_dim_size + j];
        }
      }
      Eigen::MatrixXd public_10_score_matrix = Eigen::MatrixXd::Zero(item_num, 1);
      public_10_score_matrix = fr_embed_matrix * (public_10_emb_matrix.transpose());
      auto mmu_public_10_score_setter =
        context.SetDoubleItemAttr("mmu_max_postive_score");
      auto mmu_public_10_score_index_setter =
        context.SetIntItemAttr("mmu_max_postive_pid");
      std::vector<double> max_score_list;
      std::vector<double> max_score_index_list;
      for (int i = 0; i < public_10_score_matrix.rows(); ++i) {
        double max_score = public_10_score_matrix(i, 0);
        int max_score_index = 0;
        for (int j = 1; j < public_10_score_matrix.cols(); ++j) {
          if (public_10_score_matrix(i, j) > max_score) {
            max_score = public_10_score_matrix(i, j);
            max_score_index = j;
          }
        }
        max_score_list.push_back(max_score);
        max_score_index_list.push_back(max_score_index);
      }
      for (int i = 0; i < fr_items.size(); i++) {
        mmu_public_10_score_setter(fr_items[i], max_score_list.at(i));
        mmu_public_10_score_index_setter(fr_items[i],
          user_public_postive_near_10_pids->at(max_score_index_list.at(i)));
      }
      int col_index = 0, row_index = 0;
      // LOG(ERROR) << "zmx_debug_" << "public_10_score_matrix.min"
      //   << public_10_score_matrix.minCoeff(&row_index, &col_index);
      // LOG(ERROR) << "zmx_debug_" << "public_10_score_matrix.max"
      //   << public_10_score_matrix.maxCoeff(&row_index, &col_index);
      // LOG(ERROR) << "zmx_debug_" << "public_10_score_matrix.avg"
      //   << public_10_score_matrix.mean();
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
        1e6, "reco.nearby.NearbyLightFrFunctionSet", bizName,
        "CalMmuEmbeddingScore.public_10_postive_score", "min",
        std::to_string(static_cast<double>(public_10_score_matrix.minCoeff(&row_index, &col_index))));
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
        1e6, "reco.nearby.NearbyLightFrFunctionSet", bizName,
        "CalMmuEmbeddingScore.public_10_postive_score", "max",
        std::to_string(static_cast<double>(public_10_score_matrix.maxCoeff(&row_index, &col_index))));
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
        1e6, "reco.nearby.NearbyLightFrFunctionSet", bizName,
        "CalMmuEmbeddingScore.public_10_postive_score", "avg",
        std::to_string(static_cast<double>(public_10_score_matrix.mean())));
    } else {
      base::perfutil::PerfUtilWrapper::IntervalLogStash(1,
        "reco.nearby.NearbyLightFrFunctionSet", bizName,
        "CalMmuEmbeddingScore.public_10_postive_score", "emb_wrong_format");
      // LOG(ERROR) << "zmx_debug_" << "emb_wrong_format";
    }
  }

  base::perfutil::PerfUtilWrapper::IntervalLogStash(base::GetTimestamp() - start_ts,
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalMmuEmbeddingScore.duration", "total");
  return true;
}

// static
bool NearbyLightFrFunctionSet::CalAuthorUnClickScore(const CommonRecoLightFunctionContext &context,
  RecoResultConstIter begin, RecoResultConstIter end) {
  int64 start_ts = base::GetTimestamp();
  int item_num = std::distance(begin, end);
  if (item_num <= 0) {
    return false;
  }
  static const int64 U_PRODUCT_NEBULA = 1;
  static const int64 U_FEED_TYPE_SLIDE = 1;
  std::string bizName("unknown");
  int64 uProductType = context.GetIntCommonAttr("uProductType").value_or(0);
  int64 uFeedType = context.GetIntCommonAttr("uFeedType").value_or(0);
  if (U_PRODUCT_NEBULA == uProductType) {
    if (U_FEED_TYPE_SLIDE == uFeedType) {
      bizName = "bl_nearby_in";
    } else {
      bizName = "bl_nearby";
    }
  } else {
    if (U_FEED_TYPE_SLIDE == uFeedType) {
      bizName = "nearby_in";
    } else {
      bizName = "nearby";
    }
  }

  // generate real show author show count & click count
  std::unordered_map<int64, int> author_show;
  auto user_public_real_show_aids = context.GetIntListCommonAttr("user_public_real_show_aids");
  if (user_public_real_show_aids) {
    for (int i = 0; i < user_public_real_show_aids->size(); i++) {
      int64 aId = user_public_real_show_aids->at(i);
      if (author_show.count(aId) > 0) {
        author_show[aId] += 1;
      } else {
        author_show[aId] = 1;
      }
    }
  }

  auto user_public_click_aids = context.GetIntListCommonAttr("user_public_click_aids");
  std::unordered_map<int64, int> author_click;
  if (user_public_click_aids) {
    for (int i = 0; i < user_public_click_aids->size(); i++) {
      int64 aId = user_public_click_aids->at(i);
      if (author_click.count(aId) > 0) {
        author_click[aId] += 1;
      } else {
        author_click[aId] = 1;
      }
    }
  }

  // AB Parameter
  int64 nearby_author_unclick_score_min_show_count =
    context.GetIntCommonAttr("nearby_author_unclick_score_min_show_count").value_or(10);
  int64 nearby_author_unclick_score_prior_alpha =
    context.GetIntCommonAttr("nearby_author_unclick_score_prior_alpha").value_or(1);
  int64 nearby_author_unclick_score_prior_beta =
    context.GetIntCommonAttr("nearby_author_unclick_score_prior_beta").value_or(1);

  std::mt19937 random_engine(base::GetTimestamp());
  std::vector<int64> perf_count(5, 0L);
  auto get_item_aid_func = context.GetIntItemAttr("item_info.author_id");
  auto set_aid_unclick_thompson = context.SetDoubleItemAttr("aid_unclick_thompson");
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    // "item_info.author_id"
    int64 aId = get_item_aid_func(result).value_or(0L);
    if (aId != 0L) {
      perf_count[0] += 1;
      auto it_show = author_show.find(aId);
      if (it_show != author_show.end()) {
        perf_count[1] += 1;
        int show = it_show->second;
        if (show > nearby_author_unclick_score_min_show_count) {
          perf_count[2] += 1;
          int click = 0;
          auto it_click = author_click.find(aId);
          if (it_click != author_click.end()) {
            perf_count[3] += 1;
            click = it_click->second;
          }

          if (show < click) {
            // glitch fix
            show = click;
          }
          int un_click = show - click;

          int64 alpha = un_click + nearby_author_unclick_score_prior_alpha;
          int64 beta = show - un_click + nearby_author_unclick_score_prior_beta;
          if (alpha > 0 && beta > 0) {
            perf_count[4] += 1;
            boost::random::beta_distribution<> beta_dist(alpha, beta);
            double prob = beta_dist(random_engine);
            set_aid_unclick_thompson(result, prob);
          }
        }
      }
    }
  });
  base::perfutil::PerfUtilWrapper::IntervalLogStash((perf_count[0] * 1.0 / item_num) * 1e6,
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalAuthorUnClickScore", "aid_fill_ratio");
  base::perfutil::PerfUtilWrapper::IntervalLogStash((perf_count[1] * 1.0 / item_num) * 1e6,
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalAuthorUnClickScore", "aid_show_fill_ratio");
  base::perfutil::PerfUtilWrapper::IntervalLogStash((perf_count[2] * 1.0 / item_num) * 1e6,
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalAuthorUnClickScore", "aid_show_legal_ratio");
  base::perfutil::PerfUtilWrapper::IntervalLogStash((perf_count[3] * 1.0/ item_num) * 1e6,
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalAuthorUnClickScore", "aid_click_fill_ratio");
  base::perfutil::PerfUtilWrapper::IntervalLogStash((perf_count[4] * 1.0 / item_num) * 1e6,
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalAuthorUnClickScore", "aid_unclick_prob_fill_ratio");
  base::perfutil::PerfUtilWrapper::IntervalLogStash(author_show.size(),
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalAuthorUnClickScore", "author_show_size");
  base::perfutil::PerfUtilWrapper::IntervalLogStash(author_click.size(),
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalAuthorUnClickScore", "author_click_size");
  base::perfutil::PerfUtilWrapper::IntervalLogStash(base::GetTimestamp() - start_ts,
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalAuthorUnClickScore", "total duration");
  return true;
}

// static
bool NearbyLightFrFunctionSet::CalClusterUnClickScore(const CommonRecoLightFunctionContext &context,
  RecoResultConstIter begin, RecoResultConstIter end) {
  int64 start_ts = base::GetTimestamp();
  int item_num = std::distance(begin, end);
  if (item_num <= 0) {
    return false;
  }
  static const int64 U_PRODUCT_NEBULA = 1;
  static const int64 U_FEED_TYPE_SLIDE = 1;
  std::string bizName("unknown");
  int64 uProductType = context.GetIntCommonAttr("uProductType").value_or(0);
  int64 uFeedType = context.GetIntCommonAttr("uFeedType").value_or(0);
  if (U_PRODUCT_NEBULA == uProductType) {
    if (U_FEED_TYPE_SLIDE == uFeedType) {
      bizName = "bl_nearby_in";
    } else {
      bizName = "bl_nearby";
    }
  } else {
    if (U_FEED_TYPE_SLIDE == uFeedType) {
      bizName = "nearby_in";
    } else {
      bizName = "nearby";
    }
  }

  // generate real show cluster show count & click count
  std::unordered_map<int64, int> cluster_show;
  auto user_public_real_show_hetu_cluster_ids =
    context.GetIntListCommonAttr("user_public_real_show_hetu_cluster_ids");
  if (user_public_real_show_hetu_cluster_ids) {
    for (int i = 0; i < user_public_real_show_hetu_cluster_ids->size(); i++) {
      int64 cId = user_public_real_show_hetu_cluster_ids->at(i);
      if (cluster_show.count(cId) > 0) {
        cluster_show[cId] += 1;
      } else {
        cluster_show[cId] = 1;
      }
    }
  }

  auto user_public_click_hetu_cluster_ids =
    context.GetIntListCommonAttr("user_public_click_hetu_cluster_ids");
  std::unordered_map<int64, int> cluster_click;
  if (user_public_click_hetu_cluster_ids) {
    for (int i = 0; i < user_public_click_hetu_cluster_ids->size(); i++) {
      int64 cId = user_public_click_hetu_cluster_ids->at(i);
      if (cluster_click.count(cId) > 0) {
        cluster_click[cId] += 1;
      } else {
        cluster_click[cId] = 1;
      }
    }
  }

  // AB Parameter
  int64 nearby_hetu_cluster_unclick_score_min_show_count =
    context.GetIntCommonAttr("nearby_hetu_cluster_unclick_score_min_show_count").value_or(5);
  int64 nearby_hetu_cluster_unclick_score_prior_alpha =
    context.GetIntCommonAttr("nearby_hetu_cluster_unclick_score_prior_alpha").value_or(1);
  int64 nearby_hetu_cluster_unclick_score_prior_beta =
    context.GetIntCommonAttr("nearby_hetu_cluster_unclick_score_prior_beta").value_or(1);
  double nearby_hetu_cluster_unclick_score_wilson_z =
    context.GetDoubleCommonAttr("nearby_hetu_cluster_unclick_score_wilson_z").value_or(1.960);

  std::mt19937 random_engine(base::GetTimestamp());
  std::vector<int64> perf_count(5, 0L);
  std::unordered_set<int64> hitCIDs;
  auto get_item_cid_func = context.GetIntItemAttr("item_info.hetu_tag_level_info_v2.hetu_cluster_id");
  auto set_cluster_unclick_thompson = context.SetDoubleItemAttr("cluster_unclick_thompson");
  auto set_cluster_unclick_wilson = context.SetDoubleItemAttr("cluster_unclick_wilson");
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    // "item_info.hetu_tag_level_info_v2.hetu_cluster_id"
    int64 cId = get_item_cid_func(result).value_or(0L);
    perf_count[0] += 1;
    auto it_show = cluster_show.find(cId);
    if (it_show != cluster_show.end()) {
      perf_count[1] += 1;
      hitCIDs.emplace(cId);
      int show = it_show->second;
      if (show > nearby_hetu_cluster_unclick_score_min_show_count) {
        perf_count[2] += 1;
        int click = 0;
        auto it_click = cluster_click.find(cId);
        if (it_click != cluster_click.end()) {
          perf_count[3] += 1;
          click = it_click->second;
        }

        if (show < click) {
          // glitch fix
          show = click;
        }
        int un_click = show - click;

        // cluster_unclick_thompson
        int64 alpha = un_click + nearby_hetu_cluster_unclick_score_prior_alpha;
        int64 beta = show - un_click + nearby_hetu_cluster_unclick_score_prior_beta;
        if (alpha > 0 && beta > 0) {
          perf_count[4] += 1;
          boost::random::beta_distribution<> beta_dist(alpha, beta);
          double prob = beta_dist(random_engine);
          set_cluster_unclick_thompson(result, prob);
        }

        // cluster_unclick_wilson
        double prob_wilson = CalWilsonLB(un_click * 1.0, show * 1.0,
                                         nearby_hetu_cluster_unclick_score_wilson_z);
        set_cluster_unclick_wilson(result, prob_wilson);
      }
    }
  });
  base::perfutil::PerfUtilWrapper::IntervalLogStash((perf_count[0] * 1.0 / item_num) * 1e6,
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalClusterUnClickScore", "cid_fill_ratio");
  base::perfutil::PerfUtilWrapper::IntervalLogStash((perf_count[1] * 1.0 / item_num) * 1e6,
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalClusterUnClickScore", "cid_show_fill_ratio");
  base::perfutil::PerfUtilWrapper::IntervalLogStash((perf_count[2] * 1.0 / item_num) * 1e6,
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalClusterUnClickScore", "cid_show_legal_ratio");
  base::perfutil::PerfUtilWrapper::IntervalLogStash((perf_count[3] * 1.0/ item_num) * 1e6,
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalClusterUnClickScore", "cid_click_fill_ratio");
  base::perfutil::PerfUtilWrapper::IntervalLogStash((perf_count[4] * 1.0 / item_num) * 1e6,
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalClusterUnClickScore", "cid_unclick_prob_fill_ratio");
  base::perfutil::PerfUtilWrapper::IntervalLogStash(cluster_show.size(),
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalClusterUnClickScore", "cluster_show_size");
  base::perfutil::PerfUtilWrapper::IntervalLogStash(cluster_click.size(),
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalClusterUnClickScore", "cluster_click_size");
  base::perfutil::PerfUtilWrapper::IntervalLogStash(hitCIDs.size(),
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalClusterUnClickScore", "cluster_hit_show_size");
  base::perfutil::PerfUtilWrapper::IntervalLogStash(base::GetTimestamp() - start_ts,
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalClusterUnClickScore", "total duration");
  return true;
}

bool NearbyLightFrFunctionSet::CalPubLastEmbAvg(const CommonRecoLightFunctionContext &context,
  RecoResultConstIter begin, RecoResultConstIter end) {
  // LOG(ERROR) << "zmx_debug: " << "calpublast begin!";
  auto pos_emb = context.GetDoubleListCommonAttr("switch_nearby_pub_last_pos_emb");
  auto neg_emb = context.GetDoubleListCommonAttr("switch_nearby_pub_last_neg_emb");
  std::vector<double> pos_emb_list;
  std::vector<double> neg_emb_list;
  std::vector<double> pos_emb_avg_list;
  std::vector<double> neg_emb_avg_list;
  int pos_num = 0;
  int neg_num = 0;
  int emb_dim_size = 64;
  if ((pos_emb && pos_emb->size() > 0) || (neg_emb && neg_emb->size() > 0)) {
    if (pos_emb && pos_emb->size() > 0) {
      pos_num = pos_emb->size();
      pos_num /= emb_dim_size;
      for (auto double_val : *pos_emb) {
        pos_emb_list.push_back(double_val);
      }
    }
    if (neg_emb && neg_emb->size() > 0) {
      neg_num = neg_emb->size();
      neg_num /= emb_dim_size;
      for (auto double_val : *neg_emb) {
        neg_emb_list.push_back(double_val);
      }
    }
    for (int i = 0; i < emb_dim_size; i++) {
      double cur_val = 0.0;
      for (int j = 0; j < pos_num; j++) {
        cur_val += pos_emb_list[j * emb_dim_size + i];
      }
      cur_val /= pos_num;
      pos_emb_avg_list.push_back(cur_val);
    }
    for (int i = 0; i < emb_dim_size; i++) {
      double cur_val = 0.0;
      for (int j = 0; j < neg_num; j++) {
        cur_val += neg_emb_list[j * emb_dim_size + i];
      }
      cur_val /= neg_num;
      neg_emb_avg_list.push_back(cur_val);
    }
    // LOG(ERROR) << "zmx_debug: "
    //   << "pos_num: " << pos_num
    //   << ", neg_num: " << neg_num;
    context.SetDoubleListCommonAttr("switch_nearby_pub_last_pos_emb_avg", std::move(pos_emb_avg_list));
    context.SetDoubleListCommonAttr("switch_nearby_pub_last_neg_emb_avg", std::move(neg_emb_avg_list));
  } else {
    // LOG(ERROR) << "zmx_debug: "
    //   << "pos & neg all null";
      return false;
  }
  return true;
}

bool NearbyLightFrFunctionSet::CalLiveMmuEmbScore(const CommonRecoLightFunctionContext &context,
  RecoResultConstIter begin, RecoResultConstIter end) {
    int item_num = std::distance(begin, end);
    if (item_num <= 0) {
      return false;
    }
    static const int64 U_PRODUCT_NEBULA = 1;
    static const int64 U_FEED_TYPE_SLIDE = 1;
    std::string bizName("unknown");
    int64 uProductType = context.GetIntCommonAttr("uProductType").value_or(0);
    int64 uFeedType = context.GetIntCommonAttr("uFeedType").value_or(0);
    if (U_PRODUCT_NEBULA == uProductType) {
      if (U_FEED_TYPE_SLIDE == uFeedType) {
        bizName = "bl_nearby_in";
      } else {
        bizName = "bl_nearby";
      }
    } else {
      if (U_FEED_TYPE_SLIDE == uFeedType) {
        bizName = "nearby_in";
      } else {
        bizName = "nearby";
      }
    }

    auto item_llm_emb_getter =
      context.GetDoubleListItemAttr("live_llm_embedding");
    int fill_num = 0;
    std::vector<double> item_llm_embedding;
    std::vector<CommonRecoResult> fr_items;
    int emb_dim_size = 64;

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      fr_items.push_back(result);
      // item llm emb
      auto live_llm_emb = item_llm_emb_getter(result);
      if (live_llm_emb && live_llm_emb->size() == emb_dim_size) {
        for (auto &item_e : *live_llm_emb) {
          item_llm_embedding.push_back(item_e);
        }
        fill_num++;
      } else {
        for (int i = 0; i < emb_dim_size; i++) {
          item_llm_embedding.push_back(0.0);
        }
      }
    });

    if (item_num > 0) {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(((1.0 * fill_num) / item_num) * 1000,
          "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalLiveMmuEmbScore.itemMmuEmb", "fillRatio");
    } else {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(0,
          "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalLiveMmuEmbScore.itemMmuEmb", "fillRatio_zero");
        return false;
    }

    Eigen::MatrixXd fr_embed_matrix = Eigen::MatrixXd::Zero(item_num, emb_dim_size);
    for (int i = 0; i < item_num; ++i) {
      for (int j = 0; j < emb_dim_size; ++j) {
        fr_embed_matrix(i, j) = item_llm_embedding[i * emb_dim_size + j];
      }
    }

    int public_fill_num = 0;
    int public_emb_fill_num = 0;
    std::vector<double> public_llm_embeddings;
    auto user_public_postive_pids =
      context.GetIntListCommonAttr("public_live_consume_liveids");
    if (user_public_postive_pids && user_public_postive_pids->size() > 0) {
      public_fill_num = user_public_postive_pids->size();
      auto user_public_postive_pid_embs =
        context.GetDoubleListCommonAttr("public_live_consume_liveids_emb");
      if (user_public_postive_pid_embs && user_public_postive_pid_embs->size() > 0) {
        public_emb_fill_num = user_public_postive_pid_embs->size();
        for (auto double_val : *user_public_postive_pid_embs) {
          public_llm_embeddings.push_back(double_val);
        }
      }
    }

    if (public_fill_num > 0 && public_emb_fill_num > 0) {
      if (public_fill_num * emb_dim_size == public_llm_embeddings.size()) {
        Eigen::MatrixXd public_emb_matrix = Eigen::MatrixXd::Zero(public_fill_num, emb_dim_size);
        for (int i = 0; i < public_fill_num; ++i) {
          for (int j = 0; j < emb_dim_size; ++j) {
            public_emb_matrix(i, j) = public_llm_embeddings[i * emb_dim_size + j];
          }
        }
        Eigen::MatrixXd public_score_matrix = Eigen::MatrixXd::Zero(item_num, 1);
        public_score_matrix = fr_embed_matrix * (public_emb_matrix.transpose().rowwise().sum());
        public_score_matrix = public_score_matrix / (1.0 * public_fill_num);
        auto live_llm_public_score_setter =
          context.SetDoubleItemAttr("live_llm_fr_score_v1");
        for (int i = 0; i < fr_items.size(); i++) {
          live_llm_public_score_setter(fr_items[i], public_score_matrix(i, 0));
        }
        int col_index = 0, row_index = 0;
        base::perfutil::PerfUtilWrapper::IntervalLogStash(
          public_score_matrix.minCoeff(&row_index, &col_index) * 1e6,
          "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalLiveMmuEmbScore.public_postive_score", "min");
        base::perfutil::PerfUtilWrapper::IntervalLogStash(
          public_score_matrix.maxCoeff(&row_index, &col_index) * 1e6,
          "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalLiveMmuEmbScore.public_postive_score", "max");
        base::perfutil::PerfUtilWrapper::IntervalLogStash(
          public_score_matrix.mean() * 1e6,
          "reco.nearby.NearbyLightFrFunctionSet", bizName, "CalLiveMmuEmbScore.public_postive_score", "avg");
      } else {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(1,
          "reco.nearby.NearbyLightFrFunctionSet", bizName,
          "CalLiveMmuEmbScore.public_postive_score", "emb_wrong_format");
      }
    }
    return true;
  }

  bool NearbyLightFrFunctionSet::CalLiveMmuEmbScoreV2(const CommonRecoLightFunctionContext &context,
  RecoResultConstIter begin, RecoResultConstIter end) {
    int item_num = std::distance(begin, end);
    if (item_num <= 0) {
      return false;
    }
    // 前一分钟 live emb
    auto item_llm_emb_getter_v2 = context.GetDoubleListItemAttr("live_llm_embedding_v2");
    if (!item_llm_emb_getter_v2) {
      return false;
    }

    int fill_num_v2 = 0;
    std::vector<double> item_llm_embedding_v2;
    std::vector<CommonRecoResult> fr_items;
    int emb_dim_size = 64;

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      fr_items.push_back(result);
      auto live_llm_emb_v2 = item_llm_emb_getter_v2(result);
      if (live_llm_emb_v2 && live_llm_emb_v2->size() == emb_dim_size) {
        for (auto &item_e : *live_llm_emb_v2) {
          item_llm_embedding_v2.push_back(item_e);
        }
        fill_num_v2++;
      } else {
        for (int i = 0; i < emb_dim_size; i++) {
          item_llm_embedding_v2.push_back(0.0);
        }
      }
    });

    Eigen::MatrixXd fr_embed_matrix_v2 = Eigen::MatrixXd::Zero(item_num, emb_dim_size);
    for (int i = 0; i < item_num; ++i) {
      for (int j = 0; j < emb_dim_size; ++j) {
        fr_embed_matrix_v2(i, j) = item_llm_embedding_v2[i * emb_dim_size + j];
      }
    }

    int public_fill_num_v2 = 0;
    int public_emb_fill_num_v2 = 0;
    std::vector<double> public_llm_embeddings_v2;
    auto user_public_postive_pids_v2 =
      context.GetIntListCommonAttr("public_live_consume_liveids");
    if (user_public_postive_pids_v2 && user_public_postive_pids_v2->size() > 0) {
      public_fill_num_v2 = user_public_postive_pids_v2->size();
      auto user_public_postive_pid_embs_v2 =
        context.GetDoubleListCommonAttr("public_live_consume_liveids_emb_v2");
      if (user_public_postive_pid_embs_v2 && user_public_postive_pid_embs_v2->size() > 0) {
        public_emb_fill_num_v2 = user_public_postive_pid_embs_v2->size();
        for (auto double_val : *user_public_postive_pid_embs_v2) {
          public_llm_embeddings_v2.push_back(double_val);
        }
      }
    }

    if (public_fill_num_v2 > 0 && public_emb_fill_num_v2 > 0) {
      if (public_fill_num_v2 * emb_dim_size == public_llm_embeddings_v2.size()) {
          Eigen::MatrixXd public_emb_matrix_v2 = Eigen::MatrixXd::Zero(public_fill_num_v2, emb_dim_size);
          for (int i = 0; i < public_fill_num_v2; ++i) {
              for (int j = 0; j < emb_dim_size; ++j) {
                  public_emb_matrix_v2(i, j) = public_llm_embeddings_v2[i * emb_dim_size + j];
              }
          }

          Eigen::MatrixXd public_score_matrix_v2 = Eigen::MatrixXd::Zero(item_num, 1);
          public_score_matrix_v2 = fr_embed_matrix_v2 * (public_emb_matrix_v2.transpose().rowwise().sum());
          public_score_matrix_v2 = public_score_matrix_v2 / (1.0 * public_fill_num_v2);

          auto live_llm_public_score_setter_v2 = context.SetDoubleItemAttr("live_llm_fr_score_v2");
          if (live_llm_public_score_setter_v2) {
              for (int i = 0; i < fr_items.size(); i++) {
                  live_llm_public_score_setter_v2(fr_items[i], public_score_matrix_v2(i, 0));
              }
          }
      }
    }
    return true;
  }

// static
bool NearbyLightFrFunctionSet::EnrichPhotoNearFieldSupplyFactor(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end) {
  int item_num = std::distance(begin, end);
  if (item_num <= 0) {
    return false;
  }
  int64 start_ts = base::GetTimestamp();
  std::string bizName = GetBizName(context);
  auto get_item_fullrank_ori_ctr = context.GetDoubleItemAttr("fullrank_ori_ctr");
  auto get_item_fullrank_ori_wtr = context.GetDoubleItemAttr("fullrank_ori_wtr");
  auto get_item_fullrank_ori_lvtr = context.GetDoubleItemAttr("fullrank_ori_lvtr");
  auto get_item_is_same_city_for_user_city_preference =
    context.GetIntItemAttr("is_same_city_for_user_city_preference");
  auto get_item_distance = context.GetDoubleItemAttr("pLeafDistance");
  auto get_item_fans_count = context.GetIntItemAttr("item_info.fans_count");
  auto get_item_timestamp = context.GetIntItemAttr("item_info.timestamp");


  auto item_is_same_city_setter = context.SetIntItemAttr("is_same_city_for_user_city_preference");
  NearbyPriorityQueue same_city_supply;
  NearbyPriorityQueue none_same_city_supply;
  NearbyPriorityQueue fans_1k_10w_supply;
  NearbyPriorityQueue none_fans_1k_10w_supply;
  NearbyPriorityQueue age_48h_supply;
  NearbyPriorityQueue age_48h_supply_ctr;
  NearbyPriorityQueue none_age_48h_supply;
  NearbyPriorityQueue none_age_48h_supply_ctr;

  const int64 supply_capacity = 50;
  double min_ue_score = std::numeric_limits<double>::max();
  double min_ctr = std::numeric_limits<double>::max();
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    double fullrank_ori_ctr = get_item_fullrank_ori_ctr(result).value_or(0.0);
    double fullrank_ori_wtr = get_item_fullrank_ori_wtr(result).value_or(0.0);
    double fullrank_ori_lvtr = get_item_fullrank_ori_lvtr(result).value_or(0.0);
    if (fullrank_ori_ctr <= 0.0 || fullrank_ori_wtr <= 0.0 || fullrank_ori_lvtr <= 0.0) {
      return;
    }

    double ue_score = fullrank_ori_ctr * (1.0 + fullrank_ori_wtr + fullrank_ori_lvtr);
    if (min_ue_score > ue_score) {
      min_ue_score = ue_score;
    }
    if (min_ctr > fullrank_ori_ctr) {
      min_ctr = fullrank_ori_ctr;
    }

    // same city factor
    int64 is_same_city = get_item_is_same_city_for_user_city_preference(result).value_or(0L);
    if (is_same_city > 0L) {
      MinHeapPush(result, ue_score, supply_capacity, &same_city_supply);
    } else {
      MinHeapPush(result, ue_score, supply_capacity, &none_same_city_supply);
    }

    // fans count
    int64 fans_count = get_item_fans_count(result).value_or(0);
    const int64 fans_count_lb = 1000;
    const int64 fans_count_ub = 100000;
    if (fans_count >= fans_count_lb && fans_count < fans_count_ub) {
      MinHeapPush(result, ue_score, supply_capacity, &fans_1k_10w_supply);
    } else {
      MinHeapPush(result, ue_score, supply_capacity, &none_fans_1k_10w_supply);
    }

    // photo age in days
    int64 photo_timestamp = get_item_timestamp(result).value_or(0);
    double photo_age_in_hour = (start_ts - photo_timestamp) * 1.0 / base::Time::kMicrosecondsPerHour;
    if (photo_age_in_hour < 48.0) {
      MinHeapPush(result, ue_score, supply_capacity, &age_48h_supply);
      MinHeapPush(result, fullrank_ori_ctr, supply_capacity, &age_48h_supply_ctr);
    } else {
      MinHeapPush(result, ue_score, supply_capacity, &none_age_48h_supply);
      MinHeapPush(result, fullrank_ori_ctr, supply_capacity, &none_age_48h_supply_ctr);
    }
  });

  double same_city_supply_factor =
    CalCompetitiveScore(&same_city_supply, &none_same_city_supply, supply_capacity, min_ue_score);
  double fans_1k_10w_factor =
    CalCompetitiveScore(&fans_1k_10w_supply, &none_fans_1k_10w_supply, supply_capacity, min_ue_score);
  double age_48h_factor =
    CalCompetitiveScore(&age_48h_supply, &none_age_48h_supply, supply_capacity, min_ue_score);
  double age_48h_factor_ctr =
    CalCompetitiveScore(&age_48h_supply_ctr, &none_age_48h_supply_ctr, supply_capacity, min_ctr);
  context.SetDoubleCommonAttr("same_city_photo_supply_factor", same_city_supply_factor);
  context.SetDoubleCommonAttr("fans_1k_10w_photo_supply_factor", fans_1k_10w_factor);
  context.SetDoubleCommonAttr("age_48h_photo_supply_factor", age_48h_factor);
  context.SetDoubleCommonAttr("age_48h_photo_supply_factor_ctr", age_48h_factor_ctr);

  /*LOG(INFO) << " fans_1k_10w_factor=" << fans_1k_10w_factor
            << " age_48h_photo_supply_factor=" << age_48h_factor
            << " age_48h_factor_ctr=" << age_48h_factor_ctr
            << " same_city_supply_factor=" << same_city_supply_factor;*/

  base::perfutil::PerfUtilWrapper::IntervalLogStash(base::GetTimestamp() - start_ts,
    "reco.nearby.NearbyLightFrFunctionSet", bizName,
    "EnrichPhotoNearFieldSupplyFactor", "total duration");
  return true;
}


// static
bool NearbyLightFrFunctionSet::EnrichPhotoPxtrCompetitiveDegree(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end) {
  int item_num = std::distance(begin, end);
  if (item_num <= 0) {
    return false;
  }
  int64 start_ts = base::GetTimestamp();
  std::string bizName = GetBizName(context);
  auto get_item_fullrank_ori_ctr = context.GetDoubleItemAttr("fullrank_ori_ctr");
  auto get_item_fullrank_ori_evtr = context.GetDoubleItemAttr("fullrank_ori_evtr_new");
  const int64 supply_capacity = 50;
  NearbyPriorityQueue ctr_supply;
  NearbyPriorityQueue evtr_supply;
  NearbyPriorityQueue ectr_supply;
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    double fullrank_ori_ctr = get_item_fullrank_ori_ctr(result).value_or(0.0);
    double fullrank_ori_evtr = get_item_fullrank_ori_evtr(result).value_or(0.0);
    if (fullrank_ori_ctr <= 0.0 || fullrank_ori_evtr <= 0.0) {
      return;
    }

    MinHeapPush(result, fullrank_ori_ctr, supply_capacity, &ctr_supply);
    MinHeapPush(result, fullrank_ori_evtr, supply_capacity, &evtr_supply);
    MinHeapPush(result, fullrank_ori_evtr * fullrank_ori_ctr, supply_capacity, &ectr_supply);
  });

  double ctr_conpetitive_degree = CalCompetitiveDegree(&ctr_supply, supply_capacity);
  context.SetDoubleCommonAttr("fullrank_ori_ctr_conpetitive_degree", ctr_conpetitive_degree);

  double evtr_conpetitive_degree = CalCompetitiveDegree(&evtr_supply, supply_capacity);
  context.SetDoubleCommonAttr("fullrank_ori_evtr_conpetitive_degree", evtr_conpetitive_degree);

  double ectr_conpetitive_degree = CalCompetitiveDegree(&ectr_supply, supply_capacity);
  context.SetDoubleCommonAttr("fullrank_ori_ectr_conpetitive_degree", ectr_conpetitive_degree);

  base::perfutil::PerfUtilWrapper::IntervalLogStash(base::GetTimestamp() - start_ts,
    "reco.nearby.NearbyLightFrFunctionSet", bizName,
    "EnrichPhotoPxtrCompetitiveDegree", "total duration");
  return true;
}


// static
bool NearbyLightFrFunctionSet::EnrichUserFollowPreferenceFactor(const CommonRecoLightFunctionContext &context,
  RecoResultConstIter begin, RecoResultConstIter end) {
  int item_num = std::distance(begin, end);
  if (item_num <= 0) {
    return false;
  }
  int64 start_ts = base::GetTimestamp();
  std::string bizName = GetBizName(context);
  auto get_item_fullrank_ori_ctr = context.GetDoubleItemAttr("fullrank_ori_ctr");
  auto get_item_is_followed = context.GetIntItemAttr("is_followed");

  NearbyPriorityQueue followed_items;
  NearbyPriorityQueue none_followed_items;

  const int64 compare_size = 10;
  double min_ue_score = std::numeric_limits<double>::max();
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    double fullrank_ori_ctr = get_item_fullrank_ori_ctr(result).value_or(0.0);
    if (fullrank_ori_ctr <= 0.0) {
      return;
    }

    double ue_score = fullrank_ori_ctr;
    if (min_ue_score > ue_score) {
      min_ue_score = ue_score;
    }

    // follow preference factor
    double is_followed = get_item_is_followed(result).value_or(0.0);
    if (is_followed > 0.1) {
      MinHeapPush(result, ue_score, compare_size, &followed_items);
    } else {
      MinHeapPush(result, ue_score, compare_size, &none_followed_items);
    }
  });

  double user_follow_preference_factor =
    CalCompetitiveScore(&followed_items, &none_followed_items, compare_size, min_ue_score);
  context.SetDoubleCommonAttr("user_follow_preference_factor", user_follow_preference_factor);

  base::perfutil::PerfUtilWrapper::IntervalLogStash(base::GetTimestamp() - start_ts,
    "reco.nearby.NearbyLightFrFunctionSet", bizName,
    "EnrichUserFollowPreferenceFactor", "total duration");
  return true;
}


// static
bool NearbyLightFrFunctionSet::CalNearFieldPhotoSupplyFactor(const CommonRecoLightFunctionContext &context,
  RecoResultConstIter begin, RecoResultConstIter end) {
  int64 item_num = std::distance(begin, end);
  if (item_num <= 0) {
    return false;
  }
  int64 start_ts = base::GetTimestamp();
  std::string bizName = GetBizName(context);
  int64 user_city_id = context.GetIntCommonAttr("user_city_id").value_or(0);
  int64 user_province_id = context.GetIntCommonAttr("user_province_id").value_or(0);
  int64 user_adcode = context.GetIntCommonAttr("user_adcode").value_or(0);
  int64 distance_rank_photo_supply_factor_compare_size =
    context.GetIntCommonAttr("distance_rank_photo_supply_factor_compare_size").value_or(2000);

  auto get_item_ue_score = context.GetDoubleItemAttr("distance_rank_photo_ue_score");
  auto item_province_id_accessor = context.GetIntItemAttr("item_province_id");
  auto item_city_id_accessor = context.GetIntItemAttr("item_city_id");
  auto adcode_accessor = context.GetIntItemAttr("item_info.item_geo_info.adcode");

  NearbyPriorityQueue same_city_supply;
  NearbyPriorityQueue none_same_city_supply;
  double min_ue_score = std::numeric_limits<double>::max();
  int64 near_field_coefficient = 0;
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    double item_ue_score = get_item_ue_score(result).value_or(0);
    if (min_ue_score > item_ue_score) {
      min_ue_score = item_ue_score;
    }
    int64 item_province_id = item_province_id_accessor(result).value_or(0);
    int64 item_city_id = item_city_id_accessor(result).value_or(0);
    int64 item_adcode = adcode_accessor(result).value_or(0);
    if ((item_city_id == user_city_id && item_city_id > 0
      && user_province_id == item_province_id && user_province_id > 0)
      || (user_adcode / 100 == item_adcode / 100 && item_adcode > 0)) {
      // same city
      MinHeapPush(result, item_ue_score, distance_rank_photo_supply_factor_compare_size,
        &same_city_supply);
      near_field_coefficient += 1;
    } else {
      MinHeapPush(result, item_ue_score, distance_rank_photo_supply_factor_compare_size,
        &none_same_city_supply);
    }
  });

  std::vector<std::pair<CommonRecoResult, double>> same_city_top;
  MinHeap2OrderedVec(&same_city_supply, &same_city_top, true);
  std::vector<std::pair<CommonRecoResult, double>> none_same_city_top;
  MinHeap2OrderedVec(&none_same_city_supply, &none_same_city_top, true);

  double nearby_field_photo_supply_factor_relative = CalCompetitiveScore2(same_city_top,
    none_same_city_top, distance_rank_photo_supply_factor_compare_size, min_ue_score);
  context.SetDoubleCommonAttr("nearby_field_photo_supply_factor_relative",
    nearby_field_photo_supply_factor_relative);

  double nearby_field_photo_supply_factor = CalCompetitiveDegree2(same_city_top,
    distance_rank_photo_supply_factor_compare_size);
  context.SetDoubleCommonAttr("nearby_field_photo_supply_factor", nearby_field_photo_supply_factor);

  context.SetIntCommonAttr("nearby_field_photo_supply_coefficient", near_field_coefficient);
  context.SetIntCommonAttr("nearby_field_photo_supply_coefficient2", item_num - near_field_coefficient);

  base::perfutil::PerfUtilWrapper::IntervalLogStash(base::GetTimestamp() - start_ts,
    "reco.nearby.NearbyLightFrFunctionSet", bizName,
    "CalNearFieldPhotoSupplyFactor", "total duration");
  /* LOG(INFO) << " nearby_field_photo_supply_factor_relative=" << nearby_field_photo_supply_factor_relative
            << " nearby_field_photo_supply_factor=" << nearby_field_photo_supply_factor
            << " same_city_top=" << same_city_top.size()
            << " none_same_city_top=" << none_same_city_top.size();*/
  return true;
}

// static
bool NearbyLightFrFunctionSet::EnrichPhotoConvolutionPxtr(const CommonRecoLightFunctionContext &context,
  RecoResultConstIter begin, RecoResultConstIter end) {
  int item_num = std::distance(begin, end);
  if (item_num <= 0) {
    return false;
  }
  int64 start_ts = base::GetTimestamp();
  std::string bizName = GetBizName(context);

  auto item_mmu_emb_getter = context.GetDoubleListItemAttr("photo_mmu_emb");
  auto item_ctr_getter = context.GetDoubleItemAttr("fullrank_ori_ctr");
  auto item_wtd_getter = context.GetDoubleItemAttr("fullrank_ori_wtd");
  std::vector<double> item_mmu_embedding;
  std::vector<CommonRecoResult> fr_items;
  std::vector<double> ctrs;
  std::vector<double> wtds;
  const int emb_dim_size = 64;
  std::random_device rd;  // Will be used to obtain a seed for the random number engine
  std::mt19937 gen(rd());  // Standard mersenne_twister_engine seeded with rd()
  std::uniform_real_distribution<> uniform_dis(0.0, 1.0);
  const int64 sample_count = context
    .GetIntCommonAttr("enrich_convolution_photo_pxtr_parameter_max_sample_count")
    .value_or(400);
  const double sample_rate = sample_count / (item_num * 0.8);
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    auto photo_mmu_emb = item_mmu_emb_getter(result);
    if (photo_mmu_emb && photo_mmu_emb->size() == emb_dim_size) {
      if (uniform_dis(gen) <= sample_rate) {
        for (auto &item_e : *photo_mmu_emb) {
          item_mmu_embedding.push_back(item_e);
        }
        fr_items.push_back(result);
        double ctr = item_ctr_getter(result).value_or(0.0);
        ctrs.push_back(ctr);
        double wtd = item_wtd_getter(result).value_or(0.0);
        wtds.push_back(wtd);
      }
    }
  });
  int64 fill_num = fr_items.size();
  base::perfutil::PerfUtilWrapper::IntervalLogStash(((1.0 * fill_num) / item_num) * 1000,
    "reco.nearby.NearbyLightFrFunctionSet", bizName, "EnrichPhotoConvolutionPxtr", "fillRatio");

  Eigen::MatrixXd fr_embed_matrix = Eigen::MatrixXd::Zero(fill_num, emb_dim_size);
  Eigen::MatrixXd X = Eigen::MatrixXd::Zero(fill_num, 2);
  for (int i = 0; i < fr_items.size() && i < ctrs.size() && i < wtds.size(); ++i) {
    for (int j = 0; j < emb_dim_size; ++j) {
      fr_embed_matrix(i, j) = item_mmu_embedding[i * emb_dim_size + j];
    }
    X(i, 0) = ctrs[i];
    X(i, 1) = wtds[i];
  }

  base::perfutil::PerfUtilWrapper::IntervalLogStash(base::GetTimestamp() - start_ts,
    "reco.nearby.NearbyLightFrFunctionSet", bizName,
    "EnrichPhotoConvolutionPxtr", "total duration 1");

  Eigen::MatrixXd A = Eigen::MatrixXd::Zero(fill_num, fill_num);
  A = fr_embed_matrix * fr_embed_matrix.transpose();

  base::perfutil::PerfUtilWrapper::IntervalLogStash(base::GetTimestamp() - start_ts,
    "reco.nearby.NearbyLightFrFunctionSet", bizName,
    "EnrichPhotoConvolutionPxtr", "total duration 2");

  double beta = context
    .GetDoubleCommonAttr("enrich_convolution_photo_pxtr_parameter_beta")
    .value_or(1.0);
  double tau = context
    .GetDoubleCommonAttr("enrich_convolution_photo_pxtr_parameter_tau")
    .value_or(0.80);
  for (int index = 0; index < fill_num; index++) {
    A(index, index) = beta;
  }
  for (int line = 0; line < fill_num; line++) {
    for (int col = 0; col < fill_num; col++) {
      if (A(line, col) < tau) {
        A(line, col) = 0.0;
      }
    }
  }

  base::perfutil::PerfUtilWrapper::IntervalLogStash(base::GetTimestamp() - start_ts,
    "reco.nearby.NearbyLightFrFunctionSet", bizName,
    "EnrichPhotoConvolutionPxtr", "total duration 3");

  Eigen::MatrixXd d = A.rowwise().sum().array().pow(-0.5);
  Eigen::MatrixXd D = d.asDiagonal();

  base::perfutil::PerfUtilWrapper::IntervalLogStash(base::GetTimestamp() - start_ts,
    "reco.nearby.NearbyLightFrFunctionSet", bizName,
    "EnrichPhotoConvolutionPxtr", "total duration 4");

  Eigen::MatrixXd Y = D * (A * (D * X));
  auto set_convolutional_ctr = context.SetDoubleItemAttr("fullrank_convolution_ctr");
  auto set_convolutional_wtd = context.SetDoubleItemAttr("fullrank_convolution_wtd");
  for (int index = 0; index < fill_num && index < Y.rows(); index++) {
    double convolutional_ctr = Y(index, 0);
    set_convolutional_ctr(fr_items[index], convolutional_ctr);

    double convolutional_wtd = Y(index, 1);
    set_convolutional_wtd(fr_items[index], convolutional_wtd);
  }

  base::perfutil::PerfUtilWrapper::IntervalLogStash(base::GetTimestamp() - start_ts,
    "reco.nearby.NearbyLightFrFunctionSet", bizName,
    "EnrichPhotoConvolutionPxtr", "total duration 5");

  /*LOG(INFO) << " A.rows=" << A.rows() << " A.cols=" << A.cols() << " A.size=" << A.size()
            << " D.rows=" << D.rows() << " D.cols=" << D.cols() << " D.size=" << D.size()
            << " X.rows=" << X.rows() << " X.cols=" << X.cols() << " X.size=" << X.size()
            << " Y.rows=" << Y.rows() << " Y.cols=" << Y.cols() << " Y.size=" << Y.size();*/

  return true;
}


FACTORY_REGISTER(CommonRecoBaseLightFunctionSet, NearbyLightFrFunctionSet, NearbyLightFrFunctionSet)

}  // namespace platform
}  // namespace ks
