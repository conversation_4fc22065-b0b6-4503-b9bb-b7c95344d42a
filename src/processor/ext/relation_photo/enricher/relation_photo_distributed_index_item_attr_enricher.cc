#include "dragon/src/processor/ext/relation_photo/enricher/relation_photo_distributed_index_item_attr_enricher.h"

#include <utility>
#include <algorithm>
#include <map>
#include <cmath>
#include "ks/common_reco/util/key_sign_util.h"
#include "ks/realtime_reco/index/cdoc_convertor.h"
#include "ks/realtime_reco/index/common_def.h"
#include "ks/realtime_reco/index/follow_convertor.h"
#include "ks/reco_proto/proto/realtime_reco.pb.h"

namespace ks {
namespace platform {

struct CmpByValue {
  bool operator()(const std::pair<uint64, double>& lhs, const std::pair<uint64, double>& rhs) {
    return lhs.second > rhs.second;
  }
};

static constexpr int64_t kMaxGrpcReqLen = 5000;
void RelationPhotoDistributedIndexItemAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                                           RecoResultConstIter begin,
                                                           RecoResultConstIter end) {
  item_ids_.clear();
  item_id_to_key_map_.clear();
  item_vec_.clear();
  item_keys_.clear();
  GetFriendScoreMap(context);
  GetIntimateFeatureMap(context);
  GetFriendInteractFeatureMap(context);

  std::for_each(begin, end, [this](const CommonRecoResult &result) {
    // item_key 包含了 item 类型、页面、item id
    uint64 id = Util::GetId(result.item_key);
    // 请求远程关注页索引时候，需要把 key 转化成关注页 item 的格式
    uint64 key = GetFollowPageItemKey(id);
    item_id_to_key_map_.insert(std::make_pair(id, key));
    item_keys_.emplace_back(key);
  });

  // 对于取索引之前 attr 已经有 author_id 的，填充 ua 特征
  if (context->GetIntCommonAttr("enable_fill_ua_intimate_for_author").value_or(0) > 0) {
    for (auto iter = begin; iter != end; iter++) {
      auto result = *iter;
      auto item_key = result.item_key;
      auto author_id = context->GetIntItemAttr(item_key, "author_id");
      if (author_id) {
        FillIntimateFeature(context, item_key, *author_id, std::move(intimate_feature_map_));
      }
    }
  }

  if (item_keys_.size() >= kMaxGrpcReqLen) {
    item_keys_.resize(kMaxGrpcReqLen);
    LOG(WARNING) << "candidate keys for remote distribute index exceed :" << kMaxGrpcReqLen;
  }

  if (include_browse_set_items_) {
    auto browsed_items = context->GetLatestBrowsedItems(include_browse_set_item_count_);
    for (const auto item_key : browsed_items) {
      uint64 id = Util::GetId(item_key);
      uint64 key = GetFollowPageItemKey(id);
      item_id_to_key_map_.insert(std::make_pair(id, key));
      item_keys_.emplace_back(key);
      if (item_keys_.size() >= kMaxGrpcReqLen) {
        break;
      }
    }
  }
  int source_from_common_attrs = 0;
  for (const auto &attr_name : source_common_attrs_) {
    if (auto p = context->GetIntCommonAttr(attr_name)) {
      // 为了和关注页的 key 转化方式兼容
      uint64 key = GetFollowPageItemKey(*p);
      item_id_to_key_map_.insert(std::make_pair(*p, key));
      item_keys_.emplace_back(key);
    } else if (auto p = context->GetIntListCommonAttr(attr_name)) {
      for (const auto v : *p) {
        // 限制一下 browset 上限，否则服务稳定性有问题
        if (source_from_common_attrs > 1000 || item_keys_.size() >= kMaxGrpcReqLen) {
          break;
        }

        uint64 id = Util::GetId(v);
        // 为了和关注页的 key 转化方式兼容
        uint64 key = GetFollowPageItemKey(id);
        item_id_to_key_map_.insert(std::make_pair(id, key));
        item_keys_.emplace_back(key);
        source_from_common_attrs++;
      }
    } else {
      CL_LOG(WARNING) << "cannot find int/int_list common_attr: " << attr_name;
    }
  }

  std::string request_info = "kess_service: " + FLAGS_item_doc_grpc_service_name +
                             ", timeout_ms: " + base::IntToString(FLAGS_item_doc_cache_grpc_timeout_ms) +
                             ", item_num: " + base::IntToString(item_keys_.size()) +
                             ", source_common_attrs nums" + base::IntToString(source_from_common_attrs);
  CL_LOG(INFO) << "sending request to distributed index server, " << request_info;

  bool go_backup = false;
  auto final_callback = [this, context, item_id_to_key_map = std::move(item_id_to_key_map_),
                         friend_score_map = std::move(friend_score_map_),
                         intimate_feature_map = std::move(intimate_feature_map_),
                         friend_interact_map = std::move(friend_interact_map_),
                         author_sample_friends_map = std::move(author_sample_friends_map_),
                         item_vec = std::move(item_vec_)](ItemVector *item_infos) {
    if (item_infos != nullptr) {
      CL_LOG(INFO) << "received response from distributed index server"
                   << ", photo num: " << item_infos->size();

      std::vector<std::pair<uint64, double>> friend_score_pairs;
      std::unordered_map<uint64, double>::const_iterator friend_score_iter = friend_score_map.begin();
      for (; friend_score_iter != friend_score_map.end(); friend_score_iter++) {
        friend_score_pairs.push_back(std::make_pair(friend_score_iter->first, friend_score_iter->second));
      }
      sort(friend_score_pairs.begin(), friend_score_pairs.end(), CmpByValue());
      std::map<uint64, double> friend_score_position_map;
      double position_total = (double)friend_score_pairs.size();
      for (int i = 0; i < friend_score_pairs.size(); i++) {
        friend_score_position_map.insert(std::make_pair(friend_score_pairs[i].first, i/position_total));
      }

      for (auto &item : *item_infos) {
        if (!item.get()) {
          CL_LOG_EVERY_N(WARNING, 1000) << "item is null";
          continue;
        }
        // common leaf 和 FollowItem 中 pid 到 key sign 的转化方式不同，后者还加了 page << 48
        auto it = item_id_to_key_map.find(item->GetId());
        if (it == item_id_to_key_map.end()) {
          CL_LOG_EVERY_N(WARNING, 1000) << "lose item id to key mapping, skip attr filling for"
                                        << " item_id: " << item->GetId();
          continue;
        }
        uint64 item_key = Util::GenKeysign(ks::reco::RecoEnum::ITEM_TYPE_PHOTO, item->GetId());
        auto follow_item = dynamic_cast<ks::reco::follow::FollowItem *>(item.get());
        if (!follow_item) {
          CL_LOG_EVERY_N(WARNING, 1000) << "follow_item is null, item_id: " << item->GetId();
          continue;
        }
        FillPhotoInfoAttr(context, friend_score_map, friend_score_position_map, follow_item, item_key);
        FillPhotoInfoAttrV1(context, intimate_feature_map, friend_interact_map,
          author_sample_friends_map, follow_item, item_key);
      }
    } else {
      LOG(ERROR) << "fail to get response from distributed index server";
    }
  };

  // 注册 callback 函数
  RegisterAsyncCallback(
      context, photo_store_fetcher_.AsyncMultiGetRelexed(item_keys_, &item_vec_, false, "relation_photo"),
      std::move(final_callback));
}

void RelationPhotoDistributedIndexItemAttrEnricher::FillPhotoInfoAttr(
    MutableRecoContextInterface *context, const std::unordered_map<uint64, double> &friend_score_map,
     const std::map<uint64, double> &friend_score_position_map,
    ks::reco::follow::FollowItem *follow_item, uint64 item_key) {
  ks::reco::FollowItemMemoryInfo *follow_item_memory_info = follow_item->GetMemoryItem();
  ks::reco::FollowPageDynamicItemInfo *dynamic_info = follow_item_memory_info->GetFollowDynamicItemInfo();
  ks::reco::FollowPageItemInfo *follow_item_info = follow_item_info =
      follow_item_memory_info->GetFollowItemInfo();
  context->SetIntItemAttr(item_key, "photo_id", follow_item_memory_info->id);
  uint64 author_id = follow_item_info->author_id();
  context->SetIntItemAttr(item_key, "author_id", author_id);
  int32 duration_ms = follow_item_info->duration_ms();
  context->SetIntItemAttr(item_key, "duration_ms", duration_ms);
  int32 photo_type = follow_item_info->photo_type();
  context->SetIntItemAttr(item_key, "photo_type", photo_type);
  int32 fans_count = follow_item_info->fans_count();
  context->SetIntItemAttr(item_key, "fans_count", fans_count);
  int32 city_id = follow_item_info->city_id();
  context->SetIntItemAttr(item_key, "city_id", city_id);
  uint32 audit_hot_high_tag_level = follow_item_info->audit_hot_high_tag_level();
  context->SetIntItemAttr(item_key, "audit_hot_high_tag_level", audit_hot_high_tag_level);
  ks::reco::PhotoFriendVisibleInfo::PhotoStatus photo_status =
                                follow_item_info->photo_friend_visible_info().photo_status();
  context->SetIntItemAttr(item_key, "photo_status", GenPhotoStatus(photo_status));
  bool is_valid_photo = follow_item->IsValid();
  context->SetIntItemAttr(item_key, "is_valid_photo", is_valid_photo ? 1 : 0);
  int32 friends_count = follow_item_info->friends_count();
  context->SetIntItemAttr(item_key, "friends_count", friends_count);
  // add new feature about photo
  uint64 timestamp = follow_item_info->timestamp();
  int upload_diff_day = (base::GetTimestamp() - timestamp)/(1e6*3600*24);
  bool is_official_account = follow_item_info->is_official_account();
  uint64 magic_face_id = follow_item_info->magic_face_id();
  uint64 music_id = follow_item_info->music_id();
  double photo_click_upload_rate = follow_item_info->photo_click_upload_rate();
  double author_click_upload_rate = follow_item_info->author_click_upload_rate();
  uint64 photo_cover_cluser_id = follow_item_info->photo_cover_cluser_id();
  uint64 photo_level_kmeans_id = follow_item_info->photo_level_kmeans_id();
  uint64 photo_dnn_cluster_id = follow_item_info->photo_dnn_cluster_id();
  bool is_hot_forward_photo = follow_item_info->is_hot_forward_photo();
  bool is_hot_download_photo = follow_item_info->is_hot_download_photo();
//  int is_author_hetu_tag = 0, leaf_name_id = 0, category_name1_id = 0;
//  int category_name2_id = 0, category_name3_id = 0, category_name4_id = 0;
//  ks::reco::AuthorRelationInfo author_ds = follow_item_info->author_ds_info();
//  for (auto &category : author_ds.author_category_name()) {
//    is_author_hetu_tag = category.is_author_hetu_tag();
//    leaf_name_id = category.leaf_name_id();
//    category_name1_id = category.category_name1_id();
//    category_name2_id = category.category_name2_id();
//    category_name3_id = category.category_name3_id();
//    category_name4_id = category.category_name4_id();
//    break;
//  }
  ks::reco::PhotoHetuTagInfo photo_hetu_tag_info = follow_item_info->photo_hetu_tag_info();
  ks::reco::PhotoHetuTagInfo author_hetu_tag_info = follow_item_info->photo_author_hetu_tag_info();
  std::string photo_hetu_tag_info_str;
  photo_hetu_tag_info.SerializeToString(&photo_hetu_tag_info_str);
  context->SetStringItemAttr(item_key, "p_hetu_tag", photo_hetu_tag_info_str);
  std::string author_hetu_tag_info_str;
  author_hetu_tag_info.SerializeToString(&author_hetu_tag_info_str);
  context->SetStringItemAttr(item_key, "a_hetu_tag", author_hetu_tag_info_str);
  std::vector<int64> punish_ids;
  std::vector<int64> group_ids;
  for (const auto &follow_punish : follow_item_info->percent_punish()) {
    int64 punish_id = follow_punish.punish_id();
    int64 group_id = follow_punish.group_id();
    punish_ids.emplace_back(punish_id);
    group_ids.emplace_back(group_id);
  }
  context->SetIntListItemAttr(item_key, "punish_ids", std::vector<int64>(punish_ids));
  context->SetIntListItemAttr(item_key, "punish_group_ids", std::vector<int64>(group_ids));
  float sexy_score = dynamic_info->sexyscore();
  float author_low_score = dynamic_info->author_low_score();
  float author_high_score = dynamic_info->author_high_score();

  const auto photo_stat = dynamic_info->follow_stat();
  int fs_show_cnt = photo_stat.show_count();
  int fs_realshow_cnt = photo_stat.real_show_count();
  int fs_click_cnt = photo_stat.click_count();
  int fs_like_cnt = photo_stat.like_count();
  int fs_dislike_cnt = photo_stat.dislike_count();
  int fs_forward_cnt = photo_stat.forward_count();
  int fs_cmt_cnt = photo_stat.comment_count();
  context->SetIntItemAttr(item_key, "fs_show_cnt", fs_show_cnt);
  context->SetIntItemAttr(item_key, "fs_realshow_cnt", fs_realshow_cnt);
  context->SetIntItemAttr(item_key, "fs_click_cnt", fs_click_cnt);
  context->SetIntItemAttr(item_key, "fs_like_cnt", fs_like_cnt);
  context->SetIntItemAttr(item_key, "fs_dislike_cnt", fs_dislike_cnt);
  context->SetIntItemAttr(item_key, "fs_forward_cnt", fs_forward_cnt);
  context->SetIntItemAttr(item_key, "fs_cmt_cnt", fs_cmt_cnt);

  int photo_like_cnt = follow_item_info->photo_like_count();
  int photo_neg_cnt = follow_item_info->photo_neg_count();
  context->SetIntItemAttr(item_key, "photo_like_cnt", photo_like_cnt);
  context->SetIntItemAttr(item_key, "photo_neg_cnt", photo_neg_cnt);

  ks::reco::RecoEnum::DuplicatePunishType duplicate_punish_type = dynamic_info->duplicate_punish_type();
  ks::reco::PhotoQualityScoreGroup pqsg = follow_item_info->photo_quality_score_group();
  std::string photo_quality_score_group_str;
  pqsg.SerializeToString(&photo_quality_score_group_str);
  context->SetStringItemAttr(item_key, "p_quality_score", photo_quality_score_group_str);
  float true_face = pqsg.true_face(), clear_face = pqsg.clear_face();
  float face_area_ratio = pqsg.face_area_ratio(), common_poor_quality = pqsg.common_poor_quality();
  float meaningless_quality = pqsg.meaningless_quality(), content_pure_color = pqsg.content_pure_color();
  float photo_cover_pure_color = pqsg.photo_cover_pure_color();
  ks::reco::RelationAuthorStats ras = follow_item_info->relation_author_stats();
  float a_emp_pctr = ras.a_emp_pctr(), a_emp_pltr = ras.a_emp_pltr(), a_emp_pcmtr = ras.a_emp_pcmtr();
  float a_emp_pwtr = ras.a_emp_pwtr(), a_emp_pptr = ras.a_emp_pptr(), a_emp_psvr = ras.a_emp_psvr();
  float a_emp_phtr = ras.a_emp_phtr();
  std::string relation_author_stats_str;
  ras.SerializeToString(&relation_author_stats_str);
  context->SetStringItemAttr(item_key, "relation_author_stats", relation_author_stats_str);
//  base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(a_emp_pltr * 1000),
//                            "slide_relation.index", "a_emp_pxtr", "pltr", a_emp_pltr > 0 ? "1" : "0");
  float a_upload_7d = ras.a_upload_7d();
  auto& author_attr_map = ras.author_attrs();
  context->SetIntItemAttr(item_key, "upload_stamp", timestamp/1e3);
  context->SetIntItemAttr(item_key, "upload_diff_day", upload_diff_day);
  context->SetIntItemAttr(item_key, "is_official_account", is_official_account);
  context->SetIntItemAttr(item_key, "magic_face_id", magic_face_id);
  context->SetIntItemAttr(item_key, "music_id", music_id);
  context->SetDoubleItemAttr(item_key, "photo_click_upload_rate", photo_click_upload_rate);
  context->SetDoubleItemAttr(item_key, "author_click_upload_rate", author_click_upload_rate);
  context->SetIntItemAttr(item_key, "photo_cover_cluser_id", photo_cover_cluser_id);
  context->SetIntItemAttr(item_key, "photo_level_kmeans_id", photo_level_kmeans_id);
  context->SetIntItemAttr(item_key, "photo_dnn_cluster_id", photo_dnn_cluster_id);
  context->SetIntItemAttr(item_key, "is_hot_forward_photo", is_hot_forward_photo);
  context->SetIntItemAttr(item_key, "is_hot_download_photo", is_hot_download_photo);
//  context->SetIntItemAttr(item_key, "is_author_hetu_tag", is_author_hetu_tag);
//  context->SetIntItemAttr(item_key, "leaf_name_id", leaf_name_id);
//  context->SetIntItemAttr(item_key, "category_name1_id", category_name1_id);
//  context->SetIntItemAttr(item_key, "category_name2_id", category_name2_id);
//  context->SetIntItemAttr(item_key, "category_name3_id", category_name3_id);
//  context->SetIntItemAttr(item_key, "category_name4_id", category_name4_id);
  context->SetDoubleItemAttr(item_key, "sexy_score", sexy_score);
  context->SetDoubleItemAttr(item_key, "author_low_score", author_low_score);
  context->SetDoubleItemAttr(item_key, "author_high_score", author_high_score);
  context->SetDoubleItemAttr(item_key, "true_face", true_face);
  context->SetDoubleItemAttr(item_key, "clear_face", clear_face);
  context->SetDoubleItemAttr(item_key, "face_area_ratio", face_area_ratio);
  context->SetDoubleItemAttr(item_key, "common_poor_quality", common_poor_quality);
  context->SetDoubleItemAttr(item_key, "meaningless_quality", meaningless_quality);
  context->SetDoubleItemAttr(item_key, "content_pure_color", content_pure_color);
  context->SetDoubleItemAttr(item_key, "photo_cover_pure_color", photo_cover_pure_color);
  context->SetDoubleItemAttr(item_key, "a_emp_pctr", a_emp_pctr);
  context->SetDoubleItemAttr(item_key, "a_emp_pltr", a_emp_pltr);
  context->SetDoubleItemAttr(item_key, "a_emp_pcmtr", a_emp_pcmtr);
  context->SetDoubleItemAttr(item_key, "a_emp_pwtr", a_emp_pwtr);
  context->SetDoubleItemAttr(item_key, "a_emp_pptr", a_emp_pptr);
  context->SetDoubleItemAttr(item_key, "a_emp_psvr", a_emp_psvr);
  context->SetDoubleItemAttr(item_key, "a_emp_phtr", a_emp_phtr);
  context->SetDoubleItemAttr(item_key, "a_upload_7d", a_upload_7d);
  context->SetIntListItemAttr(item_key, "p_hetu_l1", std::vector<int64>(
    photo_hetu_tag_info.hetu_level_one().begin(), photo_hetu_tag_info.hetu_level_one().end()));
  context->SetIntListItemAttr(item_key, "p_hetu_l2", std::vector<int64>(
    photo_hetu_tag_info.hetu_level_two().begin(), photo_hetu_tag_info.hetu_level_two().end()));
  context->SetIntListItemAttr(item_key, "p_hetu_l3", std::vector<int64>(
    photo_hetu_tag_info.hetu_level_three().begin(), photo_hetu_tag_info.hetu_level_three().end()));
  context->SetIntListItemAttr(item_key, "p_hetu_l4", std::vector<int64>(
    photo_hetu_tag_info.hetu_level_four().begin(), photo_hetu_tag_info.hetu_level_four().end()));
  context->SetIntListItemAttr(item_key, "p_hetu_l5", std::vector<int64>(
    photo_hetu_tag_info.hetu_level_five().begin(), photo_hetu_tag_info.hetu_level_five().end()));
  context->SetIntListItemAttr(item_key, "a_hetu_l1", std::vector<int64>(
    author_hetu_tag_info.hetu_level_one().begin(), author_hetu_tag_info.hetu_level_one().end()));
  context->SetIntListItemAttr(item_key, "a_hetu_l2", std::vector<int64>(
    author_hetu_tag_info.hetu_level_two().begin(), author_hetu_tag_info.hetu_level_two().end()));
  context->SetIntListItemAttr(item_key, "a_hetu_l3", std::vector<int64>(
    author_hetu_tag_info.hetu_level_three().begin(), author_hetu_tag_info.hetu_level_three().end()));
  context->SetIntListItemAttr(item_key, "a_hetu_l4", std::vector<int64>(
    author_hetu_tag_info.hetu_level_four().begin(), author_hetu_tag_info.hetu_level_four().end()));
  context->SetIntListItemAttr(item_key, "a_hetu_l5", std::vector<int64>(
    author_hetu_tag_info.hetu_level_five().begin(), author_hetu_tag_info.hetu_level_five().end()));
  context->SetIntItemAttr(item_key, "picture_type", follow_item_info->picture_type());
  context->SetDoubleItemAttr(item_key, "mmu_quality_score", follow_item_info->mmu_quality_score());
  if (friend_score_map.find(author_id) != friend_score_map.end()) {
    context->SetDoubleItemAttr(item_key, "friend_score", friend_score_map.at(author_id));
  } else {
    context->SetDoubleItemAttr(item_key, "friend_score", 0.0);
  }
  if (friend_score_position_map.find(author_id) != friend_score_position_map.end()) {
    context->SetDoubleItemAttr(item_key, "friend_score_position", friend_score_position_map.at(author_id));
  } else {
    context->SetDoubleItemAttr(item_key, "friend_score_position", 1.0);
  }
  context->SetIntItemAttr(item_key, "duplicate_punish_type", GenDupType(duplicate_punish_type));
  // 24.01.12 索引存在的 channel & action
  // 1(HOT): 24(unfollow)、28(negative)
  // 3(FOLLOW): 0(list)、24(unfollow)、28(negative)
  // 5(PROFILE): 0(list)、12(comment)、24(unfollow)、28(negative)
  int hot_unfollow =
      GetItemCount(dynamic_info, action::UserActionType::kUnFollow, action::ChannelType::kHot);
  int hot_neg =
      GetItemCount(dynamic_info, action::UserActionType::kNegative, action::ChannelType::kHot);
  int follow_show = GetItemCount(dynamic_info, action::UserActionType::kList, action::ChannelType::kFollow);
  int follow_unfollow =
      GetItemCount(dynamic_info, action::UserActionType::kUnFollow, action::ChannelType::kFollow);
  int follow_neg =
      GetItemCount(dynamic_info, action::UserActionType::kNegative, action::ChannelType::kFollow);
  int profile_show =
      GetItemCount(dynamic_info, action::UserActionType::kList, action::ChannelType::kProfile);
  int profile_comment =
      GetItemCount(dynamic_info, action::UserActionType::kComment, action::ChannelType::kProfile);
  int profile_unfollow =
      GetItemCount(dynamic_info, action::UserActionType::kUnFollow, action::ChannelType::kProfile);
  int profile_neg =
      GetItemCount(dynamic_info, action::UserActionType::kNegative, action::ChannelType::kProfile);

  context->SetIntItemAttr(item_key, "hot_unfollow", hot_unfollow);
  context->SetIntItemAttr(item_key, "hot_neg", hot_neg);

  context->SetIntItemAttr(item_key, "follow_page_show_count", follow_show);
  context->SetIntItemAttr(item_key, "follow_unfollow", follow_unfollow);
  context->SetIntItemAttr(item_key, "follow_neg", follow_neg);

  context->SetIntItemAttr(item_key, "profile_show_count", profile_show);
  context->SetIntItemAttr(item_key, "profile_comment_count", profile_comment);
  context->SetIntItemAttr(item_key, "profile_unfollow", profile_unfollow);
  context->SetIntItemAttr(item_key, "profile_neg", profile_neg);

  context->SetIntItemAttr(item_key, "csm_to_crt_new_upload_photo_cnt_v2",
    follow_item_info->csm_to_crt_new_upload_photo_cnt_v2());
  context->SetIntItemAttr(item_key, "csm_to_crt_play_cnt_one_day",
    follow_item_info->csm_to_crt_play_cnt_one_day());
}

void RelationPhotoDistributedIndexItemAttrEnricher::FillPhotoInfoAttrV1(
    MutableRecoContextInterface *context,
    const std::unordered_map<uint64, AuthorIntimateElem> &intimate_feature_map,
    const std::unordered_map<int64, std::vector<int64>> &friend_interact_map,
    const std::unordered_map<int64, std::vector<int64>> &author_sample_friends_map,
    ks::reco::follow::FollowItem *follow_item, uint64 item_key) {
  ks::reco::FollowItemMemoryInfo *follow_item_memory_info = follow_item->GetMemoryItem();
  ks::reco::FollowPageItemInfo *follow_item_info = follow_item_info =
      follow_item_memory_info->GetFollowItemInfo();
  uint64 author_id = follow_item_info->author_id();
  // 设置好友亲密质量特征
  FillIntimateFeature(context, item_key, author_id, intimate_feature_map);

  if (friend_interact_map.find(author_id) != friend_interact_map.end()) {
    std::vector<int64> friend_ids = friend_interact_map.at(author_id);
    context->SetIntListItemAttr(item_key, "interact_friend_list", std::vector<int64>(friend_ids));
    context->SetIntItemAttr(item_key, "interact_friend_cnt", friend_ids.size());
  }
  if (author_sample_friends_map.find(author_id) != author_sample_friends_map.end()) {
    std::vector<int64> friend_ids = author_sample_friends_map.at(author_id);
    context->SetIntListItemAttr(item_key, "author_sample_friend_list", std::vector<int64>(friend_ids));
  }
  bool punished = follow_item_info->punished();
  context->SetIntItemAttr(item_key, "punished", punished ? 1 : 0);
  int32 author_life_cycle = follow_item_info->author_life_cycle();
  context->SetIntItemAttr(item_key, "author_life_cycle", author_life_cycle);

  int32 author_age = follow_item_info->author_age();
  context->SetIntItemAttr(item_key, "author_age", author_age);
  int32 author_gender = follow_item_info->author_gender();
  context->SetIntItemAttr(item_key, "author_gender", author_gender);

  std::string device_id = follow_item_info->device_id();
  context->SetStringItemAttr(item_key, "author_device_id", device_id);
  uint64 follow_count = follow_item_info->follow_count();
  context->SetIntItemAttr(item_key, "author_follow_count", follow_count);

  ks::reco::PhotoFriendVisibleInfo::FriendListType friend_list_type =
                              follow_item_info->photo_friend_visible_info().friend_list_type();
  context->SetIntItemAttr(item_key, "friend_list_type", GenFriendListType(friend_list_type));
  std::vector<int64> photo_partial_friend_visible_uid_list;
  for (auto uid : follow_item_info->photo_friend_visible_info().friend_id_list()) {
    photo_partial_friend_visible_uid_list.emplace_back(uid);
  }
  context->SetIntListItemAttr(item_key, "photo_partial_friend_visible_uid_list",
                              std::vector<int64>(photo_partial_friend_visible_uid_list));
  int32 plc_business_type = follow_item_info->plc_business_type();
  context->SetIntItemAttr(item_key, "plc_business_type", plc_business_type);
  int32 plc_category_type = follow_item_info->plc_category_type();
  context->SetIntItemAttr(item_key, "plc_category_type", plc_category_type);
  // 生产 冷启 屏蔽实验相关字段
  int32 photo_cold_start_garantee = follow_item_info->photo_cold_start_garantee();
  context->SetIntItemAttr(item_key, "photo_cold_start_garantee", photo_cold_start_garantee);
  std::string video_cold_start_author_tails_json = follow_item_info->video_cold_start_author_tails_json();
  context->SetStringItemAttr(item_key, "video_cold_start_author_tails_json",
      video_cold_start_author_tails_json);
  int32 coldstart_guarantee_value = follow_item_info->coldstart_guarantee_value();
  context->SetIntItemAttr(item_key, "coldstart_guarantee_value", coldstart_guarantee_value);
  int32 author_op_session_class = follow_item_info->author_op_session_class();
  context->SetIntItemAttr(item_key, "author_op_session_class", author_op_session_class);
  // 生产 作者尾号
  std::vector<int64> author_tail_int_index_keys;
  std::vector<int64> author_tail_int_index_values;
  const auto& pb_tail_map = follow_item_info->author_tail_int_index_map();
  std::unordered_map<int32, int32> author_tail_int_index_map(pb_tail_map.begin(), pb_tail_map.end());
  for (auto iter = author_tail_int_index_map.begin(); iter != author_tail_int_index_map.end(); ++iter) {
    author_tail_int_index_keys.emplace_back(iter->first);
    author_tail_int_index_values.emplace_back(iter->second);
  }
  context->SetIntListItemAttr(item_key, "author_tail_int_index_keys",
    std::vector<int64>(author_tail_int_index_keys));
  context->SetIntListItemAttr(item_key, "author_tail_int_index_values",
    std::vector<int64>(author_tail_int_index_values));
  // 生产 作品质量分
  double adjust_quality_score = follow_item_info->adjust_quality_score();
  context->SetDoubleItemAttr(item_key, "adjust_quality_score", adjust_quality_score);
  // 生产 认真创作分
  double adjust_create_score = follow_item_info->adjust_create_score();
  context->SetDoubleItemAttr(item_key, "adjust_create_score", adjust_create_score);
  // 生产 位标记
  int32 is_uploaded_by_keyperson = 0;
  if (follow_item_info->has_data_set_tags_bit() && (follow_item_info->data_set_tags_bit() & (1<<1))) {
    is_uploaded_by_keyperson = 1;  // 是 KP 上传的作品
  }
  context->SetIntItemAttr(item_key, "is_uploaded_by_keyperson", is_uploaded_by_keyperson);
  int32 is_uploaded_by_produce_core_author = 0;
  if (follow_item_info->has_data_set_tags_bit() && (follow_item_info->data_set_tags_bit() & (1<<2))) {
    is_uploaded_by_produce_core_author = 1;  // 是生产核心做功作者上传的作品
  }
  context->SetIntItemAttr(item_key, "is_uploaded_by_produce_core_author", is_uploaded_by_produce_core_author);
  int32 is_coin_photo = 0;
  if (follow_item_info->has_data_set_tags_bit() && (follow_item_info->data_set_tags_bit() & (1<<5))) {
    is_coin_photo = 1;  // 左移 5 位 是金币作品
  }
  context->SetIntItemAttr(item_key, "is_coin_photo", is_coin_photo);
  int32 upload_by_causal = 0;
  if (follow_item_info->has_data_set_tags_bit() && (follow_item_info->data_set_tags_bit() & (1<<6))) {
    upload_by_causal = 1;  // 左移 6 位 是流量敏感作者上传的作品
  }
  context->SetIntItemAttr(item_key, "upload_by_causal", upload_by_causal);
  int32 is_ugc = 0;
  if (follow_item_info->has_data_set_tags_bit() && (follow_item_info->data_set_tags_bit() & (1<<7))) {
    is_ugc = 1;  // 左移 7 位 是 ugc 作者上传的作品
  }
  context->SetIntItemAttr(item_key, "is_ugc", is_ugc);
}

double RelationPhotoDistributedIndexItemAttrEnricher::GetwilsonScore(int pros, int n) {
    if (pros > n) {
        return 0.0;
    }
    double p = pros * 1.0 / n;
    double z = 1.96;
    return (p + (z * z) / (2 * n) - z * sqrt(p * (1 - p) / n + (z * z) / (4 * n * n))) / (1 + z * z / n);
}

int RelationPhotoDistributedIndexItemAttrEnricher::GetItemCount(
    const ks::reco::FollowPageDynamicItemInfo *dynamic_info, const action::UserActionType &action_type,
    const action::ChannelType &channel_type) {
//  base::perfutil::PerfUtilWrapper::CountLogStash(1, "slide_relation.index", "GetItemCount", "start");
  if (dynamic_info == nullptr) {
    return 0;
  }
  auto &item_counter = dynamic_info->count();
  if (item_counter.type_size() != item_counter.value_size()) {
    return 0;
  }
  int action_obj = static_cast<int>(action_type);
  int channel_obj = static_cast<int>(channel_type);
//  base::perfutil::PerfUtilWrapper::CountLogStash(1, "slide_relation.index", "GetItemCount",
//    "obj", std::to_string(action_obj), std::to_string(channel_obj));
  for (int i = 0; i < item_counter.type_size(); ++i) {
    int channel = CHANNEL(item_counter.type(i));
    int action = ACTION(item_counter.type(i));
//    base::perfutil::PerfUtilWrapper::CountLogStash(1, "slide_relation.index", "GetItemCount",
//      "index", std::to_string(action), std::to_string(channel));
    if ((action::ChannelType)channel == channel_type && (action::UserActionType)action == action_type) {
//      base::perfutil::PerfUtilWrapper::CountLogStash(1, "slide_relation.index", "GetItemCount",
//        "suss", std::to_string(action), std::to_string(channel));
      return item_counter.value(i);
    }
  }
  return 0;
}

uint64 RelationPhotoDistributedIndexItemAttrEnricher::GetFollowPageItemKey(uint64 id) {
  // 和关注页索引中，photo id 到 item key 的映射方式对齐, 映射方式: 56~64 位表示 item 类型
  // 48:56 位表示 page， 0:48 位表示 photo id
  return ((uint64)ks::reco::RecoEnum::ITEM_TYPE_PHOTO << 56) |
         ((uint64)ks::reco::RecoEnum::PAGE_TYPE_FOLLOW << 48) | id;
}

void RelationPhotoDistributedIndexItemAttrEnricher::GetFriendScoreMap(MutableRecoContextInterface *context) {
  friend_score_map_.clear();
  auto author_ptr = context->GetIntListCommonAttr("intimacy_authors");
  auto friend_score_ptr = context->GetDoubleListCommonAttr("intimacy_authors_score");

  if (author_ptr && friend_score_ptr &&
      friend_score_ptr->size() == author_ptr->size()) {
    for (int i = 0; i < author_ptr->size(); i++) {
      uint64 aid = author_ptr->at(i);
      double friend_score = friend_score_ptr->at(i);
      friend_score_map_.insert(std::make_pair(aid, friend_score));
    }
  }
}

void RelationPhotoDistributedIndexItemAttrEnricher::GetIntimateFeatureMap(
    MutableRecoContextInterface *context) {
  intimate_feature_map_.clear();
  auto action_ptr = context->GetIntListCommonAttr("intimate_actions");     // aid1,idx1,idx2,aid2,idx2
  auto score_ptr = context->GetDoubleListCommonAttr("intimate_scores");    //     ,val1,val2,    ,val2
  if (action_ptr && score_ptr) {
    AuthorIntimateElem elem;
    for (int i = 0, j = 0; i < action_ptr->size(); ++i) {
      uint64 aid_or_idx = action_ptr->at(i);
      if (aid_or_idx > 100) {  // 特征数 < 100
        if (elem.author_id > 0) {
          CL_LOG_EVERY_N(INFO, 10000) << "intimate_feature_debug, aid: " << elem.author_id
            << ", play: " << elem.play << ", score:" << elem.intimate_percentile_score;
          intimate_feature_map_.insert(std::make_pair(elem.author_id, elem));
          elem = {};
          elem.author_id = aid_or_idx;
        } else {
          elem.author_id = aid_or_idx;
        }
      } else if (j < score_ptr->size()) {
        double score = score_ptr->at(j++);
        if (aid_or_idx == 0) {
          elem.msg = score;
        } else if (aid_or_idx == 1) {
          elem.like = score;
        } else if (aid_or_idx == 2) {
          elem.cmt = score;
        } else if (aid_or_idx == 3) {
          elem.cmt_like = score;
        } else if (aid_or_idx == 4) {
          elem.pub_at = score;
        } else if (aid_or_idx == 5) {
          elem.cmt_at = score;
        } else if (aid_or_idx == 6) {
          elem.play = score;
        } else if (aid_or_idx == 7) {
          elem.itr_play = score;
        } else if (aid_or_idx == 8) {
          elem.val_play = score;
        } else if (aid_or_idx == 9) {
          elem.val_itr_play = score;
        } else if (aid_or_idx == 10) {
          elem.enter_prf = score;
        } else if (aid_or_idx == 11) {
          elem.dur_p = score;
        } else if (aid_or_idx == 12) {
          elem.dur_l = score;
        } else if (aid_or_idx == 13) {
          elem.poke = score;
        } else if (aid_or_idx == 14) {
          elem.amt_l = score;
        } else if (aid_or_idx == 15) {
          elem.favor = score;
        } else if (aid_or_idx == 16) {
          elem.search = score;
        } else if (aid_or_idx == 17) {
          elem.intimate = score;
        } else if (aid_or_idx == 18) {
          elem.alias = score;
        } else if (aid_or_idx == 19) {
          elem.privacy = score;
        } else if (aid_or_idx == 20) {
          elem.intimate_score = score;
        } else if (aid_or_idx == 21) {
          elem.intimate_percentile_score = score;
        }
      }
    }
    if (context->GetIntCommonAttr("enable_fill_ua_intimate_fix").value_or(0) > 0) {
      if (elem.author_id > 0) {
        intimate_feature_map_.insert(std::make_pair(elem.author_id, elem));
      }
    }
  }
}

void RelationPhotoDistributedIndexItemAttrEnricher::GetFriendInteractFeatureMap(
    MutableRecoContextInterface *context) {
  friend_interact_map_.clear();
  author_sample_friends_map_.clear();
  // aid1,fid1,fid1,-1,aid2,fid1,-1
  auto feature_ptr = context->GetIntListCommonAttr("friend_interact_authors");
  if (feature_ptr) {
    std::vector<int64> friend_ids;
    int author_flag = 1;
    int64 author_id;
    for (int i = 0; i < feature_ptr->size(); ++i) {
      int64 aid = feature_ptr->at(i);
      if (aid > 0) {
        if (author_flag == 1) {
          author_id = aid;
          author_flag = 0;
        } else {
          friend_ids.push_back(aid);
        }
      } else {
        friend_interact_map_.insert(std::make_pair(author_id, friend_ids));
        author_flag = 1;
        friend_ids.clear();
      }
    }
  }
  feature_ptr = context->GetIntListCommonAttr("author_sample_friends");
  if (feature_ptr) {
    std::vector<int64> friend_ids;
    int author_flag = 1;
    int64 author_id;
    for (int i = 0; i < feature_ptr->size(); ++i) {
      int64 aid = feature_ptr->at(i);
      if (aid > 0) {
        if (author_flag == 1) {
          author_id = aid;
          author_flag = 0;
        } else {
          friend_ids.push_back(aid);
        }
      } else {
        author_sample_friends_map_.insert(std::make_pair(author_id, friend_ids));
        author_flag = 1;
        friend_ids.clear();
      }
    }
  }
}

int32 RelationPhotoDistributedIndexItemAttrEnricher::GenPhotoStatus(
                                ks::reco::PhotoFriendVisibleInfo::PhotoStatus photo_status) {
  if (photo_status == ks::reco::PhotoFriendVisibleInfo::PUBLIC) {
    return 0;
  } else if (photo_status == ks::reco::PhotoFriendVisibleInfo::PARTIAL_VISIBLE) {
    return 1;
  } else if (photo_status == ks::reco::PhotoFriendVisibleInfo::ONLY_SELF) {
    return 2;
  }
  return 3;
}

int32 RelationPhotoDistributedIndexItemAttrEnricher::GenFriendListType(
                                ks::reco::PhotoFriendVisibleInfo::FriendListType friend_list_type) {
  if (friend_list_type == ks::reco::PhotoFriendVisibleInfo::DEFAULT) {
    return 0;
  } else if (friend_list_type == ks::reco::PhotoFriendVisibleInfo::WHITE_LIST_TYPE) {
    return 1;
  } else if (friend_list_type == ks::reco::PhotoFriendVisibleInfo::BLACK_LIST_TYPE) {
    return 2;
  }
  return 3;
}

int32 RelationPhotoDistributedIndexItemAttrEnricher::GenDupType(
                                ks::reco::RecoEnum::DuplicatePunishType duplicate_punish_type) {
  if (duplicate_punish_type == ks::reco::RecoEnum::UNKNOWN_DUPLICATE_PUNISH_TYPE) {
    return 0;
  } else if (duplicate_punish_type == ks::reco::RecoEnum::NON_DUPLICATE) {
    return 1;
  } else if (duplicate_punish_type == ks::reco::RecoEnum::DUPLICATE_ONLY) {
    return 2;
  } else if (duplicate_punish_type == ks::reco::RecoEnum::DUPLICATE_OTHER_MIXTURE) {
    return 3;
  }
  return 0;
}

void RelationPhotoDistributedIndexItemAttrEnricher::FillIntimateFeature(
    MutableRecoContextInterface *context, uint64 item_key, uint64 author_id,
    const std::unordered_map<uint64, AuthorIntimateElem> &intimate_feature_map) {
  if (intimate_feature_map.find(author_id) != intimate_feature_map.end()) {
    auto elem = intimate_feature_map.at(author_id);
    context->SetIntItemAttr(item_key, "ua_intimate_msg", elem.msg);
    context->SetIntItemAttr(item_key, "ua_intimate_like", elem.like);
    context->SetIntItemAttr(item_key, "ua_intimate_cmt", elem.cmt);
    context->SetIntItemAttr(item_key, "ua_intimate_cmt_like", elem.cmt_like);
    context->SetIntItemAttr(item_key, "ua_intimate_pub_at", elem.pub_at);
    context->SetIntItemAttr(item_key, "ua_intimate_cmt_at", elem.cmt_at);
    context->SetIntItemAttr(item_key, "ua_intimate_play", elem.play);
    context->SetIntItemAttr(item_key, "ua_intimate_itr_play", elem.itr_play);
    context->SetIntItemAttr(item_key, "ua_intimate_val_play", elem.val_play);
    context->SetIntItemAttr(item_key, "ua_intimate_val_itr_play", elem.val_itr_play);
    context->SetIntItemAttr(item_key, "ua_intimate_enter_prf", elem.enter_prf);
    context->SetIntItemAttr(item_key, "ua_intimate_dur_p", elem.dur_p);
    context->SetIntItemAttr(item_key, "ua_intimate_dur_l", elem.dur_l);
    context->SetIntItemAttr(item_key, "ua_intimate_poke", elem.poke);
    context->SetIntItemAttr(item_key, "ua_intimate_amt_l", elem.amt_l);
    context->SetIntItemAttr(item_key, "ua_intimate_favor", elem.favor);
    context->SetIntItemAttr(item_key, "ua_intimate_search", elem.search);
    context->SetIntItemAttr(item_key, "ua_intimate_intimate", elem.intimate);
    context->SetIntItemAttr(item_key, "ua_intimate_alias", elem.alias);
    context->SetIntItemAttr(item_key, "ua_intimate_privacy", elem.privacy);
    context->SetDoubleItemAttr(item_key, "ua_intimate_score", elem.intimate_score);
    context->SetDoubleItemAttr(item_key, "ua_intimate_percentile_score", elem.intimate_percentile_score);
    if (elem.play > 0) {
      context->SetDoubleItemAttr(item_key, "ua_intimate_itr_rate", elem.itr_play / elem.play);
      context->SetDoubleItemAttr(item_key, "ua_intimate_val_rate", elem.val_play / elem.play);
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, RelationPhotoDistributedIndexItemAttrEnricher,
                 RelationPhotoDistributedIndexItemAttrEnricher)

}  // namespace platform
}  // namespace ks

//  action::ChannelType slide_page = action::ChannelType::kBottomSelection;
//  int slide_show = GetItemCount(dynamic_info, action::UserActionType::kList, slide_page);
//  int slide_click = GetItemCount(dynamic_info, action::UserActionType::kView, slide_page);
//  int slide_in_profile = GetItemCount(dynamic_info, action::UserActionType::kViewProfile, slide_page);
//  int slide_realshow = GetItemCount(dynamic_info, action::UserActionType::kRealShow, slide_page);
//  int slide_like = GetItemCount(dynamic_info, action::UserActionType::kLike, slide_page);
//  int slide_follow = GetItemCount(dynamic_info, action::UserActionType::kFollow, slide_page);
//  int slide_comment = GetItemCount(dynamic_info, action::UserActionType::kComment, slide_page);
//  int slide_list_comment = GetItemCount(dynamic_info, action::UserActionType::kListComment, slide_page);
//  int slide_forward = GetItemCount(dynamic_info, action::UserActionType::kForward, slide_page);
//  int slide_longview = GetItemCount(dynamic_info, action::UserActionType::kLongView, slide_page);
//  int slide_shortview = GetItemCount(dynamic_info, action::UserActionType::kShortView, slide_page);
//  int slide_viewtime = GetItemCount(dynamic_info, action::UserActionType::kViewTime, slide_page);
//  int slide_unfollow = GetItemCount(dynamic_info, action::UserActionType::kUnFollow, slide_page);
//  int slide_unlike = GetItemCount(dynamic_info, action::UserActionType::kCancelLike, slide_page);
//  int slide_neg = GetItemCount(dynamic_info, action::UserActionType::kNegative, slide_page);
//  int hot_show = GetItemCount(dynamic_info, action::UserActionType::kList, action::ChannelType::kHot);
//  int hot_click = GetItemCount(dynamic_info, action::UserActionType::kView, action::ChannelType::kHot);
//  int hot_in_profile =
//      GetItemCount(dynamic_info, action::UserActionType::kViewProfile, action::ChannelType::kHot);
//  int hot_realshow =
//        GetItemCount(dynamic_info, action::UserActionType::kRealShow, action::ChannelType::kHot);
//  int hot_like = GetItemCount(dynamic_info, action::UserActionType::kLike, action::ChannelType::kHot);
//  int hot_follow = GetItemCount(dynamic_info, action::UserActionType::kFollow, action::ChannelType::kHot);
//  int hot_comment = GetItemCount(dynamic_info, action::UserActionType::kComment, action::ChannelType::kHot);
//  int hot_list_comment =
//      GetItemCount(dynamic_info, action::UserActionType::kListComment, action::ChannelType::kHot);
//  int hot_forward = GetItemCount(dynamic_info, action::UserActionType::kForward, action::ChannelType::kHot);
//  int hot_longview =
//        GetItemCount(dynamic_info, action::UserActionType::kLongView, action::ChannelType::kHot);
//  int hot_shortview =
//      GetItemCount(dynamic_info, action::UserActionType::kShortView, action::ChannelType::kHot);
//  int hot_viewtime =
//      GetItemCount(dynamic_info, action::UserActionType::kViewTime, action::ChannelType::kHot);
//  int hot_unlike =
//      GetItemCount(dynamic_info, action::UserActionType::kCancelLike, action::ChannelType::kHot);
//  int follow_click =
//        GetItemCount(dynamic_info, action::UserActionType::kView, action::ChannelType::kFollow);
//  int follow_in_profile =
//      GetItemCount(dynamic_info, action::UserActionType::kViewProfile, action::ChannelType::kFollow);
//  int follow_realshow =
//      GetItemCount(dynamic_info, action::UserActionType::kRealShow, action::ChannelType::kFollow);
//  int follow_like = GetItemCount(dynamic_info, action::UserActionType::kLike, action::ChannelType::kFollow);
//  int follow_comment =
//      GetItemCount(dynamic_info, action::UserActionType::kComment, action::ChannelType::kFollow);
//  int follow_list_comment =
//      GetItemCount(dynamic_info, action::UserActionType::kListComment, action::ChannelType::kFollow);
//  int follow_forward =
//      GetItemCount(dynamic_info, action::UserActionType::kForward, action::ChannelType::kFollow);
//  int follow_longview =
//      GetItemCount(dynamic_info, action::UserActionType::kLongView, action::ChannelType::kFollow);
//  int follow_shortview =
//      GetItemCount(dynamic_info, action::UserActionType::kShortView, action::ChannelType::kFollow);
//  int follow_viewtime =
//      GetItemCount(dynamic_info, action::UserActionType::kViewTime, action::ChannelType::kFollow);
//  int follow_unlike =
//      GetItemCount(dynamic_info, action::UserActionType::kCancelLike, action::ChannelType::kFollow);
//  int sl_hot_show = GetItemCount(dynamic_info, action::UserActionType::kList, action::ChannelType::kSLHot);
//  int sl_hot_click =
//        GetItemCount(dynamic_info, action::UserActionType::kView, action::ChannelType::kSLHot);
//  int sl_hot_in_profile =
//      GetItemCount(dynamic_info, action::UserActionType::kViewProfile, action::ChannelType::kSLHot);
//  int sl_hot_realshow =
//      GetItemCount(dynamic_info, action::UserActionType::kRealShow, action::ChannelType::kSLHot);
//  int sl_hot_like = GetItemCount(dynamic_info, action::UserActionType::kLike, action::ChannelType::kSLHot);
//  int sl_hot_follow =
//      GetItemCount(dynamic_info, action::UserActionType::kFollow, action::ChannelType::kSLHot);
//  int sl_hot_comment =
//      GetItemCount(dynamic_info, action::UserActionType::kComment, action::ChannelType::kSLHot);
//  int sl_hot_list_comment =
//      GetItemCount(dynamic_info, action::UserActionType::kListComment, action::ChannelType::kSLHot);
//  int sl_hot_forward =
//      GetItemCount(dynamic_info, action::UserActionType::kForward, action::ChannelType::kSLHot);
//  int sl_hot_longview =
//      GetItemCount(dynamic_info, action::UserActionType::kLongView, action::ChannelType::kSLHot);
//  int sl_hot_shortview =
//      GetItemCount(dynamic_info, action::UserActionType::kShortView, action::ChannelType::kSLHot);
//  int sl_hot_viewtime =
//      GetItemCount(dynamic_info, action::UserActionType::kViewTime, action::ChannelType::kSLHot);
//  int sl_hot_unfollow =
//      GetItemCount(dynamic_info, action::UserActionType::kUnFollow, action::ChannelType::kSLHot);
//  int sl_hot_unlike =
//      GetItemCount(dynamic_info, action::UserActionType::kCancelLike, action::ChannelType::kSLHot);
//  int sl_hot_neg =
//      GetItemCount(dynamic_info, action::UserActionType::kNegative, action::ChannelType::kSLHot);
//  int sl_follow_show =
//      GetItemCount(dynamic_info, action::UserActionType::kList, action::ChannelType::kSLFollow);
//  int sl_follow_click =
//      GetItemCount(dynamic_info, action::UserActionType::kView, action::ChannelType::kSLFollow);
//  int sl_follow_in_profile =
//      GetItemCount(dynamic_info, action::UserActionType::kViewProfile, action::ChannelType::kSLFollow);
//  int sl_follow_realshow =
//      GetItemCount(dynamic_info, action::UserActionType::kRealShow, action::ChannelType::kSLFollow);
//  int sl_follow_like =
//      GetItemCount(dynamic_info, action::UserActionType::kLike, action::ChannelType::kSLFollow);
//  int sl_follow_comment =
//      GetItemCount(dynamic_info, action::UserActionType::kComment, action::ChannelType::kSLFollow);
//  int sl_follow_list_comment =
//      GetItemCount(dynamic_info, action::UserActionType::kListComment, action::ChannelType::kSLFollow);
//  int sl_follow_forward =
//      GetItemCount(dynamic_info, action::UserActionType::kForward, action::ChannelType::kSLFollow);
//  int sl_follow_longview =
//      GetItemCount(dynamic_info, action::UserActionType::kLongView, action::ChannelType::kSLFollow);
//  int sl_follow_shortview =
//      GetItemCount(dynamic_info, action::UserActionType::kShortView, action::ChannelType::kSLFollow);
//  int sl_follow_viewtime =
//      GetItemCount(dynamic_info, action::UserActionType::kViewTime, action::ChannelType::kSLFollow);
//  int sl_follow_unfollow =
//      GetItemCount(dynamic_info, action::UserActionType::kUnFollow, action::ChannelType::kSLFollow);
//  int sl_follow_unlike =
//      GetItemCount(dynamic_info, action::UserActionType::kCancelLike, action::ChannelType::kSLFollow);
//  int sl_follow_neg =
//      GetItemCount(dynamic_info, action::UserActionType::kNegative, action::ChannelType::kSLFollow);
//  int bl_hot_show =
//        GetItemCount(dynamic_info, action::UserActionType::kList, action::ChannelType::kBLHot);
//  int bl_hot_click =
//        GetItemCount(dynamic_info, action::UserActionType::kView, action::ChannelType::kBLHot);
//  int bl_hot_in_profile =
//      GetItemCount(dynamic_info, action::UserActionType::kViewProfile, action::ChannelType::kBLHot);
//  int bl_hot_realshow =
//      GetItemCount(dynamic_info, action::UserActionType::kRealShow, action::ChannelType::kBLHot);
//  int bl_hot_like =
//      GetItemCount(dynamic_info, action::UserActionType::kLike, action::ChannelType::kBLHot);
//  int bl_hot_follow =
//      GetItemCount(dynamic_info, action::UserActionType::kFollow, action::ChannelType::kBLHot);
//  int bl_hot_comment =
//      GetItemCount(dynamic_info, action::UserActionType::kComment, action::ChannelType::kBLHot);
//  int bl_hot_list_comment =
//      GetItemCount(dynamic_info, action::UserActionType::kListComment, action::ChannelType::kBLHot);
//  int bl_hot_forward =
//      GetItemCount(dynamic_info, action::UserActionType::kForward, action::ChannelType::kBLHot);
//  int bl_hot_longview =
//      GetItemCount(dynamic_info, action::UserActionType::kLongView, action::ChannelType::kBLHot);
//  int bl_hot_shortview =
//      GetItemCount(dynamic_info, action::UserActionType::kShortView, action::ChannelType::kBLHot);
//  int bl_hot_viewtime =
//      GetItemCount(dynamic_info, action::UserActionType::kViewTime, action::ChannelType::kBLHot);
//  int bl_hot_unfollow =
//      GetItemCount(dynamic_info, action::UserActionType::kUnFollow, action::ChannelType::kBLHot);
//  int bl_hot_unlike =
//      GetItemCount(dynamic_info, action::UserActionType::kCancelLike, action::ChannelType::kBLHot);
//  int bl_hot_neg =
//      GetItemCount(dynamic_info, action::UserActionType::kNegative, action::ChannelType::kBLHot);
//  int bl_follow_show =
//      GetItemCount(dynamic_info, action::UserActionType::kList, action::ChannelType::kBLFollow);
//  int bl_follow_click =
//      GetItemCount(dynamic_info, action::UserActionType::kView, action::ChannelType::kBLFollow);
//  int bl_follow_in_profile =
//      GetItemCount(dynamic_info, action::UserActionType::kViewProfile, action::ChannelType::kBLFollow);
//  int bl_follow_realshow =
//      GetItemCount(dynamic_info, action::UserActionType::kRealShow, action::ChannelType::kBLFollow);
//  int bl_follow_like =
//      GetItemCount(dynamic_info, action::UserActionType::kLike, action::ChannelType::kBLFollow);
//  int bl_follow_comment =
//      GetItemCount(dynamic_info, action::UserActionType::kComment, action::ChannelType::kBLFollow);
//  int bl_follow_list_comment =
//      GetItemCount(dynamic_info, action::UserActionType::kListComment, action::ChannelType::kBLFollow);
//  int bl_follow_forward =
//      GetItemCount(dynamic_info, action::UserActionType::kForward, action::ChannelType::kBLFollow);
//  int bl_follow_longview =
//      GetItemCount(dynamic_info, action::UserActionType::kLongView, action::ChannelType::kBLFollow);
//  int bl_follow_shortview =
//      GetItemCount(dynamic_info, action::UserActionType::kShortView, action::ChannelType::kBLFollow);
//  int bl_follow_viewtime =
//      GetItemCount(dynamic_info, action::UserActionType::kViewTime, action::ChannelType::kBLFollow);
//  int bl_follow_unfollow =
//      GetItemCount(dynamic_info, action::UserActionType::kUnFollow, action::ChannelType::kBLFollow);
//  int bl_follow_unlike =
//      GetItemCount(dynamic_info, action::UserActionType::kCancelLike, action::ChannelType::kBLFollow);
//  int bl_follow_neg =
//      GetItemCount(dynamic_info, action::UserActionType::kNegative, action::ChannelType::kBLFollow);
//  int profile_click =
//      GetItemCount(dynamic_info, action::UserActionType::kView, action::ChannelType::kProfile);
//  int profile_in_profile =
//      GetItemCount(dynamic_info, action::UserActionType::kViewProfile, action::ChannelType::kProfile);
//  int profile_realshow =
//      GetItemCount(dynamic_info, action::UserActionType::kRealShow, action::ChannelType::kProfile);
//  int profile_like =
//      GetItemCount(dynamic_info, action::UserActionType::kLike, action::ChannelType::kProfile);
//  int profile_follow =
//      GetItemCount(dynamic_info, action::UserActionType::kFollow, action::ChannelType::kProfile);
//  int profile_list_comment =
//      GetItemCount(dynamic_info, action::UserActionType::kListComment, action::ChannelType::kProfile);
//  int profile_forward =
//      GetItemCount(dynamic_info, action::UserActionType::kForward, action::ChannelType::kProfile);
//  int profile_longview =
//      GetItemCount(dynamic_info, action::UserActionType::kLongView, action::ChannelType::kProfile);
//  int profile_shortview =
//      GetItemCount(dynamic_info, action::UserActionType::kShortView, action::ChannelType::kProfile);
//  int profile_viewtime =
//      GetItemCount(dynamic_info, action::UserActionType::kViewTime, action::ChannelType::kProfile);
//  int profile_unlike =
//      GetItemCount(dynamic_info, action::UserActionType::kCancelLike, action::ChannelType::kProfile);
//  context->SetIntItemAttr(item_key, "hot_show_count", hot_show);
//  context->SetIntItemAttr(item_key, "hot_click_count", hot_click);
//  context->SetIntItemAttr(item_key, "hot_like_count", hot_like);
//  context->SetIntItemAttr(item_key, "hot_comment_count", hot_comment);
//  context->SetIntItemAttr(item_key, "follow_page_click_count", follow_click);
//  context->SetIntItemAttr(item_key, "follow_page_like_count", follow_like);
//  context->SetIntItemAttr(item_key, "follow_page_comment_count", follow_comment);
//  context->SetIntItemAttr(item_key, "hot_in_profile", hot_in_profile);
//  context->SetIntItemAttr(item_key, "hot_realshow", hot_realshow);
//  context->SetIntItemAttr(item_key, "hot_follow", hot_follow);
//  context->SetIntItemAttr(item_key, "hot_list_comment", hot_list_comment);
//  context->SetIntItemAttr(item_key, "hot_forward", hot_forward);
//  context->SetIntItemAttr(item_key, "hot_longview", hot_longview);
//  context->SetIntItemAttr(item_key, "hot_shortview", hot_shortview);
//  context->SetIntItemAttr(item_key, "hot_viewtime", hot_viewtime);
//  context->SetIntItemAttr(item_key, "hot_unlike", hot_unlike);
//  context->SetIntItemAttr(item_key, "follow_in_profile", follow_in_profile);
//  context->SetIntItemAttr(item_key, "follow_realshow", follow_realshow);
//  context->SetIntItemAttr(item_key, "follow_list_comment", follow_list_comment);
//  context->SetIntItemAttr(item_key, "follow_forward", follow_forward);
//  context->SetIntItemAttr(item_key, "follow_longview", follow_longview);
//  context->SetIntItemAttr(item_key, "follow_shortview", follow_shortview);
//  context->SetIntItemAttr(item_key, "follow_viewtime", follow_viewtime);
//  context->SetIntItemAttr(item_key, "follow_unlike", follow_unlike);
//  context->SetIntItemAttr(item_key, "sl_hot_in_profile", sl_hot_in_profile);
//  context->SetIntItemAttr(item_key, "sl_hot_realshow", sl_hot_realshow);
//  context->SetIntItemAttr(item_key, "sl_hot_follow", sl_hot_follow);
//  context->SetIntItemAttr(item_key, "sl_hot_list_comment", sl_hot_list_comment);
//  context->SetIntItemAttr(item_key, "sl_hot_forward", sl_hot_forward);
//  context->SetIntItemAttr(item_key, "sl_hot_longview", sl_hot_longview);
//  context->SetIntItemAttr(item_key, "sl_hot_shortview", sl_hot_shortview);
//  context->SetIntItemAttr(item_key, "sl_hot_viewtime", sl_hot_viewtime);
//  context->SetIntItemAttr(item_key, "sl_hot_unfollow", sl_hot_unfollow);
//  context->SetIntItemAttr(item_key, "sl_hot_unlike", sl_hot_unlike);
//  context->SetIntItemAttr(item_key, "sl_hot_neg", sl_hot_neg);
//  context->SetIntItemAttr(item_key, "sl_follow_in_profile", sl_follow_in_profile);
//  context->SetIntItemAttr(item_key, "sl_follow_realshow", sl_follow_realshow);
//  context->SetIntItemAttr(item_key, "sl_follow_list_comment", sl_follow_list_comment);
//  context->SetIntItemAttr(item_key, "sl_follow_forward", sl_follow_forward);
//  context->SetIntItemAttr(item_key, "sl_follow_longview", sl_follow_longview);
//  context->SetIntItemAttr(item_key, "sl_follow_shortview", sl_follow_shortview);
//  context->SetIntItemAttr(item_key, "sl_follow_viewtime", sl_follow_viewtime);
//  context->SetIntItemAttr(item_key, "sl_follow_unfollow", sl_follow_unfollow);
//  context->SetIntItemAttr(item_key, "sl_follow_unlike", sl_follow_unlike);
//  context->SetIntItemAttr(item_key, "sl_follow_neg", sl_follow_neg);
//  context->SetIntItemAttr(item_key, "bl_hot_in_profile", bl_hot_in_profile);
//  context->SetIntItemAttr(item_key, "bl_hot_realshow", bl_hot_realshow);
//  context->SetIntItemAttr(item_key, "bl_hot_follow", bl_hot_follow);
//  context->SetIntItemAttr(item_key, "bl_hot_list_comment", bl_hot_list_comment);
//  context->SetIntItemAttr(item_key, "bl_hot_forward", bl_hot_forward);
//  context->SetIntItemAttr(item_key, "bl_hot_longview", bl_hot_longview);
//  context->SetIntItemAttr(item_key, "bl_hot_shortview", bl_hot_shortview);
//  context->SetIntItemAttr(item_key, "bl_hot_viewtime", bl_hot_viewtime);
//  context->SetIntItemAttr(item_key, "bl_hot_unfollow", bl_hot_unfollow);
//  context->SetIntItemAttr(item_key, "bl_hot_unlike", bl_hot_unlike);
//  context->SetIntItemAttr(item_key, "bl_hot_neg", bl_hot_neg);
//  context->SetIntItemAttr(item_key, "bl_follow_in_profile", bl_follow_in_profile);
//  context->SetIntItemAttr(item_key, "bl_follow_realshow", bl_follow_realshow);
//  context->SetIntItemAttr(item_key, "bl_follow_list_comment", bl_follow_list_comment);
//  context->SetIntItemAttr(item_key, "bl_follow_forward", bl_follow_forward);
//  context->SetIntItemAttr(item_key, "bl_follow_longview", bl_follow_longview);
//  context->SetIntItemAttr(item_key, "bl_follow_shortview", bl_follow_shortview);
//  context->SetIntItemAttr(item_key, "bl_follow_viewtime", bl_follow_viewtime);
//  context->SetIntItemAttr(item_key, "bl_follow_unfollow", bl_follow_unfollow);
//  context->SetIntItemAttr(item_key, "bl_follow_unlike", bl_follow_unlike);
//  context->SetIntItemAttr(item_key, "bl_follow_neg", bl_follow_neg);
//  if (follow_show > 0) {
//    context->SetDoubleItemAttr(item_key, "follow_like_rate", follow_like/(double)follow_show);
//    context->SetDoubleItemAttr(item_key, "follow_click_rate", follow_click/(double)follow_show);
//  }
//  if (follow_click > 0) {
//    context->SetDoubleItemAttr(item_key, "follow_like_click_rate", follow_like/(double)follow_click);
//  }
//  if (hot_show > 0) {
//    context->SetDoubleItemAttr(item_key, "hot_like_rate", hot_like/(double)hot_show);
//  }
//  if (hot_click > 0) {
//    context->SetDoubleItemAttr(item_key, "hot_like_click_rate", hot_like/(double)hot_click);
//  }
//  context->SetIntItemAttr(item_key, "sl_hot_show_count", sl_hot_show);
//  context->SetIntItemAttr(item_key, "sl_hot_click_count", sl_hot_click);
//  context->SetIntItemAttr(item_key, "sl_hot_like_count", sl_hot_like);
//  context->SetIntItemAttr(item_key, "sl_hot_comment_count", sl_hot_comment);
//  context->SetIntItemAttr(item_key, "sl_follow_page_show_count", sl_follow_show);
//  context->SetIntItemAttr(item_key, "sl_follow_page_click_count", sl_follow_click);
//  context->SetIntItemAttr(item_key, "sl_follow_page_like_count", sl_follow_like);
//  context->SetIntItemAttr(item_key, "sl_follow_page_comment_count", sl_follow_comment);
//  if (sl_follow_show > 0) {
//    context->SetDoubleItemAttr(item_key, "sl_follow_like_rate", sl_follow_like/(double)sl_follow_show);
//    context->SetDoubleItemAttr(item_key, "sl_follow_click_rate", sl_follow_click/(double)sl_follow_show);
//  }
//  if (sl_hot_show > 0) {
//    context->SetDoubleItemAttr(item_key, "sl_hot_like_rate", sl_hot_like/(double)sl_hot_show);
//    context->SetDoubleItemAttr(item_key, "sl_hot_click_rate", sl_hot_click/(double)sl_hot_show);
//  }
//  context->SetIntItemAttr(item_key, "bl_hot_show_count", bl_hot_show);
//  context->SetIntItemAttr(item_key, "bl_hot_click_count", bl_hot_click);
//  context->SetIntItemAttr(item_key, "bl_hot_like_count", bl_hot_like);
//  context->SetIntItemAttr(item_key, "bl_hot_comment_count", bl_hot_comment);
//  context->SetIntItemAttr(item_key, "bl_follow_page_show_count", bl_follow_show);
//  context->SetIntItemAttr(item_key, "bl_follow_page_click_count", bl_follow_click);
//  context->SetIntItemAttr(item_key, "bl_follow_page_like_count", bl_follow_like);
//  context->SetIntItemAttr(item_key, "bl_follow_page_comment_count", bl_follow_comment);
//  if (bl_follow_show > 0) {
//    context->SetDoubleItemAttr(item_key, "bl_follow_like_rate", bl_follow_like/(double)bl_follow_show);
//    context->SetDoubleItemAttr(item_key, "bl_follow_click_rate", bl_follow_click/(double)bl_follow_show);
//  }
//  if (bl_hot_show > 0) {
//    context->SetDoubleItemAttr(item_key, "bl_hot_like_rate", bl_hot_like/(double)bl_hot_show);
//    context->SetDoubleItemAttr(item_key, "bl_hot_click_rate", bl_hot_click/(double)bl_hot_show);
//  }
//  realshow += hot_realshow + bl_follow_realshow + bl_hot_realshow + sl_follow_realshow
//           + sl_hot_realshow + follow_realshow + slide_realshow;
//  click += hot_click + bl_follow_click + bl_hot_click + sl_follow_click + sl_hot_click
//           + follow_click + slide_click;
//  like += hot_like + bl_follow_like + bl_hot_like + sl_follow_like + sl_hot_like
//           + follow_like + slide_like;
//  follow += hot_follow + bl_hot_follow + sl_hot_follow + slide_follow;
//  comment += hot_comment + bl_follow_comment + bl_hot_comment + sl_follow_comment
//          + sl_hot_comment + follow_comment + slide_comment;
//  shortview += hot_shortview + bl_follow_shortview + bl_hot_shortview + sl_follow_shortview
//          + sl_hot_shortview + follow_shortview + slide_shortview;
//  longview += hot_longview + bl_follow_longview + bl_hot_longview + sl_follow_longview
//          + sl_hot_longview + follow_longview + slide_longview;
//  hate += hot_neg + bl_follow_neg + bl_hot_neg + sl_follow_neg + sl_hot_neg + follow_neg + slide_neg;
//  enter_profile += hot_in_profile + bl_follow_in_profile + bl_hot_in_profile + sl_follow_in_profile
//          + sl_hot_in_profile + follow_in_profile + slide_in_profile;
//  forward += hot_forward + bl_follow_forward + bl_hot_forward + sl_follow_forward + sl_hot_forward
//          + follow_forward + slide_forward;
//  unfollow += hot_unfollow + bl_follow_unfollow + bl_hot_unfollow + sl_follow_unfollow + sl_hot_unfollow
//          + follow_unfollow + slide_unfollow;
//  unlike += hot_unlike + bl_follow_unlike + bl_hot_unlike + sl_follow_unlike + sl_hot_unlike + follow_unlike
//          + slide_unlike;
//  list_comment = hot_list_comment + bl_follow_list_comment + bl_hot_list_comment + sl_follow_list_comment
//          + sl_hot_list_comment +  follow_list_comment + slide_list_comment;
//  if (realshow > 0) {
//      context->SetDoubleItemAttr(item_key, "item_ctr", GetwilsonScore(click, realshow));
//      context->SetDoubleItemAttr(item_key, "item_ltr", GetwilsonScore(like, realshow));
//      context->SetDoubleItemAttr(item_key, "item_wtr", GetwilsonScore(follow, realshow));
//      context->SetDoubleItemAttr(item_key, "item_cmtr", GetwilsonScore(comment, realshow));
//      context->SetDoubleItemAttr(item_key, "item_svr", GetwilsonScore(shortview, realshow));
//      context->SetDoubleItemAttr(item_key, "item_lvr", GetwilsonScore(longview, realshow));
//      context->SetDoubleItemAttr(item_key, "item_htr", GetwilsonScore(hate, realshow));
//      context->SetDoubleItemAttr(item_key, "item_ftr", GetwilsonScore(forward, realshow));
//      context->SetDoubleItemAttr(item_key, "item_pptr", GetwilsonScore(enter_profile, realshow));
//      context->SetDoubleItemAttr(item_key, "item_unwtr", GetwilsonScore(unfollow, realshow));
//      context->SetDoubleItemAttr(item_key, "item_unltr", GetwilsonScore(unlike, realshow));
//      context->SetDoubleItemAttr(item_key, "item_lcmtr", GetwilsonScore(list_comment, realshow));
//  }
