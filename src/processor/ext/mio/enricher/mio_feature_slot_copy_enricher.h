#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>

#include "dragon/src/core/common_reco_util.h"
#include "base/strings/string_number_conversions.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class MioFeatureSlotCopyEnricher : public CommonRecoBaseEnricher {
 public:
  MioFeatureSlotCopyEnricher() {}

  bool IsAsync() const override {
    return false;
  }

  inline void rewrite_one_sign(const int64 old_slot, const int64 old_sign, const int64 new_slot,
                               int64 *new_sign);

  inline void rewrite_slots_and_signs(const int64 old_slot, const int64 old_sign,
                                      std::vector<int64> *new_slot_list, std::vector<int64> *new_sign_list);

  inline void rewrite_slots_and_signs(const int64 old_slot,
                                      const absl::optional<absl::Span<const int64>> &old_sign_list,
                                      const int64 new_slot, std::vector<int64> *new_sign_list);

  void SlotAsNameInnerEnrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                             RecoResultConstIter end);

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    slot_as_attr_name_ = config()->GetBoolean("slot_as_attr_name", false);

    // slot_as_attr_name 为 false 才需检查 common_slots_attr_ 、common_signs_attr_ 、 item_slots_attr_
    // 、item_signs_attr_
    if (slot_as_attr_name_) {
      src_slot_name_prefix_ = config()->GetString("src_slot_name_prefix");
      dst_slot_name_prefix_ = config()->GetString("dst_slot_name_prefix");
      if (src_slot_name_prefix_ == dst_slot_name_prefix_) {
          LOG(ERROR) << "MioFeatureSlotCopyEnricher init failed! 'src_slot_name_prefix'"
                     << " and 'dst_slot_name_prefix' should be diffrent";
          return false;
      }
    } else {
      output_attr_suffix_ = config()->GetString("output_attr_suffix");
      if (output_attr_suffix_.empty()) {
        LOG(ERROR) << "MioFeatureSlotCopyEnricher init failed! 'output_attr_suffix' "
                   << "should not be empty to avoid modifying the raw attrs data";
        return false;
      }
      const auto *reserve_input_slots = config()->Get("reserve_input_slots");
      if (reserve_input_slots) {
        if (!RecoUtil::ExtractIntSetFromJsonConfig(reserve_input_slots, &reserve_input_slots_, true)) {
          LOG(ERROR) << "MioFeatureSlotCopyEnricher init failed, reserve_input_slots should be "
                     << "a strict int array.";
          return false;
        }
      }
      reserve_valid_slots_ = config()->GetBoolean("reserve_valid_slots", false);

      // parse config for common slots and signs attr
      const auto *common_slot_attrs = config()->Get("common_slot_attrs");
      const auto *common_sign_attrs = config()->Get("common_sign_attrs");
      if (common_slot_attrs && common_sign_attrs) {
        if (!RecoUtil::ExtractStringListFromJsonConfig(common_slot_attrs, &common_slot_attrs_)) {
          LOG(ERROR) << "MioFeatureSlotCopyEnricher init failed, 'common_slots' should be a string array";
          return false;
        }
        if (!RecoUtil::ExtractStringListFromJsonConfig(common_sign_attrs, &common_sign_attrs_)) {
          LOG(ERROR) << "MioFeatureSlotCopyEnricher init failed, 'common_signs' should be a string array";
          return false;
        }
        if (common_sign_attrs_.size() != common_slot_attrs_.size()) {
          LOG(ERROR) << "MioFeatureSlotCopyEnricher init failed, len(common_slots) != len(common_signs)";
          return false;
        }
      } else if (!common_slot_attrs || !common_sign_attrs) {
        LOG(ERROR) << "MioFeatureSlotCopyEnricher init failed, common slot/sign attr mismatch.";
        return false;
      }

      // parse config for item slots and signs attr
      const auto *item_slot_attrs = config()->Get("item_slot_attrs");
      const auto *item_sign_attrs = config()->Get("item_sign_attrs");
      if (item_slot_attrs && item_sign_attrs) {
        if (!RecoUtil::ExtractStringListFromJsonConfig(item_slot_attrs, &item_slot_attrs_)) {
          LOG(ERROR) << "MioFeatureSlotCopyEnricher init failed, 'item_slots' should be a string array";
          return false;
        }
        if (!RecoUtil::ExtractStringListFromJsonConfig(item_sign_attrs, &item_sign_attrs_)) {
          LOG(ERROR) << "MioFeatureSlotCopyEnricher init failed, 'item_signs' should be a string array";
          return false;
        }
        if (item_sign_attrs_.size() != item_slot_attrs_.size()) {
          LOG(ERROR) << "MioFeatureSlotCopyEnricher init failed, len(item_slots) != len(item_signs)";
          return false;
        }
      } else if (!item_slot_attrs || !item_sign_attrs) {
        LOG(ERROR) << "MioFeatureSlotCopyEnricher init failed, item slot/sign attr mismatch.";
        return false;
      }

      if (item_slot_attrs_.empty() && common_slot_attrs_.empty()) {
        CL_LOG(ERROR) << "MioFeatureSlotCopyEnricher init failed! common_slots and item_slots "
                      << "should not be empty at same time.";
        return false;
      }
    }

    // XXX: parse slot mapping config.
    const auto *mapping = config()->Get("slot_copy");
    if (!ReloadSlotMapping(mapping)) {
      LOG(ERROR) << "MioFeatureSlotCopyEnricher init failed! {\"1\": \"2\"} like 'slot_copy' required!";
      return false;
    }

    return true;
  }

  bool ReloadSlotMapping(const base::Json *mapping_config) {
    if (!mapping_config || !mapping_config->IsObject()) {
      return false;
    }
    // reset the map and set.
    slot_mapping_.clear();
    ignore_input_slots_.clear();

    for (const auto &pair : mapping_config->objects()) {
      int64 old_slot, new_slot;
      SlotSet copy_to_slots;
      const std::string &old_slot_str = pair.first;  // ->StringValue();
      const std::string &new_slot_str = pair.second->StringValue();
      if (!base::StringToInt64(old_slot_str, &old_slot)) {
        LOG(ERROR) << "MioFeatureSlotCopyEnricher init failed! K of the map expected to be int string!";
        return false;  // personally, I do not enjoy multi exit functions...
      }

      std::vector<std::string> tokens;
      base::SplitStringWithOptions(new_slot_str, ",", true, true, &tokens);
      if (tokens.size() <= 0) {
        LOG(ERROR) << "MioFeatureSlotCopyEnricher init failed! Size of V is 0.";
        return false;
      }

      for (const auto &t : tokens) {
        if (!base::StringToInt64(t, &new_slot)) {
          LOG(ERROR) << "MioFeatureSlotCopyEnricher init failed! V of the map expected to be int string!";
          return false;
        }
        if (new_slot == old_slot) {  // 拷贝原始值
          reserve_input_slots_.insert(new_slot);
        } else {  // 需要计算新值
          copy_to_slots.insert(new_slot);
          if (reserve_valid_slots_) {  // 检查冲突的 slot, 直接加入 ignore_input_slots_ 跳过拷贝
            ignore_input_slots_.insert(new_slot);
          }
        }
      }
      if (copy_to_slots.size() > 0) {  // 如果为空，则忽略 mapping 逻辑
        slot_mapping_[old_slot] = copy_to_slots;
      }
    }
    return true;
  }

 private:
  typedef std::unordered_set<int64> SlotSet;
  std::vector<std::string> common_slot_attrs_, common_sign_attrs_;
  std::vector<std::string> item_slot_attrs_, item_sign_attrs_;

  bool reserve_valid_slots_ = false;
  SlotSet reserve_input_slots_;  // 显式指定需要拷贝保留的 slot
  SlotSet ignore_input_slots_;   // 隐式指定有冲突不拷贝的 slot, 优先级低于 reserve_input_slots_

  std::string output_attr_suffix_;

  bool slot_as_attr_name_;
  std::string src_slot_name_prefix_;
  std::string dst_slot_name_prefix_;

  std::unordered_map<int64, SlotSet> slot_mapping_;

  DISALLOW_COPY_AND_ASSIGN(MioFeatureSlotCopyEnricher);
};

}  // namespace platform
}  // namespace ks
