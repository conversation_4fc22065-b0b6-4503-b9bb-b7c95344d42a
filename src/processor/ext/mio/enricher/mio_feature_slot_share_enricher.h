#pragma once

#include <string>
#include <vector>
#include <unordered_map>

#include "dragon/src/core/common_reco_util.h"
#include "base/strings/string_number_conversions.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class MioFeatureSlotShareEnricher : public CommonRecoBaseEnricher {
 public:
  MioFeatureSlotShareEnricher() {}

  bool IsAsync() const override {
    return false;
  }

  inline void rewrite_one_sign(const int64 old_slot, const int64 old_sign, const int64 new_slot,
                               int64 *new_sign);

  inline void rewrite_shared_signs(const int64 old_slot, const int64 old_sign,
                                   std::vector<int64> *new_sign_list);

  inline void rewrite_shared_signs(const int64 old_slot,
                                   const absl::optional<absl::Span<const int64>> &old_sign_list,
                                   const int64 new_slot, std::vector<int64> *new_sign_list);

  void SlotAsNameInnerEnrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                             RecoResultConstIter end);

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    slot_as_attr_name_ = config()->GetBoolean("slot_as_attr_name", false);

    // slot_as_attr_name 为 false 才需检查 common_slots_attr_ 、common_signs_attr_ 、 item_slots_attr_
    // 、item_signs_attr_
    if (slot_as_attr_name_) {
      slot_name_prefix_ = config()->GetString("slot_name_prefix");
    } else {
      // parse config for common slots and signs attr
      const auto *common_slot_attrs = config()->Get("common_slot_attrs");
      const auto *common_sign_attrs = config()->Get("common_sign_attrs");
      if (common_slot_attrs && common_sign_attrs) {
        if (!RecoUtil::ExtractStringListFromJsonConfig(common_slot_attrs, &common_slot_attrs_)) {
          LOG(ERROR) << "MioFeatureSlotShareEnricher init failed, 'common_slots' should be a string array";
          return false;
        }
        if (!RecoUtil::ExtractStringListFromJsonConfig(common_sign_attrs, &common_sign_attrs_)) {
          LOG(ERROR) << "MioFeatureSlotShareEnricher init failed, 'common_signs' should be a string array";
          return false;
        }
        if (common_sign_attrs_.size() != common_slot_attrs_.size()) {
          LOG(ERROR) << "MioFeatureSlotShareEnricher init failed, len(common_slots) != len(common_signs)";
          return false;
        }
      } else if (!common_slot_attrs || !common_sign_attrs) {
        LOG(ERROR) << "MioFeatureSlotShareEnricher init failed, common slot/sign attr mismatch.";
        return false;
      }

      // parse config for item slots and signs attr
      const auto *item_slot_attrs = config()->Get("item_slot_attrs");
      const auto *item_sign_attrs = config()->Get("item_sign_attrs");
      if (item_slot_attrs && item_sign_attrs) {
        if (!RecoUtil::ExtractStringListFromJsonConfig(item_slot_attrs, &item_slot_attrs_)) {
          LOG(ERROR) << "MioFeatureSlotShareEnricher init failed, 'item_slots' should be a string array";
          return false;
        }
        if (!RecoUtil::ExtractStringListFromJsonConfig(item_sign_attrs, &item_sign_attrs_)) {
          LOG(ERROR) << "MioFeatureSlotShareEnricher init failed, 'item_signs' should be a string array";
          return false;
        }
        if (item_sign_attrs_.size() != item_slot_attrs_.size()) {
          LOG(ERROR) << "MioFeatureSlotShareEnricher init failed, len(item_slots) != len(item_signs)";
          return false;
        }
      } else if (!item_slot_attrs || !item_sign_attrs) {
        LOG(ERROR) << "MioFeatureSlotShareEnricher init failed, item slot/sign attr mismatch.";
        return false;
      }

      if (item_slot_attrs_.empty() && common_slot_attrs_.empty()) {
        CL_LOG(ERROR) << "MioFeatureSlotShareEnricher init failed! common_slots and item_slots "
                      << "should not be empty at same time.";
        return false;
      }
    }

    // XXX: parse slot mapping config.
    const auto *mapping = config()->Get("slot_share");
    if (!ReloadSlotMapping(mapping)) {
      LOG(ERROR) << "MioFeatureSlotShareEnricher init failed! {\"1\": \"2\"} like 'slot_share' required!";
      return false;
    }

    return true;
  }

  bool ReloadSlotMapping(const base::Json *mapping_config) {
    if (!mapping_config || !mapping_config->IsObject()) {
      return false;
    }
    // reset the map and set.
    slot_share_.clear();

    for (const auto &pair : mapping_config->objects()) {
      int64 old_slot, new_slot;
      const std::string &old_slot_str = pair.first;  // ->StringValue();
      const std::string &new_slot_str = pair.second->StringValue();
      if (!base::StringToInt64(old_slot_str, &old_slot)) {
        LOG(ERROR) << "MioFeatureSlotShareEnricher init failed! K of the map expected to be int string!";
        return false;  // personally, I do not enjoy multi exit functions...
      }

      if (!base::StringToInt64(new_slot_str, &new_slot)) {
        LOG(ERROR) << "MioFeatureSlotShareEnricher init failed! V of the map expected to be int string!";
        return false;
      }
      if (new_slot != old_slot) {  // 拷贝原始值
        slot_share_[old_slot] = new_slot;
      }
    }
    if (slot_share_.size() == 0) {
      LOG(ERROR) << "MioFeatureSlotShareEnricher init failed! no valid slot-share pairs exist.";
      return false;
    }
    return true;
  }

 private:
  std::vector<std::string> common_slot_attrs_, common_sign_attrs_;
  std::vector<std::string> item_slot_attrs_, item_sign_attrs_;

  bool slot_as_attr_name_;
  std::string slot_name_prefix_;

  std::unordered_map<int64, int64> slot_share_;

  DISALLOW_COPY_AND_ASSIGN(MioFeatureSlotShareEnricher);
};

}  // namespace platform
}  // namespace ks
