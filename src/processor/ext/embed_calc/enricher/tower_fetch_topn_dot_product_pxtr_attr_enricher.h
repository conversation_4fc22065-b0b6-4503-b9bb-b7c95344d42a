#pragma once

#include <algorithm>
#include <string>
#include <vector>
#include <memory>
#include <unordered_map>

#include "folly/container/F14Map.h"
#include "serving_base/utility/timer.h"
#include "kess/rpc/grpc/grpc_client_builder.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/processor/ext/embed_calc/enricher/tower_fetch_remote_pxtr_attr_enricher.h"

namespace ks {
namespace platform {

class FlattenCommonRecoRequest;
class FlattenCommonRecoResponse;
class FlattenDebugInfo;

namespace fbs {
class FlattenCommonRecoResponse;
}

class TowerFetchTopNDotProductAttrEnricher : public TowerFetchRemotePxtrAttrEnricher {
 public:
  struct ItemDistance {
    uint64 item_key;
    float distance;
    ItemDistance(uint64 item_key, float distance): item_key(item_key), distance(distance) {}
  };

  TowerFetchTopNDotProductAttrEnricher();

  bool IsAsync() const override {
    return true;
  }

  void Enrich(MutableRecoContextInterface* context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  void Clear();

  int GetUserEmbeddingLenFactor(MutableRecoContextInterface *context);
  bool BuildFlattenCommonRecoRequest(MutableRecoContextInterface *context,
                                     const std::vector<float> &user_embedding,
                                     const std::vector<uint64> &item_embedding_keys,
                                     const std::vector<int> &item_offset,
                                     int label_num,
                                     FlattenCommonRecoRequest *request);

  bool AsyncGetPredictPxtrs(MutableRecoContextInterface* context,
                            const std::vector<FlattenCommonRecoRequest>& request);
  void OnResponse(const FlattenCommonRecoResponse* response, MutableRecoContextInterface* context);
  void SetResult(MutableRecoContextInterface* context);
  void PerfPxtr(MutableRecoContextInterface* context) const;

  bool IsDebugRequest(MutableRecoContextInterface *context) const;

  bool InitProcessor() override {
    kess_service_ = config()->GetString("kess_service");
    if (kess_service_.empty()) {
      LOG(ERROR) << GetName() << " init failed!"
                 << "Config of kess_service is empty.";
      return false;
    }
    kess_cluster_ = config()->GetString("kess_cluster", "PRODUCTION");
    sub_req_num_in_shard_ = config()->GetInt("sub_req_num_in_shard", 1);
    sub_req_num_in_shard_ = std::max(1, sub_req_num_in_shard_);

    shards_ = config()->GetInt("shards", -1);
    if (shards_ <= 0) {
      LOG(ERROR) << GetName() << " init failed!"
                 << "the config of shards is invalid";
      return false;
    }

    timeout_ms_ = config()->GetInt("timeout_ms", 50);
    if (timeout_ms_ <= 0) {
      LOG(ERROR) << GetName() << " init failed! timeout_ms must be > 0";
      return false;
    }

    user_embedding_attr_ = config()->GetString("user_embedding_attr", "");
    use_item_type_ = config()->GetBoolean("use_item_type", false);
    use_item_key_as_embed_key_ = config()->GetBoolean("use_item_key_as_embed_key", true);
    item_embedding_key_attr_ = config()->GetString("item_embedding_key_attr", "");
    non_unique_item_embedding_key_ = config()->GetBoolean("non_unique_item_embedding_key", false);

    // 仅支持 output_type = 4,5
    output_type_ = config()->GetInt("output_type", 0);
    if (output_type_ != 4 && output_type_ != 5) {
      LOG(ERROR) << GetName() << " unsupported output_type found: " << output_type_;
      return false;
    }
    LOG(INFO) << GetName() << " output_type found: " << output_type_;

    emb_dim_ = config()->GetInt("emb_dim", 0);

    server_request_type_ = config()->GetString("server_request_type", "");
    req_common_embedding_attr_ = config()->GetString("req_common_embedding_attr", "req_common_embedding");
    req_tower_caller_attr_ = config()->GetString("req_tower_caller_attr", "tower_caller");
    return_pxtr_value_attr_ = config()->GetString("return_pxtr_value_attr", "return_pxtr_value");
    return_sorted_item_ids_attr_ = config()->GetString("return_sorted_item_ids_attr", "sorted_item_ids");
    sorted_item_idx_attr_ = config()->GetString("sorted_item_idx_attr", "sorted_item_idx");
    sorted_item_ids_attr_ = config()->GetString("sorted_item_ids_attr", "sorted_item_ids_res");
    sorted_item_pxtrs_attr_ = config()->GetString("sorted_item_pxtrs_attr", "sorted_item_pxtrs_res");
    kconf_timeout_ms_attr_ = config()->GetString("kconf_timeout_ms_attr", "");

    filter_invalid_distance_ = config()->GetBoolean("filter_invalid_distance", false);
    invalid_max_distance_ = config()->GetFloat("invalid_max_distance", 1.0);

    auto *label_config = config()->Get("predict_labels");
    if (label_config && label_config->IsArray()) {
      for (auto c : label_config->array()) {
        std::string value;
        if (c->IsString() && c->StringValue(&value) && !value.empty()) {
          predict_labels_.push_back(value);
        }
      }
    }
    CHECK_EQ(predict_labels_.size(), 1) << GetName() << " only supports one predict label now";

    perf_item_num_ = config()->GetInt("perf_item_num", 500);

    LoadReqExtraConfig();

    LOG(INFO) << GetName() << " service:" << kess_service_ << " cluster:" << kess_cluster_
              << " shard_num:" << shards_ << " timeout_ms:" << timeout_ms_
              << " user_embedding_attr:" << user_embedding_attr_
              << " item_embedding_key_attr:" << item_embedding_key_attr_
              << " server_request_type:" << server_request_type_
              << " use_item_key_as_embed_key:" << use_item_key_as_embed_key_
              << " predict_labels:" << base::JoinStrings(predict_labels_, ",")
              << " sub_req_num_in_shard:" << sub_req_num_in_shard_;

    return !user_embedding_attr_.empty() && predict_labels_.size() > 0 && !server_request_type_.empty() &&
           (use_item_key_as_embed_key_ || !item_embedding_key_attr_.empty());
  }

 private:
  void SortItemDistance(MutableRecoContextInterface *context, size_t top_n) {
    top_n = std::min(top_n, std::min_element(item_distances_.begin(), item_distances_.end(),
                                             [](const auto &left, const auto &right) {
                                               return left.size() < right.size();
                                             })
                                ->size());
    std::unordered_map<uint64, size_t> non_uniq_key_idx;
    for (int i = 0; i < req_inum_; ++i) {
      if (filter_invalid_distance_) {
        for (auto &item_dist : item_distances_[i]) {
          if (item_dist.distance > invalid_max_distance_) {
            item_dist.distance = 0.;
          }
        }
      }
      std::partial_sort(
          item_distances_[i].begin(), item_distances_[i].begin() + top_n, item_distances_[i].end(),
          [](const ItemDistance &left, const ItemDistance &right) { return left.distance > right.distance; });
      non_uniq_key_idx.clear();
      for (int j = 0; j < top_n; ++j) {
        if (non_unique_item_embedding_key_) {
          auto distance_item_key = item_distances_[i][j].item_key;
          if (non_uniq_key_idx.find(distance_item_key) == non_uniq_key_idx.end()) {
            non_uniq_key_idx[distance_item_key] = 0;
          }
          auto idx = std::min(
            non_uniq_key_idx[distance_item_key],
            non_uniq_key_to_item_key_list_[distance_item_key].size() - 1);
          auto ori_item_key = non_uniq_key_to_item_key_list_[distance_item_key][idx];
          sorted_item_idx_.emplace_back(key_to_idx_[ori_item_key]);
          non_uniq_key_idx[distance_item_key] += 1;
          sorted_item_pxtrs_.emplace_back(item_distances_[i][j].distance);
        } else {
          sorted_item_idx_.emplace_back(key_to_idx_[item_distances_[i][j].item_key]);
          sorted_item_pxtrs_.emplace_back(item_distances_[i][j].distance);
        }
      }
    }
  }

  std::string kess_service_;
  std::string kess_cluster_;
  std::string user_embedding_attr_;
  std::string item_embedding_key_attr_;
  bool non_unique_item_embedding_key_;

  bool filter_invalid_distance_ = false;
  float invalid_max_distance_ = 1.0;

  // 请求的 tower 服务的 request_type 信息
  std::string server_request_type_;
  // 在 requst common attr 中指示 common embedding 存放的字段
  std::string req_common_embedding_attr_;
  // 在 request common attr 中标明自己的调用身份
  std::string req_tower_caller_attr_;
  // 在 request common attr 中指定返回 pxtr 结果时存放的 response common attr 字段，被调必须遵守
  std::string return_pxtr_value_attr_;
  // 在 request common attr 中指定返回的排序过的 item_ids 的字段，被调必须遵守
  std::string return_sorted_item_ids_attr_;
  // 在 common attr 中指定排序过的 item idx
  std::string sorted_item_idx_attr_;
  // 返回排序过的 item id vector
  std::string sorted_item_ids_attr_;
  // 返回排序过的 item 对应的 pxtr vector
  std::string sorted_item_pxtrs_attr_;
  // kconf 配置的访问 pxtr 超时阈值， 使用时 std::max(timeout_ts_, kconf_timeout_ts)
  std::string kconf_timeout_ms_attr_;

  // predict_labels.size * @req_common_embedding_dim_ = @req_common_embedding_len_
  std::vector<std::string> predict_labels_;

  bool use_item_type_ = false;
  bool use_item_key_as_embed_key_ = true;

  // 0: 以 std::vector<float>* 输出至 common attr （默认）
  // 1: 以 Doublelist 输出至 item attr
  // 2: 以 Double 输出至对应 pxtr 的 item attr
  int output_type_ = 0;
  int shards_;
  int sub_req_num_in_shard_;
  int timeout_ms_;
  int perf_item_num_;

  int emb_dim_;

  serving_base::Timer timer_;
  std::vector<std::shared_ptr<FlattenCommonRecoResponse>> responses_;
  std::vector<uint64> t_item_embedding_keys_;
  std::vector<float> t_item_pxtr_matrix_;
  int req_inum_;
  int merge_cnt_ = 0;
  int receive_cnt_ = 0;
  int64 tm_wait_response_ = 0;
  int64 tm_merge_response_ = 0;

  std::vector<std::vector<ItemDistance>> item_distances_;
  std::vector<int> sorted_item_idx_;
  std::vector<int64> sorted_item_ids_;
  std::vector<double> sorted_item_pxtrs_;
  folly::F14FastMap<uint64, int> key_to_idx_;
  folly::F14FastMap<uint64, std::vector<uint64>> non_uniq_key_to_item_key_list_;
};

}  // namespace platform
}  // namespace ks
