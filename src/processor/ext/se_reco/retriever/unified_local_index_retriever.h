#pragma once
#include <future>
#include <memory>
#include <mutex>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>
#include "dragon/src/processor/base/common_reco_base_retriever.h"
#include "se/txt2vid_se/indexing/rank_index/manage/multi_index_manager.h"
#include "taskflow/taskflow.hpp"
namespace ks {
namespace platform {
// 本地索引异步算子
class UnifiedLocalIndexRetriever : public CommonRecoBaseRetriever {
 public:
  UnifiedLocalIndexRetriever() {}
  ~UnifiedLocalIndexRetriever() {}

  void Retrieve(AddibleRecoContextInterface *context) override;

 private:
  bool InitProcessor() override;
  bool InitOnce();
  void RetrieveIds(AddibleRecoContextInterface *context, std::vector<uint64> *keys,
                   std::vector<uint64_t> *item_ids, std::vector<CommonRecoRetrieveResult> *retrieve_items);
  void FillItemAttrs(AddibleRecoContextInterface *context, const std::vector<uint64> &keys,
                     const std::vector<uint64_t> &item_ids);

  void ProcessItemData(AddibleRecoContextInterface *context, const uint64_t &result, const uint64_t &data_key,
                       const std::vector<ItemAttr *> &output_attr_accessors, int32_t *miss_cnt, size_t idx);

 private:
  std::string recall_table_name_;
  std::string recall_table_name_dynamic_;
  std::string feature_table_name_;
  // primary_key 对应的属性值为 string 类型，先进行 hash 再读取数据
  std::string primary_key_;
  // <original field name in flatKV data, exported attr name>
  std::vector<std::pair<std::string, std::string>> output_attr_names_;
  std::unordered_map<std::string, int> name_to_index_;
  std::unordered_map<std::string, absl::optional<FlatKvValueType>> name_to_type_;
  int batch_size_ = 0;
  const ::search::rank::index::RankIndexManager *r_table_manager_ = nullptr;
  const ::search::rank::index::RankIndexManager *f_table_manager_ = nullptr;
  std::shared_ptr<FlatKvSchemaPool> r_schema_pool_ = nullptr;
  std::shared_ptr<FlatKvSchemaPool> f_schema_pool_ = nullptr;
  const SingleFlatKvSchema *r_schema_ = nullptr;
  const SingleFlatKvSchema *f_schema_ = nullptr;

  static bool init_source_status_;
  static std::once_flag init_once_flag_;
  // static tf::Executor executor_;

  int single_num_ = 30;
  int total_num_ = 1500;
  std::string item_recall_reason_name_;
  std::string item_score_name_;
  std::string recall_name_;
  std::vector<std::string> key_list_;
  bool is_recall_decrease_ = false;
  bool is_sort_for_keys_ = false;
};
}  // namespace platform
}  // namespace ks
