#include "dragon/src/processor/ext/se_reco/retriever/unified_local_index_retriever.h"
#include <algorithm>
#include <set>
#include <utility>
#include "dragon/src/processor/ext/se_reco/util/se_reco_common_util.h"
#include "se/txt2vid_se/base/singleton.h"

namespace ks {
namespace platform {
bool UnifiedLocalIndexRetriever::init_source_status_ = false;
std::once_flag UnifiedLocalIndexRetriever::init_once_flag_;
// tf::Executor UnifiedLocalIndexRetriever::executor_{1000};

bool UnifiedLocalIndexRetriever::InitProcessor() {
  std::call_once(init_once_flag_, [this]() { init_source_status_ = InitOnce(); });
  if (!init_source_status_) {
    LOG(ERROR) << "UnifiedLocalIndexRetriever InitOnce failed";
    return false;
  }

  auto *recall_table_name = config()->Get("recall_table_name");
  if (recall_table_name!= nullptr && recall_table_name->IsString()) {
    if (auto op = RecoUtil::ExtractCommonAttrFromExpr(recall_table_name)) {
      recall_table_name_dynamic_ = std::string(op->data(), op->size());
      LOG(INFO) << "dynamic table name: " << recall_table_name_dynamic_;
    } else {
      recall_table_name_ = recall_table_name->StringValue();
      LOG(INFO) << "static table name: " << recall_table_name_;
    }
  } else {
    LOG(ERROR) << "recall_table_name must be String.";
    return false;
  }

  feature_table_name_ = config()->GetString("feature_table_name", "");
  is_recall_decrease_ = config()->GetBoolean("is_recall_decrease", false);
  is_sort_for_keys_ = config()->GetBoolean("is_sort_for_keys", false);
  if ((recall_table_name_dynamic_.empty() && recall_table_name_.empty()) ||
      feature_table_name_.empty()) {
    LOG(ERROR) << "UnifiedLocalIndexRetriever init error, table_name not set";
    return false;
  }
  primary_key_ = config()->GetString("primary_key", "");
  if (primary_key_.empty()) {
    LOG(ERROR) << "UnifiedLocalIndexRetriever init error, primary_key not set";
    return false;
  }
  item_recall_reason_name_ = config()->GetString("item_recall_reason_name", "item_recall_reason");
  if (item_recall_reason_name_.empty()) {
    LOG(ERROR) << "UnifiedLocalIndexRetriever init error, item_recall_reason_name not set";
    return false;
  }
  item_score_name_ = config()->GetString("item_score_name", "item_score");
  const auto *output_fields = config()->Get("output_attr_names");
  if (output_fields == nullptr || !output_fields->IsArray()) {
    LOG(ERROR) << "output_attr_names mut be set as a json array";
    return false;
  }
  for (auto field : output_fields->array()) {
    if (field->IsObject()) {
      std::string field_name = field->GetString("field_name");
      if (field_name.empty()) {
        continue;
      }
      std::string export_name = field->GetString("export_name");
      if (export_name.empty()) {
        export_name = field_name;
      }
      output_attr_names_.emplace_back(std::move(field_name), std::move(export_name));
    }
  }
  if (output_attr_names_.empty()) {
    LOG(ERROR) << "UnifiedLocalIndexRetriever init failed! `output_attr_name` should not be empty";
    return false;
  }
  batch_size_ = config()->GetInt("batch_size", 30);
  return true;
}

bool UnifiedLocalIndexRetriever::InitOnce() {
  auto index_managers = Singleton<::search::rank::index::MultiIndexManager>::get();
  if (!index_managers->Init()) {
    LOG(ERROR) << "Init search::rank::index::MultiIndexManager failed";
    return false;
  }
  return true;
}

void UnifiedLocalIndexRetriever::Retrieve(AddibleRecoContextInterface *context) {
  // string 类型需要先获取 id 用作召回
  int64_t start_time = base::GetTimestamp();
  if (!recall_table_name_dynamic_.empty()) {
    recall_table_name_ = GetStringProcessorParameter(context, recall_table_name_dynamic_);
  }

  auto index_managers = Singleton<::search::rank::index::MultiIndexManager>::get();
  r_table_manager_ = index_managers->GetAttrTableByName(recall_table_name_);
  f_table_manager_ = index_managers->GetAttrTableByName(feature_table_name_);
  if (r_table_manager_ == nullptr || f_table_manager_ == nullptr) {
    LOG(ERROR) << "UnifiedLocalIndexRetriever::Enrich error: no table manager for "
               << (r_table_manager_ ? feature_table_name_ : recall_table_name_);
    return;
  }
  r_schema_pool_ = r_table_manager_->GetSchema();
  f_schema_pool_ = f_table_manager_->GetSchema();
  if (r_schema_pool_ == nullptr || f_schema_pool_ == nullptr) {
    LOG(ERROR) << "UnifiedLocalIndexRetriever::Enrich error: no schema for "
               << (r_schema_pool_ ? feature_table_name_ : recall_table_name_);
    return;
  }
  std::vector<uint64> keys;
  std::vector<uint64_t> item_ids;
  std::vector<CommonRecoRetrieveResult> retrieve_items;
  // recall_table
  RetrieveIds(context, &keys, &item_ids, &retrieve_items);
  if (keys.empty()) {
    VLOG(2) << "UnifiedLocalIndexRetriever::Retrieve error: keys is empty";
    return;
  }
  int64_t retrieve_id_time = base::GetTimestamp();
  // feature table 根据 key 回填特征
  FillItemAttrs(context, keys, item_ids);
  int64_t fill_item_attrs_time = base::GetTimestamp();

  AddToRecoResults(context, retrieve_items);

  int64_t end_time = base::GetTimestamp();

  ks::infra::PerfUtil::IntervalLogStashAndLocal(retrieve_id_time - start_time, "search",
                                                GlobalHolder::GetServiceIdentifier(), "unified_retriever",
                                                recall_name_, "retrieve_id");
  ks::infra::PerfUtil::IntervalLogStashAndLocal(fill_item_attrs_time - retrieve_id_time, "search",
                                                GlobalHolder::GetServiceIdentifier(), "unified_retriever",
                                                recall_name_, "fill_item_attrs");
  ks::infra::PerfUtil::IntervalLogStashAndLocal(end_time - fill_item_attrs_time, "search",
                                                GlobalHolder::GetServiceIdentifier(), "unified_retriever",
                                                recall_name_, "add_to_results");
  ks::infra::PerfUtil::IntervalLogStashAndLocal(end_time - start_time, "search",
                                                GlobalHolder::GetServiceIdentifier(), "unified_retriever",
                                                recall_name_, "total");
}

void UnifiedLocalIndexRetriever::RetrieveIds(AddibleRecoContextInterface *context, std::vector<uint64> *keys,
                                             std::vector<uint64_t> *item_ids,
                                             std::vector<CommonRecoRetrieveResult> *retrieve_items) {
  // 根据 key 召回
  auto key_attr = context->GetStringListCommonAttr(primary_key_);
  if (!key_attr) {
    VLOG(2) << primary_key_ << " attr is not exist";
    return;
  }

  single_num_ = GetIntProcessorParameter(context, "single_num", 30);
  total_num_ = GetIntProcessorParameter(context, "total_num", 1500);
  recall_name_.clear();
  recall_name_ = GetStringProcessorParameter(context, "recall_name");
  if (recall_name_.empty()) {
    CL_LOG_ERROR_EVERY("UnifiedLocalIndexRetriever", "no recall_name", 100)
        << "local index recall cancelled: no recall_name! processor: " << GetName();
    return;
  }
  VLOG(2) << "recall_name: " << recall_name_ << " single_num: " << single_num_
          << " total_num: " << total_num_;
  int total_size = key_attr->size() * single_num_;
  keys->reserve(total_size);
  retrieve_items->reserve(total_size);
  item_ids->reserve(total_size);

  std::string recall_score = GetStringProcessorParameter(context, "recall_score");
  VLOG(2) << "recall_score: " << recall_score;

  key_list_.clear();
  key_list_.reserve(total_size);
  auto item_recall_reason_accessor = context->GetItemAttrAccessor(item_recall_reason_name_);
  auto item_score_accessor = context->GetItemAttrAccessor(item_score_name_);
  int count = 0;
  int single_num = single_num_;

  // 对齐 base
  std::vector<absl::string_view> key_attr_vec;
  key_attr_vec.reserve(key_attr->size());
  if (is_sort_for_keys_) {
    std::set<absl::string_view> key_attr_set;
    for (const auto &key : *key_attr) {
      key_attr_set.insert(key);
    }
    key_attr_vec.assign(key_attr_set.begin(), key_attr_set.end());
  } else {
    key_attr_vec.assign(key_attr->begin(), key_attr->end());
  }

  for (const auto &key : key_attr_vec) {
    if (count >= total_num_) break;
    std::string value_str;
    auto value_o = r_table_manager_->GetValue(key, value_str);
    if (unlikely(!value_o.has_value())) {
      VLOG(2) << "UnifiedLocalIndexRetriever::Retrieve error: no value for key: " << key;
      continue;
    }
    std::string key_str{key.data(), key.size()};

    std::string error;
    auto reader = FlatKvReader::Read(r_schema_pool_.get(), value_o.value(), &error);
    if (!reader) {
      CL_LOG(ERROR) << "reader is null, error:" << error;
      continue;
    }
    r_schema_ = reader->GetSchema();
    if (r_schema_ == nullptr) {
      LOG(ERROR) << "r_schema_ is nullptr";
      continue;
    }
    int attr_idx = r_schema_->GetFieldIndex(recall_name_);
    if (attr_idx < 0) {
      CL_LOG(ERROR) << "UnifiedLocalIndexRetriever::Retrieve error: no attr index for " << recall_name_;
      continue;
    }
    auto type_o = r_schema_->GetFieldType(attr_idx);
    int score_attr_idx = r_schema_->GetFieldIndex(recall_score);
    absl::optional<FlatKvValueType> score_type_o = absl::nullopt;
    if (score_attr_idx > 0) {
      score_type_o = r_schema_->GetFieldType(score_attr_idx);
    }
    if (unlikely(!type_o.has_value() || type_o.value() != FlatKvValueType::kIntList)) {
      CL_LOG(ERROR) << "UnifiedLocalIndexRetriever::Retrieve error: no int list for " << recall_name_;
      continue;
    }

    int offset = item_ids->size();

    auto int_list_o = reader->GetIntList(attr_idx);
    if (int_list_o.has_value() && !int_list_o.value().empty()) {  // set query
      // result_item & item_key
      int single_count = 0;

      for (size_t idx = 0; idx < int_list_o.value().size(); ++idx) {
        auto val = int_list_o.value().at(idx);
        if (val == 0) {
          break;
        }
        auto item_id = se_reco::GenerateItemKey(key_str, recall_name_, "_" + std::to_string(val));
        CommonRecoRetrieveResult retrieve_item(item_id, 1001, 0);
        VLOG(2) << "origin_key:" << val << ", item_id:" << item_id;
        retrieve_items->emplace_back(retrieve_item);
        context->SetStringItemAttr(item_id, item_recall_reason_accessor, key_str);
        item_ids->emplace_back(item_id);
        keys->emplace_back(val);
        if (VLOG_IS_ON(2)) {
          key_list_.emplace_back(key_str);
        }
        ++single_count;
        ++count;
        if (single_count >= single_num) {
          if (is_recall_decrease_) single_num--;
          break;
        }
      }
    }
    if (score_type_o == absl::nullopt || score_type_o.value() != FlatKvValueType::kFloatList) {
      VLOG(2) << "error: no float list for " << recall_score << " in " << key_str;
      continue;
    }
    auto float_list_o = reader->GetFloatListSpan(score_attr_idx);
    VLOG(2) << "key:" << key_str << ", score_attr_idx:" << score_attr_idx << ", recall_score:" << recall_score
            << ", score value size:" << (float_list_o.has_value() ? float_list_o.value().size() : 0);
    if (int_list_o.has_value() && float_list_o.has_value() &&
        int_list_o.value().size() == float_list_o.value().size()) {  // set score
      size_t value_size = float_list_o.value().size();
      for (size_t idx = 0; idx < value_size && offset + idx < item_ids->size(); ++idx) {
        context->SetDoubleItemAttr(item_ids->at(offset + idx), item_score_accessor,
                                   static_cast<double>(float_list_o.value().at(idx)));
      }
    }

    if (VLOG_IS_ON(2)) {
      std::stringstream ss;
      ss << "UnifiedLocalIndexRetriever::Retrieve: " << key_str << ": ";
      for (auto val : int_list_o.value()) {
        ss << val << ", ";
      }
      LOG(INFO) << ss.str();
    }
  }
}

void UnifiedLocalIndexRetriever::FillItemAttrs(AddibleRecoContextInterface *context,
                                               const std::vector<uint64> &keys,
                                               const std::vector<uint64_t> &item_ids) {
  std::vector<ItemAttr *> output_attr_accessors;
  output_attr_accessors.reserve(output_attr_names_.size());
  for (const auto &name_pair : output_attr_names_) {
    output_attr_accessors.emplace_back(context->GetItemAttrAccessor(name_pair.second));
  }

  VLOG(2) << "keys_size:" << keys.size() << ", item_ids_size:" << item_ids.size();
  if (keys.size() != item_ids.size()) {
    return;
  }
  // NOTE(fuyanzhuo): 较多是可以并行 分 batch
  int32_t miss_cnt = 0;
  for (size_t i = 0; i < keys.size(); ++i) {
    ProcessItemData(context, item_ids[i], keys[i], output_attr_accessors, &miss_cnt, i);
    VLOG(2) << "item_id:" << item_ids[i] << ", key:" << keys[i];
  }
  ks::infra::PerfUtil::IntervalLogStash(keys.size(), "search",
                                        GlobalHolder::GetServiceIdentifier(),  // count & namespace & subtag
                                        "unified_retriever", recall_name_, "feature_cnt",
                                        "total_cnt");  // extra1~3
  ks::infra::PerfUtil::IntervalLogStash(miss_cnt, "search", GlobalHolder::GetServiceIdentifier(),
                                        "unified_retriever", recall_name_, "feature_cnt", "miss_cnt");
}

void UnifiedLocalIndexRetriever::ProcessItemData(AddibleRecoContextInterface *context, const uint64_t &result,
                                                 const uint64_t &data_key,
                                                 const std::vector<ItemAttr *> &output_attr_accessors,
                                                 int32_t *miss_cnt, size_t idx) {
  std::string value_str;
  auto value_o = f_table_manager_->GetValue(data_key, value_str, false);
  if (!value_o.has_value()) {
    ++(*miss_cnt);
    if (VLOG_IS_ON(2)) {
      VLOG(2) << "UnifiedLocalIndexRetriever::ProcessItemData error: no value for key: " << data_key
              << ",result:" << result << ",recall_name:" << recall_name_
              << ", key str:" << (idx < key_list_.size() ? key_list_[idx] : "");
    }
    return;
  }
  auto reader = FlatKvReader::Read(f_schema_pool_.get(), value_o.value());
  for (int i = 0; i < output_attr_names_.size(); ++i) {
    const auto &field_name_info = output_attr_names_[i];
    const auto &attr_name = field_name_info.first;
    const auto &export_name = field_name_info.second;
    auto *output_accessor = output_attr_accessors[i];
    f_schema_ = reader->GetSchema();
    if (f_schema_ == nullptr) {
      LOG(ERROR) << "f_schema_ is nullptr";
      continue;
    }
    int attr_idx = f_schema_->GetFieldIndex(attr_name);
    if (attr_idx < 0) {
      LOG(ERROR) << "attr idx is invalid.";
      continue;
    }
    auto type_o = f_schema_->GetFieldType(attr_idx);
    if (type_o.has_value()) {
      switch (type_o.value()) {
        case FlatKvValueType::kInt:
          context->SetIntItemAttr(result, output_accessor, reader->GetInt(attr_idx).value_or(0));
          break;
        case FlatKvValueType::kFloat:
          context->SetDoubleItemAttr(result, output_accessor,
                                     static_cast<double>(reader->GetFloat(attr_idx).value_or(0.0f)));
          break;
        case FlatKvValueType::kString:
          context->SetStringItemAttr(result, output_accessor, reader->GetString(attr_idx).value_or(""));
          VLOG(2) << "key:" << data_key << ",result:" << result << ", attr_name:" << attr_name
                  << ", attr_value:" << reader->GetString(attr_idx).value_or("");
          break;
        case FlatKvValueType::kIntList: {
          auto int_list_o = reader->GetIntList(attr_idx);
          if (int_list_o.has_value() && !int_list_o.value().empty()) {
            context->SetIntListItemAttr(result, output_accessor, std::move(int_list_o.value()));
          }
        } break;
        case FlatKvValueType::kFloatList: {
          auto float_list_o = reader->GetFloatListSpan(attr_idx);
          if (float_list_o.has_value() && !float_list_o.value().empty()) {
            std::vector<double> values;
            auto &float_list = float_list_o.value();
            std::for_each(float_list.begin(), float_list.end(),
                          [&](float val) { values.push_back(static_cast<double>(val)); });
            context->SetDoubleListItemAttr(result, output_accessor, std::move(values));
          }
        } break;
        case FlatKvValueType::kStringList: {
          auto str_list_o = reader->GetStringList(attr_idx);
          if (str_list_o.has_value() && !str_list_o.value().empty()) {
            context->SetStringListItemAttr(result, output_accessor, std::move(str_list_o.value()));
          }
        }
        default:
          break;
      }
    }
  }
}
typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, UnifiedLocalIndexRetriever, UnifiedLocalIndexRetriever)
}  // namespace platform
}  // namespace ks
