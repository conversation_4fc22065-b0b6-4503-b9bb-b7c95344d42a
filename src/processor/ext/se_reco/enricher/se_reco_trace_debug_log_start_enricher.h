#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "folly/container/F14Map.h"
#include "folly/container/F14Set.h"
#include "se/online_service/protos/debug_log.pb.h"
#include "se/txt2vid_se/combo_search/search/utils/debug_log_common.h"

namespace ks {
namespace platform {

class SeRecoTraceDebugLogStartEnricher : public CommonRecoBaseEnricher {
 public:
  SeRecoTraceDebugLogStartEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    stage_key_ = config()->GetString("stage", "");
    if ("" == stage_key_) {
      CL_LOG(ERROR) << "SeRecoTraceDebugLogStartEnricher init failed! 'stage' must be set.";
      return false;
    }
    stage_ = stage_key_ + "__INNER__";
    must_output_ = config()->GetInt("must_output", 2);
    log_level_ = config()->GetInt("log_level", 3);

    const auto *items = config()->Get("item_attrs");
    if (items && !RecoUtil::ExtractStringListFromJsonConfig(items, &items_)) {
      CL_LOG(ERROR) << "SeRecoTraceDebugLogStartEnricher init failed! 'items' should be a string list.";
    }
    const auto *pack_to = config()->Get("pack_to");
    if (pack_to && !RecoUtil::ExtractStringListFromJsonConfig(pack_to, &pack_to_)) {
      CL_LOG(ERROR) << "SeRecoTraceDebugLogStartEnricher init failed! 'items' should be a string list.";
    }

    return true;
  }

  void AppendItemAttrs(MutableRecoContextInterface *context, const std::vector<uint64> &item_keys);
  bool CheckDup(bool dedup_to_common_attr, folly::F14FastSet<uint64> *dedup_set, uint64 value);

 private:
  int64_t must_output_ = 0;
  int64_t log_level_ = 0;

  std::string stage_;
  std::string stage_key_;
  std::vector<uint64> all_items_;
  std::vector<std::string> items_;
  std::vector<std::string> pack_to_;
};

}  // namespace platform
}  // namespace ks
