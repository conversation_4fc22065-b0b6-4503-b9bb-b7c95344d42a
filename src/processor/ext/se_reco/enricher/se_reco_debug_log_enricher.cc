#include "dragon/src/processor/ext/se_reco/enricher/se_reco_debug_log_enricher.h"
#include <algorithm>
#include <cstddef>
#include <iterator>
#include <string>
#include <utility>
#include <vector>
#include "absl/strings/str_cat.h"

namespace ks {
namespace platform {

void SeRecoDebugLogEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
  // 1. 拿到 ptr
  std::shared_ptr<kuaishou::ds::search::DebugLogCommon> ctx{
      context->GetMutablePtrCommonAttr<kuaishou::ds::search::DebugLogCommon>("se_debug_log_ptr"),
      [](auto) {}};
  if (ctx == nullptr) {
    CL_LOG(ERROR) << "ctx is nullptr.";
    return;
  }
  // 输出掩码
  int output_mask = OUTPUT_NONE;
  output_mask |= ctx->IsInternal() || ctx->IsTest() ? OUTPUT_TRACE : 0;
  output_mask |= ctx->IsOnlineDebug() ? OUTPUT_ONLINE : 0;
  output_mask |= ctx->IsOfflineDebug() ? OUTPUT_OFFLINE : 0;
  bool log_switch = ctx->GetLogSwitch();
  if (log_switch) {
    PrintItemAttrInfo(context, ctx, output_mask, "ArchInfo", "类型|item_key|召回源|召回分|idx",
                      {"item_id", "item_source", "item_score"}, begin, end, 1, 0);
  }

  if (!common_attrs_.empty() && output_mask) {
    PrintCommonAttrInfo(context, ctx, output_mask);
  }
  if (!item_attrs_.empty() && output_mask) {
    PrintItemAttrInfo(context, ctx, output_mask, table_name_, table_col_, item_attrs_, begin, end, log_level_,
                      must_output_);
  }
}
std::string SeRecoDebugLogEnricher::GetStringFromItemAttr(MutableRecoContextInterface *context,
                                                          const CommonRecoResult &result,
                                                          const ItemAttr *attr) {
  std::string ret_str;
  switch (attr->value_type) {
    case AttrType::INT:
      if (auto val = context->GetIntItemAttr(result, attr)) {
        ret_str = std::to_string(*val);
      }
      break;
    case AttrType::FLOAT:
      if (auto val = context->GetDoubleItemAttr(result, attr)) {
        ret_str = std::to_string(*val);
      }
      break;
    case AttrType::STRING:
      if (auto val = context->GetStringItemAttr(result, attr)) {
        ret_str = val->data();
      }
      break;
    case AttrType::STRING_LIST:
      if (auto val = context->GetStringListItemAttr(result, attr)) {
        ret_str = absl::StrJoin(*val, ",");
      }
      break;
    case AttrType::FLOAT_LIST:
      if (auto val = context->GetDoubleListItemAttr(result, attr)) {
        ret_str = absl::StrJoin(*val, ",");
      }
      break;
    case AttrType::INT_LIST:
      if (auto val = context->GetIntListItemAttr(result, attr)) {
        ret_str = absl::StrJoin(*val, ",");
      }
      break;
    default:
      // Should never reach here!
      CL_LOG(ERROR) << "SeRecoDebugLogEnricher::Enricher error: unsupported item attr type: "
                    << static_cast<int>(attr->value_type);
  }
  return ret_str;
}

std::string SeRecoDebugLogEnricher::GetStringFromCommonAttr(MutableRecoContextInterface *context,
                                                            const std::string &attr_name) {
  std::string ret_str;
  auto *attr = context->GetCommonAttrAccessor(attr_name);
  switch (attr->value_type) {
    case AttrType::INT:
      if (auto val = attr->GetIntValue()) {
        ret_str = std::to_string(*val);
      }
      break;
    case AttrType::FLOAT:
      if (auto val = attr->GetDoubleValue()) {
        ret_str = std::to_string(*val);
      }
      break;
    case AttrType::STRING:
      if (auto val = attr->GetStringValue()) {
        ret_str = val->data();
      }
      break;
    case AttrType::STRING_LIST:
      if (auto val = attr->GetStringListValue()) {
        ret_str = absl::StrJoin(*val, ",");
      }
      break;
    case AttrType::FLOAT_LIST:
      if (auto val = attr->GetDoubleListValue()) {
        ret_str = absl::StrJoin(*val, ",");
      }
      break;
    case AttrType::INT_LIST:
      if (auto val = attr->GetIntListValue()) {
        ret_str = absl::StrJoin(*val, ",");
      }
      break;
    default:
      // Should never reach here!
      CL_LOG(ERROR) << "SeRecoDebugLogEnricher::Enricher error: unsupported common attr type: "
                    << static_cast<int>(attr->value_type);
  }
  return ret_str;
}

void SeRecoDebugLogEnricher::PrintCommonAttrInfo(
    MutableRecoContextInterface *context, const std::shared_ptr<kuaishou::ds::search::DebugLogCommon> &ctx,
    int64_t output_mask) {
  if (nullptr == ctx) return;
  for (const auto &common_attr : common_attrs_) {
    const auto &key = GetStringFromCommonAttr(context, common_attr);
    for (const auto &[output_bit, handler_func] : this->dispatch_table_) {
      if (output_mask & output_bit) {
        handler_func(this, ctx, this->stage_, DebugLogElemType::COMMON, key, common_attr, {}, "");
      }
    }
  }
}
void SeRecoDebugLogEnricher::PrintItemAttrInfo(
    MutableRecoContextInterface *context, const std::shared_ptr<kuaishou::ds::search::DebugLogCommon> &ctx,
    int64_t output_mask, const std::string &table_name, const std::string &table_col,
    const std::vector<std::string> &item_attrs, RecoResultConstIter begin, RecoResultConstIter end,
    const int &log_level = 3, const int &must_output = 0) {
  if (nullptr == ctx) return;
  auto size = std::distance(begin, end);
  if (table_name == "ArchInfo") {
    for (const auto &[output_bit, handler_func] : this->dispatch_table_) {
      if (output_mask & output_bit) {
        if (output_bit == OUTPUT_ONLINE && size == 0) continue;
        handler_func(this, ctx, this->stage_, DebugLogElemType::COMMON, std::to_string(size), "item数量", {},
                     "");
      }
    }
  }
  if (ctx->LogLevel() < log_level) {
    output_mask &= must_output;
    if (!output_mask) return;
  }
  // make table
  if (output_mask & OUTPUT_OFFLINE) {
    ctx->AddOfflineDebugLogTable(stage_, table_name, table_col);
  }
  if (output_mask & OUTPUT_TRACE) {
    ctx->AddTraceLogTable(stage_, table_name, table_col);
  }
  if (output_mask & OUTPUT_ONLINE) {
    ctx->AddOnlineDebugLogTable(stage_, table_name, table_col);
  }

  std::vector<ItemAttr *> item_attr_accessors;
  item_attr_accessors.reserve(item_attrs.size());
  for (const auto &name : item_attrs) {
    item_attr_accessors.push_back(context->GetItemAttrAccessor(name));
  }

  auto stop = end;
  if (!ctx->IsOfflineDebug() && ctx->IsInternal() && size > 1000) {
    handle_trace_output(ctx, this->stage_, DebugLogElemType::COMMON, "截断", "超出1000上限", {}, "");
    stop = std::next(begin, 1000);
  }
  size_t idx = 0;
  std::for_each(begin, stop, [&](const CommonRecoResult &result) {
    std::string key, status;
    // sdk 需要额外处理前两个 ..., 第一个是主键, 第二个是主键描述
    if (item_attr_accessors.size() > 0) {
      key = GetStringFromItemAttr(context, result, item_attr_accessors[0]);
    }
    if (item_attr_accessors.size() > 1) {
      status = GetStringFromItemAttr(context, result, item_attr_accessors[1]);
    }
    std::vector<std::string> contents;
    contents.reserve(item_attr_accessors.size());
    for (size_t i = 2; i < item_attr_accessors.size(); ++i) {
      auto *attr_accessor = item_attr_accessors[i];
      const std::string &value = GetStringFromItemAttr(context, result, item_attr_accessors[i]);
      contents.emplace_back(std::move(value));
    }
    contents.emplace_back(std::to_string(idx++));
    for (const auto &[output_bit, handler_func] : this->dispatch_table_) {
      if (output_mask & output_bit) {
        handler_func(this, ctx, this->stage_, this->type_, key, status, contents, table_name);
      }
    }
  });
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, SeRecoDebugLogEnricher, SeRecoDebugLogEnricher)

}  // namespace platform
}  // namespace ks
