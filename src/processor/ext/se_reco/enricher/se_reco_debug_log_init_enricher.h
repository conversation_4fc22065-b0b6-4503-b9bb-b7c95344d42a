#pragma once
#include <memory>
#include <string>
#include <unordered_map>
#include <map>
#include <unordered_set>
#include <vector>
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "se/online_service/protos/debug_log.pb.h"
#include "se/txt2vid_se/combo_search/search/utils/debug_log_common.h"

namespace ks {
namespace platform {
class SeRecoDebugLogInitEnricher : public CommonRecoBaseEnricher {
  using DebugLogSourceType = kuaishou::search::platform::DebugLogSourceType;

 public:
  SeRecoDebugLogInitEnricher() {}
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    log_source_str_ = config()->GetString("log_source", "unknown");
    auto it = log_source_type_map_.find(log_source_str_);
    if (it == log_source_type_map_.end()) {
      LOG(ERROR) << "SeRecoDebugLogInitEnricher init failed! 'log_source' must be set.";
      return false;
    }
    log_source_ = it->second;
    is_test_ = config()->GetBoolean("is_test", false);
    return true;
  }
  bool is_test_{false};
  std::string log_source_str_;
  DebugLogSourceType log_source_{DebugLogSourceType::T_DEFAULT};
  std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<std::map<std::string, bool>>>>
      trace_log_switch_by_kconf_;
  static inline const std::unordered_map<std::string, DebugLogSourceType> log_source_type_map_{
      {"default", DebugLogSourceType::T_DEFAULT},     {"inner_sug", DebugLogSourceType::T_INNER_SUG},
      {"external", DebugLogSourceType::T_EXTERNA},    {"inner_guess", DebugLogSourceType::T_INNER_GUESS},
      {"inner_box", DebugLogSourceType::T_INNER_BOX}, {"goods", DebugLogSourceType::T_GOODS}};
};
}  // namespace platform
}  // namespace ks
