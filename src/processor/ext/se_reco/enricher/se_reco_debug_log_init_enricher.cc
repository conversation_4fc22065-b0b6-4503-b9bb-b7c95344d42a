#include "dragon/src/processor/ext/se_reco/enricher/se_reco_debug_log_init_enricher.h"
#include <algorithm>
#include <iterator>
#include <map>
#include <string>
#include <utility>
#include <vector>
#include "kconf/kconf.h"
#include "se/online_service/protos/debug_log.pb.h"
namespace ks {
namespace platform {

void SeRecoDebugLogInitEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                        RecoResultConstIter end) {
  auto ctx = std::make_shared<kuaishou::ds::search::DebugLogCommon>();
  const auto uid = context->GetUserId();
  const auto &did = context->GetDeviceId();
  const auto &session_id = context->GetRequestId();
  const auto &query = context->GetStringCommonAttr("query").value_or("");
  auto log_level = context->GetIntCommonAttr("logLevel").value_or(0);
  ctx->InitDebugLog(log_source_, uid, session_id, did, std::string(query), std::string(query), 0, log_level);
  auto parser = [](const std::string &key, std::string *val) -> bool {
    *val = key;
    return true;
  };
  ks::infra::KeyParser<std::string> key_parser = parser;
  auto default_vals = std::make_shared<std::map<std::string, bool>>();
  trace_log_switch_by_kconf_ =
      ks::infra::KConf().GetMap("se.ziyasort.trace_debug_switch", default_vals, key_parser);
  auto std_map_kconf_ptr = trace_log_switch_by_kconf_->Get();
  auto it = std_map_kconf_ptr->find(log_source_str_);
  if (it != std_map_kconf_ptr->end()) {
    ctx->SetLogSwitchByKconf((*it).second);
  }
  ctx->SetIsTest(is_test_);
  if (log_level > 0) {
    ctx->SetIsOfflineDebug(true);
  }
  int64 need_debug_log = ctx->IsOfflineDebug() || ctx->IsOnlineDebug() || ctx->IsInternal() || ctx->IsTest();
  if (ctx->IsInternal() || ctx->IsTest()) {
    auto *request = context->GetRequest();
    if (nullptr != request) {
      std::string req_string;
      google::protobuf::util::MessageToJsonString(*request, &req_string);
      ctx->SetTraceLogRequest(req_string);
    }
  }
  context->SetIntCommonAttr("need_debug_log", need_debug_log);
  if (context->HasCommonAttr("se_debug_log_ptr")) {
    CL_LOG(ERROR) << "SeRecoDebugLogInitEnricher default common ptr name `se_debug_log_ptr` already exist!";
    // return;
  }
  context->SetPtrCommonAttr("se_debug_log_ptr", ctx);
}
typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, SeRecoDebugLogInitEnricher, SeRecoDebugLogInitEnricher)

}  // namespace platform
}  // namespace ks
