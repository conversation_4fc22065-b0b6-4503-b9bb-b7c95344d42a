#include "dragon/src/processor/ext/se_reco/enricher/se_reco_trace_debug_log_start_enricher.h"
#include <algorithm>
#include <limits>

#include "dragon/src/interop/util.h"

namespace ks {
namespace platform {

void SeRecoTraceDebugLogStartEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                              RecoResultConstIter end) {
  // 1. 拿到 ptr
  std::shared_ptr<kuaishou::ds::search::DebugLogCommon> ctx{
      context->GetMutablePtrCommonAttr<kuaishou::ds::search::DebugLogCommon>("se_debug_log_ptr"),
      [](auto) {}};
  if (ctx == nullptr) {
    CL_LOG(ERROR) << "ctx is nullptr.";
    return;
  }
  if (!(ctx->IsOfflineDebug() || ctx->IsOnlineDebug() || ctx->IsInternal() || ctx->IsTest())) {
    return;
  }
  all_items_.clear();
  for (auto it = begin; it != end; ++it) {
    all_items_.push_back(it->item_key);
  }
  if (!must_output_ && ctx->LogLevel() < log_level_) return;
  AppendItemAttrs(context, all_items_);
}

void SeRecoTraceDebugLogStartEnricher::AppendItemAttrs(MutableRecoContextInterface *context,
                                                       const std::vector<uint64> &item_keys) {
  for (const uint64 item_key : item_keys) {
    context->AppendIntListCommonAttr(stage_, item_key);
    for (size_t i = 0; i < items_.size(); ++i) {
      const auto &item_attr = items_[i];
      const auto &pack_attr = pack_to_[i];
      auto attr_type = context->GetItemAttrType(item_attr);
      switch (attr_type) {
        case AttrType::INT: {
          auto p = context->GetIntItemAttr(item_key, item_attr);
          if (p) {
            context->AppendIntListCommonAttr(pack_attr, *p);
          } else {
            context->AppendIntListCommonAttr(pack_attr, 0);
          }
          break;
        }
        case AttrType::FLOAT: {
          auto p = context->GetDoubleItemAttr(item_key, item_attr);
          if (p) {
            context->AppendDoubleListCommonAttr(pack_attr, *p);
          } else {
            context->AppendDoubleListCommonAttr(pack_attr, 0.0);
          }
          break;
        }
        case AttrType::STRING: {
          auto p = context->GetStringItemAttr(item_key, item_attr);
          if (p) {
            context->AppendStringListCommonAttr(pack_attr, std::string(p->data(), p->size()));
          } else {
            context->AppendStringListCommonAttr(pack_attr, "");
          }
          break;
        }
        default:
          CL_LOG(ERROR) << "cannot get int/double/string list attr of " << item_attr
                        << " for item_key: " << item_key;
          break;
      }
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, SeRecoTraceDebugLogStartEnricher, SeRecoTraceDebugLogStartEnricher)

}  // namespace platform
}  // namespace ks
