#include "dragon/src/processor/ext/se_reco/enricher/se_reco_trace_debug_log_end_enricher.h"
#include <algorithm>
#include <cstdint>
#include "folly/container/F14Set.h"

#include "base/common/basic_types.h"

namespace ks {
namespace platform {

void SeRecoTraceDebugLogEndEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                            RecoResultConstIter end) {
  std::shared_ptr<kuaishou::ds::search::DebugLogCommon> ctx{
      context->GetMutablePtrCommonAttr<kuaishou::ds::search::DebugLogCommon>("se_debug_log_ptr"),
      [](auto) {}};
  if (ctx == nullptr) {
    CL_LOG(ERROR) << "ctx is nullptr.";
    return;
  }
  int64_t output_mask = OUTPUT_NONE;
  output_mask |= ctx->IsInternal() || ctx->IsTest() ? OUTPUT_TRACE : 0;
  output_mask |= ctx->IsOnlineDebug() ? OUTPUT_ONLINE : 0;
  output_mask |= ctx->IsOfflineDebug() ? OUTPUT_OFFLINE : 0;
  if (output_mask == OUTPUT_NONE) {
    return;
  }
  const auto &old_item_key_vec = context->GetIntListCommonAttr(stage_).value_or(absl::Span<const int64>());
  folly::F14FastMap<uint64, uint64> item_key_map;
  auto item_key_size = std::distance(begin, end);
  item_key_map.reserve(item_key_size);
  size_t idx = 0;
  for (auto it = begin; it != end; ++it) {
    const auto item_key = it->item_key;
    item_key_map.insert({it->item_key, idx++});
  }
  auto old_item_size = old_item_key_vec.size();
  auto old_item_size_str = std::to_string(old_item_size);
  if (output_mask & OUTPUT_ONLINE) {
    ctx->AddOnlineDebugLog(stage_key_, kuaishou::search::platform::DebugLogElemType::COMMON,
                           old_item_size_str, "执行前item数量", {}, "");
  }
  if (output_mask & OUTPUT_TRACE) {
    ctx->AddTraceLog(stage_key_, kuaishou::search::platform::DebugLogElemType::COMMON, old_item_size_str,
                     "执行前item数量", {}, "");
  }
  if (output_mask & OUTPUT_OFFLINE) {
    ctx->AddOfflineDebugLog(stage_key_, kuaishou::search::platform::DebugLogElemType::COMMON,
                            old_item_size_str, "执行前item数量", {}, "");
  }
  auto item_key_size_str = std::to_string(item_key_size);
  if (output_mask & OUTPUT_ONLINE) {
    ctx->AddOnlineDebugLog(stage_key_, kuaishou::search::platform::DebugLogElemType::COMMON,
                           item_key_size_str, "剩余item数量", {}, "");
  }
  if (output_mask & OUTPUT_TRACE) {
    ctx->AddTraceLog(stage_key_, kuaishou::search::platform::DebugLogElemType::COMMON, item_key_size_str,
                     "剩余item数量", {}, "");
  }
  if (output_mask & OUTPUT_OFFLINE) {
    ctx->AddOfflineDebugLog(stage_key_, kuaishou::search::platform::DebugLogElemType::COMMON,
                            item_key_size_str, "剩余item数量", {}, "");
  }
  diff_items_.clear();
  item_key_offset_.clear();
  if (ctx->LogLevel() < log_level_) {
    output_mask &= must_output_;
    if (!output_mask) return;
  }
  if (ctx->IsInternal() || ctx->IsTest()) {
    ctx->AddTraceLogTable(stage_key_, stage_key_, table_col_);
  }
  if (ctx->IsOfflineDebug()) {
    ctx->AddOfflineDebugLogTable(stage_key_, stage_key_, table_col_);
  }
  if (ctx->IsOnlineDebug()) {
    ctx->AddOnlineDebugLogTable(stage_key_, stage_key_, table_col_);
  }
  if (trace_type_ == "item") {
    for (size_t i = 0; i < old_item_key_vec.size(); ++i) {
      uint64_t item_key = static_cast<uint64_t>(old_item_key_vec[i]);
      if (item_key_map.find(item_key) == item_key_map.end()) {
        diff_items_.emplace_back(item_key);
        item_key_offset_.emplace_back(i);
      }
    }
    auto item_size = std::to_string(diff_items_.size());
    if (output_mask & OUTPUT_ONLINE) {
      ctx->AddOnlineDebugLog(stage_key_, kuaishou::search::platform::DebugLogElemType::COMMON, item_size,
                             "命中item数量", {}, "");
    }
    if (output_mask & OUTPUT_TRACE) {
      ctx->AddTraceLog(stage_key_, kuaishou::search::platform::DebugLogElemType::COMMON, item_size,
                       "命中item数量", {}, "");
    }
    if (output_mask & OUTPUT_OFFLINE) {
      ctx->AddOfflineDebugLog(stage_key_, kuaishou::search::platform::DebugLogElemType::COMMON, item_size,
                              "命中item数量", {}, "");
    }
    PrintItemAttrs(context, ctx, diff_items_, item_key_offset_, output_mask);
  } else if (trace_type_ == "attr") {
    size_t cnt = 0;
    std::vector<CommonAttr *> common_attr_accessors;
    common_attr_accessors.reserve(pack_attrs_.size());
    for (const auto &name : pack_attrs_) {
      common_attr_accessors.push_back(context->GetCommonAttrAccessor(name));
    }
    const double epsilon = 1e-6;
    const double absolute_epsilon = 1e-12;

    for (size_t i = 0; i < old_item_key_vec.size(); ++i) {
      uint64_t item_key = static_cast<uint64_t>(old_item_key_vec[i]);
      int64 new_item_idx = -1;
      if (item_key_map.count(item_key)) {
        new_item_idx = item_key_map[item_key];
      }
      std::vector<std::string> contentList;
      std::string key, status;
      bool has_diff = false;
      std::string diff_dix;
      if (new_item_idx != i) {
        has_diff = true;
        diff_dix = std::move(std::to_string(i) + "->" + std::to_string(new_item_idx));
      } else {
        diff_dix = std::move(std::to_string(i));
      }
      contentList.reserve(common_attr_accessors.size());
      for (size_t j = 0; j < common_attr_accessors.size(); ++j) {
        auto *attr = common_attr_accessors[j];
        std::string ret;
        switch (attr->value_type) {
          case AttrType::STRING_LIST:
            if (auto val = attr->GetStringListValue()) {
              if ((*val).size() > i) {
                ret = (*val).at(i).data();
                const auto &new_data = context->GetStringItemAttr(item_key, item_attrs_[j]).value_or("");
                if (ret != new_data) {
                  has_diff = true;
                  ret = ret + "->" + new_data.data();
                }
              }
            }
            break;
          case AttrType::FLOAT_LIST:
            if (auto val = attr->GetDoubleListValue()) {
              if ((*val).size() > i) {
                auto old_data = (*val).at(i);
                const auto &new_data = context->GetDoubleItemAttr(item_key, item_attrs_[j]).value_or(0.0);
                if (std::abs(old_data - new_data) >
                    std::max(absolute_epsilon, epsilon * std::max(std::abs(old_data), std::abs(new_data)))) {
                  has_diff = true;
                  ret = std::to_string(old_data) + "->" + std::to_string(new_data);
                } else {
                  ret = std::to_string(old_data);
                }
              }
            }
            break;
          case AttrType::INT_LIST:
            if (auto val = attr->GetIntListValue()) {
              if ((*val).size() > i) {
                auto old_data = (*val).at(i);
                const auto &new_data = context->GetIntItemAttr(item_key, item_attrs_[j]).value_or(0);
                if (old_data != new_data) {
                  has_diff = true;
                  ret = std::to_string(old_data) + "->" + std::to_string(new_data);
                } else {
                  ret = std::to_string(old_data);
                }
              }
            }
            break;
          default:
            // Should never reach here!
            CL_LOG(ERROR)
                << "SeRecoTraceDebugLogEndEnricher::CheckDiffAttr error: unsupported item attr type: "
                << static_cast<int>(attr->value_type);
        }
        if (has_diff) {
          VLOG(3) << "has diff for attr: " << item_attrs_[j] << "value:" << ret << "for idx:" << diff_dix;
        }
        if (j == 0) {
          key = ret;
        } else if (j == 1) {
          status = ret;
        } else {
          contentList.emplace_back(std::move(ret));
        }
      }

      if (has_diff) {
        contentList.emplace_back(std::to_string(cnt++));
        contentList.emplace_back(std::move(diff_dix));
        if (output_mask & OUTPUT_ONLINE) {
          VLOG(3) << "Trace online log for attr:" << stage_key_ << key << status;
          ctx->AddOnlineDebugLog(stage_key_, kuaishou::search::platform::DebugLogElemType::QUERY, key, status,
                                 contentList, stage_key_);
        }
        if (output_mask & OUTPUT_TRACE) {
          VLOG(3) << "Trace log for attr:" << stage_key_ << key << status;
          ctx->AddTraceLog(stage_key_, kuaishou::search::platform::DebugLogElemType::QUERY, key, status,
                           contentList, stage_key_);
        }
        if (output_mask & OUTPUT_OFFLINE) {
          VLOG(3) << "Trace Offline log for attr:" << stage_key_ << key << status;
          ctx->AddOfflineDebugLog(stage_key_, kuaishou::search::platform::DebugLogElemType::QUERY, key,
                                  status, contentList, stage_key_);
        }
      }
    }
    std::string cnt_str = std::to_string(cnt);
    if (output_mask & OUTPUT_ONLINE) {
      ctx->AddOnlineDebugLog(stage_key_, kuaishou::search::platform::DebugLogElemType::COMMON, cnt_str,
                             "存在item特征变更的数量", {}, "");
    }
    if (output_mask & OUTPUT_TRACE) {
      ctx->AddTraceLog(stage_key_, kuaishou::search::platform::DebugLogElemType::COMMON, cnt_str,
                       "存在item特征变更的数量", {}, "");
    }
    if (output_mask & OUTPUT_OFFLINE) {
      ctx->AddOfflineDebugLog(stage_key_, kuaishou::search::platform::DebugLogElemType::COMMON, cnt_str,
                              "存在item特征变更的数量", {}, "");
    }
  }
}

std::string SeRecoTraceDebugLogEndEnricher::GetStringFromPackAttr(uint64 offset, const CommonAttr *attr) {
  std::string ret_str;
  switch (attr->value_type) {
    case AttrType::STRING_LIST:
      if (auto val = attr->GetStringListValue()) {
        if ((*val).size() > offset) {
          ret_str = (*val).at(offset).data();
        }
      }
      break;
    case AttrType::FLOAT_LIST:
      if (auto val = attr->GetDoubleListValue()) {
        if ((*val).size() > offset) {
          ret_str = std::to_string((*val).at(offset));
        }
      }
      break;
    case AttrType::INT_LIST:
      if (auto val = attr->GetIntListValue()) {
        if ((*val).size() > offset) {
          ret_str = std::to_string((*val).at(offset));
        }
      }
      break;
    default:
      // Should never reach here!
      CL_LOG(ERROR)
          << "SeRecoTraceDebugLogEndEnricher::GetStringFromPackAttr error: unsupported item attr type: "
          << static_cast<int>(attr->value_type);
  }
  return ret_str;
}

void SeRecoTraceDebugLogEndEnricher::PrintItemAttrs(
    MutableRecoContextInterface *context, const std::shared_ptr<kuaishou::ds::search::DebugLogCommon> &ctx,
    const std::vector<uint64> &item_keys, const std::vector<uint64> &item_key_offset,
    const int64_t output_mask) {
  std::vector<CommonAttr *> common_attr_accessors;
  common_attr_accessors.reserve(pack_attrs_.size());
  for (const auto &name : pack_attrs_) {
    common_attr_accessors.push_back(context->GetCommonAttrAccessor(name));
  }
  for (size_t i = 0; i < item_keys.size(); ++i) {
    const auto item_key = item_keys[i];
    const auto offset = item_key_offset[i];
    std::string key, status;
    if (common_attr_accessors.size() > 0) {
      key = GetStringFromPackAttr(offset, common_attr_accessors[0]);
    }
    if (common_attr_accessors.size() > 1) {
      status = GetStringFromPackAttr(offset, common_attr_accessors[1]);
    }
    std::vector<std::string> contents;
    contents.reserve(pack_attrs_.size());
    for (size_t i = 2; i < pack_attrs_.size(); ++i) {
      const auto &value = GetStringFromPackAttr(offset, common_attr_accessors[i]);
      contents.emplace_back(std::move(value));
    }
    contents.emplace_back(std::to_string(i));
    if (output_mask & OUTPUT_ONLINE) {
      VLOG(3) << "Trace online log:" << stage_key_ << key << status;
      ctx->AddOnlineDebugLog(stage_key_, kuaishou::search::platform::DebugLogElemType::QUERY, key, status,
                             contents, stage_key_);
    }
    if (output_mask & OUTPUT_TRACE) {
      VLOG(3) << "Trace log:" << stage_key_ << key << status;
      ctx->AddTraceLog(stage_key_, kuaishou::search::platform::DebugLogElemType::QUERY, key, status, contents,
                       stage_key_);
    }
    if (output_mask & OUTPUT_OFFLINE) {
      VLOG(3) << "Trace Offline log:" << stage_key_ << key << status;
      ctx->AddOfflineDebugLog(stage_key_, kuaishou::search::platform::DebugLogElemType::QUERY, key, status,
                              contents, stage_key_);
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, SeRecoTraceDebugLogEndEnricher, SeRecoTraceDebugLogEndEnricher)

}  // namespace platform
}  // namespace ks
