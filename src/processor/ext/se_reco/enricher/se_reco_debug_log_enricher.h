#pragma once
#include <algorithm>
#include <functional>
#include <memory>
#include <string>
#include <unordered_map>
#include <map>
#include <unordered_set>
#include <vector>
#include "absl/strings/str_join.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "se/online_service/protos/debug_log.pb.h"
#include "se/txt2vid_se/combo_search/search/utils/debug_log_common.h"
namespace ks {
namespace platform {
class SeRecoDebugLogEnricher : public CommonRecoBaseEnricher {
  using DebugLogElemType = kuaishou::search::platform::DebugLogElemType;

 public:
  enum OutputType : int64_t {
    OUTPUT_NONE = 0,
    OUTPUT_ONLINE = 1 << 0,   // 值为 1
    OUTPUT_TRACE = 1 << 1,    // 值为 2
    OUTPUT_OFFLINE = 1 << 2,  // 值为 4
  };
  SeRecoDebugLogEnricher() {}
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    stage_ = config()->GetString("stage", "");
    if ("" == stage_) {
      CL_LOG(ERROR) << "SeRecoDebugLogEnricher init failed! 'stage' must be set.";
      return false;
    }
    std::transform(stage_.begin(), stage_.end(), stage_.begin(),
                   [](unsigned char c) { return std::toupper(c); });
    std::vector<std::string> stage_list;
    base::SplitString(stage_, "|", &stage_list);

    const std::string &type = config()->GetString("type", "");
    if (type_map_.find(type) == type_map_.end()) {
      CL_LOG(ERROR) << "SeRecoDebugLogEnricher init failed! type:" << type << "not exist.";
      return false;
    }
    type_ = type_map_.at(type);
    must_output_ = config()->GetInt("must_output", 0);
    log_level_ = config()->GetInt("log_level", 3);

    const auto *commons = config()->Get("common_attrs");
    if (commons && !RecoUtil::ExtractStringListFromJsonConfig(commons, &common_attrs_)) {
      CL_LOG(ERROR) << "SeRecoDebugLogEnricher init failed! 'commons' should be a string map.";
    }

    const auto *items = config()->Get("item_attrs");
    if (items && !RecoUtil::ExtractStringListFromJsonConfig(items, &item_attrs_)) {
      CL_LOG(ERROR) << "SeRecoDebugLogEnricher init failed! 'items' should be a string map.";
    }
    table_name_ = config()->GetString("table_name", "");
    if ("" == table_name_ && items) {
      CL_LOG(ERROR) << "SeRecoDebugLogEnricher init failed! 'table_name_' must be set.";
      return false;
    }
    table_col_ = config()->GetString("table_col", "");
    if ("" == table_col_ && items) {
      CL_LOG(ERROR) << "SeRecoDebugLogEnricher init failed! 'table_col_' must be set.";
      return false;
    }
    table_col_ = "类型|" + table_col_ + "|idx";
    return true;
  }
  std::string GetStringFromCommonAttr(MutableRecoContextInterface *context, const std::string &attr_name);
  std::string GetStringFromItemAttr(MutableRecoContextInterface *context, const CommonRecoResult &result,
                                    const ItemAttr *attr);

  void PrintItemAttrInfo(MutableRecoContextInterface *context,
                         const std::shared_ptr<kuaishou::ds::search::DebugLogCommon> &ctx,
                         int64_t output_mask, const std::string &table_name, const std::string &table_col,
                         const std::vector<std::string> &item_attrs, RecoResultConstIter begin,
                         RecoResultConstIter end, const int &log_level, const int &must_output);
  void PrintCommonAttrInfo(MutableRecoContextInterface *context,
                           const std::shared_ptr<kuaishou::ds::search::DebugLogCommon> &ctx,
                           int64_t output_mask);

  void handle_none_output(const std::shared_ptr<kuaishou::ds::search::DebugLogCommon> &ctx,
                          const std::string &stage_key,
                          const kuaishou::search::platform::DebugLogElemType type, const std::string &key,
                          const std::string &status, const std::vector<std::string> &contentList,
                          const std::string &table_name) {
    CL_LOG(ERROR) << "SeRecoDebugLogEnricher enricherfail! none output!";
  }
  void handle_online_output(const std::shared_ptr<kuaishou::ds::search::DebugLogCommon> &ctx,
                            const std::string &stage_key,
                            const kuaishou::search::platform::DebugLogElemType type, const std::string &key,
                            const std::string &status, const std::vector<std::string> &contentList,
                            const std::string &table_name) {
    VLOG(3) << "AddOnlineDebugLog" << key << status << stage_;
    ctx->AddOnlineDebugLog(stage_key, type, key, status, contentList, table_name);
  }
  void handle_offline_output(const std::shared_ptr<kuaishou::ds::search::DebugLogCommon> &ctx,
                             const std::string &stage_key,
                             const kuaishou::search::platform::DebugLogElemType type, const std::string &key,
                             const std::string &status, const std::vector<std::string> &contentList,
                             const std::string &table_name) {
    VLOG(3) << "AddOfflineDebugLog" << key << status << stage_;
    ctx->AddOfflineDebugLog(stage_key, type, key, status, contentList, table_name);
  }
  void handle_trace_output(const std::shared_ptr<kuaishou::ds::search::DebugLogCommon> &ctx,
                           const std::string &stage_key,
                           const kuaishou::search::platform::DebugLogElemType type, const std::string &key,
                           const std::string &status, const std::vector<std::string> &contentList,
                           const std::string &table_name) {
    VLOG(3) << "AddTraceLog" << key << status << stage_;
    ctx->AddTraceLog(stage_key, type, key, status, contentList, table_name);
  }
  int64_t log_level_ = 0;
  int64_t must_output_ = 0;
  std::string stage_;
  std::string table_col_;
  std::string table_name_;
  DebugLogElemType type_;
  std::vector<std::string> common_attrs_;
  std::vector<std::string> item_attrs_;
  const std::unordered_map<
      OutputType,
      std::function<void(ks::platform::SeRecoDebugLogEnricher *,
                         const std::shared_ptr<kuaishou::ds::search::DebugLogCommon> &, const std::string &,
                         const kuaishou::search::platform::DebugLogElemType, const std::string &,
                         const std::string &, const std::vector<std::string> &, const std::string &)>>
      dispatch_table_ = {{OUTPUT_NONE, &ks::platform::SeRecoDebugLogEnricher::handle_none_output},
                         {OUTPUT_ONLINE, &ks::platform::SeRecoDebugLogEnricher::handle_online_output},
                         {OUTPUT_OFFLINE, &ks::platform::SeRecoDebugLogEnricher::handle_offline_output},
                         {OUTPUT_TRACE, &ks::platform::SeRecoDebugLogEnricher::handle_trace_output}};
  static inline const std::unordered_map<std::string, OutputType> output_map_{
      {"online", OUTPUT_ONLINE}, {"offline", OUTPUT_OFFLINE}, {"trace", OUTPUT_TRACE}};

  static inline const std::unordered_map<std::string, DebugLogElemType> type_map_{
      {"UNKNOWN_DEFAULT", DebugLogElemType::UNKNOWN_DEFAULT},
      {"ALADDIN_KBOX", DebugLogElemType::ALADDIN_KBOX},
      {"PHOTO", DebugLogElemType::PHOTO},
      {"GOODS", DebugLogElemType::GOODS},
      {"POI", DebugLogElemType::POI},
      {"QUERY", DebugLogElemType::QUERY}};
};
}  // namespace platform
}  // namespace ks
