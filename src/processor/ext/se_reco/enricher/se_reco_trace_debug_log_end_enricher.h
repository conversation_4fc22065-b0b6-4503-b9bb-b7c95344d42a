#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>
#include <unordered_set>
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "se/online_service/protos/debug_log.pb.h"
#include "se/txt2vid_se/combo_search/search/utils/debug_log_common.h"
namespace ks {
namespace platform {

class SeRecoTraceDebugLogEndEnricher : public CommonRecoBaseEnricher {
 public:
  enum OutputType : int64_t {
    OUTPUT_NONE = 0,
    OUTPUT_ONLINE = 1 << 0,   // 值为 1
    OUTPUT_TRACE = 1 << 1,    // 值为 2
    OUTPUT_OFFLINE = 1 << 2,  // 值为 4
  };
  SeRecoTraceDebugLogEndEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    stage_key_ = config()->GetString("stage", "");
    if ("" == stage_key_) {
      CL_LOG(ERROR) << "SeRecoTraceDebugLogStartEnricher init failed! 'stage' must be set.";
      return false;
    }
    stage_ = stage_key_ + "__INNER__";
    must_output_ = config()->GetInt("must_output", 2);
    log_level_ = config()->GetInt("log_level", 3);
    trace_type_ = config()->GetString("trace_type", "item");
    if (!trace_type_set_.count(trace_type_)) {
      CL_LOG(ERROR) << "SeRecoTraceDebugLogStartEnricher init failed! 'trace_type' must be 'item' or 'attr'.";
      return false;
    }
    const auto *items = config()->Get("item_attrs");
    if (items && !RecoUtil::ExtractStringListFromJsonConfig(items, &item_attrs_)) {
      CL_LOG(ERROR) << "SeRecoTraceDebugLogStartEnricher init failed! 'items' should be a string list.";
    }
    const auto *pack_attrs = config()->Get("pack_attrs");
    if (pack_attrs && !RecoUtil::ExtractStringListFromJsonConfig(pack_attrs, &pack_attrs_)) {
      CL_LOG(ERROR) << "SeRecoTraceDebugLogStartEnricher init failed! 'items' should be a string list.";
      return false;
    }
    table_col_ = "类型|";
    for (const auto &attr : item_attrs_) {
      table_col_ += attr + "|";
    }

    if (trace_type_ == "item") {
      table_col_ += "idx";

    } else {
      table_col_ += "idx|diff_idx";
    }
    return true;
  }
  void PrintItemAttrs(MutableRecoContextInterface *context,
                      const std::shared_ptr<kuaishou::ds::search::DebugLogCommon> &ctx,
                      const std::vector<uint64> &item_keys, const std::vector<uint64> &item_key_offset,
                      const int64_t output_mask);
  std::string GetStringFromPackAttr(uint64 offset, const CommonAttr *attr);

 private:
  int64_t must_output_ = 0;
  int64_t log_level_ = 0;
  std::string trace_type_;
  std::unordered_set<std::string> trace_type_set_{"item", "attr"};
  std::string stage_key_;
  std::string stage_;
  std::string table_col_;

  std::vector<ItemAttr *> item_attr_accessors_;
  std::vector<uint64> diff_items_;
  std::vector<uint64> item_key_offset_;
  std::vector<std::string> item_attrs_;
  std::vector<std::string> pack_attrs_;
};

}  // namespace platform
}  // namespace ks
