#include <utility>

#include "dragon/src/processor/ext/se_reco/enricher/se_reco_dragon_attrs_to_ziya_enricher_v2.h"
#include "dragon/src/processor/ext/se_reco/util/se_reco_common_util.h"
#include "se/txt2vid_se/ziya_sort/src/base/specific_context.h"

namespace ks {
namespace platform {

void SeRecoDragonAttrsToZiyaV2Enricher::Enrich(MutableRecoContextInterface *context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
  std::shared_ptr<ks::search::ziya_sort::Context> ctx(
      context->GetMutablePtrCommonAttr<ks::search::ziya_sort::Context>("ZiyaContextAttrKey"), [](auto) {});
  if (ctx == nullptr) {
    LOG(ERROR) << "ctx is nullptr.";
    return;
  }
  int64_t sugg_duration_opt_2024_q3_reverse =
      context->GetIntCommonAttr("sugg_duration_opt_2024_q3_reverse").value_or(0);
  if ((sugg_duration_opt_2024_q3_reverse & 8) != 0) {
    ctx->reset();
  }
  SetBasicAttrs(context, ctx);
  SetCommonAttrs(context, ctx);
  SetIconConfig(ctx);
  CreateZiyaItems(context, ctx, begin, end);
  SetItemExtraAttrs(context, ctx, begin, end, input_extra_info_item_attrs_, true);
  SetItemExtraAttrs(context, ctx, begin, end, input_extra_params_item_attr_, false);
  // 获取所有 json 的 ab 实验结果
  ctx->req_all_ab_exp_values();
  /*
    SetBasicAttrs(context);
    SetCommonAttrs(context);
    SetIconConfig();
    CreateZiyaItems(context, begin, end);
    SetItemExtraAttrs(context, begin, end, input_extra_info_item_attrs_, true);
    SetItemExtraAttrs(context, begin, end, input_extra_params_item_attr_, false);

*/
  return;
}

void SeRecoDragonAttrsToZiyaV2Enricher::SetBasicAttrs(MutableRecoContextInterface *context,
                                                      std::shared_ptr<ks::search::ziya_sort::Context> ctx) {
  if (ctx == nullptr) {
    LOG(ERROR) << "ctx is nullptr.";
    return;
  }
  ctx->set_user_id(context->GetUserId());
  ctx->set_device_id(context->GetDeviceId());
  ctx->set_session_id(context->GetRequestId());
  ctx->mutable_abtest_mapping_id()->CopyFrom(context->GetRequest()->abtest_mapping_id());

  {
    const auto *client_info = context->GetProtoMessagePtrCommonAttr<ClientRequestInfo>("client_info");
    if (client_info) {
      ctx->set_client_info(*client_info);
    } else {
      ClientRequestInfo client_info;
      ctx->set_client_info(client_info);
      LOG(ERROR) << "client info ptr is null.";
    }
  }

  {
    int64_t int_value = context->GetIntCommonAttr("requestCount").value_or(0);
    ctx->set_count(int_value);
  }
  {
    int64_t int_value = context->GetIntCommonAttr("logLevel").value_or(0);
    ctx->set_log_level(int_value);
  }
  {
    // int64_t int_value = context->GetIntCommonAttr("debugLevel").value_or(0);
    ctx->set_debug_level(context->IsDebugRequest());
  }
  {
    int64_t int_value = context->GetIntCommonAttr("sug_live_user_rely_level").value_or(0);
    ctx->sug_live_user_rely_level_ = int_value;
  }

  // 可能没用，暂时不加
  // ctx->set_photo_ids(request->photo_id());

  // 压测标记，如果不为空即为压测流量；为空即为正常流量
  const std::string &stress_test_biz_name =
      ::ks::infra::kenv::ServiceMeta::GetRpcTraceContext().GetRpcStressTestCtx().GetBizName();
  ctx->set_rpc_stress_test_ctx_biz_name(stress_test_biz_name);

  {
    auto str_list_value =
        context->GetStringListCommonAttr("clientRealActions").value_or(std::vector<absl::string_view>());
    auto client_real_action_map = ctx->mutable_client_real_actions();
    for (auto &str_value : str_list_value) {
      ::mix::kuaishou::client::ClientRealActionFeed action;
      if (action.ParseFromString(std::string(str_value.data(), str_value.size()))) {
        uint64_t photo_id = atoll(action.feed_id().c_str());
        client_real_action_map->emplace(photo_id, action);
      } else {
        LOG(ERROR) << "refer info, action parse failed, uid: " << ctx->user_id();
      }
    }
  }

  auto extra_info_accessor = context->GetCommonAttrAccessor("extraInfoList");
  auto extra_info_list = context->GetStringListCommonAttr(extra_info_accessor);

  if (extra_info_list.has_value()) {
    ::google::protobuf::RepeatedPtrField<::ks::kfs::Feature> feature_list;
    for (auto &extra_info_value : extra_info_list.value()) {
      auto *feat = feature_list.Add();
      feat->ParseFromString(std::string(extra_info_value.data(), extra_info_value.size()));
    }
    ctx->set_request_extra_params(feature_list);
    {
      // 统一处理 server 传来的 extParams 参数
      auto ext_params_vec_ptr = ctx->get_request_extra_param<std::vector<std::string>>("extParams");
      if (ext_params_vec_ptr != nullptr && !ext_params_vec_ptr->empty()) {
        ctx->set_ext_params_jv(ext_params_vec_ptr->operator[](0));
      }
    }
  }
}

void SeRecoDragonAttrsToZiyaV2Enricher::SetCommonAttrs(MutableRecoContextInterface *context,
                                                       std::shared_ptr<ks::search::ziya_sort::Context> ctx) {
  if (ctx == nullptr) {
    LOG(ERROR) << "ctx is nullptr.";
    return;
  }
  {
    auto str_list_value = context->GetStringListCommonAttr("degrade_strategy_executors")
                              .value_or(std::vector<absl::string_view>());
    for (auto &str_value : str_list_value) {
      ctx->mutable_skip_step_set()->insert(std::string(str_value.data(), str_value.size()));
    }
  }
  {
    auto str_value = context->GetStringCommonAttr("query").value_or("");
    ctx->set_query(std::string(str_value.data(), str_value.size()));
    ctx->set_w_query(se_reco::WstringFormat(str_value));
  }
  {
    auto str_value = context->GetStringCommonAttr("rawQuery").value_or("");
    ctx->set_raw_query(std::string(str_value.data(), str_value.size()));
  }
  {
    int64_t int_value = context->GetIntCommonAttr("source").value_or(0);
    ctx->set_source(int_value);
  }
  // "prerank_recall_samples"
  {
    auto str_list_value =
        context->GetStringListCommonAttr("prerank_recall_samples").value_or(std::vector<absl::string_view>());
    for (auto &str_value : str_list_value) {
      ctx->recall_samples_.push_back(std::string(str_value.data(), str_value.size()));
    }
  }

  // "city_without_end"
  {
    auto str_value = context->GetStringCommonAttr("city_without_end").value_or("");
    ctx->city_ = std::string(str_value.data(), str_value.size());
  }
  // "prefix v4 emb"
  {
    auto str_value = context->GetStringCommonAttr("prefix_v4emb").value_or("");
    ctx->prefix_v4emb_ = std::string(str_value.data(), str_value.size());
  }
  //
  {
    int64_t int_value = context->GetIntCommonAttr("sugg_duration_opt_2024_q3_reverse").value_or(0);
    ctx->sugg_duration_opt_2024_q3_reverse_ = int_value;
  }
  // "matched_merchant_live_author_name_list"
  {
    auto str_list_value = context->GetStringListCommonAttr("matched_merchant_live_author_name_list")
                              .value_or(std::vector<absl::string_view>());
    for (auto &str_value : str_list_value) {
      ctx->merchant_author_name_.emplace(std::string(str_value.data(), str_value.size()));
    }
  }

  // "matched_ads_live_author_name_list"
  {
    auto str_list_value = context->GetStringListCommonAttr("matched_ads_live_author_name_list")
                              .value_or(std::vector<absl::string_view>());
    for (auto &str_value : str_list_value) {
      ctx->ads_author_name_.emplace(std::string(str_value.data(), str_value.size()));
    }
  }
  // "brand_name_list", 给 boost 使用
  {
    auto str_list_value =
        context->GetStringListCommonAttr("brand_name_list").value_or(std::vector<absl::string_view>());
    for (auto &str_value : str_list_value) {
      ctx->brand_name_.emplace(std::string(str_value.data(), str_value.size()));
    }
  }

  // "pb_data_source_set_serialized_str"
  {
    auto str_value = context->GetStringCommonAttr("pb_data_source_set_serialized_str").value_or("");
    ctx->prerank_pd_data_source_set_ptr_ = ctx->create_message<ks::search::jubao::PbDataSourceSet>();
    ctx->prerank_pd_data_source_set_ptr_->ParseFromString(std::string(str_value.data(), str_value.size()));
  }
  // "sug_sub_query_list"
  // "sug_sub_query_score_list"
  {
    auto query_list_attr =
        context->GetStringListCommonAttr("sug_sub_query_list").value_or(std::vector<absl::string_view>());
    auto score_list_attr =
        context->GetDoubleListCommonAttr("sug_sub_query_score_list").value_or(absl::Span<const double>());
    if (query_list_attr.size() == score_list_attr.size()) {
      for (int i = 0; i < query_list_attr.size() && i < score_list_attr.size(); i++) {
        std::string query(query_list_attr[i].data(), query_list_attr[i].size());
        ctx->sug_sub_query_map_[query] = score_list_attr[i];
      }
    }
  }

  // "refer_photo_query"
  // "refer_query"
  {
    auto refer_query_attr =
        context->GetStringListCommonAttr("refer_query").value_or(std::vector<absl::string_view>());
    auto refer_photo_query_attr =
        context->GetStringListCommonAttr("refer_photo_query").value_or(std::vector<absl::string_view>());
    std::unordered_set<std::string> refer_query_attr_set;
    for (auto &query_attr : refer_photo_query_attr) {
      std::string query(query_attr.data(), query_attr.size());
      if (refer_query_attr_set.find(query) != refer_query_attr_set.end()) {
        ctx->refer_queries_.push_back(query);
        refer_query_attr_set.insert(query);
      }
    }
    for (auto &query_attr : refer_query_attr) {
      std::string query(query_attr.data(), query_attr.size());
      if (refer_query_attr_set.find(query) != refer_query_attr_set.end()) {
        ctx->refer_queries_.push_back(query);
        refer_query_attr_set.insert(query);
      }
    }
  }

  // "matched_live_author_name_list"
  // "matched_live_author_id_list"
  {
    auto author_name_list_attr = context->GetStringListCommonAttr("matched_live_author_name_list")
                                     .value_or(std::vector<absl::string_view>());
    auto author_id_list_attr =
        context->GetIntListCommonAttr("matched_live_author_id_list").value_or(absl::Span<const int64_t>());
    if (author_name_list_attr.size() == author_id_list_attr.size()) {
      for (int i = 0; i < author_name_list_attr.size() && i < author_id_list_attr.size(); i++) {
        std::string author_name(author_name_list_attr[i].data(), author_name_list_attr[i].size());
        ctx->author_name_id_map_.emplace(author_name, author_id_list_attr[i]);
      }
    }
  }

  // "ad_ecpm_calc_ad_recommend_response"
  {
    const auto *ad_response =
        context->GetProtoMessagePtrCommonAttr<kuaishou::ad::search_ad::AdSearchQueryRecommendResponse>(
            "ad_ecpm_calc_ad_recommend_response");
    if (ad_response) {
      for (const auto &item : ad_response->recommend_querys()) {
        ctx->query_commerce_score_map_[item.query()] = item.rank_info();
        ctx->query_commerce_item_strategy_map_[item.query()] = item.strategy_params();
      }
      ctx->commerce_ctx_strategy_params_.CopyFrom(ad_response->ctx_strategy_params());
    }
  }

  {
    int64_t intent_goods_level_value = context->GetIntCommonAttr("intent_goods_level").value_or(0);
    ctx->intent_goods_level_ = intent_goods_level_value;
  }

  {
    auto buyer_type_value = context->GetStringCommonAttr("buyer_type").value_or("");
    ctx->buyer_type_ = std::string(buyer_type_value.data(), buyer_type_value.size());
  }

  {
    auto active_type_value = context->GetStringCommonAttr("user_active").value_or("");
    ctx->user_search_active_type_ = std::string(active_type_value.data(), active_type_value.size());
  }

  {
    int64_t sug_launch_time = context->GetIntCommonAttr("sug_launch_time").value_or(0);
    ctx->sug_launch_time_ = sug_launch_time;
  }

  {
    auto before_prerank_items_value = context->GetStringCommonAttr("before_prerank_items").value_or("");
    ctx->before_prerank_items_ =
        std::string(before_prerank_items_value.data(), before_prerank_items_value.size());
  }

  {
    auto before_rank_items_value = context->GetStringCommonAttr("before_rank_items").value_or("");
    ctx->before_rank_items_ = std::string(before_rank_items_value.data(), before_rank_items_value.size());
  }

  {
    auto after_rank_items_value = context->GetStringCommonAttr("after_rank_items").value_or("");
    ctx->after_rank_items_ = std::string(after_rank_items_value.data(), after_rank_items_value.size());
  }

  {
    int64_t bprk_cnt_value = context->GetIntCommonAttr("bprk_cnt").value_or(0);
    ctx->bprk_cnt_ = bprk_cnt_value;
  }

  {
    int64_t bfrk_cnt_value = context->GetIntCommonAttr("bfrk_cnt").value_or(0);
    ctx->bfrk_cnt_ = bfrk_cnt_value;
  }

  {
    int64_t afrk_cnt_value = context->GetIntCommonAttr("afrk_cnt").value_or(0);
    ctx->afrk_cnt_ = afrk_cnt_value;
  }

  {
    auto items_value = context->GetStringCommonAttr("sug_relevance_items_string").value_or("");
    ctx->sug_relevance_items_string_ = std::string(items_value.data(), items_value.size());
  }

  {
    auto sug_items = context->GetStringCommonAttr("sug_p2q_risk_items_string").value_or("");
    ctx->sug_p2q_risk_items_string_ = std::string(sug_items.data(), sug_items.size());
  }

  // "prefix_del_feedback_from_redis"
  {
    auto prefix_del_feedback_from_redis =
        context->GetStringCommonAttr("prefix_del_feedback_from_redis").value_or("");
    ctx->prefix_del_feedback_.ParseFromString(
        std::string(prefix_del_feedback_from_redis.data(), prefix_del_feedback_from_redis.size()));
  }
  // searched_query_timestamp_recent
  {
    double double_value =
        context->GetDoubleCommonAttr("recent_searched_query_timestamp_first").value_or(-1.0);
    ctx->recent_searched_query_timestamp_first_ = double_value;
  }
  // searched_query_timestamp_recent
  {
    auto str_value = context->GetStringCommonAttr("recent_searched_query_first").value_or("");
    ctx->recent_searched_query_first_ = std::string(str_value.data(), str_value.size());
  }

  {
    int64_t last_search_time_diff = context->GetIntCommonAttr("last_search_time_diff").value_or(0);
    ctx->last_search_time_diff_ = last_search_time_diff;
  }

  {
    int64_t app_launch_timestamp = context->GetIntCommonAttr("app_launch_timestamp").value_or(0);
    ctx->app_launch_timestamp_ = app_launch_timestamp;
  }

  // "cross_ssid_info_from_redis"
  {
    auto cross_ssid_info_from_redis = context->GetStringCommonAttr("cross_ssid_info_from_redis").value_or("");
    ctx->cross_ssid_info_.ParseFromString(
        std::string(cross_ssid_info_from_redis.data(), cross_ssid_info_from_redis.size()));
  }

  {
    int64_t search_user_30d_degree_value = context->GetIntCommonAttr("search_user_30d_degree").value_or(8);
    ctx->search_user_30d_degree_ = search_user_30d_degree_value;
  }

  {
    int64_t age_segment_value = context->GetIntCommonAttr("age_segment").value_or(6);
    ctx->age_segment_ = age_segment_value;
  }

  {
    auto user_valid_intent_list_val =
        context->GetStringListCommonAttr("user_valid_intent_list").value_or(std::vector<absl::string_view>());
    for (auto &str_value : user_valid_intent_list_val) {
      ctx->user_valid_intent_list_.push_back(std::string(str_value.data(), str_value.size()));
    }
  }

  {
    int64_t user_active_degree_value = context->GetIntCommonAttr("user_active_degree").value_or(9);
    ctx->user_active_degree_ = user_active_degree_value;
  }

  {
    std::shared_ptr<kuaishou::ds::search::DebugLogCommon> debug_common_ptr{
        context->GetMutablePtrCommonAttr<kuaishou::ds::search::DebugLogCommon>("se_debug_log_ptr"),
        [](auto) {}};
    ctx->set_debug_log_common(debug_common_ptr);
  }
}

void SeRecoDragonAttrsToZiyaV2Enricher::SetIconConfig(std::shared_ptr<ks::search::ziya_sort::Context> ctx) {
  if (ctx == nullptr) {
    LOG(ERROR) << "ctx is nullptr.";
    return;
  }
  auto icon_config = icon_config_->Get();
  if (icon_config == nullptr) {
    LOG(ERROR) << "icon config is nullptr";
    return;
  }
  ::Json::Value::Members member = icon_config->getMemberNames();
  for (auto it = member.begin(); it != member.end(); ++it) {
    auto jv = (*icon_config.get())[*it];
    if (!jv.isObject()) {
      continue;
    }
    // 宽高兼容历史（ 直播 icon 虽然有默认宽高，但是不穿下去 ）
    int icon_width = jv.isMember("icon_width") ? jv["icon_width"].asInt() : 28;
    int icon_height = jv.isMember("icon_height") ? jv["icon_height"].asInt() : 16;
    std::string icon_url;
    std::string icon_text;
    std::string icon_color;
    if (jv.isMember("icon_url") && !jv["icon_url"].asString().empty()) {
      icon_url = jv["icon_url"].asString();
    }
    if (jv.isMember("text") && !jv["text"].asString().empty()) {
      icon_text = jv["text"].asString();
    }
    if (jv.isMember("color") && !jv["color"].asString().empty()) {
      icon_color = jv["color"].asString();
    }
    ks::search::ziya_sort::SugIconStyle is = {icon_color, icon_text, icon_url, icon_width, icon_height};
    ctx->icon_conf_map_.emplace(*it, is);
  }
  if (VLOG_IS_ON(2)) {
    for (auto item : ctx->icon_conf_map_) {
      VLOG(2) << "key:" << item.first << ", text:" << item.second.text << ", color:" << item.second.color
              << ", icon_url:" << item.second.icon_url << ", icon_height:" << item.second.icon_height
              << ", icon_width:" << item.second.icon_width;
    }
  }
}

void SeRecoDragonAttrsToZiyaV2Enricher::CreateZiyaItems(MutableRecoContextInterface *context,
                                                        std::shared_ptr<ks::search::ziya_sort::Context> ctx,
                                                        RecoResultConstIter begin, RecoResultConstIter end) {
  if (ctx == nullptr) {
    LOG(ERROR) << "ctx is nullptr.";
    return;
  }
  auto item_id_attr = context->GetItemAttrAccessor("item_id");
  auto item_author_id_attr = context->GetItemAttrAccessor("item_author_id");
  auto item_score_attr = context->GetItemAttrAccessor("item_score");
  auto item_source_attr = context->GetItemAttrAccessor("item_source");
  auto item_recall_reason_attr = context->GetItemAttrAccessor("item_recall_reason");
  auto item_upload_timestamp_attr = context->GetItemAttrAccessor("item_upload_timestamp");
  for (auto it = begin; it != end; ++it) {
    std::string item_id;
    std::string item_source;
    std::string item_recall_reason;
    uint64_t item_author_id = 0;
    uint64_t item_upload_timestamp = 0;
    double item_score = 0.0;
    auto item_id_attr_val = context->GetStringItemAttr(*it, item_id_attr);
    if (item_id_attr_val) {
      item_id = std::string(item_id_attr_val->data(), item_id_attr_val->size());
    }

    auto item_source_attr_val = context->GetStringItemAttr(*it, item_source_attr);
    if (item_source_attr_val) {
      item_source = std::string(item_source_attr_val->data(), item_source_attr_val->size());
    }

    auto item_recall_reason_attr_val = context->GetStringItemAttr(*it, item_recall_reason_attr);
    if (item_recall_reason_attr_val) {
      item_recall_reason =
          std::string(item_recall_reason_attr_val->data(), item_recall_reason_attr_val->size());
    }

    auto item_author_id_attr_val = context->GetIntItemAttr(*it, item_author_id_attr);
    if (item_author_id_attr_val) {
      item_author_id = *item_author_id_attr_val;
    }

    auto item_upload_timestamp_attr_val = context->GetIntItemAttr(*it, item_upload_timestamp_attr);
    if (item_upload_timestamp_attr_val) {
      item_upload_timestamp = *item_upload_timestamp_attr_val;
    }

    auto item_score_attr_val = context->GetDoubleItemAttr(*it, item_score_attr);
    if (item_score_attr_val) {
      item_score = *item_score_attr_val;
    }
    if (item_author_id != 0) {
      item_id = "";
    }
    ks::search::ziya_sort::ItemPtr ziya_item = ks::search::ziya_sort::create_search_item(
        item_id, item_author_id, item_score, item_source, item_upload_timestamp, item_recall_reason);
    ctx->set_item(ziya_item);
  }
}

void SeRecoDragonAttrsToZiyaV2Enricher::SetItemExtraAttrs(MutableRecoContextInterface *context,
                                                          std::shared_ptr<ks::search::ziya_sort::Context> ctx,
                                                          RecoResultConstIter begin, RecoResultConstIter end,
                                                          const std::vector<std::string> &item_extra_attrs,
                                                          bool is_extra_info_attr) {
  if (ctx == nullptr) {
    LOG(ERROR) << "ctx is nullptr.";
    return;
  }
  for (const auto &attr_name : item_extra_attrs) {
    auto item_attr_acc = context->GetItemAttrAccessor(attr_name);
    auto items = ctx->mutable_items();
    auto ziya_iter = items->begin();
    switch (item_attr_acc->value_type) {
      case AttrType::INT: {
        for (auto it = begin; it != end && ziya_iter != items->end(); it++, ziya_iter++) {
          if (auto int_val = context->GetIntItemAttr(*it, item_attr_acc)) {
            if (is_extra_info_attr) {
              ks::search::ziya_sort::set_extra_info<int64_t>(attr_name, *int_val, *ziya_iter);
            } else {
              ctx->set_item_extra_param<int64_t>(attr_name, *int_val, *ziya_iter);
            }
          }
        }
        break;
      }
      case AttrType::FLOAT: {
        for (auto it = begin; it != end && ziya_iter != items->end(); it++, ziya_iter++) {
          if (auto double_val = context->GetDoubleItemAttr(*it, item_attr_acc)) {
            if (is_extra_info_attr) {
              ks::search::ziya_sort::set_extra_info<float>(attr_name, *double_val, *ziya_iter);
            } else {
              ctx->set_item_extra_param<float>(attr_name, *double_val, *ziya_iter);
            }
          }
        }
        break;
      }
      case AttrType::STRING: {
        for (auto it = begin; it != end && ziya_iter != items->end(); it++, ziya_iter++) {
          if (auto str_val = context->GetStringItemAttr(*it, item_attr_acc)) {
            if (attr_name == "icon_type") {
              std::string icon_type = std::string(str_val->data(), str_val->size());
              if (ctx->icon_conf_map_.find(icon_type) != ctx->icon_conf_map_.end()) {
                if (ctx->icon_conf_map_.at(icon_type).icon_url != "") {
                  ks::search::ziya_sort::set_extra_info<std::string>(
                      "icon_url", ctx->icon_conf_map_.at(icon_type).icon_url, *ziya_iter);
                  ks::search::ziya_sort::set_extra_info<int64_t>(
                      "icon_width", ctx->icon_conf_map_.at(icon_type).icon_width, *ziya_iter);
                  ks::search::ziya_sort::set_extra_info<int64_t>(
                      "icon_height", ctx->icon_conf_map_.at(icon_type).icon_height, *ziya_iter);
                } else {
                  ks::search::ziya_sort::set_extra_info<std::string>(
                      "color", ctx->icon_conf_map_.at(icon_type).color, *ziya_iter);
                }
                if (ctx->icon_conf_map_.at(icon_type).text != "") {
                  ks::search::ziya_sort::set_extra_info<std::string>(
                      "text", ctx->icon_conf_map_.at(icon_type).text, *ziya_iter);
                }
              }
            } else if (is_extra_info_attr) {
              ks::search::ziya_sort::set_extra_info<std::string>(
                  attr_name, std::string(str_val->data(), str_val->size()), *ziya_iter);
            } else {
              ctx->set_item_extra_param<std::string>(attr_name, std::string(str_val->data(), str_val->size()),
                                                     *ziya_iter);
            }
          }
        }
        break;
      }
      case AttrType::INT_LIST: {
        for (auto it = begin; it != end && ziya_iter != items->end(); it++, ziya_iter++) {
          if (auto int_list = context->GetIntListItemAttr(*it, item_attr_acc)) {
            if (is_extra_info_attr) {
              ks::search::ziya_sort::set_extra_info<std::vector<int64_t>>(
                  attr_name, {int_list->begin(), int_list->end()}, *ziya_iter);
            } else {
              ctx->set_item_extra_param<std::vector<int64_t>>(attr_name, {int_list->begin(), int_list->end()},
                                                              *ziya_iter);
            }
          }
        }
        break;
      }
      case AttrType::FLOAT_LIST: {
        for (auto it = begin; it != end && ziya_iter != items->end(); it++, ziya_iter++) {
          if (auto double_list = context->GetDoubleListItemAttr(*it, item_attr_acc)) {
            if (is_extra_info_attr) {
              ks::search::ziya_sort::set_extra_info<std::vector<float>>(
                  attr_name, {double_list->begin(), double_list->end()}, *ziya_iter);
            } else {
              ctx->set_item_extra_param<std::vector<float>>(
                  attr_name, {double_list->begin(), double_list->end()}, *ziya_iter);
            }
          }
        }
        break;
      }
      case AttrType::STRING_LIST: {
        for (auto it = begin; it != end && ziya_iter != items->end(); it++, ziya_iter++) {
          if (auto str_list = context->GetStringListItemAttr(*it, item_attr_acc)) {
            std::vector<std::string> str_vec;
            str_vec.reserve(str_list->size());
            for (const auto &str : *str_list) {
              str_vec.emplace_back(str.data(), str.size());
            }
            if (is_extra_info_attr) {
              ks::search::ziya_sort::set_extra_info<std::vector<std::string>>(attr_name, str_vec, *ziya_iter);
            } else {
              ctx->set_item_extra_param<std::vector<std::string>>(attr_name, str_vec, *ziya_iter);
            }
          }
        }
        break;
      }
      default:
        break;
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, SeRecoDragonAttrsToZiyaV2Enricher, SeRecoDragonAttrsToZiyaV2Enricher)

}  // namespace platform
}  // namespace ks
