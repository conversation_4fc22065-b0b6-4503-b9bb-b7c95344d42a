#include <utility>

#include "dragon/src/processor/ext/se_reco/enricher/se_reco_ziya_context_enricher.h"
#include "dragon/src/processor/ext/se_reco/util/se_reco_common_util.h"
#include "se/txt2vid_se/ziya_sort/src/base/specific_context.h"

namespace ks {
namespace platform {

void SeRecoZiyaContextEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                       RecoResultConstIter end) {
  if (ctx == nullptr) {
    CL_LOG(ERROR) << "ctx is nullptr.";
    return;
  }
  int64_t sugg_duration_opt_2024_q3_reverse =
      context->GetIntCommonAttr("sugg_duration_opt_2024_q3_reverse").value_or(0);
  if ((sugg_duration_opt_2024_q3_reverse & 8) == 0) {
    ctx->reset();
  }
  context->SetPtrCommonAttr("ZiyaContextAttrKey", ctx);

  return;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, SeRecoZiyaContextEnricher, SeRecoZiyaContextEnricher)

}  // namespace platform
}  // namespace ks
