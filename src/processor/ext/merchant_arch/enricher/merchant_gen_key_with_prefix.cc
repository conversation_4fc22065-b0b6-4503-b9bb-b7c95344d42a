#include "dragon/src/processor/ext/merchant_arch/enricher/merchant_gen_key_with_prefix.h"

#include <utility>
#include "base/strings/utf_char_iterator.h"
#include "third_party/abseil/absl/strings/numbers.h"
#include "third_party/abseil/absl/strings/str_split.h"

namespace ks {
namespace platform {

void MerchantGenKeyWithPrefixEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                              RecoResultConstIter end) {
  std::string key_prefix = GetStringProcessorParameter(context, "prefix");

  if (auto p_int = context->GetIntCommonAttr(input_attr_)) {
    context->SetStringCommonAttr(output_attr_, key_prefix + std::to_string(static_cast<uint64>(*p_int)));
  } else if (auto p_string = context->GetStringCommonAttr(input_attr_)) {
    context->SetStringCommonAttr(output_attr_, key_prefix + std::string(p_string->data(), p_string->size()));
  } else if (auto p_int_list = context->GetIntListCommonAttr(input_attr_)) {
    std::vector<std::string> key_list;
    for (const int64 key : *p_int_list) {
      key_list.emplace_back(key_prefix + std::to_string(static_cast<uint64>(key)));
    }
    context->SetStringListCommonAttr(output_attr_, std::move(key_list));
  } else if (auto p_string_list = context->GetStringListCommonAttr(input_attr_)) {
    std::vector<std::string> key_list;
    for (auto sv : *p_string_list) {
      key_list.emplace_back(key_prefix + std::string(sv));
    }
    context->SetStringListCommonAttr(output_attr_, std::move(key_list));
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, MerchantGenKeyWithPrefixEnricher, MerchantGenKeyWithPrefixEnricher)
}  // namespace platform
}  // namespace ks
