#include "dragon/src/processor/ext/merchant_arch/enricher/merchant_global_data_enricher.h"

#include <algorithm>
#include <map>
#include <string>
#include <vector>
#include "folly/container/F14Set.h"

namespace ks {
namespace platform {

void MerchantGlobalDataEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                        RecoResultConstIter end) {
  // save kuiba user attr to common attr
  auto kuiba_user_attr = context->GetStringCommonAttr(kuiba_user_attr_name_);
  predict_item_.Clear();
  if (kuiba_user_attr && !kuiba_user_attr->empty()) {
    if (kuiba_user_attr->data()) {
      if (predict_item_.ParseFromArray(kuiba_user_attr->data(), kuiba_user_attr->size())) {
        if (!kuiba_user_ptr_attr_name_.empty()) {
          context->SetPtrCommonAttr(kuiba_user_ptr_attr_name_, &predict_item_);
        }
        for (const auto &attr : predict_item_.attr()) {
          auto it = export_common_attrs_.find(attr.name());
          if (export_common_attrs_.end() == it) {
            continue;
          }
          ks::platform::interop::SaveSampleAttrToCommonAttr(context, attr);
        }
      } else {
        CL_LOG_EVERY_N(ERROR, 100) << "unmarshall kuiba user attrs to context";
      }
    }
  }

  auto perf_sample_rate = context->GetDoubleCommonAttr(perf_sample_rate_);
  if (perf_sample_rate) {
    int64 start_ts = base::GetTimestamp();
    uint64 hash_did_value =
        base::CityHash64WithSeed(context->GetDeviceId().c_str(), context->GetDeviceId().size(), start_ts);
    int random_value = hash_did_value % 100;
    if (random_value <= (*perf_sample_rate) * 100) {
      base::perfutil::enable_perf_sample_log = true;
    } else {
      base::perfutil::enable_perf_sample_log = false;
    }
  } else {
    CL_LOG_EVERY_N(ERROR, 100) << "error perf sample rate";
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, MerchantGlobalDataEnricher, MerchantGlobalDataEnricher)
}  // namespace platform
}  // namespace ks
