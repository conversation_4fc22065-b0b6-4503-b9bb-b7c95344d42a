#pragma once

#include <string>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class MerchantArchStringListSplitEnricher : public ks::platform::CommonRecoBaseEnricher {
 public:
  MerchantArchStringListSplitEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  void ProcessCommonAttr(MutableRecoContextInterface *context);
  std::vector<absl::string_view> StringSplit(absl::string_view input_str);

 private:
  bool InitProcessor() override {
    input_attr_ = config()->GetString("input_common_attr");
    if (input_attr_.empty()) {
      LOG(ERROR) << "MerchantArchStringListSplitEnricher init failed! input_attr is required";
      return false;
    }

    output_attr_ = config()->GetString("output_common_attr");
    if (output_attr_.empty()) {
      LOG(ERROR) << "MerchantArchStringListSplitEnricher init failed! output_attr is required";
      return false;
    }

    bool has_delimiters = config()->GetString("delimiters", &delimiters_);
    if (!has_delimiters) {
      LOG(ERROR) << "MerchantArchStringListSplitEnricher init failed!"
                 << " Missing \"delimiters\" or it is not a string.";
      return false;
    }

    trim_spaces_ = config()->GetBoolean("trim_spaces", false);
    skip_empty_tokens_ = config()->GetBoolean("skip_empty_tokens", false);
    parse_to_int_ = config()->GetBoolean("parse_to_int", false);
    parse_to_double_ = config()->GetBoolean("parse_to_double", false);

    return true;
  }

 private:
  std::string input_attr_;
  std::string output_attr_;

  std::string delimiters_;
  bool trim_spaces_ = false;
  bool skip_empty_tokens_ = false;
  bool parse_to_int_ = false;
  bool parse_to_double_ = false;

  DISALLOW_COPY_AND_ASSIGN(MerchantArchStringListSplitEnricher);
};

}  // namespace platform
}  // namespace ks
