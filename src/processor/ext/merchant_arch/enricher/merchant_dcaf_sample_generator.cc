#include "dragon/src/processor/ext/merchant_arch/enricher/merchant_dcaf_sample_generator.h"
#include <sys/param.h>

#include <algorithm>
#include <cstdint>
#include <limits>
#include <memory>
#include <tuple>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/module/plain_json_string_builder.h"
#include "ks/reco_pub/reco/util/util.h"

namespace ks {
namespace platform {

bool MerchantDcafSampleGeneratorEnricher::InitProcessor() {
  auto common_attrs_set_json = config()->Get("common_attrs");
  if (!common_attrs_set_json || !common_attrs_set_json->IsArray()) {
    LOG(ERROR) << "pack_context_features is empty or not array";
    return false;
  }
  for (const auto &attr : common_attrs_set_json->array()) {
    if (!attr->IsString()) {
      LOG(ERROR) << "pack_context_features is not string";
      return false;
    }
    common_attrs_set_.insert(attr->ToString());
  }
  dcaf_features_attr_ = config()->GetString("dcaf_features");
  if (dcaf_features_attr_.empty()) {
    LOG(ERROR) << "dcaf_features attr is empty";
    return false;
  }
  output_json_ = config()->GetString("output_json");
  if (output_json_.empty()) {
    LOG(ERROR) << "output_json is empty";
    return false;
  }
  mc_truncate_seq_item_attr_ = config()->GetString("mc_truncate_seq");
  rank_score_item_attr_ = config()->GetString("rank_score");
  if (mc_truncate_seq_item_attr_.empty() && rank_score_item_attr_.empty()) {
    LOG(ERROR) << "mc_truncate_seq and rank_score is empty, rank_score_item_attr_: " << rank_score_item_attr_
               << ", mc_truncate_seq_item_attr_: " << mc_truncate_seq_item_attr_;
    return false;
  }
  egpm_item_attr_ = config()->GetString("egpm");
  if (egpm_item_attr_.empty()) {
    LOG(ERROR) << "egpm item_attr is empty";
    return false;
  }
  mtb_rate_item_attr_ = config()->GetString("mtb_rate");
  if (!config()->Get("biz_name")) {
    LOG(ERROR) << "biz_name is empty";
    return false;
  }
  index_kconf_ = std::make_shared<MagicIndexKconf>("reco.merchant.magic_center");
  return true;
}

void MerchantDcafSampleGeneratorEnricher::Enrich(MutableRecoContextInterface *context,
                                                      RecoResultConstIter begin, RecoResultConstIter end) {
  if (!Prepare(context)) return;
  thread_local PlainJsonStringBuilder json_builder;
  json_builder.clear();
  // 填充基本信息
  json_builder.AddField("biz_name", biz_name_);
  json_builder.AddField("timestamp", base::GetTimestamp());
  json_builder.AddField("uid", context->GetUserId());
  json_builder.AddField("item_id", context->GetDeviceId());
  json_builder.AddField("item_num", end - begin);
  json_builder.AddField("version", version_);
  json_builder.AddField("bucket_cnt", bucket_cnt_);
  json_builder.AddField("quota_lower", quota_lower_);
  json_builder.AddField("quota_upper", quota_upper_);
  // 填充扩展 common attr
  if (auto common_attrs = sample_kconf_->GetCommonAttrs()) {
    for (const auto &attr_name : *common_attrs) {
      if (common_attrs_set_.find(attr_name) == common_attrs_set_.end()) {
        CL_LOG_WARNING_EVERY("dynamic_computation_allocation_sample_creation",
                             "common attr [" + attr_name + "] not import", 100)
            << ", common attr: " << attr_name << std::endl;
      }
      if (auto p = context->GetIntCommonAttr(attr_name)) {
        json_builder.AddField(attr_name, *p);
      } else if (auto p = context->GetDoubleCommonAttr(attr_name)) {
        json_builder.AddField(attr_name, *p);
      } else if (auto p = context->GetStringCommonAttr(attr_name)) {
        json_builder.AddField(attr_name, *p);
      } else if (auto p = context->GetIntListCommonAttr(attr_name)) {
        json_builder.AddField(attr_name, *p);
      } else if (auto p = context->GetDoubleListCommonAttr(attr_name)) {
        json_builder.AddField(attr_name, *p);
      } else if (auto p = context->GetStringListCommonAttr(attr_name)) {
        json_builder.AddField(attr_name, *p);
      } else {
        json_builder.AddNullField(attr_name);
      }
    }
  }
  // 填充 dcaf_sample
  if (auto dcaf_features = context->GetDoubleListCommonAttr(dcaf_features_attr_)) {
    // 够造 label
    auto labels = MakeLabels(context, begin, end);
    std::vector<double> dcaf_sample;
    std::copy(dcaf_features->begin(), dcaf_features->end(), std::back_inserter(dcaf_sample));
    if (labels != nullptr) {
      for (auto label : *labels) {
        dcaf_sample.push_back(label);
      }
    }
    json_builder.AddField("dcaf_sample", absl::Span<const double>{dcaf_sample});
  }
  // set output_json
  context->SetStringCommonAttr(output_json_, json_builder.json_string());
}

std::shared_ptr<std::vector<double>> MerchantDcafSampleGeneratorEnricher::MakeLabels(
    MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end) {
  auto labels = std::make_shared<std::vector<double>>(bucket_cnt_, 0);  // 初始化为 0, 长度为 bucket_cnt_
  // 排序
  ItemAttr *cmp_accessor = context->GetItemAttrAccessor(mc_truncate_seq_item_attr_);
  std::vector<CommonRecoResult> vec;
  for (auto it = begin; it != end; ++it) {
    vec.push_back(*it);
  }
  std::sort(vec.begin(), vec.end(), [&](const CommonRecoResult &a, const CommonRecoResult &b) {
    if (cmp_accessor && cmp_accessor->value_type == AttrType::FLOAT) {
      auto val_a = context->GetDoubleItemAttr(a, cmp_accessor).value_or(0);
      auto val_b = context->GetDoubleItemAttr(b, cmp_accessor).value_or(0);
      return val_a < val_b;
    } else if (cmp_accessor && cmp_accessor->value_type == AttrType::INT) {
      auto val_a = context->GetIntItemAttr(a, cmp_accessor).value_or(0);
      auto val_b = context->GetIntItemAttr(b, cmp_accessor).value_or(0);
      return val_a < val_b;
    }
    return false;  // Should not be reached if accessor type is checked
  });
  // 聚合
  if (bucket_cnt_ == 0) return labels;
  auto egpm_accessor = context->GetItemAttrAccessor(egpm_item_attr_);
  auto rank_score_accessor = context->GetItemAttrAccessor(rank_score_item_attr_);
  int last_pos = 0;
  auto sort_fn = [&](int i) {
    // 对 vec [0, quota_lower_ + d) 排序， 对每个流量档位排序，为了取 topk
    auto d = bucket_cnt_ <= 1 ? 0 : i * (quota_upper_ - quota_lower_ + bucket_cnt_ - 2) / (bucket_cnt_ - 1);
    while (last_pos < vec.size() &&
           context->GetIntItemAttr(vec[last_pos], cmp_accessor).value_or(0) < quota_lower_ + d) {
      last_pos++;  // 由于精排截断存在过滤，所以需要先查到第一个大于等于 quota_lower_ + d 的位置
    }
    std::sort(vec.begin(), vec.begin() + std::min(quota_lower_ + last_pos, (int64_t)vec.size()),
              [&](const CommonRecoResult &a, const CommonRecoResult &b) {
                return context->GetDoubleItemAttr(a, rank_score_accessor).value_or(1e-6) >
                       context->GetDoubleItemAttr(b, rank_score_accessor).value_or(1e-6);
              });
  };
  if (is_rerank_ == 1) {
    for (int i = 0; i < bucket_cnt_; i++) {
      // 按内部混排排序分排序
      sort_fn(i);
      // 聚合 top limit_num_ 得到 gmv
      if (vec.empty()) continue;
      auto egpm = context->GetDoubleItemAttr(vec[0], egpm_accessor).value_or(1e-6);
      (*labels)[i] = egpm - last_egpm_;
    }
  } else if (aggregator_ == "mtb_sum") {
    auto mtb_rate_accessor = context->GetItemAttrAccessor(mtb_rate_item_attr_);
    for (int i = 0; i < bucket_cnt_; i++) {
      // 按内部混排排序分排序
      sort_fn(i);
      // 聚合 top limit_num_ 得到 gmv
      for (int j = 0; j < limit_num_ && j < vec.size(); j++) {
        auto egpm = context->GetDoubleItemAttr(vec[j], egpm_accessor).value_or(1e-6);
        auto mtb_rate = context->GetDoubleItemAttr(vec[j], mtb_rate_accessor).value_or(1e-6);
        (*labels)[i] += egpm * mtb_rate;
      }
    }
  } else if (aggregator_ == "sum") {
    for (int i = 0; i < bucket_cnt_; i++) {
      // 按内部混排排序分排序
      sort_fn(i);
      // 聚合 top limit_num_ 得到 gmv
      for (int j = 0; j < limit_num_ && j < vec.size(); j++) {
        auto egpm = context->GetDoubleItemAttr(vec[j], egpm_accessor).value_or(1e-6);
        (*labels)[i] += egpm;
      }
    }
  } else if (aggregator_ == "max") {
    for (int i = 0; i < bucket_cnt_; i++) {
      // 按内部混排排序分排序
      sort_fn(i);
      // 聚合 top limit_num_ 得到 gmv
      for (int j = 0; j < limit_num_ && j < vec.size(); j++) {
        auto egpm = context->GetDoubleItemAttr(vec[j], egpm_accessor).value_or(1e-6);
        (*labels)[i] = std::max((*labels)[i], egpm);
      }
    }
  }
  return labels;
}

bool MerchantDcafSampleGeneratorEnricher::Prepare(MutableRecoContextInterface *context) {
  biz_name_ = GetStringProcessorParameter(context, "biz_name", "merchant_live_1pp");
  if (index_kconf_ == nullptr) {
    CL_LOG_ERROR_EVERY("dynamic_computation_allocation_feature_extraction", "index_kconf is nullptr", 100);
    return false;
  }
  kconf_key_ = index_kconf_->GetKconfKeyByBizName(biz_name_);
  if (kconf_key_.empty()) {
    CL_LOG_ERROR_EVERY("dynamic_computation_allocation_feature_extraction",
                       "biz_name: " + biz_name_ + " kconf_key is empty", 100);
    return false;
  }
  if (sample_kconf_map_.find(kconf_key_) == sample_kconf_map_.end()) {
    sample_kconf_map_[kconf_key_] = std::make_shared<MagicDcafSampleKconf>(kconf_key_);
  }
  sample_kconf_ = sample_kconf_map_.at(kconf_key_);
  sample_maker_ = GetStringProcessorParameter(context, "sample_maker", "score");
  aggregator_ = GetStringProcessorParameter(context, "aggregator", "sum");
  is_rerank_ = GetIntProcessorParameter(context, "is_rerank", 0);
  last_egpm_ = GetDoubleProcessorParameter(context, "last_egpm", 0.0);
  limit_num_ = GetIntProcessorParameter(context, "limit_num", 5);
  version_ = sample_kconf_->GetVersion();
  bucket_cnt_ = sample_kconf_->GetBucketCnt();
  quota_lower_ = sample_kconf_->GetQuotaLower();
  quota_upper_ = sample_kconf_->GetQuotaUpper();
  return true;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, MerchantDcafSampleGeneratorEnricher,
                 MerchantDcafSampleGeneratorEnricher);

}  // namespace platform
}  // namespace ks
