#include "dragon/src/processor/ext/merchant_arch/enricher/merchant_arch_string_list_split_enricher.h"

#include <utility>
#include "base/strings/utf_char_iterator.h"
#include "third_party/abseil/absl/strings/numbers.h"
#include "third_party/abseil/absl/strings/str_split.h"

namespace ks {
namespace platform {

void MerchantArchStringListSplitEnricher::Enrich(MutableRecoContextInterface *context,
                                                 RecoResultConstIter begin, RecoResultConstIter end) {
  ProcessCommonAttr(context);
}

static int StringViewListToIntList(const std::vector<absl::string_view> &str_value_list,
                                   const int max_num_per_str, std::vector<int64> *int_list) {
  int64 val;
  int succ_count = 0;
  int fail_count = 0;
  for (auto sv : str_value_list) {
    if (absl::SimpleAtoi(sv, &val)) {
      int_list->emplace_back(val);
      if (max_num_per_str > 0 && ++succ_count >= max_num_per_str) {
        break;
      }
    } else {
      ++fail_count;
    }
  }
  return fail_count;
}

static int StringViewListToDoubleList(const std::vector<absl::string_view> &str_value_list,
                                      const int max_num_per_str, std::vector<double> *double_list) {
  double val;
  int succ_count = 0;
  int fail_count = 0;
  for (auto sv : str_value_list) {
    if (absl::SimpleAtod(sv, &val)) {
      double_list->emplace_back(val);
      if (max_num_per_str > 0 && ++succ_count >= max_num_per_str) {
        break;
      }
    } else {
      ++fail_count;
    }
  }
  return fail_count;
}

static int StringViewListToStringList(const std::vector<absl::string_view> &str_value_list,
                                      const int max_num_per_str, std::vector<std::string> *string_list) {
  int succ_count = 0;
  for (auto sv : str_value_list) {
    string_list->emplace_back(sv.data(), sv.size());
    if (max_num_per_str > 0 && ++succ_count >= max_num_per_str) {
      break;
    }
  }
  return 0;
}

void MerchantArchStringListSplitEnricher::ProcessCommonAttr(MutableRecoContextInterface *context) {
  auto input_str_list = context->GetStringListCommonAttr(input_attr_);
  auto max_num_per_str = GetIntProcessorParameter(context, "max_num_per_str");
  if (!input_str_list) {
    CL_LOG(INFO) << "split_string cancelled due to missing input: " << input_attr_;
    return;
  }
  std::vector<std::vector<absl::string_view>> output_strs;
  for (auto input_str : *input_str_list) {
    auto tmp_output = StringSplit(input_str);
    if (tmp_output.size() == 0) {
      continue;
    }
    output_strs.emplace_back(tmp_output);
  }

  if (parse_to_double_) {
    std::vector<double> vec;
    vec.reserve(output_strs.size());
    int fail_count = 0;
    for (auto &str_value_list : output_strs) {
      fail_count += StringViewListToDoubleList(str_value_list, max_num_per_str, &vec);
    }
    context->SetDoubleListCommonAttr(output_attr_, std::move(vec));
    CL_LOG_WARNING_COUNT(fail_count, "split_string", "parse_double_fail: " + input_attr_)
        << "failed to parse double for " << fail_count << " string values from common attr: " << input_attr_;
  } else if (parse_to_int_) {
    std::vector<int64> vec;
    vec.reserve(output_strs.size());
    int fail_count = 0;
    for (auto &str_value_list : output_strs) {
      fail_count += StringViewListToIntList(str_value_list, max_num_per_str, &vec);
    }
    context->SetIntListCommonAttr(output_attr_, std::move(vec));
    CL_LOG_WARNING_COUNT(fail_count, "split_string", "parse_int_fail: " + input_attr_)
        << "failed to parse int for " << fail_count << " string values from common attr: " << input_attr_;
  } else {
    std::vector<std::string> vec;
    vec.reserve(output_strs.size());
    for (auto &str_value_list : output_strs) {
      StringViewListToStringList(str_value_list, max_num_per_str, &vec);
    }
    context->SetStringListCommonAttr(output_attr_, std::move(vec));
  }
}

std::vector<absl::string_view> MerchantArchStringListSplitEnricher::StringSplit(absl::string_view input_str) {
  std::vector<absl::string_view> output_strs;
  if (skip_empty_tokens_) {
    if (trim_spaces_) {
      output_strs = absl::StrSplit(input_str, absl::ByAnyChar(delimiters_), absl::SkipWhitespace());
    } else {
      output_strs = absl::StrSplit(input_str, absl::ByAnyChar(delimiters_), absl::SkipEmpty());
    }
  } else {
    output_strs = absl::StrSplit(input_str, absl::ByAnyChar(delimiters_), absl::AllowEmpty());
  }
  return output_strs;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, MerchantArchStringListSplitEnricher, MerchantArchStringListSplitEnricher)

}  // namespace platform
}  // namespace ks
