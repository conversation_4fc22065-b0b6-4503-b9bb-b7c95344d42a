#pragma once

#include <map>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>
#include "folly/container/F14Map.h"

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/interop/kuiba_sample_attr.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class MerchantGlobalDataEnricher : public ks::platform::CommonRecoBaseEnricher {
 public:
  MerchantGlobalDataEnricher() {}
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;
  void OnPipelineExit(ReadableRecoContextInterface *context) override {
    kuiba::PredictItem tmp;
    predict_item_.Swap(&tmp);
    // predict_item_.Clear();
  }

 private:
  bool InitProcessor() override {
    kuiba_user_attr_name_ = config()->GetString("kuiba_user_attr");
    if (kuiba_user_attr_name_.empty()) {
      LOG(ERROR) << "MerchantGlobalDataEnricher"
                 << " init failed! Missing 'kuiba_user_attr'";
      return false;
    }
    kuiba_user_ptr_attr_name_ = config()->GetString("kuiba_user_ptr_attr");

    perf_sample_rate_ = config()->GetString("perf_sampling_rate");
    if (perf_sample_rate_.empty()) {
      LOG(ERROR) << "MerchantGlobalDataEnricher"
                 << " init failed! Missing 'perf_sample_rate'";
      return false;
    }

    auto *export_common_config = config()->Get("export_common_attr");
    if (export_common_config && !RecoUtil::ParseAttrsConfig(export_common_config, &export_common_attrs_)) {
      LOG(ERROR) << "MerchantGlobalDataEnricher init failed! Failed to extract 'export_common_attr'!";
      return false;
    }
    return true;
  }

 private:
  std::string perf_sample_rate_;
  std::string kuiba_user_attr_name_;
  std::string kuiba_user_ptr_attr_name_;
  std::unordered_map<std::string, std::string> export_common_attrs_;
  kuiba::PredictItem predict_item_;
};

}  // namespace platform
}  // namespace ks
