#pragma once

#include <string>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class MerchantGenKeyWithPrefixEnricher : public ks::platform::CommonRecoBaseEnricher {
 public:
  MerchantGenKeyWithPrefixEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    input_attr_ = config()->GetString("input_common_attr");
    if (input_attr_.empty()) {
      LOG(ERROR) << "MerchantGenKeyWithPrefixEnricher init failed! input_attr is required";
      return false;
    }

    output_attr_ = config()->GetString("output_common_attr");
    if (output_attr_.empty()) {
      LOG(ERROR) << "MerchantGenKeyWithPrefixEnricher init failed! output_attr is required";
      return false;
    }

    return true;
  }

 private:
  std::string input_attr_;
  std::string output_attr_;

  DISALLOW_COPY_AND_ASSIGN(MerchantGenKeyWithPrefixEnricher);
};

}  // namespace platform
}  // namespace ks
