#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "ks/algo-engine/formula1/src/exprtk.hpp"

namespace ks {
namespace platform {

struct FormulaConfig {
  std::string output_attr;
  bool is_common = false;
  bool to_int = false;
  std::vector<std::string> common_attrs;
  std::vector<std::string> item_attrs;
  std::vector<ItemAttr *> item_attr_accessors;
  std::vector<double> values;

  exprtk2::expression<double> expression;
  exprtk2::symbol_table<double> symbol_table;
};

class CommonRecoCalcBySimpleFormulaEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoCalcBySimpleFormulaEnricher() = default;
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    auto *formula_params = config()->Get("formulas");
    if (!formula_params || !formula_params->IsArray()) {
      LOG(ERROR) << "CommonRecoCalcBySimpleFormulaEnricher init failed!"
                 << " Missing \"formulas\" config or it is not an array.";
      return false;
    }

    formulas_.clear();
    formulas_.resize(formula_params->array().size());
    int index = 0;
    for (const auto *formula_param : formula_params->array()) {
      FormulaConfig &formula = formulas_[index];
      if (!formula_param || !formula_param->IsObject()) {
        LOG(ERROR) << "CommonRecoCalcBySimpleFormulaEnricher init failed! Item of ab_params should be a dict!"
                   << " Value found: " << formula_param->ToString();
        return false;
      }

      std::string expr = formula_param->GetString("expr", "");
      if (expr.empty()) {
        LOG(ERROR) << "CommonRecoCalcBySimpleFormulaEnricher init failed! Missing config expr!";
        return false;
      }

      auto common_attrs = formula_param->Get("common_attrs");
      if (common_attrs && !RecoUtil::ExtractStringListFromJsonConfig(common_attrs, &formula.common_attrs)) {
        LOG(ERROR) << "CommonRecoCalcBySimpleFormulaEnricher init failed! common_attrs config should be a "
                      "string array";
        return false;
      }
      auto item_attrs = formula_param->Get("item_attrs");
      if (item_attrs && !RecoUtil::ExtractStringListFromJsonConfig(item_attrs, &formula.item_attrs)) {
        LOG(ERROR) << "CommonRecoCalcBySimpleFormulaEnricher init failed! item_attrs config should be a "
                      "string array";
        return false;
      }
      formula.values.resize(formula.common_attrs.size() + formula.item_attrs.size());
      for (int i = 0; i < formula.common_attrs.size(); ++i) {
        formula.symbol_table.add_variable(formula.common_attrs[i], formula.values[i]);
      }
      for (int i = 0; i < formula.item_attrs.size(); ++i) {
        formula.symbol_table.add_variable(formula.item_attrs[i],
                                          formula.values[i + formula.common_attrs.size()]);
      }

      formula.expression.register_symbol_table(formula.symbol_table);
      bool valid = parser_.compile(expr, formula.expression);
      if (!valid) {
        LOG(ERROR) << "CommonRecoCalcBySimpleFormulaEnricher init failed! invalid expr:" << expr
                   << ", error:" << parser_.error();
        return false;
      }

      formula.is_common = formula_param->GetBoolean("is_common", false);
      formula.to_int = formula_param->GetBoolean("to_int", false);
      formula.output_attr = formula_param->GetString("output_attr", "");
      if (formula.output_attr.empty()) {
        LOG(ERROR) << "CommonRecoCalcBySimpleFormulaEnricher init failed! Missing config output_attr!";
        return false;
      }
      ++index;
    }

    return true;
  };

  double GetCommonAttrValue(MutableRecoContextInterface *context, const std::string &attr_name);
  double GetItemAttrValue(MutableRecoContextInterface *context, ItemAttr *item_attr,
                          const CommonRecoResult &result);

 private:
  exprtk2::parser<double> parser_;

  std::vector<FormulaConfig> formulas_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoCalcBySimpleFormulaEnricher);
};

}  // namespace platform
}  // namespace ks
