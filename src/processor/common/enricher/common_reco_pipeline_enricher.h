#pragma once

#include <kess/rpc/grpc/grpc_client_builder.h>

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "dragon/src/common_reco_handler.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/core/single_pipeline_executor.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "folly/container/F14Set.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "serving_base/util/future_pool.h"
#include "serving_base/util/scope_exit.h"

DECLARE_bool(enable_pthread_affinity);

namespace ks {
namespace platform {

class CommonRecoPipelineEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoPipelineEnricher() {}
  ~CommonRecoPipelineEnricher() {}
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  bool IsAsync() const override {
    return true;
  }

  int GetPartitionSize(ReadableRecoContextInterface *context) const final {
    return 0;
  }

  void OnPipelineExit(ReadableRecoContextInterface *context) final {
    executor_.HandlePipelineExit();
  }

 private:
  bool InitProcessor() override {
    pipeline_name_ = config()->GetString("flow_name", "");
    if (pipeline_name_.empty()) {
      LOG(ERROR) << "CommonRecoPipelineEnricher init failed! Missing 'flow_name' config";
      return false;
    }

    // 初始化 SinglePipelineExecutor
    base::Json *pipeline_manager_config =
        GlobalHolder::GetDynamicJsonConfig()->Get("pipeline_manager_config");
    if (!pipeline_manager_config) {
      LOG(ERROR) << "pipeline_manager_config is null, you should set user_static_flags: "
                 << "use_fake_json_config_first=true when using SampleServer, "
                 << "because GlobalHolder::GetDynamicJsonConfig()="
                 << base::JsonToString(GlobalHolder::GetDynamicJsonConfig()->get(), 2);
    }
    common_reco_context_attr_name_ = config()->GetString("common_reco_context_attr_name", "");
    if (!executor_.Initialize(pipeline_manager_config, pipeline_name_,
                              common_reco_context_attr_name_.empty())) {
      LOG(ERROR) << "CommonRecoPipelineEnricher init failed! Pipeline " << pipeline_name_
                 << " initialize failed.";
      return false;
    }

    if (!RecoUtil::ParseAttrsConfig(config()->Get("merge_item_attrs"), &return_item_attrs_)) {
      LOG(ERROR) << "CommonRecoPipelineEnricher init failed! merge_item_attrs parse error.";
      return false;
    }

    if (!RecoUtil::ParseAttrsConfig(config()->Get("merge_common_attrs"), &return_common_attrs_)) {
      LOG(ERROR) << "CommonRecoPipelineEnricher init failed! merge_common_attrs parse error.";
      return false;
    }

    auto *copy_common_attrs = config()->Get("pass_common_attrs");
    RecoUtil::ExtractStringSetFromJsonConfig(copy_common_attrs, &copy_common_attrs_);

    auto *copy_item_attrs = config()->Get("pass_item_attrs");
    RecoUtil::ExtractStringSetFromJsonConfig(copy_item_attrs, &copy_item_attrs_);

    fill_common_attrs_from_request_ = config()->GetBoolean("pass_common_attrs_in_request", false);
    fill_browse_set_from_request_ = config()->GetBoolean("pass_browse_set", true);
    merge_and_overwrite_ = config()->GetBoolean("merge_and_overwrite", false);
    deep_copy_ = config()->GetBoolean("deep_copy", false);

    // 将 copy_common/item_attrs_ 拆分为深拷贝、浅拷贝两部分
    if (deep_copy_) {
      SplitAttrsByCopySemantics(copy_common_attrs_, "shadow_copy_common_attrs", &shadow_copy_common_attrs_,
                                &deep_copy_common_attrs_);
      SplitAttrsByCopySemantics(copy_item_attrs_, "shadow_copy_item_attrs", &shadow_copy_item_attrs_,
                                &deep_copy_item_attrs_);
    } else {
      SplitAttrsByCopySemantics(copy_common_attrs_, "deep_copy_common_attrs", &deep_copy_common_attrs_,
                                &shadow_copy_common_attrs_);
      SplitAttrsByCopySemantics(copy_item_attrs_, "deep_copy_item_attrs", &deep_copy_item_attrs_,
                                &shadow_copy_item_attrs_);
    }

    save_results_to_ = config()->GetString("save_results_to", "");
    merge_item_attr_for_all_items_ = config()->GetBoolean("merge_item_attr_for_all_items", false);
    SubFlowThreadPoolManager::GetInstance()->Resize(config()->GetInt("task_queue_id", 0) + 1);
    return true;
  }

  bool CloneContext(ReadableRecoContextInterface *context, RecoResultConstIter begin,
                    RecoResultConstIter end);

  void DereferenceAttrs(CommonRecoContext *context);

  void MergeSubflowStepInfo(AddibleRecoContextInterface *context);

  void SplitAttrsByCopySemantics(const folly::F14FastSet<std::string> &copy_attrs,
                                 const std::string &reference_name,
                                 folly::F14FastSet<std::string> *in_reference,
                                 folly::F14FastSet<std::string> *not_in_reference) {
    folly::F14FastSet<std::string> reference_attrs;
    RecoUtil::ExtractStringSetFromJsonConfig(config()->Get(reference_name), &reference_attrs);
    for (const auto &attr : copy_attrs) {
      if (reference_attrs.count(attr)) {
        in_reference->insert(attr);
      } else {
        not_in_reference->insert(attr);
      }
    }
  }

 private:
  std::unordered_map<std::string, std::string> return_common_attrs_;
  folly::F14FastMap<std::string, AttrConfig> return_item_attrs_;
  folly::F14FastSet<std::string> copy_common_attrs_;
  folly::F14FastSet<std::string> copy_item_attrs_;
  folly::F14FastSet<std::string> shadow_copy_common_attrs_;
  folly::F14FastSet<std::string> shadow_copy_item_attrs_;
  folly::F14FastSet<std::string> deep_copy_common_attrs_;
  folly::F14FastSet<std::string> deep_copy_item_attrs_;
  std::string pipeline_name_;
  std::string save_results_to_;
  std::string common_reco_context_attr_name_;
  bool fill_common_attrs_from_request_ = true;
  bool fill_browse_set_from_request_ = true;
  bool merge_and_overwrite_ = false;
  bool deep_copy_ = false;
  bool merge_item_attr_for_all_items_ = false;
  SinglePipelineExecutor executor_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoPipelineEnricher);
};

}  // namespace platform
}  // namespace ks
