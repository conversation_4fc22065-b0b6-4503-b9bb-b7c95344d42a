#include "dragon/src/processor/common/enricher/general_city_hash_enricher.h"

#include <stdint.h>

#include "base/hash_function/city.h"

namespace ks {
namespace platform {

void GeneralCityHashEnricher::Enrich(
    MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end) {
  if (is_common_) {
    ProcessCommon(context);
  } else {
    ProcessItem(context, begin, end);
  }
}

void GeneralCityHashEnricher::ProcessCommon(MutableRecoContextInterface *context) {
  std::vector<std::string> buffer;
  for (const auto &input_attr : input_attrs_) {
    if (auto val = context->GetIntCommonAttr(input_attr.attr_name)) {
      buffer.push_back(std::to_string(*val));
    } else if (auto val = context->GetStringCommonAttr(input_attr.attr_name)) {
      buffer.push_back(val->data());
    } else {
      CL_LOG_ERROR_EVERY("GeneralCityHashEnricher", "common_attr.not_int_or_str", 10000)
          << "attr " << input_attr.attr_name << " should be int/string";
      return;
    }
  }
  auto concat_key = absl::StrJoin(buffer.begin(), buffer.end(), delimiter_);
  auto hash_val = base::CityHash64(concat_key.data(), concat_key.size());
  if (overflow_protect_) {
    hash_val &= INT64_MAX;
  }
  context->SetIntCommonAttr(output_attr_name_, hash_val);
}

bool GeneralCityHashEnricher::ExtractCommonAttrIndexMap(
    MutableRecoContextInterface *context, std::unordered_map<int32_t, std::string>* attr_map) {
  for (int i = 0; i < input_attrs_.size(); ++i) {
    const auto &input_attr = input_attrs_[i];
    if (input_attr.is_common) {
      if (auto val = context->GetIntCommonAttr(input_attr.attr_name)) {
        attr_map->emplace(i, std::to_string(*val));
      } else if (auto val = context->GetStringCommonAttr(input_attr.attr_name)) {
        attr_map->emplace(i, val->data());
      } else {
        CL_LOG_ERROR_EVERY("GeneralCityHashEnricher", "common_for_item.not_int_or_str", 10000)
            << "attr " << input_attr.attr_name << " should be int/string";
        return false;
      }
    }
  }
  return true;
}


void GeneralCityHashEnricher::ProcessItem(
    MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end) {
  std::unordered_map<int32_t, std::string> common_attr_map;
  if (!ExtractCommonAttrIndexMap(context, &common_attr_map)) {
    return;
  }
  auto output_attr_acc = context->GetItemAttrAccessor(output_attr_name_);
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    std::vector<std::string> buffer;
    for (int i = 0; i < input_attrs_.size(); ++i) {
      const auto &input_attr = input_attrs_[i];
      auto common_it = common_attr_map.find(i);
      if (common_it != common_attr_map.end()) {
        buffer.push_back(common_it->second);
      } else {
        auto item_attr_acc = context->GetItemAttrAccessor(input_attr.attr_name);
        if (auto val = context->GetIntItemAttr(result, item_attr_acc)) {
          buffer.push_back(std::to_string(*val));
        } else if (auto val = context->GetStringItemAttr(result, item_attr_acc)) {
          buffer.push_back(val->data());
        } else {
          CL_LOG_ERROR_EVERY("GeneralCityHashEnricher", "item_attr.not_int_or_str", 10000)
              << "attr " << input_attr.attr_name << " should be int/string";
          return;
        }
      }
    }
    auto concat_key = absl::StrJoin(buffer.begin(), buffer.end(), delimiter_);
    auto hash_val = base::CityHash64(concat_key.data(), concat_key.size());
    if (overflow_protect_) {
        hash_val &= INT64_MAX;
    }
    context->SetIntItemAttr(result, output_attr_acc, hash_val);
  });
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, GeneralCityHashEnricher, GeneralCityHashEnricher)

}  // namespace platform
}  // namespace ks
