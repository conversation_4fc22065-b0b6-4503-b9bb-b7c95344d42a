#include "dragon/src/processor/common/enricher/common_reco_attr_type_cast_enricher.h"

#include "base/strings/string_number_conversions.h"
#include "third_party/abseil/absl/strings/numbers.h"
#include "third_party/abseil/absl/strings/str_format.h"
namespace ks {
namespace platform {

// NOLINT
void CommonRecoAttrTypeCastEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                            RecoResultConstIter end) {
  if (!initialized_) {
    initialized_ = true;
    for (auto &item_attr_type_cast_config : item_attr_type_cast_configs_) {
      if (!item_attr_type_cast_config.from_item_attr.empty()) {
        item_attr_type_cast_config.from_accessor =
            context->GetItemAttrAccessor(item_attr_type_cast_config.from_item_attr);
      }
      if (!item_attr_type_cast_config.to_item_attr.empty()) {
        item_attr_type_cast_config.to_accessor =
            context->GetItemAttrAccessor(item_attr_type_cast_config.to_item_attr);
      }
    }
  }
  for (const auto &common_attr_type_cast_config : common_attr_type_cast_configs_) {
    AttrType from_type = context->GetCommonAttrType(common_attr_type_cast_config.from_common_attr);
    AttrType to_type = common_attr_type_cast_config.to_type;
    if (!context->HasCommonAttr(common_attr_type_cast_config.from_common_attr)) {
      continue;
    }
    switch (from_type) {
      case AttrType::INT: {
        auto int_val_opt = context->GetIntCommonAttr(common_attr_type_cast_config.from_common_attr);
        switch (to_type) {
          case AttrType::INT: {
            context->SetIntCommonAttr(common_attr_type_cast_config.to_common_attr, *int_val_opt);
            break;
          }
          case AttrType::FLOAT: {
            context->SetDoubleCommonAttr(common_attr_type_cast_config.to_common_attr,
                                         static_cast<double>(*int_val_opt));
            break;
          }
          case AttrType::STRING: {
            context->SetStringCommonAttr(common_attr_type_cast_config.to_common_attr,
                                         base::Int64ToString(*int_val_opt));
            break;
          }
          case AttrType::FLOAT32: {
            context->SetFloat32CommonAttr(common_attr_type_cast_config.to_common_attr,
                                          static_cast<float>(*int_val_opt));
            break;
          }
          case AttrType::FLOAT16: {
            context->SetFloat16CommonAttr(common_attr_type_cast_config.to_common_attr,
                                          static_cast<float16_t>(static_cast<double>(*int_val_opt)));
            break;
          }
          default:
            break;
        }
      } break;
      case AttrType::FLOAT: {
        auto double_val_opt = context->GetDoubleCommonAttr(common_attr_type_cast_config.from_common_attr);
        switch (to_type) {
          case AttrType::INT: {
            context->SetIntCommonAttr(common_attr_type_cast_config.to_common_attr,
                                      static_cast<int64>(*double_val_opt));
            break;
          }
          case AttrType::FLOAT: {
            context->SetDoubleCommonAttr(common_attr_type_cast_config.to_common_attr, *double_val_opt);
            break;
          }
          case AttrType::STRING: {
            context->SetStringCommonAttr(common_attr_type_cast_config.to_common_attr,
                                         absl::StrFormat("%f", *double_val_opt));
            break;
          }
          case AttrType::FLOAT32: {
            context->SetFloat32CommonAttr(common_attr_type_cast_config.to_common_attr,
                                          static_cast<float>(*double_val_opt));
            break;
          }
          case AttrType::FLOAT16: {
            context->SetFloat16CommonAttr(common_attr_type_cast_config.to_common_attr,
                                          static_cast<float16_t>(*double_val_opt));
            break;
          }
          default:
            break;
        }
      } break;
      case AttrType::STRING: {
        auto string_val_opt = context->GetStringCommonAttr(common_attr_type_cast_config.from_common_attr);

        switch (to_type) {
          case AttrType::INT: {
            int64 int_val;
            if (absl::SimpleAtoi(*string_val_opt, &int_val)) {
              context->SetIntCommonAttr(common_attr_type_cast_config.to_common_attr, int_val);
            }
            break;
          }
          case AttrType::FLOAT: {
            double double_val;
            if (absl::SimpleAtod(*string_val_opt, &double_val)) {
              context->SetDoubleCommonAttr(common_attr_type_cast_config.to_common_attr, double_val);
            }
            break;
          }
          case AttrType::STRING: {
            context->SetStringCommonAttr(common_attr_type_cast_config.to_common_attr,
                                         std::string(*string_val_opt));
            break;
          }
          case AttrType::FLOAT32: {
            float float_val;
            if (absl::SimpleAtof(*string_val_opt, &float_val)) {
              context->SetFloat32CommonAttr(common_attr_type_cast_config.to_common_attr, float_val);
            }
            break;
          }
          case AttrType::FLOAT16: {
            float float_val;
            if (absl::SimpleAtof(*string_val_opt, &float_val)) {
              context->SetFloat16CommonAttr(common_attr_type_cast_config.to_common_attr,
                                            float16_t(float_val));
            }
            break;
          }

          default:
            break;
        }
      } break;
      case AttrType::INT_LIST: {
        auto int_list_val_opt = context->GetIntListCommonAttr(common_attr_type_cast_config.from_common_attr);
        switch (to_type) {
          case AttrType::INT: {
            std::vector<int64> int_list_val;
            for (auto int_val : *int_list_val_opt) {
              int_list_val.push_back(int_val);
            }
            context->SetIntListCommonAttr(common_attr_type_cast_config.to_common_attr,
                                          std::move(int_list_val));
            break;
          }
          case AttrType::FLOAT: {
            std::vector<double> double_list_val;
            for (auto int_val : *int_list_val_opt) {
              double_list_val.push_back(static_cast<double>(int_val));
            }
            context->SetDoubleListCommonAttr(common_attr_type_cast_config.to_common_attr,
                                             std::move(double_list_val));
            break;
          }
          case AttrType::STRING: {
            std::vector<std::string> string_list_val;
            for (auto int_val : *int_list_val_opt) {
              string_list_val.push_back(base::Int64ToString(int_val));
            }
            context->SetStringListCommonAttr(common_attr_type_cast_config.to_common_attr,
                                             std::move(string_list_val));
            break;
          }
          case AttrType::FLOAT32: {
            std::vector<float> float_list_val;
            for (auto int_val : *int_list_val_opt) {
              float_list_val.push_back(static_cast<float>(int_val));
            }
            context->SetFloat32ListCommonAttr(common_attr_type_cast_config.to_common_attr,
                                              std::move(float_list_val));
            break;
          }
          case AttrType::FLOAT16: {
            std::vector<float16_t> float_list_val;
            for (auto int_val : *int_list_val_opt) {
              float_list_val.push_back(static_cast<float16_t>(static_cast<double>(int_val)));
            }
            context->SetFloat16ListCommonAttr(common_attr_type_cast_config.to_common_attr,
                                              std::move(float_list_val));
            break;
          }
          default:
            break;
        }
      } break;
      case AttrType::FLOAT_LIST: {
        auto double_list_val_opt =
            context->GetDoubleListCommonAttr(common_attr_type_cast_config.from_common_attr);
        switch (to_type) {
          case AttrType::INT: {
            std::vector<int64> int_list_val;
            for (auto double_val : *double_list_val_opt) {
              int_list_val.push_back(static_cast<int64>(double_val));
            }
            context->SetIntListCommonAttr(common_attr_type_cast_config.to_common_attr,
                                          std::move(int_list_val));
            break;
          }
          case AttrType::FLOAT: {
            std::vector<double> double_list_val;
            for (auto double_val : *double_list_val_opt) {
              double_list_val.push_back(double_val);
            }
            context->SetDoubleListCommonAttr(common_attr_type_cast_config.to_common_attr,
                                             std::move(double_list_val));
            break;
          }
          case AttrType::STRING: {
            std::vector<std::string> string_list_val;
            for (auto double_val : *double_list_val_opt) {
              string_list_val.push_back(absl::StrFormat("%f", double_val));
            }
            context->SetStringListCommonAttr(common_attr_type_cast_config.to_common_attr,
                                             std::move(string_list_val));
            break;
          }
          case AttrType::FLOAT32: {
            std::vector<float> float_list_val;
            for (auto double_val : *double_list_val_opt) {
              float_list_val.push_back(static_cast<float>(double_val));
            }
            context->SetFloat32ListCommonAttr(common_attr_type_cast_config.to_common_attr,
                                              std::move(float_list_val));
            break;
          }
          case AttrType::FLOAT16: {
            std::vector<float16_t> float_list_val;
            for (auto double_val : *double_list_val_opt) {
              float_list_val.push_back(static_cast<float16_t>(double_val));
            }
            context->SetFloat16ListCommonAttr(common_attr_type_cast_config.to_common_attr,
                                              std::move(float_list_val));
            break;
          }
          default:
            break;
        }
      } break;
      case AttrType::STRING_LIST: {
        auto string_list_val_opt =
            context->GetStringListCommonAttr(common_attr_type_cast_config.from_common_attr);
        switch (to_type) {
          case AttrType::INT: {
            std::vector<int64> int_list_val;
            for (auto &string_val : *string_list_val_opt) {
              int64 int_val;
              if (absl::SimpleAtoi(string_val, &int_val)) {
                int_list_val.push_back(int_val);
              }
            }
            context->SetIntListCommonAttr(common_attr_type_cast_config.to_common_attr,
                                          std::move(int_list_val));
            break;
          }
          case AttrType::FLOAT: {
            std::vector<double> double_list_val;
            for (auto &string_val : *string_list_val_opt) {
              double double_val;
              if (absl::SimpleAtod(string_val, &double_val)) {
                double_list_val.push_back(double_val);
              }
            }
            context->SetDoubleListCommonAttr(common_attr_type_cast_config.to_common_attr,
                                             std::move(double_list_val));
            break;
          }
          case AttrType::STRING: {
            std::vector<std::string> string_list_val;
            for (auto &string_val : *string_list_val_opt) {
              string_list_val.push_back(std::string(string_val));
            }
            context->SetStringListCommonAttr(common_attr_type_cast_config.to_common_attr,
                                             std::move(string_list_val));
            break;
          }
          case AttrType::FLOAT32: {
            std::vector<float> float_list_val;
            for (auto &string_val : *string_list_val_opt) {
              float float_val;
              if (absl::SimpleAtof(string_val, &float_val)) {
                float_list_val.push_back(float_val);
              }
            }
            context->SetFloat32ListCommonAttr(common_attr_type_cast_config.to_common_attr,
                                              std::move(float_list_val));
            break;
          }
          case AttrType::FLOAT16: {
            std::vector<float16_t> float_list_val;
            for (auto &string_val : *string_list_val_opt) {
              float float_val;
              if (absl::SimpleAtof(string_val, &float_val)) {
                float_list_val.push_back(float16_t(float_val));
              }
            }
            context->SetFloat16ListCommonAttr(common_attr_type_cast_config.to_common_attr,
                                              std::move(float_list_val));
            break;
          }
          default:
            break;
        }
      } break;
      case AttrType::FLOAT16_LIST: {
        auto fp16_list_val_opt =
            context->GetDoubleListCommonAttr(common_attr_type_cast_config.from_common_attr);
        switch (to_type) {
          case AttrType::INT: {
            if (fp16_list_val_opt) {
              std::vector<int64> int_list_val;
              for (auto fp16_val : *fp16_list_val_opt) {
                int_list_val.push_back(static_cast<int64>(fp16_val));
              }
              context->SetIntListCommonAttr(common_attr_type_cast_config.to_common_attr,
                                            std::move(int_list_val));
              break;
            }
          }
          case AttrType::FLOAT: {
            if (fp16_list_val_opt) {
              std::vector<double> double_list_val;
              for (auto fp16_val : *fp16_list_val_opt) {
                double_list_val.push_back(fp16_val);
              }
              context->SetDoubleListCommonAttr(common_attr_type_cast_config.to_common_attr,
                                               std::move(double_list_val));
              break;
            }
          }
          case AttrType::STRING: {
            if (fp16_list_val_opt) {
              std::vector<std::string> string_list_val;
              for (auto fp16_val : *fp16_list_val_opt) {
                string_list_val.push_back(absl::StrFormat("%f", fp16_val));
              }
              context->SetStringListCommonAttr(common_attr_type_cast_config.to_common_attr,
                                               std::move(string_list_val));
              break;
            }
          }
          case AttrType::FLOAT32: {
            if (fp16_list_val_opt) {
              std::vector<float> float_list_val;
              for (auto fp16_val : *fp16_list_val_opt) {
                float_list_val.push_back(static_cast<float>(fp16_val));
              }
              context->SetFloat32ListCommonAttr(common_attr_type_cast_config.to_common_attr,
                                                std::move(float_list_val));
              break;
            }
          }
          case AttrType::FLOAT16: {
            if (fp16_list_val_opt) {
              std::vector<float16_t> float_list_val;
              for (auto fp16_val : *fp16_list_val_opt) {
                float_list_val.push_back(static_cast<float16_t>(fp16_val));
              }
              context->SetFloat16ListCommonAttr(common_attr_type_cast_config.to_common_attr,
                                                std::move(float_list_val));
              break;
            }
          }
          default:
            break;
        }
      } break;
      default:
        break;
    }
  }
  for (const auto &item_attr_type_cast_config : item_attr_type_cast_configs_) {
    auto from_type = item_attr_type_cast_config.from_accessor->value_type;
    auto to_type = item_attr_type_cast_config.to_type;
    switch (from_type) {
      case AttrType::INT: {
        switch (to_type) {
          case AttrType::INT: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto int_val_opt = context->GetIntItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (int_val_opt) {
                context->SetIntItemAttr(result, item_attr_type_cast_config.to_accessor, *int_val_opt);
              }
            });
            break;
          }
          case AttrType::FLOAT: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto int_val_opt = context->GetIntItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (int_val_opt) {
                context->SetDoubleItemAttr(result, item_attr_type_cast_config.to_accessor,
                                           static_cast<double>(*int_val_opt));
              }
            });
            break;
          }
          case AttrType::STRING: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto int_val_opt = context->GetIntItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (int_val_opt) {
                context->SetStringItemAttr(result, item_attr_type_cast_config.to_accessor,
                                           base::Int64ToString(*int_val_opt));
              }
            });
            break;
          }
          case AttrType::FLOAT32: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto int_val_opt = context->GetIntItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (int_val_opt) {
                context->SetFloat32ItemAttr(result, item_attr_type_cast_config.to_accessor,
                                            static_cast<float>(*int_val_opt));
              }
            });
            break;
          }
          case AttrType::FLOAT16: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto int_val_opt = context->GetIntItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (int_val_opt) {
                context->SetFloat16ItemAttr(result, item_attr_type_cast_config.to_accessor,
                                            static_cast<float16_t>(static_cast<float>(*int_val_opt)));
              }
            });
            break;
          }
          default:
            break;
        }
      } break;
      case AttrType::FLOAT: {
        switch (to_type) {
          case AttrType::INT: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto double_val_opt =
                  context->GetDoubleItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (double_val_opt) {
                context->SetIntItemAttr(result, item_attr_type_cast_config.to_accessor,
                                        static_cast<int64>(*double_val_opt));
              }
            });
            break;
          }
          case AttrType::FLOAT: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto double_val_opt =
                  context->GetDoubleItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (double_val_opt) {
                context->SetDoubleItemAttr(result, item_attr_type_cast_config.to_accessor, *double_val_opt);
              }
            });
            break;
          }
          case AttrType::STRING: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto double_val_opt =
                  context->GetDoubleItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (double_val_opt) {
                context->SetStringItemAttr(result, item_attr_type_cast_config.to_accessor,
                                           absl::StrFormat("%f", *double_val_opt));
              }
            });
            break;
          }
          case AttrType::FLOAT32: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto double_val_opt =
                  context->GetDoubleItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (double_val_opt) {
                context->SetFloat32ItemAttr(result, item_attr_type_cast_config.to_accessor,
                                            static_cast<float>(*double_val_opt));
              }
            });
            break;
          }
          case AttrType::FLOAT16: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto double_val_opt =
                  context->GetDoubleItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (double_val_opt) {
                context->SetFloat16ItemAttr(result, item_attr_type_cast_config.to_accessor,
                                            static_cast<float16_t>(*double_val_opt));
              }
            });
            break;
          }
          default:
            break;
        }
      } break;
      case AttrType::STRING: {
        switch (to_type) {
          case AttrType::INT: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto string_val_opt =
                  context->GetStringItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (string_val_opt) {
                int64 int_val;
                if (absl::SimpleAtoi(*string_val_opt, &int_val)) {
                  context->SetIntItemAttr(result, item_attr_type_cast_config.to_accessor, int_val);
                }
              }
            });
            break;
          }
          case AttrType::FLOAT: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto string_val_opt =
                  context->GetStringItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (string_val_opt) {
                double double_val;
                if (absl::SimpleAtod(*string_val_opt, &double_val)) {
                  context->SetDoubleItemAttr(result, item_attr_type_cast_config.to_accessor, double_val);
                }
              }
            });
            break;
          }
          case AttrType::STRING: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto string_val_opt =
                  context->GetStringItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (string_val_opt) {
                context->SetStringItemAttr(result, item_attr_type_cast_config.to_accessor,
                                           std::string(*string_val_opt));
              }
            });
            break;
          }
          case AttrType::FLOAT32: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto string_val_opt =
                  context->GetStringItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (string_val_opt) {
                float float_val;
                if (absl::SimpleAtof(*string_val_opt, &float_val)) {
                  context->SetFloat32ItemAttr(result, item_attr_type_cast_config.to_accessor, float_val);
                }
              }
            });
            break;
          }
          case AttrType::FLOAT16: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto string_val_opt =
                  context->GetStringItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (string_val_opt) {
                float float_val;
                if (absl::SimpleAtof(*string_val_opt, &float_val)) {
                  context->SetFloat16ItemAttr(result, item_attr_type_cast_config.to_accessor,
                                              float16_t(float_val));
                }
              }
            });
            break;
          }
          default:
            break;
        }
      } break;
      case AttrType::INT_LIST: {
        switch (to_type) {
          case AttrType::INT: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto int_list_val_opt =
                  context->GetIntListItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (int_list_val_opt) {
                std::vector<int64> int_list_val;
                for (auto &val : *int_list_val_opt) {
                  int_list_val.push_back(val);
                }
                context->SetIntListItemAttr(result, item_attr_type_cast_config.to_accessor,
                                            std::move(int_list_val));
              }
            });
            break;
          }
          case AttrType::FLOAT: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto int_list_val_opt =
                  context->GetIntListItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (int_list_val_opt) {
                std::vector<double> double_list_val;
                for (auto &val : *int_list_val_opt) {
                  double_list_val.push_back(static_cast<double>(val));
                }
                context->SetDoubleListItemAttr(result, item_attr_type_cast_config.to_accessor,
                                               std::move(double_list_val));
              }
            });
            break;
          }
          case AttrType::FLOAT32: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto int_list_val_opt =
                  context->GetIntListItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (int_list_val_opt) {
                std::vector<float> float32_list_val;
                for (auto &val : *int_list_val_opt) {
                  float32_list_val.push_back(static_cast<float>(val));
                }
                context->SetFloat32ListItemAttr(result, item_attr_type_cast_config.to_accessor,
                                                std::move(float32_list_val));
              }
            });
            break;
          }
          case AttrType::FLOAT16: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto int_list_val_opt =
                  context->GetIntListItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (int_list_val_opt) {
                std::vector<float16_t> float16_list_val;
                for (auto &val : *int_list_val_opt) {
                  float16_list_val.push_back(static_cast<float16_t>(static_cast<float>(val)));
                }
                context->SetFloat16ListItemAttr(result, item_attr_type_cast_config.to_accessor,
                                                std::move(float16_list_val));
              }
            });
            break;
          }
          case AttrType::STRING: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto int_list_val_opt =
                  context->GetIntListItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (int_list_val_opt) {
                std::vector<std::string> string_list_val;
                for (auto &val : *int_list_val_opt) {
                  string_list_val.push_back(base::Int64ToString(val));
                }
                context->SetStringListItemAttr(result, item_attr_type_cast_config.to_accessor,
                                               std::move(string_list_val));
              }
            });
            break;
          }
          default:
            break;
        }
      } break;
      case AttrType::FLOAT_LIST: {
        switch (to_type) {
          case AttrType::INT: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto double_list_val_opt =
                  context->GetDoubleListItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (double_list_val_opt) {
                std::vector<int64> int_list_val;
                for (auto &val : *double_list_val_opt) {
                  int_list_val.push_back(static_cast<int64>(val));
                }
                context->SetIntListItemAttr(result, item_attr_type_cast_config.to_accessor,
                                            std::move(int_list_val));
              }
            });
            break;
          }
          case AttrType::FLOAT: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto double_list_val_opt =
                  context->GetDoubleListItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (double_list_val_opt) {
                std::vector<double> double_list_val;
                for (auto &val : *double_list_val_opt) {
                  double_list_val.push_back(val);
                }
                context->SetDoubleListItemAttr(result, item_attr_type_cast_config.to_accessor,
                                               std::move(double_list_val));
              }
            });
            break;
          }
          case AttrType::STRING: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto double_list_val_opt =
                  context->GetDoubleListItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (double_list_val_opt) {
                std::vector<std::string> string_list_val;
                for (auto &val : *double_list_val_opt) {
                  string_list_val.push_back(absl::StrFormat("%f", val));
                }
                context->SetStringListItemAttr(result, item_attr_type_cast_config.to_accessor,
                                               std::move(string_list_val));
              }
            });
            break;
          }
          case AttrType::FLOAT32: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto double_list_val_opt =
                  context->GetDoubleListItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (double_list_val_opt) {
                std::vector<float> float32_list_val;
                for (auto &val : *double_list_val_opt) {
                  float32_list_val.push_back(static_cast<float>(val));
                }
                context->SetFloat32ListItemAttr(result, item_attr_type_cast_config.to_accessor,
                                                std::move(float32_list_val));
              }
            });
            break;
          }
          case AttrType::FLOAT16: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto double_list_val_opt =
                  context->GetDoubleListItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (double_list_val_opt) {
                std::vector<float16_t> float16_list_val;
                for (auto &val : *double_list_val_opt) {
                  float16_list_val.push_back(static_cast<float16_t>(val));
                }
                context->SetFloat16ListItemAttr(result, item_attr_type_cast_config.to_accessor,
                                                std::move(float16_list_val));
              }
            });
            break;
          }
          default:
            break;
        }
      } break;
      case AttrType::STRING_LIST: {
        switch (to_type) {
          case AttrType::INT: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto string_list_val_opt =
                  context->GetStringListItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (string_list_val_opt) {
                std::vector<int64> int_list_val;
                int64 int_val;
                for (auto &val : *string_list_val_opt) {
                  if (absl::SimpleAtoi(val, &int_val)) {
                    int_list_val.push_back(int_val);
                  }
                }
                context->SetIntListItemAttr(result, item_attr_type_cast_config.to_accessor,
                                            std::move(int_list_val));
              }
            });
            break;
          }
          case AttrType::FLOAT: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto string_list_val_opt =
                  context->GetStringListItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (string_list_val_opt) {
                std::vector<double> double_list_val;
                double double_val;
                for (auto &val : *string_list_val_opt) {
                  if (absl::SimpleAtod(val, &double_val)) {
                    double_list_val.push_back(double_val);
                  }
                }
                context->SetDoubleListItemAttr(result, item_attr_type_cast_config.to_accessor,
                                               std::move(double_list_val));
              }
            });
            break;
          }
          case AttrType::STRING: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto string_list_val_opt =
                  context->GetStringListItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (string_list_val_opt) {
                std::vector<std::string> string_list_val;
                for (auto &val : *string_list_val_opt) {
                  string_list_val.push_back(std::string(val));
                }
                context->SetStringListItemAttr(result, item_attr_type_cast_config.to_accessor,
                                               std::move(string_list_val));
              }
            });
            break;
          }
          case AttrType::FLOAT32: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto string_list_val_opt =
                  context->GetStringListItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (string_list_val_opt) {
                std::vector<float> float_list_val;
                float float_val;
                for (auto &val : *string_list_val_opt) {
                  if (absl::SimpleAtof(val, &float_val)) {
                    float_list_val.push_back(float_val);
                  }
                }
                context->SetFloat32ListItemAttr(result, item_attr_type_cast_config.to_accessor,
                                                std::move(float_list_val));
              }
            });
            break;
          }
          case AttrType::FLOAT16: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto string_list_val_opt =
                  context->GetStringListItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (string_list_val_opt) {
                std::vector<float16_t> float_list_val;
                float float_val;
                for (auto &val : *string_list_val_opt) {
                  if (absl::SimpleAtof(val, &float_val)) {
                    float_list_val.push_back(float16_t(float_val));
                  }
                }
                context->SetFloat16ListItemAttr(result, item_attr_type_cast_config.to_accessor,
                                                std::move(float_list_val));
              }
            });
            break;
          }
          default:
            break;
        }
      } break;
      case AttrType::FLOAT16_LIST: {
        switch (to_type) {
          case AttrType::INT: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto fp16_list_val_opt =
                  context->GetDoubleListItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (fp16_list_val_opt) {
                std::vector<int64> int_list_val;
                for (auto &val : *fp16_list_val_opt) {
                  int_list_val.push_back(static_cast<int64>(val));
                }
                context->SetIntListItemAttr(result, item_attr_type_cast_config.to_accessor,
                                            std::move(int_list_val));
              }
            });
            break;
          }
          case AttrType::FLOAT: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto fp16_list_val_opt =
                  context->GetDoubleListItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (fp16_list_val_opt) {
                std::vector<double> double_list_val;
                for (auto &val : *fp16_list_val_opt) {
                  double_list_val.push_back(val);
                }
                context->SetDoubleListItemAttr(result, item_attr_type_cast_config.to_accessor,
                                               std::move(double_list_val));
              }
            });
            break;
          }
          case AttrType::STRING: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto fp16_list_val_opt =
                  context->GetDoubleListItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (fp16_list_val_opt) {
                std::vector<std::string> string_list_val;
                for (auto &val : *fp16_list_val_opt) {
                  string_list_val.push_back(absl::StrFormat("%f", val));
                }
                context->SetStringListItemAttr(result, item_attr_type_cast_config.to_accessor,
                                               std::move(string_list_val));
              }
            });
            break;
          }
          case AttrType::FLOAT32: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto fp16_list_val_opt =
                  context->GetDoubleListItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (fp16_list_val_opt) {
                std::vector<float> float32_list_val;
                for (auto &val : *fp16_list_val_opt) {
                  float32_list_val.push_back(static_cast<float>(val));
                }
                context->SetFloat32ListItemAttr(result, item_attr_type_cast_config.to_accessor,
                                                std::move(float32_list_val));
              }
            });
            break;
          }
          case AttrType::FLOAT16: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto fp16_list_val_opt =
                  context->GetDoubleListItemAttr(result, item_attr_type_cast_config.from_accessor);
              if (fp16_list_val_opt) {
                std::vector<float16_t> float16_list_val;
                for (auto &val : *fp16_list_val_opt) {
                  float16_list_val.push_back(static_cast<float16_t>(val));
                }
                context->SetFloat16ListItemAttr(result, item_attr_type_cast_config.to_accessor,
                                                std::move(float16_list_val));
              }
            });
            break;
          }
          default:
            break;
        }
      } break;
      case AttrType::FLOAT32: {
        switch (to_type) {
          case AttrType::FLOAT: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto fp32_val_opt =
                  context->GetSingleItemAttr<float>(result, item_attr_type_cast_config.from_accessor);
              if (fp32_val_opt) {
                context->SetDoubleItemAttr(result, item_attr_type_cast_config.to_accessor,
                                           static_cast<double>(*fp32_val_opt));
              }
            });
            break;
          }
          default:
            break;
        }
      } break;
      case AttrType::FLOAT32_LIST: {
        switch (to_type) {
          case AttrType::FLOAT: {
            std::for_each(begin, end, [&](const CommonRecoResult &result) {
              auto fp32_list_val_opt =
                  context->GetListItemAttr<float>(result, item_attr_type_cast_config.from_accessor);
              if (fp32_list_val_opt) {
                std::vector<double> double_list_val;
                for (auto &val : *fp32_list_val_opt) {
                  double_list_val.push_back(val);
                }
                context->SetDoubleListItemAttr(result, item_attr_type_cast_config.to_accessor,
                                               std::move(double_list_val));
              }
            });
            break;
          }
          default:
            break;
        }
      } break;
      default:
        break;
    }
  }
}  // NOLINT

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoAttrTypeCastEnricher, CommonRecoAttrTypeCastEnricher)

}  // namespace platform
}  // namespace ks
