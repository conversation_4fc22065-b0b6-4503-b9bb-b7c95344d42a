#include "dragon/src/processor/common/enricher/common_reco_calc_by_simple_formula_enricher.h"

namespace ks {
namespace platform {

void CommonRecoCalcBySimpleFormulaEnricher::Enrich(MutableRecoContextInterface *context,
                                                   RecoResultConstIter begin, RecoResultConstIter end) {
  for (auto &formula : formulas_) {
    // 填充 common_attrs
    for (int i = 0; i < formula.common_attrs.size(); ++i) {
      formula.values[i] = GetCommonAttrValue(context, formula.common_attrs[i]);
    }

    // 处理 common 侧公式
    if (formula.is_common) {
      double result = formula.expression.value();
      if (!formula.to_int) {
        context->SetDoubleCommonAttr(formula.output_attr, result);
      } else {
        context->SetIntCommonAttr(formula.output_attr, result);
      }
      continue;
    }

    // 处理 item 侧公式
    formula.item_attr_accessors.clear();
    formula.item_attr_accessors.reserve(formula.item_attrs.size());
    for (const auto &attr_name : formula.item_attrs) {
      formula.item_attr_accessors.push_back(context->GetItemAttrAccessor(attr_name));
    }
    auto output_attr_accessor = context->GetItemAttrAccessor(formula.output_attr);

    for (auto iter = begin; iter != end; ++iter) {
      // 填充 item_attrs
      for (int i = 0; i < formula.item_attrs.size(); ++i) {
        double val = GetItemAttrValue(context, formula.item_attr_accessors[i], *iter);
        formula.values[i + formula.common_attrs.size()] = val;
      }
      double result = formula.expression.value();
      if (!formula.to_int) {
        context->SetDoubleItemAttr(*iter, output_attr_accessor, result);
      } else {
        context->SetIntItemAttr(*iter, output_attr_accessor, result);
      }
    }
  }
}

double CommonRecoCalcBySimpleFormulaEnricher::GetCommonAttrValue(MutableRecoContextInterface *context,
                                                                 const std::string &attr_name) {
  auto common_attr = context->GetCommonAttrAccessor(attr_name);
  switch (common_attr->value_type) {
    case AttrType::FLOAT: {
      return context->GetDoubleCommonAttr(common_attr).value_or(0.0);
    } break;
    case AttrType::INT: {
      return context->GetIntCommonAttr(common_attr).value_or(0);
    } break;
    default:
      CL_LOG_WARNING("simple_formula", "import_common_attr_fail: " + common_attr->name())
          << "import common_attr '" << common_attr->name()
          << "' fail, unsupported attr type: " << RecoUtil::GetAttrTypeName(common_attr->value_type);
      break;
  }
  return 0.0;
}

double CommonRecoCalcBySimpleFormulaEnricher::GetItemAttrValue(MutableRecoContextInterface *context,
                                                               ItemAttr *item_attr,
                                                               const CommonRecoResult &result) {
  switch (item_attr->value_type) {
    case AttrType::FLOAT: {
      return context->GetDoubleItemAttr(result, item_attr).value_or(0.0);
    } break;
    case AttrType::INT: {
      return context->GetIntItemAttr(result, item_attr).value_or(0);
    } break;
    default:
      CL_LOG_WARNING("simple_formula", "import_item_attr_fail: " + item_attr->name())
          << "import item_attr '" << item_attr->name()
          << "' fail, unsupported attr type: " << RecoUtil::GetAttrTypeName(item_attr->value_type);
      break;
  }

  return 0.0;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoCalcBySimpleFormulaEnricher,
                 CommonRecoCalcBySimpleFormulaEnricher)

}  // namespace platform
}  // namespace ks
