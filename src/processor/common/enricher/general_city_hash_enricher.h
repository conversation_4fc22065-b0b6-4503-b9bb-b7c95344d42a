#pragma once

#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class GeneralCityHashEnricher : public CommonRecoBaseEnricher {
 public:
  GeneralCityHashEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  bool InitProcessor() override {
    output_attr_name_ = config()->GetString("output_attr_name");
    if (output_attr_name_.empty()) {
      LOG(ERROR) << "[GeneralCityHashEnricher] init failed, `output_attr_name` should be a non-empty string";
      return false;
    }
    is_common_ = config()->GetBoolean("is_common", true);  // 默认输出 common attr
    overflow_protect_ = config()->GetBoolean("overflow_protect", true);  // 默认开启溢出保护
    const auto *input_attrs = config()->Get("input_attrs");
    if (input_attrs == nullptr || !input_attrs->IsArray() || input_attrs->size() == 0) {
      LOG(ERROR) << "[GeneralCityHashEnricher] init failed, `input_attrs` should be a non-empty list";
      return false;
    }
    for (const auto *attr : input_attrs->array()) {
      std::string attr_name = attr->GetString("name");
      bool attr_is_common = attr->GetBoolean("is_common", true);
      if (attr_name.empty()) {
        LOG(ERROR) << "[GeneralCityHashEnricher] init failed, met empty attr name";
        return false;
      }
      // 输出为 common attr 时输入也必须全部为 common attr
      if (is_common_ && !attr_is_common) {
        LOG(ERROR) << "[GeneralCityHashEnricher] init failed, the output attr is common, "
                   << "this means all input attrs should be common, error attr: "
                   << attr_name;
        return false;
      }
      input_attrs_.emplace_back(std::move(attr_name), attr_is_common);
    }
    delimiter_ = config()->GetString("delimiter");
    return true;
  }

 private:
  inline void ProcessCommon(MutableRecoContextInterface *context);
  inline void ProcessItem(MutableRecoContextInterface *context, RecoResultConstIter begin,
                          RecoResultConstIter end);
  inline bool ExtractCommonAttrIndexMap(MutableRecoContextInterface *context,
                                        std::unordered_map<int32_t, std::string>* attr_map);

 private:
  struct InputAttr {
    std::string attr_name;
    bool is_common;
    InputAttr() {}
    InputAttr(std::string &&name, bool common) : attr_name(std::move(name)), is_common(common) {}
  };

  std::vector<InputAttr> input_attrs_;
  std::string output_attr_name_;
  std::string delimiter_;
  bool is_common_ = true;
  bool overflow_protect_ = true;

  DISALLOW_COPY_AND_ASSIGN(GeneralCityHashEnricher);
};

}  // namespace platform
}  // namespace ks
