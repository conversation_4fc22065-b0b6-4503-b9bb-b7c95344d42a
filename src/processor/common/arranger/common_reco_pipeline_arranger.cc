#include "dragon/src/processor/common/arranger/common_reco_pipeline_arranger.h"

#include <kenv/context.h>
#include <functional>
#include <utility>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/core/single_pipeline_executor.h"
#include "dragon/src/util/ktrace_util.h"
#include "dragon/src/util/perf_report_util.h"
#include "ktrace/core/span.h"
#include "ktrace/core/span_context.h"
#include "ktrace/core/tracer_manager.h"

namespace ks {
namespace platform {

DEFINE_int64(sub_pipeline_thread_num, 600, "thread size limit for sub flow arranger.");

// NOTE(zhaoyang09): this flag is just for arranger to change local thread pool to global pool.
DEFINE_bool(use_global_sub_flow_thread_pool, false, "enable global sub flow thread pool");

std::unique_ptr<base::AsyncFuturePool> pipeline_pool;  // 全局线程池定义
std::once_flag pipeline_arranger_once;                // 控制 pipeline_pool 全局初始化一次

bool CommonRecoPipelineArranger::AddExecutor() {
  auto single_executor = std::make_unique<SinglePipelineExecutor>();
  if (!single_executor || !single_executor->Initialize(pipeline_config_, pipeline_name_)) {
    return false;
  }
  executors_.push_back(std::move(single_executor));
  return true;
}

void CommonRecoPipelineArranger::MergeSubflowStepInfo(AddibleRecoContextInterface *context, int p_num) {
  bool is_first_subflow = true;
  for (int i = 0; i < p_num; i++) {
    const auto &executor = executors_[i];
    for (int j = 0; j < executor->GetProcessorStepInfosCount(); ++j) {
      auto *info = context->GetNewStepInfo();
      if (!is_first_subflow) {
        executor->GetProcessorStepInfo(j)->clear_stage_name();
        executor->GetProcessorStepInfo(j)->clear_json_config();
        executor->GetProcessorStepInfo(j)->clear_common_attrs();
      }
      info->Swap(executor->GetProcessorStepInfo(j));
    }
    is_first_subflow = false;
  }
}

// 分片规则
// NOTICE: 已有分配下标的 RecoResult 下标保持不变
// 1. 提前预分配空间，分片 sub flow 严禁新增任何 item
// 2. 下游使用无感知，对现有逻辑透明
// 3. 需要指定 sub_flow 需要产生的 attr 列（这部分功能是否可以框架外层提供）
int CommonRecoPipelineArranger::Partition(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                          RecoResultConstIter end) {
  // Prepare context
  if (begin == end) {
    return 0;
  }

  auto unique_results = DeduplicateResults(begin, end);
  auto item_num = unique_results.size();

  int expected_partition_size = GetIntProcessorParameter(context, "expected_partition_size", 0);
  if (expected_partition_size <= 0) {
    CL_LOG_WARNING("sub_flow", "invalid_partition_size")
        << "get invalid expected_partition_size = " << expected_partition_size
        << ", using item_num = " << item_num << "instead, processor: " << GetName();
    expected_partition_size = item_num;
  }
  const std::vector<int> partitions = RecoUtil::BalancedPartition(expected_partition_size, item_num);

  // 这里数据拷贝也必须在主 worker 线程，因为框架设计会导致线程不安全
  // 因为会读取 context 里的 attr，该 attr 所使用的 hashmap 线程不安全
  auto partition_begin = unique_results.cbegin();
  for (int i = 0; i < partitions.size(); ++i) {
    auto partition_end = std::next(partition_begin, partitions[i]);
    if (!SetupExecutor(i, context, partition_begin, partition_end)) {
      return -1;
    }
    partition_begin = partition_end;
  }
  return partitions.size();
}

int CommonRecoPipelineArranger::GroupBy(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                        RecoResultConstIter end) {
  ItemAttr *attr_accessor = context->GetItemAttrAccessor(group_by_);
  folly::F14FastMap<int64, std::vector<CommonRecoResult>> group_by_attr;
  std::vector<CommonRecoResult> none_attr_list;
  int group_num = 0;

  if (attr_accessor->value_type == AttrType::INT) {
    for (auto it = begin; it < end; it++) {
      if (auto int_val = context->GetIntItemAttr(*it, attr_accessor)) {
        group_by_attr[*int_val].emplace_back(*it);
      } else {
        none_attr_list.emplace_back(*it);
      }
    }
  } else if (attr_accessor->value_type == AttrType::STRING) {
    for (auto it = begin; it < end; it++) {
      if (auto str_val = context->GetStringItemAttr(*it, attr_accessor)) {
        group_by_attr[base::CityHash64(str_val->data(), str_val->size())].emplace_back(*it);
      } else {
        none_attr_list.emplace_back(*it);
      }
    }
  } else {
    CL_LOG_WARNING("sub_flow", "invalid_group_by_attr")
        << "get invalid group_by: " << group_by_
        << ", is not int/string item_attr value type, set all item in one sub_flow instead.";
    if (SetupExecutor(group_num, context, begin, end)) {
      group_num++;
    } else {
      return -1;
    }
  }

  for (const auto &pr : group_by_attr) {
    if (SetupExecutor(group_num, context, pr.second.cbegin(), pr.second.cend())) {
      group_num++;
    } else {
      return -1;
    }
  }

  if (none_attr_list.size() > 0) {
    if (SetupExecutor(group_num, context, none_attr_list.cbegin(), none_attr_list.cend())) {
      group_num++;
    } else {
      return -1;
    }
  }
  return group_num;
}

bool CommonRecoPipelineArranger::SetupExecutor(int index, MutableRecoContextInterface *context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
  while (executors_.size() <= index) {
    if (!AddExecutor()) return false;
  }
  executors_[index]->Reset(context, fill_common_attrs_from_request_, fill_browse_set_from_request_, nullptr,
                           GetTableName());
  CloneAttr(context, executors_[index]->GetContext());
  executors_[index]->GetContext()->CloneTargetResults(static_cast<CommonRecoContext *>(context), begin, end);

  bool need_subflow_traceback =
      traceback_util::CompareTracebackDataVersion(context->GetTracebackDataVersion(), "v2.0.1") >= 0;
  if (need_subflow_traceback) {
    traceback_util::InitTraceback(context, executors_[index]->GetContext());
  }
  return true;
}

std::vector<std::future<CommonRecoContext *>> CommonRecoPipelineArranger::AsyncWithLocalThreadPool(
    MutableRecoContextInterface *context, int p_num) {
  std::vector<std::future<CommonRecoContext *>> futures;
  futures.reserve(p_num);
  auto snapshot = ks::infra::ktrace::TracerManager::CreateSnapshot();
  for (auto i = 0; i < p_num; ++i) {
    auto executor = executors_[i].get();
    auto kenv_ctx = ks::infra::kenv::Context::Current()->Copy();
    auto future = pipeline_pool->Async<CommonRecoContext *>(
        [executor, processor_name = GetName(), snapshot, kenv_ctx = std::move(kenv_ctx)]() {
          ks::infra::ktrace::TracerManager::Attach(snapshot);
          bool is_sampled = ks::platform::KtraceUtil::IsKtraceEnabledForPipeline() &&
                            ks::infra::ktrace::TracerManager::IsSampled();
          std::shared_ptr<ks::infra::ktrace::Span> local_span;
          if (is_sampled) {
            local_span = ks::infra::ktrace::TracerManager::StartSpan("SUB_FLOW");
            auto kspan = std::static_pointer_cast<ks::infra::ktrace::KSpan>(local_span);
            if (kspan != nullptr) {
              kspan->SetComponentType("LOCAL");
              kspan->SetComponentName(processor_name);
            }
          }
          kenv_ctx->Run([executor]() { executor->Run(); });
          if (local_span) {
            local_span->Finish();
          }
          return executor->GetContext();
        });
    futures.emplace_back(std::move(future));
  }
  return futures;
}

std::vector<std::future<CommonRecoContext *>> CommonRecoPipelineArranger::AsyncWithGlobalThreadPool(
    MutableRecoContextInterface *context, int p_num) {
  std::vector<std::future<CommonRecoContext *>> futures;
  if (executors_.empty()) {
    return futures;
  }
  int task_queue_id = GetIntProcessorParameter(context, "task_queue_id", 0);
  auto sub_flow_thread_pool = executors_[0]->GetAsyncPool(task_queue_id);
  if (!sub_flow_thread_pool) {
    CL_LOG_EXCEPTION("no_thread_pool_for_sub_flow")
        << "PipelineArranger " << GetName() << " task_queue_id: " << task_queue_id
        << " get sub flow thread pool failed";
    return futures;
  }

  auto waiting_task_num = sub_flow_thread_pool->GetQueueSize();
  base::perfutil::PerfUtilWrapper::IntervalLogStash(
      waiting_task_num, kPerfNs, "sub_flow_waiting_task_num", GlobalHolder::GetServiceIdentifier(),
      context->GetRequestType(), absl::StrCat("task_queue: ", task_queue_id));

  futures.reserve(p_num);
  for (int i = 0; i < p_num; ++i) {
    int64 put_in_pool_ts = base::GetTimestamp();
    auto executor = executors_[i].get();
    auto future = sub_flow_thread_pool->Async(
        context->GetRequestType(), GetName(),
        [this, executor, put_in_pool_ts, enable_perf_report = PerfReportUtil::IsEnabled()]() {
          SetPerfEnabledOneshot set_perf_enabled(enable_perf_report);
          int64 execute_start_ts = base::GetTimestamp();
          base::perfutil::PerfUtilWrapper::IntervalLogStash(
              execute_start_ts - put_in_pool_ts, kPerfNs, "sub_flow_queue_time",
              GlobalHolder::GetServiceIdentifier(), executor->GetContext()->GetRequestType(), pipeline_name_);
          executor->Run();
          base::perfutil::PerfUtilWrapper::IntervalLogStash(
              base::GetTimestamp() - execute_start_ts, kPerfNs, "sub_flow_run_time",
              GlobalHolder::GetServiceIdentifier(), executor->GetContext()->GetRequestType(), pipeline_name_);
          return executor->GetContext();
        },
        GetDownstreamProcessor());
    futures.emplace_back(std::move(future));
  }
  return futures;
}

// sync
RecoResultIter CommonRecoPipelineArranger::Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                                                   RecoResultIter end) {
  auto m_iter = std::max_element(
      begin, end, [](const auto &c1, const auto &c2) { return c1.GetAttrIndex() < c2.GetAttrIndex(); });
  for (auto &attr_i : merge_item_attrs_) {
    context->InitItemAttr(attr_i, AttrType::UNKNOWN, m_iter->GetAttrIndex());
  }

  auto p_num = group_by_.empty() ? Partition(context, begin, end) : GroupBy(context, begin, end);
  if (p_num < 0) {
    CL_LOG_ERROR("sub_flow", "create_partition_fail:" + GetName())
        << "failed to create partition for sub_flow: " << GetName();
    return end;
  }

  std::vector<std::future<CommonRecoContext *>> futures;
  if (FLAGS_use_global_sub_flow_thread_pool) {
    futures = AsyncWithGlobalThreadPool(context, p_num);
  } else {
    futures = AsyncWithLocalThreadPool(context, p_num);
  }

  // 将超时或者被 filter 的 item 过滤掉
  folly::F14FastSet<uint64> remain_keys;

  bool is_first_subflow = true;
  CommonRecoContext *main_context = static_cast<CommonRecoContext *>(context);
  for (auto &fut : futures) {
    CommonRecoContext *sub_flow_context = fut.get();
    sub_flow_context->SetMainTable(GetTableName());
    for (auto &v : sub_flow_context->GetCommonRecoResults()) {
      remain_keys.insert(v.item_key);
    }

    if (is_first_subflow) {
      main_context->MergeContext(sub_flow_context, -1, false, merge_common_attrs_, {}, pipeline_name_, false,
                                 false);
      is_first_subflow = false;
    } else {
      main_context->MergeContext(sub_flow_context, -1, false, {}, {}, pipeline_name_, false, false);
    }
    DereferenceAttrs(main_context);
  }

  bool need_subflow_traceback =
      traceback_util::CompareTracebackDataVersion(context->GetTracebackDataVersion(), "v2.0.1") >= 0;
  if (need_subflow_traceback) {
    MergeSubflowStepInfo(main_context, p_num);
  }

  auto new_end = std::remove_if(
      begin, end, [&](const auto &result) { return remain_keys.find(result.item_key) == remain_keys.end(); });

  return new_end;
}

// sub_flow 内部不允许产生 common attr 吧？如果出现这种可能
// 那也没有预分配的必要了，只能额外进行 merge
// 但是不允许，sub flow 产生一个已有的 common attr
void CommonRecoPipelineArranger::CloneAttr(ReadableRecoContextInterface *context,
                                           MutableRecoContextInterface *execute_context) {
  // common attr, read-only
  for (auto &attr_name : shadow_copy_common_attrs_) {
    execute_context->CloneCommonAttr(context, attr_name, true, true);
  }

  // item attr, read-only
  for (auto &attr_name : shadow_copy_item_attrs_) {
    execute_context->CloneItemAttr(context, attr_name, true, true);
  }

  for (auto &attr_name : deep_copy_common_attrs_) {
    execute_context->CloneCommonAttr(context, attr_name, false, false);
  }

  for (auto &attr_name : deep_copy_item_attrs_) {
    execute_context->CloneItemAttr(context, attr_name, false, false);
  }

  for (auto &attr_name : merge_item_attrs_) {
    execute_context->CloneItemAttr(context, attr_name, true, false);
  }
}

void CommonRecoPipelineArranger::DereferenceAttrs(CommonRecoContext *context) {
  for (auto &attr_name : shadow_copy_common_attrs_) {
    context->DereferenceCommonAttr(attr_name);
  }

  for (auto &attr_name : shadow_copy_item_attrs_) {
    context->DereferenceItemAttr(attr_name);
  }

  for (auto &attr_name : merge_item_attrs_) {
    context->DereferenceItemAttr(attr_name);
  }
}

std::vector<CommonRecoResult> CommonRecoPipelineArranger::DeduplicateResults(RecoResultConstIter begin,
                                                                             RecoResultConstIter end) {
  folly::F14FastSet<uint64> deduplicate_results_set;
  std::vector<CommonRecoResult> deduplicate_results;
  int item_num = std::distance(begin, end);
  deduplicate_results.reserve(item_num);
  deduplicate_results_set.reserve(item_num);
  std::for_each(begin, end,
                [&deduplicate_results_set, &deduplicate_results](const CommonRecoResult &result) mutable {
                  if (deduplicate_results_set.insert(result.item_key).second) {
                    deduplicate_results.push_back(result);
                  }
                });
  return deduplicate_results;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoPipelineArranger, CommonRecoPipelineArranger);

}  // namespace platform
}  // namespace ks
