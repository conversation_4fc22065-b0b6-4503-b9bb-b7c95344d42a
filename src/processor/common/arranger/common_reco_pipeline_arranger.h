#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/core/single_pipeline_executor.h"
#include "dragon/src/processor/base/common_reco_base_arranger.h"

namespace ks {
namespace platform {

DECLARE_int64(sub_pipeline_thread_num);
DECLARE_bool(use_global_sub_flow_thread_pool);

extern std::unique_ptr<base::AsyncFuturePool> pipeline_pool;  // 全局线程池声明
extern std::once_flag pipeline_arranger_once;

void SetupLocalThreadPool() {
  if (!FLAGS_use_global_sub_flow_thread_pool) {
    // 使用全局变量初始化 pipeline_pool 有如下问题:
    // 1. 修改了 gflags 无法感知到 (不能调整线程数)
    // 2. 没用 PipelineArranger 算子的时候，也会创建线程池
    pipeline_pool = std::make_unique<base::AsyncFuturePool>(FLAGS_sub_pipeline_thread_num);
  }
}

class CommonRecoPipelineArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoPipelineArranger() {}
  ~CommonRecoPipelineArranger() {}
  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;
  void OnPipelineExit(ReadableRecoContextInterface *context) final {
    for (auto &executor : executors_) {
      executor->HandlePipelineExit();
    }
  }

 private:
  bool InitProcessor() override {
    pipeline_name_ = config()->GetString("flow_name", "");
    if (pipeline_name_.empty()) {
      LOG(ERROR) << "CommonRecoPipelineArranger init failed! Missing "
                    "'flow_name' config";
      return false;
    }

    pipeline_config_ = GlobalHolder::GetDynamicJsonConfig()->Get("pipeline_manager_config");
    // 默认分片数
    const auto initial_parallel_num = config()->GetInt("initial_parallel_num", 8);
    if (initial_parallel_num < 1) {
      LOG(ERROR) << "PipelineExecutor init error: initial_parallel_num is incorrect."
                 << "initial_parallel_num: " << initial_parallel_num;
      return false;
    }

    group_by_ = config()->GetString("group_by");

    for (auto i = 0; i < initial_parallel_num; ++i) {
      if (!AddExecutor()) {
        LOG(ERROR) << "PipelineExecutor init error: create single executor failed.";
        return false;
      }
    }

    // 进入 sub pipeline 之前的两类属性，在 sub pipeline 中必须为 read-only
    folly::F14FastSet<std::string> copy_common_attrs;
    RecoUtil::ExtractStringSetFromJsonConfig(config()->Get("pass_common_attrs"), &copy_common_attrs);
    SplitAttrsByCopySemantics(copy_common_attrs, "deep_copy_common_attrs", &deep_copy_common_attrs_,
                              &shadow_copy_common_attrs_);

    folly::F14FastSet<std::string> copy_item_attrs;
    RecoUtil::ExtractStringSetFromJsonConfig(config()->Get("pass_item_attrs"), &copy_item_attrs);
    SplitAttrsByCopySemantics(copy_item_attrs, "deep_copy_item_attrs", &deep_copy_item_attrs_,
                              &shadow_copy_item_attrs_);

    auto *merge_common_attrs = config()->Get("merge_common_attrs");

    std::vector<std::string> merge_common_attrs_list;
    RecoUtil::ExtractStringListFromJsonConfig(merge_common_attrs, &merge_common_attrs_list);

    merge_common_attrs_.clear();
    for (auto &attr : merge_common_attrs_list) {
      merge_common_attrs_.insert({attr, attr});
    }

    auto *merge_item_attrs = config()->Get("merge_item_attrs");
    RecoUtil::ExtractStringListFromJsonConfig(merge_item_attrs, &merge_item_attrs_);

    fill_common_attrs_from_request_ = config()->GetBoolean("pass_common_attrs_in_request", false);
    fill_browse_set_from_request_ = config()->GetBoolean("pass_browse_set", true);

    std::call_once(pipeline_arranger_once, SetupLocalThreadPool);

    SubFlowThreadPoolManager::GetInstance()->Resize(config()->GetInt("task_queue_id", 0) + 1);

    return true;
  }

  bool AddExecutor();

  int Partition(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end);

  int GroupBy(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end);

  bool SetupExecutor(int index, MutableRecoContextInterface *context, RecoResultConstIter begin,
                     RecoResultConstIter end);

  void CloneAttr(ReadableRecoContextInterface *context, MutableRecoContextInterface *execute_context);

  void DereferenceAttrs(CommonRecoContext *context);

  void MergeSubflowStepInfo(AddibleRecoContextInterface *context, int p_num);

  void SaveMutableAttr(MutableRecoContextInterface *context, std::vector<CommonAttr *> *mutable_common_attrs,
                       std::vector<ItemAttr *> *mutable_item_attrs,
                       std::vector<ItemAttr *> *enable_expand_item_attrs);

  void RestoreMutableAttr(MutableRecoContextInterface *context,
                          const std::vector<CommonAttr *> &mutable_common_attrs,
                          const std::vector<ItemAttr *> &mutable_item_attrs,
                          const std::vector<ItemAttr *> &enable_expand_item_attrs);

  std::vector<CommonRecoResult> DeduplicateResults(RecoResultConstIter begin, RecoResultConstIter end);

  std::vector<std::future<CommonRecoContext *>> AsyncWithLocalThreadPool(MutableRecoContextInterface *context,
                                                                         int p_num);
  std::vector<std::future<CommonRecoContext *>> AsyncWithGlobalThreadPool(
      MutableRecoContextInterface *context, int p_num);

  void SplitAttrsByCopySemantics(const folly::F14FastSet<std::string> &copy_attrs,
                                 const std::string &reference_name,
                                 folly::F14FastSet<std::string> *in_reference,
                                 folly::F14FastSet<std::string> *not_in_reference) {
    folly::F14FastSet<std::string> reference_attrs;
    RecoUtil::ExtractStringSetFromJsonConfig(config()->Get(reference_name), &reference_attrs);
    for (const auto &attr : copy_attrs) {
      if (reference_attrs.count(attr)) {
        in_reference->insert(attr);
      } else {
        not_in_reference->insert(attr);
      }
    }
  }

 private:
  // 必须是只读属性，因为如果有写有读，其他  processor 也会
  // 造成数据不一致，逻辑上解释不通
  folly::F14FastSet<std::string> shadow_copy_common_attrs_;
  folly::F14FastSet<std::string> shadow_copy_item_attrs_;
  folly::F14FastSet<std::string> deep_copy_common_attrs_;
  folly::F14FastSet<std::string> deep_copy_item_attrs_;
  std::vector<std::string> merge_item_attrs_;
  std::string group_by_;

  bool fill_common_attrs_from_request_ = false;
  bool fill_browse_set_from_request_ = true;

  std::unordered_map<std::string, std::string> merge_common_attrs_;

  std::string pipeline_name_;
  const base::Json *deduplicate_results_config_ = nullptr;

  const base::Json *pipeline_config_ = nullptr;
  // 需要一个资源池来自适应分片
  // 主要是框架限定了，一个线程需要一套 processor
  std::vector<std::unique_ptr<SinglePipelineExecutor>> executors_;
  DISALLOW_COPY_AND_ASSIGN(CommonRecoPipelineArranger);
};

}  // namespace platform
}  // namespace ks
